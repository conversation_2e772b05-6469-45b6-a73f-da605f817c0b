<?php

namespace Nzoom\Email\Authentication;

class Config
{
    const AUTH_METHOD_OAUTH2 = 'oauth2';
    const AUTH_METHOD_IMAP = 'IMAP';

    const CERT_SSL = 'ssl';
    const CERT_TLS = 'tls';
    const CERT_STARTLS = 'starttls';
    const CERT_NOTLS = 'notls';

    private $host;
    private $port;
    private $cert;
    private $authenticationMethod;
    private $providerConfig;
    private $provider;
    private $username;
    private $password;
    /** @var bool */
    private $debug = false;

    /**
     * @param array $settings containing the following key value pairs
     *      ['host'=>'https://...', 'port'=>993, 'cert'=>'ssl', 'provider'=>'Azure',
     *       'authenticationMethod' => 'oauth2', 'username'=>'<EMAIL>', 'password'=>'pass']
     */
    public function __construct($settings)
    {
        foreach ($settings as $k=>$v) {
            if (!property_exists($this, $k)) {
                continue;
            }

            $this->$k = $v;
        }
    }


    /**
     * @return string
     */
    public function getHost(): string
    {
        return $this->host;
    }

    /**
     * @return int
     */
    public function getPort(): int
    {
        return $this->port;
    }

    /**
     * @return string
     */
    public function getCert(): string
    {
        return $this->cert;
    }

    /**
     * @return string
     */
    public function getAuthenticationMethod(): string
    {
        return $this->authenticationMethod;
    }

    /**
     * @return string
     */
    public function getProviderConfig():? array
    {
        return $this->providerConfig;
    }

    /**
     * @return string
     */
    public function getUsername(): string
    {
        return $this->username;
    }

    /**
     * @return string
     */
    public function getPassword():? string
    {
        return $this->password;
    }

    /**
     * @return bool
     */
    public function isDebug(): bool
    {
        return $this->debug;
    }

    /**
     * @param bool $debug
     */
    public function setDebug(bool $debug): void
    {
        $this->debug = $debug;
    }

}
