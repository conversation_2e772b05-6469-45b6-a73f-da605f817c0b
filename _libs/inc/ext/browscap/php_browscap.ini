;;; Provided courtesy of http://browsers.garykeith.com
;;; Created on Wednesday, June 22, 2011 at 11:26 PM GMT

;;; Keep up with the latest goings-on with the project...
;;; Follow us on Twitter <http://twitter.com/browscap>, or...
;;; Like our Facebook page <http://on.fb.me/dNNbpM> and participate in the discussion board.

[GJK_Browscap_Version]
Version=4856
Released=Wed, 22 Jun 2011 23:26:51 -0000

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; DefaultProperties

[DefaultProperties]
Browser="DefaultProperties"
Version=0
MajorVer=0
MinorVer=0
Platform=unknown
Alpha=false
Beta=false
Win16=false
Win32=false
Win64=false
Frames=false
IFrames=false
Tables=false
Cookies=false
BackgroundSounds=false
JavaScript=false
VBScript=false
JavaApplets=false
ActiveXControls=false
isBanned=false
isMobileDevice=false
isSyndicationReader=false
Crawler=false
CssVersion=0
AolVersion=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Ask

[Ask]
Parent=DefaultProperties
Browser="Ask"
Frames=true
IFrames=true
Tables=true
Crawler=true

[Mozilla/?.0 (compatible; Ask Jeeves/Teoma*)]
Parent=Ask
Browser="Teoma"

[Mozilla/2.0 (compatible; Ask Jeeves)]
Parent=Ask
Browser="AskJeeves"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Baidu

[Baidu]
Parent=DefaultProperties
Browser="Baidu"
Frames=true
IFrames=true
Tables=true
Crawler=true

[*Baiduspider*]
Parent=Baidu
Browser="BaiDu"

[*Baiduspider-ads*]
Parent=Baidu
Browser="Baidu Business search"

[*Baiduspider-cpro*]
Parent=Baidu
Browser="Baidu Union"

[*Baiduspider-favo*]
Parent=Baidu
Browser="Baidu Baidu bookmark"

[*Baiduspider-favo*]
Parent=Baidu
Browser="Baidu bookmark"

[*Baiduspider-image*]
Parent=Baidu
Browser="Baidu Image search"

[*Baiduspider-mobile*]
Parent=Baidu
Browser="Baidu Mobile search"

[*Baiduspider-news*]
Parent=Baidu
Browser="Baidu News search"

[*Baiduspider-video*]
Parent=Baidu
Browser="Baidu Video search"

[AC-BaiduBot/*]
Parent=Baidu
Browser="AC-BaiduBot"

[BaiduImageSpider*]
Parent=Baidu
Browser="BaiduImageSpider"

[Mozilla/5.0 (compatible; Baiduspider/2.0; +http://www.baidu.com/search/spider.html)]
Parent=Baidu
Browser="Baiduspider"
Version=2.0
MajorVer=2
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Google

[Google]
Parent=DefaultProperties
Browser="Google"
Frames=true
IFrames=true
Tables=true
Crawler=true

[*Googlebot-Mobile/2.*]
Parent=Google
Browser="Googlebot-Mobile"
Frames=false
IFrames=false
Tables=false

[*Googlebot/2.1*]
Parent=Google
Browser="Googlebot"
Version=2.1
MajorVer=2
MinorVer=1

[AdsBot-Google*]
Parent=Google
Browser="AdsBot-Google"

[AdsBot-Google-Mobile*]
Parent=Google
Browser="AdsBot-Google-Mobile"

[AppEngine-Google*]
Parent=Google
Browser="AppEngine-Google"

[Feedfetcher-Google*]
Parent=Google
Browser="Feedfetcher-Google"
isBanned=true
isSyndicationReader=true

[Feedfetcher-Google-iGoogleGadgets*]
Parent=Google
Browser="iGoogleGadgets"
isBanned=true
isSyndicationReader=true

[google (*Enterprise*)]
Parent=Google
Browser="Google Enterprise"

[Google OpenSocial agent*]
Parent=Google
Browser="Google OpenSocial"

[Google-Site-Verification*]
Parent=Google
Browser="Google-Site-Verification"

[Google-Sitemaps*]
Parent=Google
Browser="Google-Sitemaps"

[Googlebot-Image*]
Parent=Google
Browser="Googlebot-Image"

[Googlebot-News*]
Parent=Google
Browser="Googlebot-News"

[googlebot-urlconsole]
Parent=Google
Browser="googlebot-urlconsole"

[Googlebot-Video*]
Parent=Google
Browser="Google-Video"

[Googlebot/Test*]
Parent=Google
Browser="Googlebot/Test"

[GoogleFriendConnect*]
Parent=Google
Browser="Google Friend Connect"

[gsa-crawler*]
Parent=Google
Browser="Google Search Appliance"
isBanned=true

[Mediapartners-Google*]
Parent=Google
Browser="Mediapartners-Google"

[Mozilla/5.0 (*Feedfetcher-Google*)]
Parent=Google
Browser="Google Feedfetcher"

[Mozilla/5.0 (*Google Desktop*)]
Parent=Google
Browser="Google Desktop"

[Mozilla/5.0 (*Google Keyword Tool*)]
Parent=Google
Browser="Google Keyword Tool"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; MSN

[MSN]
Parent=DefaultProperties
Browser="MSN"
Frames=true
IFrames=true
Tables=true
Crawler=true

[adidxbot/1.1 (?http://search.msn.com/msnbot.htm)]
Parent=MSN
Browser="adidxbot"

[librabot/1.0 (*)]
Parent=MSN
Browser="librabot"

[llssbot/1.0]
Parent=MSN
Browser="llssbot"
Version=1.0
MajorVer=1
MinorVer=0

[Microsoft Bing Mobile SocialStreams Bot]
Parent=MSN
Browser="Microsoft Bing Mobile SocialStreams Bot"

[Mozilla/5.0 (compatible; bingbot/2.*http://www.bing.com/bingbot.htm)]
Parent=MSN
Browser="BingBot"

[Mozilla/5.0 (Danger hiptop 3.*; U; rv:1.7.*) Gecko/*]
Parent=MSN
Browser="Danger"

[MSMOBOT/1.1*]
Parent=MSN
Browser="msnbot-mobile"
Version=1.1
MajorVer=1
MinorVer=1

[MSNBot-Academic/1.0*]
Parent=MSN
Browser="MSNBot-Academic"
Version=1.0
MajorVer=1
MinorVer=0

[msnbot-media/1.0*]
Parent=MSN
Browser="msnbot-media"
Version=1.0
MajorVer=1
MinorVer=0

[msnbot-media/1.1*]
Parent=MSN
Browser="msnbot-media"
Version=1.1
MajorVer=1
MinorVer=1

[MSNBot-News/1.0*]
Parent=MSN
Browser="MSNBot-News"
Version=1.0
MajorVer=1
MinorVer=0

[MSNBot-NewsBlogs/1.0*]
Parent=MSN
Browser="MSNBot-NewsBlogs"
Version=1
MajorVer=1
MinorVer=0

[msnbot-NewsBlogs/2.* (+http://search.msn.com/msnbot.htm)]
Parent=MSN
Browser="msnbot-NewsBlogs"
Version=2.0
MajorVer=2
MinorVer=0

[msnbot-products]
Parent=MSN
Browser="msnbot-products"

[msnbot-webmaster/1.0 (*http://search.msn.com/msnbot.htm)]
Parent=MSN
Browser="msnbot-webmaster tools"

[msnbot/1.0*]
Parent=MSN
Browser="msnbot"
Version=1.0
MajorVer=1
MinorVer=0

[msnbot/1.1*]
Parent=MSN
Browser="msnbot"
Version=1.1
MajorVer=1
MinorVer=1

[msnbot/2.0b*]
Parent=MSN
Browser="msnbot"
Version=2.0
MajorVer=2
MinorVer=0
Beta=true

[MSR-ISRCCrawler]
Parent=MSN
Browser="MSR-ISRCCrawler"

[MSRBOT*]
Parent=MSN
Browser="MSRBOT"

[renlifangbot/1.0 (?http://search.msn.com/msnbot.htm)]
Parent=MSN
Browser="renlifangbot"

[T-Mobile Dash Mozilla/4.0 (*) MSNBOT-MOBILE/1.1 (*)]
Parent=MSN
Browser="msnbot-mobile"

[Windows-Live-Social-Object-Extractor-Engine/1.0]
Parent=MSN
Browser="Windows-Live-Social-Object-Extractor-Eng"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Yahoo

[Yahoo]
Parent=DefaultProperties
Browser="Yahoo"
Frames=true
IFrames=true
Tables=true
Crawler=true

[Mozilla/4.0 (compatible; Y!J; for robot study*)]
Parent=Yahoo
Browser="Y!J"

[Mozilla/5.0 (compatible; BMC/* (Y!J-AGENT))]
Parent=Yahoo
Browser="Y!J-AGENT/BMC"

[Mozilla/5.0 (compatible; BMF/* (Y!J-AGENT))]
Parent=Yahoo
Browser="Y!J-AGENT/BMF"

[Mozilla/5.0 (compatible; BMI/* (Y!J-AGENT; 1.0))]
Parent=Yahoo
Browser="Y!J-AGENT/BMI"

[Mozilla/5.0 (compatible; Yahoo! DE Slurp; http://help.yahoo.com/help/us/ysearch/slurp)]
Parent=Yahoo
Browser="Yahoo! Directory Engine"

[Mozilla/5.0 (compatible; Yahoo! SearchMonkey*)]
Parent=Yahoo
Browser="Yahoo! Search Monkey"

[Mozilla/5.0 (compatible; Yahoo! Slurp China*;*http://misc.yahoo.com.cn/help.html)]
Parent=Yahoo
Browser="Yahoo! Slurp China"

[Mozilla/5.0 (compatible; Yahoo! Slurp*;*http://help.yahoo.com/help/us/ysearch/slurp)]
Parent=Yahoo
Browser="Yahoo! Slurp"
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/5.0 (compatible; Yahoo! Slurp*;*http://help.yahoo.com/help/us/ysearch/slurp)]
Parent=Yahoo
Browser="Yahoo! Slurp"

[Mozilla/5.0 (compatible; Yahoo! Verifier/*)]
Parent=Yahoo
Browser="Yahoo! Verifier"
Version=1.1
MajorVer=1
MinorVer=1

[Mozilla/5.0 (compatible; Yahoo!-AdCrawler;*http://help.yahoo.com/yahoo_adcrawler)]
Parent=Yahoo
Browser="Yahoo!-AdCrawler"

[Mozilla/5.0 (compatible; YahooSeeker/M1A1-R2D2*)]
Parent=Yahoo
Browser="YahooSeeker-Mobile"

[Mozilla/5.0 (Yahoo-MMCrawler/*; mailto:<EMAIL>)]
Parent=Yahoo
Browser="Yahoo-MMCrawler"
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/5.0 (Yahoo-Test/*)]
Parent=Yahoo
Browser="Yahoo-Test"

[Mozilla/5.0 (YahooYSMcm*)]
Parent=Yahoo
Browser="YahooYSMcm"

[mp3Spider cn-search-devel at yahoo-inc dot com]
Parent=Yahoo
Browser="Yahoo! Media"
isBanned=true

[My Browser]
Parent=Yahoo
Browser="Yahoo! My Browser"

[Scooter*]
Parent=Yahoo
Browser="Scooter"

[Scooter/*Y!CrawlX]
Parent=Yahoo
Browser="Scooter/3.3Y!CrawlX"
Version=3.3
MajorVer=3
MinorVer=3

[slurp]
Parent=Yahoo
Browser="slurp"

[Y!J SearchMonkey*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRE*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRG/GSC*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRI*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRO/YFSJ*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRP/YFSBJ*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BRQ/DLCK*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-BSC*]
Parent=Yahoo
Browser="YahooFeedSeeker"
Version=1.0
MajorVer=1
MinorVer=0
isSyndicationReader=true

[Y!J-DSC*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-NSC*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-PSC*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!J-SRD*]
Parent=Yahoo
Browser="YahooFeedSeeker"
Version=1.0
MajorVer=1
MinorVer=0

[Y!J-VSC/ViSe*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[Y!OASIS*]
Parent=Yahoo
Browser="Y!OASIS"
isBanned=true

[Yahoo Mindset]
Parent=Yahoo
Browser="Yahoo Mindset"

[Yahoo Pipes*]
Parent=Yahoo
Browser="Yahoo Pipes"

[Yahoo! Mindset]
Parent=Yahoo
Browser="Yahoo! Mindset"

[Yahoo! Slurp/Site Explorer]
Parent=Yahoo
Browser="Yahoo! Site Explorer"

[Yahoo-Blogs*]
Parent=Yahoo
Browser="Yahoo-Blogs"

[Yahoo-MMAudVid*]
Parent=Yahoo
Browser="Yahoo-MMAudVid"

[Yahoo-MMCrawler*]
Parent=Yahoo
Browser="Yahoo-MMCrawler"
isBanned=true

[YahooExternalCache]
Parent=Yahoo
Browser="YahooExternalCache"

[YahooFeedSeeker*]
Parent=Yahoo
Browser="YahooFeedSeeker"
isSyndicationReader=true

[YahooSeeker*]
Parent=Yahoo
Browser="YahooSeeker"
isMobileDevice=true

[YahooSeeker/CafeKelsa*]
Parent=Yahoo
Browser="YahooSeeker/CafeKelsa"

[YahooVideoSearch*]
Parent=Yahoo
Browser="YahooVideoSearch"

[YahooYSMcm*]
Parent=Yahoo
Browser="YahooYSMcm"

[YRL_ODP_CRAWLER]
Parent=Yahoo
Browser="YRL_ODP_CRAWLER"
isBanned=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Yandex

[Yandex]
Parent=DefaultProperties
Browser="Yandex"
Frames=true
IFrames=true
Tables=true
Crawler=true

[Mozilla/5.0 (compatible; YandexAddurl/*)*]
Parent=Yandex
Browser="YandexAddURL"

[Mozilla/5.0 (compatible; YandexBlogs/*; robot)]
Parent=Yandex
Browser="YandexBlogs"

[Mozilla/5.0 (compatible; YandexBot/*)*]
Parent=Yandex
Browser="YandexBot"

[Mozilla/5.0 (compatible; YandexBot/*; MirrorDetector)]
Parent=Yandex
Browser="Yandex MirrorDetector"

[Mozilla/5.0 (compatible; YandexCatalog/*)*]
Parent=Yandex
Browser="YandexCatalog"

[Mozilla/5.0 (compatible; YandexDirect/*)*]
Parent=Yandex
Browser="YandexDirect-Dyatel"

[Mozilla/5.0 (compatible; YandexFavicons/*)*]
Parent=Yandex
Browser="YandexFavicons"

[Mozilla/5.0 (compatible; YandexImageResizer/*)*]
Parent=Yandex
Browser="YandexImageResizer"

[Mozilla/5.0 (compatible; YandexImages/*)*]
Parent=Yandex
Browser="YandexImages"

[Mozilla/5.0 (compatible; YandexMedia/*)*]
Parent=Yandex
Browser="YandexMedia"

[Mozilla/5.0 (compatible; YandexMetrika/*)*]
Parent=Yandex
Browser="YandexMetrika"

[Mozilla/5.0 (compatible; YandexNews/*)*]
Parent=Yandex
Browser="YandexNews"

[Mozilla/5.0 (compatible; YandexVideo/*)*]
Parent=Yandex
Browser="YandexVideo"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Best of the Web

[Best of the Web]
Parent=DefaultProperties
Browser="Best of the Web"
Frames=true
Tables=true
Crawler=true

[Mozilla/4.0 (compatible; BOTW Feed Grabber; *http://botw.org)]
Parent=Best of the Web
Browser="BOTW Feed Grabber"
isSyndicationReader=true

[Mozilla/4.0 (compatible; BOTW Spider; *http://botw.org)]
Parent=Best of the Web
Browser="BOTW Spider"
isBanned=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Boitho

[Boitho]
Parent=DefaultProperties
Browser="Boitho"
Frames=true
Tables=true
Crawler=true

[boitho.com-dc/*]
Parent=Boitho
Browser="boitho.com-dc"

[boitho.com-robot/*]
Parent=Boitho
Browser="boitho.com-robot"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Convera

[Convera]
Parent=DefaultProperties
Browser="Convera"
Frames=true
Tables=true
Crawler=true

[ConveraCrawler/*]
Parent=Convera
Browser="ConveraCrawler"

[ConveraMultiMediaCrawler/0.1*]
Parent=Convera
Browser="ConveraMultiMediaCrawler"
Version=0.1
MajorVer=0
MinorVer=1

[CrawlConvera*]
Parent=Convera
Browser="CrawlConvera"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; DotBot

[DotBot]
Parent=DefaultProperties
Browser="DotBot"
Frames=true
Tables=true
isBanned=true
Crawler=true

[DotBot/* (http://www.dotnetdotcom.org/*)]
Parent=DotBot

[Mozilla/5.0 (compatible; DotBot/*; http://www.dotnetdotcom.org/*)]
Parent=DotBot

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Entireweb

[Entireweb]
Parent=DefaultProperties
Browser="Entireweb"
Frames=true
Tables=true
isBanned=true
Crawler=true

[Mozilla/5.0 (compatible; Speedy Spider; http://www.entireweb.com/about/search_tech/speedy_spider/)]
Parent=Entireweb

[Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US) Speedy Spider (http://www.entireweb.com/about/search_tech/speedy_spider/)]
Parent=Entireweb

[Speedy Spider (http://www.entireweb.com/about/search_tech/speedy_spider/)]
Parent=Entireweb

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Envolk

[Envolk]
Parent=DefaultProperties
Browser="Envolk"
Frames=true
Tables=true
isBanned=true
Crawler=true

[envolk/* (?http://www.envolk.com/envolk*)]
Parent=Envolk

[envolk?ITS?spider/* (?http://www.envolk.com/envolk*)]
Parent=Envolk

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Exalead

[Exalead]
Parent=DefaultProperties
Browser="Exalead"
Frames=true
Tables=true
isBanned=true
Crawler=true

[Exabot-Images/1.0]
Parent=Exalead
Browser="Exabot-Images"
Version=1.0
MajorVer=1
MinorVer=0

[Exabot-Test/*]
Parent=Exalead
Browser="Exabot-Test"

[Exabot/2.0]
Parent=Exalead
Browser="Exabot"
Version=2.0
MajorVer=2
MinorVer=0

[Exabot/3.0]
Parent=Exalead
Browser="Exabot"
Version=3.0
MajorVer=3
MinorVer=0
Platform=Liberate

[Exalead NG/*]
Parent=Exalead
Browser="Exalead NG"
isBanned=true

[Mozilla/5.0 (compatible; Exabot-Images/3.0*)]
Parent=Exalead
Browser="Exabot-Images"
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/5.0 (compatible; Exabot/3.0*)]
Parent=Exalead
Browser="Exabot/BiggerBetter"
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/5.0 (compatible; NGBot/*)]
Parent=Exalead
Browser="NGBot"

[NG-Search/*]
Parent=Exalead
Browser="NG-Search"

[ng/*]
Parent=Exalead
Browser="Exalead Previewer"
isBanned=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Facebook

[Facebook]
Parent=DefaultProperties
Browser="Facebook"
Frames=true
Tables=true
Crawler=true

[facebookexternalhit/* (?http://www.facebook.com/externalhit_uatext.php)*]
Parent=Facebook
Browser="FacebookExternalHit"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Fast/AllTheWeb

[Fast/AllTheWeb]
Parent=DefaultProperties
Browser="Fast/AllTheWeb"
Frames=true
Tables=true
Crawler=true

[*FAST Enterprise Crawler*]
Parent=Fast/AllTheWeb
Browser="FAST Enterprise Crawler"

[FAST Data Search Document Retriever/4.0*]
Parent=Fast/AllTheWeb
Browser="FAST Data Search Document Retriever"

[FAST MetaWeb Crawler (helpdesk at fastsearch dot com)]
Parent=Fast/AllTheWeb
Browser="FAST MetaWeb Crawler"

[Fast PartnerSite Crawler*]
Parent=Fast/AllTheWeb
Browser="FAST PartnerSite"

[FAST-WebCrawler/*]
Parent=Fast/AllTheWeb
Browser="FAST-WebCrawler"

[FAST-WebCrawler/*/FirstPage*]
Parent=Fast/AllTheWeb
Browser="FAST-WebCrawler/FirstPage"

[FAST-WebCrawler/*/Fresh*]
Parent=Fast/AllTheWeb
Browser="FAST-WebCrawler/Fresh"

[FAST-WebCrawler/*/PartnerSite*]
Parent=Fast/AllTheWeb
Browser="FAST PartnerSite"

[FAST-WebCrawler/*?Multimedia*]
Parent=Fast/AllTheWeb
Browser="FAST-WebCrawler/Multimedia"

[FastSearch Web Crawler for*]
Parent=Fast/AllTheWeb
Browser="FastSearch Web Crawler"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Gigabot

[Gigabot]
Parent=DefaultProperties
Browser="Gigabot"
Frames=true
Tables=true
Crawler=true

[Gigabot*]
Parent=Gigabot

[GigabotSiteSearch/*]
Parent=Gigabot
Browser="GigabotSiteSearch"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Ilse

[Ilse]
Parent=DefaultProperties
Browser="Ilse"
Frames=true
Tables=true
Crawler=true

[IlseBot/*]
Parent=Ilse

[INGRID/?.0*]
Parent=Ilse

[Mozilla/3.0 (INGRID/*]
Parent=Ilse

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iVia Project

[iVia Project]
Parent=DefaultProperties
Browser="iVia Project"
Frames=true
Tables=true
Crawler=true

[DataFountains/DMOZ Downloader*]
Parent=iVia Project
Browser="DataFountains/DMOZ Downloader"
isBanned=true

[DataFountains/DMOZ Feature Vector Corpus Creator*]
Parent=iVia Project
Browser="DataFountains/DMOZ Feature Vector Corpus"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Jayde Online

[Jayde Online]
Parent=DefaultProperties
Browser="Jayde Online"
Frames=true
Tables=true
Crawler=true

[ExactSeek Crawler/*]
Parent=Jayde Online
Browser="ExactSeek Crawler"

[exactseek-pagereaper-* (<EMAIL>)]
Parent=Jayde Online
Browser="exactseek-pagereaper"
isBanned=true

[exactseek.com]
Parent=Jayde Online
Browser="exactseek.com"

[Jayde Crawler*]
Parent=Jayde Online
Browser="Jayde Crawler"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lycos

[Lycos]
Parent=DefaultProperties
Browser="Lycos"
Frames=true
Tables=true
Crawler=true

[Lycos*]
Parent=Lycos
Browser="Lycos"

[Lycos-Proxy]
Parent=Lycos
Browser="Lycos-Proxy"

[Lycos-Spider_(modspider)]
Parent=Lycos
Browser="Lycos-Spider_(modspider)"

[Lycos-Spider_(T-Rex)]
Parent=Lycos
Browser="Lycos-Spider_(T-Rex)"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Snap

[Snap]
Parent=DefaultProperties
Browser="Snap"
Frames=true
Tables=true
isBanned=true
Crawler=true

[Mozilla/5.0 (SnapPreviewBot) Gecko/* Firefox/*]
Parent=Snap
Browser="SnapPreviewBot"

[Snapbot/*]
Parent=Snap
Browser="Snapbot"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Sogou

[Sogou]
Parent=DefaultProperties
Browser="Sogou"
Frames=true
Tables=true
isBanned=true
Crawler=true

[shaboyi spider]
Parent=Sogou
Browser="Sogou/Shaboyi Spider"

[Sogou develop spider/*]
Parent=Sogou
Browser="Sogou Develop Spider"

[Sogou head spider*]
Parent=Sogou
Browser="Sogou Head Spider"

[sogou js robot(*)]
Parent=Sogou

[Sogou Orion spider/*]
Parent=Sogou
Browser="Sogou Orion spider"

[Sogou Pic Agent]
Parent=Sogou
Browser="Sogou/Image Crawler"

[Sogou Pic Spider/*]
Parent=Sogou
Browser="Sogou Pic Spider"

[Sogou Push Spider/*]
Parent=Sogou
Browser="Sogou Push Spider"

[sogou spider]
Parent=Sogou
Browser="Sogou/Spider"

[sogou web spider*]
Parent=Sogou
Browser="sogou web spider"

[Sogou-Test-Spider/*]
Parent=Sogou
Browser="Sogou-Test-Spider"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; YodaoBot

[YodaoBot]
Parent=DefaultProperties
Browser="YodaoBot"
Frames=true
Tables=true
isBanned=true
Crawler=true

[Mozilla/5.0 (compatible; YodaoBot/1.*)]
Parent=YodaoBot

[Mozilla/5.0 (compatible;YodaoBot-Image/1.*)]
Parent=YodaoBot
Browser="YodaoBot-Image"

[WAP_Browser/5.0 (compatible; YodaoBot/1.*)]
Parent=YodaoBot

[YodaoBot/1.* (*)]
Parent=YodaoBot

[Best Whois (http://www.bestwhois.net/)]
Parent=DNS Tools
Browser="Best Whois"

[DNSGroup/*]
Parent=DNS Tools
Browser="DNS Group Crawler"

[TouchStone]
Parent=Feeds Syndicators
Browser="TouchStone"
isSyndicationReader=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; General Crawlers

[General Crawlers]
Parent=DefaultProperties
Browser="General Crawlers"
Crawler=true

[*altervista.org*]
Parent=General Crawlers
Browser="altervista.org"

[*naver*]
Parent=General Crawlers
Browser="NaverBot"
isBanned=true

[*Spinn3r*http://spinn3r.com/robot*]
Parent=General Crawlers
Browser="Spinn3r"

[*SqwidgeBot*]
Parent=General Crawlers
Browser="SqwidgeBot"

[A .NET Web Crawler]
Parent=General Crawlers
Browser="A .NET Web Crawler"
isBanned=true

[BabalooSpider/1.*]
Parent=General Crawlers
Browser="BabalooSpider"

[BilgiBot/*]
Parent=General Crawlers
Browser="BilgiBot"
isBanned=true

[bitlybot/2.*]
Parent=General Crawlers
Browser="BitlyBot"

[bot/* (bot; *<EMAIL>)]
Parent=General Crawlers
Browser="bot"
isBanned=true

[cisco-IOS]
Parent=General Crawlers
Browser="cisco-IOS"

[Covario-IDS/*]
Parent=General Crawlers
Browser="Covario-IDS/*"

[CyberPatrol*]
Parent=General Crawlers
Browser="CyberPatrol"
isBanned=true

[Cynthia 1.0]
Parent=General Crawlers
Browser="Cynthia"
Version=1.0
MajorVer=1
MinorVer=0

[cz32ts]
Parent=General Crawlers
Browser="cz32ts"
isBanned=true

[ddetailsbot (http://www.displaydetails.com)]
Parent=General Crawlers
Browser="ddetailsbot"

[DomainCrawler/1.0 (<EMAIL>; http://www.domaincrawler.com/domains/view/*)]
Parent=General Crawlers
Browser="DomainCrawler"

[DomainsBotBot/1.*]
Parent=General Crawlers
Browser="DomainsBotBot"
isBanned=true

[DomainsDB.net MetaCrawler*]
Parent=General Crawlers
Browser="DomainsDB"

[DomainWatcher Bot*]
Parent=General Crawlers
Browser="DomainWatcher Bot"

[Drupal (*)]
Parent=General Crawlers
Browser="Drupal"

[Dumbot (version *)*]
Parent=General Crawlers
Browser="Dumbfind"

[EuripBot/*]
Parent=General Crawlers
Browser="Europe Internet Portal"

[eventax/*]
Parent=General Crawlers
Browser="eventax"

[FANGCrawl/*]
Parent=General Crawlers
Browser="Safe-t.net Web Filtering Service"
isBanned=true

[favorstarbot/*]
Parent=General Crawlers
Browser="favorstarbot"
isBanned=true

[FollowSite.com (*)]
Parent=General Crawlers
Browser="FollowSite"
isBanned=true

[Gaisbot*]
Parent=General Crawlers
Browser="Gaisbot"

[gosospider Mozilla/5.0 (compatible; GosoSpider*)]
Parent=General Crawlers
Browser="GosoSpider"

[Healthbot/Health_and_Longevity_Project_(HealthHaven.com) ]
Parent=General Crawlers
Browser="Healthbot"
isBanned=true

[hitcrawler_0.*]
Parent=General Crawlers
Browser="hitcrawler"
isBanned=true

[htdig/*]
Parent=General Crawlers
Browser="ht://Dig"

[http://hilfe.acont.de/bot.html ACONTBOT]
Parent=General Crawlers
Browser="ACONTBOT"
isBanned=true

[HuaweiSymantecSpider/*]
Parent=General Crawlers
Browser="HuaweiSymantecSpider"

[JetBrains*]
Parent=General Crawlers
Browser="Omea Pro"

[JS-Kit URL Resolver, http://js-kit.com/]
Parent=General Crawlers
Browser="JS-Kit/Echo"

[KakleBot - www.kakle.com/0.1]
Parent=General Crawlers
Browser="KakleBot"

[KBeeBot/0.*]
Parent=General Crawlers
Browser="KBeeBot"
isBanned=true

[Keyword Density/*]
Parent=General Crawlers
Browser="Keyword Density"

[LetsCrawl.com/1.0*]
Parent=General Crawlers
Browser="LetsCrawl.com"
isBanned=true

[Lincoln State Web Browser]
Parent=General Crawlers
Browser="Lincoln State Web Browser"
isBanned=true

[LinkedInBot/1.*]
Parent=General Crawlers
Browser="LinkedInBot"

[Links4US-Crawler,*]
Parent=General Crawlers
Browser="Links4US-Crawler"
isBanned=true

[Lorkyll *.* -- <EMAIL>]
Parent=General Crawlers
Browser="Lorkyll"
isBanned=true

[Lsearch/sondeur]
Parent=General Crawlers
Browser="Lsearch/sondeur"
isBanned=true

[LucidMedia ClickSense/4.?]
Parent=General Crawlers
Browser="LucidMedia-ClickSense"
isBanned=true

[Made by ZmEu @ WhiteHat v0.* (www.WhiteHat.ro)]
Parent=General Crawlers
Browser="ZmEu"
isBanned=true

[magpie-crawler/1.*]
Parent=General Crawlers
Browser="magpie-crawler"

[Mahalobot/1.0 (?http://www.mahalo.com/)]
Parent=General Crawlers
Browser="Mahalobot"

[MapoftheInternet.com?(?http://MapoftheInternet.com)]
Parent=General Crawlers
Browser="MapoftheInternet"
isBanned=true

[Marvin v0.3]
Parent=General Crawlers
Browser="MedHunt"
Version=0.3
MajorVer=0
MinorVer=3

[masidani_bot_v0.6*]
Parent=General Crawlers
Browser="masidani_bot"

[Metaspinner/0.01 (Metaspinner; http://www.meta-spinner.de/; <EMAIL>/)]
Parent=General Crawlers
Browser="Metaspinner/0.01"
Version=0.01
MajorVer=0
MinorVer=01

[metatagsdir/*]
Parent=General Crawlers
Browser="metatagsdir"
isBanned=true

[Microsoft Windows Network Diagnostics]
Parent=General Crawlers
Browser="Microsoft Windows Network Diagnostics"
isBanned=true

[Miva (<EMAIL>)]
Parent=General Crawlers
Browser="Miva"

[moget/*]
Parent=General Crawlers
Browser="Goo"

[Mozdex/0.7*]
Parent=General Crawlers
Browser="Mozdex"

[Mozilla/* (compatible; WebCapture*)]
Parent=General Crawlers
Browser="WebCapture"

[Mozilla/*(*redditbot/*http://www.reddit.com/feedback*)]
Parent=General Crawlers
Browser="Reddit"

[Mozilla/4.0 (compatible; DepSpid/*)]
Parent=General Crawlers
Browser="DepSpid"

[Mozilla/4.0 (compatible; MSIE 4.01; Vonna.com b o t)]
Parent=General Crawlers
Browser="Vonna.com"
isBanned=true

[Mozilla/4.0 (compatible; MSIE 4.01; Windows95)]
Parent=General Crawlers
Browser="Generic Crawler"
Win32=true

[Mozilla/4.0 (compatible; MyFamilyBot/*)]
Parent=General Crawlers
Browser="MyFamilyBot"

[Mozilla/4.0 (compatible; N-Stealth)]
Parent=General Crawlers
Browser="N-Stealth"

[Mozilla/4.0 (compatible; Scumbot/*; Linux/*)]
Parent=General Crawlers
Browser="Generic Crawler"
isBanned=true

[Mozilla/4.0 (compatible; Spider; Linux)]
Parent=General Crawlers
Browser="Generic Crawler"
isBanned=true

[Mozilla/4.0 (compatible; Win32)]
Parent=General Crawlers
Browser="Unknown Crawler"
isBanned=true

[Mozilla/5.0 (*http://gnomit.com/) Gecko/* Gnomit/1.0]
Parent=General Crawlers
Browser="Gnomit"
isBanned=true

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) ADM/*]
Parent=General Crawlers
Browser="Adobe Dialog Manager"
Platform=MacOSX

[Mozilla/5.0 (compatible; *; http://www.80legs.com/spider.html;) Gecko/*]
Parent=General Crawlers
Browser="80Legs"

[Mozilla/5.0 (compatible; AboutUsBot/*)]
Parent=General Crawlers
Browser="AboutUsBot"
isBanned=true

[Mozilla/5.0 (compatible; AdHitz; http://adhitz.com/)]
Parent=General Crawlers
Browser="AdHitz"

[Mozilla/5.0 (compatible; aiHitBot*/*; +http://www.aihit.com/)]
Parent=General Crawlers
Browser="aiHitBot"

[Mozilla/5.0 (compatible; BuzzRankingBot/*)]
Parent=General Crawlers
Browser="BuzzRankingBot"
isBanned=true

[Mozilla/5.0 (compatible; ClixSense; http://www.clixsense.com/)]
Parent=General Crawlers
Browser="ClixSense"
isBanned=true

[Mozilla/5.0 (compatible; Crawly/1.*; +http://*/crawler.html)]
Parent=General Crawlers
Browser="Crawly"
isBanned=true

[Mozilla/5.0 (compatible; Diffbot/0.1; +http://www.diffbot.com)]
Parent=General Crawlers
Browser="Diffbot"

[Mozilla/5.0 (compatible; Ezooms/1.0; <EMAIL>)]
Parent=General Crawlers
Browser="Ezooms"
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (compatible; FirstSearchBot/1.0; *)]
Parent=General Crawlers
Browser="FirstSearchBot"

[mozilla/5.0 (compatible; genevabot +http://www.healthdash.com)]
Parent=General Crawlers
Browser="Healthdash"

[Mozilla/5.0 (compatible; JadynAveBot; *http://www.jadynave.com/robot*]
Parent=General Crawlers
Browser="JadynAveBot"
isBanned=true

[Mozilla/5.0 (compatible; Kyluka crawl; http://www.kyluka.com/crawl.html; <EMAIL>)]
Parent=General Crawlers
Browser="Kyluka"

[Mozilla/5.0 (compatible; LegalAnalysisAgent/1.*; http://www.legalx.net)]
Parent=General Crawlers
Browser="LegalAnalysisAgent"
isBanned=true

[Mozilla/5.0 (compatible; MJ12bot/v1.*)]
Parent=General Crawlers
Browser="MJ12bot"
isBanned=true

[Mozilla/5.0 (compatible; MSIE 7.0 ?http://www.europarchive.org)]
Parent=General Crawlers
Browser="Europe Web Archive"

[Mozilla/5.0 (compatible; MSIE 7.0; MSIE 6.0; ScanAlert; +http://www.scanalert.com/bot.jsp) Firefox/*]
Parent=General Crawlers
Browser="McAffee Scan Alert"

[Mozilla/5.0 (compatible; Plukkie/1.?; http://www.botje.com/plukkie.htm)]
Parent=General Crawlers
Browser="Plukkie"

[Mozilla/5.0 (compatible; SEODat/0.* http://crawler.seodat.com)]
Parent=General Crawlers
Browser="SEODat"

[Mozilla/5.0 (compatible; Seznam screenshot-generator 2.0;*)]
Parent=General Crawlers
Browser="Seznam screenshot-generator"
isBanned=true

[Mozilla/5.0 (compatible; spbot/*; +http://www.seoprofiler.com/bot/ )]
Parent=General Crawlers
Browser="SEOprofiler"

[Mozilla/5.0 (compatible; SuchbaerBot/0.*; +http://bot.suchbaer.de/info.html)]
Parent=General Crawlers
Browser="SuchbaerBot"

[Mozilla/5.0 (compatible; Twingly Recon; http://www.twingly.com/)]
Parent=General Crawlers
Browser="Twingly Recon"

[Mozilla/5.0 (compatible; unwrapbot/2.*; +http://www.unwrap.jp*)]
Parent=General Crawlers
Browser="UnWrap"

[Mozilla/5.0 (compatible; Vermut*)]
Parent=General Crawlers
Browser="Vermut"

[Mozilla/5.0 (compatible; Viralheat Bot/*) ]
Parent=General Crawlers
Browser="Viralheat"
isBanned=true

[Mozilla/5.0 (compatible; Webbot/*)]
Parent=General Crawlers
Browser="Webbot.ru"
isBanned=true

[n4p_bot*]
Parent=General Crawlers
Browser="n4p_bot"

[nabot*]
Parent=General Crawlers
Browser="Nabot"

[NetCarta_WebMapper/*]
Parent=General Crawlers
Browser="NetCarta_WebMapper"
isBanned=true

[Netchart Adv Crawler*]
Parent=General Crawlers
Browser="Netchart Adv Crawler"
isBanned=true

[NetID.com Bot*]
Parent=General Crawlers
Browser="NetID.com Bot"
isBanned=true

[<NAME_EMAIL>]
Parent=General Crawlers
Browser="neTVision"

[NextopiaBOT*]
Parent=General Crawlers
Browser="NextopiaBOT"

[nicebot]
Parent=General Crawlers
Browser="nicebot"
isBanned=true

[niXXieBot?Foster*]
Parent=General Crawlers
Browser="niXXiebot-Foster"

[Nozilla/P.N (Just for IDS woring)]
Parent=General Crawlers
Browser="Nozilla/P.N"
isBanned=true

[NSO_Debugger_User/2.0]
Parent=General Crawlers
Browser="NSO_Debugger_User"
isBanned=true

[Nudelsalat/*]
Parent=General Crawlers
Browser="Nudelsalat"
isBanned=true

[NV32ts]
Parent=General Crawlers
Browser="NV32ts"
isBanned=true

[Ocelli/*]
Parent=General Crawlers
Browser="Ocelli"

[OpenTaggerBot (http://www.opentagger.com/opentaggerbot.htm)]
Parent=General Crawlers
Browser="OpenTaggerBot"

[Oracle Enterprise Search]
Parent=General Crawlers
Browser="Oracle Enterprise Search"
isBanned=true

[Oracle Ultra Search]
Parent=General Crawlers
Browser="Oracle Ultra Search"

[Pajaczek/*]
Parent=General Crawlers
Browser="Pajaczek"
isBanned=true

[panscient.com]
Parent=General Crawlers
Browser="panscient.com"
isBanned=true

[Patwebbot (http://www.herz-power.de/technik.html)]
Parent=General Crawlers
Browser="Patwebbot"

[PDFBot (<EMAIL>)]
Parent=General Crawlers
Browser="PDFBot"

[Pete-Spider/1.*]
Parent=General Crawlers
Browser="Pete-Spider"
isBanned=true

[PhpDig/*]
Parent=General Crawlers
Browser="PhpDig"

[PlantyNet_WebRobot*]
Parent=General Crawlers
Browser="PlantyNet"
isBanned=true

[PluckItCrawler/*]
Parent=General Crawlers
Browser="PluckItCrawler"
isMobileDevice=true

[PMAFind]
Parent=General Crawlers
Browser="PMAFind"
isBanned=true

[Poodle_predictor_1.0]
Parent=General Crawlers
Browser="Poodle Predictor"

[QuickFinder Crawler]
Parent=General Crawlers
Browser="QuickFinder"
isBanned=true

[Radiation Retriever*]
Parent=General Crawlers
Browser="Radiation Retriever"
isBanned=true

[RedCarpet/*]
Parent=General Crawlers
Browser="RedCarpet"
isBanned=true

[RixBot (http://babelserver.org/rix)]
Parent=General Crawlers
Browser="RixBot"

[roboobot/1.* (roboo; http://wap.roboo.com; <EMAIL>)]
Parent=General Crawlers
Browser="roboo"

[Rome Client (http://tinyurl.com/64t5n) Ver: 0.*]
Parent=General Crawlers
Browser="TinyURL"

[SBIder/*]
Parent=General Crawlers
Browser="SiteSell"

[ScollSpider/2.*]
Parent=General Crawlers
Browser="ScollSpider"
isBanned=true

[Search Fst]
Parent=General Crawlers
Browser="Search Fst"

[searchbot <EMAIL>]
Parent=General Crawlers
Browser="searchbot"
isBanned=true

[Seeker.lookseek.com]
Parent=General Crawlers
Browser="LookSeek"
isBanned=true

[semanticdiscovery/*]
Parent=General Crawlers
Browser="Semantic Discovery"

[SeznamBot/*]
Parent=General Crawlers
Browser="SeznamBot"
isBanned=true

[Shelob (<EMAIL>)]
Parent=General Crawlers
Browser="Shelob"
isBanned=true

[shelob v1.*]
Parent=General Crawlers
Browser="shelob"
isBanned=true

[ShopWiki/1.0*]
Parent=General Crawlers
Browser="ShopWiki"
Version=1.0
MajorVer=1
MinorVer=0

[ShowXML/1.0 libwww/5.4.0]
Parent=General Crawlers
Browser="ShowXML"
isBanned=true

[sitecheck.internetseer.com*]
Parent=General Crawlers
Browser="Internetseer"

[SMBot/*]
Parent=General Crawlers
Browser="SMBot"

[sohu*]
Parent=General Crawlers
Browser="sohu-search"
isBanned=true

[SpankBot*]
Parent=General Crawlers
Browser="SpankBot"
isBanned=true

[spider (<EMAIL>)]
Parent=General Crawlers
Browser="spider (<EMAIL>)"
isBanned=true

[Sunrise/0.*]
Parent=General Crawlers
Browser="Sunrise"
isBanned=true

[Superpages URL Verification Engine]
Parent=General Crawlers
Browser="Superpages"

[Surf Knight]
Parent=General Crawlers
Browser="Surf Knight"
isBanned=true

[SurveyBot/*]
Parent=General Crawlers
Browser="SurveyBot"
isBanned=true

[SynapticSearch/AI Crawler 1.?]
Parent=General Crawlers
Browser="SynapticSearch"
isBanned=true

[SyncMgr]
Parent=General Crawlers
Browser="SyncMgr"

[Tagyu Agent/1.0]
Parent=General Crawlers
Browser="Tagyu"

[Talkro Web-Shot/*]
Parent=General Crawlers
Browser="Talkro Web-Shot"
isBanned=true

[Tasap-image-robot/0.* (http://www.tasap.com)]
Parent=General Crawlers
Browser="Tasap-image-robot"
isBanned=true

[Tecomi Bot (http://www.tecomi.com/bot.htm)]
Parent=General Crawlers
Browser="Tecomi"

[TencentTraveler*]
Parent=General Crawlers
Browser="TencentTraveler"

[TheInformant*]
Parent=General Crawlers
Browser="TheInformant"
isBanned=true

[Toata dragostea*]
Parent=General Crawlers
Browser="Toata dragostea"
isBanned=true

[Tutorial Crawler*]
Parent=General Crawlers
Browser="Tutorial Crawler"
isBanned=true

[Twitterbot/0.*]
Parent=General Crawlers
Browser="Twitterbot"

[UbiCrawler/*]
Parent=General Crawlers
Browser="UbiCrawler"

[UCmore]
Parent=General Crawlers
Browser="UCmore"

[User*Agent:*]
Parent=General Crawlers
Browser="Generic Crawler"
isBanned=true

[USER_AGENT]
Parent=General Crawlers
Browser="Generic Crawler"
isBanned=true

[VadixBot]
Parent=General Crawlers
Browser="VadixBot"

[VengaBot/*]
Parent=General Crawlers
Browser="VengaBot"
isBanned=true

[Visicom Toolbar]
Parent=General Crawlers
Browser="Visicom Toolbar"

[Visited by http://tools.geek-tools.org]
Parent=General Crawlers
Browser="geek-tools.org"

[Webclipping.com]
Parent=General Crawlers
Browser="Webclipping.com"
isBanned=true

[webcollage*]
Parent=General Crawlers
Browser="WebCollage"
isBanned=true

[WebCrawler_1.*]
Parent=General Crawlers
Browser="WebCrawler"

[WebFilter Robot*]
Parent=General Crawlers
Browser="WebFilter Robot"

[WeBoX/*]
Parent=General Crawlers
Browser="WeBoX"

[WebTrends/*]
Parent=General Crawlers
Browser="WebTrends"

[West Wind Internet Protocols*]
Parent=General Crawlers
Browser="Versatel"
isBanned=true

[WhizBang]
Parent=General Crawlers
Browser="WhizBang"

[Willow Internet Crawler by Twotrees V*]
Parent=General Crawlers
Browser="Willow Internet Crawler"

[WIRE/* (Linux*Bot,Robot,Spider,Crawler)]
Parent=General Crawlers
Browser="WIRE"
isBanned=true

[www.fi crawler, contact <EMAIL>]
Parent=General Crawlers
Browser="www.fi crawler"

[Xerka WebBot v1.*]
Parent=General Crawlers
Browser="Xerka"
isBanned=true

[XML Sitemaps Generator*]
Parent=General Crawlers
Browser="XML Sitemaps Generator"

[XSpider*]
Parent=General Crawlers
Browser="XSpider"
isBanned=true

[YooW!/* (?http://www.yoow.eu)]
Parent=General Crawlers
Browser="YooW!"
isBanned=true

[<EMAIL>]
Parent=General Crawlers
Browser="YellowPages"

[HiddenMarket-*]
Parent=General RSS
Browser="HiddenMarket"
isBanned=true

[FOTOCHECKER]
Parent=Image Crawlers
Browser="FOTOCHECKER"
isBanned=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Search Engines

[Search Engines]
Parent=DefaultProperties
Browser="Search Engines"
Crawler=true

[*FDSE robot*]
Parent=Search Engines
Browser="FDSE Robot"

[*Fluffy the spider*]
Parent=Search Engines
Browser="SearchHippo"

[Abacho*]
Parent=Search Engines
Browser="Abacho"

[ah-ha.com crawler (<EMAIL>)]
Parent=Search Engines
Browser="Ah-Ha"

[AIBOT/*]
Parent=Search Engines
Browser="21Seek.Com"

[ALeadSoftbot/*]
Parent=Search Engines
Browser="ALeadSoftbot"

[Amfibibot/*]
Parent=Search Engines
Browser="Amfibi"

[AnswerBus (http://www.answerbus.com/)]
Parent=Search Engines
Browser="AnswerBus"

[antibot-V*]
Parent=Search Engines
Browser="antibot"

[appie*(www.walhello.com)]
Parent=Search Engines
Browser="Walhello"

[ASPSeek/*]
Parent=Search Engines
Browser="ASPSeek"

[Atrax Solutions atraxbot/0.*; http://www.atraxsolutions.com/atraxbot]
Parent=Search Engines
Browser="Atrax Solutions"

[BigCliqueBOT/*]
Parent=Search Engines
Browser="BigClique.com/BigClic.com"

[Blaiz-Bee/*]
Parent=Search Engines
Browser="RawGrunt"

[btbot/*]
Parent=Search Engines
Browser="Bit Torrent Search Engine"

[Busiversebot/v1.0 (http://www.busiverse.com/bot.php)]
Parent=Search Engines
Browser="Busiversebot"
isBanned=true

[CatchBot/*; +http://www.catchbot.com]
Parent=Search Engines
Browser="CatchBot"
Version=1.0
MajorVer=1
MinorVer=0

[CipinetBot (http://www.cipinet.com/bot.html)]
Parent=Search Engines
Browser="CipinetBot"

[Cogentbot/1.?*]
Parent=Search Engines
Browser="Cogentbot"

[compatible; Mozilla 4.0; MSIE 5.5; (SqwidgeBot v1.01 - http://www.sqwidge.com/bot/)]
Parent=Search Engines
Browser="SqwidgeBot"

[cosmos*]
Parent=Search Engines
Browser="Xyleme"

[Deepindex]
Parent=Search Engines
Browser="Deepindex"

[DiamondBot]
Parent=Search Engines
Browser="DiamondBot"

[DuckDuckBot/*; (?http://duckduckgo.com/duckduckbot.html)]
Parent=Search Engines
Browser="DuckDuckBot"

[Dumbot*]
Parent=Search Engines
Browser="Dumbot"
Version=0.2
MajorVer=0
MinorVer=2
Beta=true

[Eule?Robot*]
Parent=Search Engines
Browser="Eule-Robot"

[Faxobot/*]
Parent=Search Engines
Browser="Faxo"

[Filangy/*]
Parent=Search Engines
Browser="Filangy"

[flatlandbot/*]
Parent=Search Engines
Browser="Flatland"

[Fooky.com/ScorpionBot/ScoutOut;*]
Parent=Search Engines
Browser="ScorpionBot"
isBanned=true

[FyberSpider*]
Parent=Search Engines
Browser="FyberSpider"
isBanned=true

[Gaisbot/*]
Parent=Search Engines
Browser="Gaisbot"

[gazz/*(<EMAIL>)]
Parent=Search Engines
Browser="gazz"

[geniebot*]
Parent=Search Engines
Browser="GenieKnows"

[GOFORITBOT (?http://www.goforit.com/about/?)]
Parent=Search Engines
Browser="GoForIt"

[GoGuidesBot/*]
Parent=Search Engines
Browser="GoGuidesBot"

[GroschoBot/*]
Parent=Search Engines
Browser="GroschoBot"

[GurujiBot/1.*]
Parent=Search Engines
Browser="GurujiBot"
isBanned=true

[HenryTheMiragoRobot*]
Parent=Search Engines
Browser="Mirago"

[HolmesBot (http://holmes.ge)]
Parent=Search Engines
Browser="HolmesBot"

[Hotzonu/*]
Parent=Search Engines
Browser="Hotzonu"

[HyperEstraier/*]
Parent=Search Engines
Browser="HyperEstraier"
isBanned=true

[i1searchbot/*]
Parent=Search Engines
Browser="i1searchbot"

[IIITBOT/1.*]
Parent=Search Engines
Browser="Indian Language Web Search Engine"

[Iltrovatore-?etaccio/*]
Parent=Search Engines
Browser="Iltrovatore-Setaccio"

[InfociousBot (?http://corp.infocious.com/tech_crawler.php)]
Parent=Search Engines
Browser="InfociousBot"
isBanned=true

[Infoseek SideWinder/*]
Parent=Search Engines
Browser="Infoseek"

[iSEEKbot/*]
Parent=Search Engines
Browser="iSEEKbot"

[Knight/0.? (Zook Knight; http://knight.zook.in/; <EMAIL>)]
Parent=Search Engines
Browser="Knight"

[Kolinka Forum Search (www.kolinka.com)]
Parent=Search Engines
Browser="Kolinka Forum Search"
isBanned=true

[KRetrieve/]
Parent=Search Engines
Browser="KRetrieve"
isBanned=true

[LapozzBot/*]
Parent=Search Engines
Browser="LapozzBot"

[Linguee Bot (http://www.linguee.com/bot; <EMAIL>)]
Parent=Search Engines
Browser="Linguee Bot"

[Linknzbot*]
Parent=Search Engines
Browser="Linknzbot"

[LocalcomBot/*]
Parent=Search Engines
Browser="LocalcomBot"

[Mail.Ru/1.0]
Parent=Search Engines
Browser="Mail.Ru"

[MaSagool/*]
Parent=Search Engines
Browser="Sagoo"
Version=1.0
MajorVer=1
MinorVer=0

[miniRank/*]
Parent=Search Engines
Browser="miniRank"

[Mnogosearch*]
Parent=Search Engines
Browser="Mnogosearch"

[Mozilla/0.9* no dos :) (Linux*)]
Parent=Search Engines
Browser="goliat"
isBanned=true

[Mozilla/4.0 (compatible; *Vagabondo/*; webcrawler at wise-guys dot nl; *)]
Parent=Search Engines
Browser="Vagabondo"

[Mozilla/4.0 (compatible; Arachmo)]
Parent=Search Engines
Browser="Arachmo"

[Mozilla/4.0 (compatible; http://search.thunderstone.com/texis/websearch/about.html)]
Parent=Search Engines
Browser="ThunderStone"
isBanned=true

[Mozilla/4.0 (compatible; MSIE *; Windows NT; Girafabot; girafabot at girafa dot com; http://www.girafa.com)]
Parent=Search Engines
Browser="Girafabot"
Win32=true

[Mozilla/4.0(?compatible; MSIE 6.0; Qihoo *)]
Parent=Search Engines
Browser="Qihoo"

[Mozilla/4.7 (compatible; WhizBang; http://www.whizbang.com/crawler)]
Parent=Search Engines
Browser="Inxight Software"

[Mozilla/5.0 (*) VoilaBot*]
Parent=Search Engines
Browser="VoilaBot"
isBanned=true

[Mozilla/5.0 (compatible; ActiveTouristBot*; http://www.activetourist.com)]
Parent=Search Engines
Browser="ActiveTouristBot"

[Mozilla/5.0 (compatible; ayna-crawler*)]
Parent=Search Engines
Browser="ayna-crawler"

[Mozilla/5.0 (compatible; Butterfly/1.0; *)*]
Parent=Search Engines
Browser="Butterfly"

[Mozilla/5.0 (compatible; Charlotte/*; *)]
Parent=Search Engines
Browser="Charlotte"
Beta=true
isBanned=true

[Mozilla/5.0 (compatible; CXL-FatAssANT*)]
Parent=Search Engines
Browser="FatAssANT"

[Mozilla/5.0 (compatible; DBLBot/1.0; ?http://www.dontbuylists.com/)]
Parent=Search Engines
Browser="DBLBot"
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (compatible; EARTHCOM.info/*)]
Parent=Search Engines
Browser="EARTHCOM"

[Mozilla/5.0 (compatible; Lipperhey Spider; http://www.lipperhey.com/)]
Parent=Search Engines
Browser="Lipperhey Spider"

[Mozilla/5.0 (compatible; MojeekBot/*; http://www.mojeek.com/bot.html)]
Parent=Search Engines
Browser="MojeekBot"

[Mozilla/5.0 (compatible; NLCrawler/*]
Parent=Search Engines
Browser="Northern Light Web Search"

[Mozilla/5.0 (compatible; OsO;*]
Parent=Search Engines
Browser="Octopodus"
isBanned=true

[Mozilla/5.0 (compatible; ParchBot/1.0;*)]
Parent=Search Engines
Browser="ParchBot"

[Mozilla/5.0 (compatible; Pogodak.*)]
Parent=Search Engines
Browser="Pogodak"

[Mozilla/5.0 (compatible; Quantcastbot/1.*)]
Parent=Search Engines
Browser="Quantcastbot"

[Mozilla/5.0 (compatible; ScoutJet; +http://www.scoutjet.com/)]
Parent=Search Engines
Browser="ScoutJet"
isBanned=true

[Mozilla/5.0 (compatible; Scrubby/*; +http://www.scrubtheweb.com/abs/meta-check.html)]
Parent=Search Engines
Browser="Scrubby"
isBanned=true

[Mozilla/5.0 (compatible; YoudaoBot/1.*; http://www.youdao.com/help/webmaster/spider/*)]
Parent=Search Engines
Browser="YoudaoBot"
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (Twiceler*)]
Parent=Search Engines
Browser="Twiceler"
isBanned=true

[Mozilla/5.0 CostaCider Search*]
Parent=Search Engines
Browser="CostaCider Search"

[Mozilla/5.0 GurujiBot/1.0 (*)]
Parent=Search Engines
Browser="GurujiBot"

[NavissoBot]
Parent=Search Engines
Browser="NavissoBot"

[NextGenSearchBot*(for information visit *)]
Parent=Search Engines
Browser="ZoomInfo"
isBanned=true

[Norbert the Spider(Burf.com)]
Parent=Search Engines
Browser="Norbert the Spider"

[NuSearch Spider*]
Parent=Search Engines
Browser="nuSearch"

[ObjectsSearch/*]
Parent=Search Engines
Browser="ObjectsSearch"

[OOZBOT/0.20 ( http://www.setooz.com/oozbot.html ; agentname at setooz dot_com )]
Parent=Search Engines
Browser="Setooz"

[OpenISearch/1.*]
Parent=Search Engines
Browser="OpenISearch (Amazon)"

[Pagebull http://www.pagebull.com/]
Parent=Search Engines
Browser="Pagebull"

[PEERbot*]
Parent=Search Engines
Browser="PEERbot"

[Pompos/*]
Parent=Search Engines
Browser="Pompos"

[Popdexter/*]
Parent=Search Engines
Browser="Popdex"

[Qweery*]
Parent=Search Engines
Browser="QweeryBot"

[RedCell/* (*)]
Parent=Search Engines
Browser="RedCell"

[SaladSpoon/ShopSalad 1.* (Search Engine crawler for ShopSalad.com; *; <EMAIL>)]
Parent=Search Engines
Browser="ShopSalad"

[Scrubby/*]
Parent=Search Engines
Browser="Scrub The Web"

[Search-10/*]
Parent=Search Engines
Browser="Search-10"

[search.ch*]
Parent=Search Engines
Browser="Swiss Search Engine"

[Searchmee! Spider*]
Parent=Search Engines
Browser="Searchmee!"

[Seekbot/*]
Parent=Search Engines
Browser="Seekbot"

[SiteSpider]
Parent=Search Engines
Browser="SiteSpider"

[Sosospider?(+http://help.soso.com/webspider.htm)]
Parent=Search Engines
Browser="Sosospider"

[Spinne/*]
Parent=Search Engines
Browser="Spinne"

[sproose/*]
Parent=Search Engines
Browser="Sproose"

[Sqeobot/0.*]
Parent=Search Engines
Browser="Branzel"
isBanned=true

[SquigglebotBot/*]
Parent=Search Engines
Browser="SquigglebotBot"
isBanned=true

[StackRambler/*]
Parent=Search Engines
Browser="StackRambler"

[SygolBot*]
Parent=Search Engines
Browser="SygolBot"

[SynoBot]
Parent=Search Engines
Browser="SynoBot"

[Szukacz/*]
Parent=Search Engines
Browser="Szukacz"

[Tarantula/*]
Parent=Search Engines
Browser="Tarantula"
isBanned=true

[TerrawizBot/*]
Parent=Search Engines
Browser="TerrawizBot"
isBanned=true

[Tkensaku/*]
Parent=Search Engines
Browser="Tkensaku"

[TMCrawler]
Parent=Search Engines
Browser="TMCrawler"
isBanned=true

[TwengaBot-Discover (http://www.twenga.fr/bot-discover.html)]
Parent=Search Engines
Browser="TwengaBot-Discover"

[Twingly Recon]
Parent=Search Engines
Browser="Twingly Recon"
isBanned=true

[updated/*]
Parent=Search Engines
Browser="Updated!"

[URL Spider Pro/*]
Parent=Search Engines
Browser="URL Spider Pro"

[URL Spider SQL*]
Parent=Search Engines
Browser="Innerprise Enterprise Search"

[VMBot/*]
Parent=Search Engines
Browser="VMBot"

[voyager/2.0 (http://www.kosmix.com/html/crawler.html)]
Parent=Search Engines
Browser="Voyager"

[wadaino.jp-crawler*]
Parent=Search Engines
Browser="wadaino.jp"
isBanned=true

[WebAlta Crawler/*]
Parent=Search Engines
Browser="WebAlta Crawler"
isBanned=true

[WebCorp/*]
Parent=Search Engines
Browser="WebCorp"
isBanned=true

[webcrawl.net]
Parent=Search Engines
Browser="webcrawl.net"

[WISEbot/*]
Parent=Search Engines
Browser="WISEbot"
isBanned=true

[Wotbox/*]
Parent=Search Engines
Browser="Wotbox"

[www.zatka.com]
Parent=Search Engines
Browser="Zatka"

[WWWeasel Robot v*]
Parent=Search Engines
Browser="World Wide Weasel"

[YadowsCrawler*]
Parent=Search Engines
Browser="YadowsCrawler"

[YodaoBot/*]
Parent=Search Engines
Browser="YodaoBot"
isBanned=true

[ZeBot_www.ze.bz*]
Parent=Search Engines
Browser="ZE.bz"

[zibber-v*]
Parent=Search Engines
Browser="Zibb"

[ZipppBot/*]
Parent=Search Engines
Browser="ZipppBot"

[ATA-Translation-Service]
Parent=Translators
Browser="ATA-Translation-Service"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Hatena

[Hatena]
Parent=DefaultProperties
Browser="Hatena"
isBanned=true
Crawler=true

[Feed::Find/*]
Parent=Hatena
Browser="Feed Find"
isSyndicationReader=true

[Hatena Antenna/*]
Parent=Hatena
Browser="Hatena Antenna"

[Hatena Bookmark/*]
Parent=Hatena
Browser="Hatena Bookmark"

[Hatena RSS/*]
Parent=Hatena
Browser="Hatena RSS"
isSyndicationReader=true

[Hatena::Crawler/*]
Parent=Hatena
Browser="Hatena Crawler"

[HatenaScreenshot*]
Parent=Hatena
Browser="HatenaScreenshot"

[URI::Fetch/*]
Parent=Hatena
Browser="URI::Fetch"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Internet Archive

[Internet Archive]
Parent=DefaultProperties
Browser="Internet Archive"
Frames=true
IFrames=true
Tables=true
isBanned=true
Crawler=true

[*heritrix*]
Parent=Internet Archive
Browser="Heritrix"
isBanned=true

[ia_archiver*]
Parent=Internet Archive
Browser="Internet Archive"

[InternetArchive/*]
Parent=Internet Archive
Browser="InternetArchive"

[Mozilla/5.0 (compatible; archive.org_bot*)]
Parent=Internet Archive

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Nutch

[Nutch]
Parent=DefaultProperties
Browser="Nutch"
Frames=true
Tables=true
isBanned=true
Crawler=true

[*Nutch*]
Parent=Nutch
isBanned=true

[CazoodleBot/*]
Parent=Nutch
Browser="CazoodleBot"

[LOOQ/0.1*]
Parent=Nutch
Browser="LOOQ"

[Nutch/0.? (OpenX Spider)]
Parent=Nutch

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Webaroo

[Webaroo]
Parent=DefaultProperties
Browser="Webaroo"
Frames=true
Tables=true
Crawler=true

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Webaroo/*)]
Parent=Webaroo
Browser="Webaroo"

[Mozilla/5.0 (Windows; U; Windows *; *; rv:*) Gecko/* Firefox/* webaroo/*]
Parent=Webaroo
Browser="Webaroo"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Word Press

[Word Press]
Parent=DefaultProperties
Browser="Word Press"
Frames=true
Tables=true
isBanned=true
Crawler=true

[WordPress-B-/2.*]
Parent=Word Press
Browser="WordPress-B"

[WordPress-Do-P-/2.*]
Parent=Word Press
Browser="WordPress-Do-P"

[BlueCoat ProxySG]
Parent=Blue Coat Systems
Browser="BlueCoat ProxySG"

[CerberianDrtrs/*]
Parent=Blue Coat Systems
Browser="Cerberian"

[Inne: Mozilla/4.0 (compatible; Cerberian Drtrs*)]
Parent=Blue Coat Systems
Browser="Cerberian"

[Mozilla/4.0 (compatible; Cerberian Drtrs*)]
Parent=Blue Coat Systems
Browser="Cerberian"

[Mozilla/4.0 (compatible; MSIE 6.0; Bluecoat DRTR)]
Parent=Blue Coat Systems
Browser="Bluecoat"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Copyright/Plagiarism

[Copyright/Plagiarism]
Parent=DefaultProperties
Browser="Copyright/Plagiarism"
isBanned=true
Crawler=true

[BDFetch]
Parent=Copyright/Plagiarism
Browser="BDFetch"

[copyright sheriff (*)]
Parent=Copyright/Plagiarism
Browser="copyright sheriff"

[CopyRightCheck*]
Parent=Copyright/Plagiarism
Browser="CopyRightCheck"

[FairAd Client*]
Parent=Copyright/Plagiarism
Browser="FairAd Client"

[iCopyright Conductor*]
Parent=Copyright/Plagiarism
Browser="iCopyright Conductor"

[IPiumBot laurion(dot)com]
Parent=Copyright/Plagiarism
Browser="IPiumBot"

[IWAgent/*]
Parent=Copyright/Plagiarism
Browser="Brand Protect"

[Mozilla/5.0 (compatible; DKIMRepBot/*)]
Parent=Copyright/Plagiarism
Browser="DKIMRepBot"

[oBot]
Parent=Copyright/Plagiarism
Browser="oBot"

[SlySearch/*]
Parent=Copyright/Plagiarism
Browser="SlySearch"

[TurnitinBot/*]
Parent=Copyright/Plagiarism
Browser="TurnitinBot"

[TutorGigBot/*]
Parent=Copyright/Plagiarism
Browser="TutorGig"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; DNS Tools

[DNS Tools]
Parent=DefaultProperties
Browser="DNS Tools"
Crawler=true

[Domain Dossier utility*]
Parent=DNS Tools
Browser="Domain Dossier"

[Mozilla/5.0 (compatible; DNS-Digger/*)]
Parent=DNS Tools
Browser="DNS-Digger"

[OpenDNS <NAME_EMAIL>]
Parent=DNS Tools
Browser="OpenDNS Domain Crawler"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Download Managers

[Download Managers]
Parent=DefaultProperties
Browser="Download Managers"
isBanned=true
Crawler=true

[A1 Website Download/1.* (*) miggibot]
Parent=Download Managers
Browser="A1 Website Download"

[AndroidDownloadManager]
Parent=Download Managers
Browser="Android Download Manager"

[AutoMate5]
Parent=Download Managers
Browser="AutoMate5"

[Beamer*]
Parent=Download Managers
Browser="Beamer"

[BitBeamer/*]
Parent=Download Managers
Browser="BitBeamer"

[BitTorrent/*]
Parent=Download Managers
Browser="BitTorrent"

[DA *]
Parent=Download Managers
Browser="Download Accelerator"

[Download Demon*]
Parent=Download Managers
Browser="Download Demon"

[Download Express*]
Parent=Download Managers
Browser="Download Express"

[Download Master*]
Parent=Download Managers
Browser="Download Master"

[Download Ninja*]
Parent=Download Managers
Browser="Download Ninja"

[Download Wonder*]
Parent=Download Managers
Browser="Download Wonder"

[DownloadSession*]
Parent=Download Managers
Browser="DownloadSession"

[EasyDL/*]
Parent=Download Managers
Browser="EasyDL"

[FDM 1.x]
Parent=Download Managers
Browser="Free Download Manager"

[FlashGet]
Parent=Download Managers
Browser="FlashGet"

[FreshDownload/*]
Parent=Download Managers
Browser="FreshDownload"

[GetRight/*]
Parent=Download Managers
Browser="GetRight"

[GetRightPro/*]
Parent=Download Managers
Browser="GetRightPro"

[GetSmart/*]
Parent=Download Managers
Browser="GetSmart"

[Go!Zilla*]
Parent=Download Managers
Browser="GoZilla"

[Gozilla/*]
Parent=Download Managers
Browser="Gozilla"

[Internet Ninja*]
Parent=Download Managers
Browser="Internet Ninja"

[Kontiki Client*]
Parent=Download Managers
Browser="Kontiki Client"

[lftp/3.2.1]
Parent=Download Managers
Browser="lftp"

[LightningDownload/*]
Parent=Download Managers
Browser="LightningDownload"

[LMQueueBot/*]
Parent=Download Managers
Browser="LMQueueBot"

[MetaProducts Download Express/*]
Parent=Download Managers
Browser="Download Express"

[Mozilla/4.0 (compatible; Getleft*)]
Parent=Download Managers
Browser="Getleft"

[Myzilla]
Parent=Download Managers
Browser="Myzilla"

[Net Vampire/*]
Parent=Download Managers
Browser="Net Vampire"

[Net_Vampire*]
Parent=Download Managers
Browser="Net_Vampire"

[NetAnts*]
Parent=Download Managers
Browser="NetAnts"

[NetPumper*]
Parent=Download Managers
Browser="NetPumper"

[NetSucker*]
Parent=Download Managers
Browser="NetSucker"

[NetZip Downloader*]
Parent=Download Managers
Browser="NetZip Downloader"

[NexTools WebAgent*]
Parent=Download Managers
Browser="NexTools WebAgent"

[Offline Downloader*]
Parent=Download Managers
Browser="Offline Downloader"

[P3P Client]
Parent=Download Managers
Browser="P3P Client"

[PageDown*]
Parent=Download Managers
Browser="PageDown"

[PicaLoader*]
Parent=Download Managers
Browser="PicaLoader"

[Prozilla*]
Parent=Download Managers
Browser="Prozilla"

[RealDownload/*]
Parent=Download Managers
Browser="RealDownload"

[sEasyDL/*]
Parent=Download Managers
Browser="EasyDL"

[shareaza*]
Parent=Download Managers
Browser="shareaza"

[SmartDownload/*]
Parent=Download Managers
Browser="SmartDownload"

[SpeedDownload/*]
Parent=Download Managers
Browser="Speed Download"

[Star*Downloader/*]
Parent=Download Managers
Browser="StarDownloader"

[STEROID Download]
Parent=Download Managers
Browser="STEROID Download"

[SuperBot/*]
Parent=Download Managers
Browser="SuperBot"

[Vegas95/*]
Parent=Download Managers
Browser="Vegas95"

[WebZIP*]
Parent=Download Managers
Browser="WebZIP"

[Wget*]
Parent=Download Managers
Browser="Wget"

[WinTools]
Parent=Download Managers
Browser="WinTools"

[Xaldon WebSpider*]
Parent=Download Managers
Browser="Xaldon WebSpider"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; E-Mail Harvesters

[E-Mail Harvesters]
Parent=DefaultProperties
Browser="E-Mail Harvesters"
isBanned=true
Crawler=true

[*E-Mail Address Extractor*]
Parent=E-Mail Harvesters
Browser="E-Mail Address Extractor"

[*Larbin*]
Parent=E-Mail Harvesters
Browser="Larbin"

[*www4mail/*]
Parent=E-Mail Harvesters
Browser="www4mail"

[8484 Boston Project*]
Parent=E-Mail Harvesters
Browser="8484 Boston Project"

[Atomic_Email]
Parent=E-Mail Harvesters
Browser="Atomic_Email"

[Atomic_Email_Hunter/*]
Parent=E-Mail Harvesters
Browser="Atomic Email Hunter"

[CherryPicker*/*]
Parent=E-Mail Harvesters
Browser="CherryPickerElite"

[Chilkat/*]
Parent=E-Mail Harvesters
Browser="Chilkat"

[ContactBot/*]
Parent=E-Mail Harvesters
Browser="ContactBot"

[eCatch*]
Parent=E-Mail Harvesters
Browser="eCatch"

[EmailCollector*]
Parent=E-Mail Harvesters
Browser="E-Mail Collector"

[EMAILsearcher]
Parent=E-Mail Harvesters
Browser="EMAILsearcher"

[EmailSiphon*]
Parent=E-Mail Harvesters
Browser="E-Mail Siphon"

[EmailWolf*]
Parent=E-Mail Harvesters
Browser="EMailWolf"

[Epsilon SoftWorks' MailMunky]
Parent=E-Mail Harvesters
Browser="MailMunky"

[ExtractorPro*]
Parent=E-Mail Harvesters
Browser="ExtractorPro"

[Franklin Locator*]
Parent=E-Mail Harvesters
Browser="Franklin Locator"

[Missigua Locator*]
Parent=E-Mail Harvesters
Browser="Missigua Locator"

[Mozilla/4.0 (compatible; Advanced Email Extractor*)]
Parent=E-Mail Harvesters
Browser="Advanced Email Extractor"

[Netprospector*]
Parent=E-Mail Harvesters
Browser="Netprospector"

[ProWebWalker*]
Parent=E-Mail Harvesters
Browser="ProWebWalker"

[sna-0.0.*]
Parent=E-Mail Harvesters
Browser="Mike Elliott's E-Mail Harvester"

[WebEnhancer*]
Parent=E-Mail Harvesters
Browser="WebEnhancer"

[WebMiner*]
Parent=E-Mail Harvesters
Browser="WebMiner"

[ZIBB Crawler (email address / WWW address)]
Parent=E-Mail Harvesters
Browser="ZIBB Crawler"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Feeds Blogs

[Feeds Blogs]
Parent=DefaultProperties
Browser="Feeds Blogs"
isSyndicationReader=true
Crawler=true

[Bloglines Title Fetch/*]
Parent=Feeds Blogs
Browser="Bloglines Title Fetch"

[Bloglines/* (http://www.bloglines.com*)]
Parent=Feeds Blogs
Browser="BlogLines Web"

[BlogPulse (ISSpider-3.*)]
Parent=Feeds Blogs
Browser="BlogPulse"

[BlogPulseLive (<EMAIL>)]
Parent=Feeds Blogs
Browser="BlogPulseLive"

[blogsearchbot-pumpkin-2]
Parent=Feeds Blogs
Browser="blogsearchbot-pumpkin"
isSyndicationReader=false

[Irish Blogs Aggregator/*1.0*]
Parent=Feeds Blogs
Browser="Irish Blogs Aggregator"
Version=1.0
MajorVer=1
MinorVer=0

[kinjabot (http://www.kinja.com; *)]
Parent=Feeds Blogs
Browser="kinjabot"

[Net::Trackback/*]
Parent=Feeds Blogs
Browser="Net::Trackback"

[Reblog*]
Parent=Feeds Blogs
Browser="Reblog"

[WordPress/*]
Parent=Feeds Blogs
Browser="WordPress"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Feeds Syndicators

[Feeds Syndicators]
Parent=DefaultProperties
Browser="Feeds Syndicators"
isSyndicationReader=true
Crawler=true

[*LinkLint*]
Parent=Feeds Syndicators
Browser="LinkLint"

[*NetNewsWire/*]
Parent=Feeds Syndicators
Browser="NetNewsWire"

[*NetVisualize*]
Parent=Feeds Syndicators
Browser="NetVisualize"

[AideRSS 2.* (postrank.com)]
Parent=Feeds Syndicators
Browser="AideRSS"

[AideRSS/2.0 (aiderss.com)]
Parent=Feeds Syndicators
Browser="AideRSS"
isBanned=true

[Akregator/*]
Parent=Feeds Syndicators
Browser="Akregator"

[Apple-PubSub/*]
Parent=Feeds Syndicators
Browser="Apple-PubSub"

[AppleSyndication/*]
Parent=Feeds Syndicators
Browser="Safari RSS"
Platform=MacOSX

[Cocoal.icio.us/* (*)*]
Parent=Feeds Syndicators
Browser="Cocoal.icio.us"
isBanned=true

[Feed43 Proxy/* (*)]
Parent=Feeds Syndicators
Browser="Feed For Free"

[FeedBurner/*]
Parent=Feeds Syndicators
Browser="FeedBurner"

[FeedDemon/* (*)]
Parent=Feeds Syndicators
Browser="FeedDemon"
Platform=Win32

[FeedDigest/* (*)]
Parent=Feeds Syndicators
Browser="FeedDigest"

[FeedGhost/1.*]
Parent=Feeds Syndicators
Browser="FeedGhost"
Version=1.0
MajorVer=1
MinorVer=0

[FeedOnFeeds/0.1.* ( http://minutillo.com/steve/feedonfeeds/)]
Parent=Feeds Syndicators
Browser="FeedOnFeeds"
Version=0.1
MajorVer=0
MinorVer=1

[Feedreader * (Powered by Newsbrain)]
Parent=Feeds Syndicators
Browser="Newsbrain"

[Feedshow/* (*)]
Parent=Feeds Syndicators
Browser="Feedshow"

[Feedster Crawler/?.0; Feedster, Inc.]
Parent=Feeds Syndicators
Browser="Feedster"

[GreatNews/1.0]
Parent=Feeds Syndicators
Browser="GreatNews"
Version=1.0
MajorVer=1
MinorVer=0

[Gregarius/*]
Parent=Feeds Syndicators
Browser="Gregarius"

[intraVnews/*]
Parent=Feeds Syndicators
Browser="intraVnews"

[JetBrains Omea Reader*]
Parent=Feeds Syndicators
Browser="Omea Reader"
isBanned=true

[Liferea/1.* (Linux; *; http://liferea.sf.net/)]
Parent=Feeds Syndicators
Browser="Liferea"
isBanned=true

[livedoor FeedFetcher/0.0* (http://reader.livedoor.com/;*)]
Parent=Feeds Syndicators
Browser="FeedFetcher"
Version=0.0
MajorVer=0
MinorVer=0

[MagpieRSS/* (*)]
Parent=Feeds Syndicators
Browser="MagpieRSS"

[Mobitype * (compatible; Mozilla/*; MSIE *.*; Windows *)]
Parent=Feeds Syndicators
Browser="Mobitype"
Platform=Win32

[Mozilla/5.0 (*; Rojo *; http://www.rojo.com/corporate/help/agg; *)*]
Parent=Feeds Syndicators
Browser="Rojo"

[Mozilla/5.0 (*aggregator:TailRank; http://tailrank.com/robot)*]
Parent=Feeds Syndicators
Browser="TailRank"

[Mozilla/5.0 (compatible; MSIE 6.0; Podtech Network; <EMAIL>)]
Parent=Feeds Syndicators
Browser="Podtech Network"

[Mozilla/5.0 (compatible; Newz Crawler *; http://www.newzcrawler.com/?)]
Parent=Feeds Syndicators
Browser="Newz Crawler"

[Mozilla/5.0 (compatible; RSSMicro.com RSS/Atom Feed Robot)]
Parent=Feeds Syndicators
Browser="RSSMicro"

[Mozilla/5.0 (compatible;*newstin.com;*)]
Parent=Feeds Syndicators
Browser="NewsTin"

[Mozilla/5.0 (RSS Reader Panel)]
Parent=Feeds Syndicators
Browser="RSS Reader Panel"

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.*; aggregator:FeedParser; *) Gecko/*]
Parent=Feeds Syndicators
Browser="FeedParser"

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.*; aggregator:NewsMonster; *) Gecko/*]
Parent=Feeds Syndicators
Browser="NewsMonster"

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.*; aggregator:Rojo; *) Gecko/*]
Parent=Feeds Syndicators
Browser="Rojo"

[Mozilla/5.0 NewsFox/*]
Parent=Feeds Syndicators
Browser="NewsFox"

[Netvibes (*)]
Parent=Feeds Syndicators
Browser="Netvibes"

[NewsAlloy/* (*)]
Parent=Feeds Syndicators
Browser="NewsAlloy"

[Omnipelagos*]
Parent=Feeds Syndicators
Browser="Omnipelagos"

[Particls]
Parent=Feeds Syndicators
Browser="Particls"

[Protopage/* (*)]
Parent=Feeds Syndicators
Browser="Protopage"

[PubSub-RSS-Reader/* (*)]
Parent=Feeds Syndicators
Browser="PubSub-RSS-Reader"

[RSS Menu/*]
Parent=Feeds Syndicators
Browser="RSS Menu"

[RssBandit/*]
Parent=Feeds Syndicators
Browser="RssBandit"

[RssBar/1.2*]
Parent=Feeds Syndicators
Browser="RssBar"
Version=1.2
MajorVer=1
MinorVer=2

[SharpReader/*]
Parent=Feeds Syndicators
Browser="SharpReader"

[SimplePie/*]
Parent=Feeds Syndicators
Browser="SimplePie"

[Strategic Board Bot (?http://www.strategicboard.com)]
Parent=Feeds Syndicators
Browser="Strategic Board Bot"
isBanned=true

[TargetYourNews.com bot]
Parent=Feeds Syndicators
Browser="TargetYourNews"

[Technoratibot/*]
Parent=Feeds Syndicators
Browser="Technoratibot"

[Tumblr/* RSS syndication ( http://www.tumblr.com/) (<EMAIL>)]
Parent=Feeds Syndicators
Browser="Tumblr RSS syndication"

[Windows-RSS-Platform/1.0*]
Parent=Feeds Syndicators
Browser="Windows-RSS-Platform"
Version=1.0
MajorVer=1
MinorVer=0
Win32=true

[Wizz RSS News Reader]
Parent=Feeds Syndicators
Browser="Wizz"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; General RSS

[General RSS]
Parent=DefaultProperties
Browser="General RSS"
isSyndicationReader=true
Crawler=true

[AideRSS/1.0 (aiderss.com); * subscribers]
Parent=General RSS
Browser="AideRSS"
Version=1.0
MajorVer=1
MinorVer=0

[BlijbolReallySimpleAggregator/2.0*]
Parent=General RSS
Browser="BlijbolReallySimpleAggregator"

[CC Metadata Scaper http://wiki.creativecommons.org/Metadata_Scraper]
Parent=General RSS
Browser="CC Metadata Scaper"

[Mozilla/5.0 (compatible) GM RSS Panel]
Parent=General RSS
Browser="RSS Panel"

[Mozilla/5.0 http://www.inclue.com; <EMAIL>]
Parent=General RSS
Browser="Inclue"

[Runnk online rss reader : http://www.runnk.com/ : RSS favorites : RSS ranking : RSS aggregator*]
Parent=General RSS
Browser="Ruunk"

[UniversalFeedParser/4.* +http://feedparser.org/]
Parent=General RSS
Browser="UniversalFeedParser"

[Windows-RSS-Platform/2.0 (MSIE ?.0; Windows NT *.*)]
Parent=General RSS
Browser="Windows-RSS-Platform"
Version=2.0
MajorVer=2
MinorVer=0
Platform=Win32

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Validation Checkers

[HTML Validators]
Parent=DefaultProperties
Browser="HTML Validators"
Crawler=true

[(HTML Validator http://www.searchengineworld.com/validator/)]
Parent=HTML Validators
Browser="Search Engine World HTML Validator"

[FeedValidator/*]
Parent=HTML Validators
Browser="FeedValidator"

[Search Engine World Robots.txt Validator*]
Parent=HTML Validators
Browser="Search Engine World Robots.txt Validator"

[Weblide/*]
Parent=HTML Validators
Browser="Weblide"
Beta=true

[WebmasterWorld StickyMail Server Header Checker*]
Parent=HTML Validators
Browser="WebmasterWorld Server Header Checker"

[WWWC/*]
Parent=HTML Validators
Browser="WWWC"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Image Crawlers

[Image Crawlers]
Parent=DefaultProperties
Browser="Image Crawlers"
isBanned=true
Crawler=true

[*CFNetwork*]
Parent=Image Crawlers
Browser="CFNetwork"

[*PhotoStickies/*]
Parent=Image Crawlers
Browser="PhotoStickies"

[Camcrawler*]
Parent=Image Crawlers
Browser="Camcrawler"

[CydralSpider/*]
Parent=Image Crawlers
Browser="Cydral Web Image Search"
isBanned=true

[Der gro\xdfe BilderSauger*]
Parent=Image Crawlers
Browser="Gallery Grabber"

[Extreme Picture Finder]
Parent=Image Crawlers
Browser="Extreme Picture Finder"

[FLATARTS_FAVICO]
Parent=Image Crawlers
Browser="FlatArts Favorites Icon Tool"

[HTML2JPG Blackbox, http://www.html2jpg.com]
Parent=Image Crawlers
Browser="HTML2JPG"

[IconSurf/2.*]
Parent=Image Crawlers
Browser="IconSurf"

[Mister PIX*]
Parent=Image Crawlers
Browser="Mister PIX"

[Mozilla/5.0 (compatible; KaloogaBot; http://www.kalooga.com/info.html?page=crawler)]
Parent=Image Crawlers
Browser="KaloogaBot"

[Mozilla/5.0 (Macintosh; U; *Mac OS X; *) AppleWebKit/* (*) Pandora/2.*]
Parent=Image Crawlers
Browser="Pandora"

[naoFavicon4IE*]
Parent=Image Crawlers
Browser="naoFavicon4IE"

[pixfinder/*]
Parent=Image Crawlers
Browser="pixfinder"

[psbot/* (?http://www.picsearch.com/bot.html)]
Parent=Image Crawlers
Browser="PicSearchBot"

[rssImagesBot/0.1 (*http://herbert.groot.jebbink.nl/?app=rssImages)]
Parent=Image Crawlers
Browser="rssImagesBot"

[Web Image Collector*]
Parent=Image Crawlers
Browser="Web Image Collector"

[WebImages * (?http://herbert.groot.jebbink.nl/?app=WebImages?)]
Parent=Image Crawlers
Browser="WebImages"

[WebPix*]
Parent=Image Crawlers
Browser="Custo"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Link Checkers

[Link Checkers]
Parent=DefaultProperties
Browser="Link Checkers"
Crawler=true

[!Susie (http://www.sync2it.com/susie)]
Parent=Link Checkers
Browser="!Susie"

[*AgentName/*]
Parent=Link Checkers
Browser="AgentName"

[*Linkman*]
Parent=Link Checkers
Browser="Linkman"

[*LinksManager.com*]
Parent=Link Checkers
Browser="LinksManager"

[*Powermarks/*]
Parent=Link Checkers
Browser="Powermarks"

[*Web Link Validator*]
Parent=Link Checkers
Browser="Web Link Validator"

[*Zeus*]
Parent=Link Checkers
Browser="Zeus"
isBanned=true

[ActiveBookmark *]
Parent=Link Checkers
Browser="ActiveBookmark"

[Bookdog/*]
Parent=Link Checkers
Browser="Bookdog"

[Bookmark Buddy*]
Parent=Link Checkers
Browser="Bookmark Buddy"

[Bookmark Renewal Check Agent*]
Parent=Link Checkers
Browser="Bookmark Renewal Check Agent"

[Bookmark search tool*]
Parent=Link Checkers
Browser="Bookmark search tool"

[Bookmark-Manager]
Parent=Link Checkers
Browser="Bookmark-Manager"

[Checkbot*]
Parent=Link Checkers
Browser="Checkbot"

[CheckLinks/*]
Parent=Link Checkers
Browser="CheckLinks"

[CyberSpyder Link Test/*]
Parent=Link Checkers
Browser="CyberSpyder Link Test"

[DLC/*]
Parent=Link Checkers
Browser="DLC"

[DocWeb Link Crawler (http://doc.php.net)]
Parent=Link Checkers
Browser="DocWeb Link Crawler"

[FavOrg]
Parent=Link Checkers
Browser="FavOrg"

[Favorites Sweeper v.3.*]
Parent=Link Checkers
Browser="Favorites Sweeper"

[FindLinks/*]
Parent=Link Checkers
Browser="FindLinks"

[Funnel Web Profiler*]
Parent=Link Checkers
Browser="Funnel Web Profiler"

[Html Link Validator (www.lithopssoft.com)]
Parent=Link Checkers
Browser="HTML Link Validator"

[IECheck]
Parent=Link Checkers
Browser="IECheck"

[JCheckLinks/*]
Parent=Link Checkers
Browser="JCheckLinks"

[JRTwine Software Check Favorites Utility]
Parent=Link Checkers
Browser="JRTwine"

[Link Valet Online*]
Parent=Link Checkers
Browser="Link Valet"
isBanned=true

[LinkAlarm/*]
Parent=Link Checkers
Browser="LinkAlarm"

[Linkbot*]
Parent=Link Checkers
Browser="Linkbot"

[LinkChecker/*]
Parent=Link Checkers
Browser="LinkChecker"

[LinkextractorPro*]
Parent=Link Checkers
Browser="LinkextractorPro"
isBanned=true

[LinkLint-checkonly/*]
Parent=Link Checkers
Browser="LinkLint"

[LinkScan/*]
Parent=Link Checkers
Browser="LinkScan"

[LinkSweeper/*]
Parent=Link Checkers
Browser="LinkSweeper"

[LinkWalker*]
Parent=Link Checkers
Browser="LinkWalker"

[MetaGer-LinkChecker]
Parent=Link Checkers
Browser="MetaGer-LinkChecker"

[Mozilla/* (compatible; linktiger/*; *http://www.linktiger.com*)]
Parent=Link Checkers
Browser="LinkTiger"
isBanned=true

[Mozilla/4.0 (Compatible); URLBase*]
Parent=Link Checkers
Browser="URLBase"

[Mozilla/4.0 (compatible; Link Utility; http://net-promoter.com)]
Parent=Link Checkers
Browser="NetPromoter Link Utility"

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98) Web Link Validator*]
Parent=Link Checkers
Browser="Web Link Validator"
Win32=true

[Mozilla/4.0 (compatible; MSIE 7.0; Win32) Link Commander 3.0]
Parent=Link Checkers
Browser="Link Commander"
Version=3.0
MajorVer=3
MinorVer=0
Platform=Win32

[Mozilla/4.0 (compatible; smartBot/1.*; checking links; *)]
Parent=Link Checkers
Browser="smartBot"

[Mozilla/4.0 (compatible; SuperCleaner*;*)]
Parent=Link Checkers
Browser="SuperCleaner"

[Mozilla/5.0 gURLChecker/*]
Parent=Link Checkers
Browser="gURLChecker"
isBanned=true

[Newsgroupreporter LinkCheck]
Parent=Link Checkers
Browser="Newsgroupreporter LinkCheck"

[onCHECK Linkchecker von www.scientec.de fuer www.onsinn.de]
Parent=Link Checkers
Browser="onCHECK Linkchecker"

[online link validator (http://www.dead-links.com/)]
Parent=Link Checkers
Browser="Dead-Links.com"
isBanned=true

[REL Link Checker*]
Parent=Link Checkers
Browser="REL Link Checker"

[RLinkCheker*]
Parent=Link Checkers
Browser="RLinkCheker"

[Robozilla/*]
Parent=Link Checkers
Browser="Robozilla"

[RPT-HTTPClient/*]
Parent=Link Checkers
Browser="RPT-HTTPClient"
isBanned=true

[SafariBookmarkChecker*(?http://www.coriolis.ch/)]
Parent=Link Checkers
Browser="SafariBookmarkChecker"
Platform=MacOSX
CssVersion=2

[Simpy/* (Simpy; http://www.simpy.com/?ref=bot; feedback at simpy dot com)]
Parent=Link Checkers
Browser="Simpy"

[SiteBar/*]
Parent=Link Checkers
Browser="SiteBar"

[Susie (http://www.sync2it.com/bms/susie.php]
Parent=Link Checkers
Browser="Susie"

[URLBase/*]
Parent=Link Checkers
Browser="URLBase"

[VSE/*]
Parent=Link Checkers
Browser="VSE Link Tester"

[WebTrends Link Analyzer]
Parent=Link Checkers
Browser="WebTrends Link Analyzer"

[WorQmada/*]
Parent=Link Checkers
Browser="WorQmada"

[Xenu* Link Sleuth*]
Parent=Link Checkers
Browser="Xenu's Link Sleuth"
isBanned=true

[Z-Add Link Checker*]
Parent=Link Checkers
Browser="Z-Add Link Checker"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Microsoft

[Microsoft]
Parent=DefaultProperties
Browser="Microsoft"
isBanned=true
Crawler=true

[Live (http://www.live.com/)]
Parent=Microsoft
Browser="Microsoft Live"
isBanned=false
isSyndicationReader=true

[MFC Foundation Class Library*]
Parent=Microsoft
Browser="MFC Foundation Class Library"

[MFHttpScan]
Parent=Microsoft
Browser="MFHttpScan"

[Microsoft BITS/*]
Parent=Microsoft
Browser="BITS"

[Microsoft Data Access Internet Publishing Provider Cache Manager]
Parent=Microsoft
Browser="MS IPP"

[Microsoft Data Access Internet Publishing Provider DAV*]
Parent=Microsoft
Browser="MS IPP DAV"

[Microsoft Data Access Internet Publishing Provider Protocol Discovery]
Parent=Microsoft
Browser="MS IPPPD"

[Microsoft Internet Explorer]
Parent=Microsoft
Browser="Fake IE"

[Microsoft Office Existence Discovery]
Parent=Microsoft
Browser="Microsoft Office Existence Discovery"

[Microsoft Office Protocol Discovery]
Parent=Microsoft
Browser="MS OPD"

[Microsoft Office/* (*Picture Manager*)]
Parent=Microsoft
Browser="Microsoft Office Picture Manager"

[Microsoft URL Control*]
Parent=Microsoft
Browser="Microsoft URL Control"

[Microsoft Visio MSIE]
Parent=Microsoft
Browser="Microsoft Visio"

[Microsoft-WebDAV-MiniRedir/*]
Parent=Microsoft
Browser="Microsoft-WebDAV"

[Mozilla/5.0 (Macintosh; Intel Mac OS X) Excel/12.*]
Parent=Microsoft
Browser="Microsoft Excel"
Version=12.0
MajorVer=12
MinorVer=0
Platform=MacOSX

[MSN Feed Manager]
Parent=Microsoft
Browser="MSN Feed Manager"
isBanned=false
isSyndicationReader=true

[MSProxy/*]
Parent=Microsoft
Browser="MS Proxy"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Miscellaneous Browsers

[Miscellaneous Browsers]
Parent=DefaultProperties
Browser="Miscellaneous Browsers"
Crawler=true

[*Amiga*]
Parent=Miscellaneous Browsers
Browser="Amiga"
Platform=Amiga

[*avantbrowser*]
Parent=Miscellaneous Browsers
Browser="Avant Browser"

[12345]
Parent=Miscellaneous Browsers
Browser="12345"
isBanned=true

[1st ZipCommander (Net) - http://www.zipcommander.com/]
Parent=Miscellaneous Browsers
Browser="1st ZipCommander"

[Ace Explorer]
Parent=Miscellaneous Browsers
Browser="Ace Explorer"

[Enigma Browser*]
Parent=Miscellaneous Browsers
Browser="Enigma Browser"

[EVE-minibrowser/*]
Parent=Miscellaneous Browsers
Browser="EVE-minibrowser"
IFrames=false
Tables=false
BackgroundSounds=false
JavaScript=false
VBScript=false
JavaApplets=false
ActiveXControls=false
isBanned=false
Crawler=false

[Godzilla/* (Basic*; *; Commodore C=64; *; rv:1.*)*]
Parent=Miscellaneous Browsers
Browser="Godzilla"

[GreenBrowser]
Parent=Miscellaneous Browsers
Browser="GreenBrowser"
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Kopiczek/* (WyderOS*; *)]
Parent=Miscellaneous Browsers
Browser="Kopiczek"
Platform=WyderOS
IFrames=true
VBScript=true
JavaApplets=true
CssVersion=2

[Mozilla/* (*) - BrowseX (*)]
Parent=Miscellaneous Browsers
Browser="BrowseX"

[Mozilla/* (Win32;*Escape?*; ?)]
Parent=Miscellaneous Browsers
Browser="Escape"
Platform=Win32

[Mozilla/4.0 (compatible; ibisBrowser)]
Parent=Miscellaneous Browsers
Browser="ibisBrowser"

[Mozilla/5.0 (Macintosh; ?; PPC Mac OS X;*) AppleWebKit/* (*) HistoryHound/*]
Parent=Miscellaneous Browsers
Browser="HistoryHound"

[Mozilla/5.0 (X11; U; Linux*; *) AppleWebKit/*(KHTML, like Gecko) Safari/* Epiphany/2.30.*]
Parent=Miscellaneous Browsers
Browser="Epiphany"
Version=2.30
MajorVer=2
MinorVer=30
Platform=Linux
Win16=false
Win32=false
Win64=false
IFrames=true
VBScript=true
JavaApplets=true
CssVersion=3

[NetRecorder*]
Parent=Miscellaneous Browsers
Browser="NetRecorder"

[NetSurf*]
Parent=Miscellaneous Browsers
Browser="NetSurf"

[ogeb browser , Version 1.1.0]
Parent=Miscellaneous Browsers
Browser="ogeb browser"
Version=1.1
MajorVer=1
MinorVer=1

[SCEJ PSP BROWSER 0102pspNavigator]
Parent=Miscellaneous Browsers
Browser="Wipeout Pure"

[SlimBrowser]
Parent=Miscellaneous Browsers
Browser="SlimBrowser"

[WWW_Browser/*]
Parent=Miscellaneous Browsers
Browser="WWW Browser"
Version=1.69
MajorVer=1
MinorVer=69
Platform=Win16
CssVersion=3

[*Netcraft Webserver Survey*]
Parent=Netcraft
Browser="Netcraft Webserver Survey"
isBanned=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Offline Browsers

[Offline Browsers]
Parent=DefaultProperties
Browser="Offline Browsers"
isBanned=true
Crawler=true

[*Check&Get*]
Parent=Offline Browsers
Browser="Check&Get"

[*HTTrack*]
Parent=Offline Browsers
Browser="HTTrack"

[*MSIECrawler*]
Parent=Offline Browsers
Browser="IE Offline Browser"

[*TweakMASTER*]
Parent=Offline Browsers
Browser="TweakMASTER"

[BackStreet Browser *]
Parent=Offline Browsers
Browser="BackStreet Browser"

[Go-Ahead-Got-It*]
Parent=Offline Browsers
Browser="Go Ahead Got-It"

[iGetter/*]
Parent=Offline Browsers
Browser="iGetter"

[Teleport*]
Parent=Offline Browsers
Browser="Teleport"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Online Scanners

[Online Scanners]
Parent=DefaultProperties
Browser="Online Scanners"
isBanned=true

[JoeDog/* (X11; I; Siege *)]
Parent=Online Scanners
Browser="JoeDog"
isBanned=false

[Morfeus Fucking Scanner]
Parent=Online Scanners
Browser="Morfeus Fucking Scanner"

[Mozilla/4.0 (compatible; Trend Micro tmdr 1.*]
Parent=Online Scanners
Browser="Trend Micro"

[Titanium 2005 (4.02.01)]
Parent=Online Scanners
Browser="Panda Antivirus Titanium"

[virus_detector*]
Parent=Online Scanners
Browser="Secure Computing Corporation"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Proxy Servers

[Proxy Servers]
Parent=DefaultProperties
Browser="Proxy Servers"
isBanned=true

[*squid*]
Parent=Proxy Servers
Browser="Squid"

[Anonymisiert*]
Parent=Proxy Servers
Browser="Anonymizied"

[Anonymizer/*]
Parent=Proxy Servers
Browser="Anonymizer"

[Anonymizied*]
Parent=Proxy Servers
Browser="Anonymizied"

[Anonymous*]
Parent=Proxy Servers
Browser="Anonymous"

[Anonymous/*]
Parent=Proxy Servers
Browser="Anonymous"

[CE-Preload]
Parent=Proxy Servers
Browser="CE-Preload"

[http://Anonymouse.org/*]
Parent=Proxy Servers
Browser="Anonymouse"

[IE/6.01 (CP/M; 8-bit*)]
Parent=Proxy Servers
Browser="Squid"

[Mozilla/* (TuringOS; Turing Machine; 0.0)]
Parent=Proxy Servers
Browser="Anonymizer"

[Mozilla/4.0 (compatible; MSIE ?.0; SaferSurf*)]
Parent=Proxy Servers
Browser="SaferSurf"

[Mozilla/5.0 (compatible; del.icio.us-thumbnails/*; *) KHTML/* (like Gecko)]
Parent=Proxy Servers
Browser="Yahoo!"
isBanned=true
Crawler=true

[Nutscrape]
Parent=Proxy Servers
Browser="Squid"

[Nutscrape/* (CP/M; 8-bit*)]
Parent=Proxy Servers
Browser="Squid"

[Privoxy/*]
Parent=Proxy Servers
Browser="Privoxy"

[ProxyTester*]
Parent=Proxy Servers
Browser="ProxyTester"
isBanned=true
Crawler=true

[SilentSurf*]
Parent=Proxy Servers
Browser="SilentSurf"

[SmallProxy*]
Parent=Proxy Servers
Browser="SmallProxy"

[Space*Bison/*]
Parent=Proxy Servers
Browser="Proxomitron"

[Sqworm/*]
Parent=Proxy Servers
Browser="Websense"

[SurfControl]
Parent=Proxy Servers
Browser="SurfControl"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Research Projects

[Research Projects]
Parent=DefaultProperties
Browser="Research Projects"
isBanned=true
Crawler=true

[*research*]
Parent=Research Projects
Browser="Generic Research Crawler"

[AcadiaUniversityWebCensusClient]
Parent=Research Projects
Browser="AcadiaUniversityWebCensusClient"

[Amico Alpha * (*) Gecko/* AmicoAlpha/*]
Parent=Research Projects
Browser="Amico Alpha"

[annotate_google; http://ponderer.org/*]
Parent=Research Projects
Browser="Annotate Google"

[CMS crawler (?http://buytaert.net/crawler/)]
Parent=Research Projects

[e-SocietyRobot(http://www.yama.info.waseda.ac.jp/~yamana/es/)]
Parent=Research Projects
Browser="e-SocietyRobot"

[Forschungsportal/*]
Parent=Research Projects
Browser="Forschungsportal"

[Gulper Web *]
Parent=Research Projects
Browser="Gulper Web Bot"

[HooWWWer/*]
Parent=Research Projects
Browser="HooWWWer"

[inetbot/* (?http://www.inetbot.com/bot.html)]
Parent=Research Projects
Browser="inetbot"

[IRLbot/*]
Parent=Research Projects
Browser="IRLbot"

[JUST-CRAWLER(*)]
Parent=Research Projects
Browser="JUST-CRAWLER"

[Lachesis]
Parent=Research Projects
Browser="Lachesis"

[Mozilla/5.0 (compatible; nextthing.org/*)]
Parent=Research Projects
Browser="nextthing.org"
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (compatible; Theophrastus/*)]
Parent=Research Projects
Browser="Theophrastus"

[Mozilla/5.0 (compatible; Webscan v0.*; +http://otc.dyndns.org/webscan/)]
Parent=Research Projects
Browser="Webscan"

[MQbot*]
Parent=Research Projects
Browser="MQbot"

[OutfoxBot/*]
Parent=Research Projects
Browser="OutfoxBot"

[polybot?*]
Parent=Research Projects
Browser="Polybot"

[Shim?Crawler*]
Parent=Research Projects
Browser="Shim Crawler"

[Steeler/*]
Parent=Research Projects
Browser="Steeler"

[Taiga web spider]
Parent=Research Projects
Browser="Taiga"

[Theme Spider*]
Parent=Research Projects
Browser="Theme Spider"

[UofTDB_experiment* (<EMAIL>)]
Parent=Research Projects
Browser="UofTDB Experiment"

[USyd-NLP-Spider*]
Parent=Research Projects
Browser="USyd-NLP-Spider"

[woriobot*]
Parent=Research Projects
Browser="woriobot"

[wwwster/* (Beta, mailto:<EMAIL>)]
Parent=Research Projects
Browser="wwwster"
Beta=true

[Zao-Crawler]
Parent=Research Projects
Browser="Zao-Crawler"

[Zao/*]
Parent=Research Projects
Browser="Zao"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Rippers

[Rippers]
Parent=DefaultProperties
Browser="Ripper"
isBanned=true
Crawler=true

[*grub*]
Parent=Rippers
Browser="grub"

[*ickHTTP*]
Parent=Rippers
Browser="IP*Works"

[*java*]
Parent=Rippers
Browser="Generic Java Crawler"

[*libwww*]
Parent=Rippers
Browser="libwww"

[*WebGrabber*]
Parent=Rippers
Browser="WebGrabber"

[*WinHttpRequest*]
Parent=Rippers
Browser="WinHttp"

[3D-FTP/*]
Parent=Rippers
Browser="3D-FTP"

[3wGet/*]
Parent=Rippers
Browser="3wGet"

[ActiveRefresh*]
Parent=Rippers
Browser="ActiveRefresh"

[Ad Muncher*]
Parent=Rippers
Browser="Ad Muncher"

[Artera (Version *)]
Parent=Rippers
Browser="Artera"

[AutoHotkey]
Parent=Rippers
Browser="AutoHotkey"

[b2w/*]
Parent=Rippers
Browser="b2w"

[BasicHTTP/*]
Parent=Rippers
Browser="BasicHTTP"

[BlockNote.Net]
Parent=Rippers
Browser="BlockNote.Net"

[CAST]
Parent=Rippers
Browser="CAST"

[CFNetwork/*]
Parent=Rippers
Browser="CFNetwork"

[CFSCHEDULE*]
Parent=Rippers
Browser="ColdFusion Task Scheduler"

[CobWeb/*]
Parent=Rippers
Browser="CobWeb"

[ColdFusion*]
Parent=Rippers
Browser="ColdFusion"

[Crawl_Application]
Parent=Rippers
Browser="Crawl_Application"

[CTerm/*]
Parent=Rippers
Browser="CTerm"

[curl*]
Parent=Rippers
Browser="cURL"

[Custo*]
Parent=Rippers
Browser="Custo"

[DataCha0s/*]
Parent=Rippers
Browser="DataCha0s"

[DeepIndexer*]
Parent=Rippers
Browser="DeepIndexer"

[DISCo Pump *]
Parent=Rippers
Browser="DISCo Pump"

[eStyleSearch * (compatible; MSIE 6.0; Windows NT 5.0)]
Parent=Rippers
Browser="eStyleSearch"
Win32=true

[ezic.com http agent *]
Parent=Rippers
Browser="Ezic.com"

[fetch libfetch/*]
Parent=Rippers
Browser="fetch libfetch"

[FGet*]
Parent=Rippers
Browser="FGet"

[findfiles.net/* (Robot;<EMAIL>)]
Parent=Rippers
Browser="FindFiles"
isBanned=true

[Flaming AttackBot*]
Parent=Rippers
Browser="Flaming AttackBot"

[Foobot*]
Parent=Rippers
Browser="Foobot"

[GameSpyHTTP/*]
Parent=Rippers
Browser="GameSpyHTTP"

[gnome-vfs/*]
Parent=Rippers
Browser="gnome-vfs"

[Harvest/*]
Parent=Rippers
Browser="Harvest"

[hcat/*]
Parent=Rippers
Browser="hcat"

[HLoader]
Parent=Rippers
Browser="HLoader"

[Holmes/*]
Parent=Rippers
Browser="Holmes"

[HTMLParser/*]
Parent=Rippers
Browser="HTMLParser"

[http generic]
Parent=Rippers
Browser="http generic"

[http://arachnode.net*]
Parent=Rippers
Browser="arachnode"

[httpclient*]
Parent=Rippers
Browser="httpclient"

[httperf/*]
Parent=Rippers
Browser="httperf"

[HTTPFetch/*]
Parent=Rippers
Browser="HTTPFetch"

[HTTPGrab]
Parent=Rippers
Browser="HTTPGrab"

[HttpSession]
Parent=Rippers
Browser="HttpSession"

[httpunit/*]
Parent=Rippers
Browser="HttpUnit"

[ICE_GetFile]
Parent=Rippers
Browser="ICE_GetFile"

[iexplore.exe]
Parent=Rippers
Browser="iexplore.exe"

[Inet - Eureka App]
Parent=Rippers
Browser="Inet - Eureka App"

[INetURL/*]
Parent=Rippers
Browser="INetURL"

[InetURL:/*]
Parent=Rippers
Browser="InetURL"

[Internet Exploiter/*]
Parent=Rippers
Browser="Internet Exploiter"

[Internet Explore *]
Parent=Rippers
Browser="Fake IE"

[Internet Explorer *]
Parent=Rippers
Browser="Fake IE"

[IP*Works!*/*]
Parent=Rippers
Browser="IP*Works!"

[IrssiUrlLog/*]
Parent=Rippers
Browser="IrssiUrlLog"

[JPluck/*]
Parent=Rippers
Browser="JPluck"

[Kapere (http://www.kapere.com)]
Parent=Rippers
Browser="Kapere"

[LeechFTP]
Parent=Rippers
Browser="LeechFTP"

[LeechGet*]
Parent=Rippers
Browser="LeechGet"

[libcurl-agent/*]
Parent=Rippers
Browser="libcurl"

[libWeb/clsHTTP*]
Parent=Rippers
Browser="libWeb/clsHTTP"

[lwp*]
Parent=Rippers
Browser="lwp"

[MFC_Tear_Sample]
Parent=Rippers
Browser="MFC_Tear_Sample"

[Moozilla]
Parent=Rippers
Browser="Moozilla"

[MovableType/*]
Parent=Rippers
Browser="MovableType Web Log"

[Mozilla/* (compatible; OffByOne; Windows*) Webster Pro V3.*]
Parent=Rippers
Browser="OffByOne"
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/2.0 (compatible; NEWT ActiveX; Win32)]
Parent=Rippers
Browser="NEWT ActiveX"
Platform=Win32

[Mozilla/3.0 (compatible; Indy Library)]
Parent=Rippers
Cookies=true

[Mozilla/4.0 (compatible; BorderManager*)]
Parent=Rippers
Browser="Novell BorderManager"

[Mozilla/5.0 (compatible; IPCheck Server Monitor*)]
Parent=Rippers
Browser="IPCheck Server Monitor"

[OCN-SOC/*]
Parent=Rippers
Browser="OCN-SOC"

[Offline Explorer*]
Parent=Rippers
Browser="Offline Explorer"

[Open Web Analytics Bot*]
Parent=Rippers
Browser="Open Web Analytics Bot"

[OSSProxy*]
Parent=Rippers
Browser="OSSProxy"

[Pageload*]
Parent=Rippers
Browser="PageLoad"

[PageNest/*]
Parent=Rippers
Browser="PageNest"

[pavuk/*]
Parent=Rippers
Browser="Pavuk"

[PEAR HTTP_Request*]
Parent=Rippers
Browser="PEAR-PHP"

[PHP*]
Parent=Rippers
Browser="PHP"

[PigBlock (Windows NT 5.1; U)*]
Parent=Rippers
Browser="PigBlock"
Win32=true

[Pockey*]
Parent=Rippers
Browser="Pockey-GetHTML"

[POE-Component-Client-HTTP/*]
Parent=Rippers
Browser="POE-Component-Client-HTTP"

[PycURL/*]
Parent=Rippers
Browser="PycURL"

[Python*]
Parent=Rippers
Browser="Python"

[RepoMonkey*]
Parent=Rippers
Browser="RepoMonkey"

[SBL-BOT*]
Parent=Rippers
Browser="BlackWidow"

[ScoutAbout*]
Parent=Rippers
Browser="ScoutAbout"

[sherlock/*]
Parent=Rippers
Browser="Sherlock"

[SiteParser/*]
Parent=Rippers
Browser="SiteParser"

[SiteSnagger*]
Parent=Rippers
Browser="SiteSnagger"

[SiteSucker/*]
Parent=Rippers
Browser="SiteSucker"

[SiteWinder*]
Parent=Rippers
Browser="SiteWinder"

[Snoopy*]
Parent=Rippers
Browser="Snoopy"

[SOFTWING_TEAR_AGENT*]
Parent=Rippers
Browser="AspTear"

[SuperHTTP/*]
Parent=Rippers
Browser="SuperHTTP"

[Tcl http client package*]
Parent=Rippers
Browser="Tcl http client package"

[Twisted PageGetter]
Parent=Rippers
Browser="Twisted PageGetter"

[URL2File/*]
Parent=Rippers
Browser="URL2File"

[UtilMind HTTPGet]
Parent=Rippers
Browser="UtilMind HTTPGet"

[VCI WebViewer*]
Parent=Rippers
Browser="VCI WebViewer"

[Web Downloader*]
Parent=Rippers
Browser="Web Downloader"

[Web Downloader/*]
Parent=Rippers
Browser="Web Downloader"

[Web Magnet*]
Parent=Rippers
Browser="Web Magnet"

[WebAuto/*]
Parent=Rippers
Browser="WebAuto"

[webbandit/*]
Parent=Rippers
Browser="webbandit"

[WebCopier*]
Parent=Rippers
Browser="WebCopier"

[WebDownloader*]
Parent=Rippers
Browser="WebDownloader"

[WebFetch]
Parent=Rippers
Browser="WebFetch"

[webfetch/*]
Parent=Rippers
Browser="WebFetch"

[WebGatherer*]
Parent=Rippers
Browser="WebGatherer"

[WebGet]
Parent=Rippers
Browser="WebGet"

[WebReaper*]
Parent=Rippers
Browser="WebReaper"

[WebRipper]
Parent=Rippers
Browser="WebRipper"

[WebSauger*]
Parent=Rippers
Browser="WebSauger"

[Website Downloader*]
Parent=Rippers
Browser="Website Downloader"

[Website eXtractor*]
Parent=Rippers
Browser="Website eXtractor"

[Website Quester]
Parent=Rippers
Browser="Website Quester"

[WebsiteExtractor*]
Parent=Rippers
Browser="Website eXtractor"

[WebSnatcher*]
Parent=Rippers
Browser="WebSnatcher"

[Webster Pro*]
Parent=Rippers
Browser="Webster Pro"

[WebStripper*]
Parent=Rippers
Browser="WebStripper"

[WebWhacker*]
Parent=Rippers
Browser="WebWhacker"

[WinHttp*]
Parent=Rippers
Browser="WinHttp"

[WinScripter iNet Tools]
Parent=Rippers
Browser="WinScripter iNet Tools"

[WWW-Mechanize/*]
Parent=Rippers
Browser="WWW-Mechanize"

[Zend_Http_Client]
Parent=Rippers
Browser="Zend_Http_Client"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Site Monitors

[Site Monitors]
Parent=DefaultProperties
Browser="Site Monitors"
Crawler=true

[*EasyRider*]
Parent=Site Monitors
Browser="EasyRider"

[*maxamine.com--robot*]
Parent=Site Monitors
Browser="maxamine.com--robot"
isBanned=true

[*Pingdom*]
Parent=Site Monitors
Browser="Pingdom"

[*WebMon ?.*]
Parent=Site Monitors
Browser="WebMon"

[Kenjin Spider*]
Parent=Site Monitors
Browser="Kenjin Spider"

[Kevin http://*]
Parent=Site Monitors
Browser="Kevin"
isBanned=true

[Mozilla/4.0 (compatible; ChangeDetection/*]
Parent=Site Monitors
Browser="ChangeDetection"

[Mozilla/4.0 (compatible; MSIE ?.0; GomezAgent ?.0; Windows NT)]
Parent=Site Monitors
Browser="Gomez Site Monitor"

[Mozilla/5.0 (compatible; Chirp/1.0; +http://www.binarycanary.com/chirp.cfm)]
Parent=Site Monitors
Browser="BinaryCanary"
Version=1.0
MajorVer=1
MinorVer=0

[Myst Monitor Service v*]
Parent=Site Monitors
Browser="Myst Monitor Service"

[Net Probe]
Parent=Site Monitors
Browser="Net Probe"

[NetMechanic*]
Parent=Site Monitors
Browser="NetMechanic"

[NetReality*]
Parent=Site Monitors
Browser="NetReality"

[Pingdom.com_bot_version_*_(http://www.pingdom.com/)]
Parent=Site Monitors
Browser="Pingdom"

[Site Valet Online*]
Parent=Site Monitors
Browser="Site Valet"
isBanned=true

[SITECHECKER]
Parent=Site Monitors
Browser="SITECHECKER"

[<EMAIL>/*]
Parent=Site Monitors
Browser="ZoneEdit Failover Monitor"
isBanned=false

[UpTime Checker*]
Parent=Site Monitors
Browser="UpTime Checker"

[URL Control*]
Parent=Site Monitors
Browser="URL Control"

[URL_Access/*]
Parent=Site Monitors
Browser="URL_Access"

[URLCHECK]
Parent=Site Monitors
Browser="URLCHECK"

[URLy Warning*]
Parent=Site Monitors
Browser="URLy Warning"

[Webcheck *]
Parent=Site Monitors
Browser="Webcheck"
Version=1.0
MajorVer=1
MinorVer=0

[WebPatrol/*]
Parent=Site Monitors
Browser="WebPatrol"

[websitepulse checker/*]
Parent=Site Monitors
Browser="websitepulse checker"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Social Bookmarkers

[Social Networking]
Parent=DefaultProperties
Browser="Social Bookmarkers"
Crawler=true

[BookmarkBase(2/;http://bookmarkbase.com)]
Parent=Social Networking
Browser="BookmarkBase"

[Cocoal.icio.us/1.0 (v43) (Mac OS X; http://www.scifihifi.com/cocoalicious)]
Parent=Social Networking
Browser="Cocoalicious"

[Mozilla/5.0 (*) Gecko/* Firefox/2.0 OneRiot/1.0 (http://www.oneriot.com) ]
Parent=Social Networking
Browser="OneRiot"
isBanned=true

[Mozilla/5.0 (compatible; FriendFeedBot/0.*; +Http://friendfeed.com/about/bot)]
Parent=Social Networking
Browser="FriendFeedBot"

[Mozilla/5.0 (compatible; Twitturls; +http://twitturls.com)]
Parent=Social Networking
Browser="Twitturls"
isBanned=true

[SocialSpider-Finder/0.*]
Parent=Social Networking
Browser="SocialSpider-Finder"

[Twitturly*]
Parent=Social Networking
Browser="Twitturly"
isBanned=true

[WinkBot/*]
Parent=Social Networking
Browser="WinkBot"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Translators

[Translators]
Parent=DefaultProperties
Browser="Translators"
Frames=true
Tables=true
Crawler=true

[Seram Server]
Parent=Translators
Browser="Seram Server"

[TeragramWebcrawler/*]
Parent=Translators
Browser="TeragramWebcrawler"
Version=1.0
MajorVer=1
MinorVer=0

[WebIndexer/* (Web Indexer; *)]
Parent=Translators
Browser="WorldLingo"

[WebTrans]
Parent=Translators
Browser="WebTrans"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Version Checkers

[Version Checkers]
Parent=DefaultProperties
Browser="Version Checkers"
Crawler=true

[Automated Browscap.ini Updater. Gary contact <NAME_EMAIL> with problems.]
Parent=Version Checkers
Browser="Automated Browscap Updater"

[Automated Browscap.ini Updater. To report issues contact us at+http://www.skycomp.ca]
Parent=Version Checkers
Browser="Automated Browscap.ini Updater"

[Browscap Mirror System/1.* (browscap.giantrealm.com)]
Parent=Version Checkers
Browser="Browscap Mirror System"

[Browscap Mirror v1.30]
Parent=Version Checkers
Browser="Browscap Mirror"

[Browscap updater]
Parent=Version Checkers
Browser="Browscap updater"

[browscap updater; interval:weekly; server:rohan.doppy.nl; questions:<EMAIL>;]
Parent=Version Checkers
Browser="browscap updater"

[BrowscapUpdater1.0]
Parent=Version Checkers
Browser="BrowscapUpdater"

[Browser Capabilities Project - PHP Browscap/*]
Parent=Version Checkers
Browser="BCP - PHP Browscap"
Version=1.0
MajorVer=1
MinorVer=0

[Browser Capabilities Project AutoDownloader; created by Tom Kelleher Consulting, Inc. (tkelleher.com); used with special permission from Gary Joel Keith; uses Microsoft's WinHTTP component]
Parent=Version Checkers
Browser="TKC AutoDownloader"

[Decode Framework 0.* browscap library]
Parent=Version Checkers
Browser="Decode Framework browscap library"

[Desktop Sidebar*]
Parent=Version Checkers
Browser="Desktop Sidebar"
isBanned=true

[Mono Browser Capabilities Updater*]
Parent=Version Checkers
Browser="Mono Browser Capabilities Updater"
isBanned=true

[PHP Browser Capabilities Project/*]
Parent=Version Checkers
Browser="PHP Browser Capabilities Project"

[UpdateBrowscap*]
Parent=Version Checkers
Browser="UpdateBrowscap"

[WCC Browscap Updater/0.* (PHP: file_get_contents)]
Parent=Version Checkers
Browser="WCC Browscap Updater"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; W3C

[W3C]
Parent=DefaultProperties
Browser="W3C"
Crawler=true

[*W3C-checklink/*]
Parent=W3C
Browser="W3C-checklink"

[Jigsaw/* W3C_CSS_Validator*/*]
Parent=W3C
Browser="Jigsaw_W3C_CSS_Validator"

[P3P Validator]
Parent=W3C
Browser="P3P Validator"

[Unicorn/1.*]
Parent=W3C
Browser="W3C Unicorn"

[W3C-mobileOK/*]
Parent=W3C
Browser="W3C-mobileOK"
Alpha=false
Beta=false
Win16=false
Win32=false
Win64=false
Frames=false
IFrames=false
Tables=false
Cookies=false
BackgroundSounds=false
JavaScript=false
VBScript=false
JavaApplets=false
ActiveXControls=false
isBanned=false
isMobileDevice=false
isSyndicationReader=false
Crawler=true

[W3C-mobileOK/DDC-*]
Parent=W3C
Browser="W3C-mobileOK/DDC"
isMobileDevice=true

[W3C-WebCon/*]
Parent=W3C
Browser="W3C-WebCon"

[W3C_Validator/*]
Parent=W3C
Browser="W3C_Validator"

[W3CLineMode/*]
Parent=W3C
Browser="W3CLineMode"

[W3CRobot/*]
Parent=W3C
Browser="W3CRobot"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Become

[Become]
Parent=DefaultProperties
Browser="Become"
isSyndicationReader=true
Crawler=true

[*BecomeBot/*]
Parent=Become
Browser="BecomeBot"

[*<EMAIL>*]
Parent=Become
Browser="BecomeBot"

[*<EMAIL>*]
Parent=Become
Browser="Exabot"

[MonkeyCrawl/*]
Parent=Become
Browser="MonkeyCrawl"

[Mozilla/5.0 (compatible; BecomeJPBot/2.3; *)]
Parent=Become
Browser="BecomeJPBot"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Blue Coat Systems

[Blue Coat Systems]
Parent=DefaultProperties
Browser="Blue Coat Systems"
isBanned=true
Crawler=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; FeedHub

[FeedHub]
Parent=DefaultProperties
Browser="FeedHub"
isSyndicationReader=true
Crawler=true

[FeedHub FeedDiscovery/1.0 (http://www.feedhub.com)]
Parent=FeedHub
Browser="FeedHub FeedDiscovery"
Version=1.0
MajorVer=1
MinorVer=0

[FeedHub FeedFetcher/1.0 (http://www.feedhub.com)]
Parent=FeedHub
Browser="FeedHub FeedFetcher"
Version=1.0
MajorVer=1
MinorVer=0

[FeedHub MetaDataFetcher/1.0 (http://www.feedhub.com)]
Parent=FeedHub
Browser="FeedHub MetaDataFetcher"
Version=1.0
MajorVer=1
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Internet Content Rating Association

[Internet Content Rating Association]
Parent=DefaultProperties
Browser=""
Frames=true
Tables=true
Crawler=true

[ICRA_label_generator/1.?]
Parent=Internet Content Rating Association
Browser="ICRA_label_generator"

[ICRA_Semantic_spider/0.?]
Parent=Internet Content Rating Association
Browser="ICRA_Semantic_spider"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; NameProtect

[NameProtect]
Parent=DefaultProperties
Browser="NameProtect"
isBanned=true
Crawler=true

[abot/*]
Parent=NameProtect
Browser="NameProtect"

[NP/*]
Parent=NameProtect
Browser="NameProtect"

[NPBot*]
Parent=NameProtect
Browser="NameProtect"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netcraft

[Netcraft]
Parent=DefaultProperties
Browser="Netcraft"
isBanned=true
Crawler=true

[*Netcraft Web Server Survey*]
Parent=Netcraft
Browser="Netcraft Webserver Survey"
isBanned=true

[Mozilla/5.0 (compatible; NetcraftSurveyAgent/1.0; *<EMAIL>)]
Parent=Netcraft
Browser="NetcraftSurveyAgent"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; NewsGator

[NewsGator]
Parent=DefaultProperties
Browser="NewsGator"
isSyndicationReader=true
Crawler=true

[MarsEdit*]
Parent=NewsGator
Browser="MarsEdit"

[NetNewsWire*/*]
Parent=NewsGator
Browser="NetNewsWire"
Platform=MacOSX

[NewsFire/*]
Parent=NewsGator
Browser="NewsFire"

[NewsGator FetchLinks extension/*]
Parent=NewsGator
Browser="NewsGator FetchLinks"

[NewsGator/*]
Parent=NewsGator
Browser="NewsGator"
isBanned=true

[NewsGatorOnline/*]
Parent=NewsGator
Browser="NewsGatorOnline"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 10.0

[Chromium 10.0]
Parent=DefaultProperties
Browser="Chromium"
Version=10.0
MajorVer=10
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/10.*Chrome/*Safari/*]
Parent=Chromium 10.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/10.*Chrome/*Safari/*]
Parent=Chromium 10.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 11.0

[Chromium 11.0]
Parent=DefaultProperties
Browser="Chromium"
Version=11.0
MajorVer=11
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/11.*Chrome/*Safari/*]
Parent=Chromium 11.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/11.*Chrome/*Safari/*]
Parent=Chromium 11.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 12.0

[Chromium 12.0]
Parent=DefaultProperties
Browser="Chromium"
Version=12.0
MajorVer=12
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/12.*Chrome/*Safari/*]
Parent=Chromium 12.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/12.*Chrome/*Safari/*]
Parent=Chromium 12.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 13.0

[Chromium 13.0]
Parent=DefaultProperties
Browser="Chromium"
Version=13.0
MajorVer=13
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/13.*Chrome/*Safari/*]
Parent=Chromium 13.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/13.*Chrome/*Safari/*]
Parent=Chromium 13.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 14.0

[Chromium 14.0]
Parent=DefaultProperties
Browser="Chromium"
Version=14.0
MajorVer=14
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/14.*Chrome/*Safari/*]
Parent=Chromium 14.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/14.*Chrome/*Safari/*]
Parent=Chromium 14.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 15.0

[Chromium 15.0]
Parent=DefaultProperties
Browser="Chromium"
Version=15.0
MajorVer=15
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/15.*Chrome/*Safari/*]
Parent=Chromium 15.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/15.*Chrome/*Safari/*]
Parent=Chromium 15.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 6.0

[Chromium 6.0]
Parent=DefaultProperties
Browser="Chromium"
Version=6.0
MajorVer=6
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/6.*Chrome/*Safari/*]
Parent=Chromium 6.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/6.*Chrome/*Safari/*]
Parent=Chromium 6.0
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 7.0

[Chromium 7.0]
Parent=DefaultProperties
Browser="Chromium"
Version=7.0
MajorVer=7
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/7.*Chrome/*Safari/*]
Parent=Chromium 7.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/7.*Chrome/*Safari/*]
Parent=Chromium 7.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 8.0

[Chromium 8.0]
Parent=DefaultProperties
Browser="Chromium"
Version=8.0
MajorVer=8
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/8.*Chrome/*Safari/*]
Parent=Chromium 8.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/8.*Chrome/*Safari/*]
Parent=Chromium 8.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium 9.0

[Chromium 9.0]
Parent=DefaultProperties
Browser="Chromium"
Version=9.0
MajorVer=9
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/9.*Chrome/*Safari/*]
Parent=Chromium 9.0

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/9.*Chrome/*Safari/*]
Parent=Chromium 9.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chromium Generic

[Chromium Generic]
Parent=DefaultProperties
Browser="Chromium"
Platform=Linux
CssVersion=1

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chromium/*Chrome/*Safari/*]
Parent=Chromium Generic

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chromium/*Chrome/*Safari/*]
Parent=Chromium Generic

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 10.0

[Chrome 10.0]
Parent=DefaultProperties
Browser="Chrome"
Version=10.0
MajorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/10.*Safari/*]
Parent=Chrome 10.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 11.0

[Chrome 11.0]
Parent=DefaultProperties
Browser="Chrome"
Version=11.0
MajorVer=11
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/11.*Safari/*]
Parent=Chrome 11.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 12.0

[Chrome 12.0]
Parent=DefaultProperties
Browser="Chrome"
Version=12.0
MajorVer=12
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/12.*Safari/*]
Parent=Chrome 12.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 13.0

[Chrome 13.0]
Parent=DefaultProperties
Browser="Chrome"
Version=13.0
MajorVer=13
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/13.*Safari/*]
Parent=Chrome 13.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 14.0

[Chrome 14.0]
Parent=DefaultProperties
Browser="Chrome"
Version=14.0
MajorVer=14
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
VBScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/14.*Safari/*]
Parent=Chrome 14.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 15.0

[Chrome 15.0]
Parent=DefaultProperties
Browser="Chrome"
Version=15.0
MajorVer=15
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/15.*Safari/*]
Parent=Chrome 15.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 6.0

[Chrome 6.0]
Parent=DefaultProperties
Browser="Chrome"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/6.*Safari/*]
Parent=Chrome 6.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 7.0

[Chrome 7.0]
Parent=DefaultProperties
Browser="Chrome"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/7.*Safari/*]
Parent=Chrome 7.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 8.0

[Chrome 8.0]
Parent=DefaultProperties
Browser="Chrome"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/8.*Safari/*]
Parent=Chrome 8.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome 9.0

[Chrome 9.0]
Parent=DefaultProperties
Browser="Chrome"
Version=9.0
MajorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/9.*Safari/*]
Parent=Chrome 9.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chrome Generic

[Chrome Generic]
Parent=DefaultProperties
Browser="Chrome"
CssVersion=1

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=MacOSX

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=Linux

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=Linux

[Mozilla/5.0 (*Windows NT 4.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinNT

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=Win7

[Mozilla/5.0 (*Windows NT 6.1*WOW64*) AppleWebKit/* (KHTML, like Gecko)*Chrome/*Safari/*]
Parent=Chrome Generic
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Google Code

[Google Code]
Parent=DefaultProperties
Browser="Google Code"

[Mozilla/5.0 (Windows; U; *) AppleWebKit/* (KHTML, like Gecko, Safari/*) Arora/0.6*]
Parent=Google Code
Browser="Arora"
Version=0.6
MajorVer=0
MinorVer=6
Platform=Win32

[Mozilla/5.0 (Windows; U; *) AppleWebKit/* (KHTML, like Gecko, Safari/*) Arora/0.8.*]
Parent=Google Code
Browser="Arora"
Version=0.8.0
MajorVer=0
MinorVer=8.0
Platform=Win32

[Mozilla/5.0 (X11; U; Linux; *) AppleWebKit/* (KHTML, like Gecko, Safari/*) Arora/0.6*]
Parent=Google Code
Browser="Arora"
Version=0.6
MajorVer=0
MinorVer=6
Platform=Linux

[Mozilla/5.0 (X11; U; Linux; *) AppleWebKit/* (KHTML, like Gecko, Safari/*) Arora/0.8.*]
Parent=Google Code
Browser="Arora"
Version=0.8.0
MajorVer=0
MinorVer=8.0
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 10.0

[Iron 10.0]
Parent=DefaultProperties
Browser="Iron"
Version=10.0
MajorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/10.* Safari/*]
Parent=Iron 10.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 11.0

[Iron 11.0]
Parent=DefaultProperties
Browser="Iron"
Version=11.0
MajorVer=11
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/11.* Safari/*]
Parent=Iron 11.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 6.0

[Iron 6.0]
Parent=DefaultProperties
Browser="Iron"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/6.* Safari/*]
Parent=Iron 6.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 7.0

[Iron 7.0]
Parent=DefaultProperties
Browser="Iron"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/7.* Safari/*]
Parent=Iron 7.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 8.0

[Iron 8.0]
Parent=DefaultProperties
Browser="Iron"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/8.* Safari/*]
Parent=Iron 8.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron 9.0

[Iron 9.0]
Parent=DefaultProperties
Browser="Iron"
Version=9.0
MajorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/9.* Safari/*]
Parent=Iron 9.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iron Generic

[Iron Generic]
Parent=DefaultProperties
Browser="Iron"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=1

[Mozilla/5.0 (*Intel Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Linux i686*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=Linux
Win32=false

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=Win2003

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Iron/* Safari/*]
Parent=Iron Generic
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; RockMelt Beta

[RockMelt Beta]
Parent=DefaultProperties
Browser="RockMelt"
Version=0.9
MinorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; U; Intel Mac OS X 10*; *) AppleWebKit/* (KHTML, like Gecko) RockMelt/0.* Chrome/* Safari/*]
Parent=RockMelt Beta
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *) AppleWebKit/* (KHTML, like Gecko) RockMelt/0.* Chrome/* Safari/*]
Parent=RockMelt Beta
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *) AppleWebKit/* (KHTML, like Gecko) RockMelt/0.* Chrome/* Safari/*]
Parent=RockMelt Beta
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *) AppleWebKit/* (KHTML, like Gecko) RockMelt/0.* Chrome/* Safari/*]
Parent=RockMelt Beta
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Arora 0.10

[Arora 0.10]
Parent=DefaultProperties
Browser="Arora"
Version=0.10
MinorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.10*]
Parent=Arora 0.10
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Arora 0.11

[Arora 0.11]
Parent=DefaultProperties
Browser="Arora"
Version=0.11
MinorVer=11
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.11*]
Parent=Arora 0.11
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Arora 0.8

[Arora 0.8]
Parent=DefaultProperties
Browser="Arora"
Version=0.8
MinorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.8*]
Parent=Arora 0.8
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Arora 0.9

[Arora 0.9]
Parent=DefaultProperties
Browser="Arora"
Version=0.9
MinorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.9*]
Parent=Arora 0.9
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Arora Generic

[Arora Generic]
Parent=DefaultProperties
Browser="Arora"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=3

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=Linux
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Arora/0.?*]
Parent=Arora Generic
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Media Players

[Media Players]
Parent=DefaultProperties
Browser="Media Players"
isMobileDevice=true

[Microsoft NetShow(TM) Player with RealVideo(R)]
Parent=Media Players
Browser="Microsoft NetShow"

[Mozilla/5.0 (Macintosh; U; PPC Mac OS X; *) AppleWebKit/* RealPlayer]
Parent=Media Players
Browser="RealPlayer"
Platform=MacOSX

[MPlayer 0.9*]
Parent=Media Players
Browser="MPlayer"
Version=0.9
MajorVer=0
MinorVer=9

[MPlayer 1.*]
Parent=Media Players
Browser="MPlayer"
Version=1.0
MajorVer=1
MinorVer=0

[MPlayer HEAD CVS]
Parent=Media Players
Browser="MPlayer"

[RealPlayer*]
Parent=Media Players
Browser="RealPlayer"

[RMA/*]
Parent=Media Players
Browser="RMA"

[VLC media player*]
Parent=Media Players
Browser="VLC"

[vobsub]
Parent=Media Players
Browser="vobsub"
isBanned=true

[WinampMPEG/*]
Parent=Media Players
Browser="WinAmp"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Microsoft Zune

[Microsoft Zune]
Parent=DefaultProperties
Browser=""
Win32=true
isMobileDevice=true

[Mozilla/4.0 (compatible; MSIE ?.0; Microsoft ZuneHD 4.*)]
Parent=Microsoft Zune
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/4.0 (compatible; ZuneHD 4.*)]
Parent=Microsoft Zune
Browser="ZuneHD"
Version=4
MajorVer=4

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Nintendo

[Nintendo Wii]
Parent=DefaultProperties
Browser=""
isMobileDevice=true

[Opera/* (Nintendo DSi; Opera/*; *; *)]
Parent=Nintendo Wii
Browser="DSi"

[Opera/* (Nintendo Wii; U; *)]
Parent=Nintendo Wii
Browser="Wii"

[VLC Media Player]
Parent=DefaultProperties
Browser="VLC Media Player"

[vlc/*]
Parent=VLC Media Player

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Windows Media Player

[Windows Media Player]
Parent=DefaultProperties
Browser="Windows Media Player"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[NSPlayer/10.*]
Parent=Windows Media Player
Version=10.0
MajorVer=10
MinorVer=0

[NSPlayer/11.*]
Parent=Windows Media Player
Version=11.0
MajorVer=11
MinorVer=0

[NSPlayer/4.*]
Parent=Windows Media Player
Version=4.0
MajorVer=4
MinorVer=0

[NSPlayer/7.*]
Parent=Windows Media Player
Version=7.0
MajorVer=7
MinorVer=0

[NSPlayer/8.*]
Parent=Windows Media Player
Version=8.0
MajorVer=8
MinorVer=0

[NSPlayer/9.*]
Parent=Windows Media Player
Version=9.0
MajorVer=9
MinorVer=0

[Windows-Media-Player/10.*]
Parent=Windows Media Player
Version=10.0
MajorVer=10
MinorVer=0

[Windows-Media-Player/11.*]
Parent=Windows Media Player
Version=11.0
MajorVer=11
MinorVer=0

[Windows-Media-Player/7.*]
Parent=Windows Media Player
Version=7.0
MajorVer=7
MinorVer=0

[Windows-Media-Player/8.*]
Parent=Windows Media Player
Version=8.0
MajorVer=8
MinorVer=0

[Windows-Media-Player/9.*]
Parent=Windows Media Player
Version=9.0
MajorVer=9
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; QuickTime 10.0

[QuickTime 10.0]
Parent=DefaultProperties
Browser="QuickTime"
Version=10.0
MajorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[QuickTime*(qtver=10.*os=Mac 10.*)]
Parent=QuickTime 10.0
Platform=MacOSX
Win32=false

[QuickTime*(qtver=10.*os=Mac 9.*)]
Parent=QuickTime 10.0
Platform=MacPPC
Win32=false

[QuickTime*(qtver=10.*os=Windows 95*)]
Parent=QuickTime 10.0
Platform=Win95

[QuickTime*(qtver=10.*os=Windows 98*)]
Parent=QuickTime 10.0
Platform=Win98

[QuickTime*(qtver=10.*os=Windows Me*)]
Parent=QuickTime 10.0
Platform=WinME

[QuickTime*(qtver=10.*os=Windows NT 4.0*)]
Parent=QuickTime 10.0
Platform=WinNT

[QuickTime*(qtver=10.*os=Windows NT 5.0*)]
Parent=QuickTime 10.0
Platform=Win2000

[QuickTime*(qtver=10.*os=Windows NT 5.1*)]
Parent=QuickTime 10.0
Platform=WinXP

[QuickTime*(qtver=10.*os=Windows NT 5.2*)]
Parent=QuickTime 10.0
Platform=WinXP

[QuickTime*(qtver=10.*os=Windows NT 6.0*)]
Parent=QuickTime 10.0
Platform=WinVista

[QuickTime*(qtver=10.*os=Windows NT 6.1*)]
Parent=QuickTime 10.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; QuickTime 5.0

[QuickTime 5.0]
Parent=DefaultProperties
Browser="QuickTime"
Version=5.0
MajorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[QuickTime*(qtver=5.*os=Mac 10.*)]
Parent=QuickTime 5.0
Platform=MacOSX
Win32=false

[QuickTime*(qtver=5.*os=Mac 9.*)]
Parent=QuickTime 5.0
Platform=MacPPC
Win32=false

[QuickTime*(qtver=5.*os=Windows 95*)]
Parent=QuickTime 5.0
Platform=Win95

[QuickTime*(qtver=5.*os=Windows 98*)]
Parent=QuickTime 5.0
Platform=Win98

[QuickTime*(qtver=5.*os=Windows Me*)]
Parent=QuickTime 5.0
Platform=WinME

[QuickTime*(qtver=5.*os=Windows NT 4.0*)]
Parent=QuickTime 5.0
Platform=WinNT

[QuickTime*(qtver=5.*os=Windows NT 5.0*)]
Parent=QuickTime 5.0
Platform=Win2000

[QuickTime*(qtver=5.*os=Windows NT 5.1*)]
Parent=QuickTime 5.0
Platform=WinXP

[QuickTime*(qtver=5.*os=Windows NT 5.2*)]
Parent=QuickTime 5.0
Platform=WinXP

[QuickTime*(qtver=5.*os=Windows NT 6.0*)]
Parent=QuickTime 5.0
Platform=WinVista

[QuickTime*(qtver=5.*os=Windows NT 6.1*)]
Parent=QuickTime 5.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; QuickTime 6.0

[QuickTime 6.0]
Parent=DefaultProperties
Browser="QuickTime"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[QuickTime*(qtver=6.*os=Mac 10.*)]
Parent=QuickTime 6.0
Platform=MacOSX
Win32=false

[QuickTime*(qtver=6.*os=Mac 9.*)]
Parent=QuickTime 6.0
Platform=MacPPC
Win32=false

[QuickTime*(qtver=6.*os=Windows 95*)]
Parent=QuickTime 6.0
Platform=Win95

[QuickTime*(qtver=6.*os=Windows 98*)]
Parent=QuickTime 6.0
Platform=Win98

[QuickTime*(qtver=6.*os=Windows Me*)]
Parent=QuickTime 6.0
Platform=WinME

[QuickTime*(qtver=6.*os=Windows NT 4.0*)]
Parent=QuickTime 6.0
Platform=WinNT

[QuickTime*(qtver=6.*os=Windows NT 5.0*)]
Parent=QuickTime 6.0
Platform=Win2000

[QuickTime*(qtver=6.*os=Windows NT 5.1*)]
Parent=QuickTime 6.0
Platform=WinXP

[QuickTime*(qtver=6.*os=Windows NT 5.2*)]
Parent=QuickTime 6.0
Platform=WinXP

[QuickTime*(qtver=6.*os=Windows NT 6.0*)]
Parent=QuickTime 6.0
Platform=WinVista

[QuickTime*(qtver=6.*os=Windows NT 6.1*)]
Parent=QuickTime 6.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; QuickTime 7.0

[QuickTime 7.0]
Parent=DefaultProperties
Browser="QuickTime"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[QuickTime*(qtver=7.*;os=Windows 98*)]
Parent=QuickTime 7.0
Platform=Win98

[QuickTime*(qtver=7.*;os=Windows Me*)]
Parent=QuickTime 7.0
Platform=WinME

[QuickTime*(qtver=7.*;os=Windows NT 4.0*)]
Parent=QuickTime 7.0
Platform=WinNT

[QuickTime*(qtver=7.*;os=Windows NT 5.0*)]
Parent=QuickTime 7.0
Platform=Win2000

[QuickTime*(qtver=7.*;os=Windows NT 5.1*)]
Parent=QuickTime 7.0
Platform=WinXP

[QuickTime*(qtver=7.*;os=Windows NT 5.2*)]
Parent=QuickTime 7.0
Platform=WinXP

[QuickTime*(qtver=7.*;os=Windows NT 6.0*)]
Parent=QuickTime 7.0
Platform=WinVista

[QuickTime*(qtver=7.*;os=Windows NT 6.1*)]
Parent=QuickTime 7.0
Platform=Win7

[QuickTime*(qtver=7.*os=Mac 10.*)]
Parent=QuickTime 7.0
Platform=MacOSX
Win32=false

[QuickTime*(qtver=7.*os=Mac 9.*)]
Parent=QuickTime 7.0
Platform=MacPPC
Win32=false

[QuickTime*(qtver=7.*os=Windows 95*)]
Parent=QuickTime 7.0
Platform=Win95

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; QuickTime 7.6

[QuickTime 7.6]
Parent=DefaultProperties
Browser="QuickTime"
Version=7.6
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[QuickTime*(qtver=7.6*;os=Mac 9.*)]
Parent=QuickTime 7.6
Platform=MacPPC
Win32=false

[QuickTime*(qtver=7.6*;os=Windows 95*)]
Parent=QuickTime 7.6
Platform=Win95

[QuickTime*(qtver=7.6*;os=Windows 98*)]
Parent=QuickTime 7.6
Platform=Win98

[QuickTime*(qtver=7.6*;os=Windows Me*)]
Parent=QuickTime 7.6
Platform=WinME

[QuickTime*(qtver=7.6*;os=Windows NT 4.0*)]
Parent=QuickTime 7.6
Platform=WinNT

[QuickTime*(qtver=7.6*;os=Windows NT 5.0*)]
Parent=QuickTime 7.6
Platform=Win2000

[QuickTime*(qtver=7.6*;os=Windows NT 5.1*)]
Parent=QuickTime 7.6
Platform=WinXP

[QuickTime*(qtver=7.6*;os=Windows NT 5.2*)]
Parent=QuickTime 7.6
Platform=WinXP

[QuickTime*(qtver=7.6*;os=Windows NT 6.0*)]
Parent=QuickTime 7.6
Platform=WinVista

[QuickTime*(qtver=7.6*;os=Windows NT 6.1*)]
Parent=QuickTime 7.6
Platform=Win7

[QuickTime*(qtver=7.6*os=Mac 10.*)]
Parent=QuickTime 7.6
Platform=MacOSX
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lotus Notes 5.0

[Lotus Notes 5.0]
Parent=DefaultProperties
Browser="Lotus Notes"
Version=5.0
MajorVer=5

[Mozilla/4.0 (compatible; Lotus-Notes/5.0; Macintosh PPC)]
Parent=Lotus Notes 5.0
Platform=MacOSX

[Mozilla/4.0 (compatible; Lotus-Notes/5.0; Windows-NT)]
Parent=Lotus Notes 5.0
Platform=WinNT
Win32=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lotus Notes 6.0

[Lotus Notes 6.0]
Parent=DefaultProperties
Browser="Lotus Notes"
Version=6.0
MajorVer=6

[Mozilla/4.0 (compatible; Lotus-Notes/6.0; Windows-NT)]
Parent=Lotus Notes 6.0
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Microsoft Outlook 2007

[Microsoft Outlook 2007]
Parent=DefaultProperties
Browser="Microsoft Outlook"
Version=2007
MajorVer=2007
Frames=true
Tables=true
CssVersion=2

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.1; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 5.2; Trident/4.0; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=Win2003

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; WOW64; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=WinVista
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/4.0; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; WOW64; Trident/4.0; *MSOffice 12)]
Parent=Microsoft Outlook 2007
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Microsoft Outlook 2010

[Microsoft Outlook 2010]
Parent=DefaultProperties
Browser="Microsoft Outlook"
Version=2010
MajorVer=2010
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
CssVersion=2

[Microsoft Office/14.0 (Windows NT 5.1; Microsoft Outlook 14.*; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=WinXP

[Microsoft Office/14.0 (Windows NT 6.1; Microsoft Outlook 14.*; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.0; Trident/4.0; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/4.0; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Trident/5.0; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 7.0; Windows NT 6.1; Win64; x64; Trident/4.0; *MSOffice 14)]
Parent=Microsoft Outlook 2010
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Windows Live Mail

[Windows Live Mail]
Parent=DefaultProperties
Browser="Windows Live Mail"
Version=7.0
MajorVer=7
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
CssVersion=2

[Outlook-Express/7.0 (MSIE 7.0; Windows NT 5.1; Trident/4.0; *)]
Parent=Windows Live Mail
Platform=WinXP

[Outlook-Express/7.0 (MSIE 7.0; Windows NT 6.1; Trident/4.0; *)]
Parent=Windows Live Mail
Platform=Win7

[Outlook-Express/7.0 (MSIE 7.0; Windows NT 6.1; WOW64; Trident/4.0; *)]
Parent=Windows Live Mail
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Google Android

[Android]
Parent=DefaultProperties
Browser="Android"
Platform=Android
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/5.0 (Linux; U; Android 1.0*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/3.0* Mobile Safari/5*]
Parent=Android
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (Linux; U; Android 1.5*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/3.1.2* Mobile Safari/5*]
Parent=Android
Version=1.5
MajorVer=1
MinorVer=5

[Mozilla/5.0 (Linux; U; Android 1.6*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/3.1.2* Mobile Safari/5*]
Parent=Android
Version=1.6
MajorVer=1
MinorVer=6

[Mozilla/5.0 (Linux; U; Android 2.0*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/5.0 (Linux; U; Android 2.0.1; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.0.1
MajorVer=2
MinorVer=0.1

[Mozilla/5.0 (Linux; U; Android 2.1**; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/5.0 (Linux; U; Android 2.1-update1; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.1.1
MajorVer=2
MinorVer=1.1

[Mozilla/5.0 (Linux; U; Android 2.2*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.2
MajorVer=2
MinorVer=2

[Mozilla/5.0 (Linux; U; Android 2.3*; *; *) AppleWebKit/5* (KHTML, like Gecko) *Version/4.0 Mobile Safari/5*]
Parent=Android
Version=2.3
MajorVer=2
MinorVer=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; BlackBerry

[BlackBerry]
Parent=DefaultProperties
Browser="BlackBerry"
Platform=BlackBerry OS
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[*BlackBerry*]
Parent=BlackBerry

[*BlackBerrySimulator*]
Parent=BlackBerry

[Mozilla/*(PlayBook*RIM Tablet OS 1.0.0*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/0.*Safari/*]
Parent=BlackBerry
Browser="Blackberry Playbook Tablet"
Version=1.0
MajorVer=1
MinorVer=0
Platform=RIM Tablet OS

[Mozilla/5.0 (BlackBerry; U; BlackBerry*) AppleWebKit/534.1+ (KHTML, like Gecko) *Version/6.* Mobile Safari/534.1+]
Parent=BlackBerry
Version=6.0
MajorVer=6
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Handspring Blazer

[Blazer]
Parent=DefaultProperties
Browser="Handspring Blazer"
Platform=Palm
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 95; PalmSource; Blazer 3.0) 16;160x160]
Parent=Blazer
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/*; Blazer/4.0) 16;320x448]
Parent=Blazer
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/*; Blazer/4.1) 16;320x320]
Parent=Blazer
Version=4.1
MajorVer=4
MinorVer=1

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/*; Blazer/4.2) 16;320x320]
Parent=Blazer
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/*; Blazer/4.4) 16;320x320]
Parent=Blazer
Version=4.4
MajorVer=4
MinorVer=4

[Mozilla/4.0 (compatible; MSIE 6.0; Windows 98; PalmSource/*; Blazer/4.5) 16;320x320]
Parent=Blazer
Version=4.5
MajorVer=4
MinorVer=5

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Brew

[Brew]
Parent=DefaultProperties
Browser="Brew"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[*-*/1.0 BREW/2.0* Browser/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1]
Parent=Brew
Version=2.0
MajorVer=2
MinorVer=0
CssVersion=1

[*-*/1.0 BREW/2.1* Browser/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1]
Parent=Brew
Version=2.1
MajorVer=2
MinorVer=1
CssVersion=1

[*-*/1.0 BREW/3.0* Browser/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1]
Parent=Brew
Version=3.0
MajorVer=3
MinorVer=1
CssVersion=1

[*-*/1.0 BREW/3.1* Browser/1.0 Profile/MIDP-2.0 Configuration/CLDC-1.1]
Parent=Brew
Version=3.1
MajorVer=3
MinorVer=1
CssVersion=1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; DoCoMo

[DoCoMo]
Parent=DefaultProperties
Browser="DoCoMo"
Platform=DoCoMo
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[DoCoMo/1.0*]
Parent=DoCoMo
Version=1.0
MajorVer=1
MinorVer=0

[DoCoMo/2.0*]
Parent=DoCoMo
Version=2.0
MajorVer=2
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IEMobile

[IEMobile]
Parent=DefaultProperties
Browser="IEMobile"
Platform=WinCE
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 6.*)*]
Parent=IEMobile
Version=6.0
MajorVer=6
MinorVer=0

[Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 7.*)*]
Parent=IEMobile
Version=7.0
MajorVer=7
MinorVer=0

[Mozilla/4.0 (compatible; MSIE 6.0; Windows CE; IEMobile 8.*)*]
Parent=IEMobile
Version=8.0
MajorVer=8
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 6.0

[Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1; Windows Phone 6.5 HTC_HD2/1.0)]
Parent=IEMobile
Browser="IE"
Version=6.0
MajorVer=6
MinorVer=0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0; Windows Phone OS 7.0; Trident/3.1; IEMobile/7.0*)*]
Parent=IEMobile
Version=7.0
MajorVer=7
MinorVer=0
Platform=WinPhone7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iPad

[iPad]
Parent=DefaultProperties
Browser="iPad"
Platform=iPhone OSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=3

[Mozilla/*(iPad*CPU OS 3_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=3.2
MajorVer=3
MinorVer=2

[Mozilla/*(iPad*CPU OS 3_2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=3.2
MajorVer=3
MinorVer=2

[Mozilla/*(iPad*CPU OS 3_2_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=3.2
MajorVer=3
MinorVer=2

[Mozilla/*(iPad*CPU OS 4_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/*(iPad*CPU OS 4_2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/*(iPad*CPU OS 4_3* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=4.3
MajorVer=4
MinorVer=3

[Mozilla/*(iPad*CPU OS 4_3_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=4.3
MajorVer=4
MinorVer=3

[Mozilla/*(iPad*CPU OS 4_3_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/* Mobile/* Safari/*]
Parent=iPad
Version=4.3
MajorVer=4
MinorVer=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iPhone

[iPhone]
Parent=DefaultProperties
Browser="iPhone"
Platform=iPhone OSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=3

[Mozilla/?.*(iPhone*CPU iPhone OS 2_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/?.*(iPhone*CPU iPhone OS 3_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/?.*(iPhone*CPU iPhone OS 3_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=3.1
MajorVer=3
MinorVer=1

[Mozilla/?.*(iPhone*CPU iPhone OS 3_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=3.2
MajorVer=3
MinorVer=2

[Mozilla/?.*(iPhone*CPU iPhone OS 4_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/?.*(iPhone*CPU iPhone OS 4_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.1
MajorVer=4
MinorVer=1

[Mozilla/?.*(iPhone*CPU iPhone OS 4_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/?.*(iPhone*CPU iPhone OS 4_2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/?.*(iPhone*CPU iPhone OS 4_2_5* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/?.*(iPhone*CPU iPhone OS 4_3* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=4.3
MajorVer=4
MinorVer=3

[Mozilla/?.*(iPhone;*CPU like Mac OS X;*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPhone
Version=1.0
MajorVer=1
MinorVer=0
Platform=iPhone OSX

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iPod Touch

[iPod Touch]
Parent=DefaultProperties
Browser="iPod Touch"
Platform=iPhone OSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/?.*(iPod*CPU iPhone OS 2_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 2_0_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 2_0_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 2_1_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 2_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.2
MajorVer=2
MinorVer=2

[Mozilla/?.*(iPod*CPU iPhone OS 2_2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=2.2
MajorVer=2
MinorVer=2

[Mozilla/?.*(iPod*CPU iPhone OS 3_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 3_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=3.1
MajorVer=3
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 3_1_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=3.1
MajorVer=3
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 3_1_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=3.1
MajorVer=3
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 3_1_3* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=3.1
MajorVer=3
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 4_* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 4_0_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/?.*(iPod*CPU iPhone OS 4_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.1
MajorVer=4
MinorVer=1

[Mozilla/?.*(iPod*CPU iPhone OS 4_2_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.2
MajorVer=4
MinorVer=2

[Mozilla/?.*(iPod*CPU iPhone OS 4_3* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.3
MajorVer=4
MinorVer=3

[Mozilla/?.*(iPod*CPU iPhone OS 4_3_1* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.3
MajorVer=4
MinorVer=3

[Mozilla/?.*(iPod*CPU iPhone OS 4_3_2* like Mac OS X*)*AppleWebKit/*(*KHTML, like Gecko*)*Version/*Mobile/*Safari/*]
Parent=iPod Touch
Version=4.3
MajorVer=4
MinorVer=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; KDDI

[KDDI]
Parent=DefaultProperties
Browser="KDDI"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[KDDI-* UP.Browser/* (GUI) MMP/*]
Parent=KDDI

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Kindle

[Kindle]
Parent=DefaultProperties
Browser="Kindle"
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/4.0 (compatible; Linux*) NetFront/3.* Kindle/1.0 (screen 600x800)]
Parent=Kindle
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/4.0 (compatible; Linux*) NetFront/3.* Kindle/2.0 (screen 600x800)]
Parent=Kindle
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/5.0 (Linux; U; *) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Version/4.0 Kindle/3.0 (screen 600x800; rotate)]
Parent=Kindle
Version=3.0
MajorVer=3
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Maemo Browser

[Maemo]
Parent=DefaultProperties
Browser="Maemo"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.9.*) Gecko/* Firefox/* Maemo Browser 1.7.*]
Parent=Maemo
Version=1.7
MajorVer=1
MinorVer=7
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Motorola Internet Browser

[Motorola Internet Browser]
Parent=DefaultProperties
Browser="Motorola Internet Browser"
Frames=true
IFrames=true
Tables=true
Cookies=true
isMobileDevice=true

[MOT-*/*]
Parent=Motorola Internet Browser

[MOT-1*/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-8700_/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-A-0A/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-A-2B/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-A-88/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-C???/* MIB/*]
Parent=Motorola Internet Browser

[MOT-GATW_/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-L6/* MIB/*]
Parent=Motorola Internet Browser

[MOT-L7/* MIB/*]
Parent=Motorola Internet Browser

[MOT-M*/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-MP*/* Mozilla/* (compatible; MSIE *; Windows CE; *)]
Parent=Motorola Internet Browser
Win32=true

[MOT-MP*/* Mozilla/4.0 (compatible; MSIE *; Windows CE; *)]
Parent=Motorola Internet Browser
Win32=true

[MOT-SAP4_/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-T*/*]
Parent=Motorola Internet Browser

[MOT-T7*/* MIB/*]
Parent=Motorola Internet Browser

[MOT-T721*]
Parent=Motorola Internet Browser

[MOT-TA02/* MIB/*]
Parent=Motorola Internet Browser

[MOT-V*/*]
Parent=Motorola Internet Browser

[MOT-V*/* MIB/*]
Parent=Motorola Internet Browser

[MOT-V*/* UP.Browser/*]
Parent=Motorola Internet Browser

[MOT-V3/* MIB/*]
Parent=Motorola Internet Browser

[MOT-V4*/* MIB/*]
Parent=Motorola Internet Browser

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Nokia

[Nokia]
Parent=DefaultProperties
Browser="Nokia"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[*Nokia*/*]
Parent=Nokia

[Mozilla/* (SymbianOS/*; ?; *) AppleWebKit/* (KHTML, like Gecko) Safari/*]
Parent=Nokia
Platform=SymbianOS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Openwave Mobile Browser

[Openwave Mobile Browser]
Parent=DefaultProperties
Browser="Openwave Mobile Browser"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[*UP.Browser/*]
Parent=Openwave Mobile Browser

[*UP.Link/*]
Parent=Openwave Mobile Browser

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mobile

[Opera Mobile]
Parent=DefaultProperties
Browser="Opera Mobi"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[Mozilla/?.*(*Windows CE*)*Opera?*]
Parent=Opera Mobile
Browser="Opera Mobile"
Platform=WinCE

[Opera*(*Android*Opera Mobi*)*]
Parent=Opera Mobile
Platform=Android

[Opera*(*Microsoft Windows*Opera Mobi*)*]
Parent=Opera Mobile
Platform=Win

[Opera*(*SymbOS*Opera Mobi*)*]
Parent=Opera Mobile
Platform=SymbianOS

[Opera*(*Windows Mobile*Opera Mobi*)*]
Parent=Opera Mobile
Platform=WinCE

[Opera/*(*Symbian*Opera Mobi*)*]
Parent=Opera Mobile
Platform=SymbianOS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Palm Web

[Palm Web]
Parent=DefaultProperties
Browser="Palm Web"
Platform=webOS
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[Mozilla/5.0 (*webOS*) AppleWebKit/* (KHTML, like Gecko) *Version/1.* *Safari/* *Pixi/1.*]
Parent=Palm Web
Version=1.0
MajorVer=1
MinorVer=0
CssVersion=2

[Mozilla/5.0 (*webOS*) AppleWebKit/* (KHTML, like Gecko) *Version/1.* *Safari/* *Pixi/2.*]
Parent=Palm Web
Version=2.0
MajorVer=2
MinorVer=0
CssVersion=2

[Mozilla/5.0 (*webOS*) AppleWebKit/* (KHTML, like Gecko) *Version/1.* *Safari/* *Pre/2.*]
Parent=Palm Web
Version=2.0
MajorVer=2
MinorVer=0
CssVersion=2

[Mozilla/5.0 (*webOS*) AppleWebKit/* (KHTML, like Gecko) *Version/1.* Safari/* *Pre/1.*]
Parent=Palm Web
Version=1.0
MajorVer=1
MinorVer=0
CssVersion=2

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Playstation

[Playstation]
Parent=DefaultProperties
Browser="Playstation"
Platform=WAP
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true

[Mozilla/* (PLAYSTATION*)]
Parent=Playstation
Browser="Sony PlayStation 3"
Frames=false

[Mozilla/* (PSP (PlayStation Portable)*)]
Parent=Playstation
Browser="PlayStation Portable"

[Sony PS2 (Linux)]
Parent=Playstation
Browser="Sony PS2"
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Pocket PC

[Pocket PC]
Parent=DefaultProperties
Browser="Pocket PC"
Platform=WinCE
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[*(compatible; MSIE *.*; Windows CE; PPC; *)]
Parent=Pocket PC

[HTC-*/* Mozilla/* (compatible; MSIE *.*; Windows CE*)*]
Parent=Pocket PC

[Mozilla/* (compatible; MSPIE *.*; *Windows CE*)*]
Parent=Pocket PC

[T-Mobile* Mozilla/* (compatible; MSIE *.*; Windows CE; *)]
Parent=Pocket PC

[Vodafone* Mozilla/* (compatible; MSIE *.*; Windows CE; *)*]
Parent=Pocket PC

[Windows CE (Pocket PC) - Version *.*]
Parent=Pocket PC

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Polaris

[Polaris]
Parent=DefaultProperties
Browser="Polaris"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[LG-* Polaris/5.* MMP/2.*]
Parent=Polaris
Browser="Polaris LG"
Version=5.0
MajorVer=5
MinorVer=0

[LG-* Polaris/6.* MMP/2.*]
Parent=Polaris
Browser="Polaris LG"
Version=6.0
MajorVer=6
MinorVer=0

[LG-* Polaris/7.* MMP/2.*]
Parent=Polaris
Browser="Polaris LG"
Version=7.0
MajorVer=7
MinorVer=0

[Samsung-* Polaris/5.* MMP/2.*]
Parent=Polaris
Browser="Polaris Samsung"
Version=5.0
MajorVer=5
MinorVer=0

[Samsung-* Polaris/6.* MMP/2.*]
Parent=Polaris
Browser="Polaris Samsung"
Version=6.0
MajorVer=6
MinorVer=0

[Samsung-* Polaris/7.* MMP/2.*]
Parent=Polaris
Browser="Polaris Samsung"
Version=7.0
MajorVer=7
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; SEMC Browser

[SEMC Browser]
Parent=DefaultProperties
Browser="SEMC Browser"
Platform=JAVA
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[*SEMC-Browser/*]
Parent=SEMC Browser

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Teleca

[Teleca]
Parent=DefaultProperties
Browser="Teleca"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Mozilla/5.0 (compatible; Teleca *; Brew 3.0*; U; *)*]
Parent=Teleca
Version=3.0
MajorVer=3
MinorVer=0

[Mozilla/5.0 (compatible; Teleca *; Brew 3.1*; U; *)*]
Parent=Teleca
Version=3.1
MajorVer=3
MinorVer=1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini 2.0

[Opera Mini 2.0]
Parent=DefaultProperties
Browser="Opera Mini"
Version=2.0
MajorVer=2
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(Android*Opera Mini/2.*)*]
Parent=Opera Mini 2.0
Platform=Android

[Opera/*(BlackBerry*Opera Mini/2.*)*]
Parent=Opera Mini 2.0
Platform=BlackBerry OS

[Opera/*(J2ME/MIDP*Opera Mini/2.*)*]
Parent=Opera Mini 2.0
Platform=JAVA

[Opera/*(Series 60*Opera Mini/2.*)*]
Parent=Opera Mini 2.0
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/2.*)*]
Parent=Opera Mini 2.0
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini 3.0

[Opera Mini 3.0]
Parent=DefaultProperties
Browser="Opera Mini"
Version=3.0
MajorVer=3
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(Android*Opera Mini/3.*)*]
Parent=Opera Mini 3.0
Platform=Android

[Opera/*(BlackBerry*Opera Mini/3.*)*]
Parent=Opera Mini 3.0
Platform=BlackBerry OS

[Opera/*(J2ME/MIDP*Opera Mini/3.*)*]
Parent=Opera Mini 3.0
Platform=JAVA

[Opera/*(Series 60*Opera Mini/3.*)*]
Parent=Opera Mini 3.0
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/3.*)*]
Parent=Opera Mini 3.0
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini 4.0

[Opera Mini 4.0]
Parent=DefaultProperties
Browser="Opera Mini"
Version=4.0
MajorVer=4
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(Android*Opera Mini/4.*)*]
Parent=Opera Mini 4.0
Platform=Android

[Opera/*(BlackBerry*Opera Mini/4.*)*]
Parent=Opera Mini 4.0
Platform=BlackBerry OS

[Opera/*(J2ME/MIDP*Opera Mini/4.*)*]
Parent=Opera Mini 4.0
Platform=JAVA

[Opera/*(Series 60*Opera Mini/4.*)*]
Parent=Opera Mini 4.0
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/4.*)*]
Parent=Opera Mini 4.0
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini 5.0

[Opera Mini 5.0]
Parent=DefaultProperties
Browser="Opera Mini"
Version=5.0
MajorVer=5
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(Android*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=Android

[Opera/*(BlackBerry*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=BlackBerry OS

[Opera/*(iPhone*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=iPhone OSX

[Opera/*(J2ME/MIDP*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=JAVA

[Opera/*(Series 60*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/5.*)*]
Parent=Opera Mini 5.0
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini 6.0

[Opera Mini 6.0]
Parent=DefaultProperties
Browser="Opera Mini"
Version=6.0
MajorVer=6
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(*iPhone*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=iPhone OSX

[Opera/*(Android*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=Android

[Opera/*(BlackBerry*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=BlackBerry OS

[Opera/*(J2ME/MIDP*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=JAVA

[Opera/*(Series 60*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/6.*)*]
Parent=Opera Mini 6.0
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Mini Generic

[Opera Mini Generic]
Parent=DefaultProperties
Browser="Opera Mini"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=1

[Opera/*(Android*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=Android

[Opera/*(BlackBerry*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=BlackBerry OS

[Opera/*(iPhone*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=iPhone OSX

[Opera/*(J2ME/MIDP*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=JAVA

[Opera/*(Series 60*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=SymbianOS

[Opera/*(Windows Mobile*Opera Mini/*)*]
Parent=Opera Mini Generic
Platform=WinMobile

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Access NetFront 2.0

[NetFront 2.0]
Parent=DefaultProperties
Browser="Access NetFront"
Version=2.0
MajorVer=2
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[*NetFront/2.*]
Parent=NetFront 2.0

[Mozilla/4.0 (*) *NetFront/2.*]
Parent=NetFront 2.0

[Mozilla/4.0 (*NetFront/2.*)*]
Parent=NetFront 2.0

[SAMSUNG* *NetFront/2.*]
Parent=NetFront 2.0

[SEC-* *NetFront/2.*]
Parent=NetFront 2.0

[SonyEricsson*/* Mozilla/5.0 (SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* *Safari/*]
Parent=NetFront 2.0
Platform=SymbianOS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Access NetFront 3.0

[NetFront 3.0]
Parent=DefaultProperties
Browser="Access NetFront"
Version=3.0
MajorVer=3
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=2

[*NetFront/3.*]
Parent=NetFront 3.0

[Mozilla/4.0 (*) *NetFront/3.*]
Parent=NetFront 3.0

[Mozilla/4.0 (*NetFront/3.*)*]
Parent=NetFront 3.0

[SAMSUNG* *NetFront/3.*]
Parent=NetFront 3.0

[SEC-* *NetFront/3.*]
Parent=NetFront 3.0

[SonyEricsson*; Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* *Safari/*]
Parent=NetFront 3.0
Platform=SymbianOS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Boxee

[Boxee]
Parent=DefaultProperties
Browser="Boxee"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[curl/* (Linux * intel.*dlink.dsm* *; *; beta) boxee/*]
Parent=Boxee
Version=1.0
MajorVer=1
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; GoogleTV

[GoogleTV]
Parent=DefaultProperties
Browser="GoogleTV"
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (X11; U: Linux i686; *) AppleWebKit/* (KHTML, like Gecko) Chrome/5.* Large Screen Safari/* GoogleTV/*]
Parent=GoogleTV
Version=b39389

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netbox

[Netbox]
Parent=DefaultProperties
Browser="Netbox"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=1

[Mozilla/3.01 (compatible; Netbox/*; Linux*)]
Parent=Netbox
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; PowerTV

[PowerTV]
Parent=DefaultProperties
Browser="PowerTV"
Platform=PowerTV
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/4.0 PowerTV/1.5 (Compatible; Spyglass DM 3.2.1, EXPLORER)]
Parent=PowerTV
Version=1.5
MajorVer=1
MinorVer=5

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; WebTV/MSNTV

[WebTV]
Parent=DefaultProperties
Browser="WebTV/MSNTV"
Platform=WebTV
Frames=true
IFrames=true
Tables=true
Cookies=true

[Mozilla/3.0 WebTV/1.*(compatible; MSIE 2.0)]
Parent=WebTV
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/4.0 WebTV/2.0*(compatible; MSIE 3.0)]
Parent=WebTV
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/4.0 WebTV/2.1*(compatible; MSIE 3.0)]
Parent=WebTV
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/4.0 WebTV/2.2*(compatible; MSIE 3.0)]
Parent=WebTV
Version=2.2
MajorVer=2
MinorVer=2

[Mozilla/4.0 WebTV/2.3*(compatible; MSIE 3.0)]
Parent=WebTV
Version=2.3
MajorVer=2
MinorVer=3

[Mozilla/4.0 WebTV/2.4*(compatible; MSIE 3.0)]
Parent=WebTV
Version=2.4
MajorVer=2
MinorVer=4

[Mozilla/4.0 WebTV/2.5*(compatible; MSIE 4.0)]
Parent=WebTV
Version=2.5
MajorVer=2
MinorVer=5
CssVersion=1

[Mozilla/4.0 WebTV/2.6*(compatible; MSIE 4.0)]
Parent=WebTV
Version=2.6
MajorVer=2
MinorVer=6
CssVersion=1

[Mozilla/4.0 WebTV/2.7*(compatible; MSIE 4.0)]
Parent=WebTV
Version=2.7
MajorVer=2
MinorVer=7
CssVersion=1

[Mozilla/4.0 WebTV/2.8*(compatible; MSIE 4.0)]
Parent=WebTV
Version=2.8
MajorVer=2
MinorVer=8
VBScript=true
CssVersion=1

[Mozilla/4.0 WebTV/2.9*(compatible; MSIE 4.0)]
Parent=WebTV
Version=2.9
MajorVer=2
MinorVer=9
VBScript=true
CssVersion=1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Amaya

[Amaya]
Parent=DefaultProperties
Browser="Amaya"
Frames=true
Tables=true

[amaya/10.*]
Parent=Amaya
Version=10.0
MajorVer=10
MinorVer=0

[amaya/11.*]
Parent=Amaya
Version=11.0
MajorVer=11
MinorVer=0

[amaya/7.*]
Parent=Amaya
Version=7.0
MajorVer=7
MinorVer=0

[amaya/8.*]
Parent=Amaya
Version=8.0
MajorVer=8
MinorVer=0
CssVersion=2

[amaya/9.*]
Parent=Amaya
Version=9.0
MajorVer=9
MinorVer=0

[Emacs-w3m/*]
Parent=Emacs/W3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Links

[Links]
Parent=DefaultProperties
Browser="Links"
Frames=true
Tables=true

[Links (0.9*; CYGWIN_NT-5.1*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=WinXP

[Links (0.9*; Darwin*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=MacPPC

[Links (0.9*; FreeBSD*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=FreeBSD

[Links (0.9*; Linux*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=Linux

[Links (0.9*; OS/2*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=OS/2

[Links (0.9*; Unix*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=Unix

[Links (0.9*; Win32*)]
Parent=Links
Version=0.9
MajorVer=0
MinorVer=9
Platform=Win32
Win32=true

[Links (1.0*; CYGWIN_NT-5.1*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Links (1.0*; FreeBSD*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=FreeBSD

[Links (1.0*; Linux*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=Linux

[Links (1.0*; OS/2*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=OS/2

[Links (1.0*; Unix*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=Unix

[Links (1.0*; Win32*)]
Parent=Links
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win32
Win32=true

[Links (2.0*; Linux*)]
Parent=Links
Version=2.0
MajorVer=2
MinorVer=0
Platform=Linux

[Links (2.1*; FreeBSD*)]
Parent=Links
Version=2.1
MajorVer=2
MinorVer=1
Platform=FreeBSD

[Links (2.1*; Linux *)]
Parent=Links
Version=2.1
MajorVer=2
MinorVer=1
Platform=Linux

[Links (2.1*; OpenBSD*)]
Parent=Links
Version=2.1
MajorVer=2
MinorVer=1
Platform=OpenBSD

[Links (2.2*; FreeBSD*)]
Parent=Links
Version=2.2
MajorVer=2
MinorVer=2
Platform=FreeBSD

[Links (2.2*; Linux *)]
Parent=Links
Version=2.2
MajorVer=2
MinorVer=2
Platform=Linux

[Links (2.2*; OpenBSD*)]
Parent=Links
Version=2.2
MajorVer=2
MinorVer=2
Platform=OpenBSD

[Links (2.3*; FreeBSD*)]
Parent=Links
Version=2.3
MajorVer=2
MinorVer=3
Platform=Linux

[Links (2.3*; Linux*)]
Parent=Links
Version=2.3
MajorVer=2
MinorVer=3
Platform=Linux

[Links (2.3*; OpenBSD*)]
Parent=Links
Version=2.3
MajorVer=2
MinorVer=3
Platform=OpenBSD

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lynx

[Lynx]
Parent=DefaultProperties
Browser="Lynx"
Frames=true
IFrames=true
Tables=true

[Lynx *]
Parent=Lynx

[Lynx/2.3*]
Parent=Lynx
Version=2.3
MajorVer=2
MinorVer=3

[Lynx/2.4*]
Parent=Lynx
Version=2.4
MajorVer=2
MinorVer=4

[Lynx/2.5*]
Parent=Lynx
Version=2.5
MajorVer=2
MinorVer=5

[Lynx/2.6*]
Parent=Lynx
Version=2.6
MajorVer=2
MinorVer=6

[Lynx/2.7*]
Parent=Lynx
Version=2.7
MajorVer=2
MinorVer=7

[Lynx/2.8*]
Parent=Lynx
Version=2.8
MajorVer=2
MinorVer=8

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; NCSA Mosaic

[Mosaic]
Parent=DefaultProperties
Browser="Mosaic"
Frames=true
Tables=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; w3m

[w3m]
Parent=DefaultProperties
Browser="w3m"
Frames=true
Tables=true

[w3m/0.1*]
Parent=w3m
Version=0.1
MajorVer=0
MinorVer=1

[w3m/0.2*]
Parent=w3m
Version=0.2
MajorVer=0
MinorVer=2

[w3m/0.3*]
Parent=w3m
Version=0.3
MajorVer=0
MinorVer=3

[w3m/0.4*]
Parent=w3m
Version=0.4
MajorVer=0
MinorVer=4
Cookies=true

[w3m/0.5*]
Parent=w3m
Version=0.5
MajorVer=0
MinorVer=5
Cookies=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; ELinks 0.10

[ELinks 0.10]
Parent=DefaultProperties
Browser="ELinks"
Version=0.10
MinorVer=10
Frames=true
Tables=true

[ELinks (0.10*; *AIX*)]
Parent=ELinks 0.10
Platform=AIX

[ELinks (0.10*; *BeOS*)]
Parent=ELinks 0.10
Platform=BeOS

[ELinks (0.10*; *CygWin*)]
Parent=ELinks 0.10
Platform=CygWin

[ELinks (0.10*; *Darwin*)]
Parent=ELinks 0.10
Platform=Darwin

[ELinks (0.10*; *Digital Unix*)]
Parent=ELinks 0.10
Platform=Digital Unix

[ELinks (0.10*; *FreeBSD*)]
Parent=ELinks 0.10
Platform=FreeBSD

[ELinks (0.10*; *HPUX*)]
Parent=ELinks 0.10
Platform=HP-UX

[ELinks (0.10*; *IRIX*)]
Parent=ELinks 0.10
Platform=IRIX

[ELinks (0.10*; *Linux*)]
Parent=ELinks 0.10
Platform=Linux

[ELinks (0.10*; *NetBSD*)]
Parent=ELinks 0.10
Platform=NetBSD

[ELinks (0.10*; *OpenBSD*)]
Parent=ELinks 0.10
Platform=OpenBSD

[ELinks (0.10*; *OS/2*)]
Parent=ELinks 0.10
Platform=OS/2

[ELinks (0.10*; *RISC*)]
Parent=ELinks 0.10
Platform=RISC OS

[ELinks (0.10*; *Solaris*)]
Parent=ELinks 0.10
Platform=Solaris

[ELinks (0.10*; *Unix*)]
Parent=ELinks 0.10
Platform=Unix

[ELinks/0.10* (*AIX*)]
Parent=ELinks 0.10
Platform=AIX

[ELinks/0.10* (*BeOS*)]
Parent=ELinks 0.10
Platform=BeOS

[ELinks/0.10* (*CygWin*)]
Parent=ELinks 0.10
Platform=CygWin

[ELinks/0.10* (*Darwin*)]
Parent=ELinks 0.10
Platform=Darwin

[ELinks/0.10* (*Digital Unix*)]
Parent=ELinks 0.10
Platform=Digital Unix

[ELinks/0.10* (*FreeBSD*)]
Parent=ELinks 0.10
Platform=FreeBSD

[ELinks/0.10* (*HPUX*)]
Parent=ELinks 0.10
Platform=HP-UX

[ELinks/0.10* (*IRIX*)]
Parent=ELinks 0.10
Platform=IRIX

[ELinks/0.10* (*Linux*)]
Parent=ELinks 0.10
Platform=Linux

[ELinks/0.10* (*NetBSD*)]
Parent=ELinks 0.10
Platform=NetBSD

[ELinks/0.10* (*OpenBSD*)]
Parent=ELinks 0.10
Platform=OpenBSD

[ELinks/0.10* (*OS/2*)]
Parent=ELinks 0.10
Platform=OS/2

[ELinks/0.10* (*RISC*)]
Parent=ELinks 0.10
Platform=RISC OS

[ELinks/0.10* (*Solaris*)]
Parent=ELinks 0.10
Platform=Solaris

[ELinks/0.10* (*Unix*)]
Parent=ELinks 0.10
Platform=Unix

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; ELinks 0.11

[ELinks 0.11]
Parent=DefaultProperties
Browser="ELinks"
Version=0.11
MinorVer=11
Frames=true
Tables=true

[ELinks (0.11*; *AIX*)]
Parent=ELinks 0.11
Platform=AIX

[ELinks (0.11*; *BeOS*)]
Parent=ELinks 0.11
Platform=BeOS

[ELinks (0.11*; *CygWin*)]
Parent=ELinks 0.11
Platform=CygWin

[ELinks (0.11*; *Darwin*)]
Parent=ELinks 0.11
Platform=Darwin

[ELinks (0.11*; *Digital Unix*)]
Parent=ELinks 0.11
Platform=Digital Unix

[ELinks (0.11*; *FreeBSD*)]
Parent=ELinks 0.11
Platform=FreeBSD

[ELinks (0.11*; *HPUX*)]
Parent=ELinks 0.11
Platform=HP-UX

[ELinks (0.11*; *IRIX*)]
Parent=ELinks 0.11
Platform=IRIX

[ELinks (0.11*; *Linux*)]
Parent=ELinks 0.11
Platform=Linux

[ELinks (0.11*; *NetBSD*)]
Parent=ELinks 0.11
Platform=NetBSD

[ELinks (0.11*; *OpenBSD*)]
Parent=ELinks 0.11
Platform=OpenBSD

[ELinks (0.11*; *OS/2*)]
Parent=ELinks 0.11
Platform=OS/2

[ELinks (0.11*; *RISC*)]
Parent=ELinks 0.11
Platform=RISC OS

[ELinks (0.11*; *Solaris*)]
Parent=ELinks 0.11
Platform=Solaris

[ELinks (0.11*; *Unix*)]
Parent=ELinks 0.11
Platform=Unix

[ELinks/0.11* (*AIX*)]
Parent=ELinks 0.11
Platform=AIX

[ELinks/0.11* (*BeOS*)]
Parent=ELinks 0.11
Platform=BeOS

[ELinks/0.11* (*CygWin*)]
Parent=ELinks 0.11
Platform=CygWin

[ELinks/0.11* (*Darwin*)]
Parent=ELinks 0.11
Platform=Darwin

[ELinks/0.11* (*Digital Unix*)]
Parent=ELinks 0.11
Platform=Digital Unix

[ELinks/0.11* (*FreeBSD*)]
Parent=ELinks 0.11
Platform=FreeBSD

[ELinks/0.11* (*HPUX*)]
Parent=ELinks 0.11
Platform=HP-UX

[ELinks/0.11* (*IRIX*)]
Parent=ELinks 0.11
Platform=IRIX

[ELinks/0.11* (*Linux*)]
Parent=ELinks 0.11
Platform=Linux

[ELinks/0.11* (*NetBSD*)]
Parent=ELinks 0.11
Platform=NetBSD

[ELinks/0.11* (*OpenBSD*)]
Parent=ELinks 0.11
Platform=OpenBSD

[ELinks/0.11* (*OS/2*)]
Parent=ELinks 0.11
Platform=OS/2

[ELinks/0.11* (*RISC*)]
Parent=ELinks 0.11
Platform=RISC OS

[ELinks/0.11* (*Solaris*)]
Parent=ELinks 0.11
Platform=Solaris

[ELinks/0.11* (*Unix*)]
Parent=ELinks 0.11
Platform=Unix

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; ELinks 0.12

[ELinks 0.12]
Parent=DefaultProperties
Browser="ELinks"
Version=0.12
MinorVer=12
Frames=true
Tables=true

[ELinks (0.12*; *AIX*)]
Parent=ELinks 0.12
Platform=AIX

[ELinks (0.12*; *BeOS*)]
Parent=ELinks 0.12
Platform=BeOS

[ELinks (0.12*; *CygWin*)]
Parent=ELinks 0.12
Platform=CygWin

[ELinks (0.12*; *Darwin*)]
Parent=ELinks 0.12
Platform=Darwin

[ELinks (0.12*; *Digital Unix*)]
Parent=ELinks 0.12
Platform=Digital Unix

[ELinks (0.12*; *FreeBSD*)]
Parent=ELinks 0.12
Platform=FreeBSD

[ELinks (0.12*; *HPUX*)]
Parent=ELinks 0.12
Platform=HP-UX

[ELinks (0.12*; *IRIX*)]
Parent=ELinks 0.12
Platform=IRIX

[ELinks (0.12*; *Linux*)]
Parent=ELinks 0.12
Platform=Linux

[ELinks (0.12*; *NetBSD*)]
Parent=ELinks 0.12
Platform=NetBSD

[ELinks (0.12*; *OpenBSD*)]
Parent=ELinks 0.12
Platform=OpenBSD

[ELinks (0.12*; *OS/2*)]
Parent=ELinks 0.12
Platform=OS/2

[ELinks (0.12*; *RISC*)]
Parent=ELinks 0.12
Platform=RISC OS

[ELinks (0.12*; *Solaris*)]
Parent=ELinks 0.12
Platform=Solaris

[ELinks (0.12*; *Unix*)]
Parent=ELinks 0.12
Platform=Unix

[ELinks/0.12* (*AIX*)]
Parent=ELinks 0.12
Platform=AIX

[ELinks/0.12* (*BeOS*)]
Parent=ELinks 0.12
Platform=BeOS

[ELinks/0.12* (*CygWin*)]
Parent=ELinks 0.12
Platform=CygWin

[ELinks/0.12* (*Darwin*)]
Parent=ELinks 0.12
Platform=Darwin

[ELinks/0.12* (*Digital Unix*)]
Parent=ELinks 0.12
Platform=Digital Unix

[ELinks/0.12* (*FreeBSD*)]
Parent=ELinks 0.12
Platform=FreeBSD

[ELinks/0.12* (*HPUX*)]
Parent=ELinks 0.12
Platform=HP-UX

[ELinks/0.12* (*IRIX*)]
Parent=ELinks 0.12
Platform=IRIX

[ELinks/0.12* (*Linux*)]
Parent=ELinks 0.12
Platform=Linux

[ELinks/0.12* (*NetBSD*)]
Parent=ELinks 0.12
Platform=NetBSD

[ELinks/0.12* (*OpenBSD*)]
Parent=ELinks 0.12
Platform=OpenBSD

[ELinks/0.12* (*OS/2*)]
Parent=ELinks 0.12
Platform=OS/2

[ELinks/0.12* (*RISC*)]
Parent=ELinks 0.12
Platform=RISC OS

[ELinks/0.12* (*Solaris*)]
Parent=ELinks 0.12
Platform=Solaris

[ELinks/0.12* (*Unix*)]
Parent=ELinks 0.12
Platform=Unix

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; ELinks 0.13

[ELinks 0.13]
Parent=DefaultProperties
Browser="ELinks"
Version=0.13
MinorVer=13
Frames=true
Tables=true

[ELinks (0.13*; *AIX*)]
Parent=ELinks 0.13
Platform=AIX

[ELinks (0.13*; *BeOS*)]
Parent=ELinks 0.13
Platform=BeOS

[ELinks (0.13*; *CygWin*)]
Parent=ELinks 0.13
Platform=CygWin

[ELinks (0.13*; *Darwin*)]
Parent=ELinks 0.13
Platform=Darwin

[ELinks (0.13*; *Digital Unix*)]
Parent=ELinks 0.13
Platform=Digital Unix

[ELinks (0.13*; *FreeBSD*)]
Parent=ELinks 0.13
Platform=FreeBSD

[ELinks (0.13*; *HPUX*)]
Parent=ELinks 0.13
Platform=HP-UX

[ELinks (0.13*; *IRIX*)]
Parent=ELinks 0.13
Platform=IRIX

[ELinks (0.13*; *Linux*)]
Parent=ELinks 0.13
Platform=Linux

[ELinks (0.13*; *NetBSD*)]
Parent=ELinks 0.13
Platform=NetBSD

[ELinks (0.13*; *OpenBSD*)]
Parent=ELinks 0.13
Platform=OpenBSD

[ELinks (0.13*; *OS/2*)]
Parent=ELinks 0.13
Platform=OS/2

[ELinks (0.13*; *RISC*)]
Parent=ELinks 0.13
Platform=RISC OS

[ELinks (0.13*; *Solaris*)]
Parent=ELinks 0.13
Platform=Solaris

[ELinks (0.13*; *Unix*)]
Parent=ELinks 0.13
Platform=Unix

[ELinks/0.13* (*AIX*)]
Parent=ELinks 0.13
Platform=AIX

[ELinks/0.13* (*BeOS*)]
Parent=ELinks 0.13
Platform=BeOS

[ELinks/0.13* (*CygWin*)]
Parent=ELinks 0.13
Platform=CygWin

[ELinks/0.13* (*Darwin*)]
Parent=ELinks 0.13
Platform=Darwin

[ELinks/0.13* (*Digital Unix*)]
Parent=ELinks 0.13
Platform=Digital Unix

[ELinks/0.13* (*FreeBSD*)]
Parent=ELinks 0.13
Platform=FreeBSD

[ELinks/0.13* (*HPUX*)]
Parent=ELinks 0.13
Platform=HP-UX

[ELinks/0.13* (*IRIX*)]
Parent=ELinks 0.13
Platform=IRIX

[ELinks/0.13* (*Linux*)]
Parent=ELinks 0.13
Platform=Linux

[ELinks/0.13* (*NetBSD*)]
Parent=ELinks 0.13
Platform=NetBSD

[ELinks/0.13* (*OpenBSD*)]
Parent=ELinks 0.13
Platform=OpenBSD

[ELinks/0.13* (*OS/2*)]
Parent=ELinks 0.13
Platform=OS/2

[ELinks/0.13* (*RISC*)]
Parent=ELinks 0.13
Platform=RISC OS

[ELinks/0.13* (*Solaris*)]
Parent=ELinks 0.13
Platform=Solaris

[ELinks/0.13* (*Unix*)]
Parent=ELinks 0.13
Platform=Unix

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; ELinks 0.9

[ELinks 0.9]
Parent=DefaultProperties
Browser="ELinks"
Version=0.9
MinorVer=9
Frames=true
Tables=true

[ELinks (0.9*; *AIX*)]
Parent=ELinks 0.9
Platform=AIX

[ELinks (0.9*; *BeOS*)]
Parent=ELinks 0.9
Platform=BeOS

[ELinks (0.9*; *CygWin*)]
Parent=ELinks 0.9
Platform=CygWin

[ELinks (0.9*; *Darwin*)]
Parent=ELinks 0.9
Platform=Darwin

[ELinks (0.9*; *Digital Unix*)]
Parent=ELinks 0.9
Platform=Digital Unix

[ELinks (0.9*; *FreeBSD*)]
Parent=ELinks 0.9
Platform=FreeBSD

[ELinks (0.9*; *HPUX*)]
Parent=ELinks 0.9
Platform=HP-UX

[ELinks (0.9*; *IRIX*)]
Parent=ELinks 0.9
Platform=IRIX

[ELinks (0.9*; *Linux*)]
Parent=ELinks 0.9
Platform=Linux

[ELinks (0.9*; *NetBSD*)]
Parent=ELinks 0.9
Platform=NetBSD

[ELinks (0.9*; *OpenBSD*)]
Parent=ELinks 0.9
Platform=OpenBSD

[ELinks (0.9*; *OS/2*)]
Parent=ELinks 0.9
Platform=OS/2

[ELinks (0.9*; *RISC*)]
Parent=ELinks 0.9
Platform=RISC OS

[ELinks (0.9*; *Solaris*)]
Parent=ELinks 0.9
Platform=Solaris

[ELinks (0.9*; *Unix*)]
Parent=ELinks 0.9
Platform=Unix

[ELinks/0.9* (*AIX*)]
Parent=ELinks 0.9
Platform=AIX

[ELinks/0.9* (*BeOS*)]
Parent=ELinks 0.9
Platform=BeOS

[ELinks/0.9* (*CygWin*)]
Parent=ELinks 0.9
Platform=CygWin

[ELinks/0.9* (*Darwin*)]
Parent=ELinks 0.9
Platform=Darwin

[ELinks/0.9* (*Digital Unix*)]
Parent=ELinks 0.9
Platform=Digital Unix

[ELinks/0.9* (*FreeBSD*)]
Parent=ELinks 0.9
Platform=FreeBSD

[ELinks/0.9* (*HPUX*)]
Parent=ELinks 0.9
Platform=HP-UX

[ELinks/0.9* (*IRIX*)]
Parent=ELinks 0.9
Platform=IRIX

[ELinks/0.9* (*Linux*)]
Parent=ELinks 0.9
Platform=Linux

[ELinks/0.9* (*NetBSD*)]
Parent=ELinks 0.9
Platform=NetBSD

[ELinks/0.9* (*OpenBSD*)]
Parent=ELinks 0.9
Platform=OpenBSD

[ELinks/0.9* (*OS/2*)]
Parent=ELinks 0.9
Platform=OS/2

[ELinks/0.9* (*RISC*)]
Parent=ELinks 0.9
Platform=RISC OS

[ELinks/0.9* (*Solaris*)]
Parent=ELinks 0.9
Platform=Solaris

[ELinks/0.9* (*Unix*)]
Parent=ELinks 0.9
Platform=Unix

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Camino

[Camino]
Parent=DefaultProperties
Browser="Camino"
Platform=MacOSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (Macintosh; *Intel Mac OS X*; *; rv:1.9.*) Gecko/* Camino/2.0*]
Parent=Camino
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/0.7*]
Parent=Camino
Version=0.7
MajorVer=0
MinorVer=7
Beta=true

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/0.8*]
Parent=Camino
Version=0.8
MajorVer=0
MinorVer=8
Beta=true

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/0.9*]
Parent=Camino
Version=0.9
MajorVer=0
MinorVer=9
Beta=true

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.0*]
Parent=Camino
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.2*]
Parent=Camino
Version=1.2
MajorVer=1
MinorVer=2

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.3*]
Parent=Camino
Version=1.3
MajorVer=1
MinorVer=3
Platform=MacOSX

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.4*]
Parent=Camino
Version=1.4
MajorVer=1
MinorVer=4
Platform=MacOSX

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.5*]
Parent=Camino
Version=1.5
MajorVer=1
MinorVer=5
Platform=MacOSX

[Mozilla/5.0 (Macintosh; *Mac OS X*) Gecko/* Camino/1.6*]
Parent=Camino
Version=1.6
MajorVer=1
MinorVer=6
Platform=MacOSX

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Chimera

[Chimera]
Parent=DefaultProperties
Browser="Chimera"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/5.0 (Macintosh; U; *Mac OS X*; *; rv:1.*) Gecko/* Chimera/*]
Parent=Chimera
Platform=MacOSX

[Mozilla/5.0 Gecko/* Chimera/*]
Parent=Chimera

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Dillo

[Dillo]
Parent=DefaultProperties
Browser="Dillo"
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Dillo/0.6*]
Parent=Dillo
Version=0.6
MajorVer=0
MinorVer=6

[Dillo/0.7*]
Parent=Dillo
Version=0.7
MajorVer=0
MinorVer=7

[Dillo/0.8*]
Parent=Dillo
Version=0.8
MajorVer=0
MinorVer=8

[Dillo/2.0]
Parent=Dillo
Version=2.0
MajorVer=2
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Emacs/W3

[Emacs/W3]
Parent=DefaultProperties
Browser="Emacs/W3"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=default

[Emacs/W3/2.* (Unix*]
Parent=Emacs/W3
Version=2.0
MajorVer=2
MinorVer=0
Platform=Unix

[Emacs/W3/2.* (X11*]
Parent=Emacs/W3
Version=2.0
MajorVer=2
MinorVer=0
Platform=Linux

[Emacs/W3/3.* (Unix*]
Parent=Emacs/W3
Version=3.0
MajorVer=3
MinorVer=0
Platform=Unix

[Emacs/W3/3.* (X11*]
Parent=Emacs/W3
Version=3.0
MajorVer=3
MinorVer=0
Platform=Linux

[Emacs/W3/4.* (Unix*]
Parent=Emacs/W3
Version=4.0
MajorVer=4
MinorVer=0
Platform=Unix

[Emacs/W3/4.* (X11*]
Parent=Emacs/W3
Version=4.0
MajorVer=4
MinorVer=0
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; fantomas

[fantomas]
Parent=DefaultProperties
Browser="fantomas"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/4.0 (cloakBrowser)]
Parent=fantomas
Browser="fantomas cloakBrowser"

[Mozilla/4.0 (fantomas shadowMaker Browser)]
Parent=fantomas
Browser="fantomas shadowMaker Browser"

[Mozilla/4.0 (fantomBrowser)]
Parent=fantomas
Browser="fantomas fantomBrowser"

[Mozilla/4.0 (fantomCrew Browser)]
Parent=fantomas
Browser="fantomas fantomCrew Browser"

[Mozilla/4.0 (stealthBrowser)]
Parent=fantomas
Browser="fantomas stealthBrowser"

[multiBlocker browser*]
Parent=fantomas
Browser="fantomas multiBlocker browser"

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; FrontPage

[FrontPage]
Parent=DefaultProperties
Browser="FrontPage"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/?* (compatible; MS FrontPage*)]
Parent=FrontPage

[MSFrontPage/*]
Parent=FrontPage

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Galeon

[Galeon]
Parent=DefaultProperties
Browser="Galeon"
Platform=Linux
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (X11; U; Linux*) Gecko/* Galeon/1.*]
Parent=Galeon
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (X11; U; Linux*) Gecko/* Galeon/2.*]
Parent=Galeon
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/5.0 Galeon/1.* (X11; Linux*)*]
Parent=Galeon
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 Galeon/2.* (X11; Linux*)*]
Parent=Galeon
Version=2.0
MajorVer=2
MinorVer=0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; HP Secure Web Browser

[HP Secure Web Browser]
Parent=DefaultProperties
Browser="HP Secure Web Browser"
Platform=OpenVMS
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.0*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.1*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.1
MajorVer=1
MinorVer=1

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.2*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.2
MajorVer=1
MinorVer=2

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.3*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.3
MajorVer=1
MinorVer=3

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.4*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.4
MajorVer=1
MinorVer=4

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.5*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.5
MajorVer=1
MinorVer=5

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.6*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.6
MajorVer=1
MinorVer=6

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.7*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.7
MajorVer=1
MinorVer=7

[Mozilla/5.0 (X11; U; OpenVMS*; *; rv:1.8*) Gecko/*]
Parent=HP Secure Web Browser
Version=1.8
MajorVer=1
MinorVer=8

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IBrowse

[IBrowse]
Parent=DefaultProperties
Browser="IBrowse"
Platform=Amiga
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Arexx (compatible; MSIE 6.0; AmigaOS5.0) IBrowse 4.0]
Parent=IBrowse
Version=4.0
MajorVer=4
MinorVer=0

[IBrowse/1.22 (AmigaOS *)]
Parent=IBrowse
Version=1.22
MajorVer=1
MinorVer=22

[IBrowse/2.1 (AmigaOS *)]
Parent=IBrowse
Version=2.1
MajorVer=2
MinorVer=1

[IBrowse/2.2 (AmigaOS *)]
Parent=IBrowse
Version=2.2
MajorVer=2
MinorVer=2

[IBrowse/2.3 (AmigaOS *)]
Parent=IBrowse
Version=2.2
MajorVer=2
MinorVer=3

[Mozilla/* (Win98; I) IBrowse/2.1 (AmigaOS 3.1)]
Parent=IBrowse
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/* (Win98; I) IBrowse/2.2 (AmigaOS 3.1)]
Parent=IBrowse
Version=2.2
MajorVer=2
MinorVer=2

[Mozilla/* (Win98; I) IBrowse/2.3 (AmigaOS 3.1)]
Parent=IBrowse
Version=2.3
MajorVer=2
MinorVer=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iCab

[iCab]
Parent=DefaultProperties
Browser="iCab"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=1

[iCab/2.7* (Macintosh; ?; 68K*)]
Parent=iCab
Version=2.7
MajorVer=2
MinorVer=7
Platform=Mac68K

[iCab/2.7* (Macintosh; ?; PPC*)]
Parent=iCab
Version=2.7
MajorVer=2
MinorVer=7
Platform=MacPPC

[iCab/2.8* (Macintosh; ?; *Mac OS X*)]
Parent=iCab
Version=2.8
MajorVer=2
MinorVer=8
Platform=MacOSX

[iCab/2.8* (Macintosh; ?; 68K*)]
Parent=iCab
Version=2.8
MajorVer=2
MinorVer=8
Platform=Mac68K

[iCab/2.8* (Macintosh; ?; PPC)]
Parent=iCab
Version=2.8
MajorVer=2
MinorVer=8
Platform=MacPPC

[iCab/2.9* (Macintosh; ?; *Mac OS X*)]
Parent=iCab
Version=2.9
MajorVer=2
MinorVer=9
Platform=MacOSX

[iCab/2.9* (Macintosh; ?; 68K*)]
Parent=iCab
Version=2.9
MajorVer=2
MinorVer=9
Platform=Mac68K

[iCab/2.9* (Macintosh; ?; PPC*)]
Parent=iCab
Version=2.9
MajorVer=2
MinorVer=9
Platform=MacPPC

[iCab/3.0* (Macintosh; ?; *Mac OS X*)]
Parent=iCab
Version=3.0
MajorVer=3
MinorVer=0
Platform=MacOSX
CssVersion=2

[iCab/3.0* (Macintosh; ?; PPC*)]
Parent=iCab
Version=3.0
MajorVer=3
MinorVer=0
Platform=MacPPC
CssVersion=2

[iCab/4.0 (Macintosh; U; *Mac OS X)]
Parent=iCab
Version=4.0
MajorVer=4
MinorVer=0
Platform=MacOSX

[Mozilla/* (compatible; iCab 3.0*; Macintosh; *Mac OS X*)]
Parent=iCab
Version=3.0
MajorVer=3
MinorVer=0
Platform=MacOSX
CssVersion=2

[Mozilla/* (compatible; iCab 3.0*; Macintosh; ?; PPC*)]
Parent=iCab
Version=3.0
MajorVer=3
MinorVer=0
Platform=MacPPC
CssVersion=2

[Mozilla/4.5 (compatible; iCab 2.7*; Macintosh; ?; 68K*)]
Parent=iCab
Version=2.7
MajorVer=2
MinorVer=7
Platform=Mac68K

[Mozilla/4.5 (compatible; iCab 2.7*; Macintosh; ?; PPC*)]
Parent=iCab
Version=2.7
MajorVer=2
MinorVer=7
Platform=MacPPC

[Mozilla/4.5 (compatible; iCab 2.8*; Macintosh; ?; *Mac OS X*)]
Parent=iCab
Version=2.8
MajorVer=2
MinorVer=8
Platform=MacOSX

[Mozilla/4.5 (compatible; iCab 2.8*; Macintosh; ?; PPC*)]
Parent=iCab
Version=2.8
MajorVer=2
MinorVer=8
Platform=MacPPC

[Mozilla/4.5 (compatible; iCab 2.9*; Macintosh; *Mac OS X*)]
Parent=iCab
Version=2.9
MajorVer=2
MinorVer=9
Platform=MacOSX

[Mozilla/4.5 (compatible; iCab 2.9*; Macintosh; ?; PPC*)]
Parent=iCab
Version=2.9
MajorVer=2
MinorVer=9
Platform=MacPPC

[Mozilla/4.5 (compatible; iCab 4.2*; Macintosh; *Mac OS X*)]
Parent=iCab
Version=4.2
MajorVer=4
MinorVer=2
Platform=MacOSX

[Mozilla/5.0 (Macintosh; U; Intel Mac OS X*; *) AppleWebKit/* (KHTML, like Gecko) iCab/4.7 Safari/*]
Parent=iCab
Version=4.7
MajorVer=4
MinorVer=7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; iSiloX

[iSiloX]
Parent=DefaultProperties
Browser="iSiloX"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[iSiloX/4.0* MacOS]
Parent=iSiloX
Version=4.0
MajorVer=4
MinorVer=0
Platform=MacPPC

[iSiloX/4.0* Windows/32]
Parent=iSiloX
Version=4.0
MajorVer=4
MinorVer=0
Platform=Win32
Win32=true

[iSiloX/4.1* MacOS]
Parent=iSiloX
Version=4.1
MajorVer=4
MinorVer=1
Platform=MacPPC

[iSiloX/4.1* Windows/32]
Parent=iSiloX
Version=4.1
MajorVer=4
MinorVer=1
Platform=Win32
Win32=true

[iSiloX/4.2* MacOS]
Parent=iSiloX
Version=4.2
MajorVer=4
MinorVer=2
Platform=MacPPC

[iSiloX/4.2* Windows/32]
Parent=iSiloX
Version=4.2
MajorVer=4
MinorVer=2
Platform=Win32
Win32=true

[iSiloX/4.3* MacOS]
Parent=iSiloX
Version=4.3
MajorVer=4
MinorVer=4
Platform=MacOSX

[iSiloX/4.3* Windows/32]
Parent=iSiloX
Version=4.3
MajorVer=4
MinorVer=3
Platform=Win32
Win32=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lycoris Desktop/LX

[Lycoris Desktop/LX]
Parent=DefaultProperties
Browser="Lycoris Desktop/LX"
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/5.0 (*Linux i686* *Desktop/LX Amethyst*) *Gecko/*]
Parent=Lycoris Desktop/LX
Version=1.0
MajorVer=1
MinorVer=0
Platform=Linux

[Mozilla/4.0 (VMS_Mosaic)]
Parent=Mosaic
Platform=OpenVMS

[VMS_Mosaic/3.7*]
Parent=Mosaic
Version=3.7
MajorVer=3
MinorVer=7
Platform=OpenVMS

[VMS_Mosaic/3.8*]
Parent=Mosaic
Version=3.8
MajorVer=3
MinorVer=8
Platform=OpenVMS

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; NetPositive

[NetPositive]
Parent=DefaultProperties
Browser="NetPositive"
Platform=BeOS
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[*NetPositive/2.2*]
Parent=NetPositive
Version=2.2
MajorVer=2
MinorVer=2

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Shiira

[Shiira]
Parent=DefaultProperties
Browser="Shiira"
Platform=MacOSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/0.9*]
Parent=Shiira
Version=0.9
MajorVer=0
MinorVer=9

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/1.0*]
Parent=Shiira
Version=1.0
MajorVer=1
MinorVer=0

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/1.1*]
Parent=Shiira
Version=1.1
MajorVer=1
MinorVer=1

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/1.2*]
Parent=Shiira
Version=1.2
MajorVer=1
MinorVer=2

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/2.1*]
Parent=Shiira
Version=2.1
MajorVer=2
MinorVer=1

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* *Shiira/2.2*]
Parent=Shiira
Version=2.2
MajorVer=2
MinorVer=2

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; K-Meleon 1.0

[K-Meleon 1.0]
Parent=DefaultProperties
Browser="K-Meleon"
Version=1.0
MajorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (Windows; *; Win95; *; rv:1.*) Gecko/* K-Meleon/1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win95

[Mozilla/5.0 (Windows; *; Win98; *; rv:1.*) Gecko/* K-Meleon/1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win98

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* K-Meleon?1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.*) Gecko/* K-Meleon/1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.*) Gecko/* K-Meleon/1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2003

[Mozilla/5.0 (Windows; *; WinNT4.0; *; rv:1.*) Gecko/* K-Meleon/1.0*]
Parent=K-Meleon 1.0
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; K-Meleon 1.1

[K-Meleon 1.1]
Parent=DefaultProperties
Browser="K-Meleon"
Version=1.1
MajorVer=1
MinorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (Windows; *; Win95; *; rv:1.*) Gecko/* K-Meleon/1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win95

[Mozilla/5.0 (Windows; *; Win98; *; rv:1.*) Gecko/* K-Meleon/1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win98

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* K-Meleon?1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.*) Gecko/* K-Meleon/1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.*) Gecko/* K-Meleon/1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2003

[Mozilla/5.0 (Windows; *; WinNT4.0; *; rv:1.*) Gecko/* K-Meleon/1.1*]
Parent=K-Meleon 1.1
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; K-Meleon 1.5

[K-Meleon 1.5]
Parent=DefaultProperties
Browser="K-Meleon"
Version=1.5
MajorVer=1
MinorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (Windows; *; Win95; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win95

[Mozilla/5.0 (Windows; *; Win98; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win98

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* K-Meleon?1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Platform=Win7

[Mozilla/5.0 (Windows; *; WinNT4.0; *; rv:1.*) Gecko/* K-Meleon/1.5*]
Parent=K-Meleon 1.5
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; K-Meleon 1.6

[K-Meleon 1.6]
Parent=DefaultProperties
Browser="K-Meleon"
Version=1.6
MajorVer=1
MinorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* K-Meleon?1.6*]
Parent=K-Meleon 1.6
Version=1.0
MajorVer=1
MinorVer=0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.*) Gecko/* K-Meleon/1.6*]
Parent=K-Meleon 1.6
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.*) Gecko/* K-Meleon/1.6*]
Parent=K-Meleon 1.6
Version=1.0
MajorVer=1
MinorVer=0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.*) Gecko/* K-Meleon/1.6*]
Parent=K-Meleon 1.6
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.*) Gecko/* K-Meleon/1.6*]
Parent=K-Meleon 1.6
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Konqueror 3.0

[Konqueror 3.0]
Parent=DefaultProperties
Browser="Konqueror"
Version=3.0
MajorVer=3
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[*Konqueror/3.*]
Parent=Konqueror 3.0
Version=3.0
MajorVer=3
MinorVer=0
IFrames=false

[*Konqueror/3.*FreeBSD*]
Parent=Konqueror 3.0
Version=3.0
MajorVer=3
MinorVer=0
Platform=FreeBSD
IFrames=false

[*Konqueror/3.*Linux*]
Parent=Konqueror 3.0
Version=3.0
MajorVer=3
MinorVer=0
Platform=Linux

[*Konqueror/3.*OpenBSD*]
Parent=Konqueror 3.0
Version=3.0
MajorVer=3
MinorVer=0
Platform=OpenBSD

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Konqueror 4.0

[Konqueror 4.0]
Parent=DefaultProperties
Browser="Konqueror"
Version=4.0
MajorVer=4
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (compatible; Konqueror/4.*; *Linux) KHTML/4.* (like Gecko)]
Parent=Konqueror 4.0
Platform=Linux

[Mozilla/5.0 (compatible; Konqueror/4.*; FreeBSD) KHTML/4.* (like Gecko)]
Parent=Konqueror 4.0
Platform=FreeBSD

[Mozilla/5.0 (compatible; Konqueror/4.*; NetBSD) KHTML/4.* (like Gecko)]
Parent=Konqueror 4.0
Platform=NetBSD

[Mozilla/5.0 (compatible; Konqueror/4.0*) KHTML/4.* (like Gecko)]
Parent=Konqueror 4.0

[Mozilla/5.0 (compatible; Konqueror/4.0*; Debian) KHTML/4.* (like Gecko)]
Parent=Konqueror 4.0
Platform=Debian

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Konqueror 4.5

[Konqueror 4.5]
Parent=DefaultProperties
Browser="Konqueror"
Version=4.5
MajorVer=4
MinorVer=5
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (compatible; Konqueror/4.5*) KHTML/4.5* (like Gecko)*]
Parent=Konqueror 4.5

[Mozilla/5.0 (compatible; Konqueror/4.5*; *Linux*) KHTML/4.5* (like Gecko)*]
Parent=Konqueror 4.5
Platform=Linux

[Mozilla/5.0 (compatible; Konqueror/4.5*; Debian) KHTML/4.5* (like Gecko)*]
Parent=Konqueror 4.5
Platform=Debian

[Mozilla/5.0 (compatible; Konqueror/4.5*; FreeBSD) KHTML/4.5* (like Gecko)*]
Parent=Konqueror 4.5
Platform=FreeBSD

[Mozilla/5.0 (compatible; Konqueror/4.5*; NetBSD) KHTML/4.5* (like Gecko)*]
Parent=Konqueror 4.5
Platform=NetBSD

[Mozilla/5.0 (compatible; Konqueror/4.5*; Windows) KHTML/4.5* (like Gecko)]
Parent=Konqueror 4.5
Platform=Win

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Konqueror 4.6

[Konqueror 4.6]
Parent=DefaultProperties
Browser="Konqueror"
Version=4.6
MajorVer=4
MinorVer=6
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/5.0 (compatible; Konqueror/4.6*) KHTML/4.6* (like Gecko)*]
Parent=Konqueror 4.6

[Mozilla/5.0 (compatible; Konqueror/4.6*; *Linux*) KHTML/4.6* (like Gecko)*]
Parent=Konqueror 4.6
Platform=Linux

[Mozilla/5.0 (compatible; Konqueror/4.6*; Debian) KHTML/4.6* (like Gecko)*]
Parent=Konqueror 4.6
Platform=Debian

[Mozilla/5.0 (compatible; Konqueror/4.6*; FreeBSD) KHTML/4.6* (like Gecko)*]
Parent=Konqueror 4.6
Platform=FreeBSD

[Mozilla/5.0 (compatible; Konqueror/4.6*; NetBSD) KHTML/4.6* (like Gecko)*]
Parent=Konqueror 4.6
Platform=NetBSD

[Mozilla/5.0 (compatible; Konqueror/4.6*; Windows) KHTML/4.6* (like Gecko)]
Parent=Konqueror 4.6
Platform=Win
Win32=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Safari 2.0

[Safari 2.0]
Parent=DefaultProperties
Browser="Safari"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*CentOS*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=CentOS
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=Linux

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Version/2.* Safari/*]
Parent=Safari 2.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Safari 3.0

[Safari 3.0]
Parent=DefaultProperties
Browser="Safari"
Version=3.0
MajorVer=3
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*CentOS*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=CentOS
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=Linux

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Version/3.* Safari/*]
Parent=Safari 3.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Safari 4.0

[Safari 4.0]
Parent=DefaultProperties
Browser="Safari"
Version=4.0
MajorVer=4
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*CentOS*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=CentOS
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=Linux

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Version/4.* Safari/*]
Parent=Safari 4.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Safari 5.0

[Safari 5.0]
Parent=DefaultProperties
Browser="Safari"
Version=5.0
MajorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=3

[Mozilla/5.0 (*CentOS*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=CentOS
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=Linux

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Version/5.* Safari/*]
Parent=Safari 5.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Safari Generic

[Safari Generic]
Parent=DefaultProperties
Browser="Safari"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=1

[Mozilla/5.0 (*CentOS*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=CentOS
Win32=false

[Mozilla/5.0 (*Linux x86_64*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Win32=false

[Mozilla/5.0 (*Linux*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) *AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*SymbianOS*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/5.0 (*Windows NT 5.0*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.2*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=WinXP

[Mozilla/5.0 (*Windows NT 6.0*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.1*) AppleWebKit/* (KHTML, like Gecko) *Safari/*]
Parent=Safari Generic
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lunascape 5.0

[Lunascape 5.0]
Parent=DefaultProperties
Browser="Lunascape"
Version=5.0
MajorVer=5
Platform=Win32
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT *Lunascape 5.0*)*]
Parent=Lunascape 5.0

[Mozilla/5.0 (Windows; U; Windows NT *; *) AppleWebKit/* (KHTML, like Gecko*) Lunascape/5.0*]
Parent=Lunascape 5.0

[Mozilla/5.0 (Windows; U; Windows NT *; *; rv:1.9.*) Gecko/* Firefox/3.* Lunascape/5.0*]
Parent=Lunascape 5.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lunascape 5.1

[Lunascape 5.1]
Parent=DefaultProperties
Browser="Lunascape"
Version=5.1
MajorVer=5
MinorVer=1
Platform=Win32
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT *Lunascape 5.1*)*]
Parent=Lunascape 5.1

[Mozilla/5.0 (Windows; U; Windows NT *; *) AppleWebKit/* (KHTML, like Gecko*) Lunascape/5.1*]
Parent=Lunascape 5.1

[Mozilla/5.0 (Windows; U; Windows NT *; *; rv:1.9.*) Gecko/* Firefox/3.* Lunascape/5.1*]
Parent=Lunascape 5.1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Lunascape 6.0

[Lunascape 6.0]
Parent=DefaultProperties
Browser="Lunascape"
Version=6.0
MajorVer=6
Platform=Win32
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT *Lunascape 6.0*)*]
Parent=Lunascape 6.0

[Mozilla/5.0 (Windows; U; Windows NT *; *) AppleWebKit/* (KHTML, like Gecko*) Lunascape/6.0*]
Parent=Lunascape 6.0

[Mozilla/5.0 (Windows; U; Windows NT *; *; rv:1.9.*) Gecko/* Firefox/3.* Lunascape/6.0*]
Parent=Lunascape 6.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Maxthon 2.0

[Maxthon 2.0]
Parent=DefaultProperties
Browser="Maxthon"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=2

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.0; *Maxthon*)*]
Parent=Maxthon 2.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.1; *Maxthon*)*]
Parent=Maxthon 2.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.2; *Maxthon*)*]
Parent=Maxthon 2.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 6.0; *Maxthon*)*]
Parent=Maxthon 2.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 6.1; *Maxthon*)*]
Parent=Maxthon 2.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Maxthon 3.0

[Maxthon 3.0]
Parent=DefaultProperties
Browser="Maxthon"
Version=3.0
MajorVer=3
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *) AppleWebKit/* (KHTML, like Gecko) Maxthon/3.0 Safari/*]
Parent=Maxthon 3.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *) AppleWebKit/* (KHTML, like Gecko) Maxthon/3.0 Safari/*]
Parent=Maxthon 3.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *) AppleWebKit/* (KHTML, like Gecko) Maxthon/3.0 Safari/*]
Parent=Maxthon 3.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *) AppleWebKit/* (KHTML, like Gecko) Maxthon/3.0 Safari/*]
Parent=Maxthon 3.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *) AppleWebKit/* (KHTML, like Gecko) Maxthon/3.0 Safari/*]
Parent=Maxthon 3.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; OmniWeb 5.0

[OmniWeb 5.0]
Parent=DefaultProperties
Browser="OmniWeb"
Version=5.0
MajorVer=5
Platform=MacOSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *OmniWeb/*]
Parent=OmniWeb 5.0
CssVersion=1

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko) *OmniWeb/v558.*]
Parent=OmniWeb 5.0
Version=5.0
MajorVer=5
MinorVer=0

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari) *OmniWeb/v563.*]
Parent=OmniWeb 5.0
Version=5.1
MajorVer=5
MinorVer=1

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/420) *OmniWeb/v*]
Parent=OmniWeb 5.0
Version=5.5
MajorVer=5
MinorVer=5

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/522) *OmniWeb/v61?.0.*]
Parent=OmniWeb 5.0
Version=5.6
MajorVer=5
MinorVer=6

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/523.10) *OmniWeb/v621.*]
Parent=OmniWeb 5.0
Version=5.7
MajorVer=5
MinorVer=7

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/525.20) *OmniWeb/v622.*]
Parent=OmniWeb 5.0
Version=5.9
MajorVer=5
MinorVer=9

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/525.20) *OmniWeb/v622.3.*]
Parent=OmniWeb 5.0
Version=5.8
MajorVer=5
MinorVer=8

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/420  (KHTML, like Gecko, Safari) *OmniWeb/v*]
Parent=OmniWeb 5.0
Version=5.5
MajorVer=5
MinorVer=5

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; OmniWeb 5.10

[OmniWeb 5.10]
Parent=DefaultProperties
Browser="OmniWeb"
Version=5.10
MajorVer=5
MinorVer=10
Platform=MacOSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/528.16) *OmniWeb/622.*]
Parent=OmniWeb 5.10

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/528.16) *Version/5.10* *OmniWeb/v622.*]
Parent=OmniWeb 5.10

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; OmniWeb 5.11

[OmniWeb 5.11]
Parent=DefaultProperties
Browser="OmniWeb"
Version=5.11
MajorVer=5
MinorVer=11
Platform=MacOSX
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; OmniWeb 5.11

[Mozilla/5.0 (*Mac OS X*) AppleWebKit/* (KHTML, like Gecko, Safari/*) *Version/5.11* *OmniWeb/*]
Parent=OmniWeb 5.11
Browser="OmniWeb"
Version=5.11
MajorVer=5
MinorVer=11
CssVersion=3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 10.00

[Opera 10.00]
Parent=DefaultProperties
Browser="Opera"
Version=10.00
MajorVer=10
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?10.*]
Parent=Opera 10.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?10.*]
Parent=Opera 10.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?10.*]
Parent=Opera 10.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.*FreeBSD*)*]
Parent=Opera 10.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/10.*Linux*)*]
Parent=Opera 10.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/10.*Mac OS X*)*]
Parent=Opera 10.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.*SunOS*)*]
Parent=Opera 10.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/10.*Win 9x 4.90*)*]
Parent=Opera 10.00
Platform=WinME

[Mozilla/?.*(*Opera/10.*Windows 2000*)*]
Parent=Opera 10.00
Platform=Win2000

[Mozilla/?.*(*Opera/10.*Windows 95*)*]
Parent=Opera 10.00
Platform=Win95

[Mozilla/?.*(*Opera/10.*Windows 98*)*]
Parent=Opera 10.00
Platform=Win98

[Mozilla/?.*(*Opera/10.*Windows ME*)*]
Parent=Opera 10.00

[Mozilla/?.*(*Opera/10.*Windows NT 4.0*)*]
Parent=Opera 10.00
Platform=WinNT

[Mozilla/?.*(*Opera/10.*Windows NT 5.0*)*]
Parent=Opera 10.00
Platform=Win2000

[Mozilla/?.*(*Opera/10.*Windows NT 5.1*)*]
Parent=Opera 10.00
Platform=WinXP

[Mozilla/?.*(*Opera/10.*Windows NT 5.2*)*]
Parent=Opera 10.00
Platform=WinXP

[Mozilla/?.*(*Opera/10.*Windows NT 6.0*)*]
Parent=Opera 10.00
Platform=WinVista

[Mozilla/?.*(*Opera/10.*Windows NT 6.1*)*]
Parent=Opera 10.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?10.*]
Parent=Opera 10.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?10.*]
Parent=Opera 10.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?10.*]
Parent=Opera 10.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?10.*]
Parent=Opera 10.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?10.*]
Parent=Opera 10.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?10.*]
Parent=Opera 10.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?10.*]
Parent=Opera 10.00
Platform=Win7

[Opera/10.*(*FreeBSD*)*]
Parent=Opera 10.00
Platform=FreeBSD
Win32=false

[Opera/10.*(*Linux*)*]
Parent=Opera 10.00
Platform=Linux
Win32=false

[Opera/10.*(*Mac OS X*)*]
Parent=Opera 10.00
Platform=MacOSX
Win32=false

[Opera/10.*(*SunOS*)*]
Parent=Opera 10.00
Platform=SunOS
Win32=false

[Opera/10.*(*Win 9x 4.90*)*]
Parent=Opera 10.00
Platform=WinME

[Opera/10.*(*Windows 2000*)*]
Parent=Opera 10.00
Platform=Win2000

[Opera/10.*(*Windows 95*)*]
Parent=Opera 10.00
Platform=Win95

[Opera/10.*(*Windows 98*)*]
Parent=Opera 10.00
Platform=Win98

[Opera/10.*(*Windows ME*)*]
Parent=Opera 10.00
Platform=WinME

[Opera/10.*(*Windows NT 4.0*)*]
Parent=Opera 10.00
Platform=WinNT

[Opera/10.*(*Windows NT 5.0*)*]
Parent=Opera 10.00
Platform=Win2000

[Opera/10.*(*Windows NT 5.1*)*]
Parent=Opera 10.00
Platform=WinXP

[Opera/10.*(*Windows NT 5.2*)*]
Parent=Opera 10.00
Platform=WinXP

[Opera/10.*(*Windows NT 6.0*)*]
Parent=Opera 10.00
Platform=WinVista

[Opera/10.*(*Windows NT 6.1*)*]
Parent=Opera 10.00
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/10.*]
Parent=Opera 10.00
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/10.*]
Parent=Opera 10.00
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/10.*]
Parent=Opera 10.00
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/10.*]
Parent=Opera 10.00
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/10.*]
Parent=Opera 10.00
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/10.*]
Parent=Opera 10.00
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/10.*]
Parent=Opera 10.00
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/10.*]
Parent=Opera 10.00
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/10.*]
Parent=Opera 10.00
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/10.*]
Parent=Opera 10.00
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/10.*]
Parent=Opera 10.00
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/10.*]
Parent=Opera 10.00
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/10.*]
Parent=Opera 10.00
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/10.*]
Parent=Opera 10.00
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/10.*]
Parent=Opera 10.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 10.10

[Opera 10.10]
Parent=DefaultProperties
Browser="Opera"
Version=10.10
MajorVer=10
MinorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?10.1*]
Parent=Opera 10.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?10.1*]
Parent=Opera 10.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.1*FreeBSD*)*]
Parent=Opera 10.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/10.1*Linux*)*]
Parent=Opera 10.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/10.1*Mac OS X*)*]
Parent=Opera 10.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.1*SunOS*)*]
Parent=Opera 10.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/10.1*Win 9x 4.90*)*]
Parent=Opera 10.10
Platform=WinME

[Mozilla/?.*(*Opera/10.1*Windows 2000*)*]
Parent=Opera 10.10
Platform=Win2000

[Mozilla/?.*(*Opera/10.1*Windows 95*)*]
Parent=Opera 10.10
Platform=Win95

[Mozilla/?.*(*Opera/10.1*Windows 98*)*]
Parent=Opera 10.10
Platform=Win98

[Mozilla/?.*(*Opera/10.1*Windows ME*)*]
Parent=Opera 10.10

[Mozilla/?.*(*Opera/10.1*Windows NT 4.0*)*]
Parent=Opera 10.10
Platform=WinNT

[Mozilla/?.*(*Opera/10.1*Windows NT 5.0*)*]
Parent=Opera 10.10
Platform=Win2000

[Mozilla/?.*(*Opera/10.1*Windows NT 5.1*)*]
Parent=Opera 10.10
Platform=WinXP

[Mozilla/?.*(*Opera/10.1*Windows NT 5.2*)*]
Parent=Opera 10.10
Platform=WinXP

[Mozilla/?.*(*Opera/10.1*Windows NT 6.0*)*]
Parent=Opera 10.10
Platform=WinVista

[Mozilla/?.*(*Opera/10.1*Windows NT 6.1*)*]
Parent=Opera 10.10
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?10.1*]
Parent=Opera 10.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?10.1*]
Parent=Opera 10.10
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?10.1*]
Parent=Opera 10.10
Platform=Win7

[Opera/10.1*(*FreeBSD*)*]
Parent=Opera 10.10
Platform=FreeBSD
Win32=false

[Opera/10.1*(*Linux*)*]
Parent=Opera 10.10
Platform=Linux
Win32=false

[Opera/10.1*(*Mac OS X*)*]
Parent=Opera 10.10
Platform=MacOSX
Win32=false

[Opera/10.1*(*SunOS*)*]
Parent=Opera 10.10
Platform=SunOS
Win32=false

[Opera/10.1*(*Win 9x 4.90*)*]
Parent=Opera 10.10
Platform=WinME

[Opera/10.1*(*Windows 2000*)*]
Parent=Opera 10.10
Platform=Win2000

[Opera/10.1*(*Windows 95*)*]
Parent=Opera 10.10
Platform=Win95

[Opera/10.1*(*Windows 98*)*]
Parent=Opera 10.10
Platform=Win98

[Opera/10.1*(*Windows ME*)*]
Parent=Opera 10.10
Platform=WinME

[Opera/10.1*(*Windows NT 4.0*)*]
Parent=Opera 10.10
Platform=WinNT

[Opera/10.1*(*Windows NT 5.0*)*]
Parent=Opera 10.10
Platform=Win2000

[Opera/10.1*(*Windows NT 5.1*)*]
Parent=Opera 10.10
Platform=WinXP

[Opera/10.1*(*Windows NT 5.2*)*]
Parent=Opera 10.10
Platform=WinXP

[Opera/10.1*(*Windows NT 6.0*)*]
Parent=Opera 10.10
Platform=WinVista

[Opera/10.1*(*Windows NT 6.1*)*]
Parent=Opera 10.10
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/10.1*]
Parent=Opera 10.10
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/10.1*]
Parent=Opera 10.10
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/10.1*]
Parent=Opera 10.10
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/10.1*]
Parent=Opera 10.10
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/10.1*]
Parent=Opera 10.10
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/10.1*]
Parent=Opera 10.10
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/10.1*]
Parent=Opera 10.10
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/10.1*]
Parent=Opera 10.10
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/10.1*]
Parent=Opera 10.10
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/10.1*]
Parent=Opera 10.10
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 10.50

[Opera 10.50]
Parent=DefaultProperties
Browser="Opera"
Version=10.50
MajorVer=10
MinorVer=50
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?10.5*]
Parent=Opera 10.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?10.5*]
Parent=Opera 10.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.5*FreeBSD*)*]
Parent=Opera 10.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/10.5*Linux*)*]
Parent=Opera 10.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/10.5*Mac OS X*)*]
Parent=Opera 10.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.5*SunOS*)*]
Parent=Opera 10.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/10.5*Win 9x 4.90*)*]
Parent=Opera 10.50
Platform=WinME

[Mozilla/?.*(*Opera/10.5*Windows 2000*)*]
Parent=Opera 10.50
Platform=Win2000

[Mozilla/?.*(*Opera/10.5*Windows 95*)*]
Parent=Opera 10.50
Platform=Win95

[Mozilla/?.*(*Opera/10.5*Windows 98*)*]
Parent=Opera 10.50
Platform=Win98

[Mozilla/?.*(*Opera/10.5*Windows ME*)*]
Parent=Opera 10.50

[Mozilla/?.*(*Opera/10.5*Windows NT 4.0*)*]
Parent=Opera 10.50
Platform=WinNT

[Mozilla/?.*(*Opera/10.5*Windows NT 5.0*)*]
Parent=Opera 10.50
Platform=Win2000

[Mozilla/?.*(*Opera/10.5*Windows NT 5.1*)*]
Parent=Opera 10.50
Platform=WinXP

[Mozilla/?.*(*Opera/10.5*Windows NT 5.2*)*]
Parent=Opera 10.50
Platform=WinXP

[Mozilla/?.*(*Opera/10.5*Windows NT 6.0*)*]
Parent=Opera 10.50
Platform=WinVista

[Mozilla/?.*(*Opera/10.5*Windows NT 6.1*)*]
Parent=Opera 10.50
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?10.5*]
Parent=Opera 10.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?10.5*]
Parent=Opera 10.50
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?10.5*]
Parent=Opera 10.50
Platform=Win7

[Opera/10.5*(*FreeBSD*)*]
Parent=Opera 10.50
Platform=FreeBSD
Win32=false

[Opera/10.5*(*Linux*)*]
Parent=Opera 10.50
Platform=Linux
Win32=false

[Opera/10.5*(*Mac OS X*)*]
Parent=Opera 10.50
Platform=MacOSX
Win32=false

[Opera/10.5*(*SunOS*)*]
Parent=Opera 10.50
Platform=SunOS
Win32=false

[Opera/10.5*(*Win 9x 4.90*)*]
Parent=Opera 10.50
Platform=WinME

[Opera/10.5*(*Windows 2000*)*]
Parent=Opera 10.50
Platform=Win2000

[Opera/10.5*(*Windows 95*)*]
Parent=Opera 10.50
Platform=Win95

[Opera/10.5*(*Windows 98*)*]
Parent=Opera 10.50
Platform=Win98

[Opera/10.5*(*Windows ME*)*]
Parent=Opera 10.50
Platform=WinME

[Opera/10.5*(*Windows NT 4.0*)*]
Parent=Opera 10.50
Platform=WinNT

[Opera/10.5*(*Windows NT 5.0*)*]
Parent=Opera 10.50
Platform=Win2000

[Opera/10.5*(*Windows NT 5.1*)*]
Parent=Opera 10.50
Platform=WinXP

[Opera/10.5*(*Windows NT 5.2*)*]
Parent=Opera 10.50
Platform=WinXP

[Opera/10.5*(*Windows NT 6.0*)*]
Parent=Opera 10.50
Platform=WinVista

[Opera/10.5*(*Windows NT 6.1*)*]
Parent=Opera 10.50
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/10.5*]
Parent=Opera 10.50
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/10.5*]
Parent=Opera 10.50
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/10.5*]
Parent=Opera 10.50
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/10.5*]
Parent=Opera 10.50
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/10.5*]
Parent=Opera 10.50
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/10.5*]
Parent=Opera 10.50
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/10.5*]
Parent=Opera 10.50
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/10.5*]
Parent=Opera 10.50
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/10.5*]
Parent=Opera 10.50
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/10.5*]
Parent=Opera 10.50
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 10.60

[Opera 10.60]
Parent=DefaultProperties
Browser="Opera"
Version=10.60
MajorVer=10
MinorVer=60
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?10.6*]
Parent=Opera 10.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?10.6*]
Parent=Opera 10.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.6*FreeBSD*)*]
Parent=Opera 10.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/10.6*Linux*)*]
Parent=Opera 10.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/10.6*Mac OS X*)*]
Parent=Opera 10.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/10.6*SunOS*)*]
Parent=Opera 10.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/10.6*Win 9x 4.90*)*]
Parent=Opera 10.60
Platform=WinME

[Mozilla/?.*(*Opera/10.6*Windows 2000*)*]
Parent=Opera 10.60
Platform=Win2000

[Mozilla/?.*(*Opera/10.6*Windows 95*)*]
Parent=Opera 10.60
Platform=Win95

[Mozilla/?.*(*Opera/10.6*Windows 98*)*]
Parent=Opera 10.60
Platform=Win98

[Mozilla/?.*(*Opera/10.6*Windows ME*)*]
Parent=Opera 10.60

[Mozilla/?.*(*Opera/10.6*Windows NT 4.0*)*]
Parent=Opera 10.60
Platform=WinNT

[Mozilla/?.*(*Opera/10.6*Windows NT 5.0*)*]
Parent=Opera 10.60
Platform=Win2000

[Mozilla/?.*(*Opera/10.6*Windows NT 5.1*)*]
Parent=Opera 10.60
Platform=WinXP

[Mozilla/?.*(*Opera/10.6*Windows NT 5.2*)*]
Parent=Opera 10.60
Platform=WinXP

[Mozilla/?.*(*Opera/10.6*Windows NT 6.0*)*]
Parent=Opera 10.60
Platform=WinVista

[Mozilla/?.*(*Opera/10.6*Windows NT 6.1*)*]
Parent=Opera 10.60
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?10.6*]
Parent=Opera 10.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?10.6*]
Parent=Opera 10.60
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?10.6*]
Parent=Opera 10.60
Platform=Win7

[Opera/10.6*(*FreeBSD*)*]
Parent=Opera 10.60
Platform=FreeBSD
Win32=false

[Opera/10.6*(*Linux*)*]
Parent=Opera 10.60
Platform=Linux
Win32=false

[Opera/10.6*(*Mac OS X*)*]
Parent=Opera 10.60
Platform=MacOSX
Win32=false

[Opera/10.6*(*SunOS*)*]
Parent=Opera 10.60
Platform=SunOS
Win32=false

[Opera/10.6*(*Win 9x 4.90*)*]
Parent=Opera 10.60
Platform=WinME

[Opera/10.6*(*Windows 2000*)*]
Parent=Opera 10.60
Platform=Win2000

[Opera/10.6*(*Windows 95*)*]
Parent=Opera 10.60
Platform=Win95

[Opera/10.6*(*Windows 98*)*]
Parent=Opera 10.60
Platform=Win98

[Opera/10.6*(*Windows ME*)*]
Parent=Opera 10.60
Platform=WinME

[Opera/10.6*(*Windows NT 4.0*)*]
Parent=Opera 10.60
Platform=WinNT

[Opera/10.6*(*Windows NT 5.0*)*]
Parent=Opera 10.60
Platform=Win2000

[Opera/10.6*(*Windows NT 5.1*)*]
Parent=Opera 10.60
Platform=WinXP

[Opera/10.6*(*Windows NT 5.2*)*]
Parent=Opera 10.60
Platform=WinXP

[Opera/10.6*(*Windows NT 6.0*)*]
Parent=Opera 10.60
Platform=WinVista

[Opera/10.6*(*Windows NT 6.1*)*]
Parent=Opera 10.60
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/10.6*]
Parent=Opera 10.60
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/10.6*]
Parent=Opera 10.60
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/10.6*]
Parent=Opera 10.60
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/10.6*]
Parent=Opera 10.60
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/10.6*]
Parent=Opera 10.60
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/10.6*]
Parent=Opera 10.60
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/10.6*]
Parent=Opera 10.60
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/10.6*]
Parent=Opera 10.60
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/10.6*]
Parent=Opera 10.60
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/10.6*]
Parent=Opera 10.60
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 11.00

[Opera 11.00]
Parent=DefaultProperties
Browser="Opera"
Version=11.00
MajorVer=11
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?11.*]
Parent=Opera 11.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?11.*]
Parent=Opera 11.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?11.*]
Parent=Opera 11.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/11.*FreeBSD*)*]
Parent=Opera 11.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/11.*Linux*)*]
Parent=Opera 11.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/11.*Mac OS X*)*]
Parent=Opera 11.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/11.*SunOS*)*]
Parent=Opera 11.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/11.*Win 9x 4.90*)*]
Parent=Opera 11.00
Platform=WinME

[Mozilla/?.*(*Opera/11.*Windows 2000*)*]
Parent=Opera 11.00
Platform=Win2000

[Mozilla/?.*(*Opera/11.*Windows 95*)*]
Parent=Opera 11.00
Platform=Win95

[Mozilla/?.*(*Opera/11.*Windows 98*)*]
Parent=Opera 11.00
Platform=Win98

[Mozilla/?.*(*Opera/11.*Windows ME*)*]
Parent=Opera 11.00

[Mozilla/?.*(*Opera/11.*Windows NT 4.0*)*]
Parent=Opera 11.00
Platform=WinNT

[Mozilla/?.*(*Opera/11.*Windows NT 5.0*)*]
Parent=Opera 11.00
Platform=Win2000

[Mozilla/?.*(*Opera/11.*Windows NT 5.1*)*]
Parent=Opera 11.00
Platform=WinXP

[Mozilla/?.*(*Opera/11.*Windows NT 5.2*)*]
Parent=Opera 11.00
Platform=WinXP

[Mozilla/?.*(*Opera/11.*Windows NT 6.0*)*]
Parent=Opera 11.00
Platform=WinVista

[Mozilla/?.*(*Opera/11.*Windows NT 6.1*)*]
Parent=Opera 11.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?11.*]
Parent=Opera 11.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?11.*]
Parent=Opera 11.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?11.*]
Parent=Opera 11.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?11.*]
Parent=Opera 11.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?11.*]
Parent=Opera 11.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?11.*]
Parent=Opera 11.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?11.*]
Parent=Opera 11.00
Platform=Win7

[Opera/11.*(*FreeBSD*)*]
Parent=Opera 11.00
Platform=FreeBSD
Win32=false

[Opera/11.*(*Linux*)*]
Parent=Opera 11.00
Platform=Linux
Win32=false

[Opera/11.*(*Mac OS X*)*]
Parent=Opera 11.00
Platform=MacOSX
Win32=false

[Opera/11.*(*SunOS*)*]
Parent=Opera 11.00
Platform=SunOS
Win32=false

[Opera/11.*(*Win 9x 4.90*)*]
Parent=Opera 11.00
Platform=WinME

[Opera/11.*(*Windows 2000*)*]
Parent=Opera 11.00
Platform=Win2000

[Opera/11.*(*Windows 95*)*]
Parent=Opera 11.00
Platform=Win95

[Opera/11.*(*Windows 98*)*]
Parent=Opera 11.00
Platform=Win98

[Opera/11.*(*Windows ME*)*]
Parent=Opera 11.00
Platform=WinME

[Opera/11.*(*Windows NT 4.0*)*]
Parent=Opera 11.00
Platform=WinNT

[Opera/11.*(*Windows NT 5.0*)*]
Parent=Opera 11.00
Platform=Win2000

[Opera/11.*(*Windows NT 5.1*)*]
Parent=Opera 11.00
Platform=WinXP

[Opera/11.*(*Windows NT 5.2*)*]
Parent=Opera 11.00
Platform=WinXP

[Opera/11.*(*Windows NT 6.0*)*]
Parent=Opera 11.00
Platform=WinVista

[Opera/11.*(*Windows NT 6.1*)*]
Parent=Opera 11.00
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/11.*]
Parent=Opera 11.00
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/11.*]
Parent=Opera 11.00
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/11.*]
Parent=Opera 11.00
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/11.*]
Parent=Opera 11.00
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/11.*]
Parent=Opera 11.00
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/11.*]
Parent=Opera 11.00
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/11.*]
Parent=Opera 11.00
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/11.*]
Parent=Opera 11.00
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/11.*]
Parent=Opera 11.00
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/11.*]
Parent=Opera 11.00
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/11.*]
Parent=Opera 11.00
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/11.*]
Parent=Opera 11.00
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/11.*]
Parent=Opera 11.00
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/11.*]
Parent=Opera 11.00
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/11.*]
Parent=Opera 11.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 11.10

[Opera 11.10]
Parent=DefaultProperties
Browser="Opera"
Version=11.10
MajorVer=11
MinorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?11.1*]
Parent=Opera 11.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?11.1*]
Parent=Opera 11.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/11.1*FreeBSD*)*]
Parent=Opera 11.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/11.1*Linux*)*]
Parent=Opera 11.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/11.1*Mac OS X*)*]
Parent=Opera 11.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/11.1*SunOS*)*]
Parent=Opera 11.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/11.1*Win 9x 4.90*)*]
Parent=Opera 11.10
Platform=WinME

[Mozilla/?.*(*Opera/11.1*Windows 2000*)*]
Parent=Opera 11.10
Platform=Win2000

[Mozilla/?.*(*Opera/11.1*Windows 95*)*]
Parent=Opera 11.10
Platform=Win95

[Mozilla/?.*(*Opera/11.1*Windows 98*)*]
Parent=Opera 11.10
Platform=Win98

[Mozilla/?.*(*Opera/11.1*Windows ME*)*]
Parent=Opera 11.10

[Mozilla/?.*(*Opera/11.1*Windows NT 4.0*)*]
Parent=Opera 11.10
Platform=WinNT

[Mozilla/?.*(*Opera/11.1*Windows NT 5.0*)*]
Parent=Opera 11.10
Platform=Win2000

[Mozilla/?.*(*Opera/11.1*Windows NT 5.1*)*]
Parent=Opera 11.10
Platform=WinXP

[Mozilla/?.*(*Opera/11.1*Windows NT 5.2*)*]
Parent=Opera 11.10
Platform=WinXP

[Mozilla/?.*(*Opera/11.1*Windows NT 6.0*)*]
Parent=Opera 11.10
Platform=WinVista

[Mozilla/?.*(*Opera/11.1*Windows NT 6.1*)*]
Parent=Opera 11.10
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?11.1*]
Parent=Opera 11.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?11.1*]
Parent=Opera 11.10
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?11.1*]
Parent=Opera 11.10
Platform=Win7

[Opera/11.1*(*FreeBSD*)*]
Parent=Opera 11.10
Platform=FreeBSD
Win32=false

[Opera/11.1*(*Linux*)*]
Parent=Opera 11.10
Platform=Linux
Win32=false

[Opera/11.1*(*Mac OS X*)*]
Parent=Opera 11.10
Platform=MacOSX
Win32=false

[Opera/11.1*(*SunOS*)*]
Parent=Opera 11.10
Platform=SunOS
Win32=false

[Opera/11.1*(*Win 9x 4.90*)*]
Parent=Opera 11.10
Platform=WinME

[Opera/11.1*(*Windows 2000*)*]
Parent=Opera 11.10
Platform=Win2000

[Opera/11.1*(*Windows 95*)*]
Parent=Opera 11.10
Platform=Win95

[Opera/11.1*(*Windows 98*)*]
Parent=Opera 11.10
Platform=Win98

[Opera/11.1*(*Windows ME*)*]
Parent=Opera 11.10
Platform=WinME

[Opera/11.1*(*Windows NT 4.0*)*]
Parent=Opera 11.10
Platform=WinNT

[Opera/11.1*(*Windows NT 5.0*)*]
Parent=Opera 11.10
Platform=Win2000

[Opera/11.1*(*Windows NT 5.1*)*]
Parent=Opera 11.10
Platform=WinXP

[Opera/11.1*(*Windows NT 5.2*)*]
Parent=Opera 11.10
Platform=WinXP

[Opera/11.1*(*Windows NT 6.0*)*]
Parent=Opera 11.10
Platform=WinVista

[Opera/11.1*(*Windows NT 6.1*)*]
Parent=Opera 11.10
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/11.1*]
Parent=Opera 11.10
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/11.1*]
Parent=Opera 11.10
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/11.1*]
Parent=Opera 11.10
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/11.1*]
Parent=Opera 11.10
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/11.1*]
Parent=Opera 11.10
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/11.1*]
Parent=Opera 11.10
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/11.1*]
Parent=Opera 11.10
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/11.1*]
Parent=Opera 11.10
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/11.1*]
Parent=Opera 11.10
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/11.1*]
Parent=Opera 11.10
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 12.00

[Opera 12.00]
Parent=DefaultProperties
Browser="Opera"
Version=12.00
MajorVer=12
MinorVer=00
Beta=true
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?12.*]
Parent=Opera 12.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?12.*]
Parent=Opera 12.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?12.*]
Parent=Opera 12.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/12.*FreeBSD*)*]
Parent=Opera 12.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/12.*Linux*)*]
Parent=Opera 12.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/12.*Mac OS X*)*]
Parent=Opera 12.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/12.*SunOS*)*]
Parent=Opera 12.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/12.*Win 9x 4.90*)*]
Parent=Opera 12.00
Platform=WinME

[Mozilla/?.*(*Opera/12.*Windows 2000*)*]
Parent=Opera 12.00
Platform=Win2000

[Mozilla/?.*(*Opera/12.*Windows 95*)*]
Parent=Opera 12.00
Platform=Win95

[Mozilla/?.*(*Opera/12.*Windows 98*)*]
Parent=Opera 12.00
Platform=Win98

[Mozilla/?.*(*Opera/12.*Windows ME*)*]
Parent=Opera 12.00

[Mozilla/?.*(*Opera/12.*Windows NT 4.0*)*]
Parent=Opera 12.00
Platform=WinNT

[Mozilla/?.*(*Opera/12.*Windows NT 5.0*)*]
Parent=Opera 12.00
Platform=Win2000

[Mozilla/?.*(*Opera/12.*Windows NT 5.1*)*]
Parent=Opera 12.00
Platform=WinXP

[Mozilla/?.*(*Opera/12.*Windows NT 5.2*)*]
Parent=Opera 12.00
Platform=WinXP

[Mozilla/?.*(*Opera/12.*Windows NT 6.0*)*]
Parent=Opera 12.00
Platform=WinVista

[Mozilla/?.*(*Opera/12.*Windows NT 6.1*)*]
Parent=Opera 12.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?12.*]
Parent=Opera 12.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?12.*]
Parent=Opera 12.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?12.*]
Parent=Opera 12.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?12.*]
Parent=Opera 12.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?12.*]
Parent=Opera 12.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?12.*]
Parent=Opera 12.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?12.*]
Parent=Opera 12.00
Platform=Win7

[Opera/12.*(*FreeBSD*)*]
Parent=Opera 12.00
Platform=FreeBSD
Win32=false

[Opera/12.*(*Linux*)*]
Parent=Opera 12.00
Platform=Linux
Win32=false

[Opera/12.*(*Mac OS X*)*]
Parent=Opera 12.00
Platform=MacOSX
Win32=false

[Opera/12.*(*SunOS*)*]
Parent=Opera 12.00
Platform=SunOS
Win32=false

[Opera/12.*(*Win 9x 4.90*)*]
Parent=Opera 12.00
Platform=WinME

[Opera/12.*(*Windows 2000*)*]
Parent=Opera 12.00
Platform=Win2000

[Opera/12.*(*Windows 95*)*]
Parent=Opera 12.00
Platform=Win95

[Opera/12.*(*Windows 98*)*]
Parent=Opera 12.00
Platform=Win98

[Opera/12.*(*Windows ME*)*]
Parent=Opera 12.00
Platform=WinME

[Opera/12.*(*Windows NT 4.0*)*]
Parent=Opera 12.00
Platform=WinNT

[Opera/12.*(*Windows NT 5.0*)*]
Parent=Opera 12.00
Platform=Win2000

[Opera/12.*(*Windows NT 5.1*)*]
Parent=Opera 12.00
Platform=WinXP

[Opera/12.*(*Windows NT 5.2*)*]
Parent=Opera 12.00
Platform=WinXP

[Opera/12.*(*Windows NT 6.0*)*]
Parent=Opera 12.00
Platform=WinVista

[Opera/12.*(*Windows NT 6.1*)*]
Parent=Opera 12.00
Platform=Win7

[Opera/9.80*(*FreeBSD*)*Version/12.*]
Parent=Opera 12.00
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*Version/12.*]
Parent=Opera 12.00
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*Version/12.*]
Parent=Opera 12.00
Platform=MacOSX
Win32=false

[Opera/9.80*(*SunOS*)*Version/12.*]
Parent=Opera 12.00
Platform=SunOS

[Opera/9.80*(*Win 9x 4.90*)*Version/12.*]
Parent=Opera 12.00
Platform=WinME

[Opera/9.80*(*Windows 2000*)*Version/12.*]
Parent=Opera 12.00
Platform=Win2000

[Opera/9.80*(*Windows 95*)*Version/12.*]
Parent=Opera 12.00
Platform=Win95

[Opera/9.80*(*Windows 98*)*Version/12.*]
Parent=Opera 12.00
Platform=Win98

[Opera/9.80*(*Windows ME*)*Version/12.*]
Parent=Opera 12.00
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*Version/12.*]
Parent=Opera 12.00
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*Version/12.*]
Parent=Opera 12.00
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*Version/12.*]
Parent=Opera 12.00
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*Version/12.*]
Parent=Opera 12.00
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*Version/12.*]
Parent=Opera 12.00
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*Version/12.*]
Parent=Opera 12.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 2.00

[Opera 2.00]
Parent=DefaultProperties
Browser="Opera"
Version=2.00
MajorVer=2
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[Mozilla/?.*(*FreeBSD*)*Opera?2.*]
Parent=Opera 2.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?2.*]
Parent=Opera 2.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?2.*]
Parent=Opera 2.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.*FreeBSD*)*]
Parent=Opera 2.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/2.*Linux*)*]
Parent=Opera 2.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/2.*Mac OS X*)*]
Parent=Opera 2.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.*SunOS*)*]
Parent=Opera 2.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/2.*Win 9x 4.90*)*]
Parent=Opera 2.00
Platform=WinME

[Mozilla/?.*(*Opera/2.*Windows 2000*)*]
Parent=Opera 2.00
Platform=Win2000

[Mozilla/?.*(*Opera/2.*Windows 95*)*]
Parent=Opera 2.00
Platform=Win95

[Mozilla/?.*(*Opera/2.*Windows 98*)*]
Parent=Opera 2.00
Platform=Win98

[Mozilla/?.*(*Opera/2.*Windows ME*)*]
Parent=Opera 2.00

[Mozilla/?.*(*Opera/2.*Windows NT 4.0*)*]
Parent=Opera 2.00
Platform=WinNT

[Mozilla/?.*(*Opera/2.*Windows NT 5.0*)*]
Parent=Opera 2.00
Platform=Win2000

[Mozilla/?.*(*Opera/2.*Windows NT 5.1*)*]
Parent=Opera 2.00
Platform=WinXP

[Mozilla/?.*(*Opera/2.*Windows NT 5.2*)*]
Parent=Opera 2.00
Platform=WinXP

[Mozilla/?.*(*Opera/2.*Windows NT 6.0*)*]
Parent=Opera 2.00
Platform=WinVista

[Mozilla/?.*(*Opera/2.*Windows NT 6.1*)*]
Parent=Opera 2.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?2.*]
Parent=Opera 2.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?2.*]
Parent=Opera 2.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?2.*]
Parent=Opera 2.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?2.*]
Parent=Opera 2.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?2.*]
Parent=Opera 2.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?2.*]
Parent=Opera 2.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?2.*]
Parent=Opera 2.00
Platform=Win7

[Opera/2.*(*FreeBSD*)*]
Parent=Opera 2.00
Platform=FreeBSD
Win32=false

[Opera/2.*(*Linux*)*]
Parent=Opera 2.00
Platform=Linux
Win32=false

[Opera/2.*(*Mac OS X*)*]
Parent=Opera 2.00
Platform=MacOSX
Win32=false

[Opera/2.*(*SunOS*)*]
Parent=Opera 2.00
Platform=SunOS
Win32=false

[Opera/2.*(*Win 9x 4.90*)*]
Parent=Opera 2.00
Platform=WinME

[Opera/2.*(*Windows 2000*)*]
Parent=Opera 2.00
Platform=Win2000

[Opera/2.*(*Windows 95*)*]
Parent=Opera 2.00
Platform=Win95

[Opera/2.*(*Windows 98*)*]
Parent=Opera 2.00
Platform=Win98

[Opera/2.*(*Windows ME*)*]
Parent=Opera 2.00
Platform=WinME

[Opera/2.*(*Windows NT 4.0*)*]
Parent=Opera 2.00
Platform=WinNT

[Opera/2.*(*Windows NT 5.0*)*]
Parent=Opera 2.00
Platform=Win2000

[Opera/2.*(*Windows NT 5.1*)*]
Parent=Opera 2.00
Platform=WinXP

[Opera/2.*(*Windows NT 5.2*)*]
Parent=Opera 2.00
Platform=WinXP

[Opera/2.*(*Windows NT 6.0*)*]
Parent=Opera 2.00
Platform=WinVista

[Opera/2.*(*Windows NT 6.1*)*]
Parent=Opera 2.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 2.10

[Opera 2.10]
Parent=DefaultProperties
Browser="Opera"
Version=2.10
MajorVer=1
MinorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[Mozilla/?.*(*FreeBSD*)*Opera?2.1*]
Parent=Opera 2.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?2.1*]
Parent=Opera 2.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.1*FreeBSD*)*]
Parent=Opera 2.10
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/2.1*Linux*)*]
Parent=Opera 2.10
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/2.1*Mac OS X*)*]
Parent=Opera 2.10
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.1*SunOS*)*]
Parent=Opera 2.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/2.1*Win 9x 4.90*)*]
Parent=Opera 2.10
Platform=WinME

[Mozilla/?.*(*Opera/2.1*Windows 2000*)*]
Parent=Opera 2.10
Platform=Win2000

[Mozilla/?.*(*Opera/2.1*Windows 95*)*]
Parent=Opera 2.10
Platform=Win95

[Mozilla/?.*(*Opera/2.1*Windows 98*)*]
Parent=Opera 2.10
Platform=Win98

[Mozilla/?.*(*Opera/2.1*Windows ME*)*]
Parent=Opera 2.10

[Mozilla/?.*(*Opera/2.1*Windows NT 4.0*)*]
Parent=Opera 2.10
Platform=WinNT

[Mozilla/?.*(*Opera/2.1*Windows NT 5.0*)*]
Parent=Opera 2.10
Platform=Win2000

[Mozilla/?.*(*Opera/2.1*Windows NT 5.1*)*]
Parent=Opera 2.10
Platform=WinXP

[Mozilla/?.*(*Opera/2.1*Windows NT 5.2*)*]
Parent=Opera 2.10
Platform=WinXP

[Mozilla/?.*(*Opera/2.1*Windows NT 6.0*)*]
Parent=Opera 2.10
Platform=WinVista

[Mozilla/?.*(*Opera/2.1*Windows NT 6.1*)*]
Parent=Opera 2.10
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?2.1*]
Parent=Opera 2.10
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?2.1*]
Parent=Opera 2.10
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?2.1*]
Parent=Opera 2.10
Platform=Win7

[Opera/2.1*(*FreeBSD*)*]
Parent=Opera 2.10
Platform=FreeBSD
Win32=false

[Opera/2.1*(*Linux*)*]
Parent=Opera 2.10
Platform=Linux
Win32=false

[Opera/2.1*(*Mac OS X*)*]
Parent=Opera 2.10
Platform=MacOSX
Win32=false

[Opera/2.1*(*SunOS*)*]
Parent=Opera 2.10
Platform=SunOS
Win32=false

[Opera/2.1*(*Win 9x 4.90*)*]
Parent=Opera 2.10
Platform=WinME

[Opera/2.1*(*Windows 2000*)*]
Parent=Opera 2.10
Platform=Win2000

[Opera/2.1*(*Windows 95*)*]
Parent=Opera 2.10
Platform=Win95

[Opera/2.1*(*Windows 98*)*]
Parent=Opera 2.10
Platform=Win98

[Opera/2.1*(*Windows ME*)*]
Parent=Opera 2.10
Platform=WinME

[Opera/2.1*(*Windows NT 4.0*)*]
Parent=Opera 2.10
Platform=WinNT

[Opera/2.1*(*Windows NT 5.0*)*]
Parent=Opera 2.10
Platform=Win2000

[Opera/2.1*(*Windows NT 5.1*)*]
Parent=Opera 2.10
Platform=WinXP

[Opera/2.1*(*Windows NT 5.2*)*]
Parent=Opera 2.10
Platform=WinXP

[Opera/2.1*(*Windows NT 6.0*)*]
Parent=Opera 2.10
Platform=WinVista

[Opera/2.1*(*Windows NT 6.1*)*]
Parent=Opera 2.10
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 2.12

[Opera 2.12]
Parent=DefaultProperties
Browser="Opera"
Version=2.12
MajorVer=1
MinorVer=12
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[Mozilla/?.*(*FreeBSD*)*Opera?2.12*]
Parent=Opera 2.12
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?2.12*]
Parent=Opera 2.12
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.12*FreeBSD*)*]
Parent=Opera 2.12
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/2.12*Linux*)*]
Parent=Opera 2.12
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/2.12*Mac OS X*)*]
Parent=Opera 2.12
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/2.12*SunOS*)*]
Parent=Opera 2.12
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/2.12*Win 9x 4.90*)*]
Parent=Opera 2.12
Platform=WinME

[Mozilla/?.*(*Opera/2.12*Windows 2000*)*]
Parent=Opera 2.12
Platform=Win2000

[Mozilla/?.*(*Opera/2.12*Windows 95*)*]
Parent=Opera 2.12
Platform=Win95

[Mozilla/?.*(*Opera/2.12*Windows 98*)*]
Parent=Opera 2.12
Platform=Win98

[Mozilla/?.*(*Opera/2.12*Windows ME*)*]
Parent=Opera 2.12

[Mozilla/?.*(*Opera/2.12*Windows NT 4.0*)*]
Parent=Opera 2.12
Platform=WinNT

[Mozilla/?.*(*Opera/2.12*Windows NT 5.0*)*]
Parent=Opera 2.12
Platform=Win2000

[Mozilla/?.*(*Opera/2.12*Windows NT 5.1*)*]
Parent=Opera 2.12
Platform=WinXP

[Mozilla/?.*(*Opera/2.12*Windows NT 5.2*)*]
Parent=Opera 2.12
Platform=WinXP

[Mozilla/?.*(*Opera/2.12*Windows NT 6.0*)*]
Parent=Opera 2.12
Platform=WinVista

[Mozilla/?.*(*Opera/2.12*Windows NT 6.1*)*]
Parent=Opera 2.12
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?2.12*]
Parent=Opera 2.12
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?2.12*]
Parent=Opera 2.12
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?2.12*]
Parent=Opera 2.12
Platform=Win7

[Opera/2.12*(*FreeBSD*)*]
Parent=Opera 2.12
Platform=FreeBSD
Win32=false

[Opera/2.12*(*Linux*)*]
Parent=Opera 2.12
Platform=Linux
Win32=false

[Opera/2.12*(*Mac OS X*)*]
Parent=Opera 2.12
Platform=MacOSX
Win32=false

[Opera/2.12*(*SunOS*)*]
Parent=Opera 2.12
Platform=SunOS
Win32=false

[Opera/2.12*(*Win 9x 4.90*)*]
Parent=Opera 2.12
Platform=WinME

[Opera/2.12*(*Windows 2000*)*]
Parent=Opera 2.12
Platform=Win2000

[Opera/2.12*(*Windows 95*)*]
Parent=Opera 2.12
Platform=Win95

[Opera/2.12*(*Windows 98*)*]
Parent=Opera 2.12
Platform=Win98

[Opera/2.12*(*Windows ME*)*]
Parent=Opera 2.12
Platform=WinME

[Opera/2.12*(*Windows NT 4.0*)*]
Parent=Opera 2.12
Platform=WinNT

[Opera/2.12*(*Windows NT 5.0*)*]
Parent=Opera 2.12
Platform=Win2000

[Opera/2.12*(*Windows NT 5.1*)*]
Parent=Opera 2.12
Platform=WinXP

[Opera/2.12*(*Windows NT 5.2*)*]
Parent=Opera 2.12
Platform=WinXP

[Opera/2.12*(*Windows NT 6.0*)*]
Parent=Opera 2.12
Platform=WinVista

[Opera/2.12*(*Windows NT 6.1*)*]
Parent=Opera 2.12
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 3.0

[Opera 3.00]
Parent=DefaultProperties
Browser="Opera"
Version=3.00
MajorVer=3
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true

[Mozilla/?.*(*FreeBSD*)*Opera?3.*]
Parent=Opera 3.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?3.*]
Parent=Opera 3.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?3.*]
Parent=Opera 3.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.*FreeBSD*)*]
Parent=Opera 3.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/3.*Linux*)*]
Parent=Opera 3.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/3.*Mac OS X*)*]
Parent=Opera 3.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.*SunOS*)*]
Parent=Opera 3.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/3.*Win 9x 4.90*)*]
Parent=Opera 3.00
Platform=WinME

[Mozilla/?.*(*Opera/3.*Windows 2000*)*]
Parent=Opera 3.00
Platform=Win2000

[Mozilla/?.*(*Opera/3.*Windows 95*)*]
Parent=Opera 3.00
Platform=Win95

[Mozilla/?.*(*Opera/3.*Windows 98*)*]
Parent=Opera 3.00
Platform=Win98

[Mozilla/?.*(*Opera/3.*Windows ME*)*]
Parent=Opera 3.00

[Mozilla/?.*(*Opera/3.*Windows NT 4.0*)*]
Parent=Opera 3.00
Platform=WinNT

[Mozilla/?.*(*Opera/3.*Windows NT 5.0*)*]
Parent=Opera 3.00
Platform=Win2000

[Mozilla/?.*(*Opera/3.*Windows NT 5.1*)*]
Parent=Opera 3.00
Platform=WinXP

[Mozilla/?.*(*Opera/3.*Windows NT 5.2*)*]
Parent=Opera 3.00
Platform=WinXP

[Mozilla/?.*(*Opera/3.*Windows NT 6.0*)*]
Parent=Opera 3.00
Platform=WinVista

[Mozilla/?.*(*Opera/3.*Windows NT 6.1*)*]
Parent=Opera 3.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?3.*]
Parent=Opera 3.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?3.*]
Parent=Opera 3.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?3.*]
Parent=Opera 3.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?3.*]
Parent=Opera 3.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?3.*]
Parent=Opera 3.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?3.*]
Parent=Opera 3.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?3.*]
Parent=Opera 3.00
Platform=Win7

[Opera/3.*(*FreeBSD*)*]
Parent=Opera 3.00
Platform=FreeBSD
Win32=false

[Opera/3.*(*Linux*)*]
Parent=Opera 3.00
Platform=Linux
Win32=false

[Opera/3.*(*Mac OS X*)*]
Parent=Opera 3.00
Platform=MacOSX
Win32=false

[Opera/3.*(*SunOS*)*]
Parent=Opera 3.00
Platform=SunOS
Win32=false

[Opera/3.*(*Win 9x 4.90*)*]
Parent=Opera 3.00
Platform=WinME

[Opera/3.*(*Windows 2000*)*]
Parent=Opera 3.00
Platform=Win2000

[Opera/3.*(*Windows 95*)*]
Parent=Opera 3.00
Platform=Win95

[Opera/3.*(*Windows 98*)*]
Parent=Opera 3.00
Platform=Win98

[Opera/3.*(*Windows ME*)*]
Parent=Opera 3.00
Platform=WinME

[Opera/3.*(*Windows NT 4.0*)*]
Parent=Opera 3.00
Platform=WinNT

[Opera/3.*(*Windows NT 5.0*)*]
Parent=Opera 3.00
Platform=Win2000

[Opera/3.*(*Windows NT 5.1*)*]
Parent=Opera 3.00
Platform=WinXP

[Opera/3.*(*Windows NT 5.2*)*]
Parent=Opera 3.00
Platform=WinXP

[Opera/3.*(*Windows NT 6.0*)*]
Parent=Opera 3.00
Platform=WinVista

[Opera/3.*(*Windows NT 6.1*)*]
Parent=Opera 3.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 3.50

[Opera 3.50]
Parent=DefaultProperties
Browser="Opera"
Version=3.50
MajorVer=3
MinorVer=50
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaApplets=true
CssVersion=1

[Mozilla/?.*(*FreeBSD*)*Opera?3.5*]
Parent=Opera 3.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?3.5*]
Parent=Opera 3.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.5*FreeBSD*)*]
Parent=Opera 3.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/3.5*Linux*)*]
Parent=Opera 3.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/3.5*Mac OS X*)*]
Parent=Opera 3.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.5*SunOS*)*]
Parent=Opera 3.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/3.5*Win 9x 4.90*)*]
Parent=Opera 3.50
Platform=WinME

[Mozilla/?.*(*Opera/3.5*Windows 2000*)*]
Parent=Opera 3.50
Platform=Win2000

[Mozilla/?.*(*Opera/3.5*Windows 95*)*]
Parent=Opera 3.50
Platform=Win95

[Mozilla/?.*(*Opera/3.5*Windows 98*)*]
Parent=Opera 3.50
Platform=Win98

[Mozilla/?.*(*Opera/3.5*Windows ME*)*]
Parent=Opera 3.50

[Mozilla/?.*(*Opera/3.5*Windows NT 4.0*)*]
Parent=Opera 3.50
Platform=WinNT

[Mozilla/?.*(*Opera/3.5*Windows NT 5.0*)*]
Parent=Opera 3.50
Platform=Win2000

[Mozilla/?.*(*Opera/3.5*Windows NT 5.1*)*]
Parent=Opera 3.50
Platform=WinXP

[Mozilla/?.*(*Opera/3.5*Windows NT 5.2*)*]
Parent=Opera 3.50
Platform=WinXP

[Mozilla/?.*(*Opera/3.5*Windows NT 6.0*)*]
Parent=Opera 3.50
Platform=WinVista

[Mozilla/?.*(*Opera/3.5*Windows NT 6.1*)*]
Parent=Opera 3.50
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?3.5*]
Parent=Opera 3.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?3.5*]
Parent=Opera 3.50
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?3.5*]
Parent=Opera 3.50
Platform=Win7

[Opera/3.5*(*FreeBSD*)*]
Parent=Opera 3.50
Platform=FreeBSD
Win32=false

[Opera/3.5*(*Linux*)*]
Parent=Opera 3.50
Platform=Linux
Win32=false

[Opera/3.5*(*Mac OS X*)*]
Parent=Opera 3.50
Platform=MacOSX
Win32=false

[Opera/3.5*(*SunOS*)*]
Parent=Opera 3.50
Platform=SunOS
Win32=false

[Opera/3.5*(*Win 9x 4.90*)*]
Parent=Opera 3.50
Platform=WinME

[Opera/3.5*(*Windows 2000*)*]
Parent=Opera 3.50
Platform=Win2000

[Opera/3.5*(*Windows 95*)*]
Parent=Opera 3.50
Platform=Win95

[Opera/3.5*(*Windows 98*)*]
Parent=Opera 3.50
Platform=Win98

[Opera/3.5*(*Windows ME*)*]
Parent=Opera 3.50
Platform=WinME

[Opera/3.5*(*Windows NT 4.0*)*]
Parent=Opera 3.50
Platform=WinNT

[Opera/3.5*(*Windows NT 5.0*)*]
Parent=Opera 3.50
Platform=Win2000

[Opera/3.5*(*Windows NT 5.1*)*]
Parent=Opera 3.50
Platform=WinXP

[Opera/3.5*(*Windows NT 5.2*)*]
Parent=Opera 3.50
Platform=WinXP

[Opera/3.5*(*Windows NT 6.0*)*]
Parent=Opera 3.50
Platform=WinVista

[Opera/3.5*(*Windows NT 6.1*)*]
Parent=Opera 3.50
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 3.60

[Opera 3.60]
Parent=DefaultProperties
Browser="Opera"
Version=3.60
MajorVer=3
MinorVer=60
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaApplets=true
CssVersion=1

[Mozilla/?.*(*FreeBSD*)*Opera?3.6*]
Parent=Opera 3.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?3.6*]
Parent=Opera 3.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.6*FreeBSD*)*]
Parent=Opera 3.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/3.6*Linux*)*]
Parent=Opera 3.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/3.6*Mac OS X*)*]
Parent=Opera 3.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/3.6*SunOS*)*]
Parent=Opera 3.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/3.6*Win 9x 4.90*)*]
Parent=Opera 3.60
Platform=WinME

[Mozilla/?.*(*Opera/3.6*Windows 2000*)*]
Parent=Opera 3.60
Platform=Win2000

[Mozilla/?.*(*Opera/3.6*Windows 95*)*]
Parent=Opera 3.60
Platform=Win95

[Mozilla/?.*(*Opera/3.6*Windows 98*)*]
Parent=Opera 3.60
Platform=Win98

[Mozilla/?.*(*Opera/3.6*Windows ME*)*]
Parent=Opera 3.60

[Mozilla/?.*(*Opera/3.6*Windows NT 4.0*)*]
Parent=Opera 3.60
Platform=WinNT

[Mozilla/?.*(*Opera/3.6*Windows NT 5.0*)*]
Parent=Opera 3.60
Platform=Win2000

[Mozilla/?.*(*Opera/3.6*Windows NT 5.1*)*]
Parent=Opera 3.60
Platform=WinXP

[Mozilla/?.*(*Opera/3.6*Windows NT 5.2*)*]
Parent=Opera 3.60
Platform=WinXP

[Mozilla/?.*(*Opera/3.6*Windows NT 6.0*)*]
Parent=Opera 3.60
Platform=WinVista

[Mozilla/?.*(*Opera/3.6*Windows NT 6.1*)*]
Parent=Opera 3.60
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?3.6*]
Parent=Opera 3.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?3.6*]
Parent=Opera 3.60
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?3.6*]
Parent=Opera 3.60
Platform=Win7

[Opera/3.6*(*FreeBSD*)*]
Parent=Opera 3.60
Platform=FreeBSD
Win32=false

[Opera/3.6*(*Linux*)*]
Parent=Opera 3.60
Platform=Linux
Win32=false

[Opera/3.6*(*Mac OS X*)*]
Parent=Opera 3.60
Platform=MacOSX
Win32=false

[Opera/3.6*(*SunOS*)*]
Parent=Opera 3.60
Platform=SunOS
Win32=false

[Opera/3.6*(*Win 9x 4.90*)*]
Parent=Opera 3.60
Platform=WinME

[Opera/3.6*(*Windows 2000*)*]
Parent=Opera 3.60
Platform=Win2000

[Opera/3.6*(*Windows 95*)*]
Parent=Opera 3.60
Platform=Win95

[Opera/3.6*(*Windows 98*)*]
Parent=Opera 3.60
Platform=Win98

[Opera/3.6*(*Windows ME*)*]
Parent=Opera 3.60
Platform=WinME

[Opera/3.6*(*Windows NT 4.0*)*]
Parent=Opera 3.60
Platform=WinNT

[Opera/3.6*(*Windows NT 5.0*)*]
Parent=Opera 3.60
Platform=Win2000

[Opera/3.6*(*Windows NT 5.1*)*]
Parent=Opera 3.60
Platform=WinXP

[Opera/3.6*(*Windows NT 5.2*)*]
Parent=Opera 3.60
Platform=WinXP

[Opera/3.6*(*Windows NT 6.0*)*]
Parent=Opera 3.60
Platform=WinVista

[Opera/3.6*(*Windows NT 6.1*)*]
Parent=Opera 3.60
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 4.00

[Opera 4.00]
Parent=DefaultProperties
Browser="Opera"
Version=4.00
MajorVer=4
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/?.*(*FreeBSD*)*Opera?4.*]
Parent=Opera 4.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?4.*]
Parent=Opera 4.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?4.*]
Parent=Opera 4.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/4.*FreeBSD*)*]
Parent=Opera 4.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/4.*Linux*)*]
Parent=Opera 4.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/4.*Mac OS X*)*]
Parent=Opera 4.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/4.*SunOS*)*]
Parent=Opera 4.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/4.*Win 9x 4.90*)*]
Parent=Opera 4.00
Platform=WinME

[Mozilla/?.*(*Opera/4.*Windows 2000*)*]
Parent=Opera 4.00
Platform=Win2000

[Mozilla/?.*(*Opera/4.*Windows 95*)*]
Parent=Opera 4.00
Platform=Win95

[Mozilla/?.*(*Opera/4.*Windows 98*)*]
Parent=Opera 4.00
Platform=Win98

[Mozilla/?.*(*Opera/4.*Windows ME*)*]
Parent=Opera 4.00

[Mozilla/?.*(*Opera/4.*Windows NT 4.0*)*]
Parent=Opera 4.00
Platform=WinNT

[Mozilla/?.*(*Opera/4.*Windows NT 5.0*)*]
Parent=Opera 4.00
Platform=Win2000

[Mozilla/?.*(*Opera/4.*Windows NT 5.1*)*]
Parent=Opera 4.00
Platform=WinXP

[Mozilla/?.*(*Opera/4.*Windows NT 5.2*)*]
Parent=Opera 4.00
Platform=WinXP

[Mozilla/?.*(*Opera/4.*Windows NT 6.0*)*]
Parent=Opera 4.00
Platform=WinVista

[Mozilla/?.*(*Opera/4.*Windows NT 6.1*)*]
Parent=Opera 4.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?4.*]
Parent=Opera 4.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?4.*]
Parent=Opera 4.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?4.*]
Parent=Opera 4.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?4.*]
Parent=Opera 4.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?4.*]
Parent=Opera 4.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?4.*]
Parent=Opera 4.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?4.*]
Parent=Opera 4.00
Platform=Win7

[Opera/4.*(*FreeBSD*)*]
Parent=Opera 4.00
Platform=FreeBSD
Win32=false

[Opera/4.*(*Linux*)*]
Parent=Opera 4.00
Platform=Linux
Win32=false

[Opera/4.*(*Mac OS X*)*]
Parent=Opera 4.00
Platform=MacOSX
Win32=false

[Opera/4.*(*SunOS*)*]
Parent=Opera 4.00
Platform=SunOS
Win32=false

[Opera/4.*(*Win 9x 4.90*)*]
Parent=Opera 4.00
Platform=WinME

[Opera/4.*(*Windows 2000*)*]
Parent=Opera 4.00
Platform=Win2000

[Opera/4.*(*Windows 95*)*]
Parent=Opera 4.00
Platform=Win95

[Opera/4.*(*Windows 98*)*]
Parent=Opera 4.00
Platform=Win98

[Opera/4.*(*Windows ME*)*]
Parent=Opera 4.00
Platform=WinME

[Opera/4.*(*Windows NT 4.0*)*]
Parent=Opera 4.00
Platform=WinNT

[Opera/4.*(*Windows NT 5.0*)*]
Parent=Opera 4.00
Platform=Win2000

[Opera/4.*(*Windows NT 5.1*)*]
Parent=Opera 4.00
Platform=WinXP

[Opera/4.*(*Windows NT 5.2*)*]
Parent=Opera 4.00
Platform=WinXP

[Opera/4.*(*Windows NT 6.0*)*]
Parent=Opera 4.00
Platform=WinVista

[Opera/4.*(*Windows NT 6.1*)*]
Parent=Opera 4.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 5.00

[Opera 5.00]
Parent=DefaultProperties
Browser="Opera"
Version=5.00
MajorVer=5
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/?.*(*FreeBSD*)*Opera?5.*]
Parent=Opera 5.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?5.*]
Parent=Opera 5.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?5.*]
Parent=Opera 5.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/5.*FreeBSD*)*]
Parent=Opera 5.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/5.*Linux*)*]
Parent=Opera 5.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/5.*Mac OS X*)*]
Parent=Opera 5.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/5.*SunOS*)*]
Parent=Opera 5.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/5.*Win 9x 4.90*)*]
Parent=Opera 5.00
Platform=WinME

[Mozilla/?.*(*Opera/5.*Windows 2000*)*]
Parent=Opera 5.00
Platform=Win2000

[Mozilla/?.*(*Opera/5.*Windows 95*)*]
Parent=Opera 5.00
Platform=Win95

[Mozilla/?.*(*Opera/5.*Windows 98*)*]
Parent=Opera 5.00
Platform=Win98

[Mozilla/?.*(*Opera/5.*Windows ME*)*]
Parent=Opera 5.00

[Mozilla/?.*(*Opera/5.*Windows NT 4.0*)*]
Parent=Opera 5.00
Platform=WinNT

[Mozilla/?.*(*Opera/5.*Windows NT 5.0*)*]
Parent=Opera 5.00
Platform=Win2000

[Mozilla/?.*(*Opera/5.*Windows NT 5.1*)*]
Parent=Opera 5.00
Platform=WinXP

[Mozilla/?.*(*Opera/5.*Windows NT 5.2*)*]
Parent=Opera 5.00
Platform=WinXP

[Mozilla/?.*(*Opera/5.*Windows NT 6.0*)*]
Parent=Opera 5.00
Platform=WinVista

[Mozilla/?.*(*Opera/5.*Windows NT 6.1*)*]
Parent=Opera 5.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?5.*]
Parent=Opera 5.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?5.*]
Parent=Opera 5.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?5.*]
Parent=Opera 5.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?5.*]
Parent=Opera 5.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?5.*]
Parent=Opera 5.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?5.*]
Parent=Opera 5.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?5.*]
Parent=Opera 5.00
Platform=Win7

[Opera/5.*(*FreeBSD*)*]
Parent=Opera 5.00
Platform=FreeBSD
Win32=false

[Opera/5.*(*Linux*)*]
Parent=Opera 5.00
Platform=Linux
Win32=false

[Opera/5.*(*Mac OS X*)*]
Parent=Opera 5.00
Platform=MacOSX
Win32=false

[Opera/5.*(*SunOS*)*]
Parent=Opera 5.00
Platform=SunOS
Win32=false

[Opera/5.*(*Win 9x 4.90*)*]
Parent=Opera 5.00
Platform=WinME

[Opera/5.*(*Windows 2000*)*]
Parent=Opera 5.00
Platform=Win2000

[Opera/5.*(*Windows 95*)*]
Parent=Opera 5.00
Platform=Win95

[Opera/5.*(*Windows 98*)*]
Parent=Opera 5.00
Platform=Win98

[Opera/5.*(*Windows ME*)*]
Parent=Opera 5.00
Platform=WinME

[Opera/5.*(*Windows NT 4.0*)*]
Parent=Opera 5.00
Platform=WinNT

[Opera/5.*(*Windows NT 5.0*)*]
Parent=Opera 5.00
Platform=Win2000

[Opera/5.*(*Windows NT 5.1*)*]
Parent=Opera 5.00
Platform=WinXP

[Opera/5.*(*Windows NT 5.2*)*]
Parent=Opera 5.00
Platform=WinXP

[Opera/5.*(*Windows NT 6.0*)*]
Parent=Opera 5.00
Platform=WinVista

[Opera/5.*(*Windows NT 6.1*)*]
Parent=Opera 5.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 6.00

[Opera 6.00]
Parent=DefaultProperties
Browser="Opera"
Version=6.00
MajorVer=6
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/?.*(*FreeBSD*)*Opera?6.*]
Parent=Opera 6.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?6.*]
Parent=Opera 6.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?6.*]
Parent=Opera 6.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/6.*FreeBSD*)*]
Parent=Opera 6.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/6.*Linux*)*]
Parent=Opera 6.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/6.*Mac OS X*)*]
Parent=Opera 6.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/6.*SunOS*)*]
Parent=Opera 6.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/6.*Win 9x 4.90*)*]
Parent=Opera 6.00
Platform=WinME

[Mozilla/?.*(*Opera/6.*Windows 2000*)*]
Parent=Opera 6.00
Platform=Win2000

[Mozilla/?.*(*Opera/6.*Windows 95*)*]
Parent=Opera 6.00
Platform=Win95

[Mozilla/?.*(*Opera/6.*Windows 98*)*]
Parent=Opera 6.00
Platform=Win98

[Mozilla/?.*(*Opera/6.*Windows ME*)*]
Parent=Opera 6.00

[Mozilla/?.*(*Opera/6.*Windows NT 4.0*)*]
Parent=Opera 6.00
Platform=WinNT

[Mozilla/?.*(*Opera/6.*Windows NT 5.0*)*]
Parent=Opera 6.00
Platform=Win2000

[Mozilla/?.*(*Opera/6.*Windows NT 5.1*)*]
Parent=Opera 6.00
Platform=WinXP

[Mozilla/?.*(*Opera/6.*Windows NT 5.2*)*]
Parent=Opera 6.00
Platform=WinXP

[Mozilla/?.*(*Opera/6.*Windows NT 6.0*)*]
Parent=Opera 6.00
Platform=WinVista

[Mozilla/?.*(*Opera/6.*Windows NT 6.1*)*]
Parent=Opera 6.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?6.*]
Parent=Opera 6.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Symbian*)*Opera?6.*]
Parent=Opera 6.00
Platform=SymbianOS
Win32=false
isMobileDevice=true

[Mozilla/?.*(*Win 9x 4.90*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?6.*]
Parent=Opera 6.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?6.*]
Parent=Opera 6.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?6.*]
Parent=Opera 6.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?6.*]
Parent=Opera 6.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?6.*]
Parent=Opera 6.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?6.*]
Parent=Opera 6.00
Platform=Win7

[Opera/6.*(*FreeBSD*)*]
Parent=Opera 6.00
Platform=FreeBSD
Win32=false

[Opera/6.*(*Linux*)*]
Parent=Opera 6.00
Platform=Linux
Win32=false

[Opera/6.*(*Mac OS X*)*]
Parent=Opera 6.00
Platform=MacOSX
Win32=false

[Opera/6.*(*SunOS*)*]
Parent=Opera 6.00
Platform=SunOS
Win32=false

[Opera/6.*(*Win 9x 4.90*)*]
Parent=Opera 6.00
Platform=WinME

[Opera/6.*(*Windows 2000*)*]
Parent=Opera 6.00
Platform=Win2000

[Opera/6.*(*Windows 95*)*]
Parent=Opera 6.00
Platform=Win95

[Opera/6.*(*Windows 98*)*]
Parent=Opera 6.00
Platform=Win98

[Opera/6.*(*Windows ME*)*]
Parent=Opera 6.00
Platform=WinME

[Opera/6.*(*Windows NT 4.0*)*]
Parent=Opera 6.00
Platform=WinNT

[Opera/6.*(*Windows NT 5.0*)*]
Parent=Opera 6.00
Platform=Win2000

[Opera/6.*(*Windows NT 5.1*)*]
Parent=Opera 6.00
Platform=WinXP

[Opera/6.*(*Windows NT 5.2*)*]
Parent=Opera 6.00
Platform=WinXP

[Opera/6.*(*Windows NT 6.0*)*]
Parent=Opera 6.00
Platform=WinVista

[Opera/6.*(*Windows NT 6.1*)*]
Parent=Opera 6.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 7.00

[Opera 7.00]
Parent=DefaultProperties
Browser="Opera"
Version=7.00
MajorVer=7
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?7.*]
Parent=Opera 7.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?7.*]
Parent=Opera 7.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?7.*]
Parent=Opera 7.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/7.*FreeBSD*)*]
Parent=Opera 7.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/7.*Linux*)*]
Parent=Opera 7.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/7.*Mac OS X*)*]
Parent=Opera 7.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/7.*SunOS*)*]
Parent=Opera 7.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/7.*Win 9x 4.90*)*]
Parent=Opera 7.00
Platform=WinME

[Mozilla/?.*(*Opera/7.*Windows 2000*)*]
Parent=Opera 7.00
Platform=Win2000

[Mozilla/?.*(*Opera/7.*Windows 95*)*]
Parent=Opera 7.00
Platform=Win95

[Mozilla/?.*(*Opera/7.*Windows 98*)*]
Parent=Opera 7.00
Platform=Win98

[Mozilla/?.*(*Opera/7.*Windows ME*)*]
Parent=Opera 7.00

[Mozilla/?.*(*Opera/7.*Windows NT 4.0*)*]
Parent=Opera 7.00
Platform=WinNT

[Mozilla/?.*(*Opera/7.*Windows NT 5.0*)*]
Parent=Opera 7.00
Platform=Win2000

[Mozilla/?.*(*Opera/7.*Windows NT 5.1*)*]
Parent=Opera 7.00
Platform=WinXP

[Mozilla/?.*(*Opera/7.*Windows NT 5.2*)*]
Parent=Opera 7.00
Platform=WinXP

[Mozilla/?.*(*Opera/7.*Windows NT 6.0*)*]
Parent=Opera 7.00
Platform=WinVista

[Mozilla/?.*(*Opera/7.*Windows NT 6.1*)*]
Parent=Opera 7.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?7.*]
Parent=Opera 7.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?7.*]
Parent=Opera 7.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?7.*]
Parent=Opera 7.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?7.*]
Parent=Opera 7.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?7.*]
Parent=Opera 7.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?7.*]
Parent=Opera 7.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?7.*]
Parent=Opera 7.00
Platform=Win7

[Opera/7.*(*FreeBSD*)*]
Parent=Opera 7.00
Platform=FreeBSD
Win32=false

[Opera/7.*(*Linux*)*]
Parent=Opera 7.00
Platform=Linux
Win32=false

[Opera/7.*(*Mac OS X*)*]
Parent=Opera 7.00
Platform=MacOSX
Win32=false

[Opera/7.*(*SunOS*)*]
Parent=Opera 7.00
Platform=SunOS
Win32=false

[Opera/7.*(*Win 9x 4.90*)*]
Parent=Opera 7.00
Platform=WinME

[Opera/7.*(*Windows 2000*)*]
Parent=Opera 7.00
Platform=Win2000

[Opera/7.*(*Windows 95*)*]
Parent=Opera 7.00
Platform=Win95

[Opera/7.*(*Windows 98*)*]
Parent=Opera 7.00
Platform=Win98

[Opera/7.*(*Windows ME*)*]
Parent=Opera 7.00
Platform=WinME

[Opera/7.*(*Windows NT 4.0*)*]
Parent=Opera 7.00
Platform=WinNT

[Opera/7.*(*Windows NT 5.0*)*]
Parent=Opera 7.00
Platform=Win2000

[Opera/7.*(*Windows NT 5.1*)*]
Parent=Opera 7.00
Platform=WinXP

[Opera/7.*(*Windows NT 5.2*)*]
Parent=Opera 7.00
Platform=WinXP

[Opera/7.*(*Windows NT 6.0*)*]
Parent=Opera 7.00
Platform=WinVista

[Opera/7.*(*Windows NT 6.1*)*]
Parent=Opera 7.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 8.00

[Opera 8.00]
Parent=DefaultProperties
Browser="Opera"
Version=8.00
MajorVer=8
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?8.*]
Parent=Opera 8.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?8.*]
Parent=Opera 8.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?8.*]
Parent=Opera 8.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/8.*FreeBSD*)*]
Parent=Opera 8.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/8.*Linux*)*]
Parent=Opera 8.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/8.*Mac OS X*)*]
Parent=Opera 8.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/8.*SunOS*)*]
Parent=Opera 8.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/8.*Win 9x 4.90*)*]
Parent=Opera 8.00
Platform=WinME

[Mozilla/?.*(*Opera/8.*Windows 2000*)*]
Parent=Opera 8.00
Platform=Win2000

[Mozilla/?.*(*Opera/8.*Windows 95*)*]
Parent=Opera 8.00
Platform=Win95

[Mozilla/?.*(*Opera/8.*Windows 98*)*]
Parent=Opera 8.00
Platform=Win98

[Mozilla/?.*(*Opera/8.*Windows ME*)*]
Parent=Opera 8.00

[Mozilla/?.*(*Opera/8.*Windows NT 4.0*)*]
Parent=Opera 8.00
Platform=WinNT

[Mozilla/?.*(*Opera/8.*Windows NT 5.0*)*]
Parent=Opera 8.00
Platform=Win2000

[Mozilla/?.*(*Opera/8.*Windows NT 5.1*)*]
Parent=Opera 8.00
Platform=WinXP

[Mozilla/?.*(*Opera/8.*Windows NT 5.2*)*]
Parent=Opera 8.00
Platform=WinXP

[Mozilla/?.*(*Opera/8.*Windows NT 6.0*)*]
Parent=Opera 8.00
Platform=WinVista

[Mozilla/?.*(*Opera/8.*Windows NT 6.1*)*]
Parent=Opera 8.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?8.*]
Parent=Opera 8.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?8.*]
Parent=Opera 8.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?8.*]
Parent=Opera 8.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?8.*]
Parent=Opera 8.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?8.*]
Parent=Opera 8.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?8.*]
Parent=Opera 8.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?8.*]
Parent=Opera 8.00
Platform=Win7

[Opera/8.*(*FreeBSD*)*]
Parent=Opera 8.00
Platform=FreeBSD
Win32=false

[Opera/8.*(*Linux*)*]
Parent=Opera 8.00
Platform=Linux
Win32=false

[Opera/8.*(*Mac OS X*)*]
Parent=Opera 8.00
Platform=MacOSX
Win32=false

[Opera/8.*(*SunOS*)*]
Parent=Opera 8.00
Platform=SunOS
Win32=false

[Opera/8.*(*Win 9x 4.90*)*]
Parent=Opera 8.00
Platform=WinME

[Opera/8.*(*Windows 2000*)*]
Parent=Opera 8.00
Platform=Win2000

[Opera/8.*(*Windows 95*)*]
Parent=Opera 8.00
Platform=Win95

[Opera/8.*(*Windows 98*)*]
Parent=Opera 8.00
Platform=Win98

[Opera/8.*(*Windows ME*)*]
Parent=Opera 8.00
Platform=WinME

[Opera/8.*(*Windows NT 4.0*)*]
Parent=Opera 8.00
Platform=WinNT

[Opera/8.*(*Windows NT 5.0*)*]
Parent=Opera 8.00
Platform=Win2000

[Opera/8.*(*Windows NT 5.1*)*]
Parent=Opera 8.00
Platform=WinXP

[Opera/8.*(*Windows NT 5.2*)*]
Parent=Opera 8.00
Platform=WinXP

[Opera/8.*(*Windows NT 6.0*)*]
Parent=Opera 8.00
Platform=WinVista

[Opera/8.*(*Windows NT 6.1*)*]
Parent=Opera 8.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 9.00

[Opera 9.00]
Parent=DefaultProperties
Browser="Opera"
Version=9.00
MajorVer=9
MinorVer=00
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?9.*]
Parent=Opera 9.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?9.*]
Parent=Opera 9.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?9.*]
Parent=Opera 9.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Mac_PowerPC*)*Opera?9.*]
Parent=Opera 9.00
Platform=MacPPC
Win32=false

[Mozilla/?.*(*Opera/9.*FreeBSD*)*]
Parent=Opera 9.00
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/9.*Linux*)*]
Parent=Opera 9.00
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/9.*Mac OS X*)*]
Parent=Opera 9.00
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/9.*SunOS*)*]
Parent=Opera 9.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/9.*Win 9x 4.90*)*]
Parent=Opera 9.00
Platform=WinME

[Mozilla/?.*(*Opera/9.*Windows 2000*)*]
Parent=Opera 9.00
Platform=Win2000

[Mozilla/?.*(*Opera/9.*Windows 95*)*]
Parent=Opera 9.00
Platform=Win95

[Mozilla/?.*(*Opera/9.*Windows 98*)*]
Parent=Opera 9.00
Platform=Win98

[Mozilla/?.*(*Opera/9.*Windows ME*)*]
Parent=Opera 9.00

[Mozilla/?.*(*Opera/9.*Windows NT 4.0*)*]
Parent=Opera 9.00
Platform=WinNT

[Mozilla/?.*(*Opera/9.*Windows NT 5.0*)*]
Parent=Opera 9.00
Platform=Win2000

[Mozilla/?.*(*Opera/9.*Windows NT 5.1*)*]
Parent=Opera 9.00
Platform=WinXP

[Mozilla/?.*(*Opera/9.*Windows NT 5.2*)*]
Parent=Opera 9.00
Platform=WinXP

[Mozilla/?.*(*Opera/9.*Windows NT 6.0*)*]
Parent=Opera 9.00
Platform=WinVista

[Mozilla/?.*(*Opera/9.*Windows NT 6.1*)*]
Parent=Opera 9.00
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?9.*]
Parent=Opera 9.00
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?9.*]
Parent=Opera 9.00
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?9.*]
Parent=Opera 9.00
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?9.*]
Parent=Opera 9.00
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?9.*]
Parent=Opera 9.00
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?9.*]
Parent=Opera 9.00
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?9.*]
Parent=Opera 9.00
Platform=Win7

[Opera/9.*(*FreeBSD*)*]
Parent=Opera 9.00
Platform=FreeBSD
Win32=false

[Opera/9.*(*Linux*)*]
Parent=Opera 9.00
Platform=Linux
Win32=false

[Opera/9.*(*Mac OS X*)*]
Parent=Opera 9.00
Platform=MacOSX
Win32=false

[Opera/9.*(*SunOS*)*]
Parent=Opera 9.00
Platform=SunOS
Win32=false

[Opera/9.*(*Win 9x 4.90*)*]
Parent=Opera 9.00
Platform=WinME

[Opera/9.*(*Windows 2000*)*]
Parent=Opera 9.00
Platform=Win2000

[Opera/9.*(*Windows 95*)*]
Parent=Opera 9.00
Platform=Win95

[Opera/9.*(*Windows 98*)*]
Parent=Opera 9.00
Platform=Win98

[Opera/9.*(*Windows ME*)*]
Parent=Opera 9.00
Platform=WinME

[Opera/9.*(*Windows NT 4.0*)*]
Parent=Opera 9.00
Platform=WinNT

[Opera/9.*(*Windows NT 5.0*)*]
Parent=Opera 9.00
Platform=Win2000

[Opera/9.*(*Windows NT 5.1*)*]
Parent=Opera 9.00
Platform=WinXP

[Opera/9.*(*Windows NT 5.2*)*]
Parent=Opera 9.00
Platform=WinXP

[Opera/9.*(*Windows NT 6.0*)*]
Parent=Opera 9.00
Platform=WinVista

[Opera/9.*(*Windows NT 6.1*)*]
Parent=Opera 9.00
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 9.50

[Opera 9.50]
Parent=DefaultProperties
Browser="Opera"
Version=9.50
MajorVer=9
MinorVer=50
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?9.5*]
Parent=Opera 9.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?9.5*]
Parent=Opera 9.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/9.5*FreeBSD*)*]
Parent=Opera 9.50
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/9.5*Linux*)*]
Parent=Opera 9.50
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/9.5*Mac OS X*)*]
Parent=Opera 9.50
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/9.5*SunOS*)*]
Parent=Opera 9.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/9.5*Win 9x 4.90*)*]
Parent=Opera 9.50
Platform=WinME

[Mozilla/?.*(*Opera/9.5*Windows 2000*)*]
Parent=Opera 9.50
Platform=Win2000

[Mozilla/?.*(*Opera/9.5*Windows 95*)*]
Parent=Opera 9.50
Platform=Win95

[Mozilla/?.*(*Opera/9.5*Windows 98*)*]
Parent=Opera 9.50
Platform=Win98

[Mozilla/?.*(*Opera/9.5*Windows ME*)*]
Parent=Opera 9.50

[Mozilla/?.*(*Opera/9.5*Windows NT 4.0*)*]
Parent=Opera 9.50
Platform=WinNT

[Mozilla/?.*(*Opera/9.5*Windows NT 5.0*)*]
Parent=Opera 9.50
Platform=Win2000

[Mozilla/?.*(*Opera/9.5*Windows NT 5.1*)*]
Parent=Opera 9.50
Platform=WinXP

[Mozilla/?.*(*Opera/9.5*Windows NT 5.2*)*]
Parent=Opera 9.50
Platform=WinXP

[Mozilla/?.*(*Opera/9.5*Windows NT 6.0*)*]
Parent=Opera 9.50
Platform=WinVista

[Mozilla/?.*(*Opera/9.5*Windows NT 6.1*)*]
Parent=Opera 9.50
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?9.5*]
Parent=Opera 9.50
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?9.5*]
Parent=Opera 9.50
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?9.5*]
Parent=Opera 9.50
Platform=Win7

[Opera/9.5*(*FreeBSD*)*]
Parent=Opera 9.50
Platform=FreeBSD
Win32=false

[Opera/9.5*(*Linux*)*]
Parent=Opera 9.50
Platform=Linux
Win32=false

[Opera/9.5*(*Mac OS X*)*]
Parent=Opera 9.50
Platform=MacOSX
Win32=false

[Opera/9.5*(*SunOS*)*]
Parent=Opera 9.50
Platform=SunOS
Win32=false

[Opera/9.5*(*Win 9x 4.90*)*]
Parent=Opera 9.50
Platform=WinME

[Opera/9.5*(*Windows 2000*)*]
Parent=Opera 9.50
Platform=Win2000

[Opera/9.5*(*Windows 95*)*]
Parent=Opera 9.50
Platform=Win95

[Opera/9.5*(*Windows 98*)*]
Parent=Opera 9.50
Platform=Win98

[Opera/9.5*(*Windows ME*)*]
Parent=Opera 9.50
Platform=WinME

[Opera/9.5*(*Windows NT 4.0*)*]
Parent=Opera 9.50
Platform=WinNT

[Opera/9.5*(*Windows NT 5.0*)*]
Parent=Opera 9.50
Platform=Win2000

[Opera/9.5*(*Windows NT 5.1*)*]
Parent=Opera 9.50
Platform=WinXP

[Opera/9.5*(*Windows NT 5.2*)*]
Parent=Opera 9.50
Platform=WinXP

[Opera/9.5*(*Windows NT 6.0*)*]
Parent=Opera 9.50
Platform=WinVista

[Opera/9.5*(*Windows NT 6.1*)*]
Parent=Opera 9.50
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera 9.60

[Opera 9.60]
Parent=DefaultProperties
Browser="Opera"
Version=9.60
MajorVer=9
MinorVer=60
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/?.*(*FreeBSD*)*Opera?9.6*]
Parent=Opera 9.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Linux*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Mac OS X*)*Opera?9.6*]
Parent=Opera 9.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/9.6*FreeBSD*)*]
Parent=Opera 9.60
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera/9.6*Linux*)*]
Parent=Opera 9.60
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera/9.6*Mac OS X*)*]
Parent=Opera 9.60
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera/9.6*SunOS*)*]
Parent=Opera 9.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera/9.6*Win 9x 4.90*)*]
Parent=Opera 9.60
Platform=WinME

[Mozilla/?.*(*Opera/9.6*Windows 2000*)*]
Parent=Opera 9.60
Platform=Win2000

[Mozilla/?.*(*Opera/9.6*Windows 95*)*]
Parent=Opera 9.60
Platform=Win95

[Mozilla/?.*(*Opera/9.6*Windows 98*)*]
Parent=Opera 9.60
Platform=Win98

[Mozilla/?.*(*Opera/9.6*Windows ME*)*]
Parent=Opera 9.60

[Mozilla/?.*(*Opera/9.6*Windows NT 4.0*)*]
Parent=Opera 9.60
Platform=WinNT

[Mozilla/?.*(*Opera/9.6*Windows NT 5.0*)*]
Parent=Opera 9.60
Platform=Win2000

[Mozilla/?.*(*Opera/9.6*Windows NT 5.1*)*]
Parent=Opera 9.60
Platform=WinXP

[Mozilla/?.*(*Opera/9.6*Windows NT 5.2*)*]
Parent=Opera 9.60
Platform=WinXP

[Mozilla/?.*(*Opera/9.6*Windows NT 6.0*)*]
Parent=Opera 9.60
Platform=WinVista

[Mozilla/?.*(*Opera/9.6*Windows NT 6.1*)*]
Parent=Opera 9.60
Platform=Win7

[Mozilla/?.*(*SunOS*)*Opera?9.6*]
Parent=Opera 9.60
Platform=SunOS
Win32=false

[Mozilla/?.*(*Win 9x 4.90*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinME

[Mozilla/?.*(*Windows 2000*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Win2000

[Mozilla/?.*(*Windows 95*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Win95

[Mozilla/?.*(*Windows 98*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Win98

[Mozilla/?.*(*Windows ME*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinME

[Mozilla/?.*(*Windows NT 4.0*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinNT

[Mozilla/?.*(*Windows NT 5.0*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Win2000

[Mozilla/?.*(*Windows NT 5.1*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 5.2*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinXP

[Mozilla/?.*(*Windows NT 6.0*)*Opera?9.6*]
Parent=Opera 9.60
Platform=WinVista

[Mozilla/?.*(*Windows NT 6.1*)*Opera?9.6*]
Parent=Opera 9.60
Platform=Win7

[Opera/9.6*(*FreeBSD*)*]
Parent=Opera 9.60
Platform=FreeBSD
Win32=false

[Opera/9.6*(*Linux*)*]
Parent=Opera 9.60
Platform=Linux
Win32=false

[Opera/9.6*(*Mac OS X*)*]
Parent=Opera 9.60
Platform=MacOSX
Win32=false

[Opera/9.6*(*SunOS*)*]
Parent=Opera 9.60
Platform=SunOS
Win32=false

[Opera/9.6*(*Win 9x 4.90*)*]
Parent=Opera 9.60
Platform=WinME

[Opera/9.6*(*Windows 2000*)*]
Parent=Opera 9.60
Platform=Win2000

[Opera/9.6*(*Windows 95*)*]
Parent=Opera 9.60
Platform=Win95

[Opera/9.6*(*Windows 98*)*]
Parent=Opera 9.60
Platform=Win98

[Opera/9.6*(*Windows ME*)*]
Parent=Opera 9.60
Platform=WinME

[Opera/9.6*(*Windows NT 4.0*)*]
Parent=Opera 9.60
Platform=WinNT

[Opera/9.6*(*Windows NT 5.0*)*]
Parent=Opera 9.60
Platform=Win2000

[Opera/9.6*(*Windows NT 5.1*)*]
Parent=Opera 9.60
Platform=WinXP

[Opera/9.6*(*Windows NT 5.2*)*]
Parent=Opera 9.60
Platform=WinXP

[Opera/9.6*(*Windows NT 6.0*)*]
Parent=Opera 9.60
Platform=WinVista

[Opera/9.6*(*Windows NT 6.1*)*]
Parent=Opera 9.60
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Opera Generic

[Opera Generic]
Parent=DefaultProperties
Browser="Opera"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
CssVersion=1

[Mozilla/?.*(*Opera?*FreeBSD*)*]
Parent=Opera Generic
Platform=FreeBSD
Win32=false

[Mozilla/?.*(*Opera?*Linux*)*]
Parent=Opera Generic
Platform=Linux
Win32=false

[Mozilla/?.*(*Opera?*Mac OS X*)*]
Parent=Opera Generic
Platform=MacOSX
Win32=false

[Mozilla/?.*(*Opera?*Macintosh*)*]
Parent=Opera Generic
Platform=MacPPC
Win32=false

[Mozilla/?.*(*Opera?*SunOS*)*]
Parent=Opera Generic
Platform=SunOS
Win32=false

[Mozilla/?.*(*Opera?*UNIX*)*]
Parent=Opera Generic
Platform=Unix
Win32=false

[Mozilla/?.*(*Opera?*Win 9x 4.90*)*]
Parent=Opera Generic
Platform=WinME

[Mozilla/?.*(*Opera?*Windows 2000*)*]
Parent=Opera Generic
Platform=Win2000

[Mozilla/?.*(*Opera?*Windows 95*)*]
Parent=Opera Generic
Platform=Win95

[Mozilla/?.*(*Opera?*Windows 98*)*]
Parent=Opera Generic
Platform=Win98

[Mozilla/?.*(*Opera?*Windows CE*)*]
Parent=Opera Generic
Platform=WinCE

[Mozilla/?.*(*Opera?*Windows ME*)*]
Parent=Opera Generic
Platform=WinME

[Mozilla/?.*(*Opera?*Windows NT 4.0*)*]
Parent=Opera Generic
Platform=WinNT

[Mozilla/?.*(*Opera?*Windows NT 5.0*)*]
Parent=Opera Generic
Platform=Win2000

[Mozilla/?.*(*Opera?*Windows NT 5.1*)*]
Parent=Opera Generic
Platform=WinXP

[Mozilla/?.*(*Opera?*Windows NT 5.2*)*]
Parent=Opera Generic
Platform=WinXP

[Mozilla/?.*(*Opera?*Windows NT 6.0*)*]
Parent=Opera Generic
Platform=WinVista

[Mozilla/?.*(*Opera?*Windows NT 6.1*)*]
Parent=Opera Generic
Platform=Win7

[Mozilla/?.*(*Opera?*Windows XP*)*]
Parent=Opera Generic
Platform=WinXP

[Mozilla/?.*(*Windows CE*Opera*]
Parent=Opera Generic
Platform=WinCE

[Mozilla/?.**(*FreeBSD*)*Opera*]
Parent=Opera Generic
Platform=FreeBSD
Win32=false

[Mozilla/?.**(*Linux*)*Opera*]
Parent=Opera Generic
Platform=Linux
Win32=false

[Mozilla/?.**(*Mac OS X*)*Opera*]
Parent=Opera Generic
Platform=MacOSX
Win32=false

[Mozilla/?.**(*Macintosh*)*Opera*]
Parent=Opera Generic
Platform=MacPPC
Win32=false

[Mozilla/?.**(*SunOS*)*Opera*]
Parent=Opera Generic
Platform=SunOS
Win32=false

[Mozilla/?.**(*UNIX*)*Opera*]
Parent=Opera Generic
Platform=Unix
Win32=false

[Mozilla/?.**(*Win 9x 4.90*)*Opera*]
Parent=Opera Generic
Platform=WinME

[Mozilla/?.**(*Windows 2000*)*Opera*]
Parent=Opera Generic
Platform=Win2000

[Mozilla/?.**(*Windows 95*)*Opera*]
Parent=Opera Generic
Platform=Win95

[Mozilla/?.**(*Windows 98*)*Opera*]
Parent=Opera Generic
Platform=Win98

[Mozilla/?.**(*Windows ME*Opera*)*]
Parent=Opera Generic

[Mozilla/?.**(*Windows NT 4.0*)*Opera*]
Parent=Opera Generic
Platform=WinNT

[Mozilla/?.**(*Windows NT 5.0*)*Opera*]
Parent=Opera Generic
Platform=Win2000

[Mozilla/?.**(*Windows NT 5.1*)*Opera*]
Parent=Opera Generic
Platform=WinXP

[Mozilla/?.**(*Windows NT 5.2*)*Opera*]
Parent=Opera Generic
Platform=WinXP

[Mozilla/?.**(*Windows NT 6.0*)*Opera*]
Parent=Opera Generic
Platform=WinVista

[Mozilla/?.**(*Windows NT 6.1*)*Opera*]
Parent=Opera Generic
Platform=Win7

[Mozilla/?.**(*Windows XP*)*Opera*]
Parent=Opera Generic
Platform=WinXP

[Opera/*(*FreeBSD*)*]
Parent=Opera Generic
Platform=FreeBSD
Win32=false

[Opera/*(*Linux*)*]
Parent=Opera Generic
Platform=Linux
Win32=false

[Opera/*(*Mac OS X*)*]
Parent=Opera Generic
Platform=MacOSX
Win32=false

[Opera/*(*Macintosh*)*]
Parent=Opera Generic
Platform=MacPPC
Win32=false

[Opera/*(*SunOS*)*]
Parent=Opera Generic
Platform=SunOS
Win32=false

[Opera/*(*UNIX*)*]
Parent=Opera Generic
Platform=Unix
Win32=false

[Opera/*(*Win 9x 4.90*)*]
Parent=Opera Generic
Platform=WinME

[Opera/*(*Windows 2000*)*]
Parent=Opera Generic
Platform=Win2000

[Opera/*(*Windows 95*)*]
Parent=Opera Generic
Platform=Win95

[Opera/*(*Windows 98*)*]
Parent=Opera Generic
Platform=Win98

[Opera/*(*Windows CE*)*]
Parent=Opera Generic
Platform=WinCE

[Opera/*(*Windows ME*)*]
Parent=Opera Generic
Platform=WinME

[Opera/*(*Windows NT 4.0*)*]
Parent=Opera Generic
Platform=WinNT

[Opera/*(*Windows NT 5.0*)*]
Parent=Opera Generic
Platform=Win2000

[Opera/*(*Windows NT 5.1*)*]
Parent=Opera Generic
Platform=WinXP

[Opera/*(*Windows NT 5.2*)*]
Parent=Opera Generic
Platform=WinXP

[Opera/*(*Windows NT 6.0*)*]
Parent=Opera Generic
Platform=WinVista

[Opera/*(*Windows NT 6.1*)*]
Parent=Opera Generic
Platform=Win7

[Opera/*(*Windows XP*)*]
Parent=Opera Generic
Platform=WinXP

[Opera/9.80*(*FreeBSD*)*]
Parent=Opera Generic
Platform=FreeBSD
Win32=false

[Opera/9.80*(*Linux*)*]
Parent=Opera Generic
Platform=Linux
Win32=false

[Opera/9.80*(*Mac OS X*)*]
Parent=Opera Generic
Platform=MacOSX
Win32=false

[Opera/9.80*(*Macintosh*)*]
Parent=Opera Generic
Platform=MacPPC
Win32=false

[Opera/9.80*(*SunOS*)*]
Parent=Opera Generic
Platform=SunOS
Win32=false

[Opera/9.80*(*UNIX*)*]
Parent=Opera Generic
Platform=Unix
Win32=false

[Opera/9.80*(*Win 9x 4.90*)*]
Parent=Opera Generic
Platform=WinME

[Opera/9.80*(*Windows 2000*)*]
Parent=Opera Generic
Platform=Win2000

[Opera/9.80*(*Windows 95*)*]
Parent=Opera Generic
Platform=Win95

[Opera/9.80*(*Windows 98*)*]
Parent=Opera Generic
Platform=Win98

[Opera/9.80*(*Windows CE*)*]
Parent=Opera Generic
Platform=WinCE

[Opera/9.80*(*Windows ME*)*]
Parent=Opera Generic
Platform=WinME

[Opera/9.80*(*Windows NT 4.0*)*]
Parent=Opera Generic
Platform=WinNT

[Opera/9.80*(*Windows NT 5.0*)*]
Parent=Opera Generic
Platform=Win2000

[Opera/9.80*(*Windows NT 5.1*)*]
Parent=Opera Generic
Platform=WinXP

[Opera/9.80*(*Windows NT 5.2*)*]
Parent=Opera Generic
Platform=WinXP

[Opera/9.80*(*Windows NT 6.0*)*]
Parent=Opera Generic
Platform=WinVista

[Opera/9.80*(*Windows NT 6.1*)*]
Parent=Opera Generic
Platform=Win7

[Opera/9.80*(*Windows XP*)*]
Parent=Opera Generic
Platform=WinXP

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Navigator 9.0

[Navigator 9.0]
Parent=DefaultProperties
Browser="Navigator"
Version=9.0
MajorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; U; *Mac OS X*; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=MacOSX

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=Win7

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.8.*) Gecko/* Firefox/2.* Navigator/9.*]
Parent=Navigator 9.0
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netscape 4.0

[Netscape 4.0]
Parent=DefaultProperties
Browser="Netscape"
Version=4.0
MajorVer=4
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=1

[Mozilla/4.0*(Macintosh*]
Parent=Netscape 4.0
Version=4.03
MinorVer=03
Platform=MacPPC
Win32=false

[Mozilla/4.0*(Win95;*]
Parent=Netscape 4.0
Platform=Win95

[Mozilla/4.0*(Win98;*]
Parent=Netscape 4.0
Version=4.03
MinorVer=03
Platform=Win98

[Mozilla/4.0*(WinNT*]
Parent=Netscape 4.0
Version=4.03
MinorVer=03
Platform=WinNT

[Mozilla/4.0*(X11;*)]
Parent=Netscape 4.0
Platform=Linux

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netscape 6.0

[Netscape 6.0]
Parent=DefaultProperties
Browser="Netscape"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; PPC;*) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=MacPPC
Win32=false

[Mozilla/5.0 (Windows; ?; Win95;*) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Win9x 4.90; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Windows NT 4.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win7

[Mozilla/5.0 (Windows; ?; WinNT4.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; WinNT5.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; WinNT5.1; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT5.2; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT6.0; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; WinNT6.1; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Win7

[Mozilla/5.0 (X11; ?; *) Gecko/* Netscape6/6.*]
Parent=Netscape 6.0
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netscape 7.0

[Netscape 7.0]
Parent=DefaultProperties
Browser="Netscape"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; PPC Mac OS X;*) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=MacOSX

[Mozilla/5.0 (Macintosh; ?; PPC;*) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=MacPPC
Win32=false

[Mozilla/5.0 (Windows; ?; Win*9x 4.90; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win95;*) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 4.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win7

[Mozilla/5.0 (Windows; ?; WinNT4.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; WinNT5.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; WinNT5.1; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT5.2; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT6.0; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; WinNT6.1; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Win7

[Mozilla/5.0 (X11; ?; *) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*) Gecko/* Netscape*/7.*]
Parent=Netscape 7.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netscape 8.0

[Netscape 8.0]
Parent=DefaultProperties
Browser="Netscape"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; PPC Mac OS X Mach-O; *; rv:*) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Macintosh; ?; PPC Mac OS X;*) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Macintosh; ?; PPC;*) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=MacPPC
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win95;*) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Win9x 4.90; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Windows NT 4.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win7

[Mozilla/5.0 (Windows; ?; WinNT4.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; WinNT5.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; WinNT5.1; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT5.2; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT6.0; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; WinNT6.1; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Win7

[Mozilla/5.0 (X11; ?; *) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*) Gecko/* Netscape*/8.*]
Parent=Netscape 8.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Netscape 9.0

[Netscape 9.0]
Parent=DefaultProperties
Browser="Netscape"
Version=9.0
MajorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Macintosh; ?; PPC;*) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=MacPPC
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win95;*) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Win9x 4.90; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Windows NT 4.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win7

[Mozilla/5.0 (Windows; ?; WinNT4.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinNT

[Mozilla/5.0 (Windows; ?; WinNT5.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; WinNT5.1; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT5.2; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT6.0; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; WinNT6.1; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Win7

[Mozilla/5.0 (X11; ?; *) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*) Gecko/* Netscape*/9.*]
Parent=Netscape 9.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; PaleMoon 3.6

[Palemoon]
Parent=DefaultProperties
Browser="PaleMoon"
Version=3.6
MajorVer=3
MinorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *; rv:1.9.*) Gecko/20100403 Firefox/3.6.* (Palemoon/3.6.*)]
Parent=Palemoon
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.9.*) Gecko/20100403 Firefox/3.6.* (Palemoon/3.6.*)]
Parent=Palemoon
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *; rv:1.9.*) Gecko/20100403 Firefox/3.6.* (Palemoon/3.6.*)]
Parent=Palemoon
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9.*) Gecko/20100403 Firefox/3.6.* (Palemoon/3.6.*)]
Parent=Palemoon
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9.*) Gecko/20100403 Firefox/3.6.* (Palemoon/3.6.*)]
Parent=Palemoon
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; SeaMonkey 1.0

[SeaMonkey 1.0]
Parent=DefaultProperties
Browser="SeaMonkey"
Version=1.0
MajorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win98; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=Win7

[Mozilla/5.0 (X11; ?; FreeBSD*; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; ?; Linux*; *; rv:1.8*) Gecko/20060221 SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*; *; rv:1.8*) Gecko/* SeaMonkey/1.0*]
Parent=SeaMonkey 1.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; SeaMonkey 1.1

[SeaMonkey 1.1]
Parent=DefaultProperties
Browser="SeaMonkey"
Version=1.1
MajorVer=1
MinorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win98; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=Win98
Win32=false

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=Win7

[Mozilla/5.0 (X11; ?; FreeBSD*; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; ?; Linux*; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*; *; rv:1.8*) Gecko/* SeaMonkey/1.1*]
Parent=SeaMonkey 1.1
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; SeaMonkey 2.0

[SeaMonkey 2.0]
Parent=DefaultProperties
Browser="SeaMonkey"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win98; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=Win2003

[Mozilla/5.0 (Windows; ?; Windows NT 6.0; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=WinVista

[Mozilla/5.0 (Windows; ?; Windows NT 6.1; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=Win7

[Mozilla/5.0 (X11; ?; FreeBSD*; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; ?; Linux*; *; rv:1.9*) Gecko/20060221 SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; ?; SunOS*; *; rv:1.9*) Gecko/* SeaMonkey/2.0*]
Parent=SeaMonkey 2.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; SeaMonkey 2.1

[SeaMonkey 2.1]
Parent=DefaultProperties
Browser="SeaMonkey"
Version=2.1
MajorVer=2
MinorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Windows NT 5.1; rv:2.*) Gecko/* Firefox/4.* SeaMonkey/2.1*]
Parent=SeaMonkey 2.1
Platform=WinXP

[Mozilla/5.0 (Windows NT 6.0; rv:2.*) Gecko/* Firefox/4.* SeaMonkey/2.1*]
Parent=SeaMonkey 2.1
Platform=WinVista

[Mozilla/5.0 (Windows NT 6.1; rv:2.*) Gecko/* Firefox/4.* SeaMonkey/2.1*]
Parent=SeaMonkey 2.1
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Flock 1.0

[Flock 1.0]
Parent=DefaultProperties
Browser="Flock"
Version=1.0
MajorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; U; *Mac OS X*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Win 9x 4.90; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=WinME

[Mozilla/5.0 (Windows; U; Windows NT 5.0*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1*; *; rv:1.*) Gecko/* Firefox/2.* Flock/1.*]
Parent=Flock 1.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Flock 2.0

[Flock 2.0]
Parent=DefaultProperties
Browser="Flock"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Macintosh; U; *Mac OS X*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Win 9x 4.90; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=WinME

[Mozilla/5.0 (Windows; U; Windows NT 5.0*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1*; *; rv:1.*) Gecko/* Firefox/3.* Flock/2.*]
Parent=Flock 2.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Flock 3.0

[Flock 3.0]
Parent=DefaultProperties
Browser="Flock"
Version=3.0
MajorVer=3
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; U; *Mac OS X*; *) AppleWebKit/* (KHTML, like Gecko) Flock/3.* Chrome/* Safari/*]
Parent=Flock 3.0
Version=3.5
MajorVer=3
MinorVer=5
Platform=MacOSX

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Sleipnir 2.0

[Sleipnir]
Parent=DefaultProperties
Browser="Sleipnir"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.0*) Sleipnir/2.*]
Parent=Sleipnir
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.1*) Sleipnir/2.*]
Parent=Sleipnir
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 5.2*) Sleipnir/2.*]
Parent=Sleipnir
Platform=Win2003

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 6.0*) Sleipnir/2.*]
Parent=Sleipnir
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE ?.0; Windows NT 6.1*) Sleipnir/2.*]
Parent=Sleipnir
Platform=Win7

[Sleipnir*]
Parent=Sleipnir
Win32=false

[Sleipnir/2.*]
Parent=Sleipnir
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Fennec 1.0

[Fennec 1.0]
Parent=DefaultProperties
Browser="Firefox Mobile"
Version=1.0
MajorVer=1
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=3

[Mozilla/*(*Windows NT 5.1*)*Fennec/1.0*]
Parent=Fennec 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9*) Gecko/* Fennec/1.0*]
Parent=Fennec 1.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9*) Gecko/* Fennec/1.0*]
Parent=Fennec 1.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Fennec 1.1

[Fennec 1.1]
Parent=DefaultProperties
Browser="Fennec"
Version=1.1
MajorVer=1
MinorVer=1
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=3

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.9*) Gecko/* Fennec/1.1*]
Parent=Fennec 1.1
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9*) Gecko/* Fennec/1.1*]
Parent=Fennec 1.1
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9*) Gecko/* Fennec/1.1*]
Parent=Fennec 1.1
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Fennec 4.0

[Fennec 4.0]
Parent=DefaultProperties
Browser="Fennec"
Version=4.0
MajorVer=4
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
isMobileDevice=true
CssVersion=3

[Mozilla/5.0 (Android; Linux*; rv:2.*) Gecko/* Firefox/4.* Fennec/4.0*]
Parent=Fennec 4.0
Platform=Android

[Mozilla/5.0 (Macintosh; Intel Mac OS X*; rv:2.1) Gecko/* Firefox/4.0* Fennec/4.*]
Parent=Fennec 4.0
Platform=MacOSX

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 1.0

[Firefox 1.0]
Parent=DefaultProperties
Browser="Firefox"
Version=1.0
MajorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Linux; *; PPC*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=MacPPC
Win32=false

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (OS/2; *; Warp*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=OS/2
Win32=false

[Mozilla/5.0 (Windows; *; Win 9x 4.90*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=WinME

[Mozilla/5.0 (Windows; *; Win95; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Win95

[Mozilla/5.0 (Windows; *; Win98; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Win98

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.1; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=WinVista

[Mozilla/5.0 (Windows; *; WinNT4.0; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=WinNT

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; *Linux*; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; DragonFly*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.*) Gecko/* Firefox/1.*]
Parent=Firefox 1.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 2.0

[Firefox 2.0]
Parent=DefaultProperties
Browser="Firefox"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (Linux; *; PPC*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Linux
Win32=false

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (OS/2; *; Warp*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=OS/2
Win32=false

[Mozilla/5.0 (Windows; *; Win 9x 4.90; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=WinME

[Mozilla/5.0 (Windows; *; Win95; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Win95

[Mozilla/5.0 (Windows; *; Win98; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Win98

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Win7

[Mozilla/5.0 (Windows; *; WinNT4.0; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=WinNT

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.8*) Gecko/* Firefox/2.*]
Parent=Firefox 2.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 3.0

[Firefox 3.0]
Parent=DefaultProperties
Browser="Firefox"
Version=3.0
MajorVer=3
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Win7

[Mozilla/5.0 (Windows; U; Windows NT 5.1 x64; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 5.2 x64; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.0 x64; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.1 x64; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.9*) Gecko/* Firefox/3.0*]
Parent=Firefox 3.0
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 3.1

[Firefox 3.1]
Parent=DefaultProperties
Browser="Firefox"
Version=3.1
MajorVer=3
MinorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Win7

[Mozilla/5.0 (Windows; U; Windows NT 5.1 x64; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 5.2 x64; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.0 x64; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.1 x64; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.9*) Gecko/* Firefox/3.1*]
Parent=Firefox 3.1
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 3.5

[Firefox 3.5]
Parent=DefaultProperties
Browser="Firefox"
Version=3.5
MajorVer=3
MinorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Win7

[Mozilla/5.0 (Windows; U; Windows NT 5.1 x64; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 5.2 x64; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.0 x64; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.1 x64; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.9.*) Gecko/* Firefox/3.5*]
Parent=Firefox 3.5
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 3.6

[Firefox 3.6]
Parent=DefaultProperties
Browser="Firefox"
Version=3.6
MajorVer=3
MinorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (Macintosh; *; *Mac OS X*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; *; Windows NT 5.0; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Win2000

[Mozilla/5.0 (Windows; *; Windows NT 5.1; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=WinXP

[Mozilla/5.0 (Windows; *; Windows NT 5.2; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Win2003

[Mozilla/5.0 (Windows; *; Windows NT 6.0; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=WinVista

[Mozilla/5.0 (Windows; *; Windows NT 6.1; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Win7

[Mozilla/5.0 (Windows; U; Windows NT 5.1 x64; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 5.2 x64; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.0 x64; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (Windows; U; Windows NT 6.1 x64; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (X11; *; *Linux*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:1.9.2*) Gecko/* Firefox/3.6*]
Parent=Firefox 3.6
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 4.0

[Firefox 4.0]
Parent=DefaultProperties
Browser="Firefox"
Version=4.0
MajorVer=4
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*FreeBSD*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (*HP-UX*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (*IRIX64*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (*Linux*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*rv:2.0*) Gecko/*Firefox/4.0*]
Parent=Firefox 4.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*OpenBSD*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (*SunOS*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=SunOS
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 5.2*WOW64*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1 WOW64*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*rv:2.0*) Gecko/* Firefox/4.0*]
Parent=Firefox 4.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 4.2

[Firefox 4.2]
Parent=DefaultProperties
Browser="Firefox"
Version=4.2
MajorVer=4
MinorVer=2
Alpha=true
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Windows NT 5.0*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Win2003

[Mozilla/5.0 (*Windows NT 5.2*WOW64*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1 WOW64*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Win7

[Mozilla/5.0 (Macintosh; *Mac OS X*; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=MacOSX
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=SunOS
Win32=false

[Mozilla/5.0 (X11; *Linux*; rv:2.2a*) Gecko/* Firefox/4.2a*]
Parent=Firefox 4.2
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 5.0

[Firefox 5.0]
Parent=DefaultProperties
Browser="Firefox"
Version=5.0
MajorVer=5
Alpha=true
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*Windows NT 5.0*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 5.2*WOW64*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1 WOW64*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Win7

[Mozilla/5.0 (Macintosh; *Mac OS X*; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (X11; *; FreeBSD*; *; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *; HP-UX*; *; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (X11; *; IRIX64*; *; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (X11; *; OpenBSD*; *; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *; SunOS*; *; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=SunOS
Win32=false

[Mozilla/5.0 (X11; *Linux*; rv:5.*) Gecko/* Firefox/5.0*]
Parent=Firefox 5.0
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 6.0

[Firefox 6.0]
Parent=DefaultProperties
Browser="Firefox"
Version=6.0
MajorVer=6
Alpha=true
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*FreeBSD*)*Gecko/*Firefox/6.*]
Parent=Firefox 6.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (*HP-UX**) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (*IRIX64*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (*Linux*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*OpenBSD*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (*SunOS*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=SunOS
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 5.2*WOW64*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*WOW64*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*rv:*) Gecko/* Firefox/6.*]
Parent=Firefox 6.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Firefox 7.0

[Firefox 7.0]
Parent=DefaultProperties
Browser="Firefox"
Version=7.0
MajorVer=7
Alpha=true
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=3

[Mozilla/5.0 (*FreeBSD*)*Gecko/*Firefox/7.*]
Parent=Firefox 7.0
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (*HP-UX**) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=HP-UX
Win32=false

[Mozilla/5.0 (*IRIX64*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=IRIX64
Win32=false

[Mozilla/5.0 (*Linux*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Linux
Win32=false

[Mozilla/5.0 (*Mac OS X*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (*OpenBSD*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (*SunOS*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=SunOS
Win32=false

[Mozilla/5.0 (*Windows NT 5.0*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Win2000

[Mozilla/5.0 (*Windows NT 5.1*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=WinXP

[Mozilla/5.0 (*Windows NT 5.1*WOW64*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 5.2*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Win2003

[Mozilla/5.0 (*Windows NT 5.2*WOW64*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.0*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=WinVista

[Mozilla/5.0 (*Windows NT 6.0*WOW64*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*WOW64*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (*Windows NT 6.1*rv:*) Gecko/* Firefox/7.*]
Parent=Firefox 7.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Thunderbird 1.0

[Thunderbird 1.0]
Parent=DefaultProperties
Browser="Thunderbird"
Version=1.0
MajorVer=1
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/5.0 (Macintosh; *Mac OS X; U; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=Win7

[Mozilla/5.0 (X11; U; Linux i686*; *; rv:1.9.*) Gecko/* Thunderbird/1.*]
Parent=Thunderbird 1.0
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Thunderbird 2.0

[Thunderbird 2.0]
Parent=DefaultProperties
Browser="Thunderbird"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/5.0 (Macintosh; *Mac OS X; U; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=Win7

[Mozilla/5.0 (X11; U; Linux i686*; *; rv:1.9.*) Gecko/* Thunderbird/2.*]
Parent=Thunderbird 2.0
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Thunderbird 3.0

[Thunderbird 3.0]
Parent=DefaultProperties
Browser="Thunderbird"
Version=3.0
MajorVer=3
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true

[Mozilla/5.0 (Macintosh; *Mac OS X; U; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; U; Windows NT 5.0; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=Win2000

[Mozilla/5.0 (Windows; U; Windows NT 5.1; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=WinXP

[Mozilla/5.0 (Windows; U; Windows NT 5.2; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=Win2003

[Mozilla/5.0 (Windows; U; Windows NT 6.0; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=WinVista

[Mozilla/5.0 (Windows; U; Windows NT 6.1; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=Win7

[Mozilla/5.0 (X11; U; Linux i686*; *; rv:1.9.*) Gecko/* Thunderbird/3.*]
Parent=Thunderbird 3.0
Platform=Linux
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Iceweasel

[Iceweasel]
Parent=DefaultProperties
Browser="Iceweasel"
Platform=Debian
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.8.*) Gecko/* Iceweasel/2.0* (Debian-*)*]
Parent=Iceweasel
Version=2.0
MajorVer=2
MinorVer=0

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.9.*) Gecko/* Iceweasel/3.0* (Debian-*)*]
Parent=Iceweasel
Version=3.0
MajorVer=3
MinorVer=0
Platform=Debian
CssVersion=2

[Mozilla/5.0 (X11; U; Linux*; *; rv:1.9.*) Gecko/* Iceweasel/3.5* (Debian-*)]
Parent=Iceweasel
Version=3.5
MajorVer=3
MinorVer=5

[Mozilla/5.0 (X11; U; Linux; *; rv:1.9.*) Gecko/* Iceweasel/3.6* (like Firefox/3.6)*]
Parent=Iceweasel
Version=3.6
MajorVer=3
MinorVer=6

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.0

[Mozilla 1.0]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.0
MajorVer=1
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.0.*) Gecko/*]
Parent=Mozilla 1.0

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.1

[Mozilla 1.1]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.1
MajorVer=1
MinorVer=1
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.1.*) Gecko/*]
Parent=Mozilla 1.1

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.2

[Mozilla 1.2]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.2
MajorVer=1
MinorVer=2
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.2.*) Gecko/*]
Parent=Mozilla 1.2

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.3

[Mozilla 1.3]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.3
MajorVer=1
MinorVer=3
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.3.*) Gecko/*]
Parent=Mozilla 1.3

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.4

[Mozilla 1.4]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.4
MajorVer=1
MinorVer=4
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.4*) Gecko/*]
Parent=Mozilla 1.4
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.5

[Mozilla 1.5]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.5
MajorVer=1
MinorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.5*) Gecko/*]
Parent=Mozilla 1.5
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.6

[Mozilla 1.6]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.6
MajorVer=1
MinorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=WinXP

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.6*) Gecko/*]
Parent=Mozilla 1.6
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.7

[Mozilla 1.7]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.7
MajorVer=1
MinorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win98
Win32=true

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.7*) Gecko/*]
Parent=Mozilla 1.7
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.8

[Mozilla 1.8]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.8
MajorVer=1
MinorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.8*) Gecko/*]
Parent=Mozilla 1.8
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Mozilla 1.9

[Mozilla 1.9]
Parent=DefaultProperties
Browser="Mozilla"
Version=1.9
MajorVer=1
MinorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
JavaApplets=true
CssVersion=2

[Mozilla/5.0 (*rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Win32=false

[Mozilla/5.0 (Macintosh; ?; *Mac OS X*; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=MacOSX
Win32=false

[Mozilla/5.0 (Windows; ?; Win 9x 4.90; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=WinME

[Mozilla/5.0 (Windows; ?; Win3.1; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win3.11; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Win31
Win16=true
Win32=false

[Mozilla/5.0 (Windows; ?; Win95; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Win95

[Mozilla/5.0 (Windows; ?; Win98; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Win98

[Mozilla/5.0 (Windows; ?; Windows NT 5.0; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Win2000

[Mozilla/5.0 (Windows; ?; Windows NT 5.1; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=WinXP

[Mozilla/5.0 (Windows; ?; Windows NT 5.2; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Win2003

[Mozilla/5.0 (Windows; ?; WinNT4.0; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=WinNT

[Mozilla/5.0 (X11; *FreeBSD*; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=FreeBSD
Win32=false

[Mozilla/5.0 (X11; *Linux*; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=Linux
Win32=false

[Mozilla/5.0 (X11; *OpenBSD*; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=OpenBSD
Win32=false

[Mozilla/5.0 (X11; *SunOS*; *rv:1.9*) Gecko/*]
Parent=Mozilla 1.9
Platform=SunOS
Win32=false

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE Mac

[IE Mac]
Parent=DefaultProperties
Browser="IE"
Platform=MacPPC
Frames=true
IFrames=true
Tables=true
Cookies=true
JavaScript=true
CssVersion=1

[Mozilla/?.? (compatible; MSIE 4.0*; *Mac_PowerPC*]
Parent=IE Mac
Version=4.0
MajorVer=4
MinorVer=0

[Mozilla/?.? (compatible; MSIE 4.5*; *Mac_PowerPC*]
Parent=IE Mac
Version=4.5
MajorVer=4
MinorVer=5

[Mozilla/?.? (compatible; MSIE 5.0*; *Mac_PowerPC*]
Parent=IE Mac
Version=5.0
MajorVer=5
MinorVer=0

[Mozilla/?.? (compatible; MSIE 5.1*; *Mac_PowerPC*]
Parent=IE Mac
Version=5.1
MajorVer=5
MinorVer=1

[Mozilla/?.? (compatible; MSIE 5.2*; *Mac_PowerPC*]
Parent=IE Mac
Version=5.2
MajorVer=5
MinorVer=2

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.0/IE 5.5

[AOL 9.0/IE 5.5]
Parent=DefaultProperties
Browser="IE"
Version=5.5
MajorVer=5
MinorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Win 9x 4.90*]
Parent=AOL 9.0/IE 5.5
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows 95*]
Parent=AOL 9.0/IE 5.5
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows 98*]
Parent=AOL 9.0/IE 5.5
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.0/IE 5.5
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows NT 4.0*]
Parent=AOL 9.0/IE 5.5
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows NT 5.0*]
Parent=AOL 9.0/IE 5.5
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows NT 5.1*]
Parent=AOL 9.0/IE 5.5
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 5.5; *AOL 9.0*; *Windows NT 6.0*]
Parent=AOL 9.0/IE 5.5
Platform=WinVista

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.0/IE 6.0

[AOL 9.0/IE 6.0]
Parent=DefaultProperties
Browser="IE"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Win 9x 4.90*]
Parent=AOL 9.0/IE 6.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows 95*]
Parent=AOL 9.0/IE 6.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows 98*]
Parent=AOL 9.0/IE 6.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.0/IE 6.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows NT 4.0*]
Parent=AOL 9.0/IE 6.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows NT 5.0*]
Parent=AOL 9.0/IE 6.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows NT 5.1*]
Parent=AOL 9.0/IE 6.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows NT 6.0*]
Parent=AOL 9.0/IE 6.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 6.0; *AOL 9.0*; *Windows NT 6.1*]
Parent=AOL 9.0/IE 6.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.0/IE 7.0

[AOL 9.0/IE 7.0]
Parent=DefaultProperties
Browser="IE"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Win 9x 4.90*]
Parent=AOL 9.0/IE 7.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows 95*]
Parent=AOL 9.0/IE 7.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows 98*]
Parent=AOL 9.0/IE 7.0

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.0/IE 7.0

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows NT 4.0*]
Parent=AOL 9.0/IE 7.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows NT 5.0*]
Parent=AOL 9.0/IE 7.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows NT 5.1*]
Parent=AOL 9.0/IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows NT 6.0*]
Parent=AOL 9.0/IE 7.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.0*; *Windows NT 6.1*]
Parent=AOL 9.0/IE 7.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.0/IE 8.0

[AOL 9.0/IE 8.0]
Parent=DefaultProperties
Browser="IE"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Win 9x 4.90*]
Parent=AOL 9.0/IE 8.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows 95*]
Parent=AOL 9.0/IE 8.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows 98*]
Parent=AOL 9.0/IE 8.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.0/IE 8.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows NT 4.0*]
Parent=AOL 9.0/IE 8.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows NT 5.0*]
Parent=AOL 9.0/IE 8.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows NT 5.1*]
Parent=AOL 9.0/IE 8.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows NT 6.0*]
Parent=AOL 9.0/IE 8.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.0*; *Windows NT 6.1*]
Parent=AOL 9.0/IE 8.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.1/IE 7.0

[AOL 9.1/IE 7.0]
Parent=DefaultProperties
Browser="IE"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Win 9x 4.90*]
Parent=AOL 9.1/IE 7.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows 95*]
Parent=AOL 9.1/IE 7.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows 98*]
Parent=AOL 9.1/IE 7.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.1/IE 7.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 4.0*]
Parent=AOL 9.1/IE 7.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 5.0*]
Parent=AOL 9.1/IE 7.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 5.1*]
Parent=AOL 9.1/IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 5.2*]
Parent=AOL 9.1/IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 6.0*]
Parent=AOL 9.1/IE 7.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0; *AOL 9.1*; *Windows NT 6.1*]
Parent=AOL 9.1/IE 7.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.1/IE 8.0

[AOL 9.1/IE 8.0]
Parent=DefaultProperties
Browser="IE"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2
AolVersion=9

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Win 9x 4.90*]
Parent=AOL 9.1/IE 8.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows 95*]
Parent=AOL 9.1/IE 8.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows 98*]
Parent=AOL 9.1/IE 8.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows 98; Win 9x 4.90*]
Parent=AOL 9.1/IE 8.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 4.0*]
Parent=AOL 9.1/IE 8.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 5.0*]
Parent=AOL 9.1/IE 8.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 5.1*]
Parent=AOL 9.1/IE 8.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 5.2*]
Parent=AOL 9.1/IE 8.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 6.0*]
Parent=AOL 9.1/IE 8.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; *AOL 9.1*; *Windows NT 6.1*]
Parent=AOL 9.1/IE 8.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.5

[AOL 9.5]
Parent=DefaultProperties
Browser="IE"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=3
AolVersion=10

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.5; AOLBuild*; Windows NT 5.1; Trident/4.0*)*]
Parent=AOL 9.5
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.5; AOLBuild*; Windows NT 5.2; Trident/4.0*)*]
Parent=AOL 9.5
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.5; AOLBuild*; Windows NT 6.0; Trident/4.0*)*]
Parent=AOL 9.5
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.5; AOLBuild*; Windows NT 6.1; Trident/4.0*)*]
Parent=AOL 9.5
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL 9.6

[AOL 9.6]
Parent=DefaultProperties
Browser="IE"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=3
AolVersion=10

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 5.2; Trident/4.0*)*]
Parent=AOL 9.6
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 5.2; WOW64; Trident/4.0*)*]
Parent=AOL 9.6
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 6.0; Trident/4.0*)*]
Parent=AOL 9.6
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 6.0; WOW64; Trident/4.0*)*]
Parent=AOL 9.6
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 6.1; Trident/4.0*)*]
Parent=AOL 9.6
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 8.0; AOL 9.6; AOLBuild*; Windows NT 6.1; WOW64; Trident/4.0*)*]
Parent=AOL 9.6
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; AOL Generic

[AOL Generic]
Parent=DefaultProperties
Browser="IE"
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=1

[Mozilla/*(*MSIE*America Online Browser*]
Parent=AOL Generic

[Mozilla/*(*MSIE*AOL*]
Parent=AOL Generic

[Mozilla/*(*MSIE*AOL*Mac*]
Parent=AOL Generic
Platform=MacOSX

[Mozilla/*(*MSIE*AOL*Win*]
Parent=AOL Generic
Platform=Win

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Avant Browser

[Avant Browser]
Parent=DefaultProperties
Browser="Avant Browser"
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Advanced Browser (http://www.avantbrowser.com)]
Parent=Avant Browser

[Avant Browser*]
Parent=Avant Browser

[Avant Browser/*]
Parent=Avant Browser

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 1.0

[IE 1.0]
Parent=DefaultProperties
Browser="IE"
Version=1.0
MajorVer=1
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true

[Mozilla/*(*MSIE 1.*Windows 3.11*)*]
Parent=IE 1.0
Platform=Win31

[Mozilla/1.*(*MSIE 1.*Windows 95*)*]
Parent=IE 1.0
Win32=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 10.0

[IE 10.0]
Parent=DefaultProperties
Browser="IE"
Version=10.0
MajorVer=10
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=3

[Mozilla/*(*MSIE 10.*Windows NT 5.1*Trident/6.0*)*]
Parent=IE 10.0
Platform=WinXP

[Mozilla/*(*MSIE 10.*Windows NT 5.2*Trident/6.0*)*]
Parent=IE 10.0
Platform=WinXP

[Mozilla/*(*MSIE 10.*Windows NT 6.0*64*Trident/6.0*)*]
Parent=IE 10.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/*(*MSIE 10.*Windows NT 6.0*Trident/6.0*)*]
Parent=IE 10.0
Platform=WinVista

[Mozilla/*(*MSIE 10.*Windows NT 6.1*64*Trident/6.0*)*]
Parent=IE 10.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/*(*MSIE 10.*Windows NT 6.1*Trident/6.0*)*]
Parent=IE 10.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 2.0

[IE 2.0]
Parent=DefaultProperties
Browser="IE"
Version=2.0
MajorVer=2
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true

[Mozilla/*(*MSIE 2.*Windows 3.1*)*]
Parent=IE 2.0
Platform=Win31

[Mozilla/*(*MSIE 2.*Windows 95*)*]
Parent=IE 2.0
Platform=Win95

[Mozilla/*(*MSIE 2.*Windows 98*)*]
Parent=IE 2.0
Platform=Win98

[Mozilla/*(*MSIE 2.*Windows CE*)*]
Parent=IE 2.0
Platform=WinCE
isMobileDevice=true

[Mozilla/*(*MSIE 2.*Windows NT*)*]
Parent=IE 2.0
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 4.01

[IE 4.01]
Parent=DefaultProperties
Browser="IE"
Version=4.01
MajorVer=4
MinorVer=01
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/?.* (?compatible; *MSIE 4.01*)*]
Parent=IE 4.01

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows 95*)*]
Parent=IE 4.01
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows 98*)*]
Parent=IE 4.01
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows 98; Win 9x 4.90;*)*]
Parent=IE 4.01
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows NT 4.0*)*]
Parent=IE 4.01
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows NT 5.0*)*]
Parent=IE 4.01
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 4.01; *Windows NT 5.01*)*]
Parent=IE 4.01
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 4.01; Windows NT)]
Parent=IE 4.01
Platform=WinNT

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 5.0

[IE 5.0]
Parent=DefaultProperties
Browser="IE"
Version=5.0
MajorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/?.* (?compatible; *MSIE 5.0*)*]
Parent=IE 5.0

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows 95*)*]
Parent=IE 5.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows 98*)*]
Parent=IE 5.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows 98; Win 9x 4.90;*)*]
Parent=IE 5.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows NT 4.0*)*]
Parent=IE 5.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows NT 5.0*)*]
Parent=IE 5.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 5.0; *Windows NT 5.01*)*]
Parent=IE 5.0
Platform=Win2000

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 5.01

[IE 5.01]
Parent=DefaultProperties
Browser="IE"
Version=5.01
MajorVer=5
MinorVer=01
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/?.* (?compatible; *MSIE 5.01*)*]
Parent=IE 5.01

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows 95*)*]
Parent=IE 5.01
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows 98*)*]
Parent=IE 5.01
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows 98; Win 9x 4.90;*)*]
Parent=IE 5.01
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows NT 4.0*)*]
Parent=IE 5.01
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows NT 5.0*)*]
Parent=IE 5.01
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 5.01; *Windows NT 5.01*)*]
Parent=IE 5.01
Platform=Win2000

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 5.5

[IE 5.5]
Parent=DefaultProperties
Browser="IE"
Version=5.5
MajorVer=5
MinorVer=5
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/?.* (?compatible; *MSIE 5.5*)*]
Parent=IE 5.5

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows 95*)*]
Parent=IE 5.5
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows 98*)*]
Parent=IE 5.5
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows 98; Win 9x 4.90*)*]
Parent=IE 5.5
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows NT 4.0*)*]
Parent=IE 5.5
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows NT 5.0*)*]
Parent=IE 5.5
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows NT 5.01*)*]
Parent=IE 5.5
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows NT 5.1*)*]
Parent=IE 5.5
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 5.5; *Windows NT 5.2*)*]
Parent=IE 5.5
Platform=WinXP

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 6.0

[IE 6.0]
Parent=DefaultProperties
Browser="IE"
Version=6.0
MajorVer=6
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/?.* (?compatible; *MSIE 6.0*)*]
Parent=IE 6.0

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows 95*)*]
Parent=IE 6.0
Platform=Win95

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows 98*)*]
Parent=IE 6.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows 98; Win 9x 4.90*)*]
Parent=IE 6.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 4.0*)*]
Parent=IE 6.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.0*)*]
Parent=IE 6.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.01*)*]
Parent=IE 6.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.1*)*]
Parent=IE 6.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.2*)*]
Parent=IE 6.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.2;*Win64;*)*]
Parent=IE 6.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 5.2;*WOW64;*)*]
Parent=IE 6.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 6.0; *Windows NT 6.0*)*]
Parent=IE 6.0
Platform=WinVista

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 7.0

[IE 7.0]
Parent=DefaultProperties
Browser="IE"
Version=7.0
MajorVer=7
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=2

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows 98*)*]
Parent=IE 7.0
Platform=Win98

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows 98; Win 9x 4.90;*)*]
Parent=IE 7.0
Platform=WinME

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 4.0*)*]
Parent=IE 7.0
Platform=WinNT

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.0*)*]
Parent=IE 7.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.01*)*]
Parent=IE 7.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.1*)*]
Parent=IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.2*)*]
Parent=IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.2;*Win64;*)*]
Parent=IE 7.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.2;*WOW64; Trident/4.0*)*]
Parent=IE 7.0
Platform=Win2003
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 5.2;*WOW64;*)*]
Parent=IE 7.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 6.0*)*]
Parent=IE 7.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0*; *Windows NT 6.1*)*]
Parent=IE 7.0
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 7.0*; Windows NT 5.1; Trident/4.0*)*]
Parent=IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0*; Windows NT 5.2; Trident/4.0*)*]
Parent=IE 7.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 7.0*; Windows NT 6.0; Trident/4.0*)*]
Parent=IE 7.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 7.0*; Windows NT 6.1; WOW64; Trident/4.0; *)*]
Parent=IE 7.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 8.0

[IE 8.0]
Parent=DefaultProperties
Browser="IE"
Version=8.0
MajorVer=8
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=3

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.0*)*]
Parent=IE 8.0
Version=7.0
MajorVer=7
MinorVer=0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.0; Win64; x64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win2000
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.0; WOW64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win2000
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.0;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win2000

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.1*)*]
Parent=IE 8.0
Version=7.0
MajorVer=7
MinorVer=0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.1; Win64; x64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.1; WOW64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.1;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.2*)*]
Parent=IE 8.0
Version=7.0
MajorVer=7
MinorVer=0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.2; Win64; x64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.2; WOW64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 5.2;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinXP

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.0*)*]
Parent=IE 8.0
Version=7.0
MajorVer=7
MinorVer=0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.0; Win64; x64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.0; WOW64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.0;*Trident/4.0*)*]
Parent=IE 8.0
Platform=WinVista

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.1*)*]
Parent=IE 8.0
Version=7.0
MajorVer=7
MinorVer=0
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.1; Win64; x64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.1; WOW64;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 6.1;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win7

[Mozilla/4.0 (compatible; MSIE 8.0; *Windows NT 7.0;*Trident/4.0*)*]
Parent=IE 8.0
Platform=Win7

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; IE 9.0

[IE 9.0]
Parent=DefaultProperties
Browser="IE"
Version=9.0
MajorVer=9
Win32=true
Frames=true
IFrames=true
Tables=true
Cookies=true
BackgroundSounds=true
JavaScript=true
VBScript=true
JavaApplets=true
ActiveXControls=true
CssVersion=3

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 5.1; Trident/5.0*)*]
Parent=IE 9.0
Platform=WinXP

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 5.2; Trident/5.0*)*]
Parent=IE 9.0
Platform=WinXP

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.0; Trident/5.0*)*]
Parent=IE 9.0
Platform=WinVista

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.0; Win64; x64; Trident/5.0*)*]
Parent=IE 9.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.0; WOW64; Trident/5.0*)*]
Parent=IE 9.0
Platform=WinVista
Win32=false
Win64=true

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.1; Trident/5.0*)*]
Parent=IE 9.0
Platform=Win7

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.1; Win64; x64; Trident/5.0*)*]
Parent=IE 9.0
Platform=Win7
Win32=false
Win64=true

[Mozilla/5.0 (compatible; MSIE 9.0; *Windows NT 6.1; WOW64; Trident/5.0*)*]
Parent=IE 9.0
Platform=Win7
Win32=false
Win64=true

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;; Default Browser

[*]
Browser="Default Browser"
Version=0
MajorVer=0
MinorVer=0
Platform=unknown
Alpha=false
Beta=false
Win16=false
Win32=false
Win64=false
Frames=false
IFrames=false
Tables=false
Cookies=false
BackgroundSounds=false
JavaScript=false
VBScript=false
JavaApplets=false
ActiveXControls=false
isBanned=false
isMobileDevice=false
isSyndicationReader=false
Crawler=false
CssVersion=0
AolVersion=0

