/* FORM SUBMISSION FUNCTIONS */
/* ======================== */
/**
 * Manages form submissions
 *
 */

/**
 * Disable all form elements if occure disable hidden field in all forms in the document
 */
if (!$('disable_preload')) {
    Event.observe(window, 'load', preventResubmitForms);
}
Event.observe(window, 'load', manageLockedRecords);
Event.observe(window, 'load', manageReminders);
Event.observe(window, 'load', manageActivities);
Event.observe(window, 'load', manageSaveGlobalsCheck);
Event.observe(window, 'load', positionAbsoluteDivs);
Event.observe(window, 'load', function() {
    const mutationObserver = new MutationObserver(function(mutationList, observer) {searchableSelects()});
    mutationObserver.observe(document.body, {childList: true, subtree: true, attributeFilter: [ "class"]});
    searchableSelects()
});

function preventResubmitForms() {

    var forms = document.getElementsByTagName('form');
    if (forms) {
        for (var i = 0; i < forms.length; i++) {
            var form = forms[i];

            if (form.onsubmit) {
                form.oldOnSubmit = form.onsubmit;
            }
            form.onsubmit = function() {
                // prevent the submit of the form when there is still working autocompleters
                var unfinished_autocompleters = $$('.working');
                if (unfinished_autocompleters.length > 0) {
                    return false;
                }

                //alert user for empty(negative) quantities in GT2 that will not be saved
                var gt2_empty_rows = $$('.grouping_table2 .attention');
                if (gt2_empty_rows.length > 0) {
                    alert(i18n['messages']['gt2_empty_quantities']);
                    return false;
                }
                var gt2_empty_rows = $$('.grouping_table2 .attention_price');
                if (gt2_empty_rows.length > 0) {
                    alert(i18n['messages']['gt2_positive_prices']);
                    return false;
                }

                if ($('multiprintButton') != null) {
                    return true;
                }

                if (!$('submit_button') || $('submit_button').value != 'preview') {
                    if (this.disable_preload) {
                        return true;
                    }
                    if (this.oldOnSubmit) {
                        if (this.oldOnSubmit()) {
                            return preventResubmit(this);
                        } else {
                            return false;
                        }
                    } else {
                        return preventResubmit(this);
                    }
                }
            };
        }
    }
}

/**
 * When submit a form all BUTTON elements are disabled
 * to prevent next resubmission
 *
 * @param frm - the current form object
 */
function preventResubmit(frm) {
    if (frm.submitted) {
        return false;
    }

    var disable_all_buttons = true;
    if (preventResubmit.arguments[1]) {
        disable_all_buttons = false;
    }

    showLoading();

    var buttons;
    if (disable_all_buttons) {
        buttons = document.getElementsByTagName('button');
    } else {
        buttons = frm.getElementsByTagName('button');
    }
    for (var i in buttons) {
        buttons[i].className += ' button_inactive';
        buttons[i].disabled = true;
    }

    frm.submitted = true;

    return true;
}

/**
 * When we need we disable form submit
 *
 * @param frm - the current form object
 */
function preventSubmit(frm, action) {

    var buttons = frm.getElementsByTagName('button');

    if (action) {
        Effect.Center('loading');
        Effect.Appear('loading');
        for (var i = 0; i < buttons.length; i++) {
            addClass(buttons[i], 'button_inactive');
            buttons[i].disabled = action;
        }
    } else {
        Effect.Fade('loading');
        for (var i = 0; i < buttons.length; i++) {
            removeClass(buttons[i], 'button_inactive');
            buttons[i].disabled = action;
        }
    }
}

/**
 * Perform programmatic submit of form, explicitly calling onsubmit before that
 *
 * @param {Object} el - element within form
 * @return void
 */
function submitForm(el) {
    if (!el || !el.form) {
        return;
    }
    if (typeof el.form.onsubmit === 'function') {
        el.form.onsubmit();
    }
    el.form.submit();
}

/* MODEL / FORM ELEMENT MANAGMENT */
/* ============================== */
/*
 * Manages form's elements or specific model/s submission
 */

/**
 * TRANSLATION section COPY field from the BASE MODEL to the translating model
 *
 * @params field - DOM field object
 */
function copyField(field) {
    var source_id = field.id.replace(/^copy([0-9]*)_/, 'bm$1_');
    var target_id = field.id.replace(/^copy([0-9]*)_/, '');

    if ($(source_id)) {
        var source = $(source_id);
    } else {
        return false;
    }

    if ($(target_id)) {
        var target = $(target_id);
    } else {
        return false;
    }

    if (source.tagName == 'INPUT' && source.type == 'text') {
        target.value = source.value;
    } else if (source.tagName == 'TEXTAREA' && typeof(CKEDITOR) != 'undefined') {
        //when copying CK editor content
        var source_content = CKEDITOR.instances[source_id].getData();
        CKEDITOR.instances[target_id].setData(source_content);
    } else if (source.tagName == 'TEXTAREA') {
        target.value = source.value;
    }

    return true;
}


/**
 * TRANSLATION section COPY ALL FIELDS from the BASE MODEL to the translating model,
 * by interactive button clickfor each button copy option
 *
 * @params field - DOM field object
 */
function copyAllFields(field) {

    var buttons = field.form.getElementsByTagName('button');

    if (!buttons) {
        return false;
    }

    for (var i = 0; i < buttons.length; i++) {
        var button = buttons[i];
        if (button.type == 'button' && button.id.match(/^copy([0-9]*)_/) && button.parentNode.style.display!='none') {
            button.click();
        }
    }

    return true;
}

/**
 * Cancel current action and submit form so that user is redirected to selected
 * (or default) after action
 *
 * @params element - DOM field object
 */
function cancelAction(element) {
    if (window.opener && window.opener !== window && window.name.match(/^pop/) || window.parent && window.parent !== window) {
        // popup/lightbox in iframe
        closePopupWindow();
        return;
    } else if (element.up('.lb_content') && lb && lb.active) {
        // lightbox in same window
        lb.deactivate();
        return;
    }
    preventResubmit(element.form);

    var input = document.createElement("input");
    input.setAttribute("type", "hidden");
    input.setAttribute("name", "cancel_action");
    input.setAttribute("value", "1");
    element.form.appendChild(input);
    element.form.submit();
}

/**
 * Function to manage check if the global variables process is finished
 */
function manageSaveGlobalsCheck() {
    if (env && env.periodicalSaveGlobalCheckInterval && env.valid_login && env.project_url && !env.project_url.match(/(auth|ajax|multi|generate|autocomplete_filter|=(filter|addquick)&)/)) {
        updateSaveGlobalsCheck();
        //*1000 because the constant is in seconds but the interval is in milliseconds
        remindersTimeOutId = setInterval("updateSaveGlobalsCheck()", env.periodicalSaveGlobalCheckInterval*1000);
    }
}

/**
 * Position absolute divs according to width of table with layouts.
 * It is used for layouts index and for stopwatch button.
 */
function positionAbsoluteDivs() {
    var divs = $$('.abs_div');
    if (divs.length) {
        var tbl = null;
        var siblings = divs[0].parentNode.children;
        for (var i = 0; i < siblings.length; i++) {
            if (siblings[i].tagName == 'TABLE') {
                tbl = siblings[i];
                break;
            }
        }
        if (tbl != null) {
            var tbl_pos = findPos(tbl);
            // element could be in lightbox: 1) in a div in the same window or 2) in an iframe
            var in_lightbox = lb && lb.active && tbl.up('#lightbox') || window !== window.parent && window.parent.lb && window.name == window.parent.lb.params.uniqid;
            for (var i = 0; i < divs.length; i++) {
                // this is used only for dynamically loaded layouts_index
                if (!divs[i].hasClassName('stopwatch_div')) {
                    var frm_pos = [0, 0];
                    if ($('form_container') != null) {
                        frm_pos = findPos($('form_container'));
                        // if there is a title, it will be hidden/removed so reduce its height
                        var page_title = $('form_container').previous('h1') || $('form_container').up('table', 0) && $('form_container').up('table', 0).previous('h1') || null;
                        if (in_lightbox && page_title != null && page_title.visible()) {
                            frm_pos[1] -= (page_title.offsetHeight + parseFloat(page_title.getStyle('marginBottom')) + parseFloat(page_title.getStyle('marginTop'))).toFixed(0);
                        }
                    }
                    divs[i].style.top = (frm_pos[1]) + 'px';
                }
                divs[i].style.left = (tbl.offsetWidth - divs[i].offsetWidth + tbl_pos[0] + 1) + 'px';
                divs[i].style.visibility = 'visible';
            }
        }
    }
}

/**
 * checks if the background work for global
 * vars calculation is finished
 * in Event.observe function (line 17)
 */
function updateSaveGlobalsCheck() {

    //interval is not initialized so exit the function
    if (!env.periodicalSaveGlobalCheckInterval) {
        return false;
    }

    var opt = {
        asynchronous: true,
        onSuccess: function(t) {
            var text = t.responseText;
            if (!text) {
                return false;
            }
            alert(text);
            env.periodicalSaveGlobalCheckInterval = 0;
        }
    };

    var url = env.base_url + '?checkSaveGlobals=1';
    new Ajax.Request(url, opt);
}

/**
 * Manage locked records. The function is called
 * in Event.observe function (line 14)
 */
function manageLockedRecords() {

    var pulsateLockInfo = function(t) {
        new Effect.Pulsate($('m_lockbar_box'));
    };

    if (manageLockedRecords.arguments && manageLockedRecords.arguments.length > 0 &&
    (manageLockedRecords.arguments[0] == 'continue_lock' || manageLockedRecords.arguments[0] == 'check_lock')) {

        //the ajax will check the record lock or will continue the lock
        //if the record is not modified or not locked by other user
        var opt = {
            asynchronous: true,
            onSuccess: function(t) {
                if (t.responseText.match(/locked_by_other_/)) {
                    //record is locked by other user so display a message and redirect in view mode
                    id = t.responseText.replace(/locked_by_other_/, '');
                    alert(i18n['messages']['locked_by_other']);
                    regex = new RegExp('(=|&)' + env.action_name, 'gi');
                    url = window.location.href.replace(regex, '$1view');
                    url += '&unlock=' + id;
                    redirect(url);
                } else if (t.responseText.match(/modified_/)) {
                    //record is modified by other user so display a message and redirect in view mode
                    id = t.responseText.replace(/modified_/, '');
                    alert(i18n['messages']['modified_by_other']);
                    regex = new RegExp('(=|&)' + env.action_name, 'gi');
                    url = window.location.href.replace(regex, '$1view');
                    url += '&unlock=' + id;
                    redirect(url);
                } else if (t.responseText.match(/expired_\d+/)) {
                    //record is expired so ask the user if he/she want to continue the lock
                    id = t.responseText.replace(/expired_(\d+)/, '$1');
                    if (!confirm(i18n['messages']['continue_lock'])) {
                        regex = new RegExp('(=|&)' + env.action_name, 'gi');
                        url = window.location.href.replace(regex, '$1view');
                        url += '&unlock=' + id;
                        redirect(url);
                    } else {
                        manageLockedRecords('continue_lock');
                    }
                } else {
                    if (t.responseText.match(/<!-- Locked Records  -->/)) {
                        //record will expire soon
                        $('m_lockbar').innerHTML = t.responseText.replace(/locked_expire/, '');
                    }
                }
                pulsateLockInfo;
            }
        };

        if (manageLockedRecords.arguments[0] == 'continue_lock') {
            new Ajax.Request(window.location.href + '&ajax_continue_lock=1', opt);
        } else if (manageLockedRecords.arguments[0] == 'check_lock') {
            new Ajax.Request(window.location.href + '&ajax_checklock=1', opt);
        }
        return;
    }

    if (env && env.locking_records && env.valid_login && env.project_url && !env.project_url.match(/(auth|ajax|multi|reports|autocomplete_filter|=(filter|addquick)&)/)) {
        //periodical updater will be initialized
        var url = env.project_url.replace(/&save_url=1/, '');
        url = (url.match(/\?/)) ? url + '&ajax_checklock=1' : url + '?ajax_checklock=1';

        var opt = {
            asynchronous: true,
            frequency: env.periodicalUpdaterInterval,
            onSuccess: function(t) {
                text = t.responseText;
                if (text.match(/locked_by_other_\d+/)) {
                    //record is locked by other user so display a message and redirect in view mode
                    id = text.replace(/locked_by_other_(\d+)/, '$1');
                    alert(i18n['messages']['locked_by_other']);
                    regex = new RegExp('(=|&)' + env.action_name, 'gi');
                    url = window.location.href.replace(regex, '$1view');
                    url = url.replace('ajax_checklock=1', '');
                    url += '&unlock=' + id;
                    redirect(url);
                } else if (text.match(/modified_\d+/)) {
                    //record is modified by other user so display a message and redirect in view mode
                    id = text.replace(/modified_(\d+)/, '$1');
                    alert(i18n['messages']['modified_by_other']);
                    regex = new RegExp('(=|&)' + env.action_name, 'gi');
                    url = window.location.href.replace(regex, '$1view');
                    url = url.replace('ajax_checklock=1', '');
                    url += '&unlock=' + id;
                    redirect(url);
               } else if (text.match(/expired_\d+/)) {
                   //record is expired so ask the user if he/she want to continue the lock
                    id = text.replace(/expired_(\d+)/, '$1');
                    if (!confirm(i18n['messages']['continue_lock'])) {
                        regex = new RegExp('(=|&)' + env.action_name, 'gi');
                        url = window.location.href.replace(regex, '$1view');
                        url = url.replace('ajax_checklock=1', '');
                        url += '&unlock=' + id;
                        redirect(url);
                    } else {
                        manageLockedRecords('continue_lock');
                    }
                } else if (text.match(/locked_expire/)) {
                    //record will expire soon
                    if (!manageLockedRecords.arguments || (manageLockedRecords.arguments && manageLockedRecords.arguments[0] != 'check_lock')) {
                        //user will be asked if he/she want to continue the lock before the lock expiration
                        if (confirm(i18n['messages']['lock_expire_soon'])) {
                            manageLockedRecords('continue_lock');
                        } else {
                            manageLockedRecords('check_lock');
                        }
                    }

                } else {
                    if (t.responseText.match(/<!-- Locked Records  -->/)) {
                        //just show the lockinhg records info
                        $('m_lockbar').innerHTML = text;
                    }
                }
                pulsateLockInfo;
            }
        };
        new Ajax.PeriodicalUpdater('', url, opt);
    }
}

/**
 * Redirect to login page if not valid_login
 *
 */
function checkAjaxResponse(result) {
    if (result.indexOf('id="loginButton"') > 0) {
        redirectToLogin();
        return false;
    } else if (typeof result === 'string' && (matches = result.match(/<form.*action="(.+?users=password)"/)) && !(env.project_url == matches[1].replace('&amp;', '&'))) {
        url = matches[1].replace('&amp;', '&');
        redirect(url);
        return false;
    } else if (result.indexOf('id="index_page_clear_flag"') > 0 || result.indexOf('m_header_logo') > 0) {
        // under normal conditions this should not happen
        var m = result.match(/<div class="message_container">([\s\S]*?)<\/div>/);
        if (m && m.length > 1 && m[1].stripTags().strip()) {
            alert(m[1].stripTags().strip());
        }
        Effect.Fade('loading');
        return false;
    } else {
        return true;
    }
}

/**
 * Manage reminders. The function is called
 * in Event.observe function
 */
var remindersTimeOutId = 0;
function manageReminders() {
    if (env && env.reminder_records && env.valid_login && env.project_url && env.periodicalReminderInterval > 0 && !env.project_url.match(/(auth|ajax|multi|generate|autocomplete_filter|=(filter|addquick)&)/)) {
        updateReminders();
        //*1000 because the constant is in seconds but the interval is in milliseconds
        remindersTimeOutId = setInterval("updateReminders()", env.periodicalReminderInterval*1000);
    }
}

/**
 * Update reminders. The function is called every 5 minutes
 *
 */
function updateReminders() {
    const opt = {
        method: 'get',
        onSuccess: function(t) {
            const data = t.responseText;
            if (!checkAjaxResponse(data)) {
                return;
            }

            //check if the reminder HTML (by the a comment in the reminder content) is loaded
            //prevent notices, warnings, traces to be loaded in the lightbox instead
            if (data.match(/<!-- Event Reminder -->/)) {
                //stop the reminders until there is a response from the user
                clearInterval(remindersTimeOutId);

                //show the reminder layer in a lightbox
                lb = new lightbox({
                    content: data,
                    title: i18n['labels']['event_remind'],
                    icon: 'remind.png',
                    closeHandler: 'manageReminders()'
                });
                lb.activate();
            }

        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    const url = env.base_url + '?launch=calendars&calendars=ajax_checkreminders';

    new Ajax.Request(url, opt);
}

/**
 * On load start checking the user activity
 *
 * #modern
 */
function manageActivities() {
    if (typeof env !== 'undefined' && env.periodicalActivityInterval > 0 && env.valid_login && env.project_url && !env.project_url.match(/(auth|ajax|multi|generate|autocomplete_filter|=(filter|addquick)&)/)) {
        scheduleActivityCheck();
    }
}

/**
 * Schedule an activity check after certain period of time
 *
 * #modern
 */
function scheduleActivityCheck() {
    const periodicalActivityIntervalInMilliseconds = env.periodicalActivityInterval * 1000;
    setTimeout(checkActivity, periodicalActivityIntervalInMilliseconds);
}

/**
 * Check user's activity, redirect to logout, if it has expired or ask the user
 * if he wants to stay active
 *
 * #modern
 */
function checkActivity() {
    // Prepare the activity check URL
    const activityCheckUrl = new URL(env.base_url, env.base_host);
    activityCheckUrl.searchParams.set(env.module_param, 'auth');
    activityCheckUrl.searchParams.set('auth', 'ajax_checkactivity');

    // Request an activity check
    fetch(activityCheckUrl, {headers: {'Accept': 'application/json'}}).
        then(response => response.json()).
        then(activity => {
            // If there's no activity status
            // or the status is "expired"
            if (!activity.hasOwnProperty('status')
                    || activity.status === 'expired') {
                // Logout the user
                redirectToLogout();
                return;
            }

            // If the status is "active"
            if (activity.status === 'active') {
                // Schedule next activity check
                scheduleActivityCheck();
                return;
            }

            // If the status is unknown or there's no time left activity expire
            // (i.e. prevent from continuing with the code for status "expiring")
            if (activity.status !== 'expiring'
                    || !activity.hasOwnProperty('minutesLeft')
                    || activity.minutesLeft <= 0) {
                // Logout the user
                redirectToLogout();
                return;
            }

            /*
             If the status is "expiring" and the activity has minutesLeft
             ask the user if he wants to stay active
             */
            // Calculate the client-side activity expiry time based on
            // the server-side time left until activity expire
            const now = new Date();
            const activityExpiryTime = new Date(now.getTime() + 1000 * 60 * activity.minutesLeft);

            // Prepare a message to tell the user when activity will expire
            // and ask him if he wants to stay active
            const msgBeforeActivityExpire = i18n.messages['alert_periodical_activity'].replace(
                /\[periodical_activity_interval\]/,
                activityExpiryTime.print('%Y-%m-%d %H:%M:00'));

            // If he doesn't wants to stay active
            if (!confirm(msgBeforeActivityExpire)) {
                // Logout the user
                redirectToLogout();
                return;
            }

            // Try extending the activity by refreshing last activity
            const refreshLastActivityUrl = new URL(env.base_url, env.base_host);
            refreshLastActivityUrl.searchParams.set(env.module_param, 'auth');
            refreshLastActivityUrl.searchParams.set('auth', 'ajax_refresh_last_activity');
            fetch(refreshLastActivityUrl, {headers: {'Accept': 'application/json'}}).
                then(response => response.json()).
                then(activity => {
                    // If the refresh is not OK
                    if (!activity.hasOwnProperty('status') || activity.status !== 'ok') {
                        // The user answered that he wants to stay active, but probably too late
                        // Log this situation into the console
                        // TODO: The user wanted to stay active but that failed,
                        //       so show him a message, before redirecting him to the login page
                        console.error(`Failed to refresh last activity! Status: ${activity.status}`);
                        // Logout the user
                        redirectToLogout();
                        return;
                    }

                    // If the refresh is successful (i.e. the activity is extended)
                    // schedule next activity check
                    scheduleActivityCheck();
                }).
                catch((error) => {
                    // Log the error
                    console.error(error);
                    // Logout the user
                    redirectToLogout();
                });
        }).
        catch((error) => {
            // Log the error
            console.error(error);
            // Logout the user
            redirectToLogout();
        });
}

/**
 * Toggle Action for documents' types in
 * the frontend's home page
 */
var action_expanded = false;
function indexToggleActionOptions(element) {
// var transition = 'slide';

    if (action_expanded) {
        return false;
    }

    //toggleActions(false);

    //slide up the previously opened containers
    var divs = $('available_options_container').getElementsByTagName('div');
    type = element.id.replace(/_action$/, '');
    Cookie.set('index_document_type_direction', type);

    destination = element.id.replace(/_action$/, '_options');
    var destination_box = $(destination);

    if (destination_box.style.display == 'none') {
        addClass(element.parentNode, 'selected');
        var hide_id = 0;
        for (var i = 0; i < divs.length; i++) {
            if (divs[i].id.match(/_options$/) && divs[i].style.display != 'none') {
                hide_id = divs[i].id;
                break;
            }
        }
        if (hide_id) {
            removeClass($(hide_id.replace(/_options$/, '_action')).parentNode, 'selected');
            // Effect.toggle(hide_id, transition);
            var hide_id_box = $(hide_id);
            hide_id_box.style.display = 'none';
            toggleFields(hide_id, false);
            // window.setTimeout('Effect.toggle(\'' + destination + '\', \'' + transition + '\')', 1000);
            toggleFields(destination, true);
            destination_box.style.display = 'block';
            //window.setTimeout('toggleActions(\'' + true + '\')', 2300);
        } else {
            // Effect.toggle(destination, transition);
            toggleFields(destination, true);
            destination_box.style.display = 'block';
            //window.setTimeout('toggleActions(\'' + true + '\')', 2300);
        }
    } else {
        removeClass(element.parentNode, 'selected');
        // Effect.toggle(destination, transition);
        toggleFields(destination, false);
        destination_box.style.display = 'none';
        //window.setTimeout('toggleActions(\'' + true + '\')', 2300);
    }
}

/**
 * Dynamic effect for action options for each module(including models)
 *
 * @param element - form element to toggle
 * @param toggle_force_open - forces the panel to open
 */
function toggleActionOptions(element) {
// var transition = 'slide';

    if (action_expanded) {
        return false;
    }

    // toggleActions(false);

    //slide up the previously opened containers
    var divs = $('available_options_container').getElementsByTagName('div');

    if (element.type == 'button') {
        destination = typeof(env.action_name) != 'undefined' && env.action_name == 'filter' ? 'filter_options' : 'search_options';
    } else {
        destination = element.id.replace(/_action$/, '_options');
    }
    var destination_box = $(destination);

    var parentElement = element.parentNode;
    var link_element = null;
    if (parentElement.tagName.match(/^td$/i)) {
        var links = parentElement.getElementsByTagName('a');
        if (links[0]) {
            link_element = links[0];
        }
    }

    //forces the panel to open
    var toggle_force_open = (toggleActionOptions.arguments[1]) ? toggleActionOptions.arguments[1] : false;

    if (destination_box.style.display == 'none' || element.type == 'button' || toggle_force_open) {
        if (element.type != 'button') {
            if (link_element) {
                addClass(link_element, 'current');
            } else {
                addClass(element, 'current');
            }
        }
        var hide_id = 0;
        for (var i = 0; i < divs.length; i++) {
            if (divs[i].id.match(/_options$/) && divs[i].style.display != 'none' && element.type != 'button') {
                hide_id = divs[i].id;
                break;
            }
        }
        if (hide_id) {
            var hideElementParent = $(hide_id.replace(/_options$/, '_action')).parentNode;
            hide_links = hideElementParent.getElementsByTagName('a');
            if (hide_links[0]) {
                removeClass(hide_links[0], 'current');
            }

            // Effect.toggle(hide_id, transition);
            var hide_id_box = $(hide_id);
            hide_id_box.style.display = 'none';
            toggleFields(hide_id, false);
            // window.setTimeout('Effect.toggle(\'' + destination + '\', \'' + transition + '\')', 1000);
            toggleFields(destination, true);
            destination_box.style.display = 'block';
            // window.setTimeout('toggleActions(\'' + true + '\')', 2300);
        } else {
            // Effect.toggle(destination, transition);
            toggleFields(destination, true);
            destination_box.style.display = 'block';
            // window.setTimeout('toggleActions(\'' + true + '\')', 2300);
        }
    } else {
        if (link_element) {
            removeClass(link_element, 'current');
        } else {
            removeClass(element, 'current');
        }
        // Effect.toggle(destination, transition);
        toggleFields(destination, false);
        destination_box.style.display = 'none';
        // window.setTimeout('toggleActions(\'' + true + '\')', 2300);
    }

    // if the clicked tab is with combined actions
    // the options for the unchecked action will be disabled
    if ((element.id == 'adds_action' || element.id == 'transformations_action') && destination_box.style.display != 'none') {
        var combined_action_name = element.id.replace(/_action$/, '');
        var combined_action_box = combined_action_name + '_options';
        var options_row = combined_action_name + '_suboptions_row';
        var inside_inputs = $(options_row).getElementsByTagName('input');
        for (var j = 0; j < inside_inputs.length; j++) {
            if (inside_inputs[j].type == 'radio') {
                var options_box_id = combined_action_name + '_' + inside_inputs[j].id + '_box';
                var options_box = $(options_box_id);
                if (inside_inputs[j].checked == true) {
                    options_box.style.display = 'block';
                    toggleFields(options_box, true);
                } else {
                    options_box.style.display = 'none';
                    toggleFields(options_box, false);
                }
            }
        }
    }
}

/**
 * Dynamically change the tabs combining some action options
 *
 * @param element - form element to toggle
 */
function toggleCombinedActionOptions(element) {
    var id = element.id;
    //list the operations
    var operations = new Array(), base_id = '';
    if (id == 'transform' || id == 'multitransform' || id == 'clone') {
        operations = ['transform', 'multitransform', 'clone'];
        base_id = 'transformations_';
    } else if (id == 'add' || id == 'multiadd') {
        operations = ['add', 'multiadd'];
        base_id = 'adds_';
    } else if (id.match(/^(tasks|documents|events|projects|minitasks)$/)) {
        operations = ['tasks', 'documents', 'events', 'projects', 'minitasks'];
        base_id = 'create_';
    }
    for (var j = 0; j < operations.length; j++) {
        var box_id = base_id + operations[j] + '_box';
        var box = $(box_id);
        if (box) {
            if (element.id == operations[j]) {
                box.style.display = 'block';
                toggleFields(box, true);
            } else {
                box.style.display = 'none';
                toggleFields(box, false);
            }
        }
    }
}

/**
 * Dynamic effect for action options for each module(including models)
 *
 * @param mode - bolean set on/off disable mode for the ActionTag
 */
function toggleActions(mode) {
    action_expanded = !mode;

    var actions = $('action_tabs').getElementsByTagName('a');

    for (var i = 0; i < actions.length; i++) {
        var tab = actions[i];

        tab.disabled = !mode;
        if (mode) {
            removeClass(tab, 'disabled');
        } else {
            addClass(tab, 'disabled');
        }
    }

    return true;
}

/**
 * Dynamic effect for AFTER ACTION options for each module(including models)
 */
function toggleAfterActionOptions(element) {
    var transition = 'appear';
    var use_effect = false;
    var destination = element.id.replace(/^after_action_/, 'after_options_');
    var hide_id = 0;

    //slide up the previously opened containers
    var divs = $('available_after_options_container').getElementsByTagName('fieldset');
    for (var i = 0; i < divs.length; i++) {
        var after_action_name = divs[i].id.replace(/^after_options_/, '');
        if (!$('after_action_' + after_action_name).checked && divs[i].id.match(/^after_options_/) && divs[i].style.display != 'none') {
            hide_id = divs[i].id;
            break;
        }
    }

    if ($(destination) && $(destination).style.display == 'none') {
        if (hide_id) {
            if (use_effect) {
                toggleAfterActions(false);
                Effect.toggle(hide_id, transition);
                toggleFields(hide_id, false);
                window.setTimeout('Effect.toggle(\'' + destination + '\', \'' + transition + '\')', 1000);
                toggleFields(destination, true);
                window.setTimeout('toggleAfterActions(\'' + true + '\')', 2000);
            } else {
                $(hide_id).style.display = 'none';
                toggleFields(hide_id, false);
                toggleFields(destination, true);
                $(destination).style.display = 'block';
            }
        } else {
            toggleFields(destination, true);
            if (use_effect) {
                toggleAfterActions(false);
                Effect.toggle(destination, transition);
                window.setTimeout('toggleAfterActions(\'' + true + '\')', 2000);
            } else {
                $(destination).style.display = 'block';
            }
        }
    } else if (hide_id) {
        toggleFields(hide_id, false);
        if (use_effect) {
            toggleAfterActions(false);
            Effect.toggle(hide_id, transition);
            window.setTimeout('toggleAfterActions(\'' + true + '\')', 2000);
        } else {
            $(hide_id).style.display = 'none';
        }
    }
}

/**
 * Dynamic effect for AFTER ACTION for each module(including models)
 *
 * @param mode - bolean set on/off disable mode for the AfterActionTag
 */
function toggleAfterActions(mode) {
    var actions = $('after_actions_definitions').getElementsByTagName('input');

    for (var i = 0; i < actions.length; i++) {
        actions[i].disabled = !mode;
    }

    return true;
}

/**
 * Enables/disables all input and select children of specified parent element
 * unless they have a 'disabled' class name
 *
 * @param {Object|String} parent - parent element or its id
 * @param {bool} mode - bolean set on/off disable mode for the FIELDs
 */
function toggleFields(parent, mode) {
    $(parent).select('input, select').filter(function(el) { return !$(el).hasClassName('disabled'); }).invoke(mode ? 'enable' : 'disable');
    return;
}

/**
 * Toggles panels getting the elements by class name
 *
 * @param element - the switch element that launches the toggle
 * @param element - list of defined panels to toggle
 */
function togglePanels(element, panels) {

    //get elements by class name
    var container = element;
    while(container.tagName != 'TABLE') {
        container = container.parentNode;
    }

    panels = panels.split(',');
    for (var i = 0; i < panels.length; i++) {
        var panel = trim(panels[i]);
        if (panel == element.id) {
            //enable this panel
            var items = Element.select(container, '.' + panel + '_panel');
            for (var j = 0; j < items.length; j++) {
                items[j].style.display = '';
            }
            addClass($(panel).parentNode, 'selected');
            if ($('selected_panel')) {
                $('selected_panel').value=panel;
            }
        } else {
            //disable all the others
            var items = Element.select(container, '.' + panel + '_panel');
            for (var j = 0; j < items.length; j++) {
                items[j].style.display = 'none';
            }
            removeClass($(panel).parentNode, 'selected');
        }
    }
}

/**
 * Remembers all clicked checkboxes
 *
 * @param form - the object form in the DOM where are the checkboxes
 * @param module - current module
 * @param controller - current controller
 */
function sendIds(params) {

    Effect.Center('loading');
    Effect.Appear('loading');

    var the_form = params.the_form;

    var items_elements_name = 'items';
    if (typeof params.items_elements_name !== 'undefined') {
        items_elements_name = params.items_elements_name;
    }

    var update_totals = true;
    if (typeof params.update_totals !== 'undefined') {
        update_totals = params.update_totals;
    }

    var index = 1;
    if (params.button_id) {
        index = params.button_id.match(/[0-9]+$/);
    } else if (params.the_element) {
        var button_name = params.module + '_' + params.controller + '_' + params.action + '_checkall[]';
        if (params.the_element.form) {
            var button = params.the_element.form.elements[button_name];
            if (button) {
                params.button_id = button.id;
                index = button.id.match(/[0-9]+$/);
            }
        }
    } else {
        params.button_id = params.module + '_' + params.controller + '_' + params.action + '_checkall_1';
    }
    if (!params.total && $$('span.' + (params.session_param || '') + '_total').length) {
        params.total = parseInt($$('span.' + (params.session_param || '') + '_total')[0].innerHTML) || 0;
    }

    if (params.controller == '') {
        param = params.module;
    } else {
        param = 'controller=' + params.controller + '&'+ params.controller;
    }
    if (params.action == '') {
        action_str = '';
    } else {
        action_str = '&action=' + params.action;
    }

    var url;
    if (params.select_what == 'none' || (params.the_element && params.the_element.checked === false)) {
        url = env.base_url + '?' + env.module_param + '='+ params.module + '&' + param + '=insertids&delids=1' + action_str;
    } else {
        url = env.base_url + '?' + env.module_param + '='+ params.module + '&' + param + '=insertids' + action_str;
    }

    var ids_arr = [];
    if (the_form) {
        if (params.select_what == 'page' || params.select_what.match(/^first_(\d+)$/)) {
            //var items = the_form.elements['items[]'];
            //use css selector
            var items = the_form.select('input[name^='+items_elements_name+']');
            var limit = parseInt(params.select_what.replace(/^first_(\d+)$/, '$1'));
            if (!items.length) {
                items = [items];
            }
            for (var i = 0; i < items.length; i++) {
                if (limit && limit <= i ) {
                    break;
                }
                if (items[i].checked) {
                    ids_arr.push(items[i].value);
                }
            }
        }
        if (params.select_what == 'all') {
            ids_arr.push('all');
        }
    } else {
        ids_arr.push(params.the_element.value);
    }
    var opt = {
        method: 'post',
        parameters: {'ids[]': ids_arr,
                     'session_param': params.session_param,
                     'total': params.total},
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            if (update_totals) {
                if ($('selectedItemsCount_' + index)) {
                    $('selectedItemsCount_' + index).innerHTML = result;
                }
                setCheckAllBox(params);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(url, opt);
}

/**
 * REMOVES all clicked checkboxes
 *
 * @param form - the object form in the DOM where are the checkboxes
 * @param module - current module
 * @param controller - current controller
 */
function removeIds(form, module, controller) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $('countIds').innerHTML = result;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var param;
    if (controller == '') {
        param = module;
    } else {
        param = 'controller=' + controller + '&'+ controller;
    }
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + param + '=insertids&delids=1';

    new Ajax.Request(url, opt);
}

/**
 * Changes tabs of the customers kind
 *
 * @param form - the object form in the DOM
 * @param is_company - whether customer is person or legal entity (0 or 1)
 * @param div_id - id of element where response should be loaded
 */
function addCustomer(form, is_company, div_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            if (env.action_name == 'addquick') {
                scalePopup();
            }
            addClass($('company_'+is_company).parentNode, 'selected');
            removeClass($('company_'+((is_company+1)%2)).parentNode, 'selected');
            Effect.Pulsate('tab_' + ((is_company) ? 'company' : 'person'));

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=customers&customers=addnew&is_company=' + is_company;
    if (addCustomer.arguments.length > 3 && addCustomer.arguments[3] == 'type_changed') {
        url += '&type_changed=1';
    }

    new Ajax.Request(url, opt);
}

/**
 * Selects customer type in customers add mode and switches tabs for company and person
 *
 * @param element - the dropdown itself
 */
function selectCustomerType(element) {
    var class_names = element.options[element.selectedIndex].className;
    var selected_type = class_names.replace(/^(.*) group_[0-9]*$/, '$1');
    var default_group = class_names.replace(/^(.*) group_([0-9]*)$/, '$2');

    //display/hide the tabs for changing customer kind
    if (selected_type == 'company_person') {
        $('tab_company').style.display = '';
        $('tab_person').style.display = '';
    } else if (selected_type == 'person_company') {
        $('tab_company').style.display = '';
        $('tab_person').style.display = '';
    } else if (selected_type == 'company') {
        $('tab_company').style.display = '';
        $('tab_person').style.display = 'none';
    } else if (selected_type == 'person') {
        $('tab_person').style.display = '';
        $('tab_company').style.display = 'none';
    }

    //check selected type
    if (selected_type == 'company') {
        addClass($('company_1').parentNode, 'selected');
        removeClass($('company_0').parentNode, 'selected');
    } else if (selected_type == 'person') {
        addClass($('company_0').parentNode, 'selected');
        removeClass($('company_1').parentNode, 'selected');
    }

    //reload form
    if (selected_type == 'company' || selected_type == 'company_person') {
        addCustomer(element.form, 1, 'customer_data', 'type_changed');
    } else if (selected_type == 'person' || selected_type == 'person_company') {
        addCustomer(element.form, 0, 'customer_data', 'type_changed');
    }

    // select the default group for the selected customer type
    if (default_group && $('group')) {
        var group_dropdown = $('group');
        for (var i = 0; i < group_dropdown.options.length; i++) {
            if (group_dropdown.options[i].value == default_group) {
                group_dropdown.options[i].selected = true;
                if (group_dropdown.attributes.onchange) {
                    group_dropdown.onchange();
                }
                break;
            }
        }
    }

    return true;
}

/**
 * Changes model types options in Add/Edit action of Tag according to model selection.
 *
 * @param {Object} element - model dropdown
 * @param {String} div_id - id of object from the DOM where response should be loaded
 */
function selectModelForTag(element, div_id) {

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }

            var div = $(div_id);
            if (div) {
                div.innerHTML = result;
                var row = div.up('tr');
                if (row) {
                    if (element.selectedIndex > - 1 && element[element.selectedIndex].hasClassName('no_model_types')) {
                        row.addClassName('hidden');
                    } else {
                        row.removeClassName('hidden');
                    }
                }
                div.select('script').each(ajaxLoadJS);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tags&tags=addedit&use_ajax=1&model=' + (element.selectedIndex > - 1 ? element[element.selectedIndex].value : '');

    new Ajax.Request(url, opt);
}

/**
 * Changes filter options in Add/Edit finance analysis item according to selected factory for elements.
 *
 * @param form - the object form in the DOM
 * @param factory - selection from factory dropdown
 * @param div_id - object from the DOM where response should be loaded
 */
function selectFiltersForFinanceAnalysisItem(form, factory, div_id) {
    if (factory) {
        $(div_id).parentNode.style.display = '';
    } else {
        $(div_id).parentNode.style.display = 'none';
    }

    //set hidden module_name value to <module>_<controller> of new factory
    $('module_name').value = factory.match(/_/) ? factory : ((factory != '') ? factory + '_' + factory : '');

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=analysis_items&analysis_items=changefactory&use_ajax=1&factory=' + factory;

    new Ajax.Request(url, opt);
}

/**
 * Gets and displays Action Options
 *
 * @param tag_id - get the name of the tag
 * @param module - the name of the module
 * @param controller - name of the controller
 * @param action - what action
 * @param model_id - the id of the model
 * @param {Object} params - other parameters (including flag used for clearing the search filters)
 */
function getActionOptions(tag_id, module, controller, action, model_id, params) {

    //check if the ajax is already loaded
    if ($(tag_id).hasClassName('ajax_loaded') && !params.clear_flag) {
        return;
    }

    if (action == 'filters_ajax_load') {
        var filter_name = $$('#' + tag_id + ' #filter_name');
        filter_name = filter_name.length ? filter_name[0].value : '';
        if (!filter_name) {
            return;
        }
        action += '&' + action + '=' + filter_name;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        //asynchronous: false,
        method: 'get',
        parameters: params,
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(tag_id).innerHTML = result;
            if (!params.real_module) {
                addClass($(tag_id),'ajax_loaded');
            }
            var x = $(tag_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }
            // update columns in add/edit of dashlet
            if (typeof additional_columns != 'undefined' &&
            env.module_name == 'dashlets' && (env.action_name == 'add' || env.action_name == 'edit')) {
                updateDashletColumns(additional_columns);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '='+ module + '&' +
              (controller == '' ? module : 'controller=' + controller + '&'+ controller) + '=getoptions&getoptions=' +
              action +
              (model_id ? '&model_id=' + model_id : '') +
              (params.clear_flag ? '&clear_flag=' + params.clear_flag : '');

    // search for 'session_param' field only within container of current action
    if ($$('#' + tag_id.replace(/^td_/, '') + ' #session_param').length) {
        //session param for customers trademarks that are selected from nomenclatures filter popup window
        url += '&session_param=' + $$('#' + tag_id.replace(/^td_/, '') + ' #session_param')[0].value;
    }
    //alert(url);

    new Ajax.Request(url, opt);
}

/**
 * Save, delete and load user-defined search filters
 *
 * @param {Object} form - the form where the filters are
 * @param {String} action - what action to perform
 */
function filterActions(form, action, filterNameId) {
    Effect.Center('loading');
    Effect.Appear('loading');

    if (action.match(/load/)) {
        $('filters_action').value = 'loadfilter';
        $('filters_action').disabled = false;
        $('filter_name').value = $(filterNameId).value;
        $('filter_name').onchange();
        form.submit();
        return;
    }
    if (action.match(/delete/)) {
        $('filters_action').value = 'delfilter';
        $('filters_action').disabled = false;
    }
    if (action.match(/save/)) {
        $('filters_action').value = 'savefilter';
        $('filters_action').disabled = false;
    }

    var get_params = Form.serialize(form) + '&use_ajax=1';
    var inner_search = 0;
    if (['dashlets', 'filters', 'analysis_items'].indexOf(form.getAttribute('name')) > -1 && $('module_name')) {
        inner_search = 1;
        var parts = $('module_name').value.split('_');
        get_params += '&' + env.module_param + '=' + parts.shift() + '&controller=' + parts.join('_') + '&action=search';
    }

    var opt = {
        method: 'get',
        parameters: get_params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            if(typeof filterNameId == 'undefined') {
                filterNameId = 'filter_name';
            }
            var result = t.responseText;
            eval(result);
            if (typeof(result) == 'object' && action.match(/save|delete/)) {
                var td1 = $('save_filter_as').parentNode;
                var td2 = $(filterNameId).parentNode;
                td1.removeChild($('save_filter_as'));
                // remove from actions menu if present as a tab
                if (action.match(/delete/) || action.match(/save/) && !$(form).down('#save_as_action').checked) {
                    var menu_item = action.match(/delete/) ? $(filterNameId).value : selected_filter;
                    menu_item = $('search' + menu_item + '_action');
                    if (menu_item && menu_item.up('li')) {
                        menu_item = menu_item.up('li');
                        if (menu_item.parentNode && menu_item.parentNode.children.length == 1) {
                            menu_item = $(menu_item.up('div'));
                            if (menu_item.next() && menu_item.next().tagName == 'BR') {
                                menu_item.next().remove();
                            }
                        }
                        $(menu_item).remove();
                    }
                }
                td2.removeChild($(filterNameId));
                var params = {type: 'dropdown',
                              custom_id: filterNameId,
                              name: 'filter_name',
                              index: '',
                              first_empty: true,
                              options: result,
                              value: selected_filter};
                createField(params, td2);
                params = {type: 'combobox',
                          name: 'save_filter_as',
                          index: '',
                          first_empty: true,
                          options: result,
                          value: selected_filter};
                createField(params, td1);

                // reload page
                if (action.match(/save/) && selected_filter && !inner_search && $(form).down('#save_as_action').checked) {
                    filterActions(form, 'loadfilter', filterNameId);
                }
            } else {
                alert(result);
            }
            $('filters_action').value = '';
            $('filters_action').disabled = false;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url;

    new Ajax.Request(url, opt);
}

/**
 * displays/toggles GROUPs
 *
 * @param element - element object
 */
function toggleGroups(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var target = (element.checked) ? 'parents' : 'children';
    var prefix = element.name.replace('[]','') + '_';

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);
            if (result) {
                for (var i = 0; i < result.length; i++) {
                    if (result[i] != element.id) {
                        $(prefix + result[i]).checked = (target == 'parents') ? true : false;
                    }
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=groups&groups=ajax&ajax=' + target + '&id=' + element.value;
    new Ajax.Request(url, opt);
}

/**
 * displays/toggles DEPARTMENTs
 *
 * @param element - element object
 */
function toggleDepartments(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var target = (element.checked) ? 'parents' : 'children';
    var prefix = element.name.replace('[]','') + '_';

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);
            if (result) {
                for (var i = 0; i < result.length; i++) {
                    if (result[i] != element.id) {
                        $(prefix + result[i]).checked = (target == 'parents') ? true : false;
                    }
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=departments&departments=ajax&ajax=' + target + '&id=' + element.value;
    new Ajax.Request(url, opt);
}

/**
 * displays/toggles CATEGORIES
 *
 * @param element - element object
 */
function toggleCategories(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var target = (element.checked) ? 'parents' : 'children';
    var opt = {
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);
            if (result) {
                for (var i = 0; i < result.length; i++) {
                    if (result[i] != element.id) {
                        $('category_' + result[i]).checked = (target == 'parents') ? true : false;
                    }
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=nomenclatures&controller=categories&categories=ajax&ajax=' + target + '&id=' + element.value;

    new Ajax.Request(url, opt);
}

/**
 * validates document's STATUS change
 *
 * @param element - element object
 */
function validateStatusChange(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    var current_status = $$('#' + container_id + ' .current_status_base')[0].value;
    var permission_unlock = $$('#' + container_id + ' .statuses_unlock')[0].value;
    var flag_status_error = false;

    var changed_status = element.value;
    if (changed_status != current_status) {
        if (current_status == 'closed' || current_status == 'finished') {
            flag_status_error = true;
        } else if (current_status == 'locked' && changed_status == 'opened') {
            if (! permission_unlock) {
                flag_status_error = true;
            }
        }
    }

    if (flag_status_error) {
        var status_id = 'status_' + current_status;
        $$('#' + container_id + ' .' + status_id)[0].checked = true;
        return false;
    } else {
        // disable any status radios, that should be disabled
        var disabled_statuses = $$('#' + container_id + ' input[type="radio"].status.disabled');
        for (var i = 0; i < disabled_statuses.length; i++) {
            disabled_statuses[i].disabled = true;
            removeClass(disabled_statuses[i], 'dimmed');
        }
        $$('#' + container_id + ' .current_selected_status')[0].value = '';
        status_requires_comment_id = 'requires_comment_' + changed_status;
        var status_requires_comment = $$('#' + container_id + ' .' + status_requires_comment_id)[0].value;
        if (status_requires_comment == 'requires_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .requires_comment')[0].value = '1';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else if (status_requires_comment == 'optional_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        }
        return true;
    }
}

/**
 * validates SUBSTATUS change
 *
 * @param element - element object
 */
function validateSubstatusChange(element, module) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    var flag_substatus_error = false;

    var changed_substatus = element.value;
    var substatus_info = changed_substatus.split('_');
    var current_selected_status = substatus_info[0];
    var current_selected_status_id = 'status_' + current_selected_status;
    var current_selected_status_element = $$('#' + container_id + ' .' + current_selected_status_id)[0];
    current_selected_status_element.checked = true;

    if (module == 'documents' || module == 'finance' || module == 'contracts') {
        flag_substatus_error = ! validateStatusChange(current_selected_status_element);
    } else if (module == 'tasks') {
        flag_substatus_error = ! validateTaskStatusChange(current_selected_status_element);
    }

    if (flag_substatus_error) {
        var previosly_selected_status = $$('#' + container_id + ' .current_selected_status')[0].value;
        element.checked = false;
        if (previosly_selected_status && $$('#' + container_id + ' .' + previosly_selected_status).length) {
            $$('#' + container_id + ' .' + previosly_selected_status)[0].checked = true;
        }
        return false;
    } else {
        // if current status radio is disabled, enable it and
        // disable any other status radios, that should be disabled
        var disabled_statuses = $$('#' + container_id + ' input[type="radio"].status.disabled');
        for (var i = 0; i < disabled_statuses.length; i++) {
            if (disabled_statuses[i].id == current_selected_status_element.id) {
                disabled_statuses[i].disabled = false;
                addClass(disabled_statuses[i], 'dimmed');
            } else {
                disabled_statuses[i].disabled = true;
                removeClass(disabled_statuses[i], 'dimmed');
            }
        }
        $$('#' + container_id + ' .current_selected_status')[0].value = element.className;
        status_requires_comment_id = 'requires_comment_' + current_selected_status;
        substatus_requires_comment_id = 'requires_comment_' + changed_substatus;
        var substatus_requires_comment = $$('#' + container_id + ' .' + substatus_requires_comment_id)[0].value;
        if (substatus_requires_comment == 'requires_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .requires_comment')[0].value = '1';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else if (substatus_requires_comment == 'optional_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        }
        return true;
    }
}

/**
 * validates task's STATUS change
 *
 * @param element - element object
 */
function validateTaskStatusChange(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    var current_status = $$('#' + container_id + ' .current_status_base')[0].value;
    var permission_unlock = $$('#' + container_id + ' .statuses_unlock')[0].value;
    var flag_status_error = false;

    var changed_status = element.value;
    if (changed_status != current_status) {
        if (current_status == 'progress' && changed_status == 'planning') {
            flag_status_error = true;
        } else if (current_status == 'finished' && changed_status == 'planning') {
            flag_status_error = true;
        } else if (current_status == 'finished' && changed_status == 'progress') {
            if (! permission_unlock) {
                flag_status_error = true;
            }
        }
    }

    if (flag_status_error) {
        var status_id = 'status_' + current_status;
        $$('#' + container_id + ' .' + status_id)[0].checked = true;
        return false;
    } else {
        $$('#' + container_id + ' .current_selected_status')[0].value = '';
        status_requires_comment_id = 'requires_comment_' + changed_status;
        var status_requires_comment = $$('#' + container_id + ' .' + status_requires_comment_id)[0].value;
        if (status_requires_comment == 'requires_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .requires_comment')[0].value = '1';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else if (status_requires_comment == 'optional_comment') {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'visible';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        } else {
            $$('#' + container_id + ' .available_comment_table')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .required_comment')[0].style.visibility = 'hidden';
            $$('#' + container_id + ' .requires_comment')[0].value = '0';
            $$('#' + container_id + ' .comment')[0].value = '';
        }
        return true;
    }
}

/**
 * Shows/hides comment box for multiple status change
 *
 * @param element - element object - status dropdown
 */
function showHideMultistatusCommentField(element) {
    var status_requires_comment = element.options[element.selectedIndex].className;

    if (status_requires_comment == 'requires_comment') {
        $('multistatus_available_comment_table').style.display = 'inline';
        $('multistatus_required_comment').style.visibility = 'visible';
        $('multistatus_requires_comment').value = '1';
        $('multistatus_comment').value = '';
    } else if (status_requires_comment == 'optional_comment') {
        $('multistatus_available_comment_table').style.display = 'inline';
        $('multistatus_required_comment').style.visibility = 'hidden';
        $('multistatus_requires_comment').value = '0';
        $('multistatus_comment').value = '';
    } else {
        $('multistatus_available_comment_table').style.display = 'none';
        $('multistatus_required_comment').style.visibility = 'hidden';
        $('multistatus_requires_comment').value = '0';
        $('multistatus_comment').value = '';
    }
    return true;
}

/**
 * Check if comment is required during a multiple status change
 *
 * @params element - DOM field object
 */
function checkMultistatusRequiredComment(element) {
    var requires_comment = $('multistatus_requires_comment').value;
    if (requires_comment == '1') {
        var comment = $('multistatus_comment');
        if (! comment.value) {
            alert((i18n.messages['error_status_requires_comment']));
            return false;
        } else {
            return true;
        }
    } else {
        return true;
    }
}

/**
 * Check if comment is required during a status change
 *
 * @params element - DOM field object
 */
function checkRequiredComment(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    var requires_comment = $$('#' + container_id + ' .requires_comment')[0].value;
    if (requires_comment == '1') {
        var comment = $$('#' + container_id + ' .comment')[0];
        if (! comment.value) {
            alert((i18n.messages['error_status_requires_comment']));
            return false;
        } else {
            return true;
        }
    } else {
        return true;
    }
}

/**
 * displays/toggles STATUSes
 *
 * @param element - element object
 */
function toggleStatuses(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;
    var radio_buttons_statuses = $$('#' + container_id + ' .' + element.name);

    for (var i = 0; i < radio_buttons_statuses.length; i++) {
        if (radio_buttons_statuses[i].id != element.id) {
            // className serves as an identifier within current container
            var className = radio_buttons_statuses[i].className.split(' ');
            for (var j = 0; j < className.length; j++) {
                if (className[j] && className[j].match(/^status_\w+/)) {
                    className = className[j];
                    break;
                }
            }
            //check if there are substatuses
            substatuses_container_id = 'sub' + className;
            if ($$('#' + container_id + ' .' + substatuses_container_id)) {
                var radio_buttons_substatuses = $$('#' + container_id + ' .' + substatuses_container_id + ' input[type=radio]');
                for (var j = 0; j < radio_buttons_substatuses.length; j++) {
                    radio_buttons_substatuses[j].checked = false;
                }
            }
        }
    }
}

/**
 * validates project's STATUS change
 *
 * @param element - element object
 */
function validateProjectStatusChange(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    var current_status = $$('#' + container_id + ' .current_status_base')[0].value;
    var flag_status_error = false;

    var changed_status = element.value;
    if (changed_status != current_status) {
        if (current_status == 'planning') {
            flag_status_error = false;
        } else if (current_status == 'progress' && changed_status == 'planning') {
            flag_status_error = true;
        } else if (current_status == 'control' && (changed_status == 'planning' || changed_status == 'progress')) {
            flag_status_error = true;
        } else if (current_status == 'finished' && (changed_status == 'planning' || changed_status == 'progress' || changed_status == 'control')) {
            flag_status_error = true;
        }
    }

    //var substatuses_panel = $$('#' + container_id + ' .substatus_finished')[0];

    if (flag_status_error) {
        var status_id = 'status_' + current_status;
        $$('#' + container_id + ' .' + status_id)[0].checked = true;
        if (current_status != 'finished') {
            $$('#' + container_id + ' .substatus_1')[0].checked = false;
            $$('#' + container_id + ' .substatus_0')[0].checked = false;
        }
        return false;
    } else {
        if (changed_status == 'finished') {
            var substatus_value = $$('#' + container_id + ' .checked_finished_substatus')[0].value;
            if (substatus_value !== '1' && substatus_value !== '0') {
                $$('#' + container_id + ' .substatus_1')[0].checked = true;
                $$('#' + container_id + ' .checked_finished_substatus')[0].value = 1;
            } else {
                $$('#' + container_id + ' .substatus_' + substatus_value)[0].checked = true;
            }
        } else {
            $$('#' + container_id + ' .substatus_1')[0].checked = false;
            $$('#' + container_id + ' .substatus_0')[0].checked = false;
        }
        return true;
    }
}

/**
 * validates Projects' SUBSTATUS change
 *
 * @param element - element object
 */
function validateProjectsSubstatusChange(element) {
    var container_id = $('setstatus_form') != null ? 'setstatus_form' : element.form.parentNode.parentNode.id;

    $$('#' + container_id + ' .status_finished')[0].checked = true;
    $$('#' + container_id + ' .checked_finished_substatus')[0].value = element.value;
}

/**
 * validates email campaign's STATUS change
 *
 * @param element - element object
 */
function validateEmailsCampaignStatusChange(element) {
    var current_status = $('current_status_base').value;
    var flag_status_error = false;

    var changed_status = element.value;
    if (changed_status != current_status) {
        if (changed_status == 'partial' || changed_status == 'sent' || current_status == 'sent' || current_status == 'cancelled') {
            flag_status_error = true;
        } else if (current_status == 'partial' && changed_status != 'cancelled') {
            flag_status_error = true;
        }
    }

    if (flag_status_error) {
        var status_id = 'status_' + current_status;
        $(status_id).checked = true;
        return false;
    } else {
        return true;
    }
}

/* FUCTIONS regarding ARTICLES and  *
 *   nZoom's additional variables   */
/* ================================ */
/*
 * Manages form's elements or specific model/s submission
 */

/**
 * AJAX functionality that saves the data from FRANKY
 *
 * @param form - current form object
 * @param module - current MODULE
 * @param config_id - the id of the CONFIGURATOR
 * @param model_id - the id of the current MODEL - for Franky, always 0 when saving configurations for regular configurator
 * @param div_id - the id of the DIV where the FRANKY is
 * @param config_num - the number of the CONFIGURATOR
 * @param clear_hidden_fields_for_files - hidden fields to be removed after ajax is completed
 */
function saveFranky(form, module, config_id, model_id, div_id, config_num, clear_hidden_fields_for_files) {
    if (isNaN(parseInt(config_id)) && config_id == '') {
        alert(i18n['messages']['error_config_not_selected']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            if (config_id && model_id) {
                addClass($('num_row_' + config_num + '_' + config_id), 't_active');
            } else if (model_id) {
                if ($('franky_row_id_'+config_num)) {
                    addClass($('num_row_' + config_num + '_' + $('franky_row_id_' + config_num).value), 't_active');
                }
            }

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            //  if the array clear_hidden_fields_for_files is not empty
            // the hidden fields has to be cleared
            clearHiddenFieldsForFiles(clear_hidden_fields_for_files);

            //editFranky(model_id, module, 0, config_num);
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=franky' + '&config_id=' + config_id + '&id=' + model_id + '&config_num=' + config_num;

    new Ajax.Request(url, opt);
}

/**
 * Function to upload files in frankenstein or bb via AJAX.
 * It takes all the parameters used for saving Franky/BB
 * because they will be used in the later called functions
 *
 * @param {Object} form - current form object
 * @param {Object} params - all the params for the function
 */
function uploadViaAjax(form, params) {
    // defines the default name of the iframe
    var iframe_id = 'upload_target';

    // creates an element
    var iframe;
    try {
        iframe = document.createElement('<iframe name="' + iframe_id + '">');
    } catch (ex) {
        iframe = document.createElement('iframe');
        iframe.name = iframe_id;
    }

    // applies all the required properties of the iframe
    iframe.src = "javascript:false;";
    iframe.id = iframe_id;
    iframe.style.display = 'none';
    iframe.style.width = '0px';
    iframe.style.height = '0px';

    // append the in the document body
    document.body.appendChild(iframe);

    // if handling of the response is passed as a function, register it as an observer of load event of iframe
    if (params.onLoad && typeof params.onLoad === 'function') {
        iframe.style.display = '';
        Event.observe(iframe, 'load', function() {
            // expecting the content of document.body to be string representation of a JSON object (the response)
            var result = this.contentWindow.document.body.innerText;
            result = String.prototype.isJSON.call(result) ?
                String.prototype.evalJSON.call(result) : {};
            params.onLoad(result);
            // remove iframe from parent window
            Element.remove(this);
        })
    }

    // changes the target and action of the current form
    var current_target = form.target;
    form.target = 'upload_target';
    var current_action = form.action;
    var form_id = form.getAttribute('id');

    if (params.source == 'franky') {
        form.action = env.base_url + '?' + env.module_param + '='+ params.module + '&' + params.module + '=attach_additional_field_file' + '&config_id=' + params.config_id + '&id=' + params.model_id + '&config_num=' + params.config_num + '&div_id=' + params.div_id + '&form_id=' + form_id + '&field_source=' + params.source;
    } else if (params.source == 'bb') {
        form.action = env.base_url + '?' + env.module_param + '='+ params.module + '&' + params.module + '=attach_additional_field_file' + '&id=' + params.model_id + '&bb_id=' + params.bb_id + '&form_id=' + form_id + '&div_id=' + params.div_id + '&bb_num=' + params.bb_num + '&meta_id=' + params.meta_id + '&field_source=' + params.source;
    } else if (params.action) {
        form.action = params.action;
    }

    // submit the form
    form.submit();

    //returns the old properties of the form
    form.target = current_target;
    form.action = current_action;

    return true;
}

/**
 * Function called from the controller to handle the result of upload operation
 *
 * @param data object containing results
 */
function completeData(data) {
    // information for the uploaded files
    var files = data['files'];
    var errors = data['errors'];
    var form_id = data['form_id'];
    var source = data['field_source'];

    var clear_hidden_fields_for_files = new Array();
    var counter = 0;

    // completes the values of the files in
    //specially created hidden fields
    for (var_name in files) {
        if ($(var_name) != null) {
            variable = $(var_name);
            if (variable.type == 'file') {
                variable_id = variable.id;
                variable_name = variable.name;
                var cell = variable.parentNode;
                variable.disabled = true;

                //creates hidden element containing value
                var hidden = document.createElement("INPUT");
                hidden.type = 'hidden';
                hidden.id = 'dbid_' + variable_id;
                hidden.name = 'dbid_' + variable_name;
                hidden.value = files[var_name];
                cell.appendChild(hidden);

                //sets the file ids in an array to be used later
                clear_hidden_fields_for_files[counter] = variable_id;
                counter++;
            }
        }
    }

    //removes the iframe
    if ($('upload_target') != null) {
        iframe = $('upload_target');
        document.body.removeChild(iframe);
    }

    //takes the form
    //forms = document.getElementsByName(form_id);
    forms = $$('form[name="' + form_id + '"]');
    form = forms[0];

    if (errors) {
        errors = eval('(' + errors + ')');
        clearHiddenFieldsForFiles(clear_hidden_fields_for_files);
        if (errors.erred_fields && errors.erred_fields.length > 0) {
            errors.erred_fields.each(function (field) {
                field = field.toString();
                if (!field.match(/^[0-9]+$/) && $(field) != null) {
                    //the errors contain the errred field name as a key (p)
                    //clear the erred file upload inputs
                    $(field).value = '';
                    changeTriggerIcon($(field));
                }
            });
        }
        displayBBErrors(errors);
        Effect.Fade('loading');
    } else {
        if (source == 'franky') {
            // params for saveFranky function
            var module = data['module'];
            var config_id = data['config_id'];
            var model_id = data['model_id'];
            var config_num = data['config_num'];
            var div_id = data['div_id'];

            // correspond with the saveFranky function
            saveFranky(form, module, config_id, model_id, div_id, config_num, clear_hidden_fields_for_files);
        } else if (source == 'bb') {
            var module = data['module'];
            var bb_id = data['bb_id'];
            var model_id = data['model_id'];
            var div_id = data['div_id'];
            var bb_num = data['bb_num'];
            var meta_id = data['meta_id'];

            var after_action = $('bb_after_action') != null ? $('bb_after_action').value : '';
            var after_action_target = $('bb_after_action_target') != null ? $('bb_after_action_target').value : '';

            // correspond with the saveBB function
            saveBB(form, module, bb_id, model_id, div_id, bb_num, meta_id, clear_hidden_fields_for_files);

            //check if the validation of saveBB
            if ($('bb_invalid_data') != null && $('bb_invalid_data').visible()) {
                //error occurred while saving, do not submit the form
                return false;
            }

            if (after_action) {
                if (after_action == 'submit') {
                    // Current browsers do not adhere to this part of the html specification.
                    // The event only fires when it is activated by a user - and does not fire when activated by code.
                    //form.submit();
                    //so click the first enabled submit button
                    submitButton = form.select("button:enabled[type=submit]").first();
                    if (submitButton) {
                        submitButton.click();
                    }
                }
                if (after_action_target) {
                    if (after_action === 'edit' && $('img_edit_' + after_action_target) != null) {
                        $('img_edit_' + after_action_target).click();
                    } else if (after_action == 'add') {
                        addBB(form, module, 0, model_id, div_id, bb_num, after_action_target);
                    } else if (after_action == 'clone') {
                        cloneBB(model_id, module, after_action_target, meta_id, div_id, form);
                    }
                }
            }
        }
    }
}

/**
 * AJAX functionality that DELETEs data from FRANKY
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param config_id - the id of the CONFIGURATOR
 * @param div_id - the id of the current DIV tag
 * @param config_num - the number of the CONFIGURATOR
 */
function delFranky(model_id, module, config_id, div_id, config_num) {
    if (isNaN(parseInt(config_id))) {
        alert(i18n['messages']['error_config_not_present']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            if ($('editRow_'+config_num) && $('franky_row_id_'+config_num).value == config_id) {
                $('editRow_'+config_num).style.visibility = 'hidden';
            }
            if ($('franky_row_id' + config_num)) {
                addClass($('num_row_' + config_num + '_' + $('franky_row_id').value), 't_active');
            }

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=franky' + '&del_id=' + config_id + '&id=' + model_id + '&config_num=' + config_num;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that EDITs the data from FRANKY
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param config_id - the id of the CONFIGURATOR
 * @param config_num - the number of the CONFIGURATOR
 */
function editFranky(model_id, module, config_id, config_num) {
    if (isNaN(parseInt(config_id))) {
        alert(i18n['messages']['error_config_not_present']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            if ($('franky_row_id_' + config_num) != null) {
                removeClass($('num_row_'+ config_num + '_'+ $('franky_row_id_'+ config_num).value), 't_active');
            }
            $('edit_franky_'+ config_num).innerHTML = result;
            if ($('num_row_'+ config_num + '_' + config_id) != null) {
                addClass($('num_row_'+ config_num + '_' + config_id), 't_active');
            }

            var x = $('edit_franky_'+ config_num).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    // if loading saved configuration (with no model_id), get real id from field (use model_id in add mode)
    if (!model_id) {
        if ($('id') && $('id').value) {
            model_id = $('id').value;
        } else if ($('model_id') && $('model_id').value) {
            model_id = $('model_id').value;
        }
    }
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=franky' + '&edit_id=' + config_id + '&id=' + model_id;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality adds row to a bb variable
 *
 * @param form - the form element
 * @param module - name of the module (usually documents)
 * @param bb_id - the id of the bb row (0 in this case, as row is to be added)
 * @param model_id - current model's ID
 * @param div_id - id of the div element that contains the listing of bb elements
 * @param bb_num - the number of the bb variable
 * @param meta_id - the id of the containing configurator or group variable
 */
function addBB(form, module, bb_id, model_id, div_id, bb_num, meta_id) {
    if (isNaN(parseInt(meta_id)) && meta_id == '') {
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    if ($('editRow_1') != null) {
        if ($('editRow_1').getAttribute('onclick').match(/uploadViaAjax/)) {
            //store after action attributes to return back to it after AJAX file upload
            $('bb_after_action').value = 'add';
            $('bb_after_action_target').value = meta_id;
        }
        $('editRow_1').click();
    }

    if ($('editRow_1') != null) {
        Effect.Fade('loading');
        return;
    }

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            if (!result.match(/<!-- BB Vars Updater -->/)) {
                displayBBErrors(eval('(' + result + ')'));
                Effect.Fade('loading');
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            //removes active class from all the elements
            var table_element = $('bb_table');
            for (var i = 0; i < table_element.rows.length; i++) {
                if (table_element.rows[i].id.match(/^bb_row_/)) {
                    removeClass(table_element.rows[i], 't_selected_row_for_edit');
                }
            }

            //sets active class to the selected element
            addClass(table_element.rows[table_element.rows.length-2], 't_selected_row_for_edit');

            // set message to show the user that he have to save the document so all the data to be saved
            toggleBBWarningMessage();

            //collapse all rows
            toggleTableRowsAll('bb_table', 'collapse');

            //expand the last added row
            toggleTableRow(table_element.rows[table_element.rows.length-2]);

            //position the browser to the added row
            new Effect.ScrollTo(table_element.rows[table_element.rows.length-2].id);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=bb&bb_id=' + bb_id + '&id=' + model_id + '&bb_num=' + bb_num + '&meta_id=' + meta_id + '&bb_action=add';

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality clone row to a bb variable
 *
 * @param model_id - current model's ID
 * @param module - name of the module (usually documents)
 * @param bb_id - the id of the bb row to copy from
 * @param div_id - id of the div element that contains the listing of bb elements
 * @param meta_id - the id of the containing configurator or group variable
 * @param form - the form element
 */
function cloneBB(model_id, module, bb_id, meta_id, div_id, form) {
    Effect.Center('loading');
    Effect.Appear('loading');

    if ($('editRow_1') != null) {
        if ($('editRow_1').getAttribute('onclick').match(/uploadViaAjax/)) {
            //store after action attributes to return back to it after AJAX file upload
            $('bb_after_action').value = 'clone';
            $('bb_after_action_target').value = bb_id;
        }
        $('editRow_1').click();
        //a dummy way to prevent any action upon BB error
        if ($('before_calc') && $('before_calc').value == 'error') {
            Effect.Fade('loading');
            return false;
        }
    }

    if ($('editRow_1') != null) {
        Effect.Fade('loading');
        return false;
    }

    if (!confirm(i18n.messages['confirm_clone_row'])) {
        Effect.Fade('loading');
        return;
    }

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return false;
            }
            if (!result.match(/<!-- BB Vars Updater -->/)) {
                displayBBErrors(eval('(' + result + ')'));
                Effect.Fade('loading');
                return false;
            }

            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            //removes active class from all the elements
            var table_element = $('bb_table');
            for (var i = 0; i < table_element.rows.length; i++) {
                if (table_element.rows[i].id.match(/^bb_row_/)) {
                    removeClass(table_element.rows[i], 't_selected_row_for_edit');
                }
            }

            // copy the values of the main variables in the BB rows to the destination row
            var source_row_vars = $$('#bb_row_' + bb_id + ' input');
            var destination_row_index_vars = parseInt(table_element.rows[table_element.rows.length-2].id.replace(/^bb_row_/, '')) + 1;
            for (var j = 0; j < source_row_vars.length; j++) {
                if (source_row_vars[j].id) {
                    var prefix = source_row_vars[j].id.replace(/[0-9]*$/, '');
                    if ($(prefix + destination_row_index_vars)) {
                        $(prefix + destination_row_index_vars).value = source_row_vars[j].value;
                    }
                }
            }

            //sets active class to the selected element
            addClass(table_element.rows[table_element.rows.length-2], 't_selected_row_for_edit');

            // set message to show the user that he have to save the document so all the data to be saved
            toggleBBWarningMessage();

            //collapse all rows
            toggleTableRowsAll('bb_table', 'collapse');

            //expand the last added row
            toggleTableRow(table_element.rows[table_element.rows.length-2]);

            //position the browser to the added row
            new Effect.ScrollTo(table_element.rows[table_element.rows.length-2].id);

            Effect.Fade('loading');

            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=bb&bb_id=' + bb_id + '&id=' + model_id + '&meta_id=' + meta_id + '&bb_action=clone';

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to view a bb row data
 *
 * @param bb_id - the id of the bb row
 */
function viewBB(bb_id) {

    Effect.Center('loading');
    Effect.Appear('loading');

    if ($('editRow_1') != null) {
        $('editRow_1').click();
    }

    Effect.Fade('loading');
}

/**
 * AJAX functionality that EDITs a row of the bb listing
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param bb_id - the id of the bb row
 * @param bb_num - the number of the bb variable
 * @param meta_id - the id of the containing configurator or group variable
 * @param element - the element which the function is called from
 */
function editBB(model_id, module, bb_id, bb_num, meta_id, element) {
    if (isNaN(parseInt(bb_id))) {
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var element_row = element.parentNode.parentNode;
    var table_element = element_row.parentNode;

    if ($('editRow_1') != null) {
        if ($('editRow_1').getAttribute('onclick').match(/uploadViaAjax/)) {
            //store after action attributes to return back to it after AJAX file upload
            $('bb_after_action').value = 'edit';
            $('bb_after_action_target').value = bb_id;
        }
        $('editRow_1').click();
        //a dummy way to prevent any action upon BB error
        if ($('before_calc') && $('before_calc').value == 'error') {
            Effect.Fade('loading');
            return false;
        }
    }

    if ($('editRow_1') != null) {
        Effect.Fade('loading');
        return false;
    }

    var opt = {
        method: 'post',
        asynchronous: false,
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return false;
            }
            if (!result.match(/<!-- BB Vars Updater -->/)) {
                displayBBErrors(eval('(' + result + ')'));
                Effect.Fade('loading');
                return false;
            }

            //removes active class from all the elements
            var table_element = $('bb_table');
            for (var i = 0; i < table_element.rows.length; i++) {
                if (table_element.rows[i].id.match(/^bb_row_/)) {
                    removeClass(table_element.rows[i], 't_selected_row_for_edit');
                }
            }

            $('cell_'+ bb_id).innerHTML = result;

            if (bb_id) {
                addClass($('bb_row_'+ bb_id), 't_selected_row_for_edit');
            }

            var x = $('cell_'+ bb_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
               eval(x[i].text);
            }

            $('img_edit_' + bb_id).src = env.themeUrl + "images/small/download.png";
            $('img_edit_' + bb_id).setAttribute('onClick', 'viewBB(' + bb_id + ');');
            $('img_edit_' + bb_id).onclick = function() {
                return viewBB(bb_id);
            };
            if (Cookie.get('row_' + bb_id + '_box') == 'off') {
                //expand row
                toggleTableRow($('bb_row_' + bb_id));
            }
            Effect.Fade('loading');

            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=bb&bb_id=' + bb_id + '&id=' + model_id + '&meta_id=' + meta_id + '&bb_action=edit';
    if ($('model_lang') != null) {
        url += '&model_lang=' + $('model_lang').value;
    }

    //removes active class from all the elements
    for (var i = 0; i < table_element.rows.length; i++) {
        if (table_element.rows[i].id.match(/^bb_row_/)) {
            removeClass(table_element.rows[i], 't_selected_row_for_edit');
        }
    }

    //sets active class to the selected element
    addClass(element_row, 't_selected_row_for_edit');

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that DELETEs row from BB
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param bb_id - the id of the bb row
 * @param bb_num - the number of the bb
 * @param meta_id - the id of the config/GT/GT2 variable in bb
 * @param form - current form object
 */
function delBB(model_id, module, bb_id, bb_num, meta_id, form) {
    if (isNaN(parseInt(bb_id))) {
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    if ($('editRow_1') != null) {
        //only click the save button if the row open for edit is different from the one that should be deleted
        var edited_row_id = $('bb_id_' + bb_num) ? $('bb_id_' + bb_num).value : null;
        if (!edited_row_id || edited_row_id != bb_id) {
            $('editRow_1').click();
            //a dummy way to prevent any action upon BB error
            if ($('before_calc') && $('before_calc').value == 'error') {
                Effect.Fade('loading');
                return false;
            }
        }
    }

    if ($('editRow_1') != null && edited_row_id != bb_id) {
        Effect.Fade('loading');
        return false;
    }

    if (!confirm(i18n.messages['confirm_delete'])) {
        Effect.Fade('loading');
        return;
    }

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return false;
            }
            if (!result.match(/<!-- BB Vars Updater -->/)) {
                displayBBErrors(eval('(' + result + ')'));
                Effect.Fade('loading');
                return false;
            }

            $('list_bb_' + bb_num).innerHTML = result;

            var x = $('list_bb_' + bb_num).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            // set message to show the user that he have to save the document so all the data to be saved
            toggleBBWarningMessage();

            Effect.Fade('loading');

            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=bb&bb_id=' + bb_id + '&id=' + model_id + '&meta_id=' + meta_id + '&bb_action=del';

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that saves the data from BB row
 *
 * @param form - current form object
 * @param module - current MODULE
 * @param bb_id - the id of the bb row
 * @param model_id - the id of the current MODEL
 * @param div_id - the id of the DIV where the BB is
 * @param bb_num - the number of the bb (usually 1)
 * @param meta_id - the id of the config/GT/GT2 variable in bb
 * @param clear_hidden_fields_for_files - TODO parameter is not used yet
 */
function saveBB(form, module, bb_id, model_id, div_id, bb_num, meta_id, clear_hidden_fields_for_files) {
    if (isNaN(parseInt(bb_id)) && bb_id == '') {
        return;
    }

    if ($('total') != null) {
        gt2calc();
    }

    var opt = {
        method: 'post',
        asynchronous: false,
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return false;
            }
            if (!result.match(/<!-- BB Vars Updater -->/)) {
                displayBBErrors(eval('(' + result + ')'));
                if ($('before_calc') != null) {
                    if ($('bb_value_' + (bb_id + 1)) != null) {
                        $('bb_value_' + (bb_id + 1)).value = $('before_calc').value;
                    }
                    $('before_calc').value = 'error';
                }
                Effect.Fade('loading');
                return;
            }
            $(div_id).innerHTML = result;

            // set message to show the user that they have to save the document for all the data to be saved
            toggleBBWarningMessage();

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=bb&bb_id=' + bb_id + '&id=' + model_id + '&bb_num=' + bb_num + '&meta_id=' + meta_id + '&bb_action=save';

    new Ajax.Request(url, opt);
}

/**
 * Show BB message and warning to show the user that they have to save the document for all the data to be saved
 */
function toggleBBWarningMessage() {
    if ($('bb_edit_message').style.display == 'none') {
        $('bb_edit_message').style.display = '';
    }
    $('bb_edit_error').hide();
}

/**
 * Displays error messages when bb action fails
 *
 * @param {Object} result - errors and error keys
 * @return void
 */
function displayBBErrors(result) {
    if (result.erred_fields) {
        result.erred_fields.each(function(ef) {
            $$('label[for="' + ef + '"]').concat($$('table.t_grouping_table th label[for="' + ef + '_1"]')).each(function(ee) {
                addClass(ee, 'error');
            });
        });
    }
    var container = $('bb_invalid_data') || $('bb_edit_error');
    if (container) {
        container.innerHTML = result.errors;
        container.show();
        if (container.id != 'bb_edit_error') {
            $('bb_edit_error').hide();
        }
        new Effect.ScrollTo(container);
    }
}

/**
 * AJAX functionality that saves the data from GROUP TABLE
 *
 * @param form - current form object
 * @param module - current MODULE
 * @param config_group_id - the id of the GROUP PATTERN
 * @param model_id - the id of the current MODEL
 * @param div_id - the id of the DIV where the GROUP TABLE is
 * @param config_group_num - the number of the GROUP TABLE
 */
function saveConfigGroup(form, module, config_group_id, model_id, div_id, group_num) {
    if (isNaN(parseInt(config_group_id)) && config_group_id == '') {
        alert(i18n['messages']['error_config_not_selected']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=savegroupvar' + '&config_group_id=' + config_group_id + '&id=' + model_id + '&group_num=' + group_num;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
    }

     new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that EDITs the data from GROUP TABLE
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param config_group_id - the id of the GROUP PATTERN
 * @param group_num - the number of the GROUP TABLE
 */
function editConfigGroup(model_id, module, config_group_id, group_num) {
    if (isNaN(parseInt(config_group_id))) {
        alert(i18n['messages']['error_config_not_present']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $('edit_group_'+ group_num).innerHTML = result;

            var x = $('edit_group_'+ group_num).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=savegroupvar' + '&edit_id=' + config_group_id + '&id=' + model_id;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that DELETEs data from  GROUP TABLE
 *
 * @param model_id - current model's ID
 * @param module - current MODULE
 * @param config_group_id - the id of the GROUP PATTERN
 * @param group_num - the number of the GROUP TABLE
 * @param div_id - the id of the current DIV tag
 */
function delConfigGroup(model_id, module, config_group_id, div_id, group_num) {
    if (isNaN(parseInt(config_group_id))) {
        alert(i18n['messages']['error_config_not_present']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=savegroupvar' + '&del_id=' + config_group_id + '&id=' + model_id + '&group_num=' + group_num;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
    }

     new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that manages saved configuration of tasks
 *
 * @param {Object} config_field - config combo field (its value could be id or name of the saved configuration)
 * @param {string} div_id - the id of the DIV to load the result into
 * @param {string} config_action - save or delete
 */
function manageConfigTask(config_field, div_id, config_action) {
    // id or name of the saved configuration
    var config_id = config_field.value;
    var form = config_field.form;
    if (config_action != 'save') {
        config_id = parseInt(config_id);
        if (isNaN(config_id)) {
            config_id = 0;
        }
    }
    if (config_action == 'save' && isNaN(parseInt(config_id)) && config_id == '' ||
        config_action != 'save' && !config_id || !form) {
        alert(i18n['messages'][config_action == 'save' ? 'error_config_not_selected' : 'error_config_not_present']);
        return false;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
               eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&tasks=ajax_configurator&config_action=' + config_action;

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that prepare the data for transformations
 *
 * @param form - current form object
 * @param module - current MODULE
 * @param config_id - the id of the CONFIGURATOR
 * @param model_id - the id of the current MODEL
 * @param div_id - the id of the DIV where the FRANKY is
 * @param config_num - the number of the CONFIGURATOR
 * @param num_row - the number of the row of the CONFIGURATOR
 */
function prepareTransform(form, div_id, module, group_num, new_type, transform) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=ajax_prepare_transform' +
        '&group_num=' + group_num + '&new_type=' + new_type + '&transform=' + transform;

     new Ajax.Request(url, opt);
}

/**
 * AJAX functionality that check for similar customer names
 * Function is no longer used for checking customer name similarity.
 *
 * @param form - current form object
 * @param module - current MODULE
 * @param div_id - the id of the DIV where the FRANKY is
 */
function checkName(form, div_id, module) {

    if (trim($('name').value) == '') {
        alert(i18n['messages']['alert_empty_field']);
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        asynchronous: false,
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return true;
            }
            $(div_id).innerHTML = result;
            $(div_id).parentNode.style.display = '';
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '='+ module + '&' + module + '=ajax_checkname';

    new Ajax.Request(url, opt);
    if ($(div_id).innerHTML == '') {
        return true;
    } else {
        url = '#similar_names';
        redirect(url);
        return false;
    }
}

/**
 * Submits the given form, but first calculates all formulas in the additional variables
 *
 * @param frm - the form that will be submitted
 */
function calculateBeforeSubmit(frm) {
    showLoading();

    if ($('editRow_1') != null) {
        if ($('editRow_1').getAttribute('onclick').match(/uploadViaAjax/)) {
            //let the completeData function submit the form
            $('bb_after_action').value = 'submit';
        }
        $('editRow_1').click();
        //a dummy way to prevent any action upon BB error
        if ($('before_calc') && $('before_calc').value == 'error') {
            hideLoading();
            return false;
        }
    }

    if ($('editRow_1') != null) {
        Effect.Fade('loading');
        return;
    }

    preventResubmit(frm);

    runFormCalculations(frm, true);

    //submit the form
    frm.submit();

    return true;
}

/**
 * Calculates all formulas in the additional variables in form
 *
 * @param {Object} frm - form DOM element
 * @param {boolean} disable_buttons - whether to disable calculation buttons
 */
function runFormCalculations(frm, disable_buttons) {
    var buttons = frm.getElementsByTagName('button');
    var find_allcalc = 0;
    var btn_all = new Array();
    for (var ii = 0; ii < buttons.length; ii++) {
        if (buttons[ii].type == 'button') {
            if (buttons[ii].id.match(/^calc/)) {
                if (buttons[ii].disabled) {
                    buttons[ii].disabled = false;
                }
                buttons[ii].click();
                if (buttons[ii] && disable_buttons) {
                    buttons[ii].disabled = true;
                }
            } else if (buttons[ii].id.match(/^allcalc/)) {
                btn_all[find_allcalc] = buttons[ii];
                find_allcalc++;
            }
        }
    }
    if (find_allcalc > 0) {
        for (var ii = 0; ii < btn_all.length; ii++) {
            if (btn_all[ii].disabled) {
                btn_all[ii].disabled = false;
            }
            btn_all[ii].click();
            if (disable_buttons) {
                btn_all[ii].disabled = true;
            }
        }
    }
}

/**
 * Fills in values of fields in all rows where the "replace column" check is set before submit of multiedit.
 */
function replaceBeforeSubmit() {
    var inputs = $$('input[type="checkbox"][id]'),
        chk = $('replace_vals').checked,
        num,
        i,
        field,
        field_name;
    for (i = 0; i < inputs.length; i++) {
        if (inputs[i].id.match(/_all/) && inputs[i].checked) {
            field_name = inputs[i].name.replace(/_all/, '');
            field = $(field_name + '_val');
            if (!field) {
                field = $(field_name);
            }
            // radio
            if (!field && $(field_name + '_val_0')) {
                // get the value of the checked option
                field = getRadioValue($$('input[type="radio"][name="' + $(field_name + '_val_0').name + '"]'));
                field_name += '_0';
            }
            num = 1;
            while (field_arr = $(field_name + '_' + num)) {
                if (field_arr.disabled == false) {
                    if (field_arr.type == 'checkbox') {
                        field_arr.checked = field.checked;
                    } else if (field_arr.type == 'radio') {
                        $$('input[type="radio"][name="' + field_arr.name + '"]').each(function(r) {
                            r.checked = r.value == field;
                        });
                    } else {
                        field_arr.value = field.value;
                        if ($(field_name + '_autocomplete') != null && $(field_name + '_autocomplete_' + num) != null) {
                            $(field_name + '_autocomplete_' + num).value = $(field_name + '_autocomplete').value;
                        } else if ($(field_name + '_val_formatted') != null && $(field_name + '_formatted_' + num) != null) {
                            $(field_name + '_formatted_' + num).value = $(field_name + '_val_formatted').value;
                        }
                    }
                }
                num++;
            }
        }
    }

    return true;
}

/**
 * AJAX CALCULATION for a specified formula for ADDITIONAL VARIABLES
 *
 * @param object button - button object
 * @param string f_id - id of field in fields_meta
 */
function calc(button, f_id) {
    if (show_loading) {
        Effect.Center('loading');
        Effect.Appear('loading');
        show_loading = 0;
    }

    var form_element = $(button).form,
        module_name = form_element.getAttribute('name').replace(/_.*/, "");
    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(form_element),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            for (var i in result) {
                // search for the field within the form of the button
                var field = $(form_element).down('[name="' + i + '"]');
                if (field) {
                    if (field.tagName == 'INPUT' && field.type == 'radio') {
                        setRadioValue(
                            $(form_element).select('[name="' + i + '"]'),
                            result[i]
                        );
                    } else {
                        field.value = result[i];
                    }
                    if (typeof field.onchange == 'function') {
                        field.onchange();
                    }
                    updateFormattedField(field, result, form_element);
                }
            }
            if (hide_loading) {
                Effect.Fade('loading');
                show_loading = 1;
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name + '&' + module_name + '=calculate&calculate=' + f_id, opt);
}

/**
 * Updates element containing formatted value (if such exists) when field value
 * is changed
 *
 * @param {Object} field - field to search dependent field for
 * @param {Object} data - all new updated data
 * @param {Object} container - optional container element to seach into
 * @return void
 */
function updateFormattedField(field, data, container) {
    if (!field) {
        return;
    }
    if (!container) {
        container = field.form || field.up('body');
    }
    var value = field.value,
        name = field.id,
        row_suffix = '',
        formatted_field,
        date,
        matches;
    if (name.match(/.*_\d+$/)) {
        matches = (/^(.*)(_\d+)$/).exec(name);
        name = matches[1];
        row_suffix = matches[2];
    }
    formatted_field = container.down('#' + name + '_formatted' + row_suffix);
    if (field.hasClassName('datebox')) {
        // date
        if (!value || value == '0000-00-00') {
            clearDateField(field);
        } else {
            // parse the date
            date = parseISODate(value);
            if (date) {
                formatted_field.value = date.format('d.m.Y');
            } else if (value.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4}$/)) {
                //the date has been formatted already dd.mm.yyyy
                date = parseFormattedDate(value);
                formatted_field.value = date.format('d.m.Y');
                field.value = date.format('Y-m-d');
            }
        }
    } else if (field.hasClassName('datetimebox')) {
        // datetime
        if (!value || value == '0000-00-00 00:00:00') {
            clearDateField(field);
        } else {
            // parse the date
            date = parseISODate(value);
            if (date) {
                formatted_field.value = date.format('d.m.Y, H:i');
            } else if (value.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}(:[0-9]{2})?$/)) {
                //the datetime has been formatted already dd.mm.yyyy H:i or dd.mm.yyyy H:i:s
                date = parseFormattedDate(value);
                formatted_field.value = date.format('d.m.Y, H:i');
                field.value = date.format('Y-m-d H:i:00');
            }
        }
    } else if (field.hasClassName('timebox')) {
        // time
        formatted_field.value = value;
    } else if (field.hasClassName('autocompletebox') && field.hasClassName('hidden')) {
        // autocompleter
        // if hidden autocompleter field, check for link and update it
        formatted_field = container.down('a.' + field.getAttribute('uniqid'));
        if (formatted_field) {
            var id_var = formatted_field.className.split(' ').filter(function(a) { return /^id_var-/.test(a); }),
                id_var_value = '';
            id_var = id_var.length ? id_var[0].replace(/^id_var-/, '') : '';
            if (id_var) {
                id_var_value = container.down('#' + id_var + row_suffix);
                if (id_var_value) {
                    id_var_value = data[id_var_value.name] || id_var_value.value;
                }
            }
            var link_params = {};
            link_params[id_var] = id_var_value;
            updateAutocompleteLink(field, link_params);
        }
    } else if (field.tagName == 'INPUT' && field.type == 'hidden') {
        // readonly dropdown
        formatted_field = container.down('select#' + name + '_readonly' + row_suffix);
        if (formatted_field && formatted_field.disabled) {
            formatted_field.value = value;
        } else if (field.hasClassName('radio_hidden')) {
            // readonly radio
            matches = /^(.*?)(\[\-?\d+\])?$/.exec(field.name);
            setRadioValue(
                container.select('input[type="radio"][name="' + matches[1] + '_readonly' + (matches[2] || '') + '"]'),
                data[field.name]
            );
        }
    }
    if (formatted_field && typeof formatted_field.onchange == 'function') {
        formatted_field.onchange();
    }
}

/**
 * DROPDOWNs relations. Change the values of a second dropdown when the first one changes
 *
 * @param s_id - id of the object
 */
function change_options(s_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var form1 = $(s_id).form;
    var module_name = form1.getAttribute('name');
    module_name = module_name.replace(/_.*/, "");
    var select_obj = $(s_id);
    var opt = {
        asynchronous: false,
        method: 'post',
        parameters: Form.serialize(form1),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            if (result.length > 0) {
                select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj.options[0], 'undefined');
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j+1] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name + '&' + module_name + '=calculate&change_options=' + s_id, opt);
}


/**
 * DROPDOWNs relations . Change the values of a second dropdown when the first changes
 *
 * @param s_id - id of the object
 */
function changeDependingOptions(s_id, model_id, module_name, controller, please_select) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj = $(s_id);
    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            if (result.length > 0) {
                if (please_select) {
                    select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                    addClass(select_obj.options[0], 'undefined');
                }
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j+please_select] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var param = (controller == '') ? module_name : 'controller=' + controller + '&'+ controller;
    var type = $('type') != null ? '&model_type=' + $('type').value : '';
    var lang = $('model_lang') != null ? $('model_lang').value : env.current_lang;
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name + '&' + param + '=ajax_change_depending_options&model_id=' + model_id + '&model_lang=' + lang + '&s_id=' + s_id + type, opt);
}

/**
 * DROPDOWNs relations. Change the values of a second dropdown when the first one changes
 *
 * @param s_id - id of the object
 */
function changeDependingOptions2(s_id, model_id, module_name, controller, please_select, office_id, container_type) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj = $(s_id);
    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            if (result.length > 0) {
                if (please_select) {
                    select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                    addClass(select_obj.options[0], 'undefined');
                }
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j+please_select] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var param = (controller == '') ? module_name : 'controller=' + controller + '&'+ controller;
    var type = $('type') != null ? '&model_type=' + $('type').value : '';
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name + '&' + param + '=ajax_change_depending_options&model_id=' + model_id + '&model_lang=' + $('model_lang').value + '&s_id=' + s_id + '&office_id=' + office_id + '&container_type=' + container_type + type, opt);
}

/* OTHER functions */
/* =============== */

/**
 * shows Loading DIV tag
 */
show_loading = 1;
hide_loading = 1;
centered_loading = 0;
function showLoading() {
    if (show_loading) {
        if (!centered_loading) {
            Effect.Center('loading');
            centered_loading = 1;
        }
        Effect.Appear('loading');
    }
    show_loading = 0;
    hide_loading = 0;
}

/**
 * Hides Loading DIV tag
 */
function hideLoading() {
    show_loading = 1;
    hide_loading = 1;
    Effect.Fade('loading');
}

/**
 * AJAX functionality SAVES the comment
 *
 * @deprecated Used in previous comments functionality
 */
function saveComment(form, div_id) {
    if ($('content').value == '') {
        alert(i18n.messages['alert_empty_field']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var action = ($('id') && $('id').value > 0) ? 'edit' : 'add';
    var url = env.base_url + '?' + env.module_param + '=comments' + '&comments=' + action;

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality edit comment
 *
 * @deprecated Used in previous comments functionality
 */
function editComment(comment_id, div_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=comments' + '&comments=edit&edit=' + comment_id;

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to display form for modification of tags of model in
 * list view.
 *
 * @param {Number} model_id - id of model
 * @param {String} module - module
 * @param {String} controller - controller (if other than module)
 * @param {String} redirect_to_url - parameter for redirection URL after action is performed
 */
function changeTags(model_id, module) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            Effect.Fade('loading');
            if (!checkAjaxResponse(result) || !result) {
                return;
            }
            //show form in a lightbox
            lb = new lightbox({
                content: result,
                title: i18n['labels']['tags_change'],
                icon: 'tag.png',
                width: 600
            });
            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var controller = (changeTags.arguments[2]) ? changeTags.arguments[2] : '';
    var url;
    if (controller) {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + env.controller_param + '=' + controller + '&' + controller + '=ajax_tag&id=' + model_id;
    } else {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=ajax_tag&id=' + model_id;
    }
    // if there is a parameter for redirection URL after action is performed
    if (changeTags.arguments[3]) {
        url += '&redirect_to_url=' + changeTags.arguments[3];
    }

    new Ajax.Request(url, opt);
}

/**
 * If tag belongs to a group having a tag limit, check that no more than the
 * allowed number of tag checkboxes are selected.
 *
 * @param {Object} obj - javascript object element
 * @return {boolean} true|false
 */
function checkTagLimit(obj, tag_limit) {
    if (obj.checked && obj.className.match(/(^|\s)section_\d+($|\s)/) && tag_limit > 0 &&
    count_checkboxes(obj.form, 'tags', obj.className.replace(/.*(section_\d+).*/g, '$1')) > tag_limit) {
        alert(tag_limit > 1 ?
              i18n.messages['alert_tag_limit_num'].replace(/\[num\]/, tag_limit) :
              i18n.messages['alert_tag_limit_one']);
        return false;
    }

    return true;
}

/**
 * AJAX functionality to display form for modification of a type of assignment
 * of model in list view.
 *
 * @param model_id - id of model
 * @param module - module
 * @param a_type - assignment type
 * @param controller - controller (if other than module)
 */
function changeAssignments(model_id, module, a_type) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            Effect.Fade('loading');
            // do not create lightbox if response is empty
            if (!result) {
                return;
            }
            if (!checkAjaxResponse(result)) {
                return;
            }
            //show the form in a lightbox
            lb = new lightbox({
                content: result,
                title: i18n['labels']['assignments_change'] + ': ' + i18n['labels'][a_type],
                icon: 'assign.png',
                width: 600
            });
            lb.activate();

            //only when assigning a project
            if (module == 'projects') {
                initTree('departments');
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var controller = (changeAssignments.arguments[3]) ? changeAssignments.arguments[3] : '';
    var url;
    if (controller) {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + env.controller_param + '=' + controller + '&' + controller + '=ajax_assign&id=' + model_id + '&a_type=' + a_type;
    } else {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=ajax_assign&id=' + model_id + '&a_type=' + a_type;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to display form for changing status of model in list view.
 *
 * @param model_id - id of model
 * @param module - module
 * @param controller - controller (if other than module)
 */
function changeStatus(model_id, module) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            Effect.Fade('loading');
            if (!checkAjaxResponse(result)) {
                return;
            }
            //show the form in a lightbox
            lb = new lightbox({
                content: result,
                title: i18n['labels']['status_change'],
                icon: 'setstatus.png',
                width: 600
            });
            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var controller = (changeStatus.arguments[2]) ? changeStatus.arguments[2] : '';
    var url;
    if (controller) {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + env.controller_param + '=' + controller + '&' + controller + '=ajax_status&id=' + model_id;
    } else {
        url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=ajax_status&id=' + model_id;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to load form for add/edit/translate of branch
 * OR perform multiple actions (activate, deactivate, delete) with branches
 *
 * @param {Object} form - form with data to submit
 * @param {string} div_id - id of container to load result into
 * @param {string} action - performed action
 * @param {string} lang - language of model
 * @param {number} parent_id - id of (normal) customer
 * @param {number} id - id of model to save
 */
function changeBranch(form, div_id, action, lang, parent_id, id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
            new Effect.ScrollTo(div_id);
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=customers&controller=branches&branches=' + action;
    if (action == 'add') {
        url += '&parent_customer_id=' + parent_id;
    } else if (action == 'edit') {
        url += '&edit=' + id + '&parent_customer_id=' + parent_id;
    } else if (action == 'translate') {
        url += '&translate=' + id + '&parent_customer_id=' + parent_id + '';
    }
    if (lang) {
        url += '&model_lang=' + lang;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to add/edit/translate branch for customer
 *
 * @param {Object} form - form with data to submit
 * @param {string} div_id - id of container to load result into
 * @param {string} action - performed action
 * @param {number} parent_id - id of (normal) customer
 * @param {number} id - id of model to save
 */
function saveBranch(form, div_id, action, parent_id, id) {
    if ($('name').value == '') {
        alert(i18n.messages['alert_empty_field']);
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            if ($(div_id)) {
                $(div_id).innerHTML = result;
            } else {
                var div = document.createElement('div');
                div.id = div_id;
                div.innerHTML = result;
                div.style.display = 'none';
                document.body.appendChild(div);
                var err = [];
                $$('#' + div_id + ' div#branch_custom_panel div.message_container li').each(function(a) { err.push(a.innerHTML); });
                if (err.length) {
                    alert(err.join("\n"));
                }
                document.body.removeChild(div);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=customers&controller=branches&branches=' + action;
    if (action == 'edit') {
        url += '&edit=' + id + '&parent_customer_id=' + parent_id;
    } else if (action == 'translate') {
        url += '&translate=' + id + '&parent_customer_id=' + parent_id;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to load form for add/edit/translate of contact person
 * OR perform multiple actions (activate, deactivate, delete) with contact persons
 *
 * @param {Object} form - form with data to submit
 * @param {string} div_id - id of container to load result into
 * @param {string} action - performed action
 * @param {string} lang - language of model
 * @param {number} parent_id - id of (normal) customer
 * @param {number} id - id of model to save
 */
function changeContactPerson(form, div_id, action, lang, parent_id, id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
            new Effect.ScrollTo(div_id);
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=customers&controller=contactpersons&contactpersons=' + action;
    if (action == 'add') {
        url += '&parent_customer_id=' + parent_id;
    } else if (action == 'edit') {
        url += '&edit=' + id + '&parent_customer_id=' + parent_id;
    } else if (action == 'translate') {
        url += '&translate=' + id + '&parent_customer_id=' + parent_id + '';
    }
    if (lang) {
        url += '&model_lang=' + lang;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to add/edit/translate contact person for customer
 *
 * @param {Object} form - form with data to submit
 * @param {string} div_id - id of container to load result into
 * @param {string} action - performed action
 * @param {number} parent_id - id of (normal) customer
 * @param {number} id - id of model to save
 */
function saveContactPerson(form, div_id, action, parent_id, id) {
    if ($('lastname').value == '' || $('parent_branch').value == '') {
        alert(i18n.messages['alert_empty_field']);
        return;
    }

    eval('var branch_main_contacts = ' + (($('branch_main_contacts').value) ? ($('branch_main_contacts').value) : ('[]')));
    //check if the main contact of the selected branch is changed
    if ($('main_contact') && $('main_contact').checked) {
        if (branch_main_contacts[$('parent_branch').value] && branch_main_contacts[$('parent_branch').value] != id) {
            if (!confirm(i18n.messages['confirm_changing_contact_person'])) {
                return;
            }
        }
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            if ($(div_id)) {
                $(div_id).innerHTML = result;
            } else {
                var div = document.createElement('div');
                div.id = div_id;
                div.innerHTML = result;
                div.style.display = 'none';
                document.body.appendChild(div);
                var err = [];
                $$('#' + div_id + ' div#contact_persons_custom_panel div.message_container li').each(function(a) { err.push(a.innerHTML); });
                if (err.length) {
                    alert(err.join("\n"));
                }
                document.body.removeChild(div);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=customers&controller=contactpersons&contactpersons=' + action;
    if (action == 'edit') {
        url += '&edit=' + id + '&parent_customer_id=' + parent_id;
    } else if (action == 'translate') {
        url += '&translate=' + id + '&parent_customer_id=' + parent_id;
    }

    new Ajax.Request(url, opt);
}

/**
 * Set a specified value to a hidden field
 *
 * @params name - the name of the hidden field
 * @params val - the new value we will insert into the given field
 */
function setHiddenInput(name, val) {
    var input = $(name);
    input.value = val;
}

/**
 * AJAX functionality ADD, EDIT, TRANSLATE, ACTIVATE, DEACTIVATE activities for stages
 *
 * element - the elements which invokes the function
 * data_method - the method to pass the data
 * div_id - id if the div to be updated
 * action - action to be performed
 * lang - the model lang
 * parent_id - the parent stage id
 * current_id - the parent stage id
 */
function changeActivity(element, data_method, div_id, action, lang, parent_id, current_id) {
    if (data_method == 'post' && $('name').value == '') {
        alert(i18n.messages['alert_empty_field']);
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: data_method,
        parameters: Form.serialize(element),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
            new Effect.ScrollTo(div_id);
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=stages&controller=phases&phases=';

    if (action == 'delete') {
        url += 'delete_activity&activity_id=' + current_id + '&parent_stage_id=' + parent_id;
    } else {
        if (data_method == 'get') {
            url += 'add_edit_activity';
        } else {
            url += 'save_activity';
        }

        if (action == 'add') {
            url += '&activity_action=' + action + '&parent_stage_id=' + parent_id;
        } else if (action == 'edit' || action == 'translate') {
            url += '&activity_action=' + action + '&activity_id=' + current_id + '&parent_stage_id=' + parent_id + '&lang=' + lang;
        }
    }

    if (lang) {
        url += '&model_lang=' + lang;
    }

    new Ajax.Request(url, opt);
}

/**
 *
 *
 * @params element - select box
  */
function changeActiveField(element) {
    for (var i = 0; i < element.options.length; i++) {
        if (element.options[i].selected == true) {
            $(element.options[i].value).style.display = "inline";
            $(element.options[i].value).disabled = false;
            if ($(element.options[i].value + '_formatted')) {
                $(element.options[i].value + '_formatted').style.display = "inline";
                $(element.options[i].value + '_formatted').disabled = false;
            }
        } else {
            $(element.options[i].value).style.display = "none";
            $(element.options[i].value).disabled = true;
            if ($(element.options[i].value + '_formatted')) {
                $(element.options[i].value + '_formatted').style.display = "none";
                $(element.options[i].value + '_formatted').disabled = true;
            }
        }
    }
}

/**
 * check all checkboxes
 *
 * @params element - check box object
 * @params name - the name of the array object of checkboxes
 */
function checkAll(element, name) {
    if (element.checked) {
        checked = true;
    } else {
        checked = false;
    }
    if (checkAll.arguments.length > 2) {
        //get the 3rd argument
        var get = checkAll.arguments[2];
        if (get == 'class') {
            var chk = $$('.' + name);
        } else {
            //continue here with other properties if you have need to do this
        }
    } else {
        var chk = document.getElementsByName(name + '[]');
    }
    for (var i = 0; i < chk.length; i++) {
        chk[i].checked = checked;
        if (chk[i].getAttribute('onclick')) {
            chk[i].onclick();
        }
    }

}

/**
 * check all checkboxes
 *
 * @params element - check box object
 * @params parent_id - parent element id of the array object of checkboxes
 */
function checkAllChildren(element, parent_id) {
    if (element.checked) {
        checked = true;
    } else {
        checked = false;
    }
    var chk = $(parent_id).getElementsByTagName('input');
    for (var i = 0; i < chk.length; i++) {
        if (chk[i].type.toLowerCase() == 'checkbox' && chk[i] != element) {
            chk[i].checked = checked;
        }
    }

}

/**
 * Checkes/ Unchecks all checkboxes
 *
 * @params element - not in use
 * @params name - the name of the array object of checkboxes
 * @params mode - boolean '1' for select all '0' disselect all
 */
function toggleCheckboxes(element, name, mode) {
    if (toggleCheckboxes.arguments[3]) {
        container_id = toggleCheckboxes.arguments[3];
        var chk = $(container_id).getElementsByTagName('input');
        for (var i = 0; i < chk.length; i++) {
            var re = RegExp(name);
            if (chk[i].type == 'checkbox' && !chk[i].disabled && chk[i].name.match(re)) {
                chk[i].checked = mode;
            }
        }
    } else {
        var chk = document.getElementsByName(name + '[]');
        for (var i = 0; i < chk.length; i++) {
            if (chk[i].type == 'checkbox' && !chk[i].disabled) {
                chk[i].checked = mode;
            }
        }
    }
}

/**
 * Initialize Zapatec TREE class
 *
 * @params target - specifies tree instance together with 'tree_' prefix
 */
function initTree(target) {
    // If there is a second parameter that is greater than zero and is not an object
    if (initTree.arguments[1] > 0 && typeof initTree.arguments[1] != 'object') {
        // Use it as level
        level = initTree.arguments[1];
        selnode = true;
    } else {
        // Set default level
        level = 10;
        selnode = false;
    }

    // Set default theme name
    var theme_name = 'default';

    // Set default setting expandOnSignClick (i.e. the +/- icon should toggle (expand or collapse) a tree node)
    var expandOnSignClick_value = true;

    // If there is a second parameter and it's an object
    if (typeof initTree.arguments[1] == 'object') {
        // If a theme name is set
        if (typeof initTree.arguments[1].theme != 'undefined') {
            // Use the theme name from the object
            theme_name = initTree.arguments[1].theme;
        }

        // If expandOnSignClick is set
        if (typeof initTree.arguments[1].expandOnSignClick != 'undefined') {
            // Use the expandOnSignClick value from the object
            expandOnSignClick_value = initTree.arguments[1].expandOnSignClick;
        }

        // If tree level is set
        if (typeof initTree.arguments[1].level != 'undefined') {
            // Use the level value from the object
            level   = initTree.arguments[1].level;
            selnode = false;
        }
    }

    // Prepare options for the Zapatec tree
    var opt = {
      tree: "tree_" + target,
      theme: theme_name,
      initLevel: level,
      deselectSelected: true,
      hiliteSelectedNode: selnode,
      expandOnSignClick: expandOnSignClick_value,
      eventListeners: {
          // After initializing the tree, scroll to the first occurrence of the current model in the tree
          'afterCreate': function () {
              if (typeof $$('[name="branch_current_model"]')[0] !== 'undefined') {
                  $$('[name="branch_current_model"]')[0].scrollIntoView(false);
              }
          }
      }
    };
    var obj = new Zapatec.Tree(opt);
}

/**
 * Redirects to desired module by choosing it from the dropdown
 * there is an option for LOGOUT, that pops up confirm message
 *
 * @params element - DOM field object
 * @params current_option - Current option, that will be back if next option is LOGOUT
 */
function quickMenuAction(element, current_option) {
    var selected_index = element.selectedIndex;
    current_option = 'menu_opt_' + current_option;
    var current_index = 'index::frontend';
    if ($(current_option)) {
        current_index = $(current_option).index;
    }

    if (element.options[selected_index].value.match(/logout/)) {
        confirm_msg = (env && env.locking_records) ? 'confirm_logout_unlock' : 'confirm_logout';
        if (confirm(i18n.messages[confirm_msg])) {
            redirect(element.options[selected_index].value);
        } else {
            element.options[current_index].selected = true;
        }
    } else {
        redirect(element.options[selected_index].value);
    }
}

/**
 * MANAGE the COUNTER formula syntax
 *
 * @params element - DOM field object
 */
function manageCounterFormula(element) {
    var frm = element.form;

    var formula_options = frm.elements['formula_options[]'];
    var formula = $('formula').value;

    var delimiters = ['/', '.', '-', '|', ','];
    var previous_delimiter = '';

    if (formula) {
        for (var i = 0; i < delimiters.length; i++) {
            var re = RegExp('\\' + delimiters[i]);
            if (formula.match(re)) {
                previous_delimiter = delimiters[i];
                break;
            }
        }
        if (previous_delimiter && $('delimiter').value != previous_delimiter) {
            var re = new RegExp("\\" + previous_delimiter, 'g');
            formula = formula.replace(re, $('delimiter').value);
            $('formula').value = formula;
        }
    }

    if (element.id == 'delimiter' && $('delimiter').value) {
        if (!previous_delimiter) {
            formula = formula.replace(/\]\[/g, ']' + $('delimiter').value + '[');
            $('formula').value = formula;
        }
        return;
    }

    var items_used = 0;
    for (var i = 0; i < formula_options.length; i++) {
        var formula_option = formula_options[i];

        if (formula_option.checked) {
            items_used++;
            if (items_used > 5) {
                formula_option.checked = false;
                alert(i18n.messages['alert_counter_formula_options_exceeded']);
                return;
            }
        }
    }

    if (element.checked) {
        if (formula) {
            formula += $('delimiter').value;
        }
        formula += element.value;
    } else {
        var regex = element.value;
        regex = regex.replace(/\[/, '\\[');
        regex = regex.replace(/\]/, '\\]');
        if ($('delimiter').value) {
            regex = '\\' + $('delimiter').value + '*' + regex;
        }
        var re = new RegExp(regex);
        formula = formula.replace(re, '');
    }

    removeClass($('formula'), 'distinctive');
    addClass($('formula'), 'distinctive');


    $('formula').value = formula;
}

/**
 * Shows/hides counter rows when selecting a counter for the model type
 *
 * @param {Object} element - the select DOM element
 */
function typeSelectCounter(element) {
    //This var is used to check what was the previous value of the select element
    var value_prev = '';
    // get initial value after unsuccessful save, for example
    var element_prev = $(element.id + '_prev');
    if (element_prev) {
        value_prev = element_prev.value;
    }

    // If the value of the select element is changed
    if (value_prev !== element.value) {
        // If the current value is 'add_counter' or the previous value was 'add_counter'
        if (element.value == 'add_counter' || value_prev == 'add_counter') {
            $$('.section_add_counter').each(function(row) {
                if (element.value == 'add_counter') {
                    removeClass(row, 'hidden');
                } else{
                    addClass(row, 'hidden');
                }
            });
        }

        // Write the current value as previous for further use
        value_prev = element.value;
        if (element_prev) {
            element_prev.value = value_prev;
        }
    }

    return true;
}

/**
 * DISABLe or ACTIVATE all cheboxes
 *
 * @param element - DOM object
 */
function disableActivateAll(element) {
    var base = element.id;
    base = base.replace(/_all/, '_val');
    var active = $(base);
    //for the date and datetime fields
    var date_field_name = base + '_formatted';

    if (active) {
        if (element.checked) {
            active.disabled = false;
            active.removeClassName('input_inactive');
            if ($(date_field_name)) {
                $(date_field_name).disabled = false;
                $(date_field_name).removeClassName('input_inactive');
            }
        } else {
            active.disabled = true;
            active.addClassName('input_inactive');
            if ($(date_field_name)) {
                $(date_field_name).disabled = true;
                $(date_field_name).addClassName('input_inactive');
            }
        }
    } else if ($(base + '_0')) {
        // radio
        $$('input[type="radio"][name="' + $(base + '_0').name + '"]').each(function(r) {
            if (element.checked) {
                r.disabled = false;
                r.removeClassName('input_inactive');
            } else {
                r.disabled = true;
                r.addClassName('input_inactive');
            }
        });
    } else {
        // if the field is an autocompleter for customers, projects or trademarks
        var input_name = base.replace(/_val/, '');
        if (input_name == 'customer' || input_name == 'project' || input_name == 'trademark' || input_name == 'employee') {
            var autocompleter = $(input_name);
            var autocompleter_autocomplete = $(input_name + '_autocomplete');
            if (element.checked) {
                autocompleter.disabled = false;
                autocompleter_autocomplete.disabled = false;
                autocompleter_autocomplete.removeClassName('input_inactive');
            } else {
                autocompleter.disabled = true;
                autocompleter_autocomplete.disabled = true;
                autocompleter_autocomplete.addClassName('input_inactive');
            }
        }
    }
}

/**
 * SELECTs all checkboxes
 *
 * @param element - DOM object
 */
function selectAll(element) {
    var frm = element.form;
    var checkboxes = element.parentNode.parentNode.getElementsByTagName('input');
    if (checkboxes) {
        for (var i = 0; i < checkboxes.length; i++) {
            var checkbox = checkboxes[i];
            if (checkbox.id != element.id) {
                checkbox.checked = element.checked;
                disableActivateAll(checkbox);
            }
        }
    }
}

/**
 * Change phases dropdown of project
 *
 * @param {number} p_id - project id
 */
function changePhases(p_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var target = document,
        select_obj = $('phase');

    // if no project id (clearing autocompleter)
    if (!p_id) {
        select_obj.options.length = 0;
        var opt = target.createElement('OPTION');
        opt.text = '[' + i18n['labels']['project_phase'] + ']';
        opt.value = '';
        opt.defaultSelected = false;
        opt.selected = false;
        select_obj.options[0] = opt;
        addClass(select_obj, 'missing_records');
        Effect.Fade('loading');
        return;
    }

    var opt = {
        asynchronous: false,
        //method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            var opt = target.createElement('OPTION');
            opt.text = '[' + i18n['labels']['project_phase'] + ']';
            opt.value = '';
            opt.defaultSelected = false;
            opt.selected = false;
            addClass(opt, 'undefined');
            select_obj.options[0] = opt;

            if (result.length > 0) {
                removeClass(select_obj, 'missing_records');
            } else {
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                var opt = target.createElement('OPTION');
                opt.text = result[j]['label'];
                opt.value = result[j]['option_value'];
                opt.defaultSelected = false;
                opt.selected = false;
                select_obj.options[j+1] = opt;
            }
            toggleUndefined(select_obj);
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(env.base_url + '?' + env.module_param + '=projects&projects=ajax_phases&p_id=' + p_id, opt);
}

/**
 * Function to handle with checkboxes set as readonly
 */
function deselectCheckboxes(element) {
    element.checked = !element.checked;
}

/**
 * TO BE REMOVED!!!
 * Function to handle with documents' section menu
 */
function showDocumentsSectionSubMenu(element) {
    var element_id = element.id;
    var sub_menu_id = element_id + '_sub_menu';
    var menu_label_id = element_id + '_main';
    var menu_label = $(menu_label_id);
    var sub_menu = $(sub_menu_id);
    var sub_menus = element.parentNode.parentNode.getElementsByTagName('ul');
    var sub_menu_buttons = element.parentNode.parentNode.getElementsByTagName('div');
    if (sub_menus) {
        for (var i = 0; i < sub_menus.length; i++) {
            var current_sub_menu = sub_menus[i];
            var current_button = sub_menu_buttons[i];
            if (current_sub_menu.id != sub_menu.id) {
                current_sub_menu.style.display = 'none';
                removeClass(current_button, 'm_header_menu_sub_menu_arrow_up');
                addClass(current_button, 'm_header_menu_sub_menu_arrow');
            }
        }
    }
    if (sub_menu.style.display == 'block') {
        sub_menu.style.display = 'none';
        removeClass(element, 'm_header_menu_sub_menu_arrow_up');
        addClass(element, 'm_header_menu_sub_menu_arrow');
    } else {
        sub_menu.style.display = 'block';
        removeClass(element, 'm_header_menu_sub_menu_arrow');
        addClass(element, 'm_header_menu_sub_menu_arrow_up');
    }
    sub_menu.style.position = 'absolute';
    Position.clone(menu_label, sub_menu, {
        setHeight: false,
        offsetTop: menu_label.offsetHeight
    });
    sub_menu.style.width = '200px';
}

/**
 * TO BE REMOVED!!!
 * Function to handle with radio buttons set as readonly
 */
function diselectRadioButtons(element) {
    var radio_buttons = document.getElementsByName(element.name);

    if (radio_buttons) {
        for (var i = 0; i < radio_buttons.length; i++) {
            var current_radio_button = radio_buttons[i];
            if (current_radio_button.checked) {
                current_radio_button.checked = false;
            }
            if (current_radio_button.defaultChecked) {
                current_radio_button.checked = true;
            }
        }
    }
}

/**
 * DROPDOWNs relations for department change
 *
 * @param s_id - id of the object
 */
function changeAssigningOptions(element, s_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj = $(s_id);
    if (select_obj.type == 'hidden') {
        select_obj.value = 0;
        Effect.Fade('loading');
        return;
    }
    var department_id = element.value;
    var opt = {
        asynchronous: true,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            if (result.length > 0) {
                select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj.options[0], 'undefined');
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j+1] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(env.base_url + '?' + env.module_param + '=customers&customers=ajax_fill_users_department_options&department=' + department_id, opt);
}

/**
 * DROPDOWNs relations for configurator change
 *
 * @param s_id - id of the object
 */
function changeAddOtions(element, s_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    form1 = $(element).form;
    var type_id = element.value;
    var user_string = env.module_name == 'users' && env.action_name == 'mynzoom' ? '&user_id=' + $('id').value : '';
    var opt = {
        asynchronous: true,
        method: 'get',
        // parameters: Form.serialize(form1),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(s_id).innerHTML = result;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(env.base_url + '?' + env.module_param + '=tasks&tasks=ajax_fill_configurator_options&type=' + type_id + user_string, opt);
}

/**
 * Change dropdowns for branches and contact persons of customer (basic variable)
 *
 * @param {number} c_id - customer's id
 * @param {bool} is_company - if it is company or not
 */
function changeCustomerBranchAndContact(c_id, is_company) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var branch_container = $('branch_container'),
        contact_person_container = $('contact_person_container'),
        select_obj_branch = $('branch'),
        select_obj_contact = $('contact_person');

    if (branch_container && contact_person_container) {
        if (is_company != 0) {
            if (branch_container.style.display == 'none') {
                branch_container.style.display = 'inline';
            };
            if (contact_person_container.style.display == 'none' && select_obj_contact.tagName == 'SELECT') {
                contact_person_container.style.display = 'inline';
            };
        } else {
            if (branch_container.style.display == 'inline') {
                branch_container.style.display = 'none';
            };
            if (contact_person_container.style.display == 'inline') {
                contact_person_container.style.display = 'none';
            };
            // clear dropdowns and return
            if (select_obj_branch.tagName == 'SELECT') {
                select_obj_branch.options.length = 0;
                // set a blank option because otherwise field is not submitted
                select_obj_branch.options[0] = document.createElement('OPTION');
            } else {
                select_obj_branch.value = '';
            }
            if (select_obj_contact.tagName == 'SELECT') {
                select_obj_contact.options.length = 0;
                select_obj_contact.options[0] = document.createElement('OPTION');
                toggleUndefined(select_obj_contact);
            } else {
                select_obj_contact.value = '';
            }
            Effect.Fade('loading');
            return;
        }
    }

    var opt = {
        asynchronous: false,
        //method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            var branches = result.branches;
            var contacts = result.contact_person;

            var target = document;

            // If this is a dropdown
            if (select_obj_branch.tagName == 'SELECT') {
                select_obj_branch.options.length = 0;
                //sets the options to branches dropdown
                if (branches) {
                    removeClass(select_obj_branch, 'missing_records');
                    for (var j = 0; j < branches.length; j++) {
                        var opt = target.createElement('OPTION');
                        opt.text = branches[j]['label'];
                        opt.value = branches[j]['option_value'];
                        opt.defaultSelected = false;
                        opt.selected = false;
                        select_obj_branch.options[j] = opt;
                    }
                } else {
                    var opt = target.createElement('OPTION');
                    opt.text = i18n['labels']['empty_branch'];
                    opt.value = '';
                    opt.defaultSelected = false;
                    opt.selected = false;
                    select_obj_branch.options[0] = opt;
                    addClass(select_obj_branch, 'missing_records');
                }
            } else {
                if (branches) {
                    // Use the first option
                    select_obj_branch.value = branches[0]['option_value'];
                } else {
                    // Clear the value
                    select_obj_branch.value = '';
                }
                // visible text
                select_obj_branch.parentNode.select('span.branch').each(function(el) {
                    el.innerHTML = branches.length ? branches[0].label : '';
                });
            }
            select_obj_branch.title = result.branch_label || '';

            if (branch_container) {
                // help popup
                var help_spans = $$('#' + branch_container.id + ' span.help');
                var onmouseover = 'return overlib(\'' + result.branch_help_text + '\', CAPTION, \'' + result.branch_label + '\');';
                for (var k = 0; k < help_spans.length; k++) {
                    eval('help_spans[k].onmouseover = function() { ' + onmouseover + ' }');
                    help_spans[k].setAttribute('onMouseOver', onmouseover);
                }
                // help label
                $$('#' + branch_container.id + ' span.labelbox').each(function(el) {
                    el.innerHTML = result.branch_label + ':';
                });
            }

            // If this is a dropdown
            if (select_obj_contact.tagName == 'SELECT') {
                //sets the options to contact person's dropdown
                select_obj_contact.options.length = 0;
                if (contacts && contacts.length > 0) {
                    var opt = target.createElement('OPTION');
                    opt.text = i18n['labels']['please_select'];
                    opt.value = '';
                    opt.defaultSelected = false;
                    opt.selected = false;
                    addClass(opt, 'undefined');
                    select_obj_contact.options[0] = opt;
                    removeClass(select_obj_contact, 'missing_records');
                    for (var p = 0; p < contacts.length; p++) {
                        var opt = target.createElement('OPTION');
                        opt.text = contacts[p]['label'];
                        opt.value = contacts[p]['option_value'];
                        opt.defaultSelected = false;
                        opt.selected = false;
                        select_obj_contact.options[p+1] = opt;
                    }
                } else {
                    var opt = target.createElement('OPTION');
                    opt.text = i18n['labels']['empty_contact_person'];
                    opt.value = '';
                    opt.defaultSelected = false;
                    opt.selected = false;
                    select_obj_contact.options[0] = opt;
                    addClass(select_obj_contact, 'missing_records');
                }
                toggleUndefined(select_obj_contact);
            } else {
                // Clear the value
                select_obj_contact.value = '';

                if (contact_person_container) {
                    // visible text
                    contact_person_container.select('span.contact_person').each(function(el) {
                        el.innerHTML = '';
                    });
                    // hide container
                    contact_person_container.style.display = 'none';
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_branches&customer_id=' + c_id +
        ($('model_lang') ? '&model_lang=' + $('model_lang').value : '');

    new Ajax.Request(url, opt);
}

/**
 * Gets all contact persons for a certain customer (execute_after method)
 *
 * @param {Object} autocomplete - autocomplete settings
 * @param {Object data - autocompleter returned data
 */
function getCustomerContactPersons(autocomplete, data) {
    if (!autocomplete.execute_after_params.contact_person_field) {
        return false;
    }

    var index = '';
    if (data.row) {
        index = '_' + data.row;
    }

    var select_obj_contact = $(autocomplete.execute_after_params.contact_person_field + index);
    if (!select_obj_contact) {
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        asynchronous: false,
        //method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            var contacts = result.contact_persons;

            //sets the options to contact person's dropdown
            select_obj_contact.options.length = 0;
            if (contacts && contacts.length > 0) {
                select_obj_contact.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj_contact.options[0], 'undefined');
                removeClass(select_obj_contact, 'missing_records');
                for (var p = 0; p < contacts.length; p++) {
                    select_obj_contact.options[p+1] = new Option(contacts[p]['label'], contacts[p]['option_value'], false, false);
                }
            } else {
                select_obj_contact.options[0] = new Option(i18n['labels']['empty_contact_person'], '', false, false);
                addClass(select_obj_contact, 'missing_records');
            }
            toggleUndefined(select_obj_contact);
            if (select_obj_contact.getAttribute('onchange')) {
                select_obj_contact.onchange();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    if (data.id && data.id != 'undefined') {
        // if customer is person then customer name should be the only option in the dropdown
        if (data.is_company == '0' && Object.isArray(autocomplete.fill_options)) {
            var person_name = '';
            for (var i = 0; i < autocomplete.fill_options.length; i++) {
                if (autocomplete.fill_options[i].match(/^(\$.+)\s*=>\s*<name>\s*<lastname>\s*$/)) {
                    var pn_key = autocomplete.fill_options[i].replace(/^(\$.+)\s*=>\s*<name>\s*<lastname>\s*$/, '$1').replace(/\s+$/, '');
                    if (typeof(data[pn_key]) != 'undefined') {
                        person_name = data[pn_key];
                    }
                    break;
                }
            }
            // if person name is found from autocomplete data, use it
            if (person_name) {
                select_obj_contact.options.length = 0;
                select_obj_contact.options[0] = new Option(person_name, data.id, false, true);
                removeClass(select_obj_contact, 'missing_records');
                toggleUndefined(select_obj_contact);
                if (select_obj_contact.getAttribute('onchange')) {
                    select_obj_contact.onchange();
                }
                Effect.Fade('loading');
                return true;
            }
        }
        var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_contact_persons&customer_id=' + data.id;
        if ($('model_lang') != null) {
            url += '&model_lang=' + $('model_lang').value;
        }
        new Ajax.Request(url, opt);
    } else {
        //clear AC
        select_obj_contact.options.length = 0;
        select_obj_contact.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
        addClass(select_obj_contact, 'missing_records');
        toggleUndefined(select_obj_contact);
        if (select_obj_contact.getAttribute('onchange')) {
            select_obj_contact.onchange();
        }
        Effect.Fade('loading');
        return true;
    }
}

/**
 * Gets branches for a certain customer (execute_after method)
 *
 * @param object autocomplete settings
 * @param object data autocompleter returned data
 */
function getCustomerBranches(autocomplete, data) {
    if (!autocomplete.execute_after_params.branches_field) {
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var index = '';
    if (data.row) {
        index = '_' + data.row;
    }

    var select_obj_branches = $(autocomplete.execute_after_params.branches_field + index);

    var opt = {
        asynchronous: false,
        //method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            var branches = result.branches;

            //sets the options to branches' dropdown
            select_obj_branches.options.length = 0;
            if (branches && branches.length > 0) {
                select_obj_branches.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj_branches.options[0], 'undefined');
                removeClass(select_obj_branches, 'missing_records');
                for (var p = 0; p < branches.length; p++) {
                    select_obj_branches.options[p+1] = new Option(branches[p]['label'], branches[p]['option_value'], false, false);
                }
            } else {
                select_obj_branches.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj_branches, 'missing_records');
            }
            toggleUndefined(select_obj_branches);
            if (select_obj_branches.getAttribute('onchange')) {
                select_obj_branches.onchange();
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    if (data.id && data.id != 'undefined' && data.is_company == '1') {
        var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_branches&customer_id=' + data.id;
        if ($('model_lang') != null) {
            url += '&model_lang=' + $('model_lang').value;
        }
        new Ajax.Request(url, opt);
    } else {
        //clear AC
        select_obj_branches.options.length = 0;
        select_obj_branches.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
        addClass(select_obj_branches, 'missing_records');
        if (select_obj_branches.getAttribute('onchange')) {
            select_obj_branches.onchange();
        }
        Effect.Fade('loading');
        return true;
    }
}

/**
 * DROPDOWNs relations for contact persons change
 *
 * @param {Object} element - branch dropdown
 * @param {string} dropdown_id - name of the object
 */
function changeContactPersonsOptions(element, dropdown_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj = $(dropdown_id);
    var branch_id = element.value;
    var opt = {
        asynchronous: true,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            if (result.length > 0) {
                select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj.options[0], 'undefined');
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['empty_contact_person'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j+1] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_fill_contact_persons_options&branch_id=' + branch_id;
    if ($('model_lang') != null) {
        url += '&model_lang=' + $('model_lang').value;
    }
    new Ajax.Request(url, opt);
    if ($('customers_info_branch_name') && $('customers_info_contact_person') && $('customers_info_contacts')) {
        updateBranchesInfo(branch_id);
    }
}

/**
 * AJAX functionality for showing and updating side panel for customer's info
 * in models with customer
 *
 * @param {number} customer_id - id of customer of model
 */
function showCustomersInfo(customer_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var div_id = $('content_side_panel_customers_info'),
        title_div_id = $('title_side_panel_customers_info');

    title_div_id.innerHTML = title_div_id.innerHTML.replace(/(customers=view&amp;view=)\d+/g, '$1' + customer_id);

    // if no customer id (clearing autocompleter)
    if (!customer_id) {
        div_id.innerHTML = '';
        addClass(div_id.parentNode, 'hidden');
        Effect.Fade('loading');
        return;
    }

    var opt = {
        asynchronous: false,
        method: 'post',
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            div_id.innerHTML = result;
            if (div_id.innerHTML) {
                removeClass(div_id.parentNode, 'hidden');
            } else {
                addClass(div_id.parentNode, 'hidden');
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.module_name + '=ajax_show_customers_info&customer_id=' + customer_id;
    if ($('model_lang') != null) {
        url += '&model_lang=' + $('model_lang').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality for showing and updating side panel for last five records
 * having same customer and type as current model.
 *
 * @param {Object} data - autocomplete data for customer or a blank object when clearing
 */
function showLastRecords(data) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var customer_id = data.id || '';

    // update url in panel title
    $('title_side_panel_last_records').innerHTML =
        $('title_side_panel_last_records').innerHTML.replace(/(values\[1\]=).*(&amp;values_autocomplete\[1\]=).*(&amp;sort)/g,
                '$1' + customer_id + '$2' + encodeURIComponent(data['$customer_autocomplete'] || '') + '$3');

    var div_id = $('content_side_panel_last_records');

    // if no customer id (clearing autocompleter)
    if (!customer_id) {
        div_id.innerHTML = '';
        addClass(div_id.parentNode, 'hidden');
        Effect.Fade('loading');
        return;
    }

    var opt = {
        asynchronous: false,
        method: 'post',
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            div_id.innerHTML = result;
            if (div_id.innerHTML) {
                removeClass(div_id.parentNode, 'hidden');
            } else {
                addClass(div_id.parentNode, 'hidden');
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.module_name + '=ajax_show_last_records&customer_id=' + customer_id + '&model_type=' + $('side_panel_model_type').value;
    if ($('model_lang') != null) {
        url += '&model_lang=' + $('model_lang').value;
    }

    new Ajax.Request(url, opt);
}

/**
 * Change dropdown branches and contact persons
 *
 * @param {string} branch_id - branch's id
 */
function updateBranchesInfo(branch_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        asynchronous: true,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            $('customers_info_branch_name').innerHTML = result.branch_name || '';
            $('customers_info_branch_address').innerHTML = result.branch_address || '';
            $('customers_info_contact_person').innerHTML = result.contact_person_name || '';
            $('customers_info_contacts').innerHTML = result.contacts_template || '';

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_update_customers_info_panel&branch_id=' + branch_id;
    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }
    new Ajax.Request(url, opt);
}

/**
 * Change dropdown with contracts depending on the selected customer
 *
 * @param {number} c_id - customer's id
 */
function changeCustomersContracts(c_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var tm_id = $('trademark') ? $('trademark').value : 0,
        target = document,
        contract_element = $('contract'),
        contract_container = $('contract_container');

    if (contract_element.type == 'hidden') {
        contract_element.value = 0;
        if (contract_container) {
            contract_container.innerHTML = '';
        }
    } else if (!c_id) {
        // if no customer id (clearing autocopleter)
        contract_element.options.length = 0;
        var opt = target.createElement('OPTION');
        opt.text = i18n['labels']['no_select_records'];
        opt.value = '';
        opt.defaultSelected = false;
        opt.selected = false;
        contract_element.options[0] = opt;
        addClass(contract_element, 'missing_records');
        return;
    } else {
        var opt = {
            asynchronous: false,
            //method: 'post',
            onSuccess: function(t) {
                var result = eval('(' + t.responseText + ')');
                if (!checkAjaxResponse(result)) {
                    return;
                }
                var contracts = result;

                contract_element.options.length = 0;
                //sets the options to contracts dropdown

                if (contracts.length > 0) {
                    removeClass(contract_element, 'missing_records');
                    var opt = target.createElement('OPTION');
                    opt.text = i18n['labels']['please_select'];
                    opt.value = '';
                    opt.defaultSelected = false;
                    opt.selected = false;
                    addClass(opt, 'undefined');
                    contract_element.options[0] = opt;
                    for (var j = 0; j < contracts.length; j++) {
                        var opt = target.createElement('OPTION');
                        opt.text = contracts[j]['label'];
                        opt.value = contracts[j]['option_value'];
                        opt.defaultSelected = false;
                        opt.selected = false;
                        contract_element.options[j+1] = opt;
                    }
                } else {
                    var opt = target.createElement('OPTION');
                    opt.text = i18n['labels']['no_select_records'];
                    opt.value = '';
                    opt.defaultSelected = false;
                    opt.selected = false;
                    contract_element.options[0] = opt;
                    addClass(contract_element, 'missing_records');
                }
                toggleUndefined(contract_element);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=documents&documents=ajax_customers_contracts&customer_id=' + c_id + '&trademark_id=' + tm_id;
        if ($('model_lang') != null) {
            url += '&model_lang=' + $('model_lang').value;
        }

        new Ajax.Request(url, opt);
    }
}

/**
 * Sets new options to a dropdown element, keeps selected value if present in new options
 *
 * @param {Object} element - SELECT DOM element
 * @param {Object} options - dropdown options
 * @param {bool} required - if field is required or not
 */
function setDropdownOptions(element, options, required) {
    if (element != null && element.tagName == 'SELECT') {
        if (!options.length) {
            element.innerHTML = '';
            toggleUndefined(element);
            var option = new Option(i18n['labels']['no_select_records'], '', false, false);
            option.innerHTML = i18n['labels']['no_select_records'];
            addClass(option, 'missing_records');
            element.appendChild(option);
            addClass(element, 'missing_records');
        } else {
            var old_value = element.selectedIndex > -1 ? element[element.selectedIndex].value : '';
            element.options.length = 0;
            var has_first_option = 0;
            if (options.length > 1 || !(required || false)) {
                element.options[0] = new Option(i18n['labels']['please_select'], '', false, !old_value);
                addClass(element.options[0], 'undefined');
                has_first_option = 1;
            }
            for (var j = 0; j < options.length; j++) {
                element.options[j+has_first_option] = new Option(options[j]['label'], options[j]['option_value'], false, old_value == options[j]['option_value']);
            }
            removeClass(element, 'missing_records');
            toggleUndefined(element);
        }
    }
};

/**
 * Change pattern model types in function of the model
 *
 * @param radio - the radio button clicked
 */
function changePatternsTypes(radio) {

    radio = $(radio);
    var doc  = $('model_type_documents');
    var fin = $('model_type_finance');
    if (radio.id.match(/model_documents/)) {
        doc.style.display = '';
        doc.disabled = false;
        fin.style.display = 'none';
        fin.disabled = true;
    } else {
        fin.style.display = '';
        fin.disabled = false;
        doc.style.display = 'none';
        doc.disabled = true;
    }
}

/**
 * Toggles the messages container to show the maximum count of messages
 * and hides the container to the maximum allowed count of messages
 *
 * @param string type - the type of message (message, error, warning)
 * @param integer items - the total count of messages
 * @param integer max_shown - the max allowed messages to show before expanding the container
 * @return bool - the result of the operation
 */
function toggleMessages(type, items, max_shown) {
    var div = $(type + '_container');
    if (div != null) {
        if (div.style.display == 'none') {
            div.style.display = '';
            $('liToggleAll').style.display = 'none';
            $('liToggleMax').style.display = '';
        } else {
            div.style.display = 'none';
            $('liToggleAll').style.display = '';
            $('liToggleMax').style.display = 'none';
        }
    } else {
        return false;
    }

    return true;
}

/******************************** TIMESHEETS ********************************/

/**
 * Toggles tasks timesheets period/dates for input of time or exact dates
 *
 * @param element - the switch element that launches the toggle
 */
function toggleTimesheetPeriod(element) {
    mode = element.id;

    //show/hide dates cells
    $('startperiod_period').parentNode.parentNode.style.display = (mode == 'period') ? '' : 'none';
    $('startperiod_period_formatted').style.display = (mode == 'period') ? '' : 'none';
    $('endperiod_period').parentNode.parentNode.style.display = (mode == 'period') ? '' : 'none';
    $('endperiod_period_formatted').style.display = (mode == 'period') ? '' : 'none';
    $('duration').parentNode.parentNode.style.display = (mode == 'period') ? '' : 'none';
    $('duration').style.display = (mode == 'period') ? '' : 'none';

    $('startperiod_dates').parentNode.parentNode.style.display = (mode == 'period') ? 'none' : '';
    $('startperiod_dates_formatted').style.display = (mode == 'period') ? 'none' : '';
    $('endperiod_dates').parentNode.parentNode.style.display = (mode == 'period') ? 'none' : '';
    $('endperiod_dates_formatted').style.display = (mode == 'period') ? 'none' : '';

    $('period_type').value = mode;

    if (mode == 'period') {
        addClass($('period').parentNode, 'selected');
        removeClass($('dates').parentNode, 'selected');
    } else {
        removeClass($('period').parentNode, 'selected');
        addClass($('dates').parentNode, 'selected');
    }
}

/**
 * Calculates the duration and endperiod and the dates elements of the timesheet
 *
 * @param element - the element that requires manipulation
 */
function processTimesheetPeriod(element) {

    if (element.id == 'week') {
        //when the week is changed clear the start date
        $('startperiod').value = '';
    } else if (element.id == 'duration') {
        //when the duration is changed update the end date
        if ($('startperiod') && $('startperiod').value) {
            //get the start date
            var date_time_start = $('startperiod').value.split(' ');
            var date_start = date_time_start[0].split('-');
            var time_start = date_time_start[1].split(':');
            //the months begin from 0, that is why we subtract 1 from the month number
            var start_period = new Date(parseInt(date_start[0].replace(/^0/, '')),
                                        parseInt(date_start[1].replace(/^0/, ''))-1,
                                        parseInt(date_start[2].replace(/^0/, '')),
                                        parseInt(time_start[0].replace(/^0/, '')),
                                        parseInt(time_start[1].replace(/^0/, '')),
                                        0);

            //update the end period
            var end_period = new Date(start_period.getTime() + 1000*60*$('duration').value);
            $('endperiod_formatted').value = end_period.print("%d.%m.%Y, %H:%M");
            $('endperiod').value = end_period.print("%Y-%m-%d %H:%M:00");

        }
    } else if (element.id.match(/^zapatec/) && this.inputField.id == 'startperiod_formatted') {
        formatDate(element);

        //define the mode
        var mode = $('period_type').value;

        if (mode == 'period') {
            //update the end date
            if ($('duration') && $('duration').value) {
                var end_period = new Date(start_period.getTime() + 1000*60*$('duration').value);
                $('endperiod_formatted').value = end_period.print(element.dateFormat);
                $('endperiod').value = end_period.print("%Y-%m-%d %H:%M:00");
            }
        } else {
            //update the duration
            if ($('endperiod') && $('endperiod').value) {
                //get the start date from the calendar
                //var start_period = new Date(element.currentDate);
                //start_period.setSeconds(0);

                //get the start date with parsing
                var date_time_start = $('startperiod').value.split(' ');
                var date_start = date_time_start[0].split('-');
                var time_start = date_time_start[1].split(':');
                //the months begin from 0, that is why we subtract 1 from the month number
                var start_period = new Date(parseInt(date_start[0].replace(/^0/, '')),
                                            parseInt(date_start[1].replace(/^0/, ''))-1,
                                            parseInt(date_start[2].replace(/^0/, '')),
                                            parseInt(time_start[0].replace(/^0/, '')),
                                            parseInt(time_start[1].replace(/^0/, '')), 0);

                //get the end date with parsing
                var date_time_end = $('endperiod').value.split(' ');
                var date_end = date_time_end[0].split('-');
                var time_end = date_time_end[1].split(':');
                //the months begin from 0, that is why we subtract 1 from the month number
                var end_period = new Date(parseInt(date_end[0].replace(/^0/, '')),
                                          parseInt(date_end[1].replace(/^0/, ''))-1,
                                          parseInt(date_end[2].replace(/^0/, '')),
                                          parseInt(time_end[0].replace(/^0/, '')),
                                          parseInt(time_end[1].replace(/^0/, '')),
                                          0);

                var duration = end_period.getTime() - start_period.getTime();
                if (duration >= 0) {
                    //the timestamp is in milliseconds to convert it to minutes divide by 60*1000
                    $('duration').value = duration/60000;
                } else {
                    $('duration').value = '';
                }
            }
        }

        //update the week number
        var start_period = new Date(element.currentDate);
        if ($('week')) {
            $('week').value = start_period.getWeekNumber();
        }
    } else if (element.id.match(/^zapatec/) && this.inputField.id == 'endperiod_formatted') {
        formatDate(element);

        //update the duration
        if ($('startperiod') && $('startperiod').value) {
            //get the start date with parsing
            var date_time_start = $('startperiod').value.split(' ');
            var date_start = date_time_start[0].split('-');
            var time_start = date_time_start[1].split(':');

            //the months begin from 0, that is why we subtract 1 from the month number
            var start_period = new Date(parseInt(date_start[0].replace(/^0/, '')),
                                        parseInt(date_start[1].replace(/^0/, ''))-1,
                                        parseInt(date_start[2].replace(/^0/, '')),
                                        parseInt(time_start[0].replace(/^0/, '')),
                                        parseInt(time_start[1].replace(/^0/, '')),
                                        0);

            //get the end date from the calendar
            //var end_period = new Date(element.currentDate);
            //end_period.setSeconds(0);

            //get the start date with parsing
            var date_time_end = $('endperiod').value.split(' ');
            var date_end = date_time_end[0].split('-');
            var time_end = date_time_end[1].split(':');
            //the months begin from 0, that is why we subtract 1 from the month number
            var end_period = new Date(parseInt(date_end[0].replace(/^0/, '')),
                                      parseInt(date_end[1].replace(/^0/, ''))-1,
                                      parseInt(date_end[2].replace(/^0/, '')),
                                      parseInt(time_end[0].replace(/^0/, '')),
                                      parseInt(time_end[1].replace(/^0/, '')),
                                      0);

            //calculate the duration
            var duration = end_period.getTime() - start_period.getTime();
            if (duration >= 0) {
                //the timestamp is in milliseconds to convert it to minutes divide by 60*1000
                $('duration').value = duration/60000;
            } else {
                $('duration').value = '';
            }
        }
    }
}

/**
 * AJAX functionality that manages saved configurations of timesheets
 *
 * @param {Object} config_field - config combo field (its value could be id or name of the saved configuration)
 * @param {string} div_id - the id of the DIV to load the result into
 * @param {string} config_action - load, save or delete
 */
function manageConfigTimesheet(config_field, div_id, config_action) {
    // id or name of the saved configuration
    var config_id = config_field.value;
    var form = config_field.form;
    if (config_action != 'save') {
        config_id = parseInt(config_id);
        if (isNaN(config_id)) {
            config_id = 0;
        }
    }
    if (config_action == 'save' && isNaN(parseInt(config_id)) && config_id == '' ||
        config_action != 'save' && !config_id || !form) {
        alert(i18n['messages'][config_action == 'save' ? 'error_config_not_selected' : 'error_config_not_present']);
        return false;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            $(div_id).innerHTML = result;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_configurator&config_action=' + config_action;

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality SAVES the timesheet
 */
function saveTimesheet(form, div_id) {
    // Prepare var for the error messages
    var error_message = [];

    // Prepare vars for the dates
    var start_date = '';
    var end_date   = '';
    var startperiod_date = '';
    var endperiod_date   = '';

    // Get the current date formatted into the standard javascript date format
    var currentDate = new Date();
    currentDate.setDate(currentDate.getDate()+1);
    currentDate.setHours(0);
    currentDate.setMinutes(0);
    currentDate.setSeconds(0);

    // Get the dates depending on the period type
    if ($('period_type').value == 'dates') {
        if ($('startperiod_dates') && $('startperiod_dates').value) {
            start_date = $('startperiod_dates').value;
        }
        if ($('endperiod_dates') && $('endperiod_dates').value) {
            end_date = $('endperiod_dates').value;
        }
    } else {
        if ($('startperiod_period') && $('startperiod_period').value) {
            start_date = $('startperiod_period').value;
        }
        if ($('endperiod_period') && $('endperiod_period').value) {
            end_date = $('endperiod_period').value;
        }
    }

    // If there`s a value for the start period
    if (start_date) {
        // Get the start period formatted into the standard javascript date format
        startperiod_date = parseISOformatDate(start_date);
        // If start date is after the current date (i.e. is in the future)
        if (startperiod_date >= currentDate) {
            // Record an error message
            error_message.push(i18n.messages['error_start_date_after_current']);
            // Empty the start period
            startperiod_date = '';
        }
    } else {
        // Record an error message
        error_message.push(i18n.messages['error_start_date_after_current']);
    }

    // If there`s a value for the end period
    if (end_date) {
        // Get the end period formatted into the standard javascript date format
        endperiod_date = parseISOformatDate(end_date);
        // If end date is after the current date (i.e. is in the future)
        if (endperiod_date >= currentDate) {
            // Record an error message
            error_message.push(i18n.messages['error_finish_date_after_current']);
            // Empty the end period
            endperiod_date = '';
        }
    } else {
        // Record an error message
        error_message.push(i18n.messages['error_finish_date_after_current']);
    }

    // If there are valid start and end periods
    if (startperiod_date != '' && endperiod_date != '') {
        // If the period type is "dates" and the start date (without the hours) is NOT equal to the end date
        if ($('period_type').value == 'dates' && startperiod_date.format('Y-m-d') != endperiod_date.format('Y-m-d')) {
            // Record an error message
            error_message.push(i18n.messages['error_timesheets_startperiod_endperiod_invalid']);
        } else if (endperiod_date < startperiod_date) {
            // If the end date is before the start date
            // Record an error message
            error_message.push(i18n.messages['error_timesheets_startperiod_endperiod']);
        }
    }

    // If the period type is "period" and the duration is empty, negative or 0
    if ($('period_type').value == 'period' && ($('duration').value == '' || $('duration').value <= 0)) {
        // Record an error message
        error_message.push(i18n.messages['error_valid_duration']);
    }

    // If there`s no value for the content
    if ($('content').value == '') {
        // Record an error message
        error_message.push(i18n.messages['error_valid_content']);
    }

    // If there`s no value for user_id
    if ($('user_id').value == '') {
        // Record an error message
        error_message.push(i18n['messages']['error_empty_field'].replace('[var_label]', i18n['labels']['assignments_owner']));
    }

    form = $(form);
    // If there are any error messages
    if (error_message.length) {
        // Alert the user
        if (form.up('.lb_content')) {
            displayNotificationFixed(displayNotificationFixed.formatContent(error_message, 'error'));
        } else {
            alert(error_message.join("\n"));
        }
        // Exit
        return;
    }

    // identify model and model id
    var model = form.down('#parent_module') && form.down('#parent_module').value ? form.down('#parent_module').value.replace(/s$/, '') : 'task',
        model_id = model && model !== 'task' && form.down('#parent_model_id') ? form.down('#parent_model_id').value : (form.down('#model_id') ? form.down('#model_id').value : '');

    // function to perform actions on success - it can be passed as optional last parameter
    var success_func = arguments.length > 1 && typeof arguments[arguments.length - 1] === 'function' ? arguments[arguments.length - 1] : null;

    // Execute the AJAX
    preventResubmit(form, true);
    show_loading = 1;

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            if (result.match(/redirect/)) {
                //redirect to specified URL
                eval(result);
                redirect(redirect_url);
                return;
            }

            if (form.up('.lb_content')) {
                // form is in lightbox

                // expected response is an object, not a string with HTML content,
                // otherwise it is assumed to be just error messages
                var result_operation =
                    String.prototype.isJSON.call(t.responseText) ?
                    String.prototype.evalJSON.call(t.responseText) :
                    {messages: t.responseText};

                // show success or error messages
                displayNotificationFixed(result_operation.messages);

                if (result_operation.result) {
                    // prepare parameters
                    result_operation.click_action = 'timesheets';
                    result_operation.mouseenter_function = showTimesheetsInfo;
                    result_operation.check_empty_function = function(content) {
                        return content.strip() === '' || content.strip().match(/^0/);
                    };
                    if (model && model_id) {
                        // update totals in page
                        updateInlineTotals('timesheet_time', model, model_id, result_operation);
                    }

                    // function to perform actions on success instead of default ones
                    if (success_func) {
                        success_func();
                        Effect.Fade('loading');
                        return;
                    }

                    lb.deactivate();
                } else {
                    $(form).select('button').each(function(btn) {
                        btn.removeClassName('button_inactive');
                        btn.removeAttribute('disabled');
                    });
                }
            } else {
                // form is in timesheets action
                $(div_id).innerHTML = result;

                var x = $(div_id).getElementsByTagName("script");
                for (var i = 0; i < x.length; i++) {
                    eval(x[i].text);
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=save';
    if (div_id && typeof div_id == 'string') {
        url += '&session_param=' + div_id.replace(/^panel_/, '');
    }
    // set flags when timesheet is added from lightbox opened from a list
    // of records
    if (form.up('.lb_content')) {
        url += '&inline_add=1&use_ajax=1';
        if (model && model_id && $$('.history_activity_' + model + '_' + model_id).length) {
            url += '&history_total=1';
        }
    }

    new Ajax.Request(url, opt);
}

/**
 * AJAX functionality to load timesheet for edit
 *
 * @param timesheet_id - id of timesheet
 * @param div_id - id of div to load result into
 */
function editTimesheet(timesheet_id, div_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            if (result.match(/<table/)) {
                $(div_id).innerHTML = result;

                var x = $(div_id).getElementsByTagName("script");
                for (var i = 0; i < x.length; i++) {
                    eval(x[i].text);
                }
                new Effect.ScrollTo(div_id);
            } else {
                // some error occurred, this is the error message so display it
                alert(result);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=edit&id=' + timesheet_id;

    new Ajax.Request(url, opt);
}

/**
 * Functionality for management of add/edit-timesheet form when some field value is changed
 *
 * @param element - some element of form
 */
function manageTimesheet(element) {

    var event_id = $('event_id');
    var user_id = $('user_id');
    // documents, projects, tasks
    var parent_module = '';
    // id of task (or system task)
    var task_id = 0;
    if ($('model_id')) {
        // when in timesheets action
        task_id = $('model_id').value;
        if ($('parent_module') != null) {
            parent_module = $('parent_module').value != '' ? $('parent_module').value : 'tasks';
        }
        // only if: 1) timesheet is for task, 2) there is no pre-selected event, 3) task id found
        if (!(parent_module == 'tasks' && event_id && event_id.tagName == 'SELECT' && task_id > 0)) {
            return;
        }
    } else if ($('timesheet_records')) {
        // when in statements screen
        var model_id = $('timesheet_records');
        task_id = model_id.value;
        if (model_id.tagName == 'SELECT' && model_id.selectedIndex > -1) {
            // record is selected from dropdown
            var regexp = /(tasks|projects|documents)/;
            parent_module = regexp.exec(model_id.options[model_id.selectedIndex].parentNode.className);
            if (parent_module && parent_module.length > 1) {
                parent_module = parent_module[0];
            } else {
                parent_module = '';
            }
        } else if ($('parent_module') != null) {
            // adding timesheet to record from list
            parent_module = $('parent_module').value;
        }
    }

    // all date/datetime fields to be updated
    var dates = [$('startperiod_period'), $('endperiod_period'), $('startperiod_dates'), $('endperiod_dates')];

    /**
     * Filter field to allow only one specific date or reset to default filtering
     */
    var set_date_filter = function(field, date) {
        if (date) {
            field.setAttribute('disallow_before', date);
            field.setAttribute('disallow_after', date);
        } else {
            field.removeAttribute('disallow_before');
            field.setAttribute('disallow_after', '1');
        }
    };

    /**
     * Toggle visibility of a container(span, table row) holding a checkbox.
     * If hidden, uncheck field.
     */
    var toggle_checkbox = function(field, visible) {
        var span = $('span_' + field);
        field = $(field);
        if (!field || !span) {
            return;
        }
        if (visible) {
            removeClass(span, 'hidden');
        } else {
            addClass(span, 'hidden');
            field.checked = false;
        }
    };

    /**
     * Update a dropdown field with a new set of options
     *
     * @param field - SELECT element
     * @param options - array of data for dropdown options
     * @param hide - whether to hide parent row if no options
     */
    var set_options = function(field, options, hide) {
        // try to keep old value as selected
        var old_value = (field.selectedIndex > - 1) ? field.options[field.selectedIndex].value : '';
        // clear old options
        field.options.length = 0;
        if (options.length > 0) {
            var option = new Option(i18n['labels']['please_select'], '', false, !old_value);
            option.innerHTML = i18n['labels']['please_select'];
            addClass(option, 'undefined');
            field.appendChild(option);
            for (var i = 0; i < options.length; i++) {
                var selected = (old_value == options[i]['option_value']);
                var option = new Option(options[i]['label'], options[i]['option_value'], false, selected);
                option.innerHTML = options[i]['label'];
                if (options[i]['class_name']) {
                    option.className = options[i]['class_name'];
                }
                field.appendChild(option);
            }
            removeClass(field, 'missing_records');
            removeClass(field.parentNode.parentNode, 'hidden');
        } else {
            addClass(field, 'missing_records');
            field.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
            if (hide) {
                addClass(field.parentNode.parentNode, 'hidden');
            }
        }
        if (field.getAttribute('onchange')) {
            field.onchange();
        }
    };

    switch (element.id) {
    /**
     * Field is a dropdown for assigned users.
     * Action is reloading of planned time events for selected user.
     */
    case 'user_id':
        // reset date filtering
        for (var i = 0; i < dates.length; i++) {
            set_date_filter(dates[i], '');
        }
        if (parent_module == 'tasks' && element.value) {
            Effect.Center('loading');
            Effect.Appear('loading');

            var opt = {
                method: 'get',
                onSuccess: function(t) {
                    //alert(t.responseText);
                    if (!checkAjaxResponse(t.responseText)) {
                        Effect.Fade('loading');
                        return;
                    }
                    eval('var result = ' + t.responseText + ';');
                    set_options(event_id, result, true);
                    Effect.Fade('loading');
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };
            var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_get_plannedtime&task_id=' + task_id + '&user_id=' + element.value;
            new Ajax.Request(url, opt);
        } else {
            set_options(event_id, [], true);
        }
        break;

    /**
     * Field is a dropdown (not a hidden field) for planned time events.
     * Action is applying/removing restrictions to date fields and filling in event data into form.
     */
    case 'event_id':
        var done = false;
        if (element.value) {
            // the expected class name holds event_start_date|event_start_time|event_end_time|duration
            var regexp = /^.*(\d{4}-\d{2}-\d{2})\|(\d{2}:\d{2})?\|(\d{2}:\d{2})?\|(\d+)(\s|$).*$/g;
            var event_info = regexp.exec(element.options[element.selectedIndex].className);

            if (event_info) {
                var event_date = parseISODate(event_info[1]);
                for (var i = 0; i < dates.length; i++) {
                    set_date_filter(dates[i], event_info[1]);
                    if (dates[i].hasClassName('datebox')) {
                        dates[i].value = event_info[1];
                        $(dates[i].id + '_formatted').value = event_date.format('d.m.Y');
                    } else if (dates[i].hasClassName('datetimebox')) {
                        var t = (dates[i].id.match(/start/)) ?
                                (event_info[2] ? event_info[2].split(':') : [0, 0]) :
                                (event_info[3] ? event_info[3].split(':') :
                                    [Math.floor(event_info[4]/60), event_info[4]%60]);
                        event_date.setHours(t[0], t[1], 0, 0);
                        dates[i].value = event_date.format('Y-m-d H:i:00');
                        $(dates[i].id + '_formatted').value = event_date.format('d.m.Y, H:i');
                    }
                }
                $('duration').value = event_info[4];
                // toggle tabs only when in timesheets action
                if ($('dates') && $('period')) {
                    if (event_info[2] && event_info[3]) {
                        toggleTimesheetPeriod($('dates'));
                    } else {
                        toggleTimesheetPeriod($('period'));
                    }
                }
                done = true;
            }
        }
        if (!done) {
            // reset date filtering
            for (var i = 0; i < dates.length; i++) {
                set_date_filter(dates[i], '');
            }
        }

        // toggle visibility of finish_task checkbox (if task has only one unfinished event)
        if ($('finish_task') && $('last_event')) {
            toggle_checkbox('finish_task',
                ($('finish_event') && $('finish_event').checked && element.value == $('last_event').value ? 1 : 0));
        }
        break;

    /**
     * Field is 'finish_event' checkbox next to planned time events dropdown.
     * Action is toggling of checkbox for finishing planned time event.
     */
    case 'finish_event':
        // toggle visibility of finish_task checkbox (if task has only one unfinished event)
        if ($('finish_task') && $('last_event')) {
            toggle_checkbox('finish_task',
                (element.checked && $('event_id').value == $('last_event').value ? 1 : 0));
        }
        break;

    /**
     * Field is a dropdown with records that user can add timesheets for from statements screen.
     * Action is reloading of assignees for record.
     */
    case 'timesheet_records':
        // deactivate event row
        set_options(event_id, [], true);
        toggle_checkbox('finish_event', 0);

        if (element.value) {
            Effect.Center('loading');
            Effect.Appear('loading');

            var opt = {
                method: 'get',
                onSuccess: function(t) {
                    //alert(t.responseText);
                    if (!checkAjaxResponse(t.responseText)) {
                        Effect.Fade('loading');
                        return;
                    }
                    eval('var result = ' + t.responseText + ';');
                    set_options(user_id, result);
                    // select current user by default
                    if (!user_id.value && user_id.options.length && result.length &&
                    result.map(function(a) { return a.option_value; }).
                        reduce(function(a) { return a == env.current_user_id; })) {
                        user_id.value = env.current_user_id;
                        if (user_id.getAttribute('onchange')) {
                            user_id.onchange();
                        }
                    }
                    Effect.Fade('loading');
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };
            var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_get_assignments&task_id=' + task_id;
            new Ajax.Request(url, opt);
        } else {
            set_options(user_id, []);
        }
        break;
    default:
        break;
    }

    if (parent_module == 'tasks') {
        // toggle "add event" checkbox
        toggle_checkbox('add_event', !event_id.value);
        // toggle "finish event" checkbox
        toggle_checkbox('finish_event', event_id.value);
    }

    return;
}

/**************************** END TIMESHEETS ******************************/

/******************************** STATEMENTS ********************************/

/**
 * Change list of records that current user can add timesheets to after
 * customer is selected
 *
 * @param object autocomplete settings
 * @param object data autocompleter returned data
 */
function changeTimesheetsRecords(autocomplete, data) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj;
    if (data['row']) {
        select_obj = $('timesheet_records_' + data['row']);
    } else {
        select_obj = $('timesheet_records');
    }

    var opt = {
        asynchronous: true,
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            select_obj.options.length = 0;

            for (var k = select_obj.childNodes.length; k != 0; k--) {
                if (select_obj.childNodes[k]) {
                    select_obj.removeChild(select_obj.childNodes[k]);
                }
            }

            if (result.length > 0) {
                removeClass(select_obj, 'missing_records');
                select_obj.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                addClass(select_obj.options[0], 'undefined');
                addClass(select_obj, 'undefined');
                for (var j = 0; j < result.length; j++) {
                    var opt_group;
                    try {
                        opt_group = document.createElement('<optgroup label="' + result[j]['name'] + '">');
                    } catch (e) {
                        opt_group = document.createElement('optgroup');
                        opt_group.label = result[j]['name'];
                    }
                    for (var i = 0; i < result[j]['options'].length; i++) {
                        var option = new Option(result[j]['options'][i]['label'], result[j]['options'][i]['value'], false, false);
                        option.label = result[j]['options'][i]['label'];
                        opt_group.appendChild(option);
                    }
                    addClass(opt_group, result[j]['module']);
                    select_obj.appendChild(opt_group);
                }
            } else {
                select_obj.options[0] = new Option('[' + i18n['labels']['empty_records'] + ']', '', false, false);
                addClass(select_obj, 'missing_records');
            }
            if (select_obj.getAttribute('onchange')) {
                select_obj.onchange();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_load_records&customer_id=' + data['id'];

    new Ajax.Request(url, opt);
}

/**
 * Function to invoke the form to add a timesheet from statements screen or its dashlet
 *
 * @param {string} duration - period type: if the timesheet will be for a single day (dates) or it will be for period (period)
 * @param {string} start_date - day for the timesheet - ISO-formatted date (YYYY-mm-dd)
 * @param {number} task_id - if the task id is set then the timesheet is added for certain record
 * @param {string} startperiod - the starting time of the timesheet (HH:ii:ss)
 * @param {number} dashlet_id - this param marks if the timesheet has been added via dashlet
 */
function addTimesheet(duration, start_date, task_id, startperiod, dashlet_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    Event.stop(event);

    var additional_request_string = '';
    if (start_date) {
        additional_request_string += ('&start_date=' + start_date);
    }
    if (startperiod) {
        additional_request_string += ('&startperiod=' + startperiod);
    }
    if (dashlet_id) {
        additional_request_string += ('&dashlet_id=' + dashlet_id);
    }
    // we want to be able to switch between both period types
    if (!duration) {
        additional_request_string += '&inline_add=1';
    }

    var task_id_string = '';
    if (task_id) {
        task_id_string = '&task_id=' + task_id;
    }

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var result = t.responseText;

            Effect.Fade('loading');
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }

            if (String.prototype.isJSON.call(result)) {
                result = String.prototype.evalJSON.call(result);

                if (result.messages) {
                    displayNotificationFixed(result.messages);
                }
                return;
            }

            //show the form in a lightbox
            lb = new lightbox({
                content: result,
                title: i18n['labels']['add_timesheet'],
                icon: 'addtimesheet.png',
                width: 630
            });
            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_add&duration=' + duration + additional_request_string + task_id_string;

    new Ajax.Request(url, opt);
}

/**
 * Validate the added timesheet using ajax
 *
 * @param form - form to be submitted
 */
function ajaxValidateTimesheet(form) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var action = (form.id == 'add_multiple_timesheets') ?
                 'ajax_multivalidate' :
                 'ajax_validate';

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            var result = eval('(' + t.responseText + ')');
            var errors_count = result.length;

            if (errors_count) {
                var errors_text = '';
                for (var err = 0; err < errors_count; err++) {
                    errors_text = errors_text + result[err] + "\n";
                }
                Effect.Fade('loading');
                alert(errors_text);
            } else {
                form.removeAttribute("onsubmit");
                preventResubmit(form, true);
                if ($('dashlet_id') && $('dashlet_id').value) {
                    Effect.Fade('loading');
                    addTimesheetViaDashlet(form, $('dashlet_id').value);
                } else {
                    form.submit();
                }
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=' + action;
    new Ajax.Request(url, opt);
    return false;
}

/**
 * Filters the records which the user can add timesheets to
 *
 * @param form - form to be submitted
 */
function ajaxTimesheetsFilter(form) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var div_id = 'addable_timesheets_records_list';

    var opt = {
        method: 'get',
        parameters: Form.serialize(form),
        asynchronous: false,
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            } else {
                $(div_id).innerHTML = '';
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
            return false;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_filter_records';
    new Ajax.Request(url, opt);
    return false;
}

/**
 * Switch tabs in the statements screen
 *
 * @param selected_link_id - id of the selected tab
 */
function toggleStatementsTab(selected_link_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var updated_div = 'statements_subpanel_div';
    var selected_link = $(selected_link_id);
    var active_tab = selected_link.parentNode;
    var inactive_tab = (selected_link.id == 'list_timesheets_models') ?
                       $('free_add_timesheets').parentNode :
                       $('list_timesheets_models').parentNode;

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                Effect.Fade('loading');
                return;
            }
            $(updated_div).innerHTML = result;
            addClass(active_tab, 'selected');
            removeClass(inactive_tab, 'selected');

            var scripts = $(updated_div).getElementsByTagName("script");
            for (var i = 0; i < scripts.length; i++) {
                ajaxLoadJS(scripts[i]);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=tasks&tasks=ajax_switch_statements_subpanel&selected_subpanel=' + selected_link.id;

    new Ajax.Request(url, opt);
}

/**
 * Function to add timesheet via dashlet
 *
 * @param form - form to be submitted
 * @param dashlet_id - the id of the dashlet which submits the form
 */
function addTimesheetViaDashlet(form, dashlet_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var url = form.action;

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        asynchronous: false,
        onSuccess: function(t) {
            var result = t.responseText;

            if (checkAjaxResponse(result)) {
                Effect.Fade('loading');
                var container_id = 'content_dashlet_' + dashlet_id;
                lb.deactivate();
                dashletsLoad(container_id, 'plugin', 'statements', dashlet_id);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(url, opt);
    return false;
}

/****************************** END STATEMENTS ******************************/

/**
 * Change list with records with owner users
 * depending on the selected department
 */
function changeAssignmentsMultiAddRecords(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var department_id = element.value;
    var row_id = element.id.replace(/^department_(\d+)/, '$1');
    var update_assignments_element = '';

    if ($('assignments_owner_' + row_id)) {
        update_assignments_element = $('assignments_owner_' + row_id);
    }

    var selected_type = $('type').value;

    var opt = {
        asynchronous: true,
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');

            if (update_assignments_element) {
                update_assignments_element.options.length = 0;

                if (result.length > 0) {
                    removeClass(update_assignments_element, 'missing_records');
                    update_assignments_element.options[0] = new Option(i18n['labels']['please_select'], '', false, false);
                    addClass(update_assignments_element.options[0], 'undefined');

                    for (var i = 0; i < result.length; i++) {
                        option = new Option(result[i]['label'], result[i]['value'], false, false);
                        update_assignments_element.appendChild(option);
                    }
                } else {
                    update_assignments_element.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                    addClass(update_assignments_element, 'missing_records');
                }
                toggleUndefined(update_assignments_element);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=tasks&tasks=ajax_change_assignments_multiadd_records&department_id=' + department_id + '&type=' + selected_type;

    new Ajax.Request(url, opt);
}

/**
 * Function to change the options of the date format dropdown (used when making counters)
 * depending on the selected delimiter
 */
function changeDateFormatDropdown(element) {
    var new_delimiter = element.options[element.selectedIndex].value;

    if ($('date_format')) {
        var date_format = $('date_format');
        $A(date_format.options).each(function(opt) {
            opt.text = opt.className.replace(/\//g, new_delimiter);
            var date_elements = opt.value.match(/0?\%\w/g);
            opt.value = date_elements.join(new_delimiter);
        });
    }
}

/**
 * Function to parse date from ISO format
 * to its main elements
 *
 * return associative array with the elements of the date
 */
function parseISOformatDate(input_date) {
    var date_year = 0;
    var date_month = 0;
    var date_day = 0;
    var date_hour = 0;
    var date_minutes = 0;
    var date_seconds = 0;

    // defines if the date is a valid ISO format
    if (input_date.match(/[0-9]{4}-[0-9]{2}-[0-9]{2}( [0-9]{2}:[0-9]{2}:[0-9]{2})?/)) {

        // splits the string to find date part and hour's part
        var input_full_date_elements = input_date.split(' ');

        // the first element is the date part
        if (input_full_date_elements[0]) {
            var input_date_elements =  input_full_date_elements[0].split('-');
            if (input_date_elements[0]) {
                date_year = parseInt(input_date_elements[0]);
            }
            if (input_date_elements[1]) {
                date_month = parseInt(input_date_elements[1].replace(/^0/, ''));
                date_month = date_month-1;
            }
            if (input_date_elements[2]) {
                date_day = parseInt(input_date_elements[2].replace(/^0/, ''));
            }
        }

        // the second element (if exists) is the hour's part
        if (input_full_date_elements[1]) {
            var input_hours_elements =  input_full_date_elements[1].split(':');
            if (input_hours_elements[0]) {
                date_hour = parseInt(input_hours_elements[0].replace(/^0/, ''));
            }
            if (input_hours_elements[1]) {
                date_minutes = parseInt(input_hours_elements[1].replace(/^0/, ''));
            }
            if (input_hours_elements[2]) {
                date_seconds = parseInt(input_hours_elements[2].replace(/^0/, ''));
            }
        }
    }

    // build a date object based on the the parsed elements
    var date_elements = new Date(date_year,date_month,date_day,date_hour,date_minutes,date_seconds);

    return date_elements;
}

/**
 * Selects export type and loads the export filters
 *
 * @param object element - the plugin dropdown DOM element
 * @param string filters_stored_in_session - a CSV list of all filters that should be saved in the session
 */
function selectExport(element, filters_stored_in_session) {
    var export_type = element.value;

    Effect.Center('loading');
    Effect.Appear('loading');

    var get_params = {'export_module': env.module_name,
                  'export_controller': env.controller_name,
                  'plugin': element.value,
                  'previous_plugin': $('previous_plugin').value,
                  'type_id': $('type_id').value};
    filters_stored_in_session = filters_stored_in_session.split(',');
    for (var field in filters_stored_in_session) {
        if (typeof(filters_stored_in_session[field]) == 'string' && $(filters_stored_in_session[field])) {
            get_params[filters_stored_in_session[field]] = $(filters_stored_in_session[field]).value;
        }
    }

    var opt = {
        method: 'get',
        parameters: get_params,
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText);
            if (result && result.export_filters) {
                for (var field in result.export_filters) {
                    if ($(field)) {
                        $(field).value = result.export_filters[field];
                    }
                }
            }
            if (result && result.filters_visibility) {
                for (var field in result.filters_visibility) {
                    if ($(field)) {
                        //hide/show the element and its parent TR element
                        $(field).style.display = (result.filters_visibility[field]) ? '' : 'none';
                        $(field).parentNode.parentNode.style.display = (result.filters_visibility[field]) ? '' : 'none';
                    }
                }
            }
            if ($('format').style.display != 'none') {
                toggleExportFormat($('format'));
            }
            $('previous_plugin').value = export_type;

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=exports&exports=ajax_load_export_filters';

    new Ajax.Request(url, opt);
}

/**
 * Toggles export format hiding or displaying the separator(tab, colon, comma)
 *
 * @param object element - the plugin dropdown DOM element
 */
function toggleExportFormat(element) {
    //toggle the separator depending on the file format
    let format = element.getValue();
    $('separator').up('tr').hide();
    $('group_tables_0').up('tr').hide();
    if (format == 'csv') {
        $('separator').show();
        $('separator').up('tr').show();
    } else if (format == 'xlsx')  {
        $('group_tables_0').up('tr').show();
    }
}

/**
 * Clears the selected checkboxes from the list before submitting multi action
 *
 * @param element - the element which triggers the action
 */
function diselectItemsBeforeMultiAction(element) {
    frm = element.form;
    var checkbox_items = document.getElementsByName("items[]");
    var main_checkbox = $$('.checkall');

    for (var i = 0; i < checkbox_items.length; i++) {
        if (checkbox_items[i].checked) {
            checkbox_items[i].checked = false;
        }
    }

    for (var j = 0; j < main_checkbox.length; j++) {
        if (main_checkbox[j].tagName == 'BUTTON' && main_checkbox[j].type == 'submit') {
            main_checkbox[j].style.backgroundImage = 'url("' + env.themeUrl + 'images/checkbox_none.png")';
        }
    }

    var selected_items_spans = $$('.selected_items_span');
    for (var h = 0; h < selected_items_spans.length; h++) {
        selected_items_spans[h].innerHTML = 0;
    }

    frm.submit();

    return true;
}

/**
 * Selects the dropdown option that matches the typed string (while the focus is on the dropdown)
 *
 * !!! IMPORTANT: This function works correctly only with the "onkeypress" event.                  :IMPORTANT !!!
 * !!! IMPORTANT: When using it with the "onkeydown" or the "onkeyup" events the cyrilic symbols are not recognized. :IMPORTANT !!!
 *
 * @param element - the current HTML element
 * @param evt     - the current event object
 */
// Prepare a variable for the timeout
var t;
function dropdownTypingSearch(element, evt) {
    // Get the current key code
    var keyCode = evt.keyCode  ? evt.keyCode :
                    evt.charCode ? evt.charCode :
                      evt.which ? evt.which : void 0;

    // If there`s no key code or the key code is between the ignored ones then just exit
    var ignoreKeyCodes = ['17', '18', '20'];
    if (!keyCode || in_array(keyCode, ignoreKeyCodes)) {
        return true;
    }

    // Prepare a default name for the current dropdown
    var default_dropdown_name = 'tmp_dropdown_name';
    // Get the current drpdown name from its id, name or the default name
    var dropdown_name         = element.id ? element.id :
                                  element.name ? element.name : default_dropdown_name;

    // A hidden input field is used to store the typed search string
    // Build an id for the hidden input
    var hidden_input_id = dropdown_name + '_tmp_typing_search';
    // If such hidden input does NOT exist then create it
    //   else just continue using it
    if (!$(hidden_input_id)) {
        var hidden_input  = document.createElement('input');
        hidden_input.type = 'hidden';
        hidden_input.id   = hidden_input_id;
        element.parentNode.appendChild(hidden_input);
    }

    // Reset the time to store the typed search string into the hidden input
    clearTimeout(t);
    t = setTimeout("$('" + hidden_input_id + "').value = '';", 1500);

    // Convert the input key code to a letter (or other symbol)
    var key = String.fromCharCode(keyCode);

    // Append the current letter to the hidden field
    // (escape special characters)
    $(hidden_input_id).value = $(hidden_input_id).value + key.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");

    // Find the first option that matches the search string (i.e. the current value of the hidden field)
    var pattern;
    var regex;
    for (var i = 0; i < element.options.length; i++) {
        pattern = '^\-*' + $(hidden_input_id).value + '.*$';
        regex = new RegExp(pattern, 'i');
        if (element.options[i].text.match(regex)) {
            // Select the matching option
            element.selectedIndex = i;
            toggleUndefined(element);
            return true;
        }
    }

    return true;
}

/**
 * show/hide offices on function of selected companies
 * in roles ad users permissions setup screen
 */
function manageOfficesRelations(origin) {
    var companies = $$('.' + origin + '_company');
    var offices = $$('.' + origin + '_office');
    var allowed_offices = [];
    for (var i = 0; i < companies.length; i++) {
        if (companies[i].checked) {
            for (var j in co_relations[companies[i].value]) {
                if (typeof(co_relations[companies[i].value][j]) != 'function') {
                    if (!in_array(co_relations[companies[i].value][j], allowed_offices)) {
                        allowed_offices.push(co_relations[companies[i].value][j]);
                    }
                }
            }
        }
    }
    for (var i = 0; i < offices.length; i++) {
        if (in_array(offices[i].value, allowed_offices)) {
            offices[i].parentNode.style.display = '';
        } else {
            offices[i].checked = false;
            offices[i].parentNode.style.display = 'none';
        }
    }
}

/**
 * Clear date, datetime or time field
 */
function clearDateField(field) {
    // get the field(row) index (if any)
    var name = /^(.*?)(_\d+)?$/.exec(field.id),
        row = name[2] || '';
    name = name[1];

    field.value = '';
    var field_formatted = (name.match(/_formatted$/)) ? field : $(name + '_formatted' + row);
    if (field.hasClassName('datetimebox')) {
        field_formatted.value = defaultDateTime;
    } else if (field.hasClassName('datebox')) {
        field_formatted.value = defaultDate;
    } else if (field.hasClassName('timebox')) {
        field_formatted.value = defaultTime;
    }
    field_formatted.onblur();
}

function searchableSelects() {
    const searchableboxes = document.querySelectorAll(`select.searchablebox:not(.searchboxready)`);
    if (searchableboxes.length === 0) {
        return;
    }

    searchableboxes.forEach(function (sbox) {
        sbox.classList.add('searchboxready');
    });

    loadCSS(env.jsUrl + 'nice-select2/dist/css/nice-select2.css');
    loadJS(env.jsUrl + 'nice-select2/dist/js/nice-select2.js').then(res => {
        searchableboxes.forEach(function (sbox) {
            sbox.classList.remove('selbox_hov');
            sbox.classList.add('selbox');
            let clWidth = sbox.clientWidth;
            NiceSelect.bind(sbox, {searchable: true});
            sbox.parentNode.querySelector('.selbox.nice-select').style.minWidth = (clWidth+2) + 'px';
        });
    }).catch(err => {
        console.log(err)
    });
}
