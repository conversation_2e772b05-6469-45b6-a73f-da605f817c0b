/* Compatibility: onReady for default theme */
onReady = nz_ready;

/* Lightbox Override */
LightboxOptions.borderSize = 18;


/* Overlib compatibility layer */
CAPTION = '';
WIDTH = 500;
HEIGHT = 250;
STICKY = false;
CLOSETEXT = 'Close';
CLOSETITLE = 'Затваряне';
CLOSECLICK = true,
HAUTO = true,
VAUTO = true
window._overlibTooltips = {}

/**
 * @var string content
 * @var string caption
 * @var string title
 * @var int width
 * @var string height
 * @returns {boolean}
 */
function overlib() {
    // content, caption, title, width, height
    if (!arguments) return true;
    overlib2(arguments[0], arguments[1], arguments[2], arguments[3] || 350, arguments[4]);
}
/**
 * content, caption, title, width, height
 * @returns {boolean}
 */
function overlib2(content, caption, title, width, height) {
    let pointedEl = document.elementFromPoint(window.event.clientX, window.event.clientY);
    if (!pointedEl ) return false;
    if (!pointedEl.matches('[onmouseover]')) {
        childTry = pointedEl.querySelector('[onmouseover]');
        if (childTry) {
            pointedEl = childTry;
        } else {
            parentTry = pointedEl.closest('[onmouseover]');
            if (parentTry) pointedEl = parentTry;
        }
    }

    if (!pointedEl.matches('[onmouseover]') || pointedEl.classList.contains('nz-tooltip-trigger')) return false;

    pointedEl.classList.add('nz-tooltip-trigger');
    let tooltipKey = 't_'+Nz.hashString(pointedEl.outerHTML);
    let tooltip;

    if (typeof window._overlibTooltips[tooltipKey] !== "undefined") {
        return;
    }

    tooltip =  new NzTooltip({
        title: title || null,
        content: content,
        element: document.body,
        position: {panel: 'bottom center', at: 'top center', },
    });

    tooltip.attachTo(pointedEl);
    if (width) {
        tooltip.tooltipEl.style.width = width + 'px';
    }

    tooltipKey = 't_'+Nz.hashString(pointedEl.outerHTML);
    window._overlibTooltips[tooltipKey] = tooltip;
    tooltip.show();
}

function nd() {
    for (const tooltipKey in _overlibTooltips) {
        _overlibTooltips[tooltipKey].hide();
    };
}

/* GENERAL FUNCTIONS */
/* ================= */

/*
 * fix for IE to get the elements by name
 */
if (navigator.appName.match(/microsoft/i)) {
    document.getElementsByName = function(name) {
        var temp = document.all;
        var matches = [];
        for (var i = 0; i < temp.length; i++) {
            if (temp[i].name == name) {
                matches.push(temp[i]);
            }
        }
        return matches;
    };
}

async function nzShowLoading() {
    document.body.classList.remove('nz--ready');
}
async function nzHideLoading() {
    document.body.classList.add('nz--ready');
}


/**
 * Manages checkboxes Selects and deselects all checkboxes in list/search forms
 *
 * @param params - object with parameters
 */
function manage_checkboxes(params) {

    const button = document.querySelector(`#${params.button_id}`);
    const menuBoxId = params.button_id.replace(/_checkall_/, '_select_');
    const menu_box = document.querySelector(`#${menuBoxId}`);
    const index = menu_box.id.match(/[0-9]+$/);
    const the_form = button.form;

    if (!the_form || !the_form.elements['items[]']) {
        return false;
    }

    if (menu_box.style.display !== 'none') {
        menu_box.style.display = 'none';
        if (typeof params.select_what === 'undefined') {
            // clicked second time on checkbox
            return false;
        }
        const select_what = params.select_what;
        if (select_what == 'all') {
            set_checkboxes(the_form, true);
        }
        if (select_what == 'page') {
            selected_count = set_checkboxes(the_form, true);
        }

        if (select_what.match(/^first_(\d+)$/)) {
            const limit = parseInt(select_what.replace(/^first_(\d+)$/, '$1'));
            selected_count = set_checkboxes(the_form, true, limit);
        }
        if (select_what == 'none') {
            selected_count = set_checkboxes(the_form, false);
        }

        if (params.session_param && params.action != 'filter') {
            params.the_form = the_form;
            sendIds(params);
        } else if (params.action == 'filter') {
            $('selectedItemsCount_' + index).innerHTML = selected_count;
            setCheckAllBox(params);
        }
        return false;
    }

    if (menu_box.style.display === 'none') {
        if (menu_box.style.position === 'absolute') {
            let parent = button;
            const correction = {
                top: 6,
                left: 6
            }
            while((parent = parent.parentElement) && parent.tagName !== 'BODY') {
                const style = getComputedStyle(parent);
                if (style.position === 'relative') {
                    correction.top += parent.offsetTop;
                    correction.left += parent.offsetLeft;
                }
            }

            const rect = button.getBoundingClientRect();
            const position = {
                top: rect.top - correction.top + window.scrollY ,
                left: rect.left - correction.left + window.scrollX
            }

            if (rect.left + menu_box.offsetWidth > document.body.clientWidth) {
                position.left = document.body.clientWidth - menu_box.offsetWidth  - 10;
            }

            menu_box.style.top = position.top + 'px';
            menu_box.style.left = position.left + 'px';
        }

        menu_box.style.display = 'block';
    }
    return false;
}

/**
 * Changes options in timezones dropdown on change in area dropdown.
 *
 * @param element - area dropdown
 */
function setTimezones(element) {
    if (typeof(timezones) == 'undefined' && $('timezones')) {
        // workaround for IE8 and older versions
        eval('var timezones = ' + $('timezones').value);
    }
    var element = $(element);
    var index = element.id.replace(/[^\d].*_(\d+)/, '$1');

    area = element.value;
    options = timezones[area];
    var params = {name: 'timezones',
                  index: index,
                  type: 'dropdown',
                  options: options,
                  width: '200px'};
    container = $('timers_' + index + '_2');
    container.innerHTML = '';
    createField(params, container);
}

/**
 * Set checkAll checkbox (on/off/not all) according to the count of the selected
 * checkboxes
 *
 * @param params - object with parameters
 */
function setCheckAllBox(params) {
    var element = $(params.button_id);
    if (!element) {
        return;
    }
    var index = element.id.match(/[0-9]+$/);
    var selected = null;
    var total = null;
    if ($('selectedItemsCount_' + index)) {
        selected = parseInt($('selectedItemsCount_' + index).innerHTML);
    }
    if ($('totalItemsFound_' + index)) {
        total = parseInt($('totalItemsFound_' + index).innerHTML);
    }
    if (params.action == 'filter' && params.the_element) {
        if (params.the_element.checked) {
            selected++;
        } else {
            selected--;
        }
        $('selectedItemsCount_' + index).innerHTML = selected;
    }
    if (selected == 0) {
        element.style.backgroundImage = 'url(' + env.themeUrl + 'images/checkbox_none.png)';
    } else if (selected == total) {
        element.style.backgroundImage = 'url(' + env.themeUrl + 'images/checkbox_all.png)';
    } else {
        element.style.backgroundImage = 'url(' + env.themeUrl + 'images/checkbox_.png)';
    }
}

/**
 * Set checkboxes on and off according to the Selects and deselects all
 * checkboxes in list/search forms
 *
 * @param the_form -the DOM element form containing the checkboxes
 * @param set - true or false equals select or deselect
 * @param limit - optional limit to select
 */
function set_checkboxes(the_form, set, limit = null) {
    var items = the_form.elements['items[]'];
    var subitems = the_form.elements['subitems[]'];
    var total_count = 0;

    if (items) {
        var items_num = items.length;

        if (items_num) {
            for (var i = 0; i < items_num; i++) {
                if (!items[i].disabled && !$(items[i]).hasClassName('disabled')) {
                    items[i].checked = set;
                    total_count ++;
                }
                if (limit != null && Number.isInteger(limit) && total_count >= limit) {
                    break;
                }
            }
        } else {
            if (!items.disabled && !$(items).hasClassName('disabled')) {
                items.checked = set;
                total_count ++;
            }
        }
    }

    if (subitems) {
        var subitems_num = subitems.length;

        if (subitems_num) {
            for (var i = 0; i < subitems_num; i++) {
                if (!subitems[i].disabled && !$(subitems[i]).hasClassName('disabled')) {
                    subitems[i].checked = set;
                    total_count++;
                }
                if (limit != null && Number.isInteger(limit) && total_count >= limit) {
                    break;
                }
            }
        } else {
            if (!subitems.disabled && !$(subitems).hasClassName('disabled')) {
                subitems.checked = set;
                total_count++;
            }
        }
    }
    if (set) {
        return total_count;
    } else {
        return 0;
    }
}

/**
 * Counts selected checkboxes with defined name (usually 'items').
 * If third parameter (class name) specified, count only checkboxes having it.
 *
 * @param the_form -the DOM element form containing the checkboxes
 * @param element - name of the checkboxes, the default value is 'items'
 * @return integer - number of checked elements
 */
function count_checkboxes(the_form, element) {
    var els;
    if (!element) {
        els = new Array('items', 'subitems');
    } else {
        els = new Array(element);
    }
    // if class name parameter is specified, count only elements having it
    var regex_class_name = count_checkboxes.arguments[2] ? new RegExp(count_checkboxes.arguments[2], 'g') : '';

    var num_checked = 0;
    for (var j = 0; j < els.length; j++) {
        var items = the_form.elements[els[j] + '[]'];

        if (items) {
            var items_num = items.length;

            if (items_num) {
                for (var i = 0; i < items_num; i++) {
                    if (items[i].checked && (!regex_class_name || items[i].className.match(regex_class_name))) {
                        num_checked = num_checked + 1;
                    }
                }
            } else if (items && (!regex_class_name || items.className.match(regex_class_name))) {
                num_checked = items.checked;
            }
        }
    }

    return num_checked;
}

function hideSelectMenu(menu_box) {
    $(menu_box).style.display = 'none';
}

function setRadioButtons(prefix) {
    var elements = document.getElementsByTagName('input');

    for (var i = 0; i < elements.length; i++) {
        if (elements[i].id.match(prefix)) {
            elements[i].checked = true;
        }
    }
}

/**
 * Removes spaces in the beginning and end of string
 *
 * @param text - string to be trimmed
 */
function trim(text) {
    // Removes spaces from the beginning of the text.
    var re = /^ /;
    while(re.test(text)) {
        text = text.replace(re, "");
    }

    // Removes spaces from the end of the text.
    var re1 = / $/;
    while(re1.test(text)) {
        text = text.replace(re1, "");
    }

    return text;
}

/**
 * Defines which window calling function should work with depending on conditions
 *
 * @param {Array} params
 * @return {Window} - calling context window (parent window/another frame)
 */
function defineContext(params) {
    var context = params && params.context ? params.context : (window.opener || window.parent || window.top || window);
    // if there is a lightbox stack with more than one element and the next to last element is an iframe
    if (window != window.parent && window.parent.lb && window.parent.lb.lb_stack && window.parent.lb.lb_stack.length > 1 && window.parent.$$('iframe#' + window.parent.lb.lb_stack[window.parent.lb.lb_stack.length - 2].uniqid).length) {
        context = window.parent.frames[window.parent.lb.lb_stack[window.parent.lb.lb_stack.length - 2].uniqid];
    }
    return context;
}

async function nzShowLoading() {
    document.body.classList.remove('nz--ready');
}
async function nzHideLoading() {
    document.body.classList.add('nz--ready');
}

/**
 * Pops a new window and stores it a global array 'popups'
 *
 * @param src - the url of the link to be poped up
 * @param width - width of the popup window
 * @param height - height of the popup window
 */
function windowOpen(src, width, height, do_not_close) {

    var hWnd = window.open(src, 'pop'+Math.round(100*Math.random()), 'toolbar=0,location=0,status=0,menubar=0,resizable=1,scrollbars=1,width=' + width + ',height=' + height);

    // if popup blocking is on, hWnd is null
    if (hWnd) {
        if (!do_not_close) {
            hWnd.document.close();
        }
        hWnd.focus();
    }

    // remove closed popups from array
    popups = popups.findAll(function(w) { return !w.closed; });

    popups.push(hWnd);


    return hWnd;
}

/**
 * Remove window from array of popups of parent window
 */
function closePopupWindow() {
    if (window.opener) {
        if (window.opener.popups) {
            window.opener.popups.each(function(w, idx) {
                if (w == window) {
                    window.opener.popups.splice(idx, 1);
                }
            });
        }

        // check for autocomplete params (search for uniqid in page or in url
        // address) and run clean-up if possible
        var uniqid = ($('uniqid') ? $('uniqid').value : document.location.href.toQueryParams()['uniqid']) || null;
        if (uniqid) {
            (createAutocompleterCloseHandler(uniqid, window.opener))();
        }
        window.close();
        window.opener.focus();
    }

    // handle lightbox close (the lightbox is in the parent window even if the context is another iframe)
    if (window.parent && window.parent.lb && window.parent.lb.active) {
        window.parent.lb.deactivate();
    }
    window.parent.postMessage('closeDialogs', '*');
}

window.addEventListener('message',(e) => {
    if (e.data == 'closeDialogs') {
        NzDialog.closeAll();
    }
});


/**
 * Prepares functionality to run when autocompleter data should be removed
 * from user session server-side
 *
 * @param {string} uniqid - unique identifier of autocompleter field
 * @param {Window} context - window to get autocomplete parameters from
 * @return {Function} - anonymous function
 */
function createAutocompleterCloseHandler(uniqid, context) {
    const fieldData =  context['params_' + uniqid];
    if (uniqid && fieldData && fieldData.url) {
        const url = context['params_' + uniqid].url + '&uniqid=' + uniqid + '&ajax_filter=1&exit_after=1' + '&field=' + fieldData['field'];
        return function() {
            new Ajax.Request(
                url,
                {
                    // if window is popup, make synchronous request because
                    // otherwise it won't be sent when the window is closed
                    asynchronous: window.opener == null
                });
        };
    }
    return null;
}

/**
 * Closes all popups opened from current window (used before redirect) and
 * redirect to specified address
 */
function closeAllPopupsAndRedirect(redirect_url) {
    // all popups
    if (window.popups && window.popups.length) {
        window.popups.each(function(hWnd) {
            if (hWnd.popups && hWnd.popups.length) {
                // children - call same function without redirect parameter
                hWnd.popups.invoke('closeAllPopupsAndRedirect', '');
            }
            if (hWnd.opener) {
                hWnd.closePopupWindow();
            }
        });
    }
    // all lightbox layers
    if (lb && lb.active) {
        while (lb.active) {
            lb.deactivate();
        }
    }

    if (window.name.match(/^pop\d+$/) && window.opener != null && window.opener !== window) {
        // parent - call same function with redirect parameter
        window.opener.closeAllPopupsAndRedirect(redirect_url);
    } else if (redirect_url) {
        // if the top window is reached - redirect
        redirect(redirect_url);
    }
}

/**
 * Adjust lightbox dimensions according to initial parameters on content reload in iframe
 */
function scalePopup() {
    if (window !== window.parent && window.parent.lb && window.parent.lb.active && window.name == window.parent.lb.params.uniqid) {
        window.parent.lb.scaleIframe();
    }
}

/**
 * Opens specified address
 *
 * @param {string} href - web address
 * @param {string} target - optional target to open address into
 * @param {Object} event - click event
 */
function openHref(href, target, event) {
    if (isSubelementClicked(/openHref/)) {
        Event.stop(event);
        return;
    }
    if (event.button == 1 || event.shiftKey) {
        target = '_blank';
    }
    if (event.button == 2) {
        // do nothing
    } else if (target) {
        window.open(href, target);
    } else {
        redirect(href);
    }
    Event.stop(event);
}

/**
 * Adds class to a defined element
 *
 * @param element - a DOM element
 * @param className - class name to be added or an array of class names
 */
function addClass(element, className) {
    if (element == null || className == null || className == '') {
        return;
    }
    // converts the className to an array if it is not already,
    // and calls the .classList.add() with the elements as individual parameters
    element.classList.add(...(typeof className === 'string' ? className.split(' ') : className));
}

/**
 * Removes class to a defined element
 *
 * @param element - a DOM element
 * @param className - class name to be removed
 */
function removeClass(element, className) {
    if (element == null) {
        return;
    }
    // converts the className to an array if it is not already,
    // and calls the .classList.add() with the elements as individual parameters
    element.classList.remove(...(typeof className === 'string' ? className.split(' ') : className));

}

/**
 * Highlights a specific element by changing the className to <className>_hov
 *
 * @param element - a DOM element
 * @deprecated please remove any remaining calls to this function. Use styles!
 */
function highlight(element) {
    return true;
}

/**
 * Add or remove 'undefined' class for SELECT element according to selected option
 *
 * @param element
 * @return {Boolean}
 */
function toggleUndefined(element) {
    if (!element) {
        return true;
    }

    if (!element.value) {
        element.classList.add('undefined');
    } else if (element.classList.contains('undefined')) {
        element.classList.remove('undefined');
    }
    return true;
}

/**
 * Removes highlighting from a specific element by removing the _hov suffix
 *
 * @param element - a DOM element
 * @deprecated please remove any remaining calls to this function. Use styles!
 */
function unhighlight(element) {
    return true;
}


/**
 * Shows or hides buttons
 *
 * @param element -select element
 */
function changeButton(element) {
    $('referers').innerHTML='';
    var models = new Array ('document', 'project', 'task');
    for (var i = 0; i < models.length; i++) {
        var el = $(models[i]+'_btn');
        if (element.value + '_btn' == el.id) {
            el.style.display = '';
        } else {
            el.style.display = 'none';
        }
    }
}

/**
 * Checks if an element is within an array
 *
 * @param the_needle - element to search
 * @param the_haystack - array to search in
 */
function in_array(the_needle, the_haystack) {
    // TODO: The whole functionality of this function can be replaced using: the_haystack.indexOf(the_needle)
    var the_hay = the_haystack.join('|');
    if (the_hay == '') {
        return false;
    }
    var the_pattern = new RegExp('(^|\\|)' + the_needle + '(\\||$)', 'g');
    var matched = the_pattern.test(the_hay);
    return matched;
};

/**
 * Checks if a variable is defined
 *
 * @param variable - variable name
 */
function isDefined(variable) {
    if (typeof(window[variable]) !== "undefined") {
        return true;
    }
    if (!window.opener) {
        return false;
    }

    try {
        if (typeof(window.opener[variable]) !== "undefined") {
            return true;
        }
    } catch (e) {}

    return false;
}

/**
 * Checks if inner <a></a> element has been clicked. Check is used in order not
 * to execute functionality for onClick event of an outer element.
 *
 * @param {Object} exclude_regexp - expression to exclude from the onclick check, possibly the name of the triggered function
 * @return {boolean} - result of check
 */
function isSubelementClicked(exclude_regexp) {
    const el = event.target || event.srcElement || event.originalTarget;

    // do not launch the function if another clickable object has been clicked
    // (checkboxes, anchors, etc.)
    if (el && (el.onclick && !(exclude_regexp && el.onclick.toString().match(exclude_regexp)) || el.closest('a'))) {
        return true;
    }
    return false;
}

/**
 * updates content of a HTML element with content sent from the server
 *
 * @param link - server side url to be executed
 * @param target - the id of the container to be filled in
 */
function ajaxUpdater(args) {
    if (!args.link || !args.target) {
        return false;
    }

    const url = args.link;
    const target = typeof args.target === 'string' ? document.querySelector('#'+args.target) : args.target;;

    nzShowLoading();

    const updateContent = function(t) {
        if (!checkAjaxResponse(t.responseText)) {
            nzHideLoading();
            return false;
        }
        if (!t.responseText) {
            if (!env.valid_login) {
                redirectToLogin();
            }
            Effect.Fade('loading');
            if (args.execute_after && Object.isFunction(args.execute_after)) {
                args.execute_after();
            }
            nzHideLoading();
            return;
        }

        target.innerHTML = t.responseText;

        Nz.loadJSFromHTMLString(target.innerHTML);

        nzHideLoading();

        if (env.module_name == 'index' && env.action_name == 'frontend') {
            processEmptyElements(true);
        }

        // if there is some functionality specified to be executed after everything is completed
        if (args.execute_after && Object.isFunction(args.execute_after)) {
            args.execute_after();
        }
    };
    new Ajax.Request(url, {onComplete: updateContent, parameters: (args.parameters || '')});
}

/**
 * Reloads page with a specific suffix for the link This function is used for
 * select-and-go type of navigation
 *
 * @param element - master checkbox element (select/deselect all)
 */
function reloadPage(sel, url) {
    redirect(url+sel.value);
}



/* FUNCTIONS USED BY SEVERAL MODULES */
/* ================================= */
/**
 * Adds new row containing attributes for file attachments
 */
function addFileBrowse() {
    // defines the max number of file upload rows
    var max_rows = 5;
    var items = addFileBrowse.arguments.length - 1;
    var itemsEl = addFileBrowse.arguments[items];
    if (typeof itemsEl === 'string') {
        itemsEl = document.querySelector(`#${addFileBrowse.arguments[items]}`);
    }
    var tbl = itemsEl.closest('table');

    var plusButton = tbl.querySelector('.t_plus');
    var minusButton = tbl.querySelector('.t_minus');

    var lastRow = tbl.rows.length;

    var lastId = parseInt(tbl.rows[lastRow-1].cells[0].innerHTML);

    if (lastId >= max_rows) {
        return;
    }


    // insert a new row in the table
    var row = tbl.insertRow(lastRow);
    row.className = tbl.rows[lastRow-1].className;
    var cell = row.insertCell(0);
    cell.style.textAlign = 'right';
    cell.className = 't_border';
    cell.innerHTML = (lastId + 1) + '. ';
    for (var i = 0; i < items; i++) {
        var field = $(addFileBrowse.arguments[i] + '_' + lastId);

        var el = field.cloneNode(true);
        el.id = addFileBrowse.arguments[i] + '_' + (lastId+1);

        if (field.tagName == 'SELECT') {
            el.selectedIndex = field.selectedIndex;
        } else if (field.type == 'file') {
            el.value = '';
        }
        var cell = row.insertCell(i+1);
        if (i < items-1) {
            addClass(cell, 't_border');
        }
        if (i == items-1) {
            // the last column is always with colspan 2
            cell.colSpan = 2;
        }
        cell.appendChild(el);

        // cTranslator is used (alternative keyboard inputs)
        // Install the keypressed events in the texts and textareas elements
        if (isDefined('cTranslator') && !el.readonly &&
            ((el.tagName.toLowerCase() == 'input' && el.type.toLowerCase() == 'text') || el.tagName.toLowerCase() == 'textarea')) {
            cTranslator.install(el);
        }
    }

    if (lastId < max_rows-1) {
        plusButton.classList.remove('disabled', 'nz--disabled');
    } else {
        plusButton.classList.add('disabled', 'nz--disabled');
    }
    minusButton.classList.remove('disabled', 'nz--disabled');
}

/**
 * Removes a row from the file attachments rows
 *
 * @param element - the minus button itself
 */
function removeFileBrowse(element) {
    var tbl = element.closest('table');

    var lastRow = tbl.rows.length;

    var lastId = parseInt(tbl.rows[lastRow-1].cells[0].innerHTML);

    if (lastId > 1) {
        tbl.deleteRow(lastRow - 1);
    } else {
        return;
    }

    var plusButton = tbl.querySelector('.t_plus');
    var minusButton = tbl.querySelector('.t_minus');

    plusButton.classList.remove('disabled', 'nz--disabled');
    if (lastId > 2) {
        minusButton.classList.remove('disabled', 'nz--disabled');
    } else {
        minusButton.classList.add('disabled', 'nz--disabled');
    }
}

/**
 * A function used to allow edit of the file attachments
 *
 * @param element - the edit button itself
 * @param id - id of the file attachment
 * @param type - type of attachment
 */
function editFileBrowse(element, id) {
    // define the type attachments
    var type = editFileBrowse.arguments[2];

    var onclick_event = editFileBrowse.arguments[3];

    var items;
    if (type == 'generated') {
        // generated files
        items = ['g_file_names', 'g_file_descriptions', 'g_file_permissions'];
    } else {
        // attached files
        items = ['file_paths', 'file_names', 'file_descriptions', 'file_revisions', 'file_permissions'];
    }

    if (!element.hasClassName('dimmed')) {
        addClass(element, 'dimmed');
        modified = 1;
    } else if (!confirm(i18n['messages']['confirm_edit_files'])) {
        removeClass(element, 'dimmed');
        modified = 0;
    }

    for (var i = 0; i < items.length; i++) {
        var item = items[i];
        if ($(item + '_value_' + id)) {
            $(item + '_value_' + id).style.display = (modified) ? 'none' : 'block';
        }
        if ($(item + '_' + id)) {
            $(item + '_' + id).disabled = !modified;
            $(item + '_' + id).style.display = (modified) ? 'block' : 'none';
            if (onclick_event) {
                if (modified) {
                    $(item + '_' + id).parentNode.onclick = '';
                } else {
                    $(item + '_' + id).parentNode.onclick = function () {
                        window.open(onclick_event, '_blank');
                    };
                }
            }
        }
    }
}

/**
 * The function is used to allow ONLY one click on a checkbox list
 *
 * @param javascript object element
 * @return boolean true|false
 */
 function clickOnce(obj) {

    if (count_checkboxes(obj.form, 'items')> 1 && obj.checked) {
        alert(i18n.messages['alert_link_select_one']);
        return false;
    }

    return true;
}

/**
 * The function is used to allow ONLY one selected trademark in a checkbox list
 *
 * @param javascript object element
 * @return boolean true|false
 */
function checkDefaultTrademark(obj) {
    num_checked = 0;
    var all_input_items = obj.form.getElementsByTagName('input');
    var items = new Array();
    for (var i = 0; i < all_input_items.length; i++) {
        if (all_input_items[i].name.match(/^is_default\[\d+\]$/)) {
            items[items.length] = all_input_items[i];
        }
    }

    if (items) {
        var items_num = items.length;

        if (items_num) {
            for (var i = 0; i < items_num; i++) {
                if (items[i].checked) {
                    num_checked = num_checked + 1;
                }
            }
        } else if (items) {
            num_checked = items.checked;
        }
    }

    if (num_checked > 1 && obj.checked) {
        alert(i18n.messages['alert_link_select_one']);
        return false;
    }

    return true;
}

/**
 * A function used to add new hyperlink block (input for link title and the link
 * itself)
 *
 * @param link_element - the input for the link
 * @param link_title_element - the input for the title of the link
 * @param link_table - the table containing the links
 * @param link_count - number of the link item
 */
function addHyperlink() {
    // defines the max hyperlink blocks allowed
    var max_fields = 10;

    var items = addHyperlink.arguments.length;
    var tbl = $(addHyperlink.arguments[items-2]);

    var plusButton = $(addHyperlink.arguments[items-2] + '_plusButton');
    var minusButton = $(addHyperlink.arguments[items-2] + '_minusButton');

    var lastRow = addHyperlink.arguments[items-1];
    var currentTableRow = tbl.rows.length;
    if (currentTableRow > max_fields) {
        return;
    }
    next = currentTableRow+lastRow-2;

    // if there's no header row in the table, then iteration = lastRow + 1
    var iteration = lastRow;
    var row = tbl.insertRow(currentTableRow);
    for (var i = 0; i < items - 2; i++) {
        var cell_0 = row.insertCell(0);
        var field = $(addHyperlink.arguments[i] + parseInt(lastRow));
        // var cell = row.insertCell(i+1);
        var el = field.cloneNode(true);
        el.name = addHyperlink.arguments[i] + '['+next+']';
        el.id = addHyperlink.arguments[i] + (next+1);
        if (el.tagName == 'SELECT') {
            el.value = field.value;
        } else {
            el.value = '';
        }
        if (i> 0 && i <= items-3) {
            addClass(cell_0, 't_border');
        }
        cell_0.appendChild(el);

        // cTranslator is used (alternative keyboard inputs)
        // Install the keypressed events in the texts and textareas elements
        if (isDefined('cTranslator') && !el.readonly &&
            ((el.tagName.toLowerCase() == 'input' && el.type.toLowerCase() == 'text') || el.tagName.toLowerCase() == 'textarea')) {
            cTranslator.install(el);
        }
    }

    if (currentTableRow <= max_fields-1) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    if (currentTableRow > 1) {
        removeClass(minusButton, 'disabled');
    } else {
        addClass(minusButton, 'disabled');
    }
}

/**
 * This function gets all selected elements (documents, customers, projects,
 * etc.) from a popup and assigns them into the edit or add form of document,
 * customer or project
 *
 * @param the_form - the current form elements
 * @param close - flag that defines whether to close the popup or not
 */
function updateReferers(the_form, close) {
    // defines the minimum referers number to display "toggle all" buttons
    var min_refs = 4;

    var list = the_form.elements['items[]'];
    var items = [];
    // manage array
    if (!list.length) {
        items[0] = list;
    } else {
        items = list;
    }

    // fields in destination window
    var context = defineContext();

    var div_id = the_form.name + '_referers';
    var referers_div = context.$(div_id);
    if (!referers_div || referers_div.tagName != 'DIV') {
        div_id = 'referers';
        referers_div = context.$(div_id);
    }
    if (!referers_div) {
        return false;
    }

    // collect ids of referers
    var referers = [];
    var r = context.$$('input[type="checkbox"][name="' + div_id + '[]"]');
    for (var i = 0; i < r.length; i++) {
        referers.push(r[i].value);
    }

    // URL for links to selected models
    var url = env.base_url + '?' + env.module_param + '='+ env.module_name;
    if (env.module_name != env.controller_name) {
        url += '&' + env.controller_param + '='+ env.controller_name + '&' + env.controller_name;
    } else {
        url += '&' + env.module_name;
    }
    url += '=view&view=';

    for (var i = 0; i < items.length; i++) {
        var item = items[i];
        var chk_id = (div_id != 'referers' ? the_form.name + '_' : '') + 'ref' + item.value;
        if (item.checked && !in_array(item.value, referers)) {
            referers_div.appendChild(
                Builder.node('input', {type: 'checkbox', name: div_id +'[]', id: chk_id, value: item.value, checked: 'checked'})
            );
            if (context.$('dependencies')) {
                referers_div.appendChild(
                    Builder.node('select', {'class': 'selbox', style: 'width: 400px;', name: 'origin[' + item.value + ']', id: 'origin_' + item.value}, [
                        Builder.node('option', {value: 'F2S'}, i18n.labels['F2S']),
                        Builder.node('option', {value: 'F2F'}, i18n.labels['F2F']),
                        Builder.node('option', {value: 'S2S'}, i18n.labels['S2S']),
                        Builder.node('option', {value: 'S2F'}, i18n.labels['S2F'])
                    ])
                );
            }
            var a = referers_div.appendChild(Builder.node('a', {href: url + item.value, target: '_blank'}));
            a.innerHTML = $('rf' + item.value).innerHTML || '...';
            referers_div.appendChild(Builder.node('br'));

            referers.push(item.value);
        } else if (item.checked && in_array(item.value, referers)) {
            var ref = context.$(chk_id);
            if (!ref.checked) {
                ref.checked = true;
            }
        }
    }

    var toggle_checkboxes_div =
        context.$(the_form.name + '_toggleCheckboxes') ?
        context.$(the_form.name + '_toggleCheckboxes') :
        context.$('toggleCheckboxes');
    if (toggle_checkboxes_div) {
        if (toggle_checkboxes_div.style.display == 'none' && referers.length > min_refs) {
            toggle_checkboxes_div.style.display = 'block';
        } else if (toggle_checkboxes_div.style.display == 'block' && referers.length <= min_refs) {
            toggle_checkboxes_div.style.display = 'none';
        }
    }

    if (close) {
        closePopupWindow();
    }

    context.focus();
}

/**
 * This function gets all selected elements (for customer's relations) from a
 * popup and assigns them into the edit or add form of document, customer or
 * project
 *
 * @param the_form - the current form elements
 * @param customer_type - the type of the company which the filter is called
 *        from
 * @param close - flag that defines whether to close the popup or not
 */
function updateCustomersRelations(the_form, customer_type, close) {
    // defines the minimum referers number to display "toggle all" buttons
    var min_refs = 4;

    // fields in current window
    var list = the_form.elements['items[]'];
    var items = [];
    // manage array
    if (!list.length) {
        items[0] = list;
    } else {
        items = list;
    }

    // fields in destination window
    var context = defineContext();

    var div_id = 'referers';
    var _referers = context.$(div_id);
    if (!_referers) {
        return false;
    }

    var current_customer_type = context.$('current_customer_type').value;

    var referers = [];
    var r = context.document.getElementsByName(div_id+'[]');
    if (r && r.length > 0) {
        for (var i = 0; i < r.length; i++) {
            referers.push(r[i].value);
        }
    } else if (r && r.value) {
        referers.push(r.value);
    }

    var refs_ = '';
    for (var i = 0; i < items.length; i++) {
        var item = items[i];
        var item_value_elements = item.value.split('_');
        var item_value = item_value_elements[0];
        var item_type = item_value_elements[1];
        if (item.checked && !in_array(item_value, referers)) {
            // dropdowns!!!!
            var dropdown = ' <select class="selbox" name="relative_type[' + item_value + ']" id="relative_type_' + item_value + '">';

            if (customer_type != item_type) {
                if (item_type == 'company') {
                    dropdown += '<option value="major_associate-child">' + i18n.labels['customers_major_associate_child'] + '</option>' +
                               '<option value="minor_associate-child">' + i18n.labels['customers_minor_associate_child'] + '</option>' +
                               '<option value="shareholder-child">' + i18n.labels['customers_shareholder_child'] + '</option>' +
                               '<option value="employee-child">' + i18n.labels['customers_employee_child'] + '</option>';
                } else {
                    dropdown += '<option value="major_associate-parent">' + i18n.labels['customers_major_associate_parent'] + '</option>' +
                               '<option value="minor_associate-parent">' + i18n.labels['customers_minor_associate_parent'] + '</option>' +
                               '<option value="shareholder-parent">' + i18n.labels['customers_shareholder_parent'] + '</option>' +
                               '<option value="employee-parent">' + i18n.labels['customers_employee_parent'] + '</option>';
                }
            } else {
                if (customer_type == 'company') {
                    dropdown += '<option value="filial_company-child">' + i18n.labels['customers_filial_company_child'] + '</option>' +
                               '<option value="filial_company-parent">' + i18n.labels['customers_filial_company_parent'] + '</option>' +
                               '<option value="joint_venture">' + i18n.labels['customers_joint_venture'] + '</option>';
                } else {
                    dropdown += '<option value="colleague">' + i18n.labels['customers_colleague'] + '</option>' +
                               '<option value="associate">' + i18n.labels['customers_associate'] + '</option>';
                }
            }

            dropdown += '</select>';

            var rf = $('rf' + item_value);

            var textarea = '<textarea class="areabox" name="notes[' + item_value + ']" id="notes_' + item_value + '" title="" onfocus="highlight(this)" onblur="unhighlight(this)"></textarea>';

            var row = _referers.insertRow(_referers.rows.length);

            var cell1 = row.insertCell(0);
            cell1.innerHTML = '<input type="checkbox" name="' + div_id + '[]" id="ref' + item_value + '" value="' + item_value + '" checked="checked" />';

            var cell2 = row.insertCell(1);
            cell2.innerHTML = rf.innerHTML;

            var cell3 = row.insertCell(2);
            cell3.innerHTML = dropdown;

            var cell4 = row.insertCell(3);
            cell4.innerHTML = textarea;

        } else if (item.checked && in_array(item_value, referers)) {
            var ref = context.$('ref' + item_value);
            if (!ref.checked) {
                ref.checked = true;
            }
        }
    }

    if (context.$('toggleCheckboxes')) {
        if (context.$('toggleCheckboxes').style.display == 'none' && context.$$('[name="referers[]"]').length > min_refs) {
            context.$('toggleCheckboxes').style.display = 'block';
        } else if (context.$('toggleCheckboxes').style.display == 'block' && context.$$('name="referers[]"').length <= min_refs) {
            context.$('toggleCheckboxes').style.display = 'none';
        }
    }

    if (close) {
        closePopupWindow();
    }

    context.focus();
}

/**
 * Reindexes the readonly rows of a table. Those that are placed below the
 * editable rows of a group table or GT2 (under gt2_delimeter row)
 *
 * @param table - the table id
 * @param last_row - the index (starting from 0) of the last editable row in the
 *        table
 */
function reindexReadonlyRows(table) {
    // get all the readonly rows within the specified table
    var readonly_rows = $$('#' + table.id + ' tr.readonly:not(.allow_delete)');

    if (readonly_rows && readonly_rows.length > 0) {

        // define the last row number
        if (!table.hasClassName('grouping_table2')) {
            var last_row = table.rows.length;
        } else {
            last_row = $$('#' + table.id + ' #gt2_delimeter')[0];

            if (last_row) {
                last_row = last_row.rowIndex;
            } else {
                return false;
            }
        }

        // define the index of the last row
        var new_index = last_row + readonly_rows.length-1;

        // start iterating the readonly rows bottom-up,
        // because the reindexing can overlap the ids of the variables
        for (var k = readonly_rows.length-1; k >= 0; k--) {
            var row = readonly_rows[k];

            // reindex the table row itself
            row.id = table.id + '_' + new_index;

            // get the cells' count
            var cells_count = row.cells.length;

            // change the fields' attributes in each cell
            for (var c = 0; c <cells_count; c++) {
                var cell = row.cells[c];
                cell.id = row.id + '_' + c;

                var cell_innerHTML = cell.innerHTML;
                var fields = cell_innerHTML.match(/id="?[\d\w_\-]*"?(?=(\s|\/?\>))/gi);
                var items = 0;
                if (fields) {
                    items = fields.length;
                }

                for (var i = 0; i < items; i++) {

                    // get the field that should be reindexed
                    var field_id = fields[i].replace(/^id="?([\d\w_\-]*)_[0-9]+"?$/gi, "$1");
                    if (field_id.match(/^id=/)) {
                        continue;
                    }
                    var field = $$('#' + table.id + ' #' + field_id + '_' + (new_index-1))[0];

                    if (!field) {
                        // it is possible that the cell is empty and no field is
                        // inserted
                        continue;
                    }

                    if (field.name) {
                        var field_name = field.name.replace(/\[\-?[0-9]*\]/, '');
                        var regex_name = new RegExp(field_name + '\[\-?[0-9]*\]','gi');
                        if (!table.hasClassName('grouping_table2')) {
                            cell_innerHTML = cell_innerHTML.replace(regex_name, field_name + '[' + (new_index - 1) + ']');
                        } else {
                            //special(negative) index for new rows in GT2
                            var field_name_index = field.name.replace(/.*\[(\-?[0-9]*)\]/, '$1');
                            if (field_name_index < 0) {
                                cell_innerHTML = cell_innerHTML.replace(regex_name, field_name + '[' + (-new_index) + ']');
                            }
                        }
                    }

                    var regex_id = new RegExp(field_id + '_[0-9]+','gi');
                    // when replacing id of hidden field of readonly radio, update only the field itself
                    if (field.type == 'hidden' && field.hasClassName('radio_hidden')) {
                        regex_id = new RegExp(field_id + '_[0-9]+\\b','gi');
                    }
                    var regex_row = new RegExp('row=[0-9]+','gi');

                    cell_innerHTML = cell_innerHTML.replace(regex_id, field_id + '_' + new_index);
                    cell_innerHTML = cell_innerHTML.replace(regex_row, 'row=' + new_index);
                }

                if (c == 0) {
                    if (cell_innerHTML.match(/^\s*[0-9]+\s*$/)) {
                        cell_innerHTML = new_index;
                    } else {
                        var regex_1 = new RegExp("\,\'[0-9]+\\'\\)",'gi');
                        cell_innerHTML = cell_innerHTML.replace(regex_1, ',\'' + new_index + '\')');
                        regex_1 = new RegExp('\>\s*[0-9]+','gi');
                        cell_innerHTML = cell_innerHTML.replace(regex_1,'>' + new_index);
                        regex_1 = new RegExp("(visibility:.*hidden)",'gi');
                        cell_innerHTML = cell_innerHTML.replace(regex_1, '');
                    }
                }

                cell.innerHTML = cell_innerHTML;
            }

            var scripts = row.getElementsByTagName('script');
            for (var j = 0; j < scripts.length; j++) {
                ajaxLoadJS(scripts[j]);
            }

            new_index--;
        }
    }
}

/**
 * Copies a row of a table and clones the elements in it
 *
 * @param table - the is the table id
 */
function addFilterRow(table) {
    // define the max allowed rows with fields
    var max_table_rows = env.max_group_rows;

    // get the group table element
    var tbl = $(table);

    // define the last row number
    var last_row = tbl.rows.length;
    if (last_row > max_table_rows) {
        return;
    }

    // insert a new row in the table
    var row = tbl.insertRow(last_row);

    // get the cells' count
    var cells_count = tbl.rows[last_row - 1].cells.length;

    // change the fields' attributes in each cell
    for (var c = 0; c < cells_count; c++) {
        var cell = row.insertCell(c);

        var field = tbl.rows[last_row - 1].cells[c].getElementsByTagName('select')[0];
        if (!field) {
            field = tbl.rows[last_row - 1].cells[c].getElementsByTagName('input')[0];
        }
        // var cell = row.insertCell(i+1);
        var el = field.cloneNode(true);
        if (el.tagName == 'SELECT') {
            el.value = field.value;
        }

        if (c == cells_count-1) {
            tbl.rows[last_row - 1].cells[c].style.display='block';
            cell.style.display='none';
        }

        cell.appendChild(el);

        // cTranslator is used (alternative keyboard inputs)
        // Install the keypressed events in the texts and textareas elements
        if (isDefined('cTranslator') && !el.readonly &&
            ((el.tagName.toLowerCase() == 'input' && el.type.toLowerCase() == 'text') || el.tagName.toLowerCase() == 'textarea')) {
            cTranslator.install(el);
        }
    }

    // manage the +/- buttons
    var plusButton = $(table + '_plusButton');
    var minusButton = $(table + '_minusButton');

    if (last_row < max_table_rows-1) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    if (last_row > 0) {
        removeClass(minusButton, 'disabled');
    } else {
        addClass(minusButton, 'disabled');
    }

}

/**
 * Removes by completely deleting the last row of a table.
 * ATTENTION: there is no way to restore removed rows
 *
 * @param table - the table id
 */
function removeFilterRow(table) {
    var max_table_rows = env.max_group_rows;
    var tbl = $(table);
    var lastRow = tbl.rows.length;
    if (lastRow > 1) {
        tbl.deleteRow(lastRow - 1);
    }
    tbl.rows[lastRow - 2].cells[tbl.rows[lastRow - 2].cells.length - 1].style.display='none';

    // manage the +/- buttons
    var plusButton = $(table + '_plusButton');
    var minusButton = $(table + '_minusButton');

    if (lastRow < max_table_rows+1) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    if (lastRow > 2) {
        removeClass(minusButton, 'disabled');
    } else {
        addClass(minusButton, 'disabled');
    }
}

/**
 * Disables a complete row of elements in a table.
 * Note: the disabled rows could be restored
 *
 * @param string table - id of the table
 * @param string num - the index of the row in the table
 */
function disableField(table, num) {

    var tbl = $(table);
    if (tbl.hasClassName('grouping_table2')) {
        var requestGT2calc = true;
    }

    // IMPORTANT: there are a readonly rows at the bottom of the table
    // they are not editable and they are separated by a delimiter
    // so the row index should incremented
    if (tbl.rows[num].id == 'gt2_delimeter' || tbl.rows[num].hasClassName('readonly') && !tbl.rows[num].hasClassName('readonly_start')) {
        // recognize the rows by its className
        num++;
    }

    //special behaviour if we have start delimiter too
    var delimiter_start = $$('#' + tbl.id + ' #gt2_delimeter_start')[0];
    if (delimiter_start) {
        if (num >= delimiter_start.rowIndex) {
            num++;
        }
    }

    //if we have batches we have to add some rows here
    var s = 1;
    while (s < num && s < 1000) {
        if (tbl.rows[s].hasClassName('batch_data')) {
            //add +1 row for num
            num ++;
        }
        s ++;
    }
    if (tbl.rows[num].hasClassName('batch_data')) {
        // + 1 row again as the row we stop previous cycle
        // is batch data row
        num ++;
    }
    var row = tbl.rows[num];
    if (row.hasClassName('search_inactive')) {
        return;
    }

    var row_innerHTML = row.innerHTML;
    var items = row_innerHTML.match(/id="?[\d\w_\-]*"?(?=(\s|\/?\>))/gi);

    for (var i = 0; i < items.length; i++) {
        var field = items[i].replace(/^id="?([\d\w_\-]*)"?$/gi, '$1');
        // field = $(field + '_' + num);
        field = $(field);
        if (!field) {
            // it is possible that the cell is empty and no field is inserted
            continue;
        }
        if (field.disabled && !field.hasClassName('input_inactive')) {
            continue;
        }
        if (field.disabled || field.tagName == 'A') {
            field.disabled = false;
            removeClass(field, 'input_inactive');
        }
        else if (!field.id.match(/(plus|minus)Button/)) {
            addClass(field, 'input_inactive');
            field.disabled = true;
        }
    }
    // mark the row as inactive
    row = tbl.rows[num];
    if (row.hasClassName('input_inactive')) {
        removeClass(row, 'input_inactive');
    } else {
        addClass(row, 'input_inactive');
    }

    if (requestGT2calc) {
        // we have to recalculate everything in the 2nd type grouping table
        gt2calc('total');
    }
}


/**
 * function to query specific module and controller
 * and to get the total records for list/search/filter actions
 *
 * @param params - object with parameters
 */
function getTotalResults(params) {

    //check if the params are valid
    if (!params.module || !params.action || !params.total_container || !params.pages_container) {
        return false;
    }

    var opt = {
        method: 'get',
        asynchronous: true,
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            eval('var result = ' + t.responseText + ';');
            //set total records count
            $$('span.' + params.session_param + '_total')[0].innerHTML = result.total;
            // set total selected if current selection is "Select all"
            if (typeof result.selected !== 'undefined' && $$('span.' + params.session_param + '_total')[0].up('td').down('.selected_items_span')) {
                $$('span.' + params.session_param + '_total')[0].up('td').down('.selected_items_span').innerHTML = result.selected;
            }
            //set total pages count
            if ($$('span.' + params.session_param + '_pages')) {
                $$('span.' + params.session_param + '_pages')[0].innerHTML = result.last_page;
            }
            //display "select(check) all" option if needed
            if (result.last_page > 1 && params.action != 'filter') {
                all = $('check_all_menu_all_items');
                all.style.display = '';
                var replaceTotal = function(el) {
                    var fn = el.getAttribute('onclick') ? el.attributes.onclick.value : '';
                    fn = fn.replace(/(total\:\s*)\d*(\,|\b)/, '$1' + result.total + '$2');
                    eval('el.onclick = function() {' + fn + '};');
                    el.setAttribute('onclick', fn);
                };
                replaceTotal(all);
                if (all.up('form')) {
                    all.up('form').select('input[type="checkbox"][name="items[]"][onclick]').each(replaceTotal);
                }
            }
            //change page number in link for the last page
            var index = params.pages_container.replace(/^.*_(\d+)$/, '$1');
            var last = $('pagination_last_' + index);
            if (last.tagName.match(/^a$/i)) {
                //change last page for <a>
                last.href = last.href.replace(/page=last/, 'page=' + result.last_page);
            } else if (last.tagName.match(/^span$/i)) {
                //change last page for <span>
                var fn = last.getAttribute('onclick') ? last.attributes.onclick.value : '';
                fn = fn.replace(/page=last/, 'page=' + result.last_page);
                eval('last.onclick = function() {' + fn + '};');
                last.setAttribute('onclick', fn);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //prepare AJAX request URL
    var url = env.base_url + '?' + env.module_param + '=' + params.module + '&';
    if (params.controller && params.controller != params.module) {
        url += env.controller_param + '=' + params.controller + '&' + params.controller + '=';
    } else {
        url += params.module + '=';
    }
    url += 'ajax_get_totals&action_requested=' + params.action + '&session_param=' + params.session_param;

    new Ajax.Request(url, opt);

    return true;

}

/**
 * Shows/hides details row toggled from a column in main row of a list.
 *
 * @param {Object} element - trigger element (div or table cell)
 * @param {Object} params - parameters for loading details, most notably field name and URL
 * @return void
 */
function showDetailsRow(element, params) {
    if (params.event) {
        // stop click event from propagating
        Event.stop(params.event);
    }
    element = $(element);
    var source_cell = element.tagName == 'TD' ? element : element.up('td');
    if (!source_cell) {
        return;
    }
    var main_row = $(source_cell.parentNode);
    var details_row = null,
        details_cell = null;

    // first handle the row and cell
    if (main_row.next() && main_row.next().hasClassName('details_row')) {
        details_row = main_row.next();
        if (details_row.visible()) {
            details_row.hide();
            source_cell.removeClassName('has_details_row');
            element.title = params['label_expand'] || i18n['labels']['expand'];
            return;
        } else {
            details_row.show();
        }
    } else {
        main_row.insert({'after': Builder.node('tr', {'class': 'details_row'})});
        details_row = main_row.next();
        details_row.observe(
            'click',
            function(event) {
                var el = event.target || event.srcElement || event.originalTarget;
                if (el.parentNode === this) {
                    this.hide();
                    if (this.previous()) {
                        this.previous().
                            select('td.has_details_row.' + (params.field || '') + '_switch').
                            each(function(el_switch) {
                                if (el_switch.down('.t_occupy_cell')) {
                                    el_switch.down('.t_occupy_cell').title =
                                        params['label_expand'] || i18n['labels']['expand'];
                                }
                                el_switch.removeClassName('has_details_row');
                            });
                    }
                }
            }
        );
    }
    if (!details_row.children.length) {
        details_row.insert({'top': Builder.node('td', {'colSpan': main_row.cells.length})});
    }
    details_cell = $(details_row.children[0]);

    // each container in details row should have a class name based on its
    // content (the field name in main row) so that it can be identified by
    // event handlers of switch elements in main row
    var container_class_name = (params.field || '') + '_container';
    details_cell.select('> div').map(function(d) {
        d[d.hasClassName(container_class_name) ? 'show' : 'hide']();
    });
    if (!details_cell.down(' > div.' + container_class_name)) {
        details_cell.insert({'bottom': Builder.node('div', {'class': container_class_name})});
    }

    var details_div = details_cell.down('div.' + container_class_name);
    if (details_div) {
        details_div = $(details_div);
        if (!details_div.innerHTML.strip()) {
            // load the content of details div
            if (params.url) {
                details_div.addClassName('loading');
                new Ajax.Request(
                    params.url,
                    {
                        method: 'post',
                        onComplete: function(t) {
                            if (!checkAjaxResponse(t.responseText)) {
                                details_div.hide();
                                return false;
                            }

                            details_div.removeClassName('loading');
                            details_div.innerHTML = t.responseText;
                            details_div.select('script').each(ajaxLoadJS);

                            source_cell.addClassName('has_details_row');
                            element.title = params['label_collapse'] || i18n['labels']['collapse'];

                            // add event handler for click of the div, if such is specified in params
                            if (typeof params.click_function === 'function') {
                                details_div.observe(
                                    'click',
                                    function(event) {
                                        params.click_function(event, params);
                                    }
                                );

                            }

                            Effect.Fade('loading');
                        },
                        on404: function(t) {
                            alert('Error 404: location "' + t.statusText + '" was not found.');
                        },
                        onFailure: function(t) {
                            alert('Error ' + t.status + ' -- ' + t.statusText);
                        }
                    }
                );
            }
        } else {
            source_cell.addClassName('has_details_row');
            element.title = params['label_collapse'] || i18n['labels']['collapse'];
        }
        new Effect.ScrollTo(details_cell);
    } else {
        details_row.hide();
    }
}

/**
 * Loads next page in an endless list
 *
 * @param {Event} event - click event
 * @param {Object} params - parameters for loading data, most notably URL
 * @return {boolean} - always return false
 */
function showMore(event, params) {
    event.preventDefault();

    if (params.url) {
        var el = event.target || event.srcElement || event.originalTarget;
        el.addClassName('hidden').adjacent('.loading.hidden')[0].removeClassName('hidden');

        new Ajax.Request(
            params.url,
            {
                method: 'post',
                onComplete: function(t) {
                    if (!checkAjaxResponse(t.responseText)) {
                        return false;
                    }
                    var tr = el.up('tr.show_more');
                    var tr_table = tr.up('table');
                    var tr_row_index = tr.rowIndex;
                    tr.replace(t.responseText);
                    // update focus
                    tr_table.rows[tr_row_index - 1].scrollIntoView(true);
                    tr_table.parentNode.scrollIntoView(true);
                    // clear first date row if duplicate with a row in table
                    if (tr_table.rows[tr_row_index] && tr_table.rows[tr_row_index].hasClassName('activity_date')) {
                        var date_class = tr_table.rows[tr_row_index].
                            classNames().
                            select(function(a) {
                                return /^activity_date_\d{4}-\d{2}-\d{2}$/.test(a);
                            })[0];
                        if (date_class && tr_table.select('tr.' + date_class).length > 1) {
                            tr_table.rows[tr_row_index].remove();
                        }
                    }
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            }
        );
    }

    return false;
}

/**
 * Event handler for redirecting to history action from history activity DIV,
 * highlighting clicked history entry and loading its audit
 *
 * @param {Event} event - click event
 * @param {Object} params - parameters for loading data, most notably URL
 * @return void
 */
function historyActivityGoToAudit(event, params) {
    if (isSubelementClicked()) {
        return;
    }
    var el = event.target || event.srcElement || event.originalTarget;
    if (el.tagName != 'TR') {
        el = el.up('tr');
    }
    if (el && el.id && el.id.match(/^history_\d+$/)) {
        var query_string = params.url.parseQuery();
        delete(query_string.history_activity);
        delete(query_string.source);
        query_string.audit = el.id.replace(/.*_(\d+)$/, '$1');
        // redirect to history action
        url = env.base_host + env.base_url + '?' + Object.toQueryString(query_string);
        redirect(url);

    }
}

/**
 * Displays lightbox for import of data from file into grouping table
 *
 * @param {string} table - table identifier
 * @param {Object} query_params - routing parameters
 * @param {string} element - original trigger element (only after confirmation)
 */
function importTableShowForm(table, query_params, element) {
    var confirmed = typeof element !== 'undefined',
        element = element || Event.element(window.event),
        element_container = $(getAutocompleteScope(element) || document.body),
        table = typeof table === 'object' ? table : element_container.down('table.t_grouping_table#' + table),
        parameters = {},
        content_element,
        default_errors = [i18n['messages']['error_no_access_to_action']];

    if (table) {
        table = $(table);
        if (table.hasClassName('grouping_table2')) {
            content_element = $(table.parentNode);
        } else if (table.id.match(/_\d+$/)) {
            content_element = table;
        }
    } else {
        // table not found
        displayNotificationFixed(displayNotificationFixed.formatContent(default_errors, 'error'));
        return;
    }

    // if table has any values, display confirmation for removing current data
    // before displaying lightbox for import
    if (!confirmed) {
        var value_elements = table.select('input, select, textarea');
        var value_elements_length = 0;
        for (var i = 0; i < value_elements.length; i++) {
            var ve = value_elements[i];
            if (!ve.name.match(/\[-?\d*\]$/) || ve.name.match(/_(formatted|readonly)\[-?\d*\]$/) || ve.disabled || ve.type == 'radio' && !ve.checked) {
                continue;
            }
            if (ve.value !== '') {
                // one is enough
                value_elements_length++;
                break;
            }
        }
        if (value_elements_length) {
            return confirmAction(
                'import_table',
                function(el) {
                    var table_id = $(el).up('table.t_grouping_table') ? $(el).up('table.t_grouping_table').id : '';
                    importTableShowForm(table_id, query_params, el);
                },
                element
            );
        }
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        parameters: parameters,
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                displayNotificationFixed(displayNotificationFixed.formatContent(default_errors, 'error'));
                return false;
            }
            if (!t.responseText) {
                if (!env.valid_login) {
                    redirectToLogin();
                }
                displayNotificationFixed(displayNotificationFixed.formatContent(default_errors, 'error'));
                Effect.Fade('loading');
                return;
            }
            var result = typeof t.responseJSON === 'object' && t.responseJSON !== null ? t.responseJSON : {};
            if (!result.content && !result.errors) {
                result.errors = default_errors;
            }
            if (result.errors && result.errors.length) {
                displayNotificationFixed(displayNotificationFixed.formatContent(result.errors, 'error'));
            } else if (result.content) {
                lb = new lightbox({
                    content: result.content,
                    title: result.title || '',
                    width: 620,
                    closeHandler: importTableRemoveFile,
                    content_element: content_element
                });
                lb.activate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    },
    url = env.base_url + '?';

    // if query string data is not passed as function parameter, try to construct it
    if (typeof query_params !== 'object') {
        query_params = {};
        var form_query = element.up('form').getAttribute('action');
        if (form_query) {
            form_query = form_query.parseQuery();
        }
        if (typeof form_query === 'object' && typeof form_query[env.module_param] === 'string' && form_query[env.module_param] != env.module_name) {
            // if form is not loaded in module but in custom location (dashlet, report)
            // form URL from form action data
            query_params[env.module_param] = form_query[env.module_param];
            if (typeof form_query[env.controller_param] !== 'undefined') {
                query_params[env.controller_param] = form_query[env.controller_param];
                query_params[form_query[env.controller_param]] = 'ajax_import_table';
                query_params['form_action'] = form_query[form_query[env.controller_param]];
            } else {
                query_params[form_query[env.module_param]] = 'ajax_import_table';
                query_params['form_action'] = form_query[form_query[env.module_param]];
            }
        } else {
            // form URL from environment data
            query_params[env.module_param] = env.module_name;
            if (env.module_name != env.controller_name) {
                query_params[env.controller_param] = env.controller_name;
                query_params[env.controller_name] = 'ajax_import_table';
            } else {
                query_params[env.module_name] = 'ajax_import_table';
            }
            query_params['form_action'] = env.action_name;
        }
        // try to find type field
        query_params.model_type =
            element_container.down('input#type') ?
            element_container.down('input#type').value :
            (element_container.down('select#type') ? element_container.down('select#type').value : '');
        // provide grouping identifier
        if (table.hasClassName('grouping_table2')) {
            query_params.gt2 = 1;
        } else if (table.id.match(/_\d+$/)) {
            query_params.grouping = table.id.replace(/.*_(\d+)$/, '$1');
        }
    }

    url += Object.toQueryString(query_params);

    new Ajax.Request(url, opt);
}

/**
 * Handler for processing response data after background file upload on file
 * selection from lightbox for file import into grouping table
 *
 * @param {Object} element - file input DOM element
 * @param {Object} result - response data or null on clearing of file or on error
 */
function importTableUpdateForm(element, result) {
    element = $(element);
    if (!element || !element.up || !element.up('form')) {
        return;
    }

    var form = $(element.up('form'));
    var column_fields = form.select('select.column_file');
    var columns = result !== null && result.columns ? result.columns : {};
    var options = [];
    var fld, opt;
    var custom_values = ['value'];

    for (var c in columns) {
        options.push({
            option_value: c,
            label: (custom_values.indexOf(c) == -1 ? c + ' ' : '') + columns[c]
        });
    }
    if (!options.length && column_fields.length) {
        fld = column_fields[0];
        for (var j = 0; j < fld.options.length; j++) {
            opt = fld.options[j];
            if (opt.value !== '') {
                options.push({
                    option_value: opt.value,
                    label: custom_values.indexOf(opt.value) == -1 ? opt.value : opt.label
                });
            }
        }
    }
    for (var i = 0; i < column_fields.length; i++) {
        setDropdownOptions(column_fields[i], options);
    }

    if (result !== null) {
        if (result.first_row && form.down('#import_first_row')) {
            form.down('#import_first_row').value = result.first_row;
        }
        if (result.last_row && form.down('#import_last_row')) {
            form.down('#import_last_row').value = result.last_row;
        }
        if (form.down('#import_key')) {
            // set or clear import key
            form.down('#import_key').value = typeof result.import_key !== 'undefined' ? result.import_key : '';
        }
    } else {
        if (form.down('#import_key') && form.down('#import_key').value) {
            // send request to clear file data on server
            importTableRemoveFile.bind(lb)();
        }
    }
}

/**
 * Replaces content of "default value" table cell in row with a field
 * corresponding to selected field option in "table column" dropdown
 *
 * @param {Object} element - dropdown from "table column"
 */
function importTableSetValueField(element) {
    var field_name = element.value,
        index = element.id.replace(/.*_(\d+)$/, '$1'),
        dest_container = $(element.parentNode).next(),
        vars = import_table_vars || {};
    if (dest_container) {
        dest_container.innerHTML = '';
    }
    if (!field_name || !vars[field_name] || !dest_container) {
        return;
    }

    // create a copy of the variable object
    var params = {};
    Object.extend(params, vars[field_name]);
    params.name = params.custom_name;
    params.index = index;
    params.title = params.label;
    params.first_empty = true;
    if (params.type == 'radio') {
        params.type = 'dropdown';
    } else if (params.type == 'autocompleter') {
        params.options = params.autocomplete;
        params.options.clear = !params.readonly;
    }
    if (+params.width) {
        if (params.type == 'autocompleter' && !params.readonly) {
            params.width -= 22;
        }
        params.width += 'px';
    }
    if (+params.height) {
        params.height += 'px';
    }

    var dest_element = createField(params, dest_container);

    // if default is fixed value, should selection of first column be changed as well?
    /*if (params.fixed) {
        dest_container = $(element.parentNode).previous();
        if (dest_container && $(dest_container).down('select.column_file')) {
            dest_element = $(dest_container).down('select.column_file');
            dest_element.value = 'value';
            dest_element.onchange();
        }
    }*/
}

/**
 * Manages (load/save/delete) saved configurations for import of data from
 * file into GT/GT2 table of model
 *
 * @param {Object} element - DOM element that function is called from
 * @param {string} action - action to perform
 */
function importTableManageConfig(element, action) {
    var url = '',
        form = $(element).up('form[action]'),
        config_container,
        config_field,
        config_id,
        target,
        errors = [];
    if (element && form) {
        form = $(form);
        url = form.action.replace(/(=ajax_import_table)(&|$)/, '$1_configurator$2');
        config_container = element.up('table');
        if (config_container) {
            config_container = $(config_container);
            config_field = config_container.down('#saved_imports_tables_configurators');
        }
    }
    if (!url || !config_container || !config_field) {
        // error message
        errors.push(i18n['messages']['error_compareVar']);
    }
    config_id = config_field.value;
    if (['delete', 'load'].indexOf(action) != -1) {
        if (isNaN(parseInt(config_id))) {
            errors.push(i18n['messages']['error_config_not_present']);
        }
    } else {
        if (config_id === '') {
            errors.push(i18n['messages']['error_config_not_selected']);
        }
    }
    if (errors.length) {
        displayNotificationFixed(displayNotificationFixed.formatContent(errors, 'error'));
        return;
    }

    url += '&config_action=' + action;
    if (['delete', 'save'].indexOf(action) != -1) {
        target = config_container.up('td');
    } else {
        target = config_container.up('div.lb_content');
    }
    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onComplete: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (!t.responseText) {
                if (!env.valid_login) {
                    redirectToLogin();
                }
                Effect.Fade('loading');
                return;
            }
            var result = typeof t.responseJSON === 'object' && t.responseJSON !== null ? t.responseJSON : {};
            if (!result.content && !result.errors) {
                result.errors = [i18n['messages']['error_no_access_to_action']];
            }
            if (result.errors && result.errors.length) {
                displayNotificationFixed(displayNotificationFixed.formatContent(result.errors, 'error'));
            } else {
                target.innerHTML = result.content;
                target = $(target);
                target.select('script').each(ajaxLoadJS);

                if (action == 'load') {
                    target.down('form').action = form.action;
                    // replace file element
                    if (form.down('#import_file').value) {
                        target.down('#import_file').parentNode.parentNode.replaceChild(
                            form.down('#import_file').parentNode,
                            target.down('#import_file').parentNode
                        );
                    }
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    new Ajax.Request(url, opt);

    return;
}

/**
 * Handler for closing of lightbox for file import into grouping table
 */
function importTableRemoveFile() {
    $('lightbox').setStyle({zIndex: ''});
    if (!(lb && lb.active || !this.lightboxContents || !this.params)) {
        return;
    }
    var form = $(this.lightboxContents).down('> #' + this.params.uniqid + ' form[action]');
    var import_key = '';
    if (form) {
        import_key = $(form).down('#import_key');
    }

    if (!form || !form.action || !import_key || !import_key.value) {
        return;
    }
    var remove_import_key = import_key.value;

    new Ajax.Request(
        form.action,
        {
            method: 'get',
            parameters: {
                remove_import_key: remove_import_key
            },
            onComplete: function(t) {
                if (import_key !== null && import_key.value == remove_import_key) {
                    import_key.value = '';
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        }
    );
}

/**
 * Submits form for file import into grouping table
 *
 * @param {Object} element - button that submits form
 * @return {boolean} - always false
 */
function importTableSubmit(element) {
    element = $(element);
    if (!element || !element.up || !element.up('form')) {
        return;
    }

    var errors = [];
    var form = $(element.up('form'));
    var column_fields = form.select('select.column_file');
    var column_fields2 = form.select('select.column_table');

    if (!Enumerable.all.call(
        form.select('input.import_param'),
        function(el) { return el !== null && el.value !== ''; })
    ) {
        errors.push(i18n['messages']['alert_empty_field']);
    } else if (parseInt(form.down('#import_first_row').value) > parseInt(form.down('#import_last_row').value)) {
        errors.push(i18n['messages']['error_compareVar'] + ': ' +
            form.down('#import_first_row').value + ' > ' + form.down('#import_last_row').value + '!');
    }
    if (!Enumerable.any.call(
        column_fields,
        function(el, idx) {
            return el && el.value !== '' && column_fields2[idx] && column_fields2[idx].value !== '';
        })
    ) {
        errors.push(i18n['messages']['please_select'] + ': ' +
            form.select('table table th label').
                pluck('innerText').
                map(function(a) { return a.replace(':', ''); }).
                join(' <=> ') + '!');
    }
    if (errors.length) {
        displayNotificationFixed(displayNotificationFixed.formatContent(errors, 'error'));
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    // disable modification of lightbox while loading and returning data
    $('lightbox').setStyle({zIndex: '1'});
    var tableForm = $(lb.params.content_element).up('form');

    // submit form with AJAX, file is already uploaded, do not upload it again;
    // submit current form data because autocompleters might depend on it
    new Ajax.Request(
        form.action,
        {
            mathod: 'post',
            parameters: Form.serialize(tableForm) + '&' + Form.serialize(form),
            onComplete: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                if (!t.responseText) {
                    if (!env.valid_login) {
                        redirectToLogin();
                    }
                    Effect.Fade('loading');
                    return;
                }
                var result = typeof t.responseJSON === 'object' && t.responseJSON !== null ? t.responseJSON : {};
                if (!result.content && !result.errors) {
                    result.errors = [i18n['messages']['error_no_access_to_action']];
                }
                if (result.errors && result.errors.length) {
                    $('lightbox').setStyle({zIndex: ''});
                    displayNotificationFixed(displayNotificationFixed.formatContent(result.errors, 'error'));
                } else {
                    if (lb.params.content_element && lb.params.content_element.parentNode) {
                        var content_element = $(lb.params.content_element);
                        var container_element = $(content_element.parentNode);

                        // replace table element with the fetched content in DOM
                        var tmp_container = content_element.ownerDocument.createElement('div');
                        tmp_container.innerHTML = result.content;
                        var new_content_element = tmp_container.children[0];
                        container_element.replaceChild(new_content_element, content_element);
                        // eval scripts without removing them
                        $(new_content_element).select('script').each(ajaxLoadJS);

                        // run calculations
                        if (result.calculate && container_element.up('form')) {
                            runFormCalculations(container_element.up('form'), false);
                        }
                        //import is completed (trigger event to allow other js functions to hook up)
                        container_element.up('form').fire('import:completed');
                    }
                    if (form.down('#import_key')) {
                        form.down('#import_key').value = '';
                    }

                    if (result.model_id) {
                        let modelIDInput = document.querySelector('#model_id');
                        let idInput = document.querySelector('#id');
                        if (!modelIDInput) {
                             modelIDInput = document.createElement('input');
                             modelIDInput.id='model_id';
                             modelIDInput.name='model_id';
                             modelIDInput.type='hidden';
                             idInput.after(modelIDInput);
                        }
                        modelIDInput.value = result.model_id;
                    }
                    lb.deactivate();
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        }
    );

    return false;
}

/**
 * Uploads file in background mode (if specified) and executes specified
 * function afterwards
 *
 * @param {Object} element - file input DOM element
 * @param {Object} callback_function - function to be called after (un)successful upload -
 *      it takes 2 parameters - the input field and the response as an object
 */
function backgroundUploadFile(element, callback_function) {
    // invalid parameters, exit
    if (!element || !element.form || !element.form.action || typeof callback_function !== 'function') {
        return;
    }
    // no file selected, perform success function with empty result and exit
    if (!(typeof element.files !== 'undefined' && element.files.length > 0 || typeof element.files === 'undefined' && element.value)) {
        callback_function(element, null);
        return;
    }
    // allow other ways for providing URL address to upload file to
    var upload_url = element.form.action;
    // set a flag to differentiate between regular form submit and submit into iframe or via XMLHttpRequest
    upload_url = upload_url + (upload_url.indexOf('?') === -1 ? '?' : '&') + 'upload_via_ajax=1';

    Effect.Center('loading');
    Effect.Appear('loading');

    // iframe fallback
    if (typeof FormData === 'undefined') {
        return uploadViaAjax(
            element.form,
            {
                action: upload_url,
                onLoad: function(result) {
                    if (typeof result !== 'object' || result === null) {
                        result = {};
                    }
                    if (!Object.values(result).length) {
                        result.errors = [i18n['messages']['error_no_access_to_action']];
                    }
                    if (result.errors && result.errors.length) {
                        displayNotificationFixed(displayNotificationFixed.formatContent(result.errors), 'error');
                    }
                    callback_function(element, result);
                    Effect.Fade('loading');
                }
            }
        );
    }

    /**
     * first two parameters specify that form is submitted via
     * XMLHttpRequest.send() with "multipart/form-data" contentType header
     * @see https://developer.mozilla.org/en-US/docs/Web/API/FormData
     * @see https://github.com/sstephenson/prototype/commit/f63d7f7bb59bfa11adb21a79c3322a0f4d7ed7d3
     * */
    var opt = {
        postBody: (new FormData(element.form)),
        contentType: null,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (!t.responseText) {
                if (!env.valid_login) {
                    redirectToLogin();
                }
                Effect.Fade('loading');
                return;
            }
            var result = typeof t.responseJSON === 'object' && t.responseJSON !== null ? t.responseJSON : {};
            if (!Object.values(result).length) {
                result.errors = [i18n['messages']['error_no_access_to_action']];
            }
            if (result.errors && result.errors.length) {
                displayNotificationFixed(displayNotificationFixed.formatContent(result.errors), 'error');
                result = null;
            }
            callback_function(element, result);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(upload_url, opt);
}

/* MODULE SPECIFIC FUNCTIONS */
/* ========================= */
/**
 * Selects/Deselects all radio buttons Used in MynZoom email settings section
 *
 * @param form - the DOM element form
 * @param items - switch type, 'none' or 'all'
 */
function select_radio(form, items) {
    var form = document.forms[form];

     for (var i = 0; i < form.elements.length; i++) {
        if (form.elements[i].type == 'radio' && form.elements[i].id.indexOf('email_yes') != -1 && items == 'all') {
            form.elements[i].checked="checked";
        }
        if (form.elements[i].type == 'radio' && form.elements[i].id.indexOf('email_no') != -1 && items == 'none') {
            form.elements[i].checked="checked";
        }
     }
}

/**
 * loads My nZoom layouts via AJAX
 *
 * @param element - current span element
 * @param user - ID of the user whose settings have to be loaded
 */
function mynZoomLoad(element, user) {
    // Keyname of layout to be loaded
    var layout = element.title;

    var opt = {
        method: 'get',
        onComplete: function(t) {
            // Check if the user is logged in
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }

            // Get the data container
            var container = $('layouts_container');
            // Fill it with the AJAX response data
            container.innerHTML = t.responseText;

            // Get the menu items (the menu tab bars)
            var menu_items = $$('#layouts_tabs div.zpMenuNzoom div div div[id^="zpMenu"]');
            // Prepare a class name which makes a tab looks selected
            var selected = 'menu-path';
            // Unselect all tabs
            for (var i = 0; i < menu_items.length; i++) {
                // If it`s not an object then continue with the next one
                if (typeof(menu_items[i]) != 'object') {
                    continue;
                }
                // Unselect this tab
                removeClass(menu_items[i], selected);
            }
            // Make the current tab look selected (i.e. add class to the div holder)
            addClass(element.parentNode.parentNode.parentNode.parentNode.parentNode, selected);

            $('layout').form.action = $('layout').form.action.replace(/layout=.*(&|$)/, 'layout=' + layout + '$1');
            // Get the scripts
            var scripts = container.getElementsByTagName('script');
            // Prepare new var for the scripts
            var scripts_reversed = [];
            // Reverse the array (ref.: Bug 1807 Comment 11)
            for (var j = scripts.length; j > 0; j--) {
                scripts_reversed.push(scripts[j-1]);
            }
            // Load the scripts
            for (var j = scripts_reversed.length; j > 0; j--) {
                ajaxLoadJS(scripts_reversed[j-1]);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=users&users=mynzoom&mynzoom=' + user + '&layout=' + layout + '&use_ajax=1';

    new Ajax.Request(url, opt);

    return true;
}

/**
 * Function to manage(set) all values for default results per page
 *
 * @param {Object} element - dropdown element
 */
function setAllDefaultRPP(element) {
    var table = $('interface_rpp');
    var elements = table.getElementsByTagName('select');
    for (var i in elements) {
        if (typeof(elements[i]) == 'object') {
            elements[i].value = element.value;
        }
    }
    element.focus();
}

/**
 * Starts a timer to report the activity for the task
 *
 * @param element - button element or hidden field which holds event id
 * @param model_name - usually task
 * @param model_id - the id of the model
 */
function startWatch(element, model_name, model_id) {
    var now = new Date();
    var start_date = now.getFullYear() + '-' +
                    ((now.getMonth() < 9) ? '0'+(now.getMonth()+1) : now.getMonth()+1) + '-' +
                    ((now.getDate() < 10) ? '0'+now.getDate() : now.getDate()) + ' ' +
                    ((now.getHours() < 10) ? '0'+now.getHours() : now.getHours()) + ':' +
                    ((now.getMinutes() < 10) ? '0'+now.getMinutes() : now.getMinutes()) + ':00';

    var opt = {
        method: 'post',
        asynchronous: false,
        onSuccess: function(t) {
            // alert(t.responseText);
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (t.responseText && t.responseText.match(/^data=/)) {
                eval(t.responseText);

                if (!data.result) {
                    if (data.event_error) {
                        alert(i18n['messages']['start_watch_invalid_event']);
                        return false;
                    }
                    started_timer = $('m_started_timers').getElementsByTagName('img');
                    if (started_timer && started_timer.length) {
                        model = started_timer[0].className.replace(/icon_button pointer /, '');
                        alert(i18n['messages']['start_watch_invalid_' + model]);
                        Effect.Pulsate(started_timer[0], {pulses: 3});
                    } else {
                        alert(i18n['messages']['start_watch_invalid_other']);
                    }
                    return false;
                }

                var startwatch = null, stopwatch = null;
                if ($('startwatch') && $('stopwatch')) {
                    // add/edit in task, document, project
                    startwatch = $('startwatch');
                    stopwatch = $('stopwatch');
                } else if ($('startwatch_' + model_name + '_' + model_id) && $('stopwatch_' + model_name + '_' + model_id)) {
                    // statements screen
                    startwatch = $('startwatch_' + model_name + '_' + model_id);
                    stopwatch = $('stopwatch_' + model_name + '_' + model_id);
                } else if (element.hasClassName('event_id')) {
                    // allocate in task
                    var row_idx = element.id.replace(/^.*_(\d+)$/, '$1');
                    startwatch = $('startwatch_' + row_idx);
                    stopwatch = $('stopwatch_' + row_idx);
                }
                if (data.globalIndicatorHtml) {
                    document.querySelector('#m_stopwatchbar_box').innerHTML = data.globalIndicatorHtml;
                }
                let container = startwatch.closest('.stopwatch_div');
                if (container) {
                    container.classList.add('nz--active');
                } else {
                    container = startwatch.closest('.stopwatch_span');
                    container.classList.add('nz--active');
                }

            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var module = model_name + 's';
    var url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=ajax_watch' +
          '&model_id=' + model_id + '&model=' + model_name + '&date='+ start_date + '&action=start';

    // collect and submit data for planned time event if starting watch for such
    if (element.hasClassName('event_id') && parseInt(element.value) > 0) {
        var post = new Object();
        var fields = ['event_id', 'status', 'description', 'owner', 'priority', 'event_start_date', 'event_start_time', 'event_end_time', 'duration'];
        for (var i = 0; i < fields.length; i++) {
            var f = $(element.id.replace('event_id', fields[i]));
            if (f != null) {
                post[fields[i] + '[]'] = f.value;
            }
        }
        opt.parameters = Object.toQueryString(post);
    }

    new Ajax.Request(url, opt);

    return true;
}

/**
 * Stops timer and redirects to reports (timesheets)
 *
 * @param element - the current form element
 * @param model_name - usually task
 * @param model_id - the id of the model
 */
function stopWatch(element, model_name, model_id) {

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            // alert(t.responseText);
            if (t.responseText && t.responseText.match(/^data=/)) {
                eval(t.responseText);

                if (!data.result) {
                    // timer already stopped or invalid
                    alert(i18n['messages']['stop_watch_invalid']);
                    var startwatch = null, stopwatch = null;
                    if ($('startwatch') && $('stopwatch')) {
                        // add/edit in task, document, project
                        startwatch = $('startwatch');
                        stopwatch = $('stopwatch');
                    } else if ($('startwatch_' + model_name + '_' + model_id) && $('stopwatch_' + model_name + '_' + model_id)) {
                        // statements screen
                        startwatch = $('startwatch_' + model_name + '_' + model_id);
                        stopwatch = $('stopwatch_' + model_name + '_' + model_id);
                    } else if (element.hasClassName('event_id')) {
                        // allocate in task
                        var row_idx = element.id.replace(/^.*_(\d+)$/, '$1');
                        startwatch = $('startwatch_' + row_idx);
                        stopwatch = $('stopwatch_' + row_idx);
                    }

                    document.querySelector('.stopwatch_div').classList.remove('nz--active');

                    $('m_started_timers').innerHTML = '';
                    return false;
                }

                $('m_started_timers').innerHTML = '';

                var now = new Date();
                var end_date = now.getFullYear() + '-' +
                                ((now.getMonth() < 9) ? '0'+(now.getMonth()+1) : now.getMonth()+1) + '-' +
                                ((now.getDate() < 10) ? '0'+now.getDate() : now.getDate()) + ' ' +
                                ((now.getHours() < 10) ? '0'+now.getHours() : now.getHours()) + ':' +
                                ((now.getMinutes() < 10) ? '0'+now.getMinutes() : now.getMinutes()) + ':00';

                var module = model_name + 's';
                url = env.base_host + env.base_url + '?' + env.module_param + '=' + module + '&' + module +
                        '=timesheets&timesheets=' + model_id + '&startperiod='+ data.start_date +
                        '&endperiod='+ end_date + (parseInt(data.event_id) > 0 ? '&event_id=' + data.event_id : '') +
                        '&period_type=dates#add_timesheet';
                redirect(url);
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var module = model_name + 's';
    var url = env.base_url + '?' + env.module_param + '=' + module + '&' + module + '=ajax_watch' +
          '&model_id=' + model_id + '&model=' + model_name + '&action=stop';
    new Ajax.Request(url, opt);

    return true;
}

/**
 * Used for choosing edit type for events
 *
 * @param element - the current form element
 * @return boolean true|false
 */
function selectEventEditType(element) {
    if (confirm(i18n['messages']['confirm_event_edit_type'])) {
        element.href = element.href.replace(/&new_recurrence.*/, '');
    }
    return true;
}

/**
 * Used for choosing remind type for events
 *
 * @param element - the current form element
 * @return boolean true|false
 */
function selectEventRemindType(element) {
    if (confirm(i18n['messages']['confirm_event_remind_type'])) {
        element.href = element.href.replace(/&new_recurrence.*/, '');
    }
    return true;
}

/**
 * @param event_id
 * @param event_date
 */
function stopReminder(event_id, event_date, reminder_source) {

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
        },
        on404: function(t) {
            alert('Error 404');
        },
        onFailure: function(t) {
            alert('Error');
        }
    };

    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_stop_reminder&id=' + event_id + '&event_date=' + event_date + '&reminder_source=' + reminder_source;
    new Ajax.Request(url, opt);

    // deactivate the lightbox
    lb.deactivate();
    // start the reminder checks over
    manageReminders();

    return true;
}

/**
 * @param event_id
 * @param event_date
 */
function countReminder(event_id, event_date, remind_after, reminder_source) {

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
        },
        on404: function(t) {
            alert('Error 404');
        },
        onFailure: function(t) {
            alert('Error');
        }
    };

    var url = env.base_url + '?' + env.module_param + '=events&events=ajax_count_reminder&id=' + event_id + '&event_date=' + event_date + '&remind_after=' + remind_after + '&reminder_source=' + reminder_source;
    new Ajax.Request(url, opt);

    // deactivate the lightbox
    lb.deactivate();
    // start the reminder checks over
    manageReminders();

    return true;
}

function checkPasswordStrength(pfield, should_be_strong) {

    var minpwlength = (should_be_strong) ? 8 : 4;
    var fairpwlength = (should_be_strong) ? 10 : 8;
    var strengthlevel = '';
    var toggle_pass_strength = (checkPasswordStrength.arguments[2]) ? true : false;

    var password = pfield.value;

    if (toggle_pass_strength) {
        var field1 = $((pfield.id.replace(/2/, '')));
        var field2 = $(field1.id + '2');
        if (field1.value || field2.value) {
            $('pass_strength').style.visibility='visible';
            $(field1.id + '_validation').style.visibility = 'visible';
            $(field2.id + '_validation').style.visibility = 'visible';
            if (password.length == 0) {
                $(field1.id + '_validation').src = $(field1.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            }
        } else {
            $('pass_strength').style.visibility='hidden';
            $(field1.id + '_validation').src = $(field1.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            $(field2.id + '_validation').src = $(field2.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            $(field1.id + '_validation').style.visibility = 'hidden';
            $(field2.id + '_validation').style.visibility = 'hidden';
        }
    }

    if (password.match(/^.*(?=.*\d)(?=.*[a-z])(?=.*[A-Z]).*$/) && password.match(/^[^\"\']{8,64}$/)) {
        strengthlevel = 'strong';
    } else if (password.length >= fairpwlength) {
        if (password.match(/[0-9]*/)) {
            strengthlevel = 'medium';
        } else {
            strengthlevel = 'fair';
        }
    } else if (password.length >= minpwlength) {
        strengthlevel = 'weak';
    } else if (password.length != 0) {
        strengthlevel = 'short';
    }

    // set password strength class
    $(pfield.id + '_strength').className = (strengthlevel) ? 'pass_' + strengthlevel : '';
    $(pfield.id + '_strength_text').innerHTML = (strengthlevel) ? i18n['labels']['pass_' + strengthlevel] : '';

    // set password1 validity
    var valid = false;
    if (should_be_strong && strengthlevel == 'strong') {
        valid = true;
    } else if (!should_be_strong && (strengthlevel == 'strong' || strengthlevel == 'medium' || strengthlevel == 'fair')) {
        valid = true;
    }

    if (password.length != 0) {
        if (valid) {
            $(pfield.id + '_validation').src = $(pfield.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/message.png');
        } else {
            $(pfield.id + '_validation').src = $(pfield.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
        }
        $(pfield.id + '_validation').style.visibility = 'visible';
    } else {
        $(pfield.id + '_validation').style.visibility = 'hidden';
    }

    checkPasswordsMatch($(pfield.id + '2'), should_be_strong);
}

function checkPasswordsMatch(pfield, should_be_strong) {
    var field1 = $((pfield.id.replace(/2/, '')));
    var field2 = $(field1.id + '2');

    password1 = field1.value;
    password2 = field2.value;

    var toggle_pass_strength = (checkPasswordsMatch.arguments[2]) ? true : false;

    if (toggle_pass_strength) {
        if (field1.value || field2.value) {
            $('pass_strength').style.visibility='visible';
            $(field1.id + '_validation').style.visibility = 'visible';
            $(field2.id + '_validation').style.visibility = 'visible';
            if (password1.length == 0) {
                $(field1.id + '_validation').src = $(field1.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            }
        } else {
            $('pass_strength').style.visibility='hidden';
            $(field1.id + '_validation').src = $(field1.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            $(field2.id + '_validation').src = $(field2.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            $(field1.id + '_validation').style.visibility = 'hidden';
            $(field2.id + '_validation').style.visibility = 'hidden';
        }
    }

    if (password1.length != 0) {
        // check if the two passwords match
        if (password1 == password2) {
            // check if the first password is incorrect (check by the icon name)
            if ($(field1.id + '_validation').src.match(/error/)) {
                $(field2.id + '_validation').src = $(field2.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
            } else {
                $(field2.id + '_validation').src = $(field2.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/message.png');
            }
        } else {
            $(field2.id + '_validation').src = $(field2.id + '_validation').src.replace(/(.*)\/.*/, '$1' + '/error.png');
        }
        $(field2.id + '_validation').style.visibility = 'visible';
    } else if (password2.length != 0) {
        $(field1.id + '_validation').style.visibility = 'visible';
        $(field2.id + '_validation').style.visibility = 'visible';
    } else {
        $(field2.id + '_validation').style.visibility = 'hidden';
    }
}

/**
 * A function used to build search definitions depending on the selected filter
 *
 * @param element - (optional) the element where we can select a filter
 *      (a dropdown for search filter (1st column), search comparison (2nd column) or for type values (3rd column))
 * @param row_index - (optional) index of table row to update
 */
function setSearchDef(element) {

    if (!element && setSearchDef.arguments[1] && setSearchDef.arguments[1] > -1) {
        element = setSearchDef.arguments[1];
        element = $('search_container').rows[element];
        element = element.id.replace(/search_container_/, '', element);
        element = $('search_fields_' + element);
    }
    if (!element) {
        reloadSearchVars(null);
        return;
    } else if (!isAddVarsSwitch(element.value)) {
    } else if (element.className.match(/(input|search)_inactive/)) {
        reloadSearchVars(element);
        return;
    }
    // if element is disabled
    var disabled = (element && element.hasClassName('input_inactive')) ? true : false;

    if (element && element.id.match(/search_fields/)) {
        var index = parseInt(element.id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
        var prev = $('search_fields_prev_' + index);
        if (prev.value == element.value && isAddVarsSwitch(prev.value)) {
            // if we come here and function has a second parameter,
            // it is called after enabling previously disabled filter
            if (setSearchDef.arguments[1]) {
                reloadSearchVars(element);
            }
            return;
        }
        if (isAddVarsSwitch(prev.value)) {
            reloadSearchVars(element);
            element = $('search_fields_' + index);
        }
        prev.value = element.value;
        var var_name = element.value;
        var search_options = new Object();
        if (!var_name.match(/(^|\()a__/)) {
            search_options = advanced_search_obj;
        } else {
            search_options = additional_search_obj;
        }
        // create dropdown with compare conditions
        var opt_groups = var_name ? search_options[var_name].compare_options : '';

        var params = {type: 'dropdown',
                      name: 'compare_options',
                      width: '150px',
                      index: index,
                      sequences: 'setSearchDef(this)',
                      opt_groups: opt_groups,
                      disabled: disabled};

        container = $('search_container_' + index + '_2');
        container.innerHTML = '';
        var operator = createField(params, container);
        if (operator.options.length <= 1 || var_name.match(/\.((search_)?archive|deleted)$/) || var_name.match(/^(fp\.annulled|(fir|fer)\.active)$/)) {
            operator.style.visibility = 'hidden';
        }
        if (disabled) {
            operator.disabled = true;
            addClass(operator, 'input_inactive');
        }
        operator = operator.value;
    }

    if (element && element.id.match(/compare_options/)) {
        var index = parseInt(element.id.replace(/[a-zA-z_]+(\d+)$/, '$1'));
        var var_name = $('search_fields_' + index).value;
        if (!var_name.match('a__')) {
            search_options = advanced_search_obj;
        } else {
            search_options = additional_search_obj;
        }
        var operator = element.value;
        var opt_groups = search_options[var_name].compare_options;
    }
    var values = null;
    if (operator) {
        current_type = $('values_' + index);
        if (current_type) {
            current_type = current_type.getAttribute('itype');
        }
        // get the type of the next field
        for(var o in opt_groups) {
            for (var op in opt_groups[o]) {
                if (operator == opt_groups[o][op].option_value) {
                    var type = opt_groups[o][op].itype;
                    if (opt_groups[o][op].opt_groups) {
                        var opt_groups = opt_groups[o][op].opt_groups;
                        var options = null;
                        break;
                    } else if (opt_groups[o][op].options) {
                        var options = opt_groups[o][op].options;
                        var opt_groups = null;
                        break;
                    } else {
                        var options = null;
                        var opt_groups = null;
                        break;
                    }
                }
            }
            // options are found so break FOR
            if (!opt_groups) {
                break;
            }
        }
        var container = $('search_container_' + index + '_3');
        if (!type) {
            container.innerHTML = '';
            return false;
        }
        if (type == current_type && type != 'dropdown' && type != 'autocompleter') {
            return false;
        }
        if (isAddVarsSwitch(var_name)) {
            var sequences = 'setSearchDef(this);';
        } else {
            var sequences = '';
        }
        if (type == 'autocompleter') {
            if (options.fill_options) {
                // keep only fill options for fields in the search form (if such are set)
                var fill_options = [];
                for (var o = 0; o < options.fill_options.length; o++) {
                    if (options.fill_options[o].match(/^\$values(_(oldvalue|autocomplete))?\s=>\s/)) {
                        fill_options.push(options.fill_options[o]);
                    }
                }
                options.fill_options = fill_options;
            } else {
                options.fill_options = [];
            }
            if (options.filters && typeof(options.filters) == 'object' && !Object.isArray(options.filters)) {
                for (var filt in options.filters) {
                    if (options.filters[filt].match(/\$/)) {
                        // if filter is looking for a value from a variable in page, unset it
                        delete options.filters[filt];
                    }
                }
            }
            if (options.optional_filters && typeof(options.optional_filters) == 'object' && !Object.isArray(options.optional_filters)) {
                for (var filt in options.optional_filters) {
                    if (options.optional_filters[filt].match(/\$/)) {
                        // if filter is looking for a value from a variable in page, unset it
                        delete options.optional_filters[filt];
                    }
                }
            }
            // always display clear button in search
            options.clear = 1;
        }
        // create field for value to compare with
        var params = {type: type,
                      name: 'values',
                      width: (type == 'autocompleter' ? '178px' : '200px'),
                      index: index,
                      sequences: sequences,
                      options: options,
                      opt_groups: opt_groups,
                      enable_inactive_options: true,
                      disabled: disabled};

        var container = $('search_container_' + index + '_3');
        container.innerHTML = '';
        values = createField(params, container);
        if (disabled) {
            values.disabled = true;
            addClass(values, 'input_inactive');
        }
        if (!isAddVarsSwitch(var_name)) {
            values = '';
        }
    }
    if (element.id.match(/values/)) {
        reloadSearchVars(element);
    } else if (values) {
        reloadSearchVars(values);
    }
}

/**
 * Check if value of search filter is the one that requires search definitions
 * (additional variables and others) to be reloaded
 *
 * @param {string} value - value of search filter
 * @return {Boolean} - result of check
 */
function isAddVarsSwitch(value) {
    if (!value) {
        return false;
    } else {
        return value == search_additional_vars_switch || value.match(/\.type$/);
    }
}

/**
 * Reload search definitions according to current filters by type or lack of.
 * Also update columns when in add/edit in dashlets module.
 *
 * @param {Object} element - element from modified row
 * @return {Boolean}
 */
function reloadSearchVars(element) {

    var rows = $('search_container').rows;
    var values = '', current_row;
    try {
        current_row = element.parentNode.parentNode;
    } catch(e) {
        current_row = null;
    }
    for (var r = 1; r <= rows.length; r++) {
        var search_field = $('search_fields_' + r);
        var compare_field = $('compare_options_' + r);
        var value = $('values_' + r);
        if (rows[r] && !rows[r].hasClassName('search_inactive') &&
            value && !value.hasClassName('input_inactive') &&
            isAddVarsSwitch(search_field.value)) {
            // negative search by type
            if (compare_field.value != '= \'%s\'') {
                values = '&model_types[]=';
                break;
            }
            values += '&model_types[]=' + value.value;
        }
    }
    // if no filters were found, we have to check if module has type and
    // force reloading of search defs without any type filters
    if (!values) {
        for (var vn in advanced_search_obj) {
            if (vn.match(/\.type$/)) {
                values += '&model_types[]=';
                break;
            }
        }
    }
    // if module has no search by type at all (function should not be called but just make sure)
    if (!values) {
        return false;
    }

    var additional_url = '', module_name = null;
    if ((env.module_name == 'dashlets' || (env.module_name == 'finance' && env.controller_name == 'analysis_items')) &&
    env.action_name != 'search' && env.action_name != 'list' && env.action_name != 'view') {
        // we are creating//editing dashlet so add some extra parameters
        module_name = $('module_name').value;

        // split the module name to module and controller
        var module_name_elements = module_name.split('_');
        var current_module_name = module_name_elements[0];
        module_name_elements.splice(0,1);
        var current_controller_name = module_name_elements.join('_');
        module_name = [current_module_name, current_controller_name];

        additional_url = '&module_name=' + module_name[0] + '&controller_name=' + module_name[1];
    } else if (env.module_name == 'filters' && env.action_name == 'edit' && $('filter_module') && $('filter_controller')) {
        // predefined filters
        additional_url = '&module_name=' + $('filter_module').value + '&controller_name=' + $('filter_controller').value;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (!t.responseText) {
                redirectToLogin();
                return;
            }
            eval(t.responseText);
            rows = $('search_container').rows;
            var params = {type: 'dropdown',
                          name: 'search_fields',
                          first_empty: true,
                          width: '150px',
                          sequences: 'setSearchDef(this);'};

            if (basic_search_obj) {
                advanced_search_obj = basic_search_obj;
            }
            if (additional_search_obj) {
                params.opt_groups = {basic_vars: advanced_search_obj,
                                     additional_vars: additional_search_obj};
                params.optgroup_label_source = 'config';
            } else {
                params.options = advanced_search_obj;
            }
            for (var r = 1; r <= rows.length; r++) {
                if (rows[r]) {
                    index = rows[r].id.replace(/search_container_/, '');
                    var old_field = $('search_fields_' + index);
                    params.index = index;
                    params.value = old_field.value;
                    var new_field = createField(params);
                    if (rows[r] != current_row) {
                        if (new_field.value != old_field.value) {
                            if (!rows[r].hasClassName('search_inactive')) {
                                if (!rows[r].hasClassName('input_inactive')) {
                                    disableField('search_container', index);
                                    removeClass(rows[r], 'input_inactive');
                                }
                                addClass(rows[r], 'search_inactive');
                                processSearchFirstCell('search_container', index);
                            }
                        } else {
                            if (rows[r].hasClassName('search_inactive')) {
                                removeClass(rows[r], 'search_inactive');
                                if (rows[r].hasClassName('input_inactive')) {
                                    new_field.disabled = true;
                                    addClass(new_field,'input_inactive');
                                } else {
                                    addClass(rows[r], 'input_inactive');
                                    disableField('search_container', index);
                                }
                                processSearchFirstCell('search_container', index);
                            } else {
                                if (rows[r].hasClassName('input_inactive')) {
                                    new_field.disabled = true;
                                    addClass(new_field,'input_inactive');
                                }
                            }
                            old_field.parentNode.appendChild(new_field);
                            old_field.parentNode.removeChild(old_field);
                        }
                    } else {
                        if (rows[r].hasClassName('input_inactive')) {
                            new_field.disabled = true;
                            addClass(new_field,'input_inactive');
                        }
                        old_field.parentNode.appendChild(new_field);
                        old_field.parentNode.removeChild(old_field);
                    }
                }
            }

            var params_sort = {type: 'dropdown',
                               name: 'sort',
                               width: '100%',
                               sequences: '',
                               optgroup_label_source: 'config',
                               opt_groups: sortables};
            for (var r = 1; r <= 3; r++) {
                if ($('sort_' + r) != null) {
                    index = r;
                    var old_field_sort = $('sort_' + index);
                    params_sort.index = index;
                    params_sort.value = old_field_sort.value;
                    var new_field_sort = createField(params_sort);
                    var container = old_field_sort.parentNode;
                    container.innerHTML = '';
                    container.appendChild(new_field_sort);
                }
            }

            // update type-dependent filters (sub-statuses, payment statuses, tags)
            if (module_name) {
                updateSearchStatuses(module_name);
                if (module_name[0] == 'finance' && module_name[1].match(/^(incomes|expenses)_reasons$/)) {
                    updateSearchPaymentStatuses(module_name[1]);
                }
                if (advanced_search_obj && advanced_search_obj['tags.tag_id']) {
                    updateSearchTags();
                }

                // update columns in add/edit of dashlet
                if (typeof additional_columns != 'undefined' &&
                env.module_name == 'dashlets' && (env.action_name == 'add' || env.action_name == 'edit')) {
                    updateDashletColumns(additional_columns);
                }
            } else {
                updateSearchStatuses([env.module_name, env.controller_name]);
                if (env.module_name == 'finance' && env.controller_name.match(/^(incomes|expenses)_reasons$/)) {
                    updateSearchPaymentStatuses(env.controller_name);
                }
                if (advanced_search_obj && advanced_search_obj['tags.tag_id']) {
                    updateSearchTags();
                }
            }
            Effect.Fade('loading');
            return false;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '='+ env.module_name;
    if (env.module_name != env.controller_name) {
        url += '&' + env.controller_param + '='+ env.controller_name + '&' + env.controller_name;
    } else {
        url += '&' + env.module_name;
    }

    url += '=getoptions' + values + additional_url;

    // pass parameter when searching for trademarks in order to reload the corresponding search definitions
    if (window.location.search.match(/(filters\[%3Ccustomer_trademark%3E\]=1|session_param=filter_trademark_nomenclature)/)) {
        url += '&session_param=filter_trademark_nomenclature';
    }

    new Ajax.Request(url, opt);
}

/**
 * Add/Remove additional columns to/from the columns dropdown of the dashlets.
 *
 * @param {(Array|Boolean)} additional_columns - array with data for additional columns or false if none
 * @return {Boolean}
 */
function updateDashletColumns(additional_columns) {

    // Get the first columns dropdown field (i.e. the old field)
    var columns_dropdown_old = $('columns_1');

    // If no such field
    if (!columns_dropdown_old) {
        // Just exit
        return true;
    }

    // Get the optgroups of the old field
    var columns_dropdown_old_optgroups = columns_dropdown_old.getElementsByTagName('optgroup');

    // If there are no optgroups
    var have_optgroups = false;
    var columns_dropdown_old_options;
    if (columns_dropdown_old_optgroups.length == 0) {
        // Get all options from the dropdown (i.e. get the basic columns)
        columns_dropdown_old_options = columns_dropdown_old.options;
    } else {
        // Get the options from the first optgroup (i.e. get the basic columns)
        columns_dropdown_old_options = columns_dropdown_old_optgroups[0].getElementsByTagName('option');
        have_optgroups = true;
    }

    // Prepare the basic columns
    var basic_columns = new Array();
    for (var i = 0; i < columns_dropdown_old_options.length; i++) {
        basic_columns.push({
            label:        columns_dropdown_old_options[i].label,
            option_value: columns_dropdown_old_options[i].value});
    }

    // Prepare the params for the new dropdown field
    var params  = new Object();
    params.type = 'dropdown';

    // Prepare an array to keep a list of all columns (basic and additional)
    var new_options = new Array();

    // If there are any additional columns
    if (additional_columns) {
        // Prepare the basic columns and the additional columns as optgroups
        params.opt_groups = {
            basic_vars:      basic_columns,
            additional_vars: additional_columns};
        params.optgroup_label_source = 'config';

        // Get the list of columns
        for (var i = 0; i < basic_columns.length; i++) {
            new_options.push(basic_columns[i]['option_value']);
        }
        for (var i = 0; i < additional_columns.length; i++) {
            new_options.push(additional_columns[i]['option_value']);
        }
    } else {
        // If there are no additional columns and there were no optgroups
        if (!have_optgroups) {
            // Just exit
            return true;
        }

        // Prepare the basic columns as the only options of the columns dropdown
        params.options = basic_columns;
        for (var i = 0; i < basic_columns.length; i++) {
            new_options.push(basic_columns[i]['option_value']);
        }
    }

    // Get the count of all active columns dropdowns
    var max_columns = $$('#columns_settings select[id^="columns_"]').length;

    // If there are any columns dropdowns
    if (max_columns > 0) {
        // Replace each of the dropdowns
        for (var r = 1; r <= max_columns; r++) {
            // Get the old field
            var columns_dropdown_old      = $('columns_' + r);
            // Prepare a custom id for the new field
            params.custom_id              = 'columns_' + r;
            // Prepare a name for the new field
            params.name                   = 'columns[' + (r-1) + ']';
            // Get selected option from the old field
            params.value                  = columns_dropdown_old.value;
            params.title                  = columns_dropdown_old.title;
            // If the selected value for this dropdown doesn't exist in the new options
            if (new_options.indexOf(columns_dropdown_old.value) < 0) {
                // Uncheck the visibility checkbox for this dropdown
                $('visible_' + r).checked = false;
            }
            // Build the new field
            var columns_dropdown_new      = createField(params);
            // Replace the old field with the new one
            columns_dropdown_old.parentNode.replaceChild(columns_dropdown_new, columns_dropdown_old);
        }
    }
}

/**
 * A function used to process statuses and sub-statuses in search filters
 *
 * @param {Array} module_name - array with names of module and controller
 */
function updateSearchStatuses(module_name) {
    var controller_name = module_name[1] != module_name[0] ? module_name[1] : '';
    module_name = module_name[0];
    if (['documents', 'tasks', 'projects', 'contracts', 'finance'].indexOf(module_name) == -1 ||
        module_name == 'finance' && ['incomes_reasons', 'expenses_reasons', 'warehouses_documents'].indexOf(controller_name) == -1) {
        return false;
    }
    var var_name = (module_name == 'contracts' ? 'co' : module_name.substring(0,1)) +
        (controller_name ? controller_name.split('_').map(function(el) { return el.substring(0,1); }).join('') : '') +
        '.status';
    // if search definition is not found (layout is hidden)
    if (!advanced_search_obj[var_name]) {
        return false;
    }

    var params = {type: 'dropdown',
                  name: 'values',
                  width: '200px',
                  sequences: '',
                  options: []};
    var abs_lbl = i18n['labels']['absolute'];
    for (var c in advanced_search_obj[var_name].compare_options[abs_lbl]) {
        if (advanced_search_obj[var_name].compare_options[abs_lbl][c].opt_groups) {
            params.opt_groups = advanced_search_obj[var_name].compare_options[abs_lbl][c].opt_groups;
        } else {
            params.options = advanced_search_obj[var_name].compare_options[abs_lbl][c].options;
        }
        break;
    }
    updateSearchValues(var_name, params);
}

/**
 * A function to change the payment statuses of incomes/expenses reasons
 *
 * @param {string} controller_name - the name of the finance controller
 */
function updateSearchPaymentStatuses(controller_name) {
    // define the var name
    var var_name = (controller_name == 'expenses_reasons') ? 'fer.payment_status' : 'fir.payment_status';
    if (!advanced_search_obj[var_name]) {
        return;
    }

    // get the search rows
    var rows = $('search_container').rows;

    // flag to point if the payment status invoiced has to be included
    var include_invoiced_status = false;

    // get if only the proforma invoice type is selected
    for (var r = 1; r <= rows.length; r++) {
        var search_field = $('search_fields_' + r);
        var compare_field = $('compare_options_' + r);
        var value = $('values_' + r);
        if (rows[r] && !rows[r].hasClassName('search_inactive') &&
            value && !value.hasClassName('input_inactive') &&
            isAddVarsSwitch(search_field.value)) {
            // include invoiced status when there is 1) positive search 2) just for the proforma type
            if (compare_field.value != '= \'%s\'' || !(value.value == 2 || value.value == 21)) {
                include_invoiced_status = false;
                break;
            } else {
                include_invoiced_status = true;
            }
        }
    }

    // get the current options
    var compare = advanced_search_obj[var_name].compare_options[i18n['labels']['absolute']];
    var current_options = '';
    for (var c in compare) {
        if (advanced_search_obj[var_name].compare_options[i18n['labels']['absolute']][c].options.length) {
            current_options = advanced_search_obj[var_name].compare_options[i18n['labels']['absolute']][c].options;
            break;
        }
    }

    // include the invoiced status or exclude it in the current option set
    var invoiced_option_set = false;
    if (include_invoiced_status) {
        for(var j = 0; j < current_options.length; j++) {
            if (current_options[j].option_value == 'invoiced') {
                invoiced_option_set = true;
            }
        }

        if (!invoiced_option_set) {
            current_options.push({
                label:        i18n['labels']['invoiced_status_invoiced'],
                option_value: 'invoiced'});
        }
    } else {
        for(var j = 0; j < current_options.length; j++) {
            if (current_options[j].option_value == 'invoiced') {
                current_options.splice(j, 1);
                break;
            }
        }
    }

    var params = {type: 'dropdown',
                  name: 'values',
                  width: '200px',
                  sequences: '',
                  options: current_options};

    // replace the old options with the new ones
    for (var c in compare) {
        advanced_search_obj[var_name].compare_options[i18n['labels']['absolute']][c].options = current_options;
    }

    // replace the existing dropdowns with payment statuses if needed
    updateSearchValues(var_name, params);
}

/**
 * A function used to process tags in search filters
 */
function updateSearchTags() {
    var params = {type: 'dropdown',
                  name: 'values',
                  width: '200px',
                  sequences: '',
                  options: [],
                  enable_inactive_options: 1};
    var var_name = 'tags.tag_id';
    var abs_lbl = i18n['labels']['absolute'];
    for (var c in advanced_search_obj[var_name].compare_options[abs_lbl]) {
        if (advanced_search_obj[var_name].compare_options[abs_lbl][c].opt_groups) {
            params.opt_groups = advanced_search_obj[var_name].compare_options[abs_lbl][c].opt_groups;
        } else {
            params.options = advanced_search_obj[var_name].compare_options[abs_lbl][c].options;
        }
        break;
    }
    updateSearchValues(var_name, params);
}

/**
 * Goes through all search filters and replaces 'values' field of those for
 * specified variable with a new field with specified parameters
 *
 * @param {string} var_name - variable name (value of an option from 'search_fields' dropdown of a search filter)
 * @param {Object} params - array with common parameters for creating new field
 */
function updateSearchValues(var_name, params) {
    var rows = $('search_container').rows;
    for (var r = 1; r <= rows.length; r++) {
        if (rows[r] && $('search_fields_' + r).value == var_name) {
            var index = rows[r].id.replace(/search_container_/, '');
            var prev = $('search_fields_prev_' + index);
            var old_field = $('values_' + index);
            params.index = index;
            params.value = old_field.value;
            var new_field = createField(params);

            if (new_field.value != old_field.value && !isAddVarsSwitch(prev.value)) {
                if (!rows[r].hasClassName('search_inactive')) {
                    if (!rows[r].hasClassName('input_inactive')) {
                        disableField('search_container', index);
                        removeClass(rows[r], 'input_inactive');
                    }
                    addClass(rows[r], 'search_inactive');
                    processSearchFirstCell('search_container', index);
                }
            } else {
                if (rows[r].hasClassName('search_inactive')) {
                    removeClass(rows[r], 'search_inactive');
                    if (rows[r].hasClassName('input_inactive')) {
                        new_field.disabled = true;
                        addClass(new_field,'input_inactive');
                    } else {
                        addClass(rows[r], 'input_inactive');
                        disableField('search_container', index);
                    }
                    processSearchFirstCell('search_container', index);
                } else {
                    if (rows[r].hasClassName('input_inactive')) {
                        new_field.disabled = true;
                        addClass(new_field,'input_inactive');
                    }
                }
                var container = rows[r].cells[3];
                container.innerHTML = '';
                container.appendChild(new_field);
            }
        }
    }
}

/**
 * A function used to add/remove logical operators for search panel
 *
 * @param table - the id of the container
 * @param action - add / remove
 */
function processLogical(table, action) {

    table = $(table);
    var last_index = table.rows.length-1;

    // remove the logical field from the last row
    var container = table.rows[last_index].cells[4];
    container.innerHTML = '';

    if (action == 'add' && (last_index - 1) > 0) {
        // create logical operators dropdown
        var params = {type: 'dropdown',
                      name: 'logical_operator',
                      custom_id: 'logical_operator',
                      class_name: 'short',
                      index: (last_index - 1)
                     };
        params.options = [];
        params.options[0] = {label: i18n['labels']['and'],
                             option_value: 'AND'};
        params.options[1] = {label: i18n['labels']['or'],
                             option_value: 'OR'};
        var container = table.rows[last_index-1].cells[4];
        container.innerHTML = '';
        if (container.disabled) {
            params.disabled = true;
            params.class_name += ' input_inactive';
        }
        createField(params, container);
    }
    /*
     * if (action == 'remove') { var container =
     * table.rows[last_index].cells[4]; container.innerHTML = ''; }
     */
    return false;
}

/**
 * A function used to add needed functionality for the first cell in the search
 * panel
 *
 * @param {string} table - the id of the container
 * @param {?number} row_index - (optional) index of table row to update; if not specified, last row is updated
 */
function processSearchFirstCell(table) {

    table = $(table);
    var index = 0, remove_disable = false, row;
    if (processSearchFirstCell.arguments.length > 1) {
        index = processSearchFirstCell.arguments[1];
        row = table.rows[index];
        // search filter is incompatible with other filters
        if (row.hasClassName('search_inactive')) {
            remove_disable = true;
        }
    } else {
        index = table.rows.length - 1;
        row = table.rows[index];
    }

    var cell_0 = row.cells[0];
    if (cell_0 && index > 0) {
        let html = '';

        // the arguments for the special first column are prepared customly
        html = `<img src="${env.themeUrl}images/small/delete.png" class="hide_row" height="12" width="12" alt="${i18n['labels']['delete']}" title="${i18n['labels']['delete']}" onclick="processSearchDef('hide', '${index}');" />`;
        if (!remove_disable) {
            removeClass(cell_0, 'input_inactive');
            html += ` <a href="javascript: void(0);" onclick="processSearchDef('disable', '${index}');" title="${i18n['labels']['deactivate']}">${index}</a>`;
        } else {
            var content = '';
            if ($('search_fields_' + index).value.match(/\.status$/)) {
                content = i18n['messages']['invalid_status'];
            } else if ($('search_fields_' + index).value == 'tags.tag_id') {
                content = i18n['messages']['invalid_tag'];
            } else if ($('search_fields_' + index).value.match(/(^|\()a__/)) {
                content = i18n['messages']['invalid_additional_var'];
            } else {
                content = i18n['messages']['invalid_basic_var'];
            }
            html += ` <img height="11" width="11" border="0" class="help" alt="" src="${env.themeUrl}images/small/info.png" />`;
        }
        cell_0.innerHTML = html;
        cell_0.align = 'left';
        cell_0.noWrap = 'nowrap';
    }
}

function createAutocompleteField(params, container) {
    let disabled = params.disabled;
    let name = params.name;
    let id = params.custom_id ? params.custom_id : params.name;
    let index = parseInt(params.index);
    if (index) {
        name += '[' + (index-(params.eq_indexes?0:1)) + ']';
        id += '_' + index;
    } else {
        index = params.index;
        if (index) {
            name += '[' + index + ']';
            id += '_' + index;
        }
    }
    let context, eval_context;
    if (params.context) {
        context = params.context.document;
        eval_context = params.context;
    } else {
        context = document;
        eval_context = window;
    }
    let clear = params.options.clear || 0,
        combobox = params.options.combobox && !params.readonly || 0,
        element_old, element_div, element_script,
        element_clear_link = '', element_clear_img = '',
        element_combobox_link = '', element_combobox_img = '';


    // create elements in FireFox
    element = context.createElement('input');
    element_autocomplete = context.createElement('input');
    element_old = context.createElement('input');

    element_div = context.createElement('div');

    element_script = context.createElement('script');

    element.name = name;
    element_autocomplete.name = name.replace(/(\[\d+\])$/, '_autocomplete' + '$1');
    element_autocomplete.setAttribute('uniqid', uniqid('', true).replace('.', ''));
    element_old.name = name.replace(/\[(\d+)\]$/, '_oldvalue_' + (index + 1));

    element_div.name = 'suggestions_' + element_autocomplete.getAttribute('uniqid');
    if (clear) {
        element_clear_link = context.createElement('a');
        element_clear_img = context.createElement('i');
        element_clear_img.classList.add('material-icons','nz-input-icon','nz-md-backspace');
        element_clear_img.innerHTML = 'backspace';
    }
    if (combobox) {
        element_combobox_link = context.createElement('a');
        element_combobox_img = context.createElement('i');
        element_combobox_img.classList.add('material-icons','combobox_button');
        element_combobox_img.innerHTML = 'expand_more';
    }

    params.options.uniqid = element_autocomplete.getAttribute('uniqid');

    // set elements classes
    addClass(element_autocomplete, 'txtbox namebox autocompletebox' + (combobox ? ' combobox' : ''));
    addClass(element_div, 'autocompletebox autocomplete_positioned');
    element_div.style.display = 'none';

    // set elements types
    element.setAttribute('type', 'hidden');
    element_autocomplete.setAttribute('type', 'text');
    element_old.setAttribute('type', 'hidden');

    element_script.setAttribute('type', 'text/javascript');

    // set elements id
    element.setAttribute('id', id);
    element_autocomplete.setAttribute('id', id.replace(/(_\d+)$/, '_autocomplete' + '$1'));
    element_old.setAttribute('id', id.replace(/(_\d+)$/, '_oldvalue' + '$1'));

    element_div.setAttribute('id', 'suggestions_' + element_autocomplete.getAttribute('uniqid'));

    // set elements onblur and onfocus events
    element_autocomplete.onfocus = function () {
        highlight(this);
    };
    element_autocomplete.onblur = function () {
        unhighlight(this);
        setTimeout(function () {
            cancelAutocompleter(params.options);
        }, 1000);
    };
    element_autocomplete.oncontextmenu = function () {
        return false;
    };
    element_autocomplete.ondrop = function () {
        return false;
    };

    element_autocomplete.setAttribute('onFocus', 'highlight(this);');
    element_autocomplete.setAttribute('onBlur', 'unhighlight(this);setTimeout(\'cancelAutocompleter(params_' + element_autocomplete.getAttribute('uniqid') + ')\', 1000);');
    element_autocomplete.setAttribute('onDrop', 'return false;');
    element_autocomplete.setAttribute('onContextMenu', 'return false;');

    element_script.text = 'var params_' + element_autocomplete.getAttribute('uniqid') + ' = ' + Object.toJSON(params.options) + ';\n';
    element_script.text += 'initAutocompleter(params_' + element_autocomplete.getAttribute('uniqid') + ');';

    element_old.value = '';

    if (clear) {
        element_clear_link.setAttribute('href', 'javascript:void(0);');
        element_clear_link.setAttribute('onClick', 'clearAutocompleteItems(params_' + element_autocomplete.getAttribute('uniqid') + ');');
        eval('element_clear_link.onclick = function() { clearAutocompleteItems(params_' + element_autocomplete.getAttribute('uniqid') + '); };');
        element_clear_link.innerHTML += ' ';
        element_clear_link.classList.add('nz-input-controls');
        element_clear_link.appendChild(element_clear_img);
    }

    if (combobox) {
        element_combobox_link.setAttribute('href', 'javascript:void(0);');
        element_combobox_link.setAttribute('onClick', 'toggleAutocompleteItems(params_' + element_autocomplete.getAttribute('uniqid') + ');');
        eval('element_autocomplete.onclick = element_combobox_link.onclick = function() { toggleAutocompleteItems(params_' + element_autocomplete.getAttribute('uniqid') + '); };');
        element_combobox_link.appendChild(element_combobox_img);
    }

    if (disabled) {
        element.disabled = true;
        element_autocomplete.disabled = true;
        element_old.disabled = true;
    }

    if (+params.readonly) {
        element_autocomplete.readOnly = true;
        addClass(element_autocomplete, 'readonly');
    }

    if (params.value && params.value_id) {
        element_autocomplete.value = element_old.value = params.value;
        element.value = params.value_id;
    }

    // append the elements
    if (container) {
        container.appendChild(element_div);
        container.appendChild(element_autocomplete);
        container.appendChild(element);
        container.appendChild(element_old);
        container.appendChild(element_script);
        if (element_combobox_link) {
            container.appendChild(element_combobox_link);
        }
        if (element_clear_link) {
            container.appendChild(element_clear_link);
        }
    }

    // script is already eval-ed when added to container
    if (eval_context.$(element_autocomplete.id) && !eval_context.$(element_autocomplete.id).init) {
        eval_context.eval(element_script.text);
    }

    const custom_class = params.class_name || params.custom_class || '';
    if (custom_class) {
        element_autocomplete.classList.add(custom_class)
        addClass(element_autocomplete, custom_class);
    }

    if (params.width) {
        element_autocomplete.style.width = params.width;
    } /*else {
        if (params.options.clear) {
            element_autocomplete.style.width = '90%';
        } else {
            element_autocomplete.style.width = '100%';
        }
    }*/

    if (params.title) {
            element_autocomplete.title = params.title;
    }

    return element;
}

/**
 * A function used to create different fields
 *
 * @param params - the definitions for the element that will be created
 * @param container - the parent element for the field
 */
function createField(params, container) {
    let type = params.type;
    let index = parseInt(params.index);
    container = $(container);
    let value = params.value;
    let disabled = params.disabled;
    let context, eval_context;
    if (params.context) {
        context = params.context.document;
        eval_context = params.context;
    } else {
        context = document;
        eval_context = window;
    }

    let name = params.name;
    let id = params.custom_id ? params.custom_id : params.name;
    params.custom_id = id;
    if (index) {
        if (params.eq_indexes) {
            name += '[' + index + ']';
        } else {
            name += '[' + (index-1) + ']';
        }
        id += '_' + index;
    } else {
        index = params.index;
        if (index) {
            name += '[' + index + ']';
            id += '_' + index;
        }
    }

    let element = '', element_f = '';

    // create DATE or DATETIME field
    if (type == 'date' || type == 'datetime') {
        const regex1 = new RegExp(params.name,'gi');
        let name_f = name.replace(regex1, params.name + '_formatted', name);
        // var name_ft = name.replace(regex, params.name + '_formatted_trigger', name);
        const regex2 = new RegExp(params.custom_id,'gi');
        let id_f = id.replace(regex2, params.custom_id + '_formatted', id);

        // definitions for the Zapatec calendar
        let visible_format, hidden_format, show_time, className;
        if (type == 'date') {
            visible_format = env.date_short;
            hidden_format = '%Y-%m-%d';
            show_time = false;
            className = 'datebox';
        } else {
            visible_format = env.date_mid;
            hidden_format = '%Y-%m-%d %H:%M:00';
            show_time = true;
            className = 'datetimebox';
        }

        try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
            element_f = context.createElement('<input name="' + name_f + '">');
            // var trigger = context.createElement('<img>');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
            element_f = context.createElement('input');
            element_f.name = name_f;
            // var trigger = context.createElement('img');
        }

        let stFn = false;
        if (params.disallow_before) {
            element.setAttribute('disallow_before', params.disallow_before);
            stFn = 'disallowDates';
        }
        if (params.disallow_after) {
            element.setAttribute('disallow_after', params.disallow_after);
            stFn = 'disallowDates';
        }

        // set element's attributes
        addClass(element, className);
        element.setAttribute('id', id);
        element.setAttribute('type', 'hidden');

        addClass(element_f, 'txtbox ' + className);
        element_f.setAttribute('type', 'text');
        element_f.setAttribute('id', id_f);

        element_f.onfocus = function() {
            highlight(this); datetimePositionMouseCursor(this);
        };
        if (type == 'date') {
            element_f.onblur = function() {
                unhighlight(this); validateDate(this, -1); formatDate(this);
            };
        } else {
            element_f.onblur = function() {
                unhighlight(this); validateDate(this, -1); validateTime(this, -1); formatDate(this);
            };
        }
        element_f.onmousedown = function() {
            calendarInit(element_f.id, element_f.id, show_time, visible_format, hidden_format, stfn);
        };
        /*
         * trigger.onmousedown = function() { calendarInit(element_f.id,
         * trigger.id, show_time, visible_format, hidden_format , false); }
         */
        element_f.onkeydown = function() {
            return isAllowedDateKey(event);
        };
        element_f.onkeyup= function() {
            return changeKey(this, event, filterDate);
        };

        element_f.setAttribute('onFocus', 'highlight(this); datetimePositionMouseCursor(this);');
        element_f.setAttribute('onMouseDown', 'calendarInit(this.id, this.id, ' + show_time + ', \'' + visible_format + '\',\'' + hidden_format + '\', ' + stFn +');');
        if (type == 'date') {
            element_f.setAttribute('onBlur', 'unhighlight(this); validateDate(this, -1); formatDate(this);');
        } else {
            element_f.setAttribute('onBlur', 'unhighlight(this); validateDate(this, -1); validateTime(this, -1); formatDate(this);');
        }
        element_f.setAttribute('onKeyDown', 'return isAllowedDateKey(event);');
        element_f.setAttribute('onKeyUp', 'return changeKey(this, event, filterDate);');

        /*
         * addClass(trigger, 'calendar_trigger') trigger.setAttribute('src',
         * env.themeUrl + 'images/calendar.png'); trigger.setAttribute('id',
         * name_ft); trigger.setAttribute('border', '0');
         * trigger.setAttribute('onMouseDown', 'calendarInit(\'' + element.id +
         * '\', this.id, ' + show_time + ', \'' + visible_format + '\',\'' +
         * hidden_format + '\', false);');
         */

        let value_f = '';
        if (value) {
            let date = parseISODate(value);
            if (!date) {
                date = parseFormattedDate(value);
            }
            if (date) {
                if (type == 'date') {
                    value = date.format('Y-m-d');
                    value_f = date.format('d.m.Y');
                } else {
                    value = date.format('Y-m-d H:i:00');
                    value_f = date.format('d.m.Y, H:i');
                }
            } else {
                value = '';
            }
        }
        if (value) {
            element.value = value;
            element_f.value = value_f;
        } else {
            if (type == 'date') {
                element_f.value = defaultDate;
            } else {
                element_f.value = defaultDateTime;
            }
        }
        if (disabled) {
            element_f.disabled = true;
        }

        if (params.readonly) {
            element_f.readOnly = true;
            addClass(element_f, 'readonly');
        }

        if (params.onchange) {
            try {
                eval_context.eval('element.onchange = function() {' + params.onchange + '}');
            } catch (e) {

            }
            element.setAttribute('onChange', params.onchange);
        }

        // append the elements
        if (container != null) {
            container.appendChild(element);
            container.appendChild(element_f);
            // container.appendChild(trigger);
        }
        // eval(calendarInit(element_f.id, trigger.id, show_time,
        // visible_format, hidden_format , false));
        eval_context.eval(calendarInit(element_f.id, element_f.id, show_time, visible_format, hidden_format, stFn));
    }

    // create time field
    if (type == 'time') {
        const regex1 = new RegExp(params.name,'gi');
        let name_f = name.replace(regex1, params.name + '_formatted', name);
        const regex2 = new RegExp(params.custom_id,'gi');
        let id_f = id.replace(regex2, params.custom_id + '_formatted', id);

        try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
            element_f = context.createElement('<input name="' + name_f + '">');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
            element_f = context.createElement('input');
            element_f.name = name_f;
        }

        // set element's attributes
        addClass(element, 'timebox txtbox hidden');
        element.setAttribute('type', 'hidden');
        element.setAttribute('id', id);

        addClass(element_f, 'timebox txtbox');
        if (!params.width) {
            addClass(element_f, 'short');
        }
        element_f.setAttribute('type', 'text');
        element_f.setAttribute('id', id_f);

        element_f.onfocus = function() {
            highlight(this); datetimePositionMouseCursor(this);
        };
        element_f.onblur = function() {
            unhighlight(this); validateTime(this, -1); formatDate(this);
        };
        element_f.onkeydown = function() {
            return isAllowedDateKey(event);
        };
        element_f.onkeyup= function() {
            return changeKey(this, event, filterDate);
        };

        element_f.setAttribute('onFocus', 'highlight(this); datetimePositionMouseCursor(this);');
        element_f.setAttribute('onBlur', 'unhighlight(this); validateTime(this, -1); formatDate(this);');
        element_f.setAttribute('onKeyDown', 'return isAllowedDateKey(event);');
        element_f.setAttribute('onKeyUp', 'return changeKey(this, event, filterDate);');

        if (value) {
            element.value = value;
            element_f.value = value;
        } else {
            element.value = '';
            element_f.value = defaultTime;
        }
        if (disabled) {
            element_f.disabled = true;
        }
        if (params.readonly) {
            element_f.readOnly = true;
            addClass(element_f, 'readonly');
        }
        if (params.onchange) {
            try {
                eval_context.eval('element.onchange = function() {' + params.onchange + '}');
            } catch (e) {

            }
            element.setAttribute('onChange', params.onchange);
        }
        // append the elements
        if (container != null) {
            container.appendChild(element_f);
            container.appendChild(element);
        }
    }

    if (type == 'radio') {
        //TODO add support for radio
        type = 'dropdown';
    }

    // create dropdown field
    if (type == 'dropdown' || type == 'combobox') {
        let options = params.options;
        let opt_groups = params.opt_groups;

        // count elements
        let gCount = 0;
        let oCount = 0;
        if (opt_groups) {
            if (Object.isArray(opt_groups)) {
                gCount = opt_groups.length;
            } else {
                for (var i in opt_groups) {
                    gCount++;
                }
            }
        } else if (options) {
            if (Object.isArray(options)) {
                oCount = options.length;
            } else {
                for (var ii in options) {
                    oCount++;
                }
            }
        }

        try {// create element in IE
            element = context.createElement('<select name="' + name + '">');
        } catch (e) {// create element in FireFox
            element = context.createElement('select');
            element.name = name;
        }

        addClass(element, 'selbox');
        element.setAttribute('id', id);

        element.onfocus = function() {
            highlight(this);
        };
        element.onblur = function() {
            unhighlight(this);
        };

        element.setAttribute('onFocus', 'highlight(this);');
        element.setAttribute('onBlur', 'unhighlight(this);');

        // element.style.width = '100%';
        if (oCount || gCount) {
            if (params.first_empty) {
                if (type == 'combobox') {
                    option = new Option(i18n['labels']['input_or_select'], '');
                    option.innerHTML = i18n['labels']['input_or_select'];
                } else {
                    option = new Option(i18n['labels']['please_select'], '');
                    option.innerHTML = i18n['labels']['please_select'];
                }
                addClass(option, 'undefined');
                element.appendChild(option);

            }
            // create optGroups and options for the element
            if (opt_groups) {
                for (var i in opt_groups) {
                    // get options for the group
                    let options = opt_groups[i];
                    // check how to get the label for the group
                    let label;
                    if (params.optgroup_label_source == 'config') {
                        label = i18n['labels'][i];
                    } else {
                        label = i;
                    }

                    let opt_group;
                    // create optgroup
                    try {
                        opt_group = context.createElement('<optgroup label="' + label + '">');
                    } catch (e) {
                        opt_group = context.createElement('optgroup');
                        opt_group.label = label;
                    }

                    let selected = false;
                    // create options for the optgroup
                    for (let ii in options) {
                        if (!options[ii].option_value || !options[ii].label) {
                            continue;
                        }

                        let selected = options[ii].option_value == value;

                        let label = options[ii].label.replace(/<br\s*\/?>/mg,'');
                        if (typeof(options[ii].active_option) != "undefined" && !parseInt(options[ii].active_option)) {
                            label = '*&nbsp;' + label;
                        }
                        option = new Option(label, options[ii].option_value, false, selected);
                        option.innerHTML = label;
                        if (typeof(options[ii].active_option) != "undefined" && !parseInt(options[ii].active_option)) {
                            if (!params.enable_inactive_options) {
                                option.disabled = true;
                            }
                            addClass(option, "inactive_option");
                            option.title = i18n['labels']['inactive_option'];
                        }
                        if (typeof(options[ii].class_name) != 'undefined') {
                            addClass(option, options[ii].class_name);
                        }
                        opt_group.appendChild(option);
                    }
                    element.appendChild(opt_group);
                }
            } else {
                // create options for the select element
                for (let ii in options) {
                    if (typeof(options[ii].option_value) == 'undefined') {
                        continue;
                    }
                    let selected = options[ii].option_value == value;

                    var label = options[ii].label ? options[ii].label.replace(/<br\s*\/?>/mg,'') : ' ';
                    if (typeof(options[ii].active_option) != "undefined" && !parseInt(options[ii].active_option)) {
                        label = '*&nbsp;' + label;
                    }
                    option = new Option(label, options[ii].option_value, false, selected);
                    option.innerHTML = label;
                    if (typeof(options[ii].active_option) != "undefined" && !parseInt(options[ii].active_option)) {
                        if (!params.enable_inactive_options) {
                            option.disabled = true;
                        }
                        addClass(option, "inactive_option");
                        option.title = i18n['labels']['inactive_option'];
                    }
                    if (typeof(options[ii].class_name) != 'undefined') {
                        addClass(option, options[ii].class_name);
                    }
                    element.appendChild(option);
                }
            }
        } else {
            if (type == 'combobox') {
                option = new Option(i18n['labels']['please_input'], '');
                option.innerHTML = i18n['labels']['please_input'];
                addClass(option, 'undefined');
            } else {
                option = new Option(i18n['labels']['please_select'], '');
                option.innerHTML = i18n['labels']['no_select_records'];
                addClass(option, 'missing_records');
                addClass(element, 'missing_records');
            }
            element.appendChild(option);
        }
        let sequences = '';
        if (!params.sequences || !params.sequences.match(/toggleUndefined\(this\);/)) {
            sequences += 'toggleUndefined(this);';
        }
        if (params.sequences) {
            sequences += params.sequences;
        }
        try {
            eval_context.eval('element.onchange = function() {' + sequences + '}');
        } catch (e) {

        }
        element.setAttribute('onChange', sequences);
        toggleUndefined(element);
        if (disabled) {
            element.disabled = true;
        }
        if (params.readonly) {
            element.readOnly = true;
            addClass(element, 'readonly');
        }
        if (params.text_align) {
            element.style.textAlign = params.text_align;
        }
        // IMPORTANT!!! If container is not present
        // new toCombo(element.id); has to be initialized after putting
        // the element in its container
        if (container) {
            container.appendChild(element);
            if (type == 'combobox') {
                new toCombo(element.id);
            }
        }
    }

    // create text field
    if (type == 'text' || type == 'number' || type == 'date_period') {
         try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
        }

        // set element's attributes
        addClass(element, 'txtbox');
        element.setAttribute('type', 'text');
        element.setAttribute('id', id);
        element.onfocus = function() {
            highlight(element);
        };
        element.onblur = function() {
            unhighlight(element);
        };
        element.setAttribute('onFocus', 'highlight(this);');
        element.setAttribute('onBlur', 'unhighlight(this);');

        if (type == 'date_period') {
            addClass(element, 'number');
            element.onkeypress = function() {
                return changeKey(this, event, insertOnlyDigits);
            };
            element.setAttribute('onKeyPress','return changeKey(this, event, insertOnlyDigits);');
        }
        if (type == 'text' && params.js_filter && params.js_filter.match(/^insertOnly/)) {
            type = 'number';
        }
        if (type == 'number') {
            addClass(element, 'number');
            var keyChecker = params.js_filter || 'insertOnlyReals';
            element.onkeypress = function() {
                return changeKey(this, event, keyChecker);
            };
            element.setAttribute('onKeyPress', 'return changeKey(this, event, ' + keyChecker + ');');
        }
        if (params.text_align) {
            element.style.textAlign = params.text_align;
        }

        if (value) {
            element.value = value;
        }
        if (disabled) {
            element.disabled = true;
        }
        if (params.readonly) {
            element.readOnly = true;
            addClass(element, 'readonly');
        }
        // append the element
        if (container) {
            container.appendChild(element);
        }
        if (type == 'date_period') {
            element.style.width = '50%';
            element.style.marginRight = '5px';
            params.name = 'date_period';
            params.custom_id = 'date_period';
            params.type = 'dropdown';
            params.width = '50%';
            createField(params, container);

            return element;
        }
    }

    // create checkbox field
    if (type == 'checkbox') {
        let checked = params.checked;
         try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
        }

        // set element's attributes
        element.setAttribute('type', 'checkbox');
        element.setAttribute('id', id);
        element.onfocus = function() {
            highlight(element);
        };
        element.onblur = function() {
            unhighlight(element);
        };
        element.setAttribute('onFocus', 'highlight(this);');
        element.setAttribute('onBlur', 'unhighlight(this);');

        if (value) {
            element.value = value;
        }
        if (checked) {
            element.checked = true;
            element.defaultChecked = true;
        }
        if (disabled) {
            element.disabled = true;
        }
        if (params.readonly) {
            element.readOnly = true;
            addClass(element, 'readonly');
        }
        // append the element
        if (container) {
            container.appendChild(element);
        }
    }

    // create file field
    if (type == 'file') {
         try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
        }

        // set element's attributes
        addClass(element, 'txtbox');
        element.setAttribute('type', 'file');
        element.setAttribute('id', id);
        element.onfocus = function() {
            highlight(element);
        };
        element.onblur = function() {
            unhighlight(element);
        };
        element.setAttribute('onFocus', 'highlight(this);');
        element.setAttribute('onBlur', 'unhighlight(this);');

        if (disabled) {
            element.disabled = true;
        }
        if (params.readonly) {
            element.readOnly = true;
            addClass(element, 'readonly');
        }
        // append the element
        if (container) {
            container.appendChild(element);
        }
    }

    // create hidden field
    if (type == 'hidden') {
        try {// create elements in IE
            element = context.createElement('<input name="' + name + '">');
        } catch (e) {// create elements in FireFox
            element = context.createElement('input');
            element.name = name;
        }

        // set element's attributes
        element.setAttribute('type', 'hidden');
        element.setAttribute('id', id);

        if (disabled) {
            element.disabled = true;
        }

        // sets value
        if (value) {
            element.value = value;
        }

        // append the element
        if (container) {
            container.appendChild(element);
        }
    }

    // create autocompleter field
    if (type == 'autocompleter') {
        element = createAutocompleteField(params, container);
    }

    if (type == 'textarea') {
        try {// create elements in IE
            element = context.createElement('<textarea name="' + name + '"></textarea>');
        } catch (e) {// create elements in FireFox
            element = context.createElement('textarea');
            element.name = name;
        }

        // set element's attributes
        addClass(element, 'areabox');
        element.setAttribute('id', id);
        element.onfocus = function() {
            highlight(element);
        };
        element.onblur = function() {
            unhighlight(element);
        };
        element.setAttribute('onFocus', 'highlight(this);');
        element.setAttribute('onBlur', 'unhighlight(this);');

        // treat as numbers so that string '0' is not considered true
        if (+disabled) {
            element.disabled = true;
        }
        if (+params.readonly) {
            element.readOnly = true;
            addClass(element, 'readonly');
        }
        if (params.height) {
            element.style.height = params.height;
        }

        // sets value
        if (value) {
            element.value = value;
        }

        // append the element
        if (container) {
            container.appendChild(element);
        }
    }

    // unsupported type
    if (!element) {
        return element;
    }

    element.setAttribute('itype', params.type);
    let custom_class = params.class_name || params.custom_class || '';
    if (custom_class) {
        if (element_f) {
            addClass(element_f, custom_class);
        } else if (['autocompleter', 'date', 'datetime'].indexOf(type) == -1) {
            addClass(element, custom_class);
        }
    }
    if (params.width) {
        if (element_f) {
            element_f.style.width = params.width;
       /* } else if (element_autocomplete) {
            element_autocomplete.style.width = params.width;*/
        } else {
            element.style.width = params.width;
        }
    } else {
        if (type != 'checkbox') {
            if (element_f) {
                element_f.style.width = '100%';
            }
            if (type != 'autocompleter') {
                element.style.width = '100%';
            }
        }
    }
    if (params.title) {
        if (element_f) {
            element_f.title = params.title;
        } else if (type != 'autocompleter') {
            element.title = params.title;
        }
    }

    return element;
}

/**
 * A function used to add sort conditions in filters' form
 *
 * @param table - the ID of the table where conditions must be added
 */
function addSortCondition(table) {
    table = $(table);
    for (var i = 0; i < table.rows.length; i++) {
        var add = false;
        var column = table.rows[i].cells.length-1;
        if (table.rows[i].cells.length >= 3) {
            return false;
        }
        var field = table.rows[i].cells[column].getElementsByTagName('SELECT')[0];
        if (field && field.name.match(/sort/)) {
            var html = table.rows[i].cells[column].innerHTML;
            var id = 'sort_' + (column+2);
            var name = 'sort[' + (column+1) + ']';
            add = true;
        }
        if (field && field.name.match(/order/)) {
            var html = table.rows[i].cells[column].innerHTML;
            var id = 'order_' + (column+2);
            var name = 'order[' + (column+1) + ']';
            add = true;
        }
        if (add) {
            var field_name = field.name.replace(/\]/, '\\]');
            field_name = field_name.replace(/\[/, '\\[');
            var regex_name = new RegExp(field_name);
            var regex_id = new RegExp(field.id);
            html = html.replace(regex_id, id);
            html = html.replace(regex_name, name);
            cell = table.rows[i].insertCell(column+1);
            cell.style.borderLeft = '1px solid #AAAAAA';
            cell.style.width = '100px';
            cell.innerHTML = html;
        }
    }

    var plusButton  = $(table.id + '_plusButton');
    var minusButton = $(table.id + '_minusButton');

    if (table.rows[0].cells.length < 3) {
        removeClass(plusButton, 'disabled');
    } else {
        addClass(plusButton, 'disabled');
    }
    removeClass(minusButton, 'disabled');
}

/**
 * A function used to remove sort conditions in filters' form
 *
 * @param table - the ID of the table where conditions must be deleted
 */
function removeSortCondition(table) {
    table = $(table);
    for (var i = 0; i < table.rows.length; i++) {
        var column = table.rows[i].cells.length - 1;
        if (column == 0) {
            return false;
        }
        var field = table.rows[i].cells[column].getElementsByTagName('SELECT')[0];
        if (field && (field.name.match(/sort/) || field.name.match(/order/))) {
            table.rows[i].deleteCell(column);
        }
    }

    var plusButton  = $(table.id + '_plusButton');
    var minusButton = $(table.id + '_minusButton');

    removeClass(plusButton, 'disabled');
    if (table.rows[0].cells.length <= 1) {
        addClass(minusButton, 'disabled');
    } else {
        removeClass(minusButton, 'disabled');
    }
}

/**
 * A function used to show dashlets description and settings
 *
 * @param id - the ID of the dashlet
 * @param container - the ID of the container to display the info
 */
function dashletsInfo(id, container) {

    var container = $(container);
    var row = container.id.replace(/[^\d].*(\d+).*/, '$1');

    if (id.match(/_deleted/)) {
        addClass($('dashlets_container_'+row), 't_deleted');
        id=id.replace(/_deleted/, '');
        $('dashlets_'+row).disabled = true;
        container.disabled = true;
    }
    if (id.match(/_inactive/)) {
        addClass($('dashlets_container_'+row), 't_inactive');
        id=id.replace(/_inactive/, '');
        $('dashlets_'+row).disabled = true;
        container.disabled = true;
    }

    var info = dashlets_info[id];

    if (!info) {
        container.innerHTML = '';
        return;
    }

    var table = document.createElement('table');
    table.style.width = '100%';
    addClass(table, 't_borderless');

    var row = table.insertRow(0);

    var cell1 = row.insertCell(0);
    if (info.settings) {
        var cell2 = row.insertCell(1);
        cell1.style.width = '50%';
        cell2.style.width = '50%';
        cell2.innerHTML = info.settings;
    }
    cell1.innerHTML = info.module_name + (info.description ? '<br /><br />' + info.description : '');

    container.innerHTML = '';
    container.appendChild(table);
}

/**
 * A function used to load dashlets with AJAX
 *
 * @param container - the container where dashlet will display
 * @param module - dashlet's module
 * @param controller - dashlet's controller
 * @param id - id of the dashlet
 */
function dashletsLoad(container, module, controller, id) {

    // if module or controller vars are empty the dashlet is not loading
    if (!module || !controller) {
        return false;
    }

    if (!env.valid_login) {
        redirectToLogin();
        return false;
    }

    container = document.querySelector(container);

    const url = new URL(env.base_host + env.base_url);
    if (module == 'reports') {
        url.searchParams.set(env.module_param, 'reports');
        url.searchParams.set('reports', 'dashlet');
        url.searchParams.set('dashlet', id);
    } else if (module == 'plugin') {
        url.searchParams.set(env.module_param, 'dashlets');
        url.searchParams.set('dashlets', 'dashlet');
        url.searchParams.set('dashlet', id);
        url.searchParams.set('plugin', controller);
    } else if (module != controller) {
        url.searchParams.set(env.module_param, module);
        url.searchParams.set('controller', controller);
        url.searchParams.set(controller, 'dashlet');
        url.searchParams.set('dashlet', id);
    } else {
        url.searchParams.set(env.module_param, module);
        url.searchParams.set(controller, 'dashlet');
        url.searchParams.set('dashlet', id);
    }

    fetch(url)
        .then(response => response.text())
        .then(data => {
            if (!env.valid_login) {
                redirectToLogin();
                return false;
            }

            container.classList.remove('dashlet_loader');

            container.innerHTML = data;
            Nz.loadJSFromHTMLString(data);
        })
        .catch(e => {
            container.innerHTML = '<dic class="error">' + e.message + '</div>';
            console.error(e);
        })
        .finally(() => {
            container.classList.remove('dashlet_loader');
            container.classList.add('nz--ready');
        });
}

function initDashboard() {
    const dashboardEle = document.querySelector('.nz-dashboard');
    if (!dashboardEle) {
        return;
    }

    const drag = new NzDragNDropGrid({
        onDrop: () => {
            saveDashletPositions();
        }
    });
    drag.attach(dashboardEle);
}

function saveDashletPositions() {
    const dashboard = document.querySelector('.nz-dashboard');
    const dashletElements = dashboard.querySelectorAll('.nz-dashlet-wrapper');

    const formData = new FormData()
    formData.append('layout', 'dashlets');

    dashletElements.forEach((el, index) => {
        const positionEl = el.closest('.nz-dashboard-gridPosition');
        let position = positionEl.dataset.position;
        const matches = position.match(/^(?<row>\d+)fullwidth$/);
        if (matches) {
            position = `${matches.groups.row}l`;
        }
        formData.append(`dashlets[${index}]`, el.dataset.dashletId);
        formData.append(`positions[${index}]`, position);
    });

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'users');
    url.searchParams.set('users', 'ajax_mynzoom');
    url.searchParams.set('ajax_save', '1');

    fetch(url, {
        method: 'POST',
        body: formData
    })
        .then(response => response.text())
        .then(data => {
            if (data !== '1') {
                console.error(data);
            }
        });
}

/**
 * A way to instantiate an object that handles all functionality for a dashlet plugin
 * and provide handling in case of legacy browers or multiple dashlets for plugin
 * @param {string} func - name of function/object global variable
 * @param {number} id - dashlet id
 * @param {string} controller - plugin name
 * @param {Object|null} params - all other params necessary
 * @return void
 */
function initDashlet(func, id, controller, params) {
    // variable variable (Major Major)
    var type = typeof this[func];
    // dynamic reload of dashlet content - do nothing
    if (type == 'object' && this[func].dashlet_id == id) {
        return;
    }
    var err = false;
    var cookie_name = controller + '_loaded';

    if (Cookie.get(cookie_name)) {
        // if cookie is set, dashlet is loaded in another page
        err = true;
    } else {
        // set session-only cookie
        document.cookie = cookie_name + '=1';
        // cookie should be removed before page is unloaded
        window.onbeforeunload = function() {
            Cookie.erase(cookie_name);
        };

        if (type == 'function') {
            // instantiate an object from function
            this[func] = new this[func](id, controller, params);
        } else if (type == 'object' && this[func].dashlet_id != id) {
            // if type is not 'function' but 'object', there is a dashlet
            // for the same plugin already loaded
            err = true;
        }
    }

    // clear dashlet contents and display an error message instead
    if (err) {
        var content_dashet = $('content_dashlet_' + id);
        if (content_dashet != null) {
            content_dashet.innerHTML = i18n['messages']['error_dashlets_single_dashlet'].replace(
                /\[(.*)\]/,
                '<span class="pointer strong spanlink" onclick="Cookie.erase(\'' + cookie_name + '\'); location.reload();">' + '$1' + '</span>');
            addClass(content_dashet, 'error');
        }
    }

    return;
}

/**
 * Function to show/hide additional columns for full width dashlets
 *
 * @param element - the switch element
 * @param max_cols - maximum columns count
 */
function processDashletColumns(element, max_cols) {

    rows = $('columns_settings').rows.length -2;
    if (element.checked) {
        for(var i = rows; i > max_cols; i--) {
            $('visible_' + i).disabled = false;
            $('columns_' + i).disabled = false;
            $('visible_' + i).parentNode.parentNode.style.display = '';
        }
    } else {
        for(var i = rows; i > max_cols; i--) {
            $('visible_' + i).disabled = true;
            $('columns_' + i).disabled = true;
            $('visible_' + i).parentNode.parentNode.style.display = 'none';
        }
    }
}

/**
 * A function used to load side panels with AJAX
 *
 * @param container - the container where side panel will be displayed
 * @param module - module
 * @param controller - controller
 * @param id - id of model
 * @param panel - name of side panel
 * @archive - show if model is archived
 * @return {Boolean}
 */
function sidePanelsLoad(container, module, controller, id, panel, archive) {

    // if module or controller vars are empty, side panel is not loading
    if (module && controller) {
        container = $(container);
        var opt = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                if (!t.responseText) {
                    if (!env.valid_login) {
                        redirectToLogin();
                    }
                }

                removeClass(container, 'side_panel_loader');
                container.innerHTML = t.responseText;
                var scripts = container.getElementsByTagName('script');
                for (var s in scripts) {
                    ajaxLoadJS(scripts[s]);
                }
                // update total number of records in title of side panel
                var hidden_total = $$('#' + container.id + ' input[type=hidden].total');
                var caption_div = $(container.id.replace(/content/, 'title'));
                if (hidden_total.length && caption_div) {
                    hidden_total = parseInt(hidden_total[0].value);
                    if (isNaN(hidden_total)) {
                        hidden_total = 0;
                    }
                    var caption_span = $$('#' + caption_div.id + ' span.title_side_panel');
                    if (caption_span.length) {
                        caption_span[0].innerHTML += ' (' + hidden_total + ')';
                    }
                }

                // if panel content is not empty, un-hide the external panel div
                if (container.innerHTML) {
                    removeClass(container.parentNode, 'hidden');
                }

                var side_panels = $$('.side_panel_content');
                var loaded = true;
                for (var i = 0; i < side_panels.length; i++) {
                    if (side_panels[i].hasClassName('side_panel_loader')) {
                        loaded = false;
                    }
                }
                if (loaded) {
                    const sidePanelLoading = document.querySelector(`.side_panel_loading`);
                    if (sidePanelLoading) {
                        sidePanelLoading.classList.add('hidden');
                    }
                    document.querySelector(`#side_panel_${panel}`).classList.add('nz--ready');
                    document.querySelector(`.nz-page-aside`).classList.add('nz--ready');
                }

                if (panel === 'comments' || panel === 'emails') {
                    collapseCommunications();
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=';
        if (module != controller) {
            url += module + '&controller=' + controller + '&' + controller + '=ajax_sidepanel&model_id=' + id;
        } else {
            url += module + '&' + controller + '=ajax_sidepanel&model_id=' + id;
        }
        url += '&panel=' + panel;
        if ($('model_lang')) {
            url += '&model_lang=' + $('model_lang').value;
        }
        if (archive) {
            url += '&archive=1';
        }
        if ($('customer')) {
            url += '&customer=' + $('customer').value;
        }

        new Ajax.Request(url, opt);
    }

    return false;
}

/**
 * A function to show/hide side panel settings panel with available options.
 *
 * @param show - 1 = show, 0 = hide
 * @return {Boolean}
 */
function toggleAvailableSidePanelOptions(show) {
    var div_id = $('side_panel_options');
    var div_all_options = $('side_panel_all_options');
    var show_div = $('side_panel_settings_show_div');

    if (div_all_options.innerHTML == '') {
        if (show) {
            Effect.Center('loading');
            Effect.Appear('loading');

            var model_type = $('side_panel_model_type') && !isNaN(parseInt($('side_panel_model_type').value)) ?
                             parseInt($('side_panel_model_type').value) : 0;

            var parameters = '&real_module=' + env.module_name + '&real_controller=' + env.controller_name +
                              '&real_action=' + env.action_name + '&real_model_type=' + model_type;
            var opt = {
                method: 'post',
                parameters: parameters,
                onSuccess: function(t) {
                    // alert(t.responseText);
                    var result = t.responseText;
                    if (!checkAjaxResponse(result)) {
                        Effect.Fade('loading');
                        return;
                    }
                    if (!result.match(/<!-- Side Panels Personal Manager -->/)) {
                        Effect.Fade('loading');
                        return;
                    }
                    div_id.style.visibility = 'visible';
                    div_id.style.display = 'block';
                    div_id.marginTop = 68;
                    div_id.style.top = (div_id.offsetTop - div_id.marginTop) + 'px';
                    div_id.style.left = div_id.offsetLeft + 'px';
                    div_all_options.innerHTML = result;
                    var scripts = div_id.getElementsByTagName('SCRIPT');
                    for (var j = 0; j < scripts.length; j++) {
                        ajaxLoadJS(scripts[j]);
                    }
                    show_div.style.display = 'none';
                    Effect.Fade('loading');
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };

            var url = env.base_url + '?' + env.module_param + '=users&users=ajax_show_available_side_panel_options';
            new Ajax.Request(url, opt);
        } else {
            div_id.style.visibility = 'hidden';
            div_id.style.display = 'none';
            div_id.style.top = '';
            div_id.style.left = '';
            show_div.style.display = '';
        }
    } else {
        div_id.style.visibility = show ? 'visible' : 'hidden';
        div_id.style.display = show ? 'block' : 'none';
        if (!show) {
            div_id.style.top = '';
            div_id.style.left = '';
        } else {
            div_id.style.top = (div_id.offsetTop - div_id.marginTop) + 'px';
            div_id.style.left = div_id.offsetLeft + 'px';
        }
        show_div.style.display = show ? 'none' : '';
    }

    return false;
}

/**
 * Function to dynamically load java scripts with ajax
 *
 * @param script - JS script OBJECT
 */
function ajaxLoadJS(script) {

    if (script.src) {
        var opt = {
            asynchronous:false,
            method: 'get',
            evalJS: false, // Normaly the prototype will eval javascripts automaticaly, but we need to do it ourselves
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                if (!t.responseText) {
                    if (!env.valid_login) {
                        redirectToLogin();
                    }
                    return;
                }
                //the window.eval so that javascripts could be loaded dynamically with AJAX
                window.eval(t.responseText);
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };
        new Ajax.Request(script.src, opt);
    }
    if (script.text) {
        //the window.eval so that javascripts could be loaded dynamically with AJAX
        try {
            window.eval(script.text);
        } catch (e) {
            alert (e);
        }
    }
}

function ajaxForm(action, module, form, messages_container, func) {
    // prepare ajax options
    Effect.Center('loading');
    Effect.Appear('loading');

    const messageHold = 20000;
    let ft = typeof(form), url, lb;

    let opt = {
        _showMsg: function(messages_container, msgs){
            messages_container.innerHTML = msgs;
            if (messages_container.className.match(/collapsible/)) {
                //class that tells us that we have to hide the container after time
                Effect.Appear(messages_container);
                setTimeout(function() {Effect.Fade(messages_container);}, messageHold);
                new Effect.ScrollTo(messages_container);
            } else {
                messages_container.style.display = '';
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    if (ft === 'object') {
        //we have to save the data
        url = form.action;
        opt['method'] = 'post';
        opt['contentType'] = false;
        opt['postBody'] = new FormData(form);

        opt['onSuccess'] = function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            result = JSON.parse(t.responseText)
            lb = document.getElementById('lightbox').lb;

            if (typeof result.errors !== 'undefined') {
                //errors occurred
                //errors during save procedure - show them in a special container into the lightbox
                messages_container = $$('#' + lb.params.uniqid + ' #lightbox_messages_container')[0];
                opt._showMsg(messages_container, result.errors);
            } else if (typeof result.messages !== 'undefined') {
                //task has been saved successfully - show some messages for this
                messages_container = $(lb.params.messageContainer);
                opt._showMsg(messages_container, result.messages);

                if (lb.params.successFunc) {
                    //we have callback registered - invoke it
                    try {
                        lb.params.successFunc(result.id);
                    } catch(e) {
                        alert(e);
                    }
                }
                lb.deactivate();
            }
            Effect.Fade('loading');
        };

    } else if(ft === 'string') {
        //we will just display form
        opt['method'] = 'get';

        opt['onSuccess'] = function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            result = JSON.parse(t.responseText)

            if (typeof result.errors !== 'undefined') {
                //errors occurred
                messages_container = $(messages_container);
                opt._showMsg(messages_container, result.errors);
            } else if (typeof result.data !== 'undefined') {
                //show save form in a lightbox
                lb = new lightbox({
                    content: result.data,
                    title: result.title,
                    width: 780,
                    icon: 'spacer.gif'
                });
                lb.params.messageContainer = messages_container;
                if (func) {
                    //save callback in the lightbox object
                    lb.params.successFunc = typeof func == 'function' ? func : function () { func(); };
                }
                lb.activate();
                lb.lightbox.lb = lb;

                const innerContent = lb.dialogContainer.querySelector('.lb_content');
                if (innerContent) {
                    const innerSize = innerContent.scrollWidth;
                    const availableWidth = window.innerWidth - 22;
                    lb.setWidth(innerSize < availableWidth ? innerSize : availableWidth);
                }

                // enable some common toggling functionality
                initSystemSettingsBox();
                initHelpBox();
            }
            Effect.Fade('loading');
        };

        let config = '';
        if (form.match('|')) {
            form = form.split('|');
            config = form[1];
            form = form[0];
        }
        url = env.base_url + '?' + env.module_param + '='+module+'&'+module+'=' + action +
            '&' + (action == 'ajax_edit' ? action : 'type') + '=' + parseInt(form) + (config ? '&configurator=' + config : '');
    } else {
        // It's an error of somekind. Can't continue!
        return;
    }

    new Ajax.Request(url, opt);
}


/**
 * Function to show the table with the locked records
 */
function showLockedRecords() {

    var pos = getMousePosition();
    var container = $('lr_table');

    container.style.top = pos[1] - 10 + 'px';
    container.style.left = pos[0] - 10 + 'px';
    mopen(container.id);

}

/**
 * Function to unlock locked records with Ajax
 *
 * @param params - object with parameters(module, controller, action, unlock -
 *        id of the record)
 */
function unlockRecord(params) {

    // try to check if we are in the same window which has been locked the
    // record
    var url = env.base_url.replace(/\./gi, '\\.') + '\\?' + env.module_param + '=' + params.module;
    if (params.module != params.controller) {
        url += '&' + env.controller_param + '=' + params.controller;
    }
    url += '&' + params.controller + '=' + params.action;
    url += '&' + params.action + '=' + params.model_id;
    var regex = new RegExp(url);
    if (window.location.href.match(regex)) {
        regex = new RegExp('(=|&)' + params.action, 'gi');
        url = window.location.href.replace(regex, '$1view');
        url += '&unlock=' + params.unlock;
        redirect(url);
    } else {
        regex = new RegExp('(=|&)' + params.action, 'gi');
        url = url.replace(regex, '$1view');
        url = url.replace(/\\(\.|\?)/gi, '$1');
        url += '&ajax_unlock=' + params.unlock;

        var opt = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                if (!params.skip_message && eval(t.responseText)) {
                   alert(i18n['messages']['message_item_unlocked']);
                }
                manageLockedRecords();
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        new Ajax.Request(url, opt);
    }
}

/**
 * Function to switch between text area and WYSIWYG editor
 *
 * @param txtarea - the ID of the text area
 * @param editor_holder - the ID of the editor container
 */
function switchTextHTML(txtarea, editor_holder) {

    var editor_holder = $(editor_holder);
    var txtarea = $(txtarea);

    if (txtarea.style.display == 'none') {
        editor_holder.style.display = 'none';
        txtarea.style.display = '';
    } else {
        txtarea.style.display = 'none';
        editor_holder.style.display = '';
    }

    return true;
}

/**
 * Function to expand the content of announcements in list/search view
 *
 * @param id - id of the announcement
 */
function expandAnnouncement(id) {

    var container = $('announcements_content_' + id);
    if (!container) {
        return false;
    }
    if (container.style.overflow == 'hidden') {
        container.style.height = '';
        container.style.overflow = 'visible';
        var more = $('announcements_more_' + id);
        var html = more.innerHTML.replace(/src\s*=\s*\"?[^\"\s]*\"?/, 'src="' + env.themeUrl + 'images/collapse.png"');
        var regex = new RegExp(i18n['labels']['expand']);
        html = html.replace(regex, i18n['labels']['collapse']);
        more.innerHTML = html;
        url = window.location.href.replace(/\#+announcement_\d+/, '') + '#announcement_' + id;
        redirect(url);
    } else {
        container.style.height = '100px';
        container.style.overflow = 'hidden';
        var more = $('announcements_more_' + id);
        var html = more.innerHTML.replace(/src\s*=\s*\"?[^\"\s]*\"?/, 'src="' + env.themeUrl + 'images/expand.png"');
        var regex = new RegExp(i18n['labels']['collapse']);
        html = html.replace(regex, i18n['labels']['expand']);
        more.innerHTML = html;
        url = window.location.href.replace(/\#+announcement_\d+/, '') + '#announcement_' + id;
        redirect(url);
    }
    return false;
}

/**
 * Function to hide the expand/collapse buttons for announcements where they are
 * not necessary (the whole announcement fits in collapsed view).
 */
function hideUnnecessaryAnnouncementToggles() {
    var content_divs = $$('.announcements_content');
    for (var i = 0; i < content_divs.length; i++) {
        if (content_divs[i].scrollHeight <= 100) {
            $(content_divs[i].id.replace(/content/, 'more')).style.display = 'none';
        }
    }
}

/**
 * Updates some fields in announcement form according to selected type
 *
 * @param {Object} element - dropdown for announcement types
 * @param {Number} type_id - id of commercial announcements type
 * @return {Boolean}
 */
function updateAnnouncementOnTypeChange(element, type_id) {
    // Toggles display of content short row which is used only for commercial announcements
    if ($('content_short_row')) {
        if (element.value == type_id) {
            removeClass($('content_short_row'), 'hidden');
        } else {
            addClass($('content_short_row'), 'hidden');
        }
    }
    // Update validity date according to seleted type
    element.options[element.selectedIndex].className.split(' ').each(function(el) {
        if (el && /^\d{4}-\d{2}-\d{2}$/.test(el)) {
            var val;
            if (val = $('validity_term')) {
                val.value = el;
            }
            if (val = $('validity_term_formatted')) {
                val.value = el.split('-').reverse().join('.');
                addClass(val, 'refreshed');
            }
        }
    });
    return true;
}

/**
 * displays/toggles announcements
 *
 * @param container - container of the announcement body
 * @param switch_el - element we use like a switch
 */
function toggleAnnouncement(container, switch_el) {
      var container = $(container);
      if (container.style.display == 'none') {
          container.style.display = '';
          switch_el.className = 'switch_collapse';
      } else {
          container.style.display = 'none';
          switch_el.className = 'switch_expand';
      }

      return false;
}

/**
 * Function to mark announcements as read
 *
 * @param id - id of the announcement
 */
function readAnnouncement(id) {
    Effect.Center('loading');
    Effect.Appear('loading');
    var opt = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var result = parseInt(t.responseText);
                if (result) {
                    nd();
                    var img = $('announcements_read_' + id);
                    var img_new = new Image(16, 16);
                    img_new.src = img.src.replace(/announcements\.png/, 'announcements_read.png');
                    img_new.onmouseout = function() {nd();};
                    img_new.onmouseover = function() {
                        return overlib(i18n['labels']['announcements_is_read'], CAPTION, i18n['labels']['help']);
                    };
                    img.parentNode.replaceChild(img_new, img);
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

    var url = env.base_url + '?' + env.module_param + '=announcements&announcements=ajax_read&ajax_read=' + id;
    new Ajax.Request(url, opt);
    Effect.Fade('loading');
}

/**
 * Toggles visibility of elements in view mode of announcement
 *
 * @param {Object} chk - element whose visiblity should be toggled
 */
function toggleAnnouncementsAcknowledgements(chk) {
    if (chk.tagName == 'INPUT' && chk.type == 'checkbox') {
        var fn = chk.checked ? removeClass : addClass;
        $$('#container_acknowledgements_details li.' + chk.id.replace('include_', '')).each(function(el) {
            fn(el, 'hidden');
        });
    } else if (chk.tagName == 'TABLE') {
        if (chk.style.display == 'none') {
            chk.style.display = '';
            if (!chk.style.top) {
                var pos = getMousePosition();
                chk.style.top = (pos[1] - chk.offsetHeight) + 'px';
                chk.style.left = (pos[0] + 40) + 'px';
            }
        } else {
            chk.style.display = 'none';
        }
    }
}

/* Announcement bar functions */

/**
 * Loads announcement bar if there are any announcements to be displayed to current user.
 *
 * @param container - id of table cell where announcements will be loaded.
 */
function loadAnnouncementBar(container) {
    var opt = {
            method: 'get',
            onSuccess: function(t) {
                var result = t.responseText;
                if (!checkAjaxResponse(result)) {
                    return;
                }
                if (result && $(container)) {
                    container = $(container);
                    container.innerHTML = result;
                    removeClass(container, 'hidden');

                    var scripts = container.getElementsByTagName('SCRIPT');
                    for (var j = 0; j < scripts.length; j++) {
                        ajaxLoadJS(scripts[j]);
                    }
                }
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

    var url = env.base_url + '?' + env.module_param + '=announcements&announcements=ajax_announcement_bar&bar_action=load';
    new Ajax.Request(url, opt);
}

/**
 * Hides announcement bar until end of current user session.
 *
 * @param container - id of table cell with announcements.
 */
function hideAnnouncementBar(container) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var result = parseInt(t.responseText);
                if (result) {
                    addClass($(container), 'hidden');
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

    var url = env.base_url + '?' + env.module_param + '=announcements&announcements=ajax_announcement_bar&bar_action=hide';
    new Ajax.Request(url, opt);
}

/**
 * Removes announcement from announcement bar and marks it as read by user.
 *
 * @param id - id of announcement
 */
function closeAnnouncement(id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var result = parseInt(t.responseText);
                if (result) {
                    var announcement_span = $('announcement_' + id);
                    if (announcement_span) {
                        addClass(announcement_span, 'hidden');
                    }
                    var num_visible_spans = 0;
                    var announcement_spans = $$('#announcement span');
                    for (var i = 0; i < announcement_spans.length; i++) {
                        if (!announcement_spans[i].hasClassName('hidden')) {
                            num_visible_spans++;
                        }
                    }
                    if (!num_visible_spans) {
                        addClass($('announcement').parentNode.parentNode, 'hidden');
                    }
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

    var url = env.base_url + '?' + env.module_param + '=announcements&announcements=ajax_announcement_bar&ajax_announcement_bar=' + id + '&bar_action=close';
    new Ajax.Request(url, opt);
}

/**
 * Displays in lightbox full contents of an announcement from announcement bar.
 *
 * @param id - id of announcement
 */
function displayAnnouncement(id) {

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var result = t.responseText;
            Effect.Fade('loading');
            if (!checkAjaxResponse(result)) {
                return;
            }
            if (result) {
                //show layer in a lightbox
                lb = new lightbox({content: result, title: i18n['labels']['announcement'], width: 600});
                lb.activate();
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=announcements&announcements=ajax_announcement_bar&ajax_announcement_bar=' + id + '&bar_action=display';
    new Ajax.Request(url, opt);
}

/**
 * Scrolls text in announcement bar.
 */
function scrollAnnouncementBar() {
    if (!announcement){announcement = $('announcement');}
    if (announcement) {
        if (announcement.offsetLeft+announcement.offsetWidth < -1000) {
        //if (pscrnt < ( - 1500)) {
            pscrnt = psinit;
        } else {
            pscrnt += -1;
        }
        announcement.style.left = pscrnt + "px";
    }
}

/**
 * Gets scope of autocompleter field
 *
 * @param {Object} ac_field - autocompleter field
 * @return {Object|String} - scope object (DOM element) or an empty string
 */
function getAutocompleteScope(ac_field) {
    var scope = ac_field.up('.dashlet_content');
    if (!scope) {
        scope = ac_field.up('.lb_content');
    }
    return scope || '';
}

/**
 * Gets autocomplete object of autocompleter field
 *
 * @param {Object} ac_field - autocompleter field
 */
function getAutocompleteParams(ac_field) {
    return window['params_' + ac_field.getAttribute('uniqid')];
}

/**
 * Prepares autocompleter filter expression
 *
 * @param {string} filter - filter expression as passed in right part of the
 *                        autocomplete_filter setting (the one after the double arrow =>)
 *                        autocomplete_filter := <type> => IN ($variable)
 *                                                         ^  this one  ^
 * @param {string} uniqid - unique identifier of autocompleter
 * @param {string} scope - autocompleter scope
 * @return {string} - filter value after processing
 */
function prepareAutocompleteFilter(filter, uniqid, scope) {
    // the default filter value is defined in the source field
    var filter_value = filter;
    if (filter.match(/office_\$office/)) {
        var office = $$(scope + ' #office'), company = $$(scope + ' #company');
        //special behaviour for contracts add action and available quantity field
        if (office.length && office[0].value) {
            if (company.length && company[0].value) {
                // simulate structure of company_data value
                filter_value = company[0].value + '_' + office[0].value + '_bank_0';
            } else {
                filter_value = 'office_' + office[0].value;
            }
        } else {
            filter_value = '';
        }
    } else if (filter.match(/\$/)) {
        if (filter.match(/^(\s*(!?=|<=?|>=?)\s*)?\$/)) {
            // operator for equality/inequality or no operator
            filter_value = filter.replace(/^([^\$]*)\$.*/, '$1');
            //get filter value
            filter_value += getFilterValue(filter, uniqid, scope);
        } else if (filter.match(/^\s*((not\s+)?in\s*\()(.*)(\))\s*/i)) {
            // IN / NOT IN
            filter_value = filter.replace(/^\s*((not\s+)?in\s*\().*/i, '$1');
            var field = filter.replace(/^\s*((not\s+)?in\s*\()(.*)(\))\s*/i, '$3');
            field = field.split(',');
            for (var j = 0; j < field.length; j++) {
                field[j] = field[j].replace(/^\s*(.*)\s*$/, '$1');
                if (field[j].match(/^\$/)) {
                    field[j] = getFilterValue(field[j], uniqid, scope);
                }
            }
            filter_value += field.join(',') + ')';
        }
    }

    return filter_value;
}

/**
 * Gets the filter field value from the form
 *
 * @param {string} filter - field name containing the value
 * @param {string} uniqid - unique identifier of autocompleter
 * @param {string} scope - autocompleter scope
 * @return {string} - value from form
 */
function getFilterValue(filter, uniqid, scope) {
    var fvalue = filter;

    var element = $$(scope + ' [uniqid="' + uniqid + '"]')[0];
    var id = element.id;

    var index = '';
    if (id.match(/.*_\d+$/)) {
        // get the row index (if any)
        index = id.replace(/.*_(\d+)$/, '$1');
    }

    // we have to get filter value from field in the form
    var field_id = filter.replace(/^.*\$/, '');
    var found = false;
    if (index > 0) {
        field = $$(scope + ' #' + field_id + '_' + index);
        if (field.length && field[0].type != 'radio' && field[0].type != 'checkbox' &&
            field[0].up('table') && element.up('table') && field[0].up('table').id == element.up('table').id) {
            // the variable is within the same GT/GT2 table
            // get it as STRING
            fvalue = field[0].value;
            found = true;
        } else {
            field = $$(scope + ' #' + field_id + '_1' + '_' + index);
            if (field.length && field[0].type == 'radio' &&
                field[0].up('table') && element.up('table') && field[0].up('table').id == element.up('table').id) {
                // the variable is radio in the same GT/GT2 table
                // get it as STRING
                fvalue = ($$(scope + ' input:checked[name="' + field_id + '[' + (index - 1) + ']"]').length) ?
                    $$(scope + ' input:checked[name="' + field_id + '[' + (index - 1) + ']"]')[0].value : '';
                found = true;
            } else {
                field = $$(scope + ' #' + field_id + '_1_1');
                if (field.length && field[0].type == 'radio') {
                    // the variable is radio in the ANOTHER GT/GT2 table
                    // get it as ARRAY
                    fvalue = ($$(scope + ' input:checked[name^="' + field_id + '["]').length) ? $$(scope + ' input:checked[name^="' + field_id + '["]').map(Form.Element.getValue) : '';
                    found = true;
                }
            }
        }
    }
    if (!found) {
        field = $$(scope + ' #' + field_id + '_1');
        if (field.length) {
            if (field[0].type == 'radio') {
                //this is a radio button (outside of GT/GT2)
                // get it as STRING
                fvalue = ($$(scope + ' input:checked[name=' + field_id + ']').length) ? $$(scope + ' input:checked[name=' + field_id + ']')[0].value : '';
                found = true;
            } else if (field[0].type == 'checkbox') {
                // a checkbox
                // get it as ARRAY
                fvalue = ($$(scope + ' input:checked[name^="' + field_id + '["]').length) ? $$(scope + ' input:checked[name^="' + field_id + '["]').map(Form.Element.getValue) : '';
                found = true;
            } else {
                // a grouping/GT2 table
                // get it as ARRAY
                fvalue = ($$(scope + ' [name^="' + field_id + '["]').length) ? $$(scope + ' [name^="' + field_id + '["]').map(Form.Element.getValue) : '';
                found = true;
            }
        }
    }
    if (!found) {
        field = $$(scope + ' #' + field_id);
        if (field.length) {
            fvalue = field[0].value;
            found = true;
        }
    }

    //remove the empty values from array
    if (Object.prototype.toString.call(fvalue) === "[object Array]") {
        fvalue = fvalue.filter(function(n) { return n != ""; });
        if (fvalue.length == 0) {
            fvalue = '';
        }
    }

    return fvalue;
}

/**
 * Prepare data for type of fields to be filled in from autocompleter
 *
 * @param {Object} autocomplete - autocompleter parameters
 * @param {Object} context - window context (current window or popup/iframe window)
 * @param {Number} index - row number (when autocompleter is in a multi-row table) or empty
 * @return {Hash} - return result as a (Prototype) hashtable so it can be easily converted to object or query string
 */
function prepareAutocompleteFillTypes(autocomplete, context, index) {
    var hash = new $H(),
        context = context ? context : window,
        $$_fn = context.$$,
        tmp_selector,
        tmp_element,
        fill_type;

    if (autocomplete.fill_options && autocomplete.fill_options.length > 0 && !autocomplete.search_by_id) {
        // set definitions how the fields will be filled
        for (var i = 0; i < autocomplete.fill_options.length; i++) {
            tmp_selector = autocomplete.fill_options[i].split('=>');
            tmp_selector = tmp_selector[0].replace(/\$|\s|\ /g, '');
            // get field in function of the scope
            if (index > 0) {
                // add row index (if any) to the field name
                tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector + '_' + index);
                if (!tmp_element.length) {
                    tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector);
                }
            } else {
                tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector);
            }
            tmp_element = tmp_element.length ? tmp_element[0] : false;

            fill_type = '';
            if (tmp_element && tmp_element.tagName.match(/select/i)) {
                fill_type = 'dropdown';
            } else if (tmp_element && tmp_element.tagName.match(/input/i)) {
                fill_type = tmp_element.type.toLowerCase();
            } else if (tmp_element && tmp_element.tagName.toLowerCase() == 'textarea') {
                fill_type = 'text';
            } else {
                //radio
                //get the hidden element that each radio has
                if (index > 0) {
                    // add row index (if any) to the field name
                    tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector + '_0_' + index);
                    if (!tmp_element.length) {
                        tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector + '_0');
                    }
                } else {
                    tmp_element = $$_fn(autocomplete.scope + ' #' + tmp_selector + '_0');
                }
                tmp_element = tmp_element.length ? tmp_element[0] : false;
                if (tmp_element && tmp_element.type.toLowerCase() == 'radio') {
                    fill_type = 'radio';
                }
            }
            if (fill_type) {
                hash.set('fill_types[' + i + ']', fill_type);
            }
        }
    }

    return hash;
}

/**
 * Function to fill the autocompleter fields from the suggestions box
 *
 * @param {Object} li - the list item which is selected
 * @param {Object} autocomplete - autocompleter's parameters
 */
function selectAutocompleteItems(li, autocomplete) {
    if (!li.id) {
        return false;
    }

    // item data
    var data = new Object;
    eval('data = ' + $(li.id + '_data').value);

    // row (group table row number)
    var row = data.row ? '_' + data.row : '';

    // check if we have duplicated items
    if (autocomplete.unique && data['$' + autocomplete.unique]) {
        var unique = $$(autocomplete.scope + ' [name^="' + autocomplete.unique + '["]');
        var deleted = $$(autocomplete.scope + ' [name^="deleted["]');
        for (var j = 0; j < unique.length; j++) {
            if (data['$' + autocomplete.unique] == unique[j].value && unique[j].id != autocomplete.unique + row  &&
                !(deleted[j] && deleted[j].value == 1 || unique[j].hasClassName('input_inactive'))) {
                for (var i in data) {
                    // iterate through properties and CLEAN matching fields
                    // ignore all properties not starting with $
                    if (!/^\$/.test(i)) {
                        continue;
                    }
                    var name = i.replace(/\$/, ' #');
                    var field = $$(autocomplete.scope + name + row);
                    if (field.length) {
                        field[0].value = '';
                        removeClass(field[0], 'working');

                        // special behaviour for the date and datetime boxes
                        // the there is a special formatted date/datetime
                        if (field[0].className.match(/date(time)?box/)) {
                            var other_field = $$(autocomplete.scope + name + '_formatted' + row);
                            if (other_field.length) {
                                other_field[0].value = '';
                            }
                        } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                            // if hidden autocompleter field, check for link and update it
                            updateAutocompleteLink(field[0], {});
                        } else {
                            var other_field = $$(autocomplete.scope + name + '_readonly' + row);
                            if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                                // the readonly dropdowns have additional field
                                // with suffix _readonly
                                other_field[0].value = '';
                                // change style of readonly dropdowns
                                toggleUndefined(field[0]);
                            } else if (field[0].hasClassName('selbox')) {
                                // change style of dropdowns
                                toggleUndefined(field[0]);
                            }
                        }
                    }
                }
                return;
            }
        }
    }
    var gt2 = 0;
    if (row && autocomplete.id_var && $$(autocomplete.scope + ' .grouping_table2 #' + autocomplete.id_var + row).length > 0) {
        gt2 = 1;
    }

    if (autocomplete.on_select) {
        // function to prepare custom data for completing the autocompleter and the related fields
        data = executeACPluginMethod(autocomplete, data);
    }
    //prepare check if oldvalue will be filled
    var old_name = autocomplete.field.replace(new RegExp(row + '$'), '') + '_oldvalue';
    var old_reg = new RegExp(old_name);
    var old_is_set = false;
    for (var i in data) {
        // iterate through properties and fill them into fields
        // ignore all properties not starting with $
        if (!/^\$/.test(i)) {
            continue;
        }
        var name = i.replace(/\$/, ' #');
        var field = $$(autocomplete.scope + name + row);
        if (field.length) {
            if (gt2 && name.match(/price/)) {
                val = parseFloat(data[i]);
                if (!isNaN(val) && val != 'undefined') {
                    var pow = Math.pow(10, env.precision.gt2_rows);
                    val = Math.round(val * pow) / pow;
                    val = val.toFixed(env.precision.gt2_rows);
                    field[0].value = val;
                } else {
                    field[0].value = data[i];
                }
            } else {
                field[0].value = data[i];
                if (gt2 && field[0].id.match(/^(discount|surplus)_(percentage|value)/)) {
                    //set field for discount/surplus
                    var other_field = $$(autocomplete.scope + ' #discount_surplus_field' + row);
                    if (other_field.length) {
                        other_field[0].value = field[0].id.replace(/^(.*)_\d+$/, '$1');
                    }
                }
            }
            removeClass(field[0], 'working');

            // special behaviour for the date and datetime boxes
            // there is a special formatted date/datetime
            var other_field = $$(autocomplete.scope + name + '_formatted' + row);
            if (field[0].hasClassName('datebox')) {
                if (other_field.length) {
                    // parse the date
                    var date = parseISODate(data[i]);
                    if (date) {
                        other_field[0].value = date.format('d.m.Y');
                    } else if (data[i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4}$/)) {
                        //the date has been formatted already dd.mm.yyyy
                        var date = parseFormattedDate(data[i]);
                        other_field[0].value = date.format('d.m.Y');
                        field[0].value       = date.format('Y-m-d');
                    }
                }
            } else if (field[0].hasClassName('datetimebox')) {
                if (other_field.length) {
                    // parse the date
                    var date = parseISODate(data[i]);
                    if (date) {
                        other_field[0].value = date.format('d.m.Y, H:i');
                    } else if (data[i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}$/) ||
                               data[i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$/)) {
                        //the datetime has been formatted already dd.mm.yyyy H:i or dd.mm.yyyy H:i:s
                        var date = parseFormattedDate(data[i]);
                        other_field[0].value = date.format('d.m.Y, H:i');
                        field[0].value                = date.format('Y-m-d H:i:00');
                    }
                }
            } else if (field[0].hasClassName('timebox')) {
                if (other_field.length) {
                    other_field[0].value = data[i];
                }
            } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                // if hidden autocompleter field, check for link and update it
                updateAutocompleteLink(field[0], data);
            } else {
                other_field = $$(autocomplete.scope + name + '_readonly' + row);
                if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                    // the readonly dropdowns have additional field with suffix
                    // _readonly
                    other_field[0].value = data[i];
                    // change style of readonly dropdowns
                    toggleUndefined(other_field[0]);
                } else if (field[0].hasClassName('selbox')) {
                    // change style of dropdowns
                    toggleUndefined(field[0]);
                } else if (field[0].type == 'hidden') {
                    // readonly radio buttons (get the hidden element that each radio has)
                    var elements = $$(autocomplete.scope + name + '_0_readonly' + row);
                    if (elements.length) {
                        // get all radio buttons with the same name
                        elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                        if (elements.length) {
                            // special behaviour for radio buttons
                            for (var j = 0; j < elements.length; j++) {
                                // check the selected element
                                if (elements[j].value == data[i]) {
                                    elements[j].checked = 'checked';
                                } else {
                                    elements[j].checked = '';
                                }
                            }
                        }
                    }
                }
            }
            // don't fill trademark autocomplete and oldvalue if current autocompleter
            // is for customers and there is no main trademark for selected customer
            if (field[0].id.match(/^(default_)?trademark[0-9]*_/) && data.type == 'customers' && !data.trademark) {
                field[0].value = '';
            }

            if (field[0].id.match(old_reg)) {
                //oldvalue has been filled - great
                old_is_set = true;
            }
        } else {
            //get the hidden element that each radio has
            field = $$(autocomplete.scope + name + '_0' + row);
            if (field.length) {
                // get all radio buttons with the same name
                field = $$(autocomplete.scope + ' input[name="' + field[0].name + '"]');
                if (field.length) {
                    // special behaviour for radio buttons
                    for (var j = 0; j < field.length; j++) {
                        if (field[j].value == data[i]) {
                            field[j].checked = 'checked';
                        } else {
                            field[j].checked = false;
                        }
                    }
                }
            }
        }
    }

    if (!old_is_set) {
        //oldvalue has not been filled - fill it with the value of the AC field
        field = $$(autocomplete.scope + ' #' + old_name + row);
        if (field.length) {
            field[0].value = $$(autocomplete.scope + ' #' + autocomplete.field)[0].value;
        }
    }
    // clear the suggestions box
    li.parentNode.parentNode.innerHTML ='';

    // if autocompleter is for a basic variable and for a single field
    if (autocomplete.var_type && autocomplete.var_type == 'basic' && !row) {
        // if autocompleter is for customers or trademarks, update customer-related data
        if (((data.type && data.type == 'customers') || (data.subtype && data.subtype == 'trademarks')) && !autocomplete.stop_customer_details) {
            var customer_id = 0;
            if (data.subtype && data.subtype == 'trademarks') {
                customer_id = data.customer_id;
            } else {
                customer_id = data.id;
            }
            if ($$(autocomplete.scope + ' #side_panel_customers_info').length) {
                showCustomersInfo(customer_id);
            }
            if ($$(autocomplete.scope + ' #side_panel_last_records').length) {
                showLastRecords(data);
            }
            if ($$(autocomplete.scope + ' #branch').length) {
                changeCustomerBranchAndContact(customer_id, data.is_company);
            }
            if ($$(autocomplete.scope + ' #contract').length) {
                changeCustomersContracts(customer_id);
            }
        } else if (data.type && data.type == 'projects' && $$(autocomplete.scope + ' #phase').length) {
            // if autocompleter is for projects, fill the options for phases
            var project_id = data.id;
            changePhases(project_id);
        }
    }

    // autocomplete in 2nd type grouping table
    // request calculations
    if (gt2) {
        // If the autocompleter is for nomenclatures
        if (data.type == 'nomenclatures') {
            permitNegativeValues(autocomplete, data);
        }
        if (typeof data.$price !== "undefined") {
            gt2calc('price' + row);
        } else {
            gt2calc('quantity' + row);
        }
    }

    if (autocomplete.execute_after) {
        executeAfterAutocomplete(autocomplete, data);
    }

    return true;
}

/**
 * Defines whether autocompleter execute_after expression should be evaled with (autocomplete, data) arguments or not
 * IMPORTANT: the function does not apply or eval the expression. It only defines how the expression should be evaled.
 * The expression ends with ";" or ")" - applyExecuteAfterArgs returns FALSE
 *  myFunction(); -> evaled myFunction();
 *  myFunction(my, args) -> evaled myFunction(my, args);
 *
 * The expression DOES NOT end neither with  ";", nor with or ")" - applyExecuteAfterArgs returns TRUE
 *  myFunction -> evaled myFunction(autocomplete, data);
 *
 * The expression has the following structure - applyExecuteAfterArgs returns TRUE
 *  (function(autocomplete, data) { ... }) -> evaled (function(autocomplete, data) { ... })(autocomplete, data);
 *
 * @param {String} params - execute_after - the autocompleter execute_after expression
 * @return {boolean} -
 */
function applyExecuteAfterArgs(execute_after) {
    if (execute_after.match(/^ *\( *function *\(autocomplete *, * data *\).*\) *$/) || !execute_after.match(/(\)|;) *$/)) {
        return true;
    } else {
        return false;
    }
}

/**
 * Perform execute_after functionality of autocompleter, wrapped in a function,
 * so that autocomplete and data are passed as parameters and do not pollute
 * the global variable namespace of the context window, but are always available as
 * variables with such names, no matter where functionality is called from.
 *
 * @param {Object} autocomplete - autocompleter settings
 * @param {Object} data - data for record selected in autocompleter
 * @return void
 */
function executeAfterAutocomplete(autocomplete, data) {
    if (!autocomplete.execute_after) {
        return;
    }
    try {
        if (!applyExecuteAfterArgs(autocomplete.execute_after)) {
            eval(autocomplete.execute_after);
        } else {
            // add (autocomplete, data) as arguments only if the execute_after function does not end with ; or )
            eval(autocomplete.execute_after + '(autocomplete, data);');
        }
    } catch (e) {
        //alert('Autocompleter after execution error! Contact the support');
    }
}

/**
 * Function to open the popup window where we can search items with the advanced
 * filters
 *
 * @param {Object} autocomplete - autocompleter's parameters (!! or object with group table id and AC type !!)
 */
function filterAutocompleteItems(autocomplete) {
    var index = '', table = '', scope, id, select_multiple = false;
    if (autocomplete.table && autocomplete.type) {
        // the filter buttons is pressed which is near the '+' and '-' buttons
        // of the grouping table
        // get autocompleter's type
        var type = autocomplete.type;
        // we will get autocomplete scope VEEEERY tricky
        // this function caller is the onclick event of the button over GT
        // event target is the button itself... so check its scope
        var button = filterAutocompleteItems.caller.arguments[0].target;
        //check if we are in dashlet or lightbox
        scope = Element.up(button, '.dashlet_content');
        if (!scope) {
            scope = Element.up(button, '.lb_content');
        }
        if (scope) {
            scope = '#' + scope.id;
        } else {
            scope = '';
        }
        table = $$(scope + ' #' + autocomplete.table);
        if (!type || !table.length) {
            return false;
        }
        table = table[0];
        // get all autocomplete elements
        var elements = Element.select(table, '.autocomplete_' + type);
        if (!elements[0]) {
            return false;
        }
        // get autocomplete parameters
        eval('autocomplete = params_' + elements[0].getAttribute('uniqid') + ';');
        // set flag that multiple records can be selected from filtered list of records
        select_multiple = true;
        if (!table.hasClassName('grouping_table2')) {
            index = table.rows[table.rows.length-1].id.replace(/.*_(\d+)$/, '$1');
        } else {
            // we are in 2nd generation grouping table
            index = Element.select(table, '#gt2_delimeter')[0].rowIndex - 1;
            var batch_count = 0;
            for (var i = index; i > 0; i--) {
                if (table.rows[i].hasClassName('batch_data')) {
                    batch_count++;
                }
            }
            index -= batch_count;
        }
        // set the field id
        // we will lie to the system
        id = elements[0].id.replace(/(.*)_\d+$/, '$1') + '_' + index;
    } else {
        // the filter button is pressed which is near the autocomplete field
        var ac_field = $$('[uniqid="' + autocomplete.uniqid + '"]')[0];
        id = ac_field.id;
        if (id.match(/.*_\d+$/)) {
            index = id.replace(/.*_(\d+)$/, '$1');
        }
        select_multiple = autocomplete && autocomplete.select_multiple;
    }

    scope = autocomplete.scope;
    // set the url to get data from
    var url = autocomplete.url.replace(/ajax_select/, 'filter');
    url += '&field=' + id.replace(/(.*)_\d+$/, '$1');
    url += '&scope=' + encodeURIComponent(autocomplete.scope);
    url += '&uniqid=' + encodeURIComponent(autocomplete.uniqid);
    if (index > 0) {
        // set the row index (if any)
        url += '&row=' + index;
    }
    if (table) {
        url += '&table=' + table.id;
    }
    if (autocomplete.search && autocomplete.search.length > 0) {
        // set search fields to search into
        for (var i = 0; i < autocomplete.search.length; i++) {
            url += '&search[]=' + autocomplete.search[i];
        }
    }
    if (autocomplete.sort && autocomplete.sort.length > 0) {
        // set sort definitions
        for (var i = 0; i < autocomplete.sort.length; i++) {
            url += '&sort[]=' + autocomplete.sort[i];
        }
    }
    if (autocomplete.id_var) {
        // name of the variable that contains the id of the nomenclature,
        // document, customer etc.
        url += '&id_var=' + autocomplete.id_var;
    }
    if (autocomplete.filters) {
        // set the additional filters if we have any
        for (var i in autocomplete.filters) {
            if (typeof(autocomplete.filters[i]) == 'string') {
                url += '&filters[' + i + ']=' + prepareAutocompleteFilter(autocomplete.filters[i], autocomplete.uniqid, autocomplete.scope);
            }
        }
    }
    if (autocomplete.optional_filters) {
        // set the optional filters (if the filter value is empty do not use it!)
        for (var i in autocomplete.optional_filters) {
            if (typeof(autocomplete.optional_filters[i]) == 'string') {
                var filter_value = prepareAutocompleteFilter(autocomplete.optional_filters[i], autocomplete.uniqid, autocomplete.scope);
                if (filter_value) {
                    url += '&filters[' + i + ']=' + filter_value;
                }
            }
        }
    }
    if (autocomplete.currency) {
        if (autocomplete.currency.match(/^\$/)) {
            field = autocomplete.currency.replace(/\$/, '');
            if (index > 0) {
                // add row index (if any) to the field name
                field = $$(scope + ' #currency_' + index);
                if (!field.length) {
                    field = $$(scope + ' #currency');
                }
            } else {
                field = $$(scope + ' #currency');
            }
            if (field.length) {
                url += '&currency=' + field[0].value;
            }
        } else {
            url += '&currency=' + autocomplete.currency;
        }
    }
    if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        // set the fill options
        for (var i = 0; i < autocomplete.fill_options.length; i++) {
            url += '&fill_options[]=' + encodeURIComponent(autocomplete.fill_options[i]);
        }
    }
    if (autocomplete.suggestions) {
        // set suggestions format
        url += '&suggestions=' + encodeURIComponent(autocomplete.suggestions);
    }
    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }
    if (select_multiple == true) {
        url += '&autocomplete_select_multiple=1';
    }
    if (autocomplete.execute_after) {
        url += '&autocomplete_execute_after=' + encodeURIComponent(autocomplete.execute_after);
    }
    if (autocomplete.execute_after_params) {
        for (var i in autocomplete.execute_after_params) {
            url += '&autocomplete_execute_after_params['+i+']=' + encodeURIComponent(autocomplete.execute_after_params[i]);
        }
    }
    if (autocomplete.unique) {
        url += '&autocomplete_unique=' + autocomplete.unique;
    }
    if (autocomplete.stop_customer_details) {
        url += '&stop_customer_details=' + autocomplete.stop_customer_details;
    }

    // set autocomplete filters(parameters) to be different from "session"
    // when we have "session" value we will get the filters(parameters) from the
    // session
    url += '&autocomplete_filter='+ autocomplete.type;

    if (autocomplete.plugin) {
        url+= '&autocomplete_plugin=' + autocomplete.plugin;
        if (autocomplete.on_select) {
            url+= '&autocomplete_on_select=' + autocomplete.on_select;
        }
        if (autocomplete.plugin_params) {
            url+= '&autocomplete_plugin_params=' + encodeURIComponent(Object.toJSON(autocomplete.plugin_params));
        }
    }

    if (autocomplete.var_type) {
        url += '&var_type=' + autocomplete.var_type;
    }
    if (autocomplete.var_type && autocomplete.var_type == 'basic' && env.action_name.match(/^(multi)?(add|edit)$/) && !env.module_name.match(/^(finance|nomenclatures)$/)) {
        url += '&open_from='+ env.module_name + (env.module_name != env.controller_name ? '|' + env.controller_name : '');
        // extra param to get customer of model
        if (autocomplete.type != 'customers') {
            var customer = $$(autocomplete.scope + ' #customer' + (index > 0 ? '_' + index : ''));
            if (customer.length) {
                customer = customer[0].value;
            } else {
                customer = '';
            }
            url += '&customer=' + customer;
        }
    }
    if (ac_field && ac_field.value && autocomplete.type.match(/^(customers|projects|nomenclatures|documents|tasks|contracts|users)$/)) {
        url += '&search_value=' + encodeURIComponent(ac_field.value);
    }

    pop(url, 882, 580);
}

/**
 * Function to open lightbox with report in it. The selected results will be
 * returned in the autocompleter and its related fields
 *
 * @param {Object} autocomplete - autocompleter's parameters (!! or group table id !!)
 */
function reportAutocompleteItems(autocomplete) {
    // the filter report button is pressed which is near the autocomplete field

    var ac_field = $$('[uniqid="' + autocomplete.uniqid + '"]');
    if (!ac_field.length) {
        return false;
    }
    ac_field = ac_field[0];

    var index = '';
    if (ac_field.id.match(/.*_\d+$/)) {
        index = ac_field.id.replace(/.*_(\d+)$/, '$1');
    }

    // build link to access the report module
    var url = env.base_url + '?' + env.module_param + '=reports&report_type=' + autocomplete.report + '&skip_session_filters=1&use_ajax=1';

    // set the url to get data from
    url += '&field=' + ac_field.id.replace(/(.*)_\d+$/, '$1');

    if (index > 0) {
        // set the row index (if any)
        url += '&row=' + index;
    }

    if (autocomplete.id_var) {
        // name of the variable that contains the id of the nomenclature,
        // document, customer etc.
        url += '&id_var=' + autocomplete.id_var;

        // get the value of the current field
        var current_id_var = autocomplete.id_var;
        if (index > 0) {
            current_id_var += '_' + index;
        }
        var id_var_value = $$(autocomplete.scope + ' #' + current_id_var)[0].value;
        if (id_var_value > 0) {
            url += '&id_var_value=' + id_var_value;
        }
    }

    if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        // set the fill options
        for (var i = 0; i < autocomplete.fill_options.length; i++) {
            url += '&fill_options[]=' + encodeURIComponent(autocomplete.fill_options[i]);
        }
    }

    // If there are any report settings
    if (typeof(autocomplete.report_settings) !== "undefined") {
        // Go through each report setting
        for (var report_setting_key in autocomplete.report_settings) {
            // If there is such setting key
            if (typeof(report_setting_key) !== "undefined" && report_setting_key != ''
                    && typeof(autocomplete.report_settings[report_setting_key]) !== "undefined" && autocomplete.report_settings[report_setting_key] != '') {
                // Get the setting value
                var report_setting_value = autocomplete.report_settings[report_setting_key];

                // If the value starts with $, that means that the value should be taken from an existing input field
                if (report_setting_value.match(/^\$.+/)) {
                    // Get the name of the field
                    report_setting_value = report_setting_value.replace('$', '');

                    // Try to get the value from: <field name>_<index> (example: article_id_1)
                    var field = $$(autocomplete.scope + ' #' + report_setting_value + '_' + index);
                    if (!field.length) {
                        field = $$(autocomplete.scope + ' #' + report_setting_value);
                    }
                    if (field.length) {
                        report_setting_value = field[0].value;
                    } else {
                        report_setting_value = '$' + report_setting_value;
                    }
                }

                // Add the report setting into the request
                url += '&report_settings[' + report_setting_key + ']=' + encodeURIComponent(report_setting_value);
            }
        }
    }

    // lang var
    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }

    // what to be done after autocompleter is completed
    if (autocomplete.execute_after) {
        url += '&autocomplete_execute_after=' + encodeURIComponent(autocomplete.execute_after);
    }

    // params for the execute after event
    if (autocomplete.execute_after_params) {
        for (var i in autocomplete.execute_after_params) {
            url += '&autocomplete_execute_after_params['+i+']=' + encodeURIComponent(autocomplete.execute_after_params[i]);
        }
    }

    var opt = {
        method: 'get',
        asynchronous: (typeof autocomplete.asynchronous != 'undefined') ? autocomplete.asynchronous : true,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            // get the items' data
            var template = t.responseText;

            // defines label
            var label = i18n['labels']['report'];
            if (autocomplete.report_name) {
                label = autocomplete.report_name;
            }

            //show layer in a lightbox

            // check for a custom width or set a default one
            if (autocomplete.report_lightbox_width) {
                var lb_width = autocomplete.report_lightbox_width + 'px';
            } else {
                var lb_width = '820px';
            }

            // check for a custom height or set a default one
            if (autocomplete.report_lightbox_height) {
                var lb_height = autocomplete.report_lightbox_height + 'px';
            } else {
                var lb_height = '580px';
            }

            // build the lightbox
            lb = new lightbox({content: template, title: label, icon: 'spacer.gif', width: lb_width, height: lb_height});

            lb.activate();
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Function to open the popup window where we can add new model
 *
 * @param {Object} autocomplete - autocompleter's parameters
 */
function addAutocompleteItems(autocomplete) {

    var ac_field = $$('[uniqid="' + autocomplete.uniqid + '"]');
    if (!ac_field.length) {
        return false;
    }
    ac_field = ac_field[0];

    // set the url to get data from
    var url = autocomplete.url.replace(/ajax_select/, 'addquick');
    url += '&field=' + ac_field.id.replace(/(.*)_\d+$/, '$1');

    var index = '';
    if (ac_field.id.match(/.*_\d+$/)) {
        index = ac_field.id.replace(/.*_(\d+)$/, '$1');
    }
    if (index > 0) {
        // set the row index (if any)
        url += '&row=' + index;
    }

    if (autocomplete.search && autocomplete.search.length > 0) {
        // set search fields to search into
        for (var i = 0; i < autocomplete.search.length; i++) {
            url += '&search[]=' + autocomplete.search[i];
        }
    }
    if (autocomplete.id_var) {
        // name of the variable that contains the id of the nomenclature,
        // document, customer etc.
        url += '&id_var=' + autocomplete.id_var;
    }
    if (autocomplete.var_type) {
        // the type of the var
        url += '&var_type=' + autocomplete.var_type;
    }
    if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        // set the fill options
        for (var i = 0; i < autocomplete.fill_options.length; i++) {
            url += '&fill_options[]=' + encodeURIComponent(autocomplete.fill_options[i]);
        }
    }
    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }
    if (autocomplete.execute_after) {
        url += '&autocomplete_execute_after=' + encodeURIComponent(autocomplete.execute_after);
    }
    if (autocomplete.execute_after_params) {
        for (var i in autocomplete.execute_after_params) {
            url += '&autocomplete_execute_after_params['+i+']=' + encodeURIComponent(autocomplete.execute_after_params[i]);
        }
    }
    if (autocomplete.addquick_type  && autocomplete.addquick_type.length > 0) {
        for (var j = 0; j < autocomplete.addquick_type.length; j++) {
            url += '&addquick_type[]=' + autocomplete.addquick_type[j];
        }
    }
    if (autocomplete.unique) {
        url += '&autocomplete_unique=' + autocomplete.unique;
    }
    url += '&scope=' + encodeURIComponent(autocomplete.scope);
    url += '&uniqid=' + encodeURIComponent(autocomplete.uniqid);
    // set definitions how the fields will be filled
    url += '&' + prepareAutocompleteFillTypes(autocomplete, window, index).toQueryString();

    var field;
    if (autocomplete.currency) {
        if (autocomplete.currency.match(/^\$/)) {
            var field_name = autocomplete.currency.replace(/\$/, '');
            if (index > 0) {
                // add row index (if any) to the field name
                field = $$(autocomplete.scope + ' #' + field_name + '_' + index);
                if (!field.length) {
                    field = $$(autocomplete.scope + ' #' + field_name);
                }
            } else {
                field = $$(autocomplete.scope + ' #' + field_name);
            }
            if (field.length) {
                url += '&currency=' + field[0].value;
            }
        } else {
            url += '&currency=' + autocomplete.currency;
        }
    }

    if (autocomplete.filters && autocomplete.filters['<get_all_quantities>']) {
        url += '&get_all_quantities=' + autocomplete.filters['<get_all_quantities>'];
    }
    if (autocomplete.filters && autocomplete.filters['<get_available_quantities>']) {
        url += '&get_available_quantities=' + autocomplete.filters['<get_available_quantities>'];
    }

    // set autocomplete filters(parameters) to be different from "session"
    // when we have "session" value we will get the filters(parameters) from the
    // session
    url += '&autocomplete_filter=' + autocomplete.type;

    pop(url, 852, 580);
}

/**
 * Function to open the popup window where we can edit model selected in autocompleter
 *
 * @param {Object} autocomplete - autocompleter's parameters
 */
function editAutocompleteItems(autocomplete) {

    var ac_field = $$('[uniqid="' + autocomplete.uniqid + '"]');
    if (!ac_field.length || autocomplete.type == 'autocompleters' && !autocomplete.cstm_model && !autocomplete.plugin_params.unique_field) {
        return false;
    }
    ac_field = ac_field[0];

    // set the url to get data from
    var url = '';

    url += '&field=' + ac_field.id.replace(/(.*)_\d+$/, '$1');

    var index = '';
    if (ac_field.id.match(/.*_\d+$/)) {
        index = ac_field.id.replace(/.*_(\d+)$/, '$1');
    }
    if (index > 0) {
        // set the row index (if any)
        url += '&row=' + index;
    }

    /*if (autocomplete.search && autocomplete.search.length > 0) {
        // set search fields to search into
        for (var i = 0; i < autocomplete.search.length; i++) {
            url += '&search[]=' + autocomplete.search[i];
        }
    }*/
    var model_id = '',
        id_field,
        field;

    if (autocomplete.id_var) {
        // name of the variable that contains the id of the nomenclature,
        // document, customer etc.
        url += '&id_var=' + autocomplete.id_var;

        id_field = autocomplete.id_var;
        if (index) {
            id_field += '_' + index;
        }
        field = $$(autocomplete.scope + ' #' + id_field);
        if (field.length && field[0].value) {
            model_id = parseInt(field[0].value) || '';
        }
    }
    if (autocomplete.var_type) {
        // the type of the var
        url += '&var_type=' + autocomplete.var_type;
    }
    if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        // set the fill options
        for (var i = 0; i < autocomplete.fill_options.length; i++) {
            url += '&fill_options[]=' + encodeURIComponent(autocomplete.fill_options[i]);

            var option = autocomplete.fill_options[i];
            if (!model_id && option.match(/\<id\>/)) {
                // we have the field containing the id of the item
                id_field = option.replace(/^\$([^=\s]*).*/, '$1');
                if (index) {
                    id_field += '_' + index;
                }
                field = $$(autocomplete.scope + ' #' + id_field);
                if (field.length && field[0].value) {
                    model_id = parseInt(field[0].value) || '';
                }
            }
        }
    }
    if (!model_id) {
        // model id not found
        return false;
    } else {
        // put the model id first (right after the action)
        url = '&edit=' + model_id + url;
    }
    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }
    if (autocomplete.execute_after) {
        url += '&autocomplete_execute_after=' + encodeURIComponent(autocomplete.execute_after);
    }
    if (autocomplete.execute_after_params) {
        for (var i in autocomplete.execute_after_params) {
            url += '&autocomplete_execute_after_params['+i+']=' + encodeURIComponent(autocomplete.execute_after_params[i]);
        }
    }
    if (autocomplete.unique) {
        url += '&autocomplete_unique=' + autocomplete.unique;
    }
    url += '&scope=' + encodeURIComponent(autocomplete.scope);
    url += '&uniqid=' + encodeURIComponent(autocomplete.uniqid);
    // set definitions how the fields will be filled
    url += '&' + prepareAutocompleteFillTypes(autocomplete, window, index).toQueryString();

    if (autocomplete.plugin_search) {
        url += '&plugin_search=' + autocomplete.plugin_search;
    }
    if (autocomplete.plugin_params) {
        var acpp = new Object();
        for (var i in autocomplete.plugin_params) {
            var pp = autocomplete.plugin_params[i];
            if (i == 'unique_field' && autocomplete.plugin_params.search) {
                //IMPORTANT: the refresh button requires search in the unique field (usually ID)
                //           so replace search parameter with unique id
                i = 'search';
            }
            acpp[i] = prepareAutocompleteFilter(pp, autocomplete.uniqid, autocomplete.scope);
        }
        url += '&plugin_params=' + encodeURIComponent(Object.toJSON(acpp));
    }

    if (autocomplete.currency) {
        if (autocomplete.currency.match(/^\$/)) {
            var field_name = autocomplete.currency.replace(/\$/, '');
            if (index > 0) {
                // add row index (if any) to the field name
                field = $$(autocomplete.scope + ' #' + field_name + '_' + index);
                if (!field.length) {
                    field = $$(autocomplete.scope + ' #' + field_name);
                }
            } else {
                field = $$(autocomplete.scope + ' #' + field_name);
            }
            if (field.length) {
                url += '&currency=' + field[0].value;
            }
        } else {
            url += '&currency=' + autocomplete.currency;
        }
    }

    if (autocomplete.filters && autocomplete.filters['<get_all_quantities>']) {
        url += '&get_all_quantities=' + autocomplete.filters['<get_all_quantities>'];
    }
    if (autocomplete.filters && autocomplete.filters['<get_available_quantities>']) {
        url += '&get_available_quantities=' + autocomplete.filters['<get_available_quantities>'];
    }

    // set autocomplete filters(parameters) to be different from "session"
    // when we have "session" value we will get the filters(parameters) from the
    // session
    url += '&autocomplete_filter=' + autocomplete.type;

    url = autocomplete.url.replace(/ajax_select/, 'edit') + url;

    if (autocomplete.type == 'autocompleters' && autocomplete.cstm_model) {
        url = url.replace(/\bautocompleters\b/g, autocomplete.cstm_model.toLowerCase() + 's');
    }

    pop(url, 822, 580);
}

/**
 * Functionality to mimic add/edit from autocompeter without such field present
 * (intended for future use of quick edit without autocompleter field present)
 *
 * @param {Object} autocomplete - prepared parameters to use for construction of URL
 * @return void
 */
function simulateAutocompleteAction(autocomplete) {
    if (!autocomplete.url) {
        return;
    }

    // the target address should be already prepared, no replacement is performed
    var url = autocomplete.url;

    if ($('model_lang')) {
        url += '&model_lang=' + $('model_lang').value;
    }
    if (autocomplete.execute_after) {
        url += '&autocomplete_execute_after=' + encodeURIComponent(autocomplete.execute_after);
    }
    if (autocomplete.execute_after_params) {
        for (var i in autocomplete.execute_after_params) {
            url += '&autocomplete_execute_after_params['+i+']=' + encodeURIComponent(autocomplete.execute_after_params[i]);
        }
    }
    url += '&scope=' + encodeURIComponent(autocomplete.scope || '');
    url += '&uniqid=' + encodeURIComponent(autocomplete.uniqid || uniqid('', true).replace('.', ''));

    pop(url, autocomplete.width || 822, autocomplete.height || 580);
}

/**
 * Function to fill in the parent window the values from an added/edited model
 *
 * @param {Object} autocomplete_data - the json-encoded array with params and data for the autocompleter
 */
function setAutocompleteValues(autocomplete_data) {
    var autocomplete_data = eval(autocomplete_data),
        // autocompleter parameters
        autocomplete = autocomplete_data.autocomplete_params,
        // data to set into autocompleter field and related fields
        data = autocomplete_data.autocomplete_values;

    // function might be custom-called from another context
    var context = defineContext(autocomplete_data),
        row_suffix = autocomplete.row ? '_' + autocomplete.row : '',
        // id of autocompleter field (with row suffix, if in a table row)
        field_id = autocomplete.field + row_suffix,
        scope = autocomplete.scope,
        gt2 = context.$$(scope + ' #' + field_id);

    if (gt2.length) {
        gt2 = gt2[0].parentNode.parentNode.parentNode.parentNode;
        if (gt2.hasClassName('grouping_table2')) {
            gt2 = true;
            gt2calc_target = 'quantity';
        } else {
            gt2 = false;
        }
    } else {
        gt2 = false;
    }

    var field, other_field, val;

    // check if we have duplicated items
    if (autocomplete.unique && data[autocomplete.unique + row_suffix]) {
        field = context.$$(autocomplete.scope + ' #' + autocomplete.unique + row_suffix);
        var clear_row = field.length && field[0].value == data[autocomplete.unique + row_suffix];

        var unique = context.$$(autocomplete.scope + ' [name^="' + autocomplete.unique + '["]');
        var deleted = context.$$(autocomplete.scope + ' [name^="deleted["]');
        for (var j = 0; j < unique.length; j++) {
            if (data[autocomplete.unique + row_suffix] == unique[j].value && unique[j].id != autocomplete.unique + row_suffix &&
                    !(deleted[j] && deleted[j].value == 1 || unique[j].hasClassName('input_inactive'))) {
                // clear row only if ids in data and in page are the same
                // otherwise do not change data in page
                if (clear_row) {
                    for (var i in data) {
                        // iterate through properties and CLEAN matching fields
                        var name = ' #' + i;
                        if (row_suffix) {
                            var regex = new RegExp('(' + row_suffix + ')$');
                            name = name.replace(regex, '');
                        }
                        field = context.$$(scope + ' #' + i);
                        if (field.length) {
                            field[0].value = '';
                            removeClass(field[0], 'working');

                            // special behaviour for the date and datetime boxes
                            // the there is a special formatted date/datetime
                            if (field[0].className.match(/date(time)?box/)) {
                                var other_field = context.$$(autocomplete.scope + name + '_formatted' + row_suffix);
                                if (other_field.length) {
                                    other_field[0].value = '';
                                }
                            } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                                // if hidden autocompleter field, check for link and update it
                                updateAutocompleteLink(field[0], {});
                            } else {
                                var other_field = context.$$(autocomplete.scope + name + '_readonly' + row_suffix);
                                if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                                    // the readonly dropdowns have additional field
                                    // with suffix _readonly
                                    other_field[0].value = '';
                                    // change style of readonly dropdowns
                                    toggleUndefined(field[0]);
                                } else if (field[0].hasClassName('selbox')) {
                                    // change style of dropdowns
                                    toggleUndefined(field[0]);
                                }
                            }
                        }
                    }
                }

                // since we are coming from edit - we should update the values in the matching row
                var other_autocompleter = context.$$(autocomplete.scope + '#' + autocomplete.field + unique[j].id.replace(/.*(_\d+$)/, '$1'))[0];
                if (other_autocompleter) {
                    context.refreshAutocompleteItems(context.getAutocompleteParams(other_autocompleter));
                }

                data = null;
                break;
            }
        }

        // if not unique - exit function
        if (data == null) {
            if (autocomplete_data.close_window) {
                closePopupWindow();
                context.focus();
            }
            return;
        }
    }

    // complete the autocomplete vars in the parent window
    for (var i in data) {
        field = context.$$(scope + ' #' + i);
        if (field.length) {
            if (gt2 && i == ('price' + row_suffix)) {
                val = parseFloat(data[i]);
                if (!isNaN(val) && val != 'undefined') {
                    var pow = Math.pow(10, env.precision.gt2_rows);
                    val = Math.round(val * pow) / pow;
                    val = val.toFixed(env.precision.gt2_rows);
                    field[0].value = val;
                } else {
                    field[0].value = data[i];
                }
            } else {
                field[0].value = data[i];
                if (gt2 && field[0].id.match(/^(discount|surplus)_(percentage|value)/)) {
                    //set field for discount/surplus
                    var other_field = context.$$(scope + ' #discount_surplus_field' + row_suffix);
                    if (other_field.length) {
                        other_field[0].value = field[0].id.replace(/^(.*)_\d+$/, '$1');
                    }
                }
            }

            if (field[0].type == 'hidden') {
                var i_readonly = i;
                if (row_suffix) {
                    var regex = new RegExp('(' + row_suffix + ')$');
                    i_readonly = i_readonly.replace(regex, '_readonly$1');
                } else {
                    i_readonly += '_readonly';
                }
                other_field = context.$$(scope + ' #' + i_readonly);
                if (other_field.length) {
                    other_field[0].value = data[i];
                    // change style of readonly dropdowns
                    toggleUndefined(other_field[0]);
                } else {
                    i_readonly = i;
                    if (row_suffix) {
                        var regex = new RegExp('(' + row_suffix + ')$');
                        i_readonly = i_readonly.replace(regex, '_0_readonly$1');
                    } else {
                        i_readonly += '_0_readonly';
                    }
                    // readonly radio buttons (get the hidden element that each radio has)
                    var elements = context.$$(scope + ' #' + i_readonly);
                    if (elements.length) {
                        // get all radio buttons with the same name
                        elements = context.$$(scope + ' input[name="' + elements[0].name + '"]');
                        if (elements.length) {
                            // special behaviour for radio buttons
                            for (var j = 0; j < elements.length; j++) {
                                // check the selected element
                                if (elements[j].value == data[i]) {
                                    elements[j].checked = 'checked';
                                } else {
                                    elements[j].checked = '';
                                }
                            }
                        }
                    }
                }
            } else if (field[0].hasClassName('selbox')) {
                // change style of dropdowns
                toggleUndefined(field[0]);
            } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                // if hidden autocompleter field, check for link and update it
                data.context = context;
                updateAutocompleteLink(field[0], data);
                delete data.context;
            }

            if (gt2 && i == ('price' + row_suffix)) {
                gt2calc_target = 'price';
            }
            if (field[0].id == field_id) {
                // make sure autocompleter field does not have 'working' class
                removeClass(field[0], 'working');
            }
        } else {
            // radio
            var row = autocomplete.row;
            var name = i;
            if (row_suffix) {
                var regex = new RegExp('(' + row_suffix + ')$');
                name = name.replace(regex, '');
            }
            var elements = context.$$(scope + ' input[name="' + name + (parseInt(row) > 0 ? ('[' + (parseInt(row) - 1) + ']') : '') + '"]');
            if (!elements.length) {
                // new or existing row in GT2
                var el = context.$$(scope + ' #' + name + '_0' + row_suffix);
                if (el.length) {
                    elements = context.$$(scope + ' input[name="' + el[0].name + '"]');
                }
            }
            if (elements.length) {
                // special behaviour for radio buttons
                for (var j = 0; j < elements.length; j++) {
                    if (elements[j].value == data[i]) {
                        elements[j].checked = 'checked';
                    } else {
                        elements[j].checked = '';
                    }
                }
            }
        }

        if (i.match(/^(default_)?trademark[0-9]*_/) && autocomplete_data['module'] == 'customers' && data.trademark == '') {
            if (field[0]) {
                field[0].value = '';
            }
        }
    }

    // if autocompleter is for a basic variable and for a single field
    if (autocomplete && autocomplete.var_type && autocomplete.var_type == 'basic' && !row_suffix) {
        // if autocompleter is for customers, update customer-related data
        if (autocomplete_data['module'] == 'customers' && autocomplete_data['controller'] == 'customers' && autocomplete_data['model_info'] && autocomplete_data['model_info']['id'] && !autocomplete_data['model_info']['stop_customer_details']) {
            if (context.$$(scope + ' #side_panel_customers_info').length) {
                context.showCustomersInfo(autocomplete_data['model_info']['id']);
            }
            if (context.$$(scope + ' #side_panel_last_records').length) {
                context.showLastRecords({id: autocomplete_data['model_info']['id'], '$customer_autocomplete': data['customer_autocomplete']});
            }
            if (context.$$(scope + ' #branch').length) {
                context.changeCustomerBranchAndContact(autocomplete_data['model_info']['id'], autocomplete_data['model_info']['is_company']);
            }
            if (context.$$(scope + ' #contract').length) {
                context.changeCustomersContracts(autocomplete_data['model_info']['id']);
            }
        } else if (autocomplete_data['module'] == 'projects' && autocomplete_data['controller'] == 'projects' && autocomplete_data['model_id'] && context.$$(scope + ' #phase').length) {
            // if autocompleter is for projects and for "project" main variable, reload phases
            var project_id = autocomplete_data['model_id'];
            changePhases(project_id, true);
        }
    }

    // autocomplete in 2nd type grouping table
    // request calculations
    if (gt2) {
        if (autocomplete_data.module == 'nomenclatures' && autocomplete_data.controller == 'nomenclatures') {
            data.row = autocomplete.row;
            context.permitNegativeValues(autocomplete, data);
        }
        context.gt2calc(gt2calc_target + row_suffix);
    }

    if (autocomplete.execute_after) {
        data.row = autocomplete.row;
        // some functionalities expect the model id in "id" key in data
        data.id = autocomplete_data['model_id'];
        // such data is available only for customers
        if (autocomplete_data['model_info']) {
            for (var i in autocomplete_data['model_info']) {
                data[i] = autocomplete_data['model_info'][i];
            }
        }

        context.executeAfterAutocomplete(autocomplete, data);
    }

    // refresh all other autocompleter fields that hold data for the same model
    // as current autocompleter field
    context.refreshSameIdValueAutocompleters(context.$$(scope + ' #' + field_id)[0]);

    if (autocomplete_data.close_window) {
        closePopupWindow();
        context.focus();
    }
}

/**
 * Gets the id_var field of an autocompleter
 *
 * @param {Object} autocomplete - autocomplete parameters object
 * @return {string} - id of field (without row suffix) or an empty string on failure
 */
function getAutocompleteIdVar(autocomplete) {
    var id_field = '';
    if (autocomplete.id_var) {
        id_field = autocomplete.id_var;
    } else if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        for (var ii = 0; ii < autocomplete.fill_options.length; ii++) {
            var option = autocomplete.fill_options[ii];
            if (option.match(/\<id\>/)) {
                // we have the field containing the id of the item
                id_field = option.replace(/^\$([^=\s]*).*/, '$1');
                break;
            }
        }
    } else if (autocomplete.var_type == 'basic' && autocomplete.uniqid) {
        id_field = $$('input[uniqid="' + autocomplete.uniqid + '"]')[0] || '';
        if (id_field) {
            id_field = id_field.id.replace(/_autocomplete(_\d+)?$/, '');
        }
    }
    return id_field;
}

/**
 * Gets the value of the id_var field of an autocompleter
 *
 * @param {Object} autocomplete - autocomplete parameters object
 * @return {number|string} - id of model selected in autocompleter or an empty
 * string when empty
 */
function getAutocompleteIdValue(autocomplete) {
    var id_value = '',
        id_field = getAutocompleteIdVar(autocomplete),
        row_suffix = autocomplete.field && autocomplete.field.match(/.*_\d+$/)
            ? autocomplete.field.replace(/.*(_\d+)$/, '$1') : '';
    if (id_field) {
        id_value = $$(autocomplete.scope + ' #' + id_field + row_suffix)[0];
        id_value = id_value && id_value.value ? id_value.value : '';
    }
    return id_value;
}

/**
 * Refreshes all other autocompleter fields that hold data for the same model
 * as current autocompleter field
 *
 * @param {Object} ac_field_updated - autocompleter field that was just updated
 * @return void
 */
function refreshSameIdValueAutocompleters(ac_field_updated) {
    if (!ac_field_updated) {
        return;
    }
    var autocomplete = getAutocompleteParams(ac_field_updated);
    if (!autocomplete || autocomplete.type == 'autocompleters') {
        return;
    }
    var id_value_updated = getAutocompleteIdValue(autocomplete);
    if (!id_value_updated) {
        return;
    }

    $$(autocomplete.scope + ' input.autocomplete_' + autocomplete.type + '[uniqid]').each(function(ac_field) {
        // refresh other autocompleter
        if (ac_field != ac_field_updated) {
            var autocomplete_params = getAutocompleteParams(ac_field);
            if (autocomplete_params && getAutocompleteIdValue(autocomplete_params) == id_value_updated) {
                // set a temporary flag that refresh is after edit of model
                autocomplete_params.action = 'edit';
                refreshAutocompleteItems(autocomplete_params);
                delete autocomplete_params.action;
            }
        }
    });
}

/**
 * Function to refresh some(or all) of the fields for autocompleter
 *
 * @param {Object} autocomplete - autocompleter's parameters (!! or group table id !!)
 */
function refreshAutocompleteItems(autocomplete) {

    var index = '', id, scope, refresh_multiple = false;
    if (autocomplete.table) {
        // the refresh button is pressed which is near the '+' and '-' buttons
        // of the grouping table
        // get the autocompleter type
        var type = autocomplete.type;
        // we will get autocomplete scope VEEEERY tricky
        // this function caller is the onclick event of the button over GT
        // event target is the button itself... so check its scope
        var button = refreshAutocompleteItems.caller.arguments[0].target;
        //check if we are in dashlet or lightbox
        scope = Element.up(button, '.dashlet_content');
        if (!scope) {
            scope = Element.up(button, '.lb_content');
        }
        if (scope) {
            scope = '#' + scope.id;
        } else {
            scope = '';
        }
        var table = $$(scope + ' #' + autocomplete.table);
        if (!type || !table.length) {
            return false;
        }
        table = table[0];
        // get all autocomplete parameters
        var elements = Element.select(table, '.autocomplete_' + type);
        if (!elements[0]) {
            return false;
        }
        // get autocomplete parameters
        eval('autocomplete = params_' + $(elements[0].id).getAttribute('uniqid') + ';');
        // set flag that multiple records will be refreshed
        refresh_multiple = true;
        // set the name and id of the field
        // we will lie to the system
        // narrow the search to require match by field name as well
        index = Element.select(table, '.txtbox.autocompletebox.autocomplete_' + type + '[id^="' + elements[0].id.replace(/_\d+$/, '') + '"]').length;
        id = elements[0].id.replace(/(.*)_\d+$/, '$1') + '_' + index;
    } else {
        // the filter button is pressed which is near the autocomplete field
        var ac_field = $$('[uniqid="' + autocomplete.uniqid + '"]')[0];
        id = ac_field.id;
        if (id.match(/.*_\d+$/)) {
            index = id.replace(/.*_(\d+)$/, '$1');
        }
    }

    // prepare post
    var post = new Object();
    // set model_lang
    post['model_lang'] = ($('model_lang') ? $('model_lang').value : '');
    // set field to search into
    post['search[]'] = ['<id>'];
    // set suggestions format(for compatibility)
    post.suggestions = '<id>';
    // set the field
    post.field = id.replace(/(.*)_\d+$/, '$1');
    if (index) {
        // set the row index (if any)
        post.row = index;
    }

    post['fill_options[]'] = [];
    post[post.field] = '';

    if (autocomplete.plugin) {
        post.plugin = autocomplete.plugin;
    }
    if (autocomplete.dont_kill_concurent_queries) {
        post.dont_kill_concurent_queries = autocomplete.dont_kill_concurent_queries;
    }
    if (autocomplete.plugin_search) {
        post.plugin_search = autocomplete.plugin_search;
    }
    if (autocomplete.plugin_params) {
        var acpp = new Object();
        for (var i in autocomplete.plugin_params) {
            var pp = autocomplete.plugin_params[i];
            if (i == 'unique_field' && autocomplete.plugin_params.search) {
                //IMPORTANT: the refresh button requires search in the unique field (usually ID)
                //           so replace search parameter with unique id
                i = 'search';
            }
            acpp[i] = prepareAutocompleteFilter(pp, autocomplete.uniqid, autocomplete.scope);
        }
        post.plugin_params = Object.toJSON(acpp);
    }

    // prepare definitions for the fields to be refreshed
    var field;
    if (autocomplete.refresh_fields && autocomplete.refresh_fields.length > 0) {
        // if we have refresh definitions set the fill options
        // in function of them
        for (var i = 0; i < autocomplete.refresh_fields.length; i++) {
            var field_name = autocomplete.refresh_fields[i];
            for (var ii = 0; ii < autocomplete.fill_options.length; ii++) {
                var option = autocomplete.fill_options[ii];
                if (option.match(/\<id\>/) && !post[post.field]) {
                    // we have the field containing the id of the item
                    var id_field = option.replace(/^\$([^=\s]*).*/, '$1');
                    if (refresh_multiple) {
                        // we have to refresh all the rows(for grouping table)
                        for (var j = 1; j <= index; j++) {
                            field = $$(autocomplete.scope + ' #' + id_field + '_' + j);
                            if (field.length && field[0].value) {
                                post[post.field] += field[0].value;
                            } else {
                                post[post.field] += '0';
                            }
                            if (j < index) {
                                post[post.field] += ', ';
                            }
                        }
                    } else {
                        if (index) {
                            id_field += '_' + index;
                        }
                        field = $$(autocomplete.scope + ' #' + id_field);
                        if (field.length) {
                            post[post.field] = field[0].value;
                        }
                    }
                    continue;
                }
                var regex = new RegExp('\\' + field_name);
                // if the field has definition for the property to be filled
                // from, we set this definition and clear the field
                if (option.match(regex)) {
                    post['fill_options[]'].push(option);
                    field = field_name.replace(/^\$/, '');
                    if (autocomplete.id_var && field != autocomplete.id_var) {
                        if (index) {
                            field += '_' + index;
                        }
                        field = $$(autocomplete.scope + ' #' + field);
                        if (field.length) {
                            field[0].value = '';
                        }
                        //TODO also clear formatted fields etc.
                    }
                }
            }
        }
        // add field types of refresh fields
        var autocomplete_refresh = Object.clone(autocomplete);
        autocomplete_refresh.fill_options = post['fill_options[]'];
        // set definitions how the fields will be filled
        post = prepareAutocompleteFillTypes(autocomplete_refresh, window, index).merge(post).toObject();
    } else {
        // if no refresh definitions provided
        // we get the fill options
        if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
            for (var ii = 0; ii < autocomplete.fill_options.length; ii++) {
                var option = autocomplete.fill_options[ii];
                if (option.match(/\<id\>/)) {
                    // we have the field containing the id of the item
                    var id_field = option.replace(/^\$([^=\s]*).*/, '$1');
                    if (refresh_multiple) {
                        // we have to refresh all the rows(for grouping table)
                        for (var j = 1; j <= index; j++) {
                            field = $$(autocomplete.scope + ' #' + id_field + '_' + j);
                            if (field.length && field[0].value) {
                                post[post.field] += field[0].value;
                            } else {
                                post[post.field] += '0';
                            }
                            if (j < index) {
                                post[post.field] += ', ';
                            }
                        }
                    } else {
                        if (index) {
                            id_field += '_' + index;
                        }
                        field = $$(autocomplete.scope + ' #' + id_field);
                        if (field.length && field[0].value) {
                            post[post.field] = field[0].value;
                        }
                    }
                }

                // clear the field
                var field_name = option.replace(/^\$([^=\s]*).*/, '$1');
                if (autocomplete.id_var && field_name != autocomplete.id_var) {
                    if (index) {
                        field_name += '_' + index;
                    }
                    field = $$(autocomplete.scope + ' #' + field_name);
                    if (field.length && field[0].value) {
                        field[0].value = '';
                    }
                    //TODO also clear formatted fields etc.
                }
            }
            post['fill_options[]'] = autocomplete.fill_options;
            // set definitions how the fields will be filled
            post = prepareAutocompleteFillTypes(autocomplete, window, index).merge(post).toObject();
        }
    }
    if (autocomplete.currency) {
        if (autocomplete.currency.match(/^\$/)) {
            var field_name = autocomplete.currency.replace(/\$/, '');
            if (index > 0) {
                // add row index (if any) to the field name
                field = $$(autocomplete.scope + ' #' + field_name + '_' + index);
                if (!field.length) {
                    field = $$(autocomplete.scope + ' #' + field_name);
                }
            } else {
                field = $$(autocomplete.scope + ' #' + field_name);
            }
            if (field.length) {
                post.currency = field[0].value;
            }
        } else {
            post.currency = autocomplete.currency;
        }
    }
    if (!post[post.field]) {
        if (autocomplete.id_var) {
            id_field = autocomplete.id_var;
            if (index) {
                id_field += '_' + index;
            }
            field = $$(autocomplete.scope + ' #' + id_field);
            if (field.length && field[0].value) {
                post[post.field] = field[0].value;
            }
            if (!post[post.field]) {
                return;
            }
        } else {
            // if the field for the item's id is not present we terminate the
            // function
            return;
        }
    }

    if (autocomplete.type == 'customers' && autocomplete.filters && autocomplete.filters['<contactpersons>']) {
        post['filters[<contactpersons>]'] = 1;
    }

    if (autocomplete.filters && autocomplete.filters['<get_all_quantities>']) {
        post['filters[<get_all_quantities>]'] = autocomplete.filters['<get_all_quantities>'];
    }
    if (autocomplete.filters && autocomplete.filters['<get_available_quantities>']) {
        post['filters[<get_available_quantities>]'] = autocomplete.filters['<get_available_quantities>'];
    }

    var opt = {
        parameters: Object.toQueryString(post),
        asynchronous: (typeof autocomplete.asynchronous != 'undefined') ? autocomplete.asynchronous : true,
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            // get the items' data
            eval('var data = ' + t.responseText + ';');
            if (refresh_multiple) {
                index = 1;
            }
            var row_suffix = '';

            var gt2 = 0;
            var source_field = $$(autocomplete.scope + ' #' + autocomplete.id_var + '_' + index);
            if (source_field.length) {
                var tbl = source_field[0].parentNode.parentNode.parentNode.parentNode;
                if (tbl.hasClassName('grouping_table2')) {
                    gt2 = 1;
                    gt2calc_target = 'quantity';
                }
            }

            // we will process the ids in the order they are submitted so we can update
            // rows correctly when there are blank ones or multiple rows with same record selected
            var ids = post[post.field].split(',').map(trim);
            for (var field_idx = 0; field_idx < ids.length; field_idx++) {
                var item = autocomplete.type + '_' + ids[field_idx];
                // autocompleter is empty, maybe, skip row
                if (!data[item] || Object.prototype.toString.call(data[item]) != '[object Object]') {
                    continue;
                }

                // iterate through the data array to get the items
                if (autocomplete.on_select) {
                    // function to prepare custom data for completing the autocompleter and the related fields
                    data[item] = executeACPluginMethod(autocomplete, data[item]);
                }
                if (refresh_multiple && data[item].row) {
                    data[item].row = index = field_idx + 1;
                }
                row_suffix = '';
                if (data[item].row) {
                    row_suffix = '_' + data[item].row;
                }
                for (var i in data[item]) {
                    // iterate through the item array to get the properties
                    // to be refreshed
                    if (i.match(/^\$/)) {
                        var field = i.replace(/^\$/, '');
                        var name = field;
                        if (gt2 && field == 'price') {
                            gt2calc_target = 'price';
                        }
                        field += row_suffix;
                        field = $$(autocomplete.scope + ' #' + field);
                        if (field.length) {
                            if (gt2 && field[0].id.match(/price/)) {
                                val = parseFloat(data[item][i]);
                                if (!isNaN(val) && val != 'undefined') {
                                    var pow = Math.pow(10, env.precision.gt2_rows);
                                    val = Math.round(val * pow) / pow;
                                    val = val.toFixed(env.precision.gt2_rows);
                                    field[0].value = val;
                                } else {
                                    field[0].value = data[item][i];
                                }
                            } else {
                                field[0].value = data[item][i];
                                if (gt2 && name.match(/^(discount|surplus)_(percentage|value)/)) {
                                    //set field for discount/surplus
                                    $$(autocomplete.scope + ' #discount_surplus_field' + row_suffix).value = name;
                                }
                            }

                            // special behaviour for the date and datetime boxes
                            // there is a special formatted date/datetime
                            var other_field = $$(autocomplete.scope + ' #' + name + '_formatted' + row_suffix);
                            if (field[0].hasClassName('datebox')) {
                                if (other_field.length) {
                                    // parse the date
                                    var date = parseISODate(data[item][i]);
                                    if (date) {
                                        other_field[0].value = date.format('d.m.Y');
                                    } else if (data[item][i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4}$/)) {
                                        //the date has been formatted already dd.mm.yyyy
                                        var date = parseFormattedDate(data[item][i]);
                                        other_field[0].value = date.format('d.m.Y');
                                        field[0].value       = date.format('Y-m-d');
                                    }
                                }
                            } else if (field[0].hasClassName('datetimebox')) {
                                if (other_field.length) {
                                    // parse the date
                                    var date = parseISODate(data[item][i]);
                                    if (date) {
                                        other_field[0].value = date.format('d.m.Y, H:i');
                                    } else if (data[item][i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}$/) ||
                                            data[item][i].match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$/)) {
                                        //the datetime has been formatted already dd.mm.yyyy H:i or dd.mm.yyyy H:i:s
                                        var date = parseFormattedDate(data[item][i]);
                                        other_field[0].value = date.format('d.m.Y, H:i');
                                        field[0].value                = date.format('Y-m-d H:i:00');
                                    }
                                }
                            } else if (field[0].hasClassName('timebox')) {
                                if (other_field.length) {
                                    other_field[0].value = data[item][i];
                                }
                            } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                                // if hidden autocompleter field, check for link and update it
                                updateAutocompleteLink(field[0], data[item]);
                            } else {
                                other_field = $$(autocomplete.scope + ' #' + name + '_readonly' + row_suffix);
                                if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                                    // the readonly dropdowns have additional field with suffix
                                    // _readonly
                                    other_field[0].value = data[item][i];
                                    // change style of readonly dropdowns
                                    toggleUndefined(other_field[0]);
                                } else if (field[0].hasClassName('selbox')) {
                                    // change style of dropdowns
                                    toggleUndefined(field[0]);
                                } else if (field[0].type == 'hidden') {
                                    // readonly radio buttons (get the hidden element that each radio has)
                                    var elements = $$(autocomplete.scope + ' #' + name + '_0_readonly' + row_suffix);
                                    if (elements.length) {
                                        // get all radio buttons with the same name
                                        elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                                        if (elements.length) {
                                            // special behaviour for radio buttons
                                            for (var j = 0; j < elements.length; j++) {
                                                // check the selected element
                                                if (elements[j].value == data[item][i]) {
                                                    elements[j].checked = 'checked';
                                                } else {
                                                    elements[j].checked = '';
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        } else {
                            //get the hidden element that each radio has
                            field = $$(autocomplete.scope + '#' + name + '_0' + row_suffix);
                            if (field.length) {
                                // get all radio buttons with the same name
                                field = $$(autocomplete.scope + ' input[name="' + field[0].name + '"]');
                                if (field.length) {
                                    // special behaviour for radio buttons
                                    for (var j = 0; j < field.length; j++) {
                                        if (field[j].value == data[item][i]) {
                                            field[j].checked = 'checked';
                                        } else {
                                            field[j].checked = false;
                                        }
                                    }
                                }
                            }
                        }
                    }
                    // if autocompleter is for a basic variable and for a single field
                    if (i == 'type' && data[item][i] == 'customers' && autocomplete.var_type && autocomplete.var_type == 'basic' && !index) {
                        // refresh customer's info if needed
                        if ($$(autocomplete.scope + ' #side_panel_customers_info').length && !autocomplete.stop_customer_details) {
                            showCustomersInfo(data[item].id);
                        }

                        if ($$(autocomplete.scope + ' #side_panel_last_records').length && !autocomplete.stop_customer_details) {
                            showLastRecords(data[item]);
                        }

                        if ($$(autocomplete.scope + ' #branch').length && !autocomplete.stop_customer_details) {
                            changeCustomerBranchAndContact(data[item].id, data[item].is_company);
                        }

                        if ($$(autocomplete.scope + ' #contract').length && !autocomplete.stop_customer_details) {
                            changeCustomersContracts(data[item].id);
                        }
                    }
                }

                // autocomplete in 2nd type grouping table
                // request calculations
                if (gt2) {
                    if (autocomplete.type == 'nomenclatures') {
                        permitNegativeValues(autocomplete, data[item]);
                    }
                    gt2calc(gt2calc_target + '_' + index);
                }

                if (autocomplete.execute_after) {
                    executeAfterAutocomplete(autocomplete, data[item]);
                }
            }
            removeClass($$(autocomplete.scope + ' #' + autocomplete.field)[0], 'working');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = autocomplete.url + '&ajax_filter=1';
    new Ajax.Request(url, opt);
}

/**
 * Function to be executed from an autocompleter plugin when the user selects a certain option
 */
function executeACPluginMethod(autocomplete, data) {
    if (!autocomplete.plugin) {
        return data;
    }

    var acpp = new Object();
    var index = '';
    if (data.row) {
        index = '_' + data.row;
    }
    //save original plugin params
    var pp = autocomplete.plugin_params;
    var field;
    if (autocomplete.plugin_params) {
        for (var i in autocomplete.plugin_params) {
            if (autocomplete.plugin_params[i].match(/^\$/)) {
                //create plugin params with current form data
                acpp[i] = autocomplete.plugin_params[i].replace(/^\$/, '');
                // add row index (if any) to the field name
                field = $$(autocomplete.scope + ' #' + acpp[i] + index);
                if (!field.length) {
                    field = $$(autocomplete.scope + ' #' + acpp[i]);
                }
                if (field.length) {
                    acpp[i] = field[0].value;
                } else {
                    acpp[i] = autocomplete.plugin_params[i];
                }
            } else {
                acpp[i] = autocomplete.plugin_params[i];
            }
        }
        autocomplete.plugin_params = acpp;
    }

    var autocomplete_json = encodeURIComponent(Object.toJSON(autocomplete));
    if (pp) {
        //restore original plugin params
        autocomplete.plugin_params = pp;
    }
    var data_json = encodeURIComponent(Object.toJSON(data));

    var opt = {
        parameters: 'autocomplete=' + autocomplete_json + '&data=' + data_json,
        method: 'post',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            // get the items' data
            eval('data = ' + t.responseText + ';');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=autocompleters&autocompleters=execute_ac_on_select_method&use_ajax=1';
    new Ajax.Request(url, opt);
    return data;
}

/**
 * Function to clear some (or all) of the fields for autocompleter with confirmation
 *
 * @param {Object} autocomplete - autocompleter's parameters
 * @param {boolean} force - optional parameter to force clear without confirmation
 */
function clearAutocompleteItems(autocomplete) {
    if (clearAutocompleteItems.arguments.length > 1 ) {
        return;
    }

    Nz.confirm(i18n.messages.confirm_general, i18n['messages']['confirm_clear_autocompleter'])
        .then(()=>{
            clearAutocompleteItemsActual.apply(this, arguments);
        }).catch(()=>{});
}

/**
 * Function to clear some (or all) of the fields for autocompleter - no confirmation
 *
 * @param {Object} autocomplete - autocompleter's parameters
 * @param {boolean} force - optional parameter to force clear without confirmation
 */
function clearAutocompleteItemsActual(autocomplete) {
    var ac_field = $$(autocomplete.scope + ' [uniqid="' + autocomplete.uniqid +'"]');
    if (!ac_field.length) {
        return false;
    }
    ac_field = ac_field[0];

    var index = '';
    // get the field(row) index (if any)
    if (ac_field.id.match(/.*_\d+$/)) {
        index = ac_field.id.replace(/.*_(\d+)$/, '$1');
    }

    var row = index ? '_' + index : '';

    var tbl = ac_field.parentNode.parentNode.parentNode.parentNode;
    var gt2 = 0;
    var gt2calc_target = '';
    if (tbl && tbl.hasClassName('grouping_table2')) {
        gt2 = 1;
        gt2calc_target = 'quantity';
    }
    autocomplete.field = ac_field.id;

    // prepare data for execute_after
    var data = {id: '', row: index};
    var name, i, elements, field, other_field;

    if (autocomplete.clear_fields && autocomplete.clear_fields.length > 0) {
        // clear all the items defined in clear_fields
        for (i = 0; i < autocomplete.clear_fields.length; i++) {
            name = autocomplete.clear_fields[i].replace(/\$/, '');
            data['$' + name] = '';
            if (gt2 && name == 'price') {
                gt2calc_target = 'price';
            }
            field = $$(autocomplete.scope + ' #' + name + row);
            if (field.length) {
                field[0].value = '';
                if (gt2 && name.match(/^(discount|surplus)_(percentage|value)/)) {
                    //set field for discount/surplus
                    $$(autocomplete.scope + ' #discount_surplus_field' + row)[0].value = name;
                } else if (field[0].type == 'hidden') {
                    //readonly dropdown (select)
                    other_field = $$(autocomplete.scope + ' #' + name + '_readonly' + row);
                    if (other_field.length) {
                        other_field[0].options[other_field[0].selectedIndex].removeAttribute('selected');
                        other_field[0].selectedIndex = 0;
                    }

                    // readonly radio buttons (get the hidden element that each radio has)
                    elements = $$(autocomplete.scope + ' #' + name + '_0_readonly' + row);
                    if (elements.length) {
                        // get all radio buttons with the same name
                        elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                        if (elements.length) {
                            // special behaviour for radio buttons
                            for (var j = 0; j < elements.length; j++) {
                                // check the first element
                                if (!j) {
                                    elements[j].checked = 'checked';
                                } else {
                                    elements[j].checked = '';
                                }
                            }
                        }
                    }

                } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                    // if hidden autocompleter field, check for link and update it
                    updateAutocompleteLink(field[0], {});
                }
            } else {
                //get the hidden element that each radio has
                elements = $$(autocomplete.scope + ' #' + name + '_0' + row);
                if (elements.length) {
                    // get all radio buttons with the same name
                    elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                    if (elements.length) {
                        // special behaviour for radio buttons
                        for (var j = 0; j < elements.length; j++) {
                            // check the first element
                            if (!j) {
                                elements[j].checked = 'checked';
                            } else {
                                elements[j].checked = '';
                            }
                        }
                    }
                }
                // get the hidden element that checkbox has (_1)
                // uncheck all checkboxes with the matching name
                elements = $$(autocomplete.scope + ' #' + name + '_1' + row);
                if (elements.length && elements[0].type.match(/checkbox/i)) {
                    $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]').each(function(el) {
                        el.checked = false;
                    });
                }
            }
            other_field = $$(autocomplete.scope + ' #' + name + '_oldvalue' + row);
            if (other_field.length) {
                // clear the old value field
                other_field[0].value = '';
            }
            other_field = $$(autocomplete.scope + ' #' + name + '_formatted' + row);
            if (other_field.length && other_field[0].className.match(/date(time)?box/)) {
                // the date, datetime and time inputs have additional field with suffix _formatted... set initial value
                clearDateField(other_field[0]);
            } else {
                other_field = $$(autocomplete.scope + '#' + name + '_readonly' + row);
                if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                    // the readonly dropdowns have additional field with suffix _readonly... clear it
                    other_field[0].value = '';
                    // change style of readonly dropdowns
                    toggleUndefined(other_field[0]);
                } else if (field.length && field[0].hasClassName('selbox')) {
                    // change style of dropdowns
                    toggleUndefined(field[0]);
                }
            }
        }
        autocomplete.field = ac_field.id;

        // autocomplete in 2nd type grouping table
        // request calculations
        if (gt2) {
            if (autocomplete.type == 'nomenclatures') {
                permitNegativeValues(autocomplete, data);
            }
            gt2calc(gt2calc_target + row);
        }

        if (autocomplete.execute_after) {
            executeAfterAutocomplete(autocomplete, data);
        }

        removeClass(ac_field, 'working');
    } else if (autocomplete.fill_options && autocomplete.fill_options.length > 0) {
        // clear all the items defined in fill_options
        for (i = 0; i < autocomplete.fill_options.length; i++) {
            name = autocomplete.fill_options[i].replace(/\$([^= ]*).*/, '$1');
            data['$' + name] = '';
            if (autocomplete.type == 'nomenclatures' && autocomplete.filters && autocomplete.filters['<type_keyword>'] == 'trademark' && name.match(/customer/)) {
                // if autocompleter is for trademark it doesn't have to clear
                // the customer field
                continue;
            }
            if (gt2 && name == 'price') {
                gt2calc_target = 'price';
            }
            field = $$(autocomplete.scope + ' #' + name + row);
            if (field.length) {
                field[0].value = '';
                if (gt2 && name.match(/^(discount|surplus)_(percentage|value)/)) {
                    //set field for discount/surplus
                    $$(autocomplete.scope + ' #discount_surplus_field' + row)[0].value = name;
                } else if (field[0].type == 'hidden') {
                    //readonly dropdown (select)
                    other_field = $$(autocomplete.scope + ' #' + name + '_readonly' + row);
                    if (other_field.length) {
                        other_field[0].options[other_field[0].selectedIndex].removeAttribute('selected');
                        other_field[0].selectedIndex = 0;
                    }

                    // readonly radio buttons (get the hidden element that each radio has)
                    elements = $$(autocomplete.scope + ' #' + name + '_0_readonly' + row);
                    if (elements.length) {
                        // get all radio buttons with the same name
                        elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                        if (elements.length) {
                            // special behaviour for radio buttons
                            for (var j = 0; j < elements.length; j++) {
                                // check the first element
                                if (!j) {
                                    elements[j].checked = 'checked';
                                } else {
                                    elements[j].checked = '';
                                }
                            }
                        }
                    }

                } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                    // if hidden autocompleter field, check for link and update it
                    updateAutocompleteLink(field[0], {});
                }
            } else {
                //get the hidden element that each radio has
                elements = $$(autocomplete.scope + ' #' + name + '_0' + row);
                if (elements.length) {
                    // get all radio buttons with the same name
                    elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                    if (elements.length) {
                        // special behaviour for radio buttons
                        for (var j = 0; j < elements.length; j++) {
                            // check the first element
                            if (!j) {
                                elements[j].checked = 'checked';
                            } else {
                                elements[j].checked = '';
                            }
                        }
                    }
                }
                // get the hidden element that checkbox has (_1)
                // uncheck all checkboxes with the matching name
                elements = $$(autocomplete.scope + ' #' + name + '_1' + row);
                if (elements.length && elements[0].type.match(/checkbox/i)) {
                    $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]').each(function(el) {
                        el.checked = false;
                    });
                }
            }
            other_field = $$(autocomplete.scope + ' #' + name + '_oldvalue' + row);
            if (other_field.length) {
                // clear the old value field
                other_field[0].value = '';
            }
            other_field = $$(autocomplete.scope + ' #' + name + '_formatted' + row);
            if (other_field.length && other_field[0].className.match(/date(time)?box/)) {
                // the date, datetime and time inputs have additional field with suffix _formatted... set initial value
                clearDateField(other_field[0]);
            } else {
                other_field = $$(autocomplete.scope + ' #' + name + '_readonly' + row);
                if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                    // the readonly dropdowns have additional field with suffix _readonly... clear it
                    other_field[0].value = '';
                    // change style of readonly dropdowns
                    toggleUndefined(other_field[0]);
                } else if (field.length && field[0].hasClassName('selbox')) {
                    // change style of dropdowns
                    toggleUndefined(field[0]);
                }
            }
        }
        autocomplete.field = ac_field.id;

        // autocomplete in 2nd type grouping table
        // request calculations
        if (gt2) {
            if (autocomplete.type == 'nomenclatures') {
                permitNegativeValues(autocomplete, data);
            }
            gt2calc(gt2calc_target + row);
        }

        if (autocomplete.execute_after) {
            executeAfterAutocomplete(autocomplete, data);
        }

        removeClass(ac_field, 'working');

    } else {
        // clear all the items if no fill_options and clear_fields are defined
        var opt = {
            method: 'post',
            asynchronous: (typeof autocomplete.asynchronous != 'undefined') ? autocomplete.asynchronous : true,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                // get the items' data
                eval('data = ' + t.responseText + ';');

                if (!data.row && index) {
                    data.row = index;
                }

                for (var i in data) {
                    // iterate through properties and fill them into fields
                    // ignore all properties not starting with $
                    if (!/^\$/.test(i)) {
                        continue;
                    }
                    if (gt2 && i == '$price') {
                        gt2calc_target = 'price';
                    }
                    var name = i.replace(/\$/, '');
                    field = $$(autocomplete.scope + ' #' + name + row);
                    if (field.length) {
                        field[0].value = '';
                        if (gt2 && name.match(/^(discount|surplus)_(percentage|value)/)) {
                            //set field for discount/surplus
                            $$(autocomplete.scope + ' #discount_surplus_field' + row)[0].value = name;
                        } else if (field[0].hasClassName('autocompletebox') && field[0].hasClassName('hidden')) {
                            // if hidden autocompleter field, check for link and update it
                            updateAutocompleteLink(field[0], {});
                        }
                    } else {
                        field = $$(autocomplete.scope + ' #' + name);
                        if (field.length) {
                            field[0].value = '';
                        } else {
                            //get the hidden element that each radio has
                            var elements = $$(autocomplete.scope + ' #' + name + '_0' + row);
                            if (elements.length) {
                                // get all radio buttons with the same name
                                elements = $$(autocomplete.scope + ' input[name="' + elements[0].name + '"]');
                                if (elements.length) {
                                    // special behaviour for radio buttons
                                    for (var j = 0; j < elements.length; j++) {
                                        // check the first element
                                        if (!j) {
                                            elements[j].checked = 'checked';
                                        } else {
                                            elements[j].checked = '';
                                        }
                                    }
                                }
                            }
                        }
                    }
                    other_field = $$(autocomplete.scope + ' #' + name + '_oldvalue' + row);
                    if (other_field.length) {
                        // clear the old value field
                        other_field[0].value = '';
                    }
                    other_field = $$(autocomplete.scope + ' #' + name + '_formatted' + row);
                    if (field.length && field[0].className.match(/(datebox|datetimebox|timebox)/) && other_field.length) {
                        // the date and datetime inputs have additional field with suffix _formatted... clear it
                        other_field[0].value = '';
                    } else {
                        other_field = $$(autocomplete.scope + ' #' + name + '_readonly' + row);
                        if (other_field.length && other_field[0].tagName.match(/select/i) && other_field[0].disabled) {
                            // the readonly dropdowns have additional field with suffix _readonly... clear it
                            other_field[0].value = '';
                            // change style of readonly dropdowns
                            toggleUndefined(other_field[0]);
                        } else if (field.length && field[0].hasClassName('selbox')) {
                            // change style of dropdowns
                            toggleUndefined(field[0]);
                        }
                    }
                }
                autocomplete.field = ac_field.id;

                // autocomplete in 2nd type grouping table
                // request calculations
                if (gt2) {
                    if (autocomplete.type == 'nomenclatures') {
                        permitNegativeValues(autocomplete, data);
                    }
                    gt2calc(gt2calc_target + row);
                }

                if (autocomplete.execute_after) {
                    executeAfterAutocomplete(autocomplete, data);
                }

                removeClass(ac_field, 'working');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = autocomplete.url + '&ajax_filter=1&get_fill_options_only&field=' + ac_field.id.replace(/(_\d+$)/, '');
        new Ajax.Request(url, opt);
    }

    // if autocompleter is for a basic variable and for a single field
    if (autocomplete.type && autocomplete.var_type && autocomplete.var_type == 'basic' && !row) {
        // if autocompleter is for customers, clear customer-related data
        if (autocomplete.type == 'customers' && !autocomplete.stop_customer_details) {
            // clears the customers info panel
            if ($$(autocomplete.scope + ' #side_panel_customers_info').length) {
                showCustomersInfo(0);
            }
            if ($$(autocomplete.scope + ' #side_panel_last_records').length) {
                showLastRecords({});
            }
            // clears the branches and contact persons
            if ($$(autocomplete.scope + ' #branch').length) {
                changeCustomerBranchAndContact(0, 0);
            }
            // clears contracts
            if ($$(autocomplete.scope + ' #contract').length) {
                changeCustomersContracts(0);
            }
        } else if (autocomplete.type == 'projects' && $$(autocomplete.scope + ' #phase').length) {
            // if autocompleter is for projects, clear the phases dropdown
            changePhases(0);
        }
    }
}

/**
 * Toggle visibility of suggestions from combobox button or autocompleter field
 *
 * @param {Object} autocomplete - parameters of autocompleter
 */
function toggleAutocompleteItems(autocomplete) {
    //get field for autocompleter
    var ac_field = $$(autocomplete.scope + ' [uniqid="' + autocomplete.uniqid + '"]');
    if (!ac_field.length || ac_field[0].readOnly) {
        return false;
    }
    ac_field = ac_field[0];
    // get the Autocompleter object which was set as a property of the params on initialization
    var ac = autocomplete.ac;
    // if the suggestions are not visible, display them
    if (ac && ac.update.style.display == 'none') {
        // set behaviour according to specified combobox mode
        var combobox_mode = autocomplete.combobox_mode ? autocomplete.combobox_mode : 'input';
        if (combobox_mode == 'empty') {
            // search without any input (act as a dropdown)
            ac.tokenBounds = [0, 0];
            //ac.options.minChars = 0;
        } else if (combobox_mode == 'value') {
            // search with the current filled in value of the field
            ac.tokenBounds = null;
        } else {
            // search with the last manually entered value
            // if none, search with the current value of the field
            if (ac.oldElementValue && ac.element.value) {
                ac.element.value = ac.oldElementValue;
            } else {
                ac.tokenBounds = null;
                ac.oldElementValue = '';
            }
        }
        // focus the autocompleter field
        if (!(document.activeElement && document.activeElement.id == ac_field.id)) {
            Field.focus(ac_field);
        }
        ac.activate();
    } else {
        // if current active element is the input field of autocompleter
        // (click inside the autocomplete field)
        // hide suggestions and restore old value
        if (document.activeElement && document.activeElement.id == ac_field.id) {
            ac.hide();
            // temporarily remove combobox class to cause restoration of old value
            removeClass(ac_field, 'combobox');
            cancelAutocompleter(autocomplete);
            addClass(ac_field, 'combobox');
        }
        // else do nothing: on button click the autocompleter field will lose focus
        // and the suggestions will be hidden and cancelAutocompleter will be executed
    }
    Event.stop(window.event);
}

/**
 * Return the old value of an autocompleter field when autocompleter is canceled
 *
 * @param {Object} autocomplete - parameters of autocompleter
 */
function cancelAutocompleter(autocomplete) {

    var element = $$(autocomplete.scope + ' [uniqid="' + autocomplete.uniqid + '"]');
    var row = '', oldvalue = [];
    if (element.length) {
        // if autocompleter is in a grouping table, its id has a row suffix, otherwise not
        row = element[0].id.match(/^.*(_\d+)$/) ? element[0].id.replace(/^.*(_\d+)$/, '$1') : '';

        if (row) {
            oldvalue = $$(autocomplete.scope + ' #' + element[0].id.replace(/^(.*)(_\d+)$/, '$1_oldvalue$2'));
        } else {
            oldvalue = $$(autocomplete.scope + ' #' + element[0].id + '_oldvalue');
        }
        if (!oldvalue.length && element[0].id.match(/_autocomplete/)) {
            oldvalue = $$(autocomplete.scope + ' #' + element[0].id.replace(/_autocomplete/, '_oldvalue'));
        }
    }

    var clearAutocompleterSuggestions = function() {
        if (element.length) {
            // if autocompleter is the active field (it lost focus and regained it), do not clear
            if (document.activeElement && document.activeElement.id == element[0].id && element[0].hasClassName('combobox')) {
                return false;
            }
            if (oldvalue.length) {
                element[0].value = oldvalue[0].value;
            }
            removeClass(element[0], 'working');
        }
        // make sure that the suggestions content is cleaned up
        var autocomplete_options_div = $$(autocomplete.scope + ' #suggestions_' + autocomplete.uniqid);
        if (autocomplete_options_div.length) {
            autocomplete_options_div[0].innerHTML = '';
            autocomplete_options_div[0].style.display = 'none';
        }
    };

    if (!element.length || !oldvalue.length) {
        // make sure that the suggestions content is cleaned up
        setTimeout(clearAutocompleterSuggestions, 1000);
        if (element.length) {
            // if this is special autocompleter for emails it has not got oldvalues field
            // clear id value only when custom text has been entered, otherwise keep it
            var id_var = $$(autocomplete.scope + ' #' + autocomplete.id_var + row);
            if (id_var.length && element[0].hasClassName('working') && element[0] != id_var[0]) {
                id_var[0].value = '';
            }
            removeClass(element[0], 'working');
            return false;
        } else {
            return false;
        }
    }

    if (element[0].value != oldvalue[0].value) {
        addClass(element[0], 'working');
    }

    setTimeout(clearAutocompleterSuggestions, 1000);

    return true;
}

/**
 * Updates link for a related readonly autocompleter field when its data is
 * updated from current autocompleter
 *
 * @param {Object} field - related readonly autocompleter field
 * @param {Object} data - autocomplete data
 */
function updateAutocompleteLink(field, data) {
    var context = data.context || window;
    var scope = getAutocompleteScope(field);
    if (scope) {
        scope = '#' + scope.id;
    }
    var link = context.$$(scope + ' a.' + field.getAttribute('uniqid'));
    // when adding a new row, link still has uniqid of original field as class name
    if (!link.length && data.uniqid) {
        context.$$(scope + ' a.' + data.uniqid).each(function(l) {
            if (l.up('td') == field.up('td')) {
                link.push(l);
            }
        });
    }
    if (link.length) {
        link = link[0];
        var value_id = '';
        link.className.split(' ').each(function(el) {
            if (el.match(/^id_var-/)) {
                // field is formatted differently in different autocomplete actions
                var regex = new RegExp('^\\$?' + el.replace(/^id_var-/, '') + '(_\\d+)?$');
                for (var prop in data) {
                    if (prop.match(regex)) {
                        value_id = data[prop];
                        break;
                    }
                }
                throw $break;
            }
        });
        // remove code from value for basic ACs
        link.innerHTML = field.id.match(/_autocomplete(_\d+)?$/) && field.value.match(/^\[[^\]]*\]/) ?
            field.value.replace(/^\[[^\]]*\]\s*/, '') : field.value;
        link.href = link.href.replace(/=\d*$/, '=' + value_id);
        link.title = link.title.replace(/^([^:]+:).*$/, '$1 ' + link.innerHTML);
        if (!value_id) {
            addClass(link, 'hidden');
        } else {
            removeClass(link, 'hidden');
        }
    }
}

 /**
 * Triggers action for copying of data from selected record into current record.
 * Function should be called as execute_after of the autocompleter itself.
 *
 * @param {Object} autocomplete - autocompleter settings
 * @param {Object} data - data for record selected in autocompleter
 * @return void
 */
function copyVars(autocomplete, data) {
    // autocompleter should have a value (do not execute on clear)
    if (!data.id) {
        return;
    }
    var frm;
    if (autocomplete.field) {
        $$((autocomplete.scope || '') + ' #' + autocomplete.field).each(function(f) { frm = f.up('form'); });
    }
    if (frm) {
        frm.action += '&source_model_id_var=' + (autocomplete.id_var || '');
        // perform calculations
        if (typeof frm.onsubmit == 'function') {
            frm.onsubmit();
        }
        // submit form
        if (!frm.submitted) {
            frm.submit();
        }
    }
}

/**
 * Triggers action for copying of data from record whose id is set in "id_var"
 * field into current record.
 *
 * @param {string} id_var - form field which holds id of record to copy data from
 * @param {boolean} skip_confirm - whether to skip confirmation modal dialog
 * @return void
 */
function copyVarsOnClick(id_var, skip_confirm) {
    var el, frm;
    if (event) {
        // if called through user-triggered event (such as click)
        // get the triggering element (the button)
        el = event.target || event.srcElement || event.originalTarget;
    } else if ($$('[name="' + id_var + '"]').length == 1) {
        // if called directly
        // get the element holding the source model id
        el = $$('[name="' + id_var + '"]')[0];
    }
    var copy_fn = function(el) {
        // specify which field to copy from (there might be more than one in form)
        if (frm && id_var) {
            frm.action += '&source_model_id_var=' + id_var;
            // perform calculations
            if (typeof frm.onsubmit == 'function') {
                frm.onsubmit();
            }
            // submit form
            if (!frm.submitted) {
                frm.submit();
            }
        }
    }
    if (el) {
        frm = el.up('form');
    }
    if (frm) {
        // ask for confirmation first, if specified
        return skip_confirm ? copy_fn(el) : confirmAction('copy_all', copy_fn, el, i18n.messages.confirm_general);
    } else {
        // if button is clicked from view mode
        // search for edit link in the action menu and redirect to edit mode if possible
        var link = ($('edittopic_action') || $('edit_action'));
        if (link && link.tagName == 'A' && link.href) {
            url = link.href + (el ? '#' + el.id : '');
            redirect(url);
        } else {
            // inform the user that it is not happening
            alert(i18n['messages']['error_no_access_to_action']);
        }
    }
}

 /**
  * A special function used with the Zapatec Calendar
  * It disallows dates before or after certain date defined in the disallow_before/disallow_after attributes in the input date
  */
function disallowDates(check_date) {
    var endDate = disallowAfter = startDate = disallowBefore = false;

    var c_date = new Date(check_date.getTime());
    c_date.setHours(0,0,0,0);
    if (!this.params || !this.params.inputField) {
        return true;
    }

    if (disallowBefore = $(this.params.inputField.id.replace(/_formatted/, '')).getAttribute('disallow_before')) {

        if (disallowBefore.match(/^\d{4}-\d{2}-\d{2}/)) {
            //we have date in ISO format
            startDate = new Date(disallowBefore);
        } else if (disallowBefore.match(/^\$/)) {
            //we have to get the date from another field
            disallowBefore = disallowBefore.replace(/\$/, '');
            //if we are in a group table - get row index
            var idx = this.params.inputField.id.replace(/^.*(_\d+)$/, '$1');
            var field = $(disallowBefore + idx);
            if (!field) {
                //check for free field
                field = $(disallowBefore);
            }
            if (field && field.value && field.value.match(/^\d{4}-\d{2}-\d{2}/)) {
                startDate = new Date(field.value);
            } else {
                return false;
            }
        }
        if (!startDate) {
            startDate = new Date();
        }

        startDate.setHours(0,0,0,0);
        if (startDate > c_date) {
            // startDate is defined, make sure cal date is NOT before start date
            return true;
        }
    }

    if (disallowAfter = $(this.params.inputField.id.replace(/_formatted/, '')).getAttribute('disallow_after')) {
        if (disallowAfter.match(/^\d{4}-\d{2}-\d{2}/)) {
            //we have date in ISO format
            endDate = new Date(disallowAfter);
        } else if (disallowAfter.match(/^\$/)) {
            disallowAfter = disallowAfter.replace(/\$/, '');
            //we have to get the date from another field

            //if we are in a group table - get row index
            var idx = this.params.inputField.id.replace(/^.*(_\d+)$/, '$1');
            var field = $(disallowAfter + idx);
            if (!field) {
                //check for free field
                field = $(disallowAfter);
            }
            if (field && field.value && field.value.match(/^\d{4}-\d{2}-\d{2}/)) {
                endDate = new Date(field.value);
            } else {
                return false;
            }
        }

        if (!endDate) {
            endDate = new Date();
        }

        endDate.setHours(0,0,0,0);
        if (endDate < c_date) {
            // endDate defined, calendar date CANNOT be after endDate
            return true;
        }
    }

    return false;
}

/**
 * Special function that translate the date from the Zapatec Calendar if ISO
 * format and write it in the hidden INPUT field, which we use to pass the date
 * to the data base
 *
 * @param element - object which represents the visible INPUT field
 */
function formatDate(element) {

    if (element.params) {
        // get the field from calendar object params
        element = element.params.inputField;
    }
    var iso_el = $(element.id.replace(/_formatted/ , ''));
    var date = '';
    if (element.value.length > 10) {
        if (element.value != defaultDateTime) {
            date = element.value.replace(/(.{2})\.(.{2})\.(.{4})\,\s(.{2})\:(.{2})/, '$3-$2-$1 $4:$5:00');
        } else {
            date = '';
        }
    } else if (element.value.length > 5) {
        if (element.readOnly) {
            //date change is not allowed
            if (iso_el.value) {
               element.value = iso_el.value.replace(/(.{4})-(.{2})-(.{2}).*/, '$3.$2.$1');
            }  else {
                element.value = defaultDate;
            }
            return;
        }
        if (element.value != defaultDate) {
            date = element.value.replace(/(.{2})\.(.{2})\.(.{4}).*/, '$3-$2-$1');
        } else {
            date = '';
        }
    } else {
        if (element.value != defaultTime) {
            date = element.value;
        } else {
            date = '';
        }
    }
    if (iso_el.getAttribute('onchange') && iso_el.value != date) {
        iso_el.value = date;
        iso_el.onchange();
    } else {
        iso_el.value = date;
    }

}

/**
 * Special function that parses dates from ISO format
 *
 * @param isodate - string with ISO date
 */
function parseISODate(isodate) {

    var date = '';
    if (isodate.match(/^[0-9]{4}-[0-9]{2}-[0-9]{2}$/)) {
        //yyyy--mm-dd
        var year  = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/, '$1');
        var month = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/, '$2') - 1;
        var day   = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2})$/, '$3');
        date = new Date(year, month, day);
    } else if (isodate.match(/^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}:[0-9]{2}$/)) {
        //yyyy--mm-dd H:i:s
        var year    = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$1');
        var month   = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$2') - 1;
        var day     = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$3');
        var hours   = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$4');
        var minutes = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$5');
        var seconds = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$6');
        date = new Date(year, month, day, hours, minutes, seconds);
    } else if (isodate.match(/^[0-9]{4}-[0-9]{2}-[0-9]{2} [0-9]{2}:[0-9]{2}$/)) {
        //yyyy--mm-dd H:i
        var year    = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})$/, '$1');
        var month   = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})$/, '$2') - 1;
        var day     = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})$/, '$3');
        var hours   = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})$/, '$4');
        var minutes = isodate.replace(/^([0-9]{4})-([0-9]{2})-([0-9]{2}) ([0-9]{2}):([0-9]{2})$/, '$5');
        date = new Date(year, month, day, hours, minutes, 0);
    }
    return date;
}

/**
 * Special function that parses formatted dates
 *
 * @param formatted_date - string with formatted date
 */
function parseFormattedDate(formatted_date) {

    var date = '';
    if (formatted_date.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4}$/)) {
        //dd.mm.yyyy
        var year  = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4})$/, '$3');
        var month = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4})$/, '$2') - 1;
        var day   = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4})$/, '$1');
        date = new Date(year, month, day);
    } else if (formatted_date.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}:[0-9]{2}$/)) {
        //dd.mm.yyyy H:i:s
        var year    = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$3');
        var month   = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$2') - 1;
        var day     = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$1');
        var hours   = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$4');
        var minutes = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$5');
        var seconds = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2}):([0-9]{2})$/, '$6');
        date = new Date(year, month, day, hours, minutes, seconds);
    } else if (formatted_date.match(/^[0-9]{2}\.[0-9]{2}\.[0-9]{4} [0-9]{2}:[0-9]{2}/)) {
        //dd.mm.yyyy H:i
        var year    = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2})$/, '$3');
        var month   = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2})$/, '$2') - 1;
        var day     = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2})$/, '$1');
        var hours   = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2})$/, '$4');
        var minutes = formatted_date.replace(/^([0-9]{2})\.([0-9]{2})\.([0-9]{4}) ([0-9]{2}):([0-9]{2})$/, '$5');
        date = new Date(year, month, day, hours, minutes, 0);
    }
    return date;
}

/**
 * Special function that Initialize the Zapatec Calendar *
 *
 * @param elementID - the ID of the visible INPUT field
 * @param triggerButton - the ID of the button which will activate the calendar
 * @param visibleFormat - the date format user will see
 * @param innerFormat - the date format which will be passed to the data
 *        base(ISO)
 * @param statusFunction - function that can be used to change the appearance of
 *        a specific date
 */
function calendarInit(elementId, triggerButton, showTime, visibleFormat, innerFormat, statusFunction) {
    if ($(elementId).readOnly) {
        return true;
    }
    var cal = Zapatec.Calendar.setup({
                            firstDay          : 1,
                            weekNumbers       : true,
                            showOthers        : true,
                            showsTime         : showTime,
                            singleClick       : false,
                            electric          : false,
                            canType           : true,
                            inputField        : elementId,
                            button            : triggerButton,
                            ifFormat          : visibleFormat,
                            daFormat          : innerFormat,
                            dateStatusFunc    : statusFunction,
                            onUpdate          : formatDate
                          });
}

function loadColorPicker(button, input, color) {
    colorInputPicker = new Zapatec.ColorPicker({
        button : button,
        inputField: input,
        color: color,
        offset: 0,
        eventListeners:{select: function (color) {
                $(input).style.backgroundColor = color;
                $(input).style.color = color;
            }
        }
    });
}

/**
 * Special function that disables the content area of the FCK editor
 *
 * @param editorInstance - id of the FCK editor instance
 * @param InstanceName - name f the FCK editor instance
 */
function CKeditor_disableMode(editorInstance, InstanceName) {
    // disable the editArea
    if (document.all) {
        editorInstance.document.$.body.disabled = true;
    } else {
        editorInstance.document.$.designMode="off";
    }
}

/**
 * displays/toggles content
 *
 * @param el_name - element name
 * @param row - table row index
 */
function toggleContent(el_prefix, row) {
      el_full = $(el_prefix + '_full_' + row);
      el_part = $(el_prefix + '_part_' + row);
      if (el_full.style.display == 'none') {
          el_part.style.display = 'none';
          el_full.style.display = 'block';
      } else {
          el_full.style.display = 'none';
          el_part.style.display = 'block';
      }
      new Effect.ScrollTo(el_full.parentNode.parentNode);
}

/**
 * displays/toggles tabs and their container
 *
 * @param el_name - element name
 * @param row - table row index
 */
function toggleTabs(element) {
    var tab_id = element.parentNode.parentNode.id.replace(/tab_/, '');
    var tab_container_id = 'tab_container_'+tab_id;
    var tab_toggler_id = 'tab_toggler_'+tab_id;

    // hide the containers of the tabs
    var tab_containers = Element.select($(tab_container_id).parentNode, '.scroll_box');
    if (!tab_containers.length) {
        tab_containers = Element.select($(tab_container_id).parentNode, '.scroll_box_checkboxes');
    }

    for (var i = 0; i < tab_containers.length; i++) {
        if (tab_containers[i].id != tab_container_id) {
            tab_containers[i].style.display = 'none';
        }
    }
    $(tab_container_id).style.display = '';

    // make all the tabs unselected
    var tab_spans = element.parentNode.parentNode.parentNode.getElementsByTagName('span');
    for (var i = 0; i < tab_spans.length; i++) {
        removeClass(tab_spans[i], 'selected');
    }

    // add selected to the clicked tab
    addClass(element.parentNode, 'selected');

    // display the tab toggler
    var togglers = element.parentNode.parentNode.parentNode.parentNode.getElementsByTagName('div');
    for (var i = 0; i < togglers.length; i++) {
        togglers[i].style.display = 'none';
    }
    if ($(tab_toggler_id)) {
        $(tab_toggler_id).style.display = '';
    }
}

/**
 * displays/toggles tabs for related records and their containers
 *
 * @param element - span element of menu item
 */
function toggleRelatedTabs(element) {
    const tabEl = element.closest('.nz-actions-list-item').querySelector('a.nz-tab-button');
    const evt1 = new MouseEvent('click', {
            bubbles: true,
            cancelable: true,
        });

    tabEl.dispatchEvent(evt1);
    // set cookie for selected tab
    Cookie.set(env.module_name + '_selected_related_tab', element.id.replace(/tab_/, ''));
}

function getMousePosition(e) {
    var posx = 0;
    var posy = 0;
    if (!e) {
        var e = window.event;
    }
    if (e.pageX || e.pageY) {
        posx = e.pageX;
        posy = e.pageY;
    }
    else if (e.clientX || e.clientY) {
        posx = e.clientX + document.body.scrollLeft
            + document.documentElement.scrollLeft;
        posy = e.clientY + document.body.scrollTop
            + document.documentElement.scrollTop;
    }
    else if (e.screenX || e.screenY) {
        posx = e.screenX + document.body.scrollLeft
            + document.documentElement.scrollLeft;
        posy = e.screenY + document.body.scrollTop
            + document.documentElement.scrollTop;
    }
    // posx and posy contain the mouse position relative to the document

    return new Array(posx, posy);
}

/**
 * Gets absolute coordinates of an element by recursively adding offset of parent elements
 *
 * @param obj - html element
 * @return array - top and left offset of element
 */
function findPos(obj) {
    var curleft = 0;
    var curtop = 0;
    var in_lightbox = $(obj).up('#lightbox');

    if (obj.offsetParent) {
        do {
            // this is added because of elements located in lightbox
            if (in_lightbox && $(obj).getStyle('position') != 'static') {
                break;
            }
            curleft += obj.offsetLeft;
            curtop += obj.offsetTop;
        // The tricky bit: return value of the = operator
        } while (obj = obj.offsetParent);
    }

    return [curleft, curtop];
}

/**
 * Function to show only the required deadline element for projects' phases
 *
 * @param element - element name
 * @param table - table name
 */
function showRequiredDeadlineElement(element, table) {
    var all_deadline_elements = ['stages_deadline_days_hours_options', 'stages_deadline_date_options', 'stages_no_deadline_options'];

    var table_divs = $(table).getElementsByTagName('div');

    // regular expression for Days and Hours
    var regex_days_hours = 'stages_deadline_days_hours_options_[0-9]*';
    var re_days_hours = new RegExp(regex_days_hours);

    // regular expression for Date
    var regex_date = 'stages_deadline_date_options_[0-9]*';
    var re_date = new RegExp(regex_date);

    // regular expression for No deadline
    var regex_no_deadline = 'stages_no_deadline_options_[0-9]*';
    var re_no_deadline = new RegExp(regex_no_deadline);

    // var index_stage = element.name.replace(/.*\[(\d+)\]$/, '$1');

    var active_element_name = '';
    if (element.value == '1') {
        active_element_name = 'stages_deadline_days_hours_options';
    } else if (element.value == '2') {
        active_element_name = 'stages_deadline_date_options';
    } else {
        active_element_name = 'stages_no_deadline_options';
    }

    for (var i = 0; i < table_divs.length; i++) {
        if (table_divs[i].id.match(regex_days_hours) || table_divs[i].id.match(regex_date) || table_divs[i].id.match(re_no_deadline)) {
            var current_name = table_divs[i].id.replace(/_[0-9]*$/, '');
            if (current_name == active_element_name) {
                table_divs[i].style.display = 'block';
                var active_inputs = table_divs[i].getElementsByTagName('input');
                for (var j = 0; j < active_inputs.length; j++) {
                    if (active_inputs[j]) {
                        active_inputs[j].disabled = false;
                    }
                }
            } else {
                table_divs[i].style.display = 'none';
                var active_inputs = table_divs[i].getElementsByTagName('input');
                for (var j = 0; j < active_inputs.length; j++) {
                    if (active_inputs[j]) {
                        active_inputs[j].disabled = true;
                    }
                }
            }
        }
    }
}

/**
 * Function to manage checkboxes as radio buttons
 *
 * @param element - element name
 */
function checkboxAsRadio(element) {
    var table = element.parentNode.parentNode.parentNode;
    var checkboxes = $(table).getElementsByTagName('input');
    for (var i = 0; i < checkboxes.length; i++) {
        if (checkboxes[i].id == element.id) {
            checkboxes[i].checked = true;
        } else {
            checkboxes[i].checked = false;
        }
    }
}

/**
 * Function to show or hide the fields for working hours and budget in Projects
 * module
 *
 * @param element - element name
 */
function checkTypeWorkingHoursAndBudget(element) {
    var selected_index = element.selectedIndex;
    var selected_option = element.options[selected_index];
    var working_hours_row = $('working_hours_row');
    var planned_budget_row = $('planned_budget_row');
    if (selected_option.hasClassName('working_hours')) {
        working_hours_row.style.display = "table-row";
        var inputs = working_hours_row.getElementsByTagName('input');
        for (var i = 0; i < inputs.length; i++) {
            inputs[i].disabled = false;
        }
    } else {
        working_hours_row.style.display = "none";
        var inputs = working_hours_row.getElementsByTagName('input');
        for (var i = 0; i < inputs.length; i++) {
            inputs[i].disabled = true;
        }
    }
    if (selected_option.hasClassName('planned_budget')) {
        planned_budget_row.style.display = "table-row";
        var inputs = planned_budget_row.getElementsByTagName('input');
        for (var i = 0; i < inputs.length; i++) {
            inputs[i].disabled = false;
        }
    } else {
        planned_budget_row.style.display = "none";
        var inputs = planned_budget_row.getElementsByTagName('input');
        for (var i = 0; i < inputs.length; i++) {
            inputs[i].disabled = true;
        }
    }
}

/**
 * Shows panel with files of model
 *
 * @element - element name
 * @module - module of model
 * @controller - controller of model
 * @id - id of model
 * @files_ids - specific files ids if certain files are searched
 * @archive - show if the model is archived
 */
function showFiles(element, module, controller, id, files_ids, archive) {
    var model = '';
    if (module != controller) {
        model = module + '_' + controller.substring(0, controller.length - 1);
    } else {
        model = module.substring(0, module.length - 1);
    }
    var div_id = 'attachments_' + model + '_' + id;
    var td = element.parentNode.parentNode;

    var searched_files_ids = '';
    if (files_ids) {
        searched_files_ids = '&searched_files_ids=' + files_ids;
        row_element = element.parentNode.parentNode.parentNode;
        div_id = div_id + '_' + row_element.rowIndex;
    }

    var div = $(td).down('#' + div_id);

    if (!div) {
        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                // alert(t.responseText);
                if (!checkAjaxResponse(t.responseText)) {
                    Effect.Fade('loading');
                    return false;
                }
                div = document.createElement('div');
                div.id = div_id;
                div.style.position = 'absolute';
                div.style.visibility = 'hidden';
                div.style.zIndex = '10';
                div.onmouseover = function() {
                    mcancelclosetime();
                };
                div.onmouseout = function() {
                    mclosetime();
                };
                div.setAttribute('onMouseOver', 'mcancelclosetime()');
                div.setAttribute('onMouseOut', 'mclosetime()');
                div.innerHTML = t.responseText;
                if (td.down('#' + div_id)) {
                    td.down('#' + div_id).remove();
                }
                td.appendChild(div);

                mopen(div);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        Effect.Center('loading');
        Effect.Appear('loading');

        var url = env.base_url + '?' + env.module_param + '=' + module + '&';
        if (module != controller) {
            url += env.controller_param + '=' + controller + '&' + controller;
        } else {
            url += module;
        }
        url += '=ajax_getfiles&real_module=' + module + '&real_controller=' + controller + '&model_id=' + id + searched_files_ids;
        if (archive) {
            url += '&archive=1';
        }
        new Ajax.Request(url, opt);
    } else {
        mopen(div);
    }
}

/**
 * Shows info popup for last communication records for model
 *
 * @param {Object} element - trigger element (div or td)
 * @param {string} communication_type - communication type - emails or comments
 * @param {string} module - module of model
 * @param {string} controller - controller of model
 * @param {number} id - id of model
 * @param {number} archive - flag if the model is archived
 * @return void
 */
function showCommunicationsInfo(element, communication_type, module, controller, id, archive) {
    element = $(element);
    var model = '';
    if (module != controller) {
        model = module + '_' + controller.substring(0, controller.length - 1);
    } else {
        model = module.substring(0, module.length - 1);
    }
    var div_id = communication_type + '_' + model + '_' + id;
    var td = element.tagName == 'TD' ? element : element.up('td');
    var div = $(td).down('#' + div_id);

    if (!div) {
        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                // alert(t.responseText);
                if (!checkAjaxResponse(t.responseText)) {
                    Effect.Fade('loading');
                    return false;
                }
                div = document.createElement('div');
                div.id = div_id;
                div.classList.add('nz-surface', 'nz-elevation--z6');
                div.style.position = 'absolute';
                div.style.visibility = 'hidden';
                div.style.zIndex = '10';
                div.onmouseover = function() {
                    mcancelclosetime();
                };
                div.onmouseout = function() {
                    mclosetime();
                };
                div.setAttribute('onMouseOver', 'mcancelclosetime()');
                div.setAttribute('onMouseOut', 'mclosetime()');
                div.innerHTML = t.responseText;
                if (td.down('#' + div_id)) {
                    td.down('#' + div_id).remove();
                }
                td.appendChild(div);

                mopen(div);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        Effect.Center('loading');
        Effect.Appear('loading');

        var url = env.base_url + '?' + env.module_param + '=communications&communications';
        url += '=ajax_get_communications_info&real_module=' + module + '&real_controller=' + controller + '&model_id=' + id + '&communication_type=' + communication_type;
        if (archive && archive != 0) {
            url += '&archive=1';
        }
        new Ajax.Request(url, opt);
    } else {
        mopen(div);
    }
}

/**
 * Displays notifications in positioned container at top of page or in a system
 * alert dialog on failure.
 *
 * @param {string} content - formatted content to display
 * @param {Object} config - optional parameters for customizable values
 * @return {boolean}
 */
displayNotificationFixed = (function() {
    // variables will be available only for functions defined in current scope
    var container,
        timer,
        fade_effect,
        config = {
            fade_duration: 3,
            display_duration: 5,
            max_msg: 3
        };

    /**
     * Define the main function, then define the auxiliary functions, set the
     * public ones as properties of current function, keep the rest and the
     * variables private.
     */
    var display = function(content) {

        container = getContainer();

        config = Object.extend(config, arguments[1] || { });

        if (container) {
            if (content) {
                clearCloseTimeout();

                if (container.style.display == 'none' || container.style.opacity) {
                    container.innerHTML = content;
                    container.style.display = '';
                    Effect.Appear(container);
                } else {
                    var num_prev_msg = container.innerHTML.match(/>\.\.\.</g);
                    num_prev_msg = num_prev_msg != null ? num_prev_msg.length : 0;
                    // keep displaying previous messages but no more than specified number of sets at a time
                    if (num_prev_msg < config.max_msg - 1) {
                        container.innerHTML += '<ul style="list-style: none;"><li>...</li></ul>' + content;
                    } else {
                        container.innerHTML = content;
                    }
                }

                setCloseTimeout();
            } else {
                //container.innerHTML = content;
                startFade();
                clearCloseTimeout();
            }
        } else {
            if (content) {
                alert(content.stripTags().strip());
            }
        }
        return true;
    };

    /**
     * Creates fixed message div container if not existing in page and returns it
     *
     * @return {Object} - fixed message div container
     */
    function getContainer() {
        var div_id = 'message_container_fixed';
        var div = $(div_id);
        if (!div) {
            div = document.createElement('div');
            div = $(div);
            div.id = div_id;
            div.setStyle({
                width: '500px',
                display: 'none'
            });
            div.addClassName('message_container_fixed');

            div.observe('click', function(event) { close(event, {fade_duration: 0}); });
            div.observe('mouseenter', cancelFade);
            div.observe('mouseleave', close);

            document.body.appendChild(div);
        }
        div.style.marginLeft = div.style.marginRight = ((document.body.clientWidth - div.style.width.replace('px', '')) / 2) + 'px';

        return div;
    }

    /**
     * Hide notification panel
     */
    function close(event, params) {
        if (!isSubelementClicked()) {
            displayNotificationFixed('', params);
            event.preventDefault();
        }
    }

    /**
     * Start effect for hiding notification panel
     */
    function startFade() {
        // clear hash in page address
        if (window.location.hash && typeof history != 'undefined') {
            history.replaceState("", document.title, window.location.pathname + window.location.search);
        }
        fade_effect = Effect.Fade(container, {duration: config.fade_duration});
    }

    /**
     * Makes notification panel fully visible and clears timeout to hide it
     */
    function cancelFade() {
        if (typeof fade_effect != 'undefined') {
            fade_effect.cancel();
            Effect.Appear(fade_effect.element);
        }
        clearCloseTimeout();
    }

    /**
     * Starts notification close timer
     */
    function setCloseTimeout() {
        timer = setTimeout(startFade, config.display_duration * 1000);
    }

    /**
     * Clears notification close timer
     */
    function clearCloseTimeout() {
        if (typeof timer != 'undefined') {
            clearTimeout(timer);
        }
    }

    /**
     * Prepares an array of strings as a formatted HTML string (unordered list) for
     * display as a notification of specified type.
     *
     * @param {Object} content - array of strings
     * @param {string} mode - type of notification (error, warning, message)
     * @return {string} - formatted HTML content
     */
    display.formatContent = function(content, mode) {
        if (content && typeof content != 'object') {
            content = [content];
        }
        if (content && typeof content == 'object' && content.length) {
            if (!mode) {
                mode = 'error';
            }
            // format content
            content = '<ul class="' + mode + '"><li>' + content.join('</li><li>') + '</li></ul>';
        } else {
            content = '';
        }
        return content;
    };

    return display;
})();
// end displayNotificationFixed

/**
 * Updates total container cell when new record of that type is added from
 * lightbox opened from list of records
 *
 * @param {string} field - name of field (variable) in list
 * @param {string} model - model name (singular, lowercase)
 * @param {number} model_id - model id
 * @param {Object} result - new values for total number of records and history,
 * if not specified, current value is incremented; parameters for adding
 * event-handling functionality to container cell
 * @return void
 */
function updateInlineTotals(field, model, model_id, result) {
    if (typeof result != 'object') {
        result = {};
    }
    var total = result.total || 0;
    var selector_class = field + '_' + model + '_' + model_id;

    var checkEmpty =
        typeof result.check_empty_function === 'function' ?
        result.check_empty_function :
        function(content) {
            return content.strip() === '';
        }

    $$('div.' + selector_class + ' > span.' + field + '_total').each(function(t) {
        if (!total) {
            total = parseInt(t.innerText.strip());
            if (isNaN(total)) {
                total = 0;
            }
            total = (total + 1).toString();
        }
        t.up('td').select('div#' + selector_class).invoke('remove');

        if (checkEmpty(t.innerText)) {
            var module = model + 's',
            controller = module;
            if (module.indexOf('_') > -1) {
                controller = module.substring(module.indexOf('_') + 1);
                module = module.substring(0, module.indexOf('_'));
            }

            t.up('td').select('div.' + selector_class).each(function(d) {
                // add onmouseenter and onmouseleave event handlers
                if (typeof result.mouseenter_function == 'function') {
                    var fn = result.mouseenter_function;
                    // specific parameters between first and common parameters
                    var fn_args = result.mouseenter_function_args ? result.mouseenter_function_args.slice(0) : [];
                    // first parameter is the element that triggers event
                    fn_args.unshift(d);
                    // routing parameters are the same
                    fn_args.push(module, controller, model_id, '0');

                    d.observe(
                        'mouseenter',
                        function() {
                            fn.apply(null, fn_args);
                        }
                    );
                    d.observe('mouseleave', mclosetime);
                }

                // add onclick event handler
                if (result.click_action) {
                    d.addClassName('pointer');
                    d.observe(
                        'click',
                        function() {
                            url =
                                env.base_url + '?' + env.module_param + '=' + module + '&' +
                                (module == controller ? module : env.controller_param + '=' + controller + '&' + controller) + '=' +
                                result.click_action + '&' + result.click_action + '=' + model_id +
                                (result.click_additional_params || '');
                            redirect(url);
                        }
                    );

                }
            });
        }

        // set new total
        t.innerText = total;
    });

    // also update/reload history if present anywhere in the page (incl. dashlets...)
    total = result.history_total || 0;
    $$('div.history_activity_' + model + '_' + model_id).each(function(d) {
        if (!total) {
            total = parseInt(d.innerText.strip());
            if (isNaN(total)) {
                total = 0;
            }
            total = (total + 1).toString();
        }
        d.select('span.history_activity_total').each(function(t) {
            t.innerText = total;
        });

        var details_row = d.up('tr').next();
        if (details_row && details_row.hasClassName('details_row')) {
            var details_container = details_row.down('div.history_activity_container');
            if (details_container) {
                details_container.innerHTML = '';
                if (details_row.visible() && details_container.visible()) {
                    details_row.hide();
                    if (typeof d.onclick == 'function') {
                        d.onclick();
                    }
                }
            }
        }
    });
}

/**
 * Shows lightbox for inline adding of comment or email from list of records
 *
 * @param {Event} event - click event
 * @param {string} type_record - type of record to add (comment or email)
 * @param {string} module - module of model
 * @param {string} controller - controller of model
 * @param {number} id - id of model
 * @return {boolean} - always return false
 */
function loadInlineAddCommunication(event, type_record, module, controller, id) {
    Effect.Center('loading');
    Effect.Appear('loading');
    var el = event.target || event.srcElement || event.originalTarget;
    new Ajax.Request(
        env.base_url + '?' + env.module_param + '=communications&communications=ajax_load_communication_add_panel&type_record=' + type_record +
            '&action=add&model_id=' + id + '&communication_type=' + type_record + 's&module=' + module +
            (module != controller && controller ? '_' + controller : '') + '&inline_add=1',
        {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                var result = eval('(' + t.responseText + ')');

                if (result.messages) {
                    displayNotificationFixed(result.messages);
                    Effect.Fade('loading');
                    return;
                }

                lb = new lightbox({
                    content: result,
                    title: ' ',
                    width: type_record == 'email' ? 820 : 600,
                    icon: type_record == 'email' ? 'email.png' : 'comments.png',
                    onActivate: function() {
                        var content_container = $(this.params.uniqid);

                        // hide some elements
                        content_container.select('td.t_caption').invoke('hide');

                        // update lightbox caption
                        this.params.title = content_container.down('td.t_caption > .t_caption_title').innerText || '';
                        /*if (el.up('tr').down('img.help')) {
                            var tmp_img = el.up('tr').down('img.help').cloneNode(true);
                            tmp_img.style.marginBottom = '-2px';
                            this.params.title = tmp_img.outerHTML + ' ' + this.params.title;
                        }*/
                        this.updateCaption();

                        // make CKEditor dialogs visible in lightbox
                        CKEDITOR.config.baseFloatZIndex = $('lightbox').getStyles().zIndex || CKEDITOR.config.baseFloatZIndex;
                    },
                    closeHandler: function() {
                        // perform clean-up when lightbox is closed
                        if (typeof CKEDITOR != 'undefined') {
                            // close any opened CKEditor dialogs
                            if (CKEDITOR.dialog._.currentTop) {
                                CKEDITOR.dialog._.currentTop.destroy();
                            }
                            // destroy any editor instance
                            for (var inst in CKEDITOR.instances) {
                                CKEDITOR.instances[inst].destroy();
                            }
                            // restore z-index
                            CKEDITOR.config.baseFloatZIndex = 1E4;
                        }
                    }
                });
                lb.activate();

                Effect.Fade('loading');
            },
            on404 : function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure : function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        }
    );

    return false;
}

/**
 * Shows info popup for last timesheet records added to model
 *
 * @param {Object} element - trigger element (div or td)
 * @param {string} module - module of model
 * @param {string} controller - controller of model
 * @param {number} id - id of model
 * @param {number} archive - flag if the model is archived
 * @return void
 */
function showTimesheetsInfo(element, module, controller, id, archive) {
    element = $(element);
    var model = '';
    if (module != controller) {
        model = module + '_' + controller.substring(0, controller.length - 1);
    } else {
        model = module.substring(0, module.length - 1);
    }
    var div_id = 'timesheet_time_' + model + '_' + id;
    var td = element.tagName == 'TD' ? element : element.up('td');
    var div = $(td).down('#' + div_id);

    if (!div) {
        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                // alert(t.responseText);
                if (!checkAjaxResponse(t.responseText)) {
                    Effect.Fade('loading');
                    return false;
                }
                div = document.createElement('div');
                div.id = div_id;
                div.style.position = 'absolute';
                div.style.visibility = 'hidden';
                div.style.zIndex = '10';
                div.onmouseover = function() {
                    mcancelclosetime();
                };
                div.onmouseout = function() {
                    mclosetime();
                };
                div.setAttribute('onMouseOver', 'mcancelclosetime()');
                div.setAttribute('onMouseOut', 'mclosetime()');
                div.innerHTML = t.responseText;
                if (td.down('#' + div_id)) {
                    td.down('#' + div_id).remove();
                }
                td.appendChild(div);

                mopen(div);

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        Effect.Center('loading');
        Effect.Appear('loading');

        var url = env.base_url + '?' + env.module_param + '=tasks&' + env.controller_param + '=timesheets&timesheets=ajax_get_last_records_info';
        url += '&real_module=' + module + '&real_controller=' + controller + '&model_id=' + id;
        if (archive && archive != 0) {
            url += '&archive=1';
        }
        new Ajax.Request(url, opt);
    } else {
        mopen(div);
    }
}

/**
 * Closes outlooks options
 *
 * @param {Object} element - element that triggered function
 * @param {bool} clear - if true - clears contenrs
 */
function cancelOutlooksEdition (element, clear) {
    div_id = $('outlook_options');
    div_id.style.visibility = 'hidden';
    div_id.style.display = 'none';
    if (clear) {
        $('outlook_all_options').innerHTML = '';
    }
}

function toggleDocumentsListRelatives(button, container) {
    container = $(container);
    if (container.hasClassName('collapsed')) {
        removeClass(container, 'collapsed');
        addClass(container, 'expanded');
        container.style.maxHeight = '';
        button.src = button.src.replace(/expand1.png/ , 'collapse1.png');
        button.title = i18n['labels']['collapse'];
    } else {
        removeClass(container, 'expanded');
        addClass(container, 'collapsed');
        container.style.maxHeight = '36px';
        button.src = button.src.replace(/collapse1.png/ , 'expand1.png');
        button.title = i18n['labels']['expand'];
    }
}

/**
 * Function to enable data field for selecting a date when a medial document is
 * going to be added
 */
function activateMedialDocumentDateField(element) {
    var row = element.parentNode.parentNode;

    var table = row.parentNode;
    var curent_row_index = row.rowIndex;
    var next_row = table['rows'][curent_row_index+1];

    var input_elements = next_row.getElementsByTagName('input');

    var disable_date_fields = true;

    if (element.checked) {
        next_row.style.display = 'table-row';
        disable_date_fields = false;
    } else {
        next_row.style.display = 'none';
        disable_date_fields = true;
    }

    for (var j=0; j<input_elements.length; j++) {
        input_elements[j].disabled = disable_date_fields;
        if (input_elements[j].type == 'text') {
            input_elements[j].value='  .  .    ';
            if (element.checked) {
                input_elements[j].style.display = '';
            } else {
                input_elements[j].style.display = 'none';
            }
        } else {
            input_elements[j].value='';
        }
        if (element.checked) {
            removeClass(input_elements[j], 'disabled');
        } else {
            addClass(input_elements[j], 'disabled');
        }
    }
}

/**
 * Function to allow dropdown for add medial documents
 */
function allowAddMedialDocument(element) {
    const row = element.closest('tr');

    const table = row.closest('table');
    const curent_row_index = row.rowIndex;
    const next_row = table['rows'][curent_row_index+1];

    const input_elements = next_row.getElementsByTagName('input');

    if (element.options[element.selectedIndex].hasClassName('permitted_medial_add')) {
        next_row.style.display = 'table-row';
        disable_date_fields = false;
    } else {
        next_row.style.display = 'none';
        disable_date_fields = true;
    }

    for (let j=0; j<input_elements.length; j++) {
        input_elements[j].style.display = disable_date_fields ? 'none' : '';
        input_elements[j].disabled = disable_date_fields;
        input_elements[j].checked = false;
        activateMedialDocumentDateField(input_elements[j]);
    }
}

/**
 * Function to check if the applied email is unique
 */
function checkUniqueEmail(form) {
    // var group_table_id = 'table_assign';

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (t.responseText > 0) {
                var confirm_msg = env.action_name == 'add' ? 'confirm_add_user_with_same_email' : 'confirm_edit_user_with_same_email';
                if (confirm(i18n['messages'][confirm_msg])) {
                    preventResubmit(form);
                    form.submit();
                    Effect.Fade('loading');
                    return true;
                } else {
                    Effect.Fade('loading');
                    return true;
                }
            } else {
                Effect.Fade('loading');
                form.submit();
                return true;
            }
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    var url = env.base_url + '?' + env.module_param + '=users&users=ajax_check_unique_email';

    new Ajax.Request(url, opt);
}

/**
 * Function to toggle between formula and value field
 *
 * @param id - field id
 * @param set - what has to be shown
 */
function setFormulaValue(id, set) {

    // get the value of the field
    var value = $(id);
    if (value.className.match(/date(time)?box/)) {
        if (id.match(/^(.*)(_\d+)$/)) {
            value = id.replace(/^(.*)(_\d+)$/, '$1_formatted$2');
        } else {
            value = id + '_formatted';
        }
        value = $(value);
    }
    // show the other field
    if (id.match(/^(.*)(_\d+)$/)) {
        var formula = id.replace(/^(.*)(_\d+)$/, '$1_formula$2');
        var img1 = id.replace(/^(.*)(_\d+)$/, '$1_switchValue$2');
        var img2 = id.replace(/^(.*)(_\d+)$/, '$1_switchFormula$2');
    } else {
        var formula = id + '_formula';
        var img1 = id + '_switchValue';
        var img2 = id + '_switchFormula';
    }
    formula = $(formula);
    img1 = $(img1);
    img2 = $(img2);
    if (set == 'Value') {
        // show value field
        value.style.display = '';
        formula.style.display = 'none';
        $('change_formula_' + id).value = id;
        img1.style.display = 'none';
        img2.style.display = '';
    } else {
        // show formula field
        value.style.display = 'none';
        formula.style.display = '';
        $('change_formula_' + id).value = formula.id;
        img1.style.display = '';
        img2.style.display = 'none';
    }

}

/**
 * Function to change between formula and date for indexes
 */
function setIndexFormula(id, set) {

    if (id.match(/^(.*)(_\d+)$/)) {
        var formula = id.replace(/^(.*)(_\d+)$/, '$1_formula$2');
        var date = id.replace(/^(.*)(_\d+)$/, '$1_date_formatted$2');
    } else {
        var formula = id + '_formula';
        var date = id + '_date_formatted';
    }
    formula = $(formula);
    date = $(date);
    if (set == 'Value') {
        date.style.display = '';
        formula.style.display = 'none';
        $('change_formula_' + id).value = date.id.replace(/_formatted/, '');
        $(id.replace(/_index/, '_index_switchValue')).style.display = 'none';
        $(id.replace(/_index/, '_index_switchFormula')).style.display = '';
    } else {
        date.style.display = 'none';
        formula.style.display = '';
        $('change_formula_' + id).value = formula.id;
        $(id.replace(/_index/, '_index_switchValue')).style.display = '';
        $(id.replace(/_index/, '_index_switchFormula')).style.display = 'none';
    }

}

/**
 * Calculates and displays distributed and remaining percentage of items for
 * nomenclature or nomenclature type.
 *
 * @param element - input element
 */
function recalculateNomItemsPercentage(element) {
    var pow = Math.pow(10, env.precision.finance_analysis_percentage);
    var all_percentage = 100;
    var percentage = 0;
    var i = 1;

    var element_id = element.id.replace(/_[0-9]+$/, '');
    var item_id = '';
    while ($(element_id + '_' + i)) {
        item_id = element_id.replace(/percentage/, 'items');
        if ($(element_id + '_' + i).value && !$(element_id + '_' + i).disabled &&
        !$(item_id + '_' + i)[$(item_id + '_' + i).selectedIndex].disabled) {
            percentage += Math.round(parseFloat($(element_id + '_' + i).value) * pow)/pow;
        }
        i++;
    }
    percentage = Math.round(percentage * pow)/pow;
    if (isNaN(percentage)) {
        percentage = 0;
    }

    var distributed_percentage_id = 'distributed_' + element_id;
    var remaining_percentage_id = 'remaining_' + element_id;

    $(distributed_percentage_id).innerHTML = percentage;
    var remaining_percentage = Math.round((all_percentage - percentage) * pow)/pow;
    $(remaining_percentage_id).innerHTML = remaining_percentage;

    removeClass($(remaining_percentage_id), (!remaining_percentage ? 'red' : 'green'));
    addClass($(remaining_percentage_id), (!remaining_percentage ? 'green' : 'red'));
    removeClass($(distributed_percentage_id), (!remaining_percentage ? 'red' : 'green'));
    addClass($(distributed_percentage_id), (!remaining_percentage ? 'green' : 'red'));
}

/**
 * Checks if total percentage has been distributed by income and expense items
 * for nomenclature or nomenclature type.
 */
function checkNomItemsPercentage() {
    var items_income_valid = validateNomItems($('items_income_1'));
    var items_expense_valid = validateNomItems($('items_expense_1'));
    var percentage_income_valid = validateNomPercentage($('percentage_income_1'));
    var percentage_expense_valid = validateNomPercentage($('percentage_expense_1'));

    if (items_income_valid && items_expense_valid && percentage_income_valid && percentage_expense_valid) {
        return true;
    } else {
        return false;
    }
}

/**
 * Validates income/expense items for nomenclature or nomenclature type.
 *
 * @param element - input element
 */
function validateNomItems(element) {
    var element_id = element.id.replace(/_[0-9]+$/, '');
    var items_ids = Array();
    var all_valid = true;
    var i = 1;

    while ($(element_id + '_' + i)) {
        if ($(element_id + '_' + i).value) {
            if (!$(element_id + '_' + i).disabled) {
                if (!$(element_id + '_' + i)[$(element_id + '_' + i).selectedIndex].disabled) {
                    if (items_ids.indexOf($(element_id + '_' + i).value) != -1) {
                        // item can only be selected once
                        $(element_id + '_' + i).parentNode.parentNode.className = 'attention';
                        all_valid = false;
                    } else {
                        items_ids.push($(element_id + '_' + i).value);
                        $(element_id + '_' + i).parentNode.parentNode.className = '';
                    }
                } else {
                    // selected option of item is disabled
                    $(element_id + '_' + i).parentNode.parentNode.className = 'attention';
                    all_valid = false;
                }
            }
        } else {
            // no item is selected
            $(element_id + '_' + i).parentNode.parentNode.className = 'attention';
            all_valid = false;
        }
        i++;
    }

    return all_valid;
}

/**
 * Validates income/expense percentage values for nomenclature or nomenclature
 * type.
 *
 * @param element - input element
 */
function validateNomPercentage(element) {
    var pow = Math.pow(10, env.precision.finance_analysis_percentage);
    var all_percentage = 100;
    var item_percentage = 0;
    var percentage = 0;
    var i = 1;
    var negative_values = false;
    var element_id = element.id.replace(/_[0-9]+$/, '');

    recalculateNomItemsPercentage(element);

    while ($(element_id + '_' + i)) {
        if (!$(element_id + '_' + i).disabled) {
            item_percentage = Math.round(parseFloat($(element_id + '_' + i).value) * pow)/pow;
            percentage += item_percentage;
            if (item_percentage < 0) {
                negative_values = true;
            }
        }
        i++;
    }
    percentage = Math.round(percentage * pow)/pow;

    return (!negative_values && all_percentage == percentage);
}

/**
 * Gets the value of radio button
 *
 * @param radio - set of radio objects
 */
function getRadioValue(radio) {
    if (!radio) {
        return '';
    }
    var radioLength = radio.length;
    if (radioLength == undefined) {
        if (radio.checked) {
            return radio.value;
        } else {
            return '';
        }
    }
    for (var i = 0; i < radioLength; i++) {
        if (radio[i].checked) {
            return radio[i].value;
        }
    }
    return '';
}

/**
 * Sets the value of radio button
 *
 * @param radio - set of radio objects
 * @param value - the value of the radio we want to check
 */
function setRadioValue(radio, value) {
    if (!radio) {
        return;
    }
    var radioLength = radio.length;
    if (radioLength == undefined) {
        radio.checked = (radio.value == value.toString());
        return;
    }
    for (var i = 0; i < radioLength; i++) {
        radio[i].checked = false;
        if (radio[i].value == value.toString()) {
            radio[i].checked = true;
        }
    }
}

/**
 * Function designed to prepare the options for additional variable
 * button and to redirect to the selected url
 *
 * @param element - the element which calls the function
 * @param params - object with button settings
 */
function redirectToButtonUrl(element, params) {
    // start preparing the url
    var redirect_url = params.base_url;

    var parameters = '';
    if (element.form) {
        parameters = Form.serialize(element.form);
    }
    // define context - if current window is popup/iframe, execute 'open' on main window
    var context = params && params.context ? params.context : (window.opener && window.name.match(/^pop\d+$/)  ? window.opener : (window.top || window));

    // Show the loading effect
    Effect.Center('loading');
    Effect.Appear('loading');

    // Prepare the options used to show the lightbox data through AJAX
    if (params.target == 'lightbox') {
        var opt_lb = {
            method: 'get',
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var result = t.responseText;
                if (String.prototype.isJSON.call(result)) {
                    result = String.prototype.evalJSON.call(result);
                } else {
                    result = {
                        content: result
                    };
                }

                if (result.errors) {
                    displayNotificationFixed(result.errors);
                }

                var template = result.content;
                if (typeof result.title === 'string') {
                    params.title = result.title;
                }

                // no access to module or another error
                if (template.match(/<html/i)) {
                    var matches = template.match(/^.*<div\s+class="message_container">([\s\S]*?)<\/div>.*$/mi);
                    template = matches && matches.length ? matches[1] : i18n['messages']['error_no_access_to_module'];
                }

                var lb_width, lb_height;
                // check for a custom width or set a default one
                if (params.lightbox_width) {
                    if (params.lightbox_width > 0) {
                        lb_width = params.lightbox_width + 'px';
                    } else {
                        lb_width = params.lightbox_width;
                    }
                } else {
                    lb_width = '820px';
                }

                // check for a custom height or set a default one
                if (params.lightbox_height) {
                    if (params.lightbox_height > 0) {
                        lb_height = params.lightbox_height + 'px';
                    } else {
                        lb_height = params.lightbox_height;
                    }
                } else {
                    lb_height = '580px';
                }

                // build the lightbox
                lb = new lightbox({
                    content: template,
                    title: params.title ? (params.title == 'button' ? element.innerHTML : params.title) : '',
                    width: lb_width,
                    height: lb_height,
                    backgroundColor: params.lightbox_background_color || '',
                    origin: $(element)
                });

                lb.activate();
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };
    }

    if (params.vars_data) {
        // params vars data is json encoded array
        //so the params will be filled with values in php
        parameters += '&button_params=' + params.vars_data;

        // set the id in the post array
        if (params.current_model_id) {
            parameters += '&current_model_id=' + params.current_model_id;
        }

        // prepare ajax options
        var opt = {
            method: 'post',
            parameters: parameters,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                // the returned link is added to the redirect link and JS redirect to it
                redirect_url = redirect_url + t.responseText;

                if (params.target == 'lightbox') {
                    new Ajax.Request(redirect_url + '&prepare_data_for_lightbox=1&use_ajax=1', opt_lb);
                } else {
                    if (params.target == '_self') {
                        context.closeAllPopupsAndRedirect(redirect_url);
                    } else {
                        context.open(redirect_url, params.target);
                    }
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=';
        // prepare routing params from function params or from environment (current page route) by default
        if (params.module) {
            url += params.module +
                (typeof params.controller != 'undefined' && params.module != params.controller ?
                '&' + env.controller_param + '=' + params.controller + '&' + params.controller :
                '&' + params.module);
        } else {
            url += env.module_name +
                (env.controller_name != env.module_name ? '&' + env.controller_param + '=' + env.controller_name : '') +
                '&' + env.action_param;
        }
        url += '=button_link_prepare&use_ajax=1';
        new Ajax.Request(url, opt);
    } else if (redirect_url && params.target) {
        // no defined specific parameters so it seems that this is only a link button (it contains link and target)
        if (params.target == 'lightbox') {
            new Ajax.Request(redirect_url + '&prepare_data_for_lightbox=1&use_ajax=1', opt_lb);
        } else {
            if (params.target == '_self') {
                context.closeAllPopupsAndRedirect(redirect_url);
            } else {
                context.open(redirect_url, params.target);
            }
        }
        Effect.Fade('loading');
    } else {
        // the required arguments are missing so no action is going to be taken
        Effect.Fade('loading');
        return false;
    }
}

/**
 * Function to show map, depending on the selected parameters
 *
 * @param element         - the element which calls the function
 * @object params         - object with map settings
 * @string var_label      - label for the map
 * @string field_name_row - name of the var (with the row num)
 */
function showMap(element, params, var_label, field_name_row) {
    // start preparing the url
    // var redirect_url = params.base_url;

    Effect.Center('loading');
    Effect.Appear('loading');

    if (params) {
        var parameters = '';

        // get form of the element
        if (element.form) {
            parameters = Form.serialize(element.form);
        }

        // params vars data is json encoded array
        //so the params will be filled with values in php
        parameters += '&map_params=' + params.vars_data;

        // set the id in the post array
        if (params.current_model_id) {
            parameters += '&current_model_id=' + params.current_model_id;
        }

        // tries to find out if the var is in bb variable
        var bb_row = '';
        var parent_element = element.parentNode;
        while (!bb_row && parent_element && !parent_element.id.match(/^layout_[0-9]*_box$/)) {
            if (parent_element.id.match(/^row_[0-9]*_box$/)) {
                bb_row = parent_element.id.replace(/^row_([0-9]*)_box$/, '$1');
            } else if (parent_element.id.match(/^bb_row_[0-9]*$/)) {
                bb_row = parent_element.id.replace(/^bb_row_([0-9]*)$/, '$1');
            } else {
                parent_element = parent_element.parentNode;
            }
        }

        // set the row (if the var is in a grouping table)
        if (field_name_row) {
            row = field_name_row.replace(/^.*\[([0-9]*)\]$/, '$1');
            parameters += '&row=' + row;
        }

        // set the BB row (if the var is in a BB var)
        if (bb_row) {
            parameters += '&bb_contain_row=' + bb_row;
        }

        // prepare ajax options
        var opt = {
            method: 'post',
            parameters: parameters,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                // a link is returned
                var redirect_url = t.responseText;

                if (params.target == 'lightbox') {
                    if (params.static) {
                        // STATIC MAP
                        // builds an img element with the link as src
                        img_element = '<img src="' + redirect_url + '" width="' + params.width + '" height="' + params.height + '" alt="" />';

                        //show layer in a lightbox
                        lb = new lightbox({content: img_element, title: var_label, width: parseInt(params.width), height: parseInt(params.height)});
                        lb.activate();
                    } else {
                        // DYNAMIC MAP
                        // TODO activate dynamic map in lightbox


                    }
                } else {
                    // opens a static map in a target window
                    window.open(redirect_url, params.target);
                }
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        // ajax request to define the url for the static map
        var url = env.base_url + '?' + env.module_param + '=' + env.module_name;
        if (env.controller_name != env.module_name) {
            url += '&' + env.controller_param + '=' + env.controller_name;
        }
        url += '&' + env.action_param + '=prepare_map&use_ajax=1';
        new Ajax.Request(url, opt);

    } else {
        // the required arguments are missing so no action is going to be taken
        Effect.Fade('loading');
        return false;
    }
}

/**
 * Updates available_quantity field into GT2
 * if the field is visible and the office has been changed
 */
function updateAvailableQuantities(warehouse) {
    var fields = $$('.grouping_table2 .available_quantity');
    var articles = '';
    for (var i = 0; i < fields.length; i++) {
        if (fields[i].style.display == 'none' || fields[i].parentNode.style.display == 'none') {
            //the column is hidden
            return true;
        }
        var row = fields[i].id.replace(/^available_quantity_(\d+)$/, '$1');
        if (!$('article_id_' + row).value) {
            //this row has not article chosen
            continue;
        } else {
            if (warehouse && warehouse != 'office_' && !warehouse.match(/^\d*__/)) {
                articles += '&nom_id[]=' + $('article_id_' + row).value;
            } else {
                fields[i].value = '';
            }
        }
    }

    if (!articles) {
        return;
    }
    Effect.Center('loading');
    Effect.Appear('loading');
    var opt = {
        method: 'post',
        asynchronous: false,
        parameters: 'get_multiple=1' + articles,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            var result = t.responseText;
            if (result && result.match(/^({|\[)/)) {
                eval('result = ' + result + ';');
            } else {
                result = new Object();
            }
            for (var i = 0; i < fields.length; i++) {
                var row = fields[i].id.replace(/^available_quantity_(\d+)$/, '$1');
                if (!$('article_id_' + row).value) {
                    //this row has not article chosen
                    $('available_quantity_' + row).value = '';
                } else if (result[$('article_id_' + row).value] && result[$('article_id_' + row).value].quantity) {
                    $('available_quantity_' + row).value = result[$('article_id_' + row).value].quantity;
                } else {
                    $('available_quantity_' + row).value = parseFloat('0').toFixed(env.precision.gt2_quantity);
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=warehouses&';
    url += 'warehouses=ajax_get_quantity&ajax_get_quantity=' + warehouse;
    new Ajax.Request(url, opt);
}

/**
 * Function to get some info from a file path
 * This function should work as JavaScript alternative of the PHP function pathinfo()
 *
 * @param string path - path to a file
 * @return object    - the extracted information
 */
// TODO: get extension and etc.
function pathinfo(path) {
    // Prepare the pathinfo var
    var pathinfo = new Object;

    // Get the name of the file
    pathinfo['filename'] = path.replace(/^(.*)\:\\fakepath\\/, '');

    return pathinfo;
}

function toggleCheckboxesByClass(className, state) {
    var boxes = $$('.' + className);
    for(var i = 0; i < boxes.length; i++) {
        boxes[i].checked = state;
    }
}

/**
 * Convert minutes to days, hours, minutes representation (optionally do not convert to days)
 *
 * @param int minutes - number of minutes
 * @param bool get_days - whether to convert to days or not
 * @return string - formatted representation of time (hours, minutes or days, hours or just minutes)
 */
function minutes2Human(minutes, get_days) {
    var days = 0, hours = 0, sign = '';
    minutes = parseInt(minutes);
    if (minutes) {
        if (minutes < 0) {
            sign = '-';
            minutes = Math.abs(minutes);
        }
        if (get_days) {
            days = Math.floor(minutes/1440);
            minutes = minutes%1440;
        }
        hours = Math.floor(minutes/60);
        minutes = minutes%60;
    }
    var minutes_formatted =
        (days ? (sign + days + ' ' + i18n['labels']['day' + (days == 1 ? '' : 's')] + ', ') : '') +
        (days || hours ? ((hours ? sign : '') + hours + ' ' + i18n['labels']['hour' + (hours == 1 ? '' : 's')] + (!days ? ', ' : '')) : '') +
        (!days ? (minutes ? sign : '') + minutes + ' ' + i18n['labels']['minute' + (minutes == 1 ? '' : 's')] : '');

    return minutes_formatted;
}

/**
 * function made for escaping regexp characters so you can build a new RegExp with variables in it ps.: like in inCode function fline: 3
 *
 * @param string str - variable to be escaped and prepared for regex.
 * @return string - the escaped string
 */
function escapeRegExp(str) {
    return str.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g, "\\$&");
}

function uniqid(prefix, more_entropy) {
    //  discuss at: http://phpjs.org/functions/uniqid/
    // original by: Kevin van Zonneveld (http://kevin.vanzonneveld.net)
    //  revised by: Kankrelune (http://www.webfaktory.info/)
    //        test: skip
    //   example 1: uniqid();
    //   returns 1: 'a30285b160c14'
    //   example 2: uniqid('foo');
    //   returns 2: 'fooa30285b1cd361'
    //   example 3: uniqid('bar', true);
    //   returns 3: 'bara20285b23dfd1.31879087'

    if (typeof prefix === 'undefined') {
      prefix = '';
    }

    var retId;
    var formatSeed = function(seed, reqWidth) {
        seed = parseInt(seed, 10).toString(16); // to hex str
        if (reqWidth < seed.length) { // so long we split
            return seed.slice(seed.length - reqWidth);
        }
        if (reqWidth > seed.length) { // so short we pad
            return Array(1 + (reqWidth - seed.length)).join('0') + seed;
        }
        return seed;
    };

    if (!this.uniqidSeed) { // init seed with big random int
        this.uniqidSeed = Math.floor(Math.random() * 0x75bcd15);
    }
    this.uniqidSeed++;

    retId = prefix; // start with prefix, add current milliseconds hex string
    retId += formatSeed(parseInt(new Date().getTime() / 1000, 10), 8);
    retId += formatSeed(this.uniqidSeed, 5); // add seed hex string
    if (more_entropy) {
        // for more entropy we add a float lower to 10
        retId += (Math.random() * 10).toFixed(8).toString();
    }

    return retId;
}

/**
 * Toggles element to readonly or not readonly
 * For radio buttons:
 *    when readonly flag is true the radios are disabled and a hidden input added
 *    when readonly flag is false the radios are enabled and the hidden input is removed
 * For checkboxes:
 *    when readonly flag is true add onclick function deselectCheckboxes
 *    when readonly flag is false remove onclick function deselectCheckboxes
 * For dropdowns:
 *    when readonly flag is true the select is disabled and a hidden input added
 *    when readonly flag is false the select is enabled and the hidden input is removed
 * For date/datetime/time use #<variable_name> or #<variable_name>_formatted (both should work)
 *
 * @param mixed target - string of CSS selectors e.g. '.classname, #variable_id, #radiobutton_1'
 *                     - DOM element e.g. $('varname1')
 *                     - variable name e.g. 'varname1'
 *                      In this case the function automatically searches for
 *                      = the corresponding id of a radio/checkbox and its siblings
 *                      = the corresponding id of a date/date_formatted fields
 *                      = the corresponding id of a datetime/datetime_formatted fields
 *                      = the corresponding id of a time/time_formatted fields
 *                     - array of DOM elements [$('varname1'), $('varname2')]
 *                     - array of variable names ['varname1', 'varname2']
 *                     - array of selectors ['.classname', '#variable_id', '#radiobutton_1']
 * @params bool readonly - true if the element should be made readonly, false otherwise
 */
function toggleReadonly(target, readonly) {
    if (Object.isString(target)) {
        //CSS selectors passed as string
        toggleReadonly($$(target), readonly);
    } else if (Object.isArray(target)) {
        //array of selectors (or variable names)
        target.each(function(trg) {
            if (Object.isElement(trg)) {
                //DOM element
                toggleReadonly(trg, readonly);
            } else if (Object.isString(trg)) {
                if (Object.isElement($(trg))) {
                    //variable name
                    toggleReadonly($(trg), readonly);
                } else if (trg.match(/[\.\,\ ]/i)) {
                    //selector (classname, list, etc.)
                    toggleReadonly($$(trg), readonly);
                } else {
                    //variable name (but that of a radio button, checkbox, group variable, etc)
                    ['_1', '_1_1', '_readonly_1', '_1_readonly_1'].each(function(suffix){
                        if (Object.isElement($(trg + suffix))) {
                            toggleReadonly($(trg + suffix), readonly);
                            throw $break;
                        }
                    });
                }
            }
        });
    } else if (Object.isElement($(target))){
        target = $(target);
        if (target.readOnly == readonly && !target.type.match(/radio|checkbox/i) && !target.tagName.match(/select/i)) {
            //ALREADY TOGGLED! :)
            return;
        }

        if (!target.type.match(/radio|checkbox/i)) {
            //set readonly attribute and add/remove classname readonly
            //to all elements but radios and checkboxes
            target.readOnly = readonly;
            if (readonly) {
                addClass(target, 'readonly');
            } else {
                removeClass(target, 'readonly');
            }
        }

        if (target.tagName.match(/select/i) || target.hasClassName('selbox_hidden')) {
            //toggle dropdowns

            //IMPORTANT: the tricky part here is that toggleReadonly function is supposed to be universal
            //           the selector might be either #<name> or #<name>_readonly
            //           the function should always manage both of the cases
            var original_name = target.name.replace(/_readonly/g, "");
            var readonly_name = original_name.replace(/([^\[\]]+)(\[[0-9]*\])?/, "$1_readonly$2");
            var original_id = target.id.replace(/_readonly/g, "");
            var readonly_id = original_id.replace(/(_[0-9]*)?$/, "_readonly$1");

            if (readonly) {
                //the dropdown should have readonly name:
                // name: <name>_readonly or name_readonly[<index>] (for group variables)
                // id:   <name>_readonly or name_readonly_<index> (for group variables)
                // disabled: true
                // readonly: true
                // classname: +readonly

                //the hidden input should have original name:
                //  name: <name> or name[<index>] (for group variables)
                //  id:   <name> or name_<index> (for group variables)
                // readonly: true
                // classname: selbox_hidden - this is IMPORTANT

                //check if there is already a hidden input with the original id
                if ($(original_id) && $(original_id).tagName.match(/select/i)) {
                    var selbox = $(original_id);

                    //set the value of the selected option in a hidden field
                    var hidden = document.createElement('input');
                    hidden.type = 'hidden';
                    hidden.id = original_id;
                    hidden.name = original_name;
                    hidden.value = selbox.getValue();
                    hidden.readOnly = true;
                    //this is important because the function should recognize the hidden input related to the select
                    hidden.className = 'selbox_hidden';
                    // add custom classes
                    selbox.classNames().each(function(cn) { if (cn != 'selbox') { hidden.addClassName(cn) } });

                    selbox.name = readonly_name;
                    selbox.id = readonly_id;
                    selbox.disabled = true;
                    addClass(selbox, 'readonly');

                    if (!$(hidden.id)) {
                        //append the input just when the id of the select is changed
                        target.parentNode.appendChild(hidden);
                    }

                    toggleUndefined(selbox);
                }
            } else {
                //the dropdown should have original name:
                //  name: <name> or name[<index>] (for group variables)
                //  id:   <name> or name_<index> (for group variables)

                //the hidden input should be removed!

                var hidden = $(original_id);
                var selbox = $(readonly_id);
                if (hidden && hidden.tagName.match(/input/i)) {
                    //remove the hidden element
                    target.parentNode.removeChild(hidden);
                }
                if (!selbox) {
                    selbox = $(original_id);
                }

                if (selbox && selbox.tagName.match(/select/i)) {
                    removeClass(selbox, 'readonly');
                    selbox.name = original_name;
                    selbox.id = original_id;
                    selbox.disabled = false;
                }

                toggleUndefined(selbox);
            }
        } else if (target.hasClassName('autocompletebox')) {
            //display/hide the autocompleter buttons
            if (target.parentNode.select('.combobox_button').length > 0) {
                //toggle display of combobox arrow button
                target.parentNode.select('.combobox_button')[0].style.display = (readonly) ? 'none' : '';
            }
            target.parentNode.select('.icon_button').each(function (button) {
                button.parentNode.style.display = (readonly) ? 'none' : '';
                button.style.display = (readonly) ? 'none' : '';
                //recalculate the width of the autocompleter
                var sign = (readonly) ? 1 : -1;
                var w = parseInt(target.style.width.replace(/px/g, '')) + sign * 22;
                target.style.width = w + 'px';
            });
        } else if (target.className.match(/(date|time|datetime)box/)) {
            //IMPORTANT: the tricky part here is that toggleReadonly function is supposed to be universal
            //           the selector might be either #<name> or #<name>_formatted
            //           the function should always manage both of the cases
            var original_id = target.id.replace(/_readonly/g, "");
            var formatted_id = original_id.replace(/(_[0-9]*)?$/, "_readonly$1");

            //toggle the visible date/time input
            var target_formatted = $(formatted_id);
            if (target_formatted) {
                target_formatted.readOnly = readonly;
                if (readonly) {
                    addClass(target_formatted, 'readonly');
                } else {
                    removeClass(target_formatted, 'readonly');
                }

            }
        } else if (target.type.match(/radio/i) || target.hasClassName('radio_hidden')) {
            //get all the radio buttons from the group
            $$('input[name=' + target.name + '], input[name=' + target.name.replace(/_readonly/g, "").replace(/([^\[\]]+)(\[[0-9]*\])?/, "$1_readonly$2") + ']').each(function(trg) {
                //toggle radios

                //IMPORTANT: the tricky part here is that toggleReadonly function is supposed to be universal
                //           the selector might be either #<name> or #<name>_readonly
                //           the function should always manage both of the cases
                var original_name = trg.name.replace(/_readonly/g, "");
                var readonly_name = original_name.replace(/([^\[\]]+)(\[[0-9]*\])?/, "$1_readonly$2");
                var original_id = trg.id.replace(/_readonly/g, "");
                var readonly_id = original_id.replace(/(_[0-9]+)?(_[0-9]+)?$/, "$1_readonly$2");

                if (readonly) {
                    //the radio should have readonly name:
                    // name: <name>_<option_number>_readonly or name_<option_number>_readonly[<index>] (for group variables)
                    // id:   <name>_<option_number>_readonly or name_<option_number>_readonly_<index> (for group variables)
                    // disabled: true
                    // readonly: true
                    // classname: +readonly

                    //the hidden input should have original name:
                    //  name: <name> or name[<index>] (for group variables)
                    //  id:   <name> or name_<index> (for group variables)
                    // readonly: true
                    // classname: radio_hidden - this is IMPORTANT

                    //check if there is already a hidden input with the original id
                    if ($(original_id) && $(original_id).type.match(/radio/i)) {
                        var radio = $(original_id);

                        radio.name = readonly_name;
                        radio.id = readonly_id;
                        radio.disabled = true;

                        //get the label and change its for attribute
                        $$('label[for="' + original_id + '"]').each(function(lbl) {
                            lbl.htmlFor = readonly_id;
                        });

                        if (radio.checked) {
                            //set the value of the selected option in a hidden field
                            var hidden = document.createElement('input');
                            hidden.type = 'hidden';
                            //IMPORTANT: the hidden id is <variable> or <variable>_<row_num> (for group tables)
                            hidden.id = original_id.match(/(.*)_([0-9]+)(_[0-9_]+)/) ?
                                original_id.replace(/(.*)_([0-9]+)(_[0-9_]+)/, "$1$3") :
                                original_id.replace(/(.*)_([0-9]+)/, "$1");
                            hidden.name = original_name;
                            hidden.value = radio.getValue();
                            hidden.readOnly = true;
                            //this is important because the function should recognize the hidden input related to the radio
                            hidden.className = 'radio_hidden';
                            // add custom classes
                            radio.classNames().each(function(cn) { hidden.addClassName(cn) });

                            if (!$(hidden.id)) {
                                //append the input just when the id of the select is changed
                                trg.parentNode.appendChild(hidden);
                            }
                        }
                    } else if ($(original_id) && $(original_id).type.match(/hidden/i)) {
                        // ALREADY TOGGLED!!!
                        // do not continue with the rest of the radios
                        throw $break;
                    }
                } else {
                    //the radio should have original name:
                    //  name: <name> or name[<index>] (for group variables)
                    //  id:   <name> or name_<index> (for group variables)

                    //the hidden input should be removed!

                    //IMPORTANT: the hidden id is <variable> or <variable>_<row_num> (for group tables)
                    var hidden_id = original_id.match(/(.*)_([0-9]+)(_[0-9_]+)/) ?
                        original_id.replace(/(.*)_([0-9]+)(_[0-9_]+)/, "$1$3") :
                        original_id.replace(/(.*)_([0-9]+)/, "$1");
                    var hidden = $(hidden_id);
                    var radio = $(readonly_id);
                    if (hidden && hidden.type.match(/hidden/i)) {
                        //remove the hidden element
                        target.parentNode.removeChild(hidden);
                    }
                    if (!radio) {
                        radio = $(original_id);
                    }

                    if (radio && radio.type.match(/radio/i)) {
                        radio.name = original_name;
                        radio.id = original_id;
                        radio.disabled = false;
                    }

                    //get the label and change its for attribute
                    $$('label[for="' + readonly_id + '"]').each(function(lbl) {
                        lbl.htmlFor = original_id;
                    });
                }
            });
        } else if (target.type.match(/checkbox/i)) {
            //get all the checkboxes buttons from the group
            $$('input[name=' + target.name + ']').each(function(trg) {
                //toggle checkboxes

                if (readonly) {
                    trg.onclick = function() {deselectCheckboxes(this);};
                } else {
                    trg.onclick = null;
                }
            });
        }
    }
}

function redirect(url) {
    var regex = new RegExp('^' + env.base_host);
    if (!url.match(/^(#|\/)/) && !url.match(regex)) {
        return null;
    }

    window.location.href = url;
}
/**
 * @deprecated No need to explicitly go to the login page, just refresh the current page
 */
function redirectToLogin() {
    // url = env.base_host + env.base_url + '?' + env.module_param + '=auth&auth=login';
    // redirect(url);

    // Refresh
    redirect(window.location.href);
}
function redirectToLogout() {
    url = env.base_host + env.base_url + '?' + env.module_param + '=auth&auth=logout';
    redirect(url);
}

const loadJS = src =>
    new Promise((resolve, reject) => {
        const existingScript = document.querySelector(`head > script[src="${src}"]`);
        if (existingScript !== null) { // Script already in DOM
            switch (existingScript.dataset.loaded) {
                case 'started': // Script is in DOM, but not loaded yet
                    existingScript.addEventListener('load', resolve);
                    return;

                case 'done': // Script already loaded
                    resolve();
                    return;

                default: // Script failed loading
                    reject();
                    return;
            }
        }

        // Script not in DOM
        const script = document.createElement("script")
        script.src = src
        script.async = true
        script.dataset.loaded = 'started';
        document.head.appendChild(script)

        // Resolve the promise once the script is loaded
        script.addEventListener('load', (e) => {
            e.target.dataset.loaded = 'done';
            resolve();
        });

        // Reject the promise if there is an error
        script.addEventListener('error', (e) => {
            e.target.dataset.loaded = 'error'
            reject();
        });
    });

const loadCSS = src =>
    new Promise((resolve, reject) => {
        if (document.querySelector(`head > link[href="${src}"]`) !== null) return resolve()
        const link = document.createElement("link")
        link.rel  = "stylesheet"
        link.href = src
        link.async = true
        document.head.appendChild(link)
        link.onload = resolve
        link.onerror = reject
    });
