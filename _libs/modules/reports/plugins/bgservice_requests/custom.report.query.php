<?php
    Class Bgservice_Requests Extends Reports {
        public static $working_day_duration = 0;
        public static $nonworking_days = array();
        public static $nonworking_days_period_start = '';
        public static $nonworking_days_period_end = '';

        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            // include calendar model
            require_once(PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php');

            // report_results
            $report_results = array();

            $records_requests = array();

            $available_types = array(TYPE_REQUEST_CLIENT, TYPE_REQUEST, TYPE_TICKETS);
            if (!empty($filters['show_type']) && in_array($filters['show_type'], $available_types)) {
                $sql = array(
                    'select' => '',
                    'from'   => '',
                    'where'  => '',
                    'group'  => '',
                    'having' => '',
                    'order'  => ''
                );
                $where = array();

                //process statuses filter
                if (!empty($filters['status_doc'])) {
                    $statuses = array();
                    $substatuses = array();
                    foreach ($filters['status_doc'] as $filt_status) {
                        if (empty($filt_status)) {
                            continue;
                        } elseif (preg_match('#^substatus_[0-9]*$#', $filt_status)) {
                            $substatuses[] = preg_replace('#^substatus_([0-9]*)$#', '$1', $filt_status);
                        } else {
                            $statuses[] = $filt_status;
                        }
                    }
                }
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.active=1';
                $sql['order'] = ' ORDER BY d.id' . "\n";
                $status_filters = array();
                if (!empty($statuses)) {
                    $status_filters[] = 'd.status IN ("' . implode('","', $statuses) . '")';
                }
                if (!empty($substatuses)) {
                    $status_filters[] = 'd.substatus IN ("' . implode('","', $substatuses) . '")';
                }
                if (!empty($status_filters)) {
                    $where[] = '(' . implode(' OR ', $status_filters) . ')';
                }

                if (!empty($filters['customer'])) {
                    $where[] = 'd.customer = "' . $filters['customer'] . '"';
                }

                //sql to take the ids of the needed additional vars
                $add_vars_types = array(TYPE_REQUEST, TYPE_REQUEST_CLIENT);
                if (in_array($filters['show_type'], $add_vars_types)) {
                    $sql_for_add_vars = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                                        'WHERE fm.model="Document" AND ((fm.model_type="' . TYPE_REQUEST_CLIENT . '" AND fm.name="' . REQUEST_CLIENT_DESCRIPTION . '") OR (fm.model_type="' . TYPE_REQUEST . '" AND fm.name="' . REQUEST_DESCRIPTION . '"))';
                    $description_ids = $registry['db']->GetCol($sql_for_add_vars);
                }

                //sql to take the information for the requests
                $sql['select'] = 'SELECT d.id as idx, d.id as id, d.full_num, d.custom_num, d.type, d.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, d.deadline, ' . "\n" .
                                 ' CONCAT("|" , GROUP_CONCAT(da.assigned_to SEPARATOR "|"), "|") as assigned_ids, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by_name, di18n.name as name, ' . "\n" .
                                 ' ' . (in_array($filters['show_type'], $add_vars_types) ? 'd_cstm_description.value' : 'di18n.description') . ' as description, "" as history, d.status, d.substatus, ds.name as substatus_name, 0 as total_time, "" as total_time_label, 0 as comments' . "\n";

                $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                 '  ON (di18n.parent_id=d.id AND di18n.lang="' . $model_lang .'")' . "\n" .
                                 (in_array($filters['show_type'], $add_vars_types) ?
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_description' . "\n" .
                                     '  ON (d_cstm_description.model_id=d.id AND d_cstm_description.var_id IN ("' . implode('","', $description_ids) . '"))' . "\n" : '') .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                 '  ON (ci18n.parent_id=d.customer AND ci18n.lang="' . $model_lang .'")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS . ' AS da' . "\n" .
                                 '  ON (da.parent_id=d.id AND da.assignments_type="' . PH_ASSIGNMENTS_OWNER . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                                 '  ON (d.added_by=ui18n.parent_id AND ui18n.lang="' . $model_lang . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                 '  ON (ds.id=d.substatus AND ds.doc_type=d.type AND ds.lang="' . $model_lang .'")' . "\n";

                $sql['group'] = ' GROUP BY d.id' . "\n";
                $where[] = 'd.type="' . $filters['show_type'] . '"';
                if (!empty($filters['added_by'])) {
                    $where[] = 'd.added_by = "' . $filters['added_by'] . '"';
                }
                if (!empty($filters['executor'])) {
                    $sql['having'] = 'HAVING assigned_ids LIKE("%|' . $filters['executor'] . '|%")';
                }

                $sql['where'] = 'WHERE ' . implode(' AND ', $where);
                $sql = array_filter($sql);
                $requests_query = implode("\n", $sql);
                $records_requests = $registry['db']->GetAssoc($requests_query);
            }

            // process requests
            if (!empty($records_requests)) {
                if (empty(self::$working_day_duration)) {
                    // take the working time using the current date
                    $working_time_start = strtotime(sprintf('%s %s:00', date('Y-m-d'), WORKING_TIME_STARTS));
                    $working_time_end = strtotime(sprintf('%s %s:00', date('Y-m-d'), WORKING_TIME_ENDS));
                    self::$working_day_duration = $working_time_end - $working_time_start;
                }

                $older_than = 0;
                // get filter for older requests
                if (!empty($filters['requests_older_than'])) {
                    $older_than = $filters['requests_older_than'] * self::$working_day_duration;
                }

                // get all the users
                $users_names_sql = 'SELECT parent_id, CONCAT(firstname, " ", lastname) as name' . "\n" .
                                   'FROM ' . DB_TABLE_USERS_I18N . "\n" .
                                   'WHERE lang="' . $model_lang . '"';
                $users_names = $registry['db']->GetAssoc($users_names_sql);

                // get the comments
                $sql_comments = 'SELECT model_id, COUNT(model_id) FROM ' . DB_TABLE_COMMENTS . ' WHERE model="Document" AND model_id IN (' . implode(',', array_keys($records_requests)) . ') GROUP BY model, model_id';
                $documents_comments = $registry['db']->GetAssoc($sql_comments);

                foreach ($documents_comments as $doc_id => $document_comments) {
                    if (isset($records_requests[$doc_id])) {
                        $records_requests[$doc_id]['comments'] = $document_comments;
                    }
                }

                $sql_history = 'SELECT dh.h_id, dh.model_id as doc_id, da.field_name, dh.h_date, da.field_value, da.label' . "\n" .
                               'FROM ' . DB_TABLE_DOCUMENTS_HISTORY . ' AS dh' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_AUDIT . ' AS da' . "\n" .
                               '  ON (da.parent_id=dh.h_id AND da.field_name IN ("status", "substatus") AND dh.action_type NOT LIKE "%minitask")' . "\n" .
                               'WHERE dh.model="Document" AND dh.model_id IN (' . implode(',', array_keys($records_requests)) . ')  AND dh.lang="' . $model_lang . '"' . "\n" .
                               'ORDER BY dh.h_date, da.field_name ASC' . "\n";
                $history_records = $registry['db']->GetAll($sql_history);

                // aplly the history records to the requests
                foreach ($history_records as $history_rec) {
                    if (!empty($records_requests[$history_rec['doc_id']])) {
                        if (!is_array($records_requests[$history_rec['doc_id']]['history'])) {
                            $records_requests[$history_rec['doc_id']]['history'] = array();
                        }

                        // set the history records
                        if ($history_rec['field_name']=='substatus') {
                            if (!isset($records_requests[$history_rec['doc_id']]['history'][$history_rec['h_id']])) {
                                $records_requests[$history_rec['doc_id']]['history'][$history_rec['h_id']] = array(
                                    'date'          => $history_rec['h_date'],
                                    'main_status'   => '',
                                    'substatus'     => '',
                                    'current_label' => '',
                                    'duration'      => 0,
                                    'duration_lbl'  => ''
                                );
                            }
                            $records_requests[$history_rec['doc_id']]['history'][$history_rec['h_id']]['substatus'] = $history_rec['field_value'];
                            $records_requests[$history_rec['doc_id']]['history'][$history_rec['h_id']]['current_label'] = $history_rec['label'];
                        } else {
                            $records_requests[$history_rec['doc_id']]['history'][$history_rec['h_id']] = array(
                                'date'          => $history_rec['h_date'],
                                'main_status'   => $history_rec['field_value'],
                                'substatus'     => '',
                                'current_label' => $registry['translater']->translate('reports_document_' . $history_rec['field_value']),
                                'duration'      => 0,
                                'duration_lbl'  => ''
                            );
                        }
                    }
                }


                // calculate the working time duration
                // and complete assignees
                foreach ($records_requests as $key_doc => $rec_request) {
                    if (is_array($rec_request['history'])) {
                        $history_list = array_values($rec_request['history']);

                        $total_duration = 0;
                        $history_main_status = '';
                        $time_of_previous_event = '';
                        foreach ($history_list as $evnt_key => $history_event) {
                            $current_event_time_seconds = strtotime(General::strftime('%Y-%m-%d %H:%M:00', strtotime($history_event['date'])));
                            $history_list[$evnt_key]['current_status_label'] = sprintf('%s: %s', $history_event['current_label'], General::strftime('%d.%m.%Y, %H:%M', $current_event_time_seconds));

                            if (($evnt_key+1) == count($history_list) && $history_event['main_status']!= 'closed') {
                                // this event calculation
                                // this document is not closed so it is taken the working dates between the date of the previous event and the current date
                                $now_moment_seconds = strtotime(General::strftime('%Y-%m-%d %H:%M:00', time()));
                                list($current_event_duration, $current_event_duration_label) = self::calculateHistoryEventWorkingDuration($registry, $current_event_time_seconds, $now_moment_seconds);

                                $history_list[$evnt_key]['duration'] = $current_event_duration;
                                $history_list[$evnt_key]['duration_lbl'] = $current_event_duration_label;
                                $records_requests[$key_doc]['total_time'] += $current_event_duration;
                            }

                            if (!$history_main_status) {
                                $history_main_status = $history_event['main_status'];
                                $time_of_previous_event = $current_event_time_seconds;
                                continue;
                            }

                            list($previous_event_duration, $duration_label) = self::calculateHistoryEventWorkingDuration($registry, $time_of_previous_event, $current_event_time_seconds);
                            $time_of_previous_event = $current_event_time_seconds;
                            if ($history_event['main_status']) {
                                $history_main_status = $history_event['main_status'];
                            }

                            // previous event calculation
                            $history_list[$evnt_key-1]['duration'] = $previous_event_duration;
                            $history_list[$evnt_key-1]['duration_lbl'] = $duration_label;
                            $records_requests[$key_doc]['total_time'] += $previous_event_duration;
                        }

                        $records_requests[$key_doc]['history'] = $history_list;
                        $records_requests[$key_doc]['total_time_label'] = self::getDurationLabel($registry, $records_requests[$key_doc]['total_time']);
                    } else {
                        $records_requests[$key_doc]['history'] = array();
                    }

                    if ($older_than) {
                        if ($records_requests[$key_doc]['total_time'] < $older_than) {
                            unset($records_requests[$key_doc]);
                            continue;
                        }
                    }

                    $current_assignees = explode('|', $rec_request['assigned_ids']);
                    $current_assignees = array_filter($current_assignees);
                    $current_assignees = array_unique($current_assignees);

                    foreach ($current_assignees as $assign) {
                        if (!isset($records_requests[$key_doc]['assigned'])) {
                            $records_requests[$key_doc]['assigned'] = array();
                        }
                        $records_requests[$key_doc]['assigned'][] = $users_names[$assign];
                    }

                    unset($records_requests[$key_doc]['assigned_ids']);

                    if ($rec_request['type'] == TYPE_REQUEST) {
                        $records_requests[$key_doc]['bugzilla_link'] = sprintf('%s%d', BUGZILLA_BASE_LINK, $records_requests[$key_doc]['custom_num']);
                    }
                }
            }

            $records_requests['additional_options']['show_type'] = (!empty($filters['show_type']) ? $filters['show_type'] : TYPE_REQUEST_CLIENT);
            $sql = 'SELECT `name`, `name_plural` FROM ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' WHERE `parent_id`="' . $filters['show_type'] . '" AND `lang`="' . $model_lang . '"';
            $document_labels = $registry['db']->GetRow($sql);
            $records_requests['additional_options']['table_title'] = $document_labels['name_plural'];
            $records_requests['additional_options']['table_title'] = (!empty($records_requests['additional_options']['table_title']) ? $records_requests['additional_options']['table_title'] : $registry['translater']->translate('reports_requests'));
            $records_requests['additional_options']['hide_deadline_column'] = ((defined('HIDE_DEADLINE_COLUMN') && HIDE_DEADLINE_COLUMN) ? 1 : 0);
            $records_requests['additional_options']['total_colspan'] = (in_array($records_requests['additional_options']['show_type'], array(TYPE_REQUEST_CLIENT, TYPE_TICKETS)) ? 9 : 10) - $records_requests['additional_options']['hide_deadline_column'];
            $records_requests['additional_options']['num_column_label'] = sprintf('%s %s', $document_labels['name'], $registry['translater']->translate('num')) . ($records_requests['additional_options']['show_type'] != TYPE_REQUEST_CLIENT ? sprintf(' / %s', $registry['translater']->translate('reports_request_bug')) : '');

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($records_requests, 0);
            } else {
                $results = $records_requests;
            }

            return $results;
        }

        /*
         * Calculate history event working duration based on the start point of the event and the end point of it
         */
        public static function calculateHistoryEventWorkingDuration(&$registry, $event_start, $event_end) {
            // Default duration is zero
            $duration = 0;

            $working_day_duration = self::$working_day_duration;

            // Convert the start and the end date time into Unix timestamp (i.e. into seconds)
            $start_date_time = $event_start;
            $end_date_time   = $event_end;

            // CALCULATION OF THE PREVIOUS EVENT DURATION
            $start_date = General::strftime('%Y-%m-%d', $event_start);
            $end_date = General::strftime('%Y-%m-%d', $event_end);

            // If the time of the start date is before the first posible working time
            // (for example, if the time of the start date is 07:00, and the first posible working time is 09:00)
            $start_date_first_working_time = strtotime(sprintf('%s %s:00', $start_date, WORKING_TIME_STARTS));
            if ($start_date_time < $start_date_first_working_time) {
                // Set the time of the start date to be the first posible working time
                $start_date_time = $start_date_first_working_time;
            }

            // If the time of the end date is after the last posible working time
            // (for example, if the time of the end date is 20:00, and the last posible working time is 18:00)
            $end_date_first_working_time = strtotime(sprintf('%s %s:00', $end_date, WORKING_TIME_ENDS));
            if ($end_date_time > $end_date_first_working_time) {
                // Set the time of the end date to be the last posible working time
                $end_date_time = $end_date_first_working_time;
            }

            // Basic check: the start date should be before the end date
            if ($start_date_time < $end_date_time) {
                // Get the count of the working days into the current period
                $working_days_count = Calendars_Calendar::getWorkingDays($registry, $start_date, $end_date);

                // If there are any working days into the period
                if ($working_days_count > 0) {
                    $get_non_working_days = false;
                    if (empty(self::$nonworking_days_period_start) || empty(self::$nonworking_days_period_end)) {
                        self::$nonworking_days_period_start = $start_date;
                        self::$nonworking_days_period_end = $end_date;
                        $get_non_working_days = true;
                    } else {
                        $current_dates = array(self::$nonworking_days_period_start, self::$nonworking_days_period_end, $start_date, $end_date);
                        sort($current_dates);
                        if ($current_dates[0]!=self::$nonworking_days_period_start) {
                            self::$nonworking_days_period_start = $current_dates[0];
                            $get_non_working_days = true;
                        }
                        if ($current_dates[3]!=self::$nonworking_days_period_end) {
                            self::$nonworking_days_period_end = $current_dates[3];
                            $get_non_working_days = true;
                        }
                    }

                    if ($get_non_working_days) {
                        $country_code = Calendars_Calendar::getCountryCode($registry);
                        // Get the nonworking dates for the current period
                        $query = 'SELECT `date`' . "\n" .
                            '  FROM `' . DB_TABLE_COUNTRY_NONWORKDAYS . '`' . "\n" .
                            '  WHERE `date`    >= \'' . self::$nonworking_days_period_start . '\'' . "\n" .
                            '    AND `date`    <= \'' . self::$nonworking_days_period_end . '\' ' . "\n" .
                            '    AND `country` = \'' . $country_code . '\'';
                        self::$nonworking_days = $registry['db']->GetCol($query);
                    }

                    // If the start date is equal to the end date
                    if ($start_date == $end_date) {
                        // The duration can be directly calculated for this date
                        $duration = $end_date_time - $start_date_time;
                    } else {
                        // For now, each day is worked all day
                        $full_days_working_count = $working_days_count;

                        // Start date duration (this will have value only if the start date is a working day)
                        $start_date_duration = 0;
                        // If the start date is a working day
                        if (!in_array($start_date, self::$nonworking_days)) {
                            // Separate the start date from the full day worked days
                            $full_days_working_count--;

                            // Calculate the duration for the start day
                            $start_date_duration = strtotime(sprintf('%s %s:00', $start_date, WORKING_TIME_ENDS)) - $start_date_time;

                            // If the time of the start date is after the time when the working day ends
                            if ($start_date_duration < 0) {
                                // The duration for the start date is zero
                                $start_date_duration = 0;
                            }
                        }

                        // End date duration (this will have value only if the end date is a working day)
                        $end_date_duration = 0;
                        if (!in_array($end_date, self::$nonworking_days)) {
                            // Separate the start date from the full day worked days
                            $full_days_working_count--;

                            // Calculate the duration for the end day
                            $end_date_duration   = $end_date_time - strtotime(sprintf('%s %s:00', $end_date, WORKING_TIME_STARTS));

                            // If the time of the end date is before the time when the working day starts
                            if ($end_date_duration < 0) {
                                // The duration for the end date is zero
                                $end_date_duration = 0;
                            }
                        }

                        // Get the seconds for one working day
                        $working_day_duration = self::$working_day_duration;

                        // Calculate the duration for the full day working days
                        $full_days_working_duration = $full_days_working_count * $working_day_duration;

                        // Calculate the total duration for the period
                        $duration = $full_days_working_duration + $start_date_duration + $end_date_duration;
                    }
                }
            }

            // determine the duration label
            $event_duration_label = self::getDurationLabel($registry, $duration);

            return array($duration, $event_duration_label);
        }

        /*
         * Build the duration label
         */
        public static function getDurationLabel(&$registry, $event_duration) {
            $working_day_duration = self::$working_day_duration;
            $event_duration_days = floor($event_duration/$working_day_duration);
            $event_duration_hours = floor(($event_duration%$working_day_duration)/3600);
            $event_duration_minutes = floor((($event_duration%$working_day_duration)%3600)/60);
            $event_duration_label = sprintf('%d %s, %d %s, %d %s', $event_duration_days, $registry['translater']->translate('reports_days'), $event_duration_hours, $registry['translater']->translate('reports_hours'), $event_duration_minutes, $registry['translater']->translate('reports_minutes'));

            return $event_duration_label;
        }
    }
?>
