<?php
    Class Ckr_Export Extends Reports {
        public static $currency_rates = array('BGN->BGN' => 1);
        public static $report_name = 'ckr_export';

        public static function buildQuery(&$registry, $filters = array()) {
            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (empty($filters['borr']) && empty($filters['cred']) && empty($filters['cucr']) && empty($filters['f18_f22']) && empty($filters['wallet'])) {
                $registry['messages']->setError($registry['translater']->translate('error_reports_at_least_one_filter'));
                if (!empty($filters['paginate'])) {
                    $results = array(array(), 0);
                }
                return $results;
            }
            $current_date_format = General::strftime('%Y%m%d');
            $current_date_format_short = General::strftime('%y%m%d');

            $files_structure = array(
                'borr' => array(
                    'name'       => '',
                    'data_array' => 'customers_to_export',
                    'content'    => '',
                    'set_to_fixed_width' => true,
                    'structure'  => array(
                        'date'         => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'BORR_DATE'
                        ),
                        'code_declare' => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'BORR_BAE'
                        ),
                        'record_type'  => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'BORR_REC'
                        ),
                        'client'       => array(
                            'size'    => 1,
                            'padding' => 'left',
                            'title'   => 'BORR_TYPE'
                        ),
                        'ucn'          => array(
                            'size'    => 17,
                            'padding' => 'left',
                            'title'   => 'BORR_ID'
                        ),
                        'name'         => array(
                            'size'    => 60,
                            'padding' => 'left',
                            'title'   => 'BORR_NAME'
                        ),
                        'address'      => array(
                            'size'    => 60,
                            'padding' => 'left',
                            'title'   => 'BORR_ADR'
                        ),
                        'extra'        => array(
                            'size'    => 60,
                            'padding' => 'left',
                            'title'   => 'BORR_INF'
                        ),
                        'client_status'=> array(
                            'size'    => 5,
                            'padding' => 'left',
                            'title'   => 'BORR_STAT'
                        ),
                        'sector'       => array(
                            'size'    => 4,
                            'padding' => 'left',
                            'title'   => 'BORR_SECT'
                        ),
                        'branch'       => array(
                            'size'    => 5,
                            'padding' => 'left',
                            'title'   => 'BORR_BRAN'
                        )
                    )
                ),
                'cred' => array(
                    'name'       => '',
                    'data_array' => 'contracts_to_export',
                    'content'    => '',
                    'set_to_fixed_width' => true,
                    'structure'  => array(
                        'date'         => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CRED_DATE'
                        ),
                        'code_declare' => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CRED_BAE'
                        ),
                        'record_type'  => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_REC'
                        ),
                        'contract_num' => array(
                            'size'    => 20,
                            'padding' => 'left',
                            'title'   => 'CRED_NUM'
                        ),
                        'ucn'          => array(
                            'size'    => 17,
                            'padding' => 'left',
                            'title'   => 'CRED_BORR'
                        ),
                        'code_ckr'     => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CRED_SPEC'
                        ),
                        'currency'     => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CRED_CURR'
                        ),
                        'original_amount'=> array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CRED_ORG'
                        ),
                        'currency_amount'=> array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CRED_BGL'
                        ),
                        'date_start'   => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CRED_DAT1'
                        ),
                        'date_end'     => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CRED_DAT2'
                        ),
                        'colr'         => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CRED_COLR'
                        ),
                        'colt1'        => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CRED_COLT1'
                        ),
                        'colr1'        => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CRED_COLR1'
                        ),
                        'colt3'        => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CRED_COLT3'
                        ),
                        'colr3'        => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CRED_COLR3'
                        ),
                        'contract_interest_percent'=> array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_INTR_TYPE'
                        ),
                        'base_interest_percent'=> array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_INTR_BASE'
                        ),
                        'starting_interest_percent'=> array(
                            'size'    => 6,
                            'padding' => 'left',
                            'title'   => 'CRED_INTR'
                        ),
                        'extra_period' => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_GRACE_PER'
                        ),
                        'extension_period'=> array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CRED_SH_PAY'
                        ),
                        'reformat_credit_contract'=> array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_PREDOG'
                        ),
                        'reformat_reason'=> array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_PREDOG_REASON'
                        ),
                        'col1'         => array(
                            'size'    => 20,
                            'padding' => 'left',
                            'title'   => 'CRED_PREDOG_OLDNUM'
                        ),
                        'col2'         => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_TYPE112'
                        ),
                        'col3'         => array(
                            'size'    => 13,
                            'padding' => 'left',
                            'title'   => 'CRED_TYPE112_BULSTAT'
                        ),
                        'col4'         => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CRED_REFINANS'
                        ),
                        'col5'         => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CRED_REFINANS_BAE'
                        ),
                        'col6'         => array(
                            'size'    => 20,
                            'padding' => 'left',
                            'title'   => 'CRED_REFINANS_OLDNUM'
                        ),
                        'col7'         => array(
                            'size'    => 600,
                            'padding' => 'left',
                            'title'   => 'CRED_CO_BORR'
                        ),
                        'col8'         => array(
                            'size'    => 200,
                            'padding' => 'left',
                            'title'   => 'CRED_SURETIES'
                        )
                    )
                ),
                'cucr' => array(
                    'name'       => '',
                    'data_array' => 'cucr_data',
                    'content'    => '',
                    'set_to_fixed_width' => true,
                    'structure'  => array(
                        'date'                       => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CUCR_DATE'
                        ),
                        'code_declare'               => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'CUCR_BAE'
                        ),
                        'rec_type'                   => array(
                            'size'    => 2,
                            'padding' => 'left',
                            'title'   => 'CUCR_REC'
                        ),
                        'contract_num'               => array(
                            'size'    => 20,
                            'padding' => 'left',
                            'title'   => 'CUCR_CRED'
                        ),
                        'ucn'                        => array(
                            'size'    => 17,
                            'padding' => 'left',
                            'title'   => 'CUCR_BORR'
                        ),
                        'original_amount'            => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_SUMA'
                        ),
                        'glav_left'                  => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_PRINC'
                        ),
                        'glav_over_left'             => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_PRINC_OVER'
                        ),
                        'pay_left'                   => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_OVER_INTER'
                        ),
                        'dues'                       => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_JUD_DUES'
                        ),
                        'balance'                    => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_COND_BAL'
                        ),
                        'balance_before_calculation' => array(
                            'size'    => 15,
                            'padding' => 'left',
                            'title'   => 'CUCR_TOT_BALANS'
                        ),
                        'interst'                    => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_INTER_SUMA'
                        ),
                        'offbal_dues'                => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_OFFBAL_DUES'
                        ),
                        'cond_off'                   => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_COND_OFF'
                        ),
                        'over_off'                   => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_OVER_OFF'
                        ),
                        'tot_off_bal'                => array(
                            'size'    => 15,
                            'padding' => 'left',
                            'title'   => 'CUCR_TOT_OFFBAL'
                        ),
                        'deval_reason'               => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CUCR_DEVAL_REASON'
                        ),
                        'expiration'                 => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CUCR_EXP_NOM'
                        ),
                        'interest_percent'           => array(
                            'size'    => 6,
                            'padding' => 'left',
                            'title'   => 'CUCR_INTR'
                        ),
                        'month_payment'              => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_MONTH_SUMA'
                        ),
                        'prov_mss'                   => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_PROV_MSS'
                        ),
                        'colr'                       => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_COLR'
                        ),
                        'colt1'                      => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CUCR_COLT1'
                        ),
                        'colr1'                      => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_COLR1'
                        ),
                        'colt3'                      => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'CUCR_COLT3'
                        ),
                        'colr3'                      => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'CUCR_COLR3'
                        )
                    )
                ),
                'f18_f22' => array(
                    'name'       => '',
                    'data_array' => 'f18_f22_data',
                    'content'    => '',
                    'set_to_fixed_width' => false,
                    'structure'  => array(
                        'adv_one'                    => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'ADV_ONE'
                        ),
                        'adv_two'                    => array(
                            'size'    => 8,
                            'padding' => 'left',
                            'title'   => 'ADV_TWO'
                        ),
                        'status'                     => array(
                            'size'    => 15,
                            'padding' => 'left',
                            'title'   => 'STATUS'
                        ),
                        'contract_num'               => array(
                            'size'    => 15,
                            'padding' => 'left',
                            'title'   => 'CREDIT_FILE'
                        ),
                        'ucn_eik'                   => array(
                            'size'    => 13,
                            'padding' => 'left',
                            'title'   => 'UCN_EIK'
                        ),
                        'glav_left'                  => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'PRINC'
                        ),
                        'glav_over_left'             => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'PRINC_OVER'
                        ),
                        'pay_left'                   => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'OVER_INTER'
                        ),
                        'balance'                    => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'COND_BAL'
                        ),
                        'balance_before_calculation' => array(
                            'size'    => 15,
                            'padding' => 'left',
                            'title'   => 'TOT_BALANS'
                        ),
                        'interst'                    => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'INTER_SUMA'
                        ),
                        'expiration'                 => array(
                            'size'    => 3,
                            'padding' => 'left',
                            'title'   => 'EXP_NOM'
                        ),
                        'colr'                       => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'COLR'
                        ),
                        'colr1'                      => array(
                            'size'    => 10,
                            'padding' => 'left',
                            'title'   => 'COLR1'
                        )
                    )
                ),
                'wallet' => array(
                    'name'       => '',
                    'data_array' => 'contracts_main_info',
                    'content'    => '',
                    'set_to_fixed_width' => false,
                    'structure'  => array(
                        'num'                    => array(
                            'title' => 'num'
                        ),
                        'customer_name'                    => array(
                            'title' => 'customer_name'
                        ),
                        'responsible'                    => array(
                            'title' => 'responsible'
                        ),
                        'identifier'                    => array(
                            'title' => 'identifier'
                        ),
                        'show_status'                    => array(
                            'title' => 'show_status'
                        ),
                        'date'                    => array(
                            'title' => 'date'
                        ),
                        'currency'                    => array(
                            'title' => 'currency'
                        ),
                        'original_amount'                    => array(
                            'title' => 'original_amount'
                        ),
                        'amount_utilize'                    => array(
                            'title' => 'amount_utilize'
                        ),
                        'amount_nonutilize'                    => array(
                            'title' => 'amount_nonutilize'
                        ),
                        'interest'                    => array(
                            'title' => 'interest'
                        ),
                        'gpr'                    => array(
                            'title' => 'gpr'
                        ),
                        'pledged_loan'                    => array(
                            'title' => 'pledged_loan'
                        ),
                        'credit_type'                    => array(
                            'title' => 'credit_type'
                        ),
                        'funding_source'                    => array(
                            'title' => 'funding_source'
                        ),
                        'credit_reason'                    => array(
                            'title' => 'credit_reason'
                        ),
                        'period'                    => array(
                            'title' => 'period'
                        ),
                        'final_deadline_date'                    => array(
                            'title' => 'final_deadline_date'
                        ),
                        'date_last_gratis'               => array(
                            'title' => 'date_last_gratis'
                        ),
                        'days_overdue'                    => array(
                            'title' => 'days_overdue'
                        ),
                        'uncovered_principal'                    => array(
                            'title' => 'uncovered_principal'
                        ),
                        'other_taxes'                    => array(
                            'title' => 'other_taxes'
                        ),
                        'overdue_principal'                    => array(
                            'title' => 'overdue_principal'
                        ),
                        'overdue_interest'                    => array(
                            'title' => 'overdue_interest'
                        ),
                        'overdue_zp'                    => array(
                            'title' => 'overdue_zp'
                        ),
                        'mngmt_tax'                    => array(
                            'title' => 'mngmt_tax'
                        ),
                        'engmnt_tax'                    => array(
                            'title' => 'engmnt_tax'
                        ),
                        'leftover_lpg'                    => array(
                            'title' => 'leftover_lpg'
                        ),
                        'leftover_penalty_interest'                    => array(
                            'title' => 'leftover_penalty_interest'
                        ),
                        'not_covered_taxes'                    => array(
                            'title' => 'not_covered_taxes'
                        ),
                        'total_overdue_obligations'                    => array(
                            'title' => 'total_overdue_obligations'
                        ),
                        'total_owed'                    => array(
                            'title' => 'total_owed'
                        ),
                        'total_owed_eur'                    => array(
                            'title' => 'total_owed_eur'
                        ),
                    )
                )
            );

            // control row for CUCR file
            $control_row = array(
                // CUCR_DATE
                'date'                       => '',
                // CUCR_BAE
                'code_declare'               => '',
                // CUCR_REC
                'rec_type'                   => 9,
                // CUCR_CRED
                'contract_num'               => 0,
                // CUCR_BORR
                'ucn'                        => '',
                // CUCR_SUMA
                'original_amount'            => '',
                // CUCR_PRINC
                'glav_left'                  => '',
                // CUCR_PRINC_OVER
                'glav_over_left'             => '',
                // CUCR_OVER_INTER
                'pay_left'                   => '',
                // CUCR_JUD_DUES
                'dues'                       => '',
                // CUCR_COND_BAL
                'balance'                    => '',
                // CUCR_TOT_BALANS
                'balance_before_calculation' => 0,
                // (CUCR_DEVAL) (CUCR_INTER_SUMA)
                'interst'                    => '',
                // CUCR_OFFBAL_DUES
                'offbal_dues'                => '',
                // CUCR_COND_OFF
                'cond_off'                   => '',
                // CUCR_OVER_OFF
                'over_off'                   => '',
                // CUCR_TOT_OFFBAL
                'tot_off_bal'                => 0,
                // CUCR_DEVAL_REASON
                'deval_reason'               => '',
                // CUCR_EXP_NOM
                'expiration'                 => '',
                // CUCR_INTR
                'interest_percent'           => '',
                // (CUCR_PROV_RISK) CUCR_MONTH_SUMA
                'month_payment'              => '',
                // CUCR_PROV_MSS
                'prov_mss'                   => '',
                // CUCR_COLR
                'colr'                       => '',
                // CUCR_COLT1
                'colt1'                      => '',
                // CUCR_COLR1
                'colr1'                      => '',
                // CUCR_COLT3
                'colt3'                      => '',
                // CUCR_COLR3
                'colr3'                      => ''
            );

            $customers_to_export = array();
            $contracts_to_export = array();
            $cucr_to_export = array();

            $queries_to_execute = array();

            $available_tags_customers = array(TAG_CUSTOMER_EXPORT, TAG_CUSTOMER_REEXPORT);
            $available_tags_documents = array(TAG_CONTRACT_EXPORT, TAG_CONTRACT_REEXPORT);

            $customer_client_vars = array(CLIENT_BORROWER_TYPE, CLIENT_LAST_EXPORT, CLIENT_REPORTING_UNIT, CLIENT_FOREIGN_PERSON_INDENTIFIER, CLIENT_STATUS, CLIENT_ECO_CODE, CLIENT_ECO_ACT);
            $customer_owned_company_vars = array(OWNED_COMPANY_CODE_DECLARING_UNIT);
            $document_vars = array(DOCUMENT_CONTRACT_CREDIT_PRODUCT, DOCUMENT_CONTRACT_GRANT_CREDIT, DOCUMENT_YEARLY_INTEREST,
                                   DOCUMENT_DATE_PAYMENT, DOCUMENT_NEFIN_ID, DOCUMENT_DATE_TRANSFER, DOCUMENT_CASE_TOTAL_TAX,
                                   DOCUMENT_CONTRACT_GRANT_CREDIT_CURRENCY, DOCUMENT_COLLATERAL_VALUE, DOCUMENT_CASE_TOTAL_DUES,
                                   DOCUMENT_CASE_PENALTY, DOCUMENT_TYPE_INTEREST, DOCUMENT_GR_KIND, DOCUMENT_GR_MARKET_VAL,
                                   DOCUMENT_CONTRACT_GRANT_CREDIT_CEDED, DOCUMENT_GR_MARKET_CURR, DOCUMENT_AMOUNT_UTILIZE, DOCUMENT_MIN_ANNUAL_RATE,
                                   DOCUMENT_BASE_INTEREST_RATE, DOCUMENT_SUM_INDEX, DOCUMENT_GRACE_PERIOD, DOCUMENT_CREDIT_PERSONS,
                                   DOCUMENT_LOAN_REPAYMENT, DOCUMENT_CRED_PREDOG, DOCUMENT_REASON_CRED_PREDOG, DOCUMENT_REGISTERED_PRINCIPAL_REST,
                                   DOCUMENT_LOAN_ADVNACE_ONE, DOCUMENT_LOAN_ADVNACE_TWO, DOCUMENT_AMOUNT_NOTUTILIZE,
                                   DOCUMENT_GPR, DOCUMENT_PLEDGED_LOAN, DOCUMENT_CREDIT_TYPE, DOCUMENT_CREDIT_PERIOD, DOCUMENT_CREDIT_INFO,
                                   DOCUMENT_LAST_DATE_FULL_GRATIS, DOCUMENT_FUNDING_SOURCE);
            $taxes_incomes_types = array_filter(preg_split('#\s*,\s*#', DOCUMENT_TAXES_RELATED_INCOMES));

            $project_vars = array(PROJECT_CONTRACT_NUM, PROJECT_PRINCIPAL, PROJECT_REGISTERED_PRINCIPAL_LEFT, PROJECT_REGISTERED_INTEREST_LEFT, PROJECT_INTEREST_ACCORDING_LEFT, PROJECT_PENALTY_INT_LEFT, PROJECT_REGISTERED_PRINCIPAL_COURT, PROJECT_REGISTERED_INTEREST_COURT, PROJECT_INTEREST_ACCORDING_COURT, PROJECT_PENALTY_INT_COURT, PROJECT_LAWYER_IMPERATIVES_COURT, PROJECT_STATETAX_IMPER_COURT, PROJECT_IMPERATIVES_FEE_COURT, PROJECT_ENFORCEMENT_ADVOCATE_COURT, PROJECT_STATE_ENFORCE_COURT, PROJECT_ENFORCE_TAX_COURT, PROJECT_CLAIM_JURIST_COURT, PROJECT_TAX_CLAIM_COURT, PROJECT_OTHER_FEE_COURT, PROJECT_EXPERT_TAX_COURT);
            $nom_vars = array(NOM_PRODUCT_CODE);

            // get the required additional vars ids
            $clients_types = array(CLIENT_TYPE_ID, CLIENT_CODEBTOR_TYPE_ID, CLIENT_WARRANTOR_TYPE_ID);
            $nom_types = array(NOM_PRODUCT_TYPE_ID, NOM_WARRANTY_TYPE_ID, NOM_INTEREST_RATE_TYPE_ID, NOM_BASE_INTEREST_PERCENT_TYPE_ID, NOM_EXTRA_PERIOD_TYPE_ID, NOM_REPAYMENT_SCHEMA_TYPE_ID, NOM_REFORMAT_CONTRACT_TYPE_ID, NOM_REASON_REFORMAT_CONTRACT_TYPE_ID);
            $sql = 'SELECT CONCAT(`model`, "_", `model_type`, "_", `name`), `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
                   'WHERE (`model`="Customer" AND `model_type` IN ("' . implode('","', $clients_types) . '") AND `name` IN ("' . implode('","', $customer_client_vars) . '")) OR ' . "\n" .
                   '      (`model`="Customer" AND `model_type`="' . OWNED_COMPANY_TYPE_ID . '" AND `name` IN ("' . implode('","', $customer_owned_company_vars) . '")) OR ' . "\n" .
                   '      (`model`="Document" AND `model_type`="' . DOCUMENT_CONTRACT_TYPE_ID . '" AND `name` IN ("' . implode('","', $document_vars) . '")) OR ' . "\n" .
                   '      (`model`="Project" AND `model_type`="' . PROJECT_TYPE_PROCEEDING . '" AND `name` IN ("' . implode('","', $project_vars) . '")) OR ' . "\n" .
                   '      (`model`="Nomenclature" AND `model_type` IN ("' . implode('","', $nom_types) . '") AND `name` IN ("' . implode('","', $nom_vars) . '"))' . "\n";
            $add_vars = $registry['db']->GetAssoc($sql);

            $sql = 'SELECT `value` FROM ' . DB_TABLE_CUSTOMERS_CSTM . ' WHERE `var_id`="' . $add_vars['Customer_' . OWNED_COMPANY_TYPE_ID . '_' . OWNED_COMPANY_CODE_DECLARING_UNIT] . '" AND `model_id`="' . OWNED_COMPANY_CREDIT_INS_ID . '"' . "\n";
            $borrower_type = $registry['db']->GetOne($sql);

            // group up the borrower fields
            $borrower_fields_ids = array();
            $reporting_unit_ids = array();
            $foreign_person_id_ids = array();
            $client_status_ids = array();
            $client_eco_code_ids = array();
            $client_eco_act_ids = array();

            foreach ($add_vars as $key => $av) {
                if (preg_match('#Customer_([0-9]+)_' . CLIENT_BORROWER_TYPE . '#', $key)) {
                    $borrower_fields_ids[] = $av;
                } elseif (preg_match('#Customer_([0-9]+)_' . CLIENT_REPORTING_UNIT . '#', $key)) {
                    $reporting_unit_ids[] = $av;
                } elseif (preg_match('#Customer_([0-9]+)_' . CLIENT_FOREIGN_PERSON_INDENTIFIER . '#', $key)) {
                    $foreign_person_id_ids[] = $av;
                } elseif (preg_match('#Customer_([0-9]+)_' . CLIENT_STATUS . '#', $key)) {
                    $client_status_ids[] = $av;
                } elseif (preg_match('#Customer_([0-9]+)_' . CLIENT_ECO_CODE . '#', $key)) {
                    $client_eco_code_ids[] = $av;
                } elseif (preg_match('#Customer_([0-9]+)_' . CLIENT_ECO_ACT . '#', $key)) {
                    $client_eco_act_ids[] = $av;
                }
            }

            // GET THE DATA FOR THE CUSTOMERS
            if (!empty($filters['borr'])) {
                $ids_search = array();
                $search_by_tags = false;

                // take the last export
                $sql = 'SELECT * FROM ' . DB_TABLE_EXPORTS_LOG . ' WHERE `log` LIKE "%file := BORR%" ORDER BY id DESC' . "\n";
                $borr_export_data = $registry['db']->GetRow($sql);

                if (!empty($borr_export_data)) {
                    $log_info = array();
                    $log_data = preg_split('#\r\n|\r|\n#', $borr_export_data['log']);
                    $log_data = array_filter($log_data);
                    foreach ($log_data as $lg) {
                        list($log_key, $log_value) = preg_split('#\s*:=\s*#', $lg);
                        $log_info[$log_key] = $log_value;
                    }
                    $borr_export_data['log'] = $log_info;
                }

                $control_row = array('date' => $current_date_format, 'code_declare' => $borrower_type, 'record_type' => 9,
                                     'client' => '', 'ucn' => 0, 'name' => '', 'address' => '',
                                     'extra' => '', 'client_status' => '', 'sector' => '', 'branch' => '');

                $revision = 0;
                $daily_index = 0;

                $date_for_export = General::strftime('%y%m%d');
                $exoport_log_included_customers = array();
                if ($filters['borr_type_export'] == 'reexport') {
                    if (isset($borr_export_data['log']['revision'])) {
                        $revision = $borr_export_data['log']['revision']+1;
                    }
                    if (!empty($borr_export_data['log']['exported_ids'])) {
                        $exported_ids = explode(',', $borr_export_data['log']['exported_ids']);
                        foreach ($exported_ids as $e_id) {
                            list($customer_id, $record_type) = explode('_', $e_id);
                            $ids_search[$customer_id] = $record_type;
                        }
                    }
                    if (isset($borr_export_data['log']['daily_index'])) {
                        $daily_index = $borr_export_data['log']['daily_index'];
                    }
                    if (!empty($borr_export_data['log']['exported'])) {
                        $date_for_export = General::strftime('%y%m%d', strtotime($borr_export_data['log']['exported']));
                    }
                } else {
                    $search_by_tags = true;
                    if (isset($borr_export_data['log']['daily_index']) && General::strftime('%Y-%m-%d', strtotime($borr_export_data['exported'])) == date('Y-m-d')) {
                        $daily_index = $borr_export_data['log']['daily_index']+1;
                    }
                }

                if ($search_by_tags || !empty($ids_search)) {
                    $sql = array();
                    $sql['select'] = 'SELECT c.id as idx, "' . $current_date_format . '" as date, "' . $borrower_type . '" as code_declare, "" as record_type, c_cstm_rep.value as reporting_unit, ' . "\n" .
                                     '       c_cstm_for.value as foreign_person_id, c_cstm.value as client, IF(c.is_company, c.eik, c.ucn) as ucn, CONCAT(ci18n.name, " ", ci18n.lastname) as name, ' . "\n" .
                                     '       IF(c.is_company, ci18n.registration_address, ci18n.address_by_personal_id) as address, "" as extra, c_cstm_stat.value as client_status, c_cstm_eco_code.value as sector, c_cstm_eco_act.value as branch';
                    $sql['from']   = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                     '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm' . "\n" .
                                     '  ON (c.id=c_cstm.model_id AND c_cstm.var_id IN ("' . implode('","', $borrower_fields_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_rep' . "\n" .
                                     '  ON (c.id=c_cstm_rep.model_id AND c_cstm_rep.var_id IN ("' . implode('","', $reporting_unit_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_for' . "\n" .
                                     '  ON (c.id=c_cstm_for.model_id AND c_cstm_for.var_id IN ("' . implode('","', $foreign_person_id_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_stat' . "\n" .
                                     '  ON (c.id=c_cstm_stat.model_id AND c_cstm_stat.var_id IN ("' . implode('","', $client_status_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_eco_code' . "\n" .
                                     '  ON (c.id=c_cstm_eco_code.model_id AND c_cstm_eco_code.var_id IN ("' . implode('","', $client_eco_code_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_eco_act' . "\n" .
                                     '  ON (c.id=c_cstm_eco_act.model_id AND c_cstm_eco_act.var_id IN ("' . implode('","', $client_eco_act_ids) . '"))' . "\n";

                    if ($search_by_tags) {
                        $sql['select'] .= ', mt.tag_id as tag_id';
                        $sql['from'] .= 'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS mt' . "\n" .
                                        '  ON (mt.model_id=c.id AND mt.model="Customer" AND mt.tag_id IN ("' . implode('","', $available_tags_customers) . '"))' . "\n";
                    }

                    $where = array();
                    $where[] = 'c.type IN ("' . implode('","', $clients_types) . '")';
                    $where[] = 'c.active=1';
                    $where[] = 'c.deleted_by=0';
                    if (!empty($ids_search)) {
                        $where[] = 'c.id IN ("' . implode('","', array_keys($ids_search)) . '")';
                    }

                    $sql['where']   = 'WHERE ' . implode(' AND ', $where);
                    $query = implode("\n", $sql);

                    $customers_to_export = $registry['db']->GetAssoc($query);
                }

                $included_customers = array_keys($customers_to_export);
                foreach ($customers_to_export as $key => $cte) {
                    if (empty($ids_search)) {
                        // export current data
                        if ($cte['tag_id'] == TAG_CUSTOMER_EXPORT) {
                            $customers_to_export[$key]['record_type'] = 1;
                        } else {
                            $customers_to_export[$key]['record_type'] = 2;
                        }
                        unset($customers_to_export[$key]['tag_id']);
                    } else {
                        $customers_to_export[$key]['record_type'] = $ids_search[$key];
                    }

                    // process the reporting code
                    $cl_code = '';
                    switch ($cte['client']) {
                        case CLIENT_REPORTING_UNIT_FOREIGN:
                            $cl_code = $cte['reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_foreign');
                            break;
                        case CLIENT_REPORTING_UNIT_OTHER:
                            $cl_code = $cte['ucn'] . $registry['translater']->translate('reports_client_reporting_unit_other');
                            break;
                        case CLIENT_REPORTING_UNIT_FICTIVE:
                            $cl_code = $cte['reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_fictive');
                            break;
                        default:
                            break;
                    }
                    if (!empty($cl_code)) {
                        $customers_to_export[$key]['ucn'] = $cl_code;
                    }

                    //foreign_person_id
                    if ($cte['client'] == CLIENT_REPORTING_UNIT_FOREIGN) {
                        $customers_to_export[$key]['extra'] = $cte['foreign_person_id'];
                    }
                    unset($customers_to_export[$key]['reporting_unit']);
                    unset($customers_to_export[$key]['foreign_person_id']);

                    $customers_to_export[$key]['address'] = preg_replace('/[\r\n]+/', ', ', $cte['address']);
                    $exoport_log_included_customers[] = sprintf('%d_%d', $key, $customers_to_export[$key]['record_type']);
                    $control_row['ucn']++;
                }
                $customers_to_export[] = $control_row;

                $files_structure['borr']['name'] = sprintf('BORR_%s_%s%s%s.CSV', COMPANY_CREDIT_KEY, $date_for_export, sprintf('%02d', $daily_index), ($revision ? '_R' . sprintf('%02d', $revision) : ''));

                $export_log_data = array();
                $export_log_data[] = 'file := BORR';
                $export_log_data[] = 'revision := ' . $revision;
                $export_log_data[] = 'daily_index := ' . $daily_index;
                $export_log_data[] = 'exported_ids := ' . implode(',', $exoport_log_included_customers);

                if ($filters['borr_type_export'] == 'reexport' && !empty($borr_export_data)) {
                    // UPDATE query
                    $queries_to_execute[] = 'UPDATE ' . DB_TABLE_EXPORTS_LOG . ' SET `log`="' . implode('\r\n', $export_log_data) . '" WHERE `id`="' . $borr_export_data['id'] . '"';
                } else {
                    // INSERT query
                    $queries_to_execute[] = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="' . self::$report_name . '", `file_name`="' . $files_structure['borr']['name'] . '", `log`="' . implode('\r\n', $export_log_data) . '", `exported`=NOW(), `exported_by`="' . $registry['currentUser']->get('id') . '"';
                }
            } else {
                unset($files_structure['borr']);
            }

            if (!empty($filters['cred'])) {
                $ids_search = array();
                $search_by_tags = false;

                // take the last export
                $sql = 'SELECT * FROM ' . DB_TABLE_EXPORTS_LOG . ' WHERE `log` LIKE "%file := CRED%" ORDER BY id DESC' . "\n";
                $cred_export_data = $registry['db']->GetRow($sql);

                if (!empty($cred_export_data)) {
                    $log_info = array();
                    $log_data = preg_split('#\r\n|\r|\n#', $cred_export_data['log']);
                    $log_data = array_filter($log_data);
                    foreach ($log_data as $lg) {
                        list($log_key, $log_value) = preg_split('#\s*:=\s*#', $lg);
                        $log_info[$log_key] = $log_value;
                    }
                    $cred_export_data['log'] = $log_info;
                }

                $revision = 0;
                $daily_index = 0;
                $date_for_export = General::strftime('%y%m%d');
                $exoport_log_included_contracts = array();
                if ($filters['cred_type_export'] == 'reexport') {
                    if (isset($cred_export_data['log']['revision'])) {
                        $revision = $cred_export_data['log']['revision']+1;
                    }
                    if (!empty($cred_export_data['log']['exported_ids'])) {
                        $exported_ids = explode(',', $cred_export_data['log']['exported_ids']);
                        foreach ($exported_ids as $e_id) {
                            list($contract_id, $record_type) = explode('_', $e_id);
                            $ids_search[$contract_id] = $record_type;
                        }
                    }
                    if (isset($cred_export_data['log']['daily_index'])) {
                        $daily_index = $cred_export_data['log']['daily_index'];
                    }
                    if (!empty($cred_export_data['log']['exported'])) {
                        $date_for_export = General::strftime('%y%m%d', strtotime($cred_export_data['log']['exported']));
                    }
                } else {
                    $search_by_tags = true;
                    if (isset($cred_export_data['log']['daily_index']) && General::strftime('%Y-%m-%d', strtotime($cred_export_data['exported'])) == date('Y-m-d')) {
                        $daily_index = $cred_export_data['log']['daily_index']+1;
                    }
                }

                if ($search_by_tags || !empty($ids_search)) {
                    $sql = array();
                    $sql['select'] = 'SELECT d.id as idx, "' . $current_date_format . '" as date, "' . $borrower_type . '" as code_declare, "" as record_type, ' . "\n" .
                                     '       IF((d_cstm_nefin_id.value IS NOT NULL AND d_cstm_nefin_id.value!=""), d_cstm_nefin_id.value, di18n.name) as contract_num, ' . "\n" .
                                     '       IF(c.is_company, c.eik, c.ucn) as ucn, n_cstm_code_ckr.value as code_ckr, ' . "\n" .
                                     '       d_cstm_credit_curr.value as currency, d_cstm_credit.value as original_amount, d_cstm_credit.value as currency_amount, ' . "\n" .
                                     '       DATE_FORMAT(d_cstm_date_trans.value, "%Y%m%d") as date_start, "" as date_end, d_cstm_col_val.value as colr, "000" as colt1, ' . "\n" .
                                     '       "0" as colr1, "000" as colt3, "0" as colr3, n_cstm_type_intr_code_ckr.value as contract_interest_percent, ' . "\n" .
                                     '       IF(n_cstm_type_intr_code_ckr.value="10", "", n_cstm_base_intr_tp.value) as base_interest_percent, ' . "\n" .
                                     '       IF(CAST(d_cstm_percent.value AS DECIMAL(10,6))>0, d_cstm_percent.value, d_cstm_sum_idx.value) as starting_interest_percent, ' . "\n" .
                                     '       n_cstm_gr_per.value as extra_period, n_cstm_l_rep.value as extension_period, n_cstm_cred_pre.value as reformat_credit_contract, ' . "\n" .
                                     '       n_cstm_reas_ref.value as reformat_reason, "" as col1, "" as col2, "" as col3, "" as col4, "" as col5, "" as col6, "-" as col7, "-" as col8, ' . "\n" .
                                     '       c_cstm.value as borrower_type, c_cstm_rep.value as reporting_unit, d_cstm_credit_ceded.value as ceded_amount, tm.tag_id as ceded' . "\n";

                    $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n";

                    if ($search_by_tags) {
                        $sql['select'] .= ', mt.tag_id as tag_id';
                        $sql['from']  .= 'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS mt' . "\n" .
                                         '  ON (mt.model_id=d.id AND mt.model="Document" AND mt.tag_id IN ("' . implode('","', $available_tags_documents) . '"))' . "\n";
                    }

                    $sql['from']  .= 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                     '  ON (di18n.parent_id=d.id AND di18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                     '  ON (c.id=d.customer)' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm' . "\n" .
                                     '  ON (c.id=c_cstm.model_id AND c_cstm.var_id IN ("' . implode('","', $borrower_fields_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_rep' . "\n" .
                                     '  ON (c.id=c_cstm_rep.model_id AND c_cstm_rep.var_id IN ("' . implode('","', $reporting_unit_ids) . '"))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pr' . "\n" .
                                     '  ON (d.id=d_cstm_pr.model_id AND d_cstm_pr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_CREDIT_PRODUCT] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_code_ckr' . "\n" .
                                     '  ON (d_cstm_pr.value=n_cstm_code_ckr.model_id AND n_cstm_code_ckr.var_id=' . $add_vars['Nomenclature_' . NOM_PRODUCT_TYPE_ID . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit' . "\n" .
                                     '  ON (d.id=d_cstm_credit.model_id AND d_cstm_credit.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_curr' . "\n" .
                                     '  ON (d.id=d_cstm_credit_curr.model_id AND d_cstm_credit_curr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CURRENCY] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_ceded' . "\n" .
                                     '  ON (d.id=d_cstm_credit_ceded.model_id AND d_cstm_credit_ceded.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CEDED] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_percent' . "\n" .
                                     '  ON (d.id=d_cstm_percent.model_id AND d_cstm_percent.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_YEARLY_INTEREST] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_nefin_id' . "\n" .
                                     '  ON (d.id=d_cstm_nefin_id.model_id AND d_cstm_nefin_id.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_NEFIN_ID] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_trans' . "\n" .
                                     '  ON (d.id=d_cstm_date_trans.model_id AND d_cstm_date_trans.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_DATE_TRANSFER] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_col_val' . "\n" .
                                     '  ON (d.id=d_cstm_col_val.model_id AND d_cstm_col_val.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_COLLATERAL_VALUE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_intr' . "\n" .
                                     '  ON (d.id=d_cstm_type_intr.model_id AND d_cstm_type_intr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_TYPE_INTEREST] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_type_intr_code_ckr' . "\n" .
                                     '  ON (d_cstm_type_intr.value=n_cstm_type_intr_code_ckr.model_id AND n_cstm_type_intr_code_ckr.var_id=' . $add_vars['Nomenclature_' . NOM_INTEREST_RATE_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_base_intr_tp' . "\n" .
                                     '  ON (d.id=d_cstm_base_intr_tp.model_id AND d_cstm_base_intr_tp.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_BASE_INTEREST_RATE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_base_intr_tp' . "\n" .
                                     '  ON (d_cstm_base_intr_tp.value=n_cstm_base_intr_tp.model_id AND n_cstm_base_intr_tp.var_id=' . $add_vars['Nomenclature_' . NOM_BASE_INTEREST_PERCENT_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sum_idx' . "\n" .
                                     '  ON (d.id=d_cstm_sum_idx.model_id AND d_cstm_sum_idx.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_SUM_INDEX] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_gr_per' . "\n" .
                                     '  ON (d.id=d_cstm_gr_per.model_id AND d_cstm_gr_per.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GRACE_PERIOD] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_gr_per' . "\n" .
                                     '  ON (d_cstm_gr_per.value=n_cstm_gr_per.model_id AND n_cstm_gr_per.var_id=' . $add_vars['Nomenclature_' . NOM_EXTRA_PERIOD_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_l_rep' . "\n" .
                                     '  ON (d.id=d_cstm_l_rep.model_id AND d_cstm_l_rep.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LOAN_REPAYMENT] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_l_rep' . "\n" .
                                     '  ON (d_cstm_l_rep.value=n_cstm_l_rep.model_id AND n_cstm_l_rep.var_id=' . $add_vars['Nomenclature_' . NOM_REPAYMENT_SCHEMA_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_cred_pre' . "\n" .
                                     '  ON (d.id=d_cstm_cred_pre.model_id AND d_cstm_cred_pre.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CRED_PREDOG] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_cred_pre' . "\n" .
                                     '  ON (d_cstm_cred_pre.value=n_cstm_cred_pre.model_id AND n_cstm_cred_pre.var_id=' . $add_vars['Nomenclature_' . NOM_REFORMAT_CONTRACT_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_reas_ref' . "\n" .
                                     '  ON (d.id=d_cstm_reas_ref.model_id AND d_cstm_reas_ref.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_REASON_CRED_PREDOG] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_reas_ref' . "\n" .
                                     '  ON (d_cstm_reas_ref.value=n_cstm_reas_ref.model_id AND n_cstm_reas_ref.var_id=' . $add_vars['Nomenclature_' . NOM_REASON_REFORMAT_CONTRACT_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                                     '  ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id="' . DOCUMENT_TAG_CEDED . '")' . "\n";
                    $where = array();
                    $where[] = 'd.type="' . DOCUMENT_CONTRACT_TYPE_ID . '"';
                    $where[] = 'd.active=1';
                    $where[] = 'd.deleted_by=0';

                    if (!empty($ids_search)) {
                        $where[] = 'd.id IN ("' . implode('","', array_keys($ids_search)) . '")';
                    }

                    $sql['where']   = 'WHERE ' . implode(' AND ', $where);
                    $query = implode("\n", $sql);

                    $contracts_to_export = $registry['db']->GetAssoc($query);
                }

                $control_row = array('date' => '', 'code_declare' => '', 'record_type' => '', 'contract_num' => '',
                                     'ucn' => '', 'code_ckr' => '', 'currency' => '',
                                     'original_amount' => '', 'currency_amount' => '', 'date_start' => '', 'date_end' => '',
                                     'colr' => '', 'colt1' => '', 'colr1' => '', 'colt3' => '', 'colr3' => '',
                                     'contract_interest_percent' => '', 'base_interest_percent' => '', 'starting_interest_percent' => '',
                                     'extra_period' => '', 'extension_period' => '', 'reformat_credit_contract' => '', 'reformat_reason' => '',
                                     'col1' => '', 'col2' => '', 'col3' => '', 'col4' => '', 'col5' => '', 'col6' => '', 'col7' => '', 'col8' => '');

                $total = 0;
                $total_rows = 0;
                $included_contracts = array_keys($contracts_to_export);

                $contracts_extra_info = array();

                if (!empty($included_contracts)) {
                    // get the payment date at the last row
                    $sql = 'SELECT gt2.model_id, MAX(gt2.article_code) as last_payment_date, 0 as value1, "" as code1, 0 as value2, "" as code2, "" as codebtor, "" as warrantor' . "\n" .
                           'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                           'WHERE gt2.model="Document" AND gt2.model_id IN ("' . implode('","', $included_contracts) . '")' . "\n" .
                           'GROUP BY gt2.model, gt2.model_id' . "\n";
                    $contracts_extra_info = $registry['db']->GetAssoc($sql);

                    // get the data from the group tables
                    $sql = 'SELECT d.id, d_cstm_val.value as val, d_cstm_curr.value as currency, n_cstm_code.value as code, d_cstm_kind_col.value as nom_id, tm.tag_id as accept_warranty, tm2.tag_id as other_warranty' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_kind_col' . "\n" .
                           ' ON (d.id IN ("' . implode('","', $included_contracts) . '") AND d.id=d_cstm_kind_col.model_id AND d_cstm_kind_col.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_KIND] . '" AND d_cstm_kind_col.value!="" AND d_cstm_kind_col.value!="0")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                           '  ON (tm.model="Nomenclature" AND tm.model_id=d_cstm_kind_col.value AND tm.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_ACCPET)) . '"))' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm2' . "\n" .
                           '  ON (tm2.model="Nomenclature" AND tm2.model_id=d_cstm_kind_col.value AND tm2.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_OTHER)) . '"))' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_val' . "\n" .
                           ' ON (d.id=d_cstm_val.model_id AND d_cstm_val.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_VAL] . '" AND d_cstm_kind_col.num=d_cstm_val.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_curr' . "\n" .
                           ' ON (d.id=d_cstm_curr.model_id AND d_cstm_curr.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_CURR] . '" AND d_cstm_kind_col.num=d_cstm_curr.num)' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_code' . "\n" .
                           '  ON (d_cstm_kind_col.value=n_cstm_code.model_id AND n_cstm_code.var_id=' . $add_vars['Nomenclature_' . NOM_WARRANTY_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n";
                    $contracts_group_tbl = $registry['db']->GetAll($sql);

                    foreach ($contracts_group_tbl as $tbl_r) {
                        if (empty($tbl_r['val'])) {
                            continue;
                        }
                        $value = self::convertCurrencyValueToBGN($registry, $tbl_r['val'], $tbl_r['currency']);

                        if (!empty($tbl_r['accept_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value1']) {
                            $contracts_extra_info[$tbl_r['id']]['value1'] = $value;
                            $contracts_extra_info[$tbl_r['id']]['code1'] = $tbl_r['code'];
                        }
                        if (!empty($tbl_r['other_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value2']) {
                            $contracts_extra_info[$tbl_r['id']]['value2'] = $value;
                            $contracts_extra_info[$tbl_r['id']]['code2'] = $tbl_r['code'];
                        }
                    }

                    // get the customers additional data (co-debtor and warrantor)
                    $cl_types = array(CLIENT_CODEBTOR_TYPE_ID, CLIENT_WARRANTOR_TYPE_ID);
                    $sql = 'SELECT d.id, c.type as cl_type, c.id as customer_id, c_cstm.value as borrower_type, c_cstm_rep.value as code_reporting_unit, c.ucn, c.eik' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_cl' . "\n" .
                           ' ON (d.id IN ("' . implode('","', $included_contracts) . '") AND d.id=d_cstm_cl.model_id AND d_cstm_cl.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CREDIT_PERSONS] . '" AND d_cstm_cl.value!="" AND d_cstm_cl.value!="0")' . "\n" .
                           'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                           '  ON (c.id=d_cstm_cl.value AND c.type IN ("' . implode('","', $cl_types) . '"))' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm' . "\n" .
                           '  ON (c.id=c_cstm.model_id AND c_cstm.var_id IN ("' . implode('","', $borrower_fields_ids) . '"))' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_rep' . "\n" .
                           '  ON (c.id=c_cstm_rep.model_id AND c_cstm_rep.var_id IN ("' . implode('","', $reporting_unit_ids) . '"))' . "\n";
                    $additional_cust = $registry['db']->GetAll($sql);

                    foreach ($additional_cust as $add_cust) {
                        $add_var_key = '';
                        $cl_code = '';
                        switch ($add_cust['borrower_type']) {
                            case CLIENT_REPORTING_UNIT_PERSON:
                                $cl_code = $add_cust['ucn'];
                                break;
                            case CLIENT_REPORTING_UNIT_COMPANY:
                                $cl_code = $add_cust['eik'];
                                break;
                            case CLIENT_REPORTING_UNIT_FOREIGN:
                                $cl_code = $add_cust['code_reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_foreign');
                                break;
                            case CLIENT_REPORTING_UNIT_OTHER:
                                $cl_code = $add_cust['ucn'] . $registry['translater']->translate('reports_client_reporting_unit_other');
                                break;
                            case CLIENT_REPORTING_UNIT_FICTIVE:
                                $cl_code = $add_cust['code_reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_fictive');
                                break;
                            default:
                                break;
                        }

                        if ($add_cust['cl_type'] == CLIENT_CODEBTOR_TYPE_ID) {
                            $add_var_key = 'codebtor';
                        } elseif ($add_cust['cl_type'] == CLIENT_WARRANTOR_TYPE_ID) {
                            $add_var_key = 'warrantor';
                        }

                        if (!$cl_code || !$add_var_key) {
                            continue;
                        }

                        if (!is_array($contracts_extra_info[$add_cust['id']][$add_var_key])) {
                            $contracts_extra_info[$add_cust['id']][$add_var_key] = array();
                        }
                        $contracts_extra_info[$add_cust['id']][$add_var_key][] = $cl_code;
                    }
                }

                foreach ($contracts_to_export as $key => $cte) {
                    if (empty($ids_search)) {
                        // export current data
                        if ($cte['tag_id'] == TAG_CONTRACT_EXPORT) {
                            $contracts_to_export[$key]['record_type'] = 3;
                        } else {
                            $contracts_to_export[$key]['record_type'] = 4;
                        }

                        unset($contracts_to_export[$key]['tag_id']);
                    } else {
                        $contracts_to_export[$key]['record_type'] = $ids_search[$key];
                    }

                    if ($contracts_to_export[$key]['record_type'] == 4) {
                        $contracts_to_export[$key]['reformat_credit_contract'] = '51';
                        $contracts_to_export[$key]['reformat_reason'] = '65';
                    }

                    $exoport_log_included_contracts[] = sprintf('%d_%d', $key, $contracts_to_export[$key]['record_type']);

                    $contracts_to_export[$key]['starting_interest_percent'] = sprintf('%06s', sprintf('%.2f', $cte['starting_interest_percent']));
                    if ($cte['ceded']) {
                        $contracts_to_export[$key]['original_amount'] = sprintf('%.2f', $cte['ceded_amount']);
                    } else {
                        $contracts_to_export[$key]['original_amount'] = sprintf('%.2f', $cte['original_amount']);
                    }
                    $contracts_to_export[$key]['currency_amount'] = self::convertCurrencyValueToBGN($registry, $contracts_to_export[$key]['original_amount'], $cte['currency']);

                    if (!empty($contracts_extra_info[$key]['last_payment_date'])) {
                        $contracts_to_export[$key]['date_end'] = General::strftime('%Y%m%d', strtotime($contracts_extra_info[$key]['last_payment_date']));
                    }
                    if (!empty($contracts_extra_info[$key]['code1'])) {
                        $contracts_to_export[$key]['colt1'] = $contracts_extra_info[$key]['code1'];
                    }
                    if (!empty($contracts_extra_info[$key]['code2'])) {
                        $contracts_to_export[$key]['colt3'] = $contracts_extra_info[$key]['code2'];
                    }
                    if (!empty($contracts_extra_info[$key]['value1'])) {
                        $contracts_to_export[$key]['colr1'] = $contracts_extra_info[$key]['value1'];
                    }
                    if (!empty($contracts_extra_info[$key]['value2'])) {
                        $contracts_to_export[$key]['colr3'] = $contracts_extra_info[$key]['value2'];
                    }
                    if (!empty($contracts_extra_info[$key]['codebtor'])) {
                        $contracts_to_export[$key]['col7'] = implode(' ', $contracts_extra_info[$key]['codebtor']);
                    }
                    if (!empty($contracts_extra_info[$key]['warrantor'])) {
                        $contracts_to_export[$key]['col8'] = implode(' ', $contracts_extra_info[$key]['warrantor']);
                    }

                    // check borr type
                    $cl_type = '';
                    $prefix = $cte['ucn'];
                    switch ($cte['borrower_type']) {
                        case CLIENT_REPORTING_UNIT_FOREIGN:
                            $prefix = $cte['reporting_unit'];
                            $cl_type = 'foreign';
                            break;
                        case CLIENT_REPORTING_UNIT_OTHER:
                            $cl_type = 'other';
                            break;
                        case CLIENT_REPORTING_UNIT_FICTIVE:
                            $prefix = $cte['reporting_unit'];
                            $cl_type = 'fictive';
                            break;
                        default:
                            break;
                    }
                    if (!empty($cl_type)) {
                        $contracts_to_export[$key]['ucn'] = $prefix . $registry['translater']->translate('reports_client_reporting_unit_' . $cl_type);
                    }
                    unset($contracts_to_export[$key]['reporting_unit']);
                    unset($contracts_to_export[$key]['borrower_type']);
                    unset($contracts_to_export[$key]['ceded_amount']);
                    unset($contracts_to_export[$key]['ceded']);

                    $total = sprintf('%.2f', $total + floatval($contracts_to_export[$key]['currency_amount']));
                    $total_rows++;
                }

                // check the length of the total
                $total_str = strval($total);
                $round_sign = 1;
                while (strlen($total_str) > $files_structure['cred']['structure']['currency_amount']['size'] && $round_sign>=0) {
                    $total_str = strval(round(floatval($total), $round_sign));
                    $round_sign--;
                }
                if (strlen($total_str) > $files_structure['cred']['structure']['currency_amount']['size']) {
                    $total_str = substr($total_str, 0, $files_structure['cred']['structure']['currency_amount']['size']);
                }

                $control_row['date'] = $current_date_format;
                $control_row['code_declare'] = $borrower_type;
                $control_row['record_type'] = 9;
                $control_row['contract_num'] = $total_rows;
                $control_row['currency_amount'] = $total_str;
                $control_row['col7'] = '-';
                $control_row['col8'] = '-';
                $contracts_to_export[] = $control_row;

                $files_structure['cred']['name'] = sprintf('CRED_%s_%s%s%s.CSV', COMPANY_CREDIT_KEY, $date_for_export, sprintf('%02d', $daily_index), ($revision ? '_R' . sprintf('%02d', $revision) : ''));

                $export_log_data = array();
                $export_log_data[] = 'file := CRED';
                $export_log_data[] = 'revision := ' . $revision;
                $export_log_data[] = 'daily_index := ' . $daily_index;
                $export_log_data[] = 'exported_ids := ' . implode(',', $exoport_log_included_contracts);

                if ($filters['cred_type_export'] == 'reexport' && !empty($cred_export_data)) {
                    // UPDATE query
                    $queries_to_execute[] = 'UPDATE ' . DB_TABLE_EXPORTS_LOG . ' SET `log`="' . implode('\r\n', $export_log_data) . '" WHERE `id`="' . $cred_export_data['id'] . '"';
                } else {
                    // INSERT query
                    $queries_to_execute[] = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="' . self::$report_name . '", `file_name`="' . $files_structure['cred']['name'] . '", `log`="' . implode('\r\n', $export_log_data) . '", `exported`=NOW(), `exported_by`="' . $registry['currentUser']->get('id') . '"';
                }
            } else {
                unset($files_structure['cred']);
            }

            // get previous cucr file
            if (!empty($filters['cucr'])) {
                // take the last export
                $sql = 'SELECT * FROM ' . DB_TABLE_EXPORTS_LOG . ' WHERE `log` LIKE "%file := CUCR%" ORDER BY id DESC' . "\n";
                $cucr_export_data = $registry['db']->GetRow($sql);

                if ($filters['cucr_type_export'] == 'reexport') {
                    $name_of_existing_path = PH_EXPORTS_CACHE_DIR . $cucr_export_data['file_name'];
                    if (file_exists($name_of_existing_path)) {
                        $files_structure['cucr']['content'] = General::fileGetContents($name_of_existing_path);
                    } else {
                        $files_structure['cucr']['content'] = '';
                    }
                    $files_structure['cucr']['name'] = $cucr_export_data['file_name'];
                } elseif ($filters['cucr_type_export'] == 'new') {
                    $statuses_to_include = preg_split('#\s*,\s*#', DOCUMENT_STATUS_INCLUDE_IN_EXPORT);
                    $status_court_paid = preg_split('#\s*,\s*#', DOCUMENT_STATUS_COURT_PAID);
                    $status_fraud = preg_split('#\s*,\s*#', DOCUMENT_STATUS_FRAUD);
                    $tags_court_paid = preg_split('#\s*,\s*#', DOCUMENT_TAG_COURT_PAID);
                    $tags_exclude_from_ckr = preg_split('#\s*,\s*#', DOCUMENT_TAG_EXCLUDE_FROM_CKR);

                    $iso_cucr_month_last_date = strtotime('last day of previous month');
                    $contracts_list = self::getCucrContractsList($registry, $iso_cucr_month_last_date);
                    $cucr_data = array();

                    if (!empty($contracts_list)) {
                        $sql = array();
                        $sql['select'] = 'SELECT d.id as idx, "' . General::strftime('%Y%m%d', $iso_cucr_month_last_date) . '" as date,' . "\n" .
                                         '       "' . $borrower_type . '" as code_declare, d_cstm_min_rate.value as min_rate, ' . "\n" .
                                         '       IF((d_cstm_nefin_id.value IS NOT NULL AND d_cstm_nefin_id.value!=""), d_cstm_nefin_id.value, di18n.name) as contract_num,' . "\n" .
                                         '       c.eik, c.ucn, d_cstm_princ_rest.value as princ_rest, ROUND(d_cstm_credit.value,2) as original_amount, ' . "\n" .
                                         '       ROUND(d_cstm_percent.value,2) as starting_interest_percent, d_cstm_tot_dues.value as case_dues, ' . "\n" .
                                         '       d_cstm_tot_tax.value as case_tax, tm.tag_id as tag, d.status, d.substatus, c_cstm.value as borrower_type, ' . "\n" .
                                         '       c_cstm_rep.value as reporting_unit, d_cstm_credit_curr.value as currency, d_cstm_credit_ceded.value as ceded_value, ' . "\n" .
                                         '       d_cstm_gr_per.value as grace_period, tm1.tag_id as ceded, tm2.tag_id as credit_line, d_cstm_case_penal.value as case_penal,' . "\n" .
                                         '       d_cstm_amt_utilize.value as amount_utilize, d_cstm_sum_idx.value as sum_idx, d_cstm_col_val.value as collateral_val  ' . "\n";

                        $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                         '  ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                         '  ON (c.id=d.customer)' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm' . "\n" .
                                         '  ON (c.id=c_cstm.model_id AND c_cstm.var_id IN ("' . implode('","', $borrower_fields_ids) . '"))' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_rep' . "\n" .
                                         '  ON (c.id=c_cstm_rep.model_id AND c_cstm_rep.var_id IN ("' . implode('","', $reporting_unit_ids) . '"))' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit' . "\n" .
                                         '  ON (d.id=d_cstm_credit.model_id AND d_cstm_credit.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_curr' . "\n" .
                                         '  ON (d.id=d_cstm_credit_curr.model_id AND d_cstm_credit_curr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CURRENCY] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_ceded' . "\n" .
                                         '  ON (d.id=d_cstm_credit_ceded.model_id AND d_cstm_credit_ceded.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CEDED] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_percent' . "\n" .
                                         '  ON (d.id=d_cstm_percent.model_id AND d_cstm_percent.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_YEARLY_INTEREST] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_nefin_id' . "\n" .
                                         '  ON (d.id=d_cstm_nefin_id.model_id AND d_cstm_nefin_id.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_NEFIN_ID] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_gr_per' . "\n" .
                                         '  ON (d.id=d_cstm_gr_per.model_id AND d_cstm_gr_per.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GRACE_PERIOD] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_princ_rest' . "\n" .
                                         '  ON (d.id=d_cstm_princ_rest.model_id AND d_cstm_princ_rest.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_REGISTERED_PRINCIPAL_REST] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tot_dues' . "\n" .
                                         '  ON (d.id=d_cstm_tot_dues.model_id AND d_cstm_tot_dues.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_TOTAL_DUES] . '")' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tot_tax' . "\n" .
                                         '  ON (d.id=d_cstm_tot_tax.model_id AND d_cstm_tot_tax.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_TOTAL_TAX] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_penal' . "\n" .
                                         '  ON (d.id=d_cstm_case_penal.model_id AND d_cstm_case_penal.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_PENALTY] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_amt_utilize' . "\n" .
                                         '  ON (d.id=d_cstm_amt_utilize.model_id AND d_cstm_amt_utilize.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_AMOUNT_UTILIZE] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sum_idx' . "\n" .
                                         '  ON (d.id=d_cstm_sum_idx.model_id AND d_cstm_sum_idx.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_SUM_INDEX] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_min_rate' . "\n" .
                                         '  ON (d.id=d_cstm_min_rate.model_id AND d_cstm_min_rate.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_MIN_ANNUAL_RATE] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_col_val' . "\n" .
                                         '  ON (d.id=d_cstm_col_val.model_id AND d_cstm_col_val.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_COLLATERAL_VALUE] . ')' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                                         '  ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id IN ("' . implode('","', array_merge($tags_court_paid, $tags_exclude_from_ckr)) . '"))' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm1' . "\n" .
                                         '  ON (tm1.model="Document" AND tm1.model_id=d.id AND tm1.tag_id="' . DOCUMENT_TAG_CEDED . '")' . "\n" .
                                         'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm2' . "\n" .
                                         '  ON (tm2.model="Document" AND tm2.model_id=d.id AND tm2.tag_id="' . DOCUMENT_TAG_CREDIT_LINE . '")' . "\n";

                        $sql['where']   = 'WHERE d.id IN ("' . implode('","', $contracts_list) . '")';
                        $query = implode("\n", $sql);
                        $contracts_to_export_data = $registry['db']->GetAssoc($query);

                        // get the data from the group tables
                        $contracts_extra_info = array_fill_keys(array_keys($contracts_to_export_data), array('value1' => 0, 'code1' => 0, 'value2' => 0, 'code2' => 0));
                        $sql = 'SELECT d.id, d_cstm_val.value as val, d_cstm_curr.value as currency, n_cstm_code.value as code, d_cstm_kind_col.value as nom_id, tm.tag_id as accept_warranty, tm2.tag_id as other_warranty' . "\n" .
                               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_kind_col' . "\n" .
                               ' ON (d.id IN ("' . implode('","', array_keys($contracts_to_export_data)) . '") AND d.id=d_cstm_kind_col.model_id AND d_cstm_kind_col.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_KIND] . '" AND d_cstm_kind_col.value!="" AND d_cstm_kind_col.value!="0")' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                               '  ON (tm.model="Nomenclature" AND tm.model_id=d_cstm_kind_col.value AND tm.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_ACCPET)) . '"))' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm2' . "\n" .
                               '  ON (tm2.model="Nomenclature" AND tm2.model_id=d_cstm_kind_col.value AND tm2.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_OTHER)) . '"))' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_val' . "\n" .
                               ' ON (d.id=d_cstm_val.model_id AND d_cstm_val.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_VAL] . '" AND d_cstm_kind_col.num=d_cstm_val.num)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_curr' . "\n" .
                               ' ON (d.id=d_cstm_curr.model_id AND d_cstm_curr.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_CURR] . '" AND d_cstm_kind_col.num=d_cstm_curr.num)' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_code' . "\n" .
                               '  ON (d_cstm_kind_col.value=n_cstm_code.model_id AND n_cstm_code.var_id=' . $add_vars['Nomenclature_' . NOM_WARRANTY_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n";
                        $contracts_group_tbl = $registry['db']->GetAll($sql);

                        foreach ($contracts_group_tbl as $tbl_r) {
                            if (empty($tbl_r['val'])) {
                                continue;
                            }
                            $value = self::convertCurrencyValueToBGN($registry, $tbl_r['val'], $tbl_r['currency']);

                            if (!empty($tbl_r['accept_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value1']) {
                                $contracts_extra_info[$tbl_r['id']]['value1'] = $value;
                                $contracts_extra_info[$tbl_r['id']]['code1'] = $tbl_r['code'];
                            }
                            if (!empty($tbl_r['other_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value2']) {
                                $contracts_extra_info[$tbl_r['id']]['value2'] = $value;
                                $contracts_extra_info[$tbl_r['id']]['code2'] = $tbl_r['code'];
                            }
                        }

                        $projects_ids = array();
                        $projects_court_paid = array();
                        $fraud_contracts_ids = array();
                        $anulled_contracts_ids = array();

                        foreach ($contracts_to_export_data as $cte_key => $cte) {
                            // check if the data has to be taken from project
                            if (in_array($cte['status'] . '_' . $cte['substatus'], $status_court_paid) && in_array($cte['tag'], $tags_court_paid)) {
                                $projects_ids[$cte_key] = array(
                                    'month_payment'              => 0,
                                    'interst'                    => 0,
                                    'balance_before_calculation' => 0,
                                    'balance'                    => 0,
                                    'dues'                       => '0.00',
                                    'pay_left'                   => 0,
                                    'glav_over_left'             => 0,
                                    'glav_left'                  => 0,
                                    'original_amount'            => 0.00,
                                    'rec_type'                   => 6
                                );
                                $projects_court_paid[] = $cte_key;
                            } elseif (in_array($cte['status'] . '_' . $cte['substatus'], $statuses_to_include)) {
                                $projects_ids[$cte_key] = array(
                                    'month_payment'              => 0,
                                    'interst'                    => $cte['case_penal'],
                                    'balance_before_calculation' => '',
                                    'balance'                    => $cte['case_tax'],
                                    'dues'                       => $cte['case_dues'],
                                    'pay_left'                   => '',
                                    'glav_over_left'             => $cte['princ_rest'],
                                    'glav_left'                  => '',
                                    'original_amount'            => '',
                                    'rec_type'                   => ''
                                );
                            }

                            if (empty($control_row['date'])) {
                                $control_row['date'] = $cte['date'];
                                $control_row['code_declare'] = $cte['code_declare'];
                            }

                            $cucr_data[$cte_key] = array(
                                'date'                       => $cte['date'],
                                'code_declare'               => $cte['code_declare'],
                                'rec_type'                   => 5,
                                'contract_num'               => $cte['contract_num'],
                                'ucn'                        => $cte['ucn'],
                                'original_amount'            => self::convertCurrencyValueToBGN($registry, $cte['amount_utilize'], $cte['currency']),
                                'glav_left'                  => 0,
                                'glav_over_left'             => 0,
                                'pay_left'                   => 0,
                                'dues'                       => '0.00',
                                'balance'                    => 0,
                                'balance_before_calculation' => '',
                                'interst'                    => 0,
                                'offbal_dues'                => 0,
                                'cond_off'                   => 0,
                                'over_off'                   => self::convertCurrencyValueToBGN($registry, (floatval($cte['original_amount']) - floatval($cte['amount_utilize'])), $cte['currency']),
                                'tot_off_bal'                => self::convertCurrencyValueToBGN($registry, (floatval($cte['original_amount']) - floatval($cte['amount_utilize'])), $cte['currency']),
                                'deval_reason'               => '',
                                'expiration'                 => '',
                                'interest_percent'           => (floatval($cte['starting_interest_percent'])>0 ? $cte['starting_interest_percent'] : $cte['sum_idx']),
                                'month_payment'              => 0,
                                'prov_mss'                   => 0,
                                'colr'                       => $cte['collateral_val'],
                                'colt1'                      => (!empty($contracts_extra_info[$cte_key]['code1']) ? $contracts_extra_info[$cte_key]['code1'] : '000'),
                                'colr1'                      => (!empty($contracts_extra_info[$cte_key]['value1']) ? $contracts_extra_info[$cte_key]['value1'] : 0),
                                'colt3'                      => (!empty($contracts_extra_info[$cte_key]['code2']) ? $contracts_extra_info[$cte_key]['code2'] : '000'),
                                'colr3'                      => (!empty($contracts_extra_info[$cte_key]['value2']) ? $contracts_extra_info[$cte_key]['value2'] : 0),
                                'first_unpaid_payment'       => '',
                                'pay_left_base_calculated'   => 1,
                                'currency'                   => $cte['currency'],
                            );

                            if ($cte['sum_idx'] > 0) {
                                if ($cte['sum_idx'] < $cte['min_rate']) {
                                    $cucr_data[$cte_key]['interest_percent'] = $cte['min_rate'];
                                } else {
                                    $cucr_data[$cte_key]['interest_percent'] = $cte['sum_idx'];
                                }
                            } else {
                                $cucr_data[$cte_key]['interest_percent'] = $cte['starting_interest_percent'];
                            }

                            if (in_array($cte['status'] . '_' . $cte['substatus'], $status_fraud) && in_array($cte['tag'], $tags_exclude_from_ckr)) {
                                $cucr_data[$cte_key]['rec_type'] = 6;
                                $cucr_data[$cte_key]['original_amount'] = '0.00';
                                $cucr_data[$cte_key]['balance_before_calculation'] = '0.00';
                                $fraud_contracts_ids[] = $cte_key;
                                unset($cucr_data[$cte_key]['pay_left_base_calculated']);
                            }

                            if ($cte['substatus'] == DOCUMENT_STATUS_ANNULLED) {
                                $cucr_data[$cte_key]['rec_type'] = 6;
                                $cucr_data[$cte_key]['original_amount'] = '0.00';
                                $cucr_data[$cte_key]['balance_before_calculation'] = '0.00';
                                $anulled_contracts_ids[] = $cte_key;
                                unset($cucr_data[$cte_key]['pay_left_base_calculated']);
                            }

                            $cl_code = '';
                            switch ($cte['borrower_type']) {
                                case CLIENT_REPORTING_UNIT_PERSON:
                                    $cl_code = $cte['ucn'];
                                    break;
                                case CLIENT_REPORTING_UNIT_COMPANY:
                                    $cl_code = $cte['eik'];
                                    break;
                                case CLIENT_REPORTING_UNIT_FOREIGN:
                                    $cl_code = $cte['reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_foreign');
                                    break;
                                case CLIENT_REPORTING_UNIT_OTHER:
                                    $cl_code = $cte['ucn'] . $registry['translater']->translate('reports_client_reporting_unit_other');
                                    break;
                                case CLIENT_REPORTING_UNIT_FICTIVE:
                                    $cl_code = $cte['reporting_unit'] . $registry['translater']->translate('reports_client_reporting_unit_fictive');
                                    break;
                                default:
                                    break;
                            }
                            $cucr_data[$cte_key]['ucn'] = $cl_code;
                        }

                        $sql = 'SELECT gt2.*, gt2i18n.*' . "\n" .
                               'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                               'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                               ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $registry['lang'] . '")' . "\n" .
                               'WHERE gt2.model="Document" AND gt2.model_id IN ("' . implode('","', array_keys($cucr_data)) . '")' . "\n" .
                               'ORDER BY gt2.id ASC' . "\n";
                        $gt2_info = $registry['db']->GetAll($sql);

                        // define if the credit is paid
                        $totals_contracts = array();
                        $contract_non_extend_payments = array();
                        foreach ($gt2_info as $gt2) {
                            if (!in_array($gt2['model_id'], $fraud_contracts_ids) && !in_array($gt2['model_id'], $anulled_contracts_ids)) {
                                if (!isset($totals_contracts[$gt2['model_id']])) {
                                    $totals_contracts[$gt2['model_id']] = array(
                                        'left_to_pay'   => 0,
                                        'left_glav'     => 0,
                                        'left_interest' => 0,
                                        'left_penalty'  => 0,
                                        'left_lpg'      => 0,
                                        'left_zp'       => 0,
                                        'left_tax'      => 0,
                                        'left_tax_ang'  => 0,
                                    );
                                }
                                $totals_contracts[$gt2['model_id']]['left_to_pay'] += floatval($gt2['free_field5']);
                                $totals_contracts[$gt2['model_id']]['left_glav'] += floatval($gt2['average_weighted_delivery_price']);
                                $totals_contracts[$gt2['model_id']]['left_interest'] += floatval($gt2['free_field1']);
                                $totals_contracts[$gt2['model_id']]['left_penalty'] += floatval($gt2['free_field4']);
                                $totals_contracts[$gt2['model_id']]['left_lpg'] += floatval($gt2['free_text5']);
                                $totals_contracts[$gt2['model_id']]['left_tax'] += floatval($gt2['article_description']);
                                $totals_contracts[$gt2['model_id']]['left_tax_ang'] += floatval($gt2['free_field3']);
                                $totals_contracts[$gt2['model_id']]['left_zp'] += floatval($gt2['free_field2']);

                                if (!isset($contract_non_extend_payments[$gt2['model_id']])) {
                                    $contract_non_extend_payments[$gt2['model_id']] = 0;
                                }

                                if (floatval($gt2['price'])) {
                                    $contract_non_extend_payments[$gt2['model_id']]++;
                                }
                            }

                            if (!$cucr_data[$gt2['model_id']]['first_unpaid_payment'] && (floatval($gt2['average_weighted_delivery_price']) + floatval($gt2['free_field1'])) > 0) {
                                $cucr_data[$gt2['model_id']]['first_unpaid_payment'] = $gt2['article_measure_name'];
                            }

                            // calculate CUCR_OVER_OFF
                            $cucr_over_off = $cucr_data[$gt2['model_id']]['over_off'];
                            if ($contracts_to_export_data[$gt2['model_id']]['ceded'] && $contracts_to_export_data[$gt2['model_id']]['credit_line']) {
                                $cucr_over_off = self::convertCurrencyValueToBGN($registry, (floatval($contracts_to_export_data[$gt2['model_id']]['ceded_value']) - floatval($totals_contracts[$gt2['model_id']]['left_glav'])), $contracts_to_export_data[$gt2['model_id']]['currency']);
                            } elseif ($contracts_to_export_data[$gt2['model_id']]['ceded']) {
                                $cucr_over_off = self::convertCurrencyValueToBGN($registry, (floatval($contracts_to_export_data[$gt2['model_id']]['ceded_value']) - floatval($contracts_to_export_data[$gt2['model_id']]['amount_utilize'])), $contracts_to_export_data[$gt2['model_id']]['currency']);
                            } elseif ($contracts_to_export_data[$gt2['model_id']]['credit_line']) {
                                $cucr_over_off =
                                    self::convertCurrencyValueToBGN($registry, floatval($contracts_to_export_data[$gt2['model_id']]['original_amount']) - floatval($totals_contracts[$gt2['model_id']]['left_glav']), $contracts_to_export_data[$gt2['model_id']]['currency']);
                            }
                            $cucr_data[$gt2['model_id']]['over_off'] = $cucr_over_off;
                            $cucr_data[$gt2['model_id']]['tot_off_bal'] =
                                floatval($cucr_data[$gt2['model_id']]['over_off']) +
                                floatval($cucr_data[$gt2['model_id']]['offbal_dues']) +
                                floatval($cucr_data[$gt2['model_id']]['cond_off']);


                            if ($contracts_to_export_data[$gt2['model_id']]['credit_line']) {
                                $cucr_data[$gt2['model_id']]['original_amount'] =
                                    self::convertCurrencyValueToBGN($registry, $totals_contracts[$gt2['model_id']]['left_glav'], $cucr_data[$gt2['model_id']]['currency']);
                            }
                        }

                        foreach ($gt2_info as $gt2) {
                            if (!in_array($gt2['model_id'], $fraud_contracts_ids) && !in_array($gt2['model_id'], $anulled_contracts_ids)) {
                                $current_balance_before_calculation = floatval($cucr_data[$gt2['model_id']]['balance_before_calculation']);
                                if (abs($totals_contracts[$gt2['model_id']]['left_to_pay']) < 0.1 || ($contracts_to_export_data[$gt2['model_id']]['status'] . '_' . $contracts_to_export_data[$gt2['model_id']]['substatus'] == DOCUMENT_STATUS_PAID)) {
                                    // if the contract is still active complete 5 for rec type no matter of the other data. Otherwise it should be 6
                                    $cucr_data[$gt2['model_id']]['rec_type'] = preg_match('#opened#', $contracts_to_export_data[$gt2['model_id']]['status']) ? 5 : 6;
                                    $cucr_data[$gt2['model_id']]['glav_left'] = 0;
                                    $cucr_data[$gt2['model_id']]['glav_over_left'] = 0;
                                    $cucr_data[$gt2['model_id']]['pay_left'] = 0;
                                    $cucr_data[$gt2['model_id']]['interst'] = 0;
                                    $cucr_data[$gt2['model_id']]['original_amount'] = 0;
                                    unset($cucr_data[$gt2['model_id']]['pay_left_base_calculated']);
                                } else {
                                    $cucr_data[$gt2['model_id']]['rec_type'] = 5;
                                    if (isset($cucr_data[$gt2['model_id']]['pay_left_base_calculated'])) {
                                        $cucr_data[$gt2['model_id']]['pay_left'] += ($totals_contracts[$gt2['model_id']]['left_penalty'] + $totals_contracts[$gt2['model_id']]['left_lpg']);
                                        unset($cucr_data[$gt2['model_id']]['pay_left_base_calculated']);
                                    }

                                    if (General::strftime('%Y-%m-%d', $iso_cucr_month_last_date)>=$gt2['article_measure_name']) {
                                        $cucr_data[$gt2['model_id']]['balance'] += ($gt2['article_description'] +
                                                                                    $gt2['free_field3'] +
                                                                                    $gt2['free_field2']);
                                    }

                                    if ($gt2['article_measure_name'] > General::strftime('%Y-%m-%d', $iso_cucr_month_last_date)) {
                                        $cucr_data[$gt2['model_id']]['glav_left'] += $gt2['average_weighted_delivery_price'];
                                    } else {
                                        $cucr_data[$gt2['model_id']]['glav_over_left'] += $gt2['average_weighted_delivery_price'];
                                        $cucr_data[$gt2['model_id']]['pay_left'] += $gt2['free_field1'];
                                    }

                                    if ($contracts_to_export_data[$gt2['model_id']]['credit_line'] &&
                                        $contracts_to_export_data[$gt2['model_id']]['grace_period'] == DOCUMENT_GRACE_PERIOD_NO_PAYMENTS) {
                                        $cucr_data[$gt2['model_id']]['pay_left'] = 0;
                                    }
                                }

                                // check if the payment has to be taken
                                if (General::strftime('%Y-%m', $iso_cucr_month_last_date) == General::strftime('%Y-%m', strtotime($gt2['article_measure_name']))) {
                                    // calculate monthly interest per month
                                    $cucr_data[$gt2['model_id']]['month_payment'] = floatval($gt2['price']) +
                                                                                    floatval($gt2['quantity']) +
                                                                                    floatval($gt2['article_trademark']) +
                                                                                    floatval($gt2['free_text2']) +
                                                                                    floatval($gt2['free_text1']);
                                }

                                if ($contracts_to_export_data[$gt2['model_id']]['credit_line'] &&
                                    $contracts_to_export_data[$gt2['model_id']]['grace_period'] == DOCUMENT_GRACE_PERIOD_NO_PAYMENTS) {
                                    $cucr_data[$gt2['model_id']]['month_payment'] = 0;
                                }

                                if (!array_key_exists($gt2['model_id'], $projects_ids)) {
                                    $post_balance_before_calculation = sprintf('%.2f',
                                        $cucr_data[$gt2['model_id']]['glav_left'] +
                                        $cucr_data[$gt2['model_id']]['glav_over_left'] +
                                        $cucr_data[$gt2['model_id']]['pay_left'] +
                                        $cucr_data[$gt2['model_id']]['balance'] +
                                        $cucr_data[$gt2['model_id']]['interst']
                                    );
                                    $cucr_data[$gt2['model_id']]['balance_before_calculation'] = floatval($cucr_data[$gt2['model_id']]['balance_before_calculation']) + ($post_balance_before_calculation - $current_balance_before_calculation);
                                    $control_row['balance_before_calculation'] = sprintf('%.2f',
                                        ($control_row['balance_before_calculation'] +
                                            self::convertCurrencyValueToBGN($registry, ($post_balance_before_calculation - $current_balance_before_calculation), $cucr_data[$gt2['model_id']]['currency']))
                                    );
                                }
                            }

                            // calculate expiration
                            if (isset($cucr_data[$gt2['model_id']]['first_unpaid_payment'])) {
                                $datetime1 = new DateTime('now');
                                $datetime2 = new DateTime($cucr_data[$gt2['model_id']]['first_unpaid_payment']);
                                $interval = intval($datetime2->diff($datetime1)->format("%r%a"));

                                if ($interval <= 30) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 70;
                                } elseif ($interval <= 60) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 71;
                                } elseif ($interval <= 90) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 72;
                                } elseif ($interval <= 180) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 73;
                                } elseif ($interval <= 360) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 74;
                                } else {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 75;
                                }

                                if ($contracts_to_export_data[$gt2['model_id']]['credit_line'] &&
                                    $contracts_to_export_data[$gt2['model_id']]['grace_period'] == DOCUMENT_GRACE_PERIOD_NO_PAYMENTS) {
                                    $cucr_data[$gt2['model_id']]['expiration'] = 70;
                                }

                                unset($cucr_data[$gt2['model_id']]['first_unpaid_payment']);
                            }
                        }

                        /*
                         * Convert the values in BGN
                         */
                        // values to convert
                        $sums_to_convert = array('balance', 'glav_left', 'glav_over_left', 'pay_left', 'month_payment', 'balance_before_calculation');
                        foreach ($cucr_data as $contr_id => $contr_data) {
                            foreach ($sums_to_convert as $sum_to_convert) {
                                $cucr_data[$contr_id][$sum_to_convert] = sprintf('%.2f',self::convertCurrencyValueToBGN($registry, floatval($contr_data[$sum_to_convert]), $contr_data['currency']));
                            }
                        }

                        // total values to convert
                        $sums_to_convert = array('left_to_pay', 'left_glav', 'left_interest', 'left_penalty', 'left_lpg', 'left_tax', 'left_tax_ang', 'left_zp');
                        foreach ($totals_contracts as $contr_id => $tot_contr) {
                            foreach ($sums_to_convert as $sum_to_convert) {
                                $totals_contracts[$contr_id][$sum_to_convert] = sprintf('%.2f',self::convertCurrencyValueToBGN($registry, floatval($tot_contr[$sum_to_convert]), $cucr_data[$contr_id]['currency']));
                            }
                        }

                        // get the data from projects
                        if (!empty($projects_ids)) {
                            // get the project ids to get info for
                            $court_projects = array_diff(array_keys($projects_ids), $projects_court_paid);

                            if (!empty($projects_ids)) {
                                // get the needed projects data
                                $sql = 'SELECT p_contract_id.value as idx, p_principal.value as principal, p_registered_principal_left.value as registered_principal_left, p_registered_interest_left.value as registered_interest_left,' . "\n" .
                                       '       p_interest_according_left.value as interest_according_left, p_penalty_int_left.value as penalty_int_left, p_registered_principal_court.value as registered_principal_court,' . "\n" .
                                       '       p_registered_interest_court.value as registered_interest_court, p_interest_according_court.value as interest_according_court,' . "\n" .
                                       '       p_penalty_int_court.value as penalty_int_court, p_lawyer_imperatives_court.value as lawyer_imperatives_court, p_statetax_imper_court.value as statetax_imper_court,' . "\n" .
                                       '       p_imperatives_fee_court.value as imperatives_fee_court, p_enforcement_advocate_court.value as enforcement_advocate_court, p_state_enforce_court.value as state_enforce_court,' . "\n" .
                                       '       p_enforce_tax_court.value as enforce_tax_court, p_claim_jurist_court.value as claim_jurist_court, p_tax_claim_court.value as tax_claim_court, p_other_fee_court.value as other_fee_court, p_expert_tax_court.value as expert_tax_court' . "\n" .
                                       'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                       'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_contract_id' . "\n" .
                                       '  ON (p.id=p_contract_id.model_id AND p_contract_id.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_CONTRACT_NUM] . ' AND p_contract_id.value IN ("' . implode('","', $court_projects) . '") AND p.active=1 AND p.type="' . PROJECT_TYPE_PROCEEDING . '" AND p.deleted_by=0)' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_principal' . "\n" .
                                       '  ON (p.id=p_principal.model_id AND p_principal.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_PRINCIPAL] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_registered_principal_left' . "\n" .
                                       '  ON (p.id=p_registered_principal_left.model_id AND p_registered_principal_left.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_REGISTERED_PRINCIPAL_LEFT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_registered_interest_left' . "\n" .
                                       '  ON (p.id=p_registered_interest_left.model_id AND p_registered_interest_left.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_REGISTERED_INTEREST_LEFT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_interest_according_left' . "\n" .
                                       '  ON (p.id=p_interest_according_left.model_id AND p_interest_according_left.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_INTEREST_ACCORDING_LEFT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_penalty_int_left' . "\n" .
                                       '  ON (p.id=p_penalty_int_left.model_id AND p_penalty_int_left.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_PENALTY_INT_LEFT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_registered_principal_court' . "\n" .
                                       '  ON (p.id=p_registered_principal_court.model_id AND p_registered_principal_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_REGISTERED_PRINCIPAL_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_registered_interest_court' . "\n" .
                                       '  ON (p.id=p_registered_interest_court.model_id AND p_registered_interest_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_REGISTERED_INTEREST_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_interest_according_court' . "\n" .
                                       '  ON (p.id=p_interest_according_court.model_id AND p_interest_according_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_INTEREST_ACCORDING_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_penalty_int_court' . "\n" .
                                       '  ON (p.id=p_penalty_int_court.model_id AND p_penalty_int_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_PENALTY_INT_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_lawyer_imperatives_court' . "\n" .
                                       '  ON (p.id=p_lawyer_imperatives_court.model_id AND p_lawyer_imperatives_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_LAWYER_IMPERATIVES_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_statetax_imper_court' . "\n" .
                                       '  ON (p.id=p_statetax_imper_court.model_id AND p_statetax_imper_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_STATETAX_IMPER_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_imperatives_fee_court' . "\n" .
                                       '  ON (p.id=p_imperatives_fee_court.model_id AND p_imperatives_fee_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_IMPERATIVES_FEE_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_enforcement_advocate_court' . "\n" .
                                       '  ON (p.id=p_enforcement_advocate_court.model_id AND p_enforcement_advocate_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_ENFORCEMENT_ADVOCATE_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_state_enforce_court' . "\n" .
                                       '  ON (p.id=p_state_enforce_court.model_id AND p_state_enforce_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_STATE_ENFORCE_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_enforce_tax_court' . "\n" .
                                       '  ON (p.id=p_enforce_tax_court.model_id AND p_enforce_tax_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_ENFORCE_TAX_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_claim_jurist_court' . "\n" .
                                       '  ON (p.id=p_claim_jurist_court.model_id AND p_claim_jurist_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_CLAIM_JURIST_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_tax_claim_court' . "\n" .
                                       '  ON (p.id=p_tax_claim_court.model_id AND p_tax_claim_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_TAX_CLAIM_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_other_fee_court' . "\n" .
                                       '  ON (p.id=p_other_fee_court.model_id AND p_other_fee_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_OTHER_FEE_COURT] . ')' . "\n" .
                                       'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' as p_expert_tax_court' . "\n" .
                                       '  ON (p.id=p_expert_tax_court.model_id AND p_expert_tax_court.var_id=' . $add_vars['Project_' . PROJECT_TYPE_PROCEEDING . '_' . PROJECT_EXPERT_TAX_COURT] . ')' . "\n" .
                                       'WHERE p.active=1 AND p.type="' . PROJECT_TYPE_PROCEEDING . '" AND p.deleted_by=0' . "\n";
                                $projects_full_info = $registry['db']->GetAssoc($sql);

                                foreach ($projects_full_info as $proj_key => $proj_data) {
                                    if (isset($projects_ids[$proj_key])) {
                                        $projects_ids[$proj_key]['interst'] = sprintf('%.2f', round($proj_data['penalty_int_left'], 2));
                                        $projects_ids[$proj_key]['balance'] = sprintf('%.2f', round($proj_data['interest_according_left'], 2));
                                        $projects_ids[$proj_key]['pay_left'] = sprintf('%.2f', round($proj_data['registered_interest_left'], 2) + round($proj_data['interest_according_left'], 2) + round($proj_data['penalty_int_left'], 2));
                                        //$projects_ids[$proj_key]['glav_over_left'] = sprintf('%.2f', round($proj_data['registered_principal_left'], 2));
                                        $projects_ids[$proj_key]['glav_left'] = 0;
                                        //Bug 4534, comment 73
                                        //$projects_ids[$proj_key]['balance_before_calculation'] = sprintf('%.2f', round($proj_data['registered_principal_left'], 2) + round($proj_data['registered_interest_left'], 2) + round($proj_data['interest_according_left'], 2));
                                        $projects_ids[$proj_key]['balance_before_calculation'] = sprintf('%.2f',
                                            $projects_ids[$proj_key]['glav_left'] +
                                            $projects_ids[$proj_key]['glav_over_left'] +
                                            $projects_ids[$proj_key]['pay_left'] +
                                            $projects_ids[$proj_key]['balance'] +
                                            $projects_ids[$proj_key]['interst']
                                        );
                                    }
                                }
                            }

                            foreach ($projects_ids as $pcp => $project_data) {
                                if (isset($cucr_data[$pcp])) {
                                    foreach ($project_data as $proj_key => $proj_value) {
                                        if ($proj_value !== '') {
                                            $cucr_data[$pcp][$proj_key] = $proj_value;
                                            if ($proj_key == 'balance_before_calculation') {
                                                $control_row['balance_before_calculation'] = sprintf('%.2f', ($proj_value + $control_row['balance_before_calculation']));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }

                    $control_row['tot_off_bal'] = array_sum(array_column($cucr_data, 'tot_off_bal'));
                    $control_row['contract_num'] = count($cucr_data);
                    foreach ($cucr_data as $row => $data) {
                        unset($cucr_data[$row]['currency']);
                    }

                    $export_log_data = array();
                    $export_log_data[] = 'file := CUCR';
                    $export_log_data[] = 'revision := 1';
                    $export_log_data[] = 'daily_index := 0';
                    $export_log_data[] = 'export_month := ' . General::strftime('%Y-%m', $iso_cucr_month_last_date);
                    $export_log_data[] = 'exported_ids := ' . implode(',', array_keys($cucr_data));

                    $files_structure['cucr']['name'] = sprintf('CUCR_%s_%s00.CSV', COMPANY_CREDIT_KEY, General::strftime('%y%m%d', $iso_cucr_month_last_date));

                    $queries_to_execute[] = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="' . self::$report_name . '", `file_name`="' . $files_structure['cucr']['name'] . '", `log`="' . implode('\r\n', $export_log_data) . '", `exported`=NOW(), `exported_by`="' . $registry['currentUser']->get('id') . '"';

                    $cucr_data[] = $control_row;
                }
            } else {
                unset($files_structure['cucr']);
            }

            // get the data for F18_F22
            if (!empty($filters['f18_f22'])) {
                $statuses_to_include = preg_split('#\s*,\s*#', DOCUMENT_STATUS_ACTIVE);
                $statuses_to_include = array_merge($statuses_to_include, preg_split('#\s*,\s*#', DOCUMENT_STATUS_PAID));

                $f18_f22_data = array();

                $sql = array();
                $sql['select'] = 'SELECT d.id as idx, "' . General::strftime('%Y%m%d') . '" as date, "' . $borrower_type . '" as code_declare, d_cstm_min_rate.value as min_rate, ' . "\n" .
                                 '      IF((d_cstm_nefin_id.value IS NOT NULL AND d_cstm_nefin_id.value!=""), d_cstm_nefin_id.value, di18n.name) as contract_num, c.eik, c.ucn, c.is_company, d_cstm_princ_rest.value as princ_rest, ' . "\n" .
                                 '      ROUND(d_cstm_credit.value,2) as original_amount, ROUND(d_cstm_percent.value,2) as starting_interest_percent, d_cstm_tot_dues.value as case_dues, d_cstm_tot_tax.value as case_tax, ' . "\n" .
                                 '      d.status, d.substatus, ds.name as substatus_name, c_cstm.value as borrower_type, c_cstm_rep.value as reporting_unit, d_cstm_credit_curr.value as currency, d_cstm_credit_ceded.value as ceded_value, ' . "\n" .
                                 '      d_cstm_case_penal.value as case_penal, d_cstm_amt_utilize.value as amount_utilize, d_cstm_sum_idx.value as sum_idx, ' . "\n" .
                                 '      d_cstm_col_val.value as collateral_val, n1i18n.name as adv_one, n2i18n.name as adv_two' . "\n";

                $sql['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                                 '  ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                 '  ON (c.id=d.customer)' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm' . "\n" .
                                 '  ON (c.id=c_cstm.model_id AND c_cstm.var_id IN ("' . implode('","', $borrower_fields_ids) . '"))' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS c_cstm_rep' . "\n" .
                                 '  ON (c.id=c_cstm_rep.model_id AND c_cstm_rep.var_id IN ("' . implode('","', $reporting_unit_ids) . '"))' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit' . "\n" .
                                 '  ON (d.id=d_cstm_credit.model_id AND d_cstm_credit.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_curr' . "\n" .
                                 '  ON (d.id=d_cstm_credit_curr.model_id AND d_cstm_credit_curr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CURRENCY] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_ceded' . "\n" .
                                 '  ON (d.id=d_cstm_credit_ceded.model_id AND d_cstm_credit_ceded.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CEDED] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_percent' . "\n" .
                                 '  ON (d.id=d_cstm_percent.model_id AND d_cstm_percent.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_YEARLY_INTEREST] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_nefin_id' . "\n" .
                                 '  ON (d.id=d_cstm_nefin_id.model_id AND d_cstm_nefin_id.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_NEFIN_ID] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_princ_rest' . "\n" .
                                 '  ON (d.id=d_cstm_princ_rest.model_id AND d_cstm_princ_rest.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_REGISTERED_PRINCIPAL_REST] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tot_dues' . "\n" .
                                 '  ON (d.id=d_cstm_tot_dues.model_id AND d_cstm_tot_dues.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_TOTAL_DUES] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tot_tax' . "\n" .
                                 '  ON (d.id=d_cstm_tot_tax.model_id AND d_cstm_tot_tax.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_TOTAL_TAX] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_penal' . "\n" .
                                 '  ON (d.id=d_cstm_case_penal.model_id AND d_cstm_case_penal.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CASE_PENALTY] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_amt_utilize' . "\n" .
                                 '  ON (d.id=d_cstm_amt_utilize.model_id AND d_cstm_amt_utilize.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_AMOUNT_UTILIZE] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_sum_idx' . "\n" .
                                 '  ON (d.id=d_cstm_sum_idx.model_id AND d_cstm_sum_idx.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_SUM_INDEX] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_min_rate' . "\n" .
                                 '  ON (d.id=d_cstm_min_rate.model_id AND d_cstm_min_rate.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_MIN_ANNUAL_RATE] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_col_val' . "\n" .
                                 '  ON (d.id=d_cstm_col_val.model_id AND d_cstm_col_val.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_COLLATERAL_VALUE] . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_loan_adv1' . "\n" .
                                 '  ON (d.id=d_loan_adv1.model_id AND d_loan_adv1.var_id=' . (isset($add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LOAN_ADVNACE_ONE]) ? $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LOAN_ADVNACE_ONE] : '""') . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS n1i18n' . "\n" .
                                 '  ON (n1i18n.parent_id=d_loan_adv1.value AND n1i18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_loan_adv2' . "\n" .
                                 '  ON (d.id=d_loan_adv2.model_id AND d_loan_adv2.var_id=' . (isset($add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LOAN_ADVNACE_TWO]) ? $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LOAN_ADVNACE_TWO] : '""') . ')' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS n2i18n' . "\n" .
                                 '  ON (n2i18n.parent_id=d_loan_adv2.value AND n2i18n.lang="' . $registry['lang'] . '")' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                                 '  ON (d.substatus=ds.id AND ds.lang="' . $model_lang . '")' . "\n";
                $sql['where']   = 'WHERE CONCAT(d.status, "_", d.substatus) IN ("' . implode('","', $statuses_to_include) . '")';
                $query = implode("\n", $sql);
                $contracts_to_export_data = $registry['db']->GetAssoc($query);

                // get the data from the group tables
                $contracts_extra_info = array_fill_keys(array_keys($contracts_to_export_data), array('value1' => 0, 'code1' => 0, 'value2' => 0, 'code2' => 0));
                $sql = 'SELECT d.id, d_cstm_val.value as val, d_cstm_curr.value as currency, n_cstm_code.value as code, d_cstm_kind_col.value as nom_id, tm.tag_id as accept_warranty, tm2.tag_id as other_warranty' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_kind_col' . "\n" .
                       ' ON (d.id IN ("' . implode('","', array_keys($contracts_to_export_data)) . '") AND d.id=d_cstm_kind_col.model_id AND d_cstm_kind_col.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_KIND] . '" AND d_cstm_kind_col.value!="" AND d_cstm_kind_col.value!="0")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                       '  ON (tm.model="Nomenclature" AND tm.model_id=d_cstm_kind_col.value AND tm.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_ACCPET)) . '"))' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm2' . "\n" .
                       '  ON (tm2.model="Nomenclature" AND tm2.model_id=d_cstm_kind_col.value AND tm2.tag_id IN ("' . implode('","', array(NOM_WARRANTY_TAG_OTHER)) . '"))' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_val' . "\n" .
                       ' ON (d.id=d_cstm_val.model_id AND d_cstm_val.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_VAL] . '" AND d_cstm_kind_col.num=d_cstm_val.num)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_curr' . "\n" .
                       ' ON (d.id=d_cstm_curr.model_id AND d_cstm_curr.var_id="' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GR_MARKET_CURR] . '" AND d_cstm_kind_col.num=d_cstm_curr.num)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS n_cstm_code' . "\n" .
                       '  ON (d_cstm_kind_col.value=n_cstm_code.model_id AND n_cstm_code.var_id=' . $add_vars['Nomenclature_' . NOM_WARRANTY_TYPE_ID  . '_' . NOM_PRODUCT_CODE] . ')' . "\n";
                $contracts_group_tbl = $registry['db']->GetAll($sql);

                foreach ($contracts_group_tbl as $tbl_r) {
                    if (empty($tbl_r['val'])) {
                        continue;
                    }
                    $value = self::convertCurrencyValueToBGN($registry, $tbl_r['val'], $tbl_r['currency']);

                    if (!empty($tbl_r['accept_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value1']) {
                        $contracts_extra_info[$tbl_r['id']]['value1'] = $value;
                        $contracts_extra_info[$tbl_r['id']]['code1'] = $tbl_r['code'];
                    }
                    if (!empty($tbl_r['other_warranty']) && $value > $contracts_extra_info[$tbl_r['id']]['value2']) {
                        $contracts_extra_info[$tbl_r['id']]['value2'] = $value;
                        $contracts_extra_info[$tbl_r['id']]['code2'] = $tbl_r['code'];
                    }
                }

                foreach ($contracts_to_export_data as $cte_key => $cte) {
                    $f18_f22_data[$cte_key] = array(
                        'adv_one'                    => $cte['adv_one'],
                        'adv_two'                    => $cte['adv_two'],
                        'status'                     => $cte['substatus_name'],
                        'contract_num'               => $cte['contract_num'],
                        'ucn_eik'                    => ($cte['is_company'] ? $cte['eik'] : $cte['ucn']),
                        'glav_left'                  => 0,
                        'glav_over_left'             => 0,
                        'pay_left'                   => 0,
                        'balance'                    => 0,
                        'balance_before_calculation' => '',
                        'interst'                    => 0,
                        'expiration'                 => '',
                        'colr'                       => $cte['collateral_val'],
                        'colr1'                      => (!empty($contracts_extra_info[$cte_key]['value1']) ? $contracts_extra_info[$cte_key]['value1'] : 0),
                        'date'                       => $cte['date'],
                        'code_declare'               => $cte['code_declare'],
                        'rec_type'                   => 5,
                        'original_amount'            => self::convertCurrencyValueToBGN($registry, $cte['amount_utilize'], $cte['currency']),
                        'dues'                       => '0.00',
                        'offbal_dues'                => 0,
                        'cond_off'                   => 0,
                        'over_off'                   => self::convertCurrencyValueToBGN($registry, (floatval($cte['original_amount']) - floatval($cte['amount_utilize'])), $cte['currency']),
                        'tot_off_bal'                => self::convertCurrencyValueToBGN($registry, (floatval($cte['original_amount']) - floatval($cte['amount_utilize'])), $cte['currency']),
                        'deval_reason'               => '',
                        'interest_percent'           => (floatval($cte['starting_interest_percent'])>0 ? $cte['starting_interest_percent'] : $cte['sum_idx']),
                        'month_payment'              => 0,
                        'prov_mss'                   => 0,
                        'colt1'                      => (!empty($contracts_extra_info[$cte_key]['code1']) ? $contracts_extra_info[$cte_key]['code1'] : '000'),
                        'colt3'                      => (!empty($contracts_extra_info[$cte_key]['code2']) ? $contracts_extra_info[$cte_key]['code2'] : '000'),
                        'colr3'                      => (!empty($contracts_extra_info[$cte_key]['value2']) ? $contracts_extra_info[$cte_key]['value2'] : 0),
                        'first_unpaid_payment'       => '',
                        'pay_left_base_calculated'   => 1,
                        'currency'                   => $cte['currency'],
                    );

                    if ($cte['sum_idx'] > 0) {
                        if ($cte['sum_idx'] < $cte['min_rate']) {
                            $f18_f22_data[$cte_key]['interest_percent'] = $cte['min_rate'];
                        } else {
                            $f18_f22_data[$cte_key]['interest_percent'] = $cte['sum_idx'];
                        }
                    } else {
                        $f18_f22_data[$cte_key]['interest_percent'] = $cte['starting_interest_percent'];
                    }
                }

                $sql = 'SELECT gt2.*, gt2i18n.*' . "\n" .
                       'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                       ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'WHERE gt2.model="Document" AND gt2.model_id IN ("' . implode('","', array_keys($f18_f22_data)) . '")' . "\n" .
                       'ORDER BY gt2.id ASC' . "\n";
                $gt2_info = $registry['db']->GetAll($sql);

                // define if the credit is paid
                $totals_contracts = array();
                $contract_non_extend_payments = array();
                foreach ($gt2_info as $gt2) {
                    if (!isset($totals_contracts[$gt2['model_id']])) {
                        $totals_contracts[$gt2['model_id']] = array(
                            'left_to_pay' => 0,
                            'left_glav' => 0,
                            'left_interest' => 0,
                            'left_penalty' => 0,
                            'left_lpg' => 0,
                            'left_zp' => 0,
                            'left_tax' => 0,
                            'left_tax_ang' => 0,
                        );
                    }
                    $totals_contracts[$gt2['model_id']]['left_to_pay'] += floatval($gt2['free_field5']);
                    $totals_contracts[$gt2['model_id']]['left_glav'] += floatval($gt2['average_weighted_delivery_price']);
                    $totals_contracts[$gt2['model_id']]['left_interest'] += floatval($gt2['free_field1']);
                    $totals_contracts[$gt2['model_id']]['left_penalty'] += floatval($gt2['free_field4']);
                    $totals_contracts[$gt2['model_id']]['left_lpg'] += floatval($gt2['free_text5']);
                    $totals_contracts[$gt2['model_id']]['left_tax'] += floatval($gt2['article_description']);
                    $totals_contracts[$gt2['model_id']]['left_tax_ang'] += floatval($gt2['free_field3']);
                    $totals_contracts[$gt2['model_id']]['left_zp'] += floatval($gt2['free_field2']);

                    if (!isset($contract_non_extend_payments[$gt2['model_id']])) {
                        $contract_non_extend_payments[$gt2['model_id']] = 0;
                    }

                    if (floatval($gt2['price'])) {
                        $contract_non_extend_payments[$gt2['model_id']]++;
                    }

                    if (!$f18_f22_data[$gt2['model_id']]['first_unpaid_payment'] && (floatval($gt2['average_weighted_delivery_price']) + floatval($gt2['free_field1'])) > 0) {
                        $f18_f22_data[$gt2['model_id']]['first_unpaid_payment'] = $gt2['article_measure_name'];
                    }
                }

                foreach ($gt2_info as $gt2) {
                    $current_balance_before_calculation = floatval($f18_f22_data[$gt2['model_id']]['balance_before_calculation']);
                    if (abs($totals_contracts[$gt2['model_id']]['left_to_pay']) < 0.1 || ($contracts_to_export_data[$gt2['model_id']]['status'] . '_' . $contracts_to_export_data[$gt2['model_id']]['substatus'] == DOCUMENT_STATUS_PAID)) {
                        // if the contract is still active complete 5 for rec type no matter of the other data. Otherwise it should be 6
                        $f18_f22_data[$gt2['model_id']]['rec_type'] = preg_match('#opened#', $contracts_to_export_data[$gt2['model_id']]['status']) ? 5 : 6;
                        $f18_f22_data[$gt2['model_id']]['glav_left'] = 0;
                        $f18_f22_data[$gt2['model_id']]['glav_over_left'] = 0;
                        $f18_f22_data[$gt2['model_id']]['pay_left'] = 0;
                        $f18_f22_data[$gt2['model_id']]['interst'] = 0;
                        $f18_f22_data[$gt2['model_id']]['original_amount'] = 0;
                        unset($f18_f22_data[$gt2['model_id']]['pay_left_base_calculated']);
                    } else {
                        $f18_f22_data[$gt2['model_id']]['rec_type'] = 5;
                        if (isset($f18_f22_data[$gt2['model_id']]['pay_left_base_calculated'])) {
                            unset($f18_f22_data[$gt2['model_id']]['pay_left_base_calculated']);
                        }

                        if (General::strftime('%Y-%m-%d')>=$gt2['article_measure_name']) {
                            $f18_f22_data[$gt2['model_id']]['balance'] += ($gt2['article_description'] +
                                                                        $gt2['free_field3'] +
                                                                        $gt2['free_field2']);
                        }

                        if ($gt2['article_measure_name'] > General::strftime('%Y-%m-%d')) {
                            $f18_f22_data[$gt2['model_id']]['glav_left'] += $gt2['average_weighted_delivery_price'];
                        } else {
                            $f18_f22_data[$gt2['model_id']]['glav_over_left'] += $gt2['average_weighted_delivery_price'];
                            $f18_f22_data[$gt2['model_id']]['pay_left'] += $gt2['free_field1'];
                        }
                    }

                    // check if the payment has to be taken
                    if (General::strftime('%Y-%m') == General::strftime('%Y-%m', strtotime($gt2['article_measure_name']))) {
                        // calculate monthly interest per month
                        $f18_f22_data[$gt2['model_id']]['month_payment'] = floatval($gt2['price']) +
                                                                        floatval($gt2['quantity']) +
                                                                        floatval($gt2['article_trademark']) +
                                                                        floatval($gt2['free_text2']) +
                                                                        floatval($gt2['free_text1']);
                    }


                    $post_balance_before_calculation = sprintf('%.2f',
                        $f18_f22_data[$gt2['model_id']]['glav_left'] +
                        $f18_f22_data[$gt2['model_id']]['glav_over_left'] +
                        $f18_f22_data[$gt2['model_id']]['pay_left'] +
                        $f18_f22_data[$gt2['model_id']]['balance'] +
                        $f18_f22_data[$gt2['model_id']]['interst']
                    );
                    $f18_f22_data[$gt2['model_id']]['balance_before_calculation'] = floatval($f18_f22_data[$gt2['model_id']]['balance_before_calculation']) + ($post_balance_before_calculation - $current_balance_before_calculation);

                    // calculate expiration
                    if (isset($f18_f22_data[$gt2['model_id']]['first_unpaid_payment'])) {
                        $datetime1 = new DateTime('now');
                        $datetime2 = new DateTime($f18_f22_data[$gt2['model_id']]['first_unpaid_payment']);
                        $interval = intval($datetime2->diff($datetime1)->format("%r%a"));

                        if ($interval <= 30) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_30');
                        } elseif ($interval <= 90) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_90');
                        } elseif ($interval <= 180) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_180');
                        } elseif ($interval <= 365) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_365');
                        } elseif ($interval <= 730) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_730');
                        } elseif ($interval <= 1825) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_1825');
                        } elseif ($interval <= 2555) {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_2555');
                        } else {
                            $f18_f22_data[$gt2['model_id']]['expiration'] = $registry['translater']->translate('reports_expiration_more_than_2555');
                        }
                        unset($f18_f22_data[$gt2['model_id']]['first_unpaid_payment']);
                    }
                }

                /*
                 * Convert the values in BGN
                 */
                // values to convert
                $sums_to_convert = array('balance', 'glav_left', 'glav_over_left', 'pay_left', 'month_payment', 'balance_before_calculation');
                foreach ($f18_f22_data as $contr_id => $contr_data) {
                    foreach ($sums_to_convert as $sum_to_convert) {
                        $f18_f22_data[$contr_id][$sum_to_convert] = sprintf('%.2f',self::convertCurrencyValueToBGN($registry, floatval($contr_data[$sum_to_convert]), $contr_data['currency']));
                    }
                }

                $needed_data = array_keys($files_structure['f18_f22']['structure']);
                foreach ($f18_f22_data as $row => $data) {
                    foreach ($data as $var => $val) {
                        if (!in_array($var, $needed_data)) {
                            unset($f18_f22_data[$row][$var]);
                        }
                    }
                }

                $export_log_data = array();
                $export_log_data[] = 'file := F18_F22';
                $export_log_data[] = 'export_date := ' . General::strftime('%Y-%m-%d');
                $export_log_data[] = 'exported_ids := ' . implode(',', array_keys($f18_f22_data));

                $files_structure['f18_f22']['name'] = sprintf('F18_F22_%s.CSV', General::strftime('%y%m%d'));

                $queries_to_execute[] = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="' . self::$report_name . '", `file_name`="' . $files_structure['f18_f22']['name'] . '", `log`="' . implode('\r\n', $export_log_data) . '", `exported`=NOW(), `exported_by`="' . $registry['currentUser']->get('id') . '"';
            } else {
                unset($files_structure['f18_f22']);
            }

            // get the data for Wallet
            if (!empty($filters['wallet'])) {
                if (!DOCUMENT_LAST_DATE_FULL_GRATIS) {
                    unset($files_structure['wallet']['structure']['date_last_gratis']);
                }
                if (!DOCUMENT_FUNDING_SOURCE) {
                    unset($files_structure['wallet']['structure']['funding_source']);
                }

                $sql = 'SELECT d.id, di18n.name as num, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, "" as responsible, ' . "\n" .
                       '       IF (c.is_company, c.eik, c.ucn) as identifier, "" as show_status, d.status, "" as status_name, d.substatus, ds.name as substatus_name, ' . "\n" .
                       '       d.date, d_cstm_credit_curr.value as currency, ROUND(d_cstm_credit.value,2) as original_amount, d_cstm_amt_utilize.value as amount_utilize, ' . "\n" .
                       '       d_cstm_amt_nonutilize.value as amount_nonutilize, d_cstm_intr.value as interest, d_cstm_fl_intr.value as floating_interest,' . "\n" .
                       '       d_cstm_gpr.value as gpr, ni18n_pl.name as pledged_loan, ni18n.name as credit_type, fs.name as funding_source, d_cstm_ci.value as credit_reason,' . "\n" .
                       '       d_cstm_per.value as period, "" as final_deadline_date, DATE_FORMAT(d_cstm_date_gratis.value, "%d.%m.%Y") as date_last_gratis, 0 as days_overdue, 0 as uncovered_principal, 0 as other_taxes, 0 as overdue_principal, ' . "\n" .
                       '       "" as first_unpaid_payment_deadline, 0 as overdue_interest, 0 as overdue_zp, 0 as mngmt_tax, 0 as engmnt_tax, 0 as leftover_lpg, ' . "\n" .
                       '       0 as leftover_penalty_interest, 0 as not_covered_taxes,  0 as total_overdue_obligations, ' . "\n" .
                       '       0 as total_owed, 0 as total_owed_eur' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' AS di18n' . "\n" .
                       ' ON (di18n.parent_id=d.id AND di18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       ' ON (c.id=d.customer)' . "\n" .
                       'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       ' ON (ci18n.parent_id=c.id AND ci18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_STATUSES . ' AS ds' . "\n" .
                       ' ON (ds.id=d.substatus AND ds.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit' . "\n" .
                       '  ON (d.id=d_cstm_credit.model_id AND d_cstm_credit.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_credit_curr' . "\n" .
                       '  ON (d.id=d_cstm_credit_curr.model_id AND d_cstm_credit_curr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CONTRACT_GRANT_CREDIT_CURRENCY] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_amt_utilize' . "\n" .
                       '  ON (d.id=d_cstm_amt_utilize.model_id AND d_cstm_amt_utilize.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_AMOUNT_UTILIZE] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_amt_nonutilize' . "\n" .
                       '  ON (d.id=d_cstm_amt_nonutilize.model_id AND d_cstm_amt_nonutilize.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_AMOUNT_NOTUTILIZE] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_intr' . "\n" .
                       '  ON (d.id=d_cstm_intr.model_id AND d_cstm_intr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_YEARLY_INTEREST] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_fl_intr' . "\n" .
                       '  ON (d.id=d_cstm_fl_intr.model_id AND d_cstm_fl_intr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_SUM_INDEX] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_gpr' . "\n" .
                       '  ON (d.id=d_cstm_gpr.model_id AND d_cstm_gpr.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_GPR] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pl' . "\n" .
                       '  ON (d.id=d_cstm_pl.model_id AND d_cstm_pl.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_PLEDGED_LOAN] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n_pl' . "\n" .
                       ' ON (ni18n_pl.parent_id=d_cstm_pl.value AND ni18n_pl.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ct' . "\n" .
                       '  ON (d.id=d_cstm_ct.model_id AND d_cstm_ct.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CREDIT_TYPE] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                       ' ON (ni18n.parent_id=d_cstm_ct.value AND ni18n.lang="' . $registry['lang'] . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_per' . "\n" .
                       '  ON (d.id=d_cstm_per.model_id AND d_cstm_per.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CREDIT_PERIOD] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ci' . "\n" .
                       '  ON (d.id=d_cstm_ci.model_id AND d_cstm_ci.var_id=' . $add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_CREDIT_INFO] . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date_gratis' . "\n" .
                       '  ON (d.id=d_cstm_date_gratis.model_id AND d_cstm_date_gratis.var_id=' . ($add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_LAST_DATE_FULL_GRATIS] ?? '""') . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_fs' . "\n" .
                       '  ON (d.id=d_cstm_fs.model_id AND d_cstm_fs.var_id=' . ($add_vars['Document_' . DOCUMENT_CONTRACT_TYPE_ID . '_' . DOCUMENT_FUNDING_SOURCE] ?? '""') . ')' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS fs' . "\n" .
                       '  ON (fs.parent_id=d_cstm_fs.value AND fs.lang="' . $registry['lang'] . '")' . "\n" .
                       'WHERE d.type="' . DOCUMENT_CONTRACT_TYPE_ID . '" AND d.active=1 AND d.deleted_by=0' . "\n";
                $contracts_main_info = $registry['db']->GetAssoc($sql);

                array_walk($contracts_main_info, function(&$val, $key){
                    $val['responsible'] = array();
                    $val['status_sort'] = array($val['status']);
                    if (!empty($val['substatus'])) {
                        $val['status_sort'][] = $val['substatus'];
                    }
                    $val['status_sort'] = array_filter($val['status_sort']);
                    $val['status_sort'] = implode('_', $val['status_sort']);
                });

                $status_order = empty(WALLET_RESULTS_STATUS_SORT) ? array() : array_filter(preg_split('#\s*,\s*#', WALLET_RESULTS_STATUS_SORT));
                $full_status_list = array_diff(array_unique(array_column($contracts_main_info, 'status_sort')), $status_order);
                $status_order = array_merge($status_order, $full_status_list);
                uasort($contracts_main_info, function($a, $b) use ($status_order) {
                    $a_pos = array_search($a['status_sort'], $status_order);
                    $b_pos = array_search($b['status_sort'], $status_order);
                    $a_pos = ($a_pos === false) ? 1000 : $a_pos;
                    $b_pos = ($b_pos === false) ? 1000 : $b_pos;
                    return $a_pos <=> $b_pos;
                });

                // get responsible persons
                $sql = 'SELECT da.parent_id as contract, da.assigned_to as assignee, CONCAT(ui18n.firstname, " ", ui18n.lastname) as assignee_name' . "\n" .
                       'FROM ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS .  ' AS da' . "\n" .
                       'INNER JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                       ' ON (da.assigned_to=ui18n.parent_id AND da.parent_id IN ("' . implode('","', array_keys($contracts_main_info)) . '") AND da.assignments_type="' . PH_ASSIGNMENTS_RESPONSIBLE . '" AND ui18n.lang="' . $registry['lang'] . '")' . "\n";
                $contract_responsible = $registry['db']->GetAll($sql);

                foreach ($contract_responsible as $ca) {
                    if (!isset($contracts_main_info[$ca['contract']])) {
                        continue;
                    }
                    $contracts_main_info[$ca['contract']]['responsible'][$ca['assignee']] = $ca['assignee_name'];
                }

                // get the gt2 data
                $sql = 'SELECT gt2.*, gt2i18n.* FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                       'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                       ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $registry['lang'] . '" AND gt2.model="Document" AND gt2.model_id IN ("' . implode('","', array_keys($contracts_main_info)) . '"))' . "\n" .
                       'ORDER BY gt2.id ASC';
                $gt2_data = $registry['db']->GetAll($sql);

                foreach ($gt2_data as $gt2_rw) {
                    if (!isset($contracts_main_info[$gt2_rw['model_id']])) {
                        continue;
                    }
                    $contracts_main_info[$gt2_rw['model_id']]['final_deadline_date'] = $gt2_rw['article_measure_name'];

                    if ($gt2_rw['article_code']<date('Y-m-d')) {
                        $left_to_pay = floatval($gt2_rw['average_weighted_delivery_price']) +
                                       floatval($gt2_rw['free_field1']) +
                                       floatval($gt2_rw['free_field2']) +
                                       floatval($gt2_rw['article_description']) +
                                       floatval($gt2_rw['free_field3']);
                        if ($left_to_pay > 0) {
                            // first unpaid payment deadline
                            if (empty($contracts_main_info[$gt2_rw['model_id']]['first_unpaid_payment_deadline']) ||
                                $contracts_main_info[$gt2_rw['model_id']]['first_unpaid_payment_deadline']>=$gt2_rw['article_code']) {
                                $contracts_main_info[$gt2_rw['model_id']]['first_unpaid_payment_deadline'] = $gt2_rw['article_code'];
                            }
                        }

                        // overdue principal
                        $contracts_main_info[$gt2_rw['model_id']]['overdue_principal'] += floatval($gt2_rw['average_weighted_delivery_price']);

                        // overdue interest
                        $contracts_main_info[$gt2_rw['model_id']]['overdue_interest'] += floatval($gt2_rw['free_field1']);

                        // overdue zp
                        $contracts_main_info[$gt2_rw['model_id']]['overdue_zp'] += floatval($gt2_rw['free_field2']);

                        // overdue management taxes
                        $contracts_main_info[$gt2_rw['model_id']]['mngmt_tax'] += floatval($gt2_rw['article_description']);

                        // overdue taxes engagement
                        $contracts_main_info[$gt2_rw['model_id']]['engmnt_tax'] += floatval($gt2_rw['free_field3']);
                    }

                    // uncovered principal
                    if ($gt2_rw['article_code']>=date('Y-m-d')) {
                        $contracts_main_info[$gt2_rw['model_id']]['uncovered_principal'] += floatval($gt2_rw['average_weighted_delivery_price']);
                    }

                    // leftover lpg
                    $contracts_main_info[$gt2_rw['model_id']]['leftover_lpg'] += floatval($gt2_rw['free_text5']);

                    // leftover penalty interest
                    $contracts_main_info[$gt2_rw['model_id']]['leftover_penalty_interest'] += floatval($gt2_rw['free_field4']);
                }

                // prepare the information for the taxes
                $contracts_currency = array_combine(array_keys($contracts_main_info), array_column($contracts_main_info, 'currency'));
                $taxes_filters = array();
                foreach ($contracts_currency as $contract_id => $contract_currency) {
                    $taxes_filters[] = sprintf('(fir.currency="%s" AND frr.link_to="%d")', $contract_currency, $contract_id);
                }

                // prepare the sql for the taxes
                $sql = 'SELECT fir.id, fir.total_with_vat as total, SUM(fb.paid_amount) as paid, fir.date_of_payment as deadline, frr.link_to as related_doc' . "\n" .
                       'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                       'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                       ' ON (frr.parent_id=fir.id AND frr.link_to_model_name="Document" AND
                             fir.type IN ("' . implode('","', $taxes_incomes_types) . '") AND fir.active=1 AND fir.annulled_by=0 AND
                             (' . implode(' OR ', $taxes_filters) . '))' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FINANCE_BALANCE . ' AS fb' . "\n" .
                       ' ON (fb.paid_to=fir.id AND fb.paid_to_model_name="Finance_Incomes_Reason")' . "\n" .
                       'GROUP BY fir.id';
                $taxes_data = $registry['db']->GetAll($sql);

                foreach ($taxes_data as $k => $tax_dat) {
                    if (!isset($contracts_main_info[$tax_dat['related_doc']])) {
                        continue;
                    }
                    $taxes_data[$k]['deadline'] = (preg_match('#\d{4}-\d{2}-\d{2}#', $tax_dat['deadline']) ? $tax_dat['deadline'] : '');
                    $tax_k = 'not_covered_taxes';
                    if ($taxes_data[$k]['deadline']>=date('Y-m-d')) {
                        $tax_k = 'other_taxes';
                    }

                    if (!isset($contracts_main_info[$tax_dat['related_doc']][$tax_k])) {
                        $contracts_main_info[$tax_dat['related_doc']][$tax_k] = 0;
                    }
                    $contracts_main_info[$tax_dat['related_doc']][$tax_k] += (floatval($tax_dat['total'])-floatval($tax_dat['paid']));
                }


                // go through all the contracts and make the final calculations
                $total_overdue_columns = ['overdue_principal', 'overdue_interest', 'overdue_zp', 'mngmt_tax',
                                          'engmnt_tax', 'leftover_lpg', 'leftover_penalty_interest', 'not_covered_taxes'];
                $total_owed_columns = ['uncovered_principal', 'other_taxes', 'overdue_principal', 'overdue_interest', 'overdue_zp',
                                       'mngmt_tax', 'engmnt_tax', 'leftover_lpg', 'leftover_penalty_interest', 'not_covered_taxes'];
                $round_to_two_decimal_signs = ['original_amount','amount_utilize','amount_nonutilize','interest',
                                               'uncovered_principal','other_taxes','overdue_principal',
                                               'overdue_interest','overdue_zp','mngmt_tax','engmnt_tax','leftover_lpg',
                                               'leftover_penalty_interest','not_covered_taxes','total_overdue_obligations',
                                               'total_owed','total_owed_eur'];

                foreach ($contracts_main_info as $contr_id => $contract) {
                    $contracts_main_info[$contr_id]['days_overdue'] = 0;
                    if (!empty($contracts_main_info[$contr_id]['first_unpaid_payment_deadline'])) {
                        $first_unpaid_date = new DateTime($contracts_main_info[$contr_id]['first_unpaid_payment_deadline']);
                        $date_calculate = new DateTime(date('Y-m-d'));
                        $date_calculate->sub(new DateInterval('P1D'));
                        $contracts_main_info[$contr_id]['days_overdue'] = $date_calculate->diff($first_unpaid_date)->days;
                    }
                    $contracts_main_info[$contr_id]['status_name'] = $registry['translater']->translate('contract_status_' . $contract['status']);
                    $contracts_main_info[$contr_id]['show_status'] = ($contract['substatus_name'] ?: $contracts_main_info[$contr_id]['status_name']);
                    $contracts_main_info[$contr_id]['interest'] = (!empty($contract['interest']) ? $contract['interest'] : $contracts_main_info[$contr_id]['floating_interest']);
                    $contracts_main_info[$contr_id]['responsible'] = implode("\n", $contracts_main_info[$contr_id]['responsible']);
                    $contracts_main_info[$contr_id]['date'] = new DateTime($contract['date']);
                    $contracts_main_info[$contr_id]['date'] = $contracts_main_info[$contr_id]['date']->format('d.m.Y');
                    unset($contracts_main_info[$contr_id]['status']);
                    unset($contracts_main_info[$contr_id]['status_name']);
                    unset($contracts_main_info[$contr_id]['substatus']);
                    unset($contracts_main_info[$contr_id]['substatus_name']);
                    unset($contracts_main_info[$contr_id]['floating_interest']);
                    unset($contracts_main_info[$contr_id]['first_unpaid_payment_deadline']);
                    unset($contracts_main_info[$contr_id]['status_sort']);
                    if (!DOCUMENT_LAST_DATE_FULL_GRATIS) {
                        unset($contracts_main_info[$contr_id]['date_last_gratis']);
                    }
                    if (!DOCUMENT_FUNDING_SOURCE) {
                        unset($contracts_main_info[$contr_id]['funding_source']);
                    }

                    foreach ($total_overdue_columns as $toc) {
                        $contracts_main_info[$contr_id]['total_overdue_obligations'] += $contract[$toc];
                    }
                    foreach ($total_owed_columns as $toc) {
                        $contracts_main_info[$contr_id]['total_owed'] += $contract[$toc];
                    }

                    $currency_key = sprintf('%s->%s', $contract['currency'], 'EUR');
                    if (!isset(self::$currency_rates[$currency_key])) {
                        self::$currency_rates[$currency_key] = Finance_Currencies::getRate($registry, $contract['currency'], 'EUR');
                    }

                    $contracts_main_info[$contr_id]['total_owed_eur'] = floatval($contracts_main_info[$contr_id]['total_owed']) * self::$currency_rates[$currency_key];
                    foreach ($round_to_two_decimal_signs as $rnd) {
                        $contracts_main_info[$contr_id][$rnd] = sprintf('%.2f', round(floatval($contracts_main_info[$contr_id][$rnd]), 2));
                    }
                    foreach ($contracts_main_info[$contr_id] as $var => $val) {
                        if ($val === null) {
                            $contracts_main_info[$contr_id][$var] = '';
                        }
                    }
                }

                $export_log_data = array();
                $export_log_data[] = 'file := WALLET';
                $export_log_data[] = 'export_date := ' . General::strftime('%Y-%m-%d');
                $export_log_data[] = 'exported_ids := ' . implode(',', array_keys($contracts_main_info));

                $files_structure['wallet']['name'] = sprintf('WALLET_%s.CSV', General::strftime('%y%m%d'));

                $queries_to_execute[] = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="' . self::$report_name . '", `file_name`="' . $files_structure['wallet']['name'] . '", `log`="' . implode('\r\n', $export_log_data) . '", `exported`=NOW(), `exported_by`="' . $registry['currentUser']->get('id') . '"';
            } else {
                unset($files_structure['wallet']);
            }

            // start the transaction for changes
            $registry['db']->StartTrans();

            foreach ($queries_to_execute as $qte) {
                $registry['db']->Execute($qte);
            }

            if (!empty($included_customers) && $filters['borr_type_export'] != 'reexport') {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';

                $filters_cstm = array('where'      => array('c.id IN ("' . implode('","', $included_customers) . '")'),
                                      'model_lang' => $registry['lang']);
                $customers = Customers::search($registry, $filters_cstm);

                $get_old_vars = $registry->get('get_old_vars');
                $registry->set('get_old_vars', true, true);
                foreach ($customers as $customer) {
                    $customer->unsanitize();
                    $old_customer = clone $customer;
                    $old_customer->getModelTagsForAudit();
                    $old_customer->sanitize();

                    $customer->getTags();
                    $customer->deleteTags($available_tags_customers);

                    // get the updated customer
                    $filters_cstm = array('where'      => array('c.id="' . $customer->get('id') . '"'),
                                          'model_lang' => $registry['lang']);
                    $customer = Customers::searchOne($registry, $filters_cstm);
                    $customer->getModelTagsForAudit();

                    Customers_History::saveData($registry, array('model' => $customer, 'action_type' => 'tag', 'new_model' => $customer, 'old_model' => $old_customer));

                    // change last export date
                    $assoc_vars = $customer->getAssocVars();
                    $old_customer = clone $customer;
                    if ($assoc_vars[CLIENT_LAST_EXPORT]['value'] != date('Y-m-d')) {
                        $query = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) VALUES ' . "\n" .
                                 sprintf('(%d, "%d", 1, \'%s\', NOW(), %d, NOW(), %d, \'\')', $customer->get('id'), $assoc_vars[CLIENT_LAST_EXPORT]['id'], date('Y-m-d'), $registry['currentUser']->get('id'), $registry['currentUser']->get('id')) . "\n" .
                                 'ON DUPLICATE KEY UPDATE `value`="' . date('Y-m-d') . '", `modified`=NOW(), `modified_by`="' . $registry['currentUser']->get('id') . '"' . "\n";
                        $registry['db']->Execute($query);

                        // get the updated customer
                        $customer = Customers::searchOne($registry, $filters_cstm);
                        $customer->getVars();

                        Customers_History::saveData($registry, array('model' => $customer, 'action_type' => 'edit', 'new_model' => $customer, 'old_model' => $old_customer));
                    }
                }
                $registry->set('get_old_vars', $get_old_vars, true);
            }

            if (!empty($included_contracts) && $filters['cred_type_export'] != 'reexport') {
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

                $filters_docs = array('where'      => array('d.id IN ("' . implode('","', $included_contracts) . '")'),
                                      'model_lang' => $registry['lang']);
                $documents = Documents::search($registry, $filters_docs);

                foreach ($documents as $document) {
                    $document->unsanitize();
                    $old_document = clone $document;
                    $old_document->getModelTagsForAudit();
                    $old_document->sanitize();

                    $document->getTags();
                    $document->deleteTags($available_tags_documents);

                    // get the updated document
                    $filters_docs = array('where'      => array('d.id="' . $document->get('id') . '"'),
                                          'model_lang' => $registry['lang']);
                    $document = Documents::searchOne($registry, $filters_docs);
                    $document->getModelTagsForAudit();
                    $document->sanitize();

                    Documents_History::saveData($registry, array('model' => $document, 'action_type' => 'tag', 'new_model' => $document, 'old_model' => $old_document));
                }
            }

            if (!empty($cucr_data) && $filters['cucr_type_export'] != 'reexport' && (!empty($projects_court_paid) || !empty($fraud_contracts_ids))) {
                // include required clases
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

                $filters = array(
                    'where' => array(
                        'd.id IN (' . implode(',', array_merge($projects_court_paid, $fraud_contracts_ids)) . ')'
                    ),
                    'sort' => array('d.id ASC')
                );
                $contracts = Documents::search($registry, $filters);

                foreach ($contracts as $contract) {
                    // change the tags
                    // set tags for the document
                    $contract->unsanitize();
                    $old_contract = clone $contract;
                    $old_contract->getModelTagsForAudit();
                    $old_contract->sanitize();

                    $contract->getTags();
                    $contract->deleteTags(array_merge($tags_court_paid, $tags_exclude_from_ckr));

                    $filters = array('where'      => array('d.id="' . $contract->get('id') . '"'),
                                     'model_lang' => $registry['lang']);
                    $new_contract = Documents::searchOne($registry, $filters);
                    $new_contract->getModelTagsForAudit();
                    $new_contract->sanitize();

                    Documents_History::saveData($registry, array('model' => $new_contract, 'action_type' => 'tag', 'new_model' => $new_contract, 'old_model' => $old_contract));
                }
            }

            $registry['db']->CompleteTrans();

            // prepare the files to be send to the user
            if (!empty($files_structure)) {
                //create the folder
                if (!is_dir(PH_EXPORTS_CACHE_DIR)) {
                    FilesLib::createDir(PH_EXPORTS_CACHE_DIR);
                }

                // get the file to generate
                $files_to_include = array_keys($files_structure);
                $generated_file_type = reset($files_to_include);

                $archive_file_path = PH_EXPORTS_CACHE_DIR . preg_replace('#(.*)\.CSV$#', '$1', $files_structure[$generated_file_type]['name']) . '.zip';

                //manage the content
                $zip = new ZipArchive;
                $res = $zip->open($archive_file_path, ZipArchive::CREATE);

                $file_name = $files_structure[$generated_file_type]['name'];
                if ($res === TRUE) {
                    if (empty($files_structure[$generated_file_type]['content'])) {
                        $current_data_array = ${$files_structure[$generated_file_type]['data_array']};
                        $title_row = array_column($files_structure[$generated_file_type]['structure'], 'title');
                        if (in_array($generated_file_type, array('f18_f22', 'wallet'))) {
                            foreach ($title_row as $idx => $ttl) {
                                $title_row[$idx] = $registry['translater']->translate(sprintf('reports_%s_%s', $generated_file_type, strtolower($ttl)));
                            }
                        }

                        // create temp file in the memory
                        $f = fopen('php://memory', 'w+');
                        fputcsv($f, $title_row, ';', '"');
                        foreach ($current_data_array as $current_data) {
                            $current_row = array();
                            foreach ($current_data as $k => $process_data) {
                                if ($files_structure[$generated_file_type]['set_to_fixed_width']) {
                                    $current_row[] = self::setFixedTextWidth($process_data, $files_structure[$generated_file_type]['structure'][$k]['size'], '', true);
                                } else {
                                    $current_row[] = $process_data;
                                }
                            }
                            fputcsv($f, $current_row, ';');
                        }
                        rewind($f);
                        $files_structure[$generated_file_type]['content'] = stream_get_contents($f);
                        fclose($f);

                        $zip->addFromString($files_structure[$generated_file_type]['name'], iconv('UTF-8', 'windows-1251', $files_structure[$generated_file_type]['content']));
                    } else {
                        $zip->addFromString($files_structure[$generated_file_type]['name'], $files_structure[$generated_file_type]['content']);
                    }

                    if ($generated_file_type == 'cucr' && $filters['cucr_type_export'] == 'new') {
                        // write the file in the file system
                        $file_path = PH_EXPORTS_CACHE_DIR . $files_structure[$generated_file_type]['name'];
                        file_put_contents($file_path, iconv('UTF-8', 'windows-1251', $files_structure[$generated_file_type]['content']));
                    }
                }

                $zip->close();

                ob_clean();

                //send it to the user
                header('Content-Disposition: attachment; filename="' . preg_replace('#(.*)\.CSV$#', '$1', $file_name) . '.zip' . '"');
                header("Content-type: application/octet-stream;");
                header('Content-Transfer-Encoding: binary');
                header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
                header('Pragma: public');

                //send the content to the browser
                print General::fileGetContents($archive_file_path);

                //remove the archive file
                unlink($archive_file_path);

                $registry['cookie']->set($registry['request']->get('creditins_token'), 1);
            }
            exit;
        }

        public static function setFixedTextWidth($string, $size, $justification, $skip_complete_to_size = false) {
            $cut_string = trim(mb_substr($string, 0, $size, mb_detect_encoding($string)));
            $diff = $size - mb_strlen($cut_string, mb_detect_encoding($cut_string));

            if (!$skip_complete_to_size && $diff > 0) {
                if ($justification == 'right') {
                    $cut_string = str_repeat(' ', $diff) . $cut_string;
                } else {
                    $cut_string = $cut_string . str_repeat(' ', $diff);
                }
            }

            return $cut_string;
        }

        public static function convertCurrencyValueToBGN(&$registry, $value, $currency) {
            require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';

            $currency_key = sprintf('%s->%s', $currency, 'BGN');
            if (!isset(self::$currency_rates[$currency_key])) {
                self::$currency_rates[$currency_key] = Finance_Currencies::getRate($registry, $currency, 'BGN');
            }

            $value = floatval($value) * self::$currency_rates[$currency_key];

            return sprintf('%.2f', $value);
        }

        /*
         * Add months based on a date
         *
         * @param int $months - number of months since the start date
         * @param object $dateObject - the date which we will base our calculations on
         * @return object DateInterval - the interval since the passed date
         */
        function add_months($months, DateTime $dateObject) {
            $next = new DateTime($dateObject->format('Y-m-d'));
            $next->modify('last day of +' . $months . ' month');

            if($dateObject->format('d') > $next->format('d')) {
                return $dateObject->diff($next);
            } else {
                return new DateInterval('P' . $months . 'M');
            }
        }

        /*
         * Function to get and save the contracts idsthat will be used in the CUCR month file
         */
        public static function prepareCucrContractsList(&$registry, $date) {
            $export_period_start = General::strftime('%Y-%m-01 00:00', $date);
            $export_period_end = General::strftime('%Y-%m-%d 23:59', $date);

            $statuses_active = preg_split('#\s*,\s*#', DOCUMENT_STATUS_ACTIVE);
            $statuses_to_include = preg_split('#\s*,\s*#', DOCUMENT_STATUS_INCLUDE_IN_EXPORT);
            $status_court_paid = preg_split('#\s*,\s*#', DOCUMENT_STATUS_COURT_PAID);
            $status_paid = preg_split('#\s*,\s*#', DOCUMENT_STATUS_PAID);
            $status_fraud = preg_split('#\s*,\s*#', DOCUMENT_STATUS_FRAUD);
            $tags_court_paid = preg_split('#\s*,\s*#', DOCUMENT_TAG_COURT_PAID);
            $tags_exclude_from_ckr = preg_split('#\s*,\s*#', DOCUMENT_TAG_EXCLUDE_FROM_CKR);
            $tags_manual_exclude = preg_split('#\s*,\s*#', DOCUMENT_TAG_MANUAL_EXCLUDE_FROM_CKR);

            // get the needed additional var
            $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model_type`="' . DOCUMENT_CONTRACT_TYPE_ID . '" AND `model`="Document" AND `name`="' . DOCUMENT_DATE_PAYMENT . '"' . "\n";
            $date_payment_var_id = $registry['db']->GetOne($sql);

            // check all the contracts that were included in cred files
            $sql = 'SELECT `log` FROM ' . DB_TABLE_EXPORTS_LOG . ' WHERE `log` LIKE "%CRED%" AND `export_type`="' . self::$report_name . '" AND DATE_FORMAT(`exported`, "%Y-%m-%d %H:%i")<="' . $export_period_end . '" AND DATE_FORMAT(`exported`, "%Y-%m-%d %H:%i")>"' . $export_period_start . '"';
            $cred_data = $registry['db']->GetCol($sql);
            $cred_included = array();
            foreach($cred_data as $cred) {
                $log_data = preg_split('#\r\n|\r|\n#', $cred);
                $log_data = array_filter($log_data);
                foreach ($log_data as $lg) {
                    list($log_key, $log_value) = preg_split('#\s*:=\s*#', $lg);
                    if ($log_key == 'exported_ids') {
                        $cred_list = array_filter(explode(',', $log_value));
                        foreach ($cred_list as $cred_dat) {
                            list($contract_id, $contract_type) = explode('_', $cred_dat);
                            $cred_included[] = $contract_id;
                        }
                    }
                }
            }

            // define the ids of the needed contracts
            $sql = 'SELECT d.id, SUM(gt2.free_field5) as left_to_pay, CONCAT(d.status, "_", d.substatus) as status, tm.tag_id as tag' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                   '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                   '  ON (tm.model="Document" AND tm.model_id=d.id AND tm.tag_id IN ("' . implode('","', array_merge($tags_court_paid, $tags_exclude_from_ckr)) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm_excl' . "\n" .
                   '  ON (tm_excl.model="Document" AND tm_excl.model_id=d.id AND tm_excl.tag_id IN ("' . implode('","', $tags_manual_exclude) . '"))' . "\n" .
                   'WHERE d.type="' . DOCUMENT_CONTRACT_TYPE_ID . '" AND d.active=1 AND d.deleted_by=0 AND d.date<="' . General::strftime('%Y-%m-%d', $date) . '" AND (d.substatus!="' . DOCUMENT_STATUS_ANNULLED . '" OR (d.substatus="' . DOCUMENT_STATUS_ANNULLED . '" AND d.id IN("' . implode('","', $cred_included) . '"))) AND tm_excl.tag_id IS NULL' . "\n" .
                   'GROUP BY d.id' . "\n" .
                   'HAVING (status IN ("' . implode('","', $statuses_to_include) . '")) OR ' . "\n" .
                   '       (status IN ("' . implode('","', array_merge($status_court_paid, $status_fraud)) . '") AND tag IS NOT NULL) OR ' . "\n" .
                   '       (status IN ("' . implode('","', $statuses_active) . '")) OR ' . "\n" .
                   '       (status IN ("' . implode('","', $status_paid) . '") AND left_to_pay IS NOT NULL AND left_to_pay>0)' . "\n";

            // the contracts which are not finished yet
            $data = $registry['db']->GetAssoc($sql);
            $contracts_list = array_keys($data);

            $temp_date_clauses = array(
                'CONCAT(d.status, "_", d.substatus)="' . DOCUMENT_STATUS_PAID . '"',
                'DATE_FORMAT(d.status_modified, "%Y-%m-%d %H:%i")<="' . $export_period_end . '"',
                'DATE_FORMAT(d.status_modified, "%Y-%m-%d %H:%i")>"' . $export_period_start . '"'
            );

            $sql = 'SELECT d.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm' . "\n" .
                   '  ON (d.id=dcstm.model_id AND dcstm.var_id="' . $date_payment_var_id . '" AND DATE_FORMAT(dcstm.value, "%Y-%m-%d %H:%i")>"' . $export_period_start . '" AND DATE_FORMAT(dcstm.value, "%Y-%m-%d %H:%i")<="' . $export_period_end . '")' . "\n" .
                   'WHERE d.id NOT IN ("' . implode('","', $contracts_list) . '") AND d.type="' . DOCUMENT_CONTRACT_TYPE_ID . '" AND d.active=1 AND d.deleted_by=0 AND d.substatus!="' . DOCUMENT_STATUS_ANNULLED . '" AND d.date<="' . General::strftime('%Y-%m-%d', $date) . '" AND (dcstm.num IS NOT NULL OR (' . implode(' AND ', $temp_date_clauses) . '))' . "\n" .
                   'GROUP BY d.id' . "\n";

            // the contracts which are not finished yet
            $data = $registry['db']->GetCol($sql);
            $contracts_list = array_merge($contracts_list, $data);
            return array_unique($contracts_list);
        }

        /*
         * Function to get contracts ids which will be included in the CUCR
         */
        public static function getCucrContractsList(&$registry, $time) {
            $month_of_the_cucr_file = General::strftime('%Y-%m', $time);

            $sql = 'SELECT `log` FROM ' . DB_TABLE_EXPORTS_LOG . ' WHERE `export_type`="cucr_contracts_' . $month_of_the_cucr_file . '"' . "\n";
            $log = $registry['db']->GetOne($sql);
            $contracts_list = explode(',', $log);

            return $contracts_list;
        }
    }
?>
