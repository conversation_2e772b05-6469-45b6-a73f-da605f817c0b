<?php
class Ariseco_Production_On_Demand Extends Reports {
    private static $settings = [];
    private static $registry;
    private static $skusWarehousesLocationsQuantities = [];
    private static $reportFirstExecution = null;

    private static function _getData($filters, $first_execution) {
        self::$reportFirstExecution = $first_execution;

        /*
         * Get some basics
         */
        $registry = self::$registry;
        $lang = $registry['lang'];
        $db = $registry['db'];
        $settings = self::getSettings($registry);

        $results = [];
        $additional_options = [];
        if (!empty($settings['designs_tags_not'])) {
            $query = "
                SELECT n.id
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                    ON (n.type = {$settings['nom_type_designs']}
                      AND tm.model = 'Nomenclature'
                      AND tm.model_id = n.id
                      AND tm.tag_id IN ({$settings['designs_tags_not']}))";
            $tagged_designs_to_skip_list = implode(', ', $db->GetCol($query));
        }
        if (!empty($settings['parent_skus_tags_not'])) {
            $query = "
                SELECT n.id
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                    ON (n.type = {$settings['nom_type_parent_sku']}
                      AND tm.model = 'Nomenclature'
                      AND tm.model_id = n.id
                      AND tm.tag_id IN ({$settings['parent_skus_tags_not']}))";
            $tagged_parent_skus_to_skip_list = implode(', ', $db->GetCol($query));
        }
        $query = "
            SELECT name, id
              FROM _fields_meta
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_child_sku']}
                AND name IN ('model_clothes_id', 'product_id', 'product_measure', 'parentsku_name_id')";
        $child_sku_vars = $db->GetAssoc($query);
        $query = "
            SELECT name, id
              FROM _fields_meta
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_cut_sku']}
                AND name IN ('main_fabric', 'product_name', 'product_id', 'product_measure', 'material_code')";
        $cut_sku_vars = $db->GetAssoc($query);
        $query = "
            SELECT name, id
              FROM _fields_meta
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_semi_product']}
                AND name IN ('product_measure')";
        $semi_product_vars = $db->GetAssoc($query);
        $query = "
            SELECT name, id
              FROM _fields_meta
              WHERE model = 'Nomenclature'
                AND model_type = {$settings['nom_type_designs']}
                AND name IN (
                  'production_working_days',
                  'primary_atelier_prod_id',
                  'primary_atelier_prod',
                  'alt_atelier_prod_id',
                  'alt_atelier_prod',
                  'primary_atelier_cut_id',
                  'primary_atelier_cut',
                  'alt_atelier_cut_id',
                  'alt_atelier_cut')";
        $design_vars = $db->GetAssoc($query);
        $query = "
            SELECT name, id
              FROM _fields_meta
              WHERE model = 'Document'
                AND model_type = {$settings['doc_type_sales_orders_customers']}
                AND name IN ('sale_order_num', 'channel_info')";
        $sales_orders_vars = $db->GetAssoc($query);
        $sales_orders_date_filters_sql = [];
        if (!empty($filters['period_from'])) {
            $sales_orders_date_filters_sql[] = "'{$filters['period_from']}' <= d.date";
        }
        if (!empty($filters['period_to'])) {
            $sales_orders_date_filters_sql[] = "d.date <= '{$filters['period_to']}'";
        }
        $sales_orders_date_filters_sql = implode(' AND ', $sales_orders_date_filters_sql);
        $sales_orders_statuses_sql = self::_buildStatusesSql('sales_orders', DB_TABLE_DOCUMENTS_STATUSES, 'd');

        $where = [];
        if (!empty($tagged_designs_to_skip_list)) {
            $where[] = "
                (n1.id IS NULL
                OR n1.id NOT IN ({$tagged_designs_to_skip_list}))";
        }
        if (!empty($filters['atelier'])) {
            if ($filters['exclude_atelier']) {
                $where[] = "
                    (nc4.value IS NULL
                      OR nc4.value NOT IN ({$filters['atelier']})
                    AND nc5.value IS NULL
                      OR nc5.value NOT IN ({$filters['atelier']}))";
            } else {
                $where[] = "
                    (nc4.value IN ({$filters['atelier']})
                      OR nc5.value IN ({$filters['atelier']}))";
            }
        }
        $where = implode("
            AND ", $where);

        // Filter by Parent SKU
        if (!empty($filters['parent_sku']) || !empty($tagged_parent_skus_to_skip_list)) {
            $parent_sku_join_filters = [];
            if (!empty($filters['parent_sku'])) {
                $parent_sku_join_filters[] = 'nc3.value ' . ($filters['exclude_parent_sku'] ? 'NOT ' : '') . "IN ({$filters['parent_sku']})";
            }
            if (!empty($tagged_parent_skus_to_skip_list)) {
                $parent_sku_join_filters[] = "nc3.value NOT IN ({$tagged_parent_skus_to_skip_list})";
            }
            $parent_sku_join_filters = implode(' AND ', $parent_sku_join_filters);
        }

        $query = "
            SELECT gd.article_id AS child_sku_id,
                gd.article_code  AS child_sku_code,
                gdi.article_name AS child_sku_name,
                nc2.value        AS child_sku_product_measure,
                d.id             AS sales_order_id,
                dc1.value        AS sales_order_num,
                d.date           AS sales_order_date,
                d.deadline       AS sales_order_deadline,
                di.notes         AS sales_order_notes,
                ni1.name         AS channel_info,
                nc1.value        AS production_working_days,
                gd.quantity      AS ordered_quantity,
                gd.free_field3   AS picked_quantity,
                gd.free_field5   AS urgent,
                n1.id            AS design_id
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                ON (!d.deleted
                  AND d.active
                  AND d.type = {$settings['doc_type_sales_orders_customers']}" .
                  ($sales_orders_statuses_sql ? "
                  AND {$sales_orders_statuses_sql}" : '') .
                  ($sales_orders_date_filters_sql ? "
                  AND {$sales_orders_date_filters_sql}" : '') . "
                  AND gd.model = 'Document'
                  AND gd.model_id = d.id
                  AND gd.article_id != ''" .
                  (!empty($filters['child_sku']) ? "
                  AND gd.article_id " . ($filters['exclude_child_sku'] ? 'NOT ' : '') . "IN ({$filters['child_sku']})" : '') . ")
              JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                ON (!n.deleted
                  AND n.active
                  AND n.type = {$settings['nom_type_child_sku']}
                  AND n.id = gd.article_id)
              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                ON (nc.model_id = n.id
                  AND nc.var_id = {$child_sku_vars['model_clothes_id']}
                  AND nc.num = 1
                  AND nc.lang = ''" .
                  (!empty($filters['design']) ? "
                  AND nc.value " . ($filters['exclude_design'] ? 'NOT ' : '') . "IN ({$filters['design']})" : '') . ")" .
            (!empty($parent_sku_join_filters) ? "
              JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc3
                ON (nc3.model_id = n.id
                  AND nc3.var_id = {$child_sku_vars['parentsku_name_id']}
                  AND nc3.num = 1
                  AND nc3.lang = ''
                  AND {$parent_sku_join_filters})" : '') .
            (!empty($filters['atelier']) ? "
              LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc4
                ON (nc4.model_id = nc.value
                  AND nc4.var_id = {$design_vars['primary_atelier_prod_id']}
                  AND nc4.num = 1
                  AND nc4.lang = '')
              LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc5
                ON (nc5.model_id = nc.value
                  AND nc5.var_id = {$design_vars['primary_atelier_cut_id']}
                  AND nc5.num = 1
                  AND nc5.lang = '')" : '') . "
              LEFT JOIN " . DB_TABLE_DOCUMENTS_I18N . " AS di
                ON (di.parent_id = d.id
                  AND di.lang = '{$lang}')
              LEFT JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gdi
                ON (gdi.parent_id = gd.id
                  AND gdi.lang = '{$lang}')
              LEFT JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                ON (!n1.deleted
                  AND n1.active
                  AND n1.type = {$settings['nom_type_designs']}
                  AND n1.id = nc.value)
              LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                ON (dc1.model_id = d.id
                  AND dc1.var_id = {$sales_orders_vars['sale_order_num']}
                  AND dc1.num = 1
                  AND dc1.lang = '')
              " . ($filters['channel'] ? '' : "LEFT ") . "JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                ON (dc2.model_id = d.id
                  AND dc2.var_id = {$sales_orders_vars['channel_info']}
                  AND dc2.num = 1
                  AND dc2.lang = ''" .
                  ($filters['channel'] ? "
                  AND dc2.value IN ({$filters['channel']})" : '') . ")
              LEFT JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni1
                ON (ni1.parent_id = dc2.value
                  AND ni1.lang = '{$lang}')
              LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                ON (nc1.model_id = n1.id
                  AND nc1.var_id = {$design_vars['production_working_days']}
                  AND nc1.num = 1
                  AND nc1.lang = '')
              LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                ON (nc2.model_id = n.id
                  AND nc2.var_id = {$child_sku_vars['product_measure']}
                  AND nc2.num = 1
                  AND nc2.lang = '')" .
            ($where ? "
              WHERE {$where}" : '') . "
              ORDER BY child_sku_code ASC,
                sales_order_date ASC,
                sales_order_id ASC,
                gd.id ASC";
        $raw_results = $db->GetAll($query);
        if ($raw_results) {
            $child_sku_ids = [];
            $sales_orders_ids = [];
            if ($filters['categories']) {
                $designs_ids_filter = [];
            }
            foreach ($raw_results as $raw_result) {
                if (!array_key_exists($raw_result['child_sku_id'], $results)) {
                    $results[$raw_result['child_sku_id']] = [
                        'id'                           => $raw_result['child_sku_id'],
                        'code'                         => $raw_result['child_sku_code'],
                        'name'                         => $raw_result['child_sku_name'],
                        'product_measure'              => $raw_result['child_sku_product_measure'],
                        'total_ordered_quantity'       => 0,
                        'total_suggested'              => 0,
                        'total_suggested_for_cut'      => 0,
                        'total_suggested_for_semi'     => 0,
                        'shortage'                     => 0,
                        'total_remained_in_production' => 0,
                        'design_id'                    => $raw_result['design_id'],
                        'sales_orders'                 => [],
                    ];
                    $child_sku_ids[] = $raw_result['child_sku_id'];
                    if ($filters['categories'] && !empty($raw_result['design_id'])) {
                        $designs_ids_filter[$raw_result['design_id']] = $raw_result['design_id'];
                    }
                }
                if (!array_key_exists($raw_result['sales_order_id'], $results[$raw_result['child_sku_id']]['sales_orders'])) {
                    // Define deadline
                    $deadline = '';
                    if (!empty($settings['deadline_notes_contains']) && mb_strpos(mb_strtolower($raw_result['sales_order_notes']), mb_strtolower($settings['deadline_notes_contains'])) !== false) {
                        $deadline = $raw_result['sales_order_deadline'];
                    } else if (!empty($raw_result['sales_order_date']) && $raw_result['sales_order_date'] !== '0000-00-00') {
                        $deadline = Calendars_Calendar::calcDateOnWorkingDays(
                            $registry,
                            $raw_result['sales_order_date'],
                            (
                                !empty($raw_result['production_working_days']) ?
                                intval($raw_result['production_working_days']) :
                                0
                            )
                        );
                    }

                    $results[$raw_result['child_sku_id']]['sales_orders'][$raw_result['sales_order_id']] = [
                        'id'               => $raw_result['sales_order_id'],
                        'num'              => $raw_result['sales_order_num'],
                        'date'             => $raw_result['sales_order_date'],
                        'notes'            => $raw_result['sales_order_notes'],
                        'channel_info'     => $raw_result['channel_info'],
                        'deadline'         => $deadline,
                        'ordered_quantity' => 0,
                        'picked_quantity'  => 0,
                        'urgent'           => 0
                    ];
                    $sales_orders_ids[$raw_result['sales_order_id']] = $raw_result['sales_order_id'];
                }
                $results[$raw_result['child_sku_id']]['sales_orders'][$raw_result['sales_order_id']]['ordered_quantity'] += $raw_result['ordered_quantity'];
                $results[$raw_result['child_sku_id']]['sales_orders'][$raw_result['sales_order_id']]['picked_quantity'] += (!empty($raw_result['picked_quantity']) ? $raw_result['picked_quantity'] : 0);
                $results[$raw_result['child_sku_id']]['total_ordered_quantity'] += $raw_result['ordered_quantity'];
                $results[$raw_result['child_sku_id']]['sales_orders'][$raw_result['sales_order_id']]['urgent'] += (int) $raw_result['urgent'];
            }
            $child_sku_ids_list = implode(', ', $child_sku_ids);

            /*
             * Get Cut SKUs and Semi products
             */
            $query = "
                SELECT n.id   AS child_sku_id,
                    n1.id     AS id,
                    n1.code   AS code,
                    ni.name   AS name,
                    n1.type   AS type,
                    nc3.value AS product_measure
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (n.id IN ({$child_sku_ids_list})
                      AND nc.model_id = n.id
                      AND nc.var_id = {$child_sku_vars['product_id']}
                      AND nc.lang = ''
                      AND nc.value != '')
                  JOIN " . DB_TABLE_NOMENCLATURES . " AS n1
                    ON (n1.id = nc.value
                      AND n1.type IN({$settings['nom_type_cut_sku']}, {$settings['nom_type_semi_product']}))
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                    ON (ni.parent_id = n1.id
                      AND ni.lang = '{$lang}')
                  LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc3
                    ON (nc3.model_id = n1.id
                      AND nc3.var_id IN({$cut_sku_vars['product_measure']}, {$semi_product_vars['product_measure']})
                      AND nc3.num = 1
                      AND nc3.lang = '')  
                  GROUP BY child_sku_id, id
                  ORDER BY n.id, nc.num";
            $child_cut_semi_raw = $db->GetAll($query);
            $additional_options['cut_skus'] = [];
            $additional_options['semi_products'] = [];
            foreach ($child_cut_semi_raw as $child_cut_semi) {
                if ($child_cut_semi['type'] == $settings['nom_type_cut_sku']) {
                    $results[$child_cut_semi['child_sku_id']]['cut_sku_ids'][$child_cut_semi['id']] = $child_cut_semi['id'];
                    $additional_options['cut_skus'][$child_cut_semi['id']] = [
                        'id'              => $child_cut_semi['id'],
                        'code'            => $child_cut_semi['code'],
                        'name'            => $child_cut_semi['name'],
                        'type'            => $child_cut_semi['type'],
                        'product_measure' => $child_cut_semi['product_measure'],
                    ];
                } else if ($child_cut_semi['type'] == $settings['nom_type_semi_product']) {
                    $results[$child_cut_semi['child_sku_id']]['semi_product_ids'][$child_cut_semi['id']] = $child_cut_semi['id'];
                    $additional_options['semi_products'][$child_cut_semi['id']] = [
                        'id'              => $child_cut_semi['id'],
                        'code'            => $child_cut_semi['code'],
                        'name'            => $child_cut_semi['name'],
                        'type'            => $child_cut_semi['type'],
                        'product_measure' => $child_cut_semi['product_measure'],
                    ];
                }
            }

            /*
             * Prepare to filter by main fabric
             */
            $cut_sku_ids_filtered = [];
            if ($filters['main_fabric'] && !empty($additional_options['cut_skus'])) {
                $query = "
                    SELECT DISTINCT n.id
                      FROM " . DB_TABLE_NOMENCLATURES . " AS n
                      " . ($filters['exclude_main_fabric'] ? 'LEFT ' : '') . "JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                        ON (nc1.model_id = n.id
                          AND nc1.var_id = {$cut_sku_vars['main_fabric']}
                          AND nc1.lang = ''
                          AND nc1.value = 'main_fabric_yes')
                      " . ($filters['exclude_main_fabric'] ? 'LEFT ' : '') . "JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                        ON (nc2.model_id = n.id
                          AND nc2.var_id = {$cut_sku_vars['product_id']}
                          AND nc2.num = nc1.num
                          AND nc2.lang = ''" .
                          (!$filters['exclude_main_fabric'] ? "
                          AND nc2.value IN ({$filters['main_fabric']})" : '') . ")
                      WHERE n.id IN (" . implode(', ', array_keys($additional_options['cut_skus'])) . ")" .
                        ($filters['exclude_main_fabric'] ? "
                        AND (nc2.value IS NULL
                          OR nc2.value NOT IN ({$filters['main_fabric']}))" : '');
                $cut_sku_ids_filtered = $db->GetCol($query);
            }

            /*
             * Get sales orders, which has production, third party or cut orders
             */
            $sales_orders_ids_list = implode(', ', $sales_orders_ids);
            $sales_orders_production_orders_statuses_sql = self::_buildStatusesSql('sales_orders_production_orders', DB_TABLE_DOCUMENTS_STATUSES, 'd');
            $query = "
                SELECT gd.article_id AS child_sku_id,
                    gd.free_field2   AS sales_order_id
                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                  JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                    ON (!d.deleted
                      AND d.active
                      AND d.type = {$settings['doc_type_production_order']}" .
                      (!empty($sales_orders_production_orders_statuses_sql) ? "
                      AND {$sales_orders_production_orders_statuses_sql}" : '') . "
                      AND gd.model = 'Document'
                      AND gd.model_id = d.id
                      AND gd.free_field2 IN ({$sales_orders_ids_list}))";
            $child_skus_sales_orders_having_production_orders_raw = $db->GetAll($query);
            $child_skus_sales_orders_having_production_orders = [];
            foreach ($child_skus_sales_orders_having_production_orders_raw as $child_sku_sales_order) {
                $child_skus_sales_orders_having_production_orders[$child_sku_sales_order['child_sku_id']][$child_sku_sales_order['sales_order_id']] = $child_sku_sales_order['sales_order_id'];
            }
            $sales_orders_third_party_orders_statuses_sql = self::_buildStatusesSql('sales_orders_third_party_orders', DB_TABLE_DOCUMENTS_STATUSES, 'd');
            $query = "
                SELECT gd.article_id AS child_sku_id,
                    gd.free_field2   AS sales_order_id
                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                  JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                    ON (!d.deleted
                      AND d.active
                      AND d.type = {$settings['doc_type_third_party_products_delivery_order']}" .
                      (!empty($sales_orders_third_party_orders_statuses_sql) ? "
                      AND {$sales_orders_third_party_orders_statuses_sql}" : '') . "
                      AND gd.model = 'Document'
                      AND gd.model_id = d.id
                      AND gd.free_field2 IN ({$sales_orders_ids_list}))";
            $child_skus_sales_orders_having_third_party_orders_raw = $db->GetAll($query);
            $child_skus_sales_orders_having_third_party_orders = [];
            foreach ($child_skus_sales_orders_having_third_party_orders_raw as $child_sku_sales_order) {
                $child_skus_sales_orders_having_third_party_orders[$child_sku_sales_order['child_sku_id']][$child_sku_sales_order['sales_order_id']] = $child_sku_sales_order['sales_order_id'];
            }
            $sales_orders_cut_orders_statuses_sql = self::_buildStatusesSql('sales_orders_cut_orders', DB_TABLE_DOCUMENTS_STATUSES, 'd');
            $query = "
                SELECT gd.article_id AS cut_semi_id,
                    gd.free_field2   AS sales_order_id
                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                  JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                    ON (!d.deleted
                      AND d.active
                      AND d.type = {$settings['doc_type_cut_order']}" .
                      (!empty($sales_orders_cut_orders_statuses_sql) ? "
                      AND {$sales_orders_cut_orders_statuses_sql}" : '') . "
                      AND gd.model = 'Document'
                      AND gd.model_id = d.id
                      AND gd.free_field2 IN ({$sales_orders_ids_list}))";
            $cut_semi_sales_orders_having_cut_orders_raw = $db->GetAll($query);
            $cut_semi_sales_orders_having_cut_orders = [];
            foreach ($cut_semi_sales_orders_having_cut_orders_raw as $cut_semi_sales_order) {
                $cut_semi_sales_orders_having_cut_orders[$cut_semi_sales_order['cut_semi_id']][$cut_semi_sales_order['sales_order_id']] = $cut_semi_sales_order['sales_order_id'];
            }

            // Filter designs by categories
            if ($filters['categories'] && $designs_ids_filter) {
                if ($designs_ids_filter) {
                    $query = "
                        SELECT parent_id AS idx,
                            parent_id    AS design_id
                          FROM " . DB_TABLE_NOM_CATS . "
                          WHERE model = 'Nomenclature'
                            AND parent_id IN (" . implode(', ', $designs_ids_filter) . ")
                            AND cat_id IN ({$filters['categories']})
                          GROUP BY parent_id";
                    $designs_ids_filter = $db->GetAssoc($query);
                }
            }

            /**
             * Filter the results by sales orders and others
             */
            $sales_orders_ids = [];
            $designs_ids = [];
            $results_tmp = $results;
            foreach ($results_tmp as $child_sku_id => $child_sku) {
                /*
                 * Apply filtering by main fabric
                 */
                // If filtering by main fabric
                if (!empty($filters['main_fabric'])
                    // and not excluding (i.e. we're looking for ones that include such main fabrics)
                    && (!$filters['exclude_main_fabric']
                        // and there are no cut SKUs (the cut SKUs contain the main fabric)
                        && (empty($child_sku['cut_sku_ids'])
                            // or there are cut SKUs, but it's not among the ones with the given main fabrics
                            || !array_intersect($cut_sku_ids_filtered, $child_sku['cut_sku_ids']))
                        // or excluding
                        || $filters['exclude_main_fabric']
                        // and there are cut SKUs
                        && !empty($child_sku['cut_sku_ids'])
                        // which is not among the ones with the given main fabrics
                        && !array_intersect($cut_sku_ids_filtered, $child_sku['cut_sku_ids']))) {
                    // Remove the child SKU from the results
                    unset($results[$child_sku_id]);
                    continue;
                }

                // Filter by category
                if ($filters['categories']) {
                    if (empty($child_sku['design_id']) || !array_key_exists($child_sku['design_id'], $designs_ids_filter)) {
                        unset($results[$child_sku_id]);
                        continue;
                    }
                }

                foreach ($child_sku['sales_orders'] as $sales_order_id => $sales_order) {
                    if (array_key_exists($child_sku_id, $child_skus_sales_orders_having_third_party_orders)
                            && array_key_exists($sales_order_id, $child_skus_sales_orders_having_third_party_orders[$child_sku_id])) {
                        // If there is a third party order for the Child SKU,
                        // then directly unset the sales order without checking for cut order,
                        // because the conditions for having third party order are not allowing to have cut orders
                        unset($results[$child_sku_id]['sales_orders'][$sales_order_id]);
                    } else {
                        // Check if the sales order has production orders
                        if (array_key_exists($child_sku_id, $child_skus_sales_orders_having_production_orders)
                                && array_key_exists($sales_order_id, $child_skus_sales_orders_having_production_orders[$child_sku_id])) {
                            // Set flag that the sales order has production orders
                            $sales_order['has_production_orders'] = true;
                        } else if (!empty($filters['short_version']) && !empty($filters['only_production'])) {
                            // If the sales order has no production orders,
                            // and we're showing short version AND only production:
                            // remove the sales order from the results
                            // and continue to the next sales order
                            unset($results[$child_sku_id]['sales_orders'][$sales_order_id]);
                            continue;
                        }

                        // Check if the sales order has cut orders
                        if (!empty($cut_semi_sales_orders_having_cut_orders)) {
                            if (!empty($child_sku['cut_sku_ids'])) {
                                foreach ($child_sku['cut_sku_ids'] as $cutSkuId) {
                                    if (array_key_exists($cutSkuId, $cut_semi_sales_orders_having_cut_orders)
                                            && array_key_exists($sales_order_id, $cut_semi_sales_orders_having_cut_orders[$cutSkuId])) {
                                        $sales_order['has_cut_orders_for_cut_sku'] = true;
                                    }
                                }
                            }
                            if (!empty($child_sku['semi_product_ids'])) {
                                foreach ($child_sku['semi_product_ids'] as $semiProductId) {
                                    if (array_key_exists($semiProductId, $cut_semi_sales_orders_having_cut_orders)
                                            && array_key_exists($sales_order_id, $cut_semi_sales_orders_having_cut_orders[$semiProductId])) {
                                        $sales_order['has_cut_orders_for_semi_product'] = true;
                                    }
                                }
                            }
//                            foreach ($cut_semi_sales_orders_having_cut_orders as $cutSemiId => $so_having_co) {
//                                if (array_key_exists($sales_order_id, $so_having_co)) {
//                                    if (array_key_exists($cutSemiId, $additional_options['cut_skus'])) {
//                                        $sales_order['has_cut_orders_for_cut_sku'] = true;
//                                    } else if (array_key_exists($cutSemiId, $additional_options['semi_products'])) {
//                                        $sales_order['has_cut_orders_for_semi_product'] = true;
//                                    }
//                                }
//
//                                if (!empty($sales_order['has_cut_orders_for_cut_sku']) && !empty($sales_order['has_cut_orders_for_semi_product'])) {
//                                    break;
//                                }
//                            }
                        }
                        // If the sales order already has a production order
                        // and the Child SKU has no Cut SKU or Semi product
                        // (which means it can't have a cut order, so we don't check for such)
                        // or it has a Cut SKU or Semi product and there's already a cut order for it for this sales order
                        // I.e. if all is done with this sales order, then remove it
                        // if there's still something to do - leave it
                        if (!empty($sales_order['has_production_orders']) && (!array_key_exists('cut_sku_ids', $child_sku) && !array_key_exists('semi_product_ids', $child_sku) || !empty($sales_order['has_cut_orders_for_cut_sku']) || !empty($sales_order['has_cut_orders_for_semi_product']))) {
                            // Remove the sales order from the results
                            unset($results[$child_sku_id]['sales_orders'][$sales_order_id]);
                        } else if (!empty($sales_order['has_production_orders']) || !empty($sales_order['has_cut_orders_for_cut_sku']) || !empty($sales_order['has_cut_orders_for_semi_product'])) {
                            // Else if there are production orders OR cut orders (i.e. only a part of the job is done), set a marker, to hide them when showing the results
                            $results[$child_sku_id]['sales_orders'][$sales_order_id] = $sales_order;
                        }
                    }
                }
                if (empty($results[$child_sku_id]['sales_orders'])) {
                    unset($results[$child_sku_id]);
                } else {
                    $sales_orders_ids = array_merge($sales_orders_ids, array_keys($results[$child_sku_id]['sales_orders']));
                    if (!empty($child_sku['design_id'])) {
                        $designs_ids[] = $child_sku['design_id'];
                    }
                }
            }

            if ($results) {
                $sales_orders_ids_list = implode(', ', $sales_orders_ids);
                $cut_sku_ids = array_keys($additional_options['cut_skus']);
                $cut_sku_ids_list = implode(', ', $cut_sku_ids);
                $semiProductIdsList = implode(', ', array_keys($additional_options['semi_products']));

                // Count the sales orders GT2 rows
                if ($sales_orders_ids_list) {
                    $query = "
                        SELECT d.id,
                            COUNT(*) AS gt2_rows_count
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                            ON (d.id IN ({$sales_orders_ids_list})
                              AND gd.model = 'Document'
                              AND gd.model_id = d.id)
                          GROUP BY d.id";
                    $additional_options['sales_orders_gt2_rows_count'] = $db->GetAssoc($query);
                } else {
                    $additional_options['sales_orders_gt2_rows_count'] = [];
                }

                // Collect the main fabrics of all Cut SKUs
                if ($cut_sku_ids_list) {
                    $sql_filter_main_fabrics = '';
                    if ($filters['main_fabric']) {
                        if ($filters['exclude_main_fabric']) {
                            $sql_filter_main_fabrics = "nc4.value NOT IN ({$filters['main_fabric']})";
                        } else {
                            $sql_filter_main_fabrics = "nc4.value IN ({$filters['main_fabric']})";
                        }
                    }
                    $query = "
                        SELECT n.id   AS cut_sku_id,
                            nc4.value AS id,
                            nc3.value AS code,
                            nc2.value AS name
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                            ON (n.id IN ({$cut_sku_ids_list})
                              AND nc1.model_id = n.id
                              AND nc1.var_id = {$cut_sku_vars['main_fabric']}
                              AND nc1.lang = ''
                              AND nc1.value = 'main_fabric_yes')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc4
                            ON (nc4.model_id = n.id
                              AND nc4.var_id = {$cut_sku_vars['product_id']}
                              AND nc4.num = nc1.num
                              AND nc4.lang = ''" .
                              ($sql_filter_main_fabrics ? "
                              AND {$sql_filter_main_fabrics}" : '') . ")
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                            ON (nc2.model_id = n.id
                              AND nc2.var_id = {$cut_sku_vars['product_name']}
                              AND nc2.num = nc1.num
                              AND nc2.lang = '{$lang}')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc3
                            ON (nc3.model_id = n.id
                              AND nc3.var_id = {$cut_sku_vars['material_code']}
                              AND nc3.num = nc1.num
                              AND nc3.lang = '')";
                    $cut_skus_main_fabrics = $db->GetAssoc($query);
                    foreach ($cut_skus_main_fabrics as $cut_sku_id => $main_fabric) {
                        $additional_options['cut_skus'][$cut_sku_id]['main_fabric'] = $main_fabric;
                    }

                    /*
                     * Sort results by main fabric
                     * when showing short version
                     */
                    if (!empty($filters['short_version'])) {
                        $child_skus_main_fabric_codes = [];
                        foreach ($results as $child_sku_id => $child_sku) {
                            $child_skus_main_fabric_codes[$child_sku_id] = '';
                            if (!empty($child_sku['cut_sku_ids'])) {
                                foreach ($child_sku['cut_sku_ids'] as $cut_sku_id) {
                                    if (array_key_exists($cut_sku_id, $additional_options['cut_skus'])
                                            && $additional_options['cut_skus'][$cut_sku_id]['type'] == $settings['nom_type_cut_sku']
                                            && !empty($additional_options['cut_skus'][$cut_sku_id]['main_fabric'])) {
                                        $child_skus_main_fabric_codes[$child_sku_id] = $additional_options['cut_skus'][$cut_sku_id]['main_fabric']['code'];
                                    }
                                }
                            }
                        }

                        /*
                         * Sort by main fabric code
                         */
                        asort($child_skus_main_fabric_codes);
                        $results_tmp = [];
                        foreach ($child_skus_main_fabric_codes as $child_sku_id => $main_fabric_code) {
                            $results_tmp[$child_sku_id] = $results[$child_sku_id];
                        }
                        $results = $results_tmp;
                        unset($results_tmp);
                    }
                }

                $child_skus_ids_list = implode(', ', array_keys($results));
                $third_party_orders_statuses_sql = self::_buildStatusesSql('third_party_products_delivery_order', DB_TABLE_DOCUMENTS_STATUSES, 'd');
                $query = "
                    SELECT gd.article_id             AS child_sku_id,
                        d.id                         AS order_id,
                        TRIM(CONCAT(ci.name, ' ', ci.lastname))
                                                     AS customer_name,
                        gd.article_delivery_code     AS production_deadline,
                        gd.quantity - gd.free_field3 AS remained_quantity
                      FROM " . DB_TABLE_DOCUMENTS . " AS d
                      JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        ON (!d.deleted
                          AND d.active
                          AND d.type = {$settings['doc_type_third_party_products_delivery_order']}" .
                          ($third_party_orders_statuses_sql ? "
                          AND {$third_party_orders_statuses_sql}" : '') . "
                          AND gd.model = 'Document'
                          AND gd.model_id = d.id
                          AND gd.free_field5 != ''
                          AND gd.article_id IN ({$child_skus_ids_list}))
                      LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                        ON (ci.parent_id = d.customer
                          AND ci.lang = '{$lang}')
                      ORDER BY production_deadline";
                $child_skus_third_party_orders_rows = $db->GetAll($query);
                foreach ($child_skus_third_party_orders_rows as $order_row) {
                    $results[$order_row['child_sku_id']]['total_remained_in_production'] += $order_row['remained_quantity'];
                    $results[$order_row['child_sku_id']]['in_production_stock_orders'][] = $order_row;
                    if (!empty($order_row['remained_quantity'])) {
                        $results[$order_row['child_sku_id']]['has_in_production_stock_orders_remained_quantity'] = true;
                    }
                }
                $production_orders_statuses_sql = self::_buildStatusesSql('production_order', DB_TABLE_DOCUMENTS_STATUSES, 'd');
                $query = "
                    SELECT gd.article_id             AS child_sku_id,
                        d.id                         AS order_id,
                        TRIM(CONCAT(ci.name, ' ', ci.lastname))
                                                     AS customer_name,
                        gd.article_delivery_code     AS production_deadline,
                        gd.quantity - gd.free_field3 AS remained_quantity
                      FROM " . DB_TABLE_DOCUMENTS . " AS d
                      JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                        ON (!d.deleted
                          AND d.active
                          AND d.type = {$settings['doc_type_production_order']}" .
                          ($production_orders_statuses_sql ? "
                          AND {$production_orders_statuses_sql}" : '') . "
                          AND gd.model = 'Document'
                          AND gd.model_id = d.id
                          AND gd.free_field5 != ''
                          AND gd.article_id IN ({$child_skus_ids_list}))
                      LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                        ON (ci.parent_id = d.customer
                          AND ci.lang = '{$lang}')
                      ORDER BY production_deadline";
                $child_skus_production_orders_rows = $db->GetAll($query);
                foreach ($child_skus_production_orders_rows as $order_row) {
                    $results[$order_row['child_sku_id']]['total_remained_in_production'] += $order_row['remained_quantity'];
                    $results[$order_row['child_sku_id']]['in_production_stock_orders'][] = $order_row;
                    if (!empty($order_row['remained_quantity'])) {
                        $results[$order_row['child_sku_id']]['has_in_production_stock_orders_remained_quantity'] = true;
                    }
                }

                if ($designs_ids) {
                    $designs_ids_list = implode(', ', $designs_ids);

                    $query = "
                        SELECT * FROM (
                          SELECT n.id   AS design_id,
                              0         AS position,
                              nc1.value AS option_value,
                              nc2.value AS label
                            FROM " . DB_TABLE_NOMENCLATURES . " AS n
                            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                              ON (n.id IN ({$designs_ids_list})
                                AND nc1.model_id = n.id
                                AND nc1.var_id = {$design_vars['primary_atelier_prod_id']}
                                AND nc1.num = 1
                                AND nc1.lang = ''
                                AND nc1.value != '')
                            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                              ON (nc2.model_id = n.id
                                AND nc2.var_id = {$design_vars['primary_atelier_prod']}
                                AND nc2.num = nc1.num
                                AND nc2.lang = '{$lang}'
                                AND nc2.value != '')
                                
                          UNION ALL
                                
                          SELECT n.id   AS design_id,
                              nc1.num   AS position,
                              nc1.value AS option_value,
                              nc2.value AS label
                            FROM " . DB_TABLE_NOMENCLATURES . " AS n
                            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                              ON (n.id IN ({$designs_ids_list})
                                AND nc1.model_id = n.id
                                AND nc1.var_id = {$design_vars['alt_atelier_prod_id']}
                                AND nc1.lang = ''
                                AND nc1.value != '')
                            JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                              ON (nc2.model_id = n.id
                                AND nc2.var_id = {$design_vars['alt_atelier_prod']}
                                AND nc2.num = nc1.num
                                AND nc2.lang = '{$lang}'
                                AND nc2.value != '')
                        ) AS tmp
                        GROUP BY design_id, option_value
                        ORDER BY design_id, position";
                    $designs_ateliers_production = $db->GetAll($query);
                    foreach ($designs_ateliers_production as $design_atelier_production) {
                        $additional_options['designs_ateliers_production'][$design_atelier_production['design_id']][] = [
                            'option_value' => $design_atelier_production['option_value'],
                            'label'        => $design_atelier_production['label']
                        ];
                    }

                    $query = "
                        SELECT n.id   AS design_id,
                            nc1.value AS option_value,
                            nc2.value AS label
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                            ON (n.id IN ({$designs_ids_list})
                              AND nc1.model_id = n.id
                              AND nc1.var_id = {$design_vars['primary_atelier_cut_id']}
                              AND nc1.num = 1
                              AND nc1.lang = ''
                              AND nc1.value != '')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                            ON (nc2.model_id = n.id
                              AND nc2.var_id = {$design_vars['primary_atelier_cut']}
                              AND nc2.num = nc1.num
                              AND nc2.lang = '{$lang}'
                              AND nc2.value != '')
                              
                        UNION ALL
                              
                        SELECT n.id   AS design_id,
                            nc1.value AS option_value,
                            nc2.value AS label
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                            ON (n.id IN ({$designs_ids_list})
                              AND nc1.model_id = n.id
                              AND nc1.var_id = {$design_vars['alt_atelier_cut_id']}
                              AND nc1.lang = ''
                              AND nc1.value != '')
                          JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                            ON (nc2.model_id = n.id
                              AND nc2.var_id = {$design_vars['alt_atelier_cut']}
                              AND nc2.num = nc1.num
                              AND nc2.lang = '{$lang}'
                              AND nc2.value != '')";
                    $designs_ateliers_cut = $db->GetAll($query);
                    foreach ($designs_ateliers_cut as $design_atelier_cut) {
                        $additional_options['designs_ateliers_cut'][$design_atelier_cut['design_id']][] = [
                            'option_value' => $design_atelier_cut['option_value'],
                            'label'        => $design_atelier_cut['label']
                        ];
                    }
                }

                if ($cut_sku_ids_list || $semiProductIdsList) {
                    $cutSemiIdsList = implode(',', array_filter([$cut_sku_ids_list, $semiProductIdsList]));
                    $cut_orders_statuses_sql = self::_buildStatusesSql('cut_order', DB_TABLE_DOCUMENTS_STATUSES, 'd');
                    $query = "
                        SELECT gd.article_id             AS cut_semi_id,
                            n.type                       AS cut_semi_type,
                            d.id                         AS order_id,
                            TRIM(CONCAT(ci.name, ' ', ci.lastname)) 
                                                         AS customer_name,
                            gd.article_delivery_code     AS production_deadline,
                            gd.quantity - gd.free_field3 AS remained_quantity
                          FROM " . DB_TABLE_DOCUMENTS . " AS d
                          JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                            ON (!d.deleted
                              AND d.active
                              AND d.type = {$settings['doc_type_cut_order']}" .
                              ($cut_orders_statuses_sql ? "
                              AND {$cut_orders_statuses_sql}" : '') . "
                              AND gd.model = 'Document'
                              AND gd.model_id = d.id
                              AND gd.free_field5 != ''
                              AND gd.article_id IN ({$cutSemiIdsList}))
                          JOIN " . DB_TABLE_NOMENCLATURES . " AS n
                            ON (n.id = gd.article_id
                              AND n.type IN ({$settings['nom_type_cut_sku']}, {$settings['nom_type_semi_product']}))
                          LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                            ON (ci.parent_id = d.customer
                              AND ci.lang = '{$lang}')
                          ORDER BY production_deadline";
                    $cut_semi_cut_orders_rows = $db->GetAll($query);
                    foreach ($cut_semi_cut_orders_rows as $order_row) {
                        if ($order_row['cut_semi_type'] == $settings['nom_type_cut_sku']) {
                            $additional_options['cut_skus'][$order_row['cut_semi_id']]['in_cut_stock_orders'][] = $order_row;
                        } else if ($order_row['cut_semi_type'] == $settings['nom_type_semi_product']) {
                            $additional_options['semi_products'][$order_row['cut_semi_id']]['in_cut_stock_orders'][] = $order_row;
                        }
                    }

                    /*
                     * Get: Expected cut
                     */
                    if ($cut_sku_ids_list) {
                        $query = "
                            SELECT gd.article_id AS cut_sku_id,
                                gd.quantity      AS quantity,
                                d.id             AS cut_order_id,
                                d.full_num       AS cut_order_full_num
                              FROM " . DB_TABLE_DOCUMENTS . " AS d
                              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                                ON (!d.deleted
                                  AND d.active
                                  AND d.type = {$settings['doc_type_cut_order']}" .
                                  (!empty($settings['cut_orders_substatus_for_cad']) ? "
                                  AND d.substatus = {$settings['cut_orders_substatus_for_cad']}" : '') . "
                                  AND gd.model = 'Document'
                                  AND gd.model_id = d.id
                                  AND gd.article_id IN ({$cut_sku_ids_list}))
                                  AND gd.free_field5 = ''
                              GROUP BY gd.article_id";
                        $cut_skus_expected_cuts_raw = $db->GetAll($query);
                        $cut_skus_expected_cuts = [];
                        foreach ($cut_skus_expected_cuts_raw as $cut_sku_expected_cut) {
                            if (!array_key_exists($cut_sku_expected_cut['cut_sku_id'], $cut_skus_expected_cuts)) {
                                $cut_skus_expected_cuts[$cut_sku_expected_cut['cut_sku_id']] = [
                                    'total_quantity'       => 0,
                                    'cut_orders_full_nums' => [],
                                    'cut_orders_search_url' => '',
                                ];
                            }
                            $cut_skus_expected_cuts[$cut_sku_expected_cut['cut_sku_id']]['total_quantity'] += $cut_sku_expected_cut['quantity'];
                            $cut_skus_expected_cuts[$cut_sku_expected_cut['cut_sku_id']]['cut_orders_full_nums'][$cut_sku_expected_cut['cut_order_id']] = $cut_sku_expected_cut['cut_order_full_num'];
                        }
                        foreach ($cut_skus_expected_cuts as $cut_sku_id => $cut_sku_expected_cut) {
                            $cut_skus_expected_cuts[$cut_sku_id]['cut_orders_search_url'] = self::_buildDocumentsSearchURL($registry, $settings['doc_type_cut_order'], $cut_sku_expected_cut['cut_orders_full_nums']);
                        }
                        $additional_options['cut_skus_expected_cuts'] = $cut_skus_expected_cuts;

                        // Get cut availabilities
                        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses.factory.php';
                        $filters_warehouses = array(
                            'where'      => array(
                                'fwh.active = 1'
                            ),
                            'sanitize'   => true,
                            'model_lang' => $lang
                        );
                        if (!empty($settings['included_warehouses'])) {
                            $filters_warehouses['where'][] = 'fwh.id IN (' . implode(',', $settings['included_warehouses']) . ')';
                        }
                        $warehouses = Finance_Warehouses::search($registry, $filters_warehouses);
                        $waqParams = array(
                            'nom_id'      => $cut_sku_ids,
                            'force'       => true,
                            'reservation' => false,
                            'get_zeros'   => false
                        );
                        $additional_options['cut_sku_warehouses_availabilities'] = [];
                        foreach ($warehouses as $warehouse) {
                            $warehouse_quantities = $warehouse->getAvailableQuantity($waqParams);
                            foreach ($warehouse_quantities as $cut_sku_id => $cut_sku_wh_data) {
                                if (!array_key_exists($cut_sku_id, $additional_options['cut_sku_warehouses_availabilities'])) {
                                    $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id] = [
                                        'rowspan'         => 0,
                                        'total_available' => 0,
                                        'warehouses'      => []
                                    ];
                                }
                                if (!array_key_exists($warehouse->get('id'), $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses'])) {
                                    $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses'][$warehouse->get('id')] = [
                                        'name'    => $warehouse->get('name'),
                                        'batches' => []
                                    ];
                                }
                                if (!empty($cut_sku_wh_data['batch_data'])) {
                                    foreach ($cut_sku_wh_data['batch_data'] as $batch_data) {
                                        if (floatval($batch_data['quantity']) > 0) {
                                            $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses'][$warehouse->get('id')]['batches'][] = [
                                                'quantity' => $batch_data['quantity'],
                                                'code'     => $batch_data['batch_code']
                                            ];
                                            $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['total_available'] += $batch_data['quantity'];
                                        }
                                    }
                                } else {
                                    if (floatval($cut_sku_wh_data['quantity']) > 0) {
                                        // Collect quantities even if there is no batches
                                        $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses'][$warehouse->get('id')]['batches'][] = [
                                            'quantity' => $cut_sku_wh_data['quantity'],
                                            'code' => ''
                                        ];
                                        $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses']['total_available'] += $cut_sku_wh_data['quantity'];
                                    }
                                }
                                $additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['rowspan'] += count($additional_options['cut_sku_warehouses_availabilities'][$cut_sku_id]['warehouses'][$warehouse->get('id')]['batches']);
                            }
                        }
                    }
                }

                self::_loadInventory($results, $additional_options, $sales_orders_ids_list);

                foreach ($results as $child_sku_id => $child_sku) {
                    foreach ($child_sku['sales_orders'] as $sales_order_id => $sales_order) {
                        $suggested = $sales_order['ordered_quantity'] - $sales_order['picked_quantity'] - ($sales_order['from_stock'] ?? 0);
                        $suggested = ($suggested < 0 ? 0 : $suggested);
                        if (empty($sales_order['has_production_orders'])
                                && $suggested
                                && !empty($additional_options['designs_ateliers_production'][$child_sku['design_id']])) {
                            $sales_order['sewing'] = $suggested;
                        }
                        // Calculate cut only for Child SKUs which has Cut SKUs
                        if (empty($sales_order['has_cut_orders_for_cut_sku'])
                                && $suggested
                                && !empty($child_sku['cut_sku_ids'])
                                && array_intersect_key($additional_options['cut_skus'], $child_sku['cut_sku_ids'])
                                && !empty($additional_options['designs_ateliers_cut'][$child_sku['design_id']])) {
                            $sales_order['sent_for_cut'] = $suggested;
                            $child_sku['total_suggested_for_cut'] += $suggested;
                            $child_sku['has_cut'] = true;
                        }
                        // Calculate semi only for Child SKUs which has Semi products
                        if (empty($sales_order['has_cut_orders_for_semi_product'])
                                && $suggested
                                && !empty($child_sku['semi_product_ids'])
                                && array_intersect_key($additional_options['semi_products'], $child_sku['semi_product_ids'])) {
                            $sales_order['sent_for_semi'] = $suggested;
                            $child_sku['total_suggested_for_semi'] += $suggested;
                            $child_sku['has_semi'] = true;
                        }
                        $child_sku['sales_orders'][$sales_order_id] = $sales_order;
                        if (!empty($sales_order['sewing']) || !empty($sales_order['sent_for_cut']) || !empty($sales_order['sent_for_semi'])) {
                            $child_sku['total_suggested'] += $suggested;
                        }
                    }
                    if ($child_sku['total_remained_in_production']) {
                        $child_sku['shortage'] = $child_sku['total_remained_in_production'] - $child_sku['total_suggested'];
                    }
                    $results[$child_sku_id] = $child_sku;
                }

                /*
                 * Apply filters for short version and only production
                 */
                if (!empty($filters['short_version']) || !empty($filters['only_production'])) {
                    $results_tmp = $results;
                    $additional_options['main_fabrics_rowspans'] = [];
                    foreach ($results_tmp as $child_sku_id => $child_sku) {
                        // If there's any sewing or cut
                        if ($child_sku['total_suggested'] > 0) {
                            // Keep digging
                            foreach ($child_sku['sales_orders'] as $sales_order_id => $sales_order) {
                                if (!empty($filters['short_version']) && empty($sales_order['sent_for_cut']) && empty($sales_order['sent_for_semi'])
                                        || !empty($filters['only_production']) && empty($sales_order['sewing']) && empty($sales_order['sent_for_cut']) && empty($sales_order['sent_for_semi'])) {
                                    unset($results[$child_sku_id]['sales_orders'][$sales_order_id]);
                                    if (empty($results[$child_sku_id]['sales_orders'])) {
                                        unset($results[$child_sku_id]);
                                    }
                                }
                            }
                        } else {
                            // If no sewing, cut and semi
                            // then unset the entire Child SKU
                            unset($results[$child_sku_id]);
                        }

                        /*
                         * Prepare main fabrics rowspans
                         */
                        if (array_key_exists($child_sku_id, $results)) {
                            $child_sku = $results[$child_sku_id];
                            if (!empty($child_sku['cut_sku_ids'])) {
                                foreach ($child_sku['cut_sku_ids'] as $cut_sku_id) {
                                    if (array_key_exists($cut_sku_id, $additional_options['cut_skus']) && !empty($additional_options['cut_skus'][$cut_sku_id]['main_fabric'])) {
                                        $main_fabric = $additional_options['cut_skus'][$cut_sku_id]['main_fabric'];
                                        if (!array_key_exists($main_fabric['id'], $additional_options['main_fabrics_rowspans'])) {
                                            $additional_options['main_fabrics_rowspans'][$main_fabric['id']] = 0;
                                        }
                                        $additional_options['main_fabrics_rowspans'][$main_fabric['id']] += count($child_sku['sales_orders']);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        $additional_options['settings'] = $settings;

        $results['additional_options'] = $additional_options;

        return $results;
    }

    public static function buildQuery($registry, $filters = array()) {
        self::$registry = $registry;

        $results = [];

        // Check required filters
        if (empty($filters['period_from']) && empty($filters['period_to'])) {
            $registry['messages']->setError($registry['translater']->translate('error_reports_required_filters'));
        } else {
            $settings = self::getSettings($registry);

            /*
             * Process filters
             */
            $filters['channel'] = implode(', ', array_filter($filters['channel']));
            $filters['categories'] = implode(', ', array_filter($filters['categories']));
            $filters['exclude_main_fabric'] = !empty($filters['exclude_main_fabric']);
            $filters['exclude_design'] = !empty($filters['exclude_design']);
            $filters['exclude_parent_sku'] = !empty($filters['exclude_parent_sku']);
            $filters['exclude_child_sku'] = !empty($filters['exclude_child_sku']);
            $filters['exclude_atelier'] = !empty($filters['exclude_atelier']);
            $filters['main_fabric'] = implode(', ', array_filter($filters['main_fabric']));
            $filters['design'] = implode(', ', array_filter($filters['design']));
            $filters['parent_sku'] = implode(', ', array_filter($filters['parent_sku']));
            $filters['child_sku'] = implode(', ', array_filter($filters['child_sku']));
            $filters['atelier'] = implode(', ', array_filter($filters['atelier']));

            /**
             * Get data
             */
            $results = self::_getData($filters, true);

            /**
             * Get shortage data
             */
            if (empty($filters['short_version']) && !empty($settings['calc_shortage'])) {
                $results_tmp = $results;
                unset($results_tmp['additional_options']);
                $child_sku_ids = array_keys($results_tmp);
                if ($child_sku_ids) {
                    $filters['period_from'] = '';
                    $filters['period_to'] = '';
                    $filters['channel'] = '';
                    $filters['categories'] = '';
                    $filters['main_fabric'] = '';
                    $filters['design'] = '';
                    $filters['parent_sku'] = '';
                    $filters['atelier'] = '';
                    $filters['child_sku'] = implode(', ', $child_sku_ids);
                    $results['additional_options']['shortage_results'] = self::_getData($filters, false);
                }
            }
        }

        $registry->set('hide_export_button', true, true);

        if (!empty($filters['paginate'])) {
            $results = array($results, 0);
        }

        return $results;
    }

    public static function getSettings($registry) {
        if (empty(self::$settings)) {
            $settings = self::getReportSettings($registry);
            $settings['filter_short_version_default_checked_users'] = preg_split('/\s*,\s*/', $settings['filter_short_version_default_checked_users']);
            $settings['filter_only_production_default_checked_users'] = preg_split('/\s*,\s*/', $settings['filter_only_production_default_checked_users']);
            $settings['included_warehouses'] = preg_split('/\s*,\s*/', $settings['included_warehouses']);

            self::$settings = $settings;
        }

        return self::$settings;
    }

    /**
     * This is used to build an SQL for statuses depending on status settings
     *
     * @param $settings_prefix     - the status settings prefix
     * @param $statuses_table_name - the statuses table name in which to check the statuses
     * @param $sql_table_alias     - the statuses table alias in which to check the statuses
     *
     * @return array|string
     */
    private static function _buildStatusesSql($settings_prefix, $statuses_table_name, $sql_table_alias) {
        $settings = self::getSettings(self::$registry);
        foreach ($settings as $setting_name => $setting_value) {
            if (preg_match('/^.+_for_sql$/', $setting_name)) {
                $settings[$setting_name] = preg_split('/\s*,\s*/', $setting_value, -1, PREG_SPLIT_NO_EMPTY);
            }
        }

        $statuses = [];
        if (!empty($settings[$settings_prefix . '_statuses_for_sql'])) {
            $statuses = $settings[$settings_prefix . '_statuses_for_sql'];
        }
        if (!empty($settings[$settings_prefix . '_substatuses_for_sql'])) {
            $query = "
                SELECT status
                  FROM {$statuses_table_name}
                  WHERE id IN (" . implode(", ", $settings[$settings_prefix . '_substatuses_for_sql']) . ")";
            $substatuses_statuses = self::$registry['db']->GetCol($query);
            $statuses = array_diff($statuses, $substatuses_statuses);
        }
        $statuses_not = [];
        if (!empty($settings[$settings_prefix . '_statuses_not_for_sql'])) {
            $statuses_not = $settings[$settings_prefix . '_statuses_not_for_sql'];
        }
        if (!empty($settings[$settings_prefix . '_substatuses_not_for_sql'])) {
            $query = "
                SELECT status
                  FROM {$statuses_table_name}
                  WHERE id IN (" . implode(", ", $settings[$settings_prefix . '_substatuses_not_for_sql']) . ")";
            $substatuses_not_statuses = self::$registry['db']->GetCol($query);
            $statuses_not = array_diff($statuses_not, $substatuses_not_statuses);
        }
        $statuses_sql = [];
        if (!empty($statuses)) {
            $statuses_sql[] = "{$sql_table_alias}.status IN ('" . implode("', '", $statuses) . "')";
        }
        if (!empty($settings[$settings_prefix . '_substatuses_for_sql'])) {
            $statuses_sql[] = "{$sql_table_alias}.substatus IN (" . implode(", ", $settings[$settings_prefix . '_substatuses_for_sql']) . ")";
        }
        $statuses_sql = implode(' OR ', $statuses_sql);
        $statuses_not_sql = [];
        if (!empty($statuses_not)) {
            $statuses_not_sql[] = "{$sql_table_alias}.status NOT IN ('" . implode("', '", $statuses_not) . "')";
        }
        if (!empty($settings[$settings_prefix . '_substatuses_not_for_sql'])) {
            $statuses_not_sql[] = "{$sql_table_alias}.substatus NOT IN (" . implode(", ", $settings[$settings_prefix . '_substatuses_not_for_sql']) . ")";
        }
        $statuses_not_sql = implode(' AND ', $statuses_not_sql);
        $statuses_and_not_statuses_sql = [];
        if ($statuses_sql) {
            $statuses_and_not_statuses_sql[] = $statuses_sql;
        }
        if ($statuses_not_sql) {
            $statuses_and_not_statuses_sql[] = $statuses_not_sql;
        }
        if (!empty($statuses_and_not_statuses_sql)) {
            $statuses_and_not_statuses_sql = '(' . implode(') AND (', $statuses_and_not_statuses_sql) . ')';
        } else {
            $statuses_and_not_statuses_sql = '';
        }

        return $statuses_and_not_statuses_sql;
    }

    public function _requestSkuVault($endpoint_address, $action = 'GET', $post_fields = []) {
        $settings = self::getSettings(self::$registry);

        $post_fields_json = json_encode($post_fields);

        $url = $settings['skuvault_host'] . $endpoint_address;
        $ch = curl_init($url);
        curl_setopt( $ch, CURLOPT_HTTPHEADER,
            [
                'Content-Type: application/json',
                'Accept: application/json',
                ('Content-Length: ' . mb_strlen($post_fields_json)),
            ]
        );
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, $action);
        if ($post_fields_json) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields_json);
        }
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
        $response = curl_exec($ch);
        if (is_string($response)) {
            $response = json_decode($response);
        }
        $result = [
            'curl_error_number' => curl_errno($ch),
            'curl_error'        => curl_error($ch),
            'response'          => $response,
        ];
        curl_close($ch);

        return $result;
    }

    private function _getSkuVaultTenantToken() {
        return self::$registry['session']->get('tenant_token', 'report_ariseco_production_on_demand');
    }

    private function _getSkuVaultUserToken() {
        return self::$registry['session']->get('user_token', 'report_ariseco_production_on_demand');
    }

    /**
     * Get the tokens into the session
     *
     * @param bool $force - Refresh the tokens from Sku Vault
     *
     * @return bool - true if they were taken successfully
     */
    private function _prepareSkuVaultTokens($force = false) {
        if (!$force
                && self::$registry['session']->get('tenant_token', 'report_ariseco_production_on_demand')
                && self::$registry['session']->get('user_token', 'report_ariseco_production_on_demand')) {
            return true;
        }

        self::$registry['session']->remove('tenant_token', 'report_ariseco_production_on_demand');
        self::$registry['session']->remove('user_token', 'report_ariseco_production_on_demand');

        $settings = self::getSettings(self::$registry);

        $user = $settings['skuvault_user'];
        $pass = General::decrypt($settings['skuvault_pass'], $user, 'xtea');
        $data = [
            'Email' => $user,
            'Password' => $pass,
        ];
        $skuvault_result = self::_requestSkuVault('/gettokens', 'POST', $data);

        if (empty($skuvault_result['curl_error'])
                && !empty($skuvault_result['response']->TenantToken)
                && !empty($skuvault_result['response']->UserToken)) {
            $tenantToken = $skuvault_result['response']->TenantToken;
            $userToken = $skuvault_result['response']->UserToken;
            self::$registry['session']->set('tenant_token', $tenantToken, 'report_ariseco_production_on_demand', true);
            self::$registry['session']->set('user_token', $userToken, 'report_ariseco_production_on_demand', true);
        } else {
            return false;
        }

        return true;
    }

    // TODO: Execute the crontab automation which updates the picked quantities from the Sku Vault system
    private static function _updatePickedQuantities() {
        
    }

    private static function _getSkusWarehousesLocationsQuantities($sku_codes) {
        $result = [
            'error'    => '',
            'warnings' => [],
            'data'     => []
        ];

        if (!self::_prepareSkuVaultTokens()) {
            $result['error'] = 'token';
        } else {
            $sku_vault_data = [
                'IsReturnByCodes' => false,
                // This is the maximum page size
                'PageSize'        => 10000,
                'TenantToken'     => self::_getSkuVaultTenantToken(),
                'UserToken'       => self::_getSkuVaultUserToken()

            ];

            // Split the sku codes to chunks by 10000, because this is the maximum page size of Sku Vault
            $sku_codes_chunks = array_chunk($sku_codes, $sku_vault_data['PageSize']);

            // Extend server limits
            set_time_limit(0);
            ini_set('memory_limit', '2048M');

            foreach ($sku_codes_chunks as $chunk_index => $sku_codes_chunk) {
                $sku_vault_data['PageNumber'] = $chunk_index;
                $sku_vault_data['ProductSKUs'] = $sku_codes_chunk;
                $endpoint_address = '/inventory/getInventoryByLocation';
                $action = 'POST';

                // Try to get the inventory
                $sku_vault_result = self::_requestSkuVault($endpoint_address, $action, $sku_vault_data);
                // If there's a problem with the tokens
                if (!empty($sku_vault_result['response']->ResponseStatus->ErrorCode)
                        && in_array($sku_vault_result['response']->ResponseStatus->ErrorCode, ['Tenant not found', 'User not found'])) {
                    // Refresh the tokens, because they might be expired (we didn't saw them expired for now)
                    if (!self::_prepareSkuVaultTokens(true)) {
                        // And if there's still a problem with them, set an error
                        $result['error'] = 'token';
                    } else  {
                        // Try again to get the inventory, with the refreshed tokens
                        $sku_vault_data['TenantToken'] = self::_getSkuVaultTenantToken();
                        $sku_vault_data['UserToken'] = self::_getSkuVaultUserToken();
                        $sku_vault_result = self::_requestSkuVault($endpoint_address, $action, $sku_vault_data);
                    }
                }

                // If we reached the timeout
                if (!empty($sku_vault_result['response']->ResponseStatus->Message) && $sku_vault_result['response']->ResponseStatus->Message == 'Too many requests') {
                    // Then the user should wait
                    $result['error'] = 'timeout';
                } else if (!empty($sku_vault_result['curl_error']) || empty($sku_vault_result['response']->Items)) {
                    // There's some problem
                    $result['error'] = 'failed';
                }

                // If there`re SkuVault errors, set them as warnings
                if (!empty($sku_vault_result['response']->Errors)) {
                    $result['warnings'] = $sku_vault_result['response']->Errors;
                }

                // Process the result items
                if (!empty($sku_vault_result['response']->Items)) {
                    foreach ($sku_vault_result['response']->Items as $sku_code => $sku_warehouses_locations) {
                        foreach ($sku_warehouses_locations as $warehouse_location) {
                            if (!array_key_exists($sku_code, $result['data'])) {
                                $result['data'][$sku_code] = [
                                    'total_quantity'       => 0,
                                    'locations_quantities' => [],
                                ];
                            }
                            $result['data'][$sku_code]['locations_quantities'][] = [
                                'location' => "{$warehouse_location->WarehouseCode}, {$warehouse_location->LocationCode}",
                                'quantity' => $warehouse_location->Quantity
                            ];
                            $result['data'][$sku_code]['total_quantity'] += $warehouse_location->Quantity;
                        }
                    }
                }

                // It here's an error, stop processing the chunks
                if ($result['error']) {
                    break;
                }
            }
        }

        return $result;
    }

    private static function _loadInventory(&$results, &$additional_options, $sales_orders_ids_list) {
        if (empty($sales_orders_ids_list)) {
            return true;
        }

        $registry = self::$registry;
        
        $settings = self::getSettings($registry);

        // Get sales orders data
        $query = "
            SELECT d.id         AS order_id,
                d.date          AS order_date,
                gd.article_id   AS child_sku_id,
                gd.article_code AS child_sku_code,
                gd.quantity     AS ordered_quantity,
                gd.free_field3  AS picked_quantity,
                dc1.value       AS channel_id
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_GT2_DETAILS . " AS gd
                ON (d.id IN ({$sales_orders_ids_list})
                  AND gd.model = 'Document'
                  AND gd.model_id = d.id
                  AND gd.article_id != '')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                ON (fm1.model = 'Document'
                  AND fm1.model_type = d.type
                  AND fm1.name = 'channel_info')
              LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                ON (dc1.model_id = d.id
                  AND dc1.var_id = fm1.id
                  AND dc1.num = 1
                  AND dc1.lang = '')";
        $sales_orders_gt2_rows = $registry['db']->GetAll($query);

        $child_sku_codes = [];
        $sales_orders = [];
        $sales_orders_ids_sorted = [];
        $sales_orders_total_quantities = [];
        $sales_orders_order_dates = [];
        $sales_orders_channel_positions = [];

        foreach ($sales_orders_gt2_rows as $row) {
            // If there's no such combination of sales order and Child SKU
            if (!array_key_exists($row['child_sku_id'], $results) || !array_key_exists($row['order_id'], $results[$row['child_sku_id']]['sales_orders'])) {
                // Skip it
                continue;
            }

            $child_sku_codes[$row['child_sku_code']] = $row['child_sku_code'];

            if (!array_key_exists($row['order_id'], $sales_orders)) {
                $sales_orders_ids_sorted[$row['order_id']] = $row['order_id'];
                $sales_orders_total_quantities[$row['order_id']] = 0;
                $sales_orders_order_dates[$row['order_id']] = $row['order_date'];
                switch ($row['channel_id']) {
                    case $settings['channel_for_sales_shopify']:
                        $sales_orders_channel_positions[$row['order_id']] = 0;
                        break;
                    case $settings['channel_for_sales_etsy']:
                        $sales_orders_channel_positions[$row['order_id']] = 1;
                        break;
                    default:
                        $sales_orders_channel_positions[$row['order_id']] = 2;
                        break;
                }
                $sales_orders[$row['order_id']] = [
                    'child_skus'     => [],
                ];
            }
            $ordered_quantity = floatval($row['ordered_quantity']);
            $sales_orders_total_quantities[$row['order_id']] += $ordered_quantity;
            $sales_orders[$row['order_id']]['child_skus'][$row['child_sku_id']] = [
                'code'             => $row['child_sku_code'],
                'ordered_quantity' => $ordered_quantity,
                'picked_quantity'  => floatval($row['picked_quantity']),
            ];
        }

        if ($child_sku_codes) {
            // Sort orders
            array_multisort(
                // First by total quantity from biggest to smallest
                $sales_orders_total_quantities, SORT_DESC,
                // Then by date: from oldest to newest
                $sales_orders_order_dates, SORT_ASC,
                // Then by channel position (defined earlier in the code)
                $sales_orders_channel_positions, SORT_ASC,
                // And then by id ASC
                // (ordering by ID is not necessary, byt we need this final parameter, to collect all sorting steps before it)
                $sales_orders_ids_sorted, SORT_ASC
            );

            if (empty($settings['disable_sku_vault'])) {
                if (self::$reportFirstExecution === true) {
                    $skus_warehouses_locations_quantities = self::_getSkusWarehousesLocationsQuantities($child_sku_codes);
                    self::$skusWarehousesLocationsQuantities = $skus_warehouses_locations_quantities;
                } else {
                    $skus_warehouses_locations_quantities = self::$skusWarehousesLocationsQuantities;
                }
                if (!empty($skus_warehouses_locations_quantities['error'])) {
                    if (self::$reportFirstExecution === true) {
                        $err_msg_var = ($skus_warehouses_locations_quantities['error'] == 'timeout' ? 'skus_warehouses_locations_timeout' : 'skus_warehouses_locations_failed');
                        $err_msg = self::$registry['translater']->translate($err_msg_var);
                        if (self::$registry->get('action') == 'export') {
                            $additional_options['error_msg'] = $err_msg;
                        } else {
                            self::$registry['messages']->setError($err_msg);
                        }
                    }
                } else {
                    // Set warnings (if any)
                    if (!empty($skus_warehouses_locations_quantities['warnings']) && self::$reportFirstExecution === true) {
                        self::$registry['messages']->setWarning(self::$registry['translater']->translate('sku_vault_errors'));
                        foreach ($skus_warehouses_locations_quantities['warnings'] as $warning_msg) {
                            self::$registry['messages']->setWarning($warning_msg);
                        }
                    }

                    if (!empty($skus_warehouses_locations_quantities['data'])) {
                        /*
                         * Stage 1
                         *
                         * Quantity allocation for full filling of sales orders
                         */
                        $sales_orders_fully_filled = [];
                        foreach ($sales_orders_ids_sorted as $sales_order_id) {
                            $sales_order = $sales_orders[$sales_order_id];
                            $skus_from_stock = [];
                            $skip_sales_order = false;
                            foreach ($sales_order['child_skus'] as $child_sku_id => $child_sku) {
                                if (array_key_exists($child_sku['code'], $skus_warehouses_locations_quantities['data'])) {
                                    $child_sku_warehouses_locations = $skus_warehouses_locations_quantities['data'][$child_sku['code']];

                                    // Get warehouses quantities
                                    if (array_key_exists($child_sku_id, $results) && !array_key_exists('warehouses_locations', $results[$child_sku_id])) {
                                        // Record this only the first time, after that it might be decreased
                                        $results[$child_sku_id]['warehouses_locations'] = $child_sku_warehouses_locations;
                                    }

                                    // Skip allocating quantities for this sales order
                                    // if it exists in production orders
                                    if (array_key_exists($child_sku_id, $results)
                                            && array_key_exists($sales_order_id, $results[$child_sku_id]['sales_orders'])
                                            && !empty($results[$child_sku_id]['sales_orders'][$sales_order_id]['has_production_orders'])) {
                                        $skip_sales_order = true;
                                    }

                                    $from_stock = $child_sku['ordered_quantity'] - $child_sku['picked_quantity'];
                                    if ($from_stock <= 0 || $child_sku_warehouses_locations['total_quantity'] <= 0 || $from_stock > $child_sku_warehouses_locations['total_quantity']) {
                                        // Not the entire quantity exists into the warehouse, so skip the entire sales order for this stage
                                        $skip_sales_order = true;
                                    }
                                    if (!$skip_sales_order) {
                                        $skus_from_stock[$child_sku_id] = $from_stock;
                                    }
                                }
                            }
                            // If there are quantities for the entire sales order
                            if (count($skus_from_stock) == count($sales_order['child_skus'])) {
                                foreach ($skus_from_stock as $child_sku_id => $from_stock) {
                                    // If this Child SKU and sales order exists into the report results
                                    if (array_key_exists($child_sku_id, $results) && array_key_exists($sales_order_id, $results[$child_sku_id]['sales_orders'])) {
                                        // Write a proposal for how much to use for picking from the warehouses
                                        $results[$child_sku_id]['sales_orders'][$sales_order_id]['from_stock'] = $from_stock;
                                    }
                                    // No matter if this Child SKU and sales order exists into the report results,
                                    // decrease the warehouses quantity for this Child SKU (it could be used, if the Child SKU and the sales order were visible),
                                    // and continue allocating quantities
                                    $sku_code = $sales_orders[$sales_order_id]['child_skus'][$child_sku_id]['code'];
                                    $skus_warehouses_locations_quantities['data'][$sku_code]['total_quantity'] -= $from_stock;
                                    // When this Child SKU`s warehouses quantities are over
                                    if ($skus_warehouses_locations_quantities['data'][$sku_code]['total_quantity'] <= 0) {
                                        // Unset it
                                        unset($skus_warehouses_locations_quantities['data'][$sku_code]);
                                    }
                                }

                                // Mark this sales order as fully allocated
                                $sales_orders_fully_filled[$sales_order_id] = true;

                                // If there are no Child SKUs warehouses quantities left
                                if (empty($skus_warehouses_locations_quantities['data'])) {
                                    // Stop allocating quantities to sales orders
                                    break;
                                }
                            }
                        }

                        /*
                         * Stage 2
                         *
                         * Quantity allocation for filling the rest of the sales orders as much as possible
                         */
                        // If not all sales orders are fully filled with quantities from the warehouses
                        // and there are still quantities into the warehouses
                        if (count($sales_orders_fully_filled) < count($sales_orders)) {
                            foreach ($sales_orders_ids_sorted as $sales_order_id) {
                                // If this sales order is fully filled from the warehouses
                                if (array_key_exists($sales_order_id, $sales_orders_fully_filled)) {
                                    // Skip it
                                    continue;
                                }

                                $sales_order = $sales_orders[$sales_order_id];
                                foreach ($sales_order['child_skus'] as $child_sku_id => $child_sku) {
                                    // Skip allocating quantities for this sales order
                                    // if it exists in production orders
                                    if (array_key_exists($child_sku_id, $results)
                                            && array_key_exists($sales_order_id, $results[$child_sku_id]['sales_orders'])
                                            && !empty($results[$child_sku_id]['sales_orders'][$sales_order_id]['has_production_orders'])) {
                                        continue 2;
                                    }

                                    if (array_key_exists($child_sku['code'], $skus_warehouses_locations_quantities['data'])) {
                                        $child_sku_warehouses_locations = $skus_warehouses_locations_quantities['data'][$child_sku['code']];
                                        $from_stock = $child_sku['ordered_quantity'] - $child_sku['picked_quantity'];

                                        // If there's no needed quantity or there's no available quantity
                                        if ($from_stock <= 0 || $child_sku_warehouses_locations['total_quantity'] <= 0) {
                                            // We can't allocate quantity for this Child SKU, so skip it
                                            continue;
                                        }

                                        // If the available quantity is less than the needed
                                        // use it as proposal for picking (i.e. use it for "From stock")
                                        // otherwise propose the entire needed quantity (because it's available into the warehouse)
                                        // In other word: use from the warehouse as much as it needs or as much as it's left
                                        $from_stock = ($child_sku_warehouses_locations['total_quantity'] < $from_stock ? $child_sku_warehouses_locations['total_quantity'] : $from_stock);

                                        // If this Child SKU and sales order exists into the report results
                                        if (array_key_exists($child_sku_id, $results) && array_key_exists($sales_order_id, $results[$child_sku_id]['sales_orders'])) {
                                            // Write a proposal for it of how much to use for picking from the warehouses
                                            $results[$child_sku_id]['sales_orders'][$sales_order_id]['from_stock'] = $from_stock;
                                        }

                                        // Decrease the available quantity into the warehouses
                                        $skus_warehouses_locations_quantities['data'][$child_sku['code']]['total_quantity'] -= $from_stock;
                                        // When this Child SKU`s warehouses quantities are over
                                        if ($skus_warehouses_locations_quantities['data'][$child_sku['code']]['total_quantity'] <= 0) {
                                            // Unset it
                                            unset($skus_warehouses_locations_quantities['data'][$child_sku['code']]);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * Build a search URL for documents
     *
     * @param string $registry          - the registry
     * @param string $documents_type    - the ID of the documents type
     * @param array $documents_full_num - the full_num of each document
     *
     * @return string                   - the search URL
     */
    private static function _buildDocumentsSearchURL($registry, $documents_type, $documents_full_num) {
        // Check some basic requirements
        if (empty($documents_type) || empty($documents_full_num) || !is_array($documents_full_num)) {
            return '';
        }

        // Get the documents count
        $documents_count = count($documents_full_num);

        // Prepare the search url for the error message
        $search_url = 'documents&documents=search&search_document=1&search_module=documents&search_controller=&filters_action=' .
                      '&search_fields_prev[0]=d.type&search_fields[0]=d.type&compare_options[0]=%3D+%27%25s%27&values[0]=' . $documents_type .
                      '&logical_operator[0]=AND';
        $i = 0;
        foreach ($documents_full_num as $full_num) {
            $i++;
            $search_url .= '&search_fields_prev[' . $i . ']=d.full_num&search_fields[' . $i . ']=d.full_num&compare_options[' . $i . ']=%3D+%27%25s%27&values[' . $i . ']=' . $full_num;
            if ($i < $documents_count) {
                $search_url .= '&logical_operator[' . $i . ']=OR';
            }
        }
        $search_url .= '&sort[0]=d.added&order[0]=DESC&save_filter_as=&filter_name=&display=10';
        $search_url = sprintf('%s?%s=%s', $_SERVER['PHP_SELF'], $registry->get('module_param'), $search_url);

        return $search_url;
    }
}
