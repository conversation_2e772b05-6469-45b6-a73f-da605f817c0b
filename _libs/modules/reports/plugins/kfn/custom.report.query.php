<?php
    Class Kfn Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //DEFINE OPTIONS TO BE CHECKED
            $price_value_array_options = array('noimot_pj','imot_auto', 'imot_hospital', 'thefts', 'prirodni_bedst', 'injured_load', 'contract_pay', 'driver_trust', 'other_pal', 'razumni_razhodi', 'resh_propusnati_polzi', 'parking', 'resh_razliki_tv', 'resh_razhodi_izdraj', 'resh_popravka_imusht', 'resh_izvanr_trud', 'resh_razhodi_naem', 'pogrebenie_razhodi', 'razhodi_patna');

            $price_extra_expenses_array_options = array('sensibly_expense', 'adv_honorar', 'disasters', 'dep_vlice', 'iuru_vaznagr');

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_records = array();

            $case_current_num_options = array_filter(preg_split('/\s*,\s*/', CASE_CURRENT_NUM));
            $case_first_instance_options = array_filter(preg_split('/\s*,\s*/', CASE_FIRST_INSTANCE));
            $lev_ins_is_options = array_filter(preg_split('/\s*,\s*/', LEV_INS_IS));
            $damage_num_options = array_filter(preg_split('/\s*,\s*/', DAMAGE_NUM));
            $bill_num_options = array_filter(preg_split('/\s*,\s*/', BILL_NUM));
            $deadline_from_options = array_filter(preg_split('/\s*,\s*/', DEADLINE_FROM));
            $deadline_to_options = array_filter(preg_split('/\s*,\s*/', DEADLINE_TO));
            $court_case_options = array_filter(preg_split('/\s*,\s*/', COURT_CASE));
            $court_pendant_case_options = array_filter(preg_split('/\s*,\s*/', COURT_PENDANT_CASE));
            $claimant_first_side_options = array_filter(preg_split('/\s*,\s*/', CLAIMANT_FIRST_SIDE));
            $defendant_second_side_options = array_filter(preg_split('/\s*,\s*/', DEFENDANT_SECOND_SIDE));
            $third_side_options = array_filter(preg_split('/\s*,\s*/', THIRD_SIDE));
            $type_insurance_options = array_filter(preg_split('/\s*,\s*/', TYPE_INSURANCE));
            $type_event_options = array_filter(preg_split('/\s*,\s*/', TYPE_EVENT));
            $date_event_options = array_filter(preg_split('/\s*,\s*/', DATE_EVENT));
            $case_price_options = array_filter(preg_split('/\s*,\s*/', CASE_PRICE));
            $case_changed_price_options = array_filter(preg_split('/\s*,\s*/', CASE_CHANGED_PRICE));
            $case_notes_options = array_filter(preg_split('/\s*,\s*/', CASE_NOTES));
            $case_value_options = array_filter(preg_split('/\s*,\s*/', CASE_VALUE));
            $project_company_options = array_filter(preg_split('/\s*,\s*/', PROJECT_COMPANY));

            //OPTIONS FOR MATCHING THE CRITERIAS
            // case_current_num
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_current_num_options) . '")';
            $case_current_num_options = implode(',', $registry['db']->GetCol($query));

            // case_first_instance
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_first_instance_options) . '")';
            $case_first_instance_options = implode(',', $registry['db']->GetCol($query));

            // lev_ins_is
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $lev_ins_is_options) . '")';
            $lev_ins_is_options = implode(',', $registry['db']->GetCol($query));

            // damage_num
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $damage_num_options) . '")';
            $damage_num_options = implode(',', $registry['db']->GetCol($query));

            // bill_num
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $bill_num_options) . '")';
            $bill_num_options = implode(',', $registry['db']->GetCol($query));

            // deadline_from
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $deadline_from_options) . '")';
            $deadline_from_options = implode(',', $registry['db']->GetCol($query));

            // deadline_to
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $deadline_to_options) . '")';
            $deadline_to_options = implode(',', $registry['db']->GetCol($query));

            // OPTIONS FOR TAKING INFORMATION FOR THE REPORT
            // court_case
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $court_case_options) . '")';
            $court_case_options = implode(',', $registry['db']->GetCol($query));

            // court_pendant_case
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $court_pendant_case_options) . '")';
            $court_pendant_case_options = implode(',', $registry['db']->GetCol($query));

            // claimant_first_side
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $claimant_first_side_options) . '")';
            $claimant_first_side_options = implode(',', $registry['db']->GetCol($query));

            // defendant_second_side
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $defendant_second_side_options) . '")';
            $defendant_second_side_options = implode(',', $registry['db']->GetCol($query));

            // third_side
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $third_side_options) . '")';
            $third_side_options = implode(',', $registry['db']->GetCol($query));

            // type_insurance
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $type_insurance_options) . '")';
            $type_insurance_options = implode(',', $registry['db']->GetCol($query));

            // type_event
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $type_event_options) . '")';
            $type_event_options = implode(',', $registry['db']->GetCol($query));

            // date_event_options
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $date_event_options) . '")';
            $date_event_options = implode(',', $registry['db']->GetCol($query));

            //case_price
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_price_options) . '")';
            $case_price_options = implode(',', $registry['db']->GetCol($query));

            //case_changed_price
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_changed_price_options) . '")';
            $case_changed_price_options = implode(',', $registry['db']->GetCol($query));

            //case_notes
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_notes_options) . '")';
            $case_notes_options = implode(',', $registry['db']->GetCol($query));

            //case_value
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $case_value_options) . '")';
            $case_value_options = implode(',', $registry['db']->GetCol($query));

            //project_company
            $query  = 'SELECT id FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Project" AND name IN ("' . implode('","', $project_company_options) . '")';
            $project_company_options = implode(',', $registry['db']->GetCol($query));

            // takes the model_ids which match the criterias
            $projects_ids_query = array();
            $projects_ids_query_1 = array();
            $projects_ids_query_2 = array();

            $projects_ids_query['select'] = 'SELECT DISTINCT(proj.id) AS model_id' . "\n";

            $join_projects_ids = array();
            $where_projects_ids = array();
            $join_projects_ids[]   = 'FROM ' . DB_TABLE_PROJECTS . ' AS proj' . "\n";

            if (! empty($filters['case_current_num']) && ! empty($case_current_num_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm1' . "\n" .
                                       '  ON (proj_cstm1.model_id=proj.id AND proj_cstm1.var_id IN (' . $case_current_num_options . ') AND proj_cstm1.value LIKE "%' . $filters['case_current_num'] . '%")';
            }
            if (! empty($filters['case_first_instance']) && ! empty($case_first_instance_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm2' . "\n" .
                                       '  ON (proj_cstm2.model_id=proj.id AND proj_cstm2.var_id IN (' . $case_first_instance_options . ') AND proj_cstm2.value LIKE "%' . $filters['case_first_instance'] . '%")';
            }
            if (! empty($filters['lev_ins_is']) && ! empty($lev_ins_is_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm3' . "\n" .
                                       '  ON (proj_cstm3.model_id=proj.id AND proj_cstm3.var_id IN (' . $lev_ins_is_options . ') AND proj_cstm3.value="' . $filters['lev_ins_is'] . '")';
            }
            if (! empty($filters['bill_num']) && ! empty($bill_num_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm4' . "\n" .
                                       '  ON (proj_cstm4.model_id=proj.id AND proj_cstm4.var_id IN (' . $bill_num_options . ') AND proj_cstm4.value LIKE "%' . $filters['bill_num'] . '%")';
            }
            if (! empty($filters['damage_num']) && ! empty($damage_num_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm5' . "\n" .
                                       '  ON (proj_cstm5.model_id=proj.id AND proj_cstm5.var_id IN (' . $damage_num_options . ') AND proj_cstm5.value LIKE "%' . $filters['damage_num'] . '%")';
            }
            if (!empty($filters['deadline_from_date']) && !empty($deadline_from_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm6' . "\n" .
                                       '  ON (proj_cstm6.model_id=proj.id AND proj_cstm6.var_id IN (' . $deadline_from_options . ') AND DATE_FORMAT(proj_cstm6.value, "%Y-%m-%d")>="' . $filters['deadline_from_date'] . '")';
            }
            if (!empty($filters['deadline_to_date']) && !empty($deadline_to_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm7' . "\n" .
                                       '  ON (proj_cstm7.model_id=proj.id AND proj_cstm7.var_id IN (' . $deadline_to_options . ') AND DATE_FORMAT(proj_cstm7.value, "%Y-%m-%d")<="' . $filters['deadline_to_date'] . '")';
            }
            if (!empty($filters['company']) && !empty($project_company_options)) {
                $join_projects_ids[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS proj_cstm8' . "\n" .
                                       '  ON (proj_cstm8.model_id=proj.id AND proj_cstm8.var_id IN (' . $project_company_options . ') AND proj_cstm8.value="' . $filters['company'] . '")';
            }

            if (!empty($filters['archived_projects'])) {
                if ($filters['archived_projects']=='archived_only') {
                    $where_projects_ids[] = '(proj.stage IN (' . ARCHIVED_STAGES . ') OR proj.finished IS NOT NULL)';
                } else {
                    $where_projects_ids[] = '(proj.stage NOT IN (' . ARCHIVED_STAGES . ') AND proj.finished IS NULL)';
                }
            }
            if (!empty($filters['added_date_from'])) {
                $where_projects_ids[] = 'DATE_FORMAT(proj.added, "%Y-%m-%d")>="' . $filters['added_date_from'] . '"';
            }
            if (!empty($filters['added_date_to'])) {
                $where_projects_ids[] = 'DATE_FORMAT(proj.added, "%Y-%m-%d")<="' . $filters['added_date_to'] . '"';
            }
            $where_projects_ids[] = 'proj.type<=7';

            if (!empty($filters['customer']) || !empty($filters['project']) || !empty($filters['manager']) || !empty($filters['from_date']) || !empty($filters['to_date'])) {
                if (!empty($filters['customer'])) {
                    $where_projects_ids[] = 'proj.customer="' . $filters['customer'] . '"';
                }
                if (!empty($filters['project'])) {
                    $where_projects_ids[] = 'proj.id="' . $filters['project'] . '"';
                }
                if (!empty($filters['manager'])) {
                    $where_projects_ids[] = 'proj.manager="' . $filters['manager'] . '"';
                }

                if (!empty($filters['from_date']) || !empty($filters['to_date'])) {

                    // separate the queries to faster the report performance
                    $projects_ids_query_1['select'] = $projects_ids_query['select'];
                    $join_projects_ids_1 = $join_projects_ids;
                    $on_clause_1 = array();
                    $on_clause_1[] = 'd.project=proj.id AND d.active=1 AND d.deleted_by=0';
                    $on_clause_1[] = 'd.type="' . DOCUMENT_COURT_DESICION . '"';
                    $where_projects_ids_1 = $where_projects_ids;

                    // prepare archive query
                    $archive_query['select'] = $projects_ids_query['select'];
                    $join_archive_query = $join_projects_ids;

                    $projects_ids_query_2['select'] = $projects_ids_query['select'];
                    $join_projects_ids_2 = $join_projects_ids;
                    $on_clause_2 = array();
                    $on_clause_2[] = 'ph.model_id=proj.id';
                    $where_projects_ids_2 = $where_projects_ids;

                    if (!empty($filters['from_date'])) {
                        $on_clause_1[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                        $where_projects_ids_1[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")>="' . $filters['from_date'] . '"';

                        $on_clause_2[] = 'DATE_FORMAT(ph.h_date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                        $where_projects_ids_2[] = 'DATE_FORMAT(ph.h_date, "%Y-%m-%d")>="' . $filters['from_date'] . '"';
                    }
                    if (!empty($filters['to_date'])) {
                        $on_clause_1[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                        $where_projects_ids_1[] = 'DATE_FORMAT(d.added, "%Y-%m-%d")<="' . $filters['to_date'] . '"';

                        $on_clause_2[] = 'DATE_FORMAT(ph.h_date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                        $where_projects_ids_2[] = 'DATE_FORMAT(ph.h_date, "%Y-%m-%d")<="' . $filters['to_date'] . '"';
                    }

                    $join_projects_ids_1[] = 'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                             '  ON (' . implode(' AND ', $on_clause_1) . ')';
                    // prepare archive query JOIN
                    $join_archive_query[] = 'INNER JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                            '  ON (' . implode(' AND ', $on_clause_1) . ')';

                    $join_projects_ids_2[] = 'INNER JOIN ' . DB_TABLE_PROJECTS_HISTORY . ' AS ph' . "\n" .
                                             '  ON (' . implode(' AND ', $on_clause_2) . ')';

                }
            }
            if (empty($projects_ids_query_1) && empty($projects_ids_query_2)) {
                $projects_ids_query['from'] = implode("\n", $join_projects_ids);
                $projects_ids_query['where'] = 'WHERE ' . implode(" AND ", $where_projects_ids);

                $projects_ids_sql = implode("\n", $projects_ids_query);

                $projects_ids = $registry['db']->GetCol($projects_ids_sql);
            } else {
                // main query (with JOIN main DOCUMENT table)
                $projects_ids_query_1['from'] = implode("\n", $join_projects_ids_1);
                $projects_ids_query_1['where'] = 'WHERE ' . implode(" AND ", $where_projects_ids_1);
                $union_queries = array();
                $union_queries[] = implode("\n", $projects_ids_query_1);

                // archive query (with JOIN main ARCHIVE DOCUMENTS table)
                $archive_query['from'] = implode("\n", $join_archive_query);
                $archive_query['where'] = 'WHERE ' . implode(" AND ", $where_projects_ids_1);
                $union_queries[] = implode("\n", $archive_query);


                $projects_ids_sql_1 = '(' . implode(') UNION (', $union_queries) . ')';
                $projects_ids_1 = $registry['db']->GetCol($projects_ids_sql_1);

                $projects_ids_query_2['from'] = implode("\n", $join_projects_ids_2);
                $projects_ids_query_2['where'] = 'WHERE ' . implode(" AND ", $where_projects_ids_2);
                $projects_ids_sql_2 = implode("\n", $projects_ids_query_2);
                $projects_ids_2 = $registry['db']->GetCol($projects_ids_sql_2);

                $projects_ids = array_merge($projects_ids_1, $projects_ids_2);
                $projects_ids = array_unique($projects_ids);
            }

            $projects_ids_string = '';
            if (!empty($projects_ids)) {
                $projects_ids_string = implode(',', $projects_ids);
            }


            // TAKES THE SIMPLE DATA FROM THE FOUND PROJECTS
            //sql to take the full required data for the projects
            $sql_for_simple_data['select'] = 'SELECT p.id as id, p.status as status, p.stage as prj_stage, DATE_FORMAT(p.date_start, "%Y-%m-%d") AS date_start, ' . "\n" .
                                             '  sti18n.name AS stage_name, p_cstm_case_current_num.value as case_current_num, ' . "\n" .
                                             '  p_cstm_case_first_instance.value as case_first_instance, p_cstm_court_pendant_case_i18n.name as court_pendant_case, ' . "\n" .
                                             '  p_cstm_claimant_first_side.value as claimant_first_side, p_cstm_defendant_second_side.value as defendant_second_side, ' . "\n" .
                                             '  p_cstm_third_side.value as third_side, p_cstm_type_insurance_i18n.label as type_insurance, ' . "\n" .
                                             '  DATE_FORMAT(p_cstm_deadline_from.value, "%Y-%m-%d") as deadline_from, DATE_FORMAT(p_cstm_deadline_to.value, "%Y-%m-%d") as deadline_to, ' . "\n" .
                                             '  p_bill_num.value as bill_num, p_damage_num.value as damage_num, ' . "\n" .
                                             '  p_cstm_event_type_i18n.label as type_event, DATE_FORMAT(p_cstm_date_event.value, "%d.%m.%Y") as date_event, ' . "\n" .
                                             '  p_cstm_case_price.value as case_price, p_cstm_changed_price.value as changed_price, ' . "\n" .
                                             '  p_cstm_notes.value as notes, p_cstm_case_value.value as case_value, DATE_FORMAT(p.added, "%d.%m.%Y") as date_added ' . "\n";

            $sql_for_simple_data['from'] = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_STAGES_I18N . ' AS sti18n' . "\n" .
                                           '  ON (p.stage=sti18n.parent_id AND sti18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_case_current_num' . "\n" .
                                           '  ON (fm_case_current_num.model_type=p.type AND fm_case_current_num.model="Project" AND fm_case_current_num.id IN (' . $case_current_num_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_current_num' . "\n" .
                                           '  ON (p_cstm_case_current_num.model_id=p.id AND p_cstm_case_current_num.var_id=fm_case_current_num.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_case_first_instance' . "\n" .
                                           '  ON (fm_case_first_instance.model_type=p.type AND fm_case_first_instance.model="Project" AND fm_case_first_instance.id IN (' . $case_first_instance_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_first_instance' . "\n" .
                                           '  ON (p_cstm_case_first_instance.model_id=p.id AND p_cstm_case_first_instance.var_id=fm_case_first_instance.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_court_pendant_case' . "\n" .
                                           '  ON (fm_court_pendant_case.model_type=p.type AND fm_court_pendant_case.model="Project" AND fm_court_pendant_case.id IN (' . $court_pendant_case_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_court_pendant_case' . "\n" .
                                           '  ON (p_cstm_court_pendant_case.model_id=p.id AND p_cstm_court_pendant_case.var_id=fm_court_pendant_case.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS p_cstm_court_pendant_case_i18n' . "\n" .
                                           '  ON (p_cstm_court_pendant_case.value=p_cstm_court_pendant_case_i18n.parent_id AND p_cstm_court_pendant_case_i18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_claimant_first_side' . "\n" .
                                           '  ON (fm_claimant_first_side.model_type=p.type AND fm_claimant_first_side.model="Project" AND fm_claimant_first_side.id IN (' . $claimant_first_side_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_claimant_first_side' . "\n" .
                                           '  ON (p_cstm_claimant_first_side.model_id=p.id AND p_cstm_claimant_first_side.var_id=fm_claimant_first_side.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_defendant_second_side' . "\n" .
                                           '  ON (fm_defendant_second_side.model_type=p.type AND fm_defendant_second_side.model="Project" AND fm_defendant_second_side.id IN (' . $defendant_second_side_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_defendant_second_side' . "\n" .
                                           '  ON (p_cstm_defendant_second_side.model_id=p.id AND p_cstm_defendant_second_side.var_id=fm_defendant_second_side.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_third_side' . "\n" .
                                           '  ON (fm_third_side.model_type=p.type AND fm_third_side.model="Project" AND fm_third_side.id IN (' . $third_side_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_third_side' . "\n" .
                                           '  ON (p_cstm_third_side.model_id=p.id AND p_cstm_third_side.var_id=fm_third_side.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_type_insurance' . "\n" .
                                           '  ON (fm_type_insurance.model_type=p.type AND fm_type_insurance.model="Project" AND fm_type_insurance.id IN (' . $type_insurance_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_type_insurance' . "\n" .
                                           '  ON (p_cstm_type_insurance.model_id=p.id AND p_cstm_type_insurance.var_id=fm_type_insurance.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS p_cstm_type_insurance_i18n' . "\n" .
                                           '  ON (p_cstm_type_insurance.value=p_cstm_type_insurance_i18n.option_value AND p_cstm_type_insurance_i18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_deadline_from' . "\n" .
                                           '  ON (fm_deadline_from.model_type=p.type AND fm_deadline_from.model="Project" AND fm_deadline_from.id IN (' . $deadline_from_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_deadline_from' . "\n" .
                                           '  ON (p_cstm_deadline_from.model_id=p.id AND p_cstm_deadline_from.var_id=fm_deadline_from.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_deadline_to' . "\n" .
                                           '  ON (fm_deadline_to.model_type=p.type AND fm_deadline_to.model="Project" AND fm_deadline_to.id IN (' . $deadline_to_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_deadline_to' . "\n" .
                                           '  ON (p_cstm_deadline_to.model_id=p.id AND p_cstm_deadline_to.var_id=fm_deadline_to.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_damage_num' . "\n" .
                                           '  ON (fm_damage_num.model_type=p.type AND fm_damage_num.model="Project" AND fm_damage_num.id IN (' . $damage_num_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_damage_num' . "\n" .
                                           '  ON (p_damage_num.model_id=p.id AND p_damage_num.var_id=fm_damage_num.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_bill_num' . "\n" .
                                           '  ON (fm_bill_num.model_type=p.type AND fm_bill_num.model="Project" AND fm_bill_num.id IN (' . $bill_num_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_bill_num' . "\n" .
                                           '  ON (p_bill_num.model_id=p.id AND p_bill_num.var_id=fm_bill_num.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_event_type' . "\n" .
                                           '  ON (fm_event_type.model_type=p.type AND fm_event_type.model="Project" AND fm_event_type.id IN (' . $type_event_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_event_type' . "\n" .
                                           '  ON (p_cstm_event_type.model_id=p.id AND p_cstm_event_type.var_id=fm_event_type.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS p_cstm_event_type_i18n' . "\n" .
                                           '  ON (p_cstm_event_type.value=p_cstm_event_type_i18n.option_value AND p_cstm_event_type_i18n.lang="' . $model_lang . '")' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_date_event' . "\n" .
                                           '  ON (fm_date_event.model_type=p.type AND fm_date_event.model="Project" AND fm_date_event.id IN (' . $date_event_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_date_event' . "\n" .
                                           '  ON (p_cstm_date_event.model_id=p.id AND p_cstm_date_event.var_id=fm_date_event.id AND p_cstm_date_event.num=p_cstm_event_type.num)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_case_price' . "\n" .
                                           '  ON (fm_case_price.model_type=p.type AND fm_case_price.model="Project" AND fm_case_price.id IN (' . $case_price_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_price' . "\n" .
                                           '  ON (p_cstm_case_price.model_id=p.id AND p_cstm_case_price.var_id=fm_case_price.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_case_changed_price' . "\n" .
                                           '  ON (fm_case_changed_price.model_type=p.type AND fm_case_changed_price.model="Project" AND fm_case_changed_price.id IN (' . $case_changed_price_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_changed_price' . "\n" .
                                           '  ON (p_cstm_changed_price.model_id=p.id AND p_cstm_changed_price.var_id=fm_case_changed_price.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_notes' . "\n" .
                                           '  ON (fm_notes.model_type=p.type AND fm_notes.model="Project" AND fm_notes.id IN (' . $case_notes_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_notes' . "\n" .
                                           '  ON (p_cstm_notes.model_id=p.id AND p_cstm_notes.var_id=fm_notes.id)' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_case_value' . "\n" .
                                           '  ON (fm_case_value.model_type=p.type AND fm_case_value.model="Project" AND fm_case_value.id IN (' . $case_value_options . '))' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS p_cstm_case_value' . "\n" .
                                           '  ON (p_cstm_case_value.model_id=p.id AND p_cstm_case_value.var_id=fm_case_value.id)' . "\n";

            // construct where
            $where = array();
            $where[] = 'p.deleted_by=0';
            $where[] = 'p.active!=0';

            if (! empty($projects_ids_string)) {
                $where[] = 'p.id IN (' . $projects_ids_string . ')';
            } else {
                $where[] = 'p.id IN (0)';
            }

            $sql_for_simple_data['where'] = 'WHERE ' . implode(' AND ', $where);

            $query_simple_results = implode("\n", $sql_for_simple_data);
            $simple_results = $registry['db']->GetAll($query_simple_results);

            $projects_info = array();
            foreach ($simple_results as $simp_res) {
                if (! isset($projects_info[$simp_res['id']])) {
                    $projects_info[$simp_res['id']]['id'] = $simp_res['id'];
                    $projects_info[$simp_res['id']]['date_start'] = $simp_res['date_start'];
                    if ($simp_res['status'] == 'finished' && !$simp_res['prj_stage']) {
                        $projects_info[$simp_res['id']]['stage_name'] = $registry['translater']->translate('reports_default_ending_status');
                    } else {
                        $projects_info[$simp_res['id']]['stage_name'] = $simp_res['stage_name'];
                    }
                    $projects_info[$simp_res['id']]['case_current_num'] = $simp_res['case_current_num'];
                    $projects_info[$simp_res['id']]['case_first_instance'] = $simp_res['case_first_instance'];
                    $projects_info[$simp_res['id']]['court_pendant_case'] = $simp_res['court_pendant_case'];
                    $projects_info[$simp_res['id']]['claimant_first_side'] = $simp_res['claimant_first_side'];
                    $projects_info[$simp_res['id']]['defendant_second_side'] = $simp_res['defendant_second_side'];
                    $projects_info[$simp_res['id']]['third_side'] = $simp_res['third_side'];
                    $projects_info[$simp_res['id']]['case_value'] = $simp_res['case_value'];
                    $projects_info[$simp_res['id']]['interest_value'] = '';
                    $projects_info[$simp_res['id']]['other_interest_value'] = '';
                    $projects_info[$simp_res['id']]['price_extras'] = '';
                    $projects_info[$simp_res['id']]['type_insurance'] = $simp_res['type_insurance'];
                    $projects_info[$simp_res['id']]['bill_num'] = $simp_res['bill_num'];
                    $projects_info[$simp_res['id']]['deadline_from'] = $simp_res['deadline_from'];
                    $projects_info[$simp_res['id']]['deadline_to'] = $simp_res['deadline_to'];
                    $projects_info[$simp_res['id']]['damage_num'] = $simp_res['damage_num'];
                    $projects_info[$simp_res['id']]['type_event'] = array();
                    $projects_info[$simp_res['id']]['date_event'] = array();
                    $projects_info[$simp_res['id']]['case_price'] = $simp_res['case_price'];
                    $projects_info[$simp_res['id']]['changed_price'] = $simp_res['changed_price'];
                    $projects_info[$simp_res['id']]['notes'] = $simp_res['notes'];
                    $projects_info[$simp_res['id']]['date_added'] = $simp_res['date_added'];
                    $projects_info[$simp_res['id']]['type_event_string'] = '';
                    $projects_info[$simp_res['id']]['date_event_string'] = '';
                }
                if (! empty($simp_res['type_event'])) {
                    $projects_info[$simp_res['id']]['type_event'][] = $simp_res['type_event'];
                    $projects_info[$simp_res['id']]['type_event_string'] = implode(', ', $projects_info[$simp_res['id']]['type_event']);
                }
                if (! empty($simp_res['date_event'])) {
                    $projects_info[$simp_res['id']]['date_event'][] = $simp_res['date_event'];
                    $projects_info[$simp_res['id']]['date_event_string'] = implode(', ', $projects_info[$simp_res['id']]['date_event']);
                }
            }

            // QUERY TO TAKE THE COURT DESCISIONS
            $query_court_decision_documents['select'] = 'SELECT DISTINCT(p.id) as id_index, d.id as id, d.added as added' . "\n";
            $query_court_decision_documents['from']   = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                                        'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                        '  ON (d.project=p.id AND d.type="' . DOCUMENT_COURT_DESICION . '" AND d.active=1 AND d.deleted_by=0)' . "\n";
            $where = array();
            if (! empty($projects_ids_string)) {
                $where[] = 'p.id IN (' . $projects_ids_string . ')';
            } else {
                $where[] = 'p.id IN (0)';
            }

            $query_court_decision_documents['where'] = 'WHERE ' . implode(' AND ', $where);
            $query_court_decision_documents['sort'] = 'ORDER BY d.added';

            $query_court_decision_documents_archive = $query_court_decision_documents;
            $query_court_decision_documents_archive['from'] = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                                              'INNER JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                                              '  ON (d.project=p.id AND d.type="' . DOCUMENT_COURT_DESICION . '" AND d.active=1 AND d.deleted_by=0)' . "\n";

            $union_queries = array();
            $union_queries[] = implode("\n", $query_court_decision_documents);
            $union_queries[] = implode("\n", $query_court_decision_documents_archive);

            $sql_court_decision_documents = '(' . implode(') UNION (', $union_queries) . ') ORDER BY added';
            $court_descisions_per_projects = $registry['db']->GetAssoc($sql_court_decision_documents);

            //form the string with documents to be checked
            $documents_string = array();
            foreach ($court_descisions_per_projects as $cdpp) {
                $documents_string[] = $cdpp['id'];
            }
            $documents_string = implode(",", $documents_string);

            if (!empty($documents_string)) {
                //OPTIONS FOR MATCHING THE CRITERIAS
                // case_current_num
                $query_vars  = 'SELECT id, name FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND (name="' . GROUP_TABLE . '" OR name="' . DESCION_DATE . '" OR name="' . SUMMONS_DATE . '") AND model_type="' . DOCUMENT_COURT_DESICION . '"';
                $vars_ids = $registry['db']->GetAll($query_vars);

                $gruop_table_id = '';
                $descion_date_id = '';
                $summons_date_id = '';

                foreach ($vars_ids as $vars) {
                    if ($vars['name'] == GROUP_TABLE) {
                        $gruop_table_id = $vars['id'];
                    } else if ($vars['name'] == DESCION_DATE) {
                        $descion_date_id = $vars['id'];
                    } else if ($vars['name'] == SUMMONS_DATE) {
                        $summons_date_id = $vars['id'];
                    }
                }

                // SIMPLE DOCUMENT DATA
                $sql_for_doc_simple_data['select'] = 'SELECT d.id as id, d.project AS project, DATE_FORMAT(d_cstm_descion_date.value, "%Y-%m-%d") AS descion_date, ' . "\n" .
                                                     '  DATE_FORMAT(d_cstm_summons_date.value, "%Y-%m-%d") AS summons_date ' . "\n";
                $sql_for_doc_simple_data['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_descion_date ' . "\n" .
                                                     '  ON (d.id=d_cstm_descion_date.model_id AND d_cstm_descion_date.var_id="' . $descion_date_id . '")' . "\n" .
                                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_summons_date ' . "\n" .
                                                     '  ON (d.id=d_cstm_summons_date.model_id AND d_cstm_summons_date.var_id="' . $summons_date_id . '")' . "\n";
                $sql_for_doc_simple_data['where']  = 'WHERE d.id IN (' . $documents_string . ')';

                $archive_sql_for_doc_simple_data = $sql_for_doc_simple_data;
                $archive_sql_for_doc_simple_data['from'] = 'FROM ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_descion_date ' . "\n" .
                                                           '  ON (d.id=d_cstm_descion_date.model_id AND d_cstm_descion_date.var_id="' . $descion_date_id . '")' . "\n" .
                                                           'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_summons_date ' . "\n" .
                                                           '  ON (d.id=d_cstm_summons_date.model_id AND d_cstm_summons_date.var_id="' . $summons_date_id . '")' . "\n";

                //search basic details with current lang parameters
                $query_doc_simple_data = '(' . implode("\n", $sql_for_doc_simple_data) . ') UNION (' . implode("\n", $archive_sql_for_doc_simple_data) . ')';
                $documents_simple_data = $registry['db']->GetAssoc($query_doc_simple_data);

                foreach ($documents_simple_data as $doc_id => $doc_simple_data) {
                    $projects_info[$doc_simple_data['project']]['descion_date'] = $doc_simple_data['descion_date'];
                    $projects_info[$doc_simple_data['project']]['summons_date'] = $doc_simple_data['summons_date'];
                }

                // TAKING THE NEEDED BB VARS
                $documents_bb_query = '(SELECT bb.model_id as model_id, bb.params as params ' . "\n" .
                                      'FROM ' . DB_TABLE_BB . ' AS bb ' . "\n" .
                                      'WHERE  bb.model="Document" AND bb.model_type="' . DOCUMENT_COURT_DESICION . '" AND bb.meta_id="' . $gruop_table_id . '" AND bb.model_id IN (' . $documents_string . '))' . "\n" .
                                      ' UNION ' . "\n" .
                                      '(SELECT bb.model_id as model_id, bb.params as params ' . "\n" .
                                      'FROM ' . DB_TABLE_ARCHIVE_BB . ' AS bb ' . "\n" .
                                      'WHERE  bb.model="Document" AND bb.model_type="' . DOCUMENT_COURT_DESICION . '" AND bb.meta_id="' . $gruop_table_id . '" AND bb.model_id IN (' . $documents_string . '))' . "\n";
                $bb_vars = $registry['db']->GetAll($documents_bb_query);

                $documents_bb_info = array();

                // unserialize data and take data from BB vars
                foreach($bb_vars as $bb_var) {
                    if (! isset($documents_bb_info[$bb_var['model_id']])) {
                        $documents_bb_info[$bb_var['model_id']] = array(
                            'case_value'            => '',
                            'interest_value'        => '',
                            'other_interest_value'  => '',
                            'price_extras'          => ''
                        );
                    }
                    $deserialized_data = array();
                    $deserialized_data = unserialize($bb_var['params']);

                    foreach ($deserialized_data as $var_name => $data_for_current_var) {
                        if ($var_name == LEV_INS_ATTITUDE) {
                            foreach ($data_for_current_var as $row => $row_data) {
                                if ($row_data == LEV_INS_ATTITUDE_CHECK_1) {
                                    if (isset($deserialized_data[TYPE_VALUE][$row])) {
                                        if (in_array($deserialized_data[TYPE_VALUE][$row], $price_value_array_options) && isset($deserialized_data[PRINCIPAL_VALUE][$row])) {
                                            $documents_bb_info[$bb_var['model_id']]['case_value'] = sprintf("%01.2f", (round($documents_bb_info[$bb_var['model_id']]['case_value'], 2) + round($deserialized_data[PRINCIPAL_VALUE][$row], 2)));
                                        }
                                        if (in_array($deserialized_data[TYPE_VALUE][$row], $price_extra_expenses_array_options) && isset($deserialized_data[PRINCIPAL_VALUE][$row]) && $deserialized_data[PRINCIPAL_VALUE][$row] > 0) {
                                            $documents_bb_info[$bb_var['model_id']]['price_extras'] = sprintf("%01.2f", (round($documents_bb_info[$bb_var['model_id']]['price_extras'], 2) + round($deserialized_data[PRINCIPAL_VALUE][$row], 2)));
                                        }
                                    }
                                }
                            }
                        } else if ($var_name == TYPE_INTEREST && !empty($data_for_current_var)) {
                            foreach ($data_for_current_var as $row => $row_data) {
                                if ($row_data == TYPE_INTEREST_CHECK_1) {
                                    if (isset($deserialized_data[VALUE_INTEREST][$row]) && $deserialized_data[VALUE_INTEREST][$row] > 0) {
                                        $documents_bb_info[$bb_var['model_id']]['interest_value'] = sprintf("%01.2f", (round($documents_bb_info[$bb_var['model_id']]['interest_value'], 2) + round($deserialized_data[VALUE_INTEREST][$row], 2)));
                                    }
                                } else if ($row_data == TYPE_INTEREST_CHECK_2) {
                                    if (isset($deserialized_data[VALUE_INTEREST][$row]) && $deserialized_data[VALUE_INTEREST][$row] > 0) {
                                        $documents_bb_info[$bb_var['model_id']]['other_interest_value'] = sprintf("%01.2f", (round($documents_bb_info[$bb_var['model_id']]['other_interest_value'], 2) + round($deserialized_data[VALUE_INTEREST][$row], 2)));
                                    }
                                }
                            }
                        }
                    }
                }

                foreach ($court_descisions_per_projects as $project_id => $document) {
                    if (isset($documents_bb_info[$document['id']]) && isset($projects_info[$project_id])) {
                        $projects_info[$project_id]['case_value'] = $documents_bb_info[$document['id']]['case_value'];
                        if (! empty($documents_bb_info[$document['id']]['interest_value'])) {
                            $projects_info[$project_id]['interest_value'] = $documents_bb_info[$document['id']]['interest_value'];
                        }
                        if (! empty($documents_bb_info[$document['id']]['other_interest_value'])) {
                            $projects_info[$project_id]['other_interest_value'] = $documents_bb_info[$document['id']]['other_interest_value'];
                        }
                        if (! empty($documents_bb_info[$document['id']]['price_extras'])) {
                            $projects_info[$project_id]['price_extras'] = $documents_bb_info[$document['id']]['price_extras'];
                        }
                    }
                }
            }

            // QUERY TO TAKE THE EXPENSES
            $query_expense_documents['select'] = 'SELECT d.id' . "\n";
            $query_expense_documents['from']   = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                                 'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                 '  ON (d.project=p.id AND d.type="' . DOCUMENT_EXPENSE . '" AND d.active=1 AND d.deleted_by=0)' . "\n";
            $where = array();
            if (! empty($projects_ids_string)) {
                $where[] = 'p.id IN (' . $projects_ids_string . ')';
            } else {
                $where[] = 'p.id IN (0)';
            }

            $query_expense_documents['where'] = 'WHERE ' . implode(' AND ', $where);

            $archive_query_expense_documents = $query_expense_documents;
            $archive_query_expense_documents['from'] = 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                                       'INNER JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                                       '  ON (d.project=p.id AND d.type="' . DOCUMENT_EXPENSE . '" AND d.active=1 AND d.deleted_by=0)' . "\n";

            $sql_expenses_documents = '(' . implode("\n", $query_expense_documents) . ') UNION (' . implode("\n", $archive_query_expense_documents) . ')';
            $expenses_documents = $registry['db']->GetCol($sql_expenses_documents);

            //form the string with documents to be checked
            $documents_expenses_string = '';
            $documents_expenses_string = implode(",", $expenses_documents);

            if (!empty($documents_expenses_string)) {
                // taking the vars for expenses
                $query_vars  = 'SELECT id, name FROM ' . DB_TABLE_FIELDS_META . ' WHERE model="Document" AND name="' . EXPENSE_VALUE . '" AND model_type="' . DOCUMENT_EXPENSE . '"';
                $expense_value_id = $registry['db']->GetOne($query_vars);

                // EXPENSE DATA
                $sql_expenses_data['select'] = 'SELECT d.project as p_index, SUM(d_cstm_expense_value.value) as total' . "\n";
                $sql_expenses_data['from']   = 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_expense_value ' . "\n" .
                                               '  ON (d.id=d_cstm_expense_value.model_id AND d_cstm_expense_value.var_id="' . $expense_value_id . '")' . "\n";
                $sql_expenses_data['where']  = 'WHERE d.id IN (' . $documents_expenses_string . ')';
                $sql_expenses_data['group']  = 'GROUP BY d.project';

                $query_expenses_data = implode("\n", $sql_expenses_data);
                $expenses_by_projects = $registry['db']->GetAssoc($query_expenses_data);

                $archived_sql_expenses_data = $sql_expenses_data;
                $archived_sql_expenses_data['from'] = 'FROM ' . DB_TABLE_ARCHIVE_DOCUMENTS . ' AS d' . "\n" .
                                                      'LEFT JOIN ' . DB_TABLE_ARCHIVE_DOCUMENTS_CSTM . ' AS d_cstm_expense_value ' . "\n" .
                                                      '  ON (d.id=d_cstm_expense_value.model_id AND d_cstm_expense_value.var_id="' . $expense_value_id . '")' . "\n";
                $archived_query_expenses_data = implode("\n", $archived_sql_expenses_data);
                $archived_expenses_by_projects = $registry['db']->GetAssoc($archived_query_expenses_data);

                foreach ($archived_expenses_by_projects as $project_id => $project_archived_sum) {
                    if (isset($expenses_by_projects[$project_id])) {
                        $expenses_by_projects[$project_id] += $project_archived_sum;
                    } else {
                        $expenses_by_projects[$project_id] = $project_archived_sum;
                    }
                }

                foreach ($expenses_by_projects as $key_project => $expense_for_project) {
                    if (isset($projects_info[$key_project])) {
                        $projects_info[$key_project]['expenses'] = $expense_for_project;
                    }
                }
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($projects_info, 0);
            } else {
                $results = $projects_info;
            }

            return $results;
        }
    }
?>
