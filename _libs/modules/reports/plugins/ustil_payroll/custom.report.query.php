<?php

class Ustil_Payroll extends Reports {

    /**
     * Default settings
     * @var array
     */
    private static $default_settings = array(
        'customer_types' => PH_CUSTOMER_EMPLOYEE,
        'display_num_past_months' => 2,
        'num_working_hours' => 8,
        'num_working_days' => 22,
        'sickness_num_days_paid' => 3,
        'sickness_insurable_earnings_ratio' => 0.7,
    );

    /**
     * Items that reduce or increase salary
     * @var array
     */
    public static $deductions_bonuses = array(
        // +
        'production',
        'installation',
        'subsidy',
        'overtime',
        'bonus',
        // -
        'social_security',
        'advance',
        'leave',
        'sickness',
        'absence',
        'loan',
        'fine',
        'distraint',
        'correction',
    );

    /**
     * Gets settings from reports table and some defaults, if some of the former are missing
     *
     * @param Registry $registry - the main registry
     * @param string $report_name - name of report plugin
     * @return array - report settings
     */
    public static function getReportSettings(&$registry, $report_name = '') {
        return parent::getReportSettings($registry, $report_name ?: strtolower(__CLASS__)) + self::$default_settings;
    }

    public static function buildQuery(&$registry, $filters = array()) {

        // set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            // default model language is the interface language
            $model_lang = $registry['lang'];
        }
        $db = &$registry['db'];
        /**
         * In debug mode SQL queries are printed
         * @var bool $debug
         */
        $debug = $registry['currentUser']->get('role') == PH_ROLES_ADMIN && $registry['currentUser']->get('id') == PH_USERS_ADMIN && $registry['action'] == 'index' && !empty($filters['debug']);
        if ($debug) {
            $db->debug = 1;
        }

        // prepare the filters
        if (empty($filters['period'])) {
            $filters['period'] = date_create(strftime('%Y-%m-01'))->format('Y-m-01');
        }
        foreach (array('customer', 'department', 'office') as $af) {
            if (!empty($filters[$af])) {
                $filters[$af] = array_filter(array_unique(filter_var(
                    $filters[$af],
                    FILTER_VALIDATE_INT,
                    array(
                        'flags' => FILTER_FORCE_ARRAY,
                        'options' => array(
                            'default' => 0,
                            'min_range' => 1,
                        ),
                    ))));
            }
        }
        // prepare report settings
        $settings = self::getReportSettings($registry);

        $employee_vars = self::getVarIds($registry, 'customer_types');

        // prepare the array for the final results
        $final_results = array();
        $period_end_date = date_create($filters['period'])->format('Y-m-t');
        $country_code = Calendars_Calendar::getCountryCode($registry);

        $sql = array(
            'comment' => '/* main query */',
            'select' => array(
                'id' => 'c.id',
                'type' => 'c.type',
                'active' => 'c.active',
                'name' => 'TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname))',
                'office_name' => 'oi18n.name',
                'department_name' => 'di18n.name',
                'salary_total' => 'CONVERT(IFNULL(ccstm_salary.value, 0), DECIMAL(11, 2))',
                'minimum_income' => 'CONVERT(IFNULL(ccstm_minimum_income.value, 0), DECIMAL(11, 2))',
                'insurable_earnings' => 'CONVERT(IFNULL(ccstm_insurable_earnings.value, 0), DECIMAL(11, 2))',
                'deduct_social_security' => "IF(ccstm_social_security.value = '{$settings['employee_social_security_employee']}', 1, 0)",
                'start_date' => "IF(ccstm_start_date.value > '{$filters['period']}', ccstm_start_date.value, '')",
                'end_date' => "IF(ccstm_end_date.value != '' AND ccstm_end_date.value < '{$period_end_date}', ccstm_end_date.value, '')",
                // here we will collect all working days within period that employee was not working for some reason
                'days_absence' => '0',
            ),
            'from' => 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c',
            'joins' => array(
                'inner' => array(),
                'left' => array(
                    'ci18n' => array(
                        'table' => DB_TABLE_CUSTOMERS_I18N,
                        'clause' => array(
                            'ci18n.parent_id = c.id',
                            "ci18n.lang = '{$model_lang}'",
                        ),
                    ),
                    'ccstm_office' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_office.model_id = c.id',
                            "ccstm_office.var_id IN ({$employee_vars[$settings['employee_office_var']]})",
                        ),
                    ),
                    'oi18n' => array(
                        'table' => DB_TABLE_OFFICES_I18N,
                        'clause' => array(
                            'oi18n.parent_id = ccstm_office.value',
                            "oi18n.lang = '{$model_lang}'",
                        ),
                    ),
                    'di18n' => array(
                        'table' => DB_TABLE_DEPARTMENTS_I18N,
                        'clause' => array(
                            'di18n.parent_id = c.department',
                            "di18n.lang = '{$model_lang}'",
                        ),
                    ),
                    'ccstm_salary' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_salary.model_id = c.id',
                            "ccstm_salary.var_id IN ({$employee_vars[$settings['employee_salary_var']]})",
                        ),
                    ),
                    'ccstm_minimum_income' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_minimum_income.model_id = c.id',
                            "ccstm_minimum_income.var_id IN ({$employee_vars[$settings['employee_minimum_income_var']]})",
                        ),
                    ),
                    'ccstm_insurable_earnings' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_insurable_earnings.model_id = c.id',
                            "ccstm_insurable_earnings.var_id IN ({$employee_vars[$settings['employee_insurable_earnings_var']]})",
                        ),
                    ),
                    'ccstm_social_security' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_social_security.model_id = c.id',
                            "ccstm_social_security.var_id IN ({$employee_vars[$settings['employee_social_security_var']]})",
                        ),
                    ),
                    'ccstm_start_date' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_start_date.model_id = c.id',
                            "ccstm_start_date.var_id IN ({$employee_vars[$settings['employee_start_date_var']]})",
                        ),
                    ),
                    'ccstm_end_date' => array(
                        'table' => DB_TABLE_CUSTOMERS_CSTM,
                        'clause' => array(
                            'ccstm_end_date.model_id = c.id',
                            "ccstm_end_date.var_id IN ({$employee_vars[$settings['employee_end_date_var']]})",
                        ),
                    ),
                ),
            ),
            'where' => array(
                "c.type IN ({$settings['customer_types']})",
                (!empty($filters['customer']) ? 'c.id IN (' . implode(',', $filters['customer']) . ')' : 'c.active = 1'),
                'c.deleted_by = 0',
                (!empty($filters['department']) ? 'c.department IN (' . implode(',', $filters['department']) . ')' : ''),
                (!empty($filters['office']) ? 'ccstm_office.value IN (' . implode(',', $filters['office']) . ')' : ''),
            ),
            'group' => 'GROUP BY id',
            'order' => 'ORDER BY name',
        );
        $final_results = $db->GetAssoc(self::prepareQuery($sql));

        if ($final_results) {
            list($year, $month) = explode('-', $filters['period']);
            $month = intval($month);
            $deductions_bonuses_all = array_fill_keys(self::$deductions_bonuses, array());
            $employee_ids_list = implode(',', array_keys(array_filter($final_results, function($a) { return $a['type'] == PH_CUSTOMER_EMPLOYEE; }))) ?: '0';

            /************************ DEDUCTIONS **************************/

            // 1. social_security
            $deductions_bonuses_all['social_security'] = array_keys(array_filter($final_results, function($a) { return !empty($a['deduct_social_security']); }));

            if ($deductions_bonuses_all['social_security']) {
                $deductions_bonuses_all['social_security'] = $db->GetAssoc("
                    /* social security */
                    SELECT gt2.{$settings['social_security_employee_var']}, ROUND(-1 * SUM(gt2.price), 2) AS price
                    FROM " . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer
                    JOIN " . DB_TABLE_GT2_DETAILS . " AS gt2
                      ON gt2.model = 'Finance_Expenses_Reason' AND gt2.model_id = fer.id
                    WHERE fer.type = '{$settings['fin_expense_type_social_security']}'
                      AND gt2.{$settings['social_security_employee_var']} IN (" . implode(',', $deductions_bonuses_all['social_security']) . ")
                      AND fer.active = 1
                      AND gt2.{$settings['social_security_month_var']} = '{$month}'
                      AND gt2.{$settings['social_security_year_var']} = '{$year}'
                    GROUP BY gt2.{$settings['social_security_employee_var']}"
                );
            }

            // 2. advance
            $deductions_bonuses_all['advance'] = $db->GetAssoc("
                /* advance */
                SELECT fer.customer, ROUND(-1 * SUM(gt2.price), 2) AS price
                FROM " . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer
                JOIN " . DB_TABLE_GT2_DETAILS . " AS gt2
                  ON gt2.model = 'Finance_Expenses_Reason' AND gt2.model_id = fer.id
                WHERE fer.type = '{$settings['fin_expense_type_advance']}'
                  AND fer.customer IN ({$employee_ids_list})
                  AND fer.active = 1
                  AND gt2.{$settings['advance_month_var']} = '{$month}'
                  AND gt2.{$settings['advance_year_var']} = '{$year}'
                GROUP BY fer.customer"
            );

            // 3. leave
            $leave_vars = self::getVarIds($registry, 'doc_type_leave');
            $deductions_bonuses_all['leave'] = $db->GetAssoc("
                /* unpaid leave */
                SELECT d.customer,
                GROUP_CONCAT(CONCAT(dcstm_start_date.value, '|', dcstm_finish_date.value, '|', dcstm_days.value) ORDER BY dcstm_start_date.value ASC) AS leaves
                FROM " . DB_TABLE_DOCUMENTS . " AS d
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_start_date
                  ON d.id = dcstm_start_date.model_id
                    AND dcstm_start_date.var_id = '{$leave_vars[$settings['leave_start_date_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_finish_date
                  ON d.id = dcstm_finish_date.model_id
                    AND dcstm_finish_date.var_id = '{$leave_vars[$settings['leave_finish_date_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_days
                  ON d.id = dcstm_days.model_id
                    AND dcstm_days.var_id = '{$leave_vars[$settings['leave_days_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_type
                  ON d.id = dcstm_type.model_id
                    AND dcstm_type.var_id = '{$leave_vars[$settings['leave_type_var']]}'
                WHERE d.type = '{$settings['doc_type_leave']}'
                  AND d.customer IN ({$employee_ids_list})
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.status = 'closed'
                  AND d.substatus = '{$settings['leave_substatus_approved']}'
                  AND dcstm_type.value = '{$settings['leave_type_unpaid']}'
                  AND dcstm_finish_date.value >= '{$filters['period']}'
                  AND dcstm_start_date.value <= '{$period_end_date}'
                GROUP BY d.customer"
            );
            foreach ($deductions_bonuses_all['leave'] as $id => $leaves) {
                // calculate number of working days from each unpaid leave that are within period
                $deductions_bonuses_all['leave'][$id] = 0;
                $leaves = explode(',', $leaves);
                foreach ($leaves as $dates) {
                    $dates = explode('|', $dates);
                    if ($dates[0] >= $filters['period'] && $dates[1] <= $period_end_date) {
                        $deductions_bonuses_all['leave'][$id] += $dates[2];
                    } else {
                        if ($dates[0] < $filters['period']) {
                            $dates[0] = $filters['period'];
                        }
                        if ($dates[1] > $period_end_date) {
                            $dates[1] = $period_end_date;
                        }
                        $deductions_bonuses_all['leave'][$id] += Calendars_Calendar::getWorkingDays($registry, $dates[0], $dates[1]);
                    }
                }
                // add to total days absence
                $final_results[$id]['days_absence'] += $deductions_bonuses_all['leave'][$id];

                if (bccomp($final_results[$id]['salary_total'], 0, 2) == 1) {
                    $deductions_bonuses_all['leave'][$id] = round(-1 * $deductions_bonuses_all['leave'][$id] * $final_results[$id]['salary_total'] / $settings['num_working_days'], 2);
                } else {
                    unset($deductions_bonuses_all['leave'][$id]);
                }
            }

            // 4. sickness
            $sickness_vars = self::getVarIds($registry, 'doc_type_sickness');
            $deductions_bonuses_all['sickness'] = $db->GetAssoc("
                /* sickness leave */
                SELECT d.employee AS customer,
                GROUP_CONCAT(CONCAT(dcstm_start_date.value, '|', dcstm_finish_date.value, '|', dcstm_days.value) ORDER BY dcstm_start_date.value ASC) AS leaves
                FROM " . DB_TABLE_DOCUMENTS . " AS d
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_start_date
                  ON d.id = dcstm_start_date.model_id
                    AND dcstm_start_date.var_id = '{$sickness_vars[$settings['sickness_start_date_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_finish_date
                  ON d.id = dcstm_finish_date.model_id
                    AND dcstm_finish_date.var_id = '{$sickness_vars[$settings['sickness_finish_date_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_days
                  ON d.id = dcstm_days.model_id
                    AND dcstm_days.var_id = '{$sickness_vars[$settings['sickness_days_var']]}'
                WHERE d.type = '{$settings['doc_type_sickness']}'
                  AND d.employee IN ({$employee_ids_list})
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.status = 'closed'
                  AND d.substatus = '{$settings['sickness_substatus_accepted']}'
                  AND dcstm_finish_date.value >= '{$filters['period']}'
                  AND dcstm_start_date.value <= '{$period_end_date}'
                GROUP BY d.employee"
            );
            foreach ($deductions_bonuses_all['sickness'] as $id => $leaves) {
                $days_paid = $days_deducted = 0;
                $leaves = explode(',', $leaves);
                foreach ($leaves as $dates) {
                    $dates = explode('|', $dates);
                    if ($dates[0] <= $period_end_date) {
                        // end check of sickness days within searched period
                        if ($dates[1] > $period_end_date) {
                            $dates[1] = $period_end_date;
                        }

                        // total duration until end of searched period
                        $total_working_days = Calendars_Calendar::getWorkingDays($registry, $dates[0], $dates[1]);
                        // first 3 days (or less if duration is shorter)
                        $current_days_paid = min($total_working_days, $settings['sickness_num_days_paid']);
                        // last paid date (it is a working date)
                        $paid_until = Calendars_Calendar::calcDateOnWorkingDays($registry, $dates[0], $current_days_paid - intval(Calendars_Calendar::isWorkingDay($registry, $dates[0])));

                        // if any of the paid days are in searched period
                        if ($paid_until >= $filters['period']) {
                            // if any of the paid days are before searhed period
                            if ($dates[0] < $filters['period']) {
                                $current_days_paid = Calendars_Calendar::getWorkingDays($registry, $filters['period'], $paid_until);
                            }
                        } else {
                            $current_days_paid = 0;
                        }
                        // sickness days are paid to employee
                        $days_paid += $current_days_paid;

                        // calculate all sickness days within searched period
                        if ($dates[0] < $filters['period']) {
                            $dates[0] = $filters['period'];
                        }
                        if ($dates[0] <= $dates[1]) {
                            // sickness days are deducted from fixed monthly salary
                            $days_deducted += Calendars_Calendar::getWorkingDays($registry, $dates[0], $dates[1]);
                        }
                    }
                }

                // add to total days absence
                $final_results[$id]['days_absence'] += $days_deducted;

                $deductions_bonuses_all['sickness'][$id] = 0;
                if ($days_paid && bccomp($final_results[$id]['insurable_earnings'], 0, 2) == 1) {
                    // add - up to 3 days per sickness leave - for all employees
                    $deductions_bonuses_all['sickness'][$id] += round($days_paid * $final_results[$id]['insurable_earnings'] * $settings['sickness_insurable_earnings_ratio'] / $settings['num_working_days'], 2);
                }
                if ($days_deducted && bccomp($final_results[$id]['salary_total'], 0, 2) == 1) {
                    // subtract - all sickness day within month - for employees with fixed salary
                    $deductions_bonuses_all['sickness'][$id] += round(-1 * $days_deducted * $final_results[$id]['salary_total'] / $settings['num_working_days'], 2);
                }
                $deductions_bonuses_all['sickness'][$id] = round($deductions_bonuses_all['sickness'][$id], 2);
            }

            // 5. absence
            $absence_vars = self::getVarIds($registry, 'doc_type_absence');
            $deductions_bonuses_all['absence'] = $db->GetAssoc("
                /* absence */
                SELECT d.customer, SUM(IF(dcstm_hours.value IS NULL || dcstm_hours.value = 0, '{$settings['num_working_hours']}', dcstm_hours.value)) AS hours
                FROM " . DB_TABLE_DOCUMENTS . " AS d
                LEFT JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_hours
                  ON d.id = dcstm_hours.model_id AND dcstm_hours.var_id = '{$absence_vars[$settings['absence_hours_var']]}'
                WHERE d.type = '{$settings['doc_type_absence']}'
                  AND d.customer IN ({$employee_ids_list})
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.status = 'closed'
                  AND d.date >= '{$filters['period']}'
                  AND d.date <= '{$period_end_date}'
                GROUP BY d.customer"
            );
            foreach ($deductions_bonuses_all['absence'] as $id => $hours) {

                // add to total days absence (these might be partial days as values are in hours)
                $final_results[$id]['days_absence'] += $hours / $settings['num_working_hours'];

                if (bccomp($final_results[$id]['salary_total'], 0, 2) == 1) {
                    $deductions_bonuses_all['absence'][$id] = round(-1 * $hours * $final_results[$id]['salary_total'] / ($settings['num_working_hours'] * $settings['num_working_days']), 2);
                } else {
                    unset($deductions_bonuses_all['absence'][$id]);
                }
            }

            // 6. loan
            $deductions_bonuses_all['loan'] = $db->GetAssoc("
                /* loan */
                SELECT t.customer, ROUND(-1 * SUM(t.price), 2) AS price FROM (
                    SELECT fir.id, fir.customer,
                    IF(SUM(gt2.price) < fir.total_with_vat - SUM(IFNULL(fb.paid_amount, 0)), SUM(gt2.price), fir.total_with_vat - SUM(IFNULL(fb.paid_amount, 0))) AS price
                    FROM " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                    JOIN " . DB_TABLE_GT2_DETAILS . " AS gt2
                      ON gt2.model = 'Finance_Incomes_Reason' AND gt2.model_id = fir.id
                    LEFT JOIN " . DB_TABLE_FINANCE_BALANCE . " AS fb
                      ON fb.paid_to_model_name = 'Finance_Incomes_Reason' AND fb.paid_to = fir.id
                    WHERE fir.type = '{$settings['fin_income_type_loan']}'
                      AND fir.customer IN ({$employee_ids_list})
                      AND fir.active = 1
                      AND fir.status = 'finished'
                      AND fir.payment_status IN ('unpaid', 'partial')
                    GROUP BY fir.id
                ) AS t
                GROUP BY t.customer"
            );

            // 7. fine
            $deductions_bonuses_all['fine'] = $db->GetAssoc("
                /* fine */
                SELECT t.customer, ROUND(-1 * SUM(t.price), 2) AS price FROM (
                    SELECT fir.id, fir.customer, fir.total_with_vat - SUM(IFNULL(fb.paid_amount, 0)) AS price
                    FROM " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                    JOIN " . DB_TABLE_GT2_DETAILS . " AS gt2
                      ON gt2.model = 'Finance_Incomes_Reason' AND gt2.model_id = fir.id
                    LEFT JOIN " . DB_TABLE_FINANCE_BALANCE . " AS fb
                      ON fb.paid_to_model_name = 'Finance_Incomes_Reason' AND fb.paid_to = fir.id
                    WHERE fir.type = '{$settings['fin_income_type_fine']}'
                      AND fir.customer IN ({$employee_ids_list})
                      AND fir.active = 1
                      AND fir.status = 'finished'
                      AND fir.payment_status IN ('unpaid', 'partial')
                      AND fir.issue_date <= '{$period_end_date}'
                    GROUP BY fir.id
                ) AS t
                GROUP BY t.customer"
            );

            // 8. distraint
            $deductions_bonuses_all['distraint'] = $db->GetAssoc("
                /* distraint */
                SELECT fir.customer,
                    ROUND(-1 * (IF(SUM(gt2.price) < fir.total_with_vat - SUM(IFNULL(fb.paid_amount, 0)), SUM(gt2.price), fir.total_with_vat - SUM(IFNULL(fb.paid_amount, 0)))), 2) AS price
                FROM (
                    SELECT MIN(fir.id) AS id
                    FROM " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                    WHERE fir.type = '{$settings['fin_income_type_distraint']}'
                      AND fir.customer IN ({$employee_ids_list})
                      AND fir.active = 1
                      AND fir.status = 'finished'
                      AND fir.payment_status IN ('unpaid', 'partial')
                      AND fir.total_with_vat > 0
                    GROUP BY fir.customer
                ) AS t
                JOIN " . DB_TABLE_FINANCE_INCOMES_REASONS . " AS fir
                  ON fir.id = t.id
                JOIN " . DB_TABLE_GT2_DETAILS . " AS gt2
                  ON gt2.model = 'Finance_Incomes_Reason' AND gt2.model_id = fir.id
                LEFT JOIN " . DB_TABLE_FINANCE_BALANCE . " AS fb
                  ON fb.paid_to_model_name = 'Finance_Incomes_Reason' AND fb.paid_to = fir.id
                GROUP BY fir.id"
            );

            // 9. correction
            $correction_vars = self::getVarIds($registry, 'doc_type_correction');
            $deductions_bonuses_all['correction'] = $db->GetAssoc("
                /* correction */
                SELECT dcstm_employee.cstm_model_id AS customer,
                ROUND(SUM(dcstm_amount.value * (CASE dcstm_ratio.value " .
                implode('', array_map(
                    function($v, $k) { $k = str_replace('correction_ratio', '', $k); return "WHEN $k THEN $v "; },
                    array_intersect_key($settings, array_flip(array_filter(array_keys($settings), function($a) { return preg_match('#^correction_ratio\d+$#', $a); }))),
                    array_filter(array_keys($settings), function($a) { return preg_match('#^correction_ratio\d+$#', $a); })
                )) .
                " ELSE 1 END)), 2) AS price
                FROM " . DB_TABLE_DOCUMENTS . " AS d
                JOIN " . DB_TABLE_CSTM_RELATIVES . " AS dcstm_employee
                  ON d.id = dcstm_employee.model_id
                    AND dcstm_employee.model = 'Document'
                    AND dcstm_employee.cstm_model = 'Customer'
                    AND dcstm_employee.var_id = '{$correction_vars[$settings['correction_employee_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_amount
                  ON d.id = dcstm_amount.model_id
                    AND dcstm_amount.var_id = '{$correction_vars[$settings['correction_amount_var']]}'
                    AND dcstm_employee.num = dcstm_amount.num
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_ratio
                  ON d.id = dcstm_ratio.model_id
                    AND dcstm_ratio.var_id = '{$correction_vars[$settings['correction_ratio_var']]}'
                    AND dcstm_amount.num = dcstm_ratio.num
                WHERE d.type = '{$settings['doc_type_correction']}'
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.date BETWEEN '{$filters['period']}' AND '{$period_end_date}' /* for now search corrections only for searched month, will be changed in Stage 2 */
                  AND d.status = 'closed'
                  AND dcstm_employee.cstm_model_id IN ({$employee_ids_list})
                GROUP BY dcstm_employee.cstm_model_id"
            );

            /************************ BONUSES / EARNINGS **************************/

            // 10. overtime
            $timesheet_vars = self::getVarIds($registry, 'doc_type_timesheet');
            $deductions_bonuses_all['overtime'] = $db->GetAssoc("
                /* overtime */
                SELECT dcstm_employee.cstm_model_id AS customer,
                ROUND(SUM(dcstm_overtime.value * IFNULL(IF(cnwd.date IS NULL, ccstm_weekday_overtime.value, ccstm_holiday_overtime.value), 0)), 2) AS price
                FROM " . DB_TABLE_DOCUMENTS . " AS d
                JOIN " . DB_TABLE_CSTM_RELATIVES . " AS dcstm_employee
                  ON dcstm_employee.model = 'Document' AND d.id = dcstm_employee.model_id AND dcstm_employee.cstm_model = 'Customer'
                    AND dcstm_employee.var_id = '{$timesheet_vars[$settings['timesheet_employee_var']]}'
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dcstm_overtime
                  ON dcstm_employee.model_id = dcstm_overtime.model_id
                    AND dcstm_overtime.var_id = '{$timesheet_vars[$settings['timesheet_overtime_var']]}'
                    AND dcstm_employee.num = dcstm_overtime.num
                LEFT JOIN " . DB_TABLE_COUNTRY_NONWORKDAYS . " AS cnwd
                  ON d.date = cnwd.date AND cnwd.country = '{$country_code}'
                LEFT JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS ccstm_weekday_overtime
                  ON dcstm_employee.cstm_model_id = ccstm_weekday_overtime.model_id
                    AND ccstm_weekday_overtime.var_id = '{$employee_vars[$settings['employee_weekday_overtime_var']]}'
                LEFT JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS ccstm_holiday_overtime
                  ON dcstm_employee.cstm_model_id = ccstm_holiday_overtime.model_id
                    AND ccstm_holiday_overtime.var_id = '{$employee_vars[$settings['employee_holiday_overtime_var']]}'
                WHERE d.type = '{$settings['doc_type_timesheet']}'
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.date BETWEEN '{$filters['period']}' AND '{$period_end_date}'
                  AND dcstm_employee.cstm_model_id IN ({$employee_ids_list})
                GROUP BY dcstm_employee.cstm_model_id"
            );

            // 11. bonus (turnover)
            $deductions_bonuses_all['bonus'] = array();

            /**
             * WHEN-THEN string to be used when checking for match between document type and kind_sht values
             * @var string $type_to_kind_sht_mapping
             */
            $type_to_kind_sht_mapping = implode('', array_map(
                function($v, $k) { $k = str_replace('type_opt_', '', $k); return "WHEN $k THEN $v "; },
                array_intersect_key($settings, array_flip(array_filter(array_keys($settings), function($a) { return preg_match('#^type_opt_\d+$#', $a); }))),
                array_filter(array_keys($settings), function($a) { return preg_match('#^type_opt_\d+$#', $a); })
            ));

            // turnover (employees only)
            $turnover_query = array(
                'comment' => '',
                'select' => array(
                    'customer' => 'c.id',
                    'price' => "ROUND(SUM(dcstm_amount.value * ccstm_percentage.value / 100), 2)",
                ),
                'from' => 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c',
                'joins' => array(
                    'inner' => array(
                        'ccstm_turnover' => array(
                            'table' => DB_TABLE_CUSTOMERS_CSTM,
                            'clause' => array(
                                'ccstm_turnover.model_id = c.id',
                                "ccstm_turnover.value != ''",
                            ),
                        ),
                        'ccstm_percentage' => array(
                            'table' => DB_TABLE_CUSTOMERS_CSTM,
                            'clause' => array(
                                'ccstm_percentage.model_id = ccstm_turnover.model_id',
                                'ccstm_percentage.num = ccstm_turnover.num',
                                "ccstm_percentage.value != ''",
                            ),
                        ),
                        'd' => array(
                            'table' => DB_TABLE_DOCUMENTS,
                            'clause' => array(
                                "d.type IN ({$settings['doc_types_order']})",
                                'd.active = 1',
                                'd.deleted_by = 0',
                                "d.substatus NOT IN ({$settings['order_substatuses_failed']})",
                                "DATE(d.added) BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                            ),
                        ),
                        'dcstm_amount' => array(
                            'table' => DB_TABLE_DOCUMENTS_CSTM,
                            'clause' => array(
                                'dcstm_amount.model_id = d.id',
                                "dcstm_amount.var_id IN (" . (implode(',',
                                    self::getVarIds(
                                        $registry,
                                        'doc_types_order',
                                        "name IN ('" . implode("', '", preg_split('#\s*,\s*#', $settings['order_amount_vars'])) . "')"
                                    )
                                ) ?: 0) . ")",
                            ),
                        ),
                    ),
                ),
                'where' => array(
                    "c.id IN ({$employee_ids_list})",
                ),
                'group' => 'GROUP BY c.id',
                'order' => 'ORDER BY c.id',
            );

            $turnover_specific = array(
                // 11.1. orders added by (employees with users)
                'added_by' => array(
                    'comment' => '/* turnover - added_by */',
                    'joins' => array(
                        'inner' => array(
                            'ccstm_turnover' => array(
                                'clause' => array(
                                    "ccstm_turnover.var_id = '{$employee_vars[$settings['employee_turnover_customer_type_var']]}'",
                                ),
                            ),
                            'ccstm_percentage' => array(
                                'clause' => array(
                                    "ccstm_percentage.var_id = '{$employee_vars[$settings['employee_turnover_percentage_customer_type_var']]}'",
                                ),
                            ),
                            'c_order' => array(
                                'table' => DB_TABLE_CUSTOMERS,
                                'clause' => array(
                                    'c_order.id = d.customer',
                                ),
                            ),
                            'u' => array(
                                'table' => DB_TABLE_USERS,
                                'clause' => array(
                                    'u.employee = c.id',
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        'd.added_by = u.id',
                        '(' . implode(' OR ', array_map(
                            function($v, $k) { return preg_replace('#^.*(\d+)$#', 'ccstm_turnover.value = $1', $k) . " AND c_order.type IN ($v)"; },
                            array_intersect_key($settings, array_flip(array_filter(array_keys($settings), function($a) { return preg_match('#^employee_turnover_customer_type\d+$#', $a); }))),
                            array_filter(array_keys($settings), function($a) { return preg_match('#^employee_turnover_customer_type\d+$#', $a); })
                        )) . ')',
                    ),
                ),
                // 11.2. orders with specified customer
                'customer' => array(
                    'comment' => '/* turnover - customer */',
                    'joins' => array(
                        'inner' => array(
                            'ccstm_turnover' => array(
                                'clause' => array(
                                    "ccstm_turnover.var_id = '{$employee_vars[$settings['employee_turnover_customer_var']]}'",
                                ),
                            ),
                            'ccstm_percentage' => array(
                                'clause' => array(
                                    "ccstm_percentage.var_id = '{$employee_vars[$settings['employee_turnover_percentage_customer_var']]}'",
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        'd.customer = ccstm_turnover.value',
                    ),
                ),
                // 11.3. orders of customer with specified tag
                'tag' => array(
                    'comment' => '/* turnover - customer tag */',
                    'joins' => array(
                        'inner' => array(
                            'ccstm_turnover' => array(
                                'clause' => array(
                                    "ccstm_turnover.var_id = '{$employee_vars[$settings['employee_turnover_tag_var']]}'",
                                ),
                            ),
                            'ccstm_percentage' => array(
                                'clause' => array(
                                    "ccstm_percentage.var_id = '{$employee_vars[$settings['employee_turnover_percentage_tag_var']]}'",
                                ),
                            ),
                            'c_tag' => array(
                                'table' => DB_TABLE_TAGS_MODELS,
                                'clause' => array(
                                    "c_tag.model = 'Customer'",
                                    'c_tag.model_id = d.customer',
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        'c_tag.tag_id = ccstm_turnover.value',
                    ),
                ),
                // 11.4. orders with specified office
                'office' => array(
                    'comment' => '/* turnover - office */',
                    'joins' => array(
                        'inner' => array(
                            'ccstm_turnover' => array(
                                'clause' => array(
                                    "ccstm_turnover.var_id = '{$employee_vars[$settings['employee_turnover_office_var']]}'",
                                ),
                            ),
                            'ccstm_percentage' => array(
                                'clause' => array(
                                    "ccstm_percentage.var_id = '{$employee_vars[$settings['employee_turnover_percentage_office_var']]}'",
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        'd.office = ccstm_turnover.value',
                    ),
                ),
                // 11.5. orders of specified type (kind_sht)
                'type' => array(
                    'comment' => '/* turnover - type */',
                    'joins' => array(
                        'inner' => array(
                            'ccstm_turnover' => array(
                                'clause' => array(
                                    "ccstm_turnover.var_id = '{$employee_vars[$settings['employee_turnover_type_var']]}'",
                                ),
                            ),
                            'ccstm_percentage' => array(
                                'clause' => array(
                                    "ccstm_percentage.var_id = '{$employee_vars[$settings['employee_turnover_percentage_type_var']]}'",
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        "ccstm_turnover.value = CASE d.type {$type_to_kind_sht_mapping} END",
                    ),
                ),
            );

            foreach ($turnover_specific as $query_specific) {
                $bonuses = $db->GetAssoc(self::prepareQuery(array_merge_recursive($turnover_query, $query_specific)));
                foreach ($bonuses as $id => $amount) {
                    if (!array_key_exists($id, $deductions_bonuses_all['bonus'])) {
                        $deductions_bonuses_all['bonus'][$id] = 0;
                    }
                    $deductions_bonuses_all['bonus'][$id] += $amount;
                }
            }
            // end turnover

            // subsidy given to workers when their installation/production earnings are below specified minimum
            $deductions_bonuses_all['subsidy'] = array();

            // 12. installation earnings (employees + other workers)
            $deductions_bonuses_all['installation'] = array();

            /**
             * Installation specifics:
             *
             * All failed orders should be excluded. HOW to exclude them from accessories?
             *
             * Orders for construction, accessories and reclamation/repair
             * (types 16, 27 and 30) are excluded from installation of main products.
             *
             * Installation + consumables of gift blinds from order for windows (type 15) are excluded.
             *
             * Installation of main products is calculated by number (num_total)
             * if measure in Salary rate nomenclature (id 12399) is "pcs." (id 1)
             * or by quantity (name_total) otherwise.
             *
             * Installation of accessories is calculated from all data present
             * in corresponding grouping table of protocol, disregarding where
             * it came from and what nomenclatures it contains.
             *
             * Installation of construction services is calculated from all
             * data present in corresponding grouping table of protocol,
             * disregarding where it came from and what nomenclatures it contains.
             */

            $employee_ids_all_list = implode(',', array_keys($final_results)) ?: '0';
            $protocol_vars = self::getVarIds($registry, 'doc_type_installation_protocol');
            $salary_vars = self::getVarIds($registry, 'nom_type_salary_rate');
            $accessory_vars = self::getVarIds($registry, 'nom_type_accessory');
            $construction_vars = self::getVarIds($registry, 'nom_type_construction');

            // exclude installation of specified types even if present in main table of protocols
            // (these types should have no entries in the Salary rate nomenclature (id 12399) but exclude them anyway)
            $doc_types_order_installation = $settings['doc_types_order'];
            if (!empty($settings['order_types_exclude_installation'])) {
                $doc_types_order_installation = implode(',', array_diff(
                    preg_split('#\s*,\s*#', $doc_types_order_installation),
                    preg_split('#\s*,\s*#', $settings['order_types_exclude_installation'])
                )) ?: '0';
            }

            $installation_query = array(
                'comment' => '',
                'select' => array(
                    'customer' => 'dcstm_employee.cstm_model_id',
                ),
                'from' => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                'joins' => array(
                    'inner' => array(
                        'dcstm_employee' => array(
                            'table' => DB_TABLE_CSTM_RELATIVES,
                            'clause' => array(
                                "dcstm_employee.model = 'Document'",
                                "dcstm_employee.model_id = d.id",
                                "dcstm_employee.cstm_model = 'Customer'",
                                "dcstm_employee.var_id = '{$protocol_vars[$settings['protocol_employee_var']]}'",
                            ),
                        ),
                        'dcstm_ratio' => array(
                            'table' => DB_TABLE_DOCUMENTS_CSTM,
                            'clause' => array(
                                'dcstm_ratio.model_id = dcstm_employee.model_id',
                                'dcstm_ratio.num = dcstm_employee.num',
                                "dcstm_ratio.var_id = '{$protocol_vars[$settings['protocol_ratio_var']]}'",
                            ),
                        ),
                    ),
                    'left' => array(),
                ),
                'where' => array(
                    "d.type = '{$settings['doc_type_installation_protocol']}'",
                    'd.active = 1',
                    'd.deleted_by = 0',
                    "d.status = 'closed'",
                    "d.substatus != '{$settings['protocol_substatus_failed']}'",
                    "d.date BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                    "dcstm_employee.cstm_model_id IN ({$employee_ids_all_list})"
                ),
                'group' => 'GROUP BY dcstm_employee.cstm_model_id',
                'order' => 'ORDER BY dcstm_employee.cstm_model_id',
            );

            $installation_specific = array(
                // 12.1. main + consumables of main
                'main' => array(
                    'comment' => '/* installation - main products + consumables of main products */',
                    'select' => array(
                        'price' => "
                            ROUND(SUM(
                                CONVERT(IF(ncstm_measure.value = '{$settings['measure_num']}', dcstm_num.value, dcstm_qty.value), DECIMAL(10, 2)) *
                                (
                                    CONVERT(IFNULL(ncstm_rate.value, 0), DECIMAL(10, 2)) *
                                    CONVERT(dcstm_ratio.value, DECIMAL(25, 6)) +
                                    IF(dcstm_consumable.value IS NOT NULL, 1, 0) *
                                    CONVERT(IFNULL(ncstm_consumable_rate.value, 0), DECIMAL(10, 2))
                                )
                            ), 2)",
                    ),
                    'joins' => array(
                        'inner' => array(
                            'dcstm_order' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "dcstm_order.model = 'Document'",
                                    "dcstm_order.model_id = d.id",
                                    "dcstm_order.cstm_model = 'Document'",
                                    "dcstm_order.var_id = '{$protocol_vars[$settings['protocol_order_id_var']]}'",
                                ),
                            ),
                            'dcstm_order_name' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    "dcstm_order_name.model_id = dcstm_order.model_id",
                                    "dcstm_order_name.num = dcstm_order.num",
                                    "dcstm_order_name.var_id = '{$protocol_vars[$settings['protocol_order_name_var']]}'",
                                ),
                            ),
                            'd_order' => array(
                                'table' => DB_TABLE_DOCUMENTS,
                                'clause' => array(
                                    'd_order.id = dcstm_order.cstm_model_id',
                                    "d_order.type IN ({$doc_types_order_installation})",
                                    "d_order.substatus NOT IN ({$settings['order_substatuses_failed']})",
                                    // this way we ensure that data for installation of gift blinds is excluded
                                    'd_order.full_num = dcstm_order_name.value',
                                ),
                            ),
                            'dcstm_qty' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_qty.model_id = dcstm_order.model_id',
                                    'dcstm_qty.num = dcstm_order.num',
                                    "dcstm_qty.var_id = '{$protocol_vars[$settings['protocol_order_qty_var']]}'",
                                ),
                            ),
                            'dcstm_num' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_num.model_id = dcstm_qty.model_id',
                                    'dcstm_num.num = dcstm_qty.num',
                                    "dcstm_num.var_id = '{$protocol_vars[$settings['protocol_order_num_var']]}'",
                                ),
                            ),
                            'ncstm_type' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_type.model_id = '{$settings['salary_rate_id']}'",
                                    "ncstm_type.var_id = '{$salary_vars[$settings['salary_rate_installation_type_var']]}'",
                                    "ncstm_type.value = d_order.type",
                                ),
                            ),
                            'ncstm_measure' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_measure.model_id = ncstm_type.model_id",
                                    "ncstm_measure.num = ncstm_type.num",
                                    "ncstm_measure.var_id = '{$salary_vars[$settings['salary_rate_installation_measure_var']]}'",
                                ),
                            ),
                        ),
                        'left' => array(
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = ncstm_type.model_id",
                                    "ncstm_rate.num = ncstm_type.num",
                                    "ncstm_rate.var_id = '{$salary_vars[$settings['salary_rate_installation_rate_var']]}'",
                                ),
                            ),
                            'dcstm_consumable' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_consumable.model_id = dcstm_employee.model_id',
                                    'dcstm_consumable.num = dcstm_employee.num',
                                    "dcstm_consumable.var_id = '{$protocol_vars[$settings['protocol_consumable_var']]}'",
                                    "dcstm_consumable.value = '{$settings['protocol_consumable_yes']}'",
                                ),
                            ),
                            'ncstm_consumable_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_consumable_rate.model_id = ncstm_type.model_id",
                                    "ncstm_consumable_rate.num = ncstm_type.num",
                                    "ncstm_consumable_rate.var_id = '{$salary_vars[$settings['salary_rate_consumable_rate_var']]}'",
                                ),
                            ),
                        ),
                    ),
                ),
                // 12.2. accessories + consumables of accessories
                //TODO exclude failed orders - how do we know?
                'accessories' => array(
                    'comment' => '/* installation - accessories + consumables of accessories */',
                    'select' => array(
                        'price' => "
                            ROUND(SUM(
                                CONVERT(dcstm_qty.value, DECIMAL(25, 6)) *
                                (
                                    CONVERT(IFNULL(ncstm_rate.value, 0), DECIMAL(10, 2)) *
                                    CONVERT(dcstm_ratio.value, DECIMAL(25, 6)) +
                                    IF(dcstm_consumable.value IS NOT NULL, 1, 0) *
                                    CONVERT(IFNULL(ncstm_consumable_rate.value, 0), DECIMAL(10, 2))
                                )
                            ), 2)",
                    ),
                    'joins' => array(
                        'inner' => array(
                            'dcstm_acc' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_acc.model_id = dcstm_employee.model_id',
                                    "dcstm_acc.var_id = '{$protocol_vars[$settings['protocol_accessories_id_var']]}'",
                                ),
                            ),
                            'dcstm_qty' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_qty.model_id = dcstm_acc.model_id',
                                    'dcstm_qty.num = dcstm_acc.num',
                                    "dcstm_qty.var_id = '{$protocol_vars[$settings['protocol_accessories_qty_var']]}'",
                                ),
                            ),
                        ),
                        'left' => array(
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = dcstm_acc.value",
                                    "ncstm_rate.var_id = '{$accessory_vars[$settings['accessory_installation_rate_var']]}'",
                                ),
                            ),
                            'dcstm_consumable' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_consumable.model_id = dcstm_employee.model_id',
                                    'dcstm_consumable.num = dcstm_employee.num',
                                    "dcstm_consumable.var_id = '{$protocol_vars[$settings['protocol_consumable_var']]}'",
                                    "dcstm_consumable.value = '{$settings['protocol_consumable_yes']}'",
                                ),
                            ),
                            'ncstm_consumable_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_consumable_rate.model_id = dcstm_acc.value",
                                    "ncstm_consumable_rate.var_id = '{$accessory_vars[$settings['accessory_consumable_rate_var']]}'",
                                ),
                            ),
                        ),
                    ),
                ),
                // 12.3. construction services (orders of type 16)
                'construction' => array(
                    'comment' => '/* installation - construction services */',
                    'select' => array(
                        'price' => "
                            ROUND(SUM(
                                CONVERT(dcstm_qty.value, DECIMAL(10, 2)) *
                                CONVERT(ncstm_rate.value, DECIMAL(10, 2)) *
                                CONVERT(dcstm_ratio.value, DECIMAL(25, 6))
                            ), 2)",
                    ),
                    'joins' => array(
                        'inner' => array(
                            'dcstm_con' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "dcstm_con.model = 'Document'",
                                    'dcstm_con.model_id = dcstm_employee.model_id',
                                    "dcstm_con.cstm_model = 'Nomenclature'",
                                    "dcstm_con.var_id = '{$protocol_vars[$settings['protocol_construction_id_var']]}'",
                                ),
                            ),
                            'dcstm_qty' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_qty.model_id = dcstm_con.model_id',
                                    'dcstm_qty.num = dcstm_con.num',
                                    "dcstm_qty.var_id = '{$protocol_vars[$settings['protocol_construction_qty_var']]}'",
                                ),
                            ),
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = dcstm_con.cstm_model_id",
                                    "ncstm_rate.var_id = '{$construction_vars[$settings['construction_installation_rate_var']]}'",
                                ),
                            ),
                        ),
                    ),
                ),
            );

            foreach ($installation_specific as $key => $query_specific) {
                $bonuses = $db->GetAssoc(self::prepareQuery(array_merge_recursive($installation_query, $query_specific)));
                foreach ($bonuses as $id => $amount) {
                    if (!array_key_exists($id, $deductions_bonuses_all['installation'])) {
                        $deductions_bonuses_all['installation'][$id] = 0;
                    }
                    $deductions_bonuses_all['installation'][$id] += $amount;
                }
            }
            // end installation

            // 13. production earnings (employees only)
            $deductions_bonuses_all['production'] = array();

            // check if any of the selected employees works in production
            $employee_workplaces = $db->GetOne("
                SELECT GROUP_CONCAT(DISTINCT ccstm_workplace.cstm_model_id)
                FROM " . DB_TABLE_CSTM_RELATIVES . " AS ccstm_workplace
                WHERE ccstm_workplace.model = 'Customer'
                AND ccstm_workplace.model_id IN ({$employee_ids_list})
                AND ccstm_workplace.cstm_model = 'Nomenclature'
                AND ccstm_workplace.var_id = '{$employee_vars[$settings['employee_production_workplace_var']]}'
            ");

            if ($employee_workplaces) {
                $request_vars = self::getVarIds($registry, 'doc_type_production_request');

                // for horizontal blinds
                $blinds_type_vars = self::getVarIds($registry, 'nom_type_blinds_type');
                $blinds_vars = self::getVarIds($registry, 'doc_types_order',
                    "model_type IN ('{$settings['order_type_blinds']}', '{$settings['order_type_windows']}')
                    AND name IN ('{$settings['blinds_model_var']}', '{$settings['blinds_width_var']}', '{$settings['blinds_area_var']}')"
                );

                // special way of calculation for production of horizontal blinds
                // (orders of type 7 and gift blinds from orders of type 15)
                $workplace_earnings_blinds_query = array(
                    'comment' => '/* production - workplace earnings - main products (horizontal blinds) */',
                    'select' => array(
                        'idx' => "CONCAT(ncstm_workplace.value, '|', dcstm_date_produced.value)",
                        'amount' => "
                            ROUND(SUM(
                                CONVERT(dcstm_area.value, DECIMAL(10, 2)) *
                                CONVERT(
                                    IF(CONVERT(dcstm_width.value, DECIMAL(10, 2)) >= CONVERT(ncstm_model_width.value, DECIMAL(10, 2)) AND ncstm_model_rate.value != '',
                                    ncstm_model_rate.value, 1), DECIMAL(10, 2)
                                ) *
                                CONVERT(ncstm_rate.value, DECIMAL(10, 2))
                            ), 2)
                        ",
                    ),
                    'from' => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                    'joins' => array(
                        'inner' => array(
                            'dcstm_date_produced' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_date_produced.model_id = d.id',
                                    "dcstm_date_produced.var_id = '{$request_vars[$settings['request_date_produced_var']]}'",
                                ),
                            ),
                            /* 'dcstm_kind' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_kind.model_id = d.id',
                                    "dcstm_kind.var_id = '{$request_vars[$settings['request_kind_var']]}'",
                                    // kind_sht should always be horizontal blinds
                                    "dcstm_kind.value = '{$settings["type_opt_{$settings['order_type_blinds']}"]}'",
                                ),
                            ), */
                            'dr' => array(
                                'table' => DB_TABLE_DOCUMENTS_RELATIVES,
                                'clause' => array(
                                    "dr.parent_id = d.id",
                                    "dr.parent_model_name = 'Document'",
                                    "dr.link_to_model_name = 'Document'",
                                ),
                            ),
                            'd_order' => array(
                                'table' => DB_TABLE_DOCUMENTS,
                                'clause' => array(
                                    'd_order.id = dr.link_to',
                                    "d_order.type IN ('{$settings['order_type_blinds']}', '{$settings['order_type_windows']}')",
                                ),
                            ),
                            // from GT of order
                            'dcstm_model' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "dcstm_model.model = 'Document'",
                                    "dcstm_model.model_id = d_order.id",
                                    "dcstm_model.cstm_model = 'Nomenclature'",
                                    "dcstm_model.var_id IN ({$blinds_vars[$settings['blinds_model_var']]})",
                                ),
                            ),
                            'dcstm_width' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    "dcstm_width.model_id = dcstm_model.model_id",
                                    "dcstm_width.num = dcstm_model.num",
                                    "dcstm_width.var_id IN ({$blinds_vars[$settings['blinds_width_var']]})",
                                ),
                            ),
                            'dcstm_area' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    "dcstm_area.model_id = dcstm_width.model_id",
                                    "dcstm_area.num = dcstm_width.num",
                                    "dcstm_area.var_id IN ({$blinds_vars[$settings['blinds_area_var']]})",
                                ),
                            ),
                            // from Horizontal blinds model nomenclature
                            'ncstm_model_width' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_model_width.model_id = dcstm_model.cstm_model_id",
                                    "ncstm_model_width.var_id = '{$blinds_type_vars[$settings['blinds_type_production_width_var']]}'",
                                ),
                            ),
                            'ncstm_model_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_model_rate.model_id = ncstm_model_width.model_id",
                                    "ncstm_model_rate.var_id = '{$blinds_type_vars[$settings['blinds_type_production_rate_var']]}'",
                                ),
                            ),
                            // from Salary rate nomenclature
                            'ncstm_type' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_type.model_id = '{$settings['salary_rate_id']}'",
                                    "ncstm_type.var_id = '{$salary_vars[$settings['salary_rate_production_type_var']]}'",
                                    // option value equal to type of order for horizontal blinds (7)
                                    "ncstm_type.value = '{$settings['order_type_blinds']}'",
                                ),
                            ),
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = ncstm_type.model_id",
                                    "ncstm_rate.num = ncstm_type.num",
                                    "ncstm_rate.var_id = '{$salary_vars[$settings['salary_rate_production_rate_var']]}'",
                                ),
                            ),
                            'ncstm_workplace' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_workplace.model_id = ncstm_rate.model_id",
                                    "ncstm_workplace.num = ncstm_rate.num",
                                    "ncstm_workplace.var_id = '{$salary_vars[$settings['salary_rate_production_workplace_var']]}'",
                                    "ncstm_workplace.value IN ({$employee_workplaces})",
                                ),
                            ),
                        ),
                        'left' => array(),
                    ),
                    'where' => array(
                        "d.type = '{$settings['doc_type_production_request']}'",
                        'd.active = 1',
                        'd.deleted_by = 0',
                        "d.status = 'closed'",
                        "dcstm_date_produced.value BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                    ),
                    'group' => 'GROUP BY idx',
                );

                $workplace_earnings_query = array(
                    'comment' => '/* production - workplace earnings - main products (except horizontal blinds) */',
                    'select' => array(
                        'idx' => "CONCAT(ncstm_workplace.value, '|', dcstm_date_produced.value)",
                        'amount' => "
                            ROUND(SUM(
                                CONVERT(IF(ncstm_measure.value = '{$settings['measure_num']}', dcstm_num.value, dcstm_qty.value), DECIMAL(10, 2)) *
                                CONVERT(ncstm_rate.value, DECIMAL(10, 2))
                            ), 2)
                        ",
                    ),
                    'from' => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                    'joins' => array(
                        'inner' => array(
                            'dr' => array(
                                'table' => DB_TABLE_DOCUMENTS_RELATIVES,
                                'clause' => array(
                                    "dr.parent_id = d.id",
                                    "dr.parent_model_name = 'Document'",
                                    "dr.link_to_model_name = 'Document'",
                                    // group index is "1" when production request is for gift blinds
                                    // from windows order, we want to ignore those as they are
                                    // calculated in the query for horizontal blinds
                                    "dr.group_index = ''",
                                ),
                            ),
                            'd_order' => array(
                                'table' => DB_TABLE_DOCUMENTS,
                                'clause' => array(
                                    'd_order.id = dr.link_to',
                                    // exclude order types for horizontal blinds, accessories, reclamation/repair (7, 27, 30)
                                    "d_order.type IN (" . implode(',', array_diff(
                                        preg_split('#\s*,\s*#', $settings['doc_types_order']),
                                        array($settings['order_type_blinds'], $settings['order_type_accessories'], $settings['order_type_reclamation'])
                                    )) . ")",
                                ),
                            ),
                            'dcstm_date_produced' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_date_produced.model_id = d.id',
                                    "dcstm_date_produced.var_id = '{$request_vars[$settings['request_date_produced_var']]}'",
                                ),
                            ),
                            'dcstm_kind' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_kind.model_id = d.id',
                                    "dcstm_kind.var_id = '{$request_vars[$settings['request_kind_var']]}'",
                                    // exclude accessories and reclamation/repair as they have no main products
                                    // exclude horizontal blinds because they are calculated separately
                                    "dcstm_kind.value NOT IN ({$settings["type_opt_{$settings['order_type_blinds']}"]},{$settings["type_opt_{$settings['order_type_accessories']}"]},{$settings["type_opt_{$settings['order_type_reclamation']}"]})",
                                ),
                            ),
                            'dcstm_qty' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_qty.model_id = d.id',
                                    "dcstm_qty.var_id = '{$request_vars[$settings['request_qty_var']]}'",
                                ),
                            ),
                            'dcstm_num' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_num.model_id = d.id',
                                    "dcstm_num.var_id = '{$request_vars[$settings['request_num_var']]}'",
                                ),
                            ),
                            // from Salary rate nomenclature
                            'ncstm_type' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_type.model_id = '{$settings['salary_rate_id']}'",
                                    "ncstm_type.var_id = '{$salary_vars[$settings['salary_rate_production_type_var']]}'",
                                    // practically type options equal order types because special types are excluded
                                    "ncstm_type.value = d_order.type",
                                ),
                            ),
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = ncstm_type.model_id",
                                    "ncstm_rate.num = ncstm_type.num",
                                    "ncstm_rate.var_id = '{$salary_vars[$settings['salary_rate_production_rate_var']]}'",
                                ),
                            ),
                            'ncstm_measure' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_measure.model_id = ncstm_rate.model_id",
                                    "ncstm_measure.num = ncstm_rate.num",
                                    "ncstm_measure.var_id = '{$salary_vars[$settings['salary_rate_production_measure_var']]}'",
                                ),
                            ),
                            'ncstm_workplace' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_workplace.model_id = ncstm_measure.model_id",
                                    "ncstm_workplace.num = ncstm_measure.num",
                                    "ncstm_workplace.var_id = '{$salary_vars[$settings['salary_rate_production_workplace_var']]}'",
                                    "ncstm_workplace.value IN ({$employee_workplaces})",
                                ),
                            ),
                        ),
                        'left' => array(),
                    ),
                    'where' => array(
                        "d.type = '{$settings['doc_type_production_request']}'",
                        'd.active = 1',
                        'd.deleted_by = 0',
                        "d.status = 'closed'",
                        "dcstm_date_produced.value BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                    ),
                    'group' => 'GROUP BY idx',
                );

                // for accessories
                $order_types_with_accessories_production_option = $db->GetOne("
                    SELECT GROUP_CONCAT(f1.model_type) FROM " . DB_TABLE_FIELDS_META . " AS f1
                    JOIN " .  DB_TABLE_FIELDS_META . " AS f2
                    ON f1.model = 'Document' AND f1.model = f2.model
                    AND f1.model_type IN ({$settings['doc_types_order']}) AND f1.model_type = f2.model_type
                    AND f1.grouping = f2.grouping
                    WHERE f1.name = '{$settings['order_production_var']}' AND f2.name = '{$settings['protocol_accessories_id_var']}'"
                ) ?: '0';
                $order_accessories_vars = self::getVarIds($registry, 'doc_types_order',
                    "(name IN ('" . implode("', '", array($settings['protocol_accessories_id_var'], $settings['protocol_accessories_qty_var'])) . "')
                    OR name = '{$settings['order_production_var']}' AND model_type IN ($order_types_with_accessories_production_option))");

                $workplace_vars = self::getVarIds($registry, 'nom_type_workplace');
                $workplace_types = $db->GetAssoc(
                    "SELECT model_id, CONCAT('WHEN ncstm_kind.value IN (', GROUP_CONCAT(value), ') THEN ', model_id) FROM " .
                    DB_TABLE_NOMENCLATURES_CSTM .
                    " WHERE model_id IN ({$employee_workplaces}) AND value != '' AND var_id = '{$workplace_vars[$settings['workplace_production_kind_var']]}' GROUP BY model_id");
                $workplace_types = $workplace_types ? "(CASE " . implode(' ', $workplace_types) . " ELSE 0 END)" : '0';

                $workplace_earnings_accessories_query = array(
                    'comment' => '/* production - workplace earnings - accessories */',
                    'select' => array(
                        'idx' => "CONCAT({$workplace_types}, '|', dcstm_date_produced.value)",
                        'amount' => "
                            ROUND(SUM(
                                CONVERT(dcstm_order_acc_qty.value, DECIMAL(25, 6)) *
                                CONVERT(ncstm_rate.value, DECIMAL(10, 2))
                            ), 2)
                        ",
                    ),
                    'from' => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                    'joins' => array(
                        'inner' => array(
                            'dcstm_date_produced' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_date_produced.model_id = d.id',
                                    "dcstm_date_produced.var_id = '{$request_vars[$settings['request_date_produced_var']]}'",
                                ),
                            ),
                            // no need to join to kind_sht in production request because type of order is what matters
                            /* 'dcstm_kind' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_kind.model_id = d.id',
                                    "dcstm_kind.var_id = '{$request_vars[$settings['request_kind_var']]}'",
                                ),
                            ), */
                            'dr' => array(
                                'table' => DB_TABLE_DOCUMENTS_RELATIVES,
                                'clause' => array(
                                    "dr.parent_id = d.id",
                                    "dr.parent_model_name = 'Document'",
                                    "dr.link_to_model_name = 'Document'",
                                    // group index is "1" when production request is for gift blinds
                                    // from windows order, we want to ignore those as they do not refer
                                    // to the accessories and otherwise production would be calculated twice
                                    "dr.group_index = ''",
                                ),
                            ),
                            'd_order' => array(
                                'table' => DB_TABLE_DOCUMENTS,
                                'clause' => array(
                                    'd_order.id = dr.link_to',
                                    "d_order.type IN ({$settings['doc_types_order']})",
                                ),
                            ),
                            // from GT of accessories of order
                            'dcstm_order_acc' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_order_acc.model_id = d_order.id',
                                    "dcstm_order_acc.var_id IN ({$order_accessories_vars[$settings['protocol_accessories_id_var']]})",
                                    "dcstm_order_acc.value != ''",
                                ),
                            ),
                            'dcstm_order_acc_qty' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_order_acc_qty.model_id = dcstm_order_acc.model_id',
                                    'dcstm_order_acc_qty.num = dcstm_order_acc.num',
                                    "dcstm_order_acc_qty.var_id IN ({$order_accessories_vars[$settings['protocol_accessories_qty_var']]})",
                                ),
                            ),
                            // from the selected accessory nomenclature itself
                            'ncstm_kind' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_kind.model_id = dcstm_order_acc.value",
                                    "ncstm_kind.var_id = '{$accessory_vars[$settings['accessory_production_kind_var']]}'",
                                    "ncstm_kind.value != ''",
                                ),
                            ),
                            'ncstm_rate' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_rate.model_id = ncstm_kind.model_id",
                                    "ncstm_rate.var_id = '{$accessory_vars[$settings['accessory_production_rate_var']]}'",
                                    "ncstm_rate.value != ''",
                                ),
                            ),
                        ),
                        'left' => array(
                            'dcstm_order_acc_production' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_order_acc_production.model_id = dcstm_order_acc.model_id',
                                    'dcstm_order_acc_production.num = dcstm_order_acc.num',
                                    "dcstm_order_acc_production.var_id IN ({$order_accessories_vars[$settings['order_production_var']]})",
                                    "d_order.type IN ({$order_types_with_accessories_production_option})",
                                ),
                            ),
                            'ncstm_calc' => array(
                                'table' => DB_TABLE_NOMENCLATURES_CSTM,
                                'clause' => array(
                                    "ncstm_calc.model_id = ncstm_rate.model_id",
                                    "ncstm_calc.var_id = '{$accessory_vars[$settings['accessory_production_calc_var']]}'",
                                ),
                            ),
                        ),
                    ),
                    'where' => array(
                        "d.type = '{$settings['doc_type_production_request']}'",
                        'd.active = 1',
                        'd.deleted_by = 0',
                        "d.status = 'closed'",
                        "dcstm_date_produced.value BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                        // only last two types have production option
                        "(d_order.type IN ({$order_types_with_accessories_production_option})
                        AND dcstm_order_acc_production.value = '{$settings['order_production_yes']}'
                        OR d_order.type NOT IN ({$order_types_with_accessories_production_option}))",
                        // either production calc option is different from "accessories only" or order type is "accessories"
                        "(IFNULL(ncstm_calc.value, '') != '{$settings['accessory_production_calc_accessories_only']}' OR d_order.type = '{$settings['order_type_accessories']}')",
                        "{$workplace_types} != 0",
                    ),
                    'group' => 'GROUP BY idx',
                );

                $workplace_timesheets_query = array(
                    'comment' => '/* production - timesheets */',
                    'select' => array(
                        'idx' => "CONCAT(IFNULL(dcstm_workplace.cstm_model_id, ''), '|', IFNULL(d.date, ''), IF(IFNULL(dcstm_employee.cstm_model_id, '') = '', '', CONCAT('|', dcstm_employee.cstm_model_id)))",
                        'workplace' => 'dcstm_workplace.cstm_model_id',
                        'date' => 'd.date',
                        'employee' => 'dcstm_employee.cstm_model_id',
                        // (worked hours / standard work day duration (8 hours)) * employee rate for workplace
                        // only if paid per produced quantity
                        'qty_rate' => "
                            ROUND(SUM(
                                IF(ccstm_payment_type.value = '{$settings['employee_production_payment_type_qty']}',
                                CONVERT(dcstm_worktime.value, DECIMAL(10, 2)) / {$settings['num_working_hours']} * CONVERT(ccstm_rate.value, DECIMAL(10, 2)),
                                0
                            )), 2)",
                        // only if paid per hourly rate
                        'time_rate' => "
                            ROUND(SUM(
                                IF(ccstm_payment_type.value = '{$settings['employee_production_payment_type_time']}',
                                CONVERT(dcstm_worktime.value, DECIMAL(10, 2)) * CONVERT(ccstm_rate.value, DECIMAL(10, 2)),
                                0
                            )), 2)",
                    ),
                    'from' => 'FROM ' . DB_TABLE_DOCUMENTS . ' AS d',
                    'joins' => array(
                        'inner' => array(
                            'dcstm_employee' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "dcstm_employee.model = 'Document'",
                                    'dcstm_employee.model_id = d.id',
                                    "dcstm_employee.cstm_model = 'Customer'",
                                    "dcstm_employee.var_id = '{$timesheet_vars[$settings['timesheet_employee_var']]}'",
                                ),
                            ),
                            'dcstm_workplace' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "dcstm_workplace.model = 'Document'",
                                    'dcstm_workplace.model_id = dcstm_employee.model_id',
                                    "dcstm_workplace.cstm_model = 'Nomenclature'",
                                    "dcstm_workplace.cstm_model_id IN ({$employee_workplaces})",
                                    'dcstm_workplace.num = dcstm_employee.num',
                                    "dcstm_workplace.var_id = '{$timesheet_vars[$settings['timesheet_workplace_var']]}'",
                                ),
                            ),
                            'dcstm_worktime' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_worktime.model_id = dcstm_workplace.model_id',
                                    'dcstm_worktime.num = dcstm_workplace.num',
                                    "dcstm_worktime.var_id = '{$timesheet_vars[$settings['timesheet_worktime_var']]}'",
                                ),
                            ),
                            /* 'dcstm_overtime' => array(
                                'table' => DB_TABLE_DOCUMENTS_CSTM,
                                'clause' => array(
                                    'dcstm_overtime.model_id = dcstm_worktime.model_id',
                                    'dcstm_overtime.num = dcstm_worktime.num',
                                    "dcstm_overtime.var_id = '{$timesheet_vars[$settings['timesheet_overtime_var']]}'",
                                ),
                            ), */
                            'ccstm_workplace' => array(
                                'table' => DB_TABLE_CSTM_RELATIVES,
                                'clause' => array(
                                    "ccstm_workplace.model = 'Customer'",
                                    'ccstm_workplace.model_id = dcstm_employee.cstm_model_id',
                                    "ccstm_workplace.cstm_model = 'Nomenclature'",
                                    'ccstm_workplace.cstm_model_id = dcstm_workplace.cstm_model_id',
                                    "ccstm_workplace.var_id = '{$employee_vars[$settings['employee_production_workplace_var']]}'",
                                ),
                            ),
                            'ccstm_payment_type' => array(
                                'table' => DB_TABLE_CUSTOMERS_CSTM,
                                'clause' => array(
                                    'ccstm_payment_type.model_id = ccstm_workplace.model_id',
                                    'ccstm_payment_type.num = ccstm_workplace.num',
                                    "ccstm_payment_type.var_id = '{$employee_vars[$settings['employee_production_payment_type_var']]}'",
                                ),
                            ),
                            'ccstm_rate' => array(
                                'table' => DB_TABLE_CUSTOMERS_CSTM,
                                'clause' => array(
                                    'ccstm_rate.model_id = ccstm_payment_type.model_id',
                                    'ccstm_rate.num = ccstm_payment_type.num',
                                    "ccstm_rate.var_id = '{$employee_vars[$settings['employee_production_payment_rate_var']]}'",
                                ),
                            ),
                        ),
                        'left' => array(),
                    ),
                    'where' => array(
                        "d.type = '{$settings['doc_type_timesheet']}'",
                        'd.active = 1',
                        'd.deleted_by = 0',
                        //"d.status = 'closed'",
                        "d.date BETWEEN '{$filters['period']}' AND '{$period_end_date}'",
                    ),
                    'group' => 'GROUP BY dcstm_workplace.cstm_model_id, d.date, dcstm_employee.cstm_model_id WITH ROLLUP',
                );

                $produced = $db->GetAssoc(
                    "SELECT idx, SUM(amount) AS amount FROM (\n" .
                    self::prepareQuery($workplace_earnings_blinds_query) . "\nUNION ALL\n" .
                    self::prepareQuery($workplace_earnings_query) . "\nUNION ALL\n" .
                    self::prepareQuery($workplace_earnings_accessories_query) .
                    "\n) AS t\nGROUP BY idx\nORDER BY idx");

                $timesheets = $db->GetAssoc(self::prepareQuery($workplace_timesheets_query));

                $timesheets_totals = array_intersect_key($timesheets, $produced);
                foreach ($timesheets_totals as $ttk => $ttv) {
                    $timesheets_totals[$ttk]['produced'] = $produced[$ttk];
                    $timesheets_totals[$ttk]['produced_rate'] = $ttv['qty_rate'] > 0 ? $produced[$ttk] / $ttv['qty_rate'] : 0;
                }

                foreach ($timesheets as $tk => $tv) {
                    if ($tv['employee'] && array_key_exists($tv['employee'], $final_results)) {
                        $id = $tv['employee'];
                        $amount = $tv['time_rate'] + $tv['qty_rate'] * (array_key_exists($tv['workplace'] . '|' . $tv['date'], $timesheets_totals) ? $timesheets_totals[$tv['workplace'] . '|' . $tv['date']]['produced_rate'] : 0);
                        if (!$amount) {
                            continue;
                        }
                        if (bccomp($tv['qty_rate'], 0, 14) == 1 && empty($final_results[$id]['has_qty_rate'])) {
                            $final_results[$id]['has_qty_rate'] = 1;
                        }
                        if (!array_key_exists($id, $deductions_bonuses_all['production'])) {
                            $deductions_bonuses_all['production'][$id] = 0;
                        }
                        $deductions_bonuses_all['production'][$id] += $amount;
                    }
                }
            }
            // end production

            foreach ($final_results as $id => &$employee_data) {
                if ($employee_data['type'] == PH_CUSTOMER_EMPLOYEE) {
                    // calculate days absence if this is a partial period for employee
                    // (employee has started/left work within searched period)
                    if ($employee_data['start_date'] && $employee_data['start_date'] > $filters['period'] && $employee_data['start_date'] <= $period_end_date) {
                        // days absence from start of period until the day before start_date
                        $final_results[$id]['days_absence'] +=
                            Calendars_Calendar::getWorkingDays(
                                $registry,
                                $filters['period'],
                                date_sub(date_create($employee_data['start_date']), new DateInterval('P1D'))->format('Y-m-d')
                            );
                    }
                    if ($employee_data['end_date'] && $employee_data['end_date'] >= $filters['period'] && $employee_data['end_date'] < $period_end_date) {
                        // days absence from the day after end_date until end of period
                        $final_results[$id]['days_absence'] +=
                            Calendars_Calendar::getWorkingDays(
                                $registry,
                                date_add(date_create($employee_data['end_date']), new DateInterval('P1D'))->format('Y-m-d'),
                                $period_end_date
                            );
                    }

                    // recalculate partial salary
                    if (bccomp($employee_data['salary_total'], 0, 2) == 1 && ($employee_data['start_date'] || $employee_data['end_date'])) {
                        if (!$employee_data['start_date']) {
                            $employee_data['start_date'] = $filters['period'];
                        } elseif (!$employee_data['end_date']) {
                            $employee_data['end_date'] = $period_end_date;
                        }
                        if ($employee_data['start_date'] < $period_end_date && $employee_data['end_date'] > $filters['period'] && $employee_data['start_date'] <= $employee_data['end_date']) {
                            $final_results[$id]['salary_total'] = round((Calendars_Calendar::getWorkingDays($registry, $employee_data['start_date'], $employee_data['end_date']) / $settings['num_working_days']) * $final_results[$id]['salary_total'], 2);
                        } else {
                            $final_results[$id]['salary_total'] = 0;
                        }
                    } elseif (bccomp($final_results[$id]['salary_total'], 0, 2) < 1 && bccomp($employee_data['minimum_income'], 0, 2) == 1) {
                        // if salary_total is 0 and minimum_income > 0
                        // earnings amount = installation + production amount + paid sickness leave amount
                        $earnings_amount = 0;
                        if (array_key_exists($id, $deductions_bonuses_all['production']) && !empty($final_results[$id]['has_qty_rate'])) {
                            // if employee has production earnings and employee is paid per production
                            $earnings_amount += $deductions_bonuses_all['production'][$id];
                        }
                        if (array_key_exists($id, $deductions_bonuses_all['installation']) && bccomp($deductions_bonuses_all['installation'][$id], 0, 2) == 1) {
                            // if employee has installation earnings
                            $earnings_amount += $deductions_bonuses_all['installation'][$id];
                        }
                        if (array_key_exists($id, $deductions_bonuses_all['sickness']) && bccomp($deductions_bonuses_all['sickness'][$id], 0, 2) == 1) {
                            // add paid sickness to earnings
                            $earnings_amount += $deductions_bonuses_all['sickness'][$id];
                        }
                        if (bccomp($earnings_amount, 0, 2) == 1) {
                            $earnings_amount = round($earnings_amount, 2);

                            // if employee has any absence days, recalculate minimum_income as partial amount
                            $minimum_income = $employee_data['minimum_income'];
                            if (!empty($employee_data['days_absence'])) {
                                $minimum_income *= ($settings['num_working_days'] - $employee_data['days_absence']) / $settings['num_working_days'];
                            }

                            if (bccomp($earnings_amount, $minimum_income, 2) == -1) {
                                // if earnings amount is less than minimum income:
                                // add subsidy for the difference
                                $deductions_bonuses_all['subsidy'][$id] = round(bcsub($minimum_income, $earnings_amount, 2), 2);

                                // update social security to 0
                                if (!empty($deductions_bonuses_all['social_security'][$id])) {
                                    $deductions_bonuses_all['social_security'][$id] = 0;
                                }
                            } elseif (!empty($deductions_bonuses_all['social_security'][$id]) &&
                                bccomp(bcadd($earnings_amount, $deductions_bonuses_all['social_security'][$id], 2), $minimum_income, 2) == -1) {
                                    // if employee pays their own social security
                                    // and earnings amount minus social security amount is less than minimum income:
                                    // reduce social security distraint to the difference between minimum income and earnings amount (value is negative)
                                    $deductions_bonuses_all['social_security'][$id] = round(bcsub($minimum_income, $earnings_amount, 2), 2);
                            }
                        }
                    }

                    $final_results[$id]['deductions_bonuses'] = array_fill_keys(self::$deductions_bonuses, 0);
                    foreach ($final_results[$id]['deductions_bonuses'] as $k => $v) {
                        if (array_key_exists($id, $deductions_bonuses_all[$k])) {
                            $final_results[$id]['deductions_bonuses'][$k] = $deductions_bonuses_all[$k][$id];
                        }
                    }
                    $final_results[$id]['salary_final'] =
                        round($final_results[$id]['salary_total'] + array_sum($final_results[$id]['deductions_bonuses']), 2);
                } else {
                    // salary of other workers is only installation earnings
                    $final_results[$id]['deductions_bonuses'] = array();
                    $final_results[$id]['salary_final'] = $final_results[$id]['deductions_bonuses']['installation'] =
                        array_key_exists($id, $deductions_bonuses_all['installation']) ? $deductions_bonuses_all['installation'][$id] : 0;
                }
            }
            unset($employee_data);

            $totals = array_map('array_sum', $deductions_bonuses_all);
            foreach (array('salary_total', 'salary_final') as $col) {
                $totals[$col] = round(array_sum(array_map(function($a) use ($col) { return $a[$col]; }, $final_results)), 2);
            }
        }

        $types_names = array();
        $types_tables = array(
            'fin_' => DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N,
            'doc_' => DB_TABLE_DOCUMENTS_TYPES_I18N
        );
        foreach ($types_tables as $prefix => $i18n_table) {
            $types_settings = array_filter(array_keys($settings), function($a) use ($prefix) { return preg_match('#^' . $prefix . '.*type_#', $a); });
            $types_list = implode(',', array_map('constant', array_map('strtoupper', $types_settings)));
            $types_names = $types_names + array_combine(
                array_map(function($a) { return substr($a, strpos($a, '_type_') + strlen('_type_')); }, $types_settings),
                $db->GetAssoc("
                    /* type names */
                    SELECT parent_id, SUBSTRING_INDEX(name_plural, '(', 1)
                    FROM {$i18n_table}
                    WHERE lang = '{$model_lang}' AND parent_id IN ({$types_list})
                    ORDER BY FIND_IN_SET(parent_id, '{$types_list}')"
                ));
        }
        $types_names = $types_names + array_flip(self::$deductions_bonuses);
        foreach ($types_names as $k => $v) {
            if ($custom_type_name = $registry['translater']->translate("reports_$k")) {
                $types_names[$k] = $custom_type_name;
            }
        }
        $final_results['additional_options']['deductions_bonuses'] = self::$deductions_bonuses;
        $final_results['additional_options']['type_names'] = $types_names;
        if (!empty($totals)) {
            $final_results['additional_options']['totals'] = $totals;
        }
        $final_results['additional_options']['period_end_date'] = $period_end_date;

        if (!empty($filters['paginate'])) {
            $results = array($final_results, 0);
        } else {
            $results = $final_results;
        }
        if ($debug) {
            $db->debug = 0;
        }

        return $results;
    }

    /**
     * Gets ids of additional variables for specified model and model type(s)
     *
     * @param Registry $registry - the main registry
     * @param string $setting_name - a setting that defines one or more ids of types of records
     * @param string $additional_where - optional additional conditions
     * @return array - variable name as key, comma-separated list of ids of variables as value
     */
    private static function getVarIds(Registry &$registry, $setting_name, $additional_where = '') {
        if ($additional_where) {
            $additional_where = ' AND ' . $additional_where;
        }
        return $registry['db']->GetAssoc("
            /* additional vars for '$setting_name' */
            SELECT name, GROUP_CONCAT(id ORDER BY model_type ASC)
            FROM " . DB_TABLE_FIELDS_META . "
            WHERE model LIKE '" . ucfirst(substr($setting_name, 0, strpos($setting_name, '_'))) . "%'
              AND model_type IN (" . (defined(strtoupper($setting_name)) && constant(strtoupper($setting_name)) !== '' ? constant(strtoupper($setting_name)) : '0') . ")
              AND gt2 = 0 AND type NOT IN ('group', 'gt2', 'config', 'button', 'table', 'bb', 'map')
              $additional_where
            GROUP BY name
            ORDER BY name ASC"
        );
    }

    /**
     * Assembles SQL query from components
     *
     * @param array $sql - query components
     * @return string - query
     */
    private static function prepareQuery(array $sql) {
        if (!empty($sql['comment']) && is_array($sql['comment'])) {
            $sql['comment'] = implode('', $sql['comment']);
        }
        $sql['select'] = 'SELECT ' . implode(",\n",
            array_map(
                function($v, $k) { return "$v AS $k"; },
                $sql['select'],
                array_keys($sql['select'])
            ));
        if (!empty($sql['joins'])) {
            foreach ($sql['joins'] as $join_type => $joins) {
                foreach ($joins as $alias => $join) {
                    $sql['from'] .= "\n" . ($join_type == 'left' ? 'LEFT ' : '') .
                    "JOIN {$join['table']} AS $alias\n  ON " . implode(' AND ', $join['clause']);
                }
            }
            unset($sql['joins']);
        }
        if (array_key_exists('where', $sql) && is_array($sql['where'])) {
            $sql['where'] = array_filter($sql['where']);
            if (!empty($sql['where'])) {
                $sql['where'] = 'WHERE ' . implode("\n  AND ", $sql['where']);
            } else {
                unset($sql['where']);
            }
        }
        if (array_key_exists('order', $sql) && is_array($sql['order'])) {
            $sql['order'] = 'ORDER BY ' . implode(', ', $sql['order']);
        }
        $sql = implode("\n", $sql);

        return $sql;
    }
}

?>
