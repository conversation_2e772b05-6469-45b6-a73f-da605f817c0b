var Byfar_Request_Management;
Event.observe(window, 'load', function () {
    if (Byfar_Request_Management) {
        return;
    }
    Byfar_Request_Management = (function() {
        // "private" properties
        var prec = 0;//env.precision.gt2_quantity;
        var price_prec = env.precision.gt2_rows;

        var prepareArticleName = function(container, field) {
            var article_name = '',
                ridx,
                aid,
                avid;
            if (container && field) {
                ridx = field.id.replace(/^.+_(-?\d+)$/, '$1');
                aid = field.up('tr').down('#article_id_' + ridx) ? field.up('tr').down('#article_id_' + ridx).value : 0;
                avid = field.up('tr').down('#article_variant_id_' + ridx) ? field.up('tr').down('#article_variant_id_' + ridx).value : 0;
                if (container.select('td.article_name_' + aid).length) {
                    article_name = container.select('td.article_name_' + aid)[0].innerText.strip();
                    if (aid != avid && article_name.length && container.select('td.article_variant_size_' + avid).length) {
                        article_name += ' (' + container.select('td.article_variant_size_' + avid)[0].innerText.strip() + ')';
                    }
                }
            }
            return article_name;
        };

        var calculateDeliveryQuantity = function(field) {
            field = $(field);
            var container = field.up('table.reports_table');
            var aid = container.down('input[id="' + field.id.replace(/^(quantity_to_deliver)/, 'article_id') + '"]');
            aid = aid ? aid.value : '0';
            var qfields = [];
            container.select('input.article_id[value="' + aid + '"]').each(function(afield) {
                var qfield = container.down('input[id="' + afield.id.replace(/^(article_id)/, 'quantity_to_deliver') + '"]');
                if (qfield) {
                    qfields.push(qfield);
                }
            });
            var dqfield = container.down('input[id="delivery_quantity_' + aid + '"]');

            if (dqfield) {
                dqfield.value = sumFieldArray(qfields).toFixed(prec);
                calculateDeliverySubtotal(dqfield);
                calculateTotalDeliveryQuantity(dqfield);
            }
        };

        var calculateTotalDeliveryQuantity = function(field) {
            field = $(field);
            var container = field.up('table.reports_table');
            var tdqfield = container.down('input[id="delivery_quantity_total"]');
            if (tdqfield) {
                tdqfield.value = sumFieldArray(container.select('input.delivery_quantity')).toFixed(prec);
            }
        };

        var calculateDeliverySubtotal = function(field) {
            var container = $(field).up('table.reports_table');
            var qfield, pfield;
            var trow = $(field.up('tr'));
            if (field.id.match(/^delivery_quantity_\d+$/)) {
                qfield = field;
                pfield = trow.down('input[id="' + qfield.id.replace(/^(delivery_quantity)/, 'delivery_price') + '"]');
            } else if (field.id.match(/^delivery_price_\d+$/)) {
                pfield = field;
                qfield = trow.down('input[id="' + pfield.id.replace(/^(delivery_price)/, 'delivery_quantity') + '"]');
            }
            if (!qfield || !pfield) {
                return;
            }

            var dsfield = trow.down('input[id="' + pfield.id.replace(/^(delivery_price)/, 'delivery_subtotal') + '"]');
            if (dsfield) {
                dsfield.value = ((parseFloat(qfield.value) || 0) * (parseFloat(pfield.value) || 0)).toFixed(price_prec);
            }

            var dtfield = container.down('input[id="delivery_total"]');
            if (dtfield) {
                dtfield.value = sumFieldArray(container.select('input.delivery_subtotal')).toFixed(price_prec);
            }
        };

        var sumFieldArray = function(fields) {
            var tv = 0;
            fields.pluck('value').map(function(v) { tv += (parseFloat(v) || 0); });
            return tv;
        };

        // "public" methods
        return {
            /**
             * Validate quantity to request,
             * clear erred class of autocompleter when quantity to request is cleared
             *
             * @param {Object} field - quantity field for article and customer
             */
            request_quantity: function(field) {
                field = $(field);
                var container = field.up('table.reports_table');

                // clear erred class of autocompleter
                var aid = container.down('input[id="' + field.id.replace(/^(quantity_to_request)/, 'article_id') + '"]');
                if (field && (!field.value || parseFloat(field.value) == 0) && aid) {
                    container.select('[name="producer_name[' + aid.value + ']"]').invoke('removeClassName', ['erred']);
                }

                var val = parseFloat(field.value);
                if (!val) {
                    val = 0;
                }
                // validate max qty to request for row
                var max_qty = field.up().down('[id="' + field.id.replace(/quantity_to_request_/, 'quantity_to_request_max_') + '"]');
                max_qty = max_qty && max_qty.value && !isNaN(max_qty.value) ? parseFloat(max_qty.value) : 0;
                field[val > max_qty ? 'addClassName' : 'removeClassName']('erred');

                // toggle action buttons
                var total_quantity_all = container.select('input.quantity_to_request')/*.concat(container.select('input.autocompletebox'))*/;
                ['request'].each(function(action_type) {
                    var btn_enabled =
                        !total_quantity_all.filter(function(a) { return a.hasClassName('erred'); }).length &&
                        total_quantity_all.filter(function(a) { return parseFloat(a.value) > 0; }).length;
                    container.select('.request_button_container button.' + action_type).each(function(btn) {
                        btn = $(btn);
                        btn.disabled = !btn_enabled;
                        btn[btn_enabled ? 'removeClassName' : 'addClassName']('inactive');
                    });
                });
                return;
            },

            /**
             * Removes erred class from producer autocompleter when radio with
             * value is selected (onchange event is triggered only for the
             * selected option)
             *
             * @param {Object} field - radio option field for producer autocompleter
             */
            producer: function(field) {
                field = $(field);
                if (field.value) {
                    field.adjacent('input.autocompletebox.erred').invoke('removeClassName', ['erred']);
                }
            },

            /**
             * Validates quantity to reserve from specified warehouse
             *
             * @param {Object} field - quantity field for article and warehouse
             */
            warehouse_quantity: function(field) {
                field = $(field);
                var ridx = field.id.replace(/^.+_(-?\d+)$/, '$1'),
                    avid = field.up('tr').down('#article_variant_id_' + ridx) ? field.up('tr').down('#article_variant_id_' + ridx).value : 0,
                    wid = field.up('tr').down('#warehouse_' + ridx) ? field.up('tr').down('#warehouse_' + ridx).value : 0,
                    i;
                var container = field.up('table.reports_table');
                // total quantity entered field per article variant + warehouse
                var total_quantity = $('warehouse' + wid + '_quantity_' + avid);
                // total available quantity field per article variant + warehouse
                var total_available = $('warehouse' + wid + '_available_' + avid);
                // calculate total entered quantity for article variant + warehouse
                var tquantity_reservation = 0;

                // quantity fields for article variant + warehouse
                var qfields = container.select('input.quantity_to_reserve.warehouse' + wid + '_quantity.article_variant' + avid + '_quantity');
                for (i = 0; i < qfields.length; i++) {
                    var val = parseFloat(qfields[i].value);
                    if (!val) {
                        val = 0;
                    }
                    // validate max qty to reserve for row
                    var max_qty = qfields[i].up().down('[id="' + qfields[i].id.replace(/quantity_to_reserve_/, 'quantity_to_reserve_max_') + '"]');
                    max_qty = max_qty && max_qty.value && !isNaN(max_qty.value) ? parseFloat(max_qty.value) : 0;
                    qfields[i][val > max_qty ? 'addClassName' : 'removeClassName']('erred');

                    // reservation can be done only from available, not reserved
                    tquantity_reservation += val;

                    if (qfields[i].id != field.id) {
                        val = parseFloat(qfields[i].value);
                        if (val) {
                            qfields[i].value = val.toFixed(prec);
                        }
                    }
                }
                // validate max qty to reserve for article variant and warehouse
                total_quantity.value = tquantity_reservation.toFixed(prec);
                total_quantity[tquantity_reservation > total_available.value ? 'addClassName' : 'removeClassName']('erred');

                // toggle action buttons
                var total_quantity_all = container.select('input.quantity_to_reserve.erred').concat(container.select('input.total_quantity'));
                ['reservation'].each(function(action_type) {
                    var btn_enabled =
                        !total_quantity_all.filter(function(a) { return a.hasClassName('erred'); }).length &&
                        total_quantity_all.filter(function(a) { return parseFloat(a.value) > 0; }).length;
                    container.select('.reservation_button_container button.' + action_type).each(function(btn) {
                        btn = $(btn);
                        btn.disabled = !btn_enabled;
                        btn[btn_enabled ? 'removeClassName' : 'addClassName']('inactive');
                    });
                });
                return;
            },

            /**
             * Validate quantity to deliver
             *
             * @param {Object} field - quantity field for article
             */
            delivery_quantity: function(field) {
                field = $(field);
                var container = field.up('table.reports_table');

                // clear erred class of delivery price
                var aid = container.down('input[id="' + field.id.replace(/^(quantity_to_deliver)/, 'article_id') + '"]');
                if (field && (!field.value || parseFloat(field.value) == 0) && aid) {
                    container.select('[name="delivery_price[' + aid.value + ']"]').invoke('removeClassName', ['erred']);
                }

                var val = parseFloat(field.value);
                if (!val) {
                    val = 0;
                }
                // validate max qty to deliver for row
                var max_qty = field.up().down('[id="' + field.id.replace(/quantity_to_deliver_/, 'quantity_to_deliver_max_') + '"]');
                max_qty = max_qty && max_qty.value && !isNaN(max_qty.value) ? parseFloat(max_qty.value) : 0;
                field[val > max_qty ? 'addClassName' : 'removeClassName']('erred');

                // calculate total delivery quantity for article
                calculateDeliveryQuantity(field);

                // toggle action buttons
                var total_quantity_all = container.select('input.quantity_to_deliver')/*.concat(container.select('input.delivery_price'))*/;
                ['delivery'].each(function(action_type) {
                    var btn_enabled =
                        !total_quantity_all.filter(function(a) { return a.hasClassName('erred'); }).length &&
                        total_quantity_all.filter(function(a) { return parseFloat(a.value) > 0; }).length;
                    container.select('.delivery_button_container button.' + action_type).each(function(btn) {
                        btn = $(btn);
                        btn.disabled = !btn_enabled;
                        btn[btn_enabled ? 'removeClassName' : 'addClassName']('inactive');
                    });
                });
                return;
            },

            /**
             * Validate delivery price of article to deliver
             *
             * @param {Object} field - delivery price field for article
             */
            delivery_price: function(field) {
                field = $(field);

                // round entered value to GT2 price precision
                var pow = Math.pow(10, price_prec);
                if (field.value.match(/^[^\.]*\./)) {
                    var cnt = field.value.replace(/^[^\.]*\./, '');
                    if (cnt.length > price_prec) {
                        field.value = Math.round(field.value * pow) / pow;
                    }
                }

                var container = field.up('table.reports_table');
                var val = parseFloat(field.value);
                var aid = field.name.replace(/^.+\[(\d+)\]$/, '$1');
                var qfields_num = 0;
                if (!val) {
                    val = 0;
                }

                // check if all quantity fields are empty or not
                if (val == 0) {
                    container.select('input.article_id[value="' + aid + '"]').each(function(afield) {
                        var qfield = container.down('input[id="' + afield.id.replace(/^(article_id)/, 'quantity_to_deliver') + '"]');
                        if (qfield && qfield.value && parseFloat(qfield.value) > 0) {
                            qfields_num++;
                        }
                    });
                }

                // price value is valid
                if (val > 0 || !qfields_num) {
                    field.removeClassName('erred');
                }

                // calculate delivery subtotal for article
                calculateDeliverySubtotal(field);
            },

            /**
             * Validate company_data selection
             *
             * @param {Object} field - company_data dropdown
             */
            company_data: function(field) {
                field = $(field);
                var btn_enabled = !!field.value;
                field[btn_enabled ? 'removeClassName' : 'addClassName']('erred');
                var container = field.up('table.reports_table');
                container.select('.delivery_button_container button.delivery').each(function(btn) {
                    btn = $(btn);
                    btn.disabled = !btn_enabled;
                    btn[btn_enabled ? 'removeClassName' : 'addClassName']('inactive');
                });
            },

            /**
             * Starts creation of multiple records
             *
             * @param {Object} element - element that triggers action (clicked button)
             */
            createMultiple: function(element) {
                var errors = [],
                    form_valid = true,
                    element_form,
                    container = $(element.up('table.reports_table')),
                    qfields,
                    i,
                    names_erred;

                if (element && element.name && element.form) {
                    switch (element.name) {
                        case 'btn_request':
                            var producers_valid = true;
                            // validate article quantites
                            qfields = container.select('input.quantity_to_request').filter(function(a) { return parseFloat(a.value) > 0; });
                            if (!qfields.length) {
                                errors.push(i18n.messages.error_reports_empty_quantity_to_request);
                            } else {
                                if (container.select('input.quantity_to_request.erred').length) {
                                    names_erred = [];
                                    container.select('input.quantity_to_request.erred').each(function(field) {
                                        names_erred.push(prepareArticleName(container, $(field)));
                                    });
                                    if (names_erred.length) {
                                        errors.push(names_erred.uniq().join(', ') + ': ' + i18n.messages.error_reports_total_quantity_over_unrequested);
                                    }
                                }
                                // validate producers
                                for (i = 0; i < qfields.length; i++) {
                                    var aid = container.down('input[id="' + qfields[i].id.replace(/^(quantity_to_request)/, 'article_id') + '"]');
                                    if (!aid || !aid.value || !getRadioValue(container.select('input[type="radio"][name="producer[' + aid.value + ']"]'))) {
                                        producers_valid = false;
                                        if (aid && aid.value) {
                                            container.select('[name="producer_name[' + aid.value + ']"]').invoke('addClassName', ['erred']);
                                        }
                                    }
                                }
                                if (!producers_valid) {
                                    errors.push(i18n.messages.error_reports_empty_producer);
                                }
                            }
                            if (errors.length > 1 || errors.length == 1 && producers_valid) {
                                $(element).disable().addClassName('inactive');
                            }
                            break;
                        case 'btn_reservation':
                            // validate article quantites
                            qfields = container.select('input.quantity_to_reserve').filter(function(a) { return parseFloat(a.value) > 0; });
                            if (!qfields.length) {
                                errors.push(i18n.messages.error_reports_empty_quantity_to_reserve);
                            } else {
                                if (container.select('input.quantity_to_reserve.erred').length) {
                                    names_erred = [];
                                    container.select('input.quantity_to_reserve.erred').each(function(field) {
                                        names_erred.push(prepareArticleName(container, $(field)));
                                    });
                                    if (names_erred.length) {
                                        errors.push(names_erred.uniq().join(', ') + ': ' + i18n.messages.error_reports_total_quantity_over_unreserved);
                                    }
                                }
                                if (container.select('input.total_quantity.erred').length) {
                                    names_erred = [];
                                    container.select('input.total_quantity.erred').each(function(field) {
                                        names_erred.push(prepareArticleName(container, $(field).up('tr').select('input.quantity_to_reserve')[0]));
                                    });
                                    if (names_erred.length) {
                                        errors.push(names_erred.uniq().join(', ') + ': ' + i18n.messages.error_reports_total_quantity_over_reservation);
                                    }
                                }
                            }
                            if (errors.length) {
                                $(element).disable().addClassName('inactive');
                            }
                            break;
                        case 'btn_delivery':
                            var prices_valid = true;
                            // validate article quantites
                            qfields = container.select('input.quantity_to_deliver').filter(function(a) { return parseFloat(a.value) > 0; });
                            if (!qfields.length) {
                                errors.push(i18n.messages.error_reports_empty_quantity_to_deliver);
                            } else {
                                if (container.select('input.quantity_to_deliver.erred').length) {
                                    names_erred = [];
                                    container.select('input.quantity_to_deliver.erred').each(function(field) {
                                        names_erred.push(prepareArticleName(container, $(field)));
                                    });
                                    if (names_erred.length) {
                                        errors.push(names_erred.uniq().join(', ') + ': ' + i18n.messages.error_reports_total_quantity_over_undelivered);
                                    }
                                }
                                // validate prices
                                for (i = 0; i < qfields.length; i++) {
                                    var aid = container.down('input[id="' + qfields[i].id.replace(/^(quantity_to_deliver)/, 'article_id') + '"]');
                                    if (!aid || !aid.value) {
                                        prices_valid = false;
                                    } else {
                                        var dp = container.down('[name="delivery_price[' + aid.value + ']"]');
                                        if (!(dp && parseFloat(dp.value) > 0)) {
                                            prices_valid = false;
                                            container.select('[name="delivery_price[' + aid.value + ']"]').invoke('addClassName', ['erred']);
                                        }
                                    }
                                }
                                if (!prices_valid) {
                                    errors.push(i18n.messages.error_reports_empty_delivery_price);
                                }
                            }
                            // validate company_data
                            if (!(container.down('#company_data') && container.down('#company_data').value)) {
                                errors.push(i18n.messages.error_reports_select_company_data);
                                container.select('[id="company_data"]').invoke('addClassName', ['erred']);
                            }

                            if (errors.length > 1 || errors.length == 1 && prices_valid) {
                                $(element).disable().addClassName('inactive');
                            }
                            break;
                        default:
                            form_valid = false;
                            break;
                    }

                    form_valid = form_valid && !errors.length;

                    if (form_valid) {
                        // set form action and submit it
                        element_form = $(element.form);
                        element_form.elements['reports'].value = element.name.replace(/^btn/, 'ajax_create');
                        element_form.action = env.base_url + '?' + env.module_param + '=reports&report_type=' + element_form.elements['report_type'].value;
                        if (typeof element_form.onsubmit == 'function') {
                            element_form.onsubmit();
                        }
                        element_form.submit();
                    } else {
                        if (errors.length) {
                            // display errors
                            alert(errors.join("\n"));
                        }
                    }
                }

                return false;
            },

            /**
             * Manages report filters when search mode is changed
             *
             * @param {string} mode - selected mode
             * @param {Object} params - filter dependencies
             */
            toggleFilters: function(mode, params) {
                var used_params, i, j;
                if (params.dependent_visible_filters) {
                    used_params = params.dependent_visible_filters[mode] || params.dependent_visible_filters['default'] || [];
                    if (used_params.length == 2) {
                        // hidden ones are at 0-index, visible ones are at 1-index
                        switchVisibleRows(used_params[1].join(','), used_params[0].join(','));
                    }
                }

                if (params.dependent_required_filters) {
                    used_params = params.dependent_required_filters[mode] || params.dependent_required_filters['default'] || [];
                    if (used_params.length == 2) {
                        // non-required ones are at 0-index, required ones are at 1-index
                        for (i = 0; i < used_params.length; i++) {
                            for (j = 0; j < used_params[i].length; j++) {
                                toggleRequiredRow(used_params[i][j], i);
                            }
                        }
                    }
                }
            }
        };
    })();
});
