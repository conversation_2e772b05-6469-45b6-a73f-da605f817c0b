<?php
    Class Investor_Customer_Paying_Report Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {
            //set interface lang filter
            $lang = $registry['lang'];

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            if (! empty($filters['customer'])) {
                //sql to take the ids of the needed additional vars for CONTRACTS
                $sql_for_add_vars_contracts = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_CONTRACT_ID . ' AND (fm.name="' . OFFER_VALUE . '") ORDER BY fm.position';
                $var_ids_contract = $registry['db']->GetAll($sql_for_add_vars_contracts);

                $offer_value_contract_id = '';

                //assign the ids to vars
                foreach ($var_ids_contract as $vars) {
                    if ($vars['name'] == OFFER_VALUE) {
                        $offer_value_contract_id = $vars['id'];
                    }
                }

                //sql to take the information from Contracts
                $sql_for_contracts['select'] = 'SELECT d.id as id_x, d.id as id, d.added_by as added_by, d.full_num as full_num, ' . "\n" .
                                               DOCUMENT_TYPE_CONTRACT_DIRECTION . ' as direction, d.type as type, dti18n.name as type_name, ' . "\n" .
                                               'd.customer as customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                                               'd_cstm_offer_value.value as total_price' . "\n";

                $sql_for_contracts['from']   =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                                '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .

                                                'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                                                '  ON (d.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_offer_value' . "\n" .
                                                '  ON (d_cstm_offer_value.model_id=d.id AND d_cstm_offer_value.var_id="' . $offer_value_contract_id . '")' . "\n";

                // construct where
                $where = array();
                $where[] = 'd.deleted_by=0';
                $where[] = 'd.type="' . DOCUMENT_TYPE_CONTRACT_ID . '"';
                $where[] = 'd.active!=0';

                if (! empty($filters['from_date'])) {
                    $where[] = 'd.added >= "' . $filters['from_date'] . '"';
                }
                if (! empty($filters['to_date'])) {
                    $where[] = 'd.added <= "' . $filters['to_date'] . '"';
                }
                if (! empty($filters['customer'])) {
                    $where[] = 'd.customer = "' . $filters['customer'] . '"';
                }

                $sql_for_contracts['where'] = 'WHERE ' . implode(' AND ', $where);

                $query_for_contracts = implode("\n", $sql_for_contracts);
                $combine_results = $registry['db']->GetAssoc($query_for_contracts);

                $main_results = array();
                $ids_to_search = array();

                if (! empty($combine_results)){
                    foreach ($combine_results as $res) {
                        $res['incomes'] = array();
                        $res['left_to_pay'] = 0;
                        $res['total_docs'] = 0;
                        $res['paid'] = 0;
                        $main_results[$res['id']] = $res;
                        $ids_to_search[] = $res['id'];
                    }

                    $ids_to_search_string = implode(', ', $ids_to_search);


                    //sql to take the ids of the needed additional vars for INCOME
                    $sql_for_add_vars_income = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type=' . DOCUMENT_TYPE_INCOME_ID . ' AND (fm.name="' . PAYING_VALUE . '" OR fm.name="' . PAYING_CURRENCY . '" OR fm.name="' . TAKE_MONEY . '" OR fm.name="' . TYPE_INCOME . '" OR fm.name="' . FAKTURA_NUM . '" OR fm.name="' . FAKTURA_DATE . '" OR fm.name="' . CASE_FAKTURA_DATE . '" OR fm.name="' . MONEY_DATE . '") ORDER BY fm.position';
                    $var_ids_income = $registry['db']->GetAll($sql_for_add_vars_income);

                    $paying_value_id = '';
                    $paying_currency_id = '';
                    $take_money_id = '';
                    $type_income_id = '';
                    $faktura_num_id = '';
                    $faktura_date_id = '';
                    $money_date_id = '';
                    $case_faktura_date_id = '';

                    //assign the ids to vars
                    foreach ($var_ids_income as $vars) {
                        if ($vars['name'] == PAYING_VALUE) {
                            $paying_value_id = $vars['id'];
                        } else if ($vars['name'] == PAYING_CURRENCY) {
                            $paying_currency_id = $vars['id'];
                        } else if ($vars['name'] == TAKE_MONEY) {
                            $take_money_id = $vars['id'];
                        } else if ($vars['name'] == TYPE_INCOME) {
                            $type_income_id = $vars['id'];
                        } else if ($vars['name'] == FAKTURA_NUM) {
                            $faktura_num_id = $vars['id'];
                        } else if ($vars['name'] == FAKTURA_DATE) {
                            $faktura_date_id = $vars['id'];
                        } else if ($vars['name'] == MONEY_DATE) {
                            $money_date_id = $vars['id'];
                        } else if ($vars['name'] == CASE_FAKTURA_DATE) {
                            $case_faktura_date_id = $vars['id'];
                        }
                    }

                    //sql to take the information from sells
                    $sql_for_income['select'] = 'SELECT dr.link_to, dr.parent_id, dr.parent_id, dr.origin, d.id as income_id, ' . "\n" .
                                                'd_cstm_paying_value.value as paying_value, d_cstm_paying_currency.value as paying_currency, ' . "\n" .
                                                'd_cstm_take_money.value as take_money, d_cstm_take_money_name.label as take_money_name, d_cstm_type_income_name.label as type_income, ' . "\n" .
                                                'd_cstm_faktura_num.value as faktura_num, d_cstm_faktura_date.value as faktura_date, ' . "\n" .
                                                'd_cstm_money_date.value as money_date, DATE_FORMAT(d_cstm_case_faktura_date.value, "%Y-%m-%d") as case_faktura_date';

                    $sql_for_income['from']  =  'FROM ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                                '  ON (d.id=dr.parent_id OR d.id=dr.link_to) AND dr.link_to_model_name="Document"' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_value' . "\n" .
                                                '  ON (d_cstm_paying_value.model_id=d.id AND d_cstm_paying_value.var_id="' . $paying_value_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_paying_currency' . "\n" .
                                                '  ON (d_cstm_paying_currency.model_id=d.id AND d_cstm_paying_currency.var_id="' . $paying_currency_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_take_money' . "\n" .
                                                '  ON (d_cstm_take_money.model_id=d.id AND d_cstm_take_money.var_id="' . $take_money_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_take_money_name' . "\n" .
                                                '  ON (d_cstm_take_money_name.parent_name="' . TAKE_MONEY . '" AND d_cstm_take_money_name.option_value=d_cstm_take_money.value AND d_cstm_take_money_name.lang="' . $model_lang . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_type_income' . "\n" .
                                                '  ON (d_cstm_type_income.model_id=d.id AND d_cstm_type_income.var_id="' . $type_income_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_type_income_name' . "\n" .
                                                '  ON (d_cstm_type_income_name.parent_name="' . TYPE_INCOME . '" AND d_cstm_type_income_name.option_value=d_cstm_type_income.value AND d_cstm_type_income_name.lang="' . $model_lang . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_faktura_num' . "\n" .
                                                '  ON (d_cstm_faktura_num.model_id=d.id AND d_cstm_faktura_num.var_id="' . $faktura_num_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_faktura_date' . "\n" .
                                                '  ON (d_cstm_faktura_date.model_id=d.id AND d_cstm_faktura_date.var_id="' . $faktura_date_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_money_date' . "\n" .
                                                '  ON (d_cstm_money_date.model_id=d.id AND d_cstm_money_date.var_id="' . $money_date_id . '")' . "\n" .
                                                'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_case_faktura_date' . "\n" .
                                                '  ON (d_cstm_case_faktura_date.model_id=d.id AND d_cstm_case_faktura_date.var_id="' . $case_faktura_date_id . '")' . "\n";

                    // construct where
                    $where = array();
                    $where[] = 'd.deleted_by=0';
                    $where[] = 'd.type="' . DOCUMENT_TYPE_INCOME_ID . '"';
                    $where[] = 'd.active!=0';
                    $where[] = '(dr.link_to IN (' . $ids_to_search_string . ') OR dr.parent_id IN (' . $ids_to_search_string . '))';

                    $sql_for_income['where'] = 'WHERE ' . implode(' AND ', $where);

                    $query_for_income = implode("\n", $sql_for_income);
                    $records_for_incomes = $registry['db']->GetAll($query_for_income);

                    foreach ($records_for_incomes as $rec_inc) {
                        if ($rec_inc['origin']=='transformed') {
                            $main_results[$rec_inc['link_to']]['incomes'][] = $rec_inc;
                        } else if ($rec_inc['origin']=='inherited') {
                            $main_results[$rec_inc['parent_id']]['incomes'][] = $rec_inc;
                        }
                    }

                    // totals
                    $total_value = 0;
                    $total_left_to_pay = 0;
                    $total_paid = 0;

                    foreach ($main_results as $key => $mr) {
                        foreach ($mr['incomes'] as $key_inc => $mr_inc) {
                            if ($mr_inc['take_money'] == 'yes') {
                                $paid_sum = sprintf("%01.2f", $mr_inc['paying_value']);
                                if ($mr_inc['paying_currency'] == "EUR") {
                                    $paid_sum = sprintf("%01.2f", ($paid_sum * 1.9558));
                                }
                                $main_results[$key]['paid'] = sprintf("%01.2f", ($main_results[$key]['paid'] + $paid_sum));
                            }
                            if (!empty($filters['take_money'])) {
                                if ($mr_inc['take_money'])
                                if ($filters['take_money'] != $mr_inc['take_money']) {
                                    unset($main_results[$key]['incomes'][$key_inc]);
                                }
                            }
                            if (!empty($filters['document_num'])) {
                                if ($filters['document_num'] != $mr_inc['faktura_num'] && isset($main_results[$key]['incomes'][$key_inc])) {
                                    unset($main_results[$key]['incomes'][$key_inc]);
                                }
                            }
                            if (!empty($filters['document_num'])) {
                                if ($filters['document_num'] != $mr_inc['faktura_num'] && isset($main_results[$key]['incomes'][$key_inc])) {
                                    unset($main_results[$key]['incomes'][$key_inc]);
                                }
                            }

                            if (!empty($filters['falling_date'])) {
                                if ($filters['falling_date'] == 'passed') {
                                    if (((strtotime(date('Y-m-d')) <= strtotime($mr_inc['case_faktura_date'])) || (isset($mr_inc['take_money']) && $mr_inc['take_money'] == 'yes')) && isset($main_results[$key]['incomes'][$key_inc])) {
                                        unset($main_results[$key]['incomes'][$key_inc]);
                                    }
                                } else if ($filters['falling_date'] == 'not_passed') {
                                    if ((strtotime(date('Y-m-d')) > strtotime($mr_inc['case_faktura_date'])) && isset($main_results[$key]['incomes'][$key_inc])) {
                                        unset($main_results[$key]['incomes'][$key_inc]);
                                    }
                                }
                            }

                            if (isset($main_results[$key]['incomes'][$key_inc])) {
                                if (($mr_inc['take_money'] && $mr_inc['case_faktura_date']) && ((strtotime(date('Y-m-d')) >= strtotime($mr_inc['case_faktura_date'])) && ($mr_inc['take_money'] == 'no'))) {
                                    $main_results[$key]['incomes'][$key_inc]['colored'] = true;
                                }
                            }
                        }
                        $main_results[$key]['total_docs'] = count($main_results[$key]['incomes']);
                        if ($main_results[$key]['total_docs']) {
                            $main_results[$key]['rowspan'] = $main_results[$key]['total_docs'] + 1;
                        } else {
                            $main_results[$key]['rowspan'] = 2;
                        }
                        $main_results[$key]['left_to_pay'] = sprintf("%01.2f", ($main_results[$key]['total_price'] - $main_results[$key]['paid']));
                        $total_paid += $main_results[$key]['paid'];
                        $total_value += $main_results[$key]['total_price'];
                        $total_left_to_pay += $main_results[$key]['left_to_pay'];
                    }
                    $main_results['additional_options']['total_value'] = $total_value;
                    $main_results['additional_options']['total_left_to_pay'] = $total_left_to_pay;
                    $main_results['additional_options']['total_paid'] = $total_paid;
                    $final_results = $main_results;
                } else {
                    $final_results = array();
                }
            } else {
                $final_results = array();
            }

            // pagination
            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>