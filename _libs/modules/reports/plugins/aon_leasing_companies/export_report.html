<html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
    {literal}
      <style type="text/css">
        br { mso-data-placement: same-cell; }
      </style>
    {/literal}
  </head>
  <body>
    {assign var=total_colspan value=21}
    {if $reports_additional_options.include_discount}
      {math equation='2+x' x=$total_colspan assign='total_colspan'}
    {/if}
    {if $reports_additional_options.include_agents}
      {math equation='6+x' x=$total_colspan assign='total_colspan'}
    {/if}
    {if $reports_additional_options.show_commission_distribution_fields}
      {math equation='2+x' x=$total_colspan assign='total_colspan'}
    {/if}
    {if $reports_additional_options.show_commission_damage_fields}
      {math equation='2+x' x=$total_colspan assign='total_colspan'}
    {/if}
    <table border="1" cellpadding="0" cellspacing="0">
      <tr>
        <td style="vertical-align: middle;"><strong>{#reports_policy_num#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_type_insurance#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_insurer#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_leasing_company#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_insured#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_status#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_insurance_value#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_start_date#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_end_date#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_deadline#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_installment_num#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_installment_value#|escape}</strong></td>
        {if $reports_additional_options.include_discount}
          <td style="vertical-align: middle;"><strong>{#reports_discount#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_installment_value_with_discount#|escape}</strong></td>
        {/if}
        <td style="vertical-align: middle;"><strong>{#reports_tax#|escape}</strong></td>
        {if $reports_additional_options.show_commission_distribution_fields}
          <td style="vertical-align: middle;"><strong>{#reports_commission_distribution_percent#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_commission_distribution_value#|escape}</strong></td>
        {/if}
        {if $reports_additional_options.show_commission_damage_fields}
          <td style="vertical-align: middle;"><strong>{#reports_commission_damage_percent#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_commission_damage_value#|escape}</strong></td>
        {/if}
        <td style="vertical-align: middle;"><strong>{#reports_commision_percent#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_commision_bgn#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_fee#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_total_sum#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_balance#|escape}</strong></td>
        {if $reports_additional_options.include_agents}
          <td style="vertical-align: middle;"><strong>{#reports_agent_name#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_agent_percent#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_agent_value#|escape}</strong></td>
        {/if}
        <td style="vertical-align: middle;"><strong>{#reports_automobile_code#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_automobile_rama#|escape}</strong></td>
        <td style="vertical-align: middle;"><strong>{#reports_automobile_model#|escape}</strong></td>
        {if $reports_additional_options.include_agents}
          <td style="vertical-align: middle;"><strong>{#reports_property_address#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_property_description#|escape}</strong></td>
          <td style="vertical-align: middle;"><strong>{#reports_property_notes#|escape}</strong></td>
        {/if}
      </tr>
      {foreach from=$reports_results item=installment name=inst}
        {capture assign="current_row_class"}{cycle values='t_odd1 t_odd2,t_even1 t_even2'}{/capture}
        {foreach from=$installment.cars item=car name=ca}
          <tr>
            {if $smarty.foreach.ca.first}
              <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$installment.rowspan}">
                {$installment.num|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$installment.rowspan}">
                {$installment.insurance_name|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$installment.rowspan}">
                {$installment.insurer_name|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$installment.rowspan}">
                {$installment.leasing_company_name|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$installment.rowspan}">
                {$installment.insured_name|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle;" rowspan="{$installment.rowspan}">
                {$installment.status|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.insurance_value|string_format:"%.2f"|default:"0.00"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$installment.rowspan}">
                {$installment.date_start|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$installment.rowspan}">
                {$installment.date_end|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'dd\.mm\.yyyy';" rowspan="{$installment.rowspan}">
                {$installment.deadline|date_format:#date_short#|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'\@';" rowspan="{$installment.rowspan}">
                {$installment.instalment_num|escape|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.value|string_format:"%.2f"|default:"0.00"}
              </td>
              {if $reports_additional_options.include_discount}
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.discount|escape|default:"0.00"|string_format:"%.2f"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.value_with_discount|escape|default:"0.00"|string_format:"%.2f"}
                </td>
              {/if}
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.tax|string_format:"%.2f"|default:"0.00"}
              </td>
              {if $reports_additional_options.show_commission_distribution_fields}
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.commission_distribution_percent|string_format:"%.2f"|default:"0.00"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.commission_distribution_value|string_format:"%.2f"|default:"0.00"}
                </td>
              {/if}
              {if $reports_additional_options.show_commission_damage_fields}
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.commission_damage_percent|string_format:"%.2f"|default:"0.00"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {$installment.commission_damage_value|string_format:"%.2f"|default:"0.00"}
                </td>
              {/if}
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.commission_percent|string_format:"%.2f"|default:"0.00"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.commission_bgn|string_format:"%.2f"|default:"0.00"}
              </td>
              <td style="vertical-align: middle; text-align: right; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.fee|default:"&nbsp;"}
              </td>
              <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                {$installment.premium|string_format:"%.2f"|default:"0.00"}
              </td>
              <td style="vertical-align: middle; text-align: right;" rowspan="{$installment.rowspan}">
                {$installment.balance|default:"&nbsp;"}
              </td>
              {if $reports_additional_options.include_agents}
                <td style="vertical-align: middle; mso-number-format:'@';" rowspan="{$installment.rowspan}">
                  {$installment.agent|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {if $installment.percent}
                    {$installment.percent|string_format:"%.2f"|default:"&nbsp;"}
                  {else}
                    &nbsp;
                  {/if}
                </td>
                <td style="vertical-align: middle; mso-number-format:'0\.00';" rowspan="{$installment.rowspan}">
                  {if $installment.agent_commission}
                    {$installment.agent_commission|string_format:"%.2f"|default:"&nbsp;"}
                  {else}
                    &nbsp;
                  {/if}
                </td>
              {/if}
            {/if}
            <td style="vertical-align: middle; mso-number-format:'\@';">
              {$car.code|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle; mso-number-format:'\@';">
              {$car.rama|escape|default:"&nbsp;"}
            </td>
            <td style="vertical-align: middle;">
              {$car.name|escape|default:"&nbsp;"}
            </td>
            {if $reports_additional_options.include_agents}
              {if $smarty.foreach.ca.first}
                <td style="vertical-align: middle; mso-number-format:'@';" rowspan="{$installment.rowspan}">
                  {$installment.property_address|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'@';" rowspan="{$installment.rowspan}">
                  {$installment.property_description|escape|default:"&nbsp;"}
                </td>
                <td style="vertical-align: middle; mso-number-format:'@';" rowspan="{$installment.rowspan}">
                  {$installment.notes|escape|nl2br|default:"&nbsp;"}
                </td>
              {/if}
            {/if}
          </tr>
        {/foreach}
      {foreachelse}
        <tr>
          <td colspan="{$total_colspan}"><span style="color: red;">{#no_items_found#|escape}</span></td>
        </tr>
      {/foreach}
    </table>
  </body>
</html>
