<?php

require_once PH_MODULES_DIR . 'reports/controllers/reports.controller.php';

class Custom_Report_Controller extends Reports_Controller {

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch($this->action) {
            case 'ajax_redirect_to_customer_proceedings':
                $this->_redirectToCustomerProceedings();
                break;
            case 'ajax_save_customer_note':
                $this->_saveCustomerNote();
                break;
            case 'ajax_advance_loan_cover':
                $this->_advanceLoanCover();
                break;
            case 'ajax_advance_loan_cover_calculate':
                $this->_advanceLoanCoverCalculate();
                break;
            case 'ajax_advance_principal_cover':
                $this->_advancePrincipalCover();
                break;
            case 'ajax_fully_cover_loan_contract':
                $this->_fullyCoverLoanContract();
                break;
            case 'ajax_activate_edit_contract':
                $this->_activateEditContract();
                break;
            case 'ajax_validate_edit_contract':
                $this->_validateEditContract();
                break;
            case 'ajax_edit_contract':
                $this->_editContract();
                break;
            case 'ajax_annul_contract':
                $this->_annulContract();
                break;
            case 'search_related_persons':
                $this->_searchRelatedPersons();
                break;
            case 'ajax_show_paid_repayment_plan_gt2':
                $this->_showPaidRepaymentPlanGT2();
                break;
            case 'ajax_show_ckr_noi_add_panel':
                $this->_showCkrNoiAddPanel();
                break;
            case 'ajax_save_ckr_noi_data':
                $this->_saveCkrNoiData();
                break;
            case 'ajax_submit_ckr_noi_file':
                $this->_submitCkrNoiFile();
                break;
            case 'ajax_load_sms_panel':
                $this->_loadSMSPanel();
                break;
            case 'ajax_principal_cover_change_plan':
                $this->_principalCoverChangePlan();
                break;
            case 'ajax_create_payment_plan_version':
                $this->_createPaymentPlanVersion();
                break;
            case 'ajax_get_payment_plan_versions':
                $this->_getPaymentPlanVersions();
                break;
            case 'ajax_apply_repayment_plan_version':
                $this->_applyPaymentPlanVersion();
                break;
            case 'ajax_reload_report_panels':
                $this->_reloadReportPanels();
                break;
            default:
                parent::execute();
                break;
        }
    }

    /*
     * Function to create debit note in the selected invoice
     */
    public function _redirectToCustomerProceedings() {
        $this->prepareReportSettings();

        $search_data = $this->registry['request']->get('search_data');
        $search_type = $this->registry['request']->get('search_type');

        $redirect_url = '';

        // get the infomration for the customer
        $sql = 'SELECT TRIM(CONCAT("[", c.code, "] ", CONCAT(ci18n.name, " ", ci18n.lastname)))' . "\n" .
               'FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
               'LEFT JOIN '. DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
               '  ON (ci18n.parent_id=c.id AND ci18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE c.id="' . $this->registry['request']->get('customer_id') . '"' . "\n";
        $customer_name = $this->registry['db']->GetOne($sql);

        $search_filters = array(
            'hidden_type'      => PROJECT_PROCEEDING_TYPE,
            'search_fields'    => array(),
            'compare_options'  => array(),
            'values'           => array(),
            'logical_operator' => array(),
            'date_period'      => array(),
            'sort'             => array('p.added'),
            'order'            => array('DESC'),
            'display'          => 10
        );

        $search_filters['search_fields'][0] = 'p.type';
        $search_filters['compare_options'][0] = "= '%s'";
        $search_filters['values'][0] = PROJECT_PROCEEDING_TYPE;
        $search_filters['logical_operator'][0] = 'AND';
        $search_filters['date_period'][0] = '';

        $search_filters['search_fields'][1] = 'p.customer';
        $search_filters['compare_options'][1] = "= '%s'";
        $search_filters['values'][1] = $this->registry['request']->get('customer_id');
        $search_filters['logical_operator'][1] = 'AND';
        $search_filters['date_period'][1] = '';
        $search_filters['values_autocomplete'][1] = $customer_name;

        $this->registry['session']->set('search_project', $search_filters, '', true);
        $redirect_url = sprintf('%s://%s%sindex.php?%s=projects&projects=search',
                                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param']);

        echo($redirect_url);
        exit;
    }

    /*
     * Function to save customer note entered in the report
     */
    public function _saveCustomerNote() {
        $this->prepareReportSettings();

        $message = '';
        $request = $this->registry['request'];

        $request->get('credit_ins_customer_note');

        // get the model
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';

        // complete the additional data
        $filters = array('where' => array('c.id="' . $request->get('customer_id') . '"'));
        $customer = Customers::searchOne($this->registry, $filters);

        if ($customer->get('notes') != $request->get('credit_ins_customer_note')) {
            $old_customer = clone $customer;

            $customer->set('notes', $request->get('credit_ins_customer_note'), true);

            $this->registry['db']->StartTrans();
            if ($customer->save()) {
                // write history
                $new_customer = Customers::searchOne($this->registry, $filters);
                Customers_History::saveData($this->registry, array('model' => $new_customer, 'action_type' => 'edit', 'new_model' => $new_customer, 'old_model' => $old_customer));

                $message = $this->i18n('reports_customer_note_edit_successfully');
            } else {
                $message = $this->i18n('error_reports_customer_note_edit_failed');
                $this->registry['db']->FailTrans();
            }
            $this->registry['db']->CompleteTrans();
        } else {
            $message = $this->i18n('reports_customer_note_edit_successfully');
        }

        echo($message);
        return true;
    }

    /*
     * Function actvate the form for advance loan cover
     */
    public function _advanceLoanCover() {
        $this->prepareReportSettings();

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_loan_cover.html';

        $viewer->data['repayment_plan_id'] = $this->registry['request']->get('contract_id');

        $operation_result['template']['content'] = $viewer->fetch();
        $operation_result['template']['title'] = $this->i18n('reports_repayment_plan_advanced_full_cover');

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function actvate the form for advance principal cover
     */
    public function _advancePrincipalCover() {
        $this->prepareReportSettings();

        $total_owed = $this->calculateOwedToDate($this->registry['request']->get('contract_id'));

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_principal_cover.html';

        $viewer->data['repayment_plan_id'] = $this->registry['request']->get('contract_id');
        $viewer->data['repayment_total_owed'] = $total_owed['owed_to_current_date'];
        $viewer->data['owed_dues_to_date'] = json_encode($total_owed['owed_dues_to_date']);
        $dates_list = array_reverse(array_keys($total_owed['owed_dues_to_date']));
        $viewer->data['last_possible_date'] = reset($dates_list);
        $viewer->data['current_date'] = date('Y-m-d');

        $operation_result['template']['content'] = $viewer->fetch();
        $operation_result['template']['title'] = $this->i18n('reports_repayment_prepay_principal');

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to change the repayment plan based
     */
    public function _principalCoverChangePlan() {
        $this->prepareReportSettings();
        $operation_result = array(
            'result'                   => false,
            'message'                  => '',
            'template_repayment_plans' => '',
            'template_credits_list'    => '',
            'template_all_credits_list'=> ''
        );

        $document_id = $this->registry['request']->get('contract_id');
        $doc_filters = array('where'    => array('d.id = ' . $document_id),
                             'sanitize' => false);
        $contract = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $contract->getVars();
        $contract->getAssocVars();
        $current_gt2_var = $contract->getGT2Vars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $owed_dues = $this->calculateOwedToDate($contract->get('id'), $this->registry['request']->get('advance_pay_date'));

        $principal_paid = $this->registry['request']->get('advance_pay_sum');
        $principal_paid = floatval($principal_paid) - floatval($owed_dues['owed_to_current_date']);

        require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
        $automations_controller = new Credits_Automations_Controller($this->registry);
        $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');

        // load automations i18n files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
            false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }
        $adapter = $automations_controller->getAdapter();
        $adapter->current_model = clone $contract;
        $adapter->current_model_vars = $contract->getAssocVars();
        $adapter->calculate_from_date = $this->registry['request']->get('principal_repayment_date');
        $adapter->prepaid_principal = $principal_paid;
        $adapter->reason_for_change = 'prepaid_principal';
        $operation_result['result'] = $adapter->calculateInvestmentCreditPayments();

        if ($operation_result['result']) {
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');
        } else {
            $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed');
        }

        print json_encode($operation_result);
        exit;
    }



    /*
     * Function actvate the form for advance loan cover
     */
    public function _advanceLoanCoverCalculate() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        $changed_gt2_table = $this->advancedLoanCoverRecalculate($this->registry['request']->get('repayment_plan_id'), $this->registry['request']->get('advance_pay_date'));
        $sums_to_pay = $changed_gt2_table['sums_to_pay'];

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_loan_cover_sums.html';
        $viewer->data['sums_to_pay'] = $sums_to_pay;
        $viewer->data['repayment_date'] = $this->registry['request']->get('advance_pay_date');
        $viewer->data['repayment_plan'] = $this->registry['request']->get('repayment_plan_id');

        $operation_result['content'] = $viewer->fetch();
        $operation_result['title'] = $this->i18n('reports_repayment_plan_total_sums');

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to fully cover a loan contract based on certain date
     */
    public function _fullyCoverLoanContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'                   => true,
            'message'                  => '',
            'template_repayment_plans' => '',
            'template_credits_list'    => '',
            'template_all_credits_list'=> ''
        );

        $changed_data = $this->advancedLoanCoverRecalculate($this->registry['request']->get('contract_id'), $this->registry['request']->get('repayment_date'));

        // make the changes for the document
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $document->getGT2Vars();
        $assoc_vars = $document->getAssocVars();
        $old_document = clone $document;

        // start the transaction
        $this->registry['db']->StartTrans();

        // get the new sum of the incomes reason
        $new_total = 0;
        foreach ($changed_data['gt2']['values'] as $row_id => $row_values) {
            $new_total += $row_values['article_second_code'];
        }
        $payments = $this->getPaymentsAmounts($document->get('id'), INCOMES_REASON_TYPE_ID);

        // substract the values in different currencies (different from the currency of the contract)
        foreach ($payments as $p) {
            if ($p['rate'] != 1) {
                $new_total = $new_total-$p['contract_paid_amount'];
            }
        }

        $issue_correction_result = $this->issueReasonCorrection($document->get('id'), $new_total);
        if ($issue_correction_result['result']) {
            // update the GT2 table
            $document->set('grouping_table_2', $changed_data['gt2'], true);
            $document->calculateGT2();
            $document->set('table_values_are_set', true, true);

            if ($document->saveGT2Vars()) {
                // update the prepayment flag
                $insert = array();
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PREPAYMENT]['id'], 1, 1, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), (!empty($assoc_vars[SCHEDULE_VAR_PREPAYMENT]['multilang']) ? $this->registry['lang'] : ''));

                // perform the insert queries
                $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                       'VALUES ' . implode(",\n", $insert) . "\n" .
                       'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($sql);

                // write history
                $new_document = Documents::searchOne($this->registry, $doc_filters);
                $new_document->getVars();

                $history_params = array(
                    'model'       => $document,
                    'action_type' => 'edit',
                    'new_model'   => $new_document,
                    'old_model'   => $old_document
                );

                Documents_History::saveData($this->registry, $history_params);

                if (floatval(round($changed_data['sums_to_pay']['extra'], 2)) > 0) {
                    require_once PH_MODULES_DIR . 'automations/plugins/credilink/controllers/credilink.automations.controller.php';
                    $automations_controller = new Credilink_Automations_Controller($this->registry);

                    // create new incomes reason
                    $inc_reason_currency = $document->getVarValue(SCHEDULE_VAR_CREDIT_CURRENCY);
                    $params_reason = array(
                        'customer'     => $document->get('customer'),
                        'issue_date'   => date('Y-m-d'),
                        'payment_type' => 'bank',
                        'document_id'  => $document->get('id'),
                        'total'        => round($changed_data['sums_to_pay']['extra'], 2),
                        'currency'     => $inc_reason_currency,
                        'company'      => INCOMES_REASON_PREPAYMENT_TAX_COMPANY ,
                        'office'       => INCOMES_REASON_PREPAYMENT_TAX_OFFICE,
                        'type'         => INCOMES_REASON_PREPAYMENT_TAX,
                        'container_id' => (defined('INCOMES_REASON_PREPAYMENT_TAX_CONTAINER_' . strtoupper($inc_reason_currency)) ? constant('INCOMES_REASON_PREPAYMENT_TAX_CONTAINER_' . strtoupper($inc_reason_currency)) : 'BGN'),
                        'article_id'   => INCOMES_REASON_PREPAYMENT_TAX_ARTICLE_ID,
                        'parent_reason' => '',
                        'name'         => $this->i18n('reports_repayment_plan_tax_advance_cover'),
                    );
                    $errors = $automations_controller->createContractIncomesReason($params_reason);

                    if (!empty($errors)) {
                        $operation_result['result'] = false;
                        $operation_result['message'] = $this->i18n('error_repayment_plan_add_tax_prepayment_failed');
                        $this->registry['db']->FailTrans();
                    }
                }
            } else {
                // error occurred
                $operation_result['result'] = false;
                $operation_result['message'] = $this->i18n('error_repayment_plan_edit_contract_failed');
                $this->registry['db']->FailTrans();
            }
        } else {
            $operation_result['result'] = false;
            $operation_result['message'] = $this->i18n('error_repayment_plan_issue_correction_failed') . "\n\n" . implode("\n", $issue_correction_result['messages']);
            $this->registry['db']->FailTrans();
        }

        // complete the transaction
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($operation_result['result']) {
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');
        }

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to prepare the gt2 table for edit and prepare the form where the table will be situated
     */
    public function _activateEditContract() {
        $this->prepareReportSettings();
        // check the super users
        $super_roles = array();
        if (defined('SUPER_ROLES')) {
            $super_roles = preg_split('#\s*,\s*#', SUPER_ROLES);
            $super_roles = array_filter($super_roles);
        }

        $operation_result = array(
            'result'  => true,
            'message' => '',
            'content' => '',
            'title'   => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $document->sanitize();

        $document_vars = $document->get('vars');

        // goes through all of the vars to find the GT2 table
        foreach ($document_vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                if (!$this->registry['request']->get('annul')) {
                    $vars_to_make_editable = array(
                        'article_trademark', 'article_delivery_code', 'article_deliverer_name', 'article_barcode', 'article_height', 'article_weight',
                        'free_text2', 'free_text1', 'free_text3', 'article_name', 'article_width', 'free_text4'
                    );

                    if (in_array($this->registry['currentUser']->get('role'), $super_roles)) {
                        $vars_to_make_editable[] = 'quantity';
                        $vars_to_make_editable[] = 'price';
                    }
                } else {
                    $vars_to_make_editable = array();
                }

                $gt2_vars = $var['vars'];
                foreach ($gt2_vars as $var_name => $var_data) {
                    if (in_array($var_name, $vars_to_make_editable)) {
                        $document_vars[$var_idx]['vars'][$var_name]['readonly'] = false;
                        $document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur'] = 'credilinkRoundEditedValues(this);' . (!empty($document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur']) ? $document_vars[$var_idx]['vars'][$var_name]['js_methods']['onblur'] : '');
                        $document_vars[$var_idx]['vars'][$var_name]['js_filter'] = 'insertOnlyReals';
                    } else {
                        $document_vars[$var_idx]['vars'][$var_name]['readonly'] = true;
                    }
                }

                // check which row have to be read only
                $current_iterator = 1;
                $readonly_rows = array();
                foreach ($var['values'] as $row_id => $var_data) {
                    // define the readonly rows
                    if ($var_data['article_code'] < date('Y-m-d') && !in_array($this->registry['currentUser']->get('role'), $super_roles)) {
                        $readonly_rows[] = $current_iterator;
                    }
                    $current_iterator++;
                }

                $document_vars[$var_idx]['rows_readonly'] = $readonly_rows;
                $document_vars[$var_idx]['delimeter_start'] = count($readonly_rows);
                $document_vars[$var_idx]['hide_delete'] = true;
                $document_vars[$var_idx]['hide_multiple_rows_buttons'] = true;
                $document->set('grouping_table_2', $document_vars[$var_idx], true);
            }
        }

        $this->registry['include_gt2'] = true;
        $document->set('vars', $document_vars, true);
        $document->getLayoutVars();

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = $viewer->theme->templatesDir;
        $viewer->template = '_gt2_edit.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['model'] = $document;

        $template = $viewer->fetch();
        $title = '';
        if ($this->registry['request']->get('annul')) {
            $template = sprintf(
                '<form action="" method="post">%s<button type="button" class="button" style="margin-left: 5px;" onclick="credilinkAnnulContract(this, \'%d\'); return false;">%s</button></form>',
                $template,
                $document->get('id'),
                $this->i18n('reports_repayment_plan_option_annul')
            );
            $title = $this->i18n('reports_repayment_plan_annul_title');
        } else {
            $template = sprintf(
                '<form action="" method="post">%s<button type="button" class="button" style="margin-left: 5px;" onclick="credilinkEditContract(this, \'%d\'); return false;">%s</button></form>',
                $template,
                $document->get('id'),
                $this->i18n('edit')
            );
            $title = $this->i18n('reports_repayment_plan_edit_title');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        $operation_result['content'] = $template;
        $operation_result['title'] = $title;

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to validate the edition of the contract
     */
    public function _validateEditContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'   => false,
            'message' => array()
        );

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $current_gt2_var = $document->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);
        $old_document = clone $document;

        // make empty new contract
        $request_document = new Document($this->registry);
        $request_document->set('type', $document->get('type'), true);
        $request_document->getVars();

        // find the gt2 var in the request model and get its values
        $request_gt2 = $request_document->getGT2Vars();

        $new_owed = 0;
        $new_paid_total = 0;
        foreach ($request_gt2['values'] as $row_vals) {
            $new_owed += sprintf('%.2f', floatval($row_vals['article_second_code']));
            $new_paid_total += sprintf('%.2f', floatval($row_vals['article_volume']));
        }
        $new_paid_total = sprintf('%.2f', round($new_paid_total, 2));
        $new_owed = sprintf('%.2f', round($new_owed, 2));

        // get payments amounts
        $payments = $this->getPaymentsAmounts($document->get('id'), INCOMES_REASON_TYPE_ID);

        $payments_total = 0;
        foreach ($payments as $p) {
            $payments_total += $p['reason_paid_amount'];
        }
        $payments_total = sprintf('%.2f', round($payments_total, 2));

        if ($new_paid_total > $payments_total) {
            $operation_result['error'] = true;
            $operation_result['message'][] = sprintf($this->i18n('error_repayment_plan_edit_contract_paid_value_mismatch_more'), $payments_total);
        }

        if ($new_owed < $new_paid_total) {
            $operation_result['error'] = true;
            $operation_result['message'][] = $this->i18n('error_repayment_plan_edit_contract_paid_value_more_than_current_value');
        }

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to edit the contract
     */
    public function _editContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'                     => false,
            'message'                   => '',
            'template_repayment_plans'  => '',
            'template_credits_list'     => '',
            'template_all_credits_list' => ''
        );

        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        // include the lang files for contracts
        $lang_files = array(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry['lang'] . '/documents.ini');
        $this->loadI18NFiles($lang_files);

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $current_gt2_var = $document->getGT2Vars();
        $old_assoc_vars = $document->getAssocVars();
        $old_principal = sprintf('%.2f', round(floatval($old_assoc_vars[DOCUMENT_CONTRACT_CREDIT]['value']), 2));
        $this->registry->set('get_old_vars', false, true);
        $old_document = clone $document;

        // make empty new contract
        $request_document = new Document($this->registry);
        $request_document->set('type', $document->get('type'), true);
        $request_document->getVars();

        $old_paid_total = 0;
        $old_total = 0;
        foreach ($current_gt2_var['values'] as $row_vals) {
            $old_total += $row_vals['article_second_code'];
            $old_paid_total += $row_vals['article_volume'];
        }
        $old_paid_total = sprintf('%.2f', round(floatval($old_paid_total), 2));
        $old_total = sprintf('%.2f', round(floatval($old_total), 2));

        // find the gt2 var in the request model and get its values
        $request_gt2 = $request_document->getGT2Vars();
        $current_gt2_var['values'] = $request_gt2['values'];
        unset($request_gt2);

        $new_total = 0;
        $new_paid_total = 0;
        $new_principal = 0;
        foreach ($current_gt2_var['values'] as $row_id => $row_vals) {
            $new_total += $row_vals['article_second_code'];
            $new_paid_total += $row_vals['article_volume'];
            $new_principal += $row_vals['price'];
            if (round(floatval($row_vals['article_deliverer_name']), 2) > 0) {
                $current_gt2_var['values'][$row_id]['discount_surplus_field'] = 'discount_value';
            }
        }
        $new_paid_total = sprintf('%.2f', round(floatval($new_paid_total), 2));
        $new_principal = sprintf('%.2f', round(floatval($new_principal), 2));
        $new_total = sprintf('%.2f', round(floatval($new_total), 2));

        // get the payments total to deduce the paid part in different currency
        $schedule_payments = $this->getPaymentsAmounts($document->get('id'), INCOMES_REASON_TYPE_ID);
        $main_incomes_reason_total = $new_total;
        $contract_currency = $document->getVarValue(SCHEDULE_VAR_CREDIT_CURRENCY);
        foreach ($schedule_payments as $sc_pay) {
            if ($sc_pay['currency'] != $contract_currency) {
                $main_incomes_reason_total = $main_incomes_reason_total - $sc_pay['contract_paid_amount'];
            }
        }
        $main_incomes_reason_total = round($main_incomes_reason_total, 2);

        $document->set('grouping_table_2', $current_gt2_var, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);

        $this->registry->set('get_old_vars', true, true);

        $this->registry['db']->StartTrans();
        if ($document->saveGT2Vars()) {
            // activate the function that will change the status and the tag of the contract
            require_once PH_MODULES_DIR . 'automations/plugins/credilink/controllers/credilink.automations.controller.php';
            $automations_controller = new Credilink_Automations_Controller($this->registry);
            $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');
            $automations_controller->calculateCollateralToOutstanding($document);

            // write history
            $new_document = Documents::searchOne($this->registry, $doc_filters);
            $new_document->getVars();

            $history_params = array(
                'model'       => $document,
                'action_type' => 'edit',
                'new_model'   => $new_document,
                'old_model'   => $old_document
            );

            Documents_History::saveData($this->registry, $history_params);

            $upd_var = $old_assoc_vars[DOCUMENT_CONTRACT_CREDIT]['id'];
            if ($automations_controller->defineCreditLine($document)) {
                // change the principal which the next comparison will be made with
                $old_principal = sprintf('%.2f', round(floatval($old_assoc_vars[$automations_controller->settings['credit_amount_utilized']]['value']), 2));
                $upd_var = $old_assoc_vars[$automations_controller->settings['credit_amount_utilized']]['id'];
            }

            if ($old_principal != $new_principal) {
                $old_document = clone $new_document;
                $old_document->sanitize();

                // check the lang of the var
                $query = 'SELECT `lang` FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $new_document->get('id') . '" AND `var_id`="' . $upd_var . '"';
                $var_lang = $this->registry['db']->GetOne($query);

                // update the principal
                $insert = array();
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $new_document->get('id'), $upd_var, 1, $new_principal, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), $var_lang);

                // perform the insert queries
                $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                       'VALUES ' . implode(",\n", $insert) . "\n" .
                       'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($sql);

                $filters = array('where'      => array('d.id="' . $new_document->get('id') . '"'),
                                 'model_lang' => $this->registry['lang']);
                $new_document = Documents::searchOne($this->registry, $filters);
                $new_document->getVars();
                Documents_History::saveData($this->registry, array('model' => $new_document, 'action_type' => 'edit', 'new_model' => $new_document, 'old_model' => $old_document));
            }

            if (!$this->registry['db']->HasFailedTrans()) {
                // success occurred
                $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');

                // check if the incomes reason has to be changed
                $rel_incomes_reason = $this->getRelatedIncomesReason($new_document->get('id'));
                if ($main_incomes_reason_total != $rel_incomes_reason->get('total_with_vat')) {
                    $issue_correction_result = $this->issueReasonCorrection($new_document->get('id'), $main_incomes_reason_total);

                    if (!$issue_correction_result['result']) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed_correction_error') . "\n\n" . implode("\n", $issue_correction_result['messages']);
                    }
                }

                if ($new_paid_total != $old_paid_total && !$operation_result['error']) {
                    $additional_sum_to_distribute = $new_paid_total - $old_paid_total;
                    $distribute_result = $this->distributeAdditionalSum($new_document->get('id'), $additional_sum_to_distribute);

                    if (!$distribute_result) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed_additional_sum_distribution');
                    }
                }
            }

            if (!$this->registry['db']->HasFailedTrans()) {
                $params_payment_check = array(
                    'status'                      => DOCUMENT_FULL_PAID_STATUS,
                    'incomes_reason_type'         => INCOMES_REASON_TYPE_ID,
                    'full_payment_var'            => DOCUMENT_CONTRACT_REPAYMENT_DATE,
                    'repayment_status'            => DOCUMENT_CONTRACT_TYPE_REPAYMENT,
                    'repayment_status_in_advance' => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_ADVANCE,
                    'repayment_status_in_time'    => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_TIME
                );

                if (!$automations_controller->checkFullPayment($new_document, $params_payment_check)) {
                    $operation_result['error'] = true;
                    $operation_result['message'] = $this->i18n('error_reports_repayment_plan_tag_status_failed');
                    $this->registry['db']->FailTrans();
                } else {
                    // distribute payments
                    $gt2 = $new_document->getGT2Vars();

                    // recalculate grouping table and get payments
                    $result = $this->recalculateGroupingTable($new_document, $gt2, 'edit');

                    if (!$result) {
                        $this->registry['db']->FailTrans();
                        $operation_result['error'] = true;
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_redestribute_failed');
                    }
                }
            }
        } else {
            // error occured
            $operation_result['error'] = true;
            $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to ajax annul the contract
     */
    public function _annulContract() {
        $this->prepareReportSettings();

        $operation_result = array(
            'error'                     => false,
            'message'                   => '',
            'template_repayment_plans'  => '',
            'template_credits_list'     => '',
            'template_all_credits_list' => ''
        );

        // include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

        // include the lang files for contracts
        $lang_files = array(PH_MODULES_DIR . 'contracts/i18n/' . $this->registry['lang'] . '/documents.ini');
        $this->loadI18NFiles($lang_files);

        // get old document
        $doc_filters = array('where'    => array('d.id = ' . $this->registry['request']->get('contract_id')),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();

        $old_document = clone $document;

        $gt2_var = $document->getGT2Vars();

        $current_date = date('Y-m-d');
        $previous_date = $document->get('date_start');
        $new_total = 0;
        $current_payment = '';
        foreach ($gt2_var['values'] as $row_id => $row_values) {
            if ($previous_date>$current_date) {
                $gt2_var['values'][$row_id]['quantity'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['article_trademark'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['free_text2'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['free_text1'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['free_text3'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['article_delivery_code'] = sprintf('%.2f', 0);
                $gt2_var['values'][$row_id]['article_second_code'] = $row_values['price'];
            } else {
                $current_payment = $row_id;
            }
            $gt2_var['values'][$row_id]['article_deliverer_name'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_barcode'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_height'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_name'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_width'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['free_text4'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_weight'] = sprintf('%.2f', 0);
            $gt2_var['values'][$row_id]['article_volume'] = sprintf('%.2f', 0);

            $gt2_var['values'][$row_id]['average_weighted_delivery_price'] = $gt2_var['values'][$row_id]['price'];
            $gt2_var['values'][$row_id]['free_field1'] = $gt2_var['values'][$row_id]['quantity'];
            $gt2_var['values'][$row_id]['free_field2'] = $gt2_var['values'][$row_id]['article_trademark'];
            $gt2_var['values'][$row_id]['article_description'] = $gt2_var['values'][$row_id]['free_text2'];
            $gt2_var['values'][$row_id]['free_field3'] = $gt2_var['values'][$row_id]['free_text1'];
            $gt2_var['values'][$row_id]['free_text5'] = $gt2_var['values'][$row_id]['free_text3'];
            $gt2_var['values'][$row_id]['free_field4'] = $gt2_var['values'][$row_id]['article_delivery_code'];
            $gt2_var['values'][$row_id]['free_field5'] = $gt2_var['values'][$row_id]['article_second_code'];

            $previous_date = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($row_values['article_code'])));

            $new_total += $gt2_var['values'][$row_id]['article_second_code'];
        }

        // get the paid sum and redistribute all the sums per different values
        $schedule_payments = $this->getPaymentsAmounts($document->get('id'), INCOMES_REASON_TYPE_ID);
        $incomes_reason_total = $new_total;
        $amount_left = 0;
        foreach ($schedule_payments as $sch_pay) {
            $amount_left += $sch_pay['contract_amount'];
            if ($sch_pay['currency'] != $document->getVarValue(SCHEDULE_VAR_CREDIT_CURRENCY))  {
                $incomes_reason_total = $incomes_reason_total - $sch_pay['contract_amount'];
            }
        }

        $overpay = 0;
        if ($amount_left > $new_total) {
            $overpay = $amount_left - $new_total;
        }
        $new_total += $overpay;

        $order_completion = array(
            'tax_ang' => array(
                'paid' => 'article_width',
                'left' => 'free_field3'
            ),
            'tax' => array(
                'paid' => 'article_name',
                'left' => 'article_description',
            ),
            'warranty' => array(
                'paid' => 'article_height',
                'left' => 'free_field2',
            ),
            'interest' => array(
                'paid' => 'article_barcode',
                'left' => 'free_field1',
            ),
            'lpg' => array(
                'paid' => 'free_text4',
                'left' => 'free_text5',
            ),
            'penalty' => array(
                'paid' => 'article_weight',
                'left' => 'free_field4',
            )
        );

        foreach ($gt2_var['values'] as $key => $vals) {
            // add the overpay sum to the warranty
            if ($key == $current_payment) {
/*                $gt2_var['values'][$key]['article_trademark'] = sprintf('%.2f', round($gt2_var['values'][$key]['article_trademark'] + $overpay, 2));
                $gt2_var['values'][$key]['article_second_code'] = sprintf('%.2f', round($gt2_var['values'][$key]['article_second_code'] + $overpay, 2));
                $gt2_var['values'][$key]['free_field2'] = sprintf('%.2f', round($gt2_var['values'][$key]['free_field2'] + $overpay, 2));
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', round($gt2_var['values'][$key]['free_field5'] + $overpay, 2));*/
            }

            if ($vals['average_weighted_delivery_price'] > 0 && $amount_left) {
                if ($vals['average_weighted_delivery_price'] > $amount_left) {
                    $gt2_var['values'][$key]['average_weighted_delivery_price'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] - $amount_left);
                    $gt2_var['values'][$key]['article_deliverer_name'] = sprintf('%.2f', $gt2_var['values'][$key]['article_deliverer_name'] + $amount_left);
                    $amount_left = 0;
                } else {
                    $gt2_var['values'][$key]['average_weighted_delivery_price'] = sprintf('%.2f', 0);
                    $gt2_var['values'][$key]['article_deliverer_name'] = sprintf('%.2f', $gt2_var['values'][$key]['article_deliverer_name'] + $vals['average_weighted_delivery_price']);
                    $amount_left = $amount_left - $vals['average_weighted_delivery_price'];
                }

                // update sum fields
                $gt2_var['values'][$key]['article_width'] = sprintf('%.2f', $gt2_var['values'][$key]['article_barcode'] + $gt2_var['values'][$key]['article_height']);
                $gt2_var['values'][$key]['article_volume'] = sprintf('%.2f', $gt2_var['values'][$key]['article_deliverer_name'] + $gt2_var['values'][$key]['article_width'] + $gt2_var['values'][$key]['article_weight']);
                $gt2_var['values'][$key]['free_field3'] = sprintf('%.2f', $gt2_var['values'][$key]['free_field1'] + $gt2_var['values'][$key]['free_field2']);
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] + $gt2_var['values'][$key]['free_field3'] + $gt2_var['values'][$key]['free_field4']);
                $gt2_var['values'][$key]['discount_surplus_field'] = 'discount_value';
            }
        }

        foreach ($gt2_var['values'] as $key => $vals) {
            if ($vals['free_field5'] > 0 && $amount_left) {
                foreach ($order_completion as $cover => $cover_var) {
                    if ($vals[$cover_var['left']] > 0 && $amount_left) {
                        if ($vals[$cover_var['left']] > $amount_left) {
                            $current_payments_data[$cover] = $amount_left;
                            $gt2_var['values'][$key][$cover_var['left']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['left']] - $amount_left);
                            $gt2_var['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['paid']] + $amount_left);
                            $amount_left = 0;
                        } else {
                            $current_payments_data[$cover] = $vals[$cover_var['left']];
                            $gt2_var['values'][$key][$cover_var['left']] = sprintf('%.2f', 0);
                            $gt2_var['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2_var['values'][$key][$cover_var['paid']] + $vals[$cover_var['left']]);
                            $amount_left = $amount_left - $vals[$cover_var['left']];
                        }
                    }
                }

                // update sum fields
                $gt2_var['values'][$key]['article_width'] = sprintf('%.2f', $gt2_var['values'][$key]['article_barcode'] + $gt2_var['values'][$key]['article_height']);
                $gt2_var['values'][$key]['article_volume'] = sprintf('%.2f', $gt2_var['values'][$key]['article_deliverer_name'] + $gt2_var['values'][$key]['article_width'] + $gt2_var['values'][$key]['article_weight']);
                $gt2_var['values'][$key]['free_field3'] = sprintf('%.2f', $gt2_var['values'][$key]['free_field1'] + $gt2_var['values'][$key]['free_field2']);
                $gt2_var['values'][$key]['free_field5'] = sprintf('%.2f', $gt2_var['values'][$key]['average_weighted_delivery_price'] + $gt2_var['values'][$key]['free_field3'] + $gt2_var['values'][$key]['free_field4']);
                $gt2_var['values'][$key]['discount_surplus_field'] = 'discount_value';
            }
        }

        $this->registry['db']->StartTrans();
        $document->set('grouping_table_2', $gt2_var, true);
        $document->calculateGT2();
        $document->set('table_values_are_set', true, true);

        if ($document->saveGT2Vars()) {
            // write history
            $new_document = Documents::searchOne($this->registry, $doc_filters);
            $new_document->getVars();
            $gt2_var = $new_document->getGT2Vars();

            $history_params = array(
                'model'       => $document,
                'action_type' => 'edit',
                'new_model'   => $new_document,
                'old_model'   => $old_document
            );

            Documents_History::saveData($this->registry, $history_params);

            $gt2_var = $new_document->getGT2Vars();
            $old_document = clone $new_document;

            require_once PH_MODULES_DIR . 'automations/plugins/credilink/controllers/credilink.automations.controller.php';
            $automations_controller = new Credilink_Automations_Controller($this->registry);

            $params_payment_check = array(
                'status'                      => DOCUMENT_FULL_PAID_STATUS,
                'incomes_reason_type'         => INCOMES_REASON_TYPE_ID,
                'full_payment_var'            => DOCUMENT_CONTRACT_REPAYMENT_DATE,
                'repayment_status'            => DOCUMENT_CONTRACT_TYPE_REPAYMENT,
                'repayment_status_in_advance' => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_ADVANCE,
                'repayment_status_in_time'    => DOCUMENT_CONTRACT_TYPE_REPAYMENT_IN_TIME
            );

            if (!$automations_controller->checkFullPayment($new_document, $params_payment_check)) {
                $operation_result['error'] = true;
                $operation_result['message'] = $this->i18n('error_reports_repayment_plan_tag_status_failed');
                $this->registry['db']->FailTrans();
            } else {
                // recalculate grouping table and get payments
                $result = $this->recalculateGroupingTable($new_document, $gt2_var, 'annul');
                $issue_correction_result = $this->issueReasonCorrection($new_document->get('id'), sprintf('%.2f', $new_total));

                if ($result && $issue_correction_result['result']) {
                    $new_document = Documents::searchOne($this->registry, $doc_filters);
                    $new_document->getVars();

                    $history_params = array(
                        'model'       => $new_document,
                        'action_type' => 'edit',
                        'new_model'   => $new_document,
                        'old_model'   => $old_document
                    );

                    Documents_History::saveData($this->registry, $history_params);
                } else {
                    // error occured
                    $operation_result['error'] = true;
                    if (!$result) {
                        $operation_result['message'] = $this->i18n('error_reports_repayment_plan_edit_failed');
                    } else {
                        $operation_result['message'] = implode("\n", array_merge(array($this->i18n('error_repayment_plan_issue_correction_failed')), $issue_correction_result['messages']));
                    }
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            // error occured
            $operation_result['error'] = true;
            $operation_result['message'] = $this->i18n('error_repayment_plan_edit_contract_failed');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        if (!$operation_result['error']) {
            $operation_result['message'] = $this->i18n('reports_repayment_plan_edit_successfully');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to ajax search the related persons
     */
    public function _searchRelatedPersons() {
        $this->prepareReportSettings();

        // get the data which have to refreshed
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

        // prepare the table which have to be reloaded
        list($bonds, $bonds_pagination) = Credilink_Customer_File::getBonds($this->registry, $this->registry['request']->get('customer'), $this->registry['request']->get('page'));

        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_related_persons.html';
        $viewer->data['customer_id'] = $this->registry['request']->get('customer');
        $viewer->data['related_persons'] = $bonds;
        $viewer->data['related_persons_pagination'] = $bonds_pagination;
        $viewer->data['report_type'] = $this->report;

        echo $viewer->fetch();
    }

    /*
     * Function to ajax search the related persons
     */
    public function _showPaidRepaymentPlanGT2() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $this->registry['request']->get('contract_id') . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $document->sanitize();

        $document_vars = $document->get('vars');

        // goes through all of the vars to find the GT2 table
        foreach ($document_vars as $var_idx => $var) {
            if ($var['type'] == 'gt2') {
                $document->set('grouping_table_2', $var, true);
                break;
            }
        }

        $this->registry['include_gt2'] = true;
        $document->set('vars', $document_vars, true);
        $document->getLayoutVars();

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = $viewer->theme->templatesDir;
        $viewer->template = '_gt2_view.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['model'] = $document;
        $template = $viewer->fetch();

        $this->registry->set('get_old_vars', $get_old_vars, true);

        $operation_result['content'] = $template;
        $operation_result['title'] = sprintf($this->i18n('reports_repayment_plan_for_contract'), $document->get('name'));

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to activate a panel for adding NOI or CKR report
     */
    public function _showCkrNoiAddPanel() {
        $this->prepareReportSettings();

        $operation_result = array(
            'content' => '',
            'title'   => ''
        );

        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_add_ckr_noi_panel.html';
        $viewer->data['customer_id'] = $this->registry['request']->get('customer_id');
        $viewer->data['type_report_add'] = $this->registry['request']->get('type_report_to_add');
        $template = $viewer->fetch();

        $operation_result['content'] = $template;
        $operation_result['title'] = ($this->registry['request']->get('type_report_to_add') == 'ckr' ? $this->i18n('reports_report_add_ckr_report') : $this->i18n('reports_report_add_noi_report'));

        print json_encode($operation_result);
        exit;
    }

    /*
     * Save the data CKR and NOI
     */
    public function _saveCkrNoiData() {
        $this->prepareReportSettings();

        $operation_result = array(
            'result'        => true,
            'message'       => array(),
            'table_num_row' => ''
        );

        // Include required classes
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.history.php';

        // get the model of the customer
        $cust_filters = array('where'      => array('c.id = \'' . $this->registry['request']->get('report_ckr_noi_customer') . '\''),
                              'model_lang' => $this->registry['lang']);
        $customer = Customers::searchOne($this->registry, $cust_filters);

        $get_old_vars = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $customer->getVars();
        $customer->sanitize();
        $old_customer = clone $customer;

        $assoc_vars = $customer->getAssocVars();

        $noi_ckr_value = '';
        if ($this->registry['request']->get('type_report_add') == 'ckr') {
            $noi_ckr_option = CUSTOMER_CLIENT_CKR_OPTION;
        } else {
            $noi_ckr_option = CUSTOMER_CLIENT_NOI_OPTION;
        }

        if (!empty($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE])) {
            // define the row
            $new_row = 1;

            $type_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['value'];
            $file_report = $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['value'];
            $date_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['value'];
            $notes_report = $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['value'];

            foreach ($type_report as $row_id => $row_value) {
                if (empty($row_value) && empty($file_report[$row_id]) && empty($date_report[$row_id]) && empty($notes_report[$row_id])) {
                    $new_row = $row_id;
                    break;
                }
                $new_row++;
            }

            // prepare the queries
            $insert = array();
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['id'], $new_row, $noi_ckr_option, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_TYPE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['id'], $new_row, '', $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['id'], $new_row, $this->registry['request']->get('ckr_noi_add_date'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_DATE]['multilang'] ? $this->registry['lang'] : ''));
            $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['id'], $new_row, General::slashesEscape($this->registry['request']->get('ckr_noi_add_notes')), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_REPORT_NOTES]['multilang'] ? $this->registry['lang'] : ''));

            $this->registry['db']->StartTrans();

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $insert) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);

            $new_customer = Customers::searchOne($this->registry, $cust_filters);

            Customers_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model'       => $customer,
                    'new_model'   => $new_customer,
                    'old_model'   => $old_customer
                )
            );

            if ($this->registry['db']->HasFailedTrans()) {
                // error occured
                $operation_result['result'] = false;
                $operation_result['message'] = $this->i18n('error_reports_ckr_noi_save_failed');
            } else {
                $operation_result['table_num_row'] = $new_row;
            }

            $this->registry['db']->CompleteTrans();
        } else {
            $operation_result['result'] = false;
            $operation_result['message'] = $this->i18n('error_reports_ckr_noi_installation_not_set_properly');
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        print json_encode($operation_result);
        exit;
    }

    /*
     * Function to save the file submited via AJAX
     */
    public function _submitCkrNoiFile() {
        $this->prepareReportSettings();
        $result = array(
            'result'      => true,
            'message'     => '',
            'template'    => ''
        );

        $new_row_id = $this->registry['request']->get('table_num_row');

        if (!empty($_FILES['ckr_noi_add_file']) && !$_FILES['ckr_noi_add_file']['error']) {
            $file = $_FILES['ckr_noi_add_file'];

            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            // main panel translations
            $filters = array('where'      => array('c.id = ' . $this->registry['request']->get('model_id')),
                             'model_lang' => $this->registry['lang']);
            $customer = Customers::searchOne($this->registry, $filters);

            $get_old_vars =  $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);
            $customer->getVars();
            $old_customer = clone $customer;

            // attach the file
            $params = array(
                'id'          => '',
                'name'        => $file['name'],
                'filename'    => '',
                'description' => '',
                'revision'    => '',
                'permission'  => 'all');
            $cstm_file_id = Files::attachFile($this->registry, $file, $params, $customer->sanitize());

            $customer->unsanitize();
            $assoc_vars = $customer->getAssocVars();

            // prepare the queries
            $insert_row = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")', $customer->get('id'), $assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['id'], $new_row_id, $cstm_file_id, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[CUSTOMER_CLIENT_CKR_NOI_FILE]['multilang'] ? $this->registry['lang'] : ''));

            $this->registry['db']->StartTrans();

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . $insert_row . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);

            $new_customer = Customers::searchOne($this->registry, $filters);
            $new_customer->getVars();

            Customers_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'edit',
                    'model'       => $new_customer,
                    'new_model'   => $new_customer,
                    'old_model'   => $old_customer
                )
            );

            $result['result'] = !$this->registry['db']->HasFailedTrans();
            $this->registry['db']->CompleteTrans();
        }

        if (!$result['result']) {
            $result['message'] = $this->i18n('error_reports_ckr_noi_save_file_failed');
        } else {
            // get the data which have to refreshed
            require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

            // prepare the table which have to be reloaded
            $ckr_data = Credilink_Customer_File::getCkrNoiData($this->registry, $this->registry['request']->get('model_id'));
            $ckr_noi_viewer = new Viewer($this->registry);
            $ckr_noi_viewer->loadCustomI18NFiles($this->report_lang_file);
            $ckr_noi_viewer->setFrameset('frameset_blank.html');
            $ckr_noi_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
            $ckr_noi_viewer->template = '_report_ckr_noi.html';
            $ckr_noi_viewer->data['ckr_noi'] = $ckr_data;
            $ckr_noi_viewer->data['customer_id'] = $this->registry['request']->get('model_id');
            $result['template'] = $ckr_noi_viewer->fetch();
        }

        echo('<script type="text/javascript">');
        echo('var operation_result=' . json_encode($result) . ';');
        echo('</script>');
        exit;
    }


    /*
     * Function to prepare the main report settings
     */
    public function prepareReportSettings() {
        $report = $this->getReportType();
        if (empty($report)) {
            $this->redirect($this->module, '', array('report_type' => ''), '');
        }
        $report = $report['name'];
        $this->report = $report;

        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini');
        $this->report_lang_file = $i18n_file;
        $this->registry['translater']->loadFile($this->report_lang_file);
        Reports::getReportSettings($this->registry, $report);

        return true;
    }

    /*
     * Function to prepare the main report settings
     */
    public function issueReasonCorrection($document_id, $reason_total) {
        $operation_result = array(
            'result'   => true,
            'messages' => true,
        );

        // issue correction document
        // get the incomes reason
        $incomes_reason = $this->getRelatedIncomesReason($document_id);

        if ($incomes_reason && $incomes_reason->get('total_with_vat') != $reason_total) {
            $incomes_reason->getVars();
            $gt2_var = $incomes_reason->getGT2Vars();

            $old_income_reason = clone $incomes_reason;

            foreach ($gt2_var['values'] as $key => $edited_row) {
                $gt2_var['values'][$key]['price'] = sprintf('%.2f', round($reason_total, 2));
                break;
            }

            $incomes_reason->set('grouping_table_2', $gt2_var, true);
            $incomes_reason->calculateGT2();
            $incomes_reason->set('table_values_are_set', true, true);

            $lang_files = array(PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance.ini',
                                PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_incomes_reasons.ini');
            $this->loadI18NFiles($lang_files);

            if ($incomes_reason->validate('edit') && $incomes_reason->saveCorrect($old_income_reason)) {
                $filters = array('where' => array('fir.id = ' . $incomes_reason->get('id'),
                                                  'fir.annulled_by = 0'));
                $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $new_incomes_reason->getGT2Vars();

                Finance_Incomes_Reasons_History::saveData(
                    $this->registry,
                    array(
                        'action_type' => ($old_income_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                        'new_model'   => $new_incomes_reason,
                        'old_model'   => $old_income_reason
                    )
                );
            } else {
                $operation_result['result'] = false;
                $operation_result['messages'] = $this->registry['messages']->getErrors();

                foreach ($operation_result['messages'] as $msg_id => $msg) {
                    $operation_result['messages'][$msg_id] = strip_tags($msg);
                }
            }
        }

        return $operation_result;
    }

    /*
     * Function to get the incomes reason related to the current repayment_plan
     */
    public function getRelatedIncomesReason($document_id) {
        // get the currency of the contract
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
               'WHERE `model`="Document" AND `model_type`="' . DOCUMENT_TYPE_ID . '" AND `name`="' . SCHEDULE_VAR_CREDIT_CURRENCY . '"' . "\n";
        $var_currency = $this->registry['db']->GetOne($sql);

        $sql = 'SELECT d_cstm_curr.value' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_curr' . "\n" .
               '  ON (d_cstm_curr.model_id=d.id AND d_cstm_curr.var_id="' . $var_currency . '")' . "\n" .
               'WHERE d.id="' . $document_id . '"' . "\n";
        $contract_currency = $this->registry['db']->GetOne($sql);

        $sql = 'SELECT frr.parent_id' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=frr.parent_id AND fir.type="' . INCOMES_REASON_TYPE_ID . '" AND fir.currency="' . $contract_currency . '")' . "\n" .
               'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND frr.link_to="' . $document_id . '"' . "\n";
        $incomes_reason = $this->registry['db']->GetOne($sql);

        if ($incomes_reason) {
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';

            $filters = array(
                'where' => array('fir.id = \'' . $incomes_reason . '\'')
            );
            $incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        return $incomes_reason;
    }

    /*
     * Function to distribute additional sum to the related incomes reason
     */
    public function distributeAdditionalSum($document_id, $distribute_sum) {
        $distribute_sum = sprintf('%.2f', round($distribute_sum, 2));
        $incomes_reason = $this->getRelatedIncomesReason($document_id);

        $payments = $incomes_reason->getDirectPayments();
        $result = true;

        if ($payments) {
            $amount_to_distribute = array();
            $payments = array_flip($payments);
            $payment_params['parent_model_name'] = 'Finance_Payment';
            foreach ($payments as $payment_id => $not_imporant) {
                $payment_params['parent_id'] = $payment_id;
                $payments[$payment_id] = $incomes_reason->getPaidAmount($payment_params);
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.history.php';
            $filters = array(
                'where'    => array('fp.id IN (\'' . implode('\',\'', array_keys($payments)) . '\')',
                                    'fp.not_distributed_amount>0'
                                   ),
                'sanitize' => false
            );
            $payments_models = Finance_Payments::search($this->registry, $filters);
            $assoc_payment_models = array();
            foreach ($payments_models as $payment_model) {
                $assoc_payment_models[$payment_model->get('id')] = $payment_model;
            }
            unset($payments_models);

            foreach ($assoc_payment_models as $pm_key => $payment_model) {
                if ($distribute_sum) {
                    if ($distribute_sum > $payment_model->get('not_distributed_amount')) {
                        $amount_to_distribute[$payment_model->get('id')] = $payment_model->get('not_distributed_amount');
                        $distribute_sum = $distribute_sum - $payment_model->get('not_distributed_amount');
                    } else {
                        $amount_to_distribute[$payment_model->get('id')] = $distribute_sum;
                        $distribute_sum = 0;
                    }
                } else {
                    unset($assoc_payment_models[$pm_key]);
                }
            }

            if (!empty($amount_to_distribute)) {
                foreach ($amount_to_distribute as $payment_id => $distribute_sum) {
                    $current_payment = $assoc_payment_models[$payment_id];
                    $old_payment = clone $current_payment;

                    $current_payment->set('relatives_payments', array($incomes_reason->get('id') => $distribute_sum), true);
                    $current_payment->set('selected_tab', 'reason', true);
                    $current_payment->getCustomerDocuments('reason');
                    $current_payment->getRepaymentPlanDocuments('reason');

                    if ($current_payment->updateIncomesBalance()) {
                        Finance_Payments_History::saveData($this->registry,
                                                           array('model' => $current_payment,
                                                                 'action_type' => 'balance',
                                                                 'new_model' => $current_payment,
                                                                 'old_model' => $old_payment));

                        // check if the payment has the undistributed tag and remove it if necessary
                        $current_payment->getTags();
                        $current_payment->getModelTagsForAudit();

                        $filters_pay = array('where' => array('fp.id IN (\'' . $current_payment->get('id') . '\')'));
                        $new_payment = Finance_Payments::searchOne($this->registry, $filters_pay);
                        $new_payment->getTags();

                        if (in_array(PAYMENT_TAG_NOT_DISTRIBUTED_PAYMENT, $new_payment->get('tags')) && !floatval($new_payment->get('not_distributed_amount'))) {
                            $tags = $new_payment->get('tags');
                            foreach ($tags as $k => $tag) {
                                if ($tag == PAYMENT_TAG_NOT_DISTRIBUTED_PAYMENT) {
                                    unset($tags[$k]);
                                    break;
                                }
                            }
                            $new_payment->set('tags', array_values($tags), true);
                            $new_payment->getModelTagsForAudit();
                            $old_payment = clone($new_payment);

                            if ($new_payment->updateTags(array('skip_permissions' => true))) {
                                // get the updated document
                                $tag_payment = Finance_Payments::searchOne($this->registry, $filters_pay);
                                $tag_payment->getModelTagsForAudit();
                                $tag_payment->sanitize();
                                Finance_Payments_History::saveData($this->registry, array('model' => $tag_payment, 'action_type' => 'tag', 'new_model' => $tag_payment, 'old_model' => $old_payment));
                            } else {
                                $result = false;
                            }
                        }
                    } else {
                        $result = false;
                    }
                }
            }
        }

        return $result;
    }

    /*
     * Function to prepare the main report settings
     */
    public function advancedLoanCoverRecalculate($repayment_plan, $advanced_payment_date) {
        // get the model of the repayment plan
        $doc_filters = array('where'      => array('d.id = \'' . $repayment_plan . '\''),
                             'model_lang' => $this->registry['lang']);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $document->getVars();
        $prepayment_percent = floatval($document->getVarValue(DOCUMENT_CONTRACT_PREPAYMENT_FEE_PERCENT));
        $gt2 = $document->getGT2Vars();

        $sums_to_pay = array(
            'principal' => 0,
            'interest'  => 0,
            'penalty'   => 0,
            'warranty'  => 0,
            'tax_ang'   => 0,
            'tax_mng'   => 0,
            'lpg'       => 0,
            'extra'     => 0,
            'total'     => 0
        );

        if (!empty($gt2['values'])) {
            $previous_period_end = '';
            foreach ($gt2['values'] as $row_id => $row_values) {
                if (!$previous_period_end) {
                    $payment_period_start = $document->get('date');
                } else {
                    $payment_period_start = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($previous_period_end)));
                }
                $previous_period_end = $row_values['article_code'];
                $payment_period_end = $row_values['article_code'];

                if ($advanced_payment_date < $payment_period_start) {
                    // the period of the payment is not started yet
                    $sums_to_pay['interest'] += 0;
                    $sums_to_pay['warranty'] += 0;
                    $sums_to_pay['tax_ang'] += 0;
                    $sums_to_pay['tax_mng'] += 0;
                    $sums_to_pay['lpg'] += 0;

                    $gt2['values'][$row_id]['quantity'] = floatval($gt2['values'][$row_id]['article_barcode']);
                    $gt2['values'][$row_id]['article_trademark'] = floatval($gt2['values'][$row_id]['article_height']);
                    $gt2['values'][$row_id]['free_text2'] = floatval($gt2['values'][$row_id]['article_name']);
                    $gt2['values'][$row_id]['free_text1'] = floatval($gt2['values'][$row_id]['article_width']);
                    $gt2['values'][$row_id]['free_text3'] = floatval($gt2['values'][$row_id]['free_text4']);
                } elseif ($advanced_payment_date > $payment_period_end) {
                    // the period of the payment is passed
                    $sums_to_pay['interest'] += floatval($row_values['free_field1']);
                    $sums_to_pay['warranty'] += floatval($row_values['free_field2']);
                    $sums_to_pay['tax_ang'] += floatval($row_values['free_field3']);
                    $sums_to_pay['tax_mng'] += floatval($row_values['article_description']);
                    $sums_to_pay['lpg'] += floatval($row_values['free_text5']);
                } else {
                    // the searched date is within the current payment so apply the formula
                    $sums_to_pay['interest'] += floatval($row_values['free_field1']);
                    $sums_to_pay['warranty'] += floatval($row_values['free_field2']);
                    $sums_to_pay['tax_ang'] += floatval($row_values['free_field3']);
                    $sums_to_pay['tax_mng'] += floatval($row_values['article_description']);
                    $sums_to_pay['lpg'] += floatval($row_values['free_text5']);
                }

                $sums_to_pay['principal'] += floatval($row_values['average_weighted_delivery_price']);
                $sums_to_pay['penalty'] += floatval($row_values['free_field4']);

                $gt2['values'][$row_id]['quantity'] = sprintf('%.2f', $gt2['values'][$row_id]['quantity']);
                $gt2['values'][$row_id]['article_trademark'] = sprintf('%.2f', $gt2['values'][$row_id]['article_trademark']);
                $gt2['values'][$row_id]['free_text2'] = sprintf('%.2f', $gt2['values'][$row_id]['free_text2']);
                $gt2['values'][$row_id]['free_text1'] = sprintf('%.2f', $gt2['values'][$row_id]['free_text1']);
                $gt2['values'][$row_id]['free_text3'] = sprintf('%.2f', $gt2['values'][$row_id]['free_text3']);
                $gt2['values'][$row_id]['article_delivery_code'] = sprintf('%.2f', $gt2['values'][$row_id]['article_delivery_code']);
                $gt2['values'][$row_id]['article_second_code'] = sprintf('%.2f', ($gt2['values'][$row_id]['price'] +
                                                                                  $gt2['values'][$row_id]['quantity'] +
                                                                                  $gt2['values'][$row_id]['article_trademark'] +
                                                                                  $gt2['values'][$row_id]['free_text2'] +
                                                                                  $gt2['values'][$row_id]['free_text1'] +
                                                                                  $gt2['values'][$row_id]['free_text3'] +
                                                                                  $gt2['values'][$row_id]['article_delivery_code']));

                $gt2['values'][$row_id]['average_weighted_delivery_price'] = sprintf('%.2f', ($gt2['values'][$row_id]['price'] - floatval($gt2['values'][$row_id]['article_deliverer_name'])));
                $gt2['values'][$row_id]['free_field1'] = sprintf('%.2f', ($gt2['values'][$row_id]['quantity'] - floatval($gt2['values'][$row_id]['article_barcode'])));
                $gt2['values'][$row_id]['free_field2'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_trademark'] - floatval($gt2['values'][$row_id]['article_height'])));
                $gt2['values'][$row_id]['article_description'] = sprintf('%.2f', ($gt2['values'][$row_id]['free_text2'] - floatval($gt2['values'][$row_id]['article_name'])));
                $gt2['values'][$row_id]['free_field3'] = sprintf('%.2f', ($gt2['values'][$row_id]['free_text1'] - floatval($gt2['values'][$row_id]['article_width'])));
                $gt2['values'][$row_id]['free_text5'] = sprintf('%.2f', ($gt2['values'][$row_id]['free_text3'] - floatval($gt2['values'][$row_id]['free_text4'])));
                $gt2['values'][$row_id]['free_field4'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_delivery_code'] - floatval($gt2['values'][$row_id]['article_weight'])));
                $gt2['values'][$row_id]['free_field5'] = sprintf('%.2f', ($gt2['values'][$row_id]['article_second_code'] - floatval($gt2['values'][$row_id]['article_volume'])));
            }
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $sums_to_pay['total'] = $sums_to_pay['interest'] + $sums_to_pay['warranty'] + $sums_to_pay['principal'] + $sums_to_pay['penalty'] + $sums_to_pay['tax_ang'] + $sums_to_pay['tax_mng'] + $sums_to_pay['lpg'];
        $sums_to_pay['extra'] = ($prepayment_percent ? (($sums_to_pay['principal'] * $prepayment_percent)/100) : 0);
        $sums_to_pay['total'] = $sums_to_pay['total'] + $sums_to_pay['extra'];

        return array('gt2' => $gt2, 'sums_to_pay' => $sums_to_pay);
    }

    /*
     * Function to prepare the main report settings
     */
    public function recalculateGroupingTable($document, $gt2, $action) {
        $result = true;

        $assoc_vars = $document->getAssocVars();

        // delete the existing grouping table
        $group_table_vars = array(SCHEDULE_VAR_GT2_ID, SCHEDULE_VAR_GT2_ROW, SCHEDULE_VAR_PAYMENT_ID, SCHEDULE_VAR_PAYMENT_NUM,
                                  SCHEDULE_VAR_DISTRIBUTE_DATE, SCHEDULE_VAR_PAYMENT_DATE, SCHEDULE_VAR_PRINCIPAL, SCHEDULE_VAR_WARRANTY,
                                  SCHEDULE_VAR_INTEREST, SCHEDULE_VAR_PENALTY, SCHEDULE_VAR_LPG, SCHEDULE_VAR_ORIGINAL_CURRENCY,
                                  SCHEDULE_VAR_EXCHANGE_RATE, SCHEDULE_VAR_TAX, SCHEDULE_VAR_TAX_ANG);
        $group_table_vars = array_fill_keys($group_table_vars, '');

        foreach ($group_table_vars as $key_var => $gtv) {
            $group_table_vars[$key_var] = $assoc_vars[$key_var]['id'];
        }

        $schedule_payments = $this->getPaymentsAmounts($document->get('id'), INCOMES_REASON_TYPE_ID);

        usort($schedule_payments, function($a, $b) {
            if ($a['issue_date'] > $b['issue_date']) {
                return 1;
            } elseif ($a['issue_date'] < $b['issue_date']) {
                return -1;
            } else {
                return $a['added'] > $b['added'] ? 1 : -1;
            }
        });

        $this->registry['db']->StartTrans();
        $delete_sql = 'DELETE FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' WHERE `model_id`="' . $document->get('id') . '" AND `var_id` IN ("' . implode('","', $group_table_vars) . '")' . "\n";
        $this->registry['db']->Execute($delete_sql);

        $order_completion = array();
        if ($action == 'edit') {
            $order_completion = array(
                'tax_ang' => array(
                    'paid' => 'article_width',
                    'left' => 'free_field3'
                ),
                'tax' => array(
                    'paid' => 'article_name',
                    'left' => 'article_description',
                ),
                'warranty' => array(
                    'paid' => 'article_height',
                    'left' => 'free_field2',
                ),
                'interest' => array(
                    'paid' => 'article_barcode',
                    'left' => 'free_field1',
                ),
                'principal' => array(
                    'paid' => 'article_deliverer_name',
                    'left' => 'average_weighted_delivery_price',
                ),
                'lpg' => array(
                    'paid' => 'free_text4',
                    'left' => 'free_text5',
                ),
                'penalty' => array(
                    'paid' => 'article_weight',
                    'left' => 'free_field4',
                )
            );
        } else {
            $order_completion = array(
                'penalty' => array(
                    'paid' => 'article_weight',
                    'left' => 'free_field4',
                ),
                'interest' => array(
                    'paid' => 'article_barcode',
                    'left' => 'free_field1',
                ),
                'warranty' => array(
                    'paid' => 'article_height',
                    'left' => 'free_field2',
                )
            );
        }

        $current_payments = array();

        if ($action == 'edit') {
            $left_paid_sums = array();
            foreach ($schedule_payments as $payment_id => $payment_info) {
                $amount_left = $payment_info['reason_paid_amount'];
                $row_num = 0;
                foreach ($gt2['values'] as $key => $vals) {
                    if (!isset($left_paid_sums[$key])) {
                        $left_paid_sums[$key] = array(
                            'principal' => $vals['article_deliverer_name'],
                            'interest'  => $vals['article_barcode'],
                            'warranty'  => $vals['article_height'],
                            'tax_ang'   => $vals['article_width'],
                            'tax'       => $vals['article_name'],
                            'lpg'       => $vals['free_text4'],
                            'penalty'   => $vals['article_weight']
                        );
                    }

                    if (!$amount_left) {
                        break;
                    }
                    $row_num++;

                    if (!floatval($left_paid_sums[$key]['principal']) && !floatval($left_paid_sums[$key]['interest']) &&
                        !floatval($left_paid_sums[$key]['warranty']) && !floatval($left_paid_sums[$key]['tax_ang']) &&
                        !floatval($left_paid_sums[$key]['tax']) &&
                        !floatval($left_paid_sums[$key]['penalty']) && !floatval($left_paid_sums[$key]['lpg'])
                    ) {
                        continue;
                    }

                    $current_payments_data = array(
                        'gt2_id'          => '',
                        'gt2_row'         => '',
                        'payment_id'      => '',
                        'payment_num'     => '',
                        'payment_date'    => '',
                        'distribute_date' => '',
                        'interest'        => 0,
                        'warranty'        => 0,
                        'principal'       => 0,
                        'tax'             => 0,
                        'tax_ang'         => 0,
                        'lpg'             => 0,
                        'penalty'         => 0,
                        'currency'        => '',
                        'exchange_rate'   => '',
                    );

                    foreach ($order_completion as $cover => $cover_var) {
                        if ($left_paid_sums[$key][$cover] > 0 && $amount_left) {
                            if ($left_paid_sums[$key][$cover] > $amount_left) {
                                $current_payments_data[$cover] = $amount_left;
                                $left_paid_sums[$key][$cover] = sprintf('%.2f', floatval($left_paid_sums[$key][$cover] - $amount_left));
                                $amount_left = 0;
                            } else {
                                $current_payments_data[$cover] = $left_paid_sums[$key][$cover];
                                $amount_left = $amount_left - $left_paid_sums[$key][$cover];
                                $left_paid_sums[$key][$cover] = 0;
                            }
                        }
                    }

                    // complete the data for the payment row
                    $current_payments_data['gt2_id'] = $key;
                    $current_payments_data['gt2_row'] = $row_num;
                    $current_payments_data['payment_id'] = $payment_info['id'];
                    $current_payments_data['payment_num'] = $payment_info['num'];
                    $current_payments_data['distribute_date'] = $payment_info['status_modified'];
                    $current_payments_data['payment_date'] = $payment_info['issue_date'];
                    $current_payments_data['currency'] = $payment_info['currency'];
                    $current_payments_data['exchange_rate'] = $payment_info['rate'];

                    $current_payments[] = $current_payments_data;
                }
            }
        }

        if (!empty($current_payments)) {
            $row_num = 1;
            $insert = array();
            foreach ($current_payments as $cp) {
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_GT2_ID]['id'], $row_num, $cp['gt2_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_GT2_ROW]['id'], $row_num, $cp['gt2_row'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_ID]['id'], $row_num, $cp['payment_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_NUM]['id'], $row_num, $cp['payment_num'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_DISTRIBUTE_DATE]['id'], $row_num, $cp['distribute_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PAYMENT_DATE]['id'], $row_num, $cp['payment_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PRINCIPAL]['id'], $row_num, sprintf('%.2f', $cp['principal']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_WARRANTY]['id'], $row_num, sprintf('%.2f', $cp['warranty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_INTEREST]['id'], $row_num, sprintf('%.2f', $cp['interest']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_PENALTY]['id'], $row_num, sprintf('%.2f', $cp['penalty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_TAX]['id'], $row_num, sprintf('%.2f', $cp['tax']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_TAX_ANG]['id'], $row_num, sprintf('%.2f', $cp['tax_ang']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_LPG]['id'], $row_num, sprintf('%.2f', $cp['lpg']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_ORIGINAL_CURRENCY]['id'], $row_num, $cp['currency'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $document->get('id'), $assoc_vars[SCHEDULE_VAR_EXCHANGE_RATE]['id'], $row_num, sprintf('%.6f', $cp['exchange_rate']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $row_num++;
            }

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $insert) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /*
     * Function to load the panel with sms list
     */
    public function _loadSMSPanel() {
        $this->prepareReportSettings();

        $customer_id = $this->registry['request']->get('customer_id');
        if ($this->registry['request']->get('page')) {
            $page = $this->registry['request']->get('page');
            $include_outer_container = false;
        } else {
            $page = 1;
            $include_outer_container = true;
        }

        // get the data which have to refreshed
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';

        // prepare the table which have to be reloaded
        list($sms_list, $pagination) = Credilink_Customer_File::getSMSList($this->registry, $customer_id, false, $page);

        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_sms_panel.html';
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->data['sms_list'] = $sms_list;
        $viewer->data['include_outer_container'] = $include_outer_container;
        $viewer->data['pagination'] = $pagination;
        $viewer->data['report_type'] = $this->report;
        $viewer->data['customer_id'] = $customer_id;
        $template = $viewer->fetch();

        if ($include_outer_container) {
            $operation_result['content'] = $template;
            $operation_result['title'] = $this->i18n('reports_sent_sms');
            print json_encode($operation_result);
        } else {
            print $template;
        }

        exit;
    }

    /*
     * Function to get the payments for the contract in the currency of the contract
     */
    public function getPaymentsAmounts($document_id, $incomes_reason_type) {
        $sql = 'SELECT frr.parent_id' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=frr.parent_id AND fir.type="' . $incomes_reason_type . '")' . "\n" .
               'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND frr.link_to="' . $document_id . '"' . "\n";
        $incomes_reasons = $this->registry['db']->GetCol($sql);

        $sql = 'SELECT fp.id' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fb' . "\n" .
               '  ON (fb.parent_model_name="Finance_Payment" AND fb.paid_to_model_name="Finance_Incomes_Reason" AND fb.paid_to=fir.id AND fb.paid_amount>0)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_PAYMENTS . ' as fp' . "\n" .
               '  ON (fp.type IN ("BP", "PKO") AND fp.id=fb.parent_id)' . "\n" .
               'WHERE fir.id IN ("' . implode('","', $incomes_reasons) . '")' . "\n";
        $payments = $this->registry['db']->GetCol($sql);

        // get the required additional vars from the document
        $add_vars = array(SCHEDULE_VAR_PAYMENT_ID, SCHEDULE_VAR_ORIGINAL_CURRENCY, SCHEDULE_VAR_PRINCIPAL,
                          SCHEDULE_VAR_WARRANTY, SCHEDULE_VAR_INTEREST, SCHEDULE_VAR_PENALTY, SCHEDULE_VAR_LPG,
                          SCHEDULE_VAR_TAX, SCHEDULE_VAR_TAX_ANG, SCHEDULE_VAR_EXCHANGE_RATE, SCHEDULE_VAR_CREDIT_CURRENCY);
        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
               'WHERE `model`="Document" AND `model_type`="' . DOCUMENT_TYPE_ID . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
        $add_vars = $this->registry['db']->GetAssoc($sql);

        // get the payments amounts
        $sql = 'SELECT fp.id as idx, fp.*, fir.currency as reason_currency, d_cstm_rate.value as rate, fb.paid_amount, d_cstm_currency.value as contract_currency, SUM(d_cstm_princ.value + d_cstm_war.value + d_cstm_intr.value + d_cstm_pen.value + d_cstm_lpg.value + d_cstm_tax.value + d_cstm_ang.value) as contract_paid_amount, 0 as contract_amount, 0 as reason_paid_amount' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_PAYMENTS . ' AS fp' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_BALANCE . ' as fb' . "\n" .
               '  ON (fb.parent_model_name="Finance_Payment" AND fb.parent_id=fp.id AND fb.paid_to_model_name="Finance_Incomes_Reason" AND fb.paid_to IN ("' . implode('","', $incomes_reasons) . '") AND fb.paid_amount>0)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=fb.paid_to AND fir.type="' . $incomes_reason_type . '" AND fir.currency=fp.currency)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
               '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND frr.parent_id=fir.id)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_currency' . "\n" .
               '  ON (d_cstm_currency.model_id=frr.link_to AND d_cstm_currency.var_id="' . $add_vars[SCHEDULE_VAR_CREDIT_CURRENCY] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_payment' . "\n" .
               '  ON (d_cstm_payment.model_id=frr.link_to AND d_cstm_payment.var_id="' . $add_vars[SCHEDULE_VAR_PAYMENT_ID] . '" AND d_cstm_payment.value=fp.id)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_princ' . "\n" .
               '  ON (d_cstm_princ.model_id=d_cstm_payment.model_id AND d_cstm_princ.var_id="' . $add_vars[SCHEDULE_VAR_PRINCIPAL] . '" AND d_cstm_princ.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_war' . "\n" .
               '  ON (d_cstm_war.model_id=d_cstm_payment.model_id AND d_cstm_war.var_id="' . $add_vars[SCHEDULE_VAR_WARRANTY] . '" AND d_cstm_war.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_intr' . "\n" .
               '  ON (d_cstm_intr.model_id=d_cstm_payment.model_id AND d_cstm_intr.var_id="' . $add_vars[SCHEDULE_VAR_INTEREST] . '" AND d_cstm_intr.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pen' . "\n" .
               '  ON (d_cstm_pen.model_id=d_cstm_payment.model_id AND d_cstm_pen.var_id="' . $add_vars[SCHEDULE_VAR_PENALTY] . '" AND d_cstm_pen.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_lpg' . "\n" .
               '  ON (d_cstm_lpg.model_id=d_cstm_payment.model_id AND d_cstm_lpg.var_id="' . $add_vars[SCHEDULE_VAR_LPG] . '" AND d_cstm_lpg.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tax' . "\n" .
               '  ON (d_cstm_tax.model_id=d_cstm_payment.model_id AND d_cstm_tax.var_id="' . $add_vars[SCHEDULE_VAR_TAX] . '" AND d_cstm_tax.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ang' . "\n" .
               '  ON (d_cstm_ang.model_id=d_cstm_payment.model_id AND d_cstm_ang.var_id="' . $add_vars[SCHEDULE_VAR_TAX_ANG] . '" AND d_cstm_ang.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rate' . "\n" .
               '  ON (d_cstm_rate.model_id=d_cstm_payment.model_id AND d_cstm_rate.var_id="' . $add_vars[SCHEDULE_VAR_EXCHANGE_RATE] . '" AND d_cstm_rate.num=d_cstm_payment.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_org_curr' . "\n" .
               '  ON (d_cstm_org_curr.model_id=d_cstm_payment.model_id AND d_cstm_org_curr.var_id="' . $add_vars[SCHEDULE_VAR_ORIGINAL_CURRENCY] . '" AND d_cstm_org_curr.num=d_cstm_payment.num)' . "\n" .
               'WHERE fp.id IN ("' . implode('","', $payments) . '")' . "\n" .
               'GROUP BY fp.id' . "\n";
        $current_payments = $this->registry['db']->GetAll($sql);

        foreach ($current_payments as $k => $cp) {
            if (empty($cp['rate'])) {
                // no existing conversion rate - take the current rate
                $current_payments[$k]['rate'] = Finance_Currencies::getRate($this->registry, $cp['reason_currency'], $cp['contract_currency'], $cp['issue_date']);
            }

            $current_payments[$k]['contract_amount'] = round($current_payments[$k]['rate'] * $cp['amount'], 2);
            $current_payments[$k]['reason_paid_amount'] = round($current_payments[$k]['rate'] * $cp['paid_amount'], 2);
            $current_payments[$k]['contract_paid_amount'] = round(floatval($cp['contract_paid_amount']), 2);
        }

        return $current_payments;
    }

    /**
     * Function to get the payments for the contract in the currency of the contract
     *
     * return array
     */
    public function calculateOwedToDate($document_id, $date=''): array
    {
        if (empty($date)) {
            $date = date('Y-m-d');
        }

        // get the document
        $doc_filters = array('where'    => array('d.id = ' . $document_id),
                             'sanitize' => false);
        $document = Documents::searchOne($this->registry, $doc_filters);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $current_gt2_var = $document->getGT2Vars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        // calculate the owed dues before the current period
        $owed_dues = array();
        $current_owed = array();
        $target_payment_reached  =false;
        $prev_date = '';

        foreach ($current_gt2_var['values'] as $row) {
            $current_owed_dues = array();
            $prev_row_data = array(
                'owed_principal' => 0,
                'owed_overdue_principal' => 0,
                'owed_total_principal' => 0,
                'owed_interest'  => 0,
                'owed_insurance' => 0,
                'owed_tax1'      => 0,
                'owed_tax2'      => 0,
                'owed_lpg'       => 0,
                'owed_penalty'   => 0,
                'owed_total'     => 0,
            );
            if ($prev_date) {
                $prev_row_data = $owed_dues[$prev_date];
            }
            $current_owed_dues['owed_principal'] = $prev_row_data['owed_principal'] + floatval($row['average_weighted_delivery_price']);
            $current_owed_dues['owed_overdue_principal'] = $prev_row_data['owed_principal'];
            $current_owed_dues['owed_interest'] = $prev_row_data['owed_interest'] + floatval($row['free_field1']);
            $current_owed_dues['owed_insurance'] = $prev_row_data['owed_insurance'] + floatval($row['free_field2']);
            $current_owed_dues['owed_tax1'] = $prev_row_data['owed_tax1'] + floatval($row['article_description']);
            $current_owed_dues['owed_tax2'] = $prev_row_data['owed_tax2'] + floatval($row['free_field3']);
            $current_owed_dues['owed_lpg'] = $prev_row_data['owed_lpg'] + floatval($row['free_text5']);
            $current_owed_dues['owed_penalty'] = $prev_row_data['owed_penalty'] + floatval($row['free_field4']);
            $current_owed_dues['owed_total'] = array_sum($current_owed_dues) - $current_owed_dues['owed_principal'];
            $current_owed_dues['owed_total_principal'] = floatval($row['last_delivery_price']) + floatval($row['price']);

            $owed_dues[$row['article_code']] = $current_owed_dues;

            $prev_date = $row['article_code'];
            if ($target_payment_reached) {
                continue;
            }
            if ($row['article_code']>=$date) {
                $target_payment_reached = true;
            }
            $current_owed = $current_owed_dues;
        }

        return array('owed_to_current_date' => $current_owed, 'owed_dues_to_date' => $owed_dues);
    }

    /**
     * Function to create the version of the payment plan based on the completed in the middle screen
     *
     * @return void
     */
    private function _createPaymentPlanVersion(): void
    {
        $errors = array();
        $this->prepareReportSettings();
        $request = $this->registry['request'];
        // get the document
        $doc_filters = array('where'    => array('d.id = ' . $request->get('repayment_plan_id')),
                             'sanitize' => false);
        $payment_plan = Documents::searchOne($this->registry, $doc_filters);
        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $version_pp = clone $payment_plan;
        $version_pp->set('type', VERSION_REPAYMENT_PLAN_TYPE_ID, true);
        $version_pp->set('date', date('Y-m-d'), true);
        $version_pp->unsetProperty('id', true);
        $version_pp_assoc_vars = $version_pp->getAssocVars();

        $assoc_vars = $payment_plan->getAssocVars();
        $gt2 = $payment_plan->getGT2Vars();

        foreach ($assoc_vars as $var_nm => $var_data) {
            if (isset($var_data['value']) && isset($version_pp_assoc_vars[$var_nm])) {
                $version_pp_assoc_vars[$var_nm]['value'] = $var_data['value'];
            }
        }
        $settings_request_relation_vars = array(
            PREPAID_CALCULATED_OVERDUE_PRINCIPAL => 'repayment_plan_owed_overdue_principal',
            PREPAID_COMPLETED_OVERDUE_PRINCIPAL => 'advance_pay_overdue_principal',
            PREPAID_PRINCIPAL_DATE => 'advance_pay_date',
            PREPAID_PRINCIPAL_SUM => 'advance_pay_sum',
            PREPAID_CALCULATED_INTEREST => 'repayment_plan_owed_interest',
            PREPAID_COMPLETED_INTEREST => 'advance_pay_interest',
            PREPAID_CALCULATED_INSURANCE => 'repayment_plan_owed_insurance',
            PREPAID_COMPLETED_INSURANCE => 'advance_pay_insurance',
            PREPAID_CALCULATED_MANAGEMENT => 'repayment_plan_owed_tax1',
            PREPAID_COMPLETED_MANAGEMENT => 'advance_pay_tax1',
            PREPAID_CALCULATED_COMMITMENT => 'repayment_plan_owed_tax2',
            PREPAID_COMPLETED_COMMITMENT => 'advance_pay_tax2',
            PREPAID_CALCULATED_PENALTY => 'repayment_plan_owed_penalty',
            PREPAID_COMPLETED_PENALTY => 'advance_pay_penalty',
            PREPAID_CALCULATED_LPG => 'repayment_plan_owed_lpg',
            PREPAID_COMPLETED_LPG => 'advance_pay_lpg',
            PREPAID_CALCULATED_TOTAL => 'repayment_plan_owed_total',
            PREPAID_COMPLETED_TOTAL => 'advance_pay_total',
            PREPAID_CALCULATED_PRINCIPAL => 'repayment_plan_owed_principal',
            PREPAID_COMPLETED_PRINCIPAL => 'advance_pay_principal'
        );

        foreach ($settings_request_relation_vars as $var_name => $req_var) {
            if (!isset($version_pp_assoc_vars[$var_name]) || !$request->isRequested($req_var)) {
                continue;
            }
            $version_pp_assoc_vars[$var_name]['value'] = $request->get($req_var);
        }


        /**
         * Recalculate the payment plan
         */
        require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
        $automations_controller = new Credits_Automations_Controller($this->registry);
        $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');

        // load automations i18n files
        $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
            false, '', '', true);
        if (!empty($i18n_files)) {
            $this->registry['translater']->loadFile($i18n_files);
        }
        $adapter = $automations_controller->getAdapter();
        $adapter->current_model = clone $payment_plan;
        $adapter->current_old_model = clone $payment_plan;
        $adapter->current_model_vars = $payment_plan->getAssocVars();
        $adapter->calculate_from_date = $request->get('advance_pay_date');
        $adapter->prepaid_principal = $request->get('advance_pay_principal');
        $adapter->reason_for_change = 'prepaid_principal';
        $recalculated_gt2 = $adapter->buildInvestmentCreditTable($gt2);
        $recalculated_gt2['rows'] = array();

        foreach ($version_pp_assoc_vars as $var_nm => $var_data) {
            if ($var_data['type'] == 'gt2') {
                $version_pp_assoc_vars[$var_nm] = $recalculated_gt2;
            }
        }

        $request->set('gt2_requested', 1, 'post', true);
        $version_pp->set('vars', array_values($version_pp_assoc_vars), true);
        $version_pp->set('assoc_vars', $version_pp_assoc_vars, true);
        $version_pp->set('grouping_table_2', $recalculated_gt2, true);
        $version_pp->calculateGT2();
        $version_pp->set('table_values_are_set', true, true);

        $this->registry['db']->StartTrans();
        if ($version_pp->save()) {
            $old_model = new Document($this->registry, ['type' => $version_pp->get('type')]);
            $old_model->getVars();

            // write history
            $doc_filters = array('where'      => array('d.id="' . $version_pp->get('id') . '"'),
                                 'model_lang' => $this->registry['lang']
            );
            $new_model = Documents::searchOne($this->registry, $doc_filters);
            $new_model->set('origin_id', $payment_plan->get('id'), true);
            $new_model->set('origin_model', 'Document', true);
            $new_model->set('clone_transform', 'transformed', true);
            $new_model->insertRelatives();
            $new_model->getVars();

            $history_params = array(
                'model'       => $new_model,
                'action_type' => 'add',
                'new_model'   => $new_model,
                'old_model'   => $old_model
            );
            Documents_History::saveData($this->registry, $history_params);

            // prepare tags if such are set
            $new_model->getTags();
            $tags = $new_model->get('tags');
            $tags = array_unique(array_merge($tags, array(VERSION_REPAYMENT_PLAN_TAG)));
            $new_model->getModelTagsForAudit();

            $old_model = clone($new_model);
            $new_model->set('tags', array_values($tags), true);
            if ($new_model->updateTags(array('skip_permissions' => true))) {
                // get the updated payment
                $filters = array('where'      => array('d.id="' . $new_model->get('id') . '"'),
                                 'model_lang' => $this->registry['lang']);
                $new_model = Documents::searchOne($this->registry, $filters);
                $new_model->getModelTagsForAudit();
                $new_model->sanitize();

                Documents_History::saveData($this->registry, array('model' => $new_model, 'action_type' => 'tag', 'new_model' => $new_model, 'old_model' => $old_model));
            }
        } else {
            $errors[] = $this->i18n('error_reports_repayment_plan_version_save_failed');
            $this->registry['db']->FailTrans();
        }
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);
        $request->remove('gt2_requested');

        $operation_result = array(
            'result'   => empty($errors),
            'messages' => empty($errors) ? array($this->i18n('message_reports_repayment_plan_version_created_successfully')) : $errors
        );

        print json_encode($operation_result);
        exit;
    }

    /**
     * Function to get the existing active versions of a payment plan
     */
    private function _getPaymentPlanVersions(): void
    {
        $this->prepareReportSettings();
        $versions = $this->getActivePaymentPlanVersions($this->registry['request']->get('payment_plan'));
        $versions_data = array();

        if (!empty($versions)) {
            $filters = array(
                'where' => array(
                    'd.id IN (' . implode(',', $versions) . ')'
                ),
                'model_lang' => $this->registry['lang']
            );
            $versions_models = Documents::search($this->registry, $filters);

            $get_old_vars = $this->registry->get('get_old_vars');
            $this->registry->set('get_old_vars', true, true);

            foreach ($versions_models as $versions_model) {
                $versions_model->unsanitize();
                $assoc_vars = $versions_model->getAssocVars();
                $versions_data[] = array(
                    'id'        => $versions_model->get('id'),
                    'date'      => $assoc_vars[PREPAID_PRINCIPAL_DATE]['value'],
                    'paid_sum'  => $assoc_vars[PREPAID_PRINCIPAL_SUM]['value'],
                    'principal' => $assoc_vars[PREPAID_COMPLETED_PRINCIPAL]['value'],
                );
            }
            $this->registry->set('get_old_vars', $get_old_vars, true);
        }

        // prepare viewer for the user to select the required container
        // build the viewer
        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);

        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_advance_principal_cover_versions.html';

        $viewer->data['repayment_plan_id'] = $this->registry['request']->get('payment_plan');
        $viewer->data['version_plans'] = $versions_data;

        $operation_result = array(
            'content' => $viewer->fetch(),
            'title'   => $this->i18n('reports_repayment_plan_choose_version')
        );

        print json_encode($operation_result);
        exit;
    }

    /**
     * Function to get the active payment plan versions
     *
     * @return array
     */
    private function getActivePaymentPlanVersions($payment_plan): array
    {
        $this->prepareReportSettings();
        $sql = 'SELECT d.id' . "\n" .
                'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                'INNER JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
                ' ON (d.type="' . VERSION_REPAYMENT_PLAN_TYPE_ID . '" AND d.active=1 AND d.deleted_by = 0 AND dr.link_to="' . $payment_plan . '" AND
                      dr.link_to_model_name="Document" AND dr.parent_model_name="Document" AND dr.parent_id=d.id)' . "\n" .
                'INNER JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tt' . "\n" .
                '  ON tt.model="Document" AND tt.model_id=d.id AND tt.tag_id="' . VERSION_REPAYMENT_PLAN_TAG . '"' . "\n";
        $versions = $this->registry['db']->GetCol($sql);
        return $versions;
    }

    /**
     * Function to apply the payment plan version to the original document
     */
    private function _applyPaymentPlanVersion(): void
    {
        $this->prepareReportSettings();
        $request = $this->registry['request'];

        // get the payment plan and the version to apply
        $get_old_vars = $this->registry->get('get_old_vars');
        $edit_all = $this->registry->get('edit_all');
        $this->registry->set('get_old_vars', true, true);
        $this->registry->set('edit_all', true, true);

        $doc_filters = array('where'    => array('d.id = ' . $request->get('payment_plan')),
                             'sanitize' => false);
        $payment_plan = Documents::searchOne($this->registry, $doc_filters);
        $payment_plan->getVars();
        $payment_plan_vars = $payment_plan->getAssocVars();
        $payment_plan_gt2 = $payment_plan->getGT2Vars();
        $old_payment_plan = clone $payment_plan;

        $doc_filters['where'] = array('d.id = ' . $request->get('version'));
        $version = Documents::searchOne($this->registry, $doc_filters);
        $version->getVars();
        $assoc_vars = $version->getAssocVars();
        $version_gt2 = $version->getGT2Vars();

        // get the needed dates
        $date_match = $assoc_vars[PREPAID_PRINCIPAL_DATE]['value'];
        $dates_included = array_combine(array_keys($version_gt2['values']), array_column($version_gt2['values'], 'article_code'));
        foreach ($dates_included as $k => $dat_inc) {
            if ($dat_inc < $date_match) {
                unset($dates_included[$k]);
            }
        }

        $new_rows = array();
        foreach ($payment_plan_gt2['values'] as $pp_row_id => $pp_row) {
            if (!in_array($pp_row['article_code'], $dates_included)) {
                continue;
            }
            $v_row_id = array_search($pp_row['article_code'], $dates_included);
            if (!isset($version_gt2['values'][$v_row_id])) {
                continue;
            }
            $payment_plan_gt2['values'][$pp_row_id]['deleted'] = 1;
            $new_rows[] = $version_gt2['values'][$v_row_id];
        }
        $payment_plan_gt2['values'] += $new_rows;

        $payment_plan->set('grouping_table_2', $payment_plan_gt2, true);
        $payment_plan->calculateGT2();
        $payment_plan->set('table_values_are_set', true, true);

        $messages_list = array();
        $this->registry['db']->StartTrans();
        if ($payment_plan->saveGT2Vars()) {
            // get the credits controller because we will need it later
            require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
            $automations_controller = new Credits_Automations_Controller($this->registry);
            $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');

            // load automations i18n files
            $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
                false, '', '', true);
            if (!empty($i18n_files)) {
                $this->registry['translater']->loadFile($i18n_files);
            }
            $adapter = $automations_controller->getAdapter();
            $adapter->current_model = clone $payment_plan;
            $adapter->current_old_model = clone $old_payment_plan;
            $adapter->current_model_vars = $payment_plan_vars;
            $adapter->calculate_from_date = $this->registry['request']->get('principal_repayment_date');
            $adapter->reason_for_change = 'prepaid_principal';

            $change_sum = $adapter->calculateUserCreditNewTotal() - $adapter->calculateUserCreditOldTotal();
            $incomes_reason_id = $adapter->checkExistingFinIncomesReason($payment_plan->get('id'),
                $adapter->current_model_vars[$automations_controller->settings['repayment_plan_currency']]['value'] ?: "",
                $automations_controller->settings['incomes_reason_type_id'],
                $payment_plan->get('customer'));

            // change the existing incomes reason
            if (!$adapter->issueFinIncomeReasonCorrection($incomes_reason_id, $change_sum,
                $automations_controller->settings['nom_contract_obligations_id'])) {
                $messages_list[] = $this->i18n('error_reports_repayment_plan_editing_the_obligations_failed');
                $this->registry['db']->FailTrans();
            }

            // write history for the payment plan
            // write history
            $doc_filters = array(
                'where'      => array('d.id="' . $payment_plan->get('id') . '"'),
                'model_lang' => $this->registry['lang']
            );
            $new_payment_plan = Documents::searchOne($this->registry, $doc_filters);
            $new_payment_plan->getVars();

            $history_params = array(
                'model'       => $new_payment_plan,
                'action_type' => 'edit',
                'new_model'   => $new_payment_plan,
                'old_model'   => $old_payment_plan
            );
            Documents_History::saveData($this->registry, $history_params);

            // remove the tags of all the versions that are currently active
            $versions = $this->getActivePaymentPlanVersions($new_payment_plan->get('id'));

            if (Documents::changeStatus($this->registry, $versions, 'deactivate')) {
                $filters = array('where' => array('d.id IN (\'' . implode("','", $versions) . '\')'),
                                 'model_lang' => $this->registry['lang']);
                $versions_list = Documents::search($this->registry, $filters);
                foreach ($versions_list as $version_temp) {
                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model'       => $version_temp,
                            'action_type' => 'deactivate'
                        )
                    );
                }
            } else {
                $messages_list[] = $this->i18n('error_reports_repayment_plan_versions_deactivate_failed');
                $this->registry['db']->FailTrans();
            }
        } else {
            $messages_list[] = $this->i18n('error_reports_repayment_plan_saving_gt2_failed');
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        $this->registry->set('get_old_vars', $get_old_vars, true);
        $this->registry->set('edit_all', $edit_all, true);

        if ($result) {
            $messages_list[] = $this->i18n('message_reports_repayment_plan_version_applied_successfully');
        }

        $operation_result = array(
            'result'   => $result,
            'messages' => $messages_list
        );

        print json_encode($operation_result);
        exit;
    }

    /**
     * Function to get the data for the panels and prepare them for loading in the interface
     *
     * @return void
     */
    private function _reloadReportPanels(): void
    {
        $this->prepareReportSettings();
        require_once PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/custom.report.query.php';
        $operation_result = [];

        // prepare the table which have to be reloaded
        $customer_id = $this->registry['request']->get('customer');
        $repayment_plans = Credilink_Customer_File::getRepaymentPlans($this->registry,
                                                                      array('customer' => $customer_id));
        $credits_list = Credilink_Customer_File::getCredits($this->registry, $customer_id);
        $all_credits_list = Credilink_Customer_File::getAllClientCredits($this->registry, $customer_id);
        $payment_sums = Credilink_Customer_File::getPaymentsData($this->registry, array_keys($repayment_plans));
        $taxes_list = Credilink_Customer_File::getTaxesData($this->registry, array_keys($credits_list));

        $viewer = new Viewer($this->registry);
        $viewer->loadCustomI18NFiles($this->report_lang_file);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $viewer->template = '_repayment_plans.html';
        $viewer->data['repayment_plans'] = $repayment_plans;
        $viewer->data['payment_sums'] = $payment_sums;
        $operation_result['template_repayment_plans'] = $viewer->fetch();

        $crd_viewer = new Viewer($this->registry);
        $crd_viewer->loadCustomI18NFiles($this->report_lang_file);
        $crd_viewer->setFrameset('frameset_blank.html');
        $crd_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $crd_viewer->template = '_credits_list.html';
        $crd_viewer->data['credits_list'] = $credits_list;
        $operation_result['template_credits_list'] = $crd_viewer->fetch();

        $crdall_viewer = new Viewer($this->registry);
        $crdall_viewer->loadCustomI18NFiles($this->report_lang_file);
        $crdall_viewer->setFrameset('frameset_blank.html');
        $crdall_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $crdall_viewer->template = '_customers_contract.html';
        $crdall_viewer->data['customers_contract'] = $all_credits_list;
        $operation_result['template_all_credits_list'] = $crdall_viewer->fetch();

        $taxes_viewer = new Viewer($this->registry);
        $taxes_viewer->loadCustomI18NFiles($this->report_lang_file);
        $taxes_viewer->setFrameset('frameset_blank.html');
        $taxes_viewer->templatesDir = PH_MODULES_DIR . 'reports/plugins/' . $this->report . '/';
        $taxes_viewer->template = '_taxes_list.html';
        $taxes_viewer->data['taxes_list'] = $taxes_list;
        $operation_result['template_taxes_list'] = $taxes_viewer->fetch();

        print json_encode($operation_result);
        exit;
    }

}

?>
