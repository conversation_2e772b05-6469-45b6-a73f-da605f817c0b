{if !$messages->getErrors()}
  <html>
  <head>
    <meta http-equiv="Content-Type" content="text/html; charset={#charset#|escape}" />
  </head>
  <body>
  {assign var='settings' value=$reports_additional_options.settings}
  <table border="1" cellpadding="0" cellspacing="0">
    <thead>
      <tr>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="6">{#material#}</th>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="6">{#suppliers#}</th>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="9">{#availability#}</th>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="8">{#needed_quantity_for_planned_orders#}</th>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="2">{#usage#}</th>

        {assign var='reordered_amount_for_usage_months_invalid_formula' value=false}
        {if empty($reports_additional_options.usage_period_months_count)}
          {assign var='reordered_amount_for_usage_months_invalid_formula' value=true}
        {/if}
        <th rowspan="2" colspan="2" style="vertical-align: middle; background-color: #DFDFDF;" width="90">{#reordered_amount_for_usage_months#}</th>

        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="2">{#suggested_reordered_amount#}</th>
        <th style="vertical-align: middle; background-color: #DFDFDF;" colspan="6">{#request#}</th>
      </tr>
      <tr>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="20">{#num#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="50">{#arise_code#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" style="min-width: 150px;">{#name#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="100">{#type#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="100">{#core#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="100">{#color#}</th>

        <th style="vertical-align: middle; background-color: #F1F1F1;" width="150">{#name#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="52">{#delivery_time_in_days#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="80" colspan="2">{#min_quantity_per_order#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="80" colspan="2">{#price_currency#}</th>

        <th style="vertical-align: middle; background-color: #F1F1F1;" colspan="2" width="85">{#current_availability#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" colspan="2" width="85">{#expected_availability#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="80">{#expected_delivery_date#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="85" colspan="2">{#virtual_availability#}</th>

        {assign var='threshold_quantity_invalid_formula' value=false}
        {if empty($reports_additional_options.usage_period_months_count)}
          {assign var='threshold_quantity_invalid_formula' value=true}
        {/if}
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="85" colspan="2">{#threshold_quantity#}</th>

        <th style="vertical-align: middle; background-color: #F1F1F1;" width="96" colspan="2">{#production_orders_customers#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="96" colspan="2">{#production_orders_stock_orders#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="90" colspan="2">{#total_needed#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="90" colspan="2">{#excess_shortage#}</th>

        <th style="vertical-align: middle; background-color: #F1F1F1;" width="90" colspan="2">
          {if !empty($report_filters.usage_period_from.value)}
            {#from#}<br />
            {$report_filters.usage_period_from.value|date_format:#date_short#}
          {/if}
          {if !empty($report_filters.usage_period_from.additional_filter.value)}
              <br />
              {#to#|strtolower}<br />
            {$report_filters.usage_period_from.additional_filter.value|date_format:#date_short#}
          {/if}
        </th>

        {assign var='reordered_amount_invalid_formula' value=$reordered_amount_for_usage_months_invalid_formula}
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="90" colspan="2">{#reordered_amount#}</th>

        <th style="vertical-align: middle; background-color: #F1F1F1;" width="100" colspan="2">{#request_quantity#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="150">{#from_supplier#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="100" colspan="2">{#total_price#}</th>
        <th style="vertical-align: middle; background-color: #F1F1F1;" width="150">{#notes#}</th>
      </tr>
    </thead>
    <tbody>
      {foreach from=$reports_results item='result'}
        {assign var='rowspan' value=0}
        {assign var='main_supplier_html' value=''}
        {assign var='main_supplier_threshold_quantity_html' value=''}
        {assign var='additional_supplier_html' value=''}
        {assign var='additional_supplier_threshold_quantity_html' value=''}
        {assign var='second_additional_supplier_html' value=''}
        {assign var='second_additional_supplier_threshold_quantity_html' value=''}
        {if !empty($result.main_supplier_id)}
          {assign var='rowspan' value=$rowspan+1}
          {capture assign='main_supplier_html'}
            <td style="vertical-align: middle;" align="left">{$result.main_supplier_name|escape}</td>
            <td style="vertical-align: middle;" align="center">{$result.deliveryterm}</td>
            <td style="vertical-align: middle;" align="center">{$result.main_supplier_minimum_delivery}</td>
            <td style="vertical-align: middle;" align="center">
              {if $result.main_supplier_minimum_delivery ne ''}
                {$result.product_measure}
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">{$result.main_supplier_price}</td>
            <td style="vertical-align: middle;" align="center">{$result.main_supplier_currency}</td>
          {/capture}
          {capture assign='main_supplier_threshold_quantity_html'}
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {math
                  assign='formula_a'
                  equation='material_12_months_usage / usage_period_months_count'
                  material_12_months_usage=$result.material_12_months_usage|default:0
                  usage_period_months_count=$reports_additional_options.usage_period_months_count|default:0
                }
                {math
                  assign='formula_b'
                  equation='delivery_time_in_days / 4.3'
                  delivery_time_in_days=$result.deliveryterm|default:0
                }
                {math
                  assign='threshold_quantity'
                  equation='a * b'
                  a=$formula_a
                  b=$formula_b
                }
                {$threshold_quantity|number_format_depending_type:2:".":""}
              {else}
                -
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {$result.product_measure}
              {else}
                -
              {/if}
            </td>
          {/capture}
        {/if}
        {if !empty($result.additional_supplier_id)}
          {assign var='rowspan' value=$rowspan+1}
          {capture assign='additional_supplier_html'}
            <td style="vertical-align: middle;" align="left">{$result.additional_supplier_name|escape}</td>
            <td style="vertical-align: middle;" align="center">{$result.additional_supplier_delivery_terms}</td>
            <td style="vertical-align: middle;" align="center">{$result.additional_supplier_minimum_delivery}</td>
            <td style="vertical-align: middle;" align="center">
              {if $result.additional_supplier_minimum_delivery ne ''}
                {$result.product_measure}
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">{$result.additional_supplier_price}</td>
            <td style="vertical-align: middle;" align="center">{$result.additional_supplier_currency}</td>
          {/capture}
          {capture assign='additional_supplier_threshold_quantity_html'}
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {math
                  assign='formula_a'
                  equation='material_12_months_usage / usage_period_months_count'
                  material_12_months_usage=$result.material_12_months_usage|default:0
                  usage_period_months_count=$reports_additional_options.usage_period_months_count|default:0
                }
                {math
                  assign='formula_b'
                  equation='delivery_time_in_days / 4.3'
                  delivery_time_in_days=$result.additional_supplier_delivery_terms|default:0
                }
                {math
                  assign='threshold_quantity'
                  equation='a * b'
                  a=$formula_a
                  b=$formula_b
                }
                {$threshold_quantity|number_format_depending_type:2:".":""}
              {else}
                -
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {$result.product_measure}
              {else}
                -
              {/if}
            </td>
          {/capture}
        {/if}
        {if !empty($result.second_additional_supplier_id)}
          {assign var='rowspan' value=$rowspan+1}
          {capture assign='second_additional_supplier_html'}
            <td style="vertical-align: middle;" align="left">{$result.second_additional_supplier_name|escape}</td>
            <td style="vertical-align: middle;" align="center">{$result.second_additional_supplier_delivery_terms}</td>
            <td style="vertical-align: middle;" align="center">{$result.second_additional_supplier_minimum_delivery}</td>
            <td style="vertical-align: middle;" align="center">
              {if $result.second_additional_supplier_minimum_delivery ne ''}
                {$result.product_measure}
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">{$result.second_additional_supplier_price}</td>
            <td style="vertical-align: middle;" align="center">{$result.second_additional_supplier_currency}</td>
          {/capture}
          {capture assign='second_additional_supplier_threshold_quantity_html'}
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {math
                  assign='formula_a'
                  equation='material_12_months_usage / usage_period_months_count'
                  material_12_months_usage=$result.material_12_months_usage|default:0
                  usage_period_months_count=$reports_additional_options.usage_period_months_count|default:0
                }
                {math
                  assign='formula_b'
                  equation='delivery_time_in_days / 4.3'
                  delivery_time_in_days=$result.second_additional_supplier_delivery_terms|default:0
                }
                {math
                  assign='threshold_quantity'
                  equation='a * b'
                  a=$formula_a
                  b=$formula_b
                }
                {$threshold_quantity|number_format_depending_type:2:".":""}
              {else}
                -
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">
              {if !$threshold_quantity_invalid_formula}
                {$result.product_measure}
              {else}
                -
              {/if}
            </td>
          {/capture}
        {/if}

        <tr>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {counter}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {$result.code}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="left">{$result.name}</td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="left">
            {$result.type_name}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="left">
            {$result.core_info}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="left">
            {$result.color}
          </td>

          {if $rowspan eq 0}
            <td style="vertical-align: middle;"></td>
            <td style="vertical-align: middle;"></td>
            <td style="vertical-align: middle;"></td>
            <td style="vertical-align: middle;"></td>
            <td style="vertical-align: middle;"></td>
            <td style="vertical-align: middle;"></td>
          {else}
            {if $main_supplier_html}
              {$main_supplier_html}
              {assign var='main_supplier_html' value=''}
            {elseif $additional_supplier_html}
              {$additional_supplier_html}
              {assign var='additional_supplier_html' value=''}
            {elseif $second_additional_supplier_html}
              {$second_additional_supplier_html}
              {assign var='second_additional_supplier_html' value=''}
            {/if}
          {/if}

          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.current_availability ne ''}
              {$result.current_availability|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.current_availability ne ''}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.expected_availability ne ''}
              {$result.expected_availability|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.expected_availability ne ''}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if !empty($result.expected_delivery_dates)}
              {foreach from=$result.expected_delivery_dates item='expected_delivery_date' name='expected_delivery_dates'}
                {$expected_delivery_date.date|date_format:#date_short#|default:'-'} / {$expected_delivery_date.full_num|escape}
                {if !$smarty.foreach.expected_delivery_dates.last}
                  <br/>
                {/if}
              {/foreach}
            {/if}
          </td>
          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='virtual_availability' value=''}
            {if array_key_exists('current_availability', $result) || array_key_exists('expected_availability', $result)}
              {math
                assign='virtual_availability'
                equation='a+b'
                a=$result.current_availability|default:0
                b=$result.expected_availability|default:0}
              {$virtual_availability|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if array_key_exists('current_availability', $result) || array_key_exists('expected_availability', $result)}
              {$result.product_measure}
            {/if}
          </td>
          {if $rowspan eq 0}
            <td style="vertical-align: middle;" align="center">
              {if $threshold_quantity_invalid_formula}
                -
              {/if}
            </td>
            <td style="vertical-align: middle;" align="center">
              {if $threshold_quantity_invalid_formula}
                -
              {/if}
            </td>
          {else}
            {if $main_supplier_threshold_quantity_html}
              {$main_supplier_threshold_quantity_html}
              {assign var='main_supplier_threshold_quantity_html' value=''}
            {elseif $additional_supplier_threshold_quantity_html}
              {$additional_supplier_threshold_quantity_html}
              {assign var='additional_supplier_threshold_quantity_html' value=''}
            {elseif $second_additional_supplier_threshold_quantity_html}
              {$second_additional_supplier_threshold_quantity_html}
              {assign var='second_additional_supplier_threshold_quantity_html' value=''}
            {/if}
          {/if}

          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.needed_quantities_customers ne ''}
              {$result.needed_quantities_customers|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.needed_quantities_customers ne ''}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.needed_quantities_stock_orders ne ''}
              {$result.needed_quantities_stock_orders|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.needed_quantities_stock_orders ne ''}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='total_needed' value=''}
            {if array_key_exists('needed_quantities_customers', $result) || array_key_exists('needed_quantities_stock_orders', $result)}
              {math
                assign='total_needed'
                equation='a+b'
                a=$result.needed_quantities_customers|default:0
                b=$result.needed_quantities_stock_orders|default:0}
              {$total_needed|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if array_key_exists('needed_quantities_customers', $result) || array_key_exists('needed_quantities_stock_orders', $result)}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='excess_shortage' value=''}
            {if $virtual_availability ne '' || $total_needed ne ''}
              {math
                assign='excess_shortage'
                equation='a-b'
                a=$virtual_availability|default:0
                b=$total_needed|default:0}
              <span{if $excess_shortage lt 0}{/if}>{$excess_shortage|number_format_depending_type:2:".":""}</span>
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $virtual_availability ne '' || $total_needed ne ''}
              {$result.product_measure}
            {/if}
          </td>

          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if array_key_exists('material_12_months_usage', $result) && $result.material_12_months_usage ne ''}
              {$result.material_12_months_usage|number_format_depending_type:2:".":""}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if array_key_exists('material_12_months_usage', $result) && $result.material_12_months_usage ne ''}
              {$result.product_measure}
            {/if}
          </td>

          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='reordered_amount_for_usage_months' value=''}
            {if !$reordered_amount_for_usage_months_invalid_formula}
              {math
                assign='formula_a'
                equation='material_12_months_usage / usage_period_months_count'
                material_12_months_usage=$result.material_12_months_usage|default:0
                usage_period_months_count=$reports_additional_options.usage_period_months_count|default:0
              }
              {math
                assign='formula_b'
                equation='1 + (annual_growth / 100)'
                annual_growth=$report_filters.annual_growth_percentage.value|default:0
              }
              {math
                assign='formula_c'
                equation='months_to_cover'
                months_to_cover=$report_filters.months_to_cover.value|default:0
              }
              {math
                assign='reordered_amount_for_usage_months'
                equation='a * b * c'
                a=$formula_a
                b=$formula_b
                c=$formula_c
              }
              {$reordered_amount_for_usage_months|number_format_depending_type:2:".":""}
            {else}
              -
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if !$reordered_amount_for_usage_months_invalid_formula}
              {$result.product_measure}
            {else}
              -
            {/if}
          </td>

          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='reordered_amount' value=''}
            {if !$reordered_amount_invalid_formula}
              {math
                assign='reordered_amount'
                equation='a - b - c'
                a=$virtual_availability|default:0
                b=$reordered_amount_for_usage_months|default:0
                c=$total_needed|default:0
              }
              {$reordered_amount|number_format_depending_type:2:".":""}
            {else}
              -
            {/if}
          </td>
          <td style="vertical-align: middle; background-color: #F1F1F1;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if !$reordered_amount_invalid_formula}
              {$result.product_measure}
            {else}
              -
            {/if}
          </td>

          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {assign var='show_fields' value=false}
            {if $result.main_supplier_id ne '' && $result.main_supplier_price ne '' && $result.main_supplier_currency ne ''
                || $result.additional_supplier_id ne '' && $result.additional_supplier_price ne '' && $result.additional_supplier_currency ne ''
                || $result.second_additional_supplier_id ne '' && $result.second_additional_supplier_price ne '' && $result.second_additional_supplier_currency ne ''}
              {assign var='show_fields' value=true}
              {$result.request_quantity}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $show_fields}
              {$result.product_measure}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="left">
            {if $show_fields}
              {if $result.main_supplier_id ne '' && $result.main_supplier_price ne '' && $result.main_supplier_currency ne '' && ($result.request_supplier eq '' || $result.request_supplier eq $result.main_supplier_id)}
                {$result.main_supplier_name}
              {elseif $result.additional_supplier_id ne '' && $result.additional_supplier_price ne '' && $result.additional_supplier_currency ne '' && $result.request_supplier eq $result.additional_supplier_id}
                {$result.additional_supplier_name}
              {elseif $result.second_additional_supplier_id ne '' && $result.second_additional_supplier_price ne '' && $result.second_additional_supplier_currency ne '' && $result.request_supplier eq $result.second_additional_supplier_id}
                {$result.second_additional_supplier_name}
              {/if}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.request_supplier eq '' || $result.request_supplier eq $result.main_supplier_id}
              {if $result.request_quantity ne '' && $result.main_supplier_price ne '' && $result.main_supplier_currency ne ''}
                {math
                  equation='a*b'
                  a=$result.request_quantity
                  b=$result.main_supplier_price}
              {/if}
            {elseif $result.request_supplier eq $result.additional_supplier_id}
              {if $result.request_quantity ne '' && $result.additional_supplier_price ne '' && $result.additional_supplier_currency ne ''}
                {math
                  equation='a*b'
                  a=$result.request_quantity
                  b=$result.additional_supplier_price}
              {/if}
            {elseif $result.request_supplier eq $result.second_additional_supplier_id}
              {if $result.request_quantity ne '' && $result.second_additional_supplier_price ne '' && $result.second_additional_supplier_currency ne ''}
                {math
                  equation='a*b'
                  a=$result.request_quantity
                  b=$result.second_additional_supplier_price}
              {/if}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if} align="center">
            {if $result.request_supplier eq '' || $result.request_supplier eq $result.main_supplier_id}
              {if $result.request_quantity ne '' && $result.main_supplier_price ne '' && $result.main_supplier_currency ne ''}
                {$result.main_supplier_currency}
              {/if}
            {elseif $result.request_supplier eq $result.additional_supplier_id}
              {if $result.request_quantity ne '' && $result.additional_supplier_price ne '' && $result.additional_supplier_currency ne ''}
                {$result.additional_supplier_currency}
              {/if}
            {elseif $result.request_supplier eq $result.second_additional_supplier_id}
              {if $result.request_quantity ne '' && $result.second_additional_supplier_price ne '' && $result.second_additional_supplier_currency ne ''}
                {$result.second_additional_supplier_currency}
              {/if}
            {/if}
          </td>
          <td style="vertical-align: middle;"{if $rowspan} rowspan="{$rowspan}"{/if}>
            {if $show_fields}
              {$result.request_notes|nl2br}
            {/if}
          </td>
        </tr>

        {if $rowspan gt 1}
          {if $main_supplier_html}
            <tr>
              {$main_supplier_html}
              {$main_supplier_threshold_quantity_html}
            </tr>
          {/if}
          {if $additional_supplier_html}
            <tr>
              {$additional_supplier_html}
              {$additional_supplier_threshold_quantity_html}
            </tr>
          {/if}
          {if $second_additional_supplier_html}
            <tr>
              {$second_additional_supplier_html}
              {$second_additional_supplier_threshold_quantity_html}
            </tr>
          {/if}
        {/if}
      {foreachelse}
        <tr>
          <td style="vertical-align: middle;" colspan="41" align="left">
            <span style="color: red;">{#no_items_found#|escape}</span>
          </td>
        </tr>
      {/foreach}
    </tbody>
  </table>
  </body>
  </html>
{/if}