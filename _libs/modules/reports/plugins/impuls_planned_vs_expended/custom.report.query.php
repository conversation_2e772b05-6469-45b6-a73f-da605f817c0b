<?php
    Class Impuls_Planned_Vs_Expended Extends Reports {
        public static function buildQuery(&$registry, $filters = array()) {

            //set model lang filter
            if (!empty($filters['model_lang'])) {
                $model_lang = $filters['model_lang'];
            } else {
                //default model language is the interface language
                $model_lang = $registry['lang'];
            }

            $final_results = array();
            $graphs = array();
            $total = 0;
            $total_with_vat = 0;
            $total_invoiced = 0;
            $total_difference = 0;
            $total_price = 0;
            $total_quantity = 0;
            $total_value = 0;

            if (!empty($filters['request_status']) && !empty($filters['currency'])) {
                /*
                 * DEFINE AVAILABLE TYPES
                 */
                $available_documents = array(
                    IMPULSE_CO_DOCUMENT_ID          => array(
                        'company'                       => 'impulse_co',
                        'type_id'                       => IMPULSE_CO_DOCUMENT_ID,
                        'substatus_executed'            => STATUS_IMPULSE_CO_EXECUTED,
                        'substatus_partially_executed'   => STATUS_IMPULSE_CO_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_REQUEST_MATERIALS_IK        => array(
                        'company'                       => 'impulse_co',
                        'type_id'                       => IMPULSE_REQUEST_MATERIALS_IK,
                        'substatus_executed'            => STATUS_MATERIALS_IK_EXECUTED,
                        'substatus_partially_executed'   => STATUS_MATERIALS_IK_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_FUEL_REQUEST_IK         => array(
                        'company'                       => 'impulse_co',
                        'type_id'                       => IMPULSE_FUEL_REQUEST_IK,
                        'substatus_executed'            => STATUS_FUEL_IK_EXECUTED,
                        'substatus_partially_executed'   => STATUS_FUEL_IK_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_REQUEST_TS_IK           => array(
                        'company'                       => 'impulse_co',
                        'type_id'                       => IMPULSE_REQUEST_TS_IK,
                        'substatus_executed'            => STATUS_OTHERS_IK_EXECUTED,
                        'substatus_partially_executed'   => STATUS_OTHERS_IK_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_PROJECT_DOCUMENT_ID     => array(
                        'company'                       => 'impulse_prj',
                        'type_id'                       => IMPULSE_PROJECT_DOCUMENT_ID,
                        'substatus_executed'            => STATUS_IMPULSE_PROJECT_EXECUTED,
                        'substatus_partially_executed'   => STATUS_IMPULSE_PROJECT_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_REQUEST_MATERIALS_IP    => array(
                        'company'                       => 'impulse_prj',
                        'type_id'                       => IMPULSE_REQUEST_MATERIALS_IP,
                        'substatus_executed'            => STATUS_MATERIALS_IP_EXECUTED,
                        'substatus_partially_executed'   => STATUS_MATERIALS_IP_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_FUEL_REQUEST_IP         => array(
                        'company'                       => 'impulse_prj',
                        'type_id'                       => IMPULSE_FUEL_REQUEST_IP,
                        'substatus_executed'            => STATUS_FUEL_IP_EXECUTED,
                        'substatus_partially_executed'   => STATUS_FUEL_IP_PARTIALLY_EXECUTED
                    ),
                    IMPULSE_REQUEST_TS_IP           => array(
                        'company'                       => 'impulse_prj',
                        'type_id'                       => IMPULSE_REQUEST_TS_IP,
                        'substatus_executed'            => STATUS_OTHERS_IP_EXECUTED,
                        'substatus_partially_executed'   => STATUS_OTHERS_IP_PARTIALLY_EXECUTED
                    )
                );

                require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
                $currency_rates = array();

                $documents_for_report = array();
                if ($filters['main_options'] == 'general_report') {
                    $documents_for_report = $available_documents;
                } elseif ($filters['main_options'] == 'financial_resources') {
                    $documents_for_report[IMPULSE_CO_DOCUMENT_ID] = $available_documents[IMPULSE_CO_DOCUMENT_ID];
                    $documents_for_report[IMPULSE_PROJECT_DOCUMENT_ID] = $available_documents[IMPULSE_PROJECT_DOCUMENT_ID];
                } elseif ($filters['main_options'] == 'transport') {
                    $documents_for_report[IMPULSE_FUEL_REQUEST_IK] = $available_documents[IMPULSE_FUEL_REQUEST_IK];
                    $documents_for_report[IMPULSE_FUEL_REQUEST_IP] = $available_documents[IMPULSE_FUEL_REQUEST_IP];
                } elseif ($filters['main_options'] == 'materials') {
                    $documents_for_report[IMPULSE_REQUEST_MATERIALS_IK] = $available_documents[IMPULSE_REQUEST_MATERIALS_IK];
                    $documents_for_report[IMPULSE_REQUEST_MATERIALS_IP] = $available_documents[IMPULSE_REQUEST_MATERIALS_IP];
                } elseif ($filters['main_options'] == 'warehouse') {
                    $documents_for_report[IMPULSE_REQUEST_TS_IK] = $available_documents[IMPULSE_REQUEST_TS_IK];
                    $documents_for_report[IMPULSE_REQUEST_TS_IP] = $available_documents[IMPULSE_REQUEST_TS_IP];
                }

                $company_types = array();
                if (!empty($filters['company'])) {
                    $company_types = explode(',', $filters['company']);
                } else {
                    $company_types = array_keys($documents_for_report);
                }

                $document_types = array();
                $substatus_executed = array();
                $substatus_partially_executed = array();
                foreach ($documents_for_report as $doc_type) {
                    if (in_array($doc_type['type_id'], $company_types)) {
                        $document_types[] = $doc_type['type_id'];
                        $substatus_executed[] = $doc_type['substatus_executed'];
                        $substatus_partially_executed[] = $doc_type['substatus_partially_executed'];
                    }
                }

                $deliverers_graph = array();
                if (!empty($document_types)) {
                    /*
                     * GENERAL INFORMATION FOR ALL TYPES
                     */
                    $type_table = 'general_information';
                    $query_results = array();

                    $vars_names = array(OFFICE, NOTES, DELIVERER, REQUESTED_BY_UNIT, EMPLOYEE, APPROVED, CURRENCY);
                    $sql_for_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Document" AND fm.model_type IN (' . implode(',', $document_types) . ') AND fm.name IN ("' . implode('","', $vars_names) . '") ORDER BY fm.position';
                    $var_ids = $registry['db']->GetAll($sql_for_add_vars);

                    $office_ids = array();
                    $notes_ids = array();
                    $deliverer_ids = array();
                    $requested_by_unit_ids = array();
                    $employee_ids = array();
                    $approved_ids = array();
                    $currencies_ids = array();

                    foreach ($var_ids as $vars) {
                        if ($vars['name'] == OFFICE) {
                            $office_ids[] = $vars['id'];
                        } else if ($vars['name'] == NOTES) {
                            $notes_ids[] = $vars['id'];
                        } else if ($vars['name'] == DELIVERER) {
                            $deliverer_ids[] = $vars['id'];
                        } else if ($vars['name'] == REQUESTED_BY_UNIT) {
                            $requested_by_unit_ids[] = $vars['id'];
                        } else if ($vars['name'] == EMPLOYEE) {
                            $employee_ids[] = $vars['id'];
                        } else if ($vars['name'] == APPROVED) {
                            $approved_ids[] = $vars['id'];
                        } else if ($vars['name'] == CURRENCY) {
                            $currencies_ids[] = $vars['id'];
                        }
                    }

                    // get vars ids of the nomenclatures
                    $vars_nom_names_expenses = array(NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_1,NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_2,NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_3,NOMENCLATURE_TRANSPORT_TYPE_EXPENSE,NOMENCLATURE_MATERIAL_TYPE_EXPENSE,NOMENCLATURE_FINANCIAL_TYPE_EXPENSE);
                    $vars_nom_names = array_merge($vars_nom_names_expenses, array(TRANSPORT_PPS_TYPE));
                    $sql_for_nom_add_vars = 'SELECT fm.id, fm.name FROM ' . DB_TABLE_FIELDS_META . ' AS fm WHERE fm.model="Nomenclature" AND fm.name IN ("' . implode('","', $vars_nom_names) . '")';

                    $nom_add_vars_data = $registry['db']->GetAll($sql_for_nom_add_vars);
                    $nom_vars_ids = array();
                    $nom_type_pps = '';
                    foreach ($nom_add_vars_data as $var) {
                        if (in_array($var['name'], $vars_nom_names_expenses)) {
                            $nom_vars_ids[] = $var['id'];
                        } elseif ($var['name'] == TRANSPORT_PPS_TYPE) {
                            $nom_type_pps = $var['id'];
                        }
                    }
                    unset($vars_nom_names);
                    unset($nom_add_vars_data);

                    // sql to take the data from the requests
                    $sql = array();
                    $sql['select'] = 'SELECT d.id AS id, d.type as type, dti18n.name as type_name, d.full_num AS full_num, DATE_FORMAT(d.added, "%Y-%m-%d") AS date_added, ' . "\n" .
                                     '  IF(d.type IN (' . IMPULSE_CO_DOCUMENT_ID . ',' . IMPULSE_PROJECT_DOCUMENT_ID . '), d_cstm_office.value, gt2.article_deliverer) as branch, ' . "\n" .
                                     '  IF(d.type IN (' . IMPULSE_CO_DOCUMENT_ID . ',' . IMPULSE_PROJECT_DOCUMENT_ID . '), d_cstm_office_name.label, CONCAT("[", nom.code, "] ", nomi18n.name)) as branch_name, ' . "\n" .
                                     '  gt2.id as gt2_row, nom_cstm_kind.value as type_expense_id, nom_cstm_kind_name.label as type_expense, nom_cstm_kind_name.parent_name as type_expense_parent_var, ' . "\n" .
                                     '  gt2.article_id as nomenclature, nomi18n2.name as nomenclature_name, gt2.subtotal_with_vat_with_discount, ' . "\n" .
                                     '  gt2i18n.article_description as descripton, gt2.quantity, gt2.price, gt2.subtotal_with_discount, d_cstm_notes.value as notes, ' . "\n" .
                                     '  d.project, pi18n.name as project_name, d_cstm_deliverer.value as deliverer_id, d_cstm_employee.value as employee_id, ' . "\n" .
                                     '  TRIM(CONCAT(d_cstm_deliverer_name.name, " ", d_cstm_deliverer_name.lastname)) AS deliverer_name, CONCAT(d_cstm_employee_name.name, " ", d_cstm_employee_name.lastname) as employee_name, ' . "\n" .
                                     '  d_cstm_currency.value as currency, d_cstm_unit.value as department_id, d_cstm_unit_name.name as department_name ' . "\n";

                    //from clause
                    $sql['from']  =  'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_TYPES_I18N . ' AS dti18n' . "\n" .
                                     '  ON (d.type=dti18n.parent_id AND dti18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                     '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                                     '  ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                                     '  ON (pi18n.parent_id=d.project AND pi18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS nom' . "\n" .
                                     '  ON (d.type IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') AND nom.id=gt2.article_deliverer)' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n' . "\n" .
                                     '  ON (d.type IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') AND nomi18n.parent_id=gt2.article_deliverer AND nomi18n.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS nomi18n2' . "\n" .
                                     '  ON (nomi18n2.parent_id=gt2.article_id AND nomi18n2.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm_kind' . "\n" .
                                     '  ON (gt2.article_id=nom_cstm_kind.model_id AND nom_cstm_kind.var_id IN (' . implode(',', $nom_vars_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS nom_cstm_kind_name' . "\n" .
                                     '  ON (nom_cstm_kind.value=nom_cstm_kind_name.option_value AND nom_cstm_kind_name.parent_name IN ("' . implode('","', $vars_nom_names_expenses) . '") AND nom_cstm_kind_name.lang="' . $model_lang . '")' . "\n" .
                                     // next join is for the PPS type
                                     'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nom_cstm_kind_pps' . "\n" .
                                     '  ON (gt2.article_deliverer=nom_cstm_kind_pps.model_id AND nom_cstm_kind_pps.var_id="' . $nom_type_pps . '")' . "\n" .

                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_unit' . "\n" .
                                     '  ON (d.id=d_cstm_unit.model_id AND d_cstm_unit.var_id IN (' . implode(',', $requested_by_unit_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS d_cstm_unit_name' . "\n" .
                                     '  ON (d_cstm_unit_name.parent_id=d_cstm_unit.value AND d_cstm_unit_name.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_notes' . "\n" .
                                     '  ON (d.id=d_cstm_notes.model_id AND d_cstm_notes.var_id IN (' . implode(',', $notes_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_office' . "\n" .
                                     '  ON (d.type IN (' . IMPULSE_CO_DOCUMENT_ID . ',' . IMPULSE_PROJECT_DOCUMENT_ID . ') AND d.id=d_cstm_office.model_id AND d_cstm_office.var_id IN (' . (!empty($office_ids) ? implode(',', $office_ids) : 0) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS d_cstm_office_name' . "\n" .
                                     '  ON (d_cstm_office.value=d_cstm_office_name.option_value AND d_cstm_office_name.parent_name="' . OFFICE . '" AND d_cstm_office_name.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_currency' . "\n" .
                                     '  ON (d.id=d_cstm_currency.model_id AND d_cstm_currency.var_id IN (' . implode(',', $currencies_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_deliverer' . "\n" .
                                     '  ON (d.id=d_cstm_deliverer.model_id AND d_cstm_deliverer.var_id IN (' . implode(',', $deliverer_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS d_cstm_deliverer_name' . "\n" .
                                     '  ON (d_cstm_deliverer_name.parent_id=d_cstm_deliverer.value AND d_cstm_deliverer_name.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_employee' . "\n" .
                                     '  ON (d.id=d_cstm_employee.model_id AND d_cstm_employee.var_id IN (' . implode(',', $employee_ids) . '))' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS d_cstm_employee_name' . "\n" .
                                     '  ON (d_cstm_employee_name.parent_id=d_cstm_employee.value AND d_cstm_employee_name.lang="' . $model_lang . '")' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_approved' . "\n" .
                                     '  ON (d.id=d_cstm_approved.model_id AND d_cstm_approved.var_id IN (' . implode(',', $approved_ids) . '))' . "\n";

                    $where = array();
                    $where[] = 'd.active=1';
                    $where[] = 'd.deleted_by=0';
                    $where[] = 'd.type IN (' . implode(',', $document_types) .  ')';
                    $where[] = 'gt2.id IS NOT NULL';

                    if (!empty($filters['request_status']) && $filters['request_status']!='all') {
                        if ($filters['request_status'] == 'approved_and_executed') {
                            $where[] = '(d_cstm_approved.value="' . APPROVED_YES . '" AND d.substatus IN ("' . implode('","', $substatus_executed) . '"))';
                        } elseif ($filters['request_status'] == 'approved_and_partially_executed') {
                            $where[] = '(d_cstm_approved.value="' . APPROVED_YES . '" AND d.substatus IN ("' . implode('","', $substatus_partially_executed) . '"))';
                        } elseif ($filters['request_status'] == 'disapproved') {
                            $where[] = 'd_cstm_approved.value="' . APPROVED_NO . '"';
                        } elseif ($filters['request_status'] == 'approved') {
                            $where[] = '(d_cstm_approved.value="' . APPROVED_YES . '" AND d.status="opened")';
                        }
                    }
                    if (!empty($filters['from_date'])) {
                        $where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d") >= "' . $filters['from_date'] . '"';
                    }
                    if (!empty($filters['to_date'])) {
                        $where[] = 'DATE_FORMAT(d.added, "%Y-%m-%d") <= "' . $filters['to_date'] . '"';
                    }
                    if (!empty($filters['project'])) {
                        $where[] = 'd.project="' . $filters['project'] . '"';
                    }
                    if (!empty($filters['pps_machine'])) {
                        $where[] = 'gt2.article_deliverer="' . $filters['pps_machine'] . '"';
                    }
                    if (!empty($filters['requested_by_unit'])) {
                        // get child department of the selected department
                        require_once PH_MODULES_DIR . "departments/models/departments.factory.php";
                        $child_departments = Departments::getTreeDescendantsIds($registry, $filters['requested_by_unit']);
                        $where[] = 'd_cstm_unit.value IN ("' .implode('","', $child_departments) . '")';
                    }
                    if (!empty($filters['nomenclature'])) {
                        $nomeclatures_list = array();
                        foreach ($filters['nomenclature'] as $nom_id) {
                            if (!empty($nom_id)) {
                                $nomeclatures_list[] = $nom_id;
                            }
                        }
                        if (!empty($nomeclatures_list)) {
                            $where[] = 'gt2.article_id IN (' . implode(',', $nomeclatures_list) . ')';
                        }
                    }
                    if (!empty($filters['deliverer'])) {
                        $deliverers_list = array();
                        foreach ($filters['deliverer'] as $del_id) {
                            if (!empty($del_id)) {
                                $deliverers_list[] = $del_id;
                            }
                        }
                        if (!empty($deliverers_list)) {
                            $where[] = 'd_cstm_deliverer.value IN (' . implode(',', $deliverers_list) . ')';
                        }
                    }
                    if (!empty($filters['employee'])) {
                        $where[] = 'd_cstm_employee.value="' . $filters['employee'] . '"';
                    }
                    if (!empty($filters['warehouse_type_expense'])) {
                        $where[] = '(d.type NOT IN (' . IMPULSE_REQUEST_TS_IK . ',' . IMPULSE_REQUEST_TS_IP . ') OR (d.type IN (' . IMPULSE_REQUEST_TS_IK . ',' . IMPULSE_REQUEST_TS_IP . ') AND nom_cstm_kind.value="' . $filters['warehouse_type_expense'] . '"))';
                    }
                    if (!empty($filters['financial_type_expense'])) {
                        $where[] = '(d.type NOT IN (' . IMPULSE_CO_DOCUMENT_ID . ',' . IMPULSE_PROJECT_DOCUMENT_ID . ') OR (d.type IN (' . IMPULSE_CO_DOCUMENT_ID . ',' . IMPULSE_PROJECT_DOCUMENT_ID . ') AND nom_cstm_kind.value="' . $filters['financial_type_expense'] . '"))';
                    }
                    if (!empty($filters['type_pps'])) {
                        $where[] = '(d.type NOT IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') OR (d.type IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') AND nom_cstm_kind_pps.value="' . $filters['type_pps'] . '"))';
                    }
                    if (!empty($filters['transport_type_expense'])) {
                        $where[] = '(d.type NOT IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') OR (d.type IN (' . IMPULSE_FUEL_REQUEST_IK . ',' . IMPULSE_FUEL_REQUEST_IP . ') AND nom_cstm_kind.value="' . $filters['transport_type_expense'] . '"))';
                    }
                    if (!empty($filters['materials_type_expense'])) {
                        $materials_expenses_list = explode(',', $filters['materials_type_expense']);
                        $where[] = '(d.type NOT IN (' . IMPULSE_REQUEST_MATERIALS_IK . ',' . IMPULSE_REQUEST_MATERIALS_IP . ') OR (d.type IN (' . IMPULSE_REQUEST_MATERIALS_IK . ',' . IMPULSE_REQUEST_MATERIALS_IP . ') AND nom_cstm_kind.value IN ("' . implode('","', $materials_expenses_list) . '")))';
                    }
                    if (!empty($filters['expense_description'])) {
                        $all_words = array();
                        $all_words = explode(" ", $filters['expense_description']);
                        $description_clauses = array();
                        foreach ($all_words as $word) {
                            if (mb_strlen($word, mb_detect_encoding($word)) >= 3) {
                                $description_clauses[] = '(LOWER(gt2i18n.article_description) REGEXP "[[:<:]]' . mb_strtolower($word, mb_detect_encoding($word)) . '[[:>:]]")';
                            }
                        }
                        if (!empty($description_clauses)) {
                            $where[] = '(' . implode(' OR ', $description_clauses) . ')';
                        }
                    }

                    $sql['where'] = 'WHERE ' . implode(' AND ', $where);

                    //search basic details with current lang parameters
                    $query = implode("\n", $sql);
                    $records = $registry['db']->GetAll($query);

                    // empty array to be used for data of all pie charts
                    $empty_series = array(
                                        'series' => array(
                                            array(
                                                'data' => array()
                                            )
                                        )
                                    );

                    $gt2_relations = array();
                    foreach ($records as $rec) {
                        if (!isset($final_results[$rec['id']])) {
                            $received_by = array();
                            if ($rec['deliverer_name']) {
                                $received_by[] = $rec['deliverer_name'];
                                if (!isset($deliverers_graph[$rec['deliverer_id']])) {
                                    $deliverers_graph[$rec['deliverer_id']] = array(
                                        'name'     => $rec['deliverer_name'],
                                        'y'        => 0,
                                        'gt2_rows' => array()
                                    );
                                }
                            }
                            if ($rec['employee_name']) {
                                $received_by[] = $rec['employee_name'];
                            }
                            $final_results[$rec['id']] = array(
                                'id'                => $rec['id'],
                                'full_num'          => $rec['full_num'],
                                'date_added'        => $rec['date_added'],
                                'type'              => $rec['type'],
                                'type_name'         => $rec['type_name'],
                                'project'           => $rec['project'],
                                'project_name'      => $rec['project_name'],
                                'branches'          => array(),
                                'total'             => 0,
                                'total_with_vat'    => 0,
                                'invoiced_amount'   => 0,
                                'difference'        => 0,
                                'invoices'          => array(),
                                'notes'             => $rec['notes'],
                                'received_by_name'  => (!empty($received_by) ? implode(',', $received_by) : ''),
                                'requested_by'      => $rec['department_id'],
                                'requested_by_name' => $rec['department_name'],
                                'rowspan'           => 0
                            );
                        }

                        if (!empty($rec['branch'])) {
                            $branch_id = $rec['branch'];
                            $branch_name = $rec['branch_name'];
                        } else {
                            $branch_id = 0;
                            $branch_name = '-';
                        }

                        if (!isset($final_results[$rec['id']]['branches'][$branch_id])) {
                            $final_results[$rec['id']]['branches'][$branch_id] = array(
                                'id'       => $branch_id,
                                'name'     => $branch_name,
                                'gt2_rows' => array(),
                                'rowspan'  => 0
                            );
                        }

                        $currency_key = sprintf('%s->%s', $rec['currency'], $filters['currency']);
                        if (!isset($currency_rates[$currency_key])) {
                            $currency_rates[$currency_key] = Finance_Currencies::getRate($registry, $rec['currency'], $filters['currency']);
                        }
                        $price_converted = round($rec['price'] * $currency_rates[$currency_key], 2);
                        $subtotal_with_discount_converted = round($rec['subtotal_with_discount'] * $currency_rates[$currency_key], 2);
                        $subtotal_with_vat_with_discount_converted = round($rec['subtotal_with_vat_with_discount'] * $currency_rates[$currency_key], 2);

                        $final_results[$rec['id']]['branches'][$branch_id]['gt2_rows'][$rec['gt2_row']] = array(
                            'id'                              => $rec['gt2_row'],
                            'type_expense'                    => $rec['type_expense'],
                            'nomenclature'                    => $rec['nomenclature'],
                            'nomenclature_name'               => $rec['nomenclature_name'],
                            'descripton'                      => $rec['descripton'],
                            'quantity'                        => $rec['quantity'],
                            'price'                           => $price_converted,
                            'subtotal_with_discount'          => $subtotal_with_discount_converted,
                            'subtotal_with_vat_with_discount' => $subtotal_with_vat_with_discount_converted
                        );

                        $final_results[$rec['id']]['total'] += $subtotal_with_discount_converted;
                        $final_results[$rec['id']]['total_with_vat'] += $subtotal_with_vat_with_discount_converted;
                        if (isset($deliverers_graph[$rec['deliverer_id']]) && (!in_array($rec['gt2_row'], $deliverers_graph[$rec['deliverer_id']]['gt2_rows']))) {
                            $deliverers_graph[$rec['deliverer_id']]['y'] += $subtotal_with_vat_with_discount_converted;
                            $deliverers_graph[$rec['deliverer_id']]['gt2_rows'][] = $rec['gt2_row'];
                        }
                        $final_results[$rec['id']]['rowspan']++;
                        $final_results[$rec['id']]['branches'][$branch_id]['rowspan']++;
                        $gt2_relations[$rec['gt2_row']] = sprintf('%s_%s', $rec['id'], $branch_id);
                        $total += $subtotal_with_discount_converted;
                        $total_with_vat += $subtotal_with_vat_with_discount_converted;
                        $total_price += $price_converted;
                        $total_quantity += $rec['quantity'];
                        $total_value += $subtotal_with_discount_converted;

                        // graph data
                        if ($filters['main_options'] == 'general_report') {
                            if (!isset($graphs[$filters['main_options']])) {
                                //prepare chart attributes
                                $graphs[$filters['main_options']] = $empty_series;
                                $graphs[$filters['main_options']]['series'][0]['data'] = array(
                                    'financial_resources' => array(
                                        'name' => $registry['translater']->translate('reports_financial_resources_option'),
                                        'y' => 0,
                                        'color' => '#FF0000'
                                    ),
                                    'transport' => array(
                                        'name' => $registry['translater']->translate('reports_transport_option'),
                                        'y' => 0,
                                        'color' => '#00FF00'
                                    ),
                                    'materials' => array(
                                        'name' => $registry['translater']->translate('reports_materials_option'),
                                        'y' => 0,
                                        'color' => '#0000FF'
                                    ),
                                    'warehouse' => array(
                                        'name' => $registry['translater']->translate('reports_warehouse_option'),
                                        'y' => 0,
                                        'color' => '#E5E31E'
                                    )
                                );
                            }

                            $slice = '';
                            switch ($rec['type']) {
                                case IMPULSE_CO_DOCUMENT_ID:
                                case IMPULSE_PROJECT_DOCUMENT_ID:
                                    $slice = 'financial_resources';
                                    break;
                                case IMPULSE_FUEL_REQUEST_IK:
                                case IMPULSE_FUEL_REQUEST_IP:
                                    $slice = 'transport';
                                    break;
                                case IMPULSE_REQUEST_MATERIALS_IK:
                                case IMPULSE_REQUEST_MATERIALS_IP:
                                    $slice = 'materials';
                                    break;
                                case IMPULSE_REQUEST_TS_IK:
                                case IMPULSE_REQUEST_TS_IP:
                                    $slice = 'warehouse';
                                    break;
                                default: break;
                            }
                            if ($slice) {
                                $graphs[$filters['main_options']]['series'][0]['data'][$slice]['y'] += $subtotal_with_vat_with_discount_converted;
                            }
                        } elseif ($filters['main_options'] == 'materials' || $filters['main_options'] == 'transport') {
                            if (!isset($graphs[$filters['main_options']])) {
                                $graphs[$filters['main_options']] = $empty_series;
                            }

                            // current expense id
                            if ($rec['type_expense_id']) {
                                $current_type_expense = $rec['type_expense_id'];
                            } else {
                                $current_type_expense = 0;
                            }

                            if (!isset($graphs[$filters['main_options']]['series'][0]['data'][$current_type_expense])) {
                                //prepare chart attributes
                                $graphs[$filters['main_options']]['series'][0]['data'][$current_type_expense] = array(
                                    'name' => ($rec['type_expense'] ? $rec['type_expense'] : $registry['translater']->translate('reports_undefined_type_expense')),
                                    'y' => 0
                                );
                            }
                            $graphs[$filters['main_options']]['series'][0]['data'][$current_type_expense]['y'] += $subtotal_with_vat_with_discount_converted;
                        } elseif ($filters['main_options'] == 'warehouse') {
                            // prepare four charts for this option
                            if (!isset($graphs['warehouse_total'])) {
                                //prepare chart attributes
                                $graphs['warehouse_total'] = $empty_series;
                                $graphs['warehouse_total']['series'][0]['data'] = array(
                                    'warehouse' => array(
                                        'name' => $registry['translater']->translate('reports_warehouse_optlabel_warehouse'),
                                        'y' => 0
                                    ),
                                    'zbut' => array(
                                        'name' => $registry['translater']->translate('reports_warehouse_optlabel_zbut'),
                                        'y' => 0
                                    ),
                                    'ist' => array(
                                        'name' => $registry['translater']->translate('reports_warehouse_optlabel_ist'),
                                        'y' => 0
                                    )
                                );

                                //prepare the other charts
                                $graphs['warehouse'] = $empty_series;
                                $graphs['zbut'] = $empty_series;
                                $graphs['ist'] = $empty_series;
                            }

                            $key_graph = '';
                            if ($rec['type_expense_parent_var'] == NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_1) {
                                $key_graph = 'warehouse';
                            } elseif ($rec['type_expense_parent_var'] == NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_2) {
                                $key_graph = 'zbut';
                            } elseif ($rec['type_expense_parent_var'] == NOMENCLATURE_WAREHOUSE_TYPE_EXPENSE_3) {
                                $key_graph = 'ist';
                            }

                            if ($key_graph) {
                                // complete the total graph
                                $graphs['warehouse_total']['series'][0]['data'][$key_graph]['y'] += $subtotal_with_vat_with_discount_converted;

                                // complete the sub graph
                                if (!isset($graphs[$key_graph]['series'][0]['data'][$rec['type_expense_id']])) {
                                    $graphs[$key_graph]['series'][0]['data'][$rec['type_expense_id']] = array(
                                        'name' => $rec['type_expense'],
                                        'y' => 0
                                    );
                                }
                                $graphs[$key_graph]['series'][0]['data'][$rec['type_expense_id']]['y'] += $subtotal_with_vat_with_discount_converted;
                            } else {
                                if (!isset($graphs['warehouse_total']['series'][0]['data']['undefined'])) {
                                    $graphs['warehouse_total']['series'][0]['data']['undefined'] = array(
                                        'name' => $registry['translater']->translate('reports_undefined_type_expense'),
                                        'y' => 0
                                    );
                                }
                                $graphs['warehouse_total']['series'][0]['data']['undefined']['y'] += $subtotal_with_vat_with_discount_converted;
                            }
                        }
                    }

                    if (!empty($deliverers_graph)) {
                        foreach ($deliverers_graph as $key_del => $deli)  {
                            unset($deliverers_graph[$key_del]['gt2_rows']);
                        }
                        $graphs['deliverers'] = $empty_series;
                        $graphs['deliverers']['series'][0]['data'] = array_values($deliverers_graph);
                    }

                    $exp_reason_row_relate_to_doc_row = array();
                    if (!empty($final_results) && !empty($gt2_relations)) {
                        $expenses_types = array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE);
                        $sql_relations_1 = 'SELECT frr.rows_links' . "\n" .
                                           'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                                           'JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                           '  ON (fer.id=frr.parent_id AND fer.active=1 AND fer.annulled_by=0 AND fer.type IN (' . implode(',', $expenses_types) . ') AND fer.status="finished")' . "\n" .
                                           'WHERE frr.link_to_model_name="Document" AND frr.link_to IN (' . implode(',', array_keys($final_results)) . ') AND frr.parent_model_name="Finance_Expenses_Reason"' . "\n";
                        $records_relations = $registry['db']->GetCol($sql_relations_1);

                        foreach ($records_relations as $rec_relate) {
                            $rows_links = preg_split('#\n|\r|\r\n#', $rec_relate);
                            foreach ($rows_links as $row) {
                                $row_rels = preg_split('#\s*\=\>\s*#', $row);
                                if (!empty($row_rels[1]) && array_key_exists($row_rels[1], $gt2_relations)) {
                                    $exp_reason_row_relate_to_doc_row[$row_rels[0]] = $row_rels[1];
                                }
                            }
                        }
                    }

                    if (!empty($exp_reason_row_relate_to_doc_row)) {
                        // get the data from the related invoices
                        $sql_exp_resons = 'SELECT fer.id, fer.issue_date, fer.type, fer.invoice_num, gt2.id AS gt2_id, gt2.article_id, gt2.subtotal_with_vat_with_discount, fer.currency ' . "\n" .
                                           'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                                           'LEFT JOIN ' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' AS fer' . "\n" .
                                           '  ON (gt2.model_id=fer.id)' . "\n" .
                                           'WHERE gt2.model="Finance_Expenses_Reason" AND gt2.id IN (' . implode(',', array_keys($exp_reason_row_relate_to_doc_row)) . ')' . "\n";
                        $expenses_reasons_data = $registry['db']->GetAll($sql_exp_resons);
                        foreach ($expenses_reasons_data as $exp_reas_dat) {
                            if (array_key_exists($exp_reas_dat['gt2_id'], $exp_reason_row_relate_to_doc_row) && !empty($exp_reason_row_relate_to_doc_row[$exp_reas_dat['gt2_id']])) {
                                $doc_row = $exp_reason_row_relate_to_doc_row[$exp_reas_dat['gt2_id']];
                                if (!empty($gt2_relations[$doc_row])) {
                                    list($doc_id, $branch_id) = explode('_', $gt2_relations[$doc_row]);
                                    if ($exp_reas_dat['type']==PH_FINANCE_TYPE_EXPENSES_INVOICE) {

                                        $currency_key = sprintf('%s->%s', $exp_reas_dat['currency'], $filters['currency']);
                                        if (!isset($currency_rates[$currency_key])) {
                                            $currency_rates[$currency_key] = Finance_Currencies::getRate($registry, $exp_reas_dat['currency'], $filters['currency']);
                                        }
                                        $final_results[$doc_id]['invoiced_amount'] += round($exp_reas_dat['subtotal_with_vat_with_discount'] * $currency_rates[$currency_key], 2);
                                        $final_results[$doc_id]['difference'] = $final_results[$doc_id]['total_with_vat'] - $final_results[$doc_id]['invoiced_amount'];
                                        $total_invoiced += round($exp_reas_dat['subtotal_with_vat_with_discount'] * $currency_rates[$currency_key], 2);
                                    }
                                    if (!array_key_exists($exp_reas_dat['id'], $final_results[$doc_id]['invoices'])) {
                                        $final_results[$doc_id]['invoices'][$exp_reas_dat['id']] = array(
                                            'id'          => $exp_reas_dat['id'],
                                            'invoice_num' => $exp_reas_dat['invoice_num'],
                                            'type_prefix' => ($exp_reas_dat['type']==PH_FINANCE_TYPE_EXPENSES_INVOICE ? $registry['translater']->translate('reports_exp_invoice_prefix') : $registry['translater']->translate('reports_exp_proforma_prefix')),
                                            'issue_date'  => $exp_reas_dat['issue_date'],
                                        );
                                    }
                                }
                            }
                        }
                    }

                    // calculate the total difference
                    $total_difference = $total_with_vat - $total_invoiced;
                }
            } else {
                $registry['messages']->setError($registry['translater']->translate('error_reports_complete_required_fields'));
            }

            $final_results['additional_options']['total'] = $total;
            $final_results['additional_options']['total_price'] = $total_price;
            $final_results['additional_options']['total_quantity'] = $total_quantity;
            $final_results['additional_options']['total_value'] = $total_value;
            $final_results['additional_options']['total_with_vat'] = $total_with_vat;
            $final_results['additional_options']['total_invoiced'] = $total_invoiced;
            $final_results['additional_options']['total_difference'] = $total_difference;

            // common formatting parameters for all pie charts
            $common_chart_params = array(
                'chart' => array(
                    'backgroundColor' => '#ffffff',
                    'borderWidth' => 0,
                    'height' => 250,
                    'style' => array(
                        'marginTop' => '15px',
                        'marginBottom' => '15px'
                    )
                ),
                'plotOptions' => array(
                    'pie' => array(
                        'startAngle' => 90,
                        'dataLabels' => array(
                            'formatter' => 'function() { return this.point.name + \', \' + Highcharts.numberFormat(this.point.percentage, 2) + \'%\'; }'
                        )
                    )
                ),
                'title' => array(
                    'text' => null
                ),
                'tooltip' => array(
                    'formatter' => 'function() { return this.point.name + \', \' + Highcharts.numberFormat(this.y, 2); }'
                )
            );
            $charts_index = 1;
            foreach ($graphs as $key => $chart_params) {
                $chart = new Chart($registry, 'pie', 'chart', $charts_index++);
                $chart_params['series'][0]['data'] = array_values($chart_params['series'][0]['data']);
                $chart_params = array_merge($chart_params, $common_chart_params);
                if ($chart->prepareChart($chart_params)) {
                    $final_results['additional_options']['charts'][] = $chart;
                }
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }
    }
?>
