<?php
    Class Dallbog_Cases_With_Claims Extends Reports {
        /**
         * Budget entities list
         */
        public static $add_vars_names = array();
        public static $add_vars_ids = array();
        public static $registry = array();

        public static function buildQuery(&$registry, $filters = array()) {
            // prepare final results
            self::$registry = $registry;
            self::prepareAddVars();
            
            $final_results = array();
            $cases = self::getCases($filters);
            $cases_ids = array_column($cases, 'id');
            $cases_rows = self::getCasesGTData($filters, $cases_ids);

            $rows_cases_reordered = array();

            // group by parent id
            foreach ($cases_rows as $rw) {
                if (!isset($rows_cases_reordered[$rw['id']])) {
                    $rows_cases_reordered[$rw['id']] = array();
                }
                $rows_cases_reordered[$rw['id']][$rw['row']] = $rw;
                unset($rows_cases_reordered[$rw['id']][$rw['row']]['id']);
            }
            $damage_ids = array_filter(array_column($cases_rows, 'damage_num'));
            $damage_info = self::getDamageData($damage_ids);

            $decision_info = self::getDecisionByCase($cases_ids);
            
            // prepare the final results
            $base_added_damage_row_structure = array(
                'damage_num'       => '',
                'damage_num_name'  => '',
                'status'           => '',
                'status_name'      => '',
                'case_price'       => '',
                'type_damage'      => '',
                'type_damage_name' => '',
                'law_interest'     => '',
                'moratorium_from'  => '',
                'moratorium_to'    => '',
                'moratorium_value' => '',
                'legal_expenses'   => '',
                'num'              => '',
            );
            $base_added_decision_row_structure = array(
                'decision_num' => '',
                'decision_date' => '',
                'entry_date' => '',
                'compensation' => '',
                'total_revenue' => '',
                'total_cost' => '',
                'pal_notes' => '',
            );

            foreach ($cases as $case) {
                $case_key = $case['id'];
                $dmg_keys = array();
                if (isset($rows_cases_reordered[$case['id']])) {
                    foreach ($rows_cases_reordered[$case['id']] as $cs_row) {
                        $current_row = $case + $cs_row;
                        if ($current_row['damage_num'] && isset($damage_info[$current_row['damage_num']])) {
                            $damage_row = $damage_info[$current_row['damage_num']];
                            $dmg_key = $current_row['damage_num'];
                            $dmg_keys[] = $dmg_key;
                            unset($damage_row['id']);
                            $current_row += $damage_row;
                        }
                        $final_results[sprintf('%d_%d', $case_key, $dmg_key)] = $current_row;
                    }
                } else {
                    $final_results[sprintf('%d_%d', $case_key, 0)] = $case + $base_added_damage_row_structure;
                    $dmg_keys[] = 0;
                }

                foreach ($dmg_keys as $dmg_key) {
                    if (isset($decision_info[$case['id']])) {
                        $final_results[sprintf('%d_%d', $case_key, $dmg_key)] += $decision_info[$case['id']];
                    } else {
                        $final_results[sprintf('%d_%d', $case_key, $dmg_key)] += $base_added_decision_row_structure;
                    }
                }
            }

            if (!empty($filters['paginate'])) {
                $results = array($final_results, 0);
            } else {
                $results = $final_results;
            }

            return $results;
        }


        /**
         * Function to get the needed additional variables
         *
         * @return void
         */
        public static function prepareAddVars() {
            // vars structure
            $add_vars_structure = array(
                'case_num' => 'VAR_CASE_NUM',
                'lawsuit' => 'VAR_LAWSUIT',
                'court' => 'VAR_COURT',
                'strength' => 'VAR_STRENGTH',
                'date_planned' => 'VAR_DATE_PLANNED',
                'type_insurance' => 'VAR_TYPE_INSURANCE',
                'policy_num' => 'VAR_POLICY_NUM',
                'event_type' => 'VAR_EVENT_TYPE',
                'damage_num' => 'VAR_DAMAGE_NUM',
                'damage_num_num' => 'VAR_DAMAGE_NUM_NUM',
                'case_price' => 'VAR_CASE_PRICE',
                'type_damage' => 'VAR_TYPE_DAMAGE',
                'legal_interest' => 'VAR_LEGAL_INTEREST',
                'law_interest' => 'VAR_LAW_INTEREST',
                'case_reason' => 'VAR_CASE_REASON',
                'company' => 'VAR_COMPANY',
                'company_role' => 'VAR_COMPANY_ROLE',
                'date_event' => 'VAR_DATE_EVENT',
                'moratorium_interest_from' => 'VAR_MORATORIUM_INTEREST_FROM',
                'moratorium_interest_to' => 'VAR_MORATORIUM_INTEREST_TO',
                'moratorium_value' => 'VAR_MORATORIUM_VALUE',
                'legal_expenses' => 'VAR_LEGAL_EXPENSES',
                'reserved_amount' => 'VAR_RESERVED_AMOUNT',
                'case_status' => 'VAR_CASE_STATUS',
                'decision_date' => 'VAR_DECISION_DATE',
                'entry_date' => 'VAR_ENTRY_DATE',
                'case_compensation' => 'VAR_CASE_COMPENSATION',
                'total_revenue' => 'VAR_TOTAL_REVENUE',
                'total_cost' => 'VAR_TOTAL_COST',
                'paid_amount' => 'VAR_PAID_AMOUNT',
                'expences_paid_amount' => 'VAR_EXPENCES_PAID_AMOUNT',
                'pal_notes' => 'VAR_PAL_NOTES',
                'partial_claim' => 'VAR_PARTIAL_CLAIM',
            );

            // get the vars
            foreach ($add_vars_structure as $vars_key => $ver_setting) {
                $vars_names = array();
                if (defined($ver_setting) && !empty(constant($ver_setting))) {
                    $vars_names = array_filter(preg_split('/\s*,\s*/', constant($ver_setting)));
                }
                $add_vars_structure[$vars_key] = $vars_names;
                self::$add_vars_ids[$vars_key] = array();
            }

            $vars_list = array_merge([], ...array_values($add_vars_structure));

            // get the ids of the vars
            $sql = 'SELECT `id`, `name` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `name` IN ("' . implode('","', $vars_list) . '")';
            $vars_ids = self::$registry['db']->GetAssoc($sql);

            foreach ($vars_ids as $var_id => $var_name) {
                foreach ($add_vars_structure as $var_key => $var_nms) {
                    if (!in_array($var_name, $var_nms)) {
                        continue;
                    }
                    if (!isset(self::$add_vars_ids[$var_key])) {
                        self::$add_vars_ids[$var_key] = array();
                    }
                    self::$add_vars_ids[$var_key][] = $var_id;
                }
            }

            self::$add_vars_names = $add_vars_structure;
            return;
        }

        /**
         * @param $filters
         * @return array
         */
        private static function getCases($filters): array {
            // prepare the sql
            $where = array();
            $where[] = 'p.active=1';
            $where[] = 'p.deleted_by=0';
            $projects_types = array_filter(preg_split('/\s*,\s*/', PROJECTS_TYPES));
            if (!empty($filters['customer'])) {
                $where[] = 'p.customer="' . $filters['customer'] . '"';
            }
            if (!empty($filters['manager'])) {
                $where[] = 'p.manager="' . $filters['manager'] . '"';
            }
            if (!empty($filters['project_type'])) {
                $projects_types = array_intersect($projects_types, array($filters['project_type']));
            }

            // rights pre sort
            $rights = self::$registry['currentUser']->get('rights');
            $current_user_id = self::$registry['currentUser']->get('id');
            $current_rights_where = array();
            foreach ($projects_types as $pt) {
                $current_right = isset($rights['projects' . $pt]['search']) ? $rights['projects' . $pt]['search'] : '';

                //additional 'where' for hiding not allowed models
                if ($current_user_id && $current_right) {
                    if ($current_right == 'all') {
                        if (self::$registry['currentUser']->get('is_portal')) {
                            if (self::$registry['currentUser']->get('default_customer')) {
                                $current_rights_where[] = "(((pa.assigned_to=" . $current_user_id . " AND pa.assignments_type='Users') OR p.manager='" . $current_user_id . "' OR p.added_by = '" . $current_user_id . "' OR p.customer='" . $registry['currentUser']->get('default_customer') . "') AND p.type='" . $pt . "')";
                            } else {
                                $current_rights_where[] = "0";
                            }
                        } else {
                            $current_rights_where[] = "p.type='" . $pt . "'";
                        }
                    } elseif ($current_right == 'mine') {
                        $current_rights_where[] = "(((pa.assigned_to=" . $current_user_id . " AND pa.assignments_type='Users') OR p.manager='" . $current_user_id . "' OR p.added_by = '" . $current_user_id . "') AND p.type='" . $pt . "')";
                    } elseif ($current_right == 'group') {
                        $user_groups = self::$registry['currentUser']->get('groups');
                        $current_rights_where[] = "((p.added_by=$current_user_id OR p.manager=$current_user_id" .
                            (count($user_groups) ? ' OR p.`group` IN (' . implode(',',
                                    $user_groups) . ')' : '') . ") AND p.type='" . $pt . "')";
                    } elseif ($current_right == 'none') {
                        $current_rights_where[] = "0";
                    }
                }
            }

            $p_ids = array();
            $sql = 'SELECT p.id FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' AS pa' . "\n" .
                   ' ON (pa.parent_id=p.id)' . "\n" .
                   'WHERE ' . implode(' OR ', $current_rights_where) . "\n";
            $p_ids = self::$registry['db']->GetCol($sql);
            $where[] = 'p.id IN ("' . implode('","', $p_ids) . '")';

            $sql = 'SELECT p.id, p.code, p.date_start, p.type, pti18n.name as type_name, ' . "\n" .
                   '       p.customer, CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name, ' . "\n" .
                   '       p.manager, CONCAT(ui18n.firstname, " ", ui18n.lastname) as manager_name, um.active as active_manager, ' . "\n" .
                   '       pcstm_case_num.value as case_num, pcstm_lawsuit.value as lawsuit_year, ' . "\n" .
                   '       law_y_i18n.name as lawsuit_year_name, pcstm_court.value as court, ' . "\n" .
                   '       CONCAT(court_i18n.name, " ", court_i18n.lastname) as court_name, ' . "\n" .
                   '       pcstm_status.value as status, status_i18n.name as status_name, ' . "\n" .
                   '       pcstm_strength.value as strength, strength_i18n.name as strength_name, ' . "\n" .
                   '       pcstm_date_planned.value as date_planned, pcstm_type_insurance.value as type_insurance, ' . "\n" .
                   '       fo_type_insurance.label as type_insurance_name, pcstm_policy_num.value as policy_num, ' . "\n" .
                   '       pcstm_case_reason.value as case_reason, pcstm_company.value as company, ' . "\n" .
                   '       CONCAT(company_i18n.name, " ", company_i18n.lastname) as company_name, ' . "\n" .
                   '       pcstm_company_role.value as company_role, fo_company_role.label as company_role_name, ' . "\n" .
                   '       pcstm_event_date.value as event_date, fo_partial_claim.label as partial_claim' . "\n" .
                   'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                   'INNER JOIN ' . DB_TABLE_PROJECTS_TYPES_I18N . ' AS pti18n' . "\n" .
                   ' ON (pti18n.parent_id=p.type AND pti18n.lang="' . self::$registry['lang'] . '" AND ' . implode(' AND ',
                       $where) . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                   ' ON (ci18n.parent_id=p.customer AND ci18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_USERS . ' AS um' . "\n" .
                   ' ON (um.id=p.manager)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                   ' ON (ui18n.parent_id=p.manager AND ui18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   (!empty($filters['case_num']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_case_num' . "\n" .
                   ' ON (pcstm_case_num.model_id=p.id AND pcstm_case_num.var_id IN ("' . implode('","',
                       self::$add_vars_ids['case_num']) . '")' . (!empty($filters['case_num']) ? ' AND pcstm_case_num.value LIKE "%' . $filters['case_num'] . '%"' : '') . ')' . "\n" .
                   (!empty($filters['year_case']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_lawsuit' . "\n" .
                   ' ON (pcstm_lawsuit.model_id=p.id AND pcstm_lawsuit.var_id IN ("' . implode('","',
                       self::$add_vars_ids['lawsuit']) . '")' . (!empty($filters['year_case']) ? ' AND pcstm_lawsuit.value="' . $filters['year_case'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS law_y_i18n' . "\n" .
                   ' ON (law_y_i18n.parent_id=pcstm_lawsuit.value AND ui18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   (!empty($filters['court']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_court' . "\n" .
                   ' ON (pcstm_court.model_id=p.id AND pcstm_court.var_id IN ("' . implode('","',
                       self::$add_vars_ids['court']) . '")' . (!empty($filters['court']) ? ' AND pcstm_court.value="' . $filters['court'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS court_i18n' . "\n" .
                   ' ON (court_i18n.parent_id=pcstm_court.value AND court_i18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_strength' . "\n" .
                   ' ON (pcstm_strength.model_id=p.id AND pcstm_strength.var_id IN ("' . implode('","',
                       self::$add_vars_ids['strength']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS strength_i18n' . "\n" .
                   ' ON (strength_i18n.parent_id=pcstm_strength.value AND strength_i18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_date_planned' . "\n" .
                   ' ON (pcstm_date_planned.model_id=p.id AND pcstm_date_planned.var_id IN ("' . implode('","',
                       self::$add_vars_ids['date_planned']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_type_insurance' . "\n" .
                   ' ON (pcstm_type_insurance.model_id=p.id AND pcstm_type_insurance.var_id IN ("' . implode('","',
                       self::$add_vars_ids['type_insurance']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_type_insurance' . "\n" .
                   ' ON (fo_type_insurance.option_value=pcstm_type_insurance.value AND fo_type_insurance.parent_name IN ("' . implode('","',
                       self::$add_vars_names['type_insurance']) . '"))' . "\n" .
                   (!empty($filters['policy_num']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_policy_num' . "\n" .
                   ' ON (pcstm_policy_num.model_id=p.id AND pcstm_policy_num.var_id IN ("' . implode('","',
                       self::$add_vars_ids['policy_num']) . '")' . (!empty($filters['policy_num']) ? ' AND pcstm_policy_num.value LIKE "%' . $filters['policy_num'] . '%"' : '') . ')' . "\n" .
                   (!empty($filters['status']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_status' . "\n" .
                   ' ON (pcstm_status.model_id=p.id AND pcstm_status.var_id IN ("' . implode('","',
                       self::$add_vars_ids['case_status']) . '")' . (!empty($filters['status']) ? ' AND pcstm_status.value="' . $filters['status'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS status_i18n' . "\n" .
                   ' ON (status_i18n.parent_id=pcstm_status.value AND status_i18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_case_reason' . "\n" .
                   ' ON (pcstm_case_reason.model_id=p.id AND pcstm_case_reason.var_id IN ("' . implode('","',
                       self::$add_vars_ids['case_reason']) . '"))' . "\n" .
                   (!empty($filters['company']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_company' . "\n" .
                   ' ON (pcstm_company.model_id=p.id AND pcstm_company.var_id IN ("' . implode('","',
                       self::$add_vars_ids['company']) . '")' . (!empty($filters['company']) ? ' AND pcstm_company.value="' . $filters['company'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS company_i18n' . "\n" .
                   ' ON (company_i18n.parent_id=pcstm_company.value AND company_i18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   (!empty($filters['company_role']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_company_role' . "\n" .
                   ' ON (pcstm_company_role.model_id=p.id AND pcstm_company_role.var_id IN ("' . implode('","',
                       self::$add_vars_ids['company_role']) . '")' . (!empty($filters['company_role']) ? ' AND pcstm_company_role.value="' . $filters['company_role'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_company_role' . "\n" .
                   ' ON (fo_company_role.option_value=pcstm_company_role.value AND fo_company_role.parent_name IN ("' . implode('","',
                       self::$add_vars_names['company_role']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_partial_claim' . "\n" .
                   ' ON (pcstm_partial_claim.model_id=p.id AND pcstm_partial_claim.var_id IN ("' . implode('","', self::$add_vars_ids['partial_claim']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_partial_claim' . "\n" .
                   ' ON (fo_partial_claim.option_value=pcstm_partial_claim.value AND fo_partial_claim.parent_name IN ("' . implode('","', self::$add_vars_names['partial_claim']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_event_date' . "\n" .
                   ' ON (pcstm_event_date.model_id=p.id AND pcstm_event_date.var_id IN ("' . implode('","',
                       self::$add_vars_ids['date_event']) . '"))' . "\n";

            if (!empty($filters['damage_num']) ||
                !empty($filters['type_damage']) ||
                !empty($filters['type_event'])) {
                $sql .= (!empty($filters['damage_num']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_damage_num' . "\n" .
                        ' ON (pcstm_damage_num.model_id=p.id AND pcstm_damage_num.var_id IN ("' . implode('","',
                            self::$add_vars_ids['damage_num']) . '")' . (!empty($filters['damage_num']) ? ' AND pcstm_damage_num.value="' . $filters['damage_num'] . '"' : '') . ')' . "\n" .
                        (!empty($filters['type_damage']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_type_damage' . "\n" .
                        ' ON (pcstm_type_damage.model_id=p.id AND pcstm_type_damage.var_id IN ("' . implode('","',
                            self::$add_vars_ids['type_damage']) . '") AND pcstm_type_damage.num=pcstm_damage_num.num' . (!empty($filters['type_damage']) ? ' AND pcstm_type_damage.value="' . $filters['type_damage'] . '"' : '') . ')' . "\n" .
                        (!empty($filters['type_event']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_event_type' . "\n" .
                        ' ON (pcstm_event_type.model_id=p.id AND pcstm_event_type.var_id IN ("' . implode('","',
                            self::$add_vars_ids['event_type']) . '") AND pcstm_type_damage.num=pcstm_event_type.num' . (!empty($filters['type_event']) ? ' AND pcstm_event_type.value="' . $filters['type_event'] . '"' : '') . ')' . "\n";
            }
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * @param $filters
         * @param array $cases_ids
         * @return array
         */
        private static function getCasesGTData($filters, array $cases_ids): array {
            if (empty($cases_ids)) {
                return array();
            }

            $sql = 'SELECT p.id, pcstm_damage_num.value as damage_num, pcstm_damage_num_num.value as damage_num_name, ' . "\n" .
                   '       pcstm_case_price.value as case_price, pcstm_type_damage.value as type_damage, fo_type_damage.label as type_damage_name,' . "\n" .
                   '       pcstm_legal_interest.value as legal_interest, pcstm_moratorium_from.value as moratorium_from, ' . "\n" .
                   '       pcstm_moratorium_to.value as moratorium_to, pcstm_moratorium_value.value as moratorium_value, ' . "\n" .
                   '       pcstm_legal_expenses.value as legal_expenses, pcstm_damage_num.num as `row`,' . "\n" .
                   '       pcstm_event_type.value as event_type, event_type_i18n.name as event_type_name' . "\n" .

                   'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                   (!empty($filters['damage_num']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_damage_num' . "\n" .
                   ' ON (pcstm_damage_num.model_id=p.id AND pcstm_damage_num.var_id IN ("' . implode('","',
                       self::$add_vars_ids['damage_num']) . '")' . (!empty($filters['damage_num']) ? ' AND pcstm_damage_num.value="' . $filters['damage_num'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_damage_num_num' . "\n" .
                   ' ON (pcstm_damage_num_num.model_id=p.id AND pcstm_damage_num_num.var_id IN ("' . implode('","',
                       self::$add_vars_ids['damage_num_num']) . '") AND pcstm_damage_num_num.num=pcstm_damage_num.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_case_price' . "\n" .
                   ' ON (pcstm_case_price.model_id=p.id AND pcstm_case_price.var_id IN ("' . implode('","',
                       self::$add_vars_ids['case_price']) . '") AND pcstm_case_price.num=pcstm_damage_num.num)' . "\n" .
                   (!empty($filters['type_damage']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_type_damage' . "\n" .
                   ' ON (pcstm_type_damage.model_id=p.id AND pcstm_type_damage.var_id IN ("' . implode('","',
                       self::$add_vars_ids['type_damage']) . '") AND pcstm_type_damage.num=pcstm_damage_num.num' . (!empty($filters['type_damage']) ? ' AND pcstm_type_damage.value="' . $filters['type_damage'] . '"' : '') . ')' . "\n" .
                   (!empty($filters['type_event']) ? 'INNER' : 'LEFT') . ' JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_event_type' . "\n" .
                   ' ON (pcstm_event_type.model_id=p.id AND pcstm_event_type.var_id IN ("' . implode('","',
                       self::$add_vars_ids['event_type']) . '") AND pcstm_event_type.num=pcstm_damage_num.num' . (!empty($filters['type_event']) ? ' AND pcstm_event_type.value="' . $filters['type_event'] . '"' : '') . ')' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS event_type_i18n' . "\n" .
                   ' ON (event_type_i18n.parent_id=pcstm_event_type.value AND event_type_i18n.lang="' . self::$registry['lang'] . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_FIELDS_OPTIONS . ' AS fo_type_damage' . "\n" .
                   ' ON (fo_type_damage.option_value=pcstm_type_damage.value AND fo_type_damage.parent_name IN ("' . implode('","',
                       self::$add_vars_names['type_damage']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_legal_interest' . "\n" .
                   ' ON (pcstm_legal_interest.model_id=p.id AND pcstm_legal_interest.var_id IN ("' . implode('","',
                       self::$add_vars_ids['legal_interest']) . '") AND pcstm_legal_interest.num=pcstm_damage_num.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_moratorium_from' . "\n" .
                   ' ON (pcstm_moratorium_from.model_id=p.id AND pcstm_moratorium_from.var_id IN ("' . implode('","',
                       self::$add_vars_ids['moratorium_interest_from']) . '") AND pcstm_moratorium_from.num=pcstm_damage_num.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_moratorium_to' . "\n" .
                   ' ON (pcstm_moratorium_to.model_id=p.id AND pcstm_moratorium_to.var_id IN ("' . implode('","',
                       self::$add_vars_ids['moratorium_interest_to']) . '") AND pcstm_moratorium_to.num=pcstm_damage_num.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_moratorium_value' . "\n" .
                   ' ON (pcstm_moratorium_value.model_id=p.id AND pcstm_moratorium_value.var_id IN ("' . implode('","',
                       self::$add_vars_ids['moratorium_value']) . '") AND pcstm_moratorium_value.num=pcstm_damage_num.num)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_PROJECTS_CSTM . ' AS pcstm_legal_expenses' . "\n" .
                   ' ON (pcstm_legal_expenses.model_id=p.id AND pcstm_legal_expenses.var_id IN ("' . implode('","',
                       self::$add_vars_ids['legal_expenses']) . '") AND pcstm_legal_expenses.num=pcstm_damage_num.num)' . "\n" .
                   'WHERE p.id IN (' . implode(',', $cases_ids) . ') AND pcstm_damage_num.value IS NOT NULL';
            return self::$registry['db']->GetAll($sql);
        }

        /**
         * @param array $damage_ids
         * @return array
         */
        private static function getDamageData(array $damage_ids): array {
            if (empty($damage_ids)) {
                return array();
            }
            $sql = 'SELECT d.id, dcstm_res_amount.value as reserved_amount, ' . "\n" .
                   '       dcstm_paid_amount.value as paid_amount, dcstm_exp_paid_amount.value as expences_paid_amount' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_res_amount' . "\n" .
                   ' ON (dcstm_res_amount.model_id=d.id AND dcstm_res_amount.var_id IN ("' . implode('","',
                       self::$add_vars_ids['reserved_amount']) . '") AND d.id IN (' . implode(',',
                       $damage_ids) . ') AND d.active=1 AND d.deleted_by=0)' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_paid_amount' . "\n" .
                   ' ON (dcstm_paid_amount.model_id=d.id AND dcstm_paid_amount.var_id IN ("' . implode('","',
                       self::$add_vars_ids['paid_amount']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_exp_paid_amount' . "\n" .
                   ' ON (dcstm_exp_paid_amount.model_id=d.id AND dcstm_exp_paid_amount.var_id IN ("' . implode('","',
                       self::$add_vars_ids['expences_paid_amount']) . '"))' . "\n";
            return self::$registry['db']->GetAssoc($sql);
        }

        /**
         * @param array $cases_ids
         * @return array
         */
        private static function getDecisionByCase(array $cases_ids): array {
            if (empty($cases_ids)) {
                return array();
            }
            $sql = 'SELECT d.id as decision, d.id as decision_id, d.project as case_id, d.full_num as decision_num, dcstm_decision_date.value as decision_date, ' . "\n" .
                   '       dcstm_entry_date.value as entry_date, dcstm_case_compensation.value as compensation, ' . "\n" .
                   '       dcstm_total_revenue.value as total_revenue, dcstm_total_cost.value as total_cost, ' . "\n" .
                   '       dcstm_pal_notes.value as pal_notes, dcstm_law_interest.value as law_interest ' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_decision_date' . "\n" .
                   ' ON (dcstm_decision_date.model_id=d.id AND dcstm_decision_date.var_id IN ("' . implode('","',
                       self::$add_vars_ids['decision_date']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_entry_date' . "\n" .
                   ' ON (dcstm_entry_date.model_id=d.id AND dcstm_entry_date.var_id IN ("' . implode('","',
                       self::$add_vars_ids['entry_date']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_case_compensation' . "\n" .
                   ' ON (dcstm_case_compensation.model_id=d.id AND dcstm_case_compensation.var_id IN ("' . implode('","',
                       self::$add_vars_ids['case_compensation']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_total_revenue' . "\n" .
                   ' ON (dcstm_total_revenue.model_id=d.id AND dcstm_total_revenue.var_id IN ("' . implode('","',
                       self::$add_vars_ids['total_revenue']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_total_cost' . "\n" .
                   ' ON (dcstm_total_cost.model_id=d.id AND dcstm_total_cost.var_id IN ("' . implode('","',
                       self::$add_vars_ids['total_cost']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_pal_notes' . "\n" .
                   ' ON (dcstm_pal_notes.model_id=d.id AND dcstm_pal_notes.var_id IN ("' . implode('","',
                       self::$add_vars_ids['pal_notes']) . '"))' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS dcstm_law_interest' . "\n" .
                   ' ON (dcstm_law_interest.model_id=d.id AND dcstm_law_interest.var_id IN ("' . implode('","',
                       self::$add_vars_ids['law_interest']) . '"))' . "\n" .
                   'WHERE d.id IN (SELECT MAX(d1.id) FROM documents d1 WHERE d1.type="20" AND d1.project IN (' . implode(',',
                       $cases_ids) . ') AND d1.active=1 AND d1.deleted_by=0 GROUP BY d1.project)' . "\n";
            $decision_rows = self::$registry['db']->GetAssoc($sql);

            $decision_info = array();
            foreach ($decision_rows as $des_rw) {
                if (!isset($decision_info[$des_rw['case_id']])) {
                    $decision_info[$des_rw['case_id']] = $des_rw;
                    unset($decision_info[$des_rw['case_id']]['case_id']);
                }
            }
            return $decision_info;
        }
    }

?>
