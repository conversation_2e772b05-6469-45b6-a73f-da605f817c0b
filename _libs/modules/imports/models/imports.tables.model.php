<?php

require_once (PH_PHPEXCEL_DIR . 'PHPExcel.php');

/**
 * Imports_Table model class
 */
class Imports_Table extends Model {
    /**
     * @var Registry $registry
     */
    public $registry;

    /**
     * Variable types that data can be imported into
     * @var array
     */
    private $allowedVariableTypes = array('text', 'textarea', 'dropdown', 'radio', 'date', 'datetime', 'time', 'autocompleter', 'file_upload');

    /**
     * Recommended file types for import
     * @var string
     */
    private $acceptFileTypes = '.xls, .xlsx, .ods';

    /**
     * @var PHPExcel
     */
    private $objPHPExcel;

    /**
     * Section key to store temporary import data into session with
     * @var string
     */
    const SESSION_PARAM = 'import_table';

    /**
     * Column value identifying that a fixed value should be imported for all rows
     * @var string
     */
    const FIXED_VALUE = 'value';

    /**
     * Names of properties of current model that identify grouping variable
     * that import is for
     * @var array
     */
    private $varParams = array('model', 'model_type', 'grouping', 'gt2');

    /**
     * Settings in source field of GT/GT2 variable that are used for import
     * @var array
     */
    private $sourceSettings = array(
        'row_num_var',
        'import_configurator_group',
        'import_default_value_.*',
        'import_fixed_value_.*',
        'import_plugin.*',
    );

    /**
     * Temporary files model id
     * @var string
     */
    private $tmpFileModelId = '';

    /**
     * Constructor
     *
     * @param Registry $registry - the main registry
     * @param string $params - array expected, but param is defined as string in model
     */
    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        $lang_file = PH_MODULES_DIR . 'imports/i18n/' . $registry['lang'] . '/imports.ini';
        if (!$registry['translater']->isLoadedFile($lang_file, 'module')) {
            $registry['translater']->loadFile($lang_file);
        }
    }

    /**
     * Destructor
     */
    public function __destruct() {
        // do some cleanup
        $this->removeOldImportTempFiles();

        // remove object property if it is still present (if persist_phpexcel is set)
        $this->unloadPHPExcel();
    }

    /**
     * Prepare initial data for lightbox for import from file into grouping table
     *
     * @return mixed[] - prepared form content and title or errors on failure
     */
    public function showForm() {
        $registry = &$this->registry;

        $result = array();
        $vars = $this->getTableVars();

        if ($vars) {
            $viewer = new Viewer($registry, false, array('module' => $this->getModule()));
            $viewer->setFrameset($viewer->templatesDir . 'tables_form.html');

            // set current address as action of form
            $viewer->data['submitLink'] = $_SERVER['REQUEST_URI'];
            // recommended file types
            $viewer->data['accept'] = $this->acceptFileTypes;

            $var_options = array();
            $unset_keys = array_flip(array(
                'agregate',
                'auditable',
                'back_label',
                'bb',
                'calculate',
                'configurator',
                'description',
                'grouping',
                'gt2',
                'height',
                'help',
                'id',
                'js_methods',
                'layout_id',
                'layout_visible',
                'max_num',
                'model',
                'model_id',
                'model_type',
                'multiadd',
                'multiedit',
                'multilang',
                'outlooks',
                'place',
                'position',
                'required',
                'searchable',
                'sortable',
                'source',
                'table',
                'validate',
                'width_print',
            ));
            $unset_var_names = array();
            $var_names = array_keys($vars);
            foreach ($vars as $var_name => &$var) {
                if (!$var['hidden'] && in_array($var['type'], $this->allowedVariableTypes) && $var['name'] != $this->get('row_num_var')) {
                    $var_options[] = array(
                        'option_value' => $var['name'],
                        'label' => $var['label'],
                        'class_name' => $var['type'],
                    );
                    $var['custom_name'] = 'column_value';
                    $var['value'] = '';
                    if ($this->isDefined("import_fixed_value_{$var_name}")) {
                        // fixed value
                        $var['value'] = $this->get("import_fixed_value_{$var_name}");
                        $var['fixed'] = 1;
                    } elseif ($this->isDefined("import_default_value_{$var_name}")) {
                        // default value
                        $var['value'] = $this->get("import_default_value_{$var_name}");
                    }
                    // set new lines
                    $var['value'] = preg_replace('#(\\\[nr])+#', "\n", $var['value']);
                    $var['width'] = '';
                    if ($var['type'] == 'autocompleter') {
                        // try to find id value by visible value
                        if ($var['value']) {
                            $autocomplete_values = array($var_name => $var['value']);
                            $this->prepareAutocompleteValues($var_name, $vars, $autocomplete_values);
                            if (!empty($var['autocomplete']['id_var']) && !empty($autocomplete_values[$var['autocomplete']['id_var']])) {
                                $var['value'] = $autocomplete_values[$var_name];
                                $var['value_id'] = $autocomplete_values[$var['autocomplete']['id_var']];
                            } else {
                                $var['value'] = $var['value_id'] = '';
                            }
                        }
                        $var['width'] = '178';
                        $fill_options = array();
                        if (!empty($var['autocomplete']['fill_options'])) {
                            foreach ($var['autocomplete']['fill_options'] as $opt) {
                                // keep the same format of fill options as specified in variable
                                if (preg_match('#^\$' . $var['name'] . '\s*=>#', $opt)) {
                                    $fill_options[] = str_replace('$' . $var['name'], '$' . $var['custom_name'] . '_autocomplete', $opt);
                                    $fill_options[] = str_replace('$' . $var['name'], '$' . $var['custom_name'] . '_oldvalue', $opt);
                                    $fill_options[] = '$' . $var['custom_name'] . ' => <id>';
                                    break;
                                }
                            }
                        }
                        $var['autocomplete']['fill_options'] = $fill_options;
                        $var['autocomplete']['id_var'] = $var['custom_name'];

                        //remove same-table filters which are fetched from the form/request (beginning with $)
                        if (!empty($var['autocomplete']['filters'])) {
                            $var['autocomplete']['filters'] = array_filter($var['autocomplete']['filters'], function($v) use ($var_names) {
                                return !preg_match('#\$(' . implode('|', $var_names) . ')\b#', trim($v));
                            });
                        }
                        //set empty values to same-table plugin params which are fetched from the form/request (beginning with $)
                        if ($var['autocomplete']['type'] == 'autocompleters' && !empty($var['autocomplete']['plugin_params'])) {
                            foreach ($var['autocomplete']['plugin_params'] as $ppk => $ppv) {
                                if (!in_array($ppk, array('sql', 'search', 'rights', 'unique_field')) && preg_match('#^\$(' . implode('|', $var_names) . ')\b#', $ppv)) {
                                    $var['autocomplete']['plugin_params'][$ppk] = '';
                                }
                            }
                        }

                        // Custom javascript executed after initAutocompleter:
                        // change scope in order to be able to use values of underlying form fields (autocompleter is in lightbox)
                        // clear var_type because when set to basic it triggers use of the default saved filters in filter lighbox
                        $var['autocomplete']['custom_javascript'] = "autocomplete.scope = ''; delete autocomplete.var_type;";

                        // remove all buttons but clear
                        $var['autocomplete']['buttons_hide'] = 'search refresh add edit report';
                        $var['autocomplete']['clear'] = '1';

                        // autocomplete parameters that should be removed
                        foreach (array('button_menu', 'view_mode', 'view_mode_url', 'on_select', 'execute_after', 'clear_fields') as $key) {
                            if (array_key_exists($key, $var['autocomplete'])) {
                                unset($var['autocomplete'][$key]);
                            }
                        }

                    } elseif ($var['type'] == 'radio') {
                        $var['type'] = 'dropdown';
                    }
                    foreach ($var as $key => $value) {
                        if (is_numeric($value) && $key != 'value') {
                            $var[$key] = floatval($value);
                        }
                    }
                    $var = array_diff_key($var, $unset_keys);
                } else {
                    $unset_var_names[] = $var_name;
                }
            }
            unset($var);

            if ($unset_var_names) {
                $vars = array_diff_key($vars, array_flip($unset_var_names));
            }

            $column_options = array();
            $col = 0;
            while ($col < count($var_options)) {
                $column_options[$col] = PHPExcel_Cell::stringFromColumnIndex($col);
                $column_options[$col] = array(
                    'option_value' => $column_options[$col],
                    'label' => $column_options[$col],
                );
                $col++;
            }
            $column_options[] = array(
                'option_value' => self::FIXED_VALUE,
                'label' => $this->i18n('value'),
            );

            $viewer->data['vars'] = $vars;
            $viewer->data['var_options'] = $var_options;
            $viewer->data['column_options'] = $column_options;

            // prepare import configurators
            if ($this->get('import_configurator_group')) {
                $viewer->data['config_templates'] = Imports_Tables_Configurators::getTemplatesOptgroups(
                    $registry,
                    $this->getVarParams()
                );
                // apply selected configurator, if reloading form from config load action
                if ($this->get('config_id')) {
                    $config_params = array('id' => $this->get('config_id')) + $this->getVarParams();
                    $importsTablesConfig = Imports_Tables_Configurators::searchOne(
                        $registry,
                        array(
                            'where' => array_map(
                                function($v, $k) { return "itc.$k = '$v'"; },
                                General::slashesEscape($config_params),
                                array_keys($config_params)
                            ),
                            'sanitize' => true,
                        )
                    );
                    if ($importsTablesConfig) {
                        $viewer->data['config_data'] = $importsTablesConfig->getParams();
                        $viewer->data['config_id'] = $importsTablesConfig->get('id');
                        $viewer->data['user_id'] = $importsTablesConfig->get('user_id');

                        // make sure that column options are as many as necessary for saved configuration (even if file does not have that many filled columns)
                        if (!empty($viewer->data['config_data']['column_file'])) {
                            $max_col_idx = 0;
                            foreach ($viewer->data['config_data']['column_file'] as $col_string) {
                                if ($col_string !== '' && $col_string !== self::FIXED_VALUE) {
                                    $col_idx = PHPExcel_Cell::columnIndexFromString($col_string);
                                    if ($col_idx > $max_col_idx) {
                                        $max_col_idx = $col_idx;
                                    }
                                }
                            }
                            // count number of column options (subtract fixed option)
                            $count_opt = count($viewer->data['column_options']) - 1;
                            if ($max_col_idx > $count_opt) {
                                $column_options = $viewer->data['column_options'];
                                $fixed_opt = $column_options[$count_opt];
                                unset($column_options[$count_opt]);

                                $col = $count_opt;
                                while ($col < $max_col_idx) {
                                    $column_options[$col] = PHPExcel_Cell::stringFromColumnIndex($col);
                                    $column_options[$col] = array(
                                        'option_value' => $column_options[$col],
                                        'label' => $column_options[$col],
                                    );
                                    $col++;
                                }

                                $column_options[] = $fixed_opt;
                                $viewer->data['column_options'] = $column_options;
                            }
                        }

                        // if there is а selected file, update labels
                        if ($this->get('import_key') && $registry['session']->isRequested($this->get('import_key'), self::SESSION_PARAM)) {
                            $viewer->data['import_key'] = $this->get('import_key');
                            $import_data = $registry['session']->get($this->get('import_key') . '_data', self::SESSION_PARAM) ?: array();
                            if (array_key_exists('columns', $import_data)) {
                                foreach ($viewer->data['column_options'] as &$option) {
                                    if ($option['option_value'] != self::FIXED_VALUE && array_key_exists($option['option_value'], $import_data['columns'])) {
                                        $option['label'] .= ' ' . $import_data['columns'][$option['option_value']];
                                    }
                                }
                                unset($option);
                            }
                            // if configurator rows are saved as empty, set previous form values from form (request)
                            if (empty($viewer->data['config_data']['import_first_row']) && $this->get('import_first_row')) {
                                $viewer->data['config_data']['import_first_row'] = $this->get('import_first_row');
                            }
                            if (empty($viewer->data['config_data']['import_last_row']) && $this->get('import_last_row')) {
                                $viewer->data['config_data']['import_last_row'] = $this->get('import_last_row');
                            }
                        } else {
                        }
                    } else {
                        return array(
                            'errors' => array($this->i18n('error_imports_tables_configurator_load'))
                        );
                    }
                }
            }

            $result = array(
                'content' => $viewer->fetch(),
                'title' => $this->i18n('import_table'),
            );
        } else {
            $result['errors'] = array($this->i18n('no_data'));
        }

        return $result;
    }

    /**
     * Gets set of properties (and their values) that identify grouping variable that import is for
     *
     * @return array - property names of keys, property values as values
     */
    public function getVarParams() {
        $params = array();
        foreach ($this->varParams as $key) {
            $params[$key] = $this->get($key) ?: '';
        }
        return $params;
    }

    /**
     * Prepares available fields (additional variables) for import (directly
     * updatable) for model and grouping variable as dropdown options
     *
     * @return string[][] - array of dropdown options, name as value, label as label
     */
    private function getVarOptions() {
        $registry = &$this->registry;
        $modelName = $this->get('model');
        $var_options = array();
        if (!empty($modelName) && class_exists($modelName) && $this->get('model_type') > 0) {
            /** @var Model $model */
            $model = new $modelName($registry, array('type' => (int)$this->get('model_type')));
            $table = array();
            if ($this->get('gt2')) {
                $table = $model->getGT2Vars();
            } elseif ($this->get('grouping') && $this->get('grouping') > 0) {
                $table = array('vars' => array());
                $grouping = $this->get('grouping');
                foreach ($model->getGroupingFields() as $var) {
                    if ($var['grouping'] == $grouping) {
                        if ($var['type'] == 'group') {
                            if (!empty($var['source'])) {
                                $var = $var + General::parseSettings($var['source'], '#^row_num_var$#');
                            }
                            $table = $table + $var;
                        } else {
                            $table['vars'][$var['name']] = $var;
                        }
                    }
                }
            }
            if ($table && !empty($table['vars'])) {
                $row_num_var = !empty($table['row_num_var']) ? $table['row_num_var'] : '';
                foreach ($table['vars'] as $var) {
                    if (!$var['hidden'] && in_array($var['type'], $this->allowedVariableTypes) && $var['name'] != $row_num_var) {
                        $var_options[] = array(
                            'option_value' => $var['name'],
                            'label' => $var['label'],
                        );
                    }
                }
            }
            unset($model);
        }

        return $var_options;
    }

    /**
     * Prepares updatable variables for a GT or GT2 table.
     * Sets the whole GT/GT2 variable (all vars) and import-related flags from
     * source into model.
     *
     * @return mixed[][] - grouping variables, variable name as key; grouping variable at key "0"
     */
    public function getTableVars() {
        $registry = &$this->registry;
        $modelName = $this->get('model');
        $vars = array();

        if (!empty($modelName) && class_exists($modelName) && $this->get('model_type') > 0) {
            $gov = $registry->get('get_old_vars');
            if (!$gov) {
                $registry->set('get_old_vars', true, true);
            }
            // change routing params while preparing variable
            $real_action = $registry['action'];
            $form_action = $this->get('form_action') ?: ($this->get('id') ? 'edit' : 'add');
            $registry->set('action', $form_action, true);

            /** @var Model $model */
            $model = Model_Factory::buildFromRequest($registry, $modelName);
            if (!$model->get('type')) {
                $model->set('type', (int)$this->get('model_type'), true);
            }
            if ($this->get('gt2')) {
                $printform = $modelName == 'Finance_Incomes_Reason' && $this->get('printform');
                $table = $model->getGT2Vars($printform);
                if ($table && !empty($table['vars'])) {
                    foreach ($table['vars'] as $var) {
                        if (in_array($var['type'], $this->allowedVariableTypes)) {
                            $vars[$var['name']] = $var;
                        }
                    }
                    // set import settings into current model
                    $this->setSourceSettings($table['source']);
                }
                // store the whole GT2 variable in current model
                $this->set('grouping_table_2', $table, true);
            } elseif ($this->get('grouping') && $this->get('grouping') > 0) {
                $records = $model->getGroupingFields();
                $grouping = $this->get('grouping');
                foreach ($records as $idx => $var) {
                    if ($var['grouping'] != $grouping) {
                        unset($records[$idx]);
                    }
                }
                $records = array_values($records);
                // prepare variable according to type and "source" settings
                $model->processFields($records);
                // prepare variable validation (used to define if numeric)
                $model->processFieldsForJSFM($records);
                foreach ($records as $var) {
                    if ($var['type'] == 'group') {
                        // set import settings into current model
                        $this->setSourceSettings($var['source']);
                    } elseif (in_array($var['type'], $this->allowedVariableTypes)) {
                        $vars[$var['name']] = $var;
                    }
                }
                // store the whole grouping variable (as an indexed array of variables) in current model
                $this->set('group_vars', $records, true);
            }
            if (!$gov) {
                $registry->remove('get_old_vars');
            }
            $registry->set('action', $real_action, true);
        }

        return $vars;
    }

    /**
     * Extracts import settings from source and sets them into current model
     *
     * @param string|array $source - source of GT/GT2 variable
     */
    private function setSourceSettings($source = '') {
        $regexp = '#^(' . implode('|', $this->sourceSettings) . ')$#';
        if (is_string($source)) {
            $source = General::parseSettings($source, $regexp);
        } elseif (is_array($source)) {
            $source = array_intersect_key(
                $source,
                array_flip(
                    array_filter(
                        array_keys($source),
                        function($a) use ($regexp) {
                            return preg_match($regexp, $a);
                        }
                    )
                )
            );
        } else {
            $source = array();
        }
        // set import settings into model
        foreach ($source as $setting => $value) {
            $this->set($setting, $value, true);
        }
    }

    /**
     * Loads PHPExcel from file
     *
     * @param string $filename - path to file
     * @param bool $data_only - if true ignore formatting and read data only
     * @throws Exception
     * @return PHPExcel
     */
    private function loadPHPExcel($filename, $data_only = true) {
        /** @var PHPExcel $objPHPExcel */
        $objPHPExcel = null;
        if ($filename) {
            try {
                // the factory will select the appropriate reader
                $objReader = PHPExcel_IOFactory::createReaderForFile($filename);
                // these readers have no specifics in canRead and could handle any file format - we don't want that
                if ($objReader instanceof PHPExcel_Reader_CSV || $objReader instanceof PHPExcel_Reader_HTML) {
                    throw new Exception('Unable to identify a reader for this file');
                }
                if ($data_only && method_exists($objReader, 'setReadDataOnly')) {
                    $objReader->setReadDataOnly(true);
                }
                $objPHPExcel = $objReader->load($filename);
            } catch (Exception $e) {

            }
        }
        return $objPHPExcel;
    }

    /**
     * Remove PHPExcel object property
     */
    private function unloadPHPExcel() {
        if ($this->objPHPExcel && is_object($this->objPHPExcel) && is_a($this->objPHPExcel, 'PHPExcel')) {
            $this->objPHPExcel->disconnectWorksheets();
        }
        $this->objPHPExcel = null;
    }

    /**
     * Saves uploaded file to temporary location and stores it into session.
     * Prepares additional data to show into lightbox after file selection.
     *
     * @param array $file - uploaded file from $_FILES
     * @param bool $save_data_into_session - whether to store the read structural info into session
     * @return array[] - prepared data or errors on failure
     */
    public function importTempFile(array $file, $save_data_into_session = false) {
        $result = array();
        $errors = array();
        $registry = &$this->registry;

        $import_key = $this->get('import_key') ?: '';
        if ($import_key) {
            $this->removeImportTempFile($import_key);
            $import_key = '';
        }
        $tmp_file_name = '';

        if (empty($file) || !empty($file['error'])) {
            if (empty($file) || $file['error'] == UPLOAD_ERR_NO_FILE) {
                $errors[] = $this->i18n('error_attachments_file');
            } elseif ($file['error'] == UPLOAD_ERR_INI_SIZE) {
                $errors[] = $this->i18n('error_file_greater_filesize', array(General::convertFileSize(General::convertBytes(ini_get('upload_max_filesize')))));
            } else {
                $errors[] = $this->i18n('error_file_upload_failed', array($file['name']));
            }
        } else {
            // create a copy file with 'name' because the extension cannot be identified from 'tmp_name'
            $tmp_file_name = $this->saveImportTempFile($file, $import_key);
            if (!$tmp_file_name) {
                $errors[] = $this->i18n('error_file_upload_failed', array($file['name']));
                // add errors from FilesLib library
                if ($this->registry['messages']->getErrors()) {
                    $errors = array_merge($errors, array_values($this->registry['messages']->getErrors()));
                    $this->registry['messages']->unset_vars('errors');
                }
            } else {
                $this->objPHPExcel = $this->loadPHPExcel(PH_IMPORTS_CACHE_DIR . $tmp_file_name, false);
                if (!$this->objPHPExcel) {
                    $errors[] = $this->i18n('error_file_not_allowed_extension', array($this->acceptFileTypes));
                }
            }

            if (empty($this->objPHPExcel)) {
                // remove file immediately
                if (!empty($tmp_file_name)) {
                    $this->removeImportTempFile($import_key);
                    $import_key = '';
                }
            } elseif (!$this->objPHPExcel->getSheetCount()) {
                $errors[] = $this->i18n('no_data');
            } else {
                $this->objPHPExcel->setActiveSheetIndex(0);
                $worksheet = $this->objPHPExcel->getActiveSheet();

                // get last column and row
                $last_column = $worksheet->getHighestDataColumn();
                $last_row = $worksheet->getHighestDataRow();
                // get merged cells
                $merge_cells = $worksheet->getMergeCells();
                foreach ($merge_cells as &$mc) {
                    $mc = PHPExcel_Cell::extractAllCellReferencesInRange($mc);
                }
                unset($mc);

                // try to identify the column label row
                $row = 1;
                $row_values = $prev_row_values = array();
                $filterFunction = function($a) {
                    return !empty($a) && !is_numeric($a);
                };
                while ($row < $last_row) {
                    $row_range = "A{$row}:{$last_column}{$row}";
                    $row_range_cells = PHPExcel_Cell::extractAllCellReferencesInRange($row_range);

                    $row_values = $worksheet->rangeToArray($row_range, null, true, true, true);
                    $row_values = reset($row_values);
                    $num_values = count(array_filter($row_values, $filterFunction));

                    if (count($row_values) == $num_values) {
                        // assume this is the column label row and exit
                        break;
                    } else {
                        $row++;
                    }
                }
                if ($row == $last_row) {
                    $row = 1;
                    while ($row < $last_row) {
                        $row_range = "A{$row}:{$last_column}{$row}";
                        $row_range_cells = PHPExcel_Cell::extractAllCellReferencesInRange($row_range);

                        $row_values = $worksheet->rangeToArray($row_range, null, true, true, true);
                        $row_values = reset($row_values);
                        $num_values = count(array_filter($row_values, $filterFunction));

                        if (array_filter(array_map(function($mc) use ($row_range_cells) { return array_intersect($row_range_cells, $mc); }, $merge_cells))) {
                            // row contains merged cells - assume it is some title above the data
                            $row++;
                            continue;
                        } elseif ($prev_row_values && count($prev_row_values) >= $num_values) {
                            // assume that previous row is the column label row and exit
                            $row_values = $prev_row_values;
                            $row--;
                            break;
                        } else {
                            // if the first $num_values cells have values - assume that maybe this is the column label row
                            if ($num_values > 0 && count(array_filter(array_slice($row_values, 0, $num_values), $filterFunction)) === $num_values) {
                                $prev_row_values = array_slice($row_values, 0, $num_values);
                            } else {
                                $prev_row_values = array();
                            }
                            $row_values = array();
                            $row++;
                        }
                    }
                }

                if (!$this->get('persist_phpexcel')) {
                    $this->unloadPHPExcel();
                }

                if ($row_values && $row < $last_row) {
                    // we have found the column label row
                    $column_names = $row_values;
                    $first_row = $row + 1;
                } else {
                    // we don't know which one the column label row is
                    $col = 0;
                    $last_column_index = PHPExcel_Cell::columnIndexFromString($last_column);
                    $column_names = array();
                    while ($col < $last_column_index) {
                        $column_names[$col] = PHPExcel_Cell::stringFromColumnIndex($col);
                        $col++;
                    }
                    $column_names = array_combine($column_names, $column_names);
                    $first_row = 1;
                }
                if ($this->get('upload_via_ajax')) {
                    // add custom option for interface
                    $column_names[self::FIXED_VALUE] = $this->i18n('value');
                }

                $result = array(
                    'columns' => $column_names,
                    'first_row' => $first_row,
                    'last_row' => $last_row,
                );

                if ($save_data_into_session) {
                    // different from result, so set array elements one by one
                    $data = array(
                        'import_first_row' => $result['first_row'],
                        'import_last_row' => $result['last_row'],
                        'column_file' => array_keys($result['columns']),
                        'columns' => $result['columns'],
                    );
                    $this->saveImportTempFileData($import_key, $data);
                }
            }
        }
        // always return in result
        $result['import_key'] = $import_key;

        if ($errors) {
            $result['errors'] = $errors;
        }

        return $result;
    }

    /**
     * Get the uploaded file name as selected by the user
     *
     * @param string $import_key - session key for previously selected file for current import or an empty string
     * @return string - name of uploaded file as selected by the user
     */
    public function getImportOriginalFileName($import_key) {
        $session = &$this->registry['session'];
        if ($session->isRequested($import_key, self::SESSION_PARAM)) {
            $import_temp_file = $session->get($import_key, self::SESSION_PARAM);
            if ($import_temp_file) {
                return preg_replace('#.*____(.*)$#', '$1', $import_temp_file);
            }
        }

        return '';
    }

    /**
     * Restores import file into $_FILES array
     *
     * @param string $import_key - session key for previously selected file for current import or an empty string
     */
    public function restoreImportFile($import_key) {
        $session = &$this->registry['session'];
        if ($session->isRequested($import_key, self::SESSION_PARAM)) {
            $import_temp_file = $session->get($import_key, self::SESSION_PARAM);
            if ($import_temp_file) {
                $import_temp_file_path = PH_IMPORTS_CACHE_DIR . $import_temp_file;
                $_FILES['uploaded_file'] = array(
                    'tmp_name' => $import_temp_file_path,
                    'name' => preg_replace('#.*____(.*)$#', '$1', $import_temp_file),
                    'size' => filesize($import_temp_file_path),
                    'type' => mime_content_type($import_temp_file_path)
                );
            }
        }
    }

    /**
     * Uploads temporary file and sets its name into session data
     *
     * @param array $file - file array from $_FILES
     * @param string $import_key - session key for previously selected file for current import or an empty string
     * @return string - name of uploaded file on success or empty sting on failure
     */
    public function saveImportTempFile(array $file, &$import_key = '') {
        $tmp_file_name = '';
        if (!empty($file) && !empty($file['tmp_name']) && !empty($file['name']) && file_exists($file['tmp_name'])) {
            if ($import_key) {
                // remove previous file for upload
                $this->removeImportTempFile($import_key);
            }
            // define a new key
            $import_key = pathinfo($file['tmp_name'], PATHINFO_BASENAME);

            $tmp_file_name = implode('____', array(self::SESSION_PARAM, $import_key, $file['name']));

            // first make sure directory exists and is writable;
            // create a copy file with 'name' because the extension cannot be identified from 'tmp_name'
            if (FilesLib::createDir(PH_IMPORTS_CACHE_DIR) && FilesLib::uploadFile($file, PH_IMPORTS_CACHE_DIR, $tmp_file_name)) {
                /** @var Session $session */
                $session = &$this->registry['session'];
                $session->set($import_key, $tmp_file_name, self::SESSION_PARAM, true);
            } else {
                $tmp_file_name = '';
                // preserve error messages from FilesLib
                if (FilesLib::$_errors) {
                    foreach (FilesLib::$_errors as $error) {
                        $this->registry['messages']->setError($error);
                    }
                    FilesLib::$_errors = array();
                }
            }
        }
        return $tmp_file_name;
    }

    /**
     * Saves structural information read from loading of file into a separate
     * key so that it is available for next form submit in automated import.
     *
     * @param string $import_key - session key under which file path is stored
     * @param array $data - structural data (cols, rows) for first worksheet
     * @return boolean - result of the operation
     */
    public function saveImportTempFileData($import_key, array $data) {
        /** @var Session $session */
        $session = &$this->registry['session'];
        if ($session->get($import_key, self::SESSION_PARAM) && !empty($data)) {
            $session->set($import_key . '_data', $data, self::SESSION_PARAM, true);
            return true;
        }
        return false;
    }

    /**
     * Sets structural information for current import_key from session into
     * import model.
     */
    public function setImportTempFileDataFromSession() {
        // prepare default blank values
        $result = array(
            'import_first_row' => 0,
            'import_last_row' => 0,
            'column_file' => array(),
        );
        $data = array();
        /** @var Session $session */
        $session = &$this->registry['session'];
        if ($this->get('import_key') && $session->get(self::SESSION_PARAM)) {
            $import_key = $this->get('import_key');

            $data = $session->get($import_key . '_data', self::SESSION_PARAM);

            // load corresponding file into PHPExcel
            $import_temp_file = '';
            if ($session->isRequested($import_key, self::SESSION_PARAM)) {
                $import_temp_file = $session->get($import_key, self::SESSION_PARAM);
                if ($import_temp_file) {
                    $import_temp_file = PH_IMPORTS_CACHE_DIR . $import_temp_file;
                }
            }
            if ($import_temp_file) {
                $this->objPHPExcel = $this->loadPHPExcel($import_temp_file, false);
            } else {
                $this->objPHPExcel = null;
            }
        }
        if (!empty($data) && is_array($data)) {
            $result = array_merge($result, array_intersect_key($data, $result));
        }
        foreach ($result as $key => $value) {
            $this->set($key, $value, true);
        }
    }

    /**
     * Removes temporary file for import from session data and from temporary
     * location on server
     *
     * @param string $import_key - session key for current import
     * @return bool - true on success, false if key param invalid or not found in session
     */
    public function removeImportTempFile($import_key) {
        /** @var Session $session */
        $session = &$this->registry['session'];
        if ($import_key && $session->get(self::SESSION_PARAM) && $session->isRequested($import_key, self::SESSION_PARAM)) {
            $import_temp_file = $session->get($import_key, self::SESSION_PARAM);
            $session->remove($import_key, self::SESSION_PARAM);
            $session->remove($import_key . '_data', self::SESSION_PARAM);
            if ($this->get('import_key') == $import_key) {
                $this->set('import_key', '', true);
            }

            if ($import_temp_file && file_exists(PH_IMPORTS_CACHE_DIR . $import_temp_file) && is_file(PH_IMPORTS_CACHE_DIR . $import_temp_file)) {
                unlink(PH_IMPORTS_CACHE_DIR . $import_temp_file);
            }
            return true;
        }
        return false;
    }

    /**
     * Deletes files from imports upload folder: older than three days
     * or older than one hour which are set in current user's session
     */
    public function removeOldImportTempFiles() {
        $file_list = FilesLib::readDir(PH_IMPORTS_CACHE_DIR, false, 'files_only');
        $session_file_list = array_filter(
            $this->registry['session']->get(self::SESSION_PARAM) ?: array(),
            function($a) { return is_string($a); }
        );

        $current_time = time();
        foreach ($file_list as $file) {
            if (strpos($file, self::SESSION_PARAM . '____') !== 0) {
                // not uploaded using current model
                continue;
            }
            $file_path = PH_IMPORTS_CACHE_DIR . $file;

            // very old file
            if ($current_time - filemtime($file_path) > (3600*24*3)) {
                unlink($file_path);
            }
        }
        foreach ($session_file_list as $import_key => $file) {
            $file_path = PH_IMPORTS_CACHE_DIR . $file;
            // remove old file from own session
            if (!file_exists($file_path) || $current_time - filemtime($file_path) > (3600)) {
                $this->removeImportTempFile($import_key);
            }
        }
    }

    /**
     * Validates data before import, if valid sets PHPExcel object as property
     * of current model
     *
     * {@inheritDoc}
     * @see Model::validate()
     * @return bool - result of validation
     */
    public function validate($action = '') {
        /** @var Session $session */
        $session = &$this->registry['session'];

        $import_key = $this->get('import_key');
        $import_temp_file = '';
        if ($import_key && $session->get(self::SESSION_PARAM) && $session->isRequested($import_key, self::SESSION_PARAM)) {
            $import_temp_file = $session->get($import_key, self::SESSION_PARAM);
            if ($import_temp_file) {
                $import_temp_file = PH_IMPORTS_CACHE_DIR . $import_temp_file;
            }
        }

        if (!$import_temp_file || !file_exists($import_temp_file) || !is_readable($import_temp_file)) {
            $this->raiseError('error_file_doesnot_exist');
        } else {
            if (empty($this->objPHPExcel)) {
                // load data with formatting
                $this->objPHPExcel = $this->loadPHPExcel($import_temp_file, false);
            }
            if (!$this->objPHPExcel) {
                $this->raiseError('error_file_not_allowed_extension', '', 0, array($this->acceptFileTypes));
            }
            if (!empty($this->objPHPExcel)) {
                if (!$this->objPHPExcel->getSheetCount()) {
                    $this->raiseError('no_data');
                    if (!$this->get('persist_phpexcel')) {
                        $this->unloadPHPExcel();
                    }
                }
            }
        }

        $first_row = (int)$this->get('import_first_row');
        $last_row = (int)$this->get('import_last_row');
        if (!$first_row || !$last_row || $last_row < $first_row || $last_row < 0) {
            if ($first_row <= 0) {
                $this->raiseError(
                    'error_isValidNumber',
                    '',
                    0,
                    array(
                        'var_label' => sprintf('%s: %s', $this->i18n('imports_tables_rows_to_import'), $this->i18n('from')),
                    )
                );
            }
            if ($last_row <= 0) {
                $this->raiseError(
                    'error_isValidNumber',
                    '',
                    0,
                    array(
                        'var_label' => sprintf('%s: %s', $this->i18n('imports_tables_rows_to_import'), $this->i18n('to')),
                    )
                );
            }
            if ($first_row > $last_row) {
                $this->raiseError(
                    'error_compareVar',
                    '',
                    0,
                    array(
                        'conditions' => sprintf('%s: %s <= %s', $this->i18n('imports_tables_rows_to_import'), $this->i18n('from'), $this->i18n('to')),
                    )
                );
            }
        }

        $column_file = $this->get('column_file') && is_array($this->get('column_file')) ? array_filter($this->get('column_file')) : array();
        $column_table = $this->get('column_table') && is_array($this->get('column_table')) ? array_filter($this->get('column_table')) : array();
        $column_value = $this->get('column_value') && is_array($this->get('column_value')) ? array_filter($this->get('column_value'), array($this, 'filterEmptyValues')) : array();

        if (!array_intersect_key($column_file, $column_table + $column_value)) {
            $this->raiseError(
                'error_empty_field',
                '',
                0,
                array(
                    'var_label' => sprintf('%s <=> %s', $this->i18n('imports_tables_column_file'), $this->i18n('imports_tables_column_table')),
                )
            );
        }

        if ($column_file && !empty($this->objPHPExcel)) {
            $this->objPHPExcel->setActiveSheetIndex(0);
            $worksheet = $this->objPHPExcel->getActiveSheet();

            $last_column_index = PHPExcel_Cell::columnIndexFromString($worksheet->getHighestDataColumn());
            $invalid_keys = array();
            foreach ($column_file as $idx => $col) {
                if ($col == self::FIXED_VALUE) {
                    continue;
                }
                if (!preg_match('#^[a-zA-Z]{1,3}$#', $col) || PHPExcel_Cell::columnIndexFromString($col) > $last_column_index) {
                    $invalid_keys[] = $idx + 1;
                }
            }
            if ($invalid_keys) {
                $this->raiseError(
                    'error_regexpCompare',
                    '',
                    0,
                    array(
                        'var_label' => sprintf('%s %s: %s', $this->i18n('imports_tables_column_file'), $this->i18n('num'), implode(', ', $invalid_keys)),
                        'var_help' => '!',
                    )
                );
            }
        }

        $var_names = array_map(function($a) { return $a['option_value']; }, $this->getVarOptions());

        if (!$var_names) {
            $this->raiseError('no_additional_vars');
        }
        if (array_diff($column_table, $var_names)) {
            $invalid_keys = array();
            foreach (array_keys(array_diff($column_table, $var_names)) as $invalid_key) {
                $invalid_keys[] = $invalid_key + 1;
            }
            $this->raiseError(
                'error_regexpCompare',
                '',
                0,
                array(
                    'var_label' => sprintf('%s %s: %s', $this->i18n('imports_tables_column_table'), $this->i18n('num'), implode(', ', $invalid_keys)),
                    'var_help' => '!',
                )
            );
        }

        if ($this->valid) {
            if (empty($this->objPHPExcel)) {
                $this->raiseError('no_data');
            }
        }
        if (!$this->valid && !empty($this->objPHPExcel) && !$this->get('persist_phpexcel')) {
            $this->unloadPHPExcel();
        }

        return $this->valid;
    }

    /**
     * Prepares data for import into grouping table and returns fetched HTML
     * content of grouping variable with prepared values set
     *
     * @return void|NULL[] - fetched content or error messages if errors occurred
     */
    public function fetchTable() {

        $range_values = $this->getImportValues();
        $calculate = false;

        if (!$this->registry['messages']->getErrors()) {
            $error = false;
            if ($this->get('gt2')) {
                // import into GT2 of financial and warehouse documents is
                // disabled for now because there are additional modifications
                // of variable depending on type, action etc. which are
                // applied in viewers and are very hard to replicate thoroughly
                /*if ($this->get('model') == 'Finance_Warehouses_Document') {
                    // perform the additional preparation of GT2 for warehouse documents
                    require_once PH_MODULES_DIR . 'finance/viewers/finance.warehouses_documents.add.viewer.php';
                    $finance_warehouses_document = new Finance_Warehouses_Document($this->registry, $this->getAll());
                    $finance_warehouses_document->sanitize();
                    $this->registry->set('finance_warehouses_document', $finance_warehouses_document, true);
                    $viewer = new Finance_Warehouses_Documents_Add_Viewer($this->registry);
                    $viewer->prepare();
                    $this->set('grouping_table_2', $finance_warehouses_document->get('grouping_table_2'), true);
                    $this->registry->remove('finance_warehouses_document');
                    unset($viewer);
                    unset($finance_warehouses_document);
                }*/
                $gt2 = $this->get('grouping_table_2');
                if (is_array($gt2)) {
                    $gt2['values'] = $range_values;
                    $this->set('grouping_table_2', $gt2, true);
                    $this->calculateGT2();
                    $gt2 = $this->get('grouping_table_2');

                    $viewer = new Viewer($this->registry);
                    $viewer->data['table'] = $gt2;
                    $viewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_edit.html');
                    $content = $viewer->fetch();
                } else {
                    $error = $this->i18n('no_additional_vars');
                }
            } elseif ($this->get('grouping')) {
                $group_vars = $this->get('group_vars');
                if (is_array($group_vars)) {
                    $calculate = !!array_filter($group_vars, function($a) { return !empty($a['calculate']) && empty($a['bb']); });

                    // prepare the structured grouping variable
                    $modelName = $this->get('model');
                    $model = new $modelName($this->registry, array('type' => (int)$this->get('model_type')));
                    $real_action = $this->registry['action'];
                    $form_action = $this->get('form_action') ?: ($this->get('id') ? 'edit' : 'add');
                    $this->registry->set('action', $form_action, true);

                    $group_vars = $model->processGroupingFields($group_vars);

                    $this->registry->set('action', $real_action, true);
                    $model = null;

                    if (array_key_exists($this->get('grouping'), $group_vars)) {
                        $group_var = $group_vars[$this->get('grouping')];
                        $group_var['label'] = '';
                        // set the prepared values into corresponding keys
                        $group_var['values'] = array();
                        // create variable index mapping of all included variables in GT
                        $var_mapping = array_flip($group_var['names']);
                        // rows can contain different number of values - if some rows have autocompleter-dependent fields and others are empty
                        foreach ($range_values as $idx => $row) {
                            $grow = array();
                            foreach ($row as $var_name => $var_value) {
                                if (array_key_exists($var_name, $var_mapping)) {
                                    $grow[$var_mapping[$var_name]] = $var_value;
                                }
                            }
                            $group_var['values'][$idx + 1] = $grow;
                        }

                        $viewer = new Viewer($this->registry);
                        $viewer->data['var'] = $group_var;
                        $viewer->setFrameset($this->registry['theme']->templatesDir . '_configurator_group_edit.html');
                        $content = $viewer->fetch();
                    } else {
                        $error = $this->i18n('no_additional_vars');
                    }
                } else {
                    $error = $this->i18n('no_additional_vars');
                }
            } else {
                $error = $this->i18n('error_notEmpty', array('var_label' => $this->i18n('additional_vars')));
            }

            if ($error) {
                // action not possible because some of the required data is incorrect or missing
                $this->registry['messages']->setError($error);
            }
        }

        if ($this->registry['messages']->getErrors()) {
            return array('errors' => $this->registry['messages']->getErrors());
        }

        // remove the file on success
        $this->removeImportTempFile($this->get('import_key'));

        $result = [
            'content' => $content,
            'calculate' => $calculate,
        ];

        if (!empty($this->tmpFileModelId)) {
            $result['model_id'] = $this->tmpFileModelId;
        }

        return $result;
    }

    /**
     * Get the drawings of worksheet and their coordinates
     *
     * @return PHPExcel_Worksheet_BaseDrawing[]
     */
    public function getWorkeetDrawings() {
        $worksheet = $this->objPHPExcel->getActiveSheet();
        $drawings = [];
        foreach ($worksheet->getDrawingCollection() as $drawing) {
            if ($drawing instanceof PHPExcel_Worksheet_Drawing) {
                $coordinates = $drawing->getCoordinates();
                $drawings[$coordinates] = $drawing;
            }
        }

        return $drawings;
    }

    /**
     * Gets the original model
     *
     * @return Model model
     */
    public function getModel()
    {
        $id = $this->get('id');
        $modelName = $this->get('model');
        $modelType = (int)$this->get('model_type');
        if ($id) {
            /**
             * @var Model_Factory $factory
             */
            $factory = General::singular2plural($modelName);
            //Todo: fix gettin the module/controller or alias
            $module = $controller = strtolower($factory);
            $alias = $factory::getAlias($module, $controller);
            $filters = array(
                'where' => array(
                    "{$alias}.id = " . $id
                ),
                'model_lang' => $this->get('model_lang'),
                'sanitize' => true,
            );
            $model = $factory::searchOne($this->registry, $filters);
        } else {
            $model = new $modelName($this->registry, array('type' => $modelType));
        }

        return $model;
    }

    /**
     * Reads specified range of data from first worksheet of file, processes
     * values according to corresponding variables and returns 2-dimensional
     * array of values:
     * 0-based indexed array of rows, then associative array with variable
     * names as keys and prepared values as values
     *
     * @return number|array|mixed|array[]|string[] - prepared data
     */
    public function getImportValues() {
        // objPHPExcel is set during validate procedure
        if (!$this->validate() || empty($this->objPHPExcel)) {
            return array();
        }

        // the variables where data can be imported into
        $vars = $this->getTableVars();
        // all variables in the grouping variable
        $all_vars = array();
        if ($this->get('gt2') && is_array($this->get('grouping_table_2'))) {
            $all_vars = $this->get('grouping_table_2');
            $all_vars = array_key_exists('vars', $all_vars) ? $all_vars['vars'] : array();
        } elseif ($this->get('grouping') && is_array($this->get('group_vars'))) {
            $all_vars = $this->get('group_vars');
        }

        // define whether we need to prepare row num and set it into corresponding variable (it will be idx+$first_row)
        $row_num_var = $this->get('row_num_var') && array_key_exists($this->get('row_num_var'), $vars) ? $this->get('row_num_var') : '';

        $first_row = (int)$this->get('import_first_row');
        $last_row = (int)$this->get('import_last_row');

        // make a rough calculation how many fields selected range will
        // generate in the table and do not allow extreme excess
        if (($last_row - $first_row + 1) * count($all_vars) > ini_get('max_input_vars')) {
            $this->raiseError('error_max_group_rows');
            return array();
        }

        $column_file = array_filter($this->get('column_file') ?: array());
        $column_table = array_filter($this->get('column_table') ?: array());
        $column_value = array_filter($this->get('column_value') ?: array(), array($this, 'filterEmptyValues'));
        $column_value_autocomplete = $this->get('column_value_autocomplete') ?: array();
        $column_file = array_intersect_key($column_file, $column_table + $column_value);
        $column_table = array_intersect_key($column_table, $column_file);

        // extract specified default and fixed values into associative array with variable names as keys
        // extract names of fixed variables
        // only if this is the last occurrence of the table variable - otherwise it will be overwritten
        $default_values = $fixed_var_names = array();
        foreach ($column_value as $idx => $value) {
            if (array_key_exists($idx, $column_table) && $idx == max(array_keys($column_table, $column_table[$idx])) && array_key_exists($column_table[$idx], $vars)) {
                $var_name = $column_table[$idx];
                if ($vars[$var_name]['type'] == 'autocompleter') {
                    // value
                    $default_values[$var_name] = array_key_exists($idx, $column_value_autocomplete) && $value ? $column_value_autocomplete[$idx] : '';
                    // value id
                    if (!empty($vars[$var_name]['autocomplete']['id_var'])) {
                        $default_values[$vars[$var_name]['autocomplete']['id_var']] = $value;
                    }
                } else {
                    $default_values[$var_name] = $value;
                }
                if ($column_file[$idx] == self::FIXED_VALUE) {
                    $fixed_var_names[] = $var_name;
                }
            }
        }

        // order the selected columns
        $cols = array_flip($column_file);
        // exclude columns which do not come from file
        $cols = array_diff_key($cols, array(self::FIXED_VALUE => ''));
        foreach ($cols as $col_name => $col_idx) {
            $cols[$col_name] = PHPExcel_Cell::columnIndexFromString($col_name);
        }
        $cols = array_flip($cols);
        ksort($cols);
        $first_column = reset($cols);
        $last_column = end($cols);

        // shift indexes of selected columns left to make them zero-based
        // the way they will be returned in read data
        $first_column_index = array_search($first_column, $cols);
        $cols_values = array_combine(
            array_map(function($a) use ($first_column_index) { return $a - $first_column_index; }, array_keys($cols)),
            $cols
        );

        // define which result indexes we need and under what keys to set them
        // same source field can be imported into multiple destination fields
        $var_names = array();
        foreach ($column_table as $idx => $var_name) {
            $col_idx = array_search($column_file[$idx], $cols_values);
            if ($col_idx !== false) {
                if (!array_key_exists($col_idx, $var_names)) {
                    $var_names[$col_idx] = array();
                }
                $var_names[$col_idx][$idx] = $var_name;
            }
        }

        if ($first_column && $last_column && $first_row && $last_row) {
            $range = "{$first_column}{$first_row}:{$last_column}{$last_row}";

            $this->objPHPExcel->setActiveSheetIndex(0);
            $worksheet = $this->objPHPExcel->getActiveSheet();

            // formatted or raw values?
            $formatData = false;

            $drawings = $this->getWorkeetDrawings();
            if (!empty($drawings)) {
                // rows and columns indexed by number counting from zero
                $returnCellRef = true;
                $range_values = $worksheet->rangeToArray($range, null, true, $formatData, $returnCellRef);

                //get the original model (Document, Customer, etc)
                $model = $this->getModel();

                //custom model id because the file is still temporary
                $this->setTempFileModelId();

                //IMPORTANT: set custom model id because the file is still temporary
                $model->set('id', $this->tmpFileModelId, true);

                //set a prefix of the imported files
                $importedFilesPrefix = Files::$importedFilePrefix . $this->tmpFileModelId;

                foreach ($drawings as $coordinates => $drawing) {
                    list($col, $row) = PHPExcel_Cell::coordinateFromString($coordinates);
                    if (array_key_exists($row, $range_values) && array_key_exists($col, $range_values[$row])) {
                        $tmpFilePath = $this->createTempDrawingFile($drawing);
                        if ($tmpFilePath && file_exists($tmpFilePath)) {
                            $drawingFileName = sprintf(
                                '%s_%05d.%s',
                                $importedFilesPrefix,
                                $drawing->getImageIndex(),
                                $drawing->getExtension()
                            );
                            $drawingFileId = $this->attachTempFileToModel($tmpFilePath, $drawingFileName, $model);
                            if ($drawingFileId) {
                                $range_values[$row][$col] =
                                    Files::searchOne($this->registry, [
                                        'where' => ['f.id = ' . $drawingFileId],
                                        'sanitize' => true
                                    ]);
                            }
                        }
                    }
                }
                $range_values = array_values(array_map('array_values', $range_values));
            } else {
                // rows and columns indexed by number counting from zero
                $returnCellRef = false;
                $range_values = $worksheet->rangeToArray($range, null, true, $formatData, $returnCellRef);
            }
        } else {
            $range_values = array_fill_keys(range(0, $last_row - $first_row, 1), array());
        }

        // perform all date processing as if current timezone is UTC in order to get the same formatted datetimes
        $save_timezone = date_default_timezone_get();
        date_default_timezone_set('UTC');

        // set keys for variables with fixed values (that will not be imported from file)
        $row_values_init = $fixed_var_names ? array_fill_keys($fixed_var_names, '') : array();

        foreach ($range_values as $ridx => $row_values) {
            $range_values[$ridx] = $row_values_init;
            $row_values = array_intersect_key($row_values, $var_names);
            $formatted_values_isDateTime = array();
            // in case same column data should be imported into multiple fields
            foreach ($row_values as $vidx => $value) {
                // 0-based column index, real row index
                $isDateTime =
                    $this->filterEmptyValues($value) && is_numeric($value) ?
                    PHPExcel_Shared_Date::isDateTime($worksheet->getCellByColumnAndRow($vidx + $first_column_index - 1, $ridx + $first_row)) :
                    false;
                // if value is numeric and formatted as datetime, get the formatted value of cell as well
                if ($isDateTime) {
                    // quotations are used to concatenate formatting specifiers and literals, strip them
                    $isDateTime = str_replace(
                        '"',
                        '',
                        $worksheet->getCellByColumnAndRow($vidx + $first_column_index - 1, $ridx + $first_row)
                            ->getFormattedValue()
                    );
                }
                foreach ($var_names[$vidx] as $var_name) {
                    // set the raw value
                    $range_values[$ridx][$var_name] = $value;
                    // set formatted value as corresponding flag;
                    // depending on the destination variable the raw value, the formatted value or no value should be used
                    if ($isDateTime) {
                        $formatted_values_isDateTime[$var_name] = $isDateTime;
                    }
                }
            }
            // check if raw values with replacements from default values applied to them, have any empty value
//            $has_empty_values = count($range_values[$ridx]) != count(array_filter(array_intersect_key($default_values, $range_values[$ridx]) + $range_values[$ridx], array($this, 'filterEmptyValues')));
//            if ($has_empty_values) {
//                // define which rows to clear
//                $range_values[$ridx] = array_fill_keys(array_keys($range_values[$ridx]), '');
//                $formatted_values_isDateTime = array();
//            }
            // process and format data according to variable type and settings
            // apply default values even to rows that should be empty
            $range_values[$ridx] = $this->prepareImportValuesRow($range_values[$ridx], $vars, $formatted_values_isDateTime, $default_values);
            if ($row_num_var) {
                // row index from file
                $range_values[$ridx][$row_num_var] = $ridx + $first_row;
            }
        }

        // restore timezone
        date_default_timezone_set($save_timezone);

        if (!$this->get('persist_phpexcel')) {
            $this->unloadPHPExcel();
        }

        return $range_values;
    }

    /**
     * Checks if value is empty (according to requirements of functionality)
     *
     * @param mixed $a - value to check
     * @return boolean - true if value is non-empty, otherwise false
     */
    private function filterEmptyValues($a) {
        return !($a === null || $a === '');
    }

    /**
     * Processes one row of values from file into values according to variable
     * types and their settings
     *
     * @param array $values - row of values from file with corresponding variable names as keys
     * @param array $vars - updatable GT/GT2 variables
     * @param array $formatted_values_isDateTime - formatted values of numeric values formatted as datetime in the source cell, corresponding variable names as keys
     * @param array $default_values - default values for variables
     * @return array - processed values
     */
    public function prepareImportValuesRow(array &$values, array &$vars, array &$formatted_values_isDateTime = array(), array &$default_values = array()) {
        $value = '';
        $filterMatchingOptions = function ($a) use (&$value) {
            return trim(mb_strtolower($a['label'])) === trim(mb_strtolower($value));
        };

        // reorder values so that autocompleters are processed last after all other values have been set
        foreach ($vars as $var_name => $var) {
            if (!empty($var['type']) && $var['type'] == 'autocompleter' && array_key_exists($var_name, $values)) {
                $values = array_diff_key($values, array($var_name => '')) + array($var_name => $values[$var_name]);
            }
        }
        //if ($this->get('gt2') && !array_key_exists('quantity', $values)) {
        //    $values['quantity'] = '0';
        //}

        foreach ($values as $var_name => $value) {
            if (!array_key_exists($var_name, $vars) || empty($vars[$var_name]['type'])) {
                $values[$var_name] = '';
                continue;
            }
            $var = $vars[$var_name];

            $formatted_value = array_key_exists($var_name, $formatted_values_isDateTime) ? $formatted_values_isDateTime[$var_name] : '';

            switch ($var['type']) {
                case 'autocompleter':
                    // field is formatted as datetime in source cell, ignore the value
                    if ($formatted_value) {
                        $value = $values[$var_name] = '';
                    }
                    $this->prepareAutocompleteValues($var_name, $vars, $values, $default_values);
                    $value = array_key_exists($var_name, $values) ? $values[$var_name] : '';
                    break;
                case 'dropdown':
                case 'radio':
                    // field is formatted as datetime in source cell, ignore the value
                    if ($formatted_value) {
                        $value = $values[$var_name] = '';
                    }
                    if ($value === '') {
                        $filtered_options = array();
                    } elseif (!empty($var['options'])) {
                        $filtered_options = array_values(array_filter($var['options'], $filterMatchingOptions));
                    } elseif (!empty($var['optgroups'])) {
                        $filtered_options = array();
                        foreach ($var['optgroups'] as $og) {
                            $filtered_options = array_merge($filtered_options, array_values(array_filter($og, $filterMatchingOptions)));
                        }
                    }
                    $value = count($filtered_options) == 1 ? $filtered_options[0]['option_value'] : '';
                    break;
                case 'date':
                    $value = $this->formatDateValue($value);
                    break;
                case 'datetime':
                    $value = $this->formatDateValue($value, '%Y-%m-%d %H:%M:%S');
                    break;
                case 'time':
                    $value = $this->formatTimeValue($value);
                    break;
                case 'file_upload':
                    //just do nothing
                    break;
                default:
                    // if there is numeric validation - convert value to float number
                    if (!empty($var['js_filter']) && preg_match('#^insertOnly#', strval($var['js_filter']))) {
                        // field is formatted as datetime in source cell, ignore the value
                        if ($formatted_value) {
                            $value = $values[$var_name] = '';
                        }
                        $value = floatval($value);
                    } else {
                        // set the formatted value of the cell as variable value
                        if ($formatted_value) {
                            $value = $values[$var_name] = $formatted_value;
                        }
                    }
                    break;
            }

            if (array_key_exists($var_name, $default_values) && $var['type'] != 'autocompleter' && (!$this->filterEmptyValues($value) || $value === (float)0)) {
                if ($value === (float)0) {
                    // numeric
                    $value = floatval($default_values[$var_name]);
                } else {
                    // string
                    $value = $default_values[$var_name];
                }
            }

            $values[$var_name] = $value;
        }

        //set discount/surplus field
        foreach(['discount_percentage', 'discount_value', 'surplus_percentage', 'surplus_value'] as $discountSurplusField) {
            if (!empty($values[$discountSurplusField])) {
                $values['discount_surplus_field'] = $discountSurplusField;
            }
        }

        return $values;
    }

    /**
     * Formats datetime value to specified format
     *
     * @param float|string $date - datetime representation coming from file
     * @param string $format - strftime format string
     * @return string - formatted date
     */
    public function formatDateValue($date, $format = '%Y-%m-%d') {
        // If the date is empty
        if (empty($date)) {
            // Return an empty value
            return '';
        } else {
            // If the date is numeric (i.e. if the datetime is in Unix time)
            if (is_numeric($date)) {
                // Convert the date from Unix time to the given format
                return General::strftime($format, PHPExcel_Shared_Date::ExcelToPHP($date));
            } else {
                // Change the format of the date
                return General::strftime($format, $date);
            }
        }
    }

    /**
     * Formats time value as HH:MM
     *
     * @param float|string $time - datetime representation coming from file
     * @return string - formatted time
     */
    public function formatTimeValue($time) {
        // If the time is numeric (i.e. if the datetime is in Unix time)
        if (is_numeric($time)) {
            return General::strftime('%H:%M', PHPExcel_Shared_Date::ExcelToPHP($time));
        }
        if (empty($time) || !Validator::validTime($time)) {
            return '';
        } else {
            // extract the hour and minute parts from the string
            preg_match('#(\d{1,2})\:(\d{1,2})(:\d{1,2})?#', $time, $time);
            return sprintf('%02d:%02d', $time[1], $time[2]);
        }
    }

    /**
     * Defines replacement value for variable parameter ($<var_name>) in autocomplete filter
     *
     * @param string $param_field - filter variable name
     * @param array $vars - updatable GT/GT2 variables
     * @param array $values - row of values from file with corresponding variable names as keys
     * @return string - defined filter value
     */
    private function getAutocompleteFilterValue($param_field, array &$vars, array &$values) {
        $param_value = '';
        // check if field is in the same GT/GT2 as autocompleter,
        // if so get value from current row of imported values (or set an empty value if not imported)
        // - this might not be always correct because of formatting, type conversion, dependent autocompleters etc.
        if (array_key_exists($param_field, $vars)) {
            if (array_key_exists($param_field, $values)) {
                $param_value = $values[$param_field];
            }
        } elseif ($this->isDefined($param_field)) {
            // values (all fields from underlying form) are set from Request onto current model
            $param_value = $this->get($param_field);
        }

        // matching rows from other GTs don't really make sense so get first value
        if (is_array($param_value)) {
            $param_value = reset($param_value);
        }

        return $param_value;
    }

    /**
     * Search data for dependent fields of autocompleter fields.
     * Search by field value in name of records and get data only when one model is found.
     * In all other cases set values to empty strings.
     *
     * @param string $var_name - autocompleter variable name
     * @param array $vars - updatable GT/GT2 variables
     * @param array $values - row of values from file with corresponding variable names as keys
     * @param array $default_values - default values for variables
     * @return array - processed values
     */
    public function prepareAutocompleteValues($var_name, array &$vars, array &$values, array &$default_values = array()) {
        if (empty($vars[$var_name]) || !$this->filterEmptyValues($values[$var_name]) && !array_key_exists($var_name, $default_values)) {
            return $values;
        }
        if (!(!empty($vars[$var_name]['autocomplete']) && !empty($vars[$var_name]['autocomplete']['type']) && !empty($vars[$var_name]['autocomplete']['fill_options']))) {
            return $values;
        }
        $var = $vars[$var_name];
        $var_value = isset($values[$var_name]) ? $values[$var_name] : '';
        $is_default_value = false;
        $autocomplete = $var['autocomplete'];
        $registry = &$this->registry;
        $gov = $registry->get('get_old_vars');
        if (!$gov) {
            $registry->set('get_old_vars', true, true);
        }
        // pretend that autocompleter search is performed
        $current_action = $registry['action'];
        $registry->set('action', 'ajax_select', true);

        /** @var Model $model */
        $model = null;
        /** @var Model_Factory $factory */
        $factory = implode('_', array_map('ucfirst', explode('_', $autocomplete['type'])));

        if ($autocomplete['type'] == 'autocompleters') {
            if (!empty($autocomplete['plugin_params']) && !empty($autocomplete['plugin_search']) && method_exists($factory, $autocomplete['plugin_search'])) {
                // temporarily set some data in request which might be already present (on save, for example)
                $orig_values = array();
                foreach (array('field', $var['name'], 'model_lang') as $orig) {
                    if ($this->registry['request']->isRequested($orig)) {
                        $method_key = $this->registry['request']->isPost($orig) ? 'post' : 'get';
                        $orig_values[$method_key][$orig] = $this->registry['request']->get($orig, $method_key);
                    }
                }
                $this->registry['request']->set('field', $var['name'], 'get', true);
                // set the value in request as if it is the content typed into autocompleter field
                $this->registry['request']->set($var['name'], $var_value, 'get', true);
                $this->registry['request']->set('model_lang', $this->get('model_lang'), 'get', true);

                // replace variable parameters ($<var_name>) with values from other fields of current model
                foreach ($autocomplete['plugin_params'] as $ppk => $ppv) {
                    if (in_array($ppk, array('sql', 'search', 'rights', 'unique_field')) || strpos($ppv, '$') !== 0) {
                        continue;
                    }
                    $param_field = substr($ppv, 1);
                    $autocomplete['plugin_params'][$ppk] = $this->getAutocompleteFilterValue($param_field, $vars, $values);
                }
                $autocomplete['plugin']['params'] = json_encode($autocomplete['plugin_params']);

                // method is expected to be "customQuery"
                $method = $autocomplete['plugin_search'];

                // run search algorithm when there is imported value, skip it when value is fixed
                if ($var_value !== '') {
                    // perform search
                    list($model) = $factory::$method($this->registry, $autocomplete);

                    if (count($model) == 1) {
                        $model = reset($model);
                    } elseif (count($model)) {
                        $fo_var_value = '';
                        foreach ($var['autocomplete']['fill_options'] as $option) {
                            if (preg_match('#^\$' . $var['name'] . '\s*=>\s*(.+)$#', $option, $fo_var_value)) {
                                $fo_var_value = $fo_var_value[1];
                                break;
                            }
                        }
                        $matches = array();
                        if ($fo_var_value) {
                            $fo_var_value = trim($fo_var_value);
                            preg_match_all('#<[^<>]*>#', $fo_var_value, $matches);
                            $matches = $matches[0];
                        }
                        // process all models because sorting by weight is uncertain
                        // (if matches are sorted by weight, the best match is first)
                        $model_all = $model;
                        foreach ($model_all as $model) {
                            $fo_var_value_replaced = $fo_var_value;
                            // replace all placeholders (<var_name>) with property values
                            foreach ($matches as $match) {
                                $property = preg_replace('#<|>|\s#', '', $match);
                                $replacement = $model->get($property);
                                $fo_var_value_replaced = preg_replace('#\s*$#u', '', str_replace($match, $replacement, $fo_var_value_replaced));
                            }
                            // if the result fill option value matches exactly the imported value, then match is found
                            if ($fo_var_value_replaced != $var_value) {
                                $model = null;
                            } else {
                                // model found
                                break;
                            }
                        }
                    } else {
                        $model = null;
                    }
                }

                // if not found, and there is default value,
                // try to get "model" by simulating refresh action
                if (empty($model) && !empty($autocomplete['id_var']) && !empty($default_values[$autocomplete['id_var']])) {
                    // set a flag to know that default values are used
                    $is_default_value = true;

                    // search for default model just once
                    if (empty($vars[$var_name]['default_values_fetched'])) {

                        // set a flag into the autocompleter variable that default values of
                        // dependent fields have been fetched into $default_values and can be
                        // used directly in next iterations, without running autocompleter search
                        $vars[$var_name]['default_values_fetched'] = true;

                        // try to define the id field (with alias)
                        $id_field = '';
                        $sqlmatches = array();
                        if (!empty($autocomplete['plugin_params']['unique_field'])) {
                            $id_field = $autocomplete['plugin_params']['unique_field'];
                        } elseif (
                            preg_match('#[\s\(]((\w+\.)?\w+)\)?\s+as\s+id[\s,]#i', $autocomplete['plugin_params']['sql'], $sqlmatches) ||
                            preg_match('#[\s\(]((\w+\.)?id)[\s\),]#i', $autocomplete['plugin_params']['sql'], $sqlmatches)
                        ) {
                            $id_field = $sqlmatches[1];
                        }

                        if ($id_field) {
                            // set the id value in request
                            $this->registry['request']->set($var['name'], $default_values[$autocomplete['id_var']], 'get', true);

                            // set search by id
                            if (!empty($autocomplete['plugin_params']['search'])) {
                                $autocomplete['plugin_params']['search'] = $id_field;
                            } elseif (preg_match_all('#(\s+|\()([^\s\(]*\s+((NOT )?LIKE|(!|<|>)?=)\s*(\"|\\\')(%?)<search_string_parts>(%?)(\"|\\\'))#i', $autocomplete['plugin_params']['sql'], $matches)) {
                                $autocomplete['plugin_params']['sql'] = str_replace($matches[2], $id_field . ' = \'<search_string_parts>\'', $autocomplete['plugin_params']['sql']);
                            }
                            $autocomplete['plugin']['params'] = json_encode($autocomplete['plugin_params']);

                            // perform search
                            list($model) = $factory::$method($this->registry, $autocomplete);

                            if (count($model)) {
                                $model = reset($model);
                            } else {
                                $model = null;
                            }
                        }
                    }
                }

                // restore data in request
                $this->registry['request']->remove('field');
                $this->registry['request']->remove($var['name']);
                $this->registry['request']->remove('model_lang');
                if ($orig_values) {
                    foreach ($orig_values as $method_key => $ov) {
                        foreach ($ov as $k => $v) {
                            $this->registry['request']->set($k, $v, $method_key, true);
                        }
                    }
                }
            }
        } else {
            $ids = array();
            $alias = $factory::getAlias($autocomplete['type'], '');

            // run search algorithm when there is imported value, skip it when value is fixed
            if ($var_value !== '') {
                $table_name = 'DB_TABLE_' . strtoupper($autocomplete['type']);
                $table_i18n_name = $table_name . '_I18N';
                // get uppercase names to avoid comparison differences
                if (defined($table_name)) {
                    $table_columns = array_keys($this->registry['db']->MetaColumnNames(constant($table_name), false));
                } else {
                    $table_columns = array();
                }
                if (defined($table_i18n_name)) {
                    $i18n_columns = array_keys($this->registry['db']->MetaColumnNames(constant($table_i18n_name), false));
                } else {
                    $i18n_columns = array();
                }

                $filters = array(
                    'where' => array(),
                    'model_lang' => $this->get('model_lang'),
                    'sanitize' => true,
                    'limit' => '0, 2',
                );

                // define possible exact searches
                // after the exact searches failed to find only one result
                // then search by relative comparison
                $search_exact = array();

                switch ($autocomplete['type']) {
                    case 'finance_payments':
                        $search_exact[] = "{$alias}i18n.reason";
                        $autocomplete['search'] = array($search_exact);
                        break;
                    case 'users':
                        $search_exact[] = "TRIM(CONCAT({$alias}i18n.firstname, ' ', {$alias}i18n.lastname))";
                        $autocomplete['search'] = array($search_exact);
                        break;
                    case 'nomenclatures':
                        // it is possible the exact search for nomenclature to be by code
                        // amd not by name, so define the code as second parameter
                        $search_exact[] = "{$alias}i18n.name";
                        $search_exact[] = "{$alias}.code";
                        $autocomplete['search'] = array(
                            "{$alias}.code",
                            "{$alias}i18n.name"
                        );
                        break;
                    case 'documents':
                        // it is possible the exact search for documents to be by full number
                        // and not by name, so define the full_num as second parameter
                        $search_exact[] = "{$alias}i18n.name";
                        $search_exact[] = "{$alias}.full_num";
                        $autocomplete['search'] = array(
                            "{$alias}.full_num",
                            "{$alias}i18n.name"
                        );
                        break;
                    case 'customers':
                        $search_exact[] = "TRIM(CONCAT({$alias}i18n.name, ' ', {$alias}i18n.lastname))";
                        // continue into default case
                    default:
                        $autocomplete['search'] = array("{$alias}i18n.name");
                        if (empty($search_exact)) {
                            $search_exact[] = $autocomplete['search'][0];
                        }
                        break;
                }

                $filters_split = array();

                $key = $var_value;
                foreach ($var['autocomplete']['fill_options'] as $option) {
                    // based on the format, clear the extra symbols if any
                    if (preg_match('#\$' . $var_name . '\s*=>#', $option)) {
                        $symbols = preg_replace('#(<[^<>]*>)|(\s+)|(\$' . $var_name . ')|(=>)#u', '', $option);
                        // fill options might include non-ASCII characters
                        $symbols = preg_replace_callback('#(.)#u', function($matches) {return preg_quote($matches[0], '#') . '|';}, $symbols);
                        $symbols = substr($symbols, 0, -1);
                        if (!empty($symbols)) {
                            $replaced_key = preg_replace('#' . $symbols . '#ui', ' ', $var_value);
                            // if there is at least one other character left
                            if (trim($replaced_key)) {
                                $key = $replaced_key;
                            }
                        }
                    }
                }
                // prepare "words" to search by (split value by whitespace chars)
                $key = preg_split('#\s+#u', General::slashesEscape($key));

                //sets search values
                foreach ($key as $k) {
                    $subfilters = array();
                    foreach ($autocomplete['search'] as /* $i =>  */$field) {
                        if ($k != '' && !preg_match('#\.id(?!\w)#', $field)) {
                            $subfilters[] = $field . ' LIKE \'%' . $k . '%\'';
                            if (preg_match('#ci18n\.name#', $field)) {
                                $subfilters[] = 'ci18n.lastname LIKE \'%' . $k . '%\'';
                            } elseif (preg_match('#ui18n\.firstname#', $field) && !in_array('ui18n.lastname', $autocomplete['search'])) {
                                $subfilters[] = 'ui18n.lastname LIKE \'%' . $k . '%\'';
                            }
                        /* } elseif ($k && preg_match('#\.id#', $field)) {
                            $search_ids = $k;
                            $subfilters[] = $field . ' IN (' . $k . ')'; */
                        }
                    }
                    // add search conditions to filters as if they come from the "Search" form
                    $num_sf = count($subfilters);
                    for ($sf = 0; $sf < $num_sf; $sf++) {
                        $filters_split[] = $subfilters[$sf] . ' ' . ($sf < $num_sf - 1 ? 'OR' : 'AND');
                    }
                }

                // prepare other conditions
                $additional_where = array();
                $allFilters = array();
                if (!empty($autocomplete['filters'])) {
                    $allFilters['filters'] = $autocomplete['filters'];
                }
                if (!empty($autocomplete['optional_filters'])) {
                    $allFilters['optional'] = $autocomplete['optional_filters'];
                }
                foreach ($allFilters as $filterType => $filterList) {
                    foreach ($filterList as $filter => $filter_value) {
                        $filter_alias = $alias . '.';
                        $field = preg_replace('#<|>#', '', $filter);

                        // process $-variables in value
                        if (strpos($filter_value, '$') !== false) {
                            $matches = array();
                            preg_match_all('#\$(\w+)\b#', $filter_value, $matches);
                            foreach ($matches[1] as $param_field) {
                                $filter_value = preg_replace(
                                    '#\$' . $param_field . '\b#',
                                    $this->getAutocompleteFilterValue($param_field, $vars, $values),
                                    $filter_value
                                );
                            }
                        }
                        if ($filterType == 'optional' && empty($filter_value)) {
                            continue;
                        }

                        // escape value for the SQL search query
                        $filter_value = General::slashesEscape($filter_value);

                        switch ($filter) {
                            case '<tag>':
                                $filter_alias = 'tags.';
                                $field = 'tag_id';
                                break;

                            // cases for contactpersons - start
                            case '<contactpersons>':
                                if ($autocomplete['type'] == 'customers') {
                                    $factory .= '_Contactpersons';
                                    continue 2;
                                }
                            case '<customer>':
                                if ($autocomplete['type'] == 'customers') {
                                    $additional_where[] = 'pc.id = \'' . $filter_value . '\' AND';
                                    continue 2;
                                }
                            case '<branch>':
                                if ($autocomplete['type'] == 'customers') {
                                    $additional_where[] = 'bn.id = \'' . $filter_value . '\' AND';
                                    continue 2;
                                }
                            // cases for contactpersons - end

                            // cases for nomenclatures - start
                            case '<category>':
                                if ($autocomplete['type'] == 'nomenclatures') {
                                    $filter_alias = 'nc.';
                                    $field = 'cat_id';
                                    break;
                                }
                            case '<type_keyword>':
                                if ($autocomplete['type'] == 'nomenclatures') {
                                    $field = 'type';
                                    $nt_filters = array('where' => array("nt.keyword = '{$filter_value}'"));
                                    $filter_value = Nomenclatures_Types::getIds($registry, $nt_filters);
                                    $filter_value = $filter_value ? reset($filter_value) : '';
                                    break;
                                }
                            case '<customer_trademark>':
                                if ($autocomplete['type'] == 'nomenclatures') {
                                    $filters['session_param'] = 'filter_trademark_nomenclature';
                                    continue 2;
                                }
                            case '<get_available_quantities>':
                            case '<require_warehouse>':
                            case '<get_all_quantities>':
                                if ($autocomplete['type'] == 'nomenclatures') {
                                    $additional_where[] = "{$field} = '$filter_value'";
                                    continue 2;
                                }
                            // cases for nomenclatures - end

                            default:
                                if (preg_match('#^a__*#', $field)) {
                                    // additional var
                                    $filter_alias = '';
                                } elseif (in_array(strtoupper($field), $table_columns)) {
                                    // basic var in main table
                                } elseif (in_array(strtoupper($field), $i18n_columns)) {
                                    // basic var in i18n table
                                    if ($autocomplete['type'] == 'departments') {
                                        $filter_alias = $alias . 'i18n1.';
                                    } else {
                                        $filter_alias = $alias . 'i18n.';
                                    }
                                } else {
                                    // something specific - we won't process it
                                    continue 2;
                                }
                                break;
                        }

                        $matches = array();
                        if (preg_match('#^\s*(!?=)(.*)#', $filter_value, $matches)) {
                            // search expression for a single value
                            $additional_where[] = sprintf(
                                '%s%s %s \'%s\' AND',
                                $filter_alias,
                                $field,
                                trim($matches[1]),
                                trim($matches[2])
                            );
                            continue;
                        } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $filter_value, $matches)) {
                            // search expression for multiple values
                            $negative_search = preg_match('#not#i', $matches[1]);
                            $compare_operator = $negative_search ? '!=' : '=';
                            $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                            $count_or_clauses = count($amatches);
                            foreach ($amatches as $idx => $amatch) {
                                $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                                $additional_where[] = sprintf(
                                    '%s%s %s \'%s\' %s',
                                    $filter_alias,
                                    $field,
                                    $compare_operator,
                                    $amatch,
                                    $logical_operator
                                );
                            }
                            continue;
                        }

                        // no expression, just the value
                        $vals = preg_split('#\s*,\s*#', $filter_value);
                        if (count($vals) > 1) {
                            $count_or_clauses = count($vals);
                            foreach ($vals as $idx => $val) {
                                $clause = $filter_alias . $field . ' = \'' . $val . '\'';
                                if ($idx < $count_or_clauses - 1) {
                                    $clause .= ' OR';
                                } else {
                                    $clause .= ' AND';
                                }
                                $additional_where[] = $clause;
                            }
                        } else {
                            $additional_where[] = $filter_alias . $field . ' = \'' . $vals[0] . '\' AND';
                        }
                    }
                }

                // assuming only active records are important
                if (in_array('ACTIVE', $table_columns)) {
                    $additional_where[] = "{$alias}.active = '1'";
                }
                // pass currency as pseudo-filter for price conversion
                if ($this->get('gt2') && $this->get('currency') && $factory == 'Nomenclatures') {
                    $additional_where[] = "currency = '{$this->get('currency')}'";
                }

                // first try to find exact match of the non-split value
                $ids = array();
                foreach ($search_exact as $se) {
                    $filters['where'] = array_merge(
                        array(
                            sprintf("%s = '%s' AND", $se, General::slashesEscape($var_value)),
                        ),
                        $additional_where
                    );
                    $ids = $factory::getIds($registry, $filters);
                    if (count($ids) == 1) {
                        break;
                    }
                }

                if (count($ids) != 1) {
                    $filters['where'] = array_merge($filters_split, $additional_where);
                    $ids = $factory::getIds($registry, $filters);
                }
            }

            // if not found, and there is default value, use it
            if (count($ids) != 1 && !empty($autocomplete['id_var']) && !empty($default_values[$autocomplete['id_var']])) {
                // set a flag to know that default values are used
                $is_default_value = true;

                // search for default model just once
                if (empty($vars[$var_name]['default_values_fetched'])) {
                    // set a flag into the autocompleter variable that default values of
                    // dependent fields have been fetched into $default_values and can be
                    // used directly in next iterations, without running autocompleter search
                    $vars[$var_name]['default_values_fetched'] = true;

                    $ids = array((int)$default_values[$autocomplete['id_var']]);
                }
            }

            if (count($ids) == 1) {
                // get model by id, get values for dependent fields
                if (!empty($filters['session_param']) && $filters['session_param'] == 'filter_trademark_nomenclature') {
                    $filters['where'] = array("ct.id = '{$ids[0]}'");
                } else {
                    $filters['where'] = array("{$alias}.id = '{$ids[0]}'");
                }
                $model = $factory::searchOne($registry, $filters);
            }
        }

        //fields that should not be empty so we set them to 0(zero)
        $mandatory_num = array('price', 'sell_price', 'delivery_price', 'alternative_deliverer', 'deliverer');

        foreach ($var['autocomplete']['fill_options'] as $option) {
            if (!preg_match("#=>#", $option)) {
                continue;
            }
            list($fo_var_name, $fo_var_value) = preg_split('#\s*=>\s*#', $option);
            $fo_var_name = trim($fo_var_name);
            $fo_var_name = preg_replace('#^\$#', '', $fo_var_name);
            // if the variable is not in $vars OR it is editable and imported, do not set value according to autocompleter
            if (
                !array_key_exists($fo_var_name, $vars) ||
                $fo_var_name != $var_name && !$vars[$fo_var_name]['readonly'] && !$vars[$fo_var_name]['hidden'] && array_key_exists($fo_var_name, $values)
            ) {
                continue;
            }
            // if value is set in default values, use it directly
            if ($is_default_value && array_key_exists($fo_var_name, $default_values)) {
                $values[$fo_var_name] = $default_values[$fo_var_name];
                continue;
            }

            if (!$model) {
                $values[$fo_var_name] = '';
                continue;
            }

            $fo_var_value = trim($fo_var_value);
            $matches = array();
            preg_match_all('#<[^<>]*>#', $fo_var_value, $matches);
            $matches = $matches[0];
            $values[$fo_var_name] = $fo_var_value;

            foreach ($matches as $match) {
                $property = preg_replace('#<|>|\s#', '', $match);
                if (preg_match('#^a_+#', $property)) {
                    //additional variable
                    $property = preg_replace('#^a_+(' . PH_ADDITIONAL_VAR_PREFIX . ')?#', '', $property);
                    // if true, get raw value, otherwise get formatted value
                    $force_raw = in_array($vars[$fo_var_name]['type'], array('dropdown', 'radio'));
                    // get the value without checking layout permissions
                    if (!empty($filters['session_param']) && $filters['session_param'] == 'filter_trademark_nomenclature') {
                        $tmp_id = $model->get('id');
                        $model->set('id', $model->get('trademark'), true);
                        $replacement = $model->getVarValue($property, $force_raw, true);
                        $model->set('id', $tmp_id, true);
                    } else {
                        $replacement = $model->getVarValue($property, $force_raw, true);
                    }
                } else {
                    $replacement = $model->get($property);
                    //special formatting for dates
                    //we can expand this condition if needed
                    if (strpos($property, 'date') !== false) {
                        //date/time formatting
                        if (preg_match('#^\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}$#', $replacement)) {
                            if (strpos($replacement, '0000-00-00') === false) {
                                $replacement = General::strftime($this->i18n('date_mid'), $replacement);
                            } else {
                                $replacement = '  .  .       :  ';
                            }
                        } elseif (preg_match('#^\d{4}-\d{2}-\d{2}$#', $replacement)) {
                            if (strpos($replacement, '0000-00-00') === false) {
                                $replacement = General::strftime($this->i18n('date_short'), $replacement);
                            } else {
                                $replacement = '  .  .    ';
                            }
                        }
                    }
                }
                if (is_array($replacement)) {
                    $replacement = reset($replacement);
                }
                if (empty($replacement) && in_array(preg_replace('#<|>|\s#u', '', $match), $mandatory_num)) {
                    $replacement = 0;
                }
                $values[$fo_var_name] = preg_replace('#\s*$#u', '', str_replace($match, $replacement, $values[$fo_var_name]));
                if ($is_default_value) {
                    $default_values[$fo_var_name] = $values[$fo_var_name];
                }
                /* if (!$request->get('ajax_filter')) {
                    //replace single quotation marks (apostrophes) ' with html entity &#39;
                    $values[$fo_var_name] = str_replace("'", "&#39;", $values[$fo_var_name]);
                } */
            }
        }
        if (!$gov) {
            $registry->remove('get_old_vars');
        }
        $registry->set('action', $current_action, true);

        return $values;
    }

    /**
     * Create array from a range of cells of active worksheet
     *
     * @param string $row_range
     * @param mixed $nullValue
     * @param boolean $calculateFormulas
     * @param boolean $formatData
     * @return array|mixed
     */
    public function readCells($row_range, $nullValue = null, $calculateFormulas = true, $formatData = true, $returnCellRef = false) {
        if (empty($this->objPHPExcel)) {
            return array();
        }
        return $this->objPHPExcel->getActiveSheet()->rangeToArray($row_range, $nullValue, $calculateFormulas, $formatData, $returnCellRef);
    }

    /**
     * Launches plugin from imports plugin folder
     *
     * @param string $importKey - the import key stored in session
     * @return bool $result
     */
    public function launchPlugin($importKey) {
        $result = true;

        //loading table vars capsulates the settings of the table stored in source
        $this->getTableVars();

        //check if there is import_plugin set in the table
        $importType = $this->get('import_plugin');
        if ($importType) {
            require_once PH_MODULES_DIR . '/imports/plugins/' . $importType . '/custom.import.php';
            /** @var Import $importType */
            $importPlugin = Imports::searchOne($this->registry, $filters = array('name' => $importType));
            if ($importPlugin) {
                $importPlugin->loadPreparedSettings();

                //the import uses $_FILES array, so restore it there
                $this->restoreImportFile($importKey);

                // Execute the import
                $params = array_merge(
                    array(
                        'import_type' => $importPlugin->get('type'),
                        'import_name' => $importPlugin->get('name'),
                    ),
                    $importPlugin->get('settings') ?: array()
                );
                $result = Custom_Import::import($this->registry, $params);
            }
        }

        return $result;
    }

    /**
     * Extracts drawing from sheet and creates a temporary file with it
     *
     * @param PHPExcel_Worksheet_BaseDrawing $drawing
     * @return string path to the file
     */
    public function createTempDrawingFile(PHPExcel_Worksheet_BaseDrawing $drawing): string
    {
        $zipReader = fopen($drawing->getPath(), 'r');
        $drawingContents = '';
        while (!feof($zipReader)) {
            $drawingContents .= fread($zipReader, 1024);
        }
        fclose($zipReader);
        $tmpFilePath = PH_IMPORTS_CACHE_DIR . uniqid(rand(), true) . '.' . $drawing->getExtension();
        try {
            file_put_contents($tmpFilePath, $drawingContents);
        } catch (Exception $e){
            return "";
        }

        return $tmpFilePath;
    }

    /**
     * Attaches temporary file to a model
     * @param string $tmpFilePath
     * @param string $fileBaseName
     * @param Model $model
     * @return bool|int
     */
    public function attachTempFileToModel(string $tmpFilePath, string $fileBaseName, Model $model)
    {
        $drawingFile = [
            'name' => $fileBaseName,
            'tmp_name' => $tmpFilePath,
            'size' => filesize($tmpFilePath),
            'type' => mime_content_type($tmpFilePath),
            'error' => ''
        ];
        $attachParams = array(
            //'id'          => $idx,
            'name' => $fileBaseName,
            'filename' => $fileBaseName,
            //'description' => $descriptions[$idx],
            //'permission'  => $permissions[$idx]
        );

        return Files::attachFile(
            $this->registry,
            $drawingFile,
            $attachParams,
            $model
        );
    }

    /**
     * @return int|string
     */
    public function setTempFileModelId()
    {
        $this->tmpFileModelId = time();
        //$importData = $this->registry['session']->get($this->get('import_key') . '_data', self::SESSION_PARAM);
        //$importData['tmpFileModelId'] = $this->tmpFileModelId;
        //$this->saveImportTempFileData($this->get('import_key'), $importData);

        return $this->tmpFileModelId;
    }
}
