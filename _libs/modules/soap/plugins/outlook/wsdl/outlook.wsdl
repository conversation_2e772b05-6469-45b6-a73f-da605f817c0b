<?xml version="1.0" encoding="UTF-8"?>
<wsdl:definitions xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:http="http://schemas.xmlsoap.org/wsdl/http/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:mime="http://schemas.xmlsoap.org/wsdl/mime/" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tns="nzTargetNamespace" targetNamespace="nzTargetNamespace">
  <wsdl:types>
    <xsi:schema xmlns:xsi="http://www.w3.org/2001/XMLSchema" targetNamespace="nzTargetNamespace" elementFormDefault="qualified">
      <!-- TYPE FOR DIAGNOSTIC -->
      <xsi:complexType name="tDiag">
        <xsi:sequence>
          <xsi:element name="code" type="xs:string"/>
          <xsi:element name="text" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPE FOR LANGUAGE -->
      <xsi:complexType name="tLanguage">
        <xsi:sequence>
          <xsi:element name="code" type="xs:string"/>
          <xsi:element name="text" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPE FOR PAGINATION -->
      <xsi:complexType name="tPagination">
        <xsi:sequence>
          <xsi:element name="total" type="xs:int"/>
          <xsi:element name="page" type="xs:int"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPE FOR MESSAGES -->
      <xsi:complexType name="tMessage">
        <xsi:sequence>
          <xsi:element name="code" type="xs:string"/>
          <xsi:element name="text" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPE FOR FILE -->
      <xsi:complexType name="tFile">
        <xsi:sequence>
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="description" type="xs:string"/>
          <xsi:element name="revision" type="xs:int"/>
          <xsi:element name="filename" type="xs:string"/>
          <xsi:element name="content" type="xs:base64Binary"/>
          <xsi:element name="contentId" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR CONTACTS -->
      <xsi:complexType name="tContactAddress">
        <xsi:sequence>
          <xsi:element name="country" type="xs:string"/>
          <xsi:element name="postal_code" type="xs:string"/>
          <xsi:element name="city" type="xs:string"/>
          <xsi:element name="address" type="xs:string"/>
          <xsi:element name="notes" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tContactLink">
        <xsi:sequence>
          <xsi:element name="type" type="xs:string"/>
          <xsi:element name="link" type="xs:string"/>
          <xsi:element name="note" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tContactLinksArray">
        <xsi:sequence>
          <xsi:element name="tContactLink" type="tns:tContactLink" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tModifiedContact">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="salutation" type="xs:string"/>
          <!-- mr/mrs/ms/<empty> -->
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="lastname" type="xs:string"/>
          <xsi:element name="type" type="xs:int"/>
          <xsi:element name="is_company" type="xs:int"/>
          <xsi:element name="position" type="xs:string"/>
          <xsi:element name="type_name" type="xs:string"/>
          <xsi:element name="company" type="xs:int"/>
          <xsi:element name="company_name" type="xs:string"/>
          <xsi:element name="branch" type="xs:int"/>
          <xsi:element name="branch_name" type="xs:string"/>
          <xsi:element name="address" type="tns:tContactAddress"/>
          <xsi:element name="links" type="tns:tContactLinksArray"/>
          <xsi:element name="modified" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tUpdatedContact">
        <xsi:sequence>
          <xsi:element name="oId" type="xs:int"/>
          <xsi:element name="nzId" type="xs:int"/>
          <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR TASKS -->
      <xsi:complexType name="tModifiedTask">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="customer" type="xs:int"/>
          <xsi:element name="customer_name" type="xs:string"/>
          <xsi:element name="subject" type="xs:string"/>
          <xsi:element name="description" type="xs:string"/>
          <xsi:element name="deadline" type="xs:string"/>
          <xsi:element name="severity" type="xs:string"/>
          <xsi:element name="comment" type="xs:string"/>
          <xsi:element name="status" type="xs:string"/>
          <xsi:element name="modified" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tUpdatedTask">
        <xsi:sequence>
          <xsi:element name="oId" type="xs:int"/>
          <xsi:element name="nzId" type="xs:int"/>
          <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR CUSTOMERS TYPES -->
      <xsi:complexType name="tCustomersType">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="kind" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR CUSTOMERS -->
      <xsi:complexType name="tCustomer">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="code" type="xs:string"/>
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="lastname" type="xs:string"/>
          <xsi:element name="is_company" type="xs:int"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR CUSTOMERS RECORDS -->
      <xsi:complexType name="tCustomersRecord">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="modelType" type="xs:string"/>
          <xsi:element name="name" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR BRANCHES -->
      <xsi:complexType name="tBranch">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="name" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR EVENTS TYPES -->
      <xsi:complexType name="tEventsType">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="name" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR EVENTS STATUSES -->
      <xsi:complexType name="tEventsStatus">
        <xsi:sequence>
          <xsi:element name="key" type="xs:string"/>
          <xsi:element name="name" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR EVENTS -->
      <xsi:complexType name="tEventParticipant">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="type" type="xs:string"/>
          <!-- user/customer/master -->
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="email" type="xs:string"/>
          <xsi:element name="access" type="xs:string"/>
          <!-- edit/view -->
          <xsi:element name="invitations" type="xs:int"/>
          <xsi:element name="last_invitation" type="xs:string"/>
          <xsi:element name="invitation_status" type="xs:string"/>
          <!-- pending, confirmed, denied, not_sure -->
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tModifiedEvent">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="type" type="xs:int"/>
          <xsi:element name="type_name" type="xs:string"/>
          <xsi:element name="start_date" type="xs:string"/>
          <xsi:element name="start_time" type="xs:string"/>
          <xsi:element name="end_date" type="xs:string"/>
          <xsi:element name="end_time" type="xs:string"/>
          <xsi:element name="allday" type="xs:int"/>
          <xsi:element name="priority" type="xs:string"/>
          <!-- verylow, low, medium, high, veryhigh -->
          <xsi:element name="status" type="xs:string"/>
          <!-- planning, progress, finished, unstarted, moved -->
          <xsi:element name="availability" type="xs:string"/>
          <!-- available, busy -->
          <xsi:element name="visibility" type="xs:string"/>
          <!-- public, private -->
          <xsi:element name="customer" type="xs:int"/>
          <xsi:element name="customer_name" type="xs:string"/>
          <xsi:element name="subject" type="xs:string"/>
          <xsi:element name="location" type="xs:string"/>
          <xsi:element name="description" type="xs:string"/>
          <xsi:element name="modified" type="xs:string"/>
          <xsi:element name="participants" type="tns:tEventParticipant" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tUpdatedEvent">
        <xsi:sequence>
          <xsi:element name="oId" type="xs:int"/>
          <xsi:element name="nzId" type="xs:int"/>
          <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR EMAILS -->
      <xsi:complexType name="tEmailRecipient">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="type" type="xs:string"/>
          <!-- user/customer/master -->
          <xsi:element name="origin" type="xs:string"/>
          <!-- to/cc/bcc -->
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="email" type="xs:string"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tNZEmail">
        <xsi:sequence>
          <xsi:element name="id" type="xs:int"/>
          <xsi:element name="model_id" type="xs:int"/>
          <xsi:element name="model" type="xs:string"/>
          <xsi:element name="code" type="xs:string"/>
          <xsi:element name="messageIdHeader" type="xs:string"/>
          <xsi:element name="from" type="xs:string"/>
          <xsi:element name="subject" type="xs:string"/>
          <xsi:element name="body" type="xs:base64Binary"/>
          <xsi:element name="sent" type="xs:string"/>
          <xsi:element name="recipients" type="tns:tEmailRecipient" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
          <xsi:element name="attachments" type="tns:tFile" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tOEmail">
        <xsi:sequence>
          <xsi:element name="origin" type="xs:string"/>
          <!-- sent/received -->
          <xsi:element name="messageIdHeader" type="xs:string"/>
          <xsi:element name="inReplyTo" type="xs:string"/>
          <xsi:element name="references" type="xs:string"/>
          <xsi:element name="threadIndex" type="xs:string"/>
          <xsi:element name="from" type="xs:string"/>
          <xsi:element name="subject" type="xs:string"/>
          <xsi:element name="body" type="xs:base64Binary"/>
          <xsi:element name="sent" type="xs:string"/>
          <xsi:element name="recipients" type="tns:tEmailRecipient" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
          <xsi:element name="attachments" type="tns:tFile" minOccurs="0" maxOccurs="unbounded" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <xsi:complexType name="tAddedEmail">
        <xsi:sequence>
          <xsi:element name="messageIdHeader" type="xs:string"/>
          <xsi:element name="threadIndex" type="xs:string"/>
          <xsi:element name="nzModel" type="xs:string"/>
          <xsi:element name="nzModelId" type="xs:int"/>
          <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
        </xsi:sequence>
      </xsi:complexType>
      <!-- TYPES FOR MODELS FOR CREATION -->
      <xsi:complexType name="tCreateModel">
        <xsi:sequence>
          <xsi:element name="key" type="xs:string"/>
          <xsi:element name="name" type="xs:string"/>
          <xsi:element name="permission" type="xs:string"/>
          <!-- add/email/both -->
        </xsi:sequence>
      </xsi:complexType>
      
      
      <!-- CONTACTS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzModifiedContactsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLastSyncDate" type="xs:string"/>
            <xsi:element name="oRequestPage" type="xs:int"/>
            <xsi:element name="oKind" type="xs:string"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzModifiedContactsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzModifiedContacts" type="tns:tModifiedContact" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzPagination" type="tns:tPagination"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- CONTACTS FROM OUTLOOK REQUEST AND RESPONSE -->
      <xsi:element name="oModifiedContactsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oModifiedContacts" type="tns:tModifiedContact" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="force" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="oModifiedContactsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzUpdatedContacts" type="tns:tUpdatedContact" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- TASKS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzModifiedTasksRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLastSyncDate" type="xs:string"/>
            <xsi:element name="oRequestPage" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzModifiedTasksResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzModifiedTasks" type="tns:tModifiedTask" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzPagination" type="tns:tPagination"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- TASKS FROM OUTLOOK REQUEST AND RESPONSE -->
      <xsi:element name="oModifiedTasksRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oModifiedTasks" type="tns:tModifiedTask" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="force" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="oModifiedTasksResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzUpdatedTasks" type="tns:tUpdatedTask" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- CUSTOMERS TYPES FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCustomersTypesRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oTypesKind" type="xs:string"/>
            <!-- person/company/both/all(default if empty) -->
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzCustomersTypesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzCustomersTypes" type="tns:tCustomersType" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- CUSTOMERS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCustomersRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oInput" type="xs:string"/>
            <xsi:element name="oKind" type="xs:string"/>
            <!-- person/company/all(default if empty) -->
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzCustomersResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzCustomers" type="tns:tCustomer" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- CUSTOMERS RECORDS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCustomersRecordsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oCustomer" type="xs:int"/>
            <xsi:element name="oModelType" type="xs:string"/>
            <xsi:element name="oPage" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzCustomersRecordsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzCustomersRecords" type="tns:tCustomersRecord" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzPage" type="xs:int"/>
            <xsi:element name="nzTotalPages" type="xs:int"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- CUSTOMERS BY MAIL FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCustomersByEmailRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oEmail" type="xs:string"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzCustomersByEmailResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzCustomers" type="tns:tCustomer" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- BRANCHES FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzBranchesRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oCustomer" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzBranchesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzBranches" type="tns:tBranch" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EVENTS TYPES FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzEventsTypesRequest">
      <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzEventsTypesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzEventsTypes" type="tns:tEventsType" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      
      <!-- CHECK LOGIN FOR nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCheckLoginRequest"/>
      <xsi:element name="nzCheckLoginResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="logged" type="xs:int"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EVENTS STATUSES FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzEventsStatusesRequest">
      <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzEventsStatusesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzEventsStatuses" type="tns:tEventsStatus" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- USERS PERMISSIONS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzUserPermissionsRequest"/>
      <xsi:element name="nzUserPermissionsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzUserPermissions" type="xs:string" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EVENTS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzModifiedEventsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLastSyncDate" type="xs:string"/>
            <xsi:element name="oRequestPage" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzModifiedEventsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzModifiedEvents" type="tns:tModifiedEvent" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzPagination" type="tns:tPagination"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EVENTS FROM OUTLOOK REQUEST AND RESPONSE -->
      <xsi:element name="oModifiedEventsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oModifiedEvents" type="tns:tModifiedEvent" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="force" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="oModifiedEventsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzUpdatedEvents" type="tns:tUpdatedEvent" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EMAILS FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzSentEmailsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLastSyncDate" type="xs:string"/>
            <xsi:element name="oRequestPage" type="xs:int"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzSentEmailsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzSentEmails" type="tns:tNZEmail" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzPagination" type="tns:tPagination"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EMAILS FROM OUTLOOK REQUEST AND RESPONSE -->
      <xsi:element name="oReceivedEmailsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oReceivedEmails" type="tns:tOEmail" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="force" type="xs:int"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="oReceivedEmailsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzAddedEmails" type="tns:tAddedEmail" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- MESSAGES FROM nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzMessagesRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzMessagesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzMessages" type="tns:tMessage" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- MODELS TO CREATE IN nZOOM REQUEST AND RESPONSE -->
      <xsi:element name="nzCreateModelsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oCustomer" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzCreateModelsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzCreateModels" type="tns:tCreateModel" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- MODELS TO CREATE FROM OUTLOOK REQUEST AND RESPONSE -->
      <xsi:element name="oCreateModelsRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oEmailData" type="tns:tOEmail"/>
            <xsi:element name="oModelType" type="xs:string"/>
            <xsi:element name="oModelId" type="xs:int"/>
            <xsi:element name="oCustomer" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="oCreateModelsResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzDiag" type="tns:tDiag" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
            <xsi:element name="nzModelType" type="xs:string"/>
            <xsi:element name="nzModelId" type="xs:int"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <!-- EMAIL ATTACHED TO THE MODEL REQUEST -->
      <xsi:element name="nzEmailModelRequest">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="oModelType" type="xs:string"/>
            <xsi:element name="oModelId" type="xs:int"/>
            <xsi:element name="oLang" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      <xsi:element name="nzEmailModelResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzMessage" type="xs:string"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
      
      <!-- LANGUAGES FROM nZOOM -->
      <xsi:element name="nzLanguagesRequest" />
      <xsi:element name="nzLanguagesResponse">
        <xsi:complexType>
          <xsi:sequence>
            <xsi:element name="nzLanguages" type="tns:tLanguage" maxOccurs="unbounded" minOccurs="0" nillable="true"/>
          </xsi:sequence>
        </xsi:complexType>
      </xsi:element>
    </xsi:schema>
  </wsdl:types>
  
  <wsdl:message name="msgNZEmailModelRequest">
    <wsdl:part name="parameters" element="tns:nzEmailModelRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZEmailModelResponse">
    <wsdl:part name="parameters" element="tns:nzEmailModelResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CONTACTS FROM nZOOM -->
  <wsdl:message name="msgNZModifiedContactsRequest">
    <wsdl:part name="parameters" element="tns:nzModifiedContactsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZModifiedContactsResponse">
    <wsdl:part name="parameters" element="tns:nzModifiedContactsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CONTACTS FROM OUTLOOK -->
  <wsdl:message name="msgOModifiedContactsRequest">
    <wsdl:part name="parameters" element="tns:oModifiedContactsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgOModifiedContactsResponse">
    <wsdl:part name="parameters" element="tns:oModifiedContactsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - TASKS FROM nZOOM -->
  <wsdl:message name="msgNZModifiedTasksRequest">
    <wsdl:part name="parameters" element="tns:nzModifiedTasksRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZModifiedTasksResponse">
    <wsdl:part name="parameters" element="tns:nzModifiedTasksResponse"/>
  </wsdl:message>
  <!-- MESSAGES - TASKS FROM OUTLOOK -->
  <wsdl:message name="msgOModifiedTasksRequest">
    <wsdl:part name="parameters" element="tns:oModifiedTasksRequest"/>
  </wsdl:message>
  <wsdl:message name="msgOModifiedTasksResponse">
    <wsdl:part name="parameters" element="tns:oModifiedTasksResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CUSTOMERS TYPES FROM nZOOM -->
  <wsdl:message name="msgNZCustomersTypesRequest">
    <wsdl:part name="parameters" element="tns:nzCustomersTypesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCustomersTypesResponse">
    <wsdl:part name="parameters" element="tns:nzCustomersTypesResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CUSTOMERS FROM nZOOM -->
  <wsdl:message name="msgNZCustomersRequest">
    <wsdl:part name="parameters" element="tns:nzCustomersRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCustomersResponse">
    <wsdl:part name="parameters" element="tns:nzCustomersResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CHECK LOGIN FOR nZOOM -->
  <wsdl:message name="msgNZCheckLoginRequest">
    <wsdl:part name="parameters" element="tns:nzCheckLoginRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCheckLoginResponse">
    <wsdl:part name="parameters" element="tns:nzCheckLoginResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CUSTOMERS RECORDS FROM nZOOM -->
  <wsdl:message name="msgNZCustomersRecordsRequest">
    <wsdl:part name="parameters" element="tns:nzCustomersRecordsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCustomersRecordsResponse">
    <wsdl:part name="parameters" element="tns:nzCustomersRecordsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CUSTOMERS BY MAIL FROM nZOOM -->
  <wsdl:message name="msgNZCustomersByEmailRequest">
    <wsdl:part name="paramseters" element="tns:nzCustomersByEmailRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCustomersByEmailResponse">
    <wsdl:part name="parameters" element="tns:nzCustomersByEmailResponse"/>
  </wsdl:message>
  <!-- MESSAGES - BRANCHES FROM nZOOM -->
  <wsdl:message name="msgNZBranchesRequest">
    <wsdl:part name="parameters" element="tns:nzBranchesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZBranchesResponse">
    <wsdl:part name="parameters" element="tns:nzBranchesResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EVENTS TYPES FROM nZOOM -->
  <wsdl:message name="msgNZEventsTypesRequest">
    <wsdl:part name="parameters" element="tns:nzEventsTypesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZEventsTypesResponse">
    <wsdl:part name="parameters" element="tns:nzEventsTypesResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EVENTS STATUSES FROM nZOOM -->
  <wsdl:message name="msgNZEventsStatusesRequest">
    <wsdl:part name="parameters" element="tns:nzEventsStatusesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZEventsStatusesResponse">
    <wsdl:part name="parameters" element="tns:nzEventsStatusesResponse"/>
  </wsdl:message>
  <!-- MESSAGES - USER PERMISSIONS FROM nZOOM -->
  <wsdl:message name="msgNZUserPermissionsRequest">
    <wsdl:part name="parameters" element="tns:nzUserPermissionsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZUserPermissionsResponse">
    <wsdl:part name="parameters" element="tns:nzUserPermissionsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EVENTS FROM nZOOM -->
  <wsdl:message name="msgNZModifiedEventsRequest">
    <wsdl:part name="parameters" element="tns:nzModifiedEventsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZModifiedEventsResponse">
    <wsdl:part name="parameters" element="tns:nzModifiedEventsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EVENTS FROM OUTLOOK -->
  <wsdl:message name="msgOModifiedEventsRequest">
    <wsdl:part name="parameters" element="tns:oModifiedEventsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgOModifiedEventsResponse">
    <wsdl:part name="parameters" element="tns:oModifiedEventsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EMAILS FROM nZOOM -->
  <wsdl:message name="msgNZSentEmailsRequest">
    <wsdl:part name="parameters" element="tns:nzSentEmailsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZSentEmailsResponse">
    <wsdl:part name="parameters" element="tns:nzSentEmailsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - EMAILS FROM OUTLOOK -->
  <wsdl:message name="msgOReceivedEmailsRequest">
    <wsdl:part name="parameters" element="tns:oReceivedEmailsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgOReceivedEmailsResponse">
    <wsdl:part name="parameters" element="tns:oReceivedEmailsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - MESSAGES FROM nZOOM -->
  <wsdl:message name="msgNZMessagesRequest">
    <wsdl:part name="parameters" element="tns:nzMessagesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZMessagesResponse">
    <wsdl:part name="parameters" element="tns:nzMessagesResponse"/>
  </wsdl:message>
  <!-- MESSAGES - MODELS TO CREATE -->
  <wsdl:message name="msgNZCreateModelsRequest">
    <wsdl:part name="parameters" element="tns:nzCreateModelsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNZCreateModelsResponse">
    <wsdl:part name="parameters" element="tns:nzCreateModelsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - CREATE MODEL FROM OUTLOOK -->
  <wsdl:message name="msgOCreateModelsRequest">
    <wsdl:part name="parameters" element="tns:oCreateModelsRequest"/>
  </wsdl:message>
  <wsdl:message name="msgOCreateModelsResponse">
    <wsdl:part name="parameters" element="tns:oCreateModelsResponse"/>
  </wsdl:message>
  <!-- MESSAGES - LANGUAGES FROM NZOOM -->
  <wsdl:message name="msgNzLanguagesRequest">
    <wsdl:part name="parameters" element="tns:nzLanguagesRequest"/>
  </wsdl:message>
  <wsdl:message name="msgNzLanguagesResponse">
    <wsdl:part name="parameters" element="tns:nzLanguagesResponse"/>
  </wsdl:message>
    
  <wsdl:portType name="nzPortType">
    <!-- OPERATION - MODEL FOR EMAIL -->
    <wsdl:operation name="nzEmailModel">
        <wsdl:input message="tns:msgNZEmailModelRequest"/>
        <wsdl:output message="tns:msgNZEmailModelResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CONTACTS FROM nZOOM -->
    <wsdl:operation name="nzModifiedContacts">
      <wsdl:input message="tns:msgNZModifiedContactsRequest"/>
      <wsdl:output message="tns:msgNZModifiedContactsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CHECK LOGIN -->
    <wsdl:operation name="nzCheckLogin">
      <wsdl:input message="tns:msgNZCheckLoginRequest"/>
      <wsdl:output message="tns:msgNZCheckLoginResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CONTACTS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedContacts">
      <wsdl:input message="tns:msgOModifiedContactsRequest"/>
      <wsdl:output message="tns:msgOModifiedContactsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - TASKS FROM nZOOM -->
    <wsdl:operation name="nzModifiedTasks">
      <wsdl:input message="tns:msgNZModifiedTasksRequest"/>
      <wsdl:output message="tns:msgNZModifiedTasksResponse"/>
    </wsdl:operation>
    <!-- OPERATION - TASKS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedTasks">
      <wsdl:input message="tns:msgOModifiedTasksRequest"/>
      <wsdl:output message="tns:msgOModifiedTasksResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS TYPES FROM nZOOM -->
    <wsdl:operation name="nzCustomersTypes">
      <wsdl:input message="tns:msgNZCustomersTypesRequest"/>
      <wsdl:output message="tns:msgNZCustomersTypesResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS FROM nZOOM -->
    <wsdl:operation name="nzCustomers">
      <wsdl:input message="tns:msgNZCustomersRequest"/>
      <wsdl:output message="tns:msgNZCustomersResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS FROM nZOOM -->
    <wsdl:operation name="nzCustomersRecords">
      <wsdl:input message="tns:msgNZCustomersRecordsRequest"/>
      <wsdl:output message="tns:msgNZCustomersRecordsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS FROM nZOOM -->
    <wsdl:operation name="nzCustomersByEmail">
      <wsdl:input message="tns:msgNZCustomersByEmailRequest"/>
      <wsdl:output message="tns:msgNZCustomersByEmailResponse"/>
    </wsdl:operation>
    <!-- OPERATION - BRANCHES FROM nZOOM -->
    <wsdl:operation name="nzBranches">
      <wsdl:input message="tns:msgNZBranchesRequest"/>
      <wsdl:output message="tns:msgNZBranchesResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EVENTS TYPES FROM nZOOM -->
    <wsdl:operation name="nzEventsTypes">
      <wsdl:input message="tns:msgNZEventsTypesRequest"/>
      <wsdl:output message="tns:msgNZEventsTypesResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EVENTS STATUSES FROM nZOOM -->
    <wsdl:operation name="nzEventsStatuses">
      <wsdl:input message="tns:msgNZEventsStatusesRequest"/>
      <wsdl:output message="tns:msgNZEventsStatusesResponse"/>
    </wsdl:operation>
    <!-- OPERATION - USER PERMISSSION FROM nZOOM -->
    <wsdl:operation name="nzUserPermissions">
      <wsdl:input message="tns:msgNZUserPermissionsRequest"/>
      <wsdl:output message="tns:msgNZUserPermissionsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EVENTS FROM nZOOM -->
    <wsdl:operation name="nzModifiedEvents">
      <wsdl:input message="tns:msgNZModifiedEventsRequest"/>
      <wsdl:output message="tns:msgNZModifiedEventsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EVENTS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedEvents">
      <wsdl:input message="tns:msgOModifiedEventsRequest"/>
      <wsdl:output message="tns:msgOModifiedEventsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EMAILS FROM NZOOM -->
    <wsdl:operation name="nzSentEmails">
      <wsdl:input message="tns:msgNZSentEmailsRequest"/>
      <wsdl:output message="tns:msgNZSentEmailsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - EMAILS FROM OUTLOOK -->
    <wsdl:operation name="oReceivedEmails">
      <wsdl:input message="tns:msgOReceivedEmailsRequest"/>
      <wsdl:output message="tns:msgOReceivedEmailsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - MESSAGES FROM NZOOM -->
    <wsdl:operation name="nzMessages">
      <wsdl:input message="tns:msgNZMessagesRequest"/>
      <wsdl:output message="tns:msgNZMessagesResponse"/>
    </wsdl:operation>
    <!-- OPERATION - MODELS TO CREATE IN NZOOM -->
    <wsdl:operation name="nzCreateModels">
      <wsdl:input message="tns:msgNZCreateModelsRequest"/>
      <wsdl:output message="tns:msgNZCreateModelsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - MODELS TO CREATE FROM OUTLOOK -->
    <wsdl:operation name="oCreateFromMailOrAttach">
      <wsdl:input message="tns:msgOCreateModelsRequest"/>
      <wsdl:output message="tns:msgOCreateModelsResponse"/>
    </wsdl:operation>
    <!-- OPERATION - LANGUAGES FROM NZOOM -->
    <wsdl:operation name="nzLanguages">
      <wsdl:input message="tns:msgNzLanguagesRequest"/>
      <wsdl:output message="tns:msgNzLanguagesResponse"/>
    </wsdl:operation>
  </wsdl:portType>
  
  
  <wsdl:binding name="nzBinding" type="tns:nzPortType">
    <soap:binding style="document" transport="http://schemas.xmlsoap.org/soap/http"/>
    <!-- OPERATION - MODEL FOR EMAIL -->
    <wsdl:operation name="nzEmailModel">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CONTACTS FROM nZOOM -->
    <wsdl:operation name="nzModifiedContacts">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CHECK LOGIN -->
    <wsdl:operation name="nzCheckLogin">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CONTACTS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedContacts">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - TASKS FROM nZOOM -->
    <wsdl:operation name="nzModifiedTasks">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - TASKS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedTasks">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS TYPES FROM nZOOM -->
    <wsdl:operation name="nzCustomersTypes">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS FROM nZOOM -->
    <wsdl:operation name="nzCustomers">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS FROM nZOOM -->
    <wsdl:operation name="nzCustomersRecords">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - CUSTOMERS BY MAIL FROM nZOOM -->
    <wsdl:operation name="nzCustomersByEmail">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - BRANCHES FROM nZOOM -->
    <wsdl:operation name="nzBranches">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EVENTS TYPES FROM nZOOM -->
    <wsdl:operation name="nzEventsTypes">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EVENTS STATUSES FROM nZOOM -->
    <wsdl:operation name="nzEventsStatuses">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - USER PERMISSIONS FROM nZOOM -->
    <wsdl:operation name="nzUserPermissions">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EVENTS FROM nZOOM -->
    <wsdl:operation name="nzModifiedEvents">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EVENTS FROM OUTLOOK -->
    <wsdl:operation name="oModifiedEvents">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EMAILS FROM nZOOM -->
    <wsdl:operation name="nzSentEmails">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - EMAILS FROM OUTLOOK -->
    <wsdl:operation name="oReceivedEmails">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - MESSAGES FROM nZOOM -->
    <wsdl:operation name="nzMessages">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - MODELS TO CREATE IN nZOOM -->
    <wsdl:operation name="nzCreateModels">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - MODELS TO CREATE FROM OUTLOOK -->
    <wsdl:operation name="oCreateFromMailOrAttach">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
    <!-- OPERATION - LANGUAGES FROM NZOOM -->
    <wsdl:operation name="nzLanguages">
      <soap:operation soapAction=""/>
      <wsdl:input>
        <soap:body use="literal"/>
      </wsdl:input>
      <wsdl:output>
        <soap:body use="literal"/>
      </wsdl:output>
    </wsdl:operation>
  </wsdl:binding>
  <wsdl:service name="nzSoapService">
    <wsdl:port name="nzPortType" binding="tns:nzBinding">
      <soap:address location="[server_base]soap/outlook/"/>
    </wsdl:port>
  </wsdl:service>
</wsdl:definitions>
