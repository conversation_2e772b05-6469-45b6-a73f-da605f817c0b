/**
 * Gets a fresh list of currency rates, updates the DB and the currency suggestions
 *
 * @param string code - the code of the currency to get rates for
 * @param string bank - code name for selected bank
 */
function getLatestCurrencyRates(code, bank) {

    if (!code && $('code')) {
        code = $('code').value;
    }

    if (!code) {
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var data = eval('(' + t.responseText + ')');

            if (data.rates[code]) {

                $('currency_date').innerHTML = data.date_formated;
                addClass($('currency_date'), 'red');

                // if fixing could not be fetched, set 0 as fixing rate
                if (data.rates[code].fixing) {
                    $('bm5_rate').value = data.rates[code].fixing.toString();
                } else {
                    $('bm5_rate').value = '0.00000';
                }

                if (data.rates[code].buys_bank) {
                    $('bm1_rate').value = data.rates[code].buys_bank.toString();
                    $('bm3_rate').value = (data.rates[code].buys_cash) ? data.rates[code].buys_cash.toString() : data.rates[code].buys_bank.toString();

                    $('bm2_rate').value = data.rates[code].sells_bank.toString();
                    $('bm4_rate').value = (data.rates[code].sells_cash) ? data.rates[code].sells_cash.toString() : data.rates[code].sells_bank.toString();
                } else {
                    $('bm1_rate').value = '0.00000';
                    $('bm2_rate').value = '0.00000';
                    $('bm3_rate').value = '0.00000';
                    $('bm4_rate').value = '0.00000';
                }

                addClass($('bm1_rate'), 'refreshed');
                addClass($('bm2_rate'), 'refreshed');
                addClass($('bm3_rate'), 'refreshed');
                addClass($('bm4_rate'), 'refreshed');
                addClass($('bm5_rate'), 'refreshed');
            } else {

                removeClass($('currency_date'), 'red');

                $('bm1_rate').value = '0.00000';
                $('bm2_rate').value = '0.00000';
                $('bm3_rate').value = '0.00000';
                $('bm4_rate').value = '0.00000';
                $('bm5_rate').value = '0.00000';

                removeClass($('bm1_rate'), 'refreshed');
                removeClass($('bm2_rate'), 'refreshed');
                removeClass($('bm3_rate'), 'refreshed');
                removeClass($('bm4_rate'), 'refreshed');
                removeClass($('bm5_rate'), 'refreshed');
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=finance&controller=currencies&currencies=ajax_update_rates&bank=' + bank + '&code=' + code + '&skip_save=1';

    new Ajax.Request(url, opt);
}

/**
 * Changes some form details when adding currency
 */
function changeCurrency(element) {
    if (element.value) {
        getLatestCurrencyRates(element.value, $('bank').value);
    }

}

/**
 * Gets company info and updates the company fields
 */
function getCompanyInfoByVat() {

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var data = eval('(' + t.responseText + ')');

            if (data['name']) {
                $('name').value = data['name'];
                addClass($('name'), 'refreshed');

                $('VAT_registered').checked = true;
                $('VAT_number_required').innerHTML = '*';
            }
            if (data['address']) {
                $('registration_address').value = data['address'];
                addClass($('registration_address'), 'refreshed');
            }
            if (data['vat'] && data['name']) {
                $('VAT_number').value = data['vat'];
                addClass($('VAT_number'), 'refreshed');
            }
            if (data['eik'] && data['name']) {
                $('eik').value = data['eik'];
                addClass($('eik'), 'refreshed');
            }
            if (data['countryCode'] && data['name']) {
                $('country').value = data['countryCode'];
                if ($('country').attributes.onchange) {
                    $('country').onchange();
                }
                addClass($('country'), 'refreshed');
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=finance&controller=companies&companies=get_company_info&use_ajax=1&VAT_number=' + $('VAT_number').value;

    new Ajax.Request(url, opt);

}

/**
 * DROPDOWNs relations . Change the values of a second dropdown when the first changes
 *
 * @param s_id - name of the object
 */
function changeCashboxesBankAccounts(s_id, model_id, module_name, controller, please_select, office_id, container_type){
    Effect.Center('loading');
    Effect.Appear('loading');

    var select_obj = $(s_id);
    var opt = {
        asynchronous: false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            select_obj.innerHTML = result;
            if (result) {
                removeClass(select_obj, 'missing_records');
            } else {
                select_obj.options[0] = new Option(i18n['labels']['no_select_records'], '', false, false);
                addClass(select_obj, 'missing_records');
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var param;
    if (controller == '') {
        param = module_name;
    } else {
        param = 'controller=' + controller + '&'+ controller;
    }
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name+'&'+param+'=ajax_change_depending_options&model_id='+model_id + '&model_lang=' + $('model_lang').value + '&s_id=' + s_id + '&office_id=' + office_id + '&container_type=' + container_type, opt);

}

/**
 * DROPDOWNs relations . Change the values of a second dropdown when the first changes
 *
 * @param s_id - name of the object
 */
function changeFinanceType(model_name, s_id, module_name, controller) {
    Effect.Center('loading');
    Effect.Appear('loading');

    if (controller == 'counters') {
        var select_container_id = $$('select.container_id');
        for (var i = 0; i < select_container_id.length; i++) {
            if (model_name == 'Finance_Warehouses_Document') {
                addClass(select_container_id[i], 'hidden');
                select_container_id[i].selectedIndex = 0;
            } else {
                removeClass(select_container_id[i], 'hidden');
            }
        }
    }

    var select_obj = $(s_id);
    var opt = {
        asynchronous:false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            select_obj.options.length = 0;
            for (var j = 0; j < result.length; j++) {
                select_obj.options[j] = new Option(result[j]['label'], result[j]['option_value'], false, false);
            }
            toggleUndefined(select_obj);
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    if (controller == '') {
        param = module_name;
    } else {
        param = 'controller=' + controller + '&'+ controller;
    }
    new Ajax.Request(env.base_url + '?' + env.module_param + '=' + module_name+'&'+param+'=ajax_change_model_types&model_name=' + model_name, opt);

}

/**
 * Check sum of fields
 *
 * @param amount - result must be less than amount
 * @param field - name of input elements
 */
function checkAmountSum(amount, field1){
    var elements = $$('.' + field1);
    var field_sum = 0;
    for (var i = 0; i<elements.length; i++) {
        if (elements[i].value != '') {
            field_sum += parseFloat(elements[i].value);
        }
    }

    if (Math.round(100*field_sum) > Math.round(100*amount)) {
        alert(i18n['messages']['alert_amount_sum']);
        return false;
    } else {
        return true;
    }
}

/**
 * recalculates payment date in function of issue_date
 */
function recalculatePaymentDate() {
    if ($('dop_changed')) {
        if ($('dop_changed').value != '') {
            if ($('date_of_payment').value >= $('issue_date').value) {
                return true;
            }
        }
    } else if ($('date_of_payment').value >= $('issue_date').value) {
        return true;
    }

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            if (t.responseText) {
                $('date_of_payment').value = t.responseText;
                if ($('date_of_payment_formatted')) {
                    var df = new Date(t.responseText);
                    var day = df.getDate();
                    var month = df.getMonth() + 1;
                    if (day < 10) day = '0' + day;
                    if (month < 10) month = '0' + month;
                    $('date_of_payment_formatted').value = day + '.' + month + '.' + df.getFullYear();
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    //set the url to request ajax action
    if ($('type')) {
        var type = $('type').value;
    } else if (env.action_name == 'addcorrect') {
        var type = 6;
    } else if ($('new_type')) {
        var type = $('new_type').value;
    }
    var url = env.base_url + '?' + env.module_param + '=finance&controller=expenses_reasons&expenses_reasons=recalc_payment_date';
    url += '&issue_date=' + $('issue_date').value + '&type=' + type;
    url += '&use_ajax=1';
    new Ajax.Request(url, opt);

}

/**
 * Recalculates fiscal event dates in function of issue_date
 * and company and type settings
 */
function recalculateFiscalDates() {

    //check for fiscal event date only
    if (recalculateFiscalDates.arguments.length > 0) {
        if ($('fiscal_event_date').value &&
           ($('fiscal_event_date').value < $('min_fiscal').value || $('fiscal_event_date').value > $('max_fiscal').value)) {
            alert(i18n['messages']['error_incomes_reasons_invoice_fiscal_date']);
            return false;
        }
        return true;
    }
    // prepare url to send request to
    var url = env.base_url + '?' + env.module_param + '=finance&controller=incomes_reasons&incomes_reasons=recalc_fiscal_dates';

    if (company = $('company_data')) {
        company = company.options[company.options.length - 1].value.replace(/(\d+)_.*/, '$1');
    } else {
        company = false;
    }
    if (issue_date = $('issue_date')) {
        issue_date = issue_date.value;
    } else {
        issue_date = false;
    }
    if (fiscal_event_date = $('fiscal_event_date')) {
        fiscal_event_date = fiscal_event_date.value;
    } else {
        // field is not available in form, nothing to update
        return true;
    }
    var opt = {
        method: 'post',
        parameters: {company: company, issue_date: issue_date, fiscal_event_date: fiscal_event_date},
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            if (t.responseText) {
                eval('result = ' + t.responseText + ';');
                $('fiscal_event_date').value = result.fiscal_event_date;
                if ($('fiscal_event_date_formatted').tagName.match(/input/i)) {
                    $('fiscal_event_date_formatted').value = result.fiscal_event_date.split("-").reverse().join(".");
                } else {
                    //readonly field
                    $('fiscal_event_date_formatted').innerHTML = result.fiscal_event_date.split("-").reverse().join(".");
                }
                $('max_fiscal').value = result.max_fiscal;
                $('min_fiscal').value = result.min_fiscal;
                if ($('fiscal_dates_warning')) {
                    $('fiscal_dates_warning').innerHTML = result.fiscal_dates_warning;
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    //set the url to request ajax action
    url += '&use_ajax=1';
    new Ajax.Request(url, opt);
}

/**
 * Prompts for confirmation for issuing invoice from proforma.
 *
 * @param proforma_id - id of proforma
 */
function issueInvoiceFromProforma(proforma_id) {
    // prepare url to send request to
    var url = env.base_url + '?' + env.module_param + '=finance&controller=incomes_reasons&incomes_reasons=addinvoice&addinvoice=' + proforma_id;

    var options = false;
    if (issueInvoiceFromProforma.arguments.length > 1) {
        var form = issueInvoiceFromProforma.arguments[1];
        if (form.id == 'finance_invoicefromproforma') {
            if (issueInvoiceFromProforma.arguments.length < 3 || issueInvoiceFromProforma.arguments[2] != 'reload_form') {
                if ($('fiscal_event_date').value &&
                   ($('fiscal_event_date').value < $('min_fiscal').value || $('fiscal_event_date').value > $('max_fiscal').value)) {
                    alert(i18n['messages']['error_incomes_reasons_invoice_fiscal_date']);
                    form.select('button').invoke('enable');
                    return false;
                }
                window.location = url + '&' + Form.serialize(form);
                return;
            } else {
                options = Form.serialize(form);
                options += '&reload_form=1';
            }
        }
    }
    var opt = {
        method: 'post',
        parameters: options,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            if (t.responseText) {
                if (lb && lb.active) {
                    // on form reload update content of lightbox, do not reload it
                    lb.update({content: t.responseText});
                } else {
                    lb = new lightbox({
                        content: t.responseText,
                        title: i18n['labels']['invoice_from_proforma'],
                        icon: 'info.png',
                        width: 550,
                        closeHandler: function() { return false; }
                    });
                    lb.activate();
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    //set the url to request ajax action
    url += '&use_ajax=1';
    new Ajax.Request(url, opt);
}

/**
 * Prompts for confirmation for issuing incoming invoice from proforma or expense reason.
 *
 * @param id - id of incoming proforma or expense reason
 */
function issueExpensesInvoice(id) {
    // prepare url to send request to
    var url = env.base_url + '?' + env.module_param + '=finance&controller=expenses_reasons&expenses_reasons=addinvoice&addinvoice=' + id;

    if (issueExpensesInvoice.arguments.length > 1) {
        var form = issueExpensesInvoice.arguments[1];
        if (form.id == 'finance_invoice') {
            window.location = url + '&' + Form.serialize(form);
            return;
        }
    }

    var opt = {
        method: 'post',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            if (t.responseText) {
                lb = new lightbox({content: t.responseText,
                                   title: i18n['labels']['expense_invoice'],
                                   icon: 'info.png',
                                   width: 550,
                                   closeHandler: function() { return false; }});
                lb.activate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    Effect.Center('loading');
    Effect.Appear('loading');

    //set the url to request ajax action
    url += '&use_ajax=1';
    new Ajax.Request(url, opt);
}

/**
 * Shows or hides the additional settings for
 * finance documents types
 *
 * @param - model - the model of the type
 */
function typesAdditionalSettings(model) {

    var regex = new RegExp(model);

    var rows = $$('.additional_settings');
    for (var i = 0; i < rows.length; i++) {
        if (rows[i].className.match(regex)) {
            rows[i].style.display = '';
        } else {
            rows[i].style.display = 'none';
        }
    }

    var additional_options = $$('li.sortable input[type=hidden]');
    for (var i = 0; i < additional_options.length; i++) {
        if (additional_options[i].className) {
            if (additional_options[i].className.match(regex)) {
                additional_options[i].disabled = false;
                additional_options[i].parentNode.style.display = '';
            } else {
                additional_options[i].disabled = true;
                additional_options[i].parentNode.style.display = 'none';
            }
        }
    }

    // only the first option ("after issue") is available for expense documents
    if ($('default_date_of_payment_point').options[$('default_date_of_payment_point').selectedIndex].style.display == 'none') {
        $('default_date_of_payment_point').selectedIndex = 0;
    }
}

/**
 * DROPDOWNs relations . Change the values of a second dropdown when the first changes
 *
 * @param s_id - name of the object
 */
function changeOffset(recurrence_type, s_id) {

    var offset_nums = Array();
    offset_nums['daily'] = 1;
    offset_nums['monthlyByDate'] = 31;
    offset_nums['weekly'] = 7;
    offset_nums['yearly'] = 365;
    var num = offset_nums[recurrence_type];
    select_obj = $(s_id);
    select_obj.options.length = 0;
    for (var j = 1; j <= num; j++) {
        select_obj.options[j-1] = new Option(j, j, false, false);
    }

}

/**
 * This function gets all selected elements (documents, customers, projects, etc.)
 * from a popup and assigns them into the edit or add form of document, customer or project
 *
 * @param the_form - the current form elements
 * @param close - flag that defines whether to close the popup or not
 */
function updateRepaymentReferers(the_form,close) {

    var list = the_form.elements['items[]'];
    var items = [];
    //manage array
    if (!list.length) {
        items[0] = list;
    } else {
        items = list;
    }

    // fields in destination window
    var context = defineContext();

    var div_id = 'referers';
    var _referers = context.$(div_id);
    if (!_referers) {
        return false;
    }

    var referers = [];
    var r = context.document.getElementsByName(div_id+'[]');
    if (r && r.length > 0) {
        for (var i=0; i<r.length;i++) {
            referers.push(r[i].value);
        }
    } else if(r && r.value) {
        referers.push(r.value);
    }

    for (var i=0; i<items.length;i++) {
        var item = items[i];
        if (item.checked && !in_array(item.value, referers)) {
            getReapaymentInvoice(item.value);
        }
    }

    var unpaid_amount = 0;
    var elements = context.document.getElementsByName('unpaid[]');
    for (var i=0; i<elements.length;i++) {
        unpaid_amount += parseFloat(elements[i].value);
    }
    var all_amount = Math.round(unpaid_amount * 100)/100;
    context.$('all_amount').innerHTML = all_amount + '<input type="hidden" id="all_amount_" name="all_amount" value="' + all_amount + '" />';

    var amount = 0;
    var i = 1;
    while (context.$('amount_' + i)) {
        if (!context.$('amount_' + i).disabled && context.$('amount_' + i).value) {
            amount += parseFloat(context.$('amount_' + i).value);
        }
        i++;
    }
    amount = Math.round(amount * 100)/100;
    if (isNaN(amount)) {
        amount = 0;
    }
    context.$('shared_amount').innerHTML = amount;
    var unshared_amount = Math.round((all_amount - amount) * 100)/100;
    context.$('unshared_amount').innerHTML = unshared_amount;
    if (unshared_amount != 0) {
        removeClass( context.$('unshared_amount'), 'green');
        addClass( context.$('unshared_amount'), 'red');
    } else {
        removeClass( context.$('unshared_amount'), 'red');
        addClass( context.$('unshared_amount'), 'green');
    }

    if (close) {
        closePopupWindow();
    }

    context.focus();
}

/**
  */
function getReapaymentInvoice(reason_id) {

    var opt = {
        asynchronous:false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            // fields in destination window
            var context = defineContext();

            var _referers = context.$('referers');
            _referers.innerHTML += t.responseText;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=finance&controller=incomes_reasons&incomes_reasons=repaymentdata&repaymentdata=' + reason_id + '&use_ajax=1';

    new Ajax.Request(url, opt);

    return true;
}

function recalculateUnpaid(row_id, tbl) {
    var i=$(row_id).rowIndex;
    $(tbl).deleteRow(i);

    var unpaid_amount = 0;
    var elements = document.getElementsByName('unpaid[]');
    for (var i=0; i<elements.length;i++) {
        unpaid_amount += parseFloat(elements[i].value);
    }
    var all_amount = Math.round(unpaid_amount * 100)/100;
    $('all_amount').innerHTML = all_amount + '<input type="hidden" id="all_amount_" name="all_amount" value="' + all_amount + '" />';

    recalculateSharedAmount();
}

function recalculateSharedAmount() {
    var all_amount = $('all_amount_').value;

    var amount = 0;
    var i = 1;
    while ($('amount_' + i)) {
        if (!$('amount_' + i).disabled && $('amount_' + i).value) {
            amount += parseFloat($('amount_' + i).value);
        }
        i++;
    }
    amount = Math.round(amount * 100)/100;
    if (isNaN(amount)) {
        amount = 0;
    }
    $('shared_amount').innerHTML = amount;
    var unshared_amount = Math.round((all_amount - amount) * 100)/100;
    $('unshared_amount').innerHTML = unshared_amount;
    if (unshared_amount != 0) {
        removeClass($('unshared_amount'), 'green');
        addClass($('unshared_amount'), 'red');
    } else {
        removeClass($('unshared_amount'), 'red');
        addClass($('unshared_amount'), 'green');
    }
}

function recalculatePaymentAmount() {
    var all_amount = parseFloat($('remaining_amount').innerHTML);

    var amount = 0;
    var i = 1;
    while ($('relatives_payments_' + i)) {
        if ($('relatives_payments_' + i).value) {
            amount += parseFloat($('relatives_payments_' + i).value);
        }
        i++;
    }
    amount = Math.round(amount * 100)/100;
    if (isNaN(amount)) {
        amount = 0;
    }

    if ($('unshared') != null) {
        var unshared = Math.round((all_amount - amount) * 100)/100;
        $('unshared').innerHTML = unshared.toFixed(2);
        if (unshared < 0) {
            removeClass($('unshared'), 'green');
            addClass($('unshared'), 'red');
        } else {
            removeClass($('unshared'), 'red');
            addClass($('unshared'), 'green');
        }
    }
}

function checkRepaymentAmounts() {
    recalculateSharedAmount();
    var unpaid_amount = 0;
    var elements = document.getElementsByName('unpaid[]');
    for (var i=0; i<elements.length;i++) {
        unpaid_amount += parseFloat(elements[i].value);
    }
    var all_amount = Math.round(unpaid_amount * 100)/100;

    var amount = 0;
    var i = 1;
    while ($('amount_' + i)) {
        if (!$('amount_' + i).disabled) {
            amount += parseFloat($('amount_' + i).value);
        }
        i++;
    }
    amount = Math.round(amount * 100)/100;

    if (all_amount == amount) {
        return true;
    } else {
        return false;
    }
}

/**
 * Reloads responsible employees options when warehouse_data selection is changed
 *
 * @param warehouse_data - value of warehouse_data field
 * @param select - id of employees field (combobox or hidden input)
 */
function getEmployees(warehouse_data, select) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var warehouse = '';
    if (warehouse_data && warehouse_data.match(/^\d+_\d+_\d+$/)) {
        warehouse = warehouse_data.replace(/.*_(\d+)$/, '$1');
    }
    var select_obj = $(select);
    var opt = {
        asynchronous:false,
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = eval('(' + t.responseText + ')');
            if (select_obj.tagName == 'SELECT') {
                select_obj.options.length = 0;
                if (result.length > 0) {
                    select_obj.options[0] = new Option(i18n['labels']['input_or_select'], '', false, false);
                } else {
                    select_obj.options[0] = new Option(i18n['labels']['please_input'], '', false, false);
                }
                for (var j = 0; j < result.length; j++) {
                    select_obj.options[j+1] = new Option(result[j]['label'], result[j]['option_value'], false, false);
                }
                toggleUndefined(select_obj);
            } else {
                select_obj.value = '';
                var tpl_suffix = select_obj.id.match(/_+$/) ? select_obj.id.replace(/.*[^_](_+)$/, '$1') : '';
                var lbl_span = tpl_suffix ? select_obj.id.replace(/^(.*[^_])(_+)$/, '$1_name$2') : select_obj.id + '_name';
                lbl_span = $(lbl_span);
                if (lbl_span) {
                    lbl_span.innerHTML = '';
                }
                if (result.length == 1) {
                    select_obj.value = result[0]['option_value'];
                    if (lbl_span) {
                        lbl_span.innerHTML = result[0]['label'];
                    }
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var lang = $('model_lang') != null ? $('model_lang').value : env.current_lang;
    new Ajax.Request(env.base_url + '?' + env.module_param + '=finance&controller=warehouses_documents&warehouses_documents=ajax_change_depending_options&warehouse=' + warehouse + '&model_lang=' + lang + '&s_id=employees', opt);
}

/**
 * AJAX functionality that manages saved configurations of repayment plans
 *
 * @param {Object} config_field - config combo field (its value could be id or name of the saved configuration)
 * @param {string} div_id - the id of the DIV to load the result into
 * @param {string} config_action - load, save or delete
 */
function manageConfigPlan(config_field, div_id, config_action) {
    // id or name of the saved configuration
    var config_id = config_field.value;
    var form = config_field.form;
    if (config_action != 'save') {
        config_id = parseInt(config_id);
        if (isNaN(config_id)) {
            config_id = 0;
        }
    }
    if (config_action == 'save' && isNaN(parseInt(config_id)) && config_id == '' ||
        config_action != 'save' && !config_id || !form) {
        alert(i18n['messages']['error_config_not_selected']);
        return false;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var params = {};
    if (config_action == 'save') {
        params = Form.serialize(form);
    }
    var opt = {
        method: 'post',
        parameters: params,
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }

            $(div_id).innerHTML = result;
            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
               eval(x[i].text);
            }
            if (config_action == 'load') {
                recalculateSharedAmount();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.controller_name + '=savegroupvar&use_ajax=1';
    if (config_action == 'load') {
        url += '&edit_id=' + config_id;
    } else if (config_action == 'save') {
        url += '&config_group_id=' + config_id;
    } else if (config_action == 'delete') {
        url += '&del_id=' + config_id;
    }

    new Ajax.Request(url, opt);
}

/**
 * Sets default VAT value on/off
 *
 * @param object el - the select box we change options for
 */
function setDefaultVat(el) {

    //get the number of the row(company id)
    var idx = el.id.replace(/include_vat_(\d+)/i, '$1');
    if (parseInt(el.value) > 0) {
        $('default_VAT_' + idx).disabled = false;
        removeClass($('default_VAT_' + idx), 'readonly');
    } else {
        $('default_VAT_' + idx).disabled = true;
        addClass($('default_VAT_' + idx), 'readonly');
    }
}

/**
 * AJAX functionality that update payment balance
 *
 * @param form - form element
 * @param div_id - the id of the current DIV tag
 * @param link - link address to send request to
 */
function updateBalance(form, div_id, link) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var param = '';
    if (form) {
        param = Form.serialize(form);
    }
    var opt = {
        //method: 'post',
        parameters: param,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            eval('result = ' + result + ';');

            if (result.errors) {
                $('messages_container').innerHTML = result.errors;
                new Effect.ScrollTo('messages_container');
            }
            $(div_id).innerHTML = result.content;

            var x = $(div_id).getElementsByTagName("script");
            for (var i = 0; i < x.length; i++) {
                eval(x[i].text);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = link + '&use_ajax=1';

    new Ajax.Request(url, opt);
}

/**
 * Calculates and displays distributed and remaining percentage of elements for analysis item.
 */
function recalculateItemElementsPercentage() {
    var pow = Math.pow(10, env.precision.finance_analysis_percentage);
    var all_percentage = 100;
    var percentage = 0;
    var i = 1;
    while ($('percentage_' + i)) {
        if ($('percentage_' + i).value) {
            percentage += Math.round(parseFloat($('percentage_' + i).value) * pow)/pow;
        }
        i++;
    }
    percentage = Math.round(percentage * pow)/pow;
    if (isNaN(percentage)) {
        percentage = 0;
    }

    $('distributed_percentage').innerHTML = percentage;
    var remaining_percentage = Math.round((all_percentage - percentage) * pow)/pow;
    $('remaining_percentage').innerHTML = remaining_percentage;
    if (remaining_percentage != 0) {
        removeClass($('remaining_percentage'), 'green');
        addClass($('remaining_percentage'), 'red');
        removeClass($('distributed_percentage'), 'green');
        addClass($('distributed_percentage'), 'red');
    } else {
        removeClass($('remaining_percentage'), 'red');
        addClass($('remaining_percentage'), 'green');
        removeClass($('distributed_percentage'), 'red');
        addClass($('distributed_percentage'), 'green');
    }
}

/**
 * Checks if total percentage has been distributed by elements for analysis item.
 */
function checkItemElementsPercentage() {
    recalculateItemElementsPercentage();

    var pow = Math.pow(10, env.precision.finance_analysis_percentage);
    var all_percentage = 100;
    var percentage = 0;
    var i = 1;
    var negative_values = false;
    while ($('percentage_' + i)) {
        if (!$('percentage_' + i).disabled) {
            element_percentage = Math.round(parseFloat($('percentage_' + i).value) * pow)/pow;
            percentage += element_percentage;
            if (element_percentage < 0) {
                negative_values = true;
            }
        }
        i++;
    }
    percentage = Math.round(percentage * pow)/pow;

    if (all_percentage == percentage && !negative_values) {
        return true;
    } else {
        return false;
    }
}

/**
 * Calculates total percentage per element distributed among centers of main center.
 *
 * @param element - input element
 */
function recalculateCentersPercentage(element) {
    var pow = Math.pow(10, env.precision.finance_analysis_percentage);
    var all_percentage = 100;
    var percentage = 0;
    var negative_values = false;
    var inputs = element.parentNode.parentNode.getElementsByTagName("input");
    var centers = Array();

    for (var i = 0; i < inputs.length; i++) {
        if (inputs[i].type == 'text' && inputs[i].value) {
            centers.push(inputs[i]);
        }
    }

    for (var i = 0; i < centers.length; i++) {
        element_percentage = Math.round(parseFloat(centers[i].value) * pow) / pow;
        percentage += element_percentage;
        if (element_percentage < 0) {
            negative_values = true;
        }
        //if current input is next to last, fill the last one with the remainder
        if ((i == centers.length - 2) && (element.id == centers[i].id) && !negative_values && (all_percentage >= percentage)) {
            $(centers[i+1].id).value = Math.round((all_percentage - percentage) * pow)/pow;
        }
    }

    percentage = Math.round(percentage * pow)/pow;
    if (isNaN(percentage)) {
        percentage = 0;
    }

    is_valid = (percentage == all_percentage) && !negative_values;

    for (var i = 0; i < centers.length; i++) {
        if (is_valid) {
            removeClass(centers[i], 'red');
            addClass(centers[i], 'green');
        } else {
            removeClass(centers[i], 'green');
            addClass(centers[i], 'red');
        }
    }

    return is_valid;
}

/**
 * Checks if total percentage has been distributed by elements among centers of main center.
 */
function checkCentersPercentage() {
    var first_center_id = $('first_center_id').value;
    var elements_factories = Array('customers', 'offices', 'nomenclatures', 'projects');

    var i = 0;
    var all_valid = true;
    var row_valid = true;

    if (first_center_id) {
        for (var j = 0; j < elements_factories.length; j++) {
            i = 0;
            var center_name = elements_factories[j] + '_percentage_' + first_center_id + '_';
            while ($(center_name + '_' + i)) {
                row_valid = recalculateCentersPercentage($(center_name + '_' + i));
                all_valid = all_valid && row_valid;
                i++;
            }
        }
        return all_valid;
    }

    return true;
}

/**
 * Shows either incomes or expenses table for budgets.
 *
 * @param element - input element
 */
function toggleFinanceBudgetsItemsType(element) {
    var new_type = element.id.replace(/_switch/, '');
    var current_type = (new_type == 'income') ? 'expense' : 'income';

    if ($(current_type + '_switch')) {
        $(current_type + '_row').style.display = 'none';
        $(new_type + '_row').style.display = '';

        removeClass($(current_type + '_switch').parentNode, 'selected');
        addClass($(new_type + '_switch').parentNode, 'selected');
    }

    //clear item management form
    if ($('item_container')) {
        $('item_container').innerHTML = '';
    }
    //clear messages
    if ($(current_type + '_msg_container')) {
        $(current_type + '_msg_container').innerHTML = '';
    }

    return true;
}

/**
 * Shows/hides columns with month amounts for budgets.
 *
 * @param element - input element
 */
function toggleFinanceBudgetsMonths(element) {
    var new_mode = element.className.replace(/.*(expand|collapse).*/, '$1');
    var current_mode = new_mode == 'expand' ? 'collapse' : 'expand';
    var current_class_name = element.className;
    var current_class_name_other = '';
    if (current_class_name.match(/left/)) {
        current_class_name_other = current_class_name.replace(/left/, 'right');
    } else if (current_class_name.match(/right/)) {
        current_class_name_other = current_class_name.replace(/right/, 'left');
    }
    var regex = new RegExp(new_mode);

    elements = $$('.' + current_class_name);
    for (var i = 0; i < elements.length; i++) {
        elements[i].className = current_class_name.replace(regex, current_mode);
    }

    elements = $$('.' + current_class_name_other);
    for (var i = 0; i < elements.length; i++) {
        elements[i].className = current_class_name_other.replace(regex, current_mode);
    }

    elements = $$('.month_amount');
    for (var i = 0; i < elements.length; i++) {
        elements[i].style.display = new_mode == 'collapse' ? 'none' : '';
    }

    Cookie.set('finance_budgets_months', (new_mode == 'collapse' ? 'off' : 'on'));

    return true;
}

/**
 * Checks if certain conditions in budget are satisfied in order to change budget status.
 */
function checkFinanceBudgetsOnStatusChange() {
    var status = $('status').value;
    var valid = true;

    switch (status) {
        case 'progress':
            var currentDate = new Date();
            currentDate = currentDate.format("Y-m-d H:i:00");

            //get all assignments and deadlines
            elements = $$('#income_row select').concat($$('#expense_row select'));
            elements = elements.concat($$('#income_row td.deadline input[type=hidden].datetimebox'));
            elements = elements.concat($$('#expense_row td.deadline input[type=hidden].datetimebox'));

            for (var i = 0; i < elements.length; i++) {
                if (elements[i].id.match(/_(responsible|controlling)_/) && !parseInt(elements[i].value) ||
                elements[i].id.match(/_deadline_/) && (!elements[i].value || elements[i].value <= currentDate)) {
                    addClass(elements[i].parentNode, 'attention');
                    valid = false;
                } else {
                    removeClass(elements[i].parentNode, 'attention');
                }
            }
            if (!valid) {
                alert(i18n['messages']['error_finance_budgets_status_progress']);
            }
            break;
        case 'approved':
            //get all item statuses
            elements = $$('#income_row td.status input[type=hidden]');
            elements = elements.concat($$('#expense_row td.status input[type=hidden]'));

            for (var i = 0; i < elements.length; i++) {
                if (elements[i].value != 'approved') {
                    valid = false;
                    break;
                }
            }
            if (!valid) {
                alert(i18n['messages']['error_finance_budgets_status_approved']);
            }
            break;
        default:
            break;
    }

    return valid;
}

/**
 * Expands/collapses elements of analysis item in budget.
 *
 * @param form - the object form in the DOM
 * @param div - toggle div
 * @param input_id - id of hidden input element holding id of item
 * @param tbl_id - id of income/expense table
 */
function toggleFinanceBudgetsItem(form, div, input_id, tbl_id) {
    var input = $(input_id);
    var row_index = $(input.parentNode.parentNode.id).rowIndex;
    var tbl = $(tbl_id);
    //the new mode
    var toggle_mode = div.className.match(/expand/) ? 'expand' : 'collapse';

    if (toggle_mode == 'expand') {
        div.className = div.className.replace(/expand/, 'collapse');

        if (tbl.rows[row_index + 1] && tbl.rows[row_index + 1].id.match(/element/)) {
            tbl.rows[row_index + 1].style.display = '';
        } else {
            var item_id = input.value;
            var id = $('id') ? $('id').value : '0';

            Effect.Center('loading');
            Effect.Appear('loading');

            var opt = {
                method: 'post',
                parameters: Form.serialize(form),
                onSuccess: function(t) {
                    var result = t.responseText;
                    if (!checkAjaxResponse(result)) {
                        return;
                    }

                    //eval inline scripts in template
                    result.evalScripts();

                    var el_row = tbl.insertRow(row_index + 1);
                    el_row.id = tbl.rows[row_index].id.replace(/item/, 'element');
                    var cell0 = el_row.insertCell(0);
                    cell0.className = 't_border';
                    var cell = el_row.insertCell(1);
                    cell.colSpan = 18;
                    cell.style.padding = '0px';
                    cell.style.border = '0px';

                    cell.innerHTML = result;

                    Effect.Fade('loading');
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };
            var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.action_param + '=ajax_toggle_item&item_id=' + item_id + '&id=' + id + '&model_lang=' + $('model_lang').value;

            new Ajax.Request(url, opt);
        }
    } else {
        if (tbl.rows[row_index + 1] && tbl.rows[row_index + 1].id.match(/element/)) {
            tbl.rows[row_index + 1].style.display = 'none';
            div.className = div.className.replace(/collapse/, 'expand');
        }
    }
}

/**
 * AJAX functionality enter/control data of analysis item in budget
 *
 * @param item_id - id of item
 * @param operation - enter or control
 * @param div_id - id of div where form for item management should be loaded
 */
function manageFinanceBudgetsItem(item_id, operation, div_id) {
    var id = $('id') ? $('id').value : '0';

    $(($('income_row').style.display == '' ? 'income' : 'expense') + '_msg_container').innerHTML = '';

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }
            $(div_id).innerHTML = result;
            Effect.Fade('loading');
            new Effect.ScrollTo(div_id);
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.action_param + '=ajax_manage_item&item_id=' + item_id + '&id=' + id + '&operation=' + operation;

    new Ajax.Request(url, opt);
}

/**
 * Shows/hides elements of budget item for amounts to be entered by elements/items.
 *
 * @param element - checkbox element
 */
function toggleFinanceBudgetsItemDetailed(element) {

    var detailed = element.checked;
    var toggle_visible = $$('#item_container .detailed');
    var toggle_disabled = $$('#item_container tr.detailed input');
    var toggle_readonly = $$('#item_container tr.parent_item input[type=text].month_amounts');

    if (toggle_disabled.length) {
        for (var i = 0; i < toggle_visible.length; i++) {
            toggle_visible[i].style.display = detailed ? '' : 'none';
        }
        for (var i = 0; i < toggle_disabled.length; i++) {
            toggle_disabled[i].disabled = !detailed;
        }
        for (var i = 0; i < toggle_readonly.length; i++) {
            toggle_readonly[i].readOnly = detailed;
            if (detailed) {
                addClass(toggle_readonly[i], 'readonly');
            } else {
                removeClass(toggle_readonly[i], 'readonly');
            }
        }
    }

    return true;
}

/**
 * AJAX functionality saves item (and element) amounts for budget
 *
 * @param form - the object form in the DOM
 * @param row_id - id of table row where response should be loaded
 * @param action - enter or control
 */
function saveFinanceBudgetsItem(form, row_id, action) {
    if (!itemStatusCheckRequiredComment()) {
        return false;
    }

    updateFinanceBudgetsItemTotal($('submit'));

    var id = $('budget').value;

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        parameters: Form.serialize(form),
        onSuccess: function(t) {
            //alert(t.responseText);
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }

            $('item_container').innerHTML = '';
            new Effect.ScrollTo(row_id);

            $(row_id).cells[0].innerHTML = result;
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.action_param + '=' + action + '&' + action + '=' + id + '&use_ajax=1';

    new Ajax.Request(url, opt);
}

/**
 * Updates total amounts for item
 *
 * @param field - input field
 */
function updateFinanceBudgetsItemTotal(field) {
    //setting for calculation precision
    var pow = Math.pow(10, env.precision.gt2_rows);

    var detailed = $('item_by_elements').checked;

    var row_ids = Array();
    if (field.id == 'submit') {
        if (detailed) {
            var e = 1;
            while ($('element_row_' + e)) {
                row_ids.push('element_row_' + e);
                e++;
            }
        } else {
            row_ids.push('parent_item_row');
        }
    } else {
        row_ids.push(field.parentNode.parentNode.id);
    }

    //update yearly totals
    for (var r = 0; r < row_ids.length; r++) {
        var data_amount = 0;
        var month_fields = $$('#' + row_ids[r] + ' .month_amounts');
        for (var i = 0; i < month_fields.length; i++) {
            //replace commas with decimal point
            //remove all none digits and decimal points
            var field_value = month_fields[i].value.replace(/,/g, '.').replace(/[^\d\.]/g, '');
            //leave only the last decimal point
            if (field_value.split('.').length > 2) {
                var idx = field_value.lastIndexOf(".");
                field_value = field_value.substr(0, idx).replace(/\./g, '') + field_value.substr(idx);
            }
            month_fields[i].value = field_value;
            var field_amount = Math.round(parseFloat(field_value) * pow) / pow;
            if (isNaN(field_amount)) {
                field_amount = 0;
                month_fields[i].value = 0;
            }
            data_amount += field_amount;
        }

        var data_field = $(month_fields[0].id.replace(/month_amount_\d+/, 'data_amount'));
        if (data_field) {
            data_field.value = Math.round(parseFloat(data_amount) * pow) / pow;
        }
    }

    var item_has_elements = true;
    var element_rows = $$('tr.detailed');
    if (element_rows && element_rows.length == 1 && element_rows[0].style.display == 'none') {
        item_has_elements = false;
    }

    //update monthly amounts and data amount of item from amounts of elements
    if (detailed && item_has_elements) {
        var month_ids = Array();
        if (field.id == 'submit') {
            for (var m = 1; m <= 12; m++) {
                month_ids.push(m);
            }
        } else {
            month_ids.push(field.id.replace(/month_amount_(\d+)_\d+/, '$1'));
        }
        for (var i = 0; i <= month_ids.length; i++) {
            var month_amount_total = 0;
            var month_field_total = '';
            var e = 1;
            while ($('month_amount_' + month_ids[i] + '_' + e)) {
                var field = $('month_amount_' + month_ids[i] + '_' + e);
                if (field.parentNode.parentNode.id.match(/element_row_\d+/)) {
                    var field_amount = Math.round(parseFloat(field.value) * pow) / pow;
                    if (isNaN(field_amount)) {
                        field_amount = 0;
                        field.value = 0;
                    }
                    month_amount_total += field_amount;
                } else {
                    month_field_total = field;
                }
                e++;
            }
            if (month_field_total) {
                month_field_total.value = Math.round(parseFloat(month_amount_total) * pow) / pow;
            }
        }

        var data_amount_total = 0;
        var month_fields_total = $$('#parent_item_row .month_amounts');
        for (var i = 0; i < month_fields_total.length; i++) {
            var field_amount = Math.round(parseFloat(month_fields_total[i].value) * pow) / pow;
            if (isNaN(field_amount)) {
                field_amount = 0;
                month_fields_total[i].value = 0;
            }
            data_amount_total += field_amount;
        }
        var data_field_total = $(month_fields_total[0].id.replace(/month_amount_\d+/, 'data_amount'));
        if (data_field_total) {
            data_field_total.value = Math.round(parseFloat(data_amount_total) * pow) / pow;
        }
    }

    return true;
}

/**
 * Check if comment is required during a status change
 *
 * @params element - DOM field object containing status input
 */
function itemStatusCheckRequiredComment() {
    var requires_comment = $('item_requires_comment');
    if (requires_comment) {
        requires_comment = requires_comment.value;
    } else {
        requires_comment = '0';
    }

    if (requires_comment == '1') {
        var comment = $('item_comment');
        if (!comment || !comment.value) {
            alert((i18n.messages['error_status_requires_comment']));
            return false;
        } else {
            return true;
        }
    } else {
        return true;
    }
}

/**
 * Checks if item status is changed and shows whether comment is required.
 *
 * @param element - dropdown element
 */
function itemStatusRequiresComment(element) {
    var requires_comment = $('item_requires_comment');
    var required_comment = $('item_required_comment');
    var comment = $('item_comment');

    if (requires_comment && required_comment && comment) {
        var new_status = element.value;
        var old_status = $('status_old').value;

        if (new_status != old_status) {
            required_comment.style.display = '';
            requires_comment.value = '1';
            comment.value = '';
        } else {
            required_comment.style.display = 'none';
            requires_comment.value = '0';
            comment.value = '';
        }
    }

    return true;
}

/**
 * Updates text for total income/expense amounts with new currency when changing currrency in add mode.
 *
 * @param select - currency dropdown
 */
function updateFinanceBudgetsCurrency(select) {
    var new_currency = select.value;
    var budget_amount_spans = $$('.budget_amount');
    for (var i = 0; i < budget_amount_spans.length; i++) {
        budget_amount_spans[i].innerHTML = budget_amount_spans[i].innerHTML.replace(/\w+$/, new_currency);
    }
    return true;
}

/**
 * Validates budget's STATUS change
 *
 * @param element - element object
 */
function validateBudgetStatusChange(element) {
    var current_status = $('current_status_base').value;
    var flag_status_error = false;

    var changed_status = element.value;
    if (changed_status != current_status) {
        if (current_status == 'approved' || current_status == 'obsolete') {
            flag_status_error = true;
        } else if (current_status == 'progress' && changed_status == 'preparation') {
            flag_status_error = true;
        } else if (current_status == 'preparation' && changed_status == 'approved') {
            flag_status_error = true;
        }
    }

    if (flag_status_error) {
        var status_id = 'status_' + current_status;
        $(status_id).checked = true;
        return false;
    } else {
        status_requires_comment_id = 'requires_comment_' + changed_status;
        var status_requires_comment = $(status_requires_comment_id).value;
        if (status_requires_comment == 'requires_comment') {
            $('available_comment_table').style.visibility = 'visible';
            $('required_comment').style.visibility = 'visible';
            $('requires_comment').value = '1';
            $('comment').value = '';
        } else if (status_requires_comment == 'optional_comment') {
            $('available_comment_table').style.visibility = 'visible';
            $('required_comment').style.visibility = 'hidden';
            $('requires_comment').value = '0';
            $('comment').value = '';
        } else {
            $('available_comment_table').style.visibility = 'hidden';
            $('required_comment').style.visibility = 'hidden';
            $('requires_comment').value = '0';
            $('comment').value = '';
        }
        return true;
    }
}

/**
 * Toggles visibility and change labels of fields in "Add" action when type selection is changed.
 *
 * @param element - select element for type
 * @param doc_type - id of commodities transfer document type
 */
function toggleCommoditiesTransferAddFields(element, doc_type) {
    // holds underscores suffix to append to ids of other fields
    var u_suffix = element.id.replace('type', '');
    var doc_type_is_selected = (element.value == doc_type);
    // toggle visibility
    var field = $('to_warehouse_data' + u_suffix);
    if (field != null) {
        field.disabled = !doc_type_is_selected;
        field.parentNode.parentNode.style.display =
        field.style.display = doc_type_is_selected ? '' : 'none';
    }
    // change label
    field = $('warehouse_data' + u_suffix);
    if (field != null) {
        field.title = doc_type_is_selected ? i18n['labels']['from_warehouse'] : i18n['labels']['warehouse'];
        field.parentNode.parentNode.children[0].innerHTML = field.title + ':';
    }

    return true;
}

/**
 * Expands/collapses preview of invoice in the list of invoice templates.
 *
 * @param params - template id as recorded in fin_invoices_templates_info DB table or invoice id
 */
function toggleInvoicePreview(params) {
    //if a subelement has been clicked
    if (isSubelementClicked()) {
        return true;
    }

    //define modes
    var source = 0;
    var mode = 'invoice';
    if (params.invoice) {
        source = params.invoice;
    } else if (params.expenses_reason) {
        source = params.expenses_reason;
        mode = 'expenses_reason';
    } else if (params.template) {
        source = params.template;
        mode = 'template';
    }
    var switch_id = 'switch_invoice_' + source;
    var container_id = 'invoice_' + source;
    var container_id_preview = container_id + '_preview';
    var get_data = false;
    var toggle_mode = 'expand';
    if (!$(container_id_preview)) {
        get_data = true;
    } else if ($(container_id_preview).style.display != 'none') {
        var toggle_mode = 'collapse';
    }
    var row_index = $(container_id).rowIndex;
    var tbl = $(container_id).parentNode.parentNode;

    if (get_data) {
        Effect.Center('loading');
        Effect.Appear('loading');

        var opt = {
            method: 'get',
            onSuccess: function(t) {
                var result = t.responseText;
                if (!checkAjaxResponse(result)) {
                    return;
                }

                var container_preview = tbl.insertRow(row_index + 1);
                container_preview.id = container_id_preview;
                var cell = container_preview.insertCell(0);
                cell.colSpan = tbl.rows[row_index].cells.length;
                cell.style.borderBottom = '1px solid #BBBBBB';
                cell.style.borderTop = '1px solid #BBBBBB';

                var div1 = document.createElement('div');
                div1.style.borderBottom = '1px solid #CCCCCC';
                div1.style.borderTop = '1px solid #CCCCCC';

                var div2 = document.createElement('div');
                div2.innerHTML = result;
                div2.style.borderBottom = '1px solid #DDDDDD';
                div2.style.borderTop = '1px solid #DDDDDD';
                div2.style.padding = '5px';

                div_clear = document.createElement('div');
                div_clear.className = 'clear';
                div2.appendChild(div_clear);

                cell.appendChild(div2);

                $(switch_id).className = 'switch_' + ((toggle_mode == 'expand') ? 'collapse' : 'expand');

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=finance&';
        if (mode == 'invoice') {
            url += env.controller_param + '=incomes_reasons&incomes_reasons=ajax_preview_invoice&invoice=' + source;
        } else if (mode == 'expenses_reason') {
            url += env.controller_param + '=expenses_reasons&expenses_reasons=ajax_preview_invoice&expenses_reason=' + source;
        } else {
            url += env.controller_param + '=invoices_templates&invoices_templates=ajax_preview_invoice&template=' + source;
        }

        new Ajax.Request(url, opt);
    } else {
        if (toggle_mode == 'expand' && $(container_id_preview)) {
            $(container_id_preview).style.display = '';
        } else if (toggle_mode == 'collapse' && $(container_id_preview)) {
            $(container_id_preview).style.display = 'none';
        }
        $(switch_id).className = 'switch_' + ((toggle_mode == 'expand') ? 'collapse' : 'expand');
    }

    return true;
}

/**
 * Expands/collapses preview of invoices for approving.
 *
 * @param params - template id as recorded in fin_invoices_templates_info DB table
 */
function toggleInvoicePreview2(params) {

    var source = params.template;
    var mode = 'invoice';
    if (params.mode) {
        mode = params.mode;
    }

    var switch_id = 'switch_' + mode + '_' + source;
    var container_id = mode + '_' + source;
    var container_id_preview = container_id + '_preview';

    if ($(container_id_preview).style.display == 'none') {
        var toggle_mode = 'expand';
    } else if ($(container_id_preview).style.display != 'none') {
        var toggle_mode = 'collapse';
    }

    if (toggle_mode == 'expand' && $(container_id_preview)) {
        $(container_id_preview).style.display = '';
        cells = $(container_id).cells;
        for (var i = 0; i < cells.length; i++) {
            cells[i].style.borderBottom = '1px solid #cccccc';
        }
        if (mode == 'invoice' && params.parent_doc) {
            rows = $$('.cd_list_' + params.parent_doc);
            if (rows) {
                for (var i = 0; i < rows.length; i++) {
                    if (!rows[i].id.match(/_preview$/)) {
                        rows[i].style.display = '';
                    }
                }
            }
        }
    } else if (toggle_mode == 'collapse' && $(container_id_preview)) {
        $(container_id_preview).style.display = 'none';
        cells = $(container_id).cells;
        for (var i = 0; i < cells.length; i++) {
            cells[i].style.borderBottom = 'none';
        }
        if (mode == 'invoice' && params.parent_doc) {
            rows = $$('.cd_list_' + params.parent_doc);
            if (rows) {
                for (var i = 0; i < rows.length; i++) {
                    rows[i].style.display = 'none';
                }
            }
        }
    }
    $(switch_id).className = 'switch_' + ((toggle_mode == 'expand') ? 'collapse' : 'expand');

    return true;
}

/**
 * This function cares about field locking
 * when we create correction document or credit/debit notice
 *
 * @param source - the element whose value we change
 */
function preventCorrectionColisions(source, is_invoice) {

    var fields = ['quantity', $('calc_price').value, 'surplus_value', 'surplus_percentage', 'discount_value', 'discount_percentage'];
    if (!source) {
        //process all rows
        var fixed = [];
        for (var k = 0; k < fields.length; k++) {
            //get all fields
            var els = $$('.grouping_table2 .' + fields[k]);
            for (var l = 0; l < els.length; l++) {
                if (!fixed[l] && preventCorrectionColisions(els[l], is_invoice)) {
                    fixed[l] = true;
                }
            }
        }

        return true;
    }

    //get row index
    var row = source.id.replace(/^.*(_\d+)$/, '$1');
    var name = source.id.replace(/^(.*)_\d+$/, '$1');
    var r = source.parentNode.parentNode;

    if (is_invoice && r.className && r.className.match(/readonly/)) {
        return true;
    }
    //check if the element's value is changed
    var cmp = $(name + '_duplicate' + row);
    if (cmp) {
        if (source.value == '') {
            source.value = 0.00;
        }
        if (cmp.value == '') {
            cmp.value = 0.00;
        }
    }

    // if a surplus or a discount field is changed, both S/D fields need
    // to be checked because entering S clears D or the other way
    if (source.id.match(/^(surplus|discount)/)) {
        var name_opposite = name.match(/^surplus/) ? name.replace(/^surplus/, 'discount') : name.replace(/^discount/, 'surplus');
        var source_opposite = $(name_opposite + row);
        var cmp_opposite = $(name_opposite + '_duplicate' + row);
        if (source_opposite.value == '') {
            source_opposite.value = 0.00;
        }
        if (cmp_opposite.value == '') {
            cmp_opposite.value = 0.00;
        }
        // in the very unlikely case when both surplus and discount fields are editable
        if (source_opposite.value != cmp_opposite.value) {
            for (var j = 0; j < fields.length; j++) {
                target = $(fields[j] + row);
                // we will leave the surplus/discount fields editable
                // (so the user can return the initial state) and lock the rest
                if (fields[j].match(/^(surplus|discount)/)) {
                    if (target && !target.className.match(/system_readonly/)) {
                        removeClass(target, 'readonly');
                        target.readOnly = false;
                    }
                } else {
                    if (target && (target.parentNode.parentNode.className.match(/readonly/) || is_invoice)) {
                        addClass(target, 'readonly');
                        target.readOnly = true;
                    }
                }
            }
            return true;
        }
    }

    if (!cmp || parseFloat(source.value) == parseFloat(cmp.value)) {
        for (var j = 0; j < fields.length; j++) {
            if (name == fields[j]) {
                continue;
            }
            target = $(fields[j] + row);
            if (target && !target.className.match(/system_readonly/)) {
                removeClass(target, 'readonly');
                target.readOnly = false;
            }
        }
        return false;
    }

    //value has been changed so disable the other elements
    for (var j = 0; j < fields.length; j++) {
        if (name == fields[j]) {
            continue;
        }
        target = $(fields[j] + row);
        if (target && (target.parentNode.parentNode.className.match(/readonly/) || is_invoice)) {
            addClass(target, 'readonly');
            target.readOnly = true;
        }
    }

    return true;
}

/**
 * Updates transfered amount and currency rate when from/to company_data
 * selection is changed
 * OR
 * updates container amount and currency rate for payment that would be
 * automatically issued from incomes/expenses reason
 * when company_data selection is changed.
 *
 * @param element - from/to company_data dropdown
 */
function changeContainer(element) {
    var company_data_array = element.value.split('_');
    var prefix = '';
    var container_currency_label = '';
    var amount = 0;
    if (element.id.match(/^(from|to).*/)) {
        // transfer
        prefix = element.id.replace(/^(from|to).*/, '$1');
        container_currency_label = prefix + '_' + prefix + '_currency';
        amount = $('amount').value;
    } else {
        // payment
        prefix = 'container';
        if (!$(prefix + '_rate_data')) {
            // payment cannot be automatically issued from this action
            return true;
        }
        container_currency_label = 'to_currency';
        if ($('total_with_vat')) {
            amount = $('total_with_vat').value;
        } else if ($('payment_sum')) {
            // case for add payment
            amount = $('payment_sum').value;
        }
    }
    amount = Math.round(amount * 100)/100;
    if (isNaN(amount)) {
        amount = 0;
    }

    if (typeof(company_data_array[2]) != 'undefined' && company_data_array[2].match(/^(bank|cheque)$/)) {
        Effect.Center('loading');
        Effect.Appear('loading');
        element.disabled = true;

        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                var result = t.responseText;
                if (!checkAjaxResponse(result)) {
                    return;
                }

                $(prefix + '_currency').value = result;
                $(container_currency_label).innerHTML = result;

                if (result == $('currency').value) {
                    $(prefix + '_rate').value = 1;
                    $(prefix + '_amount').value = amount;
                    $(prefix + '_rate_data').style.display = 'none';
                } else {
                    getContainerCurrencyRate(prefix);
                    $(prefix + '_rate_data').style.display = '';
                }

                element.disabled = false;
                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=bank_accounts&bank_accounts=ajax_get_currency&container_id=' + company_data_array[3];

        new Ajax.Request(url, opt);

    } else {
        var rate_element = $(prefix + '_rate');
        rate_element.value = 1;
        removeClass(rate_element, 'refreshed');
        $(prefix + '_amount').value = amount;
        $(prefix + '_currency').value = $('currency').value;
        $(container_currency_label).innerHTML = $('currency').value;
        $(prefix + '_rate_data').style.display = 'none';
    }

    return true;
}

/**
 * Get the current amount of the selected container
 *
 * @param element - cashbox/bank account dropdown
 */
function getContainerAmount(element) {
    var container_type = element.value.replace(/^[0-9]*_[0-9]*_(cash|bank)_([0-9]*)/, '$1');
    var container_id = element.value.replace(/^[0-9]*_[0-9]*_(cash|bank)_([0-9]*)/, '$2');
    var direction = element.id.replace(/^(from|to)_(.*)$/, '$1');
    if (element.value) {
        selected_currency = $('currency').value;

        if (container_type && container_id) {
            Effect.Center('loading');
            Effect.Appear('loading');

            var opt = {
                method: 'get',
                asynchronous: false,
                onSuccess: function(t) {
                    var result = t.responseText;
                    if (!checkAjaxResponse(result)) {
                        return;
                    }
                    var data = eval('(' + t.responseText + ')');

                    $('current_container_' + direction + '_amount').value = data.amount;
                    $('current_container_' + direction + '_currency').value = data.currency;

                    Effect.Fade('loading');
                },
                on404: function(t) {
                    alert('Error 404: location "' + t.statusText + '" was not found.');
                },
                onFailure: function(t) {
                    alert('Error ' + t.status + ' -- ' + t.statusText);
                }
            };

            var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param +
                     '=transfers&transfers=ajax_get_container_amount&container_id=' + container_id +
                     '&container_type=' + container_type + '&selected_currency=' + selected_currency;

            new Ajax.Request(url, opt);
        }
    } else {
        $('current_container_' + direction + '_amount').value = '';
        $('current_container_' + direction + '_currency').value = '';
    }
    reacalculateCurrentContainerAmount(direction);
}

/**
 * Recalculate the conatiner amounts when shanging the amount for the transfers
 *
 * @param element - cashbox/bank account dropdown
 */
function reacalculateCurrentContainerAmount(direction) {
    var visible_amount_field = $('actual_' + direction + '_quantity');
    var current_amount_field = $('current_container_' + direction + '_amount');
    var container_currency = $('current_container_' + direction + '_currency');
    var change_value = 0;

    // if no container is selected - just clear the field
    if (!$(direction + '_company_data').value) {
        visible_amount_field.value = '';
        return;
    }

    // container currency could be empyt if nothing is selected for container
    if (container_currency.value == $('currency').value) {
        change_value = $('amount').value;
    } else if (container_currency.value != $('currency').value && $(direction + '_amount')) {
        change_value = $(direction + '_amount').value;
    }

    change_value = Math.round(change_value * 100)/100;
    if (isNaN(change_value)) {
        change_value = 0;
    }

    old_value = Math.round(current_amount_field.value * 100)/100;
    if (isNaN(old_value)) {
        old_value = 0;
    }

    // calcualte the amount
    if (direction == 'from') {
        new_value = old_value - change_value;
    } else {
        new_value = old_value + change_value;
    }
    visible_amount_field.value = new_value.toFixed(2);

    // color the amount if needed
    if (new_value < 0) {
        visible_amount_field.style.color = 'red';
    } else {
        visible_amount_field.style.color = '';
    }
}

/**
 * Gets conversion rate for transfer amount:
 * - from 'from_currency' to 'currency' or
 * - from 'currency' to 'to_currency'
 * OR
 * gets conversion rate for payment amount from currency of financial document
 * to currency of container.
 *
 * @param prefix - 'from' or 'to' or 'container'
 */
function getContainerCurrencyRate(prefix) {
    var old_currency = '';
    var new_currency = '';
    var currency_label = '';
    var container_currency_label = '';
    if (prefix == 'from') {
        old_currency = $(prefix + '_currency').value;
        new_currency = $('currency').value;
        currency_label = prefix + '_to_currency';
        container_currency_label = prefix + '_' + prefix + '_currency';
    } else if (prefix == 'to') {
        old_currency = $('currency').value;
        new_currency = $(prefix + '_currency').value;
        currency_label = prefix + '_from_currency';
        container_currency_label = prefix + '_' + prefix + '_currency';
    } else if (prefix == 'container') {
        old_currency = $('currency').value;
        new_currency = $(prefix + '_currency').value;
        currency_label = 'from_currency';
        container_currency_label = 'to_currency';
    }
    if (!old_currency || !new_currency) {
        return true;
    }

    // company data dropdown
    var company_data = prefix == 'container' ?
                       ($('new_payment_container') != null ? $('new_payment_container') : $('company_data')) :
                       $(prefix + '_' + 'company_data');
    var company_data_array = company_data.value.split('_');

    if (typeof(company_data_array[2]) != 'undefined' && company_data_array[2].match(/^(bank|cheque)$/) && old_currency != new_currency) {
        Effect.Center('loading');
        Effect.Appear('loading');
        company_data.disabled = true;
        $('currency').disabled = true;

        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return false;
                }
                var result = parseFloat(t.responseText);
                if (isNaN(result) || result <= 0) {
                    alert(i18n['messages']['error_gt2_currency_change']);
                    result = 0;
                } else {
                    result = Math.round(result * 1000000)/1000000;
                }

                var rate_element = $(prefix + '_rate');
                rate_element.value = result.toFixed(6);
                if (result > 0) {
                    addClass(rate_element, 'refreshed');
                } else {
                    removeClass(rate_element, 'refreshed');
                }
                calculateContainerAmount(rate_element);
                $(prefix + '_rate_data').style.display = '';

                company_data.disabled = false;
                $('currency').disabled = false;
                Effect.Fade('loading');

                return true;
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=currencies';
        url += '&currencies=ajax_get_rate&source=' + old_currency + '&destination=' + new_currency;
        new Ajax.Request(url, opt);
    } else {
        var rate_element = $(prefix + '_rate');
        rate_element.value = 1;
        removeClass(rate_element, 'refreshed');
        var amount = prefix.match(/^from|to$/) ? $('amount') : $('total_with_vat');
        amount = Math.round(amount.value * 100)/100;
        if (isNaN(amount)) {
            amount = 0;
        }
        $(prefix + '_amount').value = amount;
        $(prefix + '_currency').value = $('currency').value;
        $(container_currency_label).innerHTML = $('currency').value;
        $(prefix + '_rate_data').style.display = 'none';
    }

    // update currency label
    $(currency_label).innerHTML = $('currency').value;

    return true;
}

/**
 * Calculates transfered amount from/to bank account in bank account currency
 * OR
 * calculates amount of payment into currency of bank account.
 *
 * @param element - element holding currency conversion rate
 */
function calculateContainerAmount(element) {
    if (!element) {
        return true;
    }

    var prefix = '';
    var amount = 0;
    if (element.id.match(/^(from|to)_rate$/)) {
        prefix = element.id.replace(/^(from|to).*/, '$1');
        amount = $('amount').value;
    } else if (element.id == 'container_rate') {
        prefix = 'container';
        amount = $('payment_sum') ? $('payment_sum').value : $('total_with_vat').value;
    } else {
        return true;
    }
    amount = Math.round(amount * 100)/100;
    if (isNaN(amount)) {
        amount = 0;
    }
    var rate = Math.round(element.value * 1000000)/1000000;
    if (isNaN(rate)) {
        rate = 0;
    }

    var converted_amount = 0;
    if (rate > 0) {
        if (prefix == 'from') {
            converted_amount = Math.round(amount / rate * 100)/100;
        } else if (prefix == 'to' || prefix == 'container') {
            converted_amount = Math.round(amount * rate * 100)/100;
        }
        if (isNaN(converted_amount)) {
            converted_amount = 0;
        }
    }
    $(prefix + '_amount').value = converted_amount.toFixed(2);

    return true;
}

function showTypeAdditionalFields(element) {

    var type = element.value;
    var context = element.name.match(/aa[0-9]_/) ? 'after_action' : 'action';

    if (context == 'action') {
        $('addGo').disabled = true;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText + ';');
            if (result.error) {
                return false;
            }
            var table = (context == 'action') ? $$('#td_add_options table')[0] : $$('#after_options_add table')[0];
            var len = (context == 'action') ? 3 : 2;
            var display_company = true;
            if (table.rows.length > len) {
                if (result.invoice_autocompleter == '') {
                    //the selected type is neither credit notice, nor debit notice.
                    table.deleteRow(len-1);

                    //display the company row
                    table.rows[1].style.display = '';
                    table.rows[1].select("select")[0].style.display = '';
                } else {
                    //the row with the autocompleter for selecting invoice is already there... do nothing

                    //hide the company row (there is no need to select company when adding credit/debit, it is inheritted from the expense invoice)
                    display_company = false;
                }
            } else if (result.invoice_autocompleter != '') {
                //adding credit/debit notice
                //add new row for the additional options
                var row = table.insertRow(2);
                var cell_label = row.insertCell(0);
                cell_label.innerHTML = '<label for="invoice_id' + ((context != 'action') ? '____' : '') + '">' + result.label + '</label>';
                cell_label.className = 'labelbox';
                var cell_required = row.insertCell(1);
                cell_required.innerHTML = result.required;
                cell_required.className = 'required';
                var cell_autocompleter = row.insertCell(2);
                cell_autocompleter.id = 'expenses_add_options_';
                cell_autocompleter.innerHTML = result.invoice_autocompleter;

                //hide the company row (there is no need to select company when adding credit/debit, it is inheritted from the expense invoice)
                display_company = false;

                var scripts = $$('#expenses_add_options_ script');
                for (var i = 0; i < scripts.length; i++) {
                    if (scripts[i]) {
                        ajaxLoadJS(scripts[i]);
                    }
                }
            }
            table.rows[1].style.display = (display_company) ? '' : 'none';
            table.rows[1].select("select")[0].style.display = (display_company) ? '' : 'none';

            if (context == 'action') {
                $('addGo').disabled = false;
            }
            Effect.Fade('loading');

            return true;
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=expenses_reasons';
    url += '&expenses_reasons=ajax_get_type_fields&type=' + type + '&context=' + context;
    new Ajax.Request(url, opt);
}

/**
 * Function to activate lightbox where the data for the created payment will be entered
 */
function completeReasonsPaymentData(element, action) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        asynchronous:false,
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var data = eval('(' + t.responseText + ')');
            if (data.errors) {
                $('messages_container').innerHTML = data.errors;
                new Effect.ScrollTo('messages_container');
            } else if (data.result) {
                lb = new lightbox({content: data.result,
                                   title: i18n['labels']['payment_data'],
                                   icon: 'info.png',
                                   width: 550});
                lb.activate();
                $('parent_button_id').value = element.id;
                $('parent_button_action').value = action;
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.controller_name + '=ajax_prepare_payment_form';
    new Ajax.Request(url, opt);
}

/**
 * Function to complete the payment data (from lightbox) to the parent window
 */
function confirmAddPaymentWithCompanyData(element) {
    Effect.Center('loading');
    Effect.Appear('loading');

    if (!$('new_payment_container').value || !$('new_payment_date').value) {
        // if the container and date are not completed then an error is shown
        Effect.Fade('loading');
        alert(i18n['messages']['alert_empty_field']);
    } else {
        // transfer all the needed data in the parent window
        $('payment_container').value=$('new_payment_container').value;
        $('payment_date').value=$('new_payment_date').value;
        if ($('container_rate')) {
            $('payment_container_rate').value=$('container_rate').value;
        }
        if ($('container_amount')) {
            $('payment_container_amount').value=$('container_amount').value;
        }
        if ($('container_currency')) {
            $('payment_container_currency').value=$('container_currency').value;
        }
        if ($('new_payment_reason')) {
            $('payment_reason').value=$('new_payment_reason').value;
        }
        $('finance_after_action').value='payment';

        // the button which invoked the lightbox is taken because its form has to be submitted
        var invioce_button = $('parent_button_id').value;
        var action = $('parent_button_action').value;
        if (lb && lb.active) {
            lb.deactivate();
        }
        if (action == 'add_advances') {
            updateAdvances($(invioce_button).form, 'form_container', $(invioce_button).form.action);
        } else {
            $(invioce_button).form.submit();
            preventResubmit($(invioce_button).form);
        }
    }
}

/**
 * Display lightbox to choose warehouses for handovers issue
 */
function getWarehousesLB(model, direction) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText + ';');
            if (result.error) {
                alert(result.error);
            } else if (result.content) {
                lb = new lightbox({content: result.content,
                                   title: i18n['labels']['choose_warehouses'],
                                   icon: 'info.png',
                                   width: 550});
                lb.activate();
                if ($('warehouses_1') && $('warehouses_1').value && $('setWarehousesButton')) {
                    $('setWarehousesButton').focus();
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name;
    url += '&' + env.controller_name + '=ajax_get_warehouses&ajax_get_warehouses=' + model + '&direction=' + direction;
    new Ajax.Request(url, opt);
}

/**
 * Calculate distributed quantities for batch articles
 */
function calcWhQuantity(element, idx) {
    var action = '';
    if (calcWhQuantity.arguments.length > 2) {
        action = calcWhQuantity.arguments[2];
    }
    element = $(element);
    if (!element || element.className.match(/input_inactive/)) return;
    if (element.id.match(/article_(-)?(\d+)_wh/)) {
        //warehouse has been changed
        var old_wh_el = $(element.id.replace(/_wh_/, '_wh_old_'));
        var new_wh = parseInt(element.value);
        var new_quantity = parseFloat($(element.id.replace(/_wh_/, '_quantity_')).value);
        var old_quantity_el = $(element.id.replace(/_wh_/, '_quantity_old_'));
    } else {
        if (element.value.match(/^[^\.]*\./)) {
            //quantity has been changed
            var cnt = element.value.replace(/^[^\.]*\./, '');
            if (cnt.length > env.precision.gt2_quantity) {
                //fix the number of digits after the decimal point
                var pow = Math.pow(10, env.precision.gt2_quantity);
                element.value = Math.round(element.value * pow) / pow;
            }
        }
        var old_wh_el = $(element.id.replace(/_quantity_/, '_wh_old_'));
        var new_quantity = parseFloat(element.value);
        var new_wh = $(element.id.replace(/_quantity_/, '_wh_')) ? parseInt($(element.id.replace(/_quantity_/, '_wh_')).value) : '';
        var old_quantity_el = $(element.id.replace(/_quantity_/, '_quantity_old_'));
    }
    if (action) {
        if (action == 'enable') {
            var old_quantity = 0;
        } else {
            var old_quantity = parseFloat(old_quantity_el.value);
            var new_quantity = 0;
        }
    } else {
        var old_quantity = parseFloat(old_quantity_el.value);
    }
    var old_wh = parseInt(old_wh_el.value);
    if (isNaN(old_quantity)) {
        old_quantity= '';
    }
    if (isNaN(new_quantity)) {
        new_quantity = '';
    }
    if (isNaN(old_wh)) {
        old_wh= '';
    }
    if (isNaN(new_wh)) {
        new_wh = '';
    }
    if (old_wh && old_quantity) {
        var el = $('warehouse' + old_wh + '_quantity_' + idx);
        if (!el) {
            el = $('quantity_' + idx);
        }
        if (el) {
            if (!el.readOnly) {
                el.readOnly = true;
                addClass(el, 'readonly');
            }
            if (!el.value) {
                el.value = 0;
            }
            el.value = parseFloat(el.value) - old_quantity;
            if (el.onkeyup) {
                //execute GT2 calculations
                try {
                    el.onkeyup();
                } catch (e) {};
            }
        }
    }
    if (new_wh && new_quantity) {
        var el = $('warehouse' + new_wh + '_quantity_' + idx);
        if (!el) {
            el = $('quantity_' + idx);
        }
        if (el) {
            if (!el.readOnly) {
                el.readOnly = true;
                addClass(el, 'readonly');
            }
            if (!el.value) {
                el.value = 0;
            }
            el.value = parseFloat(el.value) + new_quantity;
            if (el.onkeyup) {
                //execute GT2 calculations
                try {
                    el.onkeyup();
                } catch (e) {};
            }
        }
    }
    old_wh_el.value = new_wh;
    old_quantity_el.value = new_quantity;
}

let batchUniqueKeyElementsNames = [];
function prepareBatchUniqueKeyElementsNames() {
    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'finance');
    url.searchParams.set(env.controller_param, 'warehouses_documents');
    url.searchParams.set('warehouses_documents', 'ajax_get_batch_unique_key_elements_names');

    fetch(url, {
        headers: {
            'Accept': 'application/json',
        },
    }).
        then(response => response.json()).
        then(data => {
            batchUniqueKeyElementsNames = data;
        }).
        catch((error) => {
            // TODO: on HTTP ERROR
        });
}
prepareBatchUniqueKeyElementsNames();

/**
 * Calculate current quantity for GT2 row of warehouse document - if we have
 * spans with it for batch rows
 *
 * @param {number} idx - row index
 */
function recalcAvailableQuantity(idx) {

    var current = $('current_quantity_' + idx);
    if (current) {
        var quantities = $$('#articles_' + idx + '_batch .active_available');
        var sum = 0;
        var prec = 0;
        var count = quantities.length;
        var unique = new Object();
        var u, q, cnt, f;
        for (var i = 0; i < count; i++) {
            if (quantities[i].parentNode.parentNode.className.match(/input_inactive/)) {
                continue;
            }
            u = [];
            cnt = quantities[i].parentNode;
            cnt.id = cnt.parentNode.id + '_' + i;

            q = $$('#' + cnt.id + ' .quantity')[0];

            f = $(q.id.replace(/_quantity_/, '_batch_'));
            if (f && batchUniqueKeyElementsNames.includes('batch_id')) {
                u.push(f.value);
            }
            f = $(q.id.replace(/_quantity_/, '_delivery_price_'));
            if (f && batchUniqueKeyElementsNames.includes('delivery_price')) {
                u.push(f.value);
            }
            f = $(q.id.replace(/_quantity_/, '_currency_'));
            if (f && batchUniqueKeyElementsNames.includes('currency')) {
                u.push(f.value);
            }
            f = $(q.id.replace(/_quantity_/, '_expire_'));
            if (f && batchUniqueKeyElementsNames.includes('expire_date')) {
                u.push(f.value);
            }
            f = $(q.id.replace(/_quantity_/, '_serial_'));
            if (f && batchUniqueKeyElementsNames.includes('serial_number')) {
                u.push(f.value);
            }
            f = $(q.id.replace(/_quantity_/, '_custom_'));
            if (f && batchUniqueKeyElementsNames.includes('cstm_number')) {
                u.push(f.value);
            }
            cnt.id = "";

            u = u.join("^^^");
            // TODO: Not sure what will happen if the uniqueness key is shorter
            if (unique[u]) {
                continue;
            }
            unique[u] = 1;
            var v = quantities[i].innerHTML.replace(/[^\d\.]*/g, '');
            prec = v.replace(/^.*\.(.*)$/, '$1').length;
            v = parseFloat(v);
            sum += v;
        }
        current.value = sum.toFixed(prec);
        gt2calc($('difference_' + idx));
    }
}

/**
 * Gets article batch data for a given batch and article
 */
function getExistingBatchData(element) {

    var model = '', controller = '', el;
    if ($('link_to') != null) {
        model = $('link_to').value;
        controller = 'warehouses_documents';
    } else if ($('warehouse_data') != null) {
        model = $('warehouse_data').value.match(/^\d+_\d+_\d+$/) ? $('warehouse_data').value.replace(/.*_(\d+)$/, '$1') : '';
        controller = 'warehouses';
    }
    var parent_idx = element.parentNode.parentNode.id.replace(/articles_(\d+)_batch_\d+/, '$1');
    var article = $('article_id_' + parent_idx).value;

    element = $(element);
    var batch = element.value;
    eval ('var vars_settings = ' + $(element.id.replace(/batch(_code)?.*/, 'vars_settings')).value + ';');
    if (!vars_settings) {
        vars_settings = new Object();
    }
    //if custom batch is specified via combo
    if (element.options[element.options.selectedIndex].className == 'undefined' || getExistingBatchData.arguments.length > 1) {
        //custom batch or force cleaning

        //empty the expire
        if (el = $(element.id.replace(/batch(_code)?/, 'expire_formatted'))) {
            el.value = defaultDate;
            if (!vars_settings.expire || !vars_settings.expire.readonly || vars_settings.expire.readonly == 0) {
                el.readOnly = false;
                removeClass(el, 'readonly');
                calendarInit(el.id, el.id, false, env.date_short, '%Y-%m-%d', false);
            }
            $(element.id.replace(/batch(_code)?/, 'expire')).value = '';
        }
        if (controller == 'warehouses_documents') {
            // we will be here when we are in a handover
            // and just want to use a new custom batch
            // one batch defines unique expire date and price
            return true;
        }
        //empty the serial
        if (el = $(element.id.replace(/batch(_code)?/, 'serial'))) {
            container = el.parentNode;
            var params = new Object();
            params = {
                name: el.id.replace(/_\d+$/, ''),
                index: el.id.replace(/^.*_(\d+)$/, '$1'),
                type: 'text'
            };
            if (vars_settings.serial && vars_settings.serial.readonly && vars_settings.serial.readonly > 0) {
                params.readOnly = true;
                params.class_name = 'readonly';
            }
            if (ro_el = $(el.id.replace(/serial/, 'serial_readonly'))) {
                container.removeChild(ro_el);
            }
            container.removeChild(el);
            el = createField(params, container);
            el.style.width = '100%';
        }
        //empty the custom
        if (el = $(element.id.replace(/batch(_code)?/, 'custom'))) {
            container = el.parentNode;
            var params = new Object();
            params = {
                name: el.id.replace(/_\d+$/, ''),
                index: el.id.replace(/^.*_(\d+)$/, '$1'),
                type: 'text'
            };
            if (vars_settings.custom && vars_settings.custom.readonly && vars_settings.custom.readonly > 0) {
                params.readOnly = true;
                params.class_name = 'readonly';
            }
            if (ro_el = $(el.id.replace(/custom/, 'custom_readonly'))) {
                container.removeChild(ro_el);
            }
            container.removeChild(el);
            el = createField(params, container);
            el.style.width = '100%';

        }
        if ((el = $(element.id.replace(/batch(_code)?/, 'quantity')))) {
            var span = el.parentNode.getElementsByTagName('span');
            for (var i = span.length - 1; i >= 0; i--) {
                el.parentNode.removeChild(span[i]);
            }
            el.style.width = '100%';
        }
        //empty the delivery price and currency
        if ((el = $(element.id.replace(/batch(_code)?/, 'delivery_price'))) && el.type == 'text') {
            el.value = '';
            el.style.display = '';
            if (!vars_settings.delivery_price || !vars_settings.delivery_price.readonly || vars_settings.delivery_price.readonly == 0) {
                el.readOnly = false;
                removeClass(el, 'readonly');
            }
            if ($(el.id + '_formatted')) {
                el.parentNode.removeChild($(el.id + '_formatted'));
            }
            if ((el = $(element.id.replace(/batch(_code)?/, 'currency')))) {
                el.value = '';
                if (el.type.match(/select/)) {
                    if (el.options.length) {
                        el.value = el.options[0].value;
                    }
                    el.style.display = '';
                    if (!vars_settings.delivery_price || !vars_settings.delivery_price.readonly || vars_settings.delivery_price.readonly == 0) {
                        el.readOnly = false;
                        removeClass(el, 'readonly');
                    }
                }
                if ((el = $(element.id.replace(/batch(_code)?/, 'currency_readonly')))) {
                    el.value = '';
                    el.style.display = '';
                    if (!vars_settings.delivery_price || !vars_settings.delivery_price.readonly || vars_settings.delivery_price.readonly == 0) {
                        el.readOnly = false;
                        removeClass(el, 'readonly');
                    }
                }
            }
        }
        recalcAvailableQuantity(parent_idx);
        return true;
    }
    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval('var result = ' + t.responseText + ';');
            if (result.length === 0) {
                Effect.Fade('loading');
                //!!! force cleaning
                getExistingBatchData(element, true);
                return true;
            }
            //set the expire date
            if (el = $(element.id.replace(/batch(_code)?/, 'expire_formatted'))) {
                el.value = result.expire_formatted;
                el.readOnly = true;
                addClass(el, 'readonly');
                $(element.id.replace(/batch(_code)?/, 'expire')).value = result.expire;
            }
            if (controller == 'warehouses_documents') {
                // we will be here when we are in a handover
                // and just want to use an existing batch
                // one batch defines unique expire date and price
                Effect.Fade('loading');
                return true;
            }

            //set batch data object text in hidden element
            //to be used later
            el = element.id.replace(/batch(_code)?/, 'batch_data_json_object');
            if ($(el)) {
                $(el).value = t.responseText;
            } else {
                var params = new Object();
                params = {
                    name: el.replace(/_\d+$/, ''),
                    index: el.replace(/^.*_(\d+)$/, '$1'),
                    type: 'hidden',
                    value: t.responseText,
                    disabled: true,
                    class_name: 'dont_copy_values'
                };
                createField(params, element.parentNode);
            }
            //set the serial number
            var serials = '';
            if (el = $(element.id.replace(/batch(_code)?/, 'serial'))) {
                container = el.parentNode;
                var params = new Object();
                params = {
                    name: el.id.replace(/_\d+$/, ''),
                    index: el.id.replace(/^.*_(\d+)$/, '$1')
                };
                if (ro_el = $(el.id.replace(/serial/, 'serial_readonly'))) {
                    container.removeChild(ro_el);
                }
                container.removeChild(el);
                if (result.serial.length == 1) {
                    params.type = 'text';
                    params.class_name = 'readonly';
                    params.readOnly = true;
                    params.value = result.serial[0].option_value;
                } else {
                    if (vars_settings.serial && vars_settings.serial.readonly && vars_settings.serial.readonly > 0) {
                        params.type = 'hidden';
                        if (result.serial && result.serial[0]) {
                           params.value = result.serial[0].option_value;
                        }
                        createField(params, container);
                        params.disabled = true;
                        params.custom_id = params.name = params.name.replace(/serial/, 'serial_readonly');
                    }
                    params.options = result.serial;
                    params.type = 'dropdown';
                    params.sequences = 'showSerialCustomQuantity(this);recalcAvailableQuantity(' + parent_idx + ');';
                }
                serials = createField(params, container);
                serials.style.width = '100%';
            }
            //set the custom number
            var customs = '';
            if (el = $(element.id.replace(/batch(_code)?/, 'custom'))) {
                container = el.parentNode;
                var params = new Object();
                params = {
                    name: el.id.replace(/_\d+$/, ''),
                    index: el.id.replace(/^.*_(\d+)$/, '$1')
                };
                if (ro_el = $(el.id.replace(/custom/, 'custom_readonly'))) {
                    container.removeChild(ro_el);
                }
                container.removeChild(el);
                if (! result.custom || result.custom.length == 1) {
                    params.type = 'text';
                    params.class_name = 'readonly';
                    params.readOnly = true;
                    params.value = result.custom ? result.custom[0].option_value : '';
                } else {
                    if (vars_settings.custom && vars_settings.custom.readonly && vars_settings.custom.readonly > 0) {
                        params.type = 'hidden';
                        if (result.custom && result.custom[0]) {
                           params.value = result.custom[0].option_value;
                        }
                        createField(params, container);
                        params.disabled = true;
                        params.custom_id = params.name = params.name.replace(/custom/, 'custom_readonly');
                    }
                    params.type = 'dropdown';
                    params.options = result.custom;
                    params.sequences = 'showSerialCustomQuantity(this);recalcAvailableQuantity(' + parent_idx + ');';
                }
                customs = createField(params, container);
                customs.style.width = '100%';
            }
            var available_set = false;
            if ((el = $(element.id.replace(/batch(_code)?/, 'quantity'))) && result.available_quantity) {
                var span = el.parentNode.getElementsByTagName('span');
                el.parentNode.style.whiteSpace = 'nowrap';
                for (var i = span.length - 1; i >= 0; i--) {
                    el.parentNode.removeChild(span[i]);
                }
                for (var i in result.available_quantity) {
                    span = document.createElement('span');
                    span.id = i;
                    span.innerHTML = '&nbsp;/&nbsp;' + result.available_quantity[i];
                    var j = '';
                    if (serials) {
                        j += serials.value;
                    }
                    j += '_';
                    if (customs) {
                        j += customs.value;
                    }
                    if (i == j) {
                        available_set = true;
                        el.style.width = '50%';
                        span.className = "active_available";
                    }
                    if (i == j && vars_settings && vars_settings.available_quantity && (!vars_settings.available_quantity.hidden || vars_settings.available_quantity.hidden == 0)) {
                        span.style.display = '';
                    } else {
                        span.style.display = 'none';
                    }
                    el.parentNode.insertBefore(span, el.nextSibling);
                }
                if (!available_set) {
                    showSerialCustomQuantity(serials);
                }
            }
            //set the delivery price and currency
            if ((el = $(element.id.replace(/batch(_code)?/, 'delivery_price'))) && el.type == 'text') {
                el.value = result.delivery_price;
                el.readOnly = true;
                el.style.display = 'none';
                addClass(el, 'readonly');
                if ($(el.id + '_formatted')) {
                    div = $(el.id + '_formatted');
                } else {
                    div = document.createElement('div');
                    div.id = el.id + '_formatted';
                    div.style.textAlign = 'right';
                }
                //set the currency
                if ((el = $(element.id.replace(/batch(_code)?/, 'currency')))) {
                    if (el.type.match(/select/)) {
                        el.readOnly = true;
                        el.style.display = 'none';
                        addClass(el, 'readonly');
                    }
                    el.value = result.currency;
                    if ((el = $(element.id.replace(/batch(_code)?/, 'currency_readonly'))) && el.type.match(/select/)) {
                        el.value = result.currency;
                        el.readOnly = true;
                        el.style.display = 'none';
                        addClass(el, 'readonly');
                    } else {
                        el = $(element.id.replace(/batch(_code)?/, 'currency'));
                    }
                }
                var pow = Math.pow(10, env.precision.gt2_rows);
                pow = Math.round(result.delivery_price * pow) / pow;
                div.innerHTML = pow.toFixed(env.precision.gt2_rows) + ' ' + result.currency;
                el.parentNode.insertBefore(div, el.nextSibling);
            }
            if (available_set) {
                recalcAvailableQuantity(parent_idx);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=' + controller;
    url += '&' + controller + '=ajax_get_existing_batch_data&ajax_get_existing_batch_data=' + model;
    url += '&article=' + article + '&batch=' + batch + '&model=';
    if (env.controller_name && env.controller_name != env.module_name) {
        url += env.module_name +'_' + env.controller_name.substr(0, env.controller_name.length-1);
        if ($('type') && $('type').value) {
            url += '&type=' + $('type').value;
            if ($('id') && $('id').value) {
                url += '&model_id=' + $('id').value;
            }
        }
    } else {
        url += env.module_name.substr(0, env.controller_name.length-1);
    }
    new Ajax.Request(url, opt);
}

/**
 * Function to change depending options in function of
 * serial/custom field change
 */
function showSerialCustomQuantity(element) {
    element = $(element);
    var el = $(element.id.replace(/serial|custom/, 'batch_data_json_object'));
    var serial, custom;
    if (el.value) {
        eval('var batch_data = ' + el.value + ';');
        if (element.id.match(/serial/)) {
            //check available custom options
            custom = $(element.id.replace(/serial/, 'custom'));
            if (custom.tagName.match(/select/i) && !in_array(custom.value, batch_data.serial_customs[element.value])) {
                //we have to change custom field value
                //just set the first one we find
                for (var i = 0; i < custom.options.length; i++) {
                    if (in_array(custom.options[i].value, batch_data.serial_customs[element.value])) {
                        custom.selectedIndex = i;
                        break;
                    }
                }
            }
            serial = element.value;
            custom = custom.value;
        } else if (element.id.match(/custom/)) {
            //check available serial options
            serial = $(element.id.replace(/custom/, 'serial'));
            if (serial && serial.tagName.match(/select/i)) {
                if (!in_array(serial.value, batch_data.custom_serials[element.value])) {
                    //we have to change serial field value
                    //just set the first one we find
                    for (var i = 0; i < serial.options.length; i++) {
                        if (in_array(serial.options[i].value, batch_data.custom_serials[element.value])) {
                            serial.selectedIndex = i;
                            break;
                        }
                    }
                }
                serial = serial.value;
            } else {
                //it's possible that we don't have serial field
                serial = '';
            }
            custom = element.value;
        }
    }
    if ((el = $(element.id.replace(/serial|custom/, 'quantity')))) {
        var span = el.parentNode.getElementsByTagName('span');
        for (var i = span.length - 1; i >= 0; i--) {
            if (span[i].id == serial + '_' + custom) {
                span[i].className = "active_available";
                eval ('var vars_settings = ' + $(element.id.replace(/(serial|custom).*/, 'vars_settings')).value + ';');
                if (!vars_settings || !vars_settings.available_quantity || !vars_settings.available_quantity.hidden || vars_settings.available_quantity.hidden == 0) {
                    span[i].style.display = '';
                    el.style.width = '50%';
                }
            } else {
                span[i].style.display = 'none';
                span[i].className = "";
            }
        }
    }
}

/**
 * Enables/disables batch data row in reverse handovers
 *
 * @param el - the checkbox
 * @param row - id of the row to be enabled/disabled
 */
function reverseHandoverRowManip(el, row) {

    //get quantity field
    var q_element = $(el.id.replace(/active/, 'quantity'));
    //extract the parent row index
    var idx = q_element.getAttribute('onKeyUp');
    idx = idx.replace(/.*this,\s*(\d+).*/g, '$1');
    elements = $$('#' + row + ' input');
    if (el.checked) {
        //enable all elements in the row
        for (var i = 0; i < elements.length; i++) {
            if (elements[i] == el) {
                continue;
            }
            elements[i].disabled = false;
            removeClass(elements[i], 'input_inactive');
        }
        //calculate parent row quantity after row enablement
        calcWhQuantity(q_element, idx, 'enable');
    } else {
        //calculate parent row quantity before disabling row
        calcWhQuantity(q_element, idx, 'disable');
        for (var i = 0; i < elements.length; i++) {
            if (elements[i] == el) {
                continue;
            }
            elements[i].disabled = true;
            addClass(elements[i], 'input_inactive');
        }
    }
}

/**
 * Add batch row extension for batch articles
 */
function addBatchDataRow(autocomplete, data) {

    // if coming from addquick action, restructure necessary params
    if (data.row && data['has_batch_' + data.row] && data['article_id_' + data.row]) {
        data.$has_batch = data['has_batch_' + data.row];
        data.$article_id = data['article_id_' + data.row];
        if (data['quantity_' + data.row]) {
            data.$quantity = data['quantity_' + data.row];
        }
    }
    if (!data.$has_batch || data.$has_batch == 0) {
        // invalid data, return
        if (!data.row) {
            return true;
        }
        //not a batch article
        var table = $('var_group_0');
        var index = $('var_group_0_' + data.row).rowIndex + 1;
        var row = table.rows[index];
        //we need a TBODY element
        var tbody = row.parentNode;
        while(row && !row.id.match(/(var_group_0_|gt2_)/)) {
            //this is not a regular gt2 row
            tbody.removeChild(row);
            //get next row - it will have the same index
            row = table.rows[index];
        }
        //restore quantity field
        quantity = $('quantity_' + data.row);
        if (typeof data.$quantity == 'undefined') {
            quantity.value = '';
        }
        quantity.readOnly = false;
        removeClass(quantity, 'readonly');
        if (typeof quantity.onkeyup == 'function') {
            quantity.onkeyup();
        }
        return true;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    //set quantity to zero and readonly
    quantity = $('quantity_' + data.row);
    quantity.value = '';
    quantity.readOnly = true;
    addClass(quantity, 'readonly');
    if (typeof quantity.onkeyup == 'function') {
        quantity.onkeyup();
    }
    // get row index (either real row id or a negative number for a new row)
    var row_index = quantity.name.replace(/^.*\[(\-?\d+)\]$/, '$1');

    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            var table = $('var_group_0');
            //create temporary table just to put the new row into it
            var t1 = document.createElement('table');
            t1.id = 'really_tmp_table';
            //hide the table
            t1.style.display = 'none';
            var t2 = t1.appendChild(document.createElement('tbody'));
            t2.innerHTML = result;
            table.parentNode.appendChild(t1);
            //get the new rows as HTML objects
            t2 = $$('#' + t1.id + '>tbody>tr');
            if (!t2.length) {
                //now remove the temporary table
                table.parentNode.removeChild(t1);
                Effect.Fade('loading');
                return false;
            }
            var index = $('var_group_0_' + data.row).rowIndex + 1;
            var row = table.rows[index];
            //we need a TBODY element
            var tbody = row.parentNode;
            while(row && !row.id.match(/(var_group_0_|gt2_)/)) {
                //this is not a regular gt2 row
                tbody.removeChild(row);
                //get next row - it will have the same index
                row = table.rows[index];
            }
            //get the row we have to add batch data for
            row = table.rows[index - 1];
            var colspan = 0;
            for (var i = 0; i < row.cells.length; i++) {
                if (row.cells[i].style.display != 'none') {
                    colspan++;
                }
            }
            for (var i = 0; i < t2.length; i++) {
                //get current row in the table
                //and put the row from the hidden table after it
                row = tbody.insertBefore(t2[i], row.nextSibling);
                row.cells[0].colSpan = colspan;
            }
            //now remove the temporary table
            table.parentNode.removeChild(t1);
            row.id = 'very_tmp_row';
            var scripts = $$('#very_tmp_row table script');
            row.id = '';
            for (var i = 0; i < scripts.length; i++) {
                ajaxLoadJS(scripts[i]);
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var warehouse = '';
    if ($('warehouse_data') && $('warehouse_data').value.match(/^\d+_\d+_\d+$/)) {
        warehouse = $('warehouse_data').value.replace(/.*_(\d+)$/, '$1');
    }

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=warehouses&warehouses=ajax_get_quantity';
    url += '&ajax_get_quantity=' + warehouse + '&create_view=' + data.row + '&row_index=' + row_index + '&nom_id=' + data.$article_id;
    if ($('type') != null) {
        url += '&type=' + $('type').value;
        if ($('type').value == 9 && $('id')) {
            //we edit commodity reservation
            url += '&reservation=' + $('id').value;
        } else if ($('type').value == 12) {
            //inspection
            url += '&reservation=none';
        }
    }
    new Ajax.Request(url, opt);
}

/**
 * Function to activate lightbox where invoice currency of result invoice and
 * conversion rates for incoming proformas are specified
 * AND also to submit multiaddinvoice data.
 *
 * @param {Object} element - button which function is called form
 * @return {Boolean} - result of the operation
 */
function addMergedExpensesInvoice(element) {
    var selected = $('selectedItemsCount_1');
    if (!(selected && parseInt(selected.innerHTML) > 0)) {
        alert(i18n.messages['alert_multiaddinvoice']);
        return false;
    }

    // submitting from lightbox
    if (element.id == 'lbMultiaddinvoiceButton') {
        element.disabled = true;
        var b = $('multiaddinvoiceButton');
        if (b == null) {
            // this should not be happening but just to make sure
            if (lb && lb.active) {
                lb.deactivate();
            }
            alert(i18n['messages']['error_no_access_to_action']);
            return false;
        }
        var f = b.form;
        // append lightbox fields to the multi-action form and submit it
        var fields = $$('#multiaddinvoice_table .multiaddinvoice');
        var error = false;
        for (var i = 0; i < fields.length; i++) {
            if (fields[i].value === '' && !fields[i].className.match(/invoice_currency/)) {
                error = true;
                break;
            }
        }
        if (error) {
            alert(i18n['messages']['alert_empty_field']);
            element.disabled = false;
            return false;
        }
        for (var i = 0; i < fields.length; i++) {
            var el = fields[i].cloneNode(true);
            if (el.tagName == 'SELECT') {
                el.selectedIndex = fields[i].selectedIndex;
            }
            el.style.display = 'none';
            b.parentNode.appendChild(el);
        }
        f.action += '&' + env.action_param + '=multiaddinvoice';
        f.submit();
        return false;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'post',
        asynchronous: false,
        parameters: Form.serialize(element.form),
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            eval('result = ' + result + ';');
            if (result.errors) {
                $('messages_container').innerHTML = result.errors;
                new Effect.ScrollTo('messages_container');
            } else {
                $('messages_container').innerHTML = '';
                lb = new lightbox({content: result.content,
                                   title: i18n['labels']['expense_invoice_from_proformas'],
                                   icon: 'info.png',
                                   width: 550});
                lb.activate();
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=' + env.module_name + '&' + env.controller_param + '=' + env.controller_name + '&' + env.controller_name + '=ajax_multiaddinvoice';
    new Ajax.Request(url, opt);
}

/**
 * Shows/hides conversion rate textboxes in multiaddinvoice lightbox
 * when currency selection is changed.
 *
 * @param {Object} element - currency dropdown
 * @return {Boolean} - result of the operation
 */
function toggleMultiaddinvoiceRates(element) {
    var invoice_currency = element[element.selectedIndex].value;
    var regexp = new RegExp(invoice_currency);
    var rows = $$('#multiaddinvoice_table tr.rate');
    var info_row = null;
    var hide_info_row = true;
    for (var i = 0; i < rows.length; i++) {
        if (rows[i].className.match(/info/)) {
            info_row = rows[i];
        } else if (rows[i].className.match(regexp)) {
            rows[i].style.display = 'none';
            addClass($(rows[i].id.replace(/row/, 'rate')), 'invoice_currency');
        } else {
            rows[i].style.display = '';
            removeClass($(rows[i].id.replace(/row/, 'rate')), 'invoice_currency');
            hide_info_row = false;
        }
    }
    if (info_row != null) {
        info_row.style.display = hide_info_row ? 'none' : '';
    }
    var spans = $$('span.invoice_currency');
    for (var i = 0; i < spans.length; i++) {
        spans[i].innerHTML = invoice_currency;
    }
    return true;
}

/**
 * Toggles allocated status (enabled/disabled) of expense reason without page submit (in "view" action)
 *
 * @param {Object} element - allocated status checkbox
 */
function expensesUpdateAllocatedStatus(element) {
    if (!env || !env.model_name == 'Finance_Expenses_Reason' || !element) {
        return;
    }
    var url = env.project_url;
    var regex = new RegExp('=' + env.action_name + '&' + env.action_name + '=(\\d+)');
    if (!regex.test(url)) {
        return;
    }
    url = url.replace(regex, '=ajax_update_allocated_status&ajax_update_allocated_status=$1');
    element = $(element);

    Effect.Center('loading');
    Effect.Appear('loading');
    var opt = {
        parameters: {
            allocated_status:
                element.checked && !element.disabled ?
                element.value :
                element.adjacent('input[type="hidden"][name="' + element.name + '"]')[0].value
        },
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }
            if (!t.responseText) {
                if (!env.valid_login) {
                    redirectToLogin();
                }
                Effect.Fade('loading');
                return;
            }
            var result = typeof t.responseJSON == 'object' ? t.responseJSON : {};
            if (!result.messages && !result.errors) {
                result.errors = displayNotificationFixed.formatContent([i18n['messages']['error_edit_notallowed']], 'error');
            }
            if (result.errors && result.errors.length) {
                displayNotificationFixed(result.errors);
                element.checked = !element.checked;
            } else if (result.messages && result.messages.length) {
                displayNotificationFixed(result.messages);
                var menu_item = $('allocate_costs_action');
                if (menu_item && !element.checked) {
                    menu_item.up('div').remove();
                } else if (!menu_item && element.checked && result.redirect) {
                    setTimeout(function() {
                        url = window.location.href.replace(regex, '=allocate_costs&allocate_costs=$1');
                        redirect(url);
                    }, 1000);
                }
            }
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    new Ajax.Request(url, opt);
}

/**
 * Handles loading of commodity GT2 rows of expense selected from autocompleter
 *
 * @param {Object} autocomplete
 * @param {Object} data
 */
function expensesLoadDeliveryRows(autocomplete, data) {
    var scope = $(document.body);
    if (autocomplete.scope) {
        scope = $(scope.down(autocomplete.scope));
    }
    var rows_container = scope.down('#' + autocomplete.field).up('tr').down('div.expense_rows');
    if (!rows_container) {
        return;
    }

    if (!data.id) {
        rows_container.innerHTML = '';
        return;
    }
    // total amount to be allocated to deliveries (it can be positive or negative)
    var expense_amount_container = scope.down('span.expense_amount');
    var expense_amount = expense_amount_container ? parseFloat(expense_amount_container.innerHTML) : 0;
    if (isNaN(expense_amount)) {
        expense_amount = 0;
    }

    Effect.Center('loading');
    Effect.Appear('loading');
    var url =
        env.base_url + '?' + env.module_param + '=finance&' +
        env.controller_param + '=expenses_reasons&expenses_reasons=ajax_load_delivery_rows&ajax_load_delivery_rows=' + data.id;
    var opt = {
        parameters: {
            expense_currency: scope.down('#expense_currency').value,
            expense_amount: expense_amount,
            expense_type: scope.down('#type').value
        },
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return false;
            }

            rows_container.innerHTML = t.responseText;

            expensesAllocateInit(rows_container);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    new Ajax.Request(url, opt);
}

/**
 * Handles keyboard input of pseudo-checkbox to emulate that of checkbox
 *
 * @param {number} keyCode
 * @param {string} key
 * @param {Object} evt
 * @return {Object}
 */
function toggleTextInputSelected(keyCode, key, evt) {
    if (keyCode == '32') {
        // <Space> - if field has no value, allow a single space, if it has value - clear it
        var element = evt.element();
        var element_value = element.value;
        // toggle the attribute value
        toggleExpenseRow(element);
        // handle the event
        if (!element.value) {
            return { replaceKey: false };
        } else {
            return { replaceKey: "clearAll" };
        }
    } else if (keyCode == '9') {
        // If the key is <Tab> (key code 9), then leave it to do what it does
        return { replaceKey: false };
    } else {
        // Cancel the current key
        return { cancelKey: true };
    }
}

/**
 * Enables/disables field for allocated amount in expense row when checkbox is toggled
 *
 * @param {Object} element - the pseudo-checkbox field
 */
function toggleExpenseRow(element) {
    element = $(element);
    var element_value = element.getAttribute('value') ? '' : ' ';
    element.setAttribute('value', element_value);
    // toggle amount input field
    var amount_field = $(element.up('tr')).down('input.allocated_amount');
    if (amount_field) {
        if (element_value && amount_field.hasAttribute('disabled') || !element.value && !amount_field.hasAttribute('disabled')) {
            amount_field.toggleAttribute('disabled');
            amount_field.toggleClassName('input_inactive');
        }
        expensesAllocateCalc(amount_field);
    }
}

/**
 * Initializing functionality on load of allocate expenses screen or for response of AJAX request
 *
 * @param {Object} scope - container DOM element to execute function for
 */
function expensesAllocateInit(scope) {
    // initialize state of fields for allocated amount according to selection of rows
    $$('input.allocated_amount').each(function(el) {
        el = $(el);
        if ($(el.up('tr')).down('[id="' + el.id.replace(/allocated_amount/, 'selected') + '"][value=""]')) {
            el.setAttribute('disabled', 'disabled');
            el.addClassName('input_inactive');
        }
    });
    if (!scope) {
        scope = $$('table.expense_table')[0];
    }
    if (scope) {
        // disable GT2 fields which can't be disabled/removed using settings
        $(scope).select('input[id]:not([disabled])').each(function(a) {
            if (a.id.match(/^(calc_price|article_.*|total.*|(old_)?currency|deleted_\d+|gt2_requested)$/)) {
                a.setAttribute('disabled', 'disabled');
            }
        });
    }
}

/**
 * Calculates allocated amount
 *
 * @param {Object} element - DOM element that function is called according to
 * @return {boolean} - true if amount is allocated and remaining amount is 0, otherwise false
 */
function expensesAllocateCalc(element) {
    if (!element) {
        return;
    }
    element = $(element);
    var scope = $(element.up('form'));
    var expense_amount_container = scope.down('span.expense_amount');
    var expense_amount = expense_amount_container ? parseFloat(expense_amount_container.innerHTML) : 0;
    if (isNaN(expense_amount)) {
        expense_amount = 0;
    }
    var remaining_amount_container = scope.down('span.remaining_amount');
    var remaining_amount = remaining_amount_container ? parseFloat(remaining_amount_container.innerHTML) : 0;
    if (isNaN(remaining_amount)) {
        remaining_amount = 0;
    }
    var prec = env.precision.gt2_total;
    var pow = Math.pow(10, prec);

    if (element.hasClassName('allocated_amount') && element.value.match(/^[^\.]*\./)) {
        var cnt = element.value.replace(/^[^\.]*\./, '');
        if (cnt.length > prec) {
            element.value = Math.round(element.value * pow) / pow;
        }
    }
    var total_allocated_amount = 0;
    var allocated_fields = scope.select('table.expense_table > tbody > tr:not(.input_inactive) table.grouping_table2 input.allocated_amount');

    if (element.id == 'calculateButton') {
        // suggest default allocation according to "weight"
        var subtotal_amounts = [];
        var subtotal_amounts_sum = 0;

        allocated_fields.each(function(el) {
            el = $(el);
            if (el.disabled) {
                subtotal_amounts.push(0);
                return;
            }
            var subtotal_amount = 0;
            var subtotal_field = scope.down('[id="' + el.id.replace(/allocated_amount/, 'subtotal_amount') + '"]');
            if (subtotal_field) {
                subtotal_amount = parseFloat(subtotal_field.value);
                if (isNaN(subtotal_amount) || subtotal_amount < 0) {
                    subtotal_amount = 0;
                }
            }
            subtotal_amounts.push(subtotal_amount);
            subtotal_amounts_sum += subtotal_amount;
        });

        allocated_fields.each(function(el, idx) {
            var el_value = 0;
            el = $(el);
            if (typeof subtotal_amounts[idx] !== 'undefined' && subtotal_amounts[idx] != 0 && subtotal_amounts_sum != 0) {
                if (idx === allocated_fields.length - 1) {
                    el_value = expense_amount - total_allocated_amount;
                } else {
                    el_value = (subtotal_amounts[idx] / subtotal_amounts_sum) * expense_amount;
                }
            }
            el.removeClassName('erred');
            el.value = el_value.toFixed(prec);
            total_allocated_amount += parseFloat(el.value);
        });
    } else {
        allocated_fields.each(function(el) {
            el = $(el);
            if (el.disabled) {
                el.removeClassName('erred');
                return;
            }

            var amount = parseFloat(el.value || 0);
            var kp = el.getAttribute('onKeyPress') ? el.attributes.onkeypress.value : '';
            if (isNaN(amount) || amount > 0 && kp.match(/insertOnlyNegativeFloats/) || amount < 0 && kp.match(/insertOnlyFloats/)) {
                el.addClassName('erred');
            } else {
                el.removeClassName('erred');
                total_allocated_amount += (amount * pow) / pow;
            }
        });
    }

    var unallocated_amount = Math.round((expense_amount - total_allocated_amount) * pow) / pow;

    if (remaining_amount_container) {
        remaining_amount_container = $(remaining_amount_container);
        remaining_amount_container.innerHTML = unallocated_amount.toFixed(prec);
        if (unallocated_amount === 0) {
            remaining_amount_container.addClassName('green').removeClassName('red');
        } else {
            remaining_amount_container.addClassName('red').removeClassName('green');
        }
    }

    return unallocated_amount === 0 && !allocated_fields.filter(function(a) { return !a.disabled && $(a).hasClassName('erred'); }).length;
}

/**
 * Autofills handover rows with quantities
 *
 * @param {Object} element - DOM element that function is called according to
 * @return {boolean} - true if amount is allocated and remaining amount is 0, otherwise false
 */
function autoFillHandoverRows(element) {
    if (!element) {
        return;
    }
    element = $(element);
    let scope = $(element.up('form'));
    let quantities = scope.select('input[name^=quantity].quantity');

    quantities.each(function(quantityEl) {
        quantityEl = $(quantityEl);
        //quantityElId = quantityEl.id.replace('/^quantity_/', '');

        let rowQuantity = parseFloat(quantityEl.getValue());
        let totalQuantity = parseFloat(quantityEl.up('tr').down('.total_quantity').getValue());
        let totalAvailable = parseFloat(quantityEl.up('tr').down('.total_available').getValue());
        let remainder = rowQuantity - totalQuantity;

        if (remainder == 0 || totalAvailable <= totalQuantity) {
            return;
        }


        let batchContainer = quantityEl.up('tr').next('tr.batch_data');
        if (!batchContainer) {
            //no batches, suggest within the available warehouses quantities
            let warehousesQuantitiesEls = quantityEl.up('tr').select('.warehouse_quantity');
            warehousesQuantitiesEls.each(function (warehousesQuantitiesEl) {
                let warehouseAvailableQuantity = parseFloat($(warehousesQuantitiesEl.id.replace(/_quantity_/, '_available_')).getValue());
                let newRowQuantity = Math.min(remainder, warehouseAvailableQuantity);
                let recalculatedValue = (warehousesQuantitiesEl.getValue() ? parseFloat(warehousesQuantitiesEl.getValue()) : 0) + newRowQuantity;
                if (recalculatedValue > warehouseAvailableQuantity) {
                    return;
                }

                warehousesQuantitiesEl.value = recalculatedValue;

                gt2calc(warehousesQuantitiesEl);

                remainder -= newRowQuantity;
                if (remainder <= 0) {
                    throw $break;
                }
            });
        } else {
            let batchRows = batchContainer.select('tr[id^=article]');
            //check if there are any batch rows
            if (!batchRows || batchRows.length == 0) {
                //no need to suggest for non-batches rows
                return;
            }
            if (quantityEl.disabled) {
                return;
            }

            //batches
            batchRows.each(function (batchRowEl) {
                batchRowEl = $(batchRowEl);
                let batchQuantityEl = batchRowEl.down('.quantity');
                let batchCheckboxEl = batchRowEl.down('input[type=checkbox]');
                let batchRowAvailableQuantity = parseFloat(batchQuantityEl.next('span').innerHTML.replace(/[^\d\.]/g, ''));
                let batchRowQuantity = Math.min(remainder, batchRowAvailableQuantity);

                if (batchCheckboxEl && !batchCheckboxEl.checked) {
                    batchCheckboxEl.checked = true;
                    batchQuantityEl.value = batchRowQuantity;
                    //enable all batch row fields and calculate
                    reverseHandoverRowManip(batchCheckboxEl, batchRowEl.id);
                } else {
                    batchQuantityEl.value = (batchQuantityEl.getValue() ? parseFloat(batchQuantityEl.getValue()) : 0) + batchRowQuantity;

                    //extract the parent row index
                    let idx = batchQuantityEl.getAttribute('onKeyUp').replace(/.*this,\s*(\d+).*/g, '$1');
                    //recalculate the quantities with the main row
                    calcWhQuantity(batchQuantityEl, idx);
                }

                remainder -= batchRowQuantity;
                if (remainder <= 0) {
                    throw $break;
                }
            });
        }
    });
}

function changeCountersOffices(company) {

    if (!company) {
        //clear offices
        $('offices_row').cells[2].innerHTML = '';
        return true;
    }
    Effect.Center('loading');
    Effect.Appear('loading');
    var opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            var row = $('offices_row');
            var t = document.createElement('table');
            var b = document.createElement('tbody');
            t.appendChild(b);
            b.innerHTML = result;
            new_row = t.rows[0];
            row.parentNode.replaceChild(new_row, row);
            delete(t);
            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    //set the url to request ajax action
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=counters&counters=ajax_getoffices';
    url += '&model_lang=' + $('model_lang').value + '&company=' + company + '&counter_model=' + $('model').value;
    if ($('id') && $('id').value) {
        url += '&ajax_getoffices=' + $('id').value;
    }
    new Ajax.Request(url, opt);
}

/**
 * Toggle options for offices for counters in financial module
 * @param element
 */
function countersOfficeIndependent(element) {
    elements = $$('.counter_office');
    for (var i = 0; i < elements.length; i++) {
        if (elements[i] == element) {
            continue;
        }
        if (element.checked) {
            elements[i].disabled = true;
            elements[i].parentNode.parentNode.style.display = 'none';
        } else {
            elements[i].disabled = false;
            elements[i].parentNode.parentNode.style.display = '';
        }
    }
}

/**
 * Checks if there are remaining quantities/articles when adding a handover and
 * displays a prompt for confirmation of adding a partial handover, if necessary.
 *
 * @param {Object} btn - button that submits form
 * @return {bool} - result of check
 */
function checkHandoverQuantities(btn) {
    if (env.action_name == 'addhandover' && $('handover_direction') &&
      (env.controller_name == 'incomes_reasons' && $('handover_direction').value == 'outgoing' ||
       env.controller_name == 'expenses_reasons' && $('handover_direction').value == 'incoming')) {

        var quantities = $$('input.quantity');
        var not_completed = [];
        var counter = 1;
        for (var i = 0; i < quantities.length; i++) {
            if (quantities[i].id.match(/^quantity_\d+$/)) {
                if (!quantities[i].className.match("completed")) {
                    not_completed.push(counter);
                }
                counter++;
            }
        }
        if (not_completed.length) {
            var msg = (not_completed.length < 10) ?
                i18n['messages']['confirm_partial_handover9'].replace(/\[rows\]/, not_completed.join(', ')) :
                i18n['messages']['confirm_partial_handover10'].replace(/\[rows\]/, not_completed.length);
            return confirmAction('partial_handover', function(el) { el.form.submit(); }, btn, msg);
        }
    }

    return true;
}

/**
 * Updates financial data from customer into data of current financial document
 *
 * @param {object} element - clicked element
 * @param {number} id - id of financial document
 */
function updateCustomerData(element, id) {
    Effect.Center('loading');
    Effect.Appear('loading');
    element.setAttribute('disabled', 'disabled');
    var opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            var result = t.responseText;
            if (result.match(/^{/)) {
                eval('result = ' + result + ';');
            } else {
                result = result.match(/<div class="message_container">([\s\S]*?)<\/div>/);
                if (result && result.length > 1) {
                    result = {errors: result[1]};
                } else {
                    result = {};
                }
            }

            if (result.errors) {
                $('messages_container').innerHTML = result.errors;
                new Effect.ScrollTo('messages_container');
            } else if (result.messages) {
                $('messages_container').innerHTML = result.messages;
                new Effect.ScrollTo('messages_container');
            }
            element.removeAttribute('disabled');

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = env.base_url + '?' + env.module_param + '=finance&' + env.controller_param + '=incomes_reasons&incomes_reasons=ajax_update_customer_data&id=' + id +
        '&lang=' + (/model_lang=\w+(&|$)/.test(window.location.search) ? window.location.search.replace(/^.*model_lang=(\w+)(&.*|)$/, '$1') : env.current_lang);
    new Ajax.Request(url, opt);
}

/**
 * Reloads data in multiadd form for bank payments when selected bank account is changed
 *
 * @param {Object} element - dropdown with bank account options
 */
function multiaddChangeCompanyData(element) {
    Effect.Center('loading');
    Effect.Appear('loading');
    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var result = t.responseText;
            if (!checkAjaxResponse(result)) {
                return;
            }

            eval('result = ' + result + ';');

            if (typeof result != 'object') {
                result = new Object;
            }

            $$('.bank_account_currency').each(function(el) { el.innerHTML = result.currency || ''; });
            $('credit_limit').value = result.credit_limit || 0;
            if ($('current_amount') != null) {
                var el = $('current_amount');
                result.current_amount = parseFloat(result.current_amount);
                if (isNaN(result.current_amount)) {
                    result.current_amount = 0;
                }
                el.innerHTML = result.current_amount.toFixed(2);
                if (result.current_amount < 0) {
                    addClass(el, 'red');
                    removeClass(el, 'green');
                } else {
                    addClass(el, 'green');
                    removeClass(el, 'red');
                }
                // calculate and update new amount
                multiaddUpdateNewAmount(el.innerHTML, result);
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };
    var url = element.form.action + '&new_company_data=' + element.value + '&use_ajax=1';
    new Ajax.Request(url, opt);
}

/**
 * Formats value and changes row color in multiadd form when amount of payment is entered
 *
 * @param {Object} element - input for amount bank payment
 */
function multiaddProcessAmount(element) {
    var amount = parseFloat(element.value);
    var bg_color = '';
    if (isNaN(amount)) {
        if (element.value.match(/^-/)) {
            element.value = '-';
        } else {
            element.value = '';
        }
    } else {
        if (element.value.match(/\.\d{3,}$/)) {
            amount = (amount * 100) / 100;
            element.value = amount.toFixed(2);
        }
        if (amount < 0) {
            bg_color = '#EFB9B9';
        } else if (amount > 0) {
            bg_color = '#A5DBA8';
        }
        if (amount) {
            removeClass(element, 'erred');
        }
    }
    element.parentNode.parentNode.style.backgroundColor = bg_color;
    // calculate and update new amount
    multiaddUpdateNewAmount($('current_amount').innerHTML, {incomes: true, expenses: true, element_id: element.id});
}

/**
 * Calculates and updates new balance by adding all entered amounts to current balance
 *
 * @param {String} new_amount - current balance
 * @param {Object} result - filtering of allowed payment types
 * @return {Number} - new balance
 */
function multiaddUpdateNewAmount(new_amount, result) {
    var el2 = $('new_amount');
    if (!el2) {
        return 0;
    }
    if (typeof result == 'undefined') {
        result = {incomes: true, expenses: true};
    }

    new_amount = parseFloat(new_amount);
    if (isNaN(new_amount)) {
        new_amount = 0;
    }

    $$('input.amount').each(function(amt) {
        // change validation, if necessary, when bank account is changed
        if (!result.element_id) {
            var kp = amt.getAttribute('onKeyPress') ? amt.attributes.onkeypress.value : '', fn;
            // xor
            if (result.incomes ? !result.expenses : result.expenses) {
                fn = result.incomes ? 'insertOnlyFloats' : 'insertOnlyNegativeFloats';
            } else {
                fn = 'insertOnlyReals';
            }
            if (!kp.match(new RegExp(fn))) {
                amt.setAttribute('onKeyPress', kp.replace(/insertOnly(Reals|(Negative)?Floats)/, fn));
            }
        }

        // row is deleted or inactive
        if (amt.disabled) {
            return;
        }

        var amt_value = parseFloat(amt.value);
        // value is equal to zero
        if ((isNaN(amt_value) || !amt_value) && (!result.element_id || result.element_id != amt.id)) {
            amt.value = '';
            amt.parentNode.parentNode.style.backgroundColor = '';
            return;
        }
        if (amt_value > 0 && result.incomes || amt_value < 0 && result.expenses) {
            new_amount += (amt_value * 100) / 100;
        } else if (!result.element_id || result.element_id != amt.id) {
            amt.value = '';
            amt.parentNode.parentNode.style.backgroundColor = '';
        }
    });

    new_amount = Math.round(new_amount * 100) / 100;
    el2.innerHTML = new_amount.toFixed(2);
    if (new_amount < 0) {
        addClass(el2, 'red');
        removeClass(el2, 'green');
    } else {
        addClass(el2, 'green');
        removeClass(el2, 'red');
    }
    return new_amount;
}

/**
 * Validates data in multiadd form for payments before submit
 *
 * @param {Object} form - submitted form
 * @return {Boolean} - true on success, otherwise false
 */
function multiaddValidatePayments(form) {
    var errors = [];

    // recalculate balance
    if (multiaddUpdateNewAmount($('current_amount').innerHTML) + parseFloat($('credit_limit').value) < 0) {
        errors.push(i18n['messages']['error_invalid_balance'].
            replace('[credit_limit]', parseFloat($('credit_limit').value).toFixed(2) + ' ' + $$('.bank_account_currency')[0].innerHTML));
    }

    // required fields
    form.select('input.required, select.required').each(function(el) {
        if (el.disabled) {
            removeClass(el, 'erred');
            return;
        }

        if (!el.value || el.className.match(/datebox/) && !$(el.id.replace('_formatted', '')).value) {
            addClass(el, 'erred');
        } else if (el.className.match(/amount/)) {
            var amount = parseFloat(el.value);
            var kp = el.getAttribute('onKeyPress') ? el.attributes.onkeypress.value : '';
            if (isNaN(amount) || !amount || amount > 0 && kp.match(/insertOnlyNegativeFloats/) || amount < 0 && kp.match(/insertOnlyFloats/)) {
                addClass(el, 'erred');
            } else {
                removeClass(el, 'erred');
            }
        } else {
            removeClass(el, 'erred');
        }
    });

    if (form.select('.erred').length) {
        errors.push(i18n['messages']['alert_empty_field']);
    }

    if (errors.length) {
        var container = $('messages_container');
        if (container) {
            container.innerHTML = '';
            container.appendChild(document.createElement('ul'));
            container = container.select('ul')[0];
            container.className = 'messages_container';
            errors.each(function(err) {
                var li = document.createElement('li');
                li.innerHTML = err;
                li.className = 'error';
                container.appendChild(li);
            });
            new Effect.ScrollTo('messages_container');
        } else {
            alert(errors.join("\n"));
        }
    }

    return !errors.length;
}

/**
 * Init quantity validation in reservation documents
 */
function initReservationQuantityValidation() {
    $$('.grouping_table2 .quantity').each(function (el) {
        el.observe('keyup', reservationQuanityValidateHandler);
        el.observe('change', reservationQuanityValidateHandler);
        reservationQuanityValidate(el);
    })
    //hide +/- buttons
    $$('.grouping_table2 .t_buttons, .hide_row').each(function (el) {
        el.hide()
    });
}

/**
 * Validate reservation release quantities
 *
 * @param event
 */
function reservationQuanityValidateHandler(event) {
    reservationQuanityValidate(this);
}

/**
 * Validate reservation release quantities
 *
 * @param event
 */
function reservationQuanityValidate(element) {
    //this is the binded quantity element
    let idx = '';
    let quantity = '';

    if (element.id.match(/^quantity.*/)) {
        idx = element.id.replace(/quantity_/, '');
        quantity = parseFloat(element.value);
    } else {
        //batch row
        quantityField = element.up('tr.batch_data').previous().down('.quantity');
        idx = quantityField.id.replace(/quantity_/, '');
        quantity = parseFloat(quantityField.value);
    }
    let reserved = $('reserved_' + idx);
    let reservedVal = parseFloat(reserved.value);
    let released = $('released_' + idx);
    let releasedVal = parseFloat(released.value);
    if (quantity < 0 || quantity > (reservedVal - releasedVal).toFixed(6)) {
        addClass(element.up('tr'), 'attention');
    } else {
        removeClass(element.up('tr'), 'attention');
    }
}
