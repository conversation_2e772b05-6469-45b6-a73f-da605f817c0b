<?php
require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';

class Finance_Incomes_Reasons_Controller extends Controller {
    /**
     * Model name of this controller
     */
    public $modelName = 'Finance_Incomes_Reason';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Finance_Incomes_Reasons';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'export', 'add', 'clone', 'edit', 'view', 'generate',
        'attachments', 'setstatus', 'assign', 'tag', 'receive_date', 'annul',
        'observer', 'print', 'manage_outlooks', 'printlist',
        'payments', 'relatives', 'distribute', 'history',
        'comments', 'emails', 'communications',
        'printform'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit', 'printform'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'payments', 'relatives', 'distribute', 'history', 'communications'
    );

    /**
     * Action which are at the up right position (without tabs)
     */
    public $actionDefinitionsUpRight = array(
        'observer', 'print', 'manage_outlooks', 'printlist'
    );

    /**
     * After Action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'edit', 'view', 'translate',
        'attachments', 'assign', 'generate', 'relatives', 'printform', 'distribute'
    );

    /**
     * The fields which will switch the additional variables in the search panel
     */
    public static $searchAdditionalVarsSwitch = 'fir.type';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        switch ($this->action) {
        case 'add':
            $this->_add();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'search':
            $this->_search();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'payments':
            $this->_payments();
            break;
        case 'addpayment':
            $this->_addPayment();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assign':
            $this->_getAssignments();
            break;
        case 'observer':
            $this->_observer();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'recalc_fiscal_dates':
            $this->_recalcFiscalDates();
            break;
        case 'addinvoice':
            $this->_addInvoice();
            break;
        case 'commodities_reservation':
            $this->_commoditiesReservation();
            break;
        case 'addproformainvoice':
            $this->_addProformaInvoice();
            break;
        case 'addcorrect':
            $this->_addCorrect();
            break;
        case 'manageGT2config':
            $this->_manageGT2config();
            break;
        case 'addcreditdebit':
            $this->_addCreditDebit();
            break;
        case 'addhandover':
            $this->_addHandover();
            break;
        case 'advances':
            $this->_advances();
            break;
        case 'proformaadvances':
            $this->_proformaAdvances();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_preview_invoice':
            $this->_previewGT2Invoice();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'generate':
            $this->_generate();
            break;
        case 'print':
            $this->_print();
            break;
        case 'printform':
            $this->_printForm();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'repaymentfilter':
            $this->_repaymentFilter();
            break;
        case 'repaymentdata':
            $this->_repaymentData();
            break;
        case 'receive_date':
            $this->_receiveDate();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'history':
            $this->_history();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'distribute':
            $this->_distribute();
            break;
        case 'ajax_change_item':
            $this->_changeItem();
            break;
        case 'annul':
            $this->_annul();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'change_unpaid_status':
            $this->_changeUnpaidStatus();
            break;
        case 'auto_gen_send':
            $this->_autoGenSend();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'ajax_prepare_payment_form':
            $this->_preparePaymentForm();
            break;
        case 'ajax_get_warehouses':
            $this->_getWarehouses();
            break;
        case 'ajax_update_customer_data':
            $this->_updateCustomerData();
            break;
        case 'clone':
            $this->_clone();
            break;
        case 'ajax_import_table':
            $this->_importTable();
            break;
        case 'ajax_import_table_configurator':
            $this->_importTableConfigurator();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _list() {
        //only for update user_permissions field for all finance incomes reasons
        // EXECUTE TEMPORARY METHOD
        //Finance_Incomes_Reasons::updateUserPermissions($this->registry);

        //all the actions are within the viewer

        return true;
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer

        return true;
    }

    /**
     * Add a single model
     */
    private function _add() {

        $this->registry->set('get_old_vars', false, true);
        $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);
        $request = &$this->registry['request'];
        $skip_get_gt2_vars_for_new_model = false;

        // special case if the add action has been activated via report
        //IMPORTANT: this mechanism is also used by the clone action
        if ($request->get('report_session_param')) {
            $report_model_session_param = $request->get('report_session_param');
            $finance_incomes_reason_custom_model = $this->registry['session']->get('report_custom_model', $report_model_session_param);

            if ($finance_incomes_reason_custom_model) {
                $finance_incomes_reason_custom_model = unserialize($finance_incomes_reason_custom_model);
                $this->registry['session']->remove('', $report_model_session_param);
                $this->registry['session']->remove($report_model_session_param, 'selected_items');

                $finance_incomes_reason = $finance_incomes_reason_custom_model;
                $skip_get_gt2_vars_for_new_model = true;
                $finance_incomes_reason->unsanitize();
                unset($finance_incomes_reason_custom_model);
            }
        }

        $this->settings_assign = $this->registry['config']->getParamAsArray('finance', 'assignment_types_' . $finance_incomes_reason->get('type'));

        $model_type_name =
            $finance_incomes_reason->get('type') > PH_FINANCE_TYPE_MAX || !$finance_incomes_reason->get('type') ?
            $finance_incomes_reason->getModelTypeName() :
            ($finance_incomes_reason->get('type') == PH_FINANCE_TYPE_INVOICE ?
                $this->i18n('finance_incomes_reasons_advance') :
                $this->i18n('finance_incomes_reasons_proforma_advance'));

        $request = $this->registry['request'];

        if (!$request->isPost() && (!$request->get('company') || !$request->get('type'))) {
            // invalid data from pre-screen, redirect to list
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_failed',
                                                  array($model_type_name)), '', -1);
            if (!$request->get('company')) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_company_specified'));
            }
            if (!$request->get('type')) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invalid_type'));
            }
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        //check if details are submitted via POST
        if (!empty($finance_incomes_reason) && $this->registry['request']->isPost() && !$this->registry['request']->get('skip_post_save_model')) {
            $this->old_model = new Finance_Incomes_Reason($this->registry);
            // set type and company in order to prepare GT2 table of old model
            $this->old_model->set('type', $finance_incomes_reason->get('type'), true);
            $this->old_model->set('company', $finance_incomes_reason->get('company'), true);
            if ($finance_incomes_reason->get('advance')) {
                // set reason type in order to get VAT settings
                $this->old_model->set('advance', $finance_incomes_reason->get('advance'), true);
            }
            $this->registry->set('get_old_vars', true, true);
            $this->old_model->getGT2Vars();
            $this->old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            $transform_params = $finance_incomes_reason->get('transform_params');

            if ($finance_incomes_reason->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('id'), 'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $finance_incomes_reason->getGT2Vars();
                if ($transform_params) {
                    $finance_incomes_reason->set('transform_params', $transform_params, true);
                }

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                         array('action_type' => 'add',
                                                                               'new_model' => $finance_incomes_reason,
                                                                               'old_model' => $this->old_model
                                                                         ));

                if ($request->get('finance_after_action') == 'payment') {
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addpayment',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $finance_incomes_reason
                                                                             ));
                }

                //assign the document to the users set in the default assignments
                // and the selected users can be assigned according to the assignments rights by types in the settings
                $current_user_assignments = $this->registry['currentUser']->getAssignmentPermissions('Finance_Incomes_Reason');

                $filters_assign['type'] = $finance_incomes_reason->get('type');
                $filters_assign['department'] = $finance_incomes_reason->get('department');

                $current_user_default_assignments = $this->registry['currentUser']->getDefaultAssignments('finance', $filters_assign);
                if (empty($current_user_default_assignments)) {
                    $filters_assign['department'] = -1;
                    $current_user_default_assignments = $this->registry['currentUser']->getDefaultAssignments('finance', $filters_assign);
                }

                require_once PH_MODULES_DIR . 'users/models/users.factory.php';

                $assignments_owner = array();
                $assignments_responsible = array();
                $assignments_observer = array();
                $assignments_decision = array();

                // collect user assignment permissions per role
                // (as they are the same for all users with the same role)
                // and reuse them to reduce multiple fetching of same data from database
                $user_available_assignments_per_role = array();

                foreach ($current_user_default_assignments as $default_assign) {
                    if (in_array($default_assign['assignment_type'], $this->settings_assign)) {
                        $filters = array('model_lang'   => $request->get('model_lang'),
                                         'where'        => array('u.id ="' . $default_assign['assignee'] . '"',
                                                                 'u.hidden = 0',
                                                                 'u.active = 1'),
                                         'sort'         => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                         'sanitize'     => true
                                        );
                        $user = Users::searchOne($this->registry, $filters);
                        if ($user) {
                            // get assignment permissions for this user and set them
                            // to the array per role
                            if (empty($user_available_assignments_per_role[$user->get('role')])) {
                                $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Finance_Incomes_Reason');
                            }
                            $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

                            if (in_array($finance_incomes_reason->get('type'), $user_available_assignments[$default_assign['assignment_type']])) {
                                switch ($default_assign['assignment_type']) {
                                    case 'owner':
                                        $assignments_owner[] = $default_assign['assignee'];
                                        break;
                                    case 'responsible':
                                        $assignments_responsible[] = $default_assign['assignee'];
                                        break;
                                    case 'observer':
                                        $assignments_observer[] = $default_assign['assignee'];
                                        break;
                                    case 'decision':
                                        $assignments_decision[] = $default_assign['assignee'];
                                        break;
                                    default:
                                        break;
                                }
                            }
                        }
                    }
                }

                if (in_array('observer', $this->settings_assign) &&
                    $this->registry['currentUser']->getPersonalSettings('finance', 'set_observer') &&
                    in_array($finance_incomes_reason->get('type'), $current_user_assignments['observer'])) {
                    $assignments_observer[] = $this->registry['currentUser']->get('id');
                    $assignments_observer = array_unique($assignments_observer);
                }

                // set properties to model only for enabled assignment types
                if (in_array('owner', $this->settings_assign)) {
                    $finance_incomes_reason->set('assignments_owner', $assignments_owner, true);
                }
                if (in_array('responsible', $this->settings_assign)) {
                    $finance_incomes_reason->set('assignments_responsible', $assignments_responsible, true);
                }
                if (in_array('observer', $this->settings_assign)) {
                    $finance_incomes_reason->set('assignments_observer', $assignments_observer, true);
                }
                if (in_array('decision', $this->settings_assign)) {
                    $finance_incomes_reason->set('assignments_decision', $assignments_decision, true);
                }

                if (!empty($assignments_owner) || !empty($assignments_responsible) || !empty($assignments_observer) || !empty($assignments_decision)) {

                    $this->registry->set('getAssignments', true, true);
                    $filters = array('where'      => array('fir.id = ' . $finance_incomes_reason->get('id'),
                                                           'fir.annulled_by = 0'),
                                     'model_lang' => $finance_incomes_reason->get('model_lang'));
                    $current_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                    if ($finance_incomes_reason->assign()) {
                        $this->registry['messages']->setMessage($this->i18n('message_model_assign_success',
                                                                array($finance_incomes_reason->getModelTypeName())), '', 0);

                        $filters = array('where'      => array('fir.id = ' . $finance_incomes_reason->get('id'),
                                                               'fir.annulled_by = 0'),
                                         'model_lang' => $finance_incomes_reason->get('model_lang'));
                        $assigned_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                        Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                  array('model' => $finance_incomes_reason,
                                                                        'action_type' => 'assign',
                                                                        'new_model' => $assigned_reason,
                                                                        'old_model' => $current_reason));
                    } else {
                        $this->registry['messages']->setWarning($this->i18n('error_model_not_assigned',
                                                                array($finance_incomes_reason->getModelTypeName())));
                    }
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_add_success',
                                                        array($model_type_name)), '', -1);
                if ($this->registry['request']->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;

                //check if we have any after action requested
                $after_action = $request->get('finance_after_action');
                if ($after_action == 'invoice') {
                    $request->set('after_action', 'addinvoice', 'get', true);
                }
            } else {
                // model should not have an id
                $finance_incomes_reason->unsetProperty('id', true);

                $finance_incomes_reason->getGT2Vars();
                $grouping_table_2 = $finance_incomes_reason->get('grouping_table_2');
                if (!empty($grouping_table_2['values'])) {
                    //get original row indexes for transformations
                    $rows = array_keys($grouping_table_2['values']);
                }

                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_failed',
                                                      array($model_type_name)), '', -2);
            }
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            if (!$this->registry['request']->isPost()) {
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
            }

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';

            if (strpos( $finance_incomes_reason->get('type'), 'advance') === 0) {
                $tmp = explode('_', $finance_incomes_reason->get('type'));
                $finance_incomes_reason->set('type', PH_FINANCE_TYPE_INVOICE, true);
                $finance_incomes_reason->set('advance', $tmp[1], true);

                $finance_incomes_reason->calculateFiscalEventDates();
                if ($request->get('fiscal_event_date') &&
                    $request->get('fiscal_event_date') >= $finance_incomes_reason->get('min_fiscal') &&
                    $request->get('fiscal_event_date') <= $finance_incomes_reason->get('max_fiscal')) {
                    // validate fiscal event date from the form
                    $finance_incomes_reason->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
                }

            } elseif (preg_match('/^proforma_advance_(\d+)$/', $finance_incomes_reason->get('type'), $matches)) {
                $finance_incomes_reason->set('type', PH_FINANCE_TYPE_PRO_INVOICE, true);
                $finance_incomes_reason->set('advance', $matches[1], true);
            }

            //check validity of the type
            if (empty($financeType)) {
                $financeType = Finance_Documents_Types::searchOne($this->registry,
                    array(
                        'where' => array(
                            'fdt.model="Finance_Incomes_Reason"',
                            'fdt.id = "' . $finance_incomes_reason->get('type') . '"',
                            'fdt.active = 1'
                        ),
                        'sanitize' => true
                    )
                );
            }

            $type_permission = false;
            if ($financeType) {
                // check if adding is possible in general
                $check_add_allowed = false;
                if ($finance_incomes_reason->get('advance')) {
                    $financeTypeReason = Finance_Documents_Types::searchOne($this->registry,
                        array(
                            'where' => array(
                                'fdt.id=' . $finance_incomes_reason->get('advance')
                            ),
                            'sanitize' => true
                        )
                    );

                    if ($financeTypeReason) {
                        if ($finance_incomes_reason->get('type') == PH_FINANCE_TYPE_INVOICE) {
                            $check_add_allowed = $financeTypeReason->get('add_invoice');
                        } elseif ($finance_incomes_reason->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                            $check_add_allowed = $financeTypeReason->get('add_proforma');
                        }
                    }
                } elseif ($finance_incomes_reason->get('type') > PH_FINANCE_TYPE_MAX) {
                    $check_add_allowed = true;
                }

                $type_permission = $check_add_allowed && $this->checkActionPermissions($this->module . '_' . $this->controller . $financeType->get('id'), 'add');
            }

            if (!$financeType || !$type_permission) {
                //invalid type, redirect to list
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_failed', array($model_type_name)), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invalid_type'));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list');
            }

            // get payment way and type name for advanced invoice/proforma from type of incomes reason
            if ($finance_incomes_reason->get('advance') && !empty($financeTypeReason)) {
                $finance_incomes_reason->set('type_payment_way', $financeTypeReason->get('payment_way'), true);
                unset($financeTypeReason);
            }

            if (!$skip_get_gt2_vars_for_new_model) {
                $finance_incomes_reason->getGT2Vars();
            }

            if ($finance_incomes_reason->get('type') > PH_FINANCE_TYPE_MAX) {
                $finance_incomes_reason->set('type_payment_way', $financeType->get('payment_way'), true);
            }
            $finance_incomes_reason->set('type_name', $financeType->get('name'), true);

            if (!empty($rows)) {
                //set original row indexes for transformations
                $grouping_table_2 = $finance_incomes_reason->get('grouping_table_2');
                $grouping_table_2['rows'] = $rows;
                $finance_incomes_reason->set('grouping_table_2', $grouping_table_2, true);
            }

            if (!$this->registry['request']->isPost()) {
                //set default values from the type
                $finance_incomes_reason->setGroup(false, $financeType->get('default_group'));
                $finance_incomes_reason->setDepartment(false, $financeType->get('default_department'));
                // set employee from current user
                if (!$finance_incomes_reason->get('employee') && $this->registry['currentUser']->get('employee')) {
                    $finance_incomes_reason->set('employee', $this->registry['currentUser']->get('employee'), true);
                    $finance_incomes_reason->set('employee_name', $this->registry['currentUser']->get('employee_name'), true);
                }
            }

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
        }

        return true;
    }

    /**
     * Edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $finance_incomes_reason->get('id'),
                                               'fir.annulled_by = 0'));
            $this->old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $this->old_model->getGT2Vars();
            $this->old_model->sanitize();

            // 'link_to' and 'link_to_model_name' properties are required in
            // invoice or proforma invoice for checkAddingInvoice()
            $reason = false;
            if ($finance_incomes_reason && in_array($finance_incomes_reason->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                $finance_incomes_reason->getLinkTo();
                //get parent reason and its values
                $filters = array('where' => array ('fir.id = ' . $finance_incomes_reason->get('link_to'), 'fir.annulled_by = 0'));
                $reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $reason->getGT2Vars();
                $reason->set('invoice_id', $finance_incomes_reason->get('id'), true);
                $reason->checkAddingInvoice(true);
            }
            $this->registry->set('get_old_vars', false, true);
            if ($finance_incomes_reason->prepareInvoiceChanges($reason) && $finance_incomes_reason->save()) {
                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('id'),
                                                  'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_incomes_reason->getGT2Vars();
                $this->registry->set('get_old_vars', false, true);

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'edit',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $this->old_model
                                                                          ));

                if ($request->get('finance_after_action') == 'payment') {
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addpayment',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $finance_incomes_reason
                                                                              ));
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_success',
                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                if ($this->registry['request']->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;

                //check if we have any after action requested
                $after_action = $request->get('finance_after_action');
                if ($after_action == 'invoice') {
                    $request->set('after_action', 'addinvoice', 'get', true);
                }
            } else {
                //some error occurred
                $finance_incomes_reason->set('status', $this->old_model->get('status'), true);
                $finance_incomes_reason->set('payment_status', $this->old_model->get('payment_status'), true);
                if (!$finance_incomes_reason->get('company_data')) {
                    // set properties from old model (used in getting available options for Cashbox/Bank account field)
                    $company_data_properties = array('company', 'office', 'payment_type');
                    foreach ($company_data_properties as $prop) {
                        $finance_incomes_reason->set($prop, $this->old_model->get($prop), true);
                    }
                }
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_failed',
                                                      array($finance_incomes_reason->getModelTypeName())), '', -2);
                //ToDo - set non editable properties as currency, amount ...
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

            if ($finance_incomes_reason) {
                $this->checkAccessOwnership($finance_incomes_reason);
            }
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $finance_incomes_reason->getGT2Vars();

            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $finance_incomes_reason->get('type'))));
            $finance_incomes_reason->set('type_payment_way', $financeType->get('payment_way'), true);

            if (!$finance_incomes_reason->get('issue_date') || $finance_incomes_reason->get('issue_date') == '0000-00-00') {
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
            }

            if ($finance_incomes_reason->get('type') == PH_FINANCE_TYPE_INVOICE) {
                $finance_incomes_reason->calculateFiscalEventDates();
                if ($request->get('fiscal_event_date') &&
                        $request->get('fiscal_event_date') >= $finance_incomes_reason->get('min_fiscal') &&
                        $request->get('fiscal_event_date') <= $finance_incomes_reason->get('max_fiscal')) {
                    // validate fiscal event date from the form
                    $finance_incomes_reason->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
                }
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add invoice
     */
    private function _addInvoice() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost() && !$request->isRequested('reload_form')) {
            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $request->get('link_to'), 'fir.annulled_by = 0'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();

            // set the reason as old model for automations (so that automations can be performed on both models)
            $this->old_model = clone $old_model;

            $old_model->checkAddingInvoice(true);
            $this->registry->set('get_old_vars', false, true);

            if ($finance_incomes_reason->prepareInvoiceChanges($old_model) && $finance_incomes_reason->save()) {
                // When include_VAT = 1, we have to VAT the reason (only when the first invoice is added,
                // otherwise VAT rate of reason and invoice will be the same)
                if ($finance_incomes_reason->checkUpdateReasonVAT(true, $old_model)) {
                    $vat_params = array(
                        'total_vat_rate' => $finance_incomes_reason->get('total_vat_rate'),
                        'total_no_vat_reason' => $finance_incomes_reason->get('total_no_vat_reason'),
                        'total_no_vat_reason_text' => General::slashesStrip($finance_incomes_reason->get('total_no_vat_reason_text'))
                    );
                    $old_model->updateReasonVAT($vat_params);
                }

                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('id'), 'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_incomes_reason->getGT2Vars();
                $finance_incomes_reason->sanitize();

                $new_id = $finance_incomes_reason->get('id');
                $finance_incomes_reason->set('id', $old_model->get('id'), true);

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'addinvoice',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                $finance_incomes_reason->set('id', $new_id, true);
                $old_model = new Finance_Incomes_Reason($this->registry);
                $old_model->set('type', $finance_incomes_reason->get('type'), true);
                $this->registry->set('get_old_vars', true, true);
                $old_model->getGT2Vars();
                $old_model->sanitize();
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'add',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                if ($request->get('finance_after_action') == 'payment') {
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addpayment',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $finance_incomes_reason
                                                                              ));
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_invoice_success'), '', -1);
                if ($request->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;

                if ($finance_incomes_reason->slashesEscaped) {
                    $finance_incomes_reason->slashesStrip();
                }
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason, true);

                //redirect the user to the newly created invoice
                $url = sprintf(
                    '%s?%s=finance&%s=incomes_reasons&incomes_reasons=view&view=%d',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'],
                    $this->registry['controller_param'], $finance_incomes_reason->get('id')
                );
                $this->registry->set('redirect_to_url', $url, true);
                $this->registry->set('exit_after', true, true);

                return true;
            } else {
                //some error occurred
                //$finance_incomes_reason->set('id', $finance_incomes_reason->get('link_to'), true);
                $id = $finance_incomes_reason->get('link_to');
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_failed'), '', -2);
            }
        }

        if (!$this->actionCompleted && $id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

            $use_ajax = $request->get('use_ajax');
            if (!$finance_incomes_reason) {
                // AJAX action
                if ($use_ajax == 1) {
                    exit;
                }

                //show error 'no such record'
                $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            if ($finance_incomes_reason->get('type') == PH_FINANCE_TYPE_PRO_INVOICE || $use_ajax == 1) {

                // if action is not allowed: if using AJAX - exit, otherwise redirect to view
                $error_access = !$this->checkAccessOwnership($finance_incomes_reason, !$use_ajax);

                // display lightbox for confirmation
                if ($use_ajax == 1) {
                    $finance_incomes_reason->getGT2Vars();
                    if (!empty($error_access) || !$finance_incomes_reason->checkAddingInvoice()) {
                        // invoice cannot be issued
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_failed'), '', -1);
                    } else {
                        $this->registry['messages']->setMessage($this->i18n('finance_incomes_reasons_issue_invoice_from_proforma'), '', -1);
                    }
                    $viewer = new Viewer($this->registry, false);
                    $viewer->data['messages'] = $this->registry['messages'];
                    $new_finance_incomes_reason = clone $finance_incomes_reason;
                    $new_finance_incomes_reason->sanitize();
                    $new_finance_incomes_reason->set('old_id', $new_finance_incomes_reason->get('id'), true);
                    $new_finance_incomes_reason->unsetProperty('id', true);
                    $new_finance_incomes_reason->set('old_type', $new_finance_incomes_reason->get('type'), true);
                    $new_finance_incomes_reason->set('type', PH_FINANCE_TYPE_INVOICE, true);

                    //get dates from the request
                    if (!$issue_date = $request->get('issue_date')) {
                        $issue_date = General::strftime('%Y-%m-%d');
                    }
                    $new_finance_incomes_reason->set('issue_date', $issue_date, true);
                    if ($request->get('date_of_payment')) {
                        //we reload the form so get the date of payment from the request
                        $new_finance_incomes_reason->set('date_of_payment', $request->get('date_of_payment'), true);
                    } elseif ($new_finance_incomes_reason->isDefined('date_of_payment')) {
                        // prepare date of payment only if issue date is set (and not a formula based on receive date)
                        $day_offset = intval(round(((strtotime($new_finance_incomes_reason->get('date_of_payment')) - strtotime($finance_incomes_reason->get('issue_date')))/86400), 0));
                        $date_of_payment = General::strftime('%Y-%m-%d', strtotime('+' . $day_offset . ' day', strtotime($issue_date)));
                        $new_finance_incomes_reason->set('date_of_payment', $date_of_payment, true);
                    }
                    $new_finance_incomes_reason->calculateFiscalEventDates();
                    if ($request->get('fiscal_event_date') &&
                        $request->get('fiscal_event_date') >= $new_finance_incomes_reason->get('min_fiscal') &&
                        $request->get('fiscal_event_date') <= $new_finance_incomes_reason->get('max_fiscal')) {
                        // validate fiscal event date from the form
                        $new_finance_incomes_reason->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
                    }

                    $viewer->data['new_finance_incomes_reason'] = $new_finance_incomes_reason;
                    $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_invoice_from_proforma_confirm.html');
                    $viewer->display();
                    exit;
                } else {
                    $issue_date = $request->get('issue_date');
                    if (!$issue_date) {
                        $issue_date = General::strftime('%Y-%m-%d');
                    }
                    $error = 0;

                    // check if invoice can be issued
                    if (!$finance_incomes_reason->checkAddingInvoice()) {
                        // invoice cannot be issued
                        $error = 1;
                    }

                    // issue date of invoice cannot be before issue date of proforma
                    if ($issue_date < $finance_incomes_reason->get('issue_date')) {
                        $issue_date = $finance_incomes_reason->get('issue_date');
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reason_issue_invoice_from_proforma_issue_date'));
                        $error = 1;
                    }

                    // only if present in lightbox
                    if ($request->isRequested('date_of_payment')) {
                        // date of payment cannot be empty or before issue date
                        $date_of_payment = $request->get('date_of_payment');
                        if (!$date_of_payment || $date_of_payment < $issue_date) {
                            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reason_wrong_date_of_payment',
                                                                  array('date_of_payment' => General::strftime($this->i18n('date_short'), $issue_date))));
                            $error = 1;
                        }
                    }

                    // if proforma advance
                    if ($finance_incomes_reason->get('advance')) {
                        if (!$finance_incomes_reason->checkAddingAdvance()) {
                            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_can_not_add_advance'));
                            $error = 1;
                        }
                    }
                    // check for advance invoices
                    $tmp_model = new Finance_Incomes_Reason($this->registry);
                    $finance_incomes_reason->getRelatives(array('get_parent_reasons' => 1));
                    $parent_reasons = $finance_incomes_reason->get('parent_reasons');
                    $finance_incomes_reason->unsetProperty('parent_reasons', true);
                    if (!empty($parent_reasons[0])) {
                        $parent_reason = $parent_reasons[0];
                        $advanced_total = $parent_reason->getAdvancedTotal();
                        if ($advanced_total + $finance_incomes_reason->get('total') > $parent_reason->get('total')) {
                            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advanced_total',
                                                                  array($advanced_total + $finance_incomes_reason->get('total'),
                                                                        $parent_reason->get('total'))));
                            $error = 1;
                        }
                    }

                    // if any validation errors
                    if ($error) {
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_failed'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        $this->redirect($this->module, 'view', 'view=' . $id);
                    }

                    //create invoice from proforma invoice
                    $result_id = Finance_Incomes_Reasons::invoiceFromProforma($this->registry, array($id));

                    if (!$result_id) {
                        $this->redirect($this->module, 'view', 'view=' . $id);
                    } else {
                        //now we have only one ID and can redirect to the view action
                        //of the document

                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_invoice_success'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        $this->actionCompleted = true;

                        // set the proforma as old model and the invoice as model so that automations can be performed on both
                        $finance_incomes_reason->sanitize();
                        $this->old_model = clone $finance_incomes_reason;

                        $filters = array('where' => array('fir.id = ' . $result_id[0]));
                        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                        $finance_incomes_reason->getGT2Vars();
                        $finance_incomes_reason->sanitize();
                        $this->registry->set('finance_incomes_reason', $finance_incomes_reason, true);

                        //redirect the user to the newly created invoice
                        $url = sprintf(
                            '%s?%s=finance&%s=incomes_reasons&incomes_reasons=view&view=%d',
                            $_SERVER['PHP_SELF'], $this->registry['module_param'],
                            $this->registry['controller_param'], $finance_incomes_reason->get('id')
                        );
                        $this->registry->set('redirect_to_url', $url, true);
                        $this->registry->set('exit_after', true, true);

                        return true;
                    }
                }
            } else {
                // set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);
                if (!$request->isPost()) {
                    $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
                } else {
                    if ($request->get('issue_date')) {
                        $finance_incomes_reason->set('issue_date', $request->get('issue_date'), true);
                    } else {
                        $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
                    }
                    $finance_incomes_reason->set('date_of_payment_count', $request->get('date_of_payment_count'), true);
                    $finance_incomes_reason->set('date_of_payment_period_type', $request->get('date_of_payment_period_type'), true);
                    $finance_incomes_reason->set('date_of_payment_period', 'day', true);
                    $finance_incomes_reason->set('date_of_payment_direction', $request->get('date_of_payment_direction'), true);
                    $finance_incomes_reason->set('date_of_payment_point', $request->get('date_of_payment_point'), true);
                }
                $finance_incomes_reason->calculateFiscalEventDates();
                if ($request->get('fiscal_event_date') &&
                    $request->get('fiscal_event_date') >= $finance_incomes_reason->get('min_fiscal') &&
                    $request->get('fiscal_event_date') <= $finance_incomes_reason->get('max_fiscal')) {
                    // validate fiscal event date from the form
                    $finance_incomes_reason->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
                }

                if (!$this->registry->isRegistered('finance_incomes_reason')) {
                    $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
                }
            }
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $old_type = $finance_incomes_reason->get('type');
            $old_status = $finance_incomes_reason->get('status');
            $finance_incomes_reason->set('parent_type', $finance_incomes_reason->get('type'), true);
            $finance_incomes_reason->set('type', PH_FINANCE_TYPE_INVOICE, true);
            $finance_incomes_reason->set('status', 'opened', true);
            $gt2 = $finance_incomes_reason->getGT2Vars();
            $finance_incomes_reason->set('status', $old_status, true);
            if (!$request->isPost()) {
                // set default values for group and department
                $finance_incomes_reason->setGroup($finance_incomes_reason->get('group'));
                $finance_incomes_reason->setDepartment($finance_incomes_reason->get('department'));
            }

            $finance_incomes_reason->set('type', $old_type, true);
            $this->checkAccessOwnership($finance_incomes_reason);
            $gt2 = $finance_incomes_reason->get('grouping_table_2');
            if (empty($gt2)) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_no_quantity',
                                                      array('reason_type_name' => $finance_incomes_reason->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            } elseif ($finance_incomes_reason->checkAddingAdvance()) {
                //get advances
                $customer_advances = $finance_incomes_reason->getAllCustomerAdvances();
                if ($customer_advances) {
                    if ($request->isPost()) {
                        //unset post advance rows
                        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                        $filters = array('where' => array('n.subtype = "advance"'));
                        $nomenclatures = Nomenclatures::search($this->registry, $filters);
                        $nom = array();
                        foreach ($nomenclatures as $key => $val) {
                            $nom[] = $val->get('id');
                        }
                        if (!empty($nom)) {
                            foreach ($gt2['values'] as $key => $val) {
                                if (in_array($val['article_id'], $nom)) {
                                    unset($gt2['values'][$key]);
                                }
                            }
                        }
                    }

                    //add delimiter
                    $gt2['last_editable_row_index'] = count($gt2['values']);

                    //according to Bug 3492, allow users to change the article description
                    $gt2['fields_not_readonly'] = array('article_description');

                    $this->registry->set('get_old_vars', true, true);
                    $prec = $this->registry['config']->getSectionParams('precision');

                    // fields to process in advance rows
                    $gt2_fields = array($gt2['calculated_price']);

                    //add advance rows
                    foreach ($customer_advances as $customer_advance) {
                        $advanceValue = $customer_advance['shared'] - $customer_advance['invoiced_reason'];
                        if (!$customer_advance['checked'] || $advanceValue <= 0) {
                            //IMPORTANT: do not include row with advance for zero advance
                            continue;
                        }

                        $advance = Finance_Incomes_Reasons::searchOne($this->registry,
                                                                      array('where' => array('fir.id = ' . $customer_advance['id'],
                                                                                             'fir.annulled_by = 0')));
                        $advance->getGT2Vars();
                        $tmp_gt2 = $advance->get('grouping_table_2');
                        $tmp_gt2_vals = current($tmp_gt2['values']);
                        foreach ($gt2_fields as $field) {
                            $tmp_gt2_vals[$field] = -1*$advanceValue;
                        }

                        $advance->getRelatives(array('get_credit_notices' => true));
                        if ($advance->isDefined('credit_notices')) {
                            $advance_credit = $advance->get('credit_notices');
                            foreach ($advance_credit as $ac) {
                                if ($ac->get('annulled_by')) {
                                    continue;
                                }
                                $ac->getGT2Vars();
                                $ac_gt2 = $ac->get('grouping_table_2');
                                $ac_gt2_vals = current($ac_gt2['values']);
                                foreach ($gt2_fields as $field) {
                                    // advance credit notes always contain reduction of price, never of quantity
                                    $tmp_gt2_vals[$field] -= $ac_gt2_vals[$field];
                                }
                            }
                            $advance->unsetProperty('credit_notices', true);
                        }

                        //according to Bug 3492, put default advance text
                        $tmp_gt2_vals['article_description'] = $this->i18n('finance_incomes_reasons_advance_default_text',
                            array(
                                $advance->get('num'),
                                General::strftime($this->i18n('date_short'), $advance->get('issue_date'))
                            )
                        );
                        //IMPORTANT: set id of the advance invoice in the article_barcode field
                        $tmp_gt2_vals['article_barcode'] = $advance->get('id');
                        $gt2['values'][$tmp_gt2_vals['id']] = $tmp_gt2_vals;
                        $gt2['rows'][] = $tmp_gt2_vals['id'];
                        //set readonly advance rows
                        $gt2['rows_readonly'][] = count($gt2['values']);
                    }
                    $this->registry->set('get_old_vars', true, true);
                }
            }

            //set readonly fields - article_name
            $gt2['vars']['article_name']['readonly'] = 1;

            $finance_incomes_reason->set('grouping_table_2', $gt2, true);
            // call calculation to update all fields in advance rows
            $finance_incomes_reason->calculateGT2();

            if (!$request->isPost()) {
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add proforma invoice
     */
    private function _addProformaInvoice() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $request->get('link_to'), 'fir.annulled_by = 0'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();

            // set the reason as old model for automations (so that automations can be performed on both models)
            $this->old_model = clone $old_model;

            $old_model->checkAddingInvoice(true);
            $this->registry->set('get_old_vars', false, true);

            if ($finance_incomes_reason->prepareInvoiceChanges($old_model) && $finance_incomes_reason->save()) {
                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('id'), 'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_incomes_reason->getGT2Vars();
                $finance_incomes_reason->sanitize();

                $new_id = $finance_incomes_reason->get('id');
                $finance_incomes_reason->set('id', $old_model->get('id'), true);

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'addproformainvoice',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                $finance_incomes_reason->set('id', $new_id, true);
                $old_model = new Finance_Incomes_Reason($this->registry);
                $old_model->set('type', $finance_incomes_reason->get('type'), true);
                $this->registry->set('get_old_vars', true, true);
                $old_model->getGT2Vars();
                $old_model->sanitize();
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'add',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                if ($request->get('finance_after_action') == 'payment') {
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addpayment',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $finance_incomes_reason
                                                                              ));
                }

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_proforma_invoice_success'), '', -1);
                if ($request->get('finance_after_action') == 'payment') {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addpayment_success'));
                }
                $this->registry['messages']->insertInSession($this->registry);

                $this->actionCompleted = true;

                if ($finance_incomes_reason->slashesEscaped) {
                    $finance_incomes_reason->slashesStrip();
                }
                // set the proforma as model for automations (so that automations can be performed on both models)
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason, true);

                //redirect the user to the newly created proforma invoice
                $url = sprintf(
                    '%s?%s=finance&%s=incomes_reasons&incomes_reasons=view&view=%d',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'],
                    $this->registry['controller_param'], $finance_incomes_reason->get('id')
                );
                $this->registry->set('redirect_to_url', $url, true);
                $this->registry->set('exit_after', true, true);
            } else {
                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('link_to'), 'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_incomes_reason->getGT2Vars();
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_proforma_invoice_failed'), '', -2);
                //ToDo - set non editable properties as currency, amount ...

                $finance_incomes_reason->set('date_of_payment_count', $request->get('date_of_payment_count'), true);
                $finance_incomes_reason->set('date_of_payment_period_type', $request->get('date_of_payment_period_type'), true);
                $finance_incomes_reason->set('date_of_payment_period', 'day', true);
                $finance_incomes_reason->set('date_of_payment_direction', $request->get('date_of_payment_direction'), true);
                $finance_incomes_reason->set('date_of_payment_point', $request->get('date_of_payment_point'), true);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!$this->actionCompleted) {
            if (!empty($finance_incomes_reason)) {

                // set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);

                $this->registry->set('get_old_vars', false, true);
                $old_type = $finance_incomes_reason->get('type');
                $old_status = $finance_incomes_reason->get('status');
                $finance_incomes_reason->set('parent_type', $finance_incomes_reason->get('type'), true);
                $finance_incomes_reason->set('type', PH_FINANCE_TYPE_PRO_INVOICE, true);
                $finance_incomes_reason->set('status', 'opened', true);
                $finance_incomes_reason->getGT2Vars();
                $finance_incomes_reason->set('status', $old_status, true);
                if (!$request->isPost()) {
                    // set default values for group and department
                    $finance_incomes_reason->setGroup($finance_incomes_reason->get('group'));
                    $finance_incomes_reason->setDepartment($finance_incomes_reason->get('department'));
                }

                $finance_incomes_reason->set('type', $old_type, true);
                $this->checkAccessOwnership($finance_incomes_reason);

                $gt2 = $finance_incomes_reason->get('grouping_table_2');
                if (empty($gt2)) {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_no_quantity',
                                                          array('reason_type_name' => $finance_incomes_reason->getModelTypeName())));
                    $this->registry['messages']->insertInSession($this->registry);
                    $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
                } elseif ($finance_incomes_reason->checkAddingAdvance()) {
                    //get invoiced proforma advances and directly issued advance invoices
                    $customer_advances = $finance_incomes_reason->getAllCustomerAdvances();
                    if ($customer_advances) {
                        if ($request->isPost()) {
                            //unset post advance rows
                            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                            $filters = array('where' => array('n.subtype = "advance"'));
                            $nomenclatures = Nomenclatures::search($this->registry, $filters);
                            $nom = array();
                            foreach ($nomenclatures as $key => $val) {
                                $nom[] = $val->get('id');
                            }
                            if (!empty($nom)) {
                                foreach ($gt2['values'] as $key => $val) {
                                    if (in_array($val['article_id'], $nom)) {
                                        unset($gt2['values'][$key]);
                                    }
                                }
                            }
                        }

                        //add delimiter
                        $gt2['last_editable_row_index'] = count($gt2['values']);

                        //according to Bug 3492, allow users to change the article description
                        $gt2['fields_not_readonly'] = array('article_description');

                        $this->registry->set('get_old_vars', true, true);
                        $prec = $this->registry['config']->getSectionParams('precision');

                        // fields to process in advance rows
                        $gt2_fields = array($gt2['calculated_price']);

                        //add advance rows
                        foreach ($customer_advances as $customer_advance) {
                            $advanceValue = $customer_advance['shared'] - $customer_advance['invoiced_reason'];
                            if (!$customer_advance['checked'] || $advanceValue <= 0) {
                                //IMPORTANT: do not include row with advance for zero advance
                                continue;
                            }
                            $advance = Finance_Incomes_Reasons::searchOne($this->registry,
                                                                          array('where' => array('fir.id = ' . $customer_advance['id'],
                                                                                                 'fir.annulled_by = 0')));
                            $advance->getGT2Vars();
                            $tmp_gt2 = $advance->get('grouping_table_2');
                            $tmp_gt2_vals = current($tmp_gt2['values']);
                            foreach ($gt2_fields as $field) {
                                $tmp_gt2_vals[$field] = -1*$advanceValue;
                            }

                            $advance->getRelatives(array('get_credit_notices' => true));
                            if ($advance->isDefined('credit_notices')) {
                                $advance_credit = $advance->get('credit_notices');
                                foreach ($advance_credit as $ac) {
                                    $ac->getGT2Vars();
                                    $ac_gt2 = $ac->get('grouping_table_2');
                                    $ac_gt2_vals = current($ac_gt2['values']);
                                    foreach ($gt2_fields as $field) {
                                        // advance credit notes always contain reduction of price, never of quantity
                                        $tmp_gt2_vals[$field] -= $ac_gt2_vals[$field];
                                    }
                                }
                                $advance->unsetProperty('credit_notices', true);
                            }

                            //according to Bug 3492, put default advance text
                            $tmp_gt2_vals['article_description'] = $this->i18n('finance_incomes_reasons_advance_default_text',
                                array(
                                    $advance->get('num'),
                                    General::strftime($this->i18n('date_short'), $advance->get('issue_date'))
                                )
                            );

                            //IMPORTANT: set id of the advance invoice in the article_barcode field
                            $tmp_gt2_vals['article_barcode'] = $advance->get('id');
                            $gt2['values'][$tmp_gt2_vals['id']] = $tmp_gt2_vals;
                            $gt2['rows'][] = $tmp_gt2_vals['id'];
                            //set readonly advance rows
                            $gt2['rows_readonly'][] = count($gt2['values']);
                        }
                        $this->registry->set('get_old_vars', true, true);
                    }
                }

                //set readonly fields - article_name, currency
                $gt2['vars']['article_name']['readonly'] = 1;
                $gt2['plain_vars']['currency']['readonly'] = 1;

                $finance_incomes_reason->set('grouping_table_2', $gt2, true);
                // call calculation to update all fields in advance rows
                $finance_incomes_reason->calculateGT2();

                //register the model,
                //so that it could be used further by the viewer
                if (!$this->registry->isRegistered('finance_incomes_reason')) {
                    $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
                }
            } else {
                //show error 'no such record'
                $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }
        }

        return true;
    }

    /**
     * Add correct reason
     */
    private function _addCorrect() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $finance_incomes_reason->get('id'),
                                               'fir.annulled_by = 0'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $this->registry->set('get_old_vars', false, true);

            if ($finance_incomes_reason->validate('edit') && $finance_incomes_reason->saveCorrect($old_model)) {
                $filters = array('where' => array('fir.id = ' . $finance_incomes_reason->get('id'),
                                                  'fir.annulled_by = 0'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $finance_incomes_reason->getGT2Vars();

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => ($old_model->get('correction_id') ? 'addcorrect' : 'edit'),
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                //show success message
                if ($old_model->get('correction_id')) {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addcorrect_success'), '', -1);
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_quantity_change_success',
                                                            array('reason_type_name' => $old_model->getModelTypeName())));
                } else {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_success',
                                                            array($finance_incomes_reason->getModelTypeName())), '', -1);
                }
                if ($old_model->get('distributed') == PH_FINANCE_DISTRIBUTION_YES) {
                    $url = sprintf('<a target="_blank" href="%s?%s=%s&amp;%s=%s&amp;%s=distribute&amp;distribute=%d">%s</a>',
                                   $_SERVER['PHP_SELF'],
                                   $this->registry['module_param'], 'finance',
                                   $this->registry['controller_param'], 'incomes_reasons',
                                   'incomes_reasons', $old_model->get('id'),
                                   $old_model->getModelTypeName());
                    $this->registry['messages']->setWarning($this->i18n('warning_finance_incomes_reasons_distribution_deleted',
                                                            array('reason_type_name' => $url)));
                }
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                $this->redirect($this->module, 'view', 'view=' . ($old_model->get('correction_id') ?: $old_model->get('id')));
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addcorrect_failed',
                                                      array($old_model->getModelTypeName())), '', -2);
                //ToDo - set non editable properties as currency, amount ...
                $fir = clone $finance_incomes_reason;
                $finance_incomes_reason = $old_model;
                $finance_incomes_reason->set('parent_name', ($old_model->get('name') ? $old_model->get('name') : $old_model->get('type_name')), true);
                $finance_incomes_reason->set('name', $request->get('name'), true);
                $finance_incomes_reason->set('company_data', $fir->get('company_data'), true);
                $finance_incomes_reason->set('employee', $fir->get('employee'), true);
                $finance_incomes_reason->set('issue_date', $request->get('issue_date'), true);
                $finance_incomes_reason->set('description', $request->get('description'), true);
                $finance_incomes_reason->set('department', $fir->get('department'), true);
                $finance_incomes_reason->set('group', $fir->get('group'), true);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $old_type = $finance_incomes_reason->get('type');
            $finance_incomes_reason->set('parent_type', $finance_incomes_reason->get('type'), true);
            $finance_incomes_reason->set('type', PH_FINANCE_TYPE_CORRECT_REASON, true);

            if (!$request->isPost()) {
                // set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);

                // set default values for group and department
                $finance_incomes_reason->setGroup($finance_incomes_reason->get('group'));
                $finance_incomes_reason->setDepartment($finance_incomes_reason->get('department'));
            }
            $status = $finance_incomes_reason->get('status');
            $finance_incomes_reason->set('status', 'opened', true);
            $finance_incomes_reason->getGT2Vars();
            $finance_incomes_reason->set('status', $status, true);
            $gt2 = $finance_incomes_reason->get('grouping_table_2');
            $finance_incomes_reason->set('type', $old_type, true);
            $this->checkAccessOwnership($finance_incomes_reason);

            if (empty($gt2)) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_invoice_no_quantity',
                                                      array('reason_type_name' => $finance_incomes_reason->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Creates credit and debit notices for invoice
     */
    private function _addCreditDebit() {

        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $finance_incomes_reason->get('id'),
                                               'fir.annulled_by = 0'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();

            $this->registry->set('get_old_vars', false, true);
            $this->registry['db']->StartTrans();
            $added = false;
            if ($finance_incomes_reason->validate('edit')) {
                $added = $finance_incomes_reason->saveCreditDebit($old_model);
            }

            if (!empty($added)) {
                if ($added === true) {
                    if ($finance_incomes_reason->get('reason_model_name') != 'Contract') {
                        $this->registry['messages']->setWarning($this->i18n('warning_finance_incomes_reasons_addcorrect_denied'), '', -5);
                    }
                } elseif ($old_model->get('advance')) {
                    $this->actionCompleted = true;
                } else {
                    //we have to issue correct reason.....may be
                    $filters = array('where' => array ('fir.id = ' . $finance_incomes_reason->get('id'),
                                                       'fir.annulled_by = 0'));
                    $invoice = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                    $this->registry->set('get_old_vars', true, true);
                    $invoice->getGT2Vars();
                    //get the model and its old values
                    if ($invoice->correctFromInvoice($added)) {
                        $finance_incomes_reason = $invoice;
                        $this->actionCompleted = true;
                    } else {
                        //some error occurred
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addcreditdebit_failed'), '', -2);
                        $this->registry['db']->FailTrans();
                    }
                }
                $errors = $this->registry['messages']->getErrors();
                if (empty($errors)) {
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addcreditdebit_success'), '', -5);
                } else {
                    $this->registry['messages']->unset_vars('messages');
                    $this->registry['messages']->unset_vars('warnings');
                    $this->registry['db']->FailTrans();
                }
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addcreditdebit_failed'), '', -2);
                $this->registry['db']->FailTrans();
                $errors = $this->registry['messages']->getErrors();
            }
            $this->registry['db']->CompleteTrans();
            if (empty($errors)) {
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                if (!empty($added['credit'])) {
                    if (!empty($added['debit'])) {
                        //redirect to invoice view action
                        $this->redirect($this->module, 'view', 'view=' . $old_model->get('id'));
                    } else {
                        //redirect to credit view action
                        $this->redirect($this->module, 'view', 'view=' . $added['credit']);
                    }
                } elseif (!empty($added['debit'])) {
                    //redirect to debit view action
                    $this->redirect($this->module, 'view', 'view=' . $added['debit']);
                }

            } else {

                // keep posted values
                $fir = clone $finance_incomes_reason;

                // the model from the DB
                $filters['where'] = array('fir.id = ' . $old_model->get('id'), 'fir.annulled_by = 0');
                $filters['model_lang'] = $request->get('model_lang');
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                if ($finance_incomes_reason) {
                    $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                    $finance_incomes_reason->set('name', $fir->get('name'), true);
                    $finance_incomes_reason->set('company_data', $fir->get('company_data'), true);
                    $finance_incomes_reason->set('employee', $fir->get('employee'), true);
                    $finance_incomes_reason->set('issue_date', $fir->get('issue_date'), true);
                    $finance_incomes_reason->set('date_of_payment_count', $fir->get('date_of_payment_count'), true);
                    $finance_incomes_reason->set('date_of_payment_period_type', $fir->get('date_of_payment_period_type'), true);
                    $finance_incomes_reason->set('date_of_payment_point', $fir->get('date_of_payment_point'), true);
                    $finance_incomes_reason->set('cd_reason', $fir->get('cd_reason'), true);
                    $finance_incomes_reason->set('issue_correct', $fir->get('issue_correct'), true);
                    $finance_incomes_reason->set('description', $fir->get('description'), true);
                    $finance_incomes_reason->set('department', $fir->get('department'), true);
                    $finance_incomes_reason->set('group', $fir->get('group'), true);
                }
            }
        } elseif ($id) {
            // the model from the DB
            $filters = array('where' => array('fir.id = ' . $id,
                                              'fir.annulled_by = 0',
                                              'fir.type = \'' . PH_FINANCE_TYPE_INVOICE . '\''),
                             'model_lang' => $request->get('model_lang'));
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            $this->checkAccessOwnership($finance_incomes_reason);

            $this->registry->set('get_old_vars', false, true);
            $old_status = $finance_incomes_reason->get('status');
            $finance_incomes_reason->set('status', 'opened', true);
            $finance_incomes_reason->getGT2Vars();
            $finance_incomes_reason->set('status', $old_status, true);

            if (!$request->isPost()) {
                $finance_incomes_reason->checkAddingCredit(true);

                // if GT2 table of invoice is empty after checkAddingCredit(true)
                // credit notes have been issued for everything in invoice
                if (!$finance_incomes_reason->get('grouping_table_2')) {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addcreditdebit_failed'), '', -2);
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_credit_denied'));
                    $this->registry['messages']->insertInSession($this->registry);
                    $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
                }

                // set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);
            }

            // if not coming from POST or POST does not contain issue_date (user has no rights to set it)
            if (!$request->isPost() || !$request->isRequested('issue_date')) {
                $finance_incomes_reason->set('issue_date', General::strftime('%Y-%m-%d'), true);
            } else {
                $finance_incomes_reason->set('issue_date', $request->get('issue_date'), true);
            }
            if (!$request->isPost() || !$request->isRequested('date_of_payment_count')) {
                $filters = array('where' => array('fdt.id = ' . PH_FINANCE_TYPE_INVOICE), 'sanitize' => true);
                require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                $type = Finance_Documents_Types::searchOne($this->registry, $filters);
                $finance_incomes_reason->set('date_of_payment_count', $type->get('default_date_of_payment_count'), true);
                $finance_incomes_reason->set('date_of_payment_period_type', $type->get('default_date_of_payment_period_type'), true);
                $finance_incomes_reason->set('date_of_payment_point', $type->get('default_date_of_payment_point'), true);
            }
            $finance_incomes_reason->calculateFiscalEventDates();
            if ($request->get('fiscal_event_date') &&
                    $request->get('fiscal_event_date') >= $finance_incomes_reason->get('min_fiscal') &&
                    $request->get('fiscal_event_date') <= $finance_incomes_reason->get('max_fiscal')) {
                // validate fiscal event date from the form
                $finance_incomes_reason->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_invoice'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;

    }

    /**
     * Add record of handover
     */
    private function _addHandover() {
        $request = &$this->registry['request'];
        if ($request->get('setWarehouses', 'post')) {
            //we make a small trick here as we don't want warehouses IDs
            //to be transfered in the GET string
            $post = $request->getAll('post');
            foreach ($post as $k => $v) {
                $request->remove($k);
                $request->set($k, $v, 'all', true);
            }
        }

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {

            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $request->get('link_to')));
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $old_name = $finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name');
            $params = array(
                'model' => $finance_incomes_reason,
                'type' => PH_FINANCE_TYPE_HANDOVER,
            );
            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php');
            $result = Finance_Warehouses_Documents::addMultipleDocuments($this->registry, $params);

            if ($result['status']) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_handover_success_' . $request->get('handover_direction')) . ' ' . count($result['created']), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                if (count($result['created']) == 1) {
                    $created = reset($result['created']);
                    $this->redirect($this->module, 'view', array('view' => $created->get('id')), 'warehouses_documents');
                } else {
                    $this->redirect($this->module, 'list', array('type' => PH_FINANCE_TYPE_HANDOVER), 'warehouses_documents');
                }
            } else {
                // keep submitted values on unsuccessful adding of handovers
                $finance_incomes_reason->set('parent_name', $old_name, true);
                $finance_incomes_reason->set('name', $request->get('name'), true);
                $finance_incomes_reason->set('department', $request->get('department'), true);
                $finance_incomes_reason->set('group', $request->get('group'), true);
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_handover_failed_' . $request->get('handover_direction')), '', -1);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $old_type = $finance_incomes_reason->get('type');
            // IMPORTANT: Set parent_type and link_to_model_name to get VAT settings from the incomes reason
            $finance_incomes_reason->set('parent_type', $finance_incomes_reason->get('type'), true);
            $finance_incomes_reason->set('link_to_model_name', $finance_incomes_reason->modelName, true);
            $finance_incomes_reason->set('handover_direction', $request->get('handover_direction'), true);
            $finance_incomes_reason->set('type', PH_FINANCE_TYPE_HANDOVER, true);

            if (!$request->isPost()) {
                //set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);

                // set default values for group and department
                $finance_incomes_reason->setGroup($finance_incomes_reason->get('group'));
                $finance_incomes_reason->setDepartment($finance_incomes_reason->get('department'));
            }

            $finance_incomes_reason->set('gt2_model_name', 'Finance_Warehouses_Document', true);
            $finance_incomes_reason->getGT2Vars();
            $finance_incomes_reason->set('type', $old_type, true);
            $this->checkAccessOwnership($finance_incomes_reason);
            $gt2 = $finance_incomes_reason->get('grouping_table_2');

            if (empty($gt2)) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_handover_no_quantity',
                                                      array('reason_type_name' => $finance_incomes_reason->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add payment
     */
    private function _addPayment() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        // the model from the DB
        $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
        $filters['model_lang'] = $request->get('model_lang');
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $this->old_model = clone $finance_incomes_reason;

        if (!empty($finance_incomes_reason)) {
            // check if action is allowed for model
            $this->checkAccessOwnership($finance_incomes_reason);

            // get paid amount
            $finance_incomes_reason->getPaidAmount();
            if ($finance_incomes_reason->get('type') > PH_FINANCE_TYPE_MAX) {
                // get total amount of all invoices and debit/credit notes for reason
                $finance_incomes_reason->getInvoicedAmount();
                // add paid amount to non-invoiced proformas
                $finance_incomes_reason->set('paid_amount', $finance_incomes_reason->get('paid_amount') + $finance_incomes_reason->getProformasPaidAmount(true), true);
            }
            $finance_incomes_reason->set('total_with_vat', abs($finance_incomes_reason->get('total_with_vat')), true);
            $old_status = $finance_incomes_reason->get('status');

            // check if details are submitted via POST
            if ($request->isPost()) {
                // set values from request to model
                $request_properties = array('payment_sum', 'container_currency', 'container_rate', 'container_amount', 'payment_date', 'company_data', 'payment_reason');
                foreach ($request_properties as $prop) {
                    if ($prop == 'company_data') {
                        $finance_incomes_reason->set('payment_container', $request->get($prop), true);
                    } else {
                        $finance_incomes_reason->set($prop, $request->get($prop), true);
                    }
                }
                if ($payment_id = $finance_incomes_reason->addPayment()) {
                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addpayment',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $finance_incomes_reason
                                                                              ));
                    //show success message
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_addpayment_success',
                                                            array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);
                    $this->actionCompleted = true;
                    //redirect the user to the newly created payment
                    $url = sprintf('%s?%s=finance&%s=payments&payments=view&view=%d', $_SERVER['PHP_SELF'], $this->registry['module_param'],
                        $this->registry['controller_param'], $payment_id);
                    $this->registry->set('redirect_to_url', $url, true);
                    $this->registry->set('exit_after', true, true);
                } else {
                    //some error occurred
                    $finance_incomes_reason->set('status', $old_status, true);
                    $container_amount = round(($finance_incomes_reason->get('total_with_vat') - $finance_incomes_reason->get('paid_amount') - $finance_incomes_reason->get('invoices_amount')) * $finance_incomes_reason->get('container_rate'), 2);
                    $finance_incomes_reason->set('container_amount', $container_amount, true);
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_addpayment_failed',
                                                          array($finance_incomes_reason->getModelTypeName())), '', -2);
                    $finance_incomes_reason->set('payment_container_data', $this->registry['request']->get('company_data'), true);
                }
            } else {
                // sets container currency, suggested conversion rate and converted amount to model
                $finance_incomes_reason->prepareContainerRate(false);
            }
            $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * View model
     */
    private function _view() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        /**
         * @var Finance_Incomes_Reason $finance_incomes_reason
         */
        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_incomes_reason)) {
            // check if action is allowed for model
            $this->checkAccessOwnership($finance_incomes_reason);

            //check if we have date_of_payment submitted (via edit field)
            if ($this->registry['request']->isRequested('date_of_payment', 'post')) {
                $old_dop = $finance_incomes_reason->get('date_of_payment');
                $finance_incomes_reason->set('date_of_payment', $this->registry['request']->get('date_of_payment'), true);
                if ($finance_incomes_reason->get('issue_date') > $finance_incomes_reason->get('date_of_payment')) {
                    //small validation
                    $this->registry['messages']->setError(
                            $this->i18n('error_finance_incomes_reason_wrong_date_of_payment',
                                array('date_of_payment' => General::strftime($this->i18n('date_short'), $finance_incomes_reason->get('issue_date')))));
                    $finance_incomes_reason->set('date_of_payment', $old_dop, true);
                } elseif ($finance_incomes_reason->updateDateOfPayment()) {
                    //date has been saved so write some history
                    $finance_incomes_reason->unsetProperty('date_of_payment_count', true);
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_success',
                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $old_model = clone $finance_incomes_reason;
                    $old_model->set('date_of_payment', $old_dop, true);

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'edit',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));
                    // strip slashes after history is saved because model will be assigned to viewer
                    $finance_incomes_reason->slashesStrip();
                } else {
                    //ooops! something goes wrong
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_failed',
                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $finance_incomes_reason->set('date_of_payment', $old_dop, true);
                }
                $this->registry['request']->remove('date_of_payment');
                $this->registry['request']->remove('date_of_payment_formatted');

            }

            //check if we have submitted (via edit field)
            $old_fields = $new_fields = array();
            foreach ($finance_incomes_reason->editableDataFields as $dataField) {
                if ($this->registry['request']->isRequested($dataField, 'post')) {
                    $old_fields[$dataField] = $finance_incomes_reason->get($dataField);
                    $new_fields[$dataField] = $this->registry['request']->get($dataField);
                }
            }

            if (!empty($new_fields)) {
                foreach($new_fields as $field => $value) {
                    $finance_incomes_reason->set($field, $value, true);
                }
                if ($finance_incomes_reason->updateDataFields($new_fields)) {
                    //date has been saved so write some history
                    $finance_incomes_reason->unsetProperty($dataField, true);
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_success',
                                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $old_model = clone $finance_incomes_reason;
                    foreach($old_fields as $field => $value) {
                        $old_model->set($field, $value, true);
                    }

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'edit',
                                                                                  'new_model' => $finance_incomes_reason,
                                                                                  'old_model' => $old_model
                                                                              ));
                    // strip slashes after history is saved because model will be assigned to viewer
                    $finance_incomes_reason->slashesStrip();
                } else {
                    //ooops! something goes wrong
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_failed',
                                                                      array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $finance_incomes_reason->set('date_of_payment', $old_dop, true);
                }
                foreach($new_fields as $field => $value) {
                    $this->registry['request']->remove($field);
                }
            }

            //check if we have company_data submitted (via edit field)
            if ($this->registry['request']->isRequested('company_data', 'post')) {
                $old_cData = $finance_incomes_reason->get('company_data');
                $old_container_name = $finance_incomes_reason->get('container_name');
                $finance_incomes_reason->set('company_data', $this->registry['request']->get('company_data'), true);
                if (!$this->registry['request']->get('company_data')) {
                    $error = true;
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_no_container_id_specified', array($finance_incomes_reason->getLayoutName('company_data'))), "company_data");
                } else {
                    $company_data_arr = explode('_', $finance_incomes_reason->get('company_data'));
                    if ($company_data_arr[2] == 'cheque') {
                        $finance_incomes_reason->set('payment_type', 'bank', true);
                        $finance_incomes_reason->set('cheque', true, true);
                    } else {
                        $finance_incomes_reason->set('payment_type', $company_data_arr[2], true);
                    }
                    $finance_incomes_reason->set('container_id', $company_data_arr[3], true);
                    if ($finance_incomes_reason->get('payment_type') ==  'bank') {
                        $table = DB_TABLE_FINANCE_BANK_ACCOUNTS_I18N;
                    } else {
                        $table = DB_TABLE_FINANCE_CASHBOXES_I18N;
                    }
                    $query = 'SELECT name FROM ' . $table .
                             ' WHERE parent_id = ' . $finance_incomes_reason->get('container_id') . ' AND lang = "' . $finance_incomes_reason->get('model_lang') . '"';
                    $new_container_name = $this->registry['db']->GetOne($query);
                    $finance_incomes_reason->set('container_name', $new_container_name, true);
                }
                if (empty($error) && $finance_incomes_reason->updateCompanyData()) {
                    //company data has been saved so write some history
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_success',
                            array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $old_model = clone $finance_incomes_reason;
                    $old_model->set('company_data', $old_cData, true);

                    $company_data_arr = explode('_', $old_cData);
                    if ($company_data_arr[2] == 'cheque') {
                        $old_model->set('payment_type', 'bank', true);
                        $old_model->set('cheque', true, true);
                    } else {
                        $old_model->set('payment_type', $company_data_arr[2], true);
                    }
                    $old_model->set('container_id', $company_data_arr[3], true);
                    $old_model->set('container_name', $old_container_name, true);

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                            array('action_type' => 'edit',
                                'new_model' => $finance_incomes_reason,
                                'old_model' => $old_model
                            ));
                    // strip slashes after history is saved because model will be assigned to viewer
                    $finance_incomes_reason->slashesStrip();
                } else {
                    //ooops! something goes wrong
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_failed',
                            array($finance_incomes_reason->getModelTypeName())), '', -1);
                    $finance_incomes_reason->set('company_data', $old_cData, true);

                    $company_data_arr = explode('_', $old_cData);
                    if ($company_data_arr[2] == 'cheque') {
                        $finance_incomes_reason->set('payment_type', 'bank', true);
                        $finance_incomes_reason->set('cheque', true, true);
                    } else {
                        $finance_incomes_reason->set('payment_type', $company_data_arr[2], true);
                    }
                    $finance_incomes_reason->set('container_id', $company_data_arr[3], true);
                    $finance_incomes_reason->set('container_name', $old_container_name, true);
                }
                $this->registry['request']->remove('company_data');
            }
            $finance_incomes_reason->getGT2Vars();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add advance invoices
     */
    private function _advances() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fir.id = ' . $id,
                                          'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        if (empty($finance_incomes_reason)) {
            $this->redirect($this->module, 'list');
        }
        $this->checkAccessOwnership($finance_incomes_reason);

        if ($request->get('use_ajax') == 1) {
            if ($request->get('advances_action') == 'add') {
                //add new advance
                $advance = Finance_Incomes_Reasons::buildModel($this->registry);

                $old_model = new Finance_Incomes_Reason($this->registry);
                $old_model->set('type', $finance_incomes_reason->get('type'), true);
                $this->registry->set('get_old_vars', true, true);
                $old_model->getGT2Vars();
                $old_model->sanitize();
                $this->registry->set('get_old_vars', false, true);

                // perform some validations before save
                $error = false;

                // check if VAT should be updated after current invoice is added
                $check_update_vat = $advance->checkUpdateReasonVAT(true, $finance_incomes_reason);

                //get ONLY RELATED customer advances
                $advances = array_filter($finance_incomes_reason->getAllCustomerAdvances(), function ($advance) {
                    return $advance['checked']??false;
                });
                if (empty($advances)) {
                    //check if VAT rates are different
                    //include_VAT = 1 means that we will VAT the reason only if we have an invoice
                    //so VAT rate at this point is zero
                    if ($advance->get('total_vat_rate') != $finance_incomes_reason->get('total_vat_rate') && !$check_update_vat) {
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advances_vat_wrong'));
                        $error = true;
                    }
                } elseif ($advance->get('total_vat_rate') != $finance_incomes_reason->get('total_vat_rate')) {
                    //check if VAT rates are different
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advances_vat_wrong'));
                    $error = true;
                }

                //check total of advance invoices
                $advanced_total = $finance_incomes_reason->getAdvancedTotal() + $advance->get('total');
                $maxShareableAmount = $finance_incomes_reason->getAdvancesMaxSharableAmount();
                if ($advanced_total > $finance_incomes_reason->get('total')) {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advanced_total',
                                                          array($advanced_total, $finance_incomes_reason->get('total'))));
                    $error = true;
                } elseif ($advanced_total > $maxShareableAmount) {
                    $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_over_maxshareable'), $advanced_total, $maxShareableAmount));
                    $error = true;
                }

                // paid amount to non-invoiced advance proformas should not exceed non-invoiced amount of reason
                if (!$error) {
                    $reason_total = $finance_incomes_reason->get('total_with_vat');
                    if ($check_update_vat) {
                        $precision = $this->registry['config']->getParam('precision', 'gt2_total_with_vat');
                        $reason_total += round($reason_total*$advance->get('total_vat_rate')/100, $precision);
                    }
                    if ($advance->get('total_with_vat') <= 0) {
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advance_invalid_sum'));
                        $error = true;
                    } elseif ($finance_incomes_reason->getProformasPaidAmount() + $finance_incomes_reason->getInvoicedAmount() + $advance->get('total_with_vat') > $reason_total) {
                        $paidRelatives = $finance_incomes_reason->preparePaidRelativesError(false);
                        $this->registry['messages']->setError(
                            !empty($paidRelatives) ?
                            $this->i18n('error_finance_incomes_reasons_paid_relatives',array($paidRelatives)) :
                            $this->i18n('error_finance_incomes_reasons_paid_relatives2')

                        );
                        $error = true;
                    }
                }

                // use transaction because in methods executed after save (i.e. updatePaymentsFromParent)
                // there is validation that could fail adding invoice
                $this->registry['db']->StartTrans();
                if (!$error && $advance->save()) {
                    $filters = array('where' => array('fir.id = ' . $advance->get('id'),
                                                      'fir.annulled_by = 0'));
                    $advance = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $new_id = $advance->get('id');
                    $advance->set('id', $finance_incomes_reason->get('id'), true);
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addinvoice',
                                                                                    'new_model' => $advance,
                                                                                    'old_model' => $finance_incomes_reason
                                                                              ));

                    $advance->set('id', $new_id, true);
                    $this->registry->set('get_old_vars', true, true);
                    $advance->getGT2Vars();
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'add',
                                                                                    'new_model' => $advance,
                                                                                    'old_model' => $old_model
                                                                              ));

                    if ($request->get('finance_after_action') == 'payment') {
                        $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                                  array('action_type' => 'addpayment',
                                                                                        'new_model' => $advance,
                                                                                        'old_model' => $advance
                                                                                  ));
                    }

                    //add new advance to reason
                    $adv_id = $advance->get('id');
                    $new_advances = array(
                        $adv_id => array(
                            'id' => $adv_id,
                            'value' => $advance->get('total'),
                        )
                    );
                    $finance_incomes_reason->set('new_advances', $new_advances, true);
                    $finance_incomes_reason->updateAdvances(false);

                    //update advance invoice payments if there are payments for parent reason
                    //IMPORTANT: do not update payments of the advances!!!!!!!
                    //$advance->updatePaymentsFromParent();

                    if ($this->registry['messages']->getErrors()) {
                        $this->registry->set('new_incomes_reason', $advance->sanitize(), true);
                    } else {
                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_add_advance_success'), '', -2);
                    }
                } else {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_advance_failed'), '', -1);
                    $this->registry->set('new_incomes_reason', $advance->sanitize(), true);
                    if (!$advance->get('issue_date')) {
                        $advance->set('issue_date', General::strftime('%Y-%m-%d'), true);
                    }
                    $advance->calculateFiscalEventDates();
                }
                $this->registry['db']->CompleteTrans();
            } elseif ($request->get('advances_action') == 'edit') {
                //update advance relatives from ajax
                $new_advances = array();
                $postedAdvances = $request->get('items');
                $postedAdvanceValues = $request->get('item_values');
                if (empty($postedAdvances)) {
                    $postedAdvances = array();
                    $postedAdvanceValues = array();
                }
                foreach($postedAdvances as $advId) {
                    $new_advances[$advId] = array(
                        'id' => $advId,
                        'value' => floatval($postedAdvanceValues[$advId] ?? null),
                    );
                }
                $finance_incomes_reason->set('new_advances', $new_advances, true);

                // check total of selected advance invoices (using the 'new_advances' property of reason)
                //$advanced_total = $finance_incomes_reason->getAdvancedTotal();

                $old_model = clone $finance_incomes_reason;
                $old_model->sanitize();

                // perform some validations before save
                $error = false;

                if (count(array_filter($postedAdvanceValues, function ($a) {return $a <0;})) > 0) {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advanced_negative'));
                    $error = true;
                }


                $customerAdvances = $finance_incomes_reason->getAllCustomerAdvances();
                $advanced_total = 0;
                foreach($postedAdvanceValues as $advId => $postedAdvanceValue) {
                    $customerAdvance = $customerAdvances[$advId]??[];
                    if (empty($customerAdvance)) {
                        continue;
                    }
                    if ($postedAdvanceValue > $customerAdvance['remainder']) {
                        $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_value_greater_than_remainder'), $customerAdvance['num'], $postedAdvanceValue, $customerAdvance['remainder']));
                        $error = true;
                    } elseif ($postedAdvanceValue > $customerAdvance['total'] - $customerAdvance['invoiced_other']) {
                        $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_value_greater_than_uninvoiced'), $customerAdvance['num'], $postedAdvanceValue, $customerAdvance['total'] - $customerAdvance['invoiced_other']));
                        $error = true;
                    }
                    if ($postedAdvanceValue < $customerAdvance['invoiced_reason']) {
                        $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_value_lower_than_invoiced'), $customerAdvance['num'], $postedAdvanceValue, $customerAdvance['invoiced_reason']));
                        $error = true;
                    }
                    if ($postedAdvanceValue == 0 && $customerAdvance['invoiced_reason'] == 0) {
                        $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_value_is_invalid'), $customerAdvance['num'], $postedAdvanceValue));
                        $error = true;
                    }
                    if (!empty($postedAdvances[$advId])) {
                        $advanced_total += $postedAdvanceValue;
                    }
                }

                $maxShareableAmount = $finance_incomes_reason->getAdvancesMaxSharableAmount();
                if ($advanced_total > $finance_incomes_reason->get('total')) {
                    $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_total'), $advanced_total, $finance_incomes_reason->get('total')));
                    $error = true;
                } elseif ($advanced_total > $maxShareableAmount) {
                    $this->registry['messages']->setError(sprintf($this->i18n('error_finance_incomes_reasons_advanced_over_maxshareable'), $advanced_total, $maxShareableAmount));
                    $error = true;
                }

                if (!$error && $edit_result = $finance_incomes_reason->updateAdvances()) {

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'advances',
                                                                                    'new_model' => $finance_incomes_reason,
                                                                                    'old_model' => $old_model));

                    //update advance relatives to the reason
                    $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_advances'), '', -2);
                } else {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_advances'), '', -1);
                }
            }

            //get customer advances (RELATED AND AVAILABLE)
            $finance_incomes_reason->getAllCustomerAdvances();
            /*
            $advances = $finance_incomes_reason->get('customer_advances');
            $checked = false;
            //remove advances with different VAT rate
            foreach ($advances as $key => $adv) {
                if (!$adv['checked']) {
                    if ($adv['total_vat_rate'] != $finance_incomes_reason->get('total_vat_rate')) {
                        unset($advances[$key]);
                    }
                } else {
                    $checked = true;
                }
            }
            //checked = false if we don't have any advances related to the reason
            //in this case we will show all the advances
            if ($checked) {
                $finance_incomes_reason->set('customer_advances', $advances, true);
            }
            */

//            if ($request->get('advances_action') == 'edit' && empty($edit_result)) {
//                // set checked values from POST
//                $customer_advances = $finance_incomes_reason->get('customer_advances');
//                $postedAdvIds = $this->registry['request']->getPost('items')?:array();
//                $postedAdvValues = $this->registry['request']->getPost('item_values')?:array();
//                foreach ($customer_advances as $advId => $advance) {
//                    $customer_advances[$advId]['checked'] = array_key_exists($advId, $postedAdvIds) ? $postedAdvIds[$advId] : null;
//                    $customer_advances[$advId]['shared'] = array_key_exists($advId, $postedAdvValues) ? $postedAdvValues[$advId] : $advance['shared'];
//                }
//                $finance_incomes_reason->set('customer_advances', $customer_advances, true);
//            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
            $this->registry['include_gt2'] = true;
            //use ajax
            require_once PH_MODULES_DIR . 'finance/viewers/finance.incomes_reasons.advances.viewer.php';
            $viewer = new Finance_Incomes_Reasons_Advances_Viewer($this->registry);
            $viewer->prepare();
            //main ajax content
            $result['content'] = $viewer->fetch();
            //prepare messages
            if ($items = $this->registry['messages']->getErrors()) {
                $display = 'error';
            } elseif ($items = $this->registry['messages']->getMessages()) {
                $display = 'message';
            }
            if (!empty($items)) {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'] = $items;
                $financeViewer->data['display'] = $display;
                $result['errors'] = $financeViewer->fetch();
            }
            //print json encoded result of operation
            print json_encode($result);
            exit;
        }

        if (!empty($finance_incomes_reason)) {
            //get customer advances (RELATED AND AVAILABLE)
            $finance_incomes_reason->getAllCustomerAdvances();
            $advances = $finance_incomes_reason->get('customer_advances');
            $checked = false;
            //remove advances with different VAT rate
            foreach ($advances as $key => $adv) {
                if (!$adv['checked']) {
                    if ($adv['total_vat_rate'] != $finance_incomes_reason->get('total_vat_rate')) {
                        unset($advances[$key]);
                    }
                } else {
                    $checked = true;
                }
            }
            //checked = false if we don't have any advances related to the reason
            //in this case we will show all the advances
            if ($checked) {
                $finance_incomes_reason->set('customer_advances', $advances, true);
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
            $this->registry['include_gt2'] = true;
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add advance proforma invoices
     */
    private function _proformaAdvances() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fir.id = ' . $id,
                                          'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        if (empty($finance_incomes_reason)) {
            $this->redirect($this->module, 'list');
        }
        $this->checkAccessOwnership($finance_incomes_reason);

        if ($request->get('use_ajax') == 1) {
            if ($request->get('advances_action') == 'add') {
                //add new proforma advance
                $advance = Finance_Incomes_Reasons::buildModel($this->registry);

                $old_model = new Finance_Incomes_Reason($this->registry);
                $old_model->set('type', $finance_incomes_reason->get('type'), true);
                $this->registry->set('get_old_vars', true, true);
                $old_model->getGT2Vars();
                $old_model->sanitize();
                $this->registry->set('get_old_vars', false, true);

                // check total of current advance proforma invoice
                // (it should not exceed non-invoiced amount of reason)
                $advanced_total = $finance_incomes_reason->getAdvancedTotal() + $advance->get('total');
                if ($advanced_total > $finance_incomes_reason->get('total')) {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_advance_proforma_failed'), '', -1);
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_advanced_proforma_total',
                                                          array($advanced_total, $finance_incomes_reason->get('total'))));
                    $this->registry->set('new_incomes_reason', $advance->sanitize(), true);
                } elseif ($advance->save()) {
                    $filters = array('where' => array('fir.id = ' . $advance->get('id'),
                                                      'fir.annulled_by = 0'));
                    $advance = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    $new_id = $advance->get('id');
                    $advance->set('id', $finance_incomes_reason->get('id'), true);
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'addproformainvoice',
                                                                                    'new_model' => $advance,
                                                                                    'old_model' => $finance_incomes_reason));

                    $advance->set('id', $new_id, true);
                    $this->registry->set('get_old_vars', true, true);
                    $advance->getGT2Vars();
                    $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                              array('action_type' => 'add',
                                                                                    'new_model' => $advance,
                                                                                    'old_model' => $old_model
                                                                              ));

                    if ($request->get('finance_after_action') == 'payment') {
                        $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                                  array('action_type' => 'addpayment',
                                                                                        'new_model' => $advance,
                                                                                        'old_model' => $advance
                                                                                  ));
                    }

                    //add new advance proforma to reason
                    $adv_id = $advance->get('id');
                    $new_advances = array(
                        $adv_id => array(
                            'id' => $adv_id,
                            'value' => $advance->get('total'),
                        )
                    );
                    $finance_incomes_reason->set('new_advances', $new_advances, true);
                    $finance_incomes_reason->updateAdvances(false, PH_FINANCE_TYPE_PRO_INVOICE);

                    //update advance proforma payments if there are payments for parent reason
                    //IMPORTANT: !!!!!!!
                    //$advance->updatePaymentsFromParent();

                    if ($this->registry['messages']->getErrors()) {
                        $this->registry->set('new_incomes_reason', $advance->sanitize(), true);
                    } else {
                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_add_advance_proforma_success'), '', -2);
                    }
                } else {
                    $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_add_advance_proforma_failed'), '', -1);
                    $this->registry->set('new_incomes_reason', $advance->sanitize(), true);
                    if (!$advance->get('issue_date')) {
                        $advance->set('issue_date', General::strftime('%Y-%m-%d'), true);
                    }
                }
            }

            //get customer proforma advances
            $finance_incomes_reason->getCustomerProformaAdvances(true);
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
            $this->registry['include_gt2'] = true;
            //use ajax
            require_once PH_MODULES_DIR . 'finance/viewers/finance.incomes_reasons.proformaadvances.viewer.php';
            $viewer = new Finance_Incomes_Reasons_ProformaAdvances_Viewer($this->registry);
            $viewer->prepare();
            //main ajax content
            $result['content'] = $viewer->fetch();
            //prepare messages
            if ($items = $this->registry['messages']->getErrors()) {
                $display = 'error';
            } elseif ($items = $this->registry['messages']->getMessages()) {
                $display = 'message';
            }
            if (!empty($items)) {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'] = $items;
                $financeViewer->data['display'] = $display;
                $result['errors'] = $financeViewer->fetch();
            }
            //print json encoded result of operation
            print json_encode($result);
            exit;
        }

        if (!empty($finance_incomes_reason)) {
            //get customer proforma advances
            $finance_incomes_reason->getCustomerProformaAdvances(true);
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
            $this->registry['include_gt2'] = true;
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * The fake invoice GT2 content used for printing
     * IMPORTANT: Used only for normal (not advanced) invoices and proforma invoices!!!
     */
    private function _printForm() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and posted values for the GT2
            $filters = array('where' => array ('fir.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars('print');

            $finance_incomes_reason = clone $old_model;
            //get the original total from the invoice and set it to a new variable
            $finance_incomes_reason->set('invoice_total', $finance_incomes_reason->get('total'), true);
            $finance_incomes_reason->set('invoice_total_with_vat', $finance_incomes_reason->get('total_with_vat'), true);
            $this->registry->set('get_old_vars', false, true);
            $this->registry->set('calculate_GT2', true, true);
            $finance_incomes_reason->getGT2Vars();

            if ($finance_incomes_reason->validateInvoice() && $finance_incomes_reason->saveGT2Vars(false, false, true)) {
                //save history and audit
                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'printform',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_printform_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;

            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_printform_failed'), '', -2);
                //ToDo - set non editable properties as currency, amount ...
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id);
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            if (!$this->actionCompleted) {
                $this->checkAccessOwnership($finance_incomes_reason);

                if (!$finance_incomes_reason->isDefined('invoice_total')) {
                    //get the original total from the invoice and set it to a new variable
                    $finance_incomes_reason->set('invoice_total', $finance_incomes_reason->get('total'), true);
                    $finance_incomes_reason->set('invoice_total_with_vat', $finance_incomes_reason->get('total_with_vat'), true);
                }

                //get the ids of nomenclatures advances
                require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
                $filters_advances = array('where' => array('n.subtype = "advance"'));
                $advances_ids = Nomenclatures::getIds($this->registry, $filters_advances);

                //get the GT2 from the database
                $this->registry->set('get_old_vars', false, true);
                $this->registry->set('calculate_GT2', true, true);
                $gt2 = $finance_incomes_reason->getGT2Vars('print');

                $advances = false;
                if (!empty($advances_ids)) {
                    //check if at least one of the invoice rows is for advance
                    $gt2_original = $finance_incomes_reason->getGT2Vars();
                    foreach ($gt2_original['values'] as $row_id => $data) {
                        if (in_array($data['article_id'], $advances_ids)) {
                            $advances = true;
                            break;
                        }
                    }

                    //check if advances have been added to this invoice
                    //if added we should prevent displaying the advances twice
                    //that's why we need this flag $advances_added
                    $advances_added = false;
                    if (count($gt2['values']) > 1) {
                        foreach ($gt2['values'] as $row_id => $data) {
                            if (in_array($data['article_id'], $advances_ids)) {
                                $advances_added = true;
                                break;
                            }
                        }
                    }
                }

                //process the rows with advances depending on whether they were added or not
                if ($advances) {

                    //according to Bug 3492, allow users to change the article description
                    $gt2['fields_not_readonly'] = array('article_description');

                    if ($request->isPost() || $advances_added) {
                        //unset post advance rows (prevent displaying the advances twice - they will appear at the bottom of the table)
                        $row_index = 0;
                        foreach ($gt2['values'] as $key => $val) {
                            if (isset($val['article_id']) && in_array($val['article_id'], $advances_ids)) {
                                if ($advances_added) {
                                    //the printform has been already saved with advances

                                    if (empty($gt2['rows_readonly'])) {
                                        //add delimiter
                                        $gt2['last_editable_row_index'] = $row_index;

                                        //mark the advances rows with readonly classname
                                        $gt2['rows_readonly'][] = ++$row_index;
                                    } else {
                                        //mark the advances rows with readonly classname
                                        $gt2['rows_readonly'][] = $row_index;
                                    }
                                    $gt2['values'][] = $gt2['values'][$key];
                                }
                                //remove the ids of the GT2 rows, because new rows could be added
                                //once adding a row above the delimiter the rows below the delimiter overwrite the newly added rows
                                //that's why we need rows without indexes []
                                unset($gt2['values'][$key]);
                            }
                            $row_index++;
                        }
                    }

                    if (!$advances_added) {
                        //the printform has NOT been saved with advances yet

                        //add delimiter
                        $gt2['last_editable_row_index'] = count($gt2['values']);

                        $this->registry->set('get_old_vars', true, true);
                        $prec = $this->registry['config']->getSectionParams('precision');

                        foreach ($gt2_original['values'] as $row_id => $data) {
                            if (in_array($data['article_id'], $advances_ids)) {
                                //format some of the fields with float values
                                $data['price'] = sprintf("%." . $prec['gt2_rows'] . "F", $data['price']);
                                $data['price_with_discount'] = sprintf("%." . $prec['gt2_rows'] . "F", $data['price_with_discount']);
                                $data['subtotal'] = sprintf("%." . $prec['gt2_rows'] . "F", $data['subtotal']);
                                $data['subtotal_discount_value'] = sprintf("%." . $prec['gt2_rows'] . "F", $data['subtotal_discount_value']);
                                $data['subtotal_with_discount'] = sprintf("%." . $prec['gt2_rows'] . "F", $data['subtotal_with_discount']);
                                $gt2['values'][] = $data;
                                //set readonly advance rows
                                $gt2['rows_readonly'][] = count($gt2['values']);
                            }
                        }
                        $this->registry->set('get_old_vars', true, true);
                    }
                }
                //set readonly fields - currency
                $gt2['plain_vars']['currency']['readonly'] = 1;

                //set the autocompleters to ignore the old values
                $gt2['vars']['article_name']['exclude_oldvalues'] = 1;
                $gt2['vars']['article_deliverer_name']['exclude_oldvalues'] = 1;
                $gt2['vars']['article_alternative_deliverer_name']['exclude_oldvalues'] = 1;

                //some of the fields should BE visible and NOT be readonly
                $gt2['vars']['article_name']['readonly'] = 0;
                $gt2['vars']['article_name']['hidden'] = 0;
                $gt2['vars']['price']['hidden'] = 0;
                $gt2['vars']['price']['readonly'] = 0;
                $gt2['vars']['price_with_discount']['hidden'] = 1;
                $gt2['vars']['quantity']['readonly'] = 0;
                $gt2['vars']['quantity']['hidden'] = 0;
                $gt2['plain_vars']['total_vat_rate']['readonly'] = 1;
                $gt2['plain_vars']['currency']['readonly'] = 1;

                $finance_incomes_reason->set('grouping_table_2', $gt2, true);

                $this->registry['include_gt2'] = true;
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $id, 'fir.annulled_by = 0'),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);
            $finance_incomes_reason->set('id', $id, true);
            //the advance type is needed to define the VAT settings correctly
            $finance_incomes_reason->set('advance', $old_model->get('advance'), true);

            //the customer is necessary because the customer name and address should be translated as well
            $finance_incomes_reason->set('customer', $old_model->get('customer'), true);
            $this->registry->set('get_old_vars', false, true);

            if ($finance_incomes_reason->save()) {
                $this->registry->set('get_old_vars', true, true);
                $filters = array('where' => array('fir.id = ' . $id, 'fir.annulled_by = 0'),
                                 'model_lang' => $request->get('model_lang'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $finance_incomes_reason->getGT2Vars();

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'translate',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_translate_success',
                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_translate_failed',
                                                      array($finance_incomes_reason->getModelTypeName())), '', -2);
                //register the model, with all the posted details
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fir.id = ' . $id, 'fir.annulled_by = 0'),
                             'model_lang' => $request->get('model_lang'));
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * History of the reason
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'fir.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($finance_incomes_reason && $this->checkAccessOwnership($finance_incomes_reason, false)) {
                if (!$this->registry->isRegistered('finance_incomes_reason')) {
                    $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
                }

                require_once $this->viewersDir . 'finance.incomes_reasons.history.viewer.php';
                $viewer = new Finance_Incomes_Reasons_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Communication concerning the incomes reason (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fir.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * repayment filter
     */
    private function _repaymentFilter() {
        $this->viewer = $this->getViewer();

        $this->viewer->setFrameset('frameset_pop.html');

        return true;
    }

    /**
     * repayment filter
     */
    private function _repaymentData() {
        $this->viewer = $this->getViewer();

        $this->viewer->setFrameset('frameset_blank.html');

        return true;
    }

    /**
     * Check access and ownership of the current user to a certain model object.
     * Also performs action-specific additional checks.
     *
     * (non-PHPdoc)
     * @see Controller::checkAccessOwnership()
     */
    public function checkAccessOwnership($model, $redirect = true, $action = '') {
        $result = parent::checkAccessOwnership($model);

        if ($result) {
            if (is_object($model) && $this->action == 'edit' && $model->get('status') != 'opened') {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }
            if (is_object($model) && $this->action == 'payments' &&
                ($model->get('status') != 'finished'
                || in_array($model->get('payment_status'), array('nopay', 'invoiced'))
                || $model->get('type') == PH_FINANCE_TYPE_CORRECT_REASON)) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }
            if (is_object($model) && $this->action == 'addpayment' &&
                ($model->get('status') != 'finished'
                || in_array($model->get('payment_status'), array('nopay', 'paid', 'invoiced'))
                || $model->get('type') == PH_FINANCE_TYPE_CORRECT_REASON
                || !$model->checkAddPaymentPermissions()
                || !$this->checkActionPermissions('finance_payments', 'add'))) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }
            if (is_object($model) && $this->action == 'distribute' && ($model->get('status') != 'finished' ||
            $model->get('payment_status') == 'nopay' || $model->get('distributed') == PH_FINANCE_DISTRIBUTION_NONE ||
            !(in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE,
                                                  PH_FINANCE_TYPE_CREDIT_NOTICE,
                                                  PH_FINANCE_TYPE_DEBIT_NOTICE)) ||
            $model->get('type') > PH_FINANCE_TYPE_MAX))) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }

            if (is_object($model) && $this->action == 'printform' &&
            !($model->get('status') == 'finished' && !$model->get('annulled_by') && $model->get('payment_status') != 'nopay' &&
            !$model->get('advance') && in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE)))) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
            $model_type = $model->get('type');
            if ($model->get('advance') && $this->action == 'add') {
                // get reason type
                $model_type = $model->get('advance');
            }
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $model_type),
                                                                    'sanitize' => true));
            $rights = $this->registry['currentUser']->get('rights');
            if (($this->action == 'addinvoice' || $this->action == 'addproformainvoice') && is_object($model)) {
                $invoice_type = $this->action == 'addinvoice' ? PH_FINANCE_TYPE_INVOICE : PH_FINANCE_TYPE_PRO_INVOICE;
                $add_invoice_right = isset($rights['finance_incomes_reasons' . $invoice_type]['add']) ?
                                     $rights['finance_incomes_reasons' . $invoice_type]['add'] :
                                     'all';
                $add_property = $this->action == 'addinvoice'? 'add_invoice' : 'add_proforma';
                if ($financeType->get('id') > PH_FINANCE_TYPE_MAX && !$financeType->get($add_property) ||
                !$this->registry['request']->isPost() && !$model->checkAddingInvoice(true) ||
                $model->get('payment_status') == 'nopay' || $add_invoice_right == 'none') {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif (($this->action == 'advances' || $this->action == 'proformaadvances') && is_object($model)) {
                $invoice_type = $this->action == 'advances' ? PH_FINANCE_TYPE_INVOICE : PH_FINANCE_TYPE_PRO_INVOICE;
                $add_invoice_right = isset($rights['finance_incomes_reasons' . $invoice_type]['add']) ?
                                     $rights['finance_incomes_reasons' . $invoice_type]['add'] :
                                     'all';
                $add_property = $this->action == 'advances'? 'add_invoice' : 'add_proforma';
                if (!$financeType->get($add_property) || $model->get('type') <= PH_FINANCE_TYPE_MAX ||
                $model->get('payment_status') == 'nopay' || $add_invoice_right == 'none' || !$model->checkAddingAdvance()) {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'add' && is_object($model) && $model->get('advance') &&
            in_array($model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE))) {
                $add_property = $model->get('type') == PH_FINANCE_TYPE_INVOICE? 'add_invoice' : 'add_proforma';
                if (!$financeType->get($add_property)) {
                    if ($redirect) {
                        $this->redirect('finance', 'list');
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'addcorrect') {
                if (!$financeType->get('credit') || $model->get('payment_status') == 'nopay') {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'addhandover' && !$this->actionCompleted) {
                $direction = $this->registry['request']->get('handover_direction');
                if ($model->get('type') <= PH_FINANCE_TYPE_MAX || $financeType->get('commodity') == 'none' || !$model->checkAddingHandover($direction, true) || $model->get('payment_status') == 'nopay') {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'commodities_reservation') {
                if ($model->get('type') <= PH_FINANCE_TYPE_MAX || $financeType->get('commodity') == 'none' || $financeType->get('commodity') == 'incoming'
                  || !$model->checkCommoditiesReservation(true) || $model->get('payment_status') == 'nopay') {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'addcreditdebit' && is_object($model)) {
                $err = false;
                if ($model->get('type') != PH_FINANCE_TYPE_INVOICE || $model->get('payment_status') == 'nopay') {
                    $err = true;
                } else {
                    $model->getRelatives(array('get_reason' => true));
                    $reason = $model->get('reason');
                    // if invoice has no reason ("free" advance invoice) OR
                    // if the invoice is issued for a contract
                    //debit/credits are not allowed directly for invoices issued for a contract (Bug: 2493)
                    //IMPORTANT !!! Temporary allow credit/debit notices to be allowed for invoices from a contract
                    //TODO: When we rewite the part for annexes/TA this has to be restored again
                    if (!$reason ||
                    // OR if advance invoice issued from reason AND
                    // (no more advances can be added to reason i.e. final invoice has been issued)
                    $model->get('advance') && $reason->modelName == 'Finance_Incomes_Reason' && !$reason->checkAddingAdvance() ||
                    // OR if advanced invoice issued from Contract
                    $reason->modelName == 'Contract' && $model->get('advance')) {
                        $err = true;
                    }
                    unset($reason);
                    $model->unsetProperty('reason', true);
                }
                if ($err) {
                    if ($redirect) {
                        $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                    } else {
                        return false;
                    }
                }
            } elseif ($this->action == 'annul' && is_object($model) && !$model->allowAnnul()) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            } elseif ($this->action == 'update_customer_data' && is_object($model) &&
            ($model->get('status') != 'finished' ||
            !(in_array($model->get('type'),
                array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE)) ||
                $model->get('type') == PH_FINANCE_TYPE_PRO_INVOICE && $model->get('payment_status') != 'invoiced'
            ))) {
                if ($redirect) {
                    $this->redirect('finance', 'view', 'view=' . $model->get('id'));
                } else {
                    return false;
                }
            }

            return true;
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $this->settings_assign = array();
        if ($this->model && $this->model->get('type')) {
            $this->settings_assign = $this->registry['config']->getParamAsArray('finance', 'assignment_types_' . $this->model->get('type'));
        }

        $actions = parent::getActions($action_defs);

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        if ($this->model && $this->model->get('type')) {
            $financeType = Finance_Documents_Types::searchOne($this->registry,
                                                              array('where' => array('fdt.id=' . $this->model->get('type'))));
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['edit']) && ($this->model->get('status') != 'opened' || $this->model->get('annulled_by'))) {
                unset($actions['edit']);
            }
        }

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        if (isset($actions['add']) || isset($actions['printlist'])) {
            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                if ($this->registry['request']->get('advance')) {
                    $type_id = $this->registry['request']->get('advance');
                } else {
                    $type_id = preg_replace('/^((proforma_)?advance_)?/', '', $this->registry['request']->get('type'));
                }
                $customize = 'fdt.id="' . $type_id . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'fdt.type_section="' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_finance_incomes_reason')) {
                $custom_filters = $this->registry['session']->get($this->action . '_finance_incomes_reason');
            }

            if (!empty($custom_filters)) {
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fir.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'fdt.id="' . $custom_filters['values'][$key] . '"';
                            $found++;
                        }
                    }
                }
            }
        }

        if (isset($actions['add'])) {
            $filters = array('where' => array('fdt.model = "Finance_Incomes_Reason"',
                                              'fdt.id > ' . PH_FINANCE_TYPE_MAX,
                                              'fdt.active=1'),
                             'sort' => array('fdti18n.name ASC'),
                             'sanitize' => true);

            // if there is a model, the only available type for add
            // will be the options for the current type or the current section
            if ($found == 1 && $customize) {
                $filters['where'][] = $customize;
            }
            $financeTypes = Finance_Documents_Types::search($this->registry, $filters);
            $options = array();
            $advance_options = array();
            $proforma_advance_options = array();
            foreach ($financeTypes as $type) {
                // check permissions by type
                if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                    $options[] = array('label' => $type->get('name'),
                                       'option_value' => $type->get('id'));
                } elseif ($this->model && $this->model->get('id') && $this->model->get('type') == $type->get('id')) {
                    //if the user cannot add model, he should not be able to clone models of this type
                    unset($actions['clone']);
                }
                // only if type could add invoice
                if ($type->get('add_invoice')) {
                    // check permissions by type to add invoice/proforma invoice
                    if ($this->checkActionPermissions($module_check . PH_FINANCE_TYPE_INVOICE, 'add')) {
                        $advance_options[] =
                            array('label' => $this->i18n('finance_incomes_reasons_advance') . ' (' . $type->get('name') . ')',
                                  'option_value' => 'advance_' . $type->get('id'));
                    }
                }
                // only if type could add proforma invoice
                if ($type->get('add_proforma')) {
                    if ($this->checkActionPermissions($module_check . PH_FINANCE_TYPE_PRO_INVOICE, 'add')) {
                        $proforma_advance_options[] =
                            array('label' => $this->i18n('finance_incomes_reasons_proforma_advance') . ' (' . $type->get('name') . ')',
                                  'option_value' => 'proforma_advance_' . $type->get('id'));
                    }
                }
            }
            $options = array_merge($options, $advance_options, $proforma_advance_options);

            //prepare companies
            $customize_company = '';
            $found_company = 0;
            if (!empty($custom_filters)) {
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#fir.company#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize_company = 'fc.id="' . $custom_filters['values'][$key] . '"';
                            $found_company++;
                        }
                    }
                }
            }
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters = array('lang' => $this->registry['lang'],
                             'sanitize' => true,
                             'where' => array('fc.active=1'));
            // if there one company has been searched, the only available company to add document for
            // will be the options for the current type or the current section
            if ($found_company == 1 && $customize_company) {
                $filters['where'][] = $customize_company;
            }
            $companies_list = Finance_Companies::search($this->registry, $filters);
            $default_company = array();
            $default_company_id = '';
            $companies = array();

            foreach ($companies_list as $key => $company) {
                if ($this->registry['currentUser']->get('default_company') == $company->get('id')) {
                    $default_company_id = $company->get('id');
                    $default_company = array('label' => $company->get('name'),
                                             'option_value' => $company->get('id'));
                } else {
                    $companies[] = array('label' => $company->get('name'),
                                         'option_value' => $company->get('id'));
                }
            }

            if ($default_company) {
                array_unshift($companies, $default_company);
            }

            if (!empty($options)) {
                $add_options = array (
                    array (
                        'custom_id' => 'type_',
                        'name' => 'type',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => true,
                        'label' => $this->i18n('finance_documents_type'),
                        'help' => $this->i18n('finance_documents_type'),
                        'options' => $options,
                        'value' => ($this->registry['request']->get('type') && $options[0]['option_value'] == $this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                    array (
                        'custom_id' => 'company_',
                        'name' => 'company',
                        'type' => 'dropdown',
                        'required' => 1,
                        'really_required' => true,
                        'label' => $this->i18n('finance_incomes_reasons_company'),
                        'help' => $this->i18n('finance_incomes_reasons_company'),
                        'options' => $companies,
                        'value' => ($this->registry['request']->get('company')) ?
                                    $this->registry['request']->get('company') :
                                    $default_company_id),
                );
                $actions['add']['options'] = $add_options;

                // check if there is only one type and company and if so
                //  disables the pre-add screen
                $selected_type = '';
                $selected_company = '';
                if (count($options)==1 && count($companies)==1) {
                    $first_type = reset($options);
                    $selected_type = $first_type['option_value'];

                    $first_company = reset($companies);
                    $selected_company = $first_company['option_value'];
                }

                // set options if there is only one type available
                if ($selected_type && $selected_company) {
                    $actions['add']['url'] .= '&amp;type=' . $selected_type . '&amp;company=' . $selected_company;
                    $actions['add']['options'] = '';
                } else {
                    $actions['add']['ajax_no'] = 1;
                }
            } else {
                unset($actions['add']);
            }
        } else {
            unset($actions['add']);
            //IMPORTANT: the clone action is dependent on the add action,
            //because cloning is actually adding new record
            unset($actions['clone']);
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['assign']) && !empty($this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['assign']['options'] = 0;
        } else {
            unset($actions['assign']);
        }
        if (isset($actions['observer']) && in_array('observer', $this->settings_assign) && !$this->model->get('annulled_by')) {
            $actions['observer']['options'] = 0;
            if (Finance_Incomes_Reasons::checkObserver($this->registry, $this->model)) {
                $actions['observer']['img'] = 'stopobserve';
                $actions['observer']['label'] = $this->i18n('stopobserve');
            } else {
                $actions['observer']['img'] = 'observe';
                $actions['observer']['label'] = $this->i18n('observe');
            }
        } else {
            unset($actions['observer']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }

            // If the model have print form
            if ($this->model->get('for_printform')) {
                // Get only patterns that are for print form
                $filters['where'][] = 'p.for_printform = 1';
            } else {
                // Get only patterns that are NOT for print form
                $filters['where'][] = 'p.for_printform = 0';
            }

            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }

            $patterns = Patterns::search($this->registry, $filters);

            if (empty($patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                $actions['generate']['options'] = 1;
            }
        }

        if (isset($actions['print'])) {
            $patterns_options = array();
            if ($this->model->get('type')) {
                $filters_type = array(
                    'where' => array(
                        'fdt.id = ' . $this->model->get('type'),
                        'fdt.active = 1'
                    ),
                    'model_lang' => $this->getModelLang(),
                    'sanitize' => true
                );
                $document_type = Finance_Documents_Types::searchOne($this->registry, $filters_type);

                //get the id of the default document type print template
                $default_pattern_id = 0;
                if ($document_type && $document_type->get('default_pattern')) {
                    $default_pattern_id = $document_type->get('default_pattern');
                }

                //get all generate/print patterns for this type
                require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                $filters_patterns = array('where' => array(
                                                  'p.model = \'' . $this->modelName . '\'',
                                                  'p.model_type = \'' . $this->model->get('type') . '\'',
                                                  'p.active = 1',
                                                  'p.list = 0'),
                                          'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                                          'model_lang' => $this->getModelLang(),
                                          'sanitize' => true);

                if ($this->registry['currentUser']->get('is_portal')) {
                    $filters_patterns['where'][] = 'p.is_portal = 1';
                }

                // If the model have print form
                if ($this->model->get('for_printform')) {
                    // Get only patterns that are for print form
                    $filters_patterns['where'][] = 'p.for_printform = 1';
                } else {
                    // Get only patterns that are NOT for print form
                    $filters_patterns['where'][] = 'p.for_printform = 0';
                }

                // If there is a company (usually there is because it is a required field)
                if ($this->model->get('company')) {
                    // Add filter: get patterns only if they are for this company or they are not for a specific company
                    $filters_patterns['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
                }

                $patterns = Patterns::search($this->registry, $filters_patterns);

                $available_patterns = array();
                foreach ($patterns as $pattern) {
                    $available_patterns[] = $pattern->get('id');
                    $patterns_options[] = array(
                        'id'        => $pattern->get('id'),
                        'label'     => $pattern->get('name'),
                        'img'       => $pattern->getIcon(),
                        'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                        'target'    => '_blank',
                    );
                }
            }

            if (empty($patterns_options)) {
                unset($actions['print']);
            } else {
                if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                } elseif (count($available_patterns) == 1) {
                    // sets the first pattern in the list as default and assigns a link to the direct print
                    list($first_pattern) = $patterns_options;
                    $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                } else {
                    $actions['print']['url'] = '#';
                }
                $actions['print']['drop_menu'] = true;
                $actions['print']['no_tab'] = true;
                $actions['print']['label'] = '';
                $actions['print']['target'] = '_blank';
                $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['print']['img'] .= '_plus';
                }
                $actions['print']['options'] = $patterns_options;
            }
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments'])) {
                if (isset($actions['comments'])) {
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('finance_incomes_reasons_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
        }

        if (isset($actions['setstatus']) && $this->model && $this->model->get('id') && !$this->model->get('annulled_by')) {
            //prepare substatuses
            require_once PH_MODULES_DIR . 'finance/models/finance.documents_substatuses.factory.php';
            $filters = array('model_lang' => $this->model->get('model_lang'),
                             'where' => array('fdt.model = \'' . $this->model->modelName . '\'',
                                              'fdt.id = ' . $this->model->get('type'),
                                              'fds.active IS NOT NULL'));
            $substatuses = Finance_Documents_Substatuses::search($this->registry, $filters);
            $this->model->set('substatuses', $substatuses, true);

            $actions['setstatus']['options'] = array('label' => $this->i18n('finance_status_btn'), 'form_method' => 'post');
            $actions['setstatus']['ajax_no'] = 1;
            $actions['setstatus']['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
            $actions['setstatus']['template'] = PH_MODULES_DIR . 'finance/templates/' . '_action_status.html';
            $actions['setstatus']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['setstatus']);
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        if (isset($actions['receive_date']) && $this->model && $this->model->get('id') &&
        $this->model->get('type') == PH_FINANCE_TYPE_INVOICE && $this->model->get('status') == 'finished' &&
        !$this->model->get('annulled_by') && !$this->model->get('date_of_receive')) {
            $actions['receive_date']['options'] = 1;
            $actions['receive_date']['ajax_no'] = 1;
            $actions['receive_date']['template'] = PH_MODULES_DIR . 'finance/templates/' . '_action_receive_date.html';
            $actions['receive_date']['label'] = $this->i18n('finance_receive_date');
            $actions['receive_date']['img'] = 'day';
            $actions['receive_date']['model_id'] = $this->model->get('id');
            $actions['receive_date']['date_of_receive'] = $this->model->get('date_of_receive');
            $actions['receive_date']['issue_date'] = $this->model->get('issue_date');
        } else {
            unset($actions['receive_date']);
        }

        // !!! IMPORTANT !!!: If these conditions are changed, then update the conditions here too: Finance_Documents_Type->updateRoleDefinitions()
        if (isset($actions['annul']) && $this->model && $this->model->get('id') && $this->model->allowAnnul()) {
            if ($this->model->checkIssueAnnulmentReport()) {
                $annul_options = array(
                    array(
                        'custom_id' => 'annul_description',
                        'name' => 'annul_description',
                        'type' => 'textarea',
                        'required' => 1,
                        'label' => $this->i18n('finance_annul_description'),
                        'help' => $this->i18n('finance_annul_description'),
                        'value' => ''
                    ),
                    array (
                        'custom_id' => 'annul_fiscal_event_date',
                        'name' => 'annul_fiscal_event_date',
                        'type' => 'date',
                        'required' => 0,
                        'label' => $this->i18n('finance_incomes_reasons_fiscal_event_date'),
                        'help' => $this->i18n('finance_incomes_reasons_fiscal_event_date'),
                        'width' => 200,
                        'show_calendar_icon' => true,
                        'value' => General::strftime($this->i18n('date_iso_short'))
                    )
                );

                $actions['annul']['options'] = $annul_options;
                $actions['annul']['ajax_no'] = 1;
                $actions['annul']['template'] = PH_MODULES_DIR . 'finance/templates/' . '_action_annul.html';
                $actions['annul']['model_id'] = $this->model->get('id');
                if ($this->model->get('type') == PH_FINANCE_TYPE_INVOICE) {
                    $actions['annul']['confirm_text'] = sprintf($this->i18n('message_finance_incomes_reasons_confirm_annulment_invoice'), $this->model->get('num'));
                } elseif ($this->model->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                    $actions['annul']['confirm_text'] = sprintf($this->i18n('message_finance_incomes_reasons_confirm_annulment_credit_notice'), $this->model->get('num'));
                } elseif ($this->model->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                    $actions['annul']['confirm_text'] = sprintf($this->i18n('message_finance_incomes_reasons_confirm_annulment_debit_notice'), $this->model->get('num'));
                }
            } else {
                $actions['annul']['confirm'] = 1;
                if ($this->model->get('type') == PH_FINANCE_TYPE_INVOICE) {
                    $actions['annul']['confirm_label'] = 'annul_invoice';
                } elseif ($this->model->get('type') == PH_FINANCE_TYPE_PRO_INVOICE) {
                    $actions['annul']['confirm_label'] = 'annul_proforma_invoice';
                } elseif ($this->model->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE) {
                    $actions['annul']['confirm_label'] = 'annul_credit_notice';
                } elseif ($this->model->get('type') == PH_FINANCE_TYPE_DEBIT_NOTICE) {
                    $actions['annul']['confirm_label'] = 'annul_debit_notice';
                }
            }
        } else {
            unset($actions['annul']);
        }

        // !!! IMPORTANT !!!: If these conditions are changed, then update the conditions here too: Finance_Documents_Type->updateRoleDefinitions()
        if (isset($actions['payments']) && $this->model && $this->model->get('id') && $this->model->get('status') == 'finished' &&
        $this->model->get('type') != PH_FINANCE_TYPE_CORRECT_REASON &&
        !$this->model->get('annulled_by') && !in_array($this->model->get('payment_status'), array('nopay', 'invoiced'))) {
            if (in_array($this->model->get('type'), array(PH_FINANCE_TYPE_DEBIT_NOTICE, PH_FINANCE_TYPE_CREDIT_NOTICE))) {
                // check whether the parent invoice is in nopay status
                // if so do not allow the payments action
                $this->model->getRelatives(array('get_parent_reasons' => true));
                if ($this->model->get('parent_reasons')) {
                    foreach ($this->model->get('parent_reasons') as $parent_document) {
                        if ($parent_document->get('type') == PH_FINANCE_TYPE_INVOICE && $parent_document->get('payment_status') == 'nopay') {
                            //the invoice should not be marked with nopay payment status if any of its credit notices is partially or fully paid
                            unset($actions['payments']);
                            break;
                        }
                    }
                }
            }
        } else {
            unset($actions['payments']);
        }

        if (isset($actions['distribute']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        $this->model->get('distributed') != PH_FINANCE_DISTRIBUTION_NONE &&
        !in_array($this->model->get('type'), array(PH_FINANCE_TYPE_CORRECT_REASON, PH_FINANCE_TYPE_PRO_INVOICE))) {

        } else {
            unset($actions['distribute']);
        }

        // "printform" action is available only for invoices, proforma invoices, debit notice and credit notice
        if (isset($actions['printform']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        in_array($this->model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE)) &&
        !$this->model->get('advance')) {

        } else {
            unset($actions['printform']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^fdt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^fdt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        //sets the actions for the right and left submenu
        $_left_menu        = array();
        $_right_menu       = array();
        $_upper_right_menu = array();
        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions
        // for view and edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && !empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);

        return $actions;
    }

    /**
     * Sets custom actions definitions
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        //get permissions of the currently logged user
        $this->getUserPermissions();

        $actions = parent::getActions(array($action_name));

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);

            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }

            // If the model have print form
            if ($this->model->get('for_printform')) {
                // Get only patterns that are for print form
                $filters['where'][] = 'p.for_printform = 1';
            } else {
                // Get only patterns that are NOT for print form
                $filters['where'][] = 'p.for_printform = 0';
            }

            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }

            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                    'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                    'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern_',
                        'name' => 'pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
            }
        }

        return $actions;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _generate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('generate'));
        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_incomes_reason)) {
            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_incomes_reason->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_incomes_reason->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            } elseif (!$finance_incomes_reason->checkPrintPattern($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_generate_pattern_not_allowed'));
                $this->registry['messages']->insertInSession($this->registry);

                //print with this pattern is not allowed
                //(once printed with pattern containing "printform", printing with another pattern is forbidden)
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            //get all generated files for the selected pattern
            $files = $finance_incomes_reason->getGeneratedFiles(array('pattern_id' => $pattern_id));

            //get file details
            $revision_id = $request->get('revision');
            if ($revision_id && $files) {
                foreach ($files as $file) {
                    if ($file->get('id') == $revision_id) {
                        //prepare selected revision details
                        $finance_incomes_reason->set('revision', $file, true);
                        break;
                    }
                }
            }

            //get revision details
            if (!empty($files)) {
                $finance_incomes_reason->set('revisions', $files, true);
            }
        }

        if ($request->isPost()) {
            if (!empty($finance_incomes_reason)) {
                $patterns_vars = $finance_incomes_reason->getPatternsVars();
                $finance_incomes_reason->extender = new Extender();
                $finance_incomes_reason->extender->model_lang = $finance_incomes_reason->get('model_lang');
                $finance_incomes_reason->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $finance_incomes_reason->extender->add($key, $value);
                }
                switch ($request->get('format')) {
                    case 'xls':
                        // TO DO
                        break;
                    case 'csv':
                        // TO DO add option fields and also create a pattern which indicates the header columns
                        break;
                    case 'pdf':
                    default:
                    $this->old_model = clone $finance_incomes_reason;
                    if ($file_id = $finance_incomes_reason->generatePDF()) {
                        if ($request->get('submit_button') != 'preview') {
                            $this->registry['messages']->setMessage($this->i18n('message_finance_generate_success'), '', -1);
                            $this->registry['messages']->insertInSession($this->registry);

                            //save history
                            require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                            Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                      array('action_type' => 'generate',
                                                                            'model' => $finance_incomes_reason,
                                                                            'pattern' => $pattern->get('id'),
                                                                            'generated_file' => $file_id,
                                                                            'printform' => ((preg_match('#\[.._grouping_table_2_printform\]#', $pattern->get('content'))) ? 1 : 0)
                                                                      ));

                            $finance_incomes_reason->set('file_id', $file_id, true);
                            $this->actionCompleted = true;
                        }
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_finance_generate_failed'), '', -1);
                    }
                    break;
                }
            }
        }

        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            //register pattern to the registry
            if ($pattern) {
                $this->registry->set('pattern', $pattern->sanitize());
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('print'));
        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            //get the specified pattern
            $pattern_id = $request->get('pattern');
            if (empty($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //no pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $pattern_id,
                                              'p.model = \'' . $this->modelName . '\'',
                                              'p.model_type = \'' . $finance_incomes_reason->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0',
                                              '(p.company = \'' . $finance_incomes_reason->get('company') . '\' OR p.company = \'0\')'),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $pattern = Patterns::searchOne($this->registry, $filters);
            if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //invalid pattern specified, redirect to view mode
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            } elseif (!$finance_incomes_reason->checkPrintPattern($pattern_id)) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_not_allowed'));
                $this->registry['messages']->insertInSession($this->registry);

                //print with this pattern is not allowed
                //(once printed with pattern containing "printform", printing with another pattern is forbidden)
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            $patterns_vars = $finance_incomes_reason->getPatternsVars();
            $finance_incomes_reason->extender = new Extender();
            $finance_incomes_reason->extender->model_lang = $finance_incomes_reason->get('model_lang');
            $finance_incomes_reason->extender->module = $this->module;
            foreach ($patterns_vars as $key => $value) {
                $finance_incomes_reason->extender->add($key, $value);
            }

            if (!empty($pattern) && $pattern->get('force_generate')) {
                //generate and save file and get its id
                $this->old_model = clone $finance_incomes_reason;

                $result = false;
                if ($pattern->get('not_regenerate_finished_record') && $finance_incomes_reason->get('status') == 'finished') {
                    $previous_generated_file = $finance_incomes_reason->getLastGeneratedFile($pattern);

                    if ($previous_generated_file) {
                        $result = $previous_generated_file->get('id');
                    }
                }

                if (!$result) {
                    $result = $finance_incomes_reason->generatePDF();
                }

                if ($result) {
                    $finance_incomes_reason->set('file_id', $result, true);
                    if (!$this->registry->isRegistered('finance_incomes_reason')) {
                        $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
                    }

                    //save history
                    require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                    Finance_Incomes_Reasons_History::saveData($this->registry,
                                                              array('action_type' => 'print',
                                                                    'model' => $finance_incomes_reason,
                                                                    'pattern' => $pattern->get('id'),
                                                                    'generated_file' => $result,
                                                                    'printform' => ((preg_match('#\[.._grouping_table_2_printform\]#', $pattern->get('content'))) ? 1 : 0)
                                                              ));

                    // show the file
                    require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                    $file_id = $request->get('file');
                    $filters = array('where'    => array('f.id = ' . $result),
                                     'sanitize' => true);
                    $file = Files::searchOne($this->registry, $filters);

                    $file->viewFile();
                    exit;
                }
            } else {
                //save history
                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array('action_type' => 'print',
                                                                'model' => $finance_incomes_reason,
                                                                'pattern' => $pattern->get('id'),
                                                                'generated_file' => false,
                                                                'printform' => ((preg_match('#\[.._grouping_table_2_printform\]#', $pattern->get('content'))) ? 1 : 0)
                                                          ));

                //generate file to the browser window
                $result = $finance_incomes_reason->generatePDF('browser_mode');
            }

            if ($result) {
                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_printform_success'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;

                //the pdf content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_print_failed'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $finance_incomes_reason->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&incomes_reasons=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Finance_Incomes_Reasons::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $finance_incomes_reasons = array();
        if ($ids) {
            $filters = array('where' => array('fir.id IN (' . implode(', ', $ids) . ')',
                                              'fir.annulled_by = 0'),
                             'model_lang' => $this->registry->get('lang'),
                             'sanitize' => true);
            $finance_incomes_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);
        }

        //if no models or annulled checked
        if (empty($finance_incomes_reasons) || count($ids) != count($finance_incomes_reasons)) {
            $this->registry['messages']->setError($this->i18n('error_no_finance_incomes_reasons_or_annulled'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $finance_incomes_reasons[0]->get('type');
        foreach ($finance_incomes_reasons as $finance_incomes_reason) {
            $type_i = $finance_incomes_reason->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.id = ' . $type),
                         'sanitize' => true);
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $type_name_plural = $fin_doc_type && $fin_doc_type->get('name_plural') ? $fin_doc_type->get('name_plural') : $this->i18n('finance_incomes_reasons');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_finance_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        // Check if the models corresponds to the selected pattern
        // Check: for print form
        $pattern_for_pf = $pattern->get('for_printform');
        if (!Finance_Incomes_Reasons::checkPatternPrintForms($this->registry, $pattern_for_pf, $ids)) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            if ($pattern_for_pf) {
                $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_for_printform'));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_not_for_printform'));
            }
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }
        // Check: company
        $pattern_company = $pattern->get('company');
        if ($pattern_company && !Finance_Incomes_Reasons::checkModelsForCompany($this->registry, Finance_Incomes_Reasons::$modelName, $pattern_company, $ids)) {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $company_filters = array('sanitize'   => true,
                                     'model_lang' => $this->registry->get('lang'),
                                     'where'      => array('id = ' . $pattern_company));
            $company = Finance_Companies::searchOne($this->registry, $company_filters);
            $this->registry['messages']->setError($this->i18n('error_finance_print_pattern_companies', array($company->get('name'))));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Finance_Incomes_Reasons::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_multiprint_failed',
                                                  array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Attaches files to model
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('attachments');

        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $modified_files = array();
            $modified_genfiles = array();

            $erred_modified_files = array();
            $erred_modified_genfiles = array();
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($names[$idx])) {
                        $names[$idx] = $files['name'][$idx];
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_incomes_reason->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);
                    }
                    $finance_incomes_reason->unsanitize();
                    $modified_files[$idx] = $params;
                }
            }

            //edit existing generated files
            $generated_names        = $request->get('g_file_names');
            $generated_descriptions = $request->get('g_file_descriptions');
            $generated_permissions  = $request->get('g_file_permissions');
            $generated_revisions    = $request->get('g_file_revisions');
            $generated_indices      = $request->get('g_file_indices');

            if (!empty($generated_names)) {
                foreach ($generated_names as $idx => $name) {
                    $index = $generated_indices[$idx];

                    $file = array();
                    $params = array(
                        'id'          => $idx,
                        'name'        => $generated_names[$idx],
                        'description' => $generated_descriptions[$idx],
                        'permission'  => $generated_permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $finance_incomes_reason->sanitize())) {
                        $erred_modified_genfiles[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_gen_edit') . ' ' . $index, 'edit_gen_attachment_' . $idx);
                    }
                    $finance_incomes_reason->unsanitize();
                    $modified_genfiles[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files['name'])) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $finance_incomes_reason->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }
                    $finance_incomes_reason->unsanitize();
                    $added_files[$idx] = $params;
                }
            }

            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_attachments'));*/
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($modified_genfiles && empty($erred_modified_genfiles)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_gen_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'modified_gen'));*/
            } elseif (!empty($modified_genfiles)) {
                $this->registry['modified_genfiles'] = $modified_genfiles;
                $this->registry['erred_modified_genfiles'] = $erred_modified_genfiles;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                /*Documents_History::saveData($this->registry, array('model' => $document,
                                            'action_type' => 'add_attachments'));*/
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files) && empty($erred_modified_genfiles)) {
                $this->actionCompleted = true;
                if (!$this->registry->isRegistered('finance_incomes_reason')) {
                    $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
                }

                return true;
            }

        }

        $this->registry['added_files'] = $added_files;

        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            $finance_incomes_reason->getAttachments();
            $finance_incomes_reason->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated invoice file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'generate' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters = array('where' => array('f.id = ' . $request->get('file')),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                    }
                    break;
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $finance_incomes_reason->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Annul model
     */
    private function _annul() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $matches = array();
        if (isset($_SERVER['HTTP_REFERER'])) {
            //get referer's action
            preg_match('/&incomes_reasons=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $filters = array('where' => array('fir.id = ' . $request->get('id'),
                                          'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (empty($finance_incomes_reason)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->checkAccessOwnership($finance_incomes_reason);

        if ($request->get('annul_description')) {
            $finance_incomes_reason->set('annul_description', $request->get('annul_description'), true);
        }
        if ($request->get('annul_fiscal_event_date')) {
            $finance_incomes_reason->set('annul_fiscal_event_date', $request->get('annul_fiscal_event_date'), true);
        }
        $this->old_model = clone $finance_incomes_reason;
        $this->old_model->sanitize();

        if ($finance_incomes_reason->checkAnnul() && $finance_incomes_reason->annul()) {
            //show message 'message_finance_annul_success'
            $this->registry['messages']->setMessage($this->i18n('message_finance_annul_success',
                                                    array($finance_incomes_reason->getModelTypeName())), '', -2);

            $this->model = clone $finance_incomes_reason;
            $this->model->sanitize();

            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_annul_failed',
                                                  array($finance_incomes_reason->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action
        $this->registry['messages']->insertInSession($this->registry);

        $url = sprintf(
            '%s?%s=finance&%s=incomes_reasons&incomes_reasons=%s&%s=%d',
            $_SERVER['PHP_SELF'], $this->registry['module_param'],
            $this->registry['controller_param'], $after_action,
            $after_action, $finance_incomes_reason->get('id')
        );
        $this->registry->set('redirect_to_url', $url, true);
        $this->registry->set('exit_after', true, true);

        return true;
    }

    /**
     * Change status of model
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        if (empty($_SERVER['HTTP_REFERER'])) {
            $redirect_to_url = sprintf(
                '%s?%s=finance&%s=incomes_reasons&incomes_reasons=view&view=%d',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'],
                $this->registry['controller_param'],
                $id
            );
        } else {
            $redirect_to_url = $_SERVER['HTTP_REFERER'];
            preg_match('/&incomes_reasons=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }

        //get referer's action
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $filters = array('where' => array('fir.id = ' . $request->get('id'), 'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $old_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $finance_incomes_reason = clone $old_reason;
        $properties = $request->getAll();
        foreach ($properties as $key => $value) {
            $finance_incomes_reason->set($key, $value, true);
        }
        $old_reason->sanitize();
        $this->old_model = clone $old_reason;

        if ($finance_incomes_reason->setStatus()) {
            //show message 'message_finance_status_success'
            $this->registry['messages']->setMessage($this->i18n('message_finance_status_success',
                                                    array($old_reason->getModelTypeName())), '', -2);

            $comment = '';
            if ($request->get('comment')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::buildModel($this->registry);

                $comment->set('content', $request->get('comment'), true);
                $comment->set('subject', $this->i18n('finance_status_change_comment'), true);
                $comment->set('model', 'Finance_Incomes_Reason', true);
                $comment->set('model_id', $id, true);
                $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    //show corresponding message
                    $this->registry['messages']->setMessage($this->i18n('message_finance_comments_add_success'), '', -1);
                    //$this->registry['messages']->insertInSession($this->registry);

                    //the model was successfully saved set action as completed
                    //$this->actionCompleted = true;
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->registry['messages']->setError($this->i18n('error_comments_add_failed'), '', -1);
                }
            }

            $filters = array('where' => array('fir.id = ' . $request->get('id'),
                                              'fir.annulled_by = 0'),
                             'model_lang' => $request->get('model_lang'));
            $new_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $this->model = clone $new_reason;
            $this->model->sanitize();

            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
            $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                      array('action_type' => 'status',
                                                                            'new_model' => $new_reason,
                                                                            'old_model' => $old_reason));

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_reason);
            }

            //send notification
            //$this->sendStatusNotification($new_reason, $audit_parent);

            //set after action to view if have not permission
            if (!$new_reason->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_status_failed',
                                                  array($old_reason->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            //set parameters in registry - check them in router
            //set redirect url
            $this->registry->set('redirect_to_url', $redirect_to_url, true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
            //header("Location: " . $_SERVER['HTTP_REFERER']);exit;
        }

        return true;
    }

    /**
     * Status of models
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('model_id');
        $filters = array('where' => array('fir.id = ' . $request->get('id'),
                                          'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        //prepare substatuses
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_substatuses.factory.php';
        $filters = array('model_lang' => $finance_incomes_reason->get('model_lang'),
                         'where' => array('fds.doc_type = ' . $finance_incomes_reason->get('type'),
                                          'fds.active = 1'));
        $substatuses = Finance_Documents_Substatuses::search($this->registry, $filters);
        $finance_incomes_reason->set('substatuses', $substatuses, true);

        $setstatus['options'] = array('label' => $this->i18n('finance_status_btn'), 'form_method' => 'post');
        $setstatus['model_id'] = $finance_incomes_reason->get('id');
        $setstatus['module'] = 'finance';
        $setstatus['action'] = 'setstatus';
        $setstatus['module_param'] = $this->registry['module_param'];
        $setstatus['controller'] = 'incomes_reasons';
        $setstatus['controller_param'] = $this->registry['controller_param'];
        $setstatus['show_form'] = 1;
        $setstatus['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_action_status.html');
        $this->viewer->data['model'] = $finance_incomes_reason;
        $this->viewer->data['available_action'] = $setstatus;
        $this->viewer->data['hide_status_label'] = true;
        $this->viewer->display();
        exit;
    }

    /**
     * Change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('fir.id IN (' . implode(', ', $ids) . ')',
                                          'fir.annulled_by = 0'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $finance_incomes_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

        //if no documents or checked annulled documents
        if (empty($finance_incomes_reasons) || count($ids) != count($finance_incomes_reasons)) {
            $this->registry['messages']->setError($this->i18n('error_no_finance_incomes_reasons_or_annulled'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_document = $finance_incomes_reasons[0];

        $type = ($first_document->get('type'));
        foreach ($finance_incomes_reasons as $finance_incomes_reason) {
            $type_i = $finance_incomes_reason->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.id = ' . $type),
                         'sanitize' => true);
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $type_name_plural = $fin_doc_type && $fin_doc_type->get('name_plural') ? $fin_doc_type->get('name_plural') : $this->i18n('finance_incomes_reasons');

        $result = Finance_Incomes_Reasons::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $type_name_plural)), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_finance_change_status_not_all', array($type_name_plural)));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_multistatus_failed', array($type_name_plural)), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Finance_Incomes_Reasons::changeStatus($this->registry, $ids, $status);
        if ($result) {
            //change status successful
            if ($result > 0) {
                $this->registry['messages']->setMessage(
                    $this->i18n('message_items_' . ($result !== true ? 'some_' : '') .
                                ($status == 'activate' ? 'activated' : 'deactivated'),
                                array($result)));
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_finance_' . $status . '_not_all'));
            }
        } else {
            //change status failed
            $this->registry['messages']->setError(
                $this->i18n('error_items_not_' . ($status == 'activate' ? 'activated' : 'deactivated')));
        }
        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Relatives of the model
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $gt2_total_precision = $this->registry['config']->getParam('precision', 'gt2_total');

        if ($id) {
            $filters = array('where' => array('fir.id = ' . $id));
            if ($model_lang = $this->registry['request']->get('model_lang')) {
                $filters['model_lang'] = $model_lang;
            }
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }
        if (!empty($finance_incomes_reason)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($finance_incomes_reason);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }

            //get the relatives tree
            $this->registry->set('relatives_tree', $finance_incomes_reason->getRelativesTree());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Set relations to payment documents
     */
    private function _payments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        //search model
        $filters = array();
        $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
        $filters['model_lang'] = $request->get('model_lang');
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (!empty($finance_incomes_reason)) {
            $this->checkAccessOwnership($finance_incomes_reason);

            //get selected tab
            if (!$request->get('selected_tab')) {
                $selected_tab = 'payment';
            } else {
                $selected_tab = $request->get('selected_tab');
            }
            $finance_incomes_reason->set('selected_tab', $selected_tab, true);

            if ($request->get('use_ajax') == 1) {
                //ajax request

                $old_finance_incomes_reason = clone $finance_incomes_reason;
                $this->old_model = $old_finance_incomes_reason;
                //$old_finance_incomes_reason->getRelatives();

                if ($request->get('empty')) {
                    //remove paid amount from document
                    $parent_id = $request->get('empty');
                    if ($finance_incomes_reason->emptyPaidAmount($parent_id, $selected_tab)) {
                        //$finance_incomes_reason->getRelatives();
                        //ToDo - save history and audit
                        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                        $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                                  array('model' => $finance_incomes_reason,
                                                                                        'action_type' => 'empty',
                                                                                        'new_model' => $finance_incomes_reason,
                                                                                        'old_model' => $old_finance_incomes_reason));

                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_payment_success',
                                                                array($finance_incomes_reason->getModelTypeName())), '', -1);
                        $this->actionCompleted = true;
                    } else {
                        //some error occurred
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_payment_failed',
                                                              array($finance_incomes_reason->getModelTypeName())), '', -2);
                    }
                } elseif ($request->get('relatives_payments')) {
                    //save amounts to documents
                    $finance_incomes_reason->set('relatives_payments', $request->get('relatives_payments'), true);
                    $finance_incomes_reason->set('selected_tab', $selected_tab, true);
                    if ($finance_incomes_reason->updateFinanceRelatives($selected_tab)) {
                        //$finance_incomes_reason->getRelatives();
                        //ToDo - save history and audit
                        require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                        $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                                  array('model' => $finance_incomes_reason,
                                                                                        'action_type' => 'payments',
                                                                                        'new_model' => $finance_incomes_reason,
                                                                                        'old_model' => $old_finance_incomes_reason));

                        //show success message
                        $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_edit_payment_success',
                                                                array($finance_incomes_reason->getModelTypeName())), '', -1);
                        $this->actionCompleted = true;
                    } else {
                        //some error occurred
                        $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_edit_payment_failed',
                                                              array($finance_incomes_reason->getModelTypeName())), '', -2);
                    }
                }
            }

            if ($id) {
                //check status
                if ($finance_incomes_reason->get('status') != 'finished') {
                    $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
                }

                //prepare selected tab data
                if ($selected_tab == 'payment') {
                    $finance_incomes_reason->getCustomerPayments();
                } else {
                    $finance_incomes_reason->getPaymentsDistribution($selected_tab);
                }
            }

            $finance_incomes_reason->getRelatives(array('get_interest_accounts' => true));
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if ($request->get('use_ajax') == 1) {
            //use ajax
            require_once PH_MODULES_DIR . 'finance/viewers/finance.incomes_reasons.payments.viewer.php';
            $viewer = new Finance_Incomes_Reasons_Payments_Viewer($this->registry);
            $viewer->model = $finance_incomes_reason;
            $this->registry->set('finance_incomes_reason', $finance_incomes_reason, true);
            $viewer->prepare();
            //main ajax content
            $result['content'] = $viewer->fetch();
            //prepare messages
            if ($items = $this->registry['messages']->getErrors()) {
                $display = 'error';
            } elseif ($items = $this->registry['messages']->getMessages()) {
                $display = 'message';
            }
            if (!empty($items)) {
                $financeViewer = new Viewer($this->registry);
                $financeViewer->setFrameset('message.html');
                $financeViewer->data['items'] = $items;
                $financeViewer->data['display'] = $display;
                $result['errors'] = $financeViewer->fetch();
            }
            //print json encoded result of operation
            $this->registry->set('ajax_result', json_encode($result), true);
        }

        return true;
    }

    /**
     * Update receive date of model
     */
    private function _receiveDate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('fir.id = ' . $id,
                                          'fir.annulled_by = 0'),
                         'model_lang' => $request->get('model_lang'));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $finance_incomes_reason->set('date_of_receive', $request->get('date_of_receive'), true);

        if ($finance_incomes_reason->updateReceiveDate()) {
            $this->registry['messages']->setMessage($this->i18n('message_finance_receive_date_save_success'), '', -2);

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_finance_receive_date_save_failed'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);
        header("Location: " . $_SERVER['HTTP_REFERER']);
        exit;

        return true;
    }

    /**
     * Sets custom actions definitions
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $actions = parent::getAfterActions();

        //check for secondary controller
        $module_check = $this->module;
        if ($this->module != $this->controller) {
            $module_check .= '_' . $this->controller;
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $filters = array('where' => array('fdt.model = "Finance_Incomes_Reason"',
                                          'fdt.id > ' . PH_FINANCE_TYPE_MAX,
                                          'fdt.active=1'),
                         'sort' => array('fdti18n.name ASC'),
                         'sanitize' => true);
        $financeTypes = Finance_Documents_Types::search($this->registry, $filters);

        $options = array();
        $advance_options = array();
        $proforma_advance_options = array();

        foreach ($financeTypes as $type) {
            // check permissions by type
            if ($this->checkActionPermissions($module_check . $type->get('id'), 'add')) {
                $options[] = array('label' => $type->get('name'),
                                   'option_value' => $type->get('id'));
            }
            // only if type could add invoice
            if ($type->get('add_invoice')) {
                // check permissions by type to add invoice/proforma invoice
                if ($this->checkActionPermissions($module_check . PH_FINANCE_TYPE_INVOICE, 'add')) {
                    $advance_options[] =
                        array('label' => $this->i18n('finance_incomes_reasons_advance') . ' (' . $type->get('name') . ')',
                              'option_value' => 'advance_' . $type->get('id'));
                }
            }
            // only if type could add proforma invoice
            if ($type->get('add_proforma')) {
                if ($this->checkActionPermissions($module_check . PH_FINANCE_TYPE_PRO_INVOICE, 'add')) {
                    $proforma_advance_options[] =
                        array('label' => $this->i18n('finance_incomes_reasons_proforma_advance') . ' (' . $type->get('name') . ')',
                              'option_value' => 'proforma_advance_' . $type->get('id'));
                }
            }
        }
        $options = array_merge($options, $advance_options, $proforma_advance_options);

        if (isset($actions['add']) && !empty($options)) {
            //prepare companies
            require_once PH_MODULES_DIR . 'finance/models/finance.companies.factory.php';
            $filters = array('lang' => $this->registry['lang'],
                             'sanitize' => true,
                             'where' => array('fc.active=1'));
            $companies_list = Finance_Companies::search($this->registry, $filters);

            $default_company = array();
            $default_company_id = '';
            $companies = array();

            foreach ($companies_list as $key => $company) {
                if ($this->registry['currentUser']->get('default_company') == $company->get('id')) {
                    $default_company_id = $company->get('id');
                    $default_company = array('label' => $company->get('name'),
                                             'option_value' => $company->get('id'));
                } else {
                    $companies[] = array('label' => $company->get('name'),
                                         'option_value' => $company->get('id'));
                }
            }

            if ($default_company) {
                array_unshift($companies, $default_company);
            }

            $add_options = array (
                array (
                    'custom_id' => 'type_____',
                    'name' => 'aa2_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'really_required' => true,
                    'label' => $this->i18n('finance_incomes_reasons_type'),
                    'help' => $this->i18n('finance_incomes_reasons_add_legend'),
                    'options' => $options,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : '',
                    'custom_class' => (count($options) > 1 && !$this->registry['request']->get('type')) ? 'undefined' : ''
                ),
                array (
                    'custom_id' => 'company_____',
                    'name' => 'aa2_company',
                    'type' => 'dropdown',
                    'required' => 1,
                    'really_required' => true,
                    'label' => $this->i18n('finance_incomes_reasons_company'),
                    'help' => $this->i18n('finance_incomes_reasons_company'),
                    'options' => $companies,
                    'value' => ($this->registry['request']->get('company')) ?
                                $this->registry['request']->get('company') :
                                $default_company_id,
                    'custom_class' => (count($companies) > 1 && !$this->registry['request']->get('company') && !$default_company_id) ? 'undefined' : ''
                )
            );
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        // "printform" action is available only for invoices and proforma invoices
        if (isset($actions['printform']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        in_array($this->model->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE, PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE)) &&
        !$this->model->get('advance')) {

        } else {
            unset($actions['printform']);
        }

        if (isset($actions['generate'])) {
            //prepare generate options
            require_once(PH_MODULES_DIR . 'patterns/models/patterns.factory.php');
            $filters = array('where' => array('p.model = \'' . $this->model->modelName . '\'',
                                              'p.model_type = \'' . $this->model->get('type') . '\'',
                                              'p.active = 1',
                                              'p.list = 0'),
                             'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                             'model_lang' => $this->getModelLang(),
                             'sanitize' => true);
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters['where'][] = 'p.is_portal = 1';
            }
            // If the model have print form
            if ($this->model->get('for_printform')) {
                // Get only patterns that are for print form
                $filters['where'][] = 'p.for_printform = 1';
            } else {
                // Get only patterns that are NOT for print form
                $filters['where'][] = 'p.for_printform = 0';
            }
            // If there is a company (usually there is because it is a required field)
            if ($this->model->get('company')) {
                // Add filter: get patterns only if they are for this company or they are not for a specific company
                $filters['where'][] = '(p.company = \'' . $this->model->get('company') . '\' OR p.company = \'0\')';
            }
            $patterns = Patterns::search($this->registry, $filters);

            $_options_patterns = array();
            foreach ($patterns as $pattern) {
                $_options_patterns[] = array(
                    'label' => $pattern->get('name') . " (." . $pattern->get('format') . ")",
                    'option_value' => $pattern->get('id'));
            }

            if (empty($_options_patterns)) {
                //remove generate action, the document type does not define the types to generate to
                unset($actions['generate']);
            } else {
                //prepare generate options
                $generate_options = array (
                    array (
                        'custom_id' => 'pattern__',
                        'name' => 'aa2_pattern',
                        'type' => 'dropdown',
                        'required' => 1,
                        'label' => $this->i18n('finance_pattern'),
                        'help' => $this->i18n('finance_pattern'),
                        'options' => $_options_patterns,
                        'value' => ($this->registry['request']->get('type')) ?
                                    $this->registry['request']->get('type') : ''),
                );
                $actions['generate']['options'] = $generate_options;
            }
        }

        if (isset($actions['distribute']) && $this->model && $this->model->get('id') &&
        $this->model->get('status') == 'finished' && !$this->model->get('annulled_by') &&
        $this->model->get('payment_status') != 'nopay' &&
        $this->model->get('distributed') != PH_FINANCE_DISTRIBUTION_NONE &&
        !in_array($this->model->get('type'), array(PH_FINANCE_TYPE_CORRECT_REASON, PH_FINANCE_TYPE_PRO_INVOICE))) {

        } else {
            unset($actions['distribute']);
        }

        return $actions;

    }

    /**
     * Add commodities reservation
     */
    private function _commoditiesReservation() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        if ($request->get('setWarehouses', 'post')) {
            //we make a small trick here as we don't want warehouses IDs
            //to be transfered in the GET string
            $post = $request->getAll('post');
            foreach ($post as $k => $v) {
                $request->remove($k);
                $request->set($k, $v, 'all', true);
            }
        }

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $request->get('link_to'),
                                               'fir.annulled_by = 0'));
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $old_name = $finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name');
            $params = array(
                'model' => $finance_incomes_reason,
                'type' => PH_FINANCE_TYPE_COMMODITIES_RESERVATION,
            );
            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php');
            $result = Finance_Warehouses_Documents::addMultipleDocuments($this->registry, $params);

            if ($result['status']) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_commodities_reservation_success') . ' ' . count($result['created']), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
                if (count($result['created']) == 1) {
                    $created = reset($result['created']);
                    $this->redirect($this->module, 'view', array('view' => $created->get('id')), 'warehouses_documents');
                } else {
                    $this->redirect($this->module, 'list', array('type' => PH_FINANCE_TYPE_COMMODITIES_RESERVATION), 'warehouses_documents');
                }
            } else {
                // keep submitted values on unsuccessful adding of handovers
                $finance_incomes_reason->set('parent_name', $old_name, true);
                $finance_incomes_reason->set('name', $request->get('name'), true);
                $finance_incomes_reason->set('department', $request->get('department'), true);
                $finance_incomes_reason->set('group', $request->get('group'), true);
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_commodities_reservation_failed'), '', -1);
            }
        } elseif ($id) {
            // the model from the DB
            $filters['where'] = array('fir.id = ' . $id, 'fir.annulled_by = 0');
            $filters['model_lang'] = $request->get('model_lang');
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        }

        if (!empty($finance_incomes_reason)) {
            $this->registry->set('get_old_vars', false, true);
            $old_type = $finance_incomes_reason->get('type');
            // IMPORTANT: Set parent_type and link_to_model_name to get VAT settings from the incomes reason
            $finance_incomes_reason->set('parent_type', $finance_incomes_reason->get('type'), true);
            $finance_incomes_reason->set('link_to_model_name', $finance_incomes_reason->modelName, true);
            $finance_incomes_reason->set('type', PH_FINANCE_TYPE_COMMODITIES_RESERVATION, true);

            if (!$request->isPost()) {
                // set name to an empty string
                $finance_incomes_reason->set('parent_name', ($finance_incomes_reason->get('name') ? $finance_incomes_reason->get('name') : $finance_incomes_reason->get('type_name')), true);
                $finance_incomes_reason->set('name', '', true);

                // set default values for group and department
                $finance_incomes_reason->setGroup($finance_incomes_reason->get('group'));
                $finance_incomes_reason->setDepartment($finance_incomes_reason->get('department'));
            }

            $finance_incomes_reason->set('gt2_model_name', 'Finance_Warehouses_Document', true);
            $finance_incomes_reason->getGT2Vars();
            $finance_incomes_reason->set('type', $old_type, true);
            $this->checkAccessOwnership($finance_incomes_reason);
            $gt2 = $finance_incomes_reason->get('grouping_table_2');

            if (empty($gt2)) {
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_handover_no_quantity',
                                                      array('reason_type_name' => $finance_incomes_reason->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $finance_incomes_reason->get('id'));
            }

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
            }
        } else {
            //show error 'no such record'
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Distribute incomes reason
     *
     * @return bool - result of operation
     */
    private function _distribute() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //get the model and its old values
            $filters = array('where' => array ('fir.id = ' . $id, 'fir.annulled_by = 0'),
                             'model_lang' => $request->get('model_lang'));
            $old_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $old_model->sanitize();

            //build the model from the POST
            $finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);
            $finance_incomes_reason->set('id', $id, true);

            if ($finance_incomes_reason->saveDistribution()) {
                $finance_incomes_reason->set('distributed', PH_FINANCE_DISTRIBUTION_YES, true);
                $finance_incomes_reason->updateDistributed();

                $filters = array('where' => array('fir.id = ' . $id, 'fir.annulled_by = 0'),
                                 'model_lang' => $request->get('model_lang'));
                $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $audit_parent = Finance_Incomes_Reasons_History::saveData($this->registry,
                                                                          array('action_type' => 'distribute',
                                                                                'new_model' => $finance_incomes_reason,
                                                                                'old_model' => $old_model
                                                                          ));

                //show success message
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_reasons_distribute_success',
                                                        array($finance_incomes_reason->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                //register the model, with all the posted details
                $this->registry['messages']->setError($this->i18n('error_finance_incomes_reasons_distribute_failed',
                                                      array($finance_incomes_reason->getModelTypeName())), '', -2);
            }
        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('fir.id = ' . $id, 'fir.annulled_by = 0'),
                             'model_lang' => $request->get('model_lang'));
            $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

            if (!empty($finance_incomes_reason)) {
                $this->checkAccessOwnership($finance_incomes_reason);

                $finance_incomes_reason->getDistribution();
            }
        }

        if (!empty($finance_incomes_reason)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
             if (!$this->registry->isRegistered('finance_incomes_reason')) {
                $this->registry->set('finance_incomes_reason', $finance_incomes_reason->sanitize());
             }
        } else {
            //show 'no such record' error
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Preview the GT2 of an invoice by invoice template id
     */
    public function _previewGT2Invoice() {

        $registry = &$this->registry;
        $request = &$registry['request'];
        $db = &$this->registry['db'];

        $filters = array('where' => array('fir.id = ' . $request->get('invoice')));
        $invoice = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (is_object($invoice)) {
            $this->registry->set('get_old_vars', true, true);
            $invoice->getGT2Vars();

            //this is the preview of GT2 itself
            $viewer = new Viewer($registry);
            $viewer->setFrameset('_gt2_view.html');
            $viewer->data['table'] = $invoice->get('grouping_table_2');
            $viewer->display();
            exit;
        }

        //the script should not go further but anyway display an user-friendly and polite error
        printf('<span class="error">%s</span>', $this->i18n('error_finance_invoice_preview'));
        exit;
    }

    /**
     * Change payment status to nopay or unpaid
     */
    private function _changeUnpaidStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('fir.id = ' . $id,
                                          'fir.annulled_by=0',
                                          'fir.active=1'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => true);
        $model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        if ($model && $model->checkNopayPermission()) {
            $res = $model->changeNopayStatus();
            if ($res) {
                $this->registry['messages']->setMessage($this->i18n('message_finance_incomes_nopay_changed'));
            } else {
                $this->registry['messages']->setError($this->i18n('error_finance_nopay_change'));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_finance_nopay_denied'));
        }
        //prepare messages
        $this->registry['messages']->insertInSession($this->registry);
        $this->redirect('finance', 'view', array('view' => $id));
    }

    private function _autoGenSend() {

        $query = 'SELECT invoice_id, contract_id' . "\n" .
                 'FROM fin_invoices_templates_info' . "\n" .
                 'WHERE issue_date = "2010-09-01" AND invoice_id !=0 ORDER BY id';
        $ids = $this->registry['db']->GetAssoc($query);

        $filters = array('where' => array('fir.id in (' . implode(',', array_keys($ids)) . ')'),
                         'model_lang' => 'bg',
                         'sanitize' => false,
                         'sort' => array('fir.id'));
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        $invoices = Finance_Incomes_Reasons::search($this->registry, $filters);

        $filters = array('where' => array('u.id = -1', 'u.hidden = 1'),
                         'sanitize' => true);
        $user = Users::searchOne($this->registry, $filters);

        $old_user = clone $this->registry['currentUser'];
        $this->registry->set('currentUser', $user, true);

        $filters = array('where' => array('e.id = 1001'),
                         'sanitize' => true);
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $email = Emails::searchOne($this->registry, $filters);

        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';

        $res = array('ok' => 0, 'err' => 0, 'err_id' => array());
        foreach ($invoices as $invoice) {

//            if ($invoice->getFilesCount()) {
//                $query = 'SELECT id from files where model="finance_incomes_reason" and model_id = ' . $invoice->get('id');
//                $file = 'file_' . $this->registry['db']->GetOne($query);
//                $invoice->set('attached_files', array($file), true);
//            } else {
                $invoice->set('attached_files', array('pattern_14'), true);
////            }
            $invoice->set('email_subject', $email->get('subject'), true);
            $invoice->set('body', $email->get('body'), true);
            $invoice->set('email_template', $email->get('id'), true);

            $contract = new Contract($this->registry, array('id' => $ids[$invoice->get('id')]));
            $mail = $contract->getCstmFinancialData();
            $cc_mails = $contract->getContactCcData('cstm', 'fin');

            $mail = $mail['name'] . ' <' . $mail['email'] . '>';
            $cc = array();
            foreach ($cc_mails as $m) {
                $cc = $m['name'] . ' <' . $m['email'] . '>';
            }

            $invoice->set('customer_email', $mail, true);
            $invoice->set('customer_email_cc', $cc, true);

            $result = $invoice->sendAsMail();
            if (empty($result['erred'])) {
                $res['ok'] ++;
            } else {
                $res['err'] ++;
                $res['err_id'][$invoice->get('id')] = $result['erred'];
            }
        }
    }

    /**
     * Gets new fiscal event dates according to model issue date and company settings
     */
    private function _recalcFiscalDates() {
        $request = $this->registry['request'];
        if ($request->get('company') && $request->get('issue_date')) {
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.model.php';
            $fir = new Finance_Incomes_Reason($this->registry, $request->getAll('post'));
            $fir->set('type', PH_FINANCE_TYPE_INVOICE, true);
            $fir->calculateFiscalEventDates();
            if ($request->get('fiscal_event_date') &&
                    $request->get('fiscal_event_date') >= $fir->get('min_fiscal') &&
                    $request->get('fiscal_event_date') <= $fir->get('max_fiscal')) {
                // validate fiscal event date from the form
                $fir->set('fiscal_event_date', $request->get('fiscal_event_date'), true);
            }
            $result = array(
                'fiscal_event_date' => $fir->get('fiscal_event_date'),
                'max_fiscal' => $fir->get('max_fiscal'),
                'min_fiscal' => $fir->get('min_fiscal'),
                'fiscal_dates_warning' => $fir->get('fiscal_dates_warning')
            );
            echo json_encode($result);
        } else {
            echo '';
        }
        die;
    }

    /**
     * Prepare the form for adding payment data
     */
    private function _preparePaymentForm() {
        $registry = &$this->registry;
        $request = &$registry['request'];

        // set finance_after_action for validation
        $request->set('finance_after_action', 'payment', 'all', true);

        $temp_finance_incomes_reason = Finance_Incomes_Reasons::buildModel($this->registry);

        $result = array();

        // validate form
        if (!($temp_finance_incomes_reason->validate($temp_finance_incomes_reason->get('id') ? 'edit' : 'add') &&
        (in_array($temp_finance_incomes_reason->get('type'), array(PH_FINANCE_TYPE_INVOICE, PH_FINANCE_TYPE_PRO_INVOICE)) ?
        $temp_finance_incomes_reason->validateInvoice() : 1)) &&
        $items = $this->registry['messages']->getErrors()) {

            $financeViewer = new Viewer($this->registry);
            $financeViewer->setFrameset('message.html');
            $financeViewer->data['items'] = $items;
            $financeViewer->data['display'] = 'error';
            $result['errors'] = $financeViewer->fetch();

            print json_encode($result);
            exit;
        }

        // get the company data for payment info
        $company_id = 0;
        $office_id = 0;
        $payment_type = '';
        if (preg_match('#^(\d+)_(\d+)_(cash|bank|cheque)_\d+$#', $temp_finance_incomes_reason->get('company_data'), $matches)) {
            // get company, office and container
            $company_id = $matches[1];
            $office_id = $matches[2];
            $payment_type = $matches[3];
        }

        // if the required data are completed the option for cashboxes and bank accounts are taken
        if ($company_id && $payment_type && $office_id) {
            //prepare companies, offices, cashboxes/bank accounts
            require_once $this->modelsDir . 'finance.dropdown.php';
            $params = array($registry,
                            'lang'              => $temp_finance_incomes_reason->get('model_lang'),
                            'company_id'        => ($company_id ? $company_id : array(0)),
                            'payment_direction' => ($temp_finance_incomes_reason->get('type') == PH_FINANCE_TYPE_CREDIT_NOTICE ? 'expenses' : 'incomes'),
                            'active'            => 1);
            $companies_data = Finance_Dropdown::getCompaniesData($params);
        } else {
            $companies_data = array();
        }

        // prepare the viewer
        $viewer = new Viewer($registry);
        // sets container currency, suggested conversion rate and converted amount to model
        $viewer->data['payment_info_container'] = $temp_finance_incomes_reason->get('company_data');
        $temp_finance_incomes_reason->prepareContainerRate($companies_data);

        $viewer->data['temp_finance_reason'] = $temp_finance_incomes_reason->sanitize();
        // payment_info_container
        $viewer->data['container_options'] = $companies_data;
        $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_incomes_expenses_reasons_add_payment.html');

        $result['result'] = $viewer->fetch();

        print json_encode($result);
        exit;
    }

    /**
     * Gets warehouses to be shown in the lightbox
     * for handovers or reservation protocols issue
     */
    private function _getWarehouses() {
        $request = &$this->registry['request'];
        $direction = $request->get('direction');
        $id = $request->get($this->action);

        // the model from the DB
        $filters = array('where' => array('fir.id = ' . $id));
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        if (empty($finance_incomes_reason)) {
            //show error 'no such record'
            exit(json_encode(array('error' => array($this->i18n('error_no_such_finance_incomes_reason')))));
        }
        $parent_gt2 = $finance_incomes_reason->getGT2Vars();

        //get all active warehouses
        require_once $this->modelsDir . 'finance.warehouses.factory.php';
        $filters = array('sanitize' => true,
                         'model_lang' => $finance_incomes_reason->get('model_lang'),
                         'where' => array('fwh.active = 1',
                                          'fwh.company = "' . $finance_incomes_reason->get('company') . '"',
                                          'fwh.office = "' . $finance_incomes_reason->get('office') . '"'),
                         'sort' => array('fwhi18n.name ASC'));
        if ($direction == 'outgoing' || $direction == 'reservation') {
            //we have to check what we can reserve/handover
            if ($direction == 'reservation') {
                $finance_incomes_reason->checkCommoditiesReservation(true);
            } else {
                $finance_incomes_reason->checkAddingHandover('outgoing', true);
            }
            //here we have the rows we can reserve from
            $gt2 = $finance_incomes_reason->get('grouping_table_2');
            $articles = array();
            if (!empty($gt2['values'])) {
                foreach ($gt2['values'] as $values) {
                    if (!empty($values['article_id'])) {
                        $articles[] = $values['article_id'];
                    }
                }
            }
            $filters['where'][] = 'fwq.nomenclature_id IN (\'' . implode('\', \'', $articles) . '\')';
            $filters['where'][] = 'fwq.quantity > 0';
        }

        $warehouses = Finance_Warehouses::search($this->registry, $filters);
        if (empty($warehouses)) {
            exit(json_encode(array('error' => array($this->i18n('error_finance_whd_articles_unavailable')))));
        }
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset(PH_MODULES_DIR . 'finance/templates/_setWarehouses.html');
        $viewer->data['warehouses'] = $warehouses;
        $viewer->data['default_warehouse'] = $this->registry['currentUser']->get('default_warehouse');
        if ($direction == 'reservation') {
            $viewer->data['submit_link'] = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=commodities_reservation&amp;commodities_reservation=%d',
                    $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'], $id);
        } else {
            $viewer->data['submit_link'] = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=addhandover&amp;addhandover=%d&amp;handover_direction=%s',
                $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'],
                $id, $direction);
        }
        exit(json_encode(array('content' => $viewer->fetch())));
    }

    /**
     * Updates financial data from customer into data of current financial document
     */
    private function _updateCustomerData() {

        // change action while checking permissions
        $this->setAction(str_replace('ajax_', '', $this->action));

        $model_lang = $this->registry['request']->get('model_lang') ?: $this->registry['lang'];
        $filters = array(
            'where' => array(
                'fir.id = \'' . $this->registry['request']->get('id') . '\''
            ),
            'model_lang' => $model_lang
        );
        $finance_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        $result = $errors = array();
        if ($finance_incomes_reason && $this->checkAccessOwnership($finance_incomes_reason, false)) {
            $valid = true;
            foreach ($finance_incomes_reason->getTranslations() as $t_lang) {
                if ($t_lang != $model_lang) {
                    $finance_incomes_reason->set('model_lang', $t_lang, true);
                }
                if (!$finance_incomes_reason->validateCustomerFinData()) {
                    $valid = false;
                    $errors = array_merge($errors, $this->registry['messages']->getErrors());
                    $this->registry['messages']->flush();
                }
                if ($t_lang != $model_lang) {
                    $finance_incomes_reason->set('model_lang', $model_lang, true);
                }
            }

            if ($valid && $finance_incomes_reason->updateCustomerData()) {

                require_once(PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php');
                $this->registry->set('no_GT2', true, true);
                Finance_Incomes_Reasons_History::saveData($this->registry,
                                                          array('action_type' => 'edit',
                                                                'new_model' => Finance_Incomes_Reasons::searchOne($this->registry, $filters),
                                                                'old_model' => $finance_incomes_reason->sanitize()));
                $this->registry->remove('no_GT2');

                $result['messages'] =
                    array($this->i18n('message_finance_incomes_reasons_edit_success', array($finance_incomes_reason->getModelTypeName())));
            } else {
                // validation or save of data failed
                $errors = array_merge(
                    array($this->i18n('error_finance_incomes_reasons_edit_failed', array($finance_incomes_reason->getModelTypeName()))),
                    $errors);
            }
        } else {
            // model not found or action not allowed
            $errors = array($this->i18n('error_no_access_to_model'));
        }

        // display either success messages, or error messages
        if (!empty($errors)) {
            $result = array('errors' => $errors);
        }

        foreach ($result as $key => $items) {
            $financeViewer = new Viewer($this->registry);
            $financeViewer->setFrameset('message.html');
            $financeViewer->data['items'] = $items;
            $financeViewer->data['display'] = General::plural2singular($key);
            $result[$key] = $financeViewer->fetch();
        }

        // restore to ajax action
        $this->setAction('ajax_' . $this->action);

        // print json encoded result of operation
        $this->registry->set('ajax_result', json_encode($result), true);
    }

    /**
     * Clone document
     */
    private function _clone() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters = array('where' => array('fir.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $clone_model = Finance_Incomes_Reasons::searchOne($this->registry, $filters);

        if (empty($clone_model)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_finance_incomes_reason'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        } else {
            //check access and ownership of the model
            //IMPORTANT: only user defined types of income reasons could be cloned
            //           this is defined in the model's checkPermissions method
            $this->checkAccessOwnership($clone_model);

            //prepare transform params
            //these are used to store relation between the parent and the clone
            $transform_params = array(
                'origin_method'         => 'clone',
                'origin_model'          => $clone_model->modelName,
                'origin_id'             => $clone_model->get('id'),
                'origin_full_num'       => $clone_model->get('num'),
                'origin_name'           => $clone_model->get('name') ?: $clone_model->get('type_name'),
                'origin_gt2_relations'  => array(),
                'transform_from_report' => false,
            );
            if ($request->isRequested('skip_relatives')) {
                $transform_params['skip_relatives'] = $request->get('skip_relatives');
            }

            //IMPORTANT: cloning the incomes reason is actually moving to add action
            // set status to opened to get up-to-date available quantity of articles
            $clone_model->set('status', 'opened', true);
            $clone_model->getGT2Vars();
            $clone_model->set('transform_params', serialize($transform_params), true);
            $clone_model->sanitize();

            //use the session to store the parent model so that it could be loaded in add mode
            $this->registry['session']->set('report_custom_model', serialize($clone_model), 'clone', true);

            $this->redirect('finance', 'add', array('type' => $clone_model->get('type'), 'company' => $clone_model->get('company'), 'report_session_param' => 'clone'), 'incomes_reasons');
        }
    }
}

?>
