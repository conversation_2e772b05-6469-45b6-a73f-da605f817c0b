<?php
include_once 'processrepliedemails.trait.php';

class Bgservice_Automations_Controller extends Automations_Controller {
    use ProcessRepliedEmails_Trait;

    /**
     * Fills several fields with the custom num,
     * changes request status and stops watches(if there is started one)
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of operation
     */
    public function manageInternalRequest($params) {

        $db = &$this->registry['db'];
        $model = $params['model']->get('id');

        $model = Documents::searchOne($this->registry, array('where' => array('d.id = ' . $model)));

        // Adds relation between the current model and the FAQ document if need
        if ($request_faq_id = $model->getPlainVarValue('request_faq_id')) {
            if ($request_faq_id < $model->get('id')) {
                $link_to = $request_faq_id;
                $parent_id = $model->get('id');
            } else {
                $parent_id = $request_faq_id;
                $link_to = $model->get('id');
            }
            $query = "INSERT IGNORE INTO " . DB_TABLE_DOCUMENTS_RELATIVES . " (parent_id, parent_model_name, link_to, link_to_model_name, origin) VALUES"
                . "( {$parent_id}, 'Document', {$link_to}, 'Document', 'inherited')";
            $db->Execute($query);
        }

        $custom_num = $model->get('custom_num');
        if ($this->registry['action'] != 'edit' || !$this->registry['request']->isPost() || empty($custom_num)) {
            return true;
        }

        $new_model = clone $model;
        $new_model->set('status', $this->settings['document_status'], true);
        $new_model->set('substatus', $this->settings['document_substatus'], true);

        //check status permissions
        if (!$new_model->checkStatusSettings($model)) {
            $this->registry['messages']->setError($this->i18n('error_status_permissions'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        if ($model->sanitized) {
            $model->unsanitize();
            $sanitize_after = true;
        }

        $db->StartTrans();

        $this->setOriginalUserAsCurrent();

        //stop watch
        $stopwatch_data = $model->manageStopWatch('stop');
        if ($stopwatch_data && !empty($stopwatch_data['date']) && $stopwatch_data['date'] > '0000-00-00') {
            //prepare timesheet
            $p = array();
            $p['model_id'] = Documents::getSystemTask($this->registry, $model->get('id'));
            $p['resource'] = 'human';
            $p['parent_module'] = 'documents';
            $p['period_type'] = 'dates';
            $p['startperiod_dates'] = $stopwatch_data['date'];
            $p['endperiod_dates'] = General::strftime('%Y-%m-%d %H:%M:00');
            $p['user_id'] = $this->registry['currentUser']->get('id');
            $p['office'] = $this->registry['currentUser']->get('office');
            $p['activity'] = $this->settings['timesheet_activity'];
            $p['subject'] = $model->get('custom_num');
            $p['content'] = $model->get('custom_num');

            require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheets.factory.php';
            $lang_file = PH_MODULES_DIR . 'tasks/i18n/' . $this->registry['lang'] . '/tasks.ini';
            $this->registry['translater']->loadFile($lang_file);
            $timesheet = new Tasks_Timesheet($this->registry, $p);
            if (!$timesheet->save()) {
                $db->FailTrans();
                $this->registry['messages']->setError($this->i18n('error_adding_timesheet'));
            } else {
                $timesheet->saveHistory($model);
            }
        }

        //change status
        $old_model = clone $model;
        $query = 'SELECT name FROM ' . DB_TABLE_DOCUMENTS_STATUSES . "\n" .
            'WHERE id = ' . $this->settings['document_substatus'] . "\n" .
            '  AND lang = "' . $model->get('model_lang') . '"';
        $ss_name = $db->GetOne($query);
        $model->set('status', $this->settings['document_status'], true);
        $model->set('substatus', $this->settings['document_status'] . '_' . $this->settings['document_substatus'], true);
        $model->set('substatus_name', $ss_name, true);
        if ($model->setStatus()) {
            //show message 'message_documents_status_success'
            $this->registry['messages']->setMessage($this->i18n('message_documents_status_success', array($model->getModelTypeName())));

            //add comment
            require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
            $comment = Comments::buildModel($this->registry);
            $bugs = trim($model->get('custom_num'));
            if (!empty($bugs)) {
                $bugs = preg_split('#\s*,\s*#', $bugs);
                foreach ($bugs as $b => $bug) {
                    $bugs[$b] = sprintf('<a href="' . $this->settings['bugtracker_url'] . '" target="_blank">%d</a>', $bug, $bug);
                }
                $bugs = implode(', ', $bugs);
            }

            $comment->set('content', $bugs, true);
            $comment->set('subject', $this->i18n('documents_status_change_comment'), true);
            $comment->set('model', 'Document', true);
            $comment->set('model_id', $model->get('id'), true);
            $comment->unsetProperty('id', true);
            $comment->set('skip_send_email', true, true);

            if ($comment->save()) {
                $comment->slashesStrip();
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_documents_comments_add_success'));
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_comments_add_failed'));
            }

            $audit_parent = Documents_History::saveData($this->registry, array('model' => $model, 'action_type' => 'status', 'new_model' => $model, 'old_model' => $old_model));

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($model);
            }

            //send notification
            $controller = new Documents_Controller($this->registry);
            $model->set('comment', $comment, true);
            $controller->sendStatusNotification($model, $audit_parent);
            unset($controller);
        }

        if ($this->registry['messages']->getErrors()) {
            $db->FailTrans();
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if (!empty($sanitize_after)) {
            $model->sanitize();
        }

        $this->registry['messages']->insertInSession($this->registry);

        return $result;
    }

    /**
     * Checks if there is already a "Remote work" document for today created by current user.
     * If so, automation does not allow creation of a new one but displays
     * notification and redirects to the existing one.
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of operation
     */
    public function checkDailyReport($params) {

        // adding actions: add, adds
        if ($this->registry['request']->isPost() || !in_array($this->registry['action'], array('add', 'adds'))) {
            return true;
        }
        $db = &$this->registry['db'];

        $query = 'SELECT d.id ' . "\n" .
            'FROM ' . DB_TABLE_DOCUMENTS . ' AS d ' . "\n" .
            'WHERE d.type=' . $params['start_model_type'] . ' AND d.added_by=' . $this->registry['originalUser']->get('id') . ' AND DATE(d.added)=CURDATE()' . "\n" .
            'ORDER BY d.id DESC';
        $document_id = $db->GetOne($query);

        if ($document_id) {
            $this->registry['messages']->setError($this->i18n('message_daily_report_already_created_today'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->registry['module'], 'edit', array('edit' => $document_id));
        }

        return true;
    }

    /**
     * Function to transform the meetings from Call List documents to meetings events
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of operation
     */
    public function callListMeetings($params) {
        // take the settings
        $settings = $this->settings;

        // makes the options for minitasks as an array
        if (!empty($settings['call_minitasks'])) {
            $settings['call_minitasks'] = preg_split('/\s*,\s*/', $settings['call_minitasks']);
            $this->settings['call_minitasks'] = $settings['call_minitasks'];
        }

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';

        // prepare the filters for search the needed documents
        // only the documents from the previous day are taken
        $document_filters = array(
            'model_lang' => $this->registry['lang'],
            'where' => array(
                "DATE_FORMAT(d.added, '%Y-%m-%d') = DATE_SUB(CURDATE(), INTERVAL 1 DAY)",
                'd.type="' . $params['start_model_type'] . '"',
                'd.deleted_by = 0',
                'd.active = 1'
            ),
            'sanitize' => true
        );

        // get the documents list
        $documents_call_list = Documents::search($this->registry, $document_filters);

        if (!empty($documents_call_list)) {
            // require the needed classes
            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            require_once PH_MODULES_DIR . 'events/models/events.history.php';
            require_once PH_MODULES_DIR . 'events/models/events.audit.php';
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
            require_once PH_MODULES_DIR . 'minitasks/models/minitasks.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

            // include language file of minitasks
            $lang_file = PH_MODULES_DIR . 'minitasks/i18n/' . $this->registry['lang'] . '/minitasks.ini';
            $this->registry['translater']->loadFile($lang_file);

            // retrieve the phone call event type
            $filters_event_type = array(
                'model_lang' => $this->registry['lang'],
                'where' => array(
                    'et.keyword="meeting"',
                    'et.deleted_by = 0',
                    'et.active = 1'
                ),
                'sanitize' => true
            );
            $meeting_event_type = Events_Types::searchOne($this->registry, $filters_event_type);

            // array to save the users who will add the events
            $users_to_add = array();

            // start transaction
            $this->registry['db']->StartTrans();

            // iterates throgh the records and creates records
            foreach ($documents_call_list as $document_call) {
                // get additional vars as associative array
                $document_assoc_vars = $document_call->getAssocVars();

                // if there is no record for the required var or no values then no action is taken
                if (empty($document_assoc_vars[$settings['call_type']]) || empty($document_assoc_vars[$settings['call_type']]['value'])) {
                    $this->executionWarnings[] = 'No call statuses for document with id ' . $document_call->get('id');
                    continue;
                }

                // check if the user has already been taken in a previous iteration
                if (!array_key_exists($document_call->get('added_by'), $users_to_add)) {
                    // get the user model
                    $filters_user_to_use = array(
                        'model_lang' => $this->registry['lang'],
                        'where' => array(
                            'u.id="' . $document_call->get('added_by') . '"',
                            'u.deleted_by=0'
                        ),
                        'sanitize' => true
                    );
                    $user_for_current_event = Users::searchOne($this->registry, $filters_user_to_use);
                    $users_to_add[$document_call->get('added_by')] = $user_for_current_event;
                }
                $user_to_use = $users_to_add[$document_call->get('added_by')];

                // get the options
                $current_document_calls = $document_assoc_vars[$settings['call_type']];

                // iterates through the values
                foreach ($current_document_calls['value'] as $key_row => $doc_call) {
                    // get the needed vars
                    $customer_id = $document_assoc_vars[$settings['customer_id']];
                    $customer_name = $document_assoc_vars[$settings['customer_name']];
                    $call_date_next_step = $document_assoc_vars[$settings['call_list_next_step']];
                    $call_description = $document_assoc_vars[$settings['call_description']];
                    $next_step_comment = $document_assoc_vars[$settings['call_next_step_comment']];

                    $this->registry->set('currentUser', $user_to_use, true);

                    if ($doc_call == $settings['call_meeting']) {
                        // if this is the selected option then a meeting event is created
                        if (!$meeting_event_type) {
                            $this->executionWarnings[] = 'No meeting event type!!!';
                            continue;
                        }

                        // if the customer or the date for next step is not completed then this row is skipped
                        if (empty($customer_id['value'][$key_row]) || empty($call_date_next_step['value'][$key_row])) {
                            $this->executionWarnings[] = 'No customer or next step date for the row ' . $key_row . ' of document with id ' . $document_call->get('id');
                            continue;
                        }

                        // prepare the event to be added
                        $event = Events::buildModel($this->registry);
                        $event->set('id', null, true);
                        $event->set('name', $this->i18n('automation_bgservice_event_name') . " " . (!empty($customer_name['value'][$key_row]) ? $customer_name['value'][$key_row] : ''), true);
                        $event->set('customer', (!empty($customer_id['value'][$key_row]) ? $customer_id['value'][$key_row] : 0), true);
                        $event->set('type', $meeting_event_type->get('id'), true);

                        // set the start and end of the event
                        $event_start = substr($call_date_next_step['value'][$key_row], 0, 10) . ' 09:00:00';
                        $event_end = substr($call_date_next_step['value'][$key_row], 0, 10) . ' 11:00:00';

                        $event->set('event_start', $event_start, true);
                        $event->set('event_end', $event_end, true);

                        // forms the description
                        $description = '';
                        if (!empty($call_description['value'][$key_row])) {
                            $description = $call_description['value'][$key_row];
                        }
                        if (!empty($next_step_comment['value'][$key_row])) {
                            $description .= (!empty($description) ? "\n" : "") . $this->i18n('automation_bgservice_event_description_next_step_prefix') . " " . $next_step_comment['value'][$key_row];
                        }

                        $event->set('description', $description, true);

                        // saves the event
                        if ($event->save()) {
                            $filters_new_event = array(
                                'where' => array('e.id = ' . $event->get('id')),
                                'model_lang' => $event->get('model_lang')
                            );
                            $new_event = Events::searchOne($this->registry, $filters_new_event);
                            $old_event = new Event($this->registry);
                            $old_event->sanitize();

                            Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'add', 'new_model' => $new_event, 'old_model' => $old_event));
                        } else {
                            $this->executionErrors[] = 'An error occurred while trying to add event for row ' . $key_row . ' in document with id ' . $document_call->get('id');
                        }
                    } elseif (in_array($doc_call, $settings['call_minitasks']) || $doc_call == $settings['call_minitasks_special']) {
                        $description = '';

                        // if no description is set then this minitask is skipped because
                        //description is a required field
                        if (empty($call_description['value'][$key_row]) && empty($next_step_comment['value'][$key_row])) {
                            $this->executionWarnings[] = 'No description for the row ' . $key_row . ' of document with id ' . $document_call->get('id') . '! Description is a required field for minitasks!';
                            continue;
                        }

                        if (!empty($next_step_comment['value'][$key_row])) {
                            $current_description = $next_step_comment['value'][$key_row];
                        } else {
                            $current_description = $call_description['value'][$key_row];
                        }

                        if (in_array($doc_call, $settings['call_minitasks'])) {
                            $description = $current_description;
                        } else {
                            $description = sprintf('%s %s', $this->i18n('automation_bgservice_minitask_description_prefix'), $current_description);
                        }

                        // prepare the minitask
                        $minitask = Minitasks::buildModel($this->registry);

                        // set the properties to the minitask
                        $minitask->set('model', 'Document', true);
                        $minitask->set('model_id', $document_call->get('id'), true);
                        $minitask->set('customer', (!empty($customer_id['value'][$key_row]) ? $customer_id['value'][$key_row] : 0), true);
                        $minitask->set('description', $description, true);
                        $minitask->set('assigned_to', $document_call->get('added_by'), true);
                        $minitask->set('deadline', (!empty($call_date_next_step['value'][$key_row]) ? substr($call_date_next_step['value'][$key_row], 0, 10) : 0), true);

                        // saves the minitask
                        if ($minitask->save()) {
                            $filters = array(
                                'where' => array('m.id=' . $minitask->get('id')),
                                'sanitize' => true,
                                'check_module_permissions' => 'minitasks',
                                'model_lang' => $this->registry->get('lang')
                            );
                            $minitask = Minitasks::searchOne($this->registry, $filters);
                            $minitask->set('severity_name', $this->i18n('minitasks_' . $minitask->get('severity')), true);

                            // saves history for the document
                            $record_model = Documents::searchOne($this->registry, array('where' => array('d.id=' . $document_call->get('id'))));

                            $old_record_model = clone $record_model;
                            $old_record_model->sanitize();
                            $record_model->set('minitask', $minitask, true);

                            Documents_History::saveData($this->registry, array('model' => $record_model, 'new_model' => $record_model, 'action_type' => 'add_minitask', 'old_model' => $old_record_model));

                            unset($record_model);
                            unset($old_record_model);
                        } else {
                            $this->executionErrors[] = 'An error occurred while trying to add minitask for row ' . $key_row . ' in document with id ' . $document_call->get('id');
                        }
                    }

                    // return the assigned to original user (automation user)
                    $this->setAutomationUserAsCurrent();
                }
            }
        } else {
            $this->executionWarnings[] = 'No new call lists added the previous day.';
        }

        $this->isExecuted = true;
        $query_insert_ah = 'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY . "\n" .
            'SET parent_id = ' . $params['id'] . ', model_id = 1, added = now(), num=1, result=1' . "\n" .
            'ON DUPLICATE KEY UPDATE ' . "\n" .
            'added = now(), num=num+1, result=1';
        $this->registry['db']->Execute($query_insert_ah);

        $dbTransError = $this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        return !$dbTransError;
    }

    /**
     * The method checks if any active contract/annex is changed or created and
     * if so it copies the information we want (via settings) to the customer.
     *
     * @param array $params parameters coming from the database, settings field.
     * @return bool - the result of the operations
     */
    public function copyContractDataToCustomer($params) {
        $this->isExecuted = true;
        $registry = $this->registry;
        //start with clean errors/warnings session
        $registry['messages']->unset_vars('errors');
        $registry['messages']->unset_vars('warnings');
        $errors = array();
        $contract = $params['model'];
        //get the additional vars for contract
        $registry->set('get_old_vars', true, true);
        $contract->getVars();
        $contVars = $contract->getAssocVars();
        $registry->set('get_old_vars', false, true);

        // Get the customer
        $filters = array(
            'where' => array(
                "c.id = '{$contract->get('customer')}'",
                "c.type = '{$this->settings['customer_type']}'"
            ),
            'model_lang' => $contract->get('model_lang')
        );
        $customer = Customers::searchOne($registry, $filters);
        if (empty($customer)) {
            $this->executionErrors[] = $this->i18n(
                'error_automation_bgservice_copycontractdatatocustomer_customer_not_found',
                array($contract->get('customer'), $contract->get('id'))
            );
            return false;
        }
        //get additional vars for customer
        $registry->set('get_old_vars', true, true);
        $customer->getVars();
        $registry->set('get_old_vars', false, true);
        $oldCustomer = clone $customer;
        $custVars = $customer->get('vars');

        //iterate the variables from settings and copy the right values to customer
        foreach ($this->settings as $k => $v) {
            if (strpos($k, 'field_') === 0) {
                $contFieldName = ltrim($k, 'field_');
                if (isset($contVars[$contFieldName])) {
                    $key = array_search($v, array_column($custVars, 'name'));
                    //the field exists in the customer vars. Update it
                    if ($key !== false) {
                        $custVars[$key]['value'] = $contVars[$contFieldName]['value'];
                    } else {
                        $errors[] = $this->i18n(
                            'error_automation_bgservice_copycontractdatatocustomer_var_not_found',
                            array($v, $customer->get('id'))
                        );
                    }
                }
            }
        }
        //stop the automation if there were errors
        if (!empty($errors)) {
            $this->executionErrors[] = $this->i18n(
                'error_automation_bgservice_copycontractdatatocustomer_general_error',
                array($contract->get('id'), $customer->get('id'))
            );
            $this->executionErrors = array_merge($this->executionErrors, $errors);
            return false;
        }
        $customer->set('vars', $custVars, true);

        $registry['translater']->loadFile(PH_MODULES_DIR . 'customers/i18n/' . $registry['lang'] . '/customers.ini');

        //validate the customer in order to show errors if we have any. We still save the customer even if it has errors, but need to show it to warnings in the email.
        if (!$customer->validate()) {
            //show link to the customer
            $url = PH_SERVER_BASE . "{$_SERVER['PHP_SELF']}?{$registry['module_param']}=customers&amp;customers=view&amp;view={$customer->get('id')}";
            $link = "<a href='{$url}' target='_blank'>{$customer->get('name')}</a>";
            $valErrors = $this->i18n(
                'error_automation_bgservice_copycontractdatatocustomer_validation_errors',
                array($link, $customer->get('id'))
            );
            foreach ($registry['messages']->getErrors() as $vError) {
                $valErrors .= "<br>" . $vError;
            }
            $this->executionWarnings = array($valErrors);
        }
        //try to save
        $customer->slashesEscape();
        if ($customer->edit()) {
            //record in history if save is successful
            $customer->slashesStrip();
            $audit_parent = Customers_History::saveData(
                $registry,
                array(
                    'action_type' => 'edit',
                    'old_model' => $oldCustomer,
                    'model' => $customer,
                    'new_model' => $customer
                )
            );
            if (!$audit_parent) {
                $this->executionErrors[] = $this->i18n(
                    'error_automation_bgservice_copycontractdatatocustomer_hystory_not_saved',
                    array($customer->get('id'))
                );
                return false;
            }
        } else {
            $this->executionErrors[] = $this->i18n(
                'error_automation_bgservice_copycontractdatatocustomer_saving_customer_failed',
                array($customer->get('id'))
            );
            $this->executionErrors = array_merge($registry['messages']->getErrors(), $this->executionErrors);
            return false;
        }
        return true;
    }

    /**
     * Function for renewing contracts
     */
    public function renewContracts($params) {
        $this->isExecuted = true;
        $db = $this->registry['db'];

        //contract types
        $c_types = $this->settings['contract_types'];
        //substatuses
        $c_substatuses = $this->settings['contract_substatuses'];

        $var_name = $this->settings['check_var'];
        $var_value = $this->settings['check_var_value'];
        $before_date_validity = $this->settings['before_date_validity'];

        //get ids for additional variables
        $query = 'SELECT fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
            ' WHERE fm.name="' . $var_name . '" AND fm.model="Contract" AND fm.model_type in (' . $c_types . ')';
        $var_ids = $db->GetCol($query);

        //get contract ids and date_validity
        $query = 'SELECT co.id, co.date_validity FROM ' . DB_TABLE_CONTRACTS . ' AS co,' . DB_TABLE_CONTRACTS_CSTM . ' AS cocstm ' . "\n" .
            ' WHERE co.subtype="contract" AND co.type in (' . $c_types . ') AND co.status="closed"' . "\n" .
            ' AND co.substatus in (' . $c_substatuses . ')' . "\n" .
            ' AND co.date_validity >= CURDATE() AND co.date_validity <= DATE_ADD(CURDATE(), INTERVAL ' . $before_date_validity . ' DAY)' . "\n" .
            ' AND co.id=cocstm.model_id AND cocstm.var_id in (' . implode(',', $var_ids) . ') AND cocstm.value="1"';
        $records = $db->GetAssoc($query);

        $co_ids = array_keys($records);

        if (empty($co_ids)) {
            return true;
        }
        //get renew_period ids
        $query = 'SELECT fm.model_type, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm ' . "\n" .
            ' WHERE fm.name="renew_period" AND fm.model="Contract" AND fm.model_type in (' . $c_types . ')';
        $var_ids = $db->GetAssoc($query);

        //get renew periods
        $query = 'SELECT cocstm.model_id, cocstm.value FROM ' . DB_TABLE_CONTRACTS_CSTM . ' AS cocstm ' . "\n" .
            ' WHERE cocstm.model_id in (' . implode(',', $co_ids) . ') AND cocstm.var_id in (' . implode(',', $var_ids) . ')';
        $renew_periods = $db->GetAssoc($query);

        $db->StartTrans();
        foreach ($co_ids as $co_id) {
            //update date_validity for contract
            $query = 'UPDATE ' . DB_TABLE_CONTRACTS . "\n" .
                ' SET date_validity = DATE_ADD(date_validity, INTERVAL ' . $renew_periods[$co_id] . ' MONTH)' . "\n" .
                ' WHERE id=' . $co_id;
            $db->Execute($query);
            if ($db->ErrorMsg()) {
                $this->executionErrors[] = 'Error on updating date_validity with ' . $renew_periods[$co_id] . ' months for contract id = ' . $co_id . ': ' . $db->ErrorMsg();
            }
            //update periods_end for contract invoice template
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                ' SET periods_end = DATE_ADD("' . $records[$co_id] . '", INTERVAL ' . $renew_periods[$co_id] . ' MONTH)' . "\n" .
                ' WHERE deleted=0 AND contract_id=' . $co_id;
            $db->Execute($query);
            if ($db->ErrorMsg()) {
                $this->executionErrors[] = 'Error on updating periods_end with ' . $renew_periods[$co_id] . ' months for contract id = ' . $co_id . ': ' . $db->ErrorMsg();
            }
        }

        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();

        if (!empty($this->executionErrors) || $dbTransError) {
            $result = false;
        } else {
            $this->updateAutomationHistory($params, 0, 1);
            $result = true;
        }

        return $result;
    }

    /**
     * Validate the appointment of the Order type document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function validateDocumentOrder($params) {
        // If the current action is setstatus
        if ($this->registry['action'] == 'setstatus') {
            // Get the automation settings
            $substatus = $this->settings['substatus'];
            $appointed_to = $this->settings['appointed_to'];
            $attention_id = $this->settings['attention_id'];
            $attention_name = $this->settings['attention_name'];

            // Set the required settings
            $required_settings = array(
                'substatus',
                'appointed_to',
                'attention_id',
                'attention_name'
            );

            // Prepare the messages object
            $messages = &$this->registry['messages'];

            // If all required settings are set
            if (count(array_intersect(array_keys(array_filter($this->settings)), $required_settings)) == count($required_settings)) {
                // Execute the validation if the requested substatus is the same as the substatus from the settings
                if ($this->registry['request']->get('substatus') == $substatus) {
                    // Prepare the document object
                    $document = $params['model'];

                    // Get the vars of the document
                    $document->getAssocVars();
                    $assoc_vars = $document->get('assoc_vars');

                    // If the appointment of the document is to all employees
                    if (!empty($assoc_vars[$appointed_to]['value']) && $assoc_vars[$appointed_to]['value'] == '1') {
                        // Check if there are any employees selected
                        if (!empty($assoc_vars[$attention_id]['value']) && is_array($assoc_vars[$attention_id]['value']) && count(array_filter($assoc_vars[$attention_id]['value'])) > 0 || !empty($assoc_vars[$attention_name]['value']) && is_array($assoc_vars[$attention_name]['value']) && count(array_filter($assoc_vars[$attention_name]['value'])) > 0) {
                            // Raise an error that: no employees should be selected
                            $messages->setError(sprintf($this->i18n('error_document_order_appointed_to_all_employees'), $assoc_vars[$attention_name]['label']));
                        }
                    } elseif (!empty($assoc_vars[$appointed_to]['value']) && $assoc_vars[$appointed_to]['value'] == '2') {
                        // If the appointment of the document is to several specific employees
                        // and there are no employees selected
                        if (empty($assoc_vars[$attention_id]['value']) || !is_array($assoc_vars[$attention_id]['value']) || count(array_filter($assoc_vars[$attention_id]['value'])) < 1 || empty($assoc_vars[$attention_name]['value']) || !is_array($assoc_vars[$attention_name]['value']) || count(array_filter($assoc_vars[$attention_name]['value'])) < 1) {
                            // Raise an error that: atleast one employee should be selected
                            $messages->setError(sprintf($this->i18n('error_document_order_appointed_to_specific_employees'), $assoc_vars[$attention_name]['label']));
                        }
                    } elseif (empty($assoc_vars[$appointed_to]['value'])) {
                        // If no option is selected for the appointment, then raise an error to select one
                        $messages->setError($this->i18n('error_document_order_appointed_to_please_select'));
                    }
                }
            } else {
                // Set error
                $messages->setError(sprintf($this->i18n('error_validatedocumentorder_settings_missing'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            }

            // If there are any error messages
            if ($messages->getErrors()) {
                // Fail the automation (this will prevent the status change)
                return false;
            }
        }

        return true;
    }

    /**
     * Publish an Order type document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function publishDocumentOrder($params) {
        // Check the conditions to execute the automation:
        //   if the current action is setstatus
        //   and there is a setstatus parameter into the settings of this automation
        //   and the requested substatus is the one set into the settings
        if ($this->registry['action'] == 'setstatus') {
            // Get the automation settings
            $settings = $this->settings;

            // Set the required settings
            $required_settings = array(
                'substatus',
                'new_substatus',
                'pattern_id',
                'announcement_type_id',
                'announcement_category_type_id',
                'announcement_priority',
                'announcement_default_validity_term',
                'appointed_to',
                'attention_id'
            );

            // Prepare some basics
            $registry = &$this->registry;
            $messages = &$registry['messages'];

            // If all required settings are set
            if (count(array_intersect(array_keys(array_filter($settings)), $required_settings)) == count($required_settings)) {
                if ($registry['request']->get('substatus') == $settings['substatus']) {
                    // Get the document model
                    $document = $params['model'];
                    // Unsanitize the model if necessary
                    if ($document->isSanitized()) {
                        $document->unsanitize();
                    }
                    // Get the document vars as associative array
                    $document->getAssocVars();
                    $assoc_vars = $document->get('assoc_vars');

                    // If all required fields exist
                    if (isset($assoc_vars[$settings['appointed_to']]) && isset($assoc_vars[$settings['attention_id']])) {
                        // Get the messages before the automation
                        $messages_before_automation = $messages->getMessages();
                        $messages->unset_vars('messages');

                        ///////////////////////
                        // Generate PDF file //
                        ///////////////////////
                        // Prepare the document extender
                        $patterns_vars = $document->getPatternsVars();
                        $document->extender = new Extender();
                        $document->extender->model_lang = $document->get('model_lang');
                        $document->extender->module = $document;
                        foreach ($patterns_vars as $key => $value) {
                            $document->extender->add($key, $value);
                        }
                        // If the file is successfully generated
                        if ($file_id = $document->generatePDF(false, false, $settings['pattern_id'])) {
                            // Make history for the generated file
                            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                            Documents_History::saveData($registry, array('model' => $document,
                                'action_type' => 'generate',
                                'pattern' => $settings['pattern_id'],
                                'generated_file' => $file_id));

                            // Set the id of the new file into the document model
                            $document->set('file_id', $file_id, true);

                            // Get the messages for the generated file
                            $messages_generate_file = $messages->getMessages();
                            $messages->unset_vars('messages');

                            // Get the database object
                            $db = &$registry['db'];

                            ///////////////////////
                            // Start transaction //
                            ///////////////////////
                            $db->StartTrans();

                            //////////////////////
                            // Add announcement //
                            //////////////////////
                            // Prepare the announcement and it's data
                            require_once PH_MODULES_DIR . 'announcements/models/announcements.factory.php';
                            // Load the lang file for announcements
                            $lang_file = PH_MODULES_DIR . 'announcements/i18n/' . $registry['lang'] . '/announcements.ini';
                            $registry['translater']->loadFile($lang_file);
                            $announcement = new Announcement($registry);
                            $announcement->set('id', '', true);
                            $announcement->set('type', $settings['announcement_type_id'], true);
                            $announcement->set('category', $settings['announcement_category_type_id'], true);
                            $announcement->set('priority', $settings['announcement_priority'], true);
                            $announcement->set('subject', $document->get('name'), true);
                            $announcement->set('content', $this->i18n('message_announcement_order_content'), true);
                            // Get the validity term from the document or by default from the settings
                            $announcement->set('validity_term', ($document->get('validity_term') ? : $settings['announcement_default_validity_term']), true);
                            // Prepare the users to be assigned
                            $users_assignments = array();
                            if (!empty($assoc_vars[$settings['appointed_to']]['value'])) {
                                if ($assoc_vars[$settings['appointed_to']]['value'] == '1') {
                                    // Prepare all users
                                    $query = 'SELECT `id`' . "\n" .
                                        '  FROM `' . DB_TABLE_USERS . '`' . "\n" .
                                        '  WHERE `active` = \'1\'' . "\n" .
                                        '    AND `is_portal` = \'0\'' . "\n" .
                                        '    AND `deleted_by` = \'0\'';
                                    $users_assignments = $db->getCol($query);
                                } elseif ($assoc_vars[$settings['appointed_to']]['value'] == '2') {
                                    // Prepare specific users
                                    $users_assignments = $assoc_vars[$settings['attention_id']]['value'];
                                }
                            }
                            $announcement->set('assignments_type', 'Users', true);
                            $announcement->set('users_assignments', $users_assignments, true);
                            // If the announcement is added successfully
                            if ($announcement_saved = $announcement->save()) {
                                ///////////////////////////////////////////////////////////
                                // Attach the file from the document to the announcement //
                                ///////////////////////////////////////////////////////////
                                // Prepare the file to be attached
                                require_once PH_MODULES_DIR . 'announcements/models/announcements.factory.php';
                                $announcement_attachment_filters = array('where' => array('f.id = \'' . $file_id . '\''));
                                $announcement_attachment = Files::searchOne($registry, $announcement_attachment_filters);
                                $announcement_attachment->set('id', '', true);
                                $announcement_attachment->set('model', $announcement->modelName, true);
                                $announcement_attachment->set('model_id', $announcement->get('id'), true);
                                $announcement_attachment->set('origin', 'attached', true);
                                // If the file is attached successfully
                                if ($announcement_attachment->save()) {
                                    // Get the messages for the added announcement
                                    $messages_add_announcement = $messages->getMessages();
                                    $messages->unset_vars('messages');

                                    ///////////////////////////////////////
                                    // Change the status of the document //
                                    ///////////////////////////////////////
                                    // Prepare the old model
                                    $document_filters = array('where' => array('d.id = \'' . $document->get('id') . '\''),
                                        'model_lang' => $document->get('model_lang'));
                                    $old_document = Documents::searchOne($registry, $document_filters);

                                    // Prepare the new substatus
                                    $document->set('substatus', $settings['new_substatus'], true);

                                    // If the status of the document is changed successfully
                                    if ($document_status_changed = $document->setStatus()) {
                                        // Make history for the document status change
                                        $document_filters = array('where' => array('d.id = ' . $document->get('id')),
                                            'model_lang' => $document->get('model_lang'),
                                            'skip_assignments' => true,
                                            'skip_permissions_check' => true);
                                        $new_document = Documents::searchOne($registry, $document_filters);
                                        $audit_parent = Documents_History::saveData($registry, array('model' => $document,
                                                'action_type' => 'status',
                                                'new_model' => $new_document,
                                                'old_model' => $old_document));

                                        /////////////////////////////////////////////
                                        // Send notification for the status change //
                                        /////////////////////////////////////////////
                                        // This is the same as the sendStatusNotification() method of the documents controller
                                        $template = 'document_status';
                                        $not_users = Users::getUsersNoSend($registry, $template);
                                        $query = 'SELECT DISTINCT(u2.id),' . "\n" .
                                            '  u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname,' . "\n" .
                                            '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name ' . "\n" .
                                            'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                                            'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                                            '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_document->get('model_lang') . '")' . "\n" .
                                            'JOIN ' . DB_TABLE_DOCUMENTS_ASSIGNMENTS . ' AS da' . "\n" .
                                            '  ON (da.parent_id=' . $new_document->get('id') . ' AND u2.id=da.assigned_to)' . "\n" .
                                            'WHERE u2.active=1 AND u2.deleted_by=0';

                                        $records = $db->GetAll($query);

                                        //prepare audit data
                                        $registry['request']->set('audit', $audit_parent, 'all', true);
                                        $registry['request']->set('model_type', $new_document->get('type'), 'all', true);
                                        require_once PH_MODULES_DIR . 'documents/viewers/documents.audit.viewer.php';
                                        $configViewer = new Documents_Audit_Viewer($registry);
                                        $configViewer->templatesDir = $registry['theme']->templatesDir;
                                        $configViewer->setTemplate('_audit_email.html');

                                        //prepare the title for the audit table
                                        $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_document->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_document->get('modified'))));
                                        $configViewer->data['audit_title'] = $audit_title;
                                        $configViewer->prepare();
                                        $audit = $configViewer->data['audit'];
                                        $statuses = array();
                                        foreach ($audit['vars'] as $var) {
                                            $statuses[$var['field_name']] = $var['label'];
                                            $statuses['old_' . $var['field_name']] = $var['old_value'];
                                        }
                                        $audit_data = $configViewer->fetch();
                                        // CHECK if there is audit data and compare the settings for each user from personal settings table
                                        $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
                                        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
                                        $users_send_always = Users::getUsersSendSettings($registry, 'document_edit_send_when_audit', 'emails');

                                        $document_view_url = sprintf('%s/index.php?%s=documents&documents=view&view=%d', $registry['config']->getParam('crontab', 'base_host'), $registry['module_param'], $new_document->get('id'));
                                        $add_comment_url = sprintf('%s/index.php?%s=documents&documents=communications&communications=%d&communication_type=comments#comments_add_form', $registry['config']->getParam('crontab', 'base_host'), $registry['module_param'], $new_document->get('id'));

                                        $sent_to = array();
                                        foreach ($records as $record) {
                                            //check if the assignee want to receive email
                                            if ($record['assignment_email']) {
                                                $send = false;
                                                if (in_array($record['id'], $not_users) || $record['id'] == $registry['originalUser']->get('id')) {
                                                    //the user does not want to receive notifications when the document is edited
                                                    continue;
                                                }
                                                if (empty($audit_data) && in_array($record['id'], $users_send_always)) {
                                                    $send = true;
                                                } else if (!empty($audit_data)) {
                                                    $send = true;
                                                }
                                                if ($send) {
                                                    $mailer = new Mailer($registry, $template);
                                                    $mailer->placeholder->add('document_id', $new_document->get('id'));
                                                    $mailer->placeholder->add('document_name', $new_document->get('name'));
                                                    $mailer->placeholder->add('document_type', $new_document->get('type_name'));
                                                    $mailer->placeholder->add('document_added_by', $new_document->get('added_by_name'));
                                                    $mailer->placeholder->add('document_num', $new_document->getDocFullNum());
                                                    $mailer->placeholder->add('document_info', $audit_data);
                                                    $mailer->placeholder->add('customer_name', $new_document->get('customer_name'));
                                                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                                                    $mailer->placeholder->add('user_name', $record['assignment_name']);
                                                    $mailer->placeholder->add('last_audit', $audit_data);
                                                    $mailer->placeholder->add('document_view_url', $document_view_url);
                                                    $mailer->placeholder->add('document_add_comment_url', $add_comment_url);
                                                    $mailer->placeholder->add('modified_by_name', $new_document->get('modified_by_name'));
                                                    if (empty($statuses['status'])) {
                                                        $mailer->placeholder->add('old_status', $this->i18n('documents_status_' . $new_document->get('status')));
                                                        $mailer->placeholder->add('status', $this->i18n('documents_status_' . $new_document->get('status')));
                                                    } else {
                                                        $mailer->placeholder->add('old_status', $statuses['old_status']);
                                                        $mailer->placeholder->add('status', $statuses['status']);
                                                    }
                                                    if (isset($statuses['old_substatus']) && $statuses['old_substatus'] != '-') {
                                                        $mailer->placeholder->add('old_substatus', ' (' . $statuses['old_substatus'] . ')');
                                                    }
                                                    if (isset($statuses['substatus']) && $statuses['substatus'] != '-') {
                                                        $mailer->placeholder->add('substatus', ' (' . $statuses['substatus'] . ')');
                                                    }
                                                    $mailer->template['model_name'] = $new_document->modelName;
                                                    $mailer->template['model_id'] = $new_document->get('id');

                                                    //send email
                                                    $result = $mailer->send();
                                                    if (!@in_array($record['assignment_email'], $result['erred'])) {
                                                        $sent_to[] = $record['assignment_name'];
                                                    } else {

                                                    }
                                                }
                                            }
                                        }
                                        if (count($sent_to)) {
                                            $notify_for = $this->i18n('documents_' . $template . '_notify', array($new_document->getModelTypeName()));
                                            if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                                                $messages->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
                                            } else {
                                                $messages->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
                                            }
                                        }

                                        // Get the messages for the changed document status
                                        $messages_changed_document_status = $messages->getMessages();
                                        $messages->unset_vars('messages');
                                        /////////////////////////////////////////////////////
                                        // END OF: Send notification for the status change //
                                        /////////////////////////////////////////////////////
                                    } else {
                                        // Fail the transaction
                                        $db->FailTrans();
                                    }
                                    ///////////////////////////////////////////////
                                    // END OF: Change the status of the document //
                                    ///////////////////////////////////////////////
                                } else {
                                    // Fail the transaction
                                    $db->FailTrans();

                                    // Message: failed to attach the file to the announcement
                                    $messages->setError($this->i18n('error_announcement_attachment_failed'));
                                }
                                ///////////////////////////////////////////////////////////////////
                                // END OF: Attach the file from the document to the announcement //
                                ///////////////////////////////////////////////////////////////////
                            } else {
                                // Fail the transaction
                                $db->FailTrans();
                            }
                            //////////////////////////////
                            // END OF: Add announcement //
                            //////////////////////////////
                            // If there are any errors
                            if ($messages->getErrors()) {
                                // Fail the transaction
                                $db->FailTrans();
                            }

                            // If the transaction has failed
                            if ($db->HasFailedTrans()) {
                                // Messages: failed to add the announcement and failed to change the status of the document
                                $messages->setError($this->i18n('error_announcement_add_failed'));
                                $messages->setError(sprintf($this->i18n('error_documents_status_failed'), $document->get('type_name')));
                            }

                            // Complete the transaction
                            $db->CompleteTrans();
                            ///////////////////////////////
                            // END OF: Start transaction //
                            ///////////////////////////////
                        } else {
                            // Messages: failed to generate the file, failed to add the announcement and failed to change the status
                            $messages->setError($this->i18n('error_documents_generate_document'));
                            $messages->setError($this->i18n('error_announcement_add_failed'));
                            $messages->setError(sprintf($this->i18n('error_documents_status_failed'), $document->get('type_name')));
                        }
                        ///////////////////////////////
                        // END OF: Generate PDF file //
                        ///////////////////////////////
                        // Get all other messages
                        $messages_other = $messages->getMessages();
                        $messages->unset_vars('messages');

                        /////////////////////////////////////
                        // Add messages in a correct order //
                        /////////////////////////////////////
                        // Messages before automation
                        if (!empty($messages_before_automation) && is_array($messages_before_automation)) {
                            foreach ($messages_before_automation as $msg) {
                                $messages->setMessage($msg);
                            }
                        }

                        // Messages for the generated file
                        if (isset($file_id) && $file_id) {
                            // Message: file generated successfully
                            $messages->setMessage($this->i18n('message_documents_generate_success'));
                        }
                        if (!empty($messages_generate_file) && is_array($messages_generate_file)) {
                            foreach ($messages_generate_file as $msg) {
                                $messages->setMessage($msg);
                            }
                        }

                        // If there are no errors (i.e. the transaction is not failed)
                        if (!$messages->getErrors()) {
                            // Messages for the added announcement
                            if (isset($announcement_saved) && $announcement_saved) {
                                // Message: successfully added announcement
                                $messages->setMessage($this->i18n('message_announcement_add_success'));
                            }
                            if (!empty($messages_add_announcement) && is_array($messages_add_announcement)) {
                                foreach ($messages_add_announcement as $msg) {
                                    $messages->setMessage($msg);
                                }
                            }

                            // Messages for the changed document status
                            if (isset($document_status_changed) && $document_status_changed) {
                                // Message: successfully changed the status of the document
                                $messages->setMessage(sprintf($this->i18n('message_documents_status_success'), $document->get('type_name')));
                            }
                            if (!empty($messages_changed_document_status) && is_array($messages_changed_document_status)) {
                                foreach ($messages_changed_document_status as $msg) {
                                    $messages->setMessage($msg);
                                }
                            }

                            // Other messages (if any)
                            if (!empty($messages_other) && is_array($messages_other)) {
                                foreach ($messages_other as $msg) {
                                    $messages->setMessage($msg);
                                }
                            }
                        }
                        /////////////////////////////////////////////
                        // END OF: Add messages in a correct order //
                        /////////////////////////////////////////////
                    } else {
                        // Set error
                        $messages->setError(sprintf($this->i18n('error_publishdocumentorder_fields_missing'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
                    }
                }
            } else {
                // Set error
                $messages->setError(sprintf($this->i18n('error_publishdocumentorder_settings_missing'), (empty($params['name']) ? '' : ' "' . $params['name'] . '"')));
            }

            // Insert the messages into the session
            $messages->insertInSession($registry);

            // If there are any errors
            if ($messages->getErrors()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Custom validation when adding a "Leave request" type document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function validateAddLeaveRequest($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $request = &$registry['request'];
        $action = $registry['action'];

        // Automation should execute only: if we add/edit data and the request is POST
        if (!(($action == 'add' || $action == 'edit') && $request->isPost())) {
            return true;
        }

        // Prepare the messages object
        $messages = &$registry['messages'];

        // Get all request vars
        $request_vars = $request->getAll();

        // Get the current document model
        $document = $params['model'];

        // Get the additional vars of the document
        $vars = $document->getAssocVars();

        // Validation: year is required if this is a paid leave request
        $invalid_year = false;
        if ($request_vars['plr_leave_type'] == 'paid' && empty($request_vars['plr_leave_year'])) {
            $messages->setError(sprintf($this->i18n('error_automation_bgservice_empty_field'), $vars['plr_leave_year']['label']), 'plr_leave_year');
            $invalid_year = true;
        }

        // Validation: days count can not be less than 1
        $invalid_days = false;
        if (!empty($request_vars['plr_leave_days']) && Validator::isValidNumber($request_vars['plr_leave_days']) && !filter_var($request_vars['plr_leave_days'], FILTER_VALIDATE_INT, array('options' => array('min_range' => 1)))) {
            $messages->setError(sprintf($this->i18n('error_automation_bgservice_validate_add_leave_request_negative_days'), $vars['plr_leave_days']['label']), 'plr_leave_days');
            $invalid_days = true;
        }

        // It this is a paid leave request
        if (!$invalid_year && $request_vars['plr_leave_type'] == 'paid') {
            // Get the count of the free paid days for the selected year and customer
            $report_name = 'hr_employee_file';
            require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
            require_once PH_MODULES_DIR . 'reports/plugins/' . $report_name . '/custom.report.query.php';
            $paid_days = 0;
            $reports_results = array();
            $report = Reports::getReports(
                $registry,
                array(
                    'name' => $report_name,
                    'sanitize' => true
                )
            );
            if (isset($report[0])) {
                $report = $report[0];
                Reports::getReportSettings($registry, $report->get('type'));
                $filters = array(
                    'employee' => $document->get('customer'),
                    'years' => $request_vars['plr_leave_year']
                );
                $report_results = HR_Employee_File::buildQuery($registry, $filters);
                $paid_days = $report_results['free_days_left'];
            }

            // If there are no paid days for the selected year
            if ($paid_days < 1) {
                // Error: no paid days for the selected year
                $messages->setError(sprintf($this->i18n('error_automation_bgservice_validate_add_leave_request_empty_year'), $request_vars['plr_leave_year']), 'plr_leave_year');
            } else {
                // If the selected days are more than the available ones for the selected year
                if (!$invalid_days && $request_vars['plr_leave_days'] > $paid_days) {
                    // Error: selected days overflow
                    $messages->setError(sprintf($this->i18n('error_automation_bgservice_validate_add_leave_request_days_overflow_year'), $vars['plr_leave_days']['label'], $request_vars['plr_leave_days'], $request_vars['plr_leave_year'], $paid_days), 'plr_leave_days');
                }
            }
        }

        // If the days and the period are filled
        if (!$invalid_days && !empty($request_vars['plr_leave_days']) && !empty($request_vars['plr_leave_start_date']) && !empty($request_vars['plr_leave_finish_date'])) {
            // If the days are NOT equal to the working days count from the selected period
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $period_working_days = Calendars_Calendar::getWorkingDays($registry, $request_vars['plr_leave_start_date'], $request_vars['plr_leave_finish_date']);
            if ($request_vars['plr_leave_days'] != $period_working_days) {
                // Error: selected days are different than the period days
                $messages->setError(sprintf($this->i18n('error_automation_bgservice_validate_add_leave_request_days_overflow_period'), $vars['plr_leave_days']['label'], $request_vars['plr_leave_days'], $period_working_days, $vars['plr_leave_start_date']['label'], General::strftime('%d.%m.%Y', $request_vars['plr_leave_start_date']), $vars['plr_leave_finish_date']['label'], General::strftime('%d.%m.%Y', $request_vars['plr_leave_finish_date'])), array('plr_leave_days',
                    'plr_leave_start_date',
                    'plr_leave_finish_date'));
            }
        }

        // If there are any errors
        if ($messages->getErrors()) {
            // Cancel this action
            return false;
        }

        return true;
    }

    /**
     * Add "Leave" type event and reminders when approving some "Leave request" type document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function notifyLeaveRequest($params) {
        // Execute the automation only if changing the status of the document or changing assignments
        if (!in_array($this->registry['action'], array('setstatus', 'multistatus', 'assign'))) {
            return true;
        }

        // Require settings
        $required_settings = array('document_status_approved', 'document_substatus_approved', 'document_status_unapproved',
            'document_substatus_unapproved', 'field_start_date', 'field_end_date', 'event_type_keyword', 'reminder_type',
            'reminder_offset_days', 'reminder_offset_hours', 'reminder_offset');
        foreach ($required_settings as $setting_name) {
            if (!isset($this->settings[$setting_name]) || $this->settings[$setting_name] == '') {
                $this->registry['messages']->setError($this->i18n('error_automation_notify_leave_request_settings'));
                $this->registry['messages']->insertInSession($this->registry);
                return true;
            }
        }

        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $messages = &$registry['messages'];
        $msg = array();
        $settings = $this->settings;
        $document = $params['model'];

        // Unsanitize the document model
        $document_sanitize_after = false;
        if ($document->isSanitized()) {
            $document->unsanitize();
            $document_sanitize_after = true;
        }

        // Start transaction
        $db->StartTrans();

        // Include some required classes
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        require_once PH_MODULES_DIR . 'events/models/events.history.php';
        require_once PH_MODULES_DIR . 'events/models/events.audit.php';
        require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

        // Load the lang file for events
        $lang_file = PH_MODULES_DIR . 'events/i18n/' . $registry['lang'] . '/events.ini';
        $registry['translater']->loadFile($lang_file);

        // Set flag in registry, so that the assignments are derived from the DB
        $registry->set('getAssignments', true, true);

        // Get the "Leave" event type
        $filters_event_type = array(
            'model_lang' => $registry['lang'],
            'where' => array(
                '`et`.`keyword`    = \'' . $settings['event_type_keyword'] . '\'',
                '`et`.`deleted_by` = \'0\'',
                '`et`.`active`     = \'1\''
            ),
            'sanitize' => true
        );
        $event_type = Events_Types::searchOne($registry, $filters_event_type);

        // If no event type
        if (!$event_type) {
            // Error: no event type
            $messages->setError($this->i18n('error_automation_notify_leave_request_event_type'));
        } else {
            // Get all current assignments of the document
            $assignments = array();
            $assignments += $document->getAssignments();
            $assignments += $document->getAssignments('responsible');
            $assignments += $document->getAssignments('observer');
            $assignments += $document->getAssignments('decision');

            // Get the employee of the "Leave request" document
            $filters_employee = array(
                'where' => array('`c`.`id` = \'' . $document->get('customer') . '\''),
                'model_lang' => $registry['lang'],
                'sanitize' => true
            );
            $employee = Customers::searchOne($registry, $filters_employee);

            // Prepare the start and the end date for the event
            $event_start_date = $document->getAdditionalVarValue($settings['field_start_date']);
            $event_start = $event_start_date . ' 00:00:00';
            $event_end_date = $document->getAdditionalVarValue($settings['field_end_date']);
            // if end date is not filled in, assume that it is the same as start date
            if (!$event_end_date) {
                $event_end_date = $event_start_date;
            }
            $event_end = $event_end_date . ' 00:00:00';
            $today_date = General::strftime($this->i18n('date_iso_short'));

            // Prepare the event status
            $event_status = 'planning';
            if ($document->get('status') == $settings['document_status_unapproved'] && $document->get('substatus') == $settings['document_substatus_unapproved']) {
                $event_status = 'unstarted';
            } elseif ($event_start_date <= $today_date && $today_date <= $event_end_date) {
                $event_status = 'progress';
            } elseif ($event_end_date < $today_date) {
                $event_status = 'finished';
            }

            // Check if there is already an event for this "Leave request" document
            $filters_event = array(
                'where' => array(
                    '`e`.`deleted_by`  = \'0\'',
                    '`e`.`active`      = \'1\'',
                    '`e`.`type`        = \'' . $event_type->get('id') . '\'',
                    '`e`.`customer`    = \'' . $employee->get('id') . '\'',
                    '`e`.`event_start` = \'' . $event_start . '\''
                ),
                'model_lang' => $registry['lang']
            );
            $event = Events::searchOne($registry, $filters_event);

            // If no event
            if (!$event) {
                // If we are currently changing the status of the document to "Approved"
                if (in_array($registry['action'], array('setstatus', 'multistatus')) && $document->get('status') == $settings['document_status_approved'] && $document->get('substatus') == $settings['document_substatus_approved']) {
                    // Prepare the "Leave" event
                    $event = Events::buildModel($registry);
                    $event->set('id', null, true);
                    $event->set('name', sprintf($this->i18n('automation_notify_leave_request_event_name'), trim($employee->get('name') . ' ' . $employee->get('lastname'))), true);
                    $event->set('customer', $employee->get('id'), true);
                    $event->set('type', $event_type->get('id'), true);
                    $event->set('event_start', $event_start, true);
                    $event->set('event_end', $event_end, true);
                    $event->set('allday_event', '1', true);
                    $event->set('status', $event_status, true);
                    $event->set('group', ($document->get('group') ? $document->get('group') : 1), true);

                    // Try to add the event
                    if ($event->save()) {
                        // Prepare a view URL for the event
                        $event_view_url = sprintf('%s?%s=events&amp;events=view&amp;view=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $event->get('id'));

                        // Message: add success
                        $msg[] = sprintf($this->i18n('message_automation_notify_leave_request_add_event_success'), $event_view_url, $event_type->get('name'));

                        // Write history
                        $filters_new_event = array(
                            'where' => array('`e`.`id` = \'' . $event->get('id') . '\''),
                            'model_lang' => $event->get('model_lang')
                        );
                        $new_event = Events::searchOne($registry, $filters_new_event);
                        $old_event = new Event($registry);
                        $old_event->sanitize();
                        Events_History::saveData(
                            $registry,
                            array(
                                'model' => $event,
                                'action_type' => 'add',
                                'new_model' => $new_event,
                                'old_model' => $old_event
                            )
                        );

                        // Try to set the relation between the document and the event
                        if ($new_event->updateDocumentRelatives(array($document->get('id')))) {
                            // Write history
                            Events_History::saveData(
                                $registry,
                                array(
                                    'model' => $event,
                                    'action_type' => 'relatives'
                                )
                            );
                        } else {
                            $messages->setError('error_automation_notify_leave_request_event_relatives_failed');
                        }

                        // Get the new event as current event
                        $event = clone $new_event;

                        // Prepare the old event (for the history)
                        $old_event = clone $event;
                        $old_event->sanitize();

                        // Prepare a list of assigned users for the event
                        $new_users = array();
                        foreach (array_keys($assignments) as $user_id) {
                            $new_users[$user_id] = array(
                                'participant_id' => $user_id,
                                'ownership' => 'other',
                                'access' => 'view');
                        }
                        $query = 'SELECT `id` FROM `' . DB_TABLE_USERS . '` WHERE `employee` = \'' . $employee->get('id') . '\'';
                        $leave_request_user_id = $db->GetOne($query);
                        $new_users[$leave_request_user_id] = array(
                            'participant_id' => $leave_request_user_id,
                            'ownership' => 'mine',
                            'access' => 'edit');

                        // Set the list of assigned users into the event model
                        $event->set('new_users', $new_users, true);

                        // Try to update the list of assigned users, without sending a notifications
                        if ($event->assign(false)) {
                            // Set that the user of the "Leave request" document has confirmed the event assignment
                            $query = 'UPDATE `' . DB_TABLE_EVENTS_ASSIGNMENTS . '`' . "\n" .
                                '  SET `user_status` = \'confirmed\',' . "\n" .
                                '    `status_date`   = NOW()' . "\n" .
                                '  WHERE `parent_id`        = \'' . $event->get('id') . '\'' . "\n" .
                                '    AND `participant_type` = \'user\'' . "\n" .
                                '    AND `participant_id`   = \'' . $leave_request_user_id . '\'';
                            $db->Execute($query);

                            // Message: assign success
                            $msg[] = sprintf($this->i18n('message_automation_notify_leave_request_event_assign_success'), $event_view_url, $event_type->get('name'));

                            // After the success message, add the messages that come from the assign() method
                            $assign_msgs = $messages->getMessages();
                            if (!empty($assign_msgs) && is_array($assign_msgs)) {
                                $messages->unset_vars('messages');
                                foreach ($assign_msgs as $assign_msg) {
                                    $msg[] = $assign_msg;
                                }
                            }

                            // Write history
                            $filters_new_event = array(
                                'where' => array('`e`.`id` = \'' . $event->get('id') . '\''),
                                'model_lang' => $event->get('model_lang')
                            );
                            $new_event = Events::searchOne($registry, $filters_new_event);
                            Events_History::saveData(
                                $registry,
                                array(
                                    'model' => $event,
                                    'action_type' => 'assign',
                                    'new_model' => $new_event,
                                    'old_model' => $old_event
                                )
                            );
                        } else {
                            // Error: assign failed
                            $messages->setError(sprintf($this->i18n('error_automation_notify_leave_request_event_assign_failed'), $event_type->get('name')));
                        }

                        // Unsanitize the event
                        $event_remind_sanitize_after = false;
                        if ($event->isSanitized()) {
                            $event->unsanitize();
                            $event_remind_sanitize_after = true;
                        }

                        // Prepare the request to be used for adding of reminders
                        $registry['request']->set('reminder_type', $settings['reminder_type'], true);
                        $registry['request']->set('selected_panel', 'offset', true);
                        $registry['request']->set('reminder_offset', $settings['reminder_offset'], true);
                        $registry['request']->set('reminder_offset_hours', $settings['reminder_offset_hours'], true);
                        $registry['request']->set('reminder_offset_days', $settings['reminder_offset_days'], true);

                        // Prepare the reminder message
                        $document_assoc_vars = $document->getAssocVars();
                        $reminder_custom_message = sprintf("%s \r\n%s: %s \r\n%s: %s", $event->get('name'), $document_assoc_vars[$settings['field_start_date']]['label'], General::strftime('%d.%m.%Y', $event_start_date), $document_assoc_vars[$settings['field_end_date']]['label'], General::strftime('%d.%m.%Y', $event_end_date));
                        $registry['request']->set('custom_message', $reminder_custom_message, true);

                        // Get the users for which the reminders should be added
                        $reminder_users = $document->getAssignments('decision');
                        $reminder_users = array_keys($reminder_users);
                        $reminder_users = array_merge($reminder_users, explode(',', $settings['reminder_users']));
                        $reminder_users = array_unique($reminder_users);

                        // Add reminders
                        if (count(array_filter($reminder_users)) > 0) {
                            foreach ($reminder_users as $reminder_user) {
                                $registry['request']->set('reminder_user_id', trim($reminder_user), 'all', true);
                                if (!$event->remind()) {
                                    $messages->setError(sprintf($this->i18n('error_automation_notify_leave_request_add_reminders_failed'), $event_type->get('name')));
                                    break;
                                }
                            }
                        }

                        // Sanitize the event
                        if ($event_remind_sanitize_after) {
                            $event->sanitize();
                        }
                    } else {
                        // Error: add event failed
                        $messages->setError($this->i18n('error_automation_notify_leave_request_add_event_failed'));
                    }
                }
            } else {
                // Prepare a view URL for the event
                $event_view_url = sprintf('%s?%s=events&amp;events=view&amp;view=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $event->get('id'));

                // Set the corresponding status for the event
                $event_status_changed = false;
                if ($event->get('status') != $event_status) {
                    $status_params = array(
                        'model_id' => $event->get('id'),
                        'module' => 'events',
                        'model' => $event,
                        'new_status' => $event_status,
                        'send_mail' => false
                    );
                    // Try to set the status
                    if ($this->status($status_params)) {
                        // Message: event status change success
                        $msg[] = sprintf($this->i18n('message_automation_notify_leave_request_event_status_success'), $event_view_url, $event_type->get('name'));
                        $event_status_changed = true;
                    } else {
                        // Error: event status change failed
                        $messages->setError(sprintf($this->i18n('error_automation_notify_leave_request_event_status_failed'), $event_view_url, $event_type->get('name')));
                    }
                }

                // If we are changing the assignments
                if ($registry['action'] == 'assign') {
                    // If the event status have been changed
                    if ($event_status_changed) {
                        // Get the event from the database
                        $filters_new_event = array(
                            'where' => array('`e`.`id` = \'' . $event->get('id') . '\''),
                            'model_lang' => $event->get('model_lang')
                        );
                        $new_event = Events::searchOne($registry, $filters_new_event);

                        // Set the new event as current event
                        $event = clone $new_event;
                    }

                    // Prepare the old event (for the history)
                    $old_event = clone $event;
                    $old_event->sanitize();

                    // Prepare the new list of assigned users for the event
                    $event->getAssignments();
                    $old_users = array();
                    foreach ($event->get('users_view') as $user_view) {
                        $old_users[$user_view['participant_id']] = $user_view;
                    }
                    foreach ($event->get('users_edit') as $user_edit) {
                        $old_users[$user_edit['participant_id']] = $user_edit;
                    }
                    $new_users = array();
                    foreach (array_keys($assignments) as $user_id) {
                        if (!array_key_exists($user_id, $old_users)) {
                            $new_users[$user_id] = array(
                                'participant_id' => $user_id,
                                'ownership' => 'other',
                                'access' => 'view'
                            );
                        } else {
                            $new_users[$user_id] = $old_users[$user_id];
                        }
                    }
                    $query = 'SELECT `id` FROM `' . DB_TABLE_USERS . '` WHERE `employee` = \'' . $employee->get('id') . '\'';
                    $leave_request_user_id = $db->GetOne($query);
                    if (!array_key_exists($leave_request_user_id, $old_users)) {
                        $new_users[$leave_request_user_id] = array(
                            'participant_id' => $leave_request_user_id,
                            'ownership' => 'mine',
                            'access' => 'edit'
                        );
                    } else {
                        $new_users[$leave_request_user_id] = $old_users[$leave_request_user_id];
                    }

                    // Set the new list of assigned users into the event model
                    $event->set('new_users', $new_users, true);

                    // Try to update the list of assigned users, without sending a notifications
                    if ($event->assign(false)) {
                        // Message: assign success
                        $msg[] = sprintf($this->i18n('message_automation_notify_leave_request_event_assign_success'), $event_view_url, $event_type->get('name'));

                        // After the success message, add the messages that comes from the assign() method
                        $assign_msgs = $messages->getMessages();
                        if (!empty($assign_msgs) && is_array($assign_msgs)) {
                            $messages->unset_vars('messages');
                            foreach ($assign_msgs as $assign_msg) {
                                $msg[] = $assign_msg;
                            }
                        }

                        // Write history
                        $filters_new_event = array(
                            'where' => array('`e`.`id` = \'' . $event->get('id') . '\''),
                            'model_lang' => $event->get('model_lang')
                        );
                        $new_event = Events::searchOne($registry, $filters_new_event);
                        Events_History::saveData(
                            $registry,
                            array(
                                'model' => $event,
                                'action_type' => 'assign',
                                'new_model' => $new_event,
                                'old_model' => $old_event
                            )
                        );
                    } else {
                        // Error: assign failed
                        $messages->setError(sprintf($this->i18n('error_automation_notify_leave_request_event_assign_failed'), $event_type->get('name')));
                    }
                }
            }
        }

        // Sanitize the document
        if ($document_sanitize_after) {
            $document->sanitize();
        }

        // Fail the transaction if there are any errors
        if ($messages->getErrors()) {
            $db->FailTrans();
        }

        // Get the result of the transaction
        $dbTransError = $db->HasFailedTrans();

        // Complete the transaction
        $db->CompleteTrans();

        // Load the automation messages if there are no errors
        if (!$dbTransError && count($msg) > 0) {
            foreach ($msg as $m) {
                $messages->setMessage($m);
            }
        }

        // Load all messages into the session
        $messages->insertInSession($registry);

        // The result of the transaction is the result of the automation
        return !$dbTransError;
    }

    /**
     * Crontab automation to change the status of a "Meeting protocol" document
     * and add timesheets for each participant
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function addMeetingProtocolTimesheet($params) {
        // Flag to check if the current automation is not a crontab automation
        $not_crontab = $this->metaData['automation_type'] != 'crontab';

        // Check the conditions to run the automation
        if ($not_crontab && $this->registry['request']->isPost() && $this->registry['action'] == 'setstatus' && isset($params['model']) || !$not_crontab) {

            // Prepare some basics
            $registry = &$this->registry;
            $db = &$registry['db'];
            $document = $params['model'];
            $settings = $this->settings;
            $messages = &$registry['messages'];

            // Check required settings
            $required_settings = array(
                'customer_type_employee',
                'cp_company',
                'timesheet_activity',
                'field_doc_meeting_start',
                'field_doc_meeting_finish',
                'field_doc_person_id',
                'field_cus_link_contact_persons_id'
            );
            // If settings failed
            if (count(array_filter(array_intersect_key($settings, array_flip($required_settings)))) != count($required_settings) || empty($params['new_status'])) {
                $err = sprintf($this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_settings_failed'), (empty($params['name']) ? '' : " \"{$params['name']}\""));
                // If the automation is not a crontab
                if ($not_crontab) {
                    // Set error message
                    $messages->setError($err);
                }
                $this->executionErrors[] = $err;
                $messages->unset_vars('messages');
                return false;
            }

            // If the automation is not a crontab
            // but the status is not changed to the new_status
            if ($not_crontab && !($params['model']->get('status') != $registry['request']->get('status') && $registry['request']->get('status') == $params['new_status'])) {
                // Just skip the automation check
                return true;
            }

            // If the automation is a crontab but the status change has failed
            if (!$not_crontab && !$this->status($params)) {
                // Set error
                $this->executionErrors[] = $this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_crontab_setstatus_failed');
                // Fail the automation
                return false;
            }

            // If the document model is sanitized
            if ($document->sanitized) {
                // Unsanitize the model
                $document->unsanitize();
                $sanitize_after = true;
            }

            // Get the additional vars of the document
            $vars = $document->getAssocVars();

            // Check required fields
            if (!isset($vars[$settings['field_doc_meeting_start']]) || !isset($vars[$settings['field_doc_meeting_finish']]) || !isset($vars[$settings['field_doc_person_id']])) {
                $err = sprintf($this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_fields_not_exists'), (empty($params['name']) ? '' : " \"{$params['name']}\""));
                if ($not_crontab) {
                    // Set error message
                    $messages->setError($err);
                }
                $this->executionErrors[] = $err;
                $messages->unset_vars('messages');
                // Fail the automation
                return false;
            }

            // Get the start and the end dates for the timesheet
            $start = $vars[$settings['field_doc_meeting_start']]['value'];
            $end = $vars[$settings['field_doc_meeting_finish']]['value'];

            // Array, containing the names of the users for which timesheets are added successfully
            $users_timesheets_success = array();

            // If both start and end dates have values
            if (!empty($start) && $start > '0000-00-00' && !empty($end) && $end > '0000-00-00') {
                // Get all the contact persons from the additional vars of the document
                $contact_persons = $vars[$settings['field_doc_person_id']]['value'];

                // Remove the empty values (if any)
                if (is_array($contact_persons)) {
                    $contact_persons = array_filter($contact_persons);
                }

                // If there are any contact persons
                if (!empty($contact_persons) && is_array($contact_persons)) {
                    // Get the model lang from the current document
                    $model_lang = $document->get('model_lang');

                    // Get the id of the additional var which contains the id of the contact person for each "Employee" type customer
                    $query = "
                        SELECT id
                          FROM " . DB_TABLE_FIELDS_META . "
                          WHERE model = 'Customer'
                            AND model_type = '{$settings['customer_type_employee']}'
                            AND name = '{$settings['field_cus_link_contact_persons_id']}'";
                    $field_link_contact_persons_id = $db->GetOne($query);

                    // Get the users and their offices for each BGService contact person
                    $query = 'SELECT `u`.`id` AS `idx`,' . "\n" .
                        '    `u`.`office` AS `office`,' . "\n" .
                        '    TRIM(CONCAT(`ui18n`.`firstname`, \' \', `ui18n`.`lastname`)) AS `name`' . "\n" .
                        '  FROM `' . DB_TABLE_CUSTOMERS . '` AS `c`' . "\n" .
                        '  JOIN `' . DB_TABLE_CUSTOMERS_CSTM . '` AS `cc`' . "\n" .
                        '    ON (`cc`.`model_id` = `c`.`id`' . "\n" .
                        '      AND `cc`.`var_id` = \'' . $field_link_contact_persons_id . '\'' . "\n" .
                        '      AND `cc`.`value`  IN (\'' . implode('\', \'', $contact_persons) . '\'))' . "\n" .
                        '  JOIN `' . DB_TABLE_CUSTOMERS . '` AS `ccp`' . "\n" .
                        '    ON (`ccp`.`subtype`      = \'contact\'' . "\n" .
                        '      AND `ccp`.`id`         = `cc`.`value`' . "\n" .
                        '      AND `ccp`.`active`     = \'1\'' . "\n" .
                        '      AND `ccp`.`deleted_by` = \'0\')' . "\n" .
                        '  JOIN `' . DB_TABLE_CUSTOMERS . '` AS `cbr`' . "\n" .
                        '    ON (`cbr`.`subtype`           = \'branch\'' . "\n" .
                        '      AND `cbr`.`id`              = `ccp`.`parent_customer`' . "\n" .
                        '      AND `cbr`.`parent_customer` = \'' . $settings['cp_company'] . '\'' . "\n" .
                        '      AND `ccp`.`active`          = \'1\'' . "\n" .
                        '      AND `ccp`.`deleted_by`      = \'0\')' . "\n" .
                        '  JOIN `' . DB_TABLE_USERS . '` AS `u`' . "\n" .
                        '    ON (`u`.`employee`     = `c`.`id`' . "\n" .
                        '      AND `u`.`active`     = \'1\'' . "\n" .
                        '      AND `u`.`deleted_by` = \'0\'' . "\n" .
                        '      AND `u`.`is_portal`  = \'0\')' . "\n" .
                        '  JOIN `' . DB_TABLE_USERS_I18N . '` AS `ui18n`' . "\n" .
                        '    ON (`ui18n`.`parent_id` = `u`.`id`' . "\n" .
                        '      AND `ui18n`.`lang`    = \'' . $model_lang . '\')' . "\n" .
                        '  WHERE `c`.`type`       = \'' . $settings['customer_type_employee'] . '\'' . "\n" .
                        '    AND `c`.`active`     = \'1\'' . "\n" .
                        '    AND `c`.`deleted_by` = \'0\'';
                    $users_offices = $db->GetAssoc($query);

                    // If any users are found
                    if (!empty($users_offices)) {
                        // Include the necessary files for adding the timesheets
                        require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheets.factory.php';
                        $lang_file = PH_MODULES_DIR . 'tasks/i18n/' . $registry['lang'] . '/tasks.ini';
                        $registry['translater']->loadFile($lang_file);

                        // If the current automations is not a crontab automation
                        if ($not_crontab) {
                            // Start a transaction
                            $db->StartTrans();
                        }

                        // Go through each user
                        foreach ($users_offices as $user_id => $user) {
                            // Prepare an array with parameters for the timesheet
                            $p = array();

                            // Get the id of the system task, which should already be created for the current document
                            $p['model_id'] = Documents::getSystemTask($registry, $document->get('id'));

                            // Prepare some standard parameters
                            $p['resource'] = 'human';
                            $p['parent_module'] = 'documents';
                            $p['period_type'] = 'dates';

                            // Set the start and the end date of the timesheet
                            $p['startperiod_dates'] = $start;
                            $p['endperiod_dates'] = $end;

                            // Set the user and the office
                            $p['user_id'] = $user_id;
                            $p['office'] = $user['office'];

                            // Set: activity, subject and content
                            $p['activity'] = $settings['timesheet_activity'];
                            $p['subject'] = '';
                            $p['content'] = $this->i18n('automation_bgservice_add_meeting_protocol_timesheet_content');

                            // Build a new timesheet model
                            $timesheet = new Tasks_Timesheet($registry, $p);

                            // Try to save (add) the timesheet
                            // and what ever happens, just continue
                            $add_timesheet_success = false;
                            try {
                                if ($timesheet->save() && $not_crontab) {
                                    // If the timesheet is added successfully and the current automation is not a crontab automation
                                    // set flag that the timesheet is addes successfully and collect the name of the current user, for which the timesheet is added
                                    $add_timesheet_success = true;
                                    $users_timesheets_success[] = $user['name'];
                                }
                                if ($timesheet->get('id')) {
                                    $timesheet->saveHistory($document);
                                }
                            } catch (Exception $e) {

                            }

                            // If the current automation is not a crontab automation
                            // and the current timesheet has failed to add
                            if ($not_crontab && !$add_timesheet_success) {
                                // Fail the transaction
                                $db->FailTrans();

                                // Stop adding timesheets
                                break;
                            }
                        }
                    } else {
                        $err = $this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_add_failed_contact_persons_users');
                        if ($not_crontab) {
                            // If the current automation is not a crontab automation - error
                            $messages->setError($err);
                        }
                        $this->executionErrors[] = $err;
                    }
                } else {
                    $err = $this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_add_failed_contact_persons');
                    if ($not_crontab) {
                        // If the current automation is not a crontab automation - error
                        $messages->setError($err);
                    }
                    $this->executionErrors[] = $err;
                }
            } else {
                $err = $this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_add_failed_start_end');
                if ($not_crontab) {
                    // If the current automation is not a crontab automation - error
                    $messages->setError($err);
                }
                $this->executionErrors[] = $err;
            }

            // If the current automation is not a crontab automation
            if ($not_crontab) {
                // If there are any error messages
                if ($messages->getErrors()) {
                    // Fail the transaction
                    $db->FailTrans();
                }

                // Check if the transaction has failed (i.e. check if there are any errors)
                $error = $db->HasFailedTrans();

                // Complete the transaction
                $db->CompleteTrans();

                // If there are any errors
                if ($error) {
                    // Add a main error message
                    $messages->setError($this->i18n('error_automation_bgservice_add_meeting_protocol_timesheet_add_failed'));
                } else if (!empty($users_timesheets_success)) {
                    // If there are no errors and the timesheets have been added successfully
                    // Message: timesheets added successfully for users
                    $messages->setMessage(sprintf($this->i18n('message_automation_bgservice_add_meeting_protocol_timesheet_add_success_for_user'), implode(', ', $users_timesheets_success)));
                }

                // Insert the messages into the session
                $messages->insertInSession($registry);
            }

            // If the document model was sanitized before the automation starts
            if (!empty($sanitize_after)) {
                // Sanitize the model
                $document->sanitize();
            }

            // If the automation is a crontab automation, always return true (i.e. the automation should always be successful)
            // because if the status is changed successfully, but the adding of timesheet has failed,
            // this should not mean that the status change has failed
            // Else, if the automation is not a crontab automation, then return true if no errors or false - if any errors appeared
            if (empty($error)) {
                return true;
            } else {
                $messages->unset_vars('messages');
                return false;
            }
        }

        return true;
    }

    /**
     * Assign users and set a project to "Request (client)" type documents after adding them
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function afterAddRequestClient($params) {
        // Execute this automation only if the action is: add
        if (!in_array($this->registry['action'], array('add', 'adds'))) {
            return true;
        }

        // Prepare some basics
        $result = true;
        $registry = &$this->registry;
        $db = &$registry['db'];

        // Get the current document model from the database
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters_document = array(
            'where' => array('d.id = ' . $params['model_id']),
            'model_lang' => $params['model']->get('model_lang'),
            'sanitize' => false
        );
        $document = Documents::searchOne($registry, $filters_document);

        // Make a copy of the model (for history purposes)
        $old_document = clone $document;

        // Get the document's customer id
        $customer_id = $document->get('customer');

        // Get current assigned observers of the document
        $assigned_observers = $document->getAssignments('observer');
        $assigned_observers = array_keys($assigned_observers);

        // Get the new observers which should be added
        $query = "
            SELECT `id`
              FROM `" . DB_TABLE_USERS . "`
              WHERE `deleted_by`       = '0'
                AND `active`           = '1'
                AND `hidden`           = '0'
                AND `is_portal`        = '1'
                AND `default_customer` = '{$customer_id}'" .
            (!empty($assigned_observers) ? "
                AND `id`               NOT IN ('" . implode("', '", $assigned_observers) . "')" : '');
        $new_observers = $db->GetCol($query);

        // If there are any new observers
        if (!empty($new_observers)) {
            // Add the new observers list to the old observers list
            $assigned_observers = array_merge($assigned_observers, $new_observers);

            // Update the observers
            $assignments_params = array(
                'model_id' => $params['model_id'],
                'module' => $params['module'],
                'model' => $document,
                'new_assign_observer' => implode(',', $assigned_observers)
            );
            // If the assignments failed
            if (!$this->assign($assignments_params)) {
                // The automation result is false
                $result = false;
            }
        }

        // If the required setting project_type_support_nzoom is set then we can try to set a project for the document
        if (isset($this->settings['project_type_support_nzoom'])) {
            // Get the projects which can be used for this document (they should not be more than one)
            $query = "
                SELECT `id`
                  FROM `" . DB_TABLE_PROJECTS . "`
                  WHERE `deleted_by`       = '0'
                    AND `active`           = '1'
                    AND `type`             = '{$this->settings['project_type_support_nzoom']}'
                    AND `customer`         = '{$customer_id}'
                    AND DATE(`date_start`) <= CURDATE()
                    AND CURDATE()          <= DATE(`date_end`)";
            $project_id = $db->GetCol($query);

            // If there is a project and there is only one project
            if (!empty($project_id) && count($project_id) == 1) {
                // Get the project id
                $project_id = reset($project_id);

                // Set the project id into the document
                $document->set('project', $project_id, true);

                // Try to save the document
                if ($document->save()) {
                    // Write history
                    require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
                    $new_document_filters = array(
                        'where' => array('d.id = \'' . $document->get('id') . '\''),
                        'model_lang' => $document->get('model_lang')
                    );
                    $new_document = Documents::searchOne($registry, $new_document_filters);
                    Documents_History::saveData(
                        $registry,
                        array(
                            'model' => $new_document,
                            'action_type' => 'edit',
                            'new_model' => $new_document,
                            'old_model' => $old_document
                        )
                    );
                } else {
                    // The automation result is false
                    $result = false;
                }
            } else {
                // The automation result is false
                $result = false;
            }
        } else {
            // The automation result is false
            $result = false;
        }

        return $result;
    }

    /**
     * Change request documents' deadlines according to the settings in the db
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function changeRequestsDeadlines($params) {
        // Prepare some basics
        $result = true;
        $registry = &$this->registry;
        $db = &$registry['db'];

        $change_params = array(
            'working_day_start' => $this->settings['working_day_start'],
            'working_day_end' => $this->settings['working_day_end'],
            'type_deadline' => $this->settings['type_deadline'],
            'hours_offset' => $this->settings['hours_offset']
        );
        $new_deadline_sec = $this->calculateRequestDeadline($registry, $change_params);

        // Get the current document model from the database
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        $filters_document = array(
            'where' => array('d.id = ' . $params['model_id']),
            'model_lang' => $params['model']->get('model_lang'),
            'sanitize' => false
        );
        $document = Documents::searchOne($registry, $filters_document);

        // Make a copy of the model (for history purposes)
        $old_document = clone $document;

        // Set the project id into the document
        $document->set('deadline', General::strftime('%Y-%m-%d %H:%M:00', $new_deadline_sec), true);

        // Try to save the document
        if ($document->save()) {
            // Write history
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            $new_document_filters = array(
                'where' => array('d.id = \'' . $document->get('id') . '\''),
                'model_lang' => $document->get('model_lang')
            );
            $new_document = Documents::searchOne($registry, $new_document_filters);
            Documents_History::saveData(
                $registry,
                array(
                    'model' => $new_document,
                    'action_type' => 'edit',
                    'new_model' => $new_document,
                    'old_model' => $old_document
                )
            );
            $result = true;
        } else {
            // The automation result is false
            $result = false;
        }

        return $result;
    }

    /**
     * Change request documents' deadlines when request is created by transformation
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool         - the result of the operations
     */
    public function changeTransformedRequestsDeadlines($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];

        // get the request documents created in the last X minutes
        $last_crontab_time = date_sub(date_create(), new DateInterval('PT' . $this->settings['minutes_interval'] . 'M'))->format('Y-m-d H:i:s');

        $sql = 'SELECT DISTINCT(d.id) as id, d.substatus' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
               '  ON (d.id=dr.link_to AND dr.link_to_model_name="Document" AND dr.origin="transformed" AND dr.parent_model_name="Document" AND d.type="' . $params['start_model_type'] . '" AND d.active="1" AND d.deleted_by="0")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d1' . "\n" .
               '  ON (d1.id=dr.parent_id AND d1.added>="' . $last_crontab_time . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_AUTOMATIONS_HISTORY . ' AS ah' . "\n" .
               '  ON (ah.model_id=d.id AND ah.parent_id IN (' . $this->settings['related_automations'] . ') AND ah.added>=d1.added)' . "\n" .
               'WHERE ah.model_id IS NULL';
        $records = $db->GetAll($sql);

        foreach ($records as $rec) {
            if (isset($this->settings['type_deadline_' . $rec['substatus']])) {
                $change_params = array(
                    'working_day_start' => $this->settings['working_day_start'],
                    'working_day_end' => $this->settings['working_day_end'],
                    'type_deadline' => $this->settings['type_deadline_' . $rec['substatus']],
                    'hours_offset' => $this->settings['hours_offset_' . $rec['substatus']]
                );

                $new_deadline_sec = $this->calculateRequestDeadline($registry, $change_params);

                // Get the current document model from the database
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                $filters_document = array(
                    'where' => array('d.id = ' . $rec['id']),
                    'model_lang' => $registry['lang'],
                    'sanitize' => false
                );
                $document = Documents::searchOne($registry, $filters_document);

                // Make a copy of the model (for history purposes)
                $old_document = clone $document;

                // Set the project id into the document
                $document->set('deadline', General::strftime('%Y-%m-%d %H:%M:00', $new_deadline_sec), true);

                // Try to save the document
                if ($document->save()) {
                    // Write history
                    require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                    require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
                    $new_document_filters = array(
                        'where' => array('d.id = \'' . $document->get('id') . '\''),
                        'model_lang' => $document->get('model_lang')
                    );
                    $new_document = Documents::searchOne($registry, $new_document_filters);
                    Documents_History::saveData(
                        $registry,
                        array(
                            'model' => $new_document,
                            'action_type' => 'edit',
                            'new_model' => $new_document,
                            'old_model' => $old_document
                        )
                    );
                    $result = true;
                } else {
                    $result = false;
                }
                $query_insert_ah = 'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY . "\n" .
                    'SET parent_id = ' . $params['id'] . ', model_id = ' . $document->get('id') . ', added = now(), num=1, result=' . intval($result) . "\n" .
                    'ON DUPLICATE KEY UPDATE ' . "\n" .
                    'added = now(), num=num+1, result=' . intval($result);
                $registry['db']->Execute($query_insert_ah);
            }
        }
        return true;
    }

    /**
     * Calculate the hours offset based on the current hour and on the working days
     */
    private function calculateRequestDeadline($registry, $change_params) {
        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';

        $new_deadline_sec = '';

        // calculate the working hours per day
        if (Calendars_Calendar::getWorkingDays($registry, date('Y-m-d'), date('Y-m-d'))) {
            $working_day_starting_hour = General::strftime('%Y-%m-%d ' . $change_params['working_day_start'] . ':00');
            $working_day_ending_hour = General::strftime('%Y-%m-%d ' . $change_params['working_day_end'] . ':00');
        } else {
            // get the next working day
            $next_working_day = Calendars_Calendar::calcDateOnWorkingDays($registry, date('Y-m-d'), 1, 'after');
            $working_day_starting_hour = General::strftime($next_working_day . ' ' . $change_params['working_day_start'] . ':00');
            $working_day_ending_hour = General::strftime($next_working_day . ' ' . $change_params['working_day_end'] . ':00');
        }

        if ($change_params['type_deadline'] == 'offset') {
            // calculate working day length
            $working_day_length = strtotime($working_day_ending_hour) - strtotime($working_day_starting_hour);

            // check if the current hour is after the working day end
            $calculate_from = '';
            if (time() >= strtotime($working_day_ending_hour)) {
                // find the next working day
                $calculate_from = strtotime(Calendars_Calendar::calcDateOnWorkingDays($registry, date('Y-m-d'), 1, 'after') . ' ' . $change_params['working_day_start'] . ':00');
            } elseif (time() < strtotime($working_day_starting_hour)) {
                $calculate_from = strtotime($working_day_starting_hour);
            } else {
                $calculate_from = time();
            }

            $hours_offset_sec = $change_params['hours_offset'] * 3600;

            if (General::strftime('%Y-%m-%d %H:%M:00', ($hours_offset_sec + $calculate_from)) > General::strftime('%Y-%m-%d ' . $change_params['working_day_end'] . ':00', $calculate_from)) {
                $working_time_leftover = $hours_offset_sec % $working_day_length;

                $working_days_included = floor($hours_offset_sec / $working_day_length);
                if ($working_days_included) {
                    $new_deadline_sec = strtotime(Calendars_Calendar::calcDateOnWorkingDays($registry, General::strftime('%Y-%m-%d', $calculate_from), $working_days_included, 'after') . ' ' . General::strftime('%H:%M', $calculate_from) . ':00');
                } else {
                    $new_deadline_sec = $calculate_from;
                }

                // calculate the leftover hours
                $previous_new_deadline_sec = $new_deadline_sec;
                $new_deadline_sec = $new_deadline_sec + $working_time_leftover;

                // check if the end hour is after the end of the working day
                if (General::strftime('%Y-%m-%d %H:%M:00', $new_deadline_sec) > General::strftime('%Y-%m-%d ' . $change_params['working_day_end'] . ':00', $previous_new_deadline_sec)) {
                    // the next working day
                    $next_working_day = Calendars_Calendar::calcDateOnWorkingDays($registry, General::strftime('%Y-%m-%d', $new_deadline_sec), 1, 'after');
                    $working_time_leftover = $new_deadline_sec - strtotime(General::strftime('%Y-%m-%d ' . $change_params['working_day_end'] . ':00', $new_deadline_sec));
                    $new_deadline_sec = strtotime($next_working_day . ' ' . $change_params['working_day_start'] . ':00') + $working_time_leftover;
                }
            } else {
                $new_deadline_sec = $hours_offset_sec + $calculate_from;
            }
        } else {
            // set deadline for the next update
            $second_tuesday = General::strftime('%Y-%m-%d', strtotime('second tuesday of ' . date('F Y')));
            $fourth_tuesday = General::strftime('%Y-%m-%d', strtotime('fourth tuesday of ' . date('F Y')));

            if (date('Y-m-d') < $second_tuesday) {
                $new_deadline_sec = strtotime($second_tuesday . ' ' . $change_params['working_day_end'] . ':00');
            } elseif (date('Y-m-d') < $fourth_tuesday) {
                $new_deadline_sec = strtotime($fourth_tuesday . ' ' . $change_params['working_day_end'] . ':00');
            } else {
                // get the second tuesday of the next month
                $second_tuesday = General::strftime('%Y-%m-%d', strtotime('second tuesday of ' . date('F Y', strtotime('+1 month', strtotime(General::strftime('%Y-%m-01'))))));
                $new_deadline_sec = strtotime($second_tuesday . ' ' . $change_params['working_day_end'] . ':00');
            }
        }

        return $new_deadline_sec;
    }

    /**
     * Calculate the project implementation deadline based on the project data
     */
    public function implementationDeadline($params) {
        // Prepare some basics
        $result = true;
        $registry = &$this->registry;
        $db = &$registry['db'];

        // Get the current project model from the database
        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
        $filters_project = array(
            'where' => array('p.id = ' . $params['model_id']),
            'model_lang' => $params['model']->get('model_lang'),
            'sanitize' => false
        );
        $project = Projects::searchOne($registry, $filters_project);

        // Make a copy of the model (for history purposes)
        $old_project = clone $project;

        $project_vars = $project->getAssocVars();
        $start_date = $project_vars[$this->settings['start_project_date']]['value'];
        $count_days = $project_vars[$this->settings['count_project_days']]['value'];
        $term_type = $project_vars[$this->settings['project_term_type']]['value'];

        $end_date = '';
        if ($term_type == $this->settings['type_calendar_days']) {
            $end_date = General::strftime('%Y-%m-%d', strtotime('+' . $count_days . ' day', strtotime($start_date)));
        } else {
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $end_date = Calendars_Calendar::calcDateOnWorkingDays($registry, $start_date, $count_days, 'after');
        }

        // Set the project id into the project
        $project->set('date_end', $end_date, true);

        // Try to save the project
        if ($project->save()) {
            // Write history
            require_once PH_MODULES_DIR . 'projects/models/projects.history.php';
            require_once PH_MODULES_DIR . 'projects/models/projects.audit.php';
            $new_project_filters = array(
                'where' => array('p.id = \'' . $project->get('id') . '\''),
                'model_lang' => $project->get('model_lang')
            );
            $new_project = Projects::searchOne($registry, $new_project_filters);
            Projects_History::saveData(
                $registry,
                array(
                    'model' => $new_project,
                    'action_type' => 'edit',
                    'new_model' => $new_project,
                    'old_model' => $old_project
                )
            );
            $result = true;
        } else {
            // The automation result is false
            $result = false;
        }

        return $result;
    }

    /**
     * Plugin for validation of task fields in metting protocol (Bug 4199).
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function validateProtocolTaskFields($params) {

        // Check settings
        if (empty($this->settings['task_finish_date']) ||
            empty($this->settings['task_assignee'])) {

            // If settings failed
            $err = $this->i18n('error_automation_bgservice_validate_protocol_settings_failed', array($params['name']));
            // Set error message
            $this->registry['messages']->setError($err);
            return false;
        }

        $assignees = array_filter($this->registry['request']->get($this->settings['task_assignee']));
        $finish_dates = $this->registry['request']->get($this->settings['task_finish_date']);
        $task_names = $this->registry['request']->get($this->settings['task_name']);
        $valid = true;
        foreach ($assignees as $row_num => $not_important) {
            if (empty($task_names[$row_num])) {
                $this->registry['messages']->setError($this->i18n('error_automation_bgservice_create_tasks_invalid_task_name', array($row_num + 1)), $this->settings['task_name']);
                $valid = false;
            }
            if (empty($finish_dates[$row_num]) || $finish_dates[$row_num] < General::strftime($this->i18n('date_iso_short'))) {
                $this->registry['messages']->setError($this->i18n('error_automation_bgservice_create_tasks_invalid_finish_date', array($row_num + 1)), $this->settings['task_finish_date']);
                $valid = false;
            }
        }

        return $valid;
    }

    /**
     * Remind for cars dates
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function remindCarDates($params) {
        // Check if the automation is used as crontab
        if ($params['automation_type'] != 'crontab') {
            $this->registry['messages']->setError($this->i18n('error_automation_wrong_automation_type'));
        } else {
            // Prepare some basics
            $registry = &$this->registry;
            $db = $registry['db'];
            $settings = $this->settings;
            $lang = $registry['lang'];

            // Process some settings
            $settings['reminders_days'] = isset($settings['reminders_days']) ? preg_split('/\s*,\s*/', $settings['reminders_days']) : array();
            sort($settings['reminders_days']);
            $settings['remind_users'] = isset($settings['remind_users']) ? preg_split('/\s*,\s*/', $settings['remind_users']) : array();

            // Check required settings
            $required_settings = array(
                'reminders_days',
                'remind_users'
            );
            $required_fields_car = array(
                'field_nom_vehicle_insurer_date_finish',
                'field_nom_vehicle_insurer_payment',
                'field_nom_vehicle_technical_review_finish',
                'field_nom_vehicle_tax_finish'
            );
            $optional_fields_car = array(
                'field_nom_vehicle_casco_ins_date_finish',
                'field_nom_vehicle_casco_ins_payment',
                'field_nom_vehicle_vignette_date_finish',
                'field_nom_vehicle_tachograph_date_finish',
            );
            $all_fields_car = array_merge($required_fields_car, $optional_fields_car);

            $required_settings = array_merge($required_settings, $required_fields_car);
            foreach ($required_settings as $required_setting_name) {
                if (!isset($settings[$required_setting_name]) || is_string($settings[$required_setting_name]) && $settings[$required_setting_name] == '' || is_array($settings[$required_setting_name]) && count(array_filter($settings[$required_setting_name], function ($v) {
                            return $v != '';
                        })) < 1) {
                    $this->executionErrors[] = "Missing required setting: {$required_setting_name}!";
                }
            }

            foreach ($optional_fields_car as $option) {
                if (!array_key_exists($option, $settings)) {
                    //initialize
                    $settings[$option] = '';
                }
            }

            // If everything is fine at this moment
            if (empty($this->executionErrors)) {
                // Get fields
                $query = "
                    SELECT name, id
                        FROM " . DB_TABLE_FIELDS_META . "
                        WHERE name IN ('" . implode("', '", array_intersect_key($settings, array_flip($all_fields_car))) . "')
                          AND model = 'Nomenclature'
                          AND model_type = '{$params['start_model_type']}'";
                $fields_car = $db->GetAssoc($query);

                // Check whether ALL required car fields are present
                $are_required_fields_present = true;
                foreach ($required_fields_car as $k => $option) {
                    if (!array_key_exists($settings[$option], $fields_car)){
                        $are_required_fields_present = false;
                    }
                }

                if (!$are_required_fields_present) {
                    // Set error message
                    $this->executionErrors[] = 'Missing required fields from DB: ' . implode(', ', array_intersect($required_fields_car, array_keys($fields_car)));
                } else {
                    // Get all cars
                    $query = "
                        SELECT n.id   AS id,
                            n.code    AS code,
                            ni.name   AS name,
                            nc1.value AS cr_date,
                            nc2.value AS cr_installment,
                            nc3.value AS ati_date,
                            nc4.value AS tax_date," .
                            (!empty($fields_car[$settings['field_nom_vehicle_casco_ins_date_finish']]) ? "
                            nc5.value AS casco_date,
                              " : "") .
                            (!empty($fields_car[$settings['field_nom_vehicle_casco_ins_payment']]) ? "
                            nc6.value AS casco_installment,
                              " : "") .
                            (!empty($fields_car[$settings['field_nom_vehicle_vignette_date_finish']]) ? "
                            nc7.value AS vignette_date,
                              " : "") .
                            (!empty($fields_car[$settings['field_nom_vehicle_tachograph_date_finish']]) ? "
                            nc8.value AS tachograph_date,
                              " : "") .
                            "TRIM(CONCAT(ui.firstname, ' ', ui.lastname)) AS added_by_name
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_I18N . " AS ni
                            ON (ni.parent_id = n.id
                              AND ni.lang = '{$lang}')
                          LEFT JOIN " . DB_TABLE_USERS_I18N . " AS ui
                            ON (ui.parent_id = n.added_by
                              AND ui.lang = '{$lang}')
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc1
                            ON (nc1.model_id = n.id
                              AND nc1.var_id = '{$fields_car[$settings['field_nom_vehicle_insurer_date_finish']]}')
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc2
                            ON (nc2.model_id = n.id
                              AND nc2.var_id = '{$fields_car[$settings['field_nom_vehicle_insurer_payment']]}')
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc3
                            ON (nc3.model_id = n.id
                              AND nc3.var_id = '{$fields_car[$settings['field_nom_vehicle_technical_review_finish']]}')
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc4
                            ON (nc4.model_id = n.id
                              AND nc4.var_id = '{$fields_car[$settings['field_nom_vehicle_tax_finish']]}')" .
                          (!empty($fields_car[$settings['field_nom_vehicle_casco_ins_date_finish']]) ? "
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc5
                            ON (nc5.model_id = n.id
                              AND nc5.var_id = '{$fields_car[$settings['field_nom_vehicle_casco_ins_date_finish']]}')
                              " : "") .
                          (!empty($fields_car[$settings['field_nom_vehicle_casco_ins_payment']]) ? "
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc6
                            ON (nc6.model_id = n.id
                              AND nc6.var_id = '{$fields_car[$settings['field_nom_vehicle_casco_ins_payment']]}')
                              " : "") .
                          (!empty($fields_car[$settings['field_nom_vehicle_vignette_date_finish']]) ? "
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc7
                            ON (nc7.model_id = n.id
                              AND nc7.var_id = '{$fields_car[$settings['field_nom_vehicle_vignette_date_finish']]}')
                              " : "") .
                          (!empty($fields_car[$settings['field_nom_vehicle_tachograph_date_finish']]) ? "
                          LEFT JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc8
                            ON (nc8.model_id = n.id
                              AND nc8.var_id = '{$fields_car[$settings['field_nom_vehicle_tachograph_date_finish']]}')
                              " : "") .
                          "WHERE n.deleted_by = 0
                            AND n.active = 1
                            AND n.type = '{$params['start_model_type']}'";
                    $cars = $db->GetAll($query);

                    // If there are any cars
                    if (!empty($cars)) {
                        // Prepare a mailer
                        $mailer = new Mailer($registry, 'automations_plugin_bgservice_remindcardates');

                        // Set the value of the placeholder for the nomenclature type
                        $nomenclature_type_name = $db->GetOne("SELECT name FROM " . DB_TABLE_NOMENCLATURES_TYPES_I18N . " WHERE parent_id = '{$params['start_model_type']}' AND lang = '{$lang}'");
                        $mailer->placeholder->add('nomenclature_type_name', mb_strtolower($nomenclature_type_name, mb_detect_encoding($nomenclature_type_name)));

                        // Get the users to remind
                        $query = "
                            SELECT u.email                                   AS email,
                                TRIM(CONCAT(ui.firstname, ' ', ui.lastname)) AS name
                              FROM " . DB_TABLE_USERS . " AS u
                              LEFT JOIN " . DB_TABLE_USERS_I18N . " AS ui
                                ON (ui.parent_id = u.id
                                  AND ui.lang = '{$lang}')
                              WHERE u.deleted_by = 0
                                AND u.active = 1
                                AND u.id IN ('" . implode("', '", $settings['remind_users']) . "')";
                        $remind_users = $db->GetAll($query);

                        // Get the current date
                        $current_date = General::strftime($this->i18n('date_iso_short'));

                        // Send reminder emails for each car
                        foreach ($cars as $car) {
                            // Prepare variable to collect reminder descriptions in it for each reminder date
                            $reminder_descriptions = array();

                            // Prepare reminder descriptions for each remind days
                            foreach ($settings['reminders_days'] as $reminder_days) {
                                // Description: CR
                                $date = $car['cr_date'];
                                $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($date)));
                                if ($current_date == $remind_date) {
                                    $reminder_descriptions[] = sprintf($this->i18n('automation_bgservice_remindcardates_reminder_description_date_validity'), $this->i18n('automation_bgservice_remindcardates_date_type_cr'), General::strftime($this->i18n('date_short'), $date));
                                }

                                // Description: CR installments
                                if ($car['cr_installment'] == 'contributions_two') {
                                    $date = General::strftime($this->i18n('date_iso_short'), strtotime('-6 months', strtotime($car['cr_date'])));
                                    $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($date)));
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf($this->i18n('automation_bgservice_remindcardates_reminder_description_date_installment'), $this->i18n('automation_bgservice_remindcardates_date_type_cr'), General::strftime($this->i18n('date_short'), $date));
                                    }
                                } elseif ($car['cr_installment'] == 'contributions_four') {
                                    $date = General::strftime($this->i18n('date_iso_short'), strtotime('-3 months', strtotime($car['cr_date'])));
                                    $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($date)));
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf($this->i18n('automation_bgservice_remindcardates_reminder_description_date_installment'), $this->i18n('automation_bgservice_remindcardates_date_type_cr'), General::strftime($this->i18n('date_short'), $date));
                                    }
                                    $date = General::strftime($this->i18n('date_iso_short'), strtotime('-6 months', strtotime($car['cr_date'])));
                                    $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($date)));
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf($this->i18n('automation_bgservice_remindcardates_reminder_description_date_installment'), $this->i18n('automation_bgservice_remindcardates_date_type_cr'), General::strftime($this->i18n('date_short'), $date));
                                    }
                                    $date = General::strftime($this->i18n('date_iso_short'), strtotime('-9 months', strtotime($car['cr_date'])));
                                    $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($date)));
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf($this->i18n('automation_bgservice_remindcardates_reminder_description_date_installment'), $this->i18n('automation_bgservice_remindcardates_date_type_cr'), General::strftime($this->i18n('date_short'), $date));
                                    }
                                }

                                if (!empty($car['casco_date'])) {
                                    // Description: Casco
                                    $date = $car['casco_date'];
                                    $remind_date = General::strftime(
                                        $this->i18n('date_iso_short'),
                                        strtotime(
                                            "-{$reminder_days} days",
                                            strtotime($date)
                                        )
                                    );
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf(
                                            $this->i18n(
                                                'automation_bgservice_remindcardates_reminder_description_date_validity'
                                            ),
                                            $this->i18n('automation_bgservice_remindcardates_date_type_casco'),
                                            General::strftime($this->i18n('date_short'), $date)
                                        );
                                    }
                                }

                                if (!empty($car['casco_installment'])) {
                                    // Description: Casco installments
                                    if ($car['casco_installment'] == 'contributions_two') {
                                        $date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime('-6 months', strtotime($car['casco_date']))
                                        );
                                        $remind_date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime(
                                                "-{$reminder_days} days",
                                                strtotime($date)
                                            )
                                        );
                                        if ($current_date == $remind_date) {
                                            $reminder_descriptions[] = sprintf(
                                                $this->i18n(
                                                    'automation_bgservice_remindcardates_reminder_description_date_installment'
                                                ),
                                                $this->i18n('automation_bgservice_remindcardates_date_type_casco'),
                                                General::strftime($this->i18n('date_short'), $date)
                                            );
                                        }
                                    } elseif ($car['casco_installment'] == 'contributions_four') {
                                        $date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime('-3 months', strtotime($car['casco_date']))
                                        );
                                        $remind_date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime(
                                                "-{$reminder_days} days",
                                                strtotime($date)
                                            )
                                        );
                                        if ($current_date == $remind_date) {
                                            $reminder_descriptions[] = sprintf(
                                                $this->i18n(
                                                    'automation_bgservice_remindcardates_reminder_description_date_installment'
                                                ),
                                                $this->i18n('automation_bgservice_remindcardates_date_type_casco'),
                                                General::strftime($this->i18n('date_short'), $date)
                                            );
                                        }
                                        $date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime('-6 months', strtotime($car['casco_date']))
                                        );
                                        $remind_date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime(
                                                "-{$reminder_days} days",
                                                strtotime($date)
                                            )
                                        );
                                        if ($current_date == $remind_date) {
                                            $reminder_descriptions[] = sprintf(
                                                $this->i18n(
                                                    'automation_bgservice_remindcardates_reminder_description_date_installment'
                                                ),
                                                $this->i18n('automation_bgservice_remindcardates_date_type_casco'),
                                                General::strftime($this->i18n('date_short'), $date)
                                            );
                                        }
                                        $date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime('-9 months', strtotime($car['casco_date']))
                                        );
                                        $remind_date = General::strftime(
                                            $this->i18n('date_iso_short'),
                                            strtotime(
                                                "-{$reminder_days} days",
                                                strtotime($date)
                                            )
                                        );
                                        if ($current_date == $remind_date) {
                                            $reminder_descriptions[] = sprintf(
                                                $this->i18n(
                                                    'automation_bgservice_remindcardates_reminder_description_date_installment'
                                                ),
                                                $this->i18n('automation_bgservice_remindcardates_date_type_casco'),
                                                General::strftime($this->i18n('date_short'), $date)
                                            );
                                        }
                                    }
                                }

                                if (!empty($car['vignette_date'])) {
                                    // Description: Vignette
                                    $date = $car['vignette_date'];
                                    $remind_date = General::strftime(
                                        $this->i18n('date_iso_short'),
                                        strtotime(
                                            "-{$reminder_days} days",
                                            strtotime($date)
                                        )
                                    );
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf(
                                            $this->i18n(
                                                'automation_bgservice_remindcardates_reminder_description_date_validity'
                                            ),
                                            $this->i18n('automation_bgservice_remindcardates_date_type_vignette'),
                                            General::strftime($this->i18n('date_short'), $date)
                                        );
                                    }
                                }

                                if (!empty($car['tachograph_date'])) {
                                    // Description: Tachograph
                                    $date = $car['tachograph_date'];
                                    $remind_date = General::strftime(
                                        $this->i18n('date_iso_short'),
                                        strtotime(
                                            "-{$reminder_days} days",
                                            strtotime($date)
                                        )
                                    );
                                    if ($current_date == $remind_date) {
                                        $reminder_descriptions[] = sprintf(
                                            $this->i18n(
                                                'automation_bgservice_remindcardates_reminder_description_date_validity'
                                            ),
                                            $this->i18n('automation_bgservice_remindcardates_date_type_tachograph'),
                                            General::strftime($this->i18n('date_short'), $date)
                                        );
                                    }
                                }

                                // Description: ATI
                                $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($car['ati_date'])));
                                if ($current_date == $remind_date) {
                                    $reminder_descriptions[] = sprintf(
                                        $this->i18n(
                                            'automation_bgservice_remindcardates_reminder_description_date_validity'
                                        ),
                                        $this->i18n('automation_bgservice_remindcardates_date_type_ati'),
                                        General::strftime($this->i18n('date_short'), $car['ati_date'])
                                    );
                                }

                                // Description: tax
                                $remind_date = General::strftime($this->i18n('date_iso_short'), strtotime("-{$reminder_days} days", strtotime($car['tax_date'])));
                                if ($current_date == $remind_date) {
                                    $reminder_descriptions[] = sprintf(
                                        $this->i18n(
                                            'automation_bgservice_remindcardates_reminder_description_date_validity'
                                        ),
                                        $this->i18n('automation_bgservice_remindcardates_date_type_tax'),
                                        General::strftime($this->i18n('date_short'), $car['tax_date'])
                                    );
                                }
                            }

                            // If there is something to remind for the current car
                            if (!empty($reminder_descriptions)) {
                                // Set placeholders values for the current car
                                $mailer->placeholder->add('nomenclature_added_by', $car['added_by_name']);
                                $mailer->placeholder->add(
                                    'nomenclature_view_url', sprintf(
                                        '%s/index.php?%s=%s&%s=view&view=%d', $registry['config']->getParam('crontab', 'base_host'), $registry['module_param'], 'nomenclatures', 'nomenclatures', $car['id']
                                    )
                                );
                                $mailer->placeholder->add('nomenclature_name', $car['name']);
                                $mailer->placeholder->add('nomenclature_code', $car['code']);
                                $mailer->placeholder->add('reminder_description', implode('<br />', $reminder_descriptions));

                                // Send reminder emails for each reminder user
                                foreach ($remind_users as $user) {
                                    // Set placeholders for the current car user
                                    $mailer->placeholder->add('to_email', $user['email']);
                                    $mailer->placeholder->add('user_name', $user['name']);

                                    // Try to send the reminder email
                                    if (!$mailer->send()) {
                                        // Set an error if email failed to send
                                        $this->executionErrors[] = "Failed to send mail to: {$user['email']} for car: {$car['name']} {$car['code']}";
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Get the result depending on the execution errors
        if (empty($this->executionErrors)) {
            $result = 1;
        } else {
            $result = 0;
        }

        // Writes history
        $this->updateAutomationHistory($params, 0, $result);

        return $result;
    }

    /**
     * Add task for document of type "Client request"
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function addClientRequestTask($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $messages = &$registry['messages'];
        $settings = $this->settings;
        $lang = $registry['lang'];
        $document = $params['model'];

        // Unsanitize the document
        if ($sanitize_after = $document->isSanitized()) {
            $document->unsanitize();
        }

        // Load the tasks language vars file
        $registry['translater']->loadFile(PH_MODULES_DIR . "tasks/i18n/{$lang}/tasks.ini");

        // Get the task type object
        $filters_task_type = array(
            'where' => array('tt.id = \'' . PH_TASK_TYPE . '\''),
            'model_lang' => $lang,
            'sanitize' => true
        );
        $task_type = Tasks_Types::searchOne($registry, $filters_task_type);

        // If there is such task type
        if ($task_type) {
            // Start a transaction for the current new task
            $db->StartTrans();

            // Prepare the task
            $planned_start_date = General::strftime($this->i18n('date_iso'));
            $task_days = empty($settings['task_days']) ? 0 : $settings['task_days'];
            $planned_finish_date = General::strftime($this->i18n('date_iso'), strtotime("+ {$task_days} days", strtotime($planned_start_date)));
            $new_task_properties = array(
                'type' => PH_TASK_TYPE,
                'name' => $document->get('name'),
                'customer' => $document->get('customer'),
                'project' => $document->get('project'),
                'planned_start_date' => $planned_start_date,
                'planned_finish_date' => $planned_finish_date,
                'description' => $document->getPlainVarValue('link_problem') . "\n" . $document->getPlainVarValue('description_inq'),
                'department' => isset($settings['task_department']) && $settings['task_department'] != '' ? $settings['task_department'] : 1,
                'group' => isset($settings['task_group']) && $settings['task_group'] != '' ? $settings['task_group'] : 1,
                'status' => isset($settings['task_status']) && $settings['task_status'] != '' ? $settings['task_status'] : 'planning',
                'substatus' => isset($settings['task_substatus']) && $settings['task_substatus'] != '' ? $settings['task_substatus'] : 0,
                'severity' => isset($settings['task_severity']) && $settings['task_severity'] != '' ? $settings['task_severity'] : 'normal',
                'active' => 1
            );
            $task = new Task($registry, $new_task_properties);

            // Try to save the new task
            if ($task->save()) {
                // Write history
                $old_task = new Task($registry);
                $old_task->sanitize();
                $filters_new_task = array(
                    'where' => array("t.id = '{$task->get('id')}'"),
                    'model_lang' => $task->get('model_lang'),
                    'sanitize' => false
                );
                $new_task = Tasks::searchOne($registry, $filters_new_task);
                $audit_parent = Tasks_History::saveData(
                    $registry,
                    array(
                        'action_type' => 'add',
                        'old_model' => $old_task,
                        'model' => $task,
                        'new_model' => $new_task
                    )
                );
                if ($audit_parent) {
                    // Make relation between the task and the document
                    $query = "
                        INSERT IGNORE INTO " . DB_TABLE_TASKS_RELATIVES . "
                          SET parent_id = '{$task->get('id')}',
                            link_to = '{$document->get('id')}',
                            origin = 'document',
                            link_type = 'parent',
                            added = NOW()";
                    $db->Execute($query);

                    // Prepare an URL to the new task
                    $url = sprintf(
                        '%s?%s=tasks&amp;tasks=view&amp;view=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $task->get('id')
                    );

                    // Copy the assignments from the document to the task
                    if ($this->_copyClientRequestAssignments($document, $new_task)) {
                        // Set success message
                        $messages->setMessage(sprintf($this->i18n('automation_bgservice_addclientrequesttasks_success'), $url, $task->get('full_num')));
                    } else {
                        $messages->setWarning($this->i18n('automation_bgservice_addclientrequesttasks_failed_from_assignments'));
                    }
                } else {
                    $messages->setWarning($this->i18n('automation_bgservice_addclientrequesttasks_failed_from_history'));
                }
            } else {
                $messages->setWarning($this->i18n('automation_bgservice_addclientrequesttasks_failed_to_save_task'));
            }

            // Check for errors
            if ($messages->getWarnings()) {
                $db->FailTrans();
            } else if ($db->HasFailedTrans()) {
                $messages->setWarning($this->i18n('automation_bgservice_addclientrequesttasks_failed'));
            }

            // Complete the transaction
            $db->CompleteTrans();
        } else {
            $messages->setWarning($this->i18n('automation_bgservice_addclientrequesttasks_no_task_type'));
        }

        // Sanitize the document
        if ($sanitize_after) {
            $document->sanitize();
        }

        // Insert the messages into the session
        $messages->insertInSession($registry);

        // Result depends from the warnings
        return $messages->getWarnings() ? false : true;
    }

    /**
     * Copy all assignments from document to task
     *
     * @param object $document - the document
     * @param object $task - the task
     * @return boolean - the result of the operation
     */
    private function _copyClientRequestAssignments($document, $task) {
        // Prepare the result var
        $result = false;

        // Prepare an old copy of the task model
        $old_task = clone $task;
        $old_task->sanitize();

        // Copy all assignments from the document to the task
        $assignments_types = array('owner', 'responsible', 'observer', 'decision');
        foreach ($assignments_types as $at) {
            // Get the current type assignments
            $assignments = $document->getAssignments($at);
            // Skip the portal users
            foreach ($assignments as $user_id => $user) {
                if (!empty($user['is_portal'])) {
                    unset($assignments[$user_id]);
                }
            }
            // Set the current type assignments into the task
            $task->set('assignments_' . $at, $assignments, true);
        }

        // Save the task assignments
        if ($task->assign(false)) {
            // Write history
            $audit_parent = Tasks_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'assign',
                    'old_model' => $old_task,
                    'model' => $task,
                    'new_model' => $task
                )
            );
            if ($audit_parent) {
                $result = true;
            }
        }

        return $result;
    }

    /**
     * Get the task related to a document
     *
     * @param integer $document_id - the document id
     * @return mixed - false or the task object (unsanitized)
     */
    private function _getClientRequestTask($document_id) {
        // Get the id of the related task
        $query = "
            SELECT parent_id
              FROM " . DB_TABLE_TASKS_RELATIVES . " AS tr
              JOIN " . DB_TABLE_TASKS . " AS t
                ON (t.id = tr.parent_id
                  AND t.type = '" . PH_TASK_TYPE . "')
              WHERE tr.link_type = 'parent'
                AND tr.origin = 'document'
                AND tr.link_to = '{$document_id}'";
        if ($task_id = $this->registry['db']->GetOne($query)) {
            // Get the task object
            $filters = array(
                'where' => array("t.id = '{$task_id}'"),
                'model_lang' => $this->registry['lang'],
                'sanitize' => false
            );
            return Tasks::searchOne($this->registry, $filters);
        }

        return false;
    }

    /**
     * Copy all assignments from document of type "Client request" to a task
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function copyClientRequestAssignments($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $messages = &$registry['messages'];
        $lang = $registry['lang'];
        $document = $params['model'];
        $result = false;

        // Unsanitize the document
        if ($sanitize_after = $document->isSanitized()) {
            $document->unsanitize();
        }

        // Start a transaction
        $db->StartTrans();

        // Get the task related to the current document
        if ($task = $this->_getClientRequestTask($document->get('id'))) {
            // Load the main language vars file for tasks
            $registry['translater']->loadFile(PH_MODULES_DIR . "tasks/i18n/{$lang}/tasks.ini");

            // Prepare a URL to the task
            $url = sprintf(
                '%s?%s=tasks&amp;tasks=view&amp;view=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $task->get('id')
            );

            // Copy all assignments fro mthe document to the tasks
            if ($this->_copyClientRequestAssignments($document, $task)) {
                // Set success message
                $messages->setMessage(sprintf($this->i18n('automation_bgservice_copyclientrequestassignments_success'), $url, $task->get('full_num')));
            } else {
                $messages->setWarning(sprintf($this->i18n('automation_bgservice_copyclientrequestassignments_failed_for_task'), $url, $task->get('full_num')));
            }
        } else {
            $messages->setWarning($this->i18n('automation_bgservice_copyclientrequestassignments_no_task'));
        }

        // Check for errors
        if ($messages->getWarnings()) {
            $db->FailTrans();
        } else if ($db->HasFailedTrans()) {
            $messages->setWarning($this->i18n('automation_bgservice_copyclientrequestassignments_failed'));
        }

        // Complete the transaction
        $db->CompleteTrans();

        // Sanitize the document
        if ($sanitize_after) {
            $document->sanitize();
        }

        // Insert the messages into the session
        $messages->insertInSession($registry);

        // Result depends from the warnings
        return $messages->getWarnings() ? false : true;
    }

    /**
     * Finish task related to document of type "Client request"
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function finishClientRequestTask($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $messages = &$registry['messages'];
        $settings = $this->settings;
        $lang = $registry['lang'];
        $document = $params['model'];

        // Check required settings
        if (!isset($settings['task_finished_substatus']) || $settings['task_finished_substatus'] == '') {
            $messages->setWarning($this->i18n('automation_bgservice_finishclientrequesttasks_required_settings'));
        }

        // If no warnings
        if (!$messages->getWarnings()) {
            // Get the related task
            if ($task = $this->_getClientRequestTask($document->get('id'))) {
                // Load the main language vars filte for tasks
                $registry['translater']->loadFile(PH_MODULES_DIR . "tasks/i18n/{$lang}/tasks.ini");

                // Prepare an old copy of the task
                $old_task = clone $task;
                $old_task->sanitize();

                // Start a transaction
                $db->StartTrans();

                // Set the new status
                $task->set('status', 'finished', true);
                $task->set('substatus', 'finished_' . $settings['task_finished_substatus'], true);
                if ($task->setStatus()) {
                    // Write history
                    $filters_new_task = array(
                        'where' => array("t.id = '{$task->get('id')}'"),
                        'model_lang' => $task->get('model_lang'),
                        'sanitize' => true
                    );
                    $new_task = Tasks::searchOne($registry, $filters_new_task);
                    $audit_parent = Tasks_History::saveData(
                        $registry,
                        array(
                            'action_type' => 'status',
                            'old_model' => $old_task,
                            'model' => $task,
                            'new_model' => $new_task
                        )
                    );
                    if ($audit_parent) {
                        // Prepare a URL to the task
                        $url = sprintf(
                            '%s?%s=tasks&amp;tasks=view&amp;view=%s', $_SERVER['PHP_SELF'], $registry['module_param'], $task->get('id')
                        );
                        // Set success message
                        $messages->setMessage(sprintf($this->i18n('automation_bgservice_finishclientrequesttasks_success'), $url, $task->get('full_num')));
                    } else {
                        $messages->setWarning($this->i18n('automation_bgservice_finishclientrequesttasks_failed_history'));
                    }
                } else {
                    $messages->setWarning($this->i18n('automation_bgservice_finishclientrequesttasks_failed'));
                }

                // Check for errors
                if ($messages->getWarnings()) {
                    $db->FailTrans();
                } else if ($db->HasFailedTrans()) {
                    $messages->setWarning($this->i18n('automation_bgservice_finishclientrequesttasks_failed'));
                }

                // Complete the transaction
                $db->CompleteTrans();
            } else {
                $messages->setWarning($this->i18n('automation_bgservice_finishclientrequesttasks_failed_no_task'));
            }
        }

        // Insert the messages into the session
        $messages->insertInSession($registry);

        // Result depends from the warnings
        return $messages->getWarnings() ? false : true;
    }

    /**
     * Automation to create loan payment document when loan document is finished
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function createLoanPayment($params) {
        // Execute the automation only if changing the status of the finance document
        $result = false;
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
        $properties_to_copy = array('issue_date', 'num', 'customer', 'company', 'office', 'payment_type', 'container_id');
        $income_reason = new Finance_Incomes_Reason($this->registry);
        $old_income_reason = clone $income_reason;
        $old_income_reason->sanitize();
        $income_reason->set('type', $this->settings['payment_loan_type'], true);
        $income_reason->set('group', $params['model']->get('group'), true);

        foreach ($params['model']->properties as $prop => $prop_value) {
            if (preg_match('#total#', $prop) || preg_match('#currency#', $prop) || in_array($prop, $properties_to_copy)) {
                $income_reason->set($prop, $prop_value, true);
            }
        }

        //get the type
        $filters = array('where' => array('fdt.id=' . $this->settings['payment_loan_type']), 'sanitize' => true);

        //get the event type reminder
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $income_reason->set('date_of_payment_count', $fin_doc_type->get('default_date_of_payment_count'), true);
        $income_reason->set('date_of_payment_period_type', $fin_doc_type->get('default_date_of_payment_period_type'), true);
        $income_reason->set('date_of_payment_point', $fin_doc_type->get('default_date_of_payment_point'), true);

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $gt2_new_model = $income_reason->getGT2Vars();
        $gt2_parent_model = $params['model']->getGT2Vars();

        $gt2_new_model['values'] = $gt2_parent_model['values'];
        $gt2_new_model['plain_values'] = $gt2_parent_model['plain_values'];

        $income_reason->set('table_values_are_set', true, true);
        $income_reason->set('grouping_table_2', $gt2_new_model, true);

        if (!empty($this->settings['add_new_reason_as_finished'])) {
            $income_reason->set('status', 'finished', true);
        }

        $this->registry['db']->StartTrans();
        if ($income_reason->save()) {
            // Write history
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
            $filters = array(
                'where' => array('fir.id = \'' . $income_reason->get('id') . '\''),
                'model_lang' => $income_reason->get('model_lang')
            );
            $new_income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $new_income_reason->getVars();
            $this->registry->set('get_old_vars', false, true);

            Finance_Incomes_Reasons_History::saveData(
                $this->registry,
                array(
                    'model' => $new_income_reason,
                    'action_type' => 'add',
                    'new_model' => $income_reason,
                    'old_model' => $old_income_reason
                )
            );
            $din_doc_view_url = sprintf('%s?%s=finance&amp;%s=incomes_reasons&amp;incomes_reasons=view&amp;view=%s', $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->registry['controller_param'], $new_income_reason->get('id'));

            $sql = 'INSERT INTO `fin_reasons_relatives` (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`, `rows_links`, `changes`)' . "\n" .
                   '  VALUES ("%d", "%s", "%d", "%s", "", "")' . "\n";
            $sql = sprintf($sql, $new_income_reason->get('id'), $new_income_reason->modelName, $params['model']->get('id'), $params['model']->modelName);
            $this->registry['db']->Execute($sql);

            if (!$this->registry['db']->HasFailedTrans()) {
                // Message: add success
                $this->registry['messages']->setMessage(sprintf($this->i18n('message_automation_return_loadn_added_success'), $din_doc_view_url));
                $result = true;
            } else {
                $this->registry['messages']->setError($this->i18n('error_automation_return_loadn_added_success'));
                $result = false;
            }
        } else {
            $this->registry['db']->FailTrans();
            $this->registry['messages']->setError($this->i18n('error_automation_return_loadn_added_success'));
            $result = false;
        }
        $this->registry['db']->CompleteTrans();
        $this->registry['messages']->insertInSession($this->registry);
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * Automation to tag the expenses invoices according to the customer tags
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function tagExpenseInvoicesByCustomer($params) {
        $result = true;

        // prepare settings in tags array
        // customer_tag => invoice_tag
        $tags = array();
        $matches = array();
        foreach ($this->settings as $setting => $value) {
            if (preg_match('#^tag_invoice_([0-9]+)$#', $setting, $matches)) {
                if (!empty($matches[1]) && isset($this->settings['tag_customer_' . $matches[1]])) {
                    $tags[$value] = $this->settings['tag_customer_' . $matches[1]];
                }
            }
        }

        if (empty($tags)) {
            //no suitable settings
            return true;
        }

        //get the customer tags
        $filters = array(
            'where' => array('c.id = \'' . $params['model']->get('customer') . '\''),
            'sanitize' => true
        );
        $customer = Customers::searchOne($this->registry, $filters);
        if ($customer) {
            $customer->getTags();
            if ($customer->get('tags')) {
                //ge the
                $new_tags = array();
                foreach ($tags as $invoice_tag => $customer_tag) {
                    if (in_array($customer_tag, $customer->get('tags'))) {
                        $new_tags[] = $invoice_tag;
                    }
                }
            }
        }

        if (!empty($new_tags)) {
            $params['new_tags'] = implode(',', array_unique($new_tags));
            $result = $this->tag($params);
        }

        return $result;
    }

    /**
     * Automation to tag the expenses invoices according to another expense document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function tagExpenseInvoicesByExpenseDocument($params) {
        $result = true;

        // get the invoices in the article_deliverer
        $this->registry->set('get_old_vars', true, true);
        $gt2 = $params['model']->getGT2Vars();
        $invoices_ids = array();
        foreach ($gt2['values'] as $row) {
            $invoices_ids[] = $row['article_deliverer'];
        }

        if (empty($invoices_ids)) {
            return $result;
        }

        //get the customer tags
        $filters = array(
            'where' => array(
                'fer.id IN (' . implode(', ', $invoices_ids) . ')',
                'tags.tag_id = \'' . $this->settings['tag_VAT_restore'] . '\''
            ),
            'sanitize' => true
        );
        $invoices = Finance_Expenses_Reasons::getIds($this->registry, $filters);
        if ($invoices) {
            foreach ($invoices as $id) {
                $params['tags'] = $this->settings['tag_VAT_restore'];
                $params['model_id'] = $id;
                $result = $this->removeTags($params);
                if (!$result) {
                    break;
                }

                $params['new_tags'] = $this->settings['tag_VAT_restored'];
                $params['model_id'] = $id;
                $result = $this->tag($params);
                if (!$result) {
                    break;
                }
            }
        }

        return $result;
    }

    /**
     * Prepare some data for document of type "Warranty Card"
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function prepareWarrantyCard($params) {
        if ($params['automation_type'] == 'before_viewer' && !$this->registry['request']->isPost()) {
            // Prepare some basics
            $registry = &$this->registry;
            $request = &$registry['request'];
            $db = &$registry['db'];
            $messages = &$registry['messages'];
            $settings = $this->settings;
            $lang = $registry['lang'];
            $document = &$params['model'];

            // Check required settings
            if (empty($settings['report_files_field'])) {
                $messages->setError($this->i18n('automation_bgservice_preparewarrantycard_required_settings'));
            } else {
                // Get the data from the request
                $customer_id = trim($request->get('c'));
                $gt2_rows_ids = array_filter(preg_split('/\s*,\s*/', trim($request->get('g'))));
                $files_ids = array_filter(preg_split('/\s*,\s*/', trim($request->get('f'))));

                if (!empty($gt2_rows_ids) && !empty($customer_id)) {
                    // Set some basic vars
                    $document->set('date', General::strftime($this->i18n('date_iso')), true);
                    $document->set('customer', $customer_id, true);
                    $document->set('employee', $registry['originalUser']->get('employee'), true);

                    // Set the GT2
                    $query = "
                        SELECT *
                          FROM " . DB_TABLE_GT2_DETAILS . " AS gd
                          LEFT JOIN " . DB_TABLE_GT2_DETAILS_I18N . " AS gdi
                            ON (gdi.parent_id = gd.id
                              AND gdi.lang = '{$lang}')
                          WHERE gd.id IN ('" . implode("', '", $gt2_rows_ids) . "')";
                    $gt2_rows = $db->GetAssoc($query);
                    if (!empty($gt2_rows)) {
                        $gt2 = array();
                        $gt2_var_key = '';
                        $report_files_key = '';
                        $vars = $document->get('vars');
                        foreach ($vars as $var_key => $var) {
                            if ($var['type'] == 'gt2') {
                                $gt2 = $var;
                                $gt2_var_key = $var_key;
                            } else if ($var['name'] == $settings['report_files_field']) {
                                $report_files_key = $var_key;
                            }
                        }
                        $gt2['values'] = array();
                        foreach ($gt2_rows_ids as $gt2_row_id) {
                            $gt2['values'][] = array(
                                'article_id' => $gt2_rows[$gt2_row_id]['article_id'],
                                'article_name' => $gt2_rows[$gt2_row_id]['article_name'],
                                'article_description' => $gt2_rows[$gt2_row_id]['article_description'],
                                'free_field1' => $gt2_rows[$gt2_row_id]['free_field3'],
                                'quantity' => $gt2_rows[$gt2_row_id]['quantity'],
                                'article_measure_name' => $gt2_rows[$gt2_row_id]['article_measure_name'],
                                'free_field2' => $gt2_row_id,
                                'free_text4' => $gt2_rows[$gt2_row_id]['free_text4']
                            );
                        }
                        $vars[$gt2_var_key] = $gt2;
                        if (!empty($files_ids)) {
                            $vars[$report_files_key]['value'] = implode(',', $files_ids);
                        }
                        $document->set('vars', $vars, true);
                    } else {
                        $messages->setError($this->i18n('automation_bgservice_preparewarrantycard_failed_gt2'));
                    }
                }
            }
            $messages->insertInSession($registry);
        }

        return true;
    }

    /**
     * Attach (copy from incoming invoices) specified files after adding a
     * document of "Warranty Card" type
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function afterWarrantyCard($params) {
        $result = true;

        if ($params['automation_type'] == 'action' && $this->registry['request']->isPost()) {
            // Prepare some basics
            $registry = &$this->registry;
            $db = &$registry['db'];
            $messages = &$registry['messages'];
            $settings = $this->settings;
            $lang = $registry['lang'];
            $document = &$params['model'];

            // Check required settings
            if (empty($settings['report_files_field'])) {
                $messages->setWarning($this->i18n('automation_bgservice_afterwarrantycard_required_settings'));
            } else {
                /*
                 * Attach files from the invoices to the current document
                 */
                $vars = $document->get('vars');
                $files_var = array();
                foreach ($vars as $var) {
                    if ($var['name'] == $settings['report_files_field']) {
                        $files_var = $var;
                        break;
                    }
                }
                $files_ids = array_filter(preg_split('/\s*,\s*/', $files_var['value']));
                if (!empty($files_ids)) {
                    $query = "
                        SELECT *
                          FROM " . DB_TABLE_FILES . " AS f
                          LEFT JOIN " . DB_TABLE_FILES_I18N . " AS fi
                            ON (fi.parent_id = f.id
                              AND fi.lang = '{$lang}')
                          WHERE f.id IN ('" . implode("', '", $files_ids) . "')";
                    $files = $db->GetAll($query);
                    if (!empty($files)) {
                        // attach files as original user in order to be able to manage them later
                        $this->setOriginalUserAsCurrent();
                        // Load the current attachments and make a copy of the current version of the document
                        if ($sanitize_after = $document->isSanitized()) {
                            $document->unsanitize();
                        }
                        $document->getAttachments();
                        $old_document = clone $document;
                        if ($sanitize_after) {
                            $document->sanitize();
                        }

                        $db->StartTrans();
                        foreach ($files as $file) {
                            $revision_params = array(
                                'model' => $document->modelName,
                                'model_id' => $document->get('id'),
                                'origin' => 'attached',
                                'filename' => $file['filename']
                            );
                            $revision = Files::getLatestRevision($registry, $revision_params);
                            $db->Execute("
                                INSERT INTO `" . DB_TABLE_FILES . "` (`id`, `model`, `model_id`, `filename`, `path`, `origin`, `revision`, `pattern_id`, `permission`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
                                  (NULL, '{$document->modelName}', '{$document->get('id')}', '{$file['filename']}', '{$file['path']}', '{$file['origin']}', '{$revision}', '', 'all', NOW(), '{$registry['currentUser']->get('id')}', NOW(), '{$registry['currentUser']->get('id')}', '0000-00-00 00:00:00', 0)");
                            $db->Execute("
                                INSERT INTO `" . DB_TABLE_FILES_I18N . "` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
                                  (LAST_INSERT_ID(), '{$file['name']}', '{$file['description']}', '{$lang}', NOW())");
                        }

                        if ($db->HasFailedTrans()) {
                            $messages->setWarning($this->i18n('automation_bgservice_afterwarrantycard_failed'));
                        } else {
                            // Write history for the files attachment
                            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
                            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
                            $new_document_filters = array(
                                'where' => array("d.id = '{$document->get('id')}'"),
                                'model_lang' => $document->get('model_lang'),
                                'sanitize' => false
                            );
                            $new_document = Documents::searchOne($registry, $new_document_filters);
                            $new_document->getAttachments();
                            $new_document->sanitize();
                            Documents_History::saveData(
                                $registry,
                                array(
                                    'action_type' => 'add_attachments',
                                    'old_model' => $old_document,
                                    'model' => $document,
                                    'new_model' => $new_document
                                )
                            );
                            $messages->setMessage($this->i18n('message_attachments_added'));
                        }
                        $db->CompleteTrans();
                        // restore automation user as current user
                        $this->setAutomationUserAsCurrent();
                    } else {
                        $messages->setWarning($this->i18n('automation_bgservice_afterwarrantycard_failed_files'));
                    }
                }
            }

            // Make a relation between the document and the invoices from which it's made
            $query = "
                INSERT INTO " . DB_TABLE_FINANCE_REASONS_RELATIVES . " (parent_id, parent_model_name, link_to, link_to_model_name, rows_links, changes)
                  SELECT gd.model_id            AS parent_id,
                      'Document'                AS parent_model_name,
                      fer.id                    AS link_to,
                      'Finance_Expenses_Reason' AS link_to_model_name,
                      NULL                      AS rows_links,
                      ''                        AS changes
                    FROM " . DB_TABLE_GT2_DETAILS . " AS gd
                    JOIN " . DB_TABLE_GT2_DETAILS . " AS gd1
                      ON (gd1.model = 'Finance_Expenses_Reason'
                        AND gd1.id = gd.free_field2)
                    JOIN " . DB_TABLE_FINANCE_EXPENSES_REASONS . " AS fer
                      ON (fer.`type` = '" . PH_FINANCE_TYPE_EXPENSES_INVOICE . "'
                        AND fer.id = gd1.model_id)
                    WHERE gd.model = 'Document'
                      AND gd.model_id = '{$document->get('id')}'
                    GROUP BY fer.id";
            $db->Execute($query);
            if ($db->ErrorMsg()) {
                $messages->setWarning($this->i18n('automation_bgservice_afterwarrantycard_failed_relatives'));
            }

            $messages->insertInSession($registry);
        }

        return $result;
    }

    /**
     * Sends email for unattended clients.
     * The email content is the list of customers who have not been serviced last 30 days
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function sendEmailsForUnattendedClients($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $db = $registry['db'];
        $settings = $this->settings;

        // Process settings
        if (empty($settings['unattended_days']) && (int)$settings['unattended_days'] <= 0) {
            $this->executionErrors[] = 'Required setting unattended_days not found or it is not an integer!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }
        $emails_to = array();
        $offices_emails_to = array();
        $matches = array();
        foreach ($settings as $k => $v) {
            if (preg_match('/^emails_(.+)$/', $k, $matches)) {
                $office = $matches[1];
                $office_emails_to = preg_split('/\s*,\s*/', $v);
                $emails_to = array_merge($emails_to, $office_emails_to);
                $offices_emails_to[$office] = $office_emails_to;
            }
        }
        if (!$emails_to) {
            $this->executionErrors[] = 'Required emails_... settings are empty or missing!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }
        if (empty($settings['email_template_id'])) {
            $this->executionErrors[] = 'Required setting email_template_id is missing!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        // Get the recipients mails and names by offices
        $query = "
            SELECT u.email, TRIM(CONCAT(ui.firstname, ' ', ui.lastname))
              FROM " . DB_TABLE_USERS . " AS u
              LEFT JOIN " . DB_TABLE_USERS_I18N . " AS ui
                ON (ui.parent_id = u.id
                  AND ui.lang = '{$registry['lang']}')
              WHERE u.email IN ('" . implode("', '", $emails_to) . "')";
        $emails_to = $db->GetAssoc($query);
        if (empty($emails_to)) {
            $this->executionErrors[] = 'Can\'t find recipient emails!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }
        foreach ($offices_emails_to as $office => $emails) {
            $offices_emails_to[$office] = array_intersect_key($emails_to, array_flip($emails));
        }

        // Get the email template
        require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
        $filters = array(
            'where' => array('e.id="' . $settings['email_template_id'] . '"'),
            'sanitize' => true
        );
        $email = Emails::searchOne($registry, $filters);
        if (!$email) {
            $this->executionErrors[] = 'The email template is missing!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        // Define the "soon" date
        $date_format = $registry['translater']->translate('date_iso_short');
        $current_date = General::strftime($date_format);
        $soon_date = General::strftime($date_format, strtotime("-{$settings['unattended_days']} days", strtotime($current_date)));

        // Get all customers that are subject of support
        $query = "
            SELECT c.id                                 AS id,
                TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS name,
                cc.value                                AS office
              FROM " . DB_TABLE_CUSTOMERS . " AS c
              JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                ON(c.id = tm.model_id
                  AND c.active = 1
                  AND c.deleted_by = 0
                  AND c.`type` = 8
                  AND tm.tag_id = 6
                  AND tm.model = 'Customer')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm
                ON (fm.model = 'Customer'
                  AND fm.model_type = c.type
                  AND fm.name = 'bgs_office')
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc
                ON (cc.model_id = c.id
                  AND cc.var_id = fm.id
                  AND cc.num = 1
                  AND cc.lang = '')
              JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                ON(ci.parent_id = c.id
                  AND ci.lang = '{$registry['lang']}')";
        $customers_with_support = $db->GetAssoc($query);
        if (empty($customers_with_support)) {
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }
        $customers_with_support_list = implode(', ', array_keys($customers_with_support));

        /*
         * Get all supported customers and their last support dates
         */
        // Get supported on place
        $query = "
            SELECT d.customer       AS customer_id,
                DATE(MAX(dc.value)) AS last_support_date
              FROM " . DB_TABLE_DOCUMENTS . " as d
              JOIN " . DB_TABLE_FIELDS_META . " as fm
                ON (d.`type` = 5
                  AND d.active = 1
                  AND d.deleted_by = 0
                  AND d.customer IN ({$customers_with_support_list}))
                  AND fm.model_type = d.`type`
                  AND fm.name = 'ppp_start_work'
                  AND fm.model = 'Document'
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " as dc
                ON (dc.model_id = d.id
                  AND dc.lang = ''
                  AND dc.var_id = fm.id
                  AND dc.value != '')
              GROUP BY d.customer";
        $customers_supported_on_place = $db->GetAssoc($query);
        // $query = "
        //     SELECT dc1.value        AS customer_id,
        //         DATE(MAX(dc.value)) AS last_support_date
        //       FROM " . DB_TABLE_DOCUMENTS . " as d
        //       JOIN " . DB_TABLE_FIELDS_META . " as fm
        //         ON (d.active = 1
        //           AND d.deleted_by = 0
        //           AND d.`type` = 85
        //           AND fm.model_type = d.`type`
        //           AND fm.name = 'date_work'
        //           AND fm.model = 'Document')
        //       JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
        //         ON (dc.var_id = fm.id
        //           AND dc.model_id = d.id
        //           AND dc.`value`!=''
        //           AND dc.num = 1
        //           AND dc.lang = '')
        //       JOIN " . DB_TABLE_FIELDS_META . " as fm1
        //         ON (fm1.model_type = d.`type`
        //           AND fm1.name = 'customer_id'
        //           AND fm1.model = 'Document')
        //       JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
        //         ON (dc1.var_id = fm1.id
        //           AND dc1.lang = ''
        //           AND dc1.model_id = d.id
        //           AND dc1.value != ''
        //           AND dc1.value IN ({$customers_with_support_list}))
        //       GROUP BY dc1.value";
        // $customers_supported_remote = $db->GetAssoc($query);
        // $customers_supported = $customers_supported_on_place;
        // foreach ($customers_supported_remote as $customer_id => $last_support_date) {
        //     if (!array_key_exists($customer_id, $customers_supported) || $customers_supported[$customer_id] < $last_support_date) {
        //         $customers_supported[$customer_id] = $last_support_date;
        //     }
        // }
        // Get supported with tickets
        $query = "
            SELECT d.customer           AS customer_id,
                DATE(MAX(tt.endperiod)) AS last_support_date
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_TASKS_RELATIVES . " AS tr
                ON (!d.deleted
                  AND d.active
                  AND d.type = 121
                  AND d.customer IN ({$customers_with_support_list})
                  AND tr.origin = 'document'
                  AND tr.link_to = d.id
                  AND tr.link_type = 'child')
              JOIN " . DB_TABLE_TASKS . " AS t
                ON (!t.deleted
                  AND t.active
                  AND t.type = " . PH_TASK_SYSTEM_TYPE . "
                  AND t.id = tr.parent_id)
              JOIN " . DB_TABLE_TASKS_TIMESHEETS . " AS tt
                ON (!tt.deleted
                  AND tt.task_id = t.id)
              GROUP BY d.customer";
        $customers_supported_with_tickets = $db->GetAssoc($query);

        // Finally, for each customer, get the max date from support on place and tickets
        $customers_supported = $customers_supported_on_place;
        foreach ($customers_supported_with_tickets as $customer_id => $last_support_date) {
            if (!array_key_exists($customer_id, $customers_supported) || $customers_supported[$customer_id] < $last_support_date) {
                $customers_supported[$customer_id] = $last_support_date;
            }
        }

        // Determine which customers are not supported soon
        $customers_supported_soon = array();
        foreach ($customers_supported as $customer_id => $last_support_date) {
            if ($soon_date <= $last_support_date) {
                $customers_supported_soon[$customer_id] = $last_support_date;
            }
        }
        $customers_not_supported_soon = array_keys(array_diff_key($customers_with_support, $customers_supported_soon));
        if (empty($customers_not_supported_soon)) {
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }
        $query = "
            SELECT DISTINCT(trademark_id)
              FROM " . DB_TABLE_CUSTOMERS_TRADEMARKS . "
              WHERE parent_id IN (" . implode(', ', $customers_not_supported_soon) . ")";
        $customers_not_supported_soon_trademarks = $db->GetCol($query);
        $trademarks_customers = array();
        if (!empty($customers_not_supported_soon_trademarks)) {
            $query = "
                SELECT DISTINCT(parent_id)
                  FROM " . DB_TABLE_CUSTOMERS_TRADEMARKS . "
                  WHERE trademark_id IN (" . implode(', ', $customers_not_supported_soon_trademarks) . ")
                    AND parent_id IN ({$customers_with_support_list})";
            $trademarks_customers = $db->GetCol($query);
        }

        // Determine customers to be sent with the mails
        $customers = array_merge($customers_not_supported_soon, $trademarks_customers);
        $offices_customers = array();
        foreach ($customers as $customer_id) {
            if (array_key_exists($customer_id, $customers_supported)) {
              $last_support_date = $customers_supported[$customer_id];
            } else {
                $last_support_date = '';
            }
            $offices_customers[$customers_with_support[$customer_id]['office']][$customer_id] = $last_support_date;
        }

        // Prepare for the mails
        $url_customers_view = $registry['config']->getParam('crontab', 'base_host') . '/index.php?' . $registry['module_param'] . '=customers&amp;customers=view&amp;view=';
        $result = 1;
        $mailer = new Mailer($registry);
        $mailer->templateName = 'custom_template_name';
        $mailer->template['sent_by'] = $registry['currentUser']->get('id');
        $mailer->template['sender'] = $registry['currentUser']->get('email');
        $mailer->template['from_name'] = $registry['currentUser']->get('display_name');
        $mailer->template['replyto_email'] = $registry['config']->getParam('emails', 'replyto_email') ?: $registry['config']->getParam('emails', 'from_email');
        $mailer->template['replyto_name'] = $registry['config']->getParam('emails', 'replyto_name') ?: $registry['config']->getParam('emails', 'from_name');
        $mailer->template['subject'] = $email->get('subject');
        $mailer->template['body'] = $email->get('body');
        unset($email);

        // Send mail for each office
        foreach ($offices_emails_to as $office => $office_emails_to) {
            // Check if there are customers found for this office
            if (!array_key_exists($office, $offices_customers)) {
                continue;
            }

            // Prepare HTML for the list with unattended customers
            $unattended_customers_html = '
              <table cellspacing="0" cellpadding="5" border="1" class="table">
                <tr style="text-align: center; vertical-align: middle;">
                  <th>' . $registry['translater']->translate('automation_bgservice_sendemailsforunattendedclients_th_client') . '</th>
                  <th>' . $registry['translater']->translate('automation_bgservice_sendemailsforunattendedclients_th_last_support_date') . '</th>
                </tr>';
            $office_customers = $offices_customers[$office];
            asort($office_customers);
            foreach ($office_customers as $customer_id => $last_support_date) {
                // Define last support date color
                if ($last_support_date == '' || $last_support_date < $soon_date) {
                    // Color: Not supported soon
                    $last_support_date_color = 'red';
                } else {
                    // Color: Supported soon
                    $last_support_date_color = 'green';
                }
                if ($last_support_date != '') {
                    $last_support_date = General::strftime($registry['translater']->translate('date_short'), $last_support_date);
                } else {
                    $last_support_date = $registry['translater']->translate('automation_bgservice_sendemailsforunattendedclients_never');
                }
                $unattended_customers_html .= '
                  <tr>
                    <td>
                      <a href="' . $url_customers_view . $customer_id . '" target="_blank">' . $customers_with_support[$customer_id]['name'] . '</a>
                    </td>
                    <td style="text-align: center; color: ' . $last_support_date_color . ';">' . $last_support_date . '</td>
                  </tr>';
            }
            $unattended_customers_html .= '
              </table>';

            // Send email for the office
            $mailer->template['recipient'] = array_keys($office_emails_to);
            $mailer->template['names_to'] = array_values($office_emails_to);
            $mailer->placeholder->add('unattended_customers', $unattended_customers_html);
            $mailer_result = $mailer->send();
            if (!empty($mailer_result['erred'])) {
                $this->executionErrors[] = 'Failed to send mail to: ' . implode(', ', $mailer->template['recipient']);
                $result = 0;
            }
        }

        // Write automation history
        $this->updateAutomationHistory($params, 0, $result);

        return $result;
    }

    /**
     * Sends mail to an employee if some of their offers are going to expire
     * and changes status of the offers if necessary
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function offersStatusesAndNotifications($params) {
        // Prepare settings
        $working_days_ago = $this->settings['working_days'];
        $email_template_id = $this->settings['email_template_id'];
        $locked_substatus_id = $this->settings['locked_substatus_id'];
        $closed_substatus_id = $this->settings['closed_substatus_id'];
        if (!$locked_substatus_id || !$email_template_id || !$working_days_ago || !$closed_substatus_id) {
            $this->executionErrors[] = 'Required settings are missing!';
            $this->updateAutomationHistory($params, 0, 0);
            return false;
        }

        // The date of the working day N days ago
        $offer_date = Calendars_Calendar::calcDateOnWorkingDays($this->registry, date('Y-m-d'), $working_days_ago, 'before');
        // Gets documents whose status must be changed
        $query = "
            SELECT d.id
              FROM " . DB_TABLE_DOCUMENTS . " AS d" . "\n" .
              ((preg_match('#^a_#', $this->settings['check_var'])) ?
                "JOIN " . DB_TABLE_FIELDS_META . " AS fm
                  ON(d.active=1
                    AND d.deleted_by=0
                    AND d.`type`={$params['start_model_type']}
                    AND fm.model_type=d.`type`
                    AND fm.model='Document'
                    AND fm.name='" . (preg_replace('#^a_#', '', $this->settings['check_var'])) . "'
                    AND d.`status`='opened')
                JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                  ON(dc.var_id=fm.id
                    AND dc.model_id=d.id
                    AND dc.value='{$offer_date}'
                    AND dc.num=1
                    AND lang IN('{$this->registry['lang']}', ''))" :
            "WHERE d.active=1 AND d.deleted_by=0 AND d.`type`={$params['start_model_type']} AND d.`status`='opened' AND DATE_FORMAT(d." . (preg_replace('#^b_#', '', $this->settings['check_var'])) . ", '%Y-%m-%d')='{$offer_date}'");
        $documents_ids = $this->registry['db']->getCol($query);

        $status = $this->_getStatusBySubstatusId($locked_substatus_id);

        // Change documents status and save history
        $this->_changeDocumentsStatus($documents_ids, $status, $locked_substatus_id);

        // Gets information that will be filled into the email
        $mail_template_info = $this->_getOffersByStatus('locked', '=', false, $this->settings['check_var']);
        if (!empty($mail_template_info)) {
            // Finds email template by ID
            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
            $filters = array(
                'where' => array('e.id=' . $email_template_id),
                'sanitize' => true
            );
            $email = Emails::searchOne($this->registry, $filters);
            if (!$email) {
                $this->executionErrors[] = 'Required email template is missing!';
                $this->updateAutomationHistory($params, 0, 0);
                return false;
            }
            // Gets current date with format dd.mm.yyyy
            $today = General::strftime();

            foreach ($mail_template_info as $email_to => $values) {
                if (empty($values)) {
                    continue;
                }

                // Construction of email
                $html = "
                        <table border='1'>
                          <thead>
                            <tr>
                              <th>{$this->i18n('automation_bgservice_th_offer_number')}</th>
                              <th>{$this->i18n('automation_bgservice_th_offer_date')}</th>
                              <th>{$this->i18n('automation_bgservice_th_customer')}</th>
                              <th>{$this->i18n('automation_bgservice_th_about')}</th>
                            </tr>
                          </thead>
                          <tbody>";
                foreach ($values as $value) {
                    // Document url
                    $url = $this->registry['config']->getParam('crontab', 'base_host') . '/index.php?' . $this->registry['module_param'] . '=documents&amp;documents=view&amp;view=' . $value['document_id'];
                    // Fills the email
                    $html .= "<tr>
                                <td><a href='{$url}' target='_blank'> {$value['full_name']} </a></td>
                                <td>{$value['offer_date']}</td>
                                <td>{$value['customer_name']}</td>
                                <td>{$value['offer_text']}</td>
                              </tr>";
                }
                $html .= "  </tbody>
                        </table>";
                // Initialize email
                $mailer = new Mailer($this->registry);
                $mailer->templateName = 'custom_template_name';
                // Set default sender data for sending email from automation
                $mailer->template['sent_by'] = $this->registry['currentUser']->get('id');
                $mailer->template['sender'] = $this->registry['currentUser']->get('email');
                $mailer->template['from_name'] = $this->registry['currentUser']->get('display_name');
                $mailer->template['replyto_email'] = $this->registry['config']->getParam('emails', 'replyto_email') ? : $this->registry['config']->getParam('emails', 'from_email');
                $mailer->template['replyto_name'] = $this->registry['config']->getParam('emails', 'replyto_name') ? : $this->registry['config']->getParam('emails', 'from_name');
                $mailer->template['recipient'] = array($email_to);
                $mailer->template['names_to'] = array($value['username']);
                $mailer->template['subject'] = $email->get('subject');
                $mailer->template['body'] = $email->get('body');
                // Email placeholders
                $mailer->placeholder->add('expired_offers_user_name', $value['username']);
                $mailer->placeholder->add('expired_offers_table', $html);
                $mailer->placeholder->add('system_date', $today);
                // Send the email
                $result = $mailer->send();
                if (!empty($result['erred'])) {
                    $this->executionErrors[] = 'Failed to send mail to: ' . $email_to;
                    $result = 0;
                } else {
                    $result = 1;
                }
            }
        }
        // Gets all offers that are still not in status finished
        $documents_ids = $this->_getOffersByStatus('locked_' . $locked_substatus_id, '=', true, $this->settings['check_var']);

        $status = $this->_getStatusBySubstatusId($closed_substatus_id);

        // Changes status and substatus of the given documents and save history
        $this->_changeDocumentsStatus($documents_ids, $status, $closed_substatus_id);
        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    private function _getStatusBySubstatusId($substatus_id) {
        $query = "SELECT `status` FROM " . DB_TABLE_DOCUMENTS_STATUSES . " WHERE id=$substatus_id LIMIT 1";
        return $this->registry['db']->getOne($query);
    }

    /**
     * Get documents depends on documents.status column
     * @param string $status - opened, locked, closed
     * @param string $comparison_operator -  = OR !=
     * @return integer|array
     */
    private function _getOffersByStatus($status = 'opened', $comparison_operator = '=', $double_days = false, $var_name='a_offer_date') {
        $additional_var = false;
        $simplify_var_name = preg_replace('#^b_#', '', $var_name);
        if (preg_match('#^a_#', $var_name)) {
            $simplify_var_name = preg_replace('#^a_#', '', $var_name);
            $additional_var = true;
        }

        $status_elements = explode('_', $status);
        $status_where_clause = "d.status{$comparison_operator}'" . $status_elements[0] . "'";
        if (isset($status_elements[1])) {
            $status_where_clause = "d.substatus{$comparison_operator}" . $status_elements[1];
        }

        $query = "
            SELECT
                d.id AS id,
                d.full_num as full_num,
                d.customer as customer_id," .
                (!$additional_var ? 'd.' . $simplify_var_name : 'dc.value') . " AS offer_date,
                dc2.value AS days,
                TRIM(CONCAT(ci.name, ' ', ci.lastname)) AS customer_name,
                u.email AS email,
                TRIM(CONCAT(ui18n.firstname, ' ', ui18n.lastname)) AS username,
                dc4.value AS offer_text
              FROM " . DB_TABLE_DOCUMENTS . " AS d
              JOIN " . DB_TABLE_FIELDS_META . " AS fm2
                ON(d.active=1
                  AND d.deleted_by=0
                  AND d.`type`=13
                  AND fm2.model = 'Document'
                  AND fm2.model_type = d.`type`
                  AND fm2.name='note_validity__days'
                  AND {$status_where_clause})
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc2
                ON(dc2.var_id=fm2.id AND dc2.model_id=d.id)" . "\n" .
              ($additional_var ?
              "JOIN " . DB_TABLE_FIELDS_META . " AS fm
                ON(fm.model_type=d.`type`
                   AND fm.model='Document'
                   AND fm.name='" . $simplify_var_name . "')
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                ON(dc.var_id=fm.id AND dc.model_id=d.id)" . "\n" : '') .
              "JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci
                ON(ci.parent_id=d.customer AND ci.lang='bg')
              JOIN " . DB_TABLE_USERS . " AS u
                ON(u.id = d.added_by)
              JOIN " . DB_TABLE_USERS_I18N . " AS ui18n
                ON(ui18n.parent_id = u.id
                  AND ui18n.lang='bg')
              JOIN " . DB_TABLE_FIELDS_META . " AS fm4
                ON(fm2.model = 'Document'
                  AND fm2.model_type = 13
                  AND fm4.name='offer_text' )
              JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc4
                ON(dc4.var_id=fm4.id AND dc4.model_id=d.id)";

        $documents = $this->registry['db']->getAssoc($query);

        $documents_ids = array();
        $mail_template_info = array();
        $current_date = General::strftime('%Y-%m-%d');
        foreach ($documents as $document_id => $document) {
            $check_day = $double_days ? 2 * intval($document['days']) : intval($document['days']);
            $date = Calendars_Calendar::calcDateOnWorkingDays($this->registry, date("{$document['offer_date']}"), $check_day, 'after');
            if ($double_days && $current_date>=$date) {
                $documents_ids[] = $document_id;
            } elseif (!$double_days && $current_date==$date) {
                // The email of the user that made the offer
                $mail_template_info[$document['email']][] = array(
                    'full_name' => $document['full_num'],
                    'document_id' => $document_id,
                    'offer_date' => date('Y-m-d', strtotime($document['offer_date'])),
                    'customer_name' => $document['customer_name'],
                    'offer_text' => $document['offer_text'],
                    'username' => $document['username'],
                );
            }
        }
        return !$double_days ? $mail_template_info : $documents_ids;
    }

    /**
     * Changes status and substatus of the given documents and save history
     *
     * @param array $documents_ids - Ids of documents whose status will be changed
     * @param string $status - status for the given documents
     * @param int $substatus - substatus for the given documents
     * @return boolean
     */
    private function _changeDocumentsStatus($documents_ids, $status, $substatus) {
        if (empty($documents_ids) || !is_array($documents_ids)) {
            return false;
        }

        $filters = array(
            'where' => array('d.id IN(' . implode(', ', $documents_ids) . ')')
        );
        $result = true;
        $documents = Documents::search($this->registry, $filters);
        foreach ($documents as $document) {
            $document->unsanitize();
            $status_params = array(
                'model_id' => $document->get('id'),
                'module' => 'documents',
                'model' => $document,
                'new_status' => $status,
                'new_substatus' => $substatus,
                'send_mail' => true
            );
            // Try to set the status
            if (!$this->status($status_params)) {
                $this->executionErrors[] = 'Failed to set new status!';
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Attaches generated file from worked time report that incomes reason was
     * created from when an invoice is created for it
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function attachWorkedTimeReportFile(array $params) {
        /** @var Finance_Incomes_Reason $model */
        $model = clone $params['model'];
        if ($model->get('type') == PH_FINANCE_TYPE_INVOICE) {
            $model->getRelatives(
                array(
                    'get_parent_reasons' => !empty($this->settings['fir_type']) ? array("fir.type = '{$this->settings['fir_type']}'") : true,
                )
            );
            $parent = $model->get('parent_reasons');
            if ($parent) {
                $parent = reset($parent);
                $parent->getRelatives(array('get_parent_doc' => true));
                $parent = $parent->get('document') ?: null;

                if ($parent && !empty($this->settings['doc_type']) && $parent->get('type') != $this->settings['doc_type']) {
                    $parent = null;
                }

                if ($parent) {
                    /** @var Document $parent */
                    $parent->unsanitize();
                    $parent->getGeneratedFiles(
                        array(
                            'pattern_id' => !empty($this->settings['doc_pattern_id']) ? $this->settings['doc_pattern_id'] : null,
                        )
                    );
                    $parent->sanitize();
                    $file = $parent->get('genfiles');
                    if ($file) {
                        usort($file, function($a, $b) { return $a->get('id') < $b->get('id') ? -1 : 1; });
                        $file = end($file);

                        if (!$file->get('deleted_by') && !$file->get('not_exist')) {
                            $file_content = General::fileGetContents($file->get('path'));
                            $tmp_file_name = PH_CACHE_DIR . 'tmpc/' . $file->get('name');

                            // copy the file to a temp dir
                            if (FilesLib::writeFile($tmp_file_name, $file_content)) {
                                $file_data = array(
                                    'name'     => $file->get('filename'),
                                    'type'     => mime_content_type($tmp_file_name),
                                    'tmp_name' => $tmp_file_name,
                                    'error'    => 0,
                                    'size'     => filesize($file->get('path')),
                                );
                                $params = array(
                                    'name'        => $file->get('name'),
                                    'description' => $file->get('description'),
                                    'revision'    => '',
                                    'permission'  => 'all',
                                );

                                $file_id = Files::attachFile($this->registry, $file_data, $params, $model);

                                // delete the temp file
                                @unlink($tmp_file_name);

                                if ($file_id) {
                                    // save history even though it is incomplete for 'add_attachments' action in financial module
                                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
                                    require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
                                    Finance_Incomes_Reasons_History::saveData(
                                        $this->registry,
                                        array(
                                            'action_type' => 'add_attachments',
                                            'old_model' => $model,
                                            'new_model' => $model,
                                        ));
                                } else {
                                    $this->registry['messages']->setWarning(
                                        $this->i18n(
                                            'warning_attachment_not_uploaded',
                                            array(
                                                $file->get('filename'),
                                                $file->get('name')
                                            )
                                        )
                                    );
                                    if (!empty(FilesLib::$_errors)) {
                                        foreach (FilesLib::$_errors as $file_upload_error) {
                                            $this->registry['messages']->setWarning($file_upload_error);
                                        }
                                        // clear errors for next iteration
                                        FilesLib::$_errors = array();
                                    }
                                    $this->registry['messages']->insertInSession($this->registry);
                                }
                            }
                        }
                    }
                }
            }
        }

        return true;
    }

    /**
     * Regenerates GT2 of worked time report
     *
     * @see Bgs_Work_Outside_Contracted
     * @param array $params - array of automation settings (fetched from the DB)
     * @return boolean - result of operation
     */
    public function createWorkedTimeReportGt2(array $params) {
        /** @var Document $document */
        $document = $params['model'];

        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', empty($this->before_action), true);
        $vars = $document->getAssocVars();

        if (empty($vars['group_table_2'])) {
            $this->registry->set('get_old_vars', $gov, true);
            return false;
        }
        $gt2 = $vars['group_table_2'];
        $gt2['values'] = array();

        $db = &$this->registry['db'];
/*
        $registry = &$this->registry;
        $entities = array(
            'customer' => array('model' => 'Customer', 'model_type' => $this->settings['customer_type'], 'vars' => array()),
            'contract' => array('model' => 'Contract', 'model_type' => $this->settings['contract_type'], 'vars' => array()),
        );
        $f2 = function($k, $v) {
            return sprintf("$k = '$v'");
        };
        $f1 = function($a) use ($f2) {
            $a = array_filter($a);
            return implode(' AND ', array_map($f2, array_keys($a), $a));
        };
        $query =
            'SELECT model, model_type, name, id' . "\n" .
            'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
            'WHERE type IN ("text", "textarea", "autocompleter", "date", "datetime", "time", "dropdown", "radio")' . "\n" .
            '  AND gt2 = 0 AND (' . implode(' OR ', array_map($f1, $entities)) . ')';
        foreach ($db->GetAll($query) as $var) {
            $e = '';
            if (array_key_exists(strtolower($var['model']), $entities)) {
                $e = strtolower($var['model']);
            } else {
                foreach ($entities as $ek => $ev) {
                    if (!array_diff(array('model', 'model_type'), array_keys(array_intersect_assoc($var, $ev)))) {
                        $e = $ek;
                        break;
                    }
                }
            }
            $entities[$e]['vars'][$var['name']] = $var['id'];
        }

        $customer_ids = array($document->get('customer'));

        // prepare SELECT and JOIN clauses for additional vars of contracts and customers
        $clause_vars = array(
            'monday__to', 'monday__do',
            'saturday__to', 'saturday__do',
            'sunday__to', 'sunday__do',
        );
        $select_format = 'IFNULL(cc%d.value, \'\') AS %s';
        $select_format_int = 'CONVERT(IFNULL(cc%d.value, \'\'), SIGNED INT) AS %s';
        $select_format_float = 'CONVERT(IFNULL(cc%d.value, \'\'), DECIMAL(10,2)) AS %s';
        $join_format = 'LEFT JOIN {$cstm_table} AS cc%1$d ON c.id = cc%1$d.model_id AND cc%1$d.var_id = {$vars[\'%2$s\']} AND cc%1$d.num = 1';

        $select = $join = $join_contract_clause = array();
        $i = 0;
        foreach ($clause_vars as $var) {
            $i++;
            if (strpos($var, '__') !== false) {
                $select[] = sprintf($select_format, $i, $var);
                $join[] = sprintf($join_format, $i, $var);
            } elseif (strpos($var, 'price') !== false) {
                $select[] = sprintf($select_format_float, $i, $var);
                $join_contract_clause[] = sprintf($join_format, $i, $var);
            } else {
                $select[] = sprintf($select_format_int, $i, $var);
                $join_contract_clause[] = sprintf($join_format, $i, $var);
            }
        }
        $select[] = 'IF(cc3.value != \'\' AND cc4.value != \'\' AND cc3.value < cc4.value, 1, 0) AS working_6';
        $select[] = 'IF(cc5.value != \'\' AND cc6.value != \'\' AND cc5.value < cc6.value, 1, 0) AS working_7';
        $select_clause = implode(",\n", $select);
        $join_contract_clause = EvalString::evaluate(
            $registry,
            '"' . implode("\n", $join_contract_clause) . '";',
            array(
                'cstm_table' => DB_TABLE_CONTRACTS_CSTM,
                'vars' => $entities['contract']['vars']
            )
        );
        $join_working_hours_clause = '"' . implode("\n", $join) . '";';

        // search contracted conditions in contracts
        $query =
            'SELECT c.customer AS idx, c.id AS contract,' . "\n" .
            str_replace(
                array(
                    'IFNULL(cc2.value, \'\')',
                ),
                array(
                    'IF(cc2.value IS NOT NULL AND cc2.value != \'\', cc2.value, \'' . $this->settings['working_day_end'] . '\')',
                ),
                $select_clause
            ) . "\n" .
            'FROM ' . DB_TABLE_CONTRACTS . ' AS c' . "\n" .
            preg_replace('#^LEFT #', '', EvalString::evaluate(
                $registry,
                $join_working_hours_clause,
                array(
                    'cstm_table' => DB_TABLE_CONTRACTS_CSTM,
                    'vars' => $entities['contract']['vars']
                )
            )) . "\n" .
            $join_contract_clause . "\n" .
            'WHERE c.type = \'' . $this->settings['contract_type'] . '\' AND c.subtype = \'contract\'' . "\n" .
            '  AND c.status = \'closed\' AND c.active = 1 AND c.deleted_by = 0' . "\n" .
            '  AND c.customer IN (' . implode(', ', $customer_ids) . ')' . "\n" .
            // get newest contract per customer
            'HAVING c.id = (SELECT MAX(c0.id) FROM ' . DB_TABLE_CONTRACTS . ' AS c0' . "\n" .
            '  WHERE c0.customer = c.customer AND c0.type = c.type' . "\n" .
            '    AND c0.subtype = \'contract\' AND c0.status = \'closed\' AND c0.active = 1 AND c0.deleted_by = 0)' . "\n" .
            // monday__to is required to be filled in in order to use working hours from contract
            '  AND monday__to != \'\'' . "\n";
        $contracted = $db->GetAssoc($query);

        // search for working hours in customer profile when not set in contract
        // but always take contracted conditions from contract
        $customer_ids = array_diff($customer_ids, array_keys($contracted));

        if ($customer_ids) {
            $query =
                'SELECT c.id AS idx, IFNULL(co.id, 0) AS contract,' . "\n" .
                str_replace(
                    array(
                        'IFNULL(cc1.value, \'\')',
                        'IFNULL(cc2.value, \'\')',
                    ),
                    array(
                        'IF(cc1.value IS NOT NULL AND cc1.value != \'\', cc1.value, \'' . $this->settings['working_day_start'] . '\')',
                        'IF(cc2.value IS NOT NULL AND cc2.value != \'\', cc2.value, \'' . $this->settings['working_day_end'] . '\')',
                    ),
                    $select_clause
                ) . "\n" .
                'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                'JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                '  ON co.customer = c.id AND co.type = \'' . $this->settings['contract_type'] . '\' AND co.subtype = \'contract\'' . "\n" .
                '    AND co.status = \'closed\' AND co.active = 1 AND co.deleted_by = 0' . "\n" .
                EvalString::evaluate(
                    $registry,
                    $join_working_hours_clause,
                    array(
                        'cstm_table' => DB_TABLE_CUSTOMERS_CSTM,
                        'vars' => $entities['customer']['vars']
                    )
                ) . "\n" .
                str_replace('c.id = ', 'co.id = ', $join_contract_clause) . "\n" .
                'WHERE c.id IN (' . implode(', ', $customer_ids) . ')' . "\n" .
                // get newest contract per customer
                'HAVING contract = (SELECT MAX(c0.id) FROM ' . DB_TABLE_CONTRACTS . ' AS c0' . "\n" .
                '  WHERE c0.customer = co.customer AND c0.type = co.type' . "\n" .
                '    AND c0.subtype = \'contract\' AND c0.status = \'closed\' AND c0.active = 1 AND c0.deleted_by = 0)';
            $contracted = $db->GetAssoc($query) + $contracted;
        }

        $contracted = $contracted ? reset($contracted) : array_fill_keys($clause_vars, '');
        $contracted['dates'] = array();
*/

        $doc_ids = !empty($this->settings['docid']) && !empty($vars[$this->settings['docid']]['value']) ? $vars[$this->settings['docid']]['value'] : array();
        $doc_types =
            $doc_ids ?
            $db->GetAssoc("
                SELECT id, type FROM " . DB_TABLE_DOCUMENTS . "
                WHERE id IN ('" . implode("', '", $doc_ids) . "')
                  AND type IN ('{$this->settings['doc_type_visit']}', '{$this->settings['doc_type_remote']}')
            ") :
            array();
        $doc_dates = !empty($this->settings['docdate']) && !empty($vars[$this->settings['docdate']]['value']) ? $vars[$this->settings['docdate']]['value'] : array();
        $working = !empty($this->settings['working_rounded']) && !empty($vars[$this->settings['working_rounded']]['value']) ? $vars[$this->settings['working_rounded']]['value'] : array();
        $overtime = !empty($this->settings['overtime_rounded']) && !empty($vars[$this->settings['overtime_rounded']]['value']) ? $vars[$this->settings['overtime_rounded']]['value'] : array();

        $data = array();
        foreach ($doc_ids as $idx => $doc_id) {
            if (!empty($doc_types[$doc_id]) && !empty($doc_dates[$idx]) && Validator::validDate($doc_dates[$idx]) && (!empty($working[$idx]) || !empty($overtime[$idx]))) {
                /* $date = $doc_dates[$idx];
                $start = new DateTime($date);//TODO we don't know start datetime???
                // day of week (1 to 7)
                $dow = ($start)->format('N');
                // find out if date is a working day for customer or not
                if (!isset($contracted['dates'][$date])) {
                    // 1 = working day, 0 = not a working day
                    // use last parameter to specify if Saturday/Sunday is a working
                    // day according to contracted conditions of customer
                    $contracted['dates'][$date] = Calendars_Calendar::getWorkingDays(
                        $registry, $date, $date, ($dow > 5 && !empty($contracted["working_$dow"]) ? $dow : 5)
                    );
                }
                // work is considered inside or outside working hours depending on its start
                if (!$contracted['dates'][$date]) {
                    // not on a working day = outside working hours
                    $first = 'overtime';
                } else {
                    // on a working day
                    $fld = $dow <= 5 ? 'monday' : ($dow == 6 ? 'saturday' : 'sunday');

                    // check if start is inside or outside working hours
                    // if checked field is empty, consider outside working hours
                    $first = empty($contracted["{$fld}__to"]) || $start->format('H:i') < $contracted["{$fld}__to"] ? 'overtime' : 'working';
                } */

                $data[$idx] = array(
                    'idx' => $idx,
                    'id' => $doc_id,
                    'type' => $doc_types[$doc_id],
                    'docdate' => $doc_dates[$idx],
                    'month' => General::strftime('%Y-%m', $doc_dates[$idx]),
                    'working' => !empty($working[$idx]) ? floatval($working[$idx]) : 0,
                    'overtime' => !empty($overtime[$idx]) ? floatval($overtime[$idx]) : 0,
                    // assume that visit is counted as one outside working hours only when reported working time is 0
                    'first' => empty($working[$idx]) ? 'overtime' : 'working',
                );
            }
        }
        uasort($data, function($a, $b) {
            if ($a['docdate'] != $b['docdate']) {
                return $a['docdate'] > $b['docdate'] ? 1 : -1;
            } else {
                return $a['idx'] > $b['idx'] ? 1 : -1;
            }
        });

        $final_results = array(
            'to_invoice' => array(),
            'visits' => array(),
        );
        // get contracted data from the configurator
        foreach (array(
            'working_visit_num',
            'working_visit_price',
            'working_hour_num',
            'working_hour_price',
            'overtime_visit_num',
            'overtime_visit_price',
            'overtime_hour_num',
            'overtime_hour_price',
        ) as $param) {
            $final_results[$param] = !empty($this->settings[$param]) && !empty($vars[$this->settings[$param]]['value']) ? floatval($vars[$this->settings[$param]]['value']) : 0;
        }

        $current_month = null;
        foreach ($data as $d) {
            if (!isset($current_month) || $current_month != $d['month']) {
                $current_month = $d['month'];
                $final_results['to_invoice'][$d['month']] = array();

                // temporary values to subtract from - they are reset for each month
                foreach (array('working_visit_num', 'overtime_visit_num', 'working_hour_num', 'overtime_hour_num') as $fld) {
                    $final_results["tmp_{$fld}"] = $final_results[$fld];
                    // initialize field values for currently processed month
                    $final_results['to_invoice'][$d['month']][$fld] = 0;
                }
            }

            // number of visits
            if ($d['type'] == $this->settings['doc_type_visit']) {
                // which data to update: overtime = outside working hours, working = inside working hours
                $num = "{$d['first']}_visit_num";

                // accumulate number of visits per month - once per document
                if (!in_array($d['id'], $final_results['visits'])) {
                    $final_results['visits'][] = $d['id'];

                    if ($final_results["tmp_{$num}"] > 0) {
                        $final_results["tmp_{$num}"]--;
                    } else {
                        $final_results['to_invoice'][$d['month']][$num]++;
                    }
                }
            }

            // rounded worked hours: use directly the submitted value
            foreach (array('working', 'overtime') as $interval) {
                if ($d[$interval]) {
                    $to_invoice = $d[$interval];

                    // subtraction of contracted hours
                    if ($final_results["tmp_{$interval}_hour_num"] > 0) {
                        $final_results["tmp_{$interval}_hour_num"] -= $to_invoice;
                        if ($final_results["tmp_{$interval}_hour_num"] < 0) {
                            $to_invoice = -1 * $final_results["tmp_{$interval}_hour_num"];
                            $final_results["tmp_{$interval}_hour_num"] = 0;
                        } else {
                            $to_invoice = 0;
                        }
                    }

                    $hour_num = "{$interval}_hour_num";
                    $final_results['to_invoice'][$d['month']][$hour_num] += $to_invoice;
                }
            }
        }

        // calculate monthly subtotals according to contracted prices
        if (!empty($final_results['to_invoice'])) {
            foreach ($final_results['to_invoice'] as $month => $nums) {
                $to_invoice = 0;
                foreach ($nums as $tti => $hour_num) {
                    $to_invoice += $hour_num * $final_results[preg_replace('#^(.*)_num$#', '$1_price', $tti)];
                }
                if ($to_invoice) {
                    $final_results['to_invoice'][$month] = $to_invoice;
                } else {
                    unset($final_results['to_invoice'][$month]);
                }
            }
        }

        // default blank company data
        $blank_company_data = array_fill_keys(array(
            'has_contract', 'currency', 'total_vat_rate',
            'company', 'company_customer', 'company_customer_name',
        ), '');

        // mapping of financial companies to customers
        $own_company_mapping = $this->settings;
        $own_company_mapping = array_intersect_key(
            $own_company_mapping,
            array_flip(array_filter(
                array_keys($own_company_mapping),
                function($k) { return preg_match('#^own_company\d+$#', $k); }
            ))
        );
        if ($own_company_mapping) {
            $own_company_clause = 'CASE co.company' . "\n";
            foreach ($own_company_mapping as $k => $v) {
                $own_company_clause .= 'WHEN \'' . str_replace('own_company', '', $k) . '\' THEN \'' . $v . '\'' . "\n";
            }
            $own_company_clause .= 'ELSE \'\' END' . "\n";
        } else {
            $own_company_clause = '\'\'';
        }

        // get company from document vars
        $company_id = !empty($this->settings['company']) && !empty($vars[$this->settings['company']]['value']) ? floatval($vars[$this->settings['company']]['value']) : 0;

        // get own company data from contracts (even if contracted parameters are taken from customer)
        $query =
            'SELECT c.id AS idx, co.id AS has_contract,' . "\n" .
            'cocstm_currency.value AS currency, cocstm_vat.value AS total_vat_rate,' . "\n" .
            'co.company,' . "\n" .
            $own_company_clause . ' AS company_customer, TRIM(CONCAT(ci18n_own.name, \' \', ci18n_own.lastname)) AS company_customer_name' . "\n" .
            'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
            '  ON c.id = co.customer AND co.type = \'' . $this->settings['contract_type'] . '\'' . "\n" .
            '    AND co.subtype = \'contract\' AND co.status = \'closed\' AND co.active = 1 AND co.deleted_by = 0' . "\n" .
            ($company_id ? '    AND co.company = \'' . $company_id . '\'' : '') . "\n" .
            'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_currency' . "\n" .
            '  ON fm_currency.model = \'Contract\' AND fm_currency.model_type = co.type' . "\n" .
            '    AND fm_currency.gt2 = 1 AND fm_currency.name = \'currency\'' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS cocstm_currency' . "\n" .
            '  ON fm_currency.id = cocstm_currency.var_id AND cocstm_currency.model_id = co.id AND cocstm_currency.num = 1' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm_vat' . "\n" .
            '  ON fm_vat.model = \'Contract\' AND fm_vat.model_type = co.type' . "\n" .
            '    AND fm_vat.gt2 = 1 AND fm_vat.name = \'total_vat_rate\'' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' AS cocstm_vat' . "\n" .
            '  ON fm_vat.id = cocstm_vat.var_id AND cocstm_vat.model_id = co.id AND cocstm_vat.num = 1' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n_own' . "\n" .
            '  ON ci18n_own.parent_id = ' . $own_company_clause . ' AND ci18n_own.lang = \'' . $document->get('model_lang') . '\'' . "\n" .
            'WHERE c.id IN (\'' . implode('\', \'', array($document->get('customer'))) . '\')' . "\n" .
            // get newest contract per customer
            'HAVING co.id IS NULL OR co.id = (SELECT MAX(c0.id) FROM ' . DB_TABLE_CONTRACTS . ' AS c0' . "\n" .
            '  WHERE c0.customer = co.customer AND c0.type = co.type' . "\n" .
            '    AND c0.subtype = \'contract\' AND c0.status = \'closed\' AND c0.active = 1 AND c0.deleted_by = 0)';
        $company_data = $db->GetRow($query);

        if (empty($company_data)) {
            $company_data = $blank_company_data;
            // get defaults for company
            if ($company_id) {
                $query = "
                    SELECT fc.id AS company, fc.default_currency AS currency, IFNULL(fvr.value, 0) AS total_vat_rate
                    FROM " . DB_TABLE_FINANCE_COMPANIES . " AS fc
                    LEFT JOIN " . DB_TABLE_FINANCE_VAT_RATES . " AS fvr
                      ON fc.VAT_registered = 1
                        AND fvr.parent_id = fc.id
                        AND fvr.active = 1
                        AND fvr.is_default = 1
                   WHERE fc.id = '$company_id'";
                $company_data = ($db->GetRow($query) ?: array()) + $company_data;

                // set company customer from settings
                if (!empty($own_company_mapping["own_company{$company_id}"])) {
                    $company_data['company_customer'] = $own_company_mapping["own_company{$company_id}"];
                    $company_data['company_customer_name'] = $db->GetOne("
                        SELECT TRIM(CONCAT(name, ' ', lastname))
                        FROM " . DB_TABLE_CUSTOMERS_I18N . "
                        WHERE parent_id = '{$company_data['company_customer']}' AND lang = '{$document->get('model_lang')}'
                    ");
                }
            }
        }

        // GT2 values
        if (!empty($final_results['to_invoice'])) {
            // get article data
            $article_data = array('article_id' => '0', 'article_code' => '', 'article_name' => '',);
            if (!empty($this->settings['work_article_id'])) {
                $query =
                    'SELECT n.id AS article_id, n.code AS article_code, ni18n.name AS article_name' . "\n" .
                    'FROM ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                    '  ON n.id = ni18n.parent_id AND ni18n.lang = \'' . $document->get('model_lang') . '\'' . "\n" .
                    'WHERE n.id = \'' . $this->settings['work_article_id'] . '\'';
                $article_data = $db->GetRow($query) ?: $article_data;
            }
            // period description text
            $description_text = $this->i18n('automations_overtime_period_description');

            $idx = 0;
            foreach ($final_results['to_invoice'] as $month => $price) {
                $gt2['values'][--$idx] = $article_data + array(
                    'article_description' => sprintf($description_text, date_create($month . '-01')->format('01.m.Y - t.m.Y')),
                    'article_measure_name' => 1,
                    'article_deliverer' => $company_data['company_customer'],
                    'article_deliverer_name' => $company_data['company_customer_name'],
                    'price' => $price,
                    'price_with_discount' => $price,
                    'quantity' => 1,
                    'discount_surplus_field' => 'none',
                    'discount_value' => 0,
                    'discount_percentage' => 0,
                    'surplus_value' => 0,
                    'surplus_percentage' => 0,
                );
            }
        }
        // GT2 plain values (even for empty table)
        $gt2['plain_values'] = array(
            'currency' => Finance_Currencies::getMain($this->registry),
            'total_vat_rate' => '0.00',
            'total_discount_surplus_field' => 'none',
            'total_discount_value' => 0,
            'total_discount_percentage' => 0,
            'total_surplus_value' => 0,
            'total_surplus_percentage' => 0,
        );
        if (!empty($company_data['currency'])) {
            $gt2['plain_values']['currency'] = $company_data['currency'];
        }
        if (isset($company_data['total_vat_rate']) && $company_data['total_vat_rate'] !== '') {
            $gt2['plain_values']['total_vat_rate'] = $company_data['total_vat_rate'];
        }

        $this->registry['db']->StartTrans();

        if (!$document->saveGT2Vars($gt2)) {
            $this->registry['db']->FailTrans();
        } else {
            $document->slashesStrip();
            if (!empty($this->before_action)) {
                $old_model = clone $document->get('old_model');
            } else {
                $old_model = clone $document;
                $old_model->unsetProperty('old_model', true);
            }
            $this->registry->set('get_old_vars', true, true);
            $document->unsetVars();
            $document->getVars();

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => 'edit',
                    'new_model' => $document,
                    'old_model' => $old_model,
                )
            );
        }

        $this->registry->set('get_old_vars', $gov, true);
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();
        return $result;
    }

    /**
     * Set ticket for approval if the customer has no contract for support
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function setTicketForApproval($params)
    {
        $result = true;
        $document = $params['model'];
        /**
         * @var $db ADOConnection
         */
        $db = $this->registry['db'];

        if ($document->get('customer') == $this->settings['unknown_customer'] ||
            $this->isSupportedCustomer($document->get('customer'), $this->settings['tag_support'])) {
            //the customer or the companies within the group
            // DO HAVE support contract
            if (empty($params['action']) || $params['action'] == 'add') {
                //no action upon adding it via emails2nzoom
                return $result;
            }

            //EDIT MODE
            //set the status
            $status_params = array(
                'model_id' => $document->get('id'),
                'module' => 'documents',
                'model' => $document,
                'new_status' => $this->settings['status'],
                'new_substatus' => '',
                'send_mail' => false
            );

            $db->StartTrans();
            // Try to set the status
            if (!$this->status($status_params)) {
                $db->FailTrans();
            }
            if ($db->HasFailedTrans()) {
                $db->CompleteTrans();
                $result = false;
                return $result;
            }

            if (!empty($this->settings['assign_owner'])) {
                // remove observers
                $remove_assignments_params = array(
                    'model_id' => $document->get('id'),
                    'module' => 'documents',
                    'model' => $document,
                    'remove_assign_owner' => 1
                );
                // If the assignments failed
                if (!$this->removeAssignments($remove_assignments_params)) {
                    $db->FailTrans();
                }
            }

            $result = $db->HasFailedTrans();
            $db->CompleteTrans();
        } else {

            $db->StartTrans();
            $status_params = array(
                'model_id' => $document->get('id'),
                'module' => 'documents',
                'model' => $document,
                'new_status' => $this->settings['status'],
                'new_substatus' => $this->settings['substatus'],
                'send_mail' => false
            );

            // Try to set the status
            if (!$this->status($status_params)) {
                $db->FailTrans();
            }
            if ($db->HasFailedTrans()) {
                $db->CompleteTrans();
                $result = false;
                return $result;
            }

            if (!empty($this->settings['assign_owner'])) {
                // Update the observers
                $assignments_params = array(
                    'model_id' => $document->get('id'),
                    'module' => 'documents',
                    'model' => $document,
                    'new_assign_owner' => $this->settings['assign_owner']
                );
                // If the assignments failed
                if (!$this->assign($assignments_params)) {
                    $db->FailTrans();
                }
            }

            $result = $db->HasFailedTrans();
            $db->CompleteTrans();
        }

        return $result;
    }

    /**
     * Checks whether customer has a support contract or
     * if there is supported customer within the customer subgroup
     *
     * @param int $customerId - id of the customer
     * @param string $tagIdsCSV - CSV list of tag IDs defining support contract
     * @return bool $supported - result of the execution of the method
     */
    public function isSupportedCustomer($customerId, $tagIdsCSV) {
        $supported = false;

        //get all the customers in the group
        //ToDo: add setting for client_id variable (102237)
        $query = "SELECT nc.value
                 FROM " . DB_TABLE_NOMENCLATURES_CSTM . " as nc
                 WHERE nc.var_id=102237 AND nc.model_id IN (
                    SELECT nc2.model_id
                    FROM " . DB_TABLE_NOMENCLATURES_CSTM . " as nc2
                    WHERE nc2.var_id=102237
                      AND nc2.value=$customerId
                 )";
        $customerIds = $this->registry['db']->GetCol($query);
        array_push($customerIds, $customerId);

        $customerIdsCSV = implode(',' , array_unique($customerIds));

        $query = "SELECT COUNT(model_id)
                 FROM " . DB_TABLE_TAGS_MODELS . "
                 WHERE model='Customer'
                       AND tag_id IN ({$tagIdsCSV})
                       AND model_id IN ({$customerIdsCSV})
                 ";
        $taggedCustomersCount = $this->registry['db']->GetOne($query);

        if ($taggedCustomersCount > 0) {
            $supported = true;
        }

        return $supported;
    }

    /**
     * Merges document to another document, copying emails, comments, minitasks and timesheets
     * Sets status to the document and closes the system task
     *
     * @param array $params - arguments for the method, containing registry
     * @return bool - result of the execution of the method
     */
    public function mergeTicket($params)
    {
        $document = $params['model'];
        $old_document = clone $document;
        $db = $this->registry['db'];

        $db->StartTrans();

        //check status
        if ($document->get('status') == $this->settings['status'] &&
            $document->get('substatus') == $this->settings['substatus']) {
            $db->CompleteTrans();
            return true;
        }

        $mergeDestinationID = $document->getVarValue($this->settings['var_merge_destination_document_id']);
        if (!$mergeDestinationID) {
            $this->registry['messages']->setError($this->i18n('error_automation_bgservice_no_merge_destination_document_id'));
            $this->registry['messages']->insertInSession($this->registry);
            $db->FailTrans();
            $db->CompleteTrans();
            return true;
        }

        //check relatives
        $relatives = $document->getFirstLevelRelatedDocuments('parent');
        if (!empty($relatives) && in_array($mergeDestinationID, array_column($relatives, 'id'))) {
            $this->registry['messages']->setError($this->i18n('error_automation_bgservice_merge_relatives_error'));
            $this->registry['messages']->insertInSession($this->registry);
            $db->FailTrans();
            $db->CompleteTrans();
            return true;
        }

        $this->setOriginalUserAsCurrent();

        ///merge
        if (!$document->merge($mergeDestinationID)) {
            $this->registry['messages']->setError($this->i18n('error_automation_bgservice_no_merge_destination_document_id'));
            $this->registry['messages']->insertInSession($this->registry);
            $db->FailTrans();
            $db->CompleteTrans();
            $this->setAutomationUserAsCurrent();
            return false;
        }

        //set status
        $document->set('status', $this->settings['status'], true);
        $document->set('substatus', "{$this->settings['status']}_{$this->settings['substatus']}", true);

        if (!$document->setStatus()) {
            $db->FailTrans();
            $db->CompleteTrans();
            $this->setAutomationUserAsCurrent();
            return false;
        } else {
            $filters = array('where' => array('d.id = ' . $document->get('id')),
                'model_lang' => $document->get('model_lang'),
                'skip_assignments' => true,
                'skip_permissions_check' => true);
            $new_document = Documents::searchOne($this->registry, $filters);

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => 'status',
                    'new_model' => $new_document,
                    'old_model' => $old_document
                )
            );
        }

        //close the system task
        if ($this->settings['status'] == 'closed') {
            $system_task_id = Documents::getSystemTask($this->registry, $document->get('id'));
            if ($system_task_id) {
                $task = Tasks::searchOne(
                    $this->registry,
                    array(
                        'where' => array(
                            't.id = ' . $system_task_id,
                            't.type = ' . PH_TASK_SYSTEM_TYPE
                        )
                    )
                );

                if ($task) {
                    //open the status of task
                    $task->set('status', 'finished', true);
                    $task->setStatus();
                }
            }
        }

        $result = !$db->HasFailedTrans();
        if ($result) {
            $filters = array('where' => array("d.id = $mergeDestinationID"));
            $mergeDestinationDocument = Documents::searchOne($this->registry, $filters);
            $this->registry['messages']->setMessage(
                $this->i18n(
                    'message_automation_bgservice_merge_success',
                    array($mergeDestinationDocument->get('full_num'))
                ),
                '',
                -2
            );
            $this->registry['messages']->insertInSession($this->registry);
        }
        $db->CompleteTrans();

        $this->setAutomationUserAsCurrent();

        return $result;

    }

    /**
     * Function to prepare the default values of the deadlines
     *
     * @param array $params - array of params for the automation
     * @return bool - result of the operation
     */
    public function completeTicketDeadlines($params) {
        // define priority
        $document = $params['model'];
        $assoc_vars = $document->getAssocVars();
        $document->unsanitize();

        // calculate based on which settings var
        if (preg_match('#^a_#', $this->settings['calculation_based_on'])) {
            if (!empty($assoc_vars[preg_replace('#^a_#', '', $this->settings['calculation_based_on'])])) {
                $calculate_from_time = $assoc_vars[preg_replace('#^a_#', '', $this->settings['calculation_based_on'])]['value'];
            }
        } elseif (preg_match('#^b_#', $this->settings['calculation_based_on'])) {
            $calculate_from_time = $document->get(preg_replace('#^b_#', '', $this->settings['calculation_based_on']));
            $document->get($calculate_from_time);
        }
        if (empty($calculate_from_time)) {
            $calculate_from_time = date('Y-m-d H:i:s');
        }

        // define the array with vars to be completed
        $vars_complete_list = array();
        foreach ($this->settings as $setting_name => $setting_value) {
            if (preg_match('#^ticket_a_#', $setting_name)) {
                if (!empty($assoc_vars[preg_replace('#^ticket_a_#', '', $setting_name)])) {
                    $vars_complete_list[$setting_value] = preg_replace('#^ticket_#', '', $setting_name);
                }
            } elseif (preg_match('#^ticket_b_#', $setting_name)) {
                $vars_complete_list[$setting_value] = preg_replace('#^ticket_#', '', $setting_name);
            }
        }

        foreach ($vars_complete_list as $var_name_source => $var_name_target) {
            $var_options = $assoc_vars[$var_name_source]['options'] ?? array();
            if ($var_options) {
                // get the current var value
                $current_var_value = $assoc_vars[$var_name_source]['value'];

                if ($current_var_value) {
                    // calculate basic var datetime
                    $new_value = Calendars_Calendar::calculateBasedOnWorkTime($this->registry, $calculate_from_time, (int)$current_var_value, $this->settings['working_day_start'], $this->settings['working_day_end']);
                    $type_var = preg_replace('#^(a|b)_.*$#', '$1', $var_name_target);

                    $params_temp = array(
                        'model'     => $document,
                        'var_name'  => preg_replace('#^(a|b)_#', '', $var_name_target),
                        'var_value' => $new_value,
                        'after_action' => '',
                        'model_id' => $document->get('id'),
                        'id' => $params['id'],
                        'controller' => $this->registry['controller'],
                        'module' => $this->registry['module']
                    );

                    if ($type_var == 'a') {
                        $this->setAdditionalVar($params_temp);
                    } elseif ($type_var == 'b') {
                        $this->setBasicVar($params_temp);
                    }
                }
            }
        }

        return true;
    }

    /**
     * Function to add timesheets for document
     *
     * @param object $params - array with params for the automation
     * @return bool - result of the operation
     */
    public function createTimesheetsForVisitProtocols($params) {
        $this->isExecuted = true;
        $document = $params['model'];
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $document->getVars();
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $assoc_vars = $document->getAssocVars();
        $employees = (isset($assoc_vars[$this->settings['timesheet_employee_id']]['value']) ? $assoc_vars[$this->settings['timesheet_employee_id']]['value'] : array());
        $durations = (isset($assoc_vars[$this->settings['timesheet_duration_id']]['value']) ? $assoc_vars[$this->settings['timesheet_duration_id']]['value'] : array());

        $employees = array_filter($employees);
        $sql = 'SELECT u.employee, u.id, CONCAT(ui18n.firstname, " ", ui18n.lastname) as name, u.office, oi18n.name as office_name FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
               'INNER JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
               ' ON (ui18n.parent_id=u.id AND ui18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'LEFT JOIN ' . DB_TABLE_OFFICES_I18N . ' as oi18n' . "\n" .
               ' ON (oi18n.parent_id=u.office AND oi18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE u.employee IN ("' . implode('","', $employees) . '") AND u.is_portal=0' . "\n" .
               'ORDER BY u.id DESC' . "\n";
        $employees_data = $this->registry['db']->GetAll($sql);

        $employees_users = array();
        foreach ($employees_data as $emp_dat) {
            if (isset($employees_users[$emp_dat['employee']])) {
                continue;
            }
            $employees_users[$emp_dat['employee']] = $emp_dat;
        }

        $start_date = new DateTime($document->get('date'));
        $system_task_id = 0;
        $result = true;
        foreach ($employees as $key => $employee) {
            if (!isset($employees_users[$employee]) || !isset($durations[$key])) {
                continue;
            }
            $duration = intval($durations[$key]);
            if (!$duration) {
                // no transport time so skip this row
                continue;
            }

            // Get the id of the system task, which should already be created for the current document
            if (!$system_task_id) {
                $system_task_id = Documents::getSystemTask($this->registry, $document->get('id'));
                if (!$system_task_id) {
                    $this->registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');
                    $document->unsanitize();
                    $document->createSystemTask();
                    $system_task_id = Documents::getSystemTask($this->registry, $document->get('id'));
                }

                if (!$system_task_id) {
                    // no system task - quit execution and throw error
                    $this->executionErrors[] = "System task could not be created for document with ID {$document->get('id')}";
                    break;
                }
            }

            // Prepare an array with parameters for the timesheet
            $p = array();
            $p['startperiod_period'] = $p['endperiod_period'] = $start_date->format('Y-m-d 00:00:00');
            $p['period_type'] = 'period';
            $p['model_id'] = $system_task_id;

            // Prepare some standard parameters
            $p['resource'] = 'human';
            $p['parent_module'] = 'documents';

            // Set the start and end date of the timesheet
            $p['duration'] = strval($duration);

            // Set the user and the office
            $p['user_id'] = $employees_users[$employee]['id'];
            $p['office'] = $employees_users[$employee]['office'];
            $p['user_id_name'] = $employees_users[$employee]['name'];
            $p['office_name'] = $employees_users[$employee]['office_name'];

            $p['activity'] = $this->settings['timesheet_activity'];
            $p['subject'] = '';
            $p['content'] = $this->settings['timesheet_description'];
            $p['activity_name'] = $this->settings['timesheet_description'];

            // Build a new timesheet model
            $timesheet = new Tasks_Timesheet($this->registry, $p);
            // Try to save the timesheet
            if ($timesheet->save() && $timesheet->get('id')) {
                $timesheet->saveHistory($document);
            } else {
                $this->executionErrors[] = "Could not save timesheet for {$employees_users[$employee]['name']} for document with ID {$document->get('id')}";
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Function to complete the free days left when a leave request is added
     *
     * @param object $params - array with params for the automation
     * @return bool - result of the operation
     */
    public function completeFreeDaysLeft($params) {
        $model = $params['model'];
        $model->unsanitize();
        $assoc_vars = $model->getAssocVars();
        $messages = &$this->registry['messages'];

        // Get the number of remaining paid days off for the selected year and employee
        $report_name = 'hr_employee_file';
        require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report_name . '/custom.report.query.php';
        //load plugin i18n files
        $i18n_file = sprintf('%s%s%s%s%s%s',
            PH_MODULES_DIR,
            'reports/plugins/',
            $report_name,
            '/i18n/',
            $this->registry['lang'],
            '/reports.ini'
        );

        $this->registry['translater']->loadFile($i18n_file);
        $report = Reports::getReports($this->registry,
                                      array('name'     => $report_name,
                                            'sanitize' => true));

        if (!isset($report[0])) {
            $messages->setWarning($this->i18n('warning_automation_bgservice_complete_free_days_left_no_report'));
            $messages->insertInSession($this->registry);
            return false;
        }

        $report = $report[0];
        Reports::getReportSettings($this->registry, $report->get('type'));

        $year = $assoc_vars[$this->settings['days_off_year']]['value'];
        $filters = array('employee'                     => $model->get('customer'),
                         'years'                        => $year,
                         'validation_required_days_off' => true,
                         'exclude_documents'            => array($model->get('id')));
        $report_results = Hr_Employee_File::buildQuery($this->registry, $filters);

        // get the days for that year
        $date_start_period = $year . '-01-01';
        if ($report_results['date_start_working'] > $date_start_period) {
            $date_start_period = $report_results['date_start_working'];
        }
        $date_end_period = $year . '-12-31';

        if (!empty($assoc_vars[$this->settings['days_off_end_date']]['value']) && $assoc_vars[$this->settings['days_off_end_date']]['value'] < $date_end_period) {
            $date_end_period = $assoc_vars[$this->settings['days_off_end_date']]['value'];
        }
        if (!empty($report_results['contract_chronology_list'])) {
            $periods = Hr_Employee_File::processPeriods(date_create($date_start_period), date_create($date_end_period), $report_results['contract_chronology_list']);
            $days_available = 0;
            foreach($periods as $per) {
                $days_available += Hr_Employee_File::calculateAvailableFreeDaysPerYear(
                    $year,
                    clone($per['from']),
                    clone($per['to']),
                    $per['days_off']
                );
            }
        } else {
            if ($report_results['date_end_working'] && $report_results['date_end_working'] < $date_end_period) {
                $date_end_period = $report_results['date_end_working'];
            }
            $days_available = Hr_Employee_File::calculateAvailableFreeDaysPerYear($year, date_create($date_start_period), date_create($date_end_period), $report_results['contract_available_days_off']);
        }

        if (!empty($report_results['days_off_list'][$year])) {
            $days_left = $days_available - array_sum($report_results['days_off_list'][$year]['used_by_year']) - $report_results['days_off_list'][$year]['requested_days'] +
                         $report_results['year_extra_days_off'];
        } else {
            $days_left = 0;
        }

        $old_model = clone $model;
        $assoc_vars[$this->settings['days_left']]['value'] = $days_left;
        $model->set('vars', array_values($assoc_vars), true);

        // Allow save all variables
        $this->registry->set('edit_all', true, true);
        $this->registry['db']->StartTrans();
        if ($model->save()) {
            // write history for the changes
            $filters = array(
                'where'                  => array('d.id = ' . $model->get('id')),
                'model_lang'             => $this->registry['lang'],
                'skip_assignments'       => true,
                'skip_permissions_check' => true
            );
            $new_model = Documents::searchOne($this->registry, $filters);
            $new_model->getVars();

            Documents_History::saveData($this->registry, array(
                'model'       => $model,
                'action_type' => 'edit',
                'new_model'   => $new_model,
                'old_model'   => $old_model
            ));
        } else {
            // ERROR
            $messages->setWarning($this->i18n('warning_automation_bgservice_complete_free_days_left_edit_failed'));
            $messages->insertInSession($this->registry);
            $this->registry['db']->FailTrans();
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }
}

?>
