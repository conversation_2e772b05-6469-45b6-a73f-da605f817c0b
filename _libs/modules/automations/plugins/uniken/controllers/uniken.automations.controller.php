<?php
include_once __DIR__ . '/syncData.trait.php';

class Uniken_Automations_Controller extends Automations_Controller {
    use syncData;

    /**
     * Create invoices templates for contract of type "service"
     *
     * @param array $params
     * @return bool - result of operation
     */
    public function createInvoicesTemplatesService($params) {

        $db = &$this->registry['db'];
        $db->StartTrans();

        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';

        $model = $params['model'];
        $query = 'SELECT `fiscal` FROM ' . DB_TABLE_CONTRACTS_TYPES . ' WHERE id = ' . $model->get('type');
        $fiscal_type = $db->GetOne($query);
        if (!$fiscal_type || $model->get('subtype') == 'annex' && $model->get('subtype_status') == 'failed') {
            $db->CompleteTrans();
            return true;
        }
        if ($this->registry['request']->get('templates_generated') == 'sure') {
            //templates have been generated already in the action ajax_check_agreements_differences
            //or the action is annulment
            //so we will not regenerate them again
            $db->CompleteTrans();
            return true;
        }
        if ($this->action != 'automatic_invoices_templates_generation' && $this->action != 'ajax_check_agreements_differences') {
            if (($this->action != 'setstatus' ||
                $this->registry['request']->get('status') != 'closed' ||
                $model->get('status') == 'closed')) {

                if ($this->registry['request']->get('substatus') == 'closed_2') {
                    $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET deleted = NOW(), deleted_by= ' . PH_AUTOMATION_USER .
                             ' WHERE contract_id = ' . $model->get('id');
                    $db->Execute($query);
                }
                //this action has nothing to do with the automation
                $db->CompleteTrans();
                return true;
            }
        }

        $this->registry->set('get_old_vars', true, true);
        $vars = $model->getAssocVars();

        //CONTRACT PARTY DETAILS SETUP
        $changed = false;
        $contract = clone $model;
        if ($contract->get('subtype') != 'contract') {
            $filters = array('where' => array('co.id = ' . $contract->get('parent_record')));
            $contract = Contracts::searchOne($this->registry, $filters);
            $old_contract = clone $contract;
        } else {
            // get the old model from the DB (for audit purposes)
            $filters = array('where' => array('co.id = ' . $contract->get('id')));
            $old_contract = Contracts::searchOne($this->registry, $filters);
        }
        if (!$contract->get('self_financial')) {
            //sef fiscal contact if it has not been filled out
            $this->registry['request']->set('self_financial', '[default_financial_contact_person]', 'all', true);
            $changed = true;
        } else {
            $this->registry['request']->set('self_financial', $contract->get('self_financial'), 'all', true);
        }

        //first check if the principal financial contact should be set
        //set financial person only if both electronic_invoice/email_invoice are set
        //and there is no customer financial contact
        if (!$contract->get('cstm_fin_email') && !empty($vars['electronic_invoice']['value']) && $vars['electronic_invoice']['value'] == 1
        && !empty($vars['email_invoice']['value'])) {
            $changed = true;
            //now check if there is a contact person with the email set in email_invoice
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters = array('sanitize' => true,
                             'model_lang' => $contract->get('lang'),
                             'where' => array('c.id = ' . $contract->get('customer')));
            $customer = Customers::searchOne($this->registry, $filters);

            if ($customer->get('is_company')) {
                //the customer is a company, check if contact person with the email exists
                //!!! FOR THE CUSTOMER !!!
                $query = 'SELECT c1.id FROM ' . DB_TABLE_CUSTOMERS . ' c1' . "\n" .
                         'JOIN ' . DB_TABLE_CUSTOMERS . ' c2' ."\n" .
                         '  ON c1.parent_customer = c2.id AND c2.parent_customer = ' . $customer->get('id') . "\n" .
                         'WHERE c1.email LIKE "%' . $vars['email_invoice']['value'] . '%"';
                $contact_person = $this->registry['db']->GetOne($query);
                if ($contact_person) {
                    //set the id of the contact person
                    $this->registry['request']->set('cstm_financial', $contact_person, 'all', true);
                } else {
                    //now set the contact person details as they are written in combo
                    $this->registry['request']->set('cstm_financial', $this->i18n('label_system_contact'), 'all', true);
                    $this->registry['request']->set('cstm_financial_isCustom', 1, 'all', true);
                    $this->registry['request']->set('cstm_fin_email_isCustom', 1, 'all', true);
                }
            } else {
                //the customer is person, check if such email has been added to person
                $this->registry['request']->set('cstm_financial', $customer->get('id'), 'all', true);
                //check if the person has this email, add it if needed
                $emails = $customer->get('email');
                if (empty($emails)) {
                    $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . ' SET email="' . General::slashesEscape($vars['email_invoice']['value']) . '" WHERE id=' . $contract->get('customer');
                    $db->Execute($query);
                    //ToDo: write history and audit
                } elseif (!in_array($vars['email_invoice']['value'], $emails)) {
                    //add the email to the customer
                    $email_data = array();
                    $email_notes = $customer->get('email_note');
                    foreach($emails as $idx => $email) {
                        $email_data[] = sprintf("%s%s", $email, (!empty($email_notes[$idx])) ? '|' . $email_notes[$idx] : '');
                    }
                    $email_data[] = $vars['email_invoice']['value'];
                    $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . ' SET email="' . General::slashesEscape(implode("\n", $email_data)) . '" WHERE id=' . $contract->get('customer');
                    $db->Execute($query);
                    //ToDo: write history and audit
                }
            }
            $this->registry['request']->set('cstm_fin_email', $vars['email_invoice']['value'], 'all', true);
        } else {
            $this->registry['request']->set('cstm_fin_email', $contract->get('cstm_fin_email'), 'all', true);
            $this->registry['request']->set('cstm_financial', $contract->get('cstm_financial'), 'all', true);
        }

        //we will not care about cc contacts
        $contract->set('skip_cc', true, true);
        //set other contacts from the model into the request
        $this->registry['request']->set('cstm_administrative', $contract->get('cstm_administrative'), 'all', true);
        $this->registry['request']->set('cstm_adm_email', $contract->get('cstm_adm_email'), 'all', true);
        $this->registry['request']->set('self_administrative', $contract->get('self_administrative'), 'all', true);
        $this->registry['request']->set('self_adm_email', $contract->get('self_adm_email'), 'all', true);

        //save contract parties
        if ($changed && $contract->savePartiesData()) {
            $filters = array('where' => array('co.id = ' . $contract->get('id')));
            $new_contract = Contracts::searchOne($this->registry, $filters);
            //write some history
            //save history
            require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
            $audit_parent = Contracts_History::saveData(
                    $this->registry,
                    array(
                        'model' => $contract,
                        'action_type' => 'parties',
                        'new_model' => $new_contract,
                        'old_model' => $old_contract
                    )
            );
        }
        unset($contract);
        unset($old_contract);
        unset($new_contract);

        //END OF CONTRACT PARTY DETAILS SETUP

        //delete all GT2 rows for templates for this model and relations with the contract

        //get templates to be deleted
        $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                 'WHERE contract_id = ' . $model->get('id') . "\n" .
                 '  AND (added_by = ' . PH_AUTOMATION_USER . ' OR type = ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL . ')';
        $t_all = $db->GetCol($query);

        //get templates with invoices already issued
        $query = 'SELECT DISTINCT(parent_id)' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                 'WHERE contract_id = ' . $model->get('id') . "\n" .
                 '  AND invoice_id > 0' . "\n";
        $t_inv = $db->GetCol($query);

        if (!empty($t_inv)) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET status = "finished"' . "\n" .
                     'WHERE id IN (' . implode(', ', $t_inv) . ')';
            $db->Execute($query);

            // exclude templates with invoices
            $t_all = array_diff($t_all, $t_inv);
        }

        if (!empty($t_all)) {
            //delete relational records
            $query = 'DELETE frr.*, gt2i18n.*, gt2.*' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n,' . "\n" .
                      DB_TABLE_GT2_DETAILS . ' AS gt2,' . "\n" .
                      DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                     'WHERE gt2i18n.parent_id = gt2.id AND gt2.model = "Finance_Invoices_Template"' . "\n" .
                     '  AND gt2.model_id IN (' . implode(', ', $t_all) . ')' . "\n" .
                     '  AND frr.link_to_model_name = "Contract" AND frr.link_to = ' . $model->get('id') . "\n" .
                     '  AND frr.parent_model_name = "Finance_Invoices_Template" AND frr.parent_id = gt2.model_id';
            $db->Execute($query);

            $query = 'DELETE FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . "\n" .
                     'WHERE invoice_id = 0 AND parent_id IN (' . implode(', ', $t_all) . ')';
            $db->Execute($query);

            //delete all templates for this model
            $query = 'DELETE fiti18n.*, fit.*' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N . ' AS fiti18n,' . "\n" .
                      DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                     'WHERE fiti18n.parent_id = fit.id' . "\n" .
                     '  AND fit.id IN (' . implode(', ', $t_all) . ')';
            $db->Execute($query);

            // delete data for template relations to models in their additional vars
            $query = 'DELETE FROM ' . DB_TABLE_CSTM_RELATIVES . "\n" .
                     'WHERE model = "Finance_Invoices_Template" AND model_id IN (' . implode(', ', $t_all) . ')';
            $db->Execute($query);

        }

        //get settings for the contract from variables
        $settings = $model->getVariablesSettings();
        if (!empty($settings['magic_articles']['value'])) {
            $settings['magic_articles']['value'] = preg_split('#\s*,\s*#', $settings['magic_articles']['value']);
        }
        //set invoice period start and end
        if ($model->get('subtype') == 'contract' || $this->action == 'automatic_invoices_templates_generation' && $model->get('subtype') == 'original') {
            //get the issue start date
            if ($vars['first_invoice_date']['value']) {
                $fid = $vars['first_invoice_date']['value'];
            } else {
                $fid = $model->get('date_start');
            }
            $lid = $model->get('date_validity');
        } elseif ($model->get('subtype') == 'annex') {
            //start date is the start of the annex
            $fid = $model->get('date_start_subtype');
            if ($fid < $vars['first_invoice_date']['value']) {
                $fid = $vars['first_invoice_date']['value'];
            }
            if ($model->get('date_end_subtype') && $model->get('date_end_subtype') != '0000-00-00') {
                $lid = $model->get('date_end_subtype');
            } else {
                $lid = $model->get('date_validity');
            }
        } else {
            //we don't have to come here
            $db->FailTrans();
            $db->CompleteTrans();
            $this->registry['messages']->setError($this->i18n('warning_templates_not_created'));
            $this->registry['messages']->setError($this->i18n('warning_wrong_contract_subtype'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        if (!$fid || $fid == '0000-00-00') {
            $db->FailTrans();
            $this->registry['messages']->setError($this->i18n('warning_empty_first_date'));
        }

        if (!$lid || $lid == '0000-00-00') {
            $db->FailTrans();
            $this->registry['messages']->setError($this->i18n('warning_empty_end_date'));
        }

        if ($model->get('date_end') && $model->get('date_end') > '0000-00-00') {
            //we try to finish a contract
            if ($fid > $model->get('date_end')) {
                $db->CompleteTrans();
                return true;
            }
            if ($lid > $model->get('date_end')) {
                $lid = $model->get('date_end');
            }
        }

        $model->getGT2Vars();
        $gt2 = $model->get('grouping_table_2');

        //save date (free_field1) for the services that should be invoiced once
        //IMPORTANT: do it only for the rows with:
        //empty free_text3 (period)
        //current is 0
        //empty free_field1 (date)
        $rows_to_update = array();
        foreach($gt2['values'] as $key => $values) {
            if ($values['current'] == 0 && $values['free_text3'] == '' && $values['free_field1'] == '') {
                $gt2['values'][$key]['free_field1'] = General::strftime('%Y-%m-01', $fid);
                $rows_to_update[] = $key;
            }
        }
        if (!empty($rows_to_update)) {
            $query = 'UPDATE ' . DB_TABLE_GT2_DETAILS . "\n" .
                     'SET free_field1="' . General::strftime('%Y-%m-01', $fid) . '"' . "\n" .
                     'WHERE id IN (' . implode(', ', $rows_to_update) . ')';
            $db->Execute($query);
        }

        $final_single = $final_current = $current = array();
        $magic = false;
        $gt2['values'] = array_values($gt2['values']);
        foreach($gt2['values'] as $key => $values) {
            if (empty($values['free_text3'])) {
                $final_single[] = $values;
            } else {
                $current[] = $values['id'];

                if (empty($final_current[$values['free_text3']])) {
                    $final_current[$values['free_text3']] = array();
                }

                if (!empty($settings['magic_articles']['value']) && in_array($values['article_id'], $settings['magic_articles']['value'])) {
                    if ($magic) {
                        if ($magic != $values['free_text3']) {
                            $this->registry['messages']->setError($this->i18n('plugin_invalid_magic_article_period', array($values['article_name'], ($key + 1))));
                        }
                    } else {
                        $magic = $values['free_text3'];
                    }
                }

                $final_current[$values['free_text3']][] = $values;
            }
        }
        $errors = $this->registry['messages']->getErrors();
        if (!empty($errors)) {
            $this->registry['messages']->insertInSession($this->registry);
            $db->FailTrans();
            $db->CompleteTrans();
            return false;
        }
        $query = 'SELECT employee FROM ' . DB_TABLE_USERS . "\n" .
                 'WHERE id="' . $model->get('added_by') . '" AND active=1 AND deleted_by=0';
        $employee = $db->GetOne($query);

        // set invoice auto issue in function of a contract flag
        $ai = 0;
        if (!empty($vars['autoadd_invoice']['value']) && $vars['autoadd_invoice']['value'] == 1) {
            $ai = 1;
        }

        //prepare templates properties
        $template_single = array(
            'contract_id' => $model->get('id'),
            'link_to_model_name' => 'Contract',
            'link_to' => $model->get('id'),
            'type' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_NORMAL,
            'company_data' => sprintf('%d_%d_bank_%d', $model->get('company'), $model->get('office'), $settings['invoices_bank']['value']),
            'customer' => $model->get('customer'),
            'project' => $model->get('project'),
            'currency' => $gt2['plain_values']['currency'],
            'issue_currency' => $gt2['plain_values']['currency'],
            'employee1' => $employee,
            'auto_issue' => $ai,
            'pattern' => $settings['invoice_generate_pattern']['value'],
            'observer' => $settings['invoices_observer']['value'],
            'issue_date' => $fid,
            'recurrent' => 0,
        );

        if ($vars['electronic_invoice']['value'] == 1) {
            $template_single['auto_send'] = 1;
            $template_single['email_template'] = $settings['invoice_email_template']['value'];
        }
        if ($vars['type_issue_doc']['value'] == 1) {
            $template_single['proforma'] = 1;
            $template_single['pattern'] = $settings['proforma_generate_pattern']['value'];
            if ($vars['electronic_invoice']['value'] == 1) {
                $template_single['email_template'] = $settings['proforma_email_template']['value'];
            }
        }

        foreach($settings['invoices_payment_date']['value'] as $key => $value) {
            $template_single['date_of_payment_' . $key] = $value;
        }
        foreach($settings['invoices_fiscal_event_date']['value'] as $key => $value) {
            $template_single['fiscal_event_date_' . $key] = $value;
        }

        $template_current = $template_single;
        unset($template_current['issue_date']);
        $template_current['single_period_rows'] = $settings['invoices_period_rows']['value'];
        $template_current['periods_start'] = $fid;
        $template_current['periods_end'] = $lid;
        $template_current['change_formula_periods_start'] = 'periods_start';
        $template_current['change_formula_periods_end'] = 'periods_end';
        $template_current['recurrent'] = 1;
        $template_current['first_period_invoice'] = $settings['invoice_first_period']['value'];

        if ($vars['period_invoice']['value'] == 1) {
            $direction = 'after';
            $point = 'end';
            $suffix = '_past';
        } else {
            $direction = 'after';
            $point = 'start';
            $suffix = '_current';
        }
        foreach($settings['invoices_first_date' . $suffix]['value'] as $key => $value) {
            $template_current['issue_start_' . $key] = $value;
            $template_current['issue_start_direction'] = $direction;
            $template_current['issue_start_point'] = $point;
        }
        foreach($settings['invoices_issue_date' . $suffix]['value'] as $key => $value) {
            $template_current['issue_date_' . $key] = $value;
            $template_current['issue_date_direction'] = $direction;
            $template_current['issue_date_point'] = $point;
        }

        $template_single = new Finance_Invoices_Template($this->registry, $template_single);
        $template_single->getGT2Vars();
        $template_current = new Finance_Invoices_Template($this->registry, $template_current);
        $template_current->getGT2Vars();

        $name_counter = 1;
        if (!empty($final_single)) {
            $template_single->set('name', General::slashesStrip($model->get('customer_name')) . '_' . $name_counter, true);
            $template_single->unsetProperty('id', true);
            $tgt2 = $template_single->get('grouping_table_2');
            $tgt2['values'] = $final_single;
            $tgt2['rows'] = array();
            $tgt2['plain_values']['currency'] = $template_single->get('currency');
            $tgt2['plain_values']['total_vat_rate'] = $gt2['plain_values']['total_vat_rate'];
            $template_single->set('grouping_table_2', $tgt2, true);
            $template_single->calculateGT2();
            $tgt2 = $template_single->get('grouping_table_2');
            foreach($tgt2['plain_values'] as $key => $value) {
                $template_single->set($key, $value, true);
            }
            $template_single->set('table_values_are_set', true, true);
            if (!$template_single->save()) {
                $db->FailTrans();
            }
            $name_counter++;
        }

        foreach ($final_current as $key => $values) {
            $template_current->set('single_period_count', $key, true);
            $template_current->set('single_period_period', 'month', true);
            $template_current->set('single_period_length', 0, true);
            $template_current->set('recurrence_count', $key, true);
            $template_current->set('recurrence_period', 'month', true);
            $template_current->unsetProperty('id', true);
            $template_current->set('name', General::slashesStrip($model->get('customer_name')) . '_' . $name_counter, true);
            $tgt2 = $template_current->get('grouping_table_2');
            $tgt2['values'] = $values;
            $tgt2['rows'] = array();
            $tgt2['plain_values']['currency'] = $template_current->get('currency');
            $tgt2['plain_values']['total_vat_rate'] = $gt2['plain_values']['total_vat_rate'];
            $template_current->set('grouping_table_2', $tgt2, true);
            $template_current->calculateGT2();
            $tgt2 = $template_current->get('grouping_table_2');
            foreach($tgt2['plain_values'] as $key => $value) {
                $template_current->set($key, $value, true);
            }
            $template_current->set('table_values_are_set', true, true);
            if (!$template_current->save()) {
                $db->FailTrans();
            }
            $name_counter++;
        }

        if (!empty($current)) {
            $query = 'UPDATE ' . DB_TABLE_GT2_DETAILS . ' SET current = 1 WHERE ' .
                     'model = "Contract" AND model_id = ' . $model->get('id') .
                     ' AND id IN (' . implode(', ', $current) . ')';
            $db->Execute($query);
        }

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        if (!$result) {
            $this->registry['messages']->setError($this->i18n('warning_templates_not_created'));
        }

        return $result;
    }

    /**
     * Gets paid by field for a payment and fills customer MOL
     */
    public function validatePaymentMOL($params) {
        $model = $params['model'];
        if (!in_array($model->get('type'), array('PKO', 'BP')) ||
            !in_array($this->registry['action'], array('add', 'edit')) ||
            !$this->registry['request']->isPost() ||
            !$this->registry['request']->isRequested('note') ||
            $this->registry['request']->get('status') != 'finished') {
            return true;
        }
        $person = trim($this->registry['request']->get('note'));

        $filters = array('where' => array('c.id = ' . $model->get('customer')));
        $old = Customers::searchOne($this->registry, $filters);
        $old->getTags();

        if (empty($person) && !in_array($this->settings['tag_PM_id'], $old->get('tags'))) {
            $this->registry['messages']->setError($this->i18n('plugin_empty_person'));
            return false;
        }

        return true;
    }

    /**
     * Gets paid by field for a payment (note) and fills customer MOL
     */
    public function setCustomerMolFromPayment($params) {
        $db = &$this->registry['db'];

        $model = $params['model'];
        if (!in_array($model->get('type'), array('PKO', 'BP')) ||
            !in_array($this->registry['action'], array('add', 'edit')) ||
            !$this->registry['request']->isPost() ||
            !$this->registry['request']->isRequested('note') ||
            $this->registry['request']->get('status') != 'finished') {
            return false;
        }

        $person = trim($this->registry['request']->get('note'));

        $db->StartTrans();

        $filters = array('where' => array('c.id = ' . $model->get('customer')));
        $old = Customers::searchOne($this->registry, $filters);

        $old->getTags();
        if ($old->get('mol') != $person && !in_array($this->settings['tag_PM_id'], $old->get('tags'))) {
            //in add action the model_lang is not set in the model
            $model_lang = $model->get('lang') ? $model->get('lang') : $this->registry['lang'];
            $query = 'UPDATE ' . DB_TABLE_CUSTOMERS_I18N . ' SET mol = "' . $person . '"' . "\n" .
                'WHERE parent_id = ' . $model->get('customer') . ' AND lang = "' . $model_lang . '"';
            $db->Execute($query);
            $new = clone $old;
            $new->set('mol', $person, true);

            //write history about the change
            require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
            require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
            Customers_History::saveData(
                $this->registry,
                array(
                    'model' => $new,
                    'action_type' => 'edit',
                    'new_model' => $new,
                    'old_model' => $old
                )
            );
            $result = !$db->HasFailedTrans();
        } else {
            $result = false;
        }
        $db->CompleteTrans();

        return $result;
    }

    /**
     * Do not allow more than one active contract
     * for one and the same service-customer pair
     */
    public function validatecontractType1($params) {
        if (!$this->registry['request']->isPost() || !in_array($this->registry['action'], array('add', 'edittopic', 'addannex'))) {
            return true;
        }

        //get devices
        $devices = $this->registry['request']->get('article_alternative_deliverer') ?: array();
        $devices = array_filter($devices);
        //get services
        $services = $this->registry['request']->get('article_id') ?: array();
        $services = array_filter($services);

        //get non-empty intersection of the both arrays
        $keys = array_intersect(array_keys($devices), array_keys($services));
        $cond = array();
        foreach ($keys as $k) {
            $cond[] = 'gt2.article_alternative_deliverer = ' . $devices[$k] . ' AND article_id = ' . $services[$k];
        }

        $query = 'SELECT CONCAT(gt2i18n.article_name, " - ", gt2i18n.article_alternative_deliverer_name)' . "\n" .
                'FROM ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                'JOIN ' . DB_TABLE_GT2_DETAILS . ' gt2' . "\n" .
                '  ON gt2.model = "Contract" AND gt2.model_id = c.id AND (' . implode(' OR ', $cond ?: array(0)) . ')' . "\n" .
                'JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' gt2i18n' . "\n" .
                '  ON gt2.id = gt2i18n.parent_id AND gt2i18n.lang = "' . $params['model']->get('model_lang') . '"' . "\n" .
                'JOIN ' . DB_TABLE_FIELDS_META . ' fm' . "\n" .
                '  ON fm.model = "Contract" AND fm.model_type = c.type AND fm.name = "tripartite_contract"' . "\n" .
                'LEFT JOIN ' . DB_TABLE_CONTRACTS_CSTM . ' cc' . "\n" .
                '  ON cc.model_id = c.id AND cc.var_id = fm.id AND cc.value = 1' . "\n" .
                'WHERE cc.value IS NULL AND c.deleted_by = 0' . "\n" .
                '  AND c.annulled_by = 0' . "\n" .
                '  AND c.status = "closed"' . "\n" .
                '  AND c.substatus != 2' . "\n" .
                '  AND c.subtype = "contract"' . "\n" .
                '  AND c.type = ' . $params['model']->get('type') . "\n" .
                '  AND c.customer = "' . $params['model']->get('customer') . '"';
        if ($this->registry['action'] == 'edittopic') {
            if ($params['model']->get('subtype') == 'annex') {
                $q1 = 'SELECT date_start FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $params['model']->get('parent_record');
                $start = $this->registry['db']->GetOne($q1);
                $query .= ' AND c.id != ' . $params['model']->get('parent_record') . ' AND c.date_validity > "' . $start . '"';
            } else {
                $q1 = 'SELECT date_start FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $params['model']->get('id');
                $start = $this->registry['db']->GetOne($q1);
                $query .= ' AND c.id != ' . $params['model']->get('id') . ' AND c.date_validity > "' . $start . '"';
            }
        } elseif ($this->registry['action'] == 'addannex') {
            $q1 = 'SELECT date_start FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $params['model']->get('id');
            $start = $this->registry['db']->GetOne($q1);
            $query .= ' AND c.id != ' . $params['model']->get('id') . ' AND c.date_validity > "' . $start . '"';
        }
        $found = $this->registry['db']->GetCol($query);

        if (empty($found)) {
            return true;
        } else {
            // we will not allow more than one contract for one and the same service-customer pair
            $this->registry['messages']->setError($this->i18n('plugin_duplicate_contract_1', array(implode(', ', $found))));
            return false;
        }
    }

    /**
     * Add invoice for proforma if its payment status is paid
     * @param array $params
     */
    public function proformaToInvoice($params) {

        if ($this->registry['currentUser']->get('id') == PH_AUTOMATION_USER) {
            $this->setOriginalUserAsCurrent();
        }
        if (!empty($this->before_action)) {
            // VALIDATION
            // do not allow invoice issue if the proforma is not paid and is for a contract
            $ids = $this->registry['request']->get($this->registry['action']);
            $query = 'SELECT fir.payment_status' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' rr' . "\n" .
                '  ON rr.link_to_model_name = "Contract" AND rr.parent_model_name = "Finance_Incomes_Reason" AND rr.parent_id = fir.id' . "\n" .
                'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                '  ON c.id = rr.link_to AND c.type = 1' . "\n" .
                'WHERE fir.type = 2 AND fir.id = ' . $ids;
            $ids = $this->registry['db']->GetOne($query);
            if ($ids && $ids != 'paid' && $ids != 'invoiced') {
                $rights = $this->registry['currentUser']->get('rights');
                $rights['finance_incomes_reasons1']['add'] = 'none';
                $this->registry['currentUser']->set('rights', $rights, true);
                $this->registry['messages']->setError($this->i18n('plugin_error_proforma_not_paid'));
                return false;
            }
            return true;
        } elseif ($this->registry['controller'] == 'incomes_reasons') {
            $all_ids = array($params['model']->get('id'));
        } elseif ($this->registry['controller'] == 'payments') {
            $all_ids = $this->registry['request']->get('relatives_payments');
            if (empty($all_ids)) {
                return true;
            }
            $all_ids = array_keys($all_ids);
        } else {
            return true;
        }
        // get proforma invoices FROM CONTRACT that are paid
        // and not have an invoice already issued
        $query = 'SELECT DISTINCT(fir.id)' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' rr' . "\n" .
                 '  ON rr.link_to_model_name = "Contract" AND rr.parent_model_name = "Finance_Incomes_Reason" AND rr.parent_id = fir.id' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                 '  ON c.id = rr.link_to AND c.type = 1' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                 '  ON frr.link_to_model_name = "Finance_Incomes_Reason" AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                 '   AND frr.link_to = fir.id' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
                 '  ON f.id = frr.parent_id AND f.type = 1 AND f.annulled_by = 0' . "\n" .
                 'WHERE fir.type = 2 AND fir.payment_status IN ("paid", "invoiced") AND fir.id IN (' . implode(', ', $all_ids) . ') AND f.id IS NULL';
        $inv_ids = $this->registry['db']->GetCol($query);

        $result = true;

        //IMPORTANT: be sure to get all the data from the database, not POST
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        if (!empty($inv_ids)) {
            // issue invoice for proforma invoices
            $date_of_payment = $this->registry['request']->get('date_of_payment');
            //fully paid proformas should be paid in 10 working days
            $this->registry['request']->set('date_of_payment',
                Calendars_Calendar::calcDateOnWorkingDays($this->registry, General::strftime('%Y-%m-%d'), 10), 'all', true);
            $inv_ids = Finance_Incomes_Reasons::invoiceFromProforma($this->registry, $inv_ids);
            $this->registry['request']->set('date_of_payment', $date_of_payment, 'all', true);
            if (empty($inv_ids)) {
                $result = false;
            }
        }
        $created = array_fill_keys($inv_ids, 'invoice');
        //get magic articles
        $query = 'SELECT vc.value' . "\n" .
                 'FROM ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
                 'JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vc' . "\n" .
                 '  ON vc.var_id = vm.id AND vc.model_id = 0' . "\n" .
                 'WHERE vm.model = "Contract" AND vm.model_type = 1 AND vm.name = "magic_articles"';
        $magic = $this->registry['db']->GetOne($query);

        if ($magic) {
            // get proforma invoices FROM CONTRACT that are partially paid
            $query = 'SELECT DISTINCT(fir.id), fiti.parent_id as template_id, fiti.`from`, fiti.`to`, c.num as contract_num, c.date_sign as contract_date, c.id as contract_id, fit.recurrence, COUNT(g.id) as `rows`' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fiti' . "\n" .
                '  ON fir.id = fiti.invoice_id' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' fit' . "\n" .
                '  ON fit.id = fiti.parent_id' . "\n" .
                'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                '   on c.id = fiti.contract_id AND c.type = 1' . "\n" .
                'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                '   ON g.model = "Finance_Invoices_Template" AND g.model_id = fit.id AND g.article_id IN (' . $magic . ')' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                '  ON frr.link_to_model_name = "Finance_Incomes_Reason" AND frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                '   AND frr.link_to = fir.id' . "\n" .
                'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
                '  ON f.id = frr.parent_id AND f.type = 1 AND f.annulled_by = 0' . "\n" .
                'WHERE fir.type = 2 AND fir.payment_status = "partial" AND fir.id IN (' . implode(', ', $all_ids) . ') AND f.id IS NULL' . "\n" .
                'GROUP BY g.model_id';
            $adv_ids = $this->registry['db']->GetAssoc($query);
        }

        if ($result && !empty($adv_ids)) {
            // we need to issue advanced invoice and new proforma for each partially paid invoice
            $models = Finance_Incomes_Reasons::search($this->registry, array('where' => array('fir.id IN (' . implode(', ', array_keys($adv_ids)) . ')')));
            //get advanced article
            $query = 'SELECT n.id as article_id, ni.name as article_name, 1 as current, 1 as quantity, n.code as article_alternative_deliverer_name' . "\n" .
                    'FROM ' . DB_TABLE_NOMENCLATURES . ' n' . "\n" .
                    'JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' ni' .
                    '  ON n.id = ni.parent_id AND ni.lang = "' . $this->registry['lang'] . '"' . "\n" .
                    'WHERE n.id = 2736';
            $aarticle = $this->registry['db']->GetRow($query);
            $this->registry['db']->StartTrans();
            $tz = new DateTimeZone('UTC');

            foreach ($models as $m) {

                // get how many months we have issued in the previous invoice
                $m->unsanitize();
                $diff = date_diff(date_create($adv_ids[$m->get('id')]['from'], $tz), date_add(date_create($adv_ids[$m->get('id')]['to'], $tz), new DateInterval('P1D')));
                if ($diff->d > 0) {
                    $diff->m ++;
                }
                $diff = $diff->m;
                $new_rec = preg_split('#\r\n|\n|\r#', $adv_ids[$m->get('id')]['recurrence']);
                foreach ($new_rec as $rec) {
                    $rec = preg_split('#\s*\:\=\s*#', $rec);
                    $new_rec[$rec[0]] = $rec[1];
                }

                if ($diff > $new_rec['count']) {
                    // more periods than template's recurrence
                    $description = $this->i18n('plugin_advance_article_description1', array($adv_ids[$m->get('id')]['contract_num'], date_create($adv_ids[$m->get('id')]['contract_date'])->format('d.m.Y'), date_create($adv_ids[$m->get('id')]['from'])->format('d.m.Y'), date_create($adv_ids[$m->get('id')]['to'])->format('d.m.Y')));
                } else {
                    $description = $this->i18n('plugin_advance_article_description2', array($adv_ids[$m->get('id')]['rows'], $adv_ids[$m->get('id')]['contract_num'], date_create($adv_ids[$m->get('id')]['contract_date'])->format('d.m.Y'), date_create($adv_ids[$m->get('id')]['from'])->format('d.m.Y'), date_create($adv_ids[$m->get('id')]['to'])->format('d.m.Y')));
                }

                $paid = $m->getFullPaidAmount();

                $adv = clone $m;
                $adv->unsetProperty('id', true);
                $adv->unsetProperty('num', true);
                $adv->unsetProperty('issue_by', true);
                $adv->unsetProperty('invoice_code', true);
                $adv->set('type', PH_FINANCE_TYPE_INVOICE, true);
                $adv->set('advance', 1, true);
                $adv->set('link_to', $adv_ids[$m->get('id')]['contract_id'], true);
                $adv->set('link_to_model_name', 'Contract', true);
                $adv->set('issue_date', date('Y-m-d'), true);
                $adv->set('fiscal_event_date', date('Y-m-d'), true);
                $adv->set('date_of_payment', date('Y-m-d'), true);
                $adv->set('fin_field_2', $m->get('id'), true);
                $gt2 = $adv->getGT2Vars();
                $gt2['values'] = array($aarticle);
                $gt2['values'][0]['article_description'] = $description;
                $gt2['values'][0]['price'] = $paid / 1.2; // price before VAT
                $gt2['values'][0]['date_from'] = $adv_ids[$m->get('id')]['from'];
                $gt2['values'][0]['date_to'] = $adv_ids[$m->get('id')]['to'];
                Model_Factory::createGT2InfoData($this->registry, $gt2['values'], array('model_name' => 'Contract', 'model_type' => 1, 'contract_num' => $adv_ids[$m->get('id')]['contract_num']));
                $adv->set('grouping_table_2', $gt2, true);
                $adv->calculateGT2();
                $adv->set('table_values_are_set', true, true);

                if ($adv->save() && $adv->get('id')) {
                    // insert row in the info table for the advance
                    $query = 'INSERT INTO ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' SET' . "\n" .
                        '  parent_id = ' . $adv_ids[$m->get('id')]['template_id'] . ",\n" .
                        '  contract_id = ' . $adv_ids[$m->get('id')]['contract_id'] . ",\n" .
                        '  invoice_id = ' . $adv->get('id') . ",\n" .
                        '  advanced = "advance",' . "\n" .
                        '  `from` = "' . $adv_ids[$m->get('id')]['from'] . '",' . "\n" .
                        '  `to` = "' . $adv_ids[$m->get('id')]['to'] . '",' . "\n" .
                        '  observer_response = "issue_send",' . "\n" .
                        '  response_id = ' . PH_AUTOMATION_USER . ",\n" .
                        '  response_date = NOW()' . ",\n" .
                        '  invoice_date = NOW()' . ",\n" .
                        '  issue_date = NOW()';
                    $this->registry['db']->Execute($query);

                    //move all payments from proforma to advance
                    $query = 'UPDATE ' . DB_TABLE_FINANCE_BALANCE . ' SET' . "\n" .
                             'paid_to = ' . $adv->get('id') . "\n" .
                             'WHERE parent_model_name = "Finance_Payment" AND paid_to_model_name = "Finance_Incomes_Reason" AND paid_to = ' . $m->get('id');
                    $this->registry['db']->Execute($query);

                    $newM = clone $m;
                    $mgt2 = $newM->getGT2Vars();
                    //annul current proforma
                    $m->annul();

                    $mgt2['rows'] = array();
                    $mgt2['values'] = array_values($mgt2['values']);

                    foreach ($gt2['values'] as $v) {
                        $v['price'] *= -1;
                        $v['free_field5'] = $adv_ids[$m->get('id')]['template_id'];
                        $mgt2['values'][] = $v;
                    }

                    // prepare new proforma for saving
                    $newM->unsetProperty('id', true);
                    $newM->unsetProperty('num', true);
                    $newM->unsetProperty('date_of_payment', true);
                    $newM->unsetProperty('issue_by', true);
                    $newM->unsetProperty('invoice_code', true);
                    $newM->set('link_to_model_name', 'Contract', true);
                    $newM->set('link_to', $adv_ids[$m->get('id')]['contract_id'], true);
                    $newM->set('issue_date', date('Y-m-d'), true);
                    $newM->set('fiscal_event_date', date('Y-m-d'), true);
                    $newM->set('issue_by', $this->registry['currentUser']->get('id'), true);

                    //get date of payment settings for template
                    $query = 'SELECT date_of_payment FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' WHERE id = ' . $adv_ids[$m->get('id')]['template_id'];
                    $dof = $this->registry['db']->GetOne($query);
                    $dof = preg_split('#\r\n|\r|\n#', $dof);
                    foreach ($dof as $k => $v) {
                        $v = preg_split('#\s*:=\s*#', $v);
                        $newM->set('date_of_payment_' . $v[0], $v[1], true);
                    }
                    $newM->set('grouping_table_2', $mgt2, true);
                    $newM->calculateGT2();
                    $newM->set('table_values_are_set', true, true);
                    $newM->save();

                    if ($newM->get('id')) {
                        // insert row into info table (notify current period again) for the template
                        $query = 'INSERT INTO ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' SET' . "\n" .
                            '  parent_id = ' . $adv_ids[$m->get('id')]['template_id'] . ",\n" .
                            '  contract_id = ' . $adv_ids[$m->get('id')]['contract_id'] . ",\n" .
                            '  invoice_id = ' . $newM->get('id') . ",\n" .
                            '  `from` = "' . $adv_ids[$m->get('id')]['from'] . '",' . "\n" .
                            '  `to` = "' . $adv_ids[$m->get('id')]['to'] . '",' . "\n" .
                            '  observer_response = "issue_send",' . "\n" .
                            '  response_id = ' . PH_AUTOMATION_USER . ",\n" .
                            '  response_date = NOW()' . ",\n" .
                            '  invoice_date = NOW()' . ",\n" .
                            '  issue_date = NOW()';
                        $this->registry['db']->Execute($query);

                        $created[$adv->get('id')] = 'advance';
                        $created[$newM->get('id')] = 'proforma';
                    } else {
                        $this->registry['db']->FailTrans();
                        break;
                    }
                } else {
                    $this->registry['db']->FailTrans();
                    break;
                }
            }

            if ($this->registry['db']->HasFailedTrans()) {
                $result = false;
            }
            $this->registry['db']->CompleteTrans();
        }

        if (!$result) {
            if ($this->registry['ajax_result']) {
                $response = (array)json_decode($this->registry['ajax_result']);
                if (empty($response['errors'])) {
                    $response['errors'] = '';
                }
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['items'] = array($this->i18n('plugin_error_adding_invoice_from_proforma'));
                $v->data['display'] = 'error';
                $response['errors'] = $v->fetch() . $response['errors'];
                $this->registry->set('ajax_result', json_encode($response), true);
            } else {
                $this->registry['messages']->setError($this->i18n('plugin_error_adding_invoice_from_proforma'), '', -5);
                $this->registry['messages']->insertInSession($this->registry);
            }
            return false;
        } else {
            if ($this->registry['ajax_result']) {
                $response = (array)json_decode($this->registry['ajax_result']);
                if (empty($response['errors'])) {
                    $response['errors'] = '';
                }
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['items'] = array();
                foreach($created as $id => $type) {
                    // advances
                    $link = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=view&amp;view=%d',
                        $_SERVER['PHP_SELF'], $this->registry['module_param'], 'finance',
                        $this->registry['controller_param'], 'incomes_reasons', 'incomes_reasons', $id
                    );
                    if ($type == 'advance') {
                        $v->data['items'][] = $this->i18n('plugin_message_adding_advanced_invoice_from_proforma', array($link));
                    } elseif ($type == 'proforma') {
                        $v->data['items'][] = $this->i18n('plugin_message_adding_new_proforma', array($link));
                    } else {
                        $v->data['items'][] = $this->i18n('plugin_message_adding_invoice_from_proforma', array($link));
                    }
                }
                $v->data['display'] = 'message';
                $response['errors'] = $v->fetch() . $response['errors'];
                $this->registry->set('ajax_result', json_encode($response), true);
            } else {
                foreach($created as $id => $type) {
                    $link = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=view&amp;view=%d',
                        $_SERVER['PHP_SELF'], $this->registry['module_param'], 'finance',
                        $this->registry['controller_param'], 'incomes_reasons', 'incomes_reasons', $id
                    );
                    if ($type == 'advance') {
                        $this->registry['messages']->setMessage($this->i18n('plugin_message_adding_advanced_invoice_from_proforma', array($link)));
                    } elseif ($type == 'proforma') {
                        $this->registry['messages']->setMessage($this->i18n('plugin_message_adding_new_proforma', array($link)));
                    } else {
                        $this->registry['messages']->setMessage($this->i18n('plugin_message_adding_invoice_from_proforma', array($link)));
                    }
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        $mails = array();
        if (!empty($created)) {
            if (isset($params['mail'])) {
                // we are here when dashlet for payments is used
                foreach ($created as $id => $type) {
                    $mails[$id] = $params['mail'];
                }
            } else {
                //get issued invoices and send them to the customers
                $query = 'SELECT rr1.parent_id, IF(co.cstm_fin_email = "", 0, 1) as mail' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' rr1' . "\n" .
                         'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' rr2' . "\n" .
                         '  ON rr2.parent_id = rr1.link_to AND rr2.link_to_model_name = "Contract" AND rr2.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                         'JOIN ' . DB_TABLE_CONTRACTS . ' co' . "\n" .
                         '  ON co.id = rr2.link_to' . "\n" .
                         'WHERE rr1.parent_model_name = "Finance_Incomes_Reason" AND rr1.link_to_model_name = "Finance_Incomes_Reason" AND rr1.parent_id IN (' . implode(', ', array_keys($created)) . ')' . "\n" .
                         'UNION' . "\n" .
                         'SELECT rr1.parent_id, IF(co.cstm_fin_email = "", 0, 1) as mail' . "\n" .
                         'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' rr1' . "\n" .
                         'JOIN ' . DB_TABLE_CONTRACTS . ' co' . "\n" .
                         '  ON co.id = rr1.link_to' . "\n" .
                         'WHERE rr1.parent_model_name = "Finance_Incomes_Reason" AND rr1.link_to_model_name = "Contract" AND rr1.parent_id IN (' . implode(', ', array_keys($created)) . ')';
                $mails = $this->registry['db']->GetAssoc($query);
            }

            // get all issued invoices as models
            $invoices = Finance_Incomes_Reasons::search($this->registry, array('where' => array('fir.id IN (' . implode(', ', array_keys($created)) . ')')));
            // get email template for the invoice
            foreach ($invoices as $i) {
                if (empty($mails[$i->get('id')])) {
                    continue;
                }
                $i->getRelatives(array('get_reason' => true));
                $c = $i->get('reason');
                $i->unsetProperty('reason', true);
                $i->set('contract', $c->get('id'), true);
                unset($c);
                if ($created[$i->get('id')] == 'advance') {
                    $mTpl = 25;
                } elseif ($created[$i->get('id')] == 'invoice') {
                    $mTpl = 24;
                } elseif ($created[$i->get('id')] == 'proforma') {
                    $mTpl = 23;
                } else {
                    $mTpl = 0;
                }
                if ($mails[$i->get('id')] != 1) {
                    $m = array('email' => $mails[$i->get('id')], 'name' => '');
                    $i->set('custom_email_address', $m, true);
                }
                // ignore some warnings from PDF generator
                $result = @$i->sendToCustomer(1001, $mTpl);
            }
        }

        $this->registry->set('get_old_vars', $gov, true);

        return true;
    }

    /**
     * Issue invoices for contracts.
     * Automation runs in before_action mode for the issue_invoices crontab
     * action and performs the issuing (creation) of proformas/invoices one by
     * one, applying additional processing of special proformas which cannot be
     * created that way by standard functionality.
     *
     * @param array $params
     */
    public function issueInvoices($params) {
        // do not let automation timeout
        set_time_limit(60*60);

        $db = &$this->registry['db'];
        $check_lock = date_sub(date_create(), new DateInterval('PT12H'))->format('Y-m-d H:i:s');

        // IMPORTANT !!!! Complete the transaction started in the router
        // to be able to manage transaction for each invoice
        $this->registry['db']->CompleteTrans();

        // issuing invoices by specified ids
        $ids = '';
        if ($this->registry['request']->get('item_ids')) {
            $ids = $this->registry['request']->get('item_ids');
            if (!is_array($ids)) {
                $ids = explode(',', $ids);
            }
            array_map('intval', $ids);
        }

        $query = 'SELECT fiti.id AS idx, fit.id, fit.recurrence, fit.contract_id, fiti.issue_date, fiti.observer_response, fit.proforma,' . "\n" .
            '       fiti.from, fiti.to, fiti.response_id, u.invoice_code, co.type as contract_type, co.num as contract_num, co.id as contract_id' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
            'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
            '  ON fit.status != "finished" AND fit.id = fiti.parent_id AND fit.issue_lock <= "' . $check_lock . '"' . (empty($params['force_deleted']) ? ' AND fit.deleted_by = 0' : '') . "\n" .
            'JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
            '  ON co.id = fit.contract_id AND co.subtype="contract" AND co.status="closed" AND co.active > 0 AND co.deleted_by = 0' . "\n" .
            'JOIN ' . DB_TABLE_CONTRACTS_TYPES . ' AS ct' . "\n" .
            '  ON ct.id = co.type AND ct.active=1 AND ct.deleted=0 AND ct.fiscal > 0' . "\n" .
            'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
            '  ON fiti.response_id=u.id' . "\n" .
            'WHERE (fiti.observer_response NOT IN("none", "cancel")' . "\n" .
            '   OR fiti.observer_response = "cancel" AND fiti.`from` = "0000-00-00")' . "\n" .
            '  AND fiti.invoice_id = 0' . "\n" .
            '  AND ' . (empty($ids) ? 'fiti.issue_date <= NOW()' : 'fiti.id IN (' . implode(', ', $ids) . ')') . "\n" .
            'ORDER BY fiti.issue_date ASC, fiti.parent_id';
        $records = $db->GetAssoc($query);

        //get magic articles
        $query = 'SELECT vc.value' . "\n" .
            'FROM ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
            'JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vc' . "\n" .
            '  ON vc.var_id = vm.id AND vc.model_id = 0' . "\n" .
            'WHERE vm.model = "Contract" AND vm.model_type = 1 AND vm.name = "magic_articles"';
        $magic = $this->registry['db']->GetOne($query);

        if (!empty($params['force_deleted'])) {
            $this->registry->set('force_deleted', true, '', true);
        }
        foreach ($records as $r => $record) {
            if ($record['contract_type'] != 1 || empty($magic)) {
                // this will be issued via the standard crontab
                $records[$r]['state'] = 'normal';
                continue;
            } elseif (!$record['proforma']) {
                // invoice without proforma
                $records[$r]['state'] = 'normal';
                continue;
            }
            // check for magic articles in the current template
            $query = 'SELECT COUNT(id) FROM ' . DB_TABLE_GT2_DETAILS .
                     ' where model = "Finance_Invoices_Template" AND model_id = ' . $record['id'] . ' AND article_id IN(' . $magic . ')';
            $is_magic = $db->GetOne($query);
            if (!$is_magic) {
                // template without magic articles
                $records[$r]['state'] = 'normal';
                continue;
            }
            //check if the last proforma issued from the contract has NOT been paid(has invoice added)
            // no matter if the invoice is annulled - also check for magic articles
            $query = 'SELECT f.id, fi.id as info_id, fi.parent_id as template, fi.`from`, fi.`to`' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fi' . "\n" .
                     'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                     '  ON c.id = fi.contract_id and c.type = 1' . "\n" .
                     'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                     '  ON g.model = "Finance_Incomes_Reason" AND g.model_id = fi.invoice_id AND g.article_id IN(' . $magic . ')' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
                     '  ON f.id = fi.invoice_id AND f.annulled_by = 0 AND f.type = 2' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' fr' . "\n" .
                     '  ON fr.parent_model_name = "Finance_Incomes_Reason" AND fr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND fr.link_to = fi.invoice_id' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f1' . "\n" .
                     '  ON f1.id = fr.parent_id AND f1.type = 1' . "\n" .
                     'WHERE fi.contract_id = ' . $record['contract_id'] . ' AND fi.invoice_id > 0' . "\n" .
                     '  AND f1.id IS NULL AND f.payment_status NOT IN ("paid", "invoiced")' . "\n" .
                     'GROUP BY g.model_id' . "\n" .
                     'ORDER BY invoice_id DESC';
            $records[$r]['prof'] = $db->GetRow($query);
            $records[$r]['state'] = 'special';
        }

        require_once PH_MODULES_DIR . 'crontab/models/invoices.crontabs.model.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        $worker = new Invoices_Crontab($this->registry, array('action' => 'issue_invoices'));
        // do not send notification for result to observer right after issuing
        // (because proformas will be issued one at a time)
        $worker->skipResults = true;
        // do not generate file and send email to customer right after issuing
        // (it will be done later by the standard execution of crontab action)
        $worker->skipSend = true;

        // we will issue all invoices one-by-one (using the issue_invoices functionality)
        // as we will need a transaction for each invoice
        // because lots of magic will follow for special ones
        $errors = false;
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        // collect ids of info records with successfully issued invoices
        $ids = array();

        foreach ($records as $r => $record) {
            $this->registry['db']->StartTrans();

            $this->registry['request']->set('item_ids', $r, '', true);
            $worker->issueInvoices();
            //get issued invoice
            $query = 'SELECT invoice_id, `from` FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' WHERE id = ' . $r;
            $inv = $db->GetRow($query);

            if (!$inv || !$inv['invoice_id']) {
                // invoice has not been issued
                $db->FailTrans();
                $db->CompleteTrans();
                $errors = true;
                continue;
            }

            if (!empty($record['prof']) && !empty($record['state']) && $record['state'] == 'special') {
                $df = $inv['from'];
                $inv = Finance_Incomes_Reasons::searchOne($this->registry, array('where' => array('fir.id = ' . $inv['invoice_id'])));
                $igt2 = $inv->getGT2Vars();

                //get proforma
                $prof = Finance_Incomes_Reasons::searchOne($this->registry, array('where' => array('fir.id = ' . $record['prof']['id'])));
                $pgt2 = $prof->getGT2Vars();
                $i = -1;
                foreach ($pgt2['values'] as $v) {
                    if ($v['date_from'] < $df) {
                        $df = $v['date_from'];
                    }
                    // here we will store where this row comes from
                    if (empty($v['free_field5'])) {
                        $v['free_field5'] = $record['prof']['template'];
                    }
                    $igt2['values'][$i] = $v;
                    $i--;
                }

                $inv->set('grouping_table_2', $igt2, true);
                $inv->calculateGT2();
                $inv->set('table_values_are_set', true, true);
                $inv->save();

                //manually create rows links for proforma - contract
                $saved_gt2 = $inv->get('grouping_table_2');
                $rows_links = array();
                $i = 0;
                foreach ($saved_gt2['values'] as $row_id => $not_important) {
                    $rows_links[] = $row_id . ' => ' . $i++;
                }
                $query = 'update ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' set `rows_links` = "' . implode("\n", $rows_links) . '" WHERE `parent_model_name` = "Finance_Incomes_Reason" AND parent_id=' . $inv->get('id') . ' AND link_to_model_name="Contract"';
                $this->registry['db']->Execute($query);

                $query = 'update ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' set `from` = "' . $df . '" WHERE invoice_id = ' . $inv->get('id');
                $this->registry['db']->Execute($query);
                $prof->annul();
                // delete info row as it will be marked as "cancelled" on annul action
                $query = 'DELETE FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' WHERE id = ' . $record['prof']['info_id'];
                $db->Execute($query);
            }

            if ($db->HasFailedTrans()) {
                $errors = true;
            } else {
                $ids[] = $r;
            }
            $db->CompleteTrans();
        }

        $this->registry->set('get_old_vars', $gov, true);
        $worker->skipResults = false;
        $worker->skipSend = false;
        if (!empty($ids)) {
            $this->registry['request']->set('item_ids', implode(',', $ids), '', true);
        } else {
            $this->registry['request']->set('item_ids', '0', '', true);
        }

        // continue with the real issue invoices crontab
        // after all invoices have been processed - HERE
        // it should do sentToCustomer and process the results - ONLY

        // IMPORTANT !!!! Start new transaction as we have closed the one
        // started in the router, to be able to manage transactions per invoice
        $this->registry['db']->StartTrans();
    }

    /**
     * Issuing of waiting proformas is not allowed when there are previous unpaid proformas from template
     * @param array $params
     * @return boolean
     */
    public function validateContractInvoiceIssue($params) {
        if ($params['model']->modelName == 'Finance_Invoices_Template' && $params['model']->get('proforma')) {
            // check if we have previous proforma not paid for the same template
            // also check the parent record of the template because when agreement enters into force
            // the proforma is issued from the template of the agreement, not the one of the contract (which does not exist yet)
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fi' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
                     '  ON fi.invoice_id = f.id AND f.payment_status NOT IN ("paid", "invoiced")' . "\n" .
                     'WHERE fi.parent_id IN (\'' . $params['model']->get('template_id') . '\', \'' . $params['model']->get('parent_record') . '\')';
            $prof = $this->registry['db']->GetOne($query);
            if ($prof) {
                $this->registry['messages']->setError($this->i18n('plugin_error_issue_not_allowed'));
                $this->registry['request']->remove($this->registry['action']);
                return false;
            }
        }
        return true;
    }

    /**
     * Modify data of financial documents that have to be issued when agreement
     * enters into force in order to change standard behaviour of system into
     * one that matches specific requirements of installation
     *
     * @param array $params
     * @return boolean
     */
    public function intoForceMagic($params) {
        if ($params['model']->get('subtype') != 'annex') {
            return true;
        }
        $db = $this->registry['db'];
        //get magic articles
        $query = 'SELECT vc.value' . "\n" .
            'FROM ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
            'JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vc' . "\n" .
            '  ON vc.var_id = vm.id AND vc.model_id = 0' . "\n" .
            'WHERE vm.model = "Contract" AND vm.model_type = 1 AND vm.name = "magic_articles"';
        $magic = $db->GetOne($query);
        if (empty($magic)) {
            return true;
        }

        if (!$params['model']->validateDates()) {
            return false;
        }

        $filters = array('where' => array('co.id = ' . $params['model']->get('parent_record'),
                         'co.subtype =\'contract\''));
        $current = Contracts::searchOne($this->registry, $filters);

        //check if the last proforma issued from the contract has NOT been paid
        //also check for magic articles
        $query = 'SELECT fi.*' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fi' . "\n" .
            'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
            '  ON c.id = fi.contract_id and c.type = 1' . "\n" .
            'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
            '  ON g.model = "Finance_Incomes_Reason" AND g.model_id = fi.invoice_id AND g.article_id IN(' . $magic . ')' . "\n" .
            'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
            '  ON f.id = fi.invoice_id AND f.annulled_by = 0 AND f.type = 2' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' fr' . "\n" .
            '  ON fr.parent_model_name = "Finance_Incomes_Reason" AND fr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
            '  AND fr.link_to = fi.invoice_id' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f1' . "\n" .
            '  ON f1.id = fr.parent_id AND f1.type = 1' . "\n" .
            'WHERE fi.contract_id = ' . $current->get('id') . ' AND fi.invoice_id > 0' . "\n" .
            '  AND f1.id IS NULL AND f.payment_status NOT IN ("paid", "invoiced")' . "\n" .
            'GROUP BY g.model_id' . "\n" .
            'ORDER BY invoice_id DESC';
        $prof = $db->GetRow($query);

        if (!empty($prof)) {
            // we will set a save point here to be able to rollback the transaction to here
            // just to achieve some magic
            $db->Execute('SAVEPOINT beforemagic');
            // change the type of the proforma to invoice
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' SET type = 1 WHERE id = ' . $prof['invoice_id'];
            $db->Execute($query);
            // set flag so that method below sets prepared agreement templates
            // into registry so they are available after rollback and can be used further down the code
            $this->registry->set('register_templates', true, true);
            $result = Contracts::calcAgreementsDifferences($current, $params['model'], true);
            if (!is_array($result)) {
                $db->Execute('ROLLBACK TO beforemagic');
                if ($this->registry['action'] == 'setstatus') {
                    return $result;
                }
                if ($result === false) {
                    $this->registry['messages']->insertInSession($this->registry);
                    $result =  'res_obj = {error: 1};';
                    $this->registry->set('ajax_result', $result, true);
                    return false;
                }
            }

            $prof_found = false;
            if (!empty($result['old_invoices'])) {
                foreach ($result['old_invoices'] as $inv) {
                    if ($inv->get('id') == $prof['invoice_id']) {
                        $prof_found = true;
                    }
                }
            }
            // proforma is not present because calculation defined that no correction documents should be issued for it
            if (!$prof_found) {
                //no credit/debit data so we need the proforma invoice and its rows
                //to annul the old proforma invoice and add its rows to the new one
                $profinv = Finance_Incomes_Reasons::searchOne($this->registry, array('where' => array('fir.id = ' . $prof['invoice_id'])));
                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $profinv->getGT2Vars();
                $this->registry->set('get_old_vars', $get_old_vars, true);
                $result['old_invoices'][] = $profinv;
            }

            $old = false;
            if (!empty($result['old_invoices'])) {
                foreach ($result['old_invoices'] as $k => $inv) {
                    if ($inv->get('id') != $prof['invoice_id']) {
                        continue;
                    }
                    $old = clone $inv;
                    unset($result['old_invoices'][$k]);
                    if ($rec = $old->get('debit')) {
                        $rec->set('table_values_are_set', true, true);
                        $rec->slashesEscape();
                        if ($rec->sanitized) {
                            $rec->unsanitize();
                        }
                        $rec->set('issue_date', date('Y-m-d'), true);
                        // no validation as we will get errors
                        $rec->add();
                    }
                    if ($rec = $old->get('credit')) {
                        $rec->set('table_values_are_set', true, true);
                        $rec->slashesEscape();
                        if ($rec->sanitized) {
                            $rec->unsanitize();
                        }
                        $rec->set('issue_date', date('Y-m-d'), true);
                        // no validation as we will get errors
                        $rec->add();
                    }
                    $old->unsetProperty('credit', true);
                    $old->unsetProperty('debit', true);
                    $old->checkAddingCredit(true, 'Contract');
                    // we have just one magical invoice(and it's proforma - don't forget)
                    break;
                }
            }
            $db->Execute('ROLLBACK TO beforemagic');
            if (empty($result['old_invoices'])) {
                unset($result['old_invoices']);
            }
            $magic = preg_split('#\s*,\s*#', $magic);
            $new = array();
            if (!empty($result['new_invoices'])) {
                // check for magic articles in each invoice
                foreach ($result['new_invoices'] as $k => $inv) {
                    $gt2 = $inv->get('grouping_table_2');
                    foreach ($gt2['values'] as $values) {
                        if (in_array($values['article_id'], $magic)) {
                            //one article is enough
                            $new[] = clone $inv;
                            unset($result['new_invoices'][$k]);
                            break;
                        }
                    }
                }
            }
            //IMPORTANT: http://php.net/manual/en/language.types.array.php#language.types.array.syntax.modifying
            //Note:
            //As mentioned above, if no key is specified, the maximum of the existing integer indices is taken, and the new key will be that maximum value plus 1 (but at least 0). If no integer indices exist yet, the key will be 0 (zero).
            //Unsetting the array elements does not reset the internal pointer
            if (empty($result['new_invoices'])) {
                unset($result['new_invoices']);
            }

            if (!$old && !empty($new)) {
                $old = array_shift($new);
                // flag is not used anywhere
                $old->set('from_new', true, true);
            } elseif ($old && $this->registry['action'] == 'setstatus') {
                $old->set('type', PH_FINANCE_TYPE_PRO_INVOICE, true);
                $result['annul'] = array(clone $old);
            }
            if ($old) {
                $ogt2 = $old->get('grouping_table_2');
                $from = $to = false;
                foreach ($ogt2['values'] as $values) {
                    if (!$from) {
                        $from = $values['date_from'];
                        $to = $values['date_to'];
                        continue;
                    }
                    if ($from > $values['date_from']) {
                        $from = $values['date_from'];
                    }
                    if ($to < $values['date_to']) {
                        $to = $values['date_to'];
                    }
                }
                $template_is_set = false;
                if (!empty($new)) {
                    // new proforma should be issued, so merge its rows with those of existing proforma
                    foreach ($new as $inv) {
                        $gt2 = $inv->get('grouping_table_2');
                        $old->set('template_id', $inv->get('template_id'), true);
                        $template_is_set = true;
                        foreach ($gt2['values'] as $values) {
                            $ogt2['values'][] = $values;
                            if ($from > $values['date_from']) {
                                $from = $values['date_from'];
                            }
                            if ($to < $values['date_to']) {
                                $to = $values['date_to'];
                            }
                        }
                    }
                } else {
                    // get registered templates in the registry
                    $templates = $this->registry['invoices_templates'];
                    if (!empty($templates)) {
                        //get ID of the new templates with magic articles
                        foreach($templates as $t) {
                            $tgt2 = $t->get('grouping_table_2');
                            foreach ($tgt2['values'] as $values) {
                                if (in_array($values['article_id'], $magic)) {
                                    //one article is enough
                                    $old->set('template_id', $t->get('id'), true);
                                    $template_is_set = true;
                                    break;
                                }
                            }
                        }
                    }
                    // new template was not found maybe because its start does not intersect with end of invoiced period of proforma
                    // so we try to find if it exists at all because new proforma should be attached to it
                    if (!$template_is_set) {
                        /** @var Contract $annex */
                        $annex = $params['model'];
                        $gov = $this->registry['get_old_vars'];
                        if (!$gov) {
                            $this->registry->set('get_old_vars', true, true);
                        }
                        $annex_templates = $annex->getInvoicesTemplates(
                            array(
                                'exclude_invoices' => true,
                                'sanitize' => true,
                                'recurrent' => '1',
                                'type' => array(PH_FINANCE_TYPE_INVOICE_TEMPLATE_NORMAL),
                            )
                        );
                        if (!$gov) {
                            $this->registry->remove('get_old_vars');
                        }
                        $annex->unsetProperty('invoices_templates', true);

                        foreach ($annex_templates as $t) {
                            $tgt2 = $t->get('grouping_table_2');
                            foreach ($tgt2['values'] as $values) {
                                if (!empty($values['article_id']) && in_array($values['article_id'], $magic)) {
                                    //one article is enough
                                    $old->set('template_id', $t->get('id'), true);
                                    $template_is_set = true;
                                    // only one template is supposed to contain magic articles
                                    break 2;
                                }
                            }
                        }
                    }
                }
                usort($ogt2['values'], array('self', 'sortInvoiceRows'));
                Model_Factory::createGT2InfoData($this->registry, $ogt2['values'], array('model_name' => 'Contract', 'model_type' => 1, 'force' => 1, 'contract_num' => $current->get('num')));
                $old->set('type', PH_FINANCE_TYPE_PRO_INVOICE, true);
                $old->set('from', $from, true);
                $old->set('to', $to, true);
                $old->set('grouping_table_2', $ogt2, true);
                $old->set('id', $old->get('template_id') . '_0', true);
                // make checkbox in screen readonly so that it cannot be unchecked and proforma will always be marked for issue
                $old->set('readonly', true, true);
                // force issuing of proforma even if issue date is in the future
                $old->set('has_invoiced_articles', true, true);
                $this->registry->set('force_invoices', true, true);
                // make sure that the old-new proforma has been attached to a template from annex, otherwise don't include it
                if ($template_is_set) {
                    $result['new_invoices'][] = $old;
                } else {
                    // we will not annul the old proforma because there will be no next proformas with magic articles according to annex
                    if (!empty($result['annul'])) {
                        unset($result['annul']);
                    }
                }
            }
            if (empty($result['new_invoices'])) {
                unset($result['new_invoices']);
            }

            if ($this->registry['action'] == 'setstatus') {
                $this->registry->set('agreement_defferences_result', $result, true);
                return true;
            }
            $view = $params['model']->prepareDifferenceView($result);
            // close the transaction from router
            $db->CompleteTrans();
            echo $view->fetch();
            exit;
        }
        return true;
    }

    /**
     * start annexes of the contracts
     * This is a before_action automation which should run on crontab module
     * before the standard start_contracts_agreements action
     *
     * @param array $params
     * @return bool - result of the operation
     */
    public function startContractsAgreements($params) {
        $db = $this->registry['db'];

        //get magic articles
        $query = 'SELECT vc.value' . "\n" .
            'FROM ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
            'JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vc' . "\n" .
            '  ON vc.var_id = vm.id AND vc.model_id = 0' . "\n" .
            'WHERE vm.model = "Contract" AND vm.model_type = 1 AND vm.name = "magic_articles"';
        $magic = $db->GetOne($query);
        if (empty($magic)) {
            return true;
        }

        //get contracts that have agreements to be pushed into force
        $query = 'SELECT DISTINCT(c1.parent_record)' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' as c1 ' . "\n" .
                 ' WHERE c1.subtype="annex"' . "\n" .
                 ' AND c1.status="closed"' . "\n" .
                 ' AND c1.subtype_status="waiting"' . "\n" .
                 ' AND c1.date_start_subtype <= CURDATE()' . "\n" .
                 'ORDER BY c1.id DESC' . "\n";
        $contracts = $db->GetCol($query);
        if (empty($contracts)) {
            return true;
        }
        //check if the last proforma issued from each contract has NOT been paid
        //also check for magic articles
        $query = 'SELECT fi.contract_id' . "\n" .
            'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' fi' . "\n" .
            'JOIN ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
            '  ON c.id = fi.contract_id and c.type = 1' . "\n" .
            'JOIN ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
            '  ON g.model = "Finance_Incomes_Reason" AND g.model_id = fi.invoice_id AND g.article_id IN(' . $magic . ')' . "\n" .
            'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f' . "\n" .
            '  ON f.id = fi.invoice_id AND f.annulled_by = 0 AND f.type = 2' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' fr' . "\n" .
            '  ON fr.parent_model_name = "Finance_Incomes_Reason" AND fr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
            '  AND fr.link_to = fi.invoice_id' . "\n" .
            'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' f1' . "\n" .
            '  ON f1.id = fr.parent_id AND f1.type = 1' . "\n" .
            'WHERE fi.contract_id IN (' . implode(', ', $contracts) . ') AND fi.invoice_id > 0' . "\n" .
            '  AND f1.id IS NULL AND f.payment_status NOT IN ("paid", "invoiced")' . "\n" .
            'GROUP BY g.model_id' . "\n" .
            'ORDER BY invoice_id DESC';
        $contracts = $db->GetCol($query);

        if (!empty($contracts)) {
            $this->registry->set('has_fin_docs', $contracts, true);
        }
        return true;
    }

    /**
     * Sorts GT2 rows by article (specific advance article rows go to the bottom) and then by period ascending
     * @param array $a
     * @param array $b
     * @return number
     */
    private function sortInvoiceRows($a, $b) {
        if ($a['article_id'] == 2736) {
            if ($b['article_id'] == $a['article_id']) {
                if ($a['date_from'] < $b['date_from']) {
                    return 1;
                } elseif ($a['date_from'] > $b['date_to']) {
                    return -1;
                } elseif ($a['date_to'] < $b['date_to']) {
                    return 1;
                } elseif ($a['date_to'] > $b['date_to']) {
                    return -1;
                }
            } else {
                return 1;
            }
        } else {
            if ($b['article_id'] == $a['article_id']) {
                if ($a['date_from'] < $b['date_from']) {
                    return 1;
                } elseif ($a['date_from'] > $b['date_from']) {
                    return -1;
                } elseif ($a['date_to'] < $b['date_to']) {
                    return 1;
                } elseif ($a['date_to'] > $b['date_to']) {
                    return -1;
                }
            } else {
                return -1;
            }
        }
        return 0;
    }

    /**
     * Notifies specified recipients of unpaid proforma invoices from contracts
     * whose invoicing period is overdue specified number of days.
     *
     * @param array $params
     * @return bool - result of operation
     */
    public function notifyOfUnpaidInvoices($params) {
        $current_date = 'CURDATE()';
        $added = 'NOW()';
        if ($this->registry['request']->get('current_date')) {
            $date = General::strftime('%Y-%m-%d', $this->registry['request']->get('current_date'));
            $current_date = '"' . $date . '"';
            $added = '"' . $date . ' ' . date('H:i:s') . '"';
        }

        if (!$this->settings['recipients'] || !preg_match('#[0-9]+ (day|month|year)#i', $this->settings['interval'])) {
            //the interval and recipients (CSV of emails) are mandatory settings
            $this->registry['db']->Execute(
                //use time() to skip the UNIQUE parent_id/model_id key
                'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY .  "\n" .
                '  SET parent_id=' . $params['id'] . ',' . "\n" .
                '      model_id=' . time() . ',' . "\n" .
                '      added=NOW(),' . "\n" .
                '      num=1,' . "\n".
                '      result=0'
            );
            return true;
        }

        //get the magical article IDs
        $query = 'SELECT vc.value' . "\n" .
                 'FROM ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
                 'JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vc' . "\n" .
                 '  ON vm.name="magic_articles" AND vm.model="Contract" AND vm.id=vc.var_id';
        $magic_articles = $this->registry['db']->GetOne($query);

        //get the proforma data
        $query = 'SELECT proforma_id, proforma_num, proforma_issue_date, proforma_date_of_payment, ' . "\n" .
                 '       customer_id, TRIM(CONCAT(ci18n.name, " ", ci18n.lastname)) as customer_name,' . "\n" .
                 '       contract_id, contract_num, contract_date_validity, contract_date_sign, proforma_start_period ' . "\n" .
                 'FROM (' . "\n" .
                 '  SELECT fir.id as proforma_id, fir.customer as customer_id, fir.num as proforma_num, ' . "\n" .
                 '         fir.issue_date as proforma_issue_date, fir.date_of_payment as proforma_date_of_payment, ' . "\n" .
                 '         co.id as contract_id, co.num as contract_num, co.date_validity as contract_date_validity, ' . "\n" .
                 '         co.date_sign as contract_date_sign, MIN(gt2.date_from) as proforma_start_period' . "\n" .
                 '  FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n" .
                 '  JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                 '    ON fir.type=' . PH_FINANCE_TYPE_PRO_INVOICE . ' AND ' . "\n" .
                 '       active=1 AND annulled=0 AND payment_status IN ("unpaid", "partial") AND ' . "\n" .
                 '       gt2.model="Finance_Incomes_Reason" AND gt2.model_id=fir.id AND gt2.article_id IN (' . $magic_articles .')' . "\n" .
                 '  JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                 '    ON fir.id=frr.parent_id AND frr.parent_model_name="Finance_Incomes_Reason" AND ' . "\n" .
                 '       frr.link_to_model_name="Contract"' . "\n" .
                 '  JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 '    ON frr.link_to=co.id' . "\n" .
                 '  GROUP BY fir.id' . "\n" .
                 '  HAVING DATE_ADD(proforma_start_period, INTERVAL ' . $this->settings['interval'] . ') <= ' . $current_date . "\n" .
                 ') as unpaid_invoices ' . "\n" .
                 'JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                 '  ON customer_id=ci18n.parent_id AND ci18n.lang="' . $this->registry['lang'] . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_AUTOMATIONS_HISTORY . ' AS ah' . "\n" .
                 '  ON ah.model_id=contract_id AND ah.result=proforma_start_period AND ah.parent_id=' . $params['id'] . "\n" .
                 'WHERE ah.model_id IS NULL';
        $proforma_invoices = $this->registry['db']->GetAssoc($query);

        if (empty($proforma_invoices)) {
            //nothing to notify of
            //store history so that the automation is started only once a day
            $this->registry['db']->Execute(
                //use time() to skip the UNIQUE parent_id/model_id key
                'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY .  "\n" .
                '  SET parent_id=' . $params['id'] . ',' . "\n" .
                '      model_id=' . time() . ',' . "\n" .
                '      added=NOW(),' . "\n" .
                '      num=1,' . "\n".
                '      result=0'
            );
            return true;
        }

        require_once (PH_PHPEXCEL_DIR . 'PHPExcel.php');
        require_once (PH_PHPEXCEL_DIR . 'PHPExcel/Writer/Excel2007.php');

        $objPHPExcel = new PHPExcel();

        $objPHPExcel->getProperties()->setCreator("nZoom");
        $objPHPExcel->getProperties()->setTitle("Proforma Invoices Due" . General::strftime('%d.%m.%Y'));
        $objPHPExcel->getProperties()->setDescription("Proforma Invoices @ UNIKEN due " . General::strftime('%d.%m.%Y'));

        $objPHPExcel->setActiveSheetIndex(0);

        //set the header of the table
        $objPHPExcel->getActiveSheet()->SetCellValue('A1', $this->i18n('excel_num'));
        $objPHPExcel->getActiveSheet()->SetCellValue('B1', $this->i18n('excel_customer'));
        $objPHPExcel->getActiveSheet()->SetCellValue('C1', $this->i18n('excel_contract'));
        $objPHPExcel->getActiveSheet()->SetCellValue('D1', $this->i18n('excel_contract_period'));
        $objPHPExcel->getActiveSheet()->SetCellValue('E1', $this->i18n('excel_proforma_invoice'));
        $objPHPExcel->getActiveSheet()->SetCellValue('F1', $this->i18n('excel_last_issue_date'));
        $objPHPExcel->getActiveSheet()->SetCellValue('G1', $this->i18n('excel_date_of_payment'));
        $objPHPExcel->getActiveSheet()->SetCellValue('H1', $this->i18n('excel_due_days'));
        $objPHPExcel->getActiveSheet()->getStyle('A1:H1')->getFont()->setBold(true);

        //set width
        $objPHPExcel->getActiveSheet()->getColumnDimension('A')->setWidth(4);
        $objPHPExcel->getActiveSheet()->getColumnDimension('B')->setWidth(55);
        $objPHPExcel->getActiveSheet()->getColumnDimension('C')->setWidth(10);
        $objPHPExcel->getActiveSheet()->getColumnDimension('D')->setWidth(27);
        $objPHPExcel->getActiveSheet()->getColumnDimension('E')->setWidth(20);
        $objPHPExcel->getActiveSheet()->getColumnDimension('F')->setWidth(27);
        $objPHPExcel->getActiveSheet()->getColumnDimension('G')->setWidth(15);
        $objPHPExcel->getActiveSheet()->getColumnDimension('H')->setWidth(23);

        //prepare the hyperlink to proformas
        $pf_hyperlink  = $this->registry['config']->getParam('crontab', 'base_host');
        $pf_hyperlink .= sprintf('/index.php?%s=finance&controller=incomes_reasons&incomes_reasons=view&view=', Router::MODULE_PARAM);

        $row = 2;
        $inserts = array();
        foreach($proforma_invoices as $pid => $pinvoice) {
            $objPHPExcel->getActiveSheet()->SetCellValue('A' . $row, $row-1);
            $objPHPExcel->getActiveSheet()->SetCellValue('B' . $row, $pinvoice['customer_name']);
            $objPHPExcel->getActiveSheet()->SetCellValue('C' . $row, $pinvoice['contract_num']);
            $objPHPExcel->getActiveSheet()->SetCellValue('D' . $row, preg_replace('#(\d+)-(\d+)-(\d+)#', '\3.\2.\1', $pinvoice['contract_date_sign']) . ' - ' .
                                                                     preg_replace('#(\d+)-(\d+)-(\d+)#', '\3.\2.\1', $pinvoice['contract_date_validity']));

            $objPHPExcel->getActiveSheet()->SetCellValue('E' . $row, '=HYPERLINK("' . $pf_hyperlink . $pid . '","' . $pinvoice['proforma_num'] . '")');
            $objPHPExcel->getActiveSheet()->SetCellValue('F' . $row, preg_replace('#(\d+)-(\d+)-(\d+)#', '\3.\2.\1', $pinvoice['proforma_issue_date']));
            $objPHPExcel->getActiveSheet()->SetCellValue('G' . $row, preg_replace('#(\d+)-(\d+)-(\d+)#', '\3.\2.\1', $pinvoice['proforma_date_of_payment']));
            $objPHPExcel->getActiveSheet()->SetCellValue('H' . $row, $this->_calculateProformaDueDays($pinvoice['proforma_start_period']));

            //IMPORTANT: the automation history stores the contract_id in `automations_history.model_id` and
            //           the starting date of the period in `automations_history.result`
            //           because the proforma invoices are issued in the beginning of each month: a new proforma is issued, the old one is annulled.
            //           So the only way to inform only ONCE of unpaid proforma is to store the contract ID and start of the unpaid period
            $inserts[] = 'INSERT INTO ' . DB_TABLE_AUTOMATIONS_HISTORY . ' VALUES' . "\n" .
                         '('.$params['id'].', '. $pinvoice['contract_id'] . ', ' . $added . ', 1, "' . $pinvoice['proforma_start_period'] . '")' . "\n" .
                         'ON DUPLICATE KEY UPDATE added=' . $added . ', result="' . $pinvoice['proforma_start_period'] . '"';
            $row++;
        }

        //save the file to temporary path
        $temp_file_path = tempnam(ini_get('upload_tmp_dir'), 'xlsx');
        $objWriter = PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel2007');
        $objWriter->save($temp_file_path);

        $lang_file = sprintf('%s%s%s%s', PH_MODULES_DIR, 'nomenclatures/i18n/', $this->registry['lang'], '/nomenclatures.ini');
        $this->loadI18NFiles(array($lang_file));

        //start transaction of creating a new notifying nomenclature
        $this->registry['db']->StartTrans();

        //add new nomenclature that stores the type of notifying 50/80 days
        // and the XLSX file sent to the recipients (setting in this automation)
        $nomenclature = new Nomenclature($this->registry);
        $nomenclature->set('type', $this->settings['nomenclature_type'], true);
        $nomenclature->set('name', sprintf($this->i18n('nomenclature_name'), preg_replace('# (day|minute|month|year)#i', '', $this->settings['interval'])), true);
        $old_nomenclature = clone $nomenclature;
        $nomenclature->getVars();
        $vars = $nomenclature->get('vars');
        foreach($vars as $idx => $var) {
            if ($var['name'] == 'sentdate_updaid_proformas') {
                $vars[$idx]['value'] = date('Y-m-d H:i:s');
            }
        }
        $nomenclature->set('vars', $vars, true);
        if ($nomenclature->save()) {
            // write history
            $nomenclature_filters = array(
                'where' => array('n.id = \'' . $nomenclature->get('id') . '\''),
                'model_lang' => $nomenclature->get('model_lang')
            );
            $new_nomenclature = Nomenclatures::searchOne($this->registry, $nomenclature_filters);
            Nomenclatures_History::saveData(
                $this->registry,
                array(
                    'model' => $new_nomenclature,
                    'action_type' => 'add',
                    'new_model' => $new_nomenclature,
                    'old_model' => $old_nomenclature
                )
            );

            //attach the file to the nomenclature
            $params = array(
                'id'          => 0,
                'name'        => date('Y-m-d') . '_proformas_' . $this->settings['interval'] . '.xlsx',
                'filename'    => date('Y-m-d') . '_proformas_' . $this->settings['interval'] . '.xlsx',
                'description' => '',
                'revision'    => '',
                'permission'  => 'all'
            );
            $file = array(
                'name'     => date('Y-m-d') . '_proformas_' . $this->settings['interval'] . '.xlsx',
                'type'     => 'xlsx',
                'tmp_name' => $temp_file_path,
                'error'    => '',
                'size'     => filesize($temp_file_path)
            );
            $attachment_id = Files::attachFile($this->registry, $file, $params, $nomenclature->sanitize());
        }

        //now update the automation history
        foreach($inserts as $insert) {
            $this->registry['db']->Execute($insert);
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        if ($result) {
            //IMPORTANT: send email only if the transaction is OK
            //prepare mail sending
            $mailer = new Mailer($this->registry, $this->settings['email_template']);
            $mailer->placeholder->add('interval', preg_replace('# (day|minute|month|year)#i', '', $this->settings['interval']));
            $mailer->placeholder->add('to_email', $this->settings['recipients']);
            $mailer->template['model_id'] = $nomenclature->get('id');
            $mailer->template['model_name'] = 'Nomenclature';
            $mailer->template['system_flag'] = '0';
            $mailer->attach(array($attachment_id => array('filename' => date('Y-m-d') . '_proformas_' . $this->settings['interval']. '.xlsx', 'path' => $temp_file_path)));

            //send email
            $result = $mailer->send();
        } else {
            $this->executionErrors[] = 'The transaction has failed! Last mysql error: ' . $this->registry['db']->ErrorMsg();
        }

        //delete the temp file
        unlink($temp_file_path);

        return $result;
    }

    /**
     * Calculates proforma invoices period of unpaid amounts in working days
     *
     * @param string $date
     * @return string $days
     */
    private function _calculateProformaDueDays($date) {
        $days = 0;
        if (empty($date) || !preg_match('#^[0-9]{4}-[0-9]{2}-[0-9]{2}$#', $date)) {
            return $days;
        }
        //IMPORTANT: get the date of payment (it is always 10 working days)
        $date_of_payment = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date, 10);

        //now calculate how many working days have passed since this date of payment
        $days = Calendars_Calendar::getWorkingDays($this->registry, $date_of_payment, General::strftime('%Y-%m-%d'));

        return $days;
    }
}

?>
