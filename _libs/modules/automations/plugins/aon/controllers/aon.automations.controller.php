<?php
include 'aon.automations.medical-notifications.trait.php';
include 'aon.autoCreateDebitNote.trait.php';

class Aon_Automations_Controller extends Automations_Controller {
    use Aon_Automations_MedicalNotifications_Trait;
    use autoCreateDebitNote;

    public $registry;

    public $metaData = array();

    /**
     * Calculates date of payment
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function calculateDateOfPayment($params) {
        // Get the current action
        $action = $this->registry['action'];

        // check if the automation has to be executed
        $model = $this->metaData['model'];

        // includes the contracts class
        if (!empty($this->settings['precalculation'])) {
            $old_model = clone $model;
        } else {
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $filters = array('where'      => array('co.id = ' . $model->get('id')),
                             'model_lang' => $this->registry['lang']);
            $model = Contracts::searchOne($this->registry, $filters);
            $model->getVars();
            $model->sanitize();
            $old_model = clone $model;
        }

        // get vars as an associative array
        $model_assoc_vars = $old_model->getAssocVars();

        // flag for skipping recalculation of the table
        $skip_recalculation = false;

        if ($action == 'edittopic') {
            // check if the table recalculation is triggered
            // if there is no commission percent the recalculation is skipped
            if (!empty($model_assoc_vars['shared_commission_percent']['value'])) {
                $filtered_values = array_filter($model_assoc_vars['shared_commission_percent']['value']);
                if (count($filtered_values)) {
                    // check the gt2 table
                    $gt2 = $model->getGT2Vars();

                    // recalculation is not possible if debit or invoice are attached for even one row
                    foreach ($gt2['values'] as $row => $row_data) {
                        if ($row_data['free_text2'] || $row_data['free_text5']) {
                            $skip_recalculation = true;
                            break;
                        }
                    }
                } else {
                    // no commission percent so exit the automation
                    $skip_recalculation = true;
                }
            } else {
                // no commission percent so exit the automation
                $skip_recalculation = true;
            }
        }

        if ($skip_recalculation) {
            return false;
        }

        // vars to check
        $vars_to_check = array(
            'main_vars'       => array('date_start', 'date_validity'),
            'additional_vars' => array(
                $this->settings['premium_value'],
                $this->settings['premium_currency'],
                $this->settings['number_rows']
            )
        );

        $empty_vars = false;

        foreach ($vars_to_check as $var_type => $check_var_type) {
            foreach ($check_var_type as $check_var) {
                $var_value = '';

                if ($var_type == 'main_vars') {
                    $var_value = $old_model->get($check_var);
                } elseif ($var_type == 'additional_vars') {
                    $var_value = isset($model_assoc_vars[$check_var]) ? $model_assoc_vars[$check_var]['value'] : '';
                    if ($check_var == $this->settings['premium_value']) {
                        $var_value = (float)$var_value;
                    }
                }

                if ($check_var == $this->settings['premium_value'] && $var_value!=='') {
                    continue;
                } else if (!$var_value) {
                    $empty_vars = true;
                    // if there is at least one var empty then the check has to be interrupted
                    // and the automation will not be executed
                    break 2;
                }
            }
        }

        if (!empty($empty_vars)) {
            // if there are empty vars the execution must be stoped
            return false;
        }

        // get all the vars
        $vars_list = $model->get('vars');
        $gt2_vars = '';
        $gt2_var_idx = '';

        // find the GT2 var
        foreach ($vars_list as $var_idx => $vl) {
            if ($vl['type'] == 'gt2') {
                $gt2_vars = $vl;
                $gt2_var_idx = $var_idx;
            }
        }

        // delete all the old rows
        if (!empty($gt2_vars['values'])) {
            foreach ($gt2_vars['values'] as $row_idx => $row_values) {
                if (empty($row_values)) {
                    unset($gt2_vars['values'][$row_idx]);
                } else {
                    $gt2_vars['values'][$row_idx]['deleted'] = 1;
                }
            }
        }

        $model_assoc_vars = $model->getAssocVars();

        $number_rows = (!empty($model_assoc_vars[$this->settings['number_rows']]) ? $model_assoc_vars[$this->settings['number_rows']]['value'] : 0);
        $contract_currency = (!empty($model_assoc_vars[$this->settings['premium_currency']]) ? $model_assoc_vars[$this->settings['premium_currency']]['value'] : '');
        $commission = (!empty($model_assoc_vars[$this->settings['commission_percent']]) ? $model_assoc_vars[$this->settings['commission_percent']]['value'] : '0');
        $commission_type_standart = $this->settings['commission_type_standart'];
        $commission_type_shared = $this->settings['commission_type_shared'];

        // calculates the exchage rate for the selected currency depending on the main currency of installation
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $main_currency = Finance_Currencies::getMain($this->registry);
        $currency_for_gt2_rows = Finance_Currencies::getRate($this->registry, $contract_currency, $main_currency);

        $insurer_id = 0;
        $insurer_name = '';
        $insurer_branch = 0;
        if (!$this->settings['alternative_insurer']) {
            $insurer_id = (!empty($model_assoc_vars[$this->settings['insurer_id']]) ? $model_assoc_vars[$this->settings['insurer_id']]['value'] : '');
            $insurer_name = (!empty($model_assoc_vars[$this->settings['insurer_name']]) ? $model_assoc_vars[$this->settings['insurer_name']]['value'] : '');
            if (!empty($this->settings['insurer_branch'])) {
                $insurer_branch = (!empty($model_assoc_vars[$this->settings['insurer_branch']]) ? $model_assoc_vars[$this->settings['insurer_branch']]['value'] : 0);
            }
        } else {
            $alternative_deliverer_flag = (!empty($model_assoc_vars[$this->settings['alternative_insurer']]) ? $model_assoc_vars[$this->settings['alternative_insurer']]['value'] : '');
            if ($alternative_deliverer_flag == $this->settings['use_alternative_insurer']) {
                $insurer_id = (!empty($model_assoc_vars[$this->settings['alternative_insurer_id']]) ? $model_assoc_vars[$this->settings['alternative_insurer_id']]['value'] : '');
                $insurer_name = (!empty($model_assoc_vars[$this->settings['alternative_insurer_name']]) ? $model_assoc_vars[$this->settings['alternative_insurer_name']]['value'] : '');
                if (!empty($this->settings['insurer_branch'])) {
                    $insurer_branch = (!empty($model_assoc_vars[$this->settings['insurer_branch']]) ? $model_assoc_vars[$this->settings['insurer_branch']]['value'] : 0);
                }
            } else {
                $insurer_id = (!empty($model_assoc_vars[$this->settings['insurer_id']]) ? $model_assoc_vars[$this->settings['insurer_id']]['value'] : '');
                $insurer_name = (!empty($model_assoc_vars[$this->settings['insurer_name']]) ? $model_assoc_vars[$this->settings['insurer_name']]['value'] : '');
                $insurer_branch = 0;
            }
        }

        $row_ids = array();
        if ($this->settings['has_shared_commission']) {
            $shared_commission_ids = (!empty($model_assoc_vars[$this->settings['shared_commission']]) ? $model_assoc_vars[$this->settings['shared_commission']]['value'] : array());
            foreach ($shared_commission_ids as $row_id => $s_id) {
                if (!empty($s_id)) {
                    $row_ids[] = $row_id;
                }
            }
        }

        // There are two options: one period which will include everything in one row or many periods
        // If it is for many periods, the contracts will be for an year. Every period has to last a certain number of months
        // so we have to calculate how many months the current contract period lasts

        $date_start = new DateTime($model->get('date_start'));
        $date_end = new DateTime($model->get('date_validity'));

        // check if the table is not already set in the session (by precalculation)
        $skip_building_table = false;
        $session_diffs = 0;
        if (!empty($model_assoc_vars[$this->settings['contract_precalculated_gt2_field']])) {
            $session_data = $this->registry['session']->get($model_assoc_vars[$this->settings['contract_precalculated_gt2_field']]['value']);
            if (!empty($session_data)) {
                // check if the session base precalculation vars has the same values as currently completed
                foreach ($session_data['precalculation_data'] as $var_to_check => $value_to_check) {
                    if ($session_diffs) {
                        break;
                    }
                    if (($var_to_check == 'date_start' && $value_to_check!=$model->get('date_start')) ||
                        ($var_to_check == 'date_validity' && $value_to_check!=$model->get('date_validity')) ||
                        ($var_to_check == 'insurer_id' && $value_to_check!=$insurer_id) ||
                        ($var_to_check == 'premium' && $value_to_check!=$model_assoc_vars[$this->settings['premium_value']]['value']) ||
                        ($var_to_check == 'commission_percent' && $value_to_check!=$commission) ||
                        ($var_to_check == 'installments' && $value_to_check!=$number_rows) ||
                        ($var_to_check == 'currency' && $value_to_check!=$contract_currency) ||
                        ($var_to_check == 'insurance_type' && $value_to_check!=$model_assoc_vars[$this->settings['insurance_type_var_prefix'] . $model->get('type')]['value']) ||
                        ($var_to_check == 'discount' && $value_to_check!=$model_assoc_vars[$this->settings['discount_percentage_field']]['value'])) {
                        $session_diffs++;
                    } elseif ($var_to_check == 'shared_commission_data') {
                        // check the shared commission
                        $current_shared_commission = array();

                        $shared_commission_customer = array();
                        $shared_commission_percent = array();
                        if ($model_assoc_vars[$this->settings['shared_commission']] && !empty($model_assoc_vars[$this->settings['shared_commission']]['value'])) {
                            $shared_commission_customer = $model_assoc_vars[$this->settings['shared_commission']]['value'];
                        }
                        if ($model_assoc_vars[$this->settings['shared_commission_percent']] && !empty($model_assoc_vars[$this->settings['shared_commission_percent']]['value'])) {
                            $shared_commission_percent = $model_assoc_vars[$this->settings['shared_commission_percent']]['value'];
                        }

                        foreach ($shared_commission_customer as $k_scc => $scc) {
                            if (!empty($scc) && !empty($shared_commission_percent[$k_scc])) {
                                $current_shared_commission[$k_scc] = array(
                                    'customer' => $scc,
                                    'percent'  => $shared_commission_percent[$k_scc]
                                );
                            }
                        }

                        if (count($current_shared_commission) != count($value_to_check)) {
                            $session_diffs++;
                        } else {
                            foreach ($value_to_check as $row_key => $val_dat) {
                                if (!isset($current_shared_commission[$row_key]) || $current_shared_commission[$row_key]['customer'] != $val_dat['customer'] || $current_shared_commission[$row_key]['percent'] != $val_dat['percent']) {
                                    $session_diffs++;
                                    break;
                                }
                            }
                        }
                    }
                }

                if (!$session_diffs) {
                    $skip_building_table = true;
                    $gt2_vars = $session_data['gt2'];
                }
            }

            if (empty($this->settings['precalculation'])) {
                $this->registry['session']->remove($model_assoc_vars[$this->settings['contract_precalculated_gt2_field']]['value']);
            }
        }

        $interval = $date_start->diff($date_end);
        $total_months_in_period = ceil($interval->format('%a') / 30);

        $single_period_months = floor($total_months_in_period / $number_rows);
        if ($single_period_months == 0) {
            $single_period_months = 1;
        }

        // calculates the last date of payment for the first period
        $previous_month = strtotime(General::strftime('%Y-%m', strtotime($model->get('date_start'))));
        $previous_date = $model->get('date_start');
        $contract_day_of_month_validity = sprintf("%d", General::strftime('%d', strtotime($model->get('date_start'))));

        $price_per_row = (!empty($model_assoc_vars[$this->settings['premium_value']]) ? floatval($model_assoc_vars[$this->settings['premium_value']]['value']) : 0)/$number_rows;
        $discount_value = ($price_per_row * floatval($commission))/100;

        $tax_per_row = (!empty($model_assoc_vars[$this->settings['tax_value']]) ? floatval($model_assoc_vars[$this->settings['tax_value_bgn']]['value']) : 0)/$number_rows;

        // define the insurer id
        $insurer_id = (!empty($this->settings['use_alternative_insurer']) ? $model_assoc_vars[$this->settings['alternative_insurer_id']]['value'] : $model_assoc_vars[$this->settings['insurer_id']]['value']);

        $customer_vars = array();
        $casco_policies_data_completed = false;
        $casco_policies_fee_total = 0;
        if (!empty($this->settings['casco_policy']) && $insurer_id && $model_assoc_vars[$this->settings['insurance_group']]['value'] == $this->settings['insurance_group_go']) {
            // get the taxes of the insurer for casco policies
            $casco_policies_data_completed = true;
            $customer_vars = array($this->settings['insurer_sticker'], $this->settings['insurer_green_card'], $this->settings['insurer_fond']);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Customer" AND `model_type`=' . $this->settings['insurer_customer_type'] . ' AND `name` IN ("' . implode('","', $customer_vars) . '")';
            $customer_vars = $this->registry['db']->GetAssoc($sql);

            // get customer vars
            $sql = 'SELECT `var_id`, `value`' . "\n" .
                   'FROM ' . DB_TABLE_CUSTOMERS_CSTM . "\n" .
                   'WHERE `model_id`="' . $insurer_id . '" AND `var_id` IN ("' . implode('","', $customer_vars) . '")' . "\n";
            $customer_vars_values = $this->registry['db']->GetAssoc($sql);

            foreach ($customer_vars as $cstm_var => $cstm_var_id) {
                $customer_vars[$cstm_var] = (!empty($customer_vars_values[$cstm_var_id]) ? sprintf('%.2f', $customer_vars_values[$cstm_var_id]) : '');
            }

            unset($customer_vars_values);
        }

        if (!$skip_building_table) {
            $tax_left = floatval($model_assoc_vars[$this->settings['tax_value_bgn']]['value']);
            $premium_value_left = floatval($model_assoc_vars[$this->settings['premium_value']]['value']);
            $price_per_row = round($price_per_row, 2);
            $discount_left = (!empty($model_assoc_vars[$this->settings['discount_total_field']]['value']) ? round(bcmul($model_assoc_vars[$this->settings['discount_total_field']]['value'], -1, 6), 2) : 0);
            for ($iteration = 0; $iteration < $number_rows; $iteration++) {
                $gt2_row = array(
                    'article_id'                         => '',
                    'free_field2'                        => '',
                    'free_field5'                        => $iteration+1,
                    'free_text1'                         => '',
                    'quantity'                           => $currency_for_gt2_rows,
                    'price'                              => $price_per_row,
                    'free_field1'                        => $contract_currency,
                    'free_field4'                        => $commission_type_standart,
                    'article_alternative_deliverer'      => $insurer_id,
                    'article_alternative_deliverer_name' => $insurer_name,
                    'article_height'                     => $insurer_branch,
                    'discount_percentage'                => $commission,
                    'discount_value'                     => $discount_value,
                    'discount_surplus_field'             => 'discount_percentage',
                );

                if ($iteration == ($number_rows-1)) {
                    $gt2_row['last_delivery_price'] = sprintf('%.2f', round($tax_left, 2));
                    $gt2_row['price'] = sprintf('%.2f', round($premium_value_left, 2));
                } else {
                    $gt2_row['last_delivery_price'] = sprintf('%.2f', round($tax_per_row, 2));
                    $tax_left = $tax_left - $gt2_row['last_delivery_price'];
                    $premium_value_left = $premium_value_left - $price_per_row;
                }

                if ($this->settings['casco_policy']) {
                    $gt2_row['article_code'] = ($iteration == 0 ? @$customer_vars[$this->settings['insurer_fond']] : '');
                    $gt2_row['article_width'] = ($iteration == 0 ? @$customer_vars[$this->settings['insurer_sticker']] : '');
                    $gt2_row['article_weight'] = ($iteration == 0 ? @$customer_vars[$this->settings['insurer_green_card']] : '');
                    $gt2_row['average_weighted_delivery_price'] = sprintf('%.2f',
                        (sprintf('%.2f', $gt2_row['article_code']) +
                         sprintf('%.2f', $gt2_row['article_width']) +
                         sprintf('%.2f', $gt2_row['article_weight']) +
                         sprintf('%.2f', $gt2_row['last_delivery_price']) +
                         sprintf('%.2f', round($gt2_row['price'] * $gt2_row['quantity'], 2)))
                    );
                } else {
                    $gt2_row['average_weighted_delivery_price'] = sprintf('%.2f',
                        (sprintf('%.2f', $gt2_row['last_delivery_price']) +
                         sprintf('%.2f', round($gt2_row['price'] * $gt2_row['quantity'], 2)))
                    );
                }
                $gt2_row['article_deliverer_name'] = sprintf('%.2f', $gt2_row['average_weighted_delivery_price'] * (-1));
                $casco_policies_fee_total += (floatval(isset($gt2_row['article_code']) ? $gt2_row['article_code'] : 0) + floatval(isset($gt2_row['article_width']) ? $gt2_row['article_width'] : 0) + floatval(isset($gt2_row['article_weight']) ? $gt2_row['article_weight'] : 0));

                if ($iteration == 0) {
                    $gt2_row['free_text1'] = $model->get('date_start');
                } else {
                    // calculate the date depending on the date of payment
                    $current_period_month = strtotime('+' . $single_period_months . ' months', $previous_month);
                    $current_month = General::strftime('%Y-%m', $current_period_month);
                    $number_days_in_current_month = date('t', $current_period_month);
                    if ($number_days_in_current_month < $contract_day_of_month_validity) {
                        $current_date = $current_month . '-' . sprintf('%02s', $number_days_in_current_month);
                    } else {
                        $current_date = $current_month . '-' . sprintf('%02s', $contract_day_of_month_validity);
                    }

                    if ($current_date>$model->get('date_validity')) {
                        $gt2_row['free_text1'] = $previous_date;
                    } else {
                        $gt2_row['free_text1'] = $current_date;
                        $previous_month = $current_period_month;
                        $previous_date = $current_date;
                    }
                }

                $gt2_vars['values'][] = $gt2_row;

                if (!empty($row_ids)) {
                    foreach ($row_ids as $row_num) {
                        if (empty($shared_commission_customers)) {
                            $shared_commission_customers = (!empty($model_assoc_vars[$this->settings['shared_commission']]) ? $model_assoc_vars[$this->settings['shared_commission']]['value'] : array());
                            $shared_commission_customers_names = (!empty($model_assoc_vars[$this->settings['shared_commission_name']]) ? $model_assoc_vars[$this->settings['shared_commission_name']]['value'] : array());
                            $shared_commission_percents = (!empty($model_assoc_vars[$this->settings['shared_commission_percent']]) ? $model_assoc_vars[$this->settings['shared_commission_percent']]['value'] : array());
                        }

                        //calculate the discount according to the price stored in GT2, not according to the average price
                        $sher_discount_value = ($gt2_row['price'] * (!empty($shared_commission_percents[$row_num]) ? floatval($shared_commission_percents[$row_num]) : 0))/100;

                        $gt2_vars['values'][] = array(
                            'article_id'                         => '',
                            'free_field2'                        => '',
                            'free_field5'                        => $gt2_row['free_field5'],
                            'free_text1'                         => $gt2_row['free_text1'],
                            'quantity'                           => $currency_for_gt2_rows,
                            'price'                              => ($gt2_row[(!empty($this->settings['shared_commission_parent_field_base_calculate']) ? $this->settings['shared_commission_parent_field_base_calculate'] : 'price')])*(-1),
                            'free_field1'                        => $contract_currency,
                            'free_field4'                        => $commission_type_shared,
                            'article_alternative_deliverer'      => (!empty($shared_commission_customers[$row_num]) ? $shared_commission_customers[$row_num] : 0),
                            'article_alternative_deliverer_name' => (!empty($shared_commission_customers_names[$row_num]) ? $shared_commission_customers_names[$row_num] : ''),
                            'article_height'                     => 0,
                            'discount_percentage'                => (!empty($shared_commission_percents[$row_num]) ? $shared_commission_percents[$row_num] : 0),
                            'discount_value'                     => (!empty($sher_discount_value) ? $sher_discount_value : 0)*(-1),
                            'discount_surplus_field'             => !empty($this->settings['shared_commission_discount_field_priority']) ? $this->settings['shared_commission_discount_field_priority'] : (!empty($discount_value) ? 'discount_percentage' : 'discount_value'),
                        );
                    }
                }

                // check for discount
                if (!empty($model_assoc_vars[$this->settings['discount_percentage_field']]['value'])) {
                    $discount_customer_data = array(
                        'id'   => '',
                        'name' => '',
                    );
                    if (empty($discount_customer_data['id'])) {
                        $discount_customer_data['id'] = $this->settings['discount_customer'];
                        $sql = 'SELECT CONCAT(ci18n.name, " ", ci18n.lastname)' . "\n" .
                               'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                               'INNER JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                               ' ON c.id="' . $discount_customer_data['id'] . '" AND c.id=ci18n.parent_id AND ci18n.lang="' . $this->registry['lang'] . '"';
                        $discount_customer_data['name'] = $this->registry['db']->GetOne($sql);
                    }
                    if ($iteration == ($number_rows-1)) {
                        $discount_calculate = $discount_left;
                    } else {
                        $discount_calculate = round(bcmul(bcmul($gt2_row['price'], bcdiv($model_assoc_vars[$this->settings['discount_percentage_field']]['value'], 100, 6), 6), -1, 6), 2);
                        $discount_left = round(bcsub($discount_left, $discount_calculate, 6), 2);
                    }

                    // add a row for discount
                    $gt2_vars['values'][] = array(
                        'article_id'                         => '',
                        'free_field2'                        => '',
                        'free_field5'                        => $gt2_row['free_field5'],
                        'free_text1'                         => $gt2_row['free_text1'],
                        'quantity'                           => $currency_for_gt2_rows,
                        'price'                              => $discount_calculate,
                        'free_field1'                        => $contract_currency,
                        'free_field4'                        => $this->settings['commission_type_discount'],
                        'article_alternative_deliverer'      => $discount_customer_data['id'],
                        'article_alternative_deliverer_name' => $discount_customer_data['name'],
                        'article_deliverer_name'             => round($discount_calculate * $currency_for_gt2_rows, 2),
                        'average_weighted_delivery_price'    => round($discount_calculate * $currency_for_gt2_rows, 2),
                        'article_height'                     => 0,
                        'discount_percentage'                => 100,
                        'discount_value'                     => $discount_calculate,
                        'discount_surplus_field'             => 'discount_percentage',
                    );
                }
            }
        }

        if (!empty($this->settings['precalculation'])) {
            return $gt2_vars;
        }

        // get the bso nomenclatures
        $system_noms = array();
        if (!empty($this->settings['system_nomenclatures'])) {
            $system_noms = array_filter(preg_split('#\s*,\s*#', $this->settings['system_nomenclatures']));
        }

        $bso_list = array();
        if ($this->settings['casco_policy']) {
            foreach ($gt2_vars['values'] as $row => $row_vals) {
                if (!empty($row_vals['article_id'])) {
                    if (!in_array($row_vals['article_id'], $system_noms)) {
                        $bso_list[] = $row_vals['article_id'];
                    }
                }
                if (!empty($row_vals['article_barcode'])) {
                    if (!in_array($row_vals['article_barcode'], $system_noms)) {
                        $bso_list[] = $row_vals['article_barcode'];
                    }
                }
            }
        }

        // set the new values in the model
        $model->set('table_values_are_set', true, true);
        $model->set('grouping_table_2', $gt2_vars, true);
        $model->unsanitize();
        $result = $model->saveGT2Vars($gt2_vars);

        $insert = array();
        if ($casco_policies_data_completed && $casco_policies_fee_total && !$model_assoc_vars[$this->settings['fee_value']]['value']) {

            // complete the fee data for the contract
            $insert[] = sprintf('("%d", "%d", "%s", "1", NOW(), "%d", NOW(), "%d", "")', $model->get('id'), $model_assoc_vars[$this->settings['fee_value_bgn']]['id'], sprintf('%.2f', $casco_policies_fee_total), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
            $insert[] = sprintf('("%d", "%d", "%s", "1", NOW(), "%d", NOW(), "%d", "")', $model->get('id'), $model_assoc_vars[$this->settings['fee_value']]['id'], sprintf('%.2f', round($casco_policies_fee_total/$currency_for_gt2_rows, 2)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
        }

        if ($casco_policies_data_completed && $this->registry['action'] == 'add') {
            // prepare the queries for the starting commission

            $commission_value = round(($model_assoc_vars[$this->settings['commission_percent']]['value'] * (!empty($model_assoc_vars[$this->settings['premium_value']]) ? $model_assoc_vars[$this->settings['premium_value']]['value'] : 0) / 100), 2);
            $insert[] = sprintf('("%d", "%d", "%s", "1", NOW(), "%d", NOW(), "%d", "")', $model->get('id'), $model_assoc_vars[$this->settings['starting_commission']]['id'], $model_assoc_vars[$this->settings['commission_percent']]['value'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
            $insert[] = sprintf('("%d", "%d", "%s", "1", NOW(), "%d", NOW(), "%d", "")', $model->get('id'), $model_assoc_vars[$this->settings['starting_commission_value']]['id'], sprintf('%.2f', $commission_value), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
            $insert[] = sprintf('("%d", "%d", "%s", "1", NOW(), "%d", NOW(), "%d", "")', $model->get('id'), $model_assoc_vars[$this->settings['starting_commission_value_bgn']]['id'], sprintf('%.2f', round($commission_value*$currency_for_gt2_rows, 2)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
        }

        if (!empty($insert)) {
            $query = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CSTM . ' (`model_id`, `var_id`, `value`, `num`, `modified`, `modified_by`, `added`, `added_by`, `lang`) VALUES ' . "\n" .
                     implode(', ' . "\n", $insert) .
                     'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)';
            $this->registry['db']->Execute($query);
        }

        $filters = array('where'      => array('co.id = ' . $model->get('id')),
                         'model_lang' => $this->registry['lang']);
        $new_model = Contracts::searchOne($this->registry, $filters);

        $this->registry->set('get_old_vars', true, true);
        $new_model->getVars();
        $new_model->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        // saves history
        require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
        require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';

        Contracts_History::saveData($this->registry, array('model' => $model, 'action_type' => 'edittopic', 'new_model' => $new_model, 'old_model' => $old_model));

        if (!empty($bso_list)) {
            // include required classes
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';

            // GET THE SEARCHED NOMENCLATURES
            $filters = array('where'      => array('n.id IN ("' . implode('","', $bso_list) . '")'),
                             'model_lang' => $this->registry['lang'],
                             'sanitize'   => true
            );
            $bso_models = Nomenclatures::search($this->registry, $filters);

            // GOES THROUGH ALL THE NOMENCLATURES AND FILL THEM WITH THE NEW DATA
            foreach ($bso_models as $bso) {
                // unsanitze the model
                $bso->unsanitize();
                $old_bso = clone $bso;

                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $bso->getVars();
                $this->registry->set('get_old_vars', $get_old_vars, true);

                $bso_vars = $bso->get('vars');

                foreach ($bso_vars as $idx_var => $bso_var) {
                    if ($bso_var['name'] == $this->settings['nomenclature_policy_id']) {
                        $bso_vars[$idx_var]['value'] = $new_model->get('id');
                    } elseif ($bso_var['name'] == $this->settings['nomenclature_policy_num']) {
                        $bso_vars[$idx_var]['value'] = General::slashesStrip($new_model->get('custom_num'));
                    } elseif ($bso_var['name'] == $this->settings['nomenclature_status']) {
                        $bso_vars[$idx_var]['value'] = $this->settings['nomenclature_status_used'];
                    } elseif ($bso_var['name'] == $this->settings['nomenclature_client_id']) {
                        $bso_vars[$idx_var]['value'] = $new_model->get('customer');
                    } elseif ($bso_var['name'] == $this->settings['nomenclature_client_name']) {
                        $bso_vars[$idx_var]['value'] = General::slashesStrip($new_model->get('customer_name'));
                    }
                }

                // set the filled vars array back to the model
                $bso->set('vars', $bso_vars, true);

                // saves the model
                if ($bso->save()) {
                    $filters = array('where'      => array('n.id = ' . $bso->get('id')),
                                     'model_lang' => $bso->get('model_lang'));
                    $new_bso = Nomenclatures::searchOne($this->registry, $filters);
                    $get_old_vars = $this->registry->get('get_old_vars');
                    $this->registry->set('get_old_vars', true, true);
                    $new_bso->getVars();

                    // write history
                    Nomenclatures_History::saveData($this->registry, array('model' => $bso, 'action_type' => 'edit', 'new_model' => $new_bso, 'old_model' => $old_bso));
                    $this->registry->set('get_old_vars', $get_old_vars, true);
                }
            }
        }

        return true;
    }

    /**
     * Validates insurers
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function validateInsurers($params) {
        if (!$this->registry['request']->isPost() || !in_array($this->registry['action'], array('add', 'edittopic', 'addannex'))) {
            return true;
        }
        $request = $this->registry['request'];

        if (!($request->get('insurer_name') && $request->get('insurer_id') || $request->get('name_insurer') && $request->get('id_insurer'))) {
            $this->registry['messages']->setError($this->i18n('error_invalid_insurer_' . $params['model']->get('type')), array('insurer_name', 'name_insurer'));
            return false;
        }

        return true;
    }

    /**
     * Set the paid records of the GT2 to be readonly rows
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function setGT2ReadonlyRows($params) {
        // Get the current action
        $action = $this->registry['action'];

        // If this automation should be executed
        if ($action == 'addannex' || $action == 'edittopic' && $params['model']->get('subtype') == 'annex') {
            // Get the model
            $model = $params['model'];

            // Get the variables
            $model->getVarsForTemplate();
            $vars = $model->get('vars');

            // Get the gt2 var
            foreach ($vars as $key => $var) {
                if ($var['type'] == 'gt2') {
                    $gt2_key = $key;
                    $gt2     = $var;
                    break;
                }
            }

            // If the GT2 is empty - just exit the automation
            if (empty($gt2)) {
                return true;
            }

            // If the request is post (this is used when the form is submitted but there are some errors)
            if ($this->registry['request']->isPost()) {
                // Set into the registry that the old vars should be used
                $this->registry->set('get_old_vars', true, true);

                // Get only the GT2 vars values
                $old_values = $model->getGT2Vars();
                $old_values = $old_values['values'];

                // Set into the registry that the old vars should NOT be used
                $this->registry->set('get_old_vars', false, true);
            } else {
                // Set the old values to equal the new ones
                $old_values = $gt2['values'];
            }

            // Set which rows should be readonly (the paid ones)
            $rows_readonly           = array();
            $i                       = 1;
            $last_editable_row_index = 1;
            foreach ($gt2['values'] as $key => $values) {
                // if $values is empty (i.e. GT2 table has no rows)
                if (empty($values)) continue;
                if (isset($old_values[$key]) && $values['free_field2'] == 'fsa_yes' && $old_values[$key]['free_field2'] == 'fsa_yes') {
                    $rows_readonly[]         = $i;
                    $last_editable_row_index = $i;
                }
                $i++;
            }

            // Prepare the readonly rows list
            $gt2['rows_readonly']           = $rows_readonly;
            // Set to use a delimiter_start to determine when the "-" button should stop working
            $gt2['delimeter_start']         = true;
            // Append a new empty row which is marked as deleted. It will be
            // used as a template row when adding new rows from the "+" button
            // there is no need to edit the GT2, so remove the empty row
            //$gt2['values']                  = $gt2['values'] + array(array('deleted' => '1'));
            // Set the index of the last editable row to determine where to put the delimiter
            $gt2['last_editable_row_index'] = $last_editable_row_index-1;

            // Set back the gt2
            $vars[$gt2_key]                 = $gt2;
            // Set back the vars
            $model->set('vars', $vars, true);

            // Set the model back into the registry
            $this->registry->set('contract', $model, true);

            return true;
        } else {
            return true;
        }
    }

    /**
     * Set unique key for each row of the contract's/annex's GT2 to correctly identify it
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function setGT2RowsKeys($params) {
        // Get the current action
        $action = $this->registry['action'];

        // If this automation should be executed
        $actions_list = array_filter(preg_split('/\s*,\s*/', $this->settings['actions_execute']));

        if (in_array($action, $actions_list)) {
            // Get the current model from the meta data
            $model    = $this->metaData['model'];
            $model_id = $model->get('id');

            // Get the model of the contract/annex from the database
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $filters = array('where'      => array('co.id = ' . $model_id),
                             'model_lang' => $this->registry['lang']);
            $model = Contracts::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            // Load the model's vars
            $model->getVars();
            $this->registry->set('get_old_vars', false, true);

            // Get the vars
            $vars = $model->get('vars');

            // Get the GT2
            foreach ($vars as $key => $var) {
                if ($var['type'] == 'gt2') {
                    $gt2_key = $key;
                    $gt2     = $var;
                    break;
                }
            }

            // If the GT2 is empty - just exit the automation
            if (empty($gt2)) {
                return true;
            }

            // Generate uniquie keys for each GT2 row
            $new_keys_added = false;
            foreach ($gt2['values'] as $key => $values) {
                // If there is no key for this row
                if (empty($values['free_field3'])) {
                    // Generate one
                    $values['free_field3'] = uniqid();
                    $gt2['values'][$key]   = $values;
                    $new_keys_added        = true;
                } else {
                    // Just continue
                    continue;
                }
            }

            // Set the keys
            if ($new_keys_added) {
                $model->set('table_values_are_set', true, true);
                $model->unsanitize();
                $model->saveGT2Vars($gt2, true);
            }

            return true;
        } else {
            return true;
        }
    }

    /**
     * Validate that the number of GT2 rows should not exceed the number of installments
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function validateGT2RowsCount($params) {
        // Get the basic data
        $action              = $this->registry['action'];
        $request             = $this->registry['request'];
        $number_installments = $request->get('number_installments');

        // If this automation should be executed
        if (($action == 'addannex' || $action == 'edittopic' && $params['model']->get('subtype') == 'annex')
              && $request->isPost()
              && !empty($number_installments)
              && $number_installments >= 0) {
            // Get the list with the information which rows are deleted
            $gt2_rows_deleted = $request->get('deleted');

            // Count the non-deleted GT2 rows
            $gt2_rows_count   = 0;
            if ($gt2_rows_deleted) {
                foreach ($gt2_rows_deleted as $gt2_row_deleted) {
                    if ($gt2_row_deleted != 1) {
                        $gt2_rows_count++;
                    }
                }
            }

            // If the number of the GT2 rows exceeds the number of the installments
            if ($gt2_rows_count > 0 AND $gt2_rows_count > $number_installments) {
                // Raise an error
                $params['model']->raiseError('error_number_installments', 'number_installments', null, array($gt2_rows_count, $number_installments));

                // Fail the automation
                return false;
            }
        }

        return true;
    }

    /**
     * Sets employee of model as administrative contact of own company when a contract is added.
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function setContractContacts($params) {

        // Get the current action
        $action = $this->registry['action'];

        // only when adding contract of 'contract' subtype
        if ($action != 'add' || !$params['model']->get('employee')) {
            return false;
        }

        $employee = $params['model']->get('employee');
        $employee_email = '';

        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('where' => array('c.id = \'' . $employee . '\'',
                                          'c.active = 1'),
                         'sanitize' => true);
        $employee_customer = Customers::searchOne($this->registry, $filters);

        if ($employee_customer && $employee_customer->get('email')) {
            $employee_email = $employee_customer->get('email');
            $employee_email = reset($employee_email);
        }

        if ($employee && $employee_email) {

            $this->registry['request']->set('self_administrative', $employee, 'all', true);
            $this->registry['request']->set('self_adm_email', $employee_email, 'all', true);
            $this->registry['request']->set('self_financial', '[default_financial_contact_person]', 'all', true);

            // get the old model from the DB (for audit purposes)
            $filters = array('where' => array('co.id = ' . $params['model_id']));
            $this->registry->set('getContactCc', true, true);
            $model = Contracts::searchOne($this->registry, $filters);

            $old_contract = clone $model;
            $old_contract->sanitize();

            //save contract parties
            if ($model->savePartiesData()) {
                $filters = array('where' => array('co.id = ' . $model->get('id')),
                                 'sanitize' => true);
                $new_contract = Contracts::searchOne($this->registry, $filters);

                //save history
                require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
                $audit_parent = Contracts_History::saveData(
                    $this->registry,
                    array(
                        'model' => $model,
                        'action_type' => 'parties',
                        'new_model' => $new_contract,
                        'old_model' => $old_contract
                    )
                );
            }
            $this->registry->remove('getContactCc');
        }

        return true;
    }

    /**
     * Automation to add nomenclatures depending on the BSO document's
     * properties when the acceptance protocol is set as active
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function addBSONomenclatures($params) {
        // get the model
        $model = $params['model'];

        // get needed vars from the document
        $model_vars = $model->getAssocVars();
        $type_blank = $model_vars[$this->settings['document_type_blank']]['value'];
        $starting_number = sprintf('%d', $model_vars[$this->settings['document_starting_number']]['value']);
        $ending_number = sprintf('%d', $model_vars[$this->settings['document_ending_number']]['value']);
        $prefix = trim(sprintf('%s', $model_vars[$this->settings['document_prefix']]['value']));
        $suffix = trim(sprintf('%s', $model_vars[$this->settings['document_suffix']]['value']));

        // include required classes
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';

        // forms an array with vars that automation has to complete
        $nom_var_names = array($this->settings['nomenclature_type_blank'], $this->settings['nomenclature_insurer'], $this->settings['nomenclature_insurer_name'], $this->settings['nomenclature_protocol'], $this->settings['nomenclature_protocol_num'], $this->settings['nomenclature_office']);

        // goes through all the numbers
        for ($i = $starting_number; $i <= $ending_number; $i++) {
            // old nomenclature model
            $old_nomenclature = new Nomenclature($this->registry);
            $this->registry->set('get_old_vars', true, true);
            $old_nomenclature->getVars();
            $old_nomenclature->sanitize();

            // prepare the nomenclature model
            $nomenclature = Nomenclatures::buildModel($this->registry);
            $nomenclature->set('id', null, true);
            $nomenclature->set('name', sprintf('%s%d%s', $prefix, $i, $suffix), true);
            $nomenclature->set('type', $this->settings['nomenclature_type'], true);
            $nomenclature->set($this->settings['nomenclature_type_blank'], $type_blank, true);
            $nomenclature->set($this->settings['nomenclature_insurer'], $model->get('customer'), true);
            $nomenclature->set($this->settings['nomenclature_insurer_name'], $model->get('customer_name'), true);
            $nomenclature->set($this->settings['nomenclature_protocol'], $model->get('id'), true);
            $nomenclature->set($this->settings['nomenclature_protocol_num'], sprintf('%s/%s', $model->get('full_num'), $model->get('custom_num')), true);
            $nomenclature->set($this->settings['nomenclature_office'], $model->get('office'), true);
            $nomenclature->set('plain_vars', null, true);
            $this->registry->set('get_old_vars', false, true);
            $nomenclature->getVars();

            // complete the values for the additional variables
            $nom_vars = $nomenclature->get('vars');
            foreach ($nom_vars as $idx => $var_info) {
                if (in_array($var_info['name'], $nom_var_names)) {
                    $nom_vars[$idx]['value'] = $nomenclature->get($var_info['name']);
                } else if ($var_info['name'] == $this->settings['nomenclature_status']) {
                    $nom_vars[$idx]['value'] = $this->settings['nomenclature_status_free'];
                }
            }
            $nomenclature->set('vars', $nom_vars, true);

            // saves the model
            if ($nomenclature->save()) {
                $filters = array('where' => array('n.id = ' . $nomenclature->get('id')),
                                 'model_lang' => $nomenclature->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $filters);
                $this->registry->set('get_old_vars', true, true);
                $new_nomenclature->getVars();

                // write history
                $audit_parent = Nomenclatures_History::saveData($this->registry, array('model' => $nomenclature, 'action_type' => 'add', 'new_model' => $new_nomenclature, 'old_model' => $old_nomenclature));
                $this->registry->set('get_old_vars', false, true);
                unset($nomenclature);
            }
        }
        return true;
    }

    /**
     * Crontab automation to start agreements that have to be applied the very same day
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function startAdditionalAgreements($params) {
        $db = $this->registry['db'];

        $result = true;

        // get the additional agreements which will be executed
        $query = 'SELECT c1.id, c1.parent_record as contract_id' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' as c1 ' . "\n" .
                 ' WHERE c1.subtype="annex"' . "\n" .
                 ' AND c1.status="closed"' . "\n" .
                 ' AND c1.subtype_status="waiting"' . "\n" .
                 ' AND c1.date_start_subtype <= CURDATE()';
        $agreements_contracts_relation = $db->GetAssoc($query);

        if (!empty($agreements_contracts_relation)) {
            // get the gt2 rows from the parent contracts
            $query_gt2 = 'SELECT `id`, `article_second_code`' . "\n" .
                         'FROM ' . DB_TABLE_GT2_DETAILS . "\n" .
                         'WHERE `model`="Contract" AND `model_id` IN ("' . implode('","', $agreements_contracts_relation) . '")';
            $contract_documents = $db->GetAssoc($query_gt2);

            $db->StartTrans();

            // start the agreements
            require_once PH_MODULES_DIR . 'crontab/models/contracts.crontabs.model.php';
            $crontab = new Contracts_Crontab($this->registry, array('action'=>'start_annex'));
            $crontab->startContractsAgreements();
            unset($crontab);

            $query = 'SELECT `id`, `parent_record`' . "\n" .
                     'FROM ' . DB_TABLE_CONTRACTS . "\n" .
                     'WHERE `id` IN ("' . implode('","', array_keys($agreements_contracts_relation)) . '")' . "\n" .
                     ' AND `subtype_status`="started"';
            $started_agreements = $db->GetAssoc($query);

            if (!empty($started_agreements)) {
                // get the gt2 rows for the parent contracts
                $query_new_gt2_contracts = 'SELECT `id`, `article_second_code`' . "\n" .
                                           'FROM ' . DB_TABLE_GT2_DETAILS . "\n" .
                                           'WHERE `model`="Contract" AND `model_id` IN ("' . implode('","', $started_agreements) . '") AND `article_second_code`!="" AND `article_second_code`!="0" AND `article_second_code` IS NOT NULL';
                $new_contracts_documents_relations = $db->GetAssoc($query_new_gt2_contracts);

                // build and execute gt2
                $i = 1;
                foreach ($new_contracts_documents_relations as $contract_gt2_id => $document_gt2_id) {
                    $previous_gt2_contract = array_search($document_gt2_id, $contract_documents);
                    if ($previous_gt2_contract && !$db->HasFailedTrans()) {
                        $update_query = sprintf('UPDATE %s SET `free_field2`="%d" WHERE `id`="%d" AND `free_field2`="%d"', DB_TABLE_GT2_DETAILS, $contract_gt2_id, $document_gt2_id, $previous_gt2_contract);
                        $db->Execute($update_query);
                        if ($db->HasFailedTrans()) {
                            $this->executionErrors[$i] = $this->prepareErrorMessage('Error when trying to execute query:' . "\n" . $update_query . "\n" . ' (%s)');
                            $i++;
                            $db->FailTrans();
                        }
                    }
                }
            }

            $result = !$db->HasFailedTrans();
            if (!$result) {
                $this->executionErrors[0] = 'The additional agreements for the current day are not started!!!';
            }
            $db->CompleteTrans();
        }

        // writes history
        $this->updateAutomationHistory($params, 0, $result);

        return $result;
    }

    /**
     * Prepares error message from error messages from database and registry.
     *
     * @param string $error_message_text - text for error message with placeholder
     * @return string - error message text with collected error messages filled in
     */
    private function prepareErrorMessage($error_message_text = '%s') {
        $error_messages = array();

        $db = &$this->registry['db'];
        // DB error message
        if ($db->ErrorMsg()) {
            $error_messages[] = 'DB error message: ' . $db->ErrorMsg();
        }

        //check for error messages from execution
        $error_messages = array_merge($error_messages, $this->registry['messages']->getErrors());
        $this->registry['messages']->flush();

        return sprintf($error_message_text, implode('; ', $error_messages));
    }

    /**
     * Exports all clients, insurers, documents (invoices), users, departments and nomenclatures
     * into a shared folder, so that they can be imported periodically
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function periodicalExport($params) {
        $result = true;

        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //check settings first
        if (empty($this->settings['export_host']) || empty($this->settings['export_db']) || empty($this->settings['export_user']) || empty($this->settings['export_pass'])) {
            $this->executionErrors[] = $this->i18n('error_no_db_settings');
        }

        $dsn = sprintf('%s://%s:%s@%s%s/%s?persist',
                    'mysqli',
                    $this->settings['export_user'],
                    $this->settings['export_pass'],
                    $this->settings['export_host'],
                    (isset($this->settings['port'])) ? ':' . $this->settings['port'] : '',
                    $this->settings['export_db']
       );

        //create new DB connection for export
        $dbe = @ADONewConnection($dsn);

        if (is_object($dbe)) {
            //set charset of the connection: UTF-8
            $dbe->Execute('SET NAMES utf8');

            //set default fetch mode
            $dbe->SetFetchMode(ADODB_FETCH_ASSOC);
            $tables = $dbe->MetaTables();
        } else {
            $this->executionErrors[] = $this->i18n('error_no_db_connection');
        }

        if (empty($this->executionErrors)) {

            $dbe->StartTrans();

            //EXPORT THE CLIENTS
            if (empty($tables) || !in_array($this->settings['export_table_clients'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_clients']}` (
                          `CLI_CLIENT_ID` int(11) NOT NULL DEFAULT '0',
                          `CLI_LOCAL_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_LATIN_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_DUNS_NUMBER` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_LOCAL_GROUP` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_ULTIMATE_PARENT_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_CITY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_MARKET_SEGMENT` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_INDUSTRY_ID` int(11) DEFAULT NULL,
                          `CLI_AGCN` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_FOREIGN_DIRECT_INV_CTY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_PUBLIC_SECTOR` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `CLI_ACCOUNTEXECUTIVE_ID` int(11) DEFAULT NULL,
                          `CLI_ACCOUNT_HANDLER_ID` int(11) DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          PRIMARY KEY (`CLI_CLIENT_ID`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_clients'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_clients'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //select clients to delete
                $query = 'SELECT c.id' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'WHERE c.type="' . $this->settings['client_type'] . '" AND c.subtype="normal" AND (c.active=0 AND c.modified>"' . $last_export . '" OR c.deleted>"' . $last_export . '")' . "\n" .
                         'ORDER BY c.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_clients'] . "\n" .
                             'WHERE CLI_CLIENT_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_clients'));
                    }
                }

                //select the clients (type = $this->settings['client_type'])
                $query = 'SELECT c.id, TRIM(CONCAT(ci.name, " ", ci.lastname)) as name, ' . "\n" .
                         '       ci.city, c.added, c.modified,' . "\n" .
                         '       (SELECT GROUP_CONCAT(ti.name)' . "\n" .
                         '       FROM ' . DB_TABLE_TAGS_MODELS . ' AS ct' . "\n" .
                         '       LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti' . "\n" .
                         '         ON (ct.tag_id=ti.parent_id AND ti.lang="' . $this->registry['lang'] . '")' . "\n" .
                         '       WHERE ct.model=\'Customer\' AND ct.model_id=c.id) AS tags' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci ' . "\n" .
                         '  ON (c.id=ci.parent_id AND ci.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'WHERE c.type="' . $this->settings['client_type'] . '" AND c.subtype="normal" AND c.modified>"' . $last_export . '"' . "\n" .
                         '      AND c.active=1 AND c.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY c.modified';
                $clients = $this->registry['db']->GetAll($query);
                foreach($clients as $client) {
                    $update = array();
                    $update['CLI_LOCAL_NAME']             = 'CLI_LOCAL_NAME="' . General::slashesEscape($client['name']) . '"';                    // 2: CLI_LOCAL_NAME: name + lastname (BG)
                    $update['CLI_LATIN_NAME']             = 'CLI_LATIN_NAME="' . General::slashesEscape($client['name']) . '"';                    // 3: CLI_LATIN_NAME: name + lastname (BG)
                    $update['CLI_DUNS_NUMBER']            = 'CLI_DUNS_NUMBER=""';                                                                  // 4: CLI_DUNS_NUMBER
                    $update['CLI_LOCAL_GROUP']            = 'CLI_LOCAL_GROUP=""';                                                                  // 5: CLI_LOCAL_GROUP
                    $update['CLI_ULTIMATE_PARENT_NAME']   = 'CLI_ULTIMATE_PARENT_NAME=""';                                                         // 6: CLI_ULTIMATE_PARENT_NAME
                    $update['CLI_CITY']                   = 'CLI_CITY="' . General::slashesEscape($client['city']) . '"';                                                  // 7: CLI_CITY: customer city
                    $update['CLI_MARKET_SEGMENT']         = 'CLI_MARKET_SEGMENT=""';                                                               // 8: CLI_MARKET_SEGMENT
                    $update['CLI_INDUSTRY_ID']            = 'CLI_INDUSTRY_ID=""';                                                                  // 9: CLI_INDUSTRY_ID
                    $update['CLI_AGCN']                   = 'CLI_AGCN="' . ((strpos($client['tags'], 'AGCN') !== false) ? 'AGCN' : 'Local') . '"'; //10: CLI_AGCN: if tagged with tag 7(AGCN) -> AGCN, else Local
                    $update['CLI_FOREIGN_DIRECT_INV_CTY'] = 'CLI_FOREIGN_DIRECT_INV_CTY=""';                                                       //11: CLI_FOREIGN_DIRECT_INV_CTY
                    $update['CLI_PUBLIC_SECTOR']          = 'CLI_PUBLIC_SECTOR=""';                                                                //12: CLI_PUBLIC_SECTOR
                    $update['CLI_ACCOUNTEXECUTIVE_ID']    = 'CLI_ACCOUNTEXECUTIVE_ID=""';                                                          //13: CLI_ACCOUNTEXECUTIVE_ID
                    $update['CLI_ACCOUNT_HANDLER_ID']     = 'CLI_ACCOUNT_HANDLER_ID=""';                                                           //14: CLI_ACCOUNT_HANDLER_ID
                    $update['added']                      = 'added="' . $client['added'] . '"';                                                    //15: added
                    $update['modified']                   = 'modified="' . $client['modified'] . '"';                                              //16: modified
                    $insert = $update;
                    $insert['CLI_CLIENT_ID'] = 'CLI_CLIENT_ID="' . $client['id'] . '"';                                                            // 1: CLI_CLIENT_ID: customer ID
                    $query = 'INSERT INTO ' . $this->settings['export_table_clients'] . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . "\n" .
                              implode(', ', $update);
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $client['name'] . ' (' . $dbe->ErrorMsg() . ')';
                    }
                }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), count($clients), $this->i18n('lbl_clients'));
            }

            //EXPORT THE INSURERS
            if (empty($tables) || !in_array($this->settings['export_table_insurers'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_insurers']}` (
                          `INS_INSURER_ID` int(11) NOT NULL DEFAULT '0',
                          `INS_INSURER_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `INS_GROUP_ID` int(11) DEFAULT NULL,
                          `INS_GROUP_LEGAL_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `INS_GLOBAL_GROUP_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          PRIMARY KEY (`INS_INSURER_ID`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_insurers'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_insurers'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //select insurers to delete
                $query = 'SELECT c.id' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'WHERE (c.type="' . $this->settings['insurer_type'] . '" AND c.subtype="normal" OR subtype="branch") AND (c.active=0 AND c.modified>"' . $last_export . '" OR c.deleted>"' . $last_export . '")' . "\n" .
                         'ORDER BY c.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_insurers'] . "\n" .
                             'WHERE INS_INSURER_ID IN (' . implode(', ', $to_delete) . ') OR INS_GROUP_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_insurers'));
                    }
                }

                //get the name of the default main branch
                $this->registry['translater']->loadFile(PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini');
                $default_main_branch = $this->i18n('customers_central_office');

                //select the insurers (type = $this->settings['insurer_type'])
                $query = 'SELECT c.id, cb.id as branch_id, cbi.name as branch, TRIM(CONCAT(ci.name, " ", ci.lastname)) as name, ' . "\n" .
                         '       c.added, c.modified' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci ' . "\n" .
                         '  ON (c.id=ci.parent_id AND ci.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'JOIN ' . DB_TABLE_CUSTOMERS . ' as cb ' . "\n" .
                         '  ON (c.id=cb.parent_customer AND cb.subtype="branch" AND cb.is_main=1)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as cbi ' . "\n" .
                         '  ON (cb.id=cbi.parent_id AND cbi.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'WHERE c.type="' . $this->settings['insurer_type'] . '" AND c.subtype="normal" AND c.modified>"' . $last_export . '"' . "\n" .
                         '      AND c.active=1 AND c.deleted="0000-00-00 00:00:00" AND cb.active=1 AND cb.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY c.modified';
                $insurers = $this->registry['db']->GetAll($query);
                foreach($insurers as $insurer) {
                    //add branch to INS_INSURER_NAME only if it is not the default branch name
                    $branch = ($insurer['branch'] == $default_main_branch) ? '' : ' ' . $insurer['branch'];

                    $update = array();
                    $update['INS_INSURER_NAME']      = 'INS_INSURER_NAME="' . General::slashesEscape($insurer['name'] . $branch) . '"';                  // 2: INS_INSURER_NAME: main branch name
                    $update['INS_GROUP_ID']          = 'INS_GROUP_ID="' . $insurer['id'] . '"';                                                          // 3: INS_GROUP_ID: id of the customer
                    $update['INS_GROUP_LEGAL_NAME']  = 'INS_GROUP_LEGAL_NAME="' . General::slashesEscape($insurer['name']) . '"';                        // 4: INS_GROUP_LEGAL_NAME: name of the customer
                    $update['INS_GLOBAL_GROUP_NAME'] = 'INS_GLOBAL_GROUP_NAME=""';                                                                       // 5: INS_GLOBAL_GROUP_NAME
                    $update['added']                 = 'added="' . $insurer['added'] . '"';                                                              // 6: added
                    $update['modified']              = 'modified="' . $insurer['modified'] . '"';                                                        // 7: modified
                    $insert = $update;
                    $insert['INS_INSURER_ID'] = 'INS_INSURER_ID="' . $insurer['branch_id'] . '"';                                                        // 1: INS_INSURER_ID: id of the main branch of the insurer
                    $query = 'INSERT INTO ' . $this->settings['export_table_insurers'] . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . "\n" .
                              implode(', ', $update);
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $insurer['name'] . ' (' . $dbe->ErrorMsg() . ')';
                    }
                }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), count($insurers), $this->i18n('lbl_insurers'));
            }

            //EXPORT THE EMPLOYEES
            if (empty($tables) || !in_array($this->settings['export_table_users'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_users']}` (
                          `USR_USER_ID` int(11) NOT NULL DEFAULT '0',
                          `USR_USERNAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `USR_DEPARTMENT_ID` int(11) DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          PRIMARY KEY (`USR_USER_ID`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_users'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_users'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //select employees to delete
                $query = 'SELECT c.id' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'WHERE c.type="' . $this->settings['employee_type'] . '" AND c.subtype="normal" AND (c.active=0 AND c.modified>"' . $last_export . '" OR c.deleted>"' . $last_export . '")' . "\n" .
                         'ORDER BY c.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_users'] . "\n" .
                             'WHERE USR_USER_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_users'));
                    }
                }

                //select the employees (type = $this->settings['employee_type'])
                $query = 'SELECT c.id, TRIM(CONCAT(ci.name, " ", ci.lastname)) as name, u.default_department as department,' . "\n" .
                         '       c.added, c.modified' . "\n" .
                         'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci ' . "\n" .
                         '  ON (c.id=ci.parent_id AND ci.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS . ' as u ' . "\n" .
                         '  ON (c.id=u.employee)' . "\n" .
                         'WHERE c.type="' . $this->settings['employee_type'] . '" AND c.subtype="normal" AND c.modified>"' . $last_export . '"' . "\n" .
                         '      AND c.active=1 AND c.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY c.modified';
                $employees = $this->registry['db']->GetAll($query);
                foreach($employees as $employee) {
                    $update = array();
                    $update['USR_USERNAME']      = 'USR_USERNAME="' . General::slashesEscape($employee['name']) . '"'; // 2: USR_USERNAME
                    $update['USR_DEPARTMENT_ID'] = 'USR_DEPARTMENT_ID="' . $employee['department'] . '"';              // 3: USR_DEPARTMENT_ID: default_department of the corresponding user
                    $update['added']             = 'added="' . $employee['added'] . '"';                               // 4: added
                    $update['modified']          = 'modified="' . $employee['modified'] . '"';                         // 5: modified
                    $insert = $update;
                    $insert['USR_USER_ID'] = 'USR_USER_ID="' . $employee['id'] . '"';                                  // 1: USR_USER_ID: id of the employee
                    $query = 'INSERT INTO ' . $this->settings['export_table_users'] . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . "\n" .
                              implode(', ', $update);
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $employee['name'] . ' (' . $dbe->ErrorMsg() . ')';
                    }
                }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), count($employees), $this->i18n('lbl_users'));
            }

            //EXPORT THE DEPARTMENTS
            if (empty($tables) || !in_array($this->settings['export_table_departments'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_departments']}` (
                          `DEP_DEPARTMENT_ID` int(11) NOT NULL DEFAULT '0',
                          `DEP_DEPARTMENT_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_BRANCH_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_BRANCH_ID`  int(11) DEFAULT NULL,
                          `DEP_LOCATION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_COUNTRY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_REGION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_CLUSTER` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `DEP_HFM_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          PRIMARY KEY (`DEP_DEPARTMENT_ID`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_departments'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_departments'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //select departments to delete
                $query = 'SELECT d.id' . "\n" .
                         'FROM ' . DB_TABLE_DEPARTMENTS . ' as d ' . "\n" .
                         'WHERE d.active=0 AND d.modified>"' . $last_export . '" OR d.deleted>"' . $last_export . '"' . "\n" .
                         'ORDER BY d.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_departments'] . "\n" .
                             'WHERE DEP_DEPARTMENT_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_departments'));
                    }
                }

                //select the departments
                $query = 'SELECT d.id, di.name,' . "\n" .
                         '       d.added, d.modified' . "\n" .
                         'FROM ' . DB_TABLE_DEPARTMENTS . ' as d ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' as di ' . "\n" .
                         '  ON (d.id=di.parent_id AND di.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'WHERE d.id!="' . PH_DEPARTMENT_FIRST . '" AND d.modified>"' . $last_export . '"' . "\n" .
                         '      AND d.active=1 AND d.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY d.modified';
                $departments = $this->registry['db']->GetAll($query);
                foreach($departments as $department) {
                    $update = array();
                    $update['DEP_DEPARTMENT_NAME'] = 'DEP_DEPARTMENT_NAME="' . General::slashesEscape($department['name']) . '"';                        // 2: DEP_DEPARTMENT_NAME
                    $update['DEP_BRANCH_NAME']     = 'DEP_BRANCH_NAME="AON Sofia"';                                                                      // 3: DEP_BRANCH_NAME
                    $update['DEP_BRANCH_ID']       = 'DEP_BRANCH_ID="32"';                                                                               // 4: DEP_BRANCH_ID
                    $update['DEP_LOCATION']        = 'DEP_LOCATION="Sofia, 103, Alexander Stamboliyski blvd., Mall of Sofia – Sofia Tower, 4th floor "'; // 5: DEP_LOCATION
                    $update['DEP_COUNTRY']         = 'DEP_COUNTRY="Bulgaria"';                                                                           // 6: DEP_COUNTRY
                    $update['DEP_REGION']          = 'DEP_REGION="SEE"';                                                                                 // 7: DEP_REGION
                    $update['DEP_CLUSTER']         = 'DEP_CLUSTER="South Eastern Europe"';                                                               // 8: DEP_CLUSTER
                    $update['DEP_HFM_CODE']        = 'DEP_HFM_CODE=""';                                                                                  // 9: DEP_HFM_CODE
                    $update['added']               = 'added="' . $department['added'] . '"';                                                             //10: added
                    $update['modified']            = 'modified="' . $department['modified'] . '"';                                                       //11: modified
                    $insert = $update;
                    $insert['DEP_DEPARTMENT_ID']   = 'DEP_DEPARTMENT_ID="' . $department['id'] . '"';                                                    // 1: DEP_DEPARTMENT_ID
                    $query = 'INSERT INTO ' . $this->settings['export_table_departments'] . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . "\n" .
                              implode(', ', $update);
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $department['name'] . ' (' . $dbe->ErrorMsg() . ')';
                    }
                }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), count($departments), $this->i18n('lbl_departments'));
            }

            //EXPORT THE PRODUCTS
            if (empty($tables) || !in_array($this->settings['export_table_products'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_products']}` (
                          `PRD_PRODUCT_ID` int(11) NOT NULL DEFAULT '0',
                          `PRD_DESCRIPTION` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `PRD_GROUP_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `PRD_GLOBAL_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `PRD_CATEGORY_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `PRD_CATEGORY_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                          PRIMARY KEY (`PRD_PRODUCT_ID`)
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_products'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_products'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //get the nomenclature types
                $query = 'SELECT parent_id FROM ' . DB_TABLE_NOMENCLATURES_TYPES_I18N . "\n" .
                         'WHERE name REGEXP "[0-9]{2}\..*"';
                $nom_types = $this->registry['db']->GetCol($query);

                //select products to delete
                $query = 'SELECT n.id' . "\n" .
                         'FROM ' . DB_TABLE_NOMENCLATURES . ' as n ' . "\n" .
                         'WHERE n.type IN (' . implode(', ', $nom_types) . ') AND n.active=0 AND n.modified>"' . $last_export . '" OR n.deleted>"' . $last_export . '"' . "\n" .
                         'ORDER BY n.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_products'] . "\n" .
                             'WHERE PRD_PRODUCT_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_products'));
                    }
                }

                //select the nomenclatures
                $query = 'SELECT n.id, ni.name,' . "\n" .
                         '       n.added, n.modified' . "\n" .
                         'FROM ' . DB_TABLE_NOMENCLATURES . ' as n ' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni ' . "\n" .
                         '  ON (n.id=ni.parent_id AND ni.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'WHERE n.type IN (' . implode(', ', $nom_types) . ') AND n.modified>"' . $last_export . '"' . "\n" .
                         '      AND n.active=1 AND n.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY n.modified';
                $products = $this->registry['db']->GetAll($query);
                foreach($products as $product) {
                    $update = array();
                    $update['PRD_DESCRIPTION']   = 'PRD_DESCRIPTION="' . General::slashesEscape($product['name']) . '"'; // 2: PRD_DESCRIPTION
                    $update['PRD_GROUP_CODE']    = 'PRD_GROUP_CODE=""';                                                  // 3: PRD_GROUP_CODE
                    $update['PRD_GLOBAL_CODE']   = 'PRD_GLOBAL_CODE=""';                                                 // 4: PRD_GLOBAL_CODE
                    $update['PRD_CATEGORY_NAME'] = 'PRD_CATEGORY_NAME=""';                                               // 5: PRD_CATEGORY_NAME
                    $update['PRD_CATEGORY_CODE'] = 'PRD_CATEGORY_CODE=""';                                               // 6: PRD_CATEGORY_CODE
                    $update['added']             = 'added="' . $product['added'] . '"';                                  // 7: added
                    $update['modified']          = 'modified="' . $product['modified'] . '"';                            // 8: modified
                    $insert = $update;
                    $insert['PRD_PRODUCT_ID']   = 'PRD_PRODUCT_ID="' . $product['id'] . '"';                             // 1: PRD_PRODUCT_ID
                    $query = 'INSERT INTO ' . $this->settings['export_table_products'] . "\n" .
                              'SET ' . implode(', ', $insert) . "\n" .
                              'ON DUPLICATE KEY UPDATE ' . "\n" .
                              implode(', ', $update);
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $product['name'] . ' (' . $dbe->ErrorMsg() . ')';
                    }
                }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), count($products), $this->i18n('lbl_products'));
            }

            //EXPORT THE REVENUE
            if (empty($tables) || !in_array($this->settings['export_table_revenue'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_revenue']}` (
                          `REV_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_CLIENT_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_INSURER_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_DEPARTMENT_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_PRODUCT_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_ACCOUNTEXECUTIVE_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_BROKER_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_SUBBROKER_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_INTERNAL_SUBBROKER_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_ACCOUNT_HANDLER_ID` int(11) NOT NULL DEFAULT '0',
                          `REV_POLICYNUMBER` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_INVOICE_NUMBER` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_INSTALLMENT_NUMBER` int(11) NOT NULL DEFAULT '0',
                          `REV_PAYMENTDATE` date NOT NULL DEFAULT '0000-00-00',
                          `REV_DUEDATE` date NOT NULL DEFAULT '0000-00-00',
                          `REV_INVOICEDATE` date NOT NULL DEFAULT '0000-00-00',
                          `REV_INCOMEDATE` date NOT NULL DEFAULT '0000-00-00',
                          `REV_PAYMENT_AMOUNT` double(15,6) NOT NULL DEFAULT '0.000000',
                          `REV_BROKERAGE_RATE` double(15,6) NOT NULL DEFAULT '0.000000',
                          `REV_GROSS_PREMIUM` double(15,6) NOT NULL DEFAULT '0.000000',
                          `REV_GROSS_REVENUE` double(15,6) NOT NULL DEFAULT '0.000000',
                          `REV_SUBAGENT` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_DISCOUNT` double(15,6) NOT NULL DEFAULT '0.000000',
                          `REV_INCOMECLASS` int(11) NOT NULL DEFAULT '0',
                          `REV_INTERCOMPANY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_REVENUE_TYPE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_CURRENCY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_LINEOFBUSINESS_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_ENTITY_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_AGRC` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_SPECIALTY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_AFFINITY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_AFFINITY_DIST_CHANNEL` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_AFFINITY_SPONSOR_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `REV_AFFINITY_SPONSOR_TYPE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_revenue'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }
            if (empty($this->executionErrors)) {
                //get the last date of export
                $query = 'SELECT exported FROM ' . $this->settings['export_table_revenue'] . "\n" .
                         'ORDER BY exported DESC' . "\n" .
                         'LIMIT 1';
                $last_export = $dbe->GetOne($query);

                //select incomes to delete
                $query = 'SELECT d.id' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS . ' as d ' . "\n" .
                         'WHERE d.type="' . $this->settings['revenue_type'] . '" AND d.active=0 AND d.modified>"' . $last_export . '" OR d.deleted>"' . $last_export . '"' . "\n" .
                         'ORDER BY d.id';
                $to_delete = $this->registry['db']->GetCol($query);
                if (!empty($to_delete)) {
                    $query = 'DELETE FROM ' . $this->settings['export_table_revenue'] . "\n" .
                             'WHERE REV_ID IN (' . implode(', ', $to_delete) . ')';
                    if (!$dbe->Execute($query)) {
                        $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                    } else {
                        //used in the confirmation email
                        $this->executionWarnings[] = sprintf($this->i18n('msg_del_records'), $dbe->Affected_Rows(), $this->i18n('lbl_revenue'));
                    }
                }

                //select the documents
                $query = 'SELECT d.id, d.customer, d.branch, d.department, d.custom_num, d.date, d.added, d.modified' . "\n" .
                         'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_DOCUMENTS_I18N . ' as di ' . "\n" .
                         '  ON (d.id=di.parent_id AND di.lang="' . $this->registry['lang'] . '")' . "\n" .
                         'WHERE d.type="' . $this->settings['revenue_type'] . '" AND d.modified>"' . $last_export . '"' . "\n" .
                         '      AND d.active=1 AND d.deleted="0000-00-00 00:00:00"' . "\n" .
                         'ORDER BY d.modified';
                $incomes = $this->registry['db']->GetAll($query);

                $revenue_count = 0;
                foreach($incomes as $income) {
                    $query = 'SELECT gt2.article_barcode, gt2.article_id, gt2.free_field3, gt2.article_deliverer,' . "\n" .
                             '       gt2i.article_name, gt2.subtotal_with_discount, gt2.discount_percentage, gt2.subtotal, gt2.subtotal_discount_value' . "\n" .
                             'FROM ' . DB_TABLE_GT2_DETAILS . ' as gt2 ' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' as gt2i ' . "\n" .
                             '  ON (gt2.id=gt2i.parent_id AND gt2i.lang="' . $this->registry['lang'] . '")' . "\n" .
                             'WHERE gt2.article_id>0 AND gt2.model_id="' . $income['id'] . '" AND gt2.model="Document"' . "\n" .
                             'ORDER BY gt2.id';
                    $rows = $this->registry['db']->GetAll($query);
                    if (!empty($rows)) {
                        $query = 'DELETE FROM ' . $this->settings['export_table_revenue'] . "\n" .
                                 'WHERE REV_ID=' . $income['id'];
                        if (!$dbe->Execute($query)) {
                            $this->executionErrors[] = $this->i18n('error_del_records') . ' (' . $dbe->ErrorMsg() . ')';
                        }

                        //first delete all the rows from the revenue table
                        foreach($rows as $row) {
                            $date_of_payment = '';
                            if ($row['article_barcode']) {
                                $query = 'SELECT gi.free_text1' . "\n".
                                         'FROM ' . DB_TABLE_GT2_DETAILS_I18N . ' as gi' . "\n".
                                         'JOIN ' . DB_TABLE_GT2_DETAILS . ' as g' . "\n".
                                         '  ON gi.parent_id=g.id' . "\n".
                                         'JOIN ' . DB_TABLE_CONTRACTS . ' as co' . "\n".
                                         '  ON g.model_id=co.id AND g.model="Contract"' . "\n".
                                         'WHERE free_field5="' . $row['article_barcode'] . '" AND co.id=' . $row['article_id'];
                                $date_of_payment = $this->registry['db']->GetOne($query);
                            }

                            //get the business_type and client_type from the contract to define income_classification
                            $income_classification = '';
                            $query = 'SELECT coc.value' . "\n".
                                     'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' as coc' . "\n".
                                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n".
                                     '  ON fm.id=coc.var_id AND fm.model="Contract"' . "\n".
                                     'WHERE fm.name="business_type" AND coc.model_id=' . $row['article_id'];
                            $business_type = $this->registry['db']->GetOne($query);
                            $query = 'SELECT coc.value' . "\n".
                                     'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' as coc' . "\n".
                                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n".
                                     '  ON fm.id=coc.var_id AND fm.model="Contract"' . "\n".
                                     'WHERE fm.name="client_type" AND coc.model_id=' . $row['article_id'];
                            $client_type = $this->registry['db']->GetOne($query);
                            if ($client_type == 'renewal_client') {
                                $income_classification = 1;
                            } elseif ($client_type == 'new_existing' && $business_type == 'recurring') {
                                $income_classification = 2;
                            } elseif ($client_type == 'new_existing' && $business_type == 'non_recurring') {
                                $income_classification = 5;
                            } elseif ($client_type == 'new_new' && $business_type == 'recurring') {
                                $income_classification = 3;
                            } elseif ($client_type == 'new_new' && $business_type == 'non_recurring') {
                                $income_classification = 4;
                            }

                            //get the producing_office from the contract to define intercompany_revenue
                            $intercompany_revenue = 'N';
                            $query = 'SELECT coc.value' . "\n".
                                     'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' as coc' . "\n".
                                     'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n".
                                     '  ON fm.id=coc.var_id AND fm.model="Contract"' . "\n".
                                     'WHERE fm.name="producing_office" AND coc.model_id=' . $row['article_id'];
                            $producing_office = $this->registry['db']->GetOne($query);
                            //Освен вече описаното условие, имаме тип договор 7 - Fee. Ако при него името на контрагента съдържа "AON " (важна е паузата след aon) тогава пак да се брои като Intecompany
                            $query = 'SELECT ci.name' . "\n".
                                     'FROM ' . DB_TABLE_CONTRACTS . ' as co' . "\n".
                                     'JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci' . "\n".
                                     '  ON co.customer=ci.parent_id AND ci.lang="' . $this->registry['lang'] . '"' . "\n".
                                     'WHERE co.type=' . $this->settings['contract_type_fee'] . ' AND ci.name LIKE "AON %" AND co.id=' . $row['article_id'];
                            $own_contract = $this->registry['db']->GetOne($query);
                            if ($producing_office || $own_contract) {
                                $intercompany_revenue = 'Y';
                            }

                            //revenue_type default is Commission, for all Fee contracts (Type: 7) it is Fee
                            $revenue_type = 'Commission';
                            $query = 'SELECT co.type' . "\n".
                                     'FROM ' . DB_TABLE_CONTRACTS . ' as co' . "\n".
                                     'WHERE co.id=' . $row['article_id'];
                            $contract_type = $this->registry['db']->GetOne($query);
                            if ($contract_type == $this->settings['contract_type_fee']) {
                                $revenue_type = 'Fee';
                            }

                            $insert = array();
                            $insert['REV_ID']                    = 'REV_ID="' . $income['id'] . '"';                              // 1: REV_ID
                            $insert['REV_CLIENT_ID']             = 'REV_CLIENT_ID="' . $income['customer'] . '"';                 // 2: REV_CLIENT_ID
                            $insert['REV_INSURER_ID']            = 'REV_INSURER_ID="' . $income['branch'] . '"';                  // 3: REV_INSURER_ID: branch id
                            $insert['REV_DEPARTMENT_ID']         = 'REV_DEPARTMENT_ID="' . $income['department'] . '"';           // 4: REV_DEPARTMENT_ID: department id
                            $insert['REV_PRODUCT_ID']            = 'REV_PRODUCT_ID="' . $row['free_field3'] . '"';                // 5: REV_PRODUCT_ID
                            $insert['REV_ACCOUNTEXECUTIVE_ID']   = 'REV_ACCOUNTEXECUTIVE_ID="' . $row['article_deliverer'] . '"'; // 6: REV_ACCOUNTEXECUTIVE_ID
                            $insert['REV_BROKER_ID']             = 'REV_BROKER_ID=""';                                            // 7: REV_BROKER_ID
                            $insert['REV_SUBBROKER_ID']          = 'REV_SUBBROKER_ID=""';                                         // 8: REV_SUBBROKER_ID
                            $insert['REV_INTERNAL_SUBBROKER_ID'] = 'REV_INTERNAL_SUBBROKER_ID=""';                                // 9: REV_INTERNAL_SUBBROKER_ID
                            $insert['REV_ACCOUNT_HANDLER_ID']    = 'REV_ACCOUNT_HANDLER_ID="' . $row['article_deliverer'] . '"';  //10: REV_ACCOUNT_HANDLER_ID
                            $insert['REV_POLICYNUMBER']          = 'REV_POLICYNUMBER="' . $row['article_name'] . '"';             //11: REV_POLICYNUMBER
                            $insert['REV_INVOICE_NUMBER']        = 'REV_INVOICE_NUMBER="' . $income['custom_num'] . '"';          //12: REV_INVOICE_NUMBER
                            $insert['REV_INSTALLMENT_NUMBER']    = 'REV_INSTALLMENT_NUMBER="' . $row['article_barcode'] . '"';    //13: REV_INSTALLMENT_NUMBER
                            $insert['REV_PAYMENTDATE']           = 'REV_PAYMENTDATE=""';                                          //14: REV_PAYMENTDATE
                            $insert['REV_DUEDATE']               = 'REV_DUEDATE="' . $date_of_payment . '"';                      //15: REV_DUEDATE
                            $insert['REV_INVOICEDATE']           = 'REV_INVOICEDATE="' . $income['date'] . '"';                   //16: REV_INVOICEDATE
                            $insert['REV_INCOMEDATE']            = 'REV_INCOMEDATE="' . $income['date'] . '"';                    //17: REV_INCOMEDATE
                            $insert['REV_PAYMENT_AMOUNT']        = 'REV_PAYMENT_AMOUNT="' . $row['subtotal_with_discount'] . '"'; //18: REV_PAYMENT_AMOUNT: subtotal_with_discount
                            $insert['REV_BROKERAGE_RATE']        = 'REV_BROKERAGE_RATE="' . $row['discount_percentage'] . '"';    //19: REV_BROKERAGE_RATE
                            $insert['REV_GROSS_PREMIUM']         = 'REV_GROSS_PREMIUM="' . $row['subtotal'] . '"';                //20: REV_GROSS_PREMIUM
                            $insert['REV_GROSS_REVENUE']         = 'REV_GROSS_REVENUE="' . $row['subtotal_discount_value'] . '"'; //21: REV_GROSS_REVENUE
                            $insert['REV_SUBAGENT']              = 'REV_SUBAGENT=""';                                             //22: REV_SUBAGENT
                            $insert['REV_DISCOUNT']              = 'REV_DISCOUNT=""';                                             //23: REV_DISCOUNT
                            $insert['REV_INCOMECLASS']           = 'REV_INCOMECLASS="' . $income_classification . '"';            //24: REV_INCOMECLASS
                            $insert['REV_INTERCOMPANY']          = 'REV_INTERCOMPANY="' . $intercompany_revenue . '"';            //25: REV_INTERCOMPANY
                            $insert['REV_REVENUE_TYPE']          = 'REV_REVENUE_TYPE="' . $revenue_type . '"';                    //26: REV_REVENUE_TYPE: Commission or Fee
                            $insert['REV_CURRENCY']              = 'REV_CURRENCY="BGN"';                                          //27: REV_CURRENCY
                            $insert['REV_LINEOFBUSINESS_CODE']   = 'REV_LINEOFBUSINESS_CODE=""';                                  //28: REV_LINEOFBUSINESS_CODE
                            $insert['REV_ENTITY_NAME']           = 'REV_ENTITY_NAME="АОН България ЕООД"';                         //29: REV_ENTITY_NAME: "АОН България ЕООД" (own company full name)
                            $insert['REV_AGRC']                  = 'REV_AGRC=""';                                                 //30: REV_AGRC
                            $insert['REV_SPECIALTY']             = 'REV_SPECIALTY=""';                                            //31: REV_SPECIALTY
                            $insert['REV_AFFINITY']              = 'REV_AFFINITY=""';                                             //32: REV_AFFINITY
                            $insert['REV_AFFINITY_DIST_CHANNEL'] = 'REV_AFFINITY_DIST_CHANNEL=""';                                //33: REV_AFFINITY_DIST_CHANNEL
                            $insert['REV_AFFINITY_SPONSOR_NAME'] = 'REV_AFFINITY_SPONSOR_NAME=""';                                //34: REV_AFFINITY_SPONSOR_NAME
                            $insert['REV_AFFINITY_SPONSOR_TYPE'] = 'REV_AFFINITY_SPONSOR_TYPE=""';                                //35: REV_AFFINITY_SPONSOR_TYPE
                            $insert['added']                     = 'added="' . $income['added'] . '"';                            //36: added
                            $insert['modified']                  = 'modified="' . $income['modified'] . '"';                      //37: modified
                            $query = 'INSERT INTO ' . $this->settings['export_table_revenue'] . "\n" .
                                      'SET ' . implode(', ', $insert);
                            if (!$dbe->Execute($query)) {
                                $this->executionErrors[] = $this->i18n('error_sync_record') . ' ' . $income['id'] . ' (' . $dbe->ErrorMsg() . ')';
                            }

                        }
                        $revenue_count++;
                    }
               }
                //used in the confirmation email
                $this->executionWarnings[] = sprintf($this->i18n('msg_sync_records'), $revenue_count, $this->i18n('lbl_revenue'));
            }

            //EXPORT THE INDUSTRY (empty table for now)
            //ToDo: Define what to export
            if (empty($tables) || !in_array($this->settings['export_table_industry'], $tables)) {
                //create the DB table first
                $query = "CREATE TABLE IF NOT EXISTS `{$this->settings['export_table_industry']}` (
                          `IND_NAME` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `IND_INDUSTRY_CODE` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `IND_SPECIALTY` varchar(255) COLLATE utf8mb4_unicode_ci DEFAULT NULL,
                          `added` datetime DEFAULT NULL,
                          `modified` datetime DEFAULT NULL,
                          `exported` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
                        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;";
                if (!$dbe->Execute($query)) {
                    $this->executionErrors[] = $this->i18n('error_creating_table') . ' ' . $this->settings['export_table_industry'] . '(' . $dbe->ErrorMsg() . ')';
                }
            }

            $result = !$dbe->HasFailedTrans();
            $dbe->CompleteTrans();
        }

        if ($this->executionErrors) {
            $result = false;
        }

        //save history, so that the automation is executed only once a day
        $this->updateAutomationHistory($params, '', $result);

        return $result;
    }

    /**
     * Automation to translate models automatically
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function autoTranslate(array $params) {
        $model = &$params['model'];
        $model->unsanitize();

        //check if the model has been translated
        $translations = $model->getTranslations();
        $langs = preg_split('#\s*,\s*#', $this->registry['config']->getParam('i18n', 'model_langs'));

        sort($translations);
        sort($langs);
        $translate_to = array_diff($langs, $translations);
        if (empty($translate_to)) {
            //nothing to translate to
            return true;
        }

        $original_model_lang = $model->get('model_lang');

        //construct the factory name
        $factory_name = General::singular2plural($model->modelName);

        //get the alias
        $alias = $factory_name::getAlias($this->registry['module'], $this->registry['controller']);

        //compose the filters
        $filters = array('where' => array($alias . '.id = ' . $model->get('id')),
                         'model_lang' => $model->get('model_lang'));

        //get the model from the db
        $model = $factory_name::searchOne($this->registry, $filters);
        $gov = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $model->getVars();
        $this->registry->set('get_old_vars', $gov, true);

        //old model is needed for history and audit
        $old_model = clone $model;

        //change the action to 'translate' because some of the validate methods use this as a flag
        // IMPORTANT: when action is "translate" multilang additional vars are saved in current model lang
        // for all models that have additional variables
        $action = $this->registry['action'];
        $this->registry->set('action', 'translate', true);
        // flag to use set GT2 values, not to auto-translate
        $model->set('translation_values_are_set', true, true);
        // we need to be able to translate readonly layouts
        $this->registry->set('edit_all', true, true);

        foreach ($translate_to as $lang) {
            $model->set('model_lang', $lang, true);
            if ($model->save()) {
                //show corresponding message
                $message_parm = 'message_' . $this->registry['module'] . '_';
                $message_parm .= ($this->registry['module'] == $this->registry['controller']) ? '' : $this->registry['controller'] . '_';
                $message_parm .= 'translate_success';
                $this->registry['messages']->setMessage($this->i18n($message_parm, array($old_model->getModelTypeName())), '', 100);
                $filters = array('where' => array($alias . '.id = ' . $model->get('id')),
                                 'model_lang' => $lang);
                $new_model = $factory_name::searchOne($this->registry, $filters);
                //save history
                $history_class = $factory_name . '_History';
                $history_class::saveData($this->registry, array('model' => $model, 'action_type' => 'translate', 'new_model' => $new_model, 'old_model' => $old_model));
            } else {
                $this->registry['messages']->setError($this->i18n('error_' . $this->registry['module'] . '_translate_failed', array($old_model->getModelTypeName())), '', 100);
            }
        }
        $this->registry['messages']->insertInSession($this->registry);

        //restore the previous action
        $this->registry->set('action', $action, true);
        $this->registry->remove('edit_all');

        //restore the original model lang
        $model->set('model_lang', $original_model_lang, true);
    }

    /**
     * Make assignments in function of document status
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function manageVacationsAssignments($params) {

        // Get the current action
        $action = $this->registry['action'];
        // these were moved to the conditions field in the automation table
        /*
        if ($action != 'setstatus' || !$this->registry['request']->isPost()) {
            return true;
        }*/

        $request = &$this->registry['request'];
        $substatus='';
        if ($action == 'setstatus') {
            if ($request->get('substatus')) {
                $substatus = intval(preg_replace('#^.*_(\d+)$#', '$1', $request->get('substatus')));
            }
            if ($request->get('status') == $request->get('current_status_base')) {
                //check if we have some changes
                if ($substatus == intval($request->get('current_substatus_base'))) {
                    //nothing has been changed
                    return true;
                }
            }
        } else {
            $substatus = 'SELECT substatus FROM ' . DB_TABLE_DOCUMENTS . ' WHERE id = ' . $params['model']->get('id');
            $substatus = $this->registry['db']->GetOne($substatus);
        }

        //get current employee and its settings
        $query = 'SELECT customer FROM ' . DB_TABLE_DOCUMENTS . ' WHERE id = ' . $params['model']->get('id');
        $employee = $this->registry['db']->GetOne($query);
        if (!$employee) {
            return true;
        }
        require_once PH_MODULES_DIR .'customers/models/customers.factory.php';
        $employee = Customers::searchOne($this->registry, array('where' => array('c.id = ' . $employee)));
        $this->registry->set('get_old_vars', true, true);
        $employee->getAssocVars();
        $this->registry->set('get_old_vars', false, true);
        $vars = $employee->get('assoc_vars');
        unset($employee);
        $next_assigned = false;
        if (isset($this->settings['substatus_for_manager']) && $substatus == $this->settings['substatus_for_manager']) {
            if (!empty($vars['supervisor_id']['value'])) {
                $next_assigned = $vars['supervisor_id']['value'];
            } elseif (!empty($vars['accountant_id']['value'])) {
                $next_assigned = $vars['accountant_id']['value'];
            }
        } elseif (isset($this->settings['substatus_for_accountant']) && $substatus == $this->settings['substatus_for_accountant']) {
            if (!empty($vars['accountant_id']['value'])) {
                $next_assigned = $vars['accountant_id']['value'];
            }
        } elseif (isset($this->settings['substatus_for_ceo']) && $substatus == $this->settings['substatus_for_ceo']) {
            if (!empty($vars['final_approval_id']['value'])) {
                $next_assigned = $vars['final_approval_id']['value'];
            }
        } else {
            //nothing to do
            return true;
        }

        if (empty($next_assigned)) {
            $this->registry['messages']->setError($this->i18n('plugin_not_assignment_possible'));
            return false;
        }
        $this->registry['db']->StartTrans();
        //we will do the things with documents controller's help
        $post = $request->getAll('post');
        //clear post
        foreach ($post as $k => $v) {
            $request->remove($k);
        }
        require_once PH_MODULES_DIR . 'documents/controllers/documents.controller.php';
        $ctrl = new Documents_Controller($this->registry);
        $ctrl->action = 'assign';
        $request->set('documents', 'assign', 'all', true);
        $request->set('assign', $params['model']->get('id'), 'all', true);
        $request->set('a_type', 'owner', 'all', true);
        $request->set('assignments_owner', array($next_assigned), true);
        $assign_settings = $this->registry['config']->getParamAsArray('documents', 'assignment_types_' . $params['model']->get('type'));
        $assign_settings[] = 'owner';
        $this->registry['config']->_config_data['db_settings']['sections']['documents']['vars'][ 'assignment_types_' . $params['model']->get('type')] = implode(',', $assign_settings);
        $ctrl->execute();
        // clean redirection prepared from the controller
        $this->registry->remove('redirect_to_url');
        $this->registry->remove('exit_after');
        $request->remove('documents');
        $request->remove('assign');
        $request->remove('a_type');
        $request->remove('assignments_owner');
        foreach ($post as $k => $v) {
            $request->set($k, $v, 'post', true);
        }
        array_pop($assign_settings);
        $this->registry['config']->_config_data['db_settings']['sections']['documents']['vars'][ 'assignment_types_' . $params['model']->get('type')] = implode(',', $assign_settings);
        if ($this->registry['messages']->getErrors()) {
            $this->registry['db']->FailTrans();
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Create claims list for Health insurance (document type 4)
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function createClaimList($params) {

        // Get the current action
        if (!in_array($this->registry['action'], array('add', 'edit')) || !$this->registry['request']->isPost()) {
            return true;
        }

        $concat_var = $this->settings['concat_var'];
        $vars = $params['model']->getAssocVars();
        $concatenated = array();
        if (!empty($vars[$concat_var])) {
            $var = $vars[$concat_var];
            if ($var['type'] == 'dropdown') {
                foreach($var['value'] as $value) {
                    foreach($var['options'] as $option) {
                        if ($value == $option['option_value']) {
                            $concatenated[] = $option['label'];
                            break;
                        }
                    }
                }
            } else {
                $concatenated = $var['value'];
            }
        }

        if (!empty($this->settings['make_unique']) && !empty($concatenated)) {
            $concatenated = array_unique($concatenated);
        }

        if (!empty($this->settings['sort']) && !empty($concatenated)) {
            sort($concatenated);
        }
        $glue = $this->settings['concat_glue'];
        if (empty($glue)) {
            $glue = '+';
        }
        $concatenated = implode(' ' . $this->settings['concat_glue'] . ' ', $concatenated);

        $this->registry['request']->set($this->settings['update_var'], $concatenated, 'all', true);

        return true;
    }

    /**
     * Calculates deadline of Health insurance (document type 4)
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function calculateHealthInsuranceDeadline($params) {
        // Get the current action
        if (!in_array($this->registry['action'], array('edit', 'multiedit')) || !$this->registry['request']->isPost()) {
            return true;
        }

        $this->registry->set('get_old_vars', true, true);

        if ($params['model']->isSanitized()) {
            $params['model']->unsanitize();
        }

        //get the label of the variable to set warning/success message
        $layout = $params['model']->getLayoutsDetails($this->settings['update_var']);
        $doc_type_name = $params['model']->get('name');

        $date = $params['model']->get($this->settings['date_insurer_var']);
        $type_days = $params['model']->getPlainVarValue($this->settings['type_days_var']);
        $period = $params['model']->getPlainVarValue($this->settings['period_var']);

        if (!in_array($type_days, array(1, 2)) || $period == '') {
            if ($this->registry['action'] == 'multiedit') {
                $this->registry['messages']->setWarning(sprintf($this->i18n('warning_set_deadline3'), $doc_type_name, $params['model']->get('full_num'), $layout['name']));
            } else {
                $this->registry['messages']->setWarning(sprintf($this->i18n('warning_set_deadline4'), $layout['name']));
            }
            $this->registry['messages']->insertInSession($this->registry);
            return true;
        }

        if ($type_days == 1) {
            $deadline = General::strftime('%Y-%m-%d', strtotime(sprintf('+ %s day', $period), strtotime($date)));
        } elseif ($type_days == 2) {
            include_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $deadline = Calendars_Calendar::calcDateOnWorkingDays($this->registry, $date, $period);
        }

        // get old model for audit of edit action
        $old_doc = clone $params['model'];
        $old_doc->sanitize();

        $params['model']->set($this->settings['update_var'], $deadline, true);

        // Start a transaction
        $this->registry['db']->StartTrans();

        // Try to save the document vars
        if ($params['model']->save()) {
            // If the vars are saved, then save history for the document
            $filters = array('where' => array('d.id = ' . $params['model']->get('id')),
                             'model_lang' => $params['model']->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_doc = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_doc->getVars();
            $audit_parent = Documents_History::saveData($this->registry,
                                                        array('model'       => $new_doc,
                                                              'action_type' => 'edit',
                                                              'new_model'   => $new_doc,
                                                              'old_model'   => $old_doc));
        }

        if ($this->registry['db']->HasFailedTrans()) {
            if ($this->registry['action'] == 'multiedit') {
                $this->registry['messages']->setWarning(sprintf($this->i18n('warning_set_deadline1'), $doc_type_name, $params['model']->get('full_num'), $layout['name']));
            } else {
                $this->registry['messages']->setWarning(sprintf($this->i18n('warning_set_deadline2'), $layout['name']));
            }
        } else {
            if ($this->registry['action'] == 'multiedit') {
                $this->registry['messages']->setMessage(sprintf($this->i18n('message_set_deadline1'), $doc_type_name, $params['model']->get('full_num'), $layout['name'], General::strftime('%d.%m.%Y', strtotime($deadline))));
            } else {
                $this->registry['messages']->setMessage(sprintf($this->i18n('message_set_deadline2'), $layout['name'], General::strftime('%d.%m.%Y', strtotime($deadline))));
            }
        }

        $this->registry['messages']->insertInSession($this->registry);

        //complete the transaction
        $this->registry['db']->CompleteTrans();

        return true;
    }

    /**
     * Automation to validate the commission data to match the data in the payment table
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function validateInsuranceCommissionSum($params) {
        if (!empty($this->settings['automation_id'])) {
            $sql = 'SELECT `parent_id` FROM ' . DB_TABLE_AUTOMATIONS_HISTORY . ' WHERE model_id="' . $params['model']->get('id') . '" AND parent_id="' . $this->settings['automation_id'] . '"';
            $executed_automation = $this->registry['db']->GetOne($sql);
        } else {
            $executed_automation = true;
        }

        if (!$executed_automation || !in_array($this->registry['action'], array('edittopic')) || !$this->registry['request']->isPost()) {
            return true;
        }

        $request = $this->registry['request'];
        $premium_value = $request->get($this->settings['premium_value']);
        $count_installments = $request->get($this->settings['number_rows']);
        $installments_currency = $request->get($this->settings['rows_currency']);

        $model = clone($params['model']);
        $this->registry->set('get_old_vars', true, true);
        $gt2 = $model->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        $table_rows = 0;

        foreach ($gt2['values'] as $row_values) {
            if ($row_values[$this->settings['shared_commission_field']] == $this->settings['standard_commission_type']) {
                $table_rows++;
            }
        }

        $table_value = 0;
        $currencies = array();
        foreach ($gt2['values'] as $row_id => $row_values) {
            if ($row_values[$this->settings['shared_commission_field']] == $this->settings['standard_commission_type']) {
                $table_value += $row_values[$this->settings['table_commission_value']];
                $currencies[] = $row_values[$this->settings['table_currency']];
            }
        }
        $error = false;

        // calculate accptable difference
        $calculated_value = $premium_value / $count_installments;
        $rounded_calculated_value = (round($calculated_value, 2) * $count_installments);
        $acceptable_difference = round(abs($premium_value-$rounded_calculated_value), 6);
        $difference_in_values = round(abs($table_value - $premium_value), 6);

        if ($difference_in_values>$acceptable_difference) {
            $this->registry['messages']->setError($this->i18n('error_sums_commission_bgn_does_not_match'));
            $error = true;
        }
        if ($count_installments != $table_rows) {
            $this->registry['messages']->setError($this->i18n('error_number_installments_use_dashlet', array($table_rows, $count_installments)));
            $error = true;
        }

        $currencies = array_unique($currencies);
        if (count($currencies) != 1) {
            $this->registry['messages']->setError($this->i18n('error_more_than_one_currency_in_the_deadline_table'));
            $error = true;
        } else {
            $table_currency = reset($currencies);
            if ($table_currency != $installments_currency) {
                $this->registry['messages']->setError($this->i18n('error_currency_does_not_match'));
                $error = true;
            }
        }

        return !$error;
    }

    /**
     * Automation to update the left paid sum of a debit note
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function updateDebitNotePaidAmount($params) {
        $this->registry->set('get_old_vars', true, true);
        $gt2_vars = $params['model']->getGT2Vars();
        $this->registry->set('get_old_vars', false, true);

        $debit_notes_currently_paid_sum = array();
        $debit_notes_total_paid = array();

        foreach ($gt2_vars['values'] as $row_id => $values) {
            if (!isset($debit_notes_currently_paid_sum[$values[$this->settings['debit_note_payment_id']]])) {
                $debit_notes_currently_paid_sum[$values[$this->settings['debit_note_payment_id']]] = 0;
            }
            $debit_notes_currently_paid_sum[$values[$this->settings['debit_note_payment_id']]] += $values[$this->settings['debit_note_payment_value']];
        }

        $debit_notes = array();
        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

        if (!empty($debit_notes_currently_paid_sum)) {
            // take the related debit notes
            $filters = array('where' => array('d.id IN (' . implode(',', array_keys($debit_notes_currently_paid_sum)) . ')'),
                             'model_lang' => $params['model']->get('model_lang'),
                             'skip_permissions_check' => true);
            $debit_notes = Documents::search($this->registry, $filters);
        }

        $this->registry['db']->StartTrans();
        foreach ($debit_notes as $debit_note) {
            $this->registry->set('get_old_vars', true, true);
            $debit_note->getVars();
            $this->registry->set('get_old_vars', false, true);

            $old_debit_note = clone $debit_note;
            $assoc_vars = $debit_note->getAssocVars();

            $added_sum = $debit_notes_currently_paid_sum[$debit_note->get('id')];
            $updated_sum = sprintf('%.2f', ($assoc_vars[$this->settings['debit_note_paid_value']]['value'] + $added_sum));
            $debit_notes_total_paid[$debit_note->get('id')] = $updated_sum;

            // create update queries
            $update = $insert = array();
            $insert[] = $update[] = 'value = "' . $updated_sum . '"';
            $insert[] = $update[] = 'modified = NOW()';
            $insert[] = $update[] = 'modified_by = ' . $this->registry['currentUser']->get('id');

            $insert[] = 'model_id = ' . $debit_note->get('id');
            $insert[] = 'var_id = ' . $assoc_vars[$this->settings['debit_note_paid_value']]['id'];
            $insert[] = 'added = NOW()';
            $insert[] = 'added_by = ' . $this->registry['currentUser']->get('id');
            $insert[] = 'num = 1';
            $insert[] = 'lang = ""';

            $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' SET ' . implode(', ', $insert) . "\n" .
                     'ON DUPLICATE KEY UPDATE ' . implode(', ', $update);
            $this->registry['db']->Execute($query);

            $filters = array('where' => array('d.id=' . $debit_note->get('id')),
                             'model_lang' => $debit_note->get('model_lang'),
                             'skip_permissions_check' => true);
            $new_debit_note = Documents::searchOne($this->registry, $filters);

            $this->registry->set('get_old_vars', true, true);
            $new_debit_note->getVars();
            $this->registry->set('get_old_vars', false, true);

            Documents_History::saveData($this->registry,
                                        array('model'       => $new_debit_note,
                                              'action_type' => 'edit',
                                              'new_model'   => $new_debit_note,
                                              'old_model'   => $old_debit_note));
        }

        if (!empty($debit_notes_total_paid)) {
            // get all the unfinished debit notes payments where these debit notes are included
            $sql = 'SELECT d.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                   ' ON (gt2.model="Document" AND gt2.model_id=d.id AND gt2.article_id IN ("' . implode('","', array_keys($debit_notes_total_paid)) . '"))' . "\n" .
                   'WHERE d.type="' . $params['start_model_type'] . '" AND d.status!="closed" AND d.active=1 AND d.deleted_by=0'  . "\n";
            $related_debit_note_payments = $this->registry['db']->GetCol($sql);

            if (!empty($related_debit_note_payments)) {
                $filters = array('where' => array('d.id IN (' . implode(',', $related_debit_note_payments) . ')'),
                                 'model_lang' => $params['model']->get('model_lang'),
                                 'skip_permissions_check' => true);
                $debit_notes_payments = Documents::search($this->registry, $filters);

                foreach ($debit_notes_payments as $dnp) {
                    $this->registry->set('get_old_vars', true, true);
                    $gt2_var = $dnp->getGT2Vars();
                    $old_dnp = clone $dnp;

                    foreach ($gt2_var['values'] as $row_num => $row_values) {
                        if (array_key_exists($row_values[$this->settings['debit_note_payment_id']], $debit_notes_total_paid)) {
                            $gt2_var['values'][$row_num][$this->settings['debit_note_payment_currently_paid']] = $debit_notes_total_paid[$row_values[$this->settings['debit_note_payment_id']]];
                        }
                    }

                    $dnp->set('grouping_table_2', $gt2_var, true);

                    $dnp->calculateGT2();
                    $dnp->set('table_values_are_set', true, true);
                    if (!$dnp->saveGT2Vars()) {
                        $this->registry['db']->FailTrans();
                    }

                    $filters = array('where' => array('d.id=' . $dnp->get('id')),
                                     'model_lang' => $dnp->get('model_lang'),
                                     'skip_permissions_check' => true);
                    $new_dnp = Documents::searchOne($this->registry, $filters);

                    $this->registry->set('get_old_vars', true, true);
                    $dnp->getVars();
                    $this->registry->set('get_old_vars', false, true);
                    $this->registry->set('get_old_vars', false, true);

                    Documents_History::saveData($this->registry,
                                                array('model'       => $new_dnp,
                                                      'action_type' => 'edit',
                                                      'new_model'   => $new_dnp,
                                                      'old_model'   => $old_dnp));
                }
            }
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Automation to validate the annulment of debit note payment
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function validateDebitNotePaymentAnnulment($params) {
        $request = $this->registry['request'];

        $current_status = sprintf('%s_%d', $params['model']->get('status'), $params['model']->get('substatus'));

        if ($request->get('substatus') != $this->settings['check_status'] || $current_status == $request->get('substatus')) {
            return true;
        }

        // check the tags
        $errors = array();
        $params['model']->getTags();
        if (in_array($this->settings['tag_reported_payment'], $params['model']->get('tags'))) {
            $errors[] = $this->i18n('error_debit_note_payment_already_reported');
        }

        foreach ($errors as $error) {
            $this->registry['messages']->setError($error);
        }

        return empty($errors);
    }

    /**
     * Automation to validate if a debit note can be annulled
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function checkDebitNoteAnullment($params) {
        // get the new status
        $current_status = $this->registry['request']->get('substatus');
        $result = true;

        if ($current_status == $this->settings['check_status']) {
            $sql = 'SELECT COUNT(gt2.id)' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' as gt2' . "\n" .
                   '  ON (gt2.model="Document" AND gt2.model_id=d.id AND gt2.article_id="' . $params['model']->get('id') . '")' . "\n" .
                   'WHERE d.type="' . $this->settings['payment_type'] . '" AND d.active=1 AND d.deleted_by=0 AND d.status="closed"' . "\n";
            $finished_payments = $this->registry['db']->GetOne($sql);

            if ($finished_payments) {
                $this->registry['messages']->setError($this->i18n('error_debit_note_related_with_unfinished_payments'));
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Automation to clear all the data related to the debit note that is annulled
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function clearAnnulledDebitNoteRelations($params) {
        $this->registry['db']->StartTrans();
        // get the related payments
        $sql = 'SELECT gt2.model_id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' as d' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' as gt2' . "\n" .
               '  ON (gt2.model="Document" AND gt2.model_id=d.id AND gt2.article_id="' . $params['model']->get('id') . '")' . "\n" .
               'WHERE d.type="' . $this->settings['payment_type'] . '" AND d.active=1 AND d.deleted_by=0' . "\n";
        $payments = $this->registry['db']->GetCol($sql);

        if (!empty($payments)) {
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            $payments = array_unique($payments);

            // take the related debit note
            $filters = array('where'                  => array('d.id IN ("' . implode('","', $payments) . '")'),
                             'model_lang'             => $params['model']->get('model_lang'),
                             'skip_permissions_check' => true);
            $payments_list = Documents::search($this->registry, $filters);

            foreach ($payments_list as $payment) {
                $this->registry->set('get_old_vars', true, true);
                $payment->getVars();
                $this->registry->set('get_old_vars', false, true);
                $old_model = clone $payment;

                // get all the vars
                $vars_list = $payment->get('vars');
                $gt2_vars = '';
                $gt2_var_idx = '';

                // find the GT2 var
                foreach ($vars_list as $var_idx => $vl) {
                    if ($vl['type'] == 'gt2') {
                        $gt2_vars = $vl;
                        $gt2_var_idx = $var_idx;
                    }
                }

                // delete all the rows for the annulled debit note
                if (!empty($gt2_vars['values'])) {
                    foreach ($gt2_vars['values'] as $row_idx => $row_values) {
                        if ($row_values['article_id'] == $params['model']->get('id')) {
                            $gt2_vars['values'][$row_idx]['deleted'] = 1;
                        }
                    }
                }

                // set the new values in the model
                $payment->set('table_values_are_set', true, true);
                $payment->set('grouping_table_2', $gt2_vars, true);
                $payment->unsanitize();
                $payment->saveGT2Vars($gt2_vars);

                $this->registry->set('get_old_vars', true, true);
                $gt2_vars = $payment->getGT2Vars();
                $this->registry->set('get_old_vars', false, true);

                $vars_list[$gt2_var_idx] = $gt2_vars;
                $payment->set('grouping_table_2', 1, true);
                $payment->set('vars', $vars_list, true);

                // saves history
                Documents_History::saveData($this->registry, array('model' => $payment, 'action_type' => 'edit', 'new_model' => $payment, 'old_model' => $old_model));
            }
        }

        // get the policies to be changed
        $sql = 'SELECT gt2.free_field2, gt2.article_id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
               '  ON (gt2.model="Document" AND gt2.model_id=d.id AND gt2.article_id!="" AND gt2.article_id IS NOT NULL)' . "\n" .
               'WHERE d.id="' . $params['model']->get('id') . '"' . "\n";
        $policies = $this->registry['db']->GetAssoc($sql);

        if ($policies) {
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
            require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';

            // take the related debit note
            $filters = array('where'                  => array('co.id IN ("' . implode('","', $policies) . '")'),
                             'model_lang'             => $params['model']->get('model_lang'),
                             'skip_permissions_check' => true);
            $policies_list = Contracts::search($this->registry, $filters);

            $old_models = array();
            foreach ($policies_list as $policy) {
                $this->registry->set('get_old_vars', true, true);
                $policy->getVars();
                $this->registry->set('get_old_vars', false, true);

                $old_models[$policy->get('id')] = clone $policy;
            }

            $sql = sprintf('UPDATE %s as gt2, %s as gt2i18n SET gt2i18n.free_text5="", gt2.article_volume="" WHERE gt2.id IN ("%s") AND gt2i18n.parent_id=gt2.id', DB_TABLE_GT2_DETAILS, DB_TABLE_GT2_DETAILS_I18N, implode('","', array_keys($policies)));
            $this->registry['db']->Execute($sql);

            $filters = array('where'                  => array('co.id IN ("' . implode('","', $policies) . '")'),
                             'model_lang'             => $params['model']->get('model_lang'),
                             'skip_permissions_check' => true);
            $policies_list = Contracts::search($this->registry, $filters);

            foreach ($policies_list as $policy) {
                $this->registry->set('get_old_vars', true, true);
                $policy->getVars();
                $this->registry->set('get_old_vars', false, true);

                Contracts_History::saveData($this->registry, array('model' => $policy, 'action_type' => 'edittopic', 'new_model' => $policy, 'old_model' => $old_models[$policy->get('id')]));
            }
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Automation to check if there are duplicate documents schedule visits for same period
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function checkDuplicateScheduleVisits($params) {
        $result = true;

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        if ($this->registry['request']->get($this->settings['policy']) &&
            $this->registry['request']->get('date') &&
            $this->registry['request']->get($this->settings['insurer']) &&
            $this->registry['request']->get($this->settings['hospital']) &&
            $this->registry['request']->get($this->settings['location'])) {
            // check for a document with mismatch data

            // CHECK FOR OTHER DOCUMENTS WITH SIMILAR DATA
            $filters_docs = array(
                'model_lang'    => $this->registry['lang'],
                'where'         => array('d.type=' . $params['start_model_type'],
                                         'd.customer=' . $this->registry['request']->get('customer'),
                                         'a__' . $this->settings['policy'] . '="' . $this->registry['request']->get($this->settings['policy']) . '"',
                                         'a__' . $this->settings['insurer'] . '="' . $this->registry['request']->get($this->settings['insurer']) . '"',
                                         'a__' . $this->settings['hospital'] . '="' . $this->registry['request']->get($this->settings['hospital']) . '"',
                                         'a__' . $this->settings['location'] . '="' . $this->registry['request']->get($this->settings['location']) . '"',
                                         'DATE_FORMAT(d.date, "%Y")="' . General::strftime('%Y', strtotime($this->registry['request']->get('date'))) . '"',
                                         'd.active = 1',
                                         'd.id!="' . $this->registry['request']->get('id') . '"')
            );
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
            $documents_ids = Documents::getIds($this->registry, $filters_docs, $sql);

            // check if there is no such schedule already inserted with the same data
            if (!empty($documents_ids)) {
                $url = "{$_SERVER['PHP_SELF']}?{$this->registry['module_param']}=documents&amp;documents=view&amp;view=" . reset($documents_ids) ."&amp;model_lang={$this->registry['lang']}";
                $this->registry['messages']->setError(sprintf($this->i18n('error_document_schedule_visits_exists_for_the_selected_period'), $url));
                $result = false;
            }
        }

        // prepare each period element as array
        $plan_from = $this->registry['request']->get($this->settings['plan_from']) ? $this->registry['request']->get($this->settings['plan_from']) : array();
        $plan_to = $this->registry['request']->get($this->settings['plan_to']) ? $this->registry['request']->get($this->settings['plan_to']) : array();
        $plan_start = $this->registry['request']->get($this->settings['start_hour']) ? $this->registry['request']->get($this->settings['start_hour']) : array();
        $plan_end = $this->registry['request']->get($this->settings['end_hour']) ? $this->registry['request']->get($this->settings['end_hour']) : array();
        $hour_interval = $this->registry['request']->get($this->settings['hour_interval']) ? $this->registry['request']->get($this->settings['hour_interval']) : array();
        $hours_capacity = $this->registry['request']->get($this->settings['hours_capacity']) ? $this->registry['request']->get($this->settings['hours_capacity']) : array();

        $periods = array();
        $periods_indexes = array();
        $skip_check_periods = false;
        $skip_check_locked_rows = false;
        $current_period_row = 0;
        foreach ($plan_from as $key => $pf) {
            if (!$pf || !$plan_to[$key] || !$plan_start[$key] || !$plan_end[$key] || !$hour_interval[$key]) {
                // skip the periods check - the fields must be set as required so additional fields validation will display error
                $skip_check_periods = true;
                break;
            } else {
                // build the period in readable format
                $periods[$current_period_row]['from'] = $plan_from[$key];
                $periods[$current_period_row]['from_hour'] = sprintf('%s:00', $plan_start[$key]);
                $periods[$current_period_row]['to'] = $plan_to[$key];
                $periods[$current_period_row]['to_hour'] = sprintf('%s:00', $plan_end[$key]);
                $periods[$current_period_row]['period_daily_interval'] = (strtotime(sprintf('%s %s:00', date('Y-m-d'), $plan_end[$key])) - strtotime(sprintf('%s %s:00', date('Y-m-d'), $plan_start[$key]))) / 60;
                $periods[$current_period_row]['hour_interval'] = $hour_interval[$key];
                $periods_indexes[] = sprintf('%s_%s_%s_%s_%s_%s', $pf, $plan_to[$key], $plan_start[$key], $plan_end[$key], $hour_interval[$key], (!empty($hours_capacity[$key]) ? $hours_capacity[$key] : 0));

                $current_period_row++;
            }
        }

        // check if the document have ID
        // if not then this action is add and the locked rows check will be skipped
        $locked_rows = array();
        if ($this->registry['request']->get('id')) {
            $filters_od = array(
                'model_lang'    => $this->registry['lang'],
                'where'         => array('d.id="' . $this->registry['request']->get('id') . '"')
            );
            $old_document = Documents::searchOne($this->registry, $filters_od);

            $this->registry->set('get_old_vars', true, true);
            $assoc_vars = $old_document->getAssocVars();
            $this->registry->set('get_old_vars', false, true);

            unset($old_document);
            $locked_rows = !empty($assoc_vars[$this->settings['plan_locked']]['value']) ? $assoc_vars[$this->settings['plan_locked']]['value'] : array();
            $locked_rows = array_filter($locked_rows);

            foreach ($locked_rows as $key_r => $locked_data) {
                $current_row_index = sprintf('%s_%s_%s_%s_%s_%s',
                                             !empty($assoc_vars[$this->settings['plan_from']]['value'][$key_r]) ? $assoc_vars[$this->settings['plan_from']]['value'][$key_r] : 0,
                                             !empty($assoc_vars[$this->settings['plan_to']]['value'][$key_r]) ? $assoc_vars[$this->settings['plan_to']]['value'][$key_r] : 0,
                                             !empty($assoc_vars[$this->settings['start_hour']]['value'][$key_r]) ? $assoc_vars[$this->settings['start_hour']]['value'][$key_r] : 0,
                                             !empty($assoc_vars[$this->settings['end_hour']]['value'][$key_r]) ? $assoc_vars[$this->settings['end_hour']]['value'][$key_r] : 0,
                                             !empty($assoc_vars[$this->settings['hour_interval']]['value'][$key_r]) ? $assoc_vars[$this->settings['hour_interval']]['value'][$key_r] : 0,
                                             !empty($assoc_vars[$this->settings['hours_capacity']]['value'][$key_r]) ? $assoc_vars[$this->settings['hours_capacity']]['value'][$key_r] : 0);
                if (!in_array($current_row_index, $periods_indexes)) {
                    $this->registry['messages']->setError(sprintf($this->i18n('error_document_schedule_visits_locked_row_can_not_be_changed'), $key_r));
                    $result = false;
                }
            }

            unset($locked_rows);
        }

        if (!$skip_check_periods) {
            foreach ($periods as $key => $per) {
                if ($per['from'] > $per['to'] || $per['from_hour'] >= $per['to_hour']) {
                    // if the end date is before begin date an error will be displayed
                    $this->registry['messages']->setError(sprintf($this->i18n('error_document_schedule_visits_wrong_from_to_period'), $key+1));
                    $result = false;
                    continue;
                }

                if ($per['period_daily_interval'] % $per['hour_interval']) {
                    $this->registry['messages']->setError(sprintf($this->i18n('error_document_schedule_visits_incorrect_interval'), $key+1));
                    $result = false;
                }

                // verify each date period with each other date period
                foreach ($periods as $key_inner => $per_inner) {
                    if ($key_inner <= $key) {
                        // skip the periods which are already checked
                        continue;
                    }
                    if ($per_inner['from'] <= $per['to'] && $per_inner['to'] >= $per['from'] && $per_inner['from_hour'] < $per['to_hour'] && $per_inner['to_hour'] > $per['from_hour']) {
                        // on mismatch an error shall be displayed
                        $this->registry['messages']->setError(sprintf($this->i18n('error_document_schedule_visits_mismatch_periods'), ($key+1), ($key_inner+1)));
                        $result = false;
                    }
                }
            }
        }

        return $result;
    }

    /**
     * Reminds appointments for medical examinations
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function reminderMedicalExams($params) {
        $document = $params['model'];
        $vars = $document->getAssocVars();
        $result = false;

        //get the emails from 'insured_person_email' field in the HIN nomenclature
        $query = 'SELECT nc.model_id, nc.value' . "\n".
                 'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' AS nc' . "\n" .
                 'WHERE var_id=902 AND model_id IN (' . implode(', ', array_filter($vars['insured_person_id']['value'])) . ')';
        $emails = $this->registry['db']->GetAssoc($query);

        $data = array();
        foreach ($vars['insured_person_id']['value'] as $idx => $hin_id) {
            if ($hin_id) {
                //IMPORTANT: check if the reservation date is incoming tomorrow
                //           the interval is not a setting because it in the "where" conditions as well
                $date = new DateTime($vars['reserved_date']['value'][$idx]);
                $date->sub(new DateInterval('P1D'));
                if ($date->format('Y-m-d') == General::strftime('%Y-%m-%d')) {
                    $data[] = array(
                        'name' => $vars['insured_person_name']['value'][$idx],
                        'email' => $emails[$hin_id],
                        'date' => $vars['reserved_date']['value'][$idx],
                        'hour' => $vars['reserved_hour']['value'][$idx],
                    );
                }
            }
        }

        if (!empty($data)) {
            //get email template
            $filters = array('where' => array('e.id = ' . $this->settings['email_template']), 'sanitize' => true);
            $mail = Emails::searchOne($this->registry, $filters);
            if (!empty($mail)) {
                //get the user from the online web form and replace it as current user
                $user = Users::searchOne($this->registry, array('where' => array('u.username = "online"'), 'sanitize' => true));
                $this->registry->set('currentUser', $user, true);

                //IMPORTANT: the email notifications are probably OFF for this installation, but this email SHOULD BE SENT!
                $do_not_send = $this->registry['config']->getParam('emails', 'do_not_send');
                //turn on the notifications in order to send the email
                $this->registry['config']->setInt('emails', 'do_not_send', 0, 0);

                $contact_data = array_filter(array(
                    $vars['aon_employee_name']['value'],
                    $vars['aon_employee_gsm']['value'],
                    $vars['aon_employee_email']['value']
                ));
                foreach ($data as $item) {
                    //set additional placeholders
                    $document->set('additionalEmailVars', array(
                        'hin_name' => $item['name'],
                        'reservation_date' => General::strftime('%d.%m.%Y (%A)', $item['date']),
                        'reservation_start_hour' => $item['hour'],
                        'contact_data' => implode(', ', $contact_data),
                    ), true);

                    $document->set('body', $mail->get('body'), true);
                    $document->set('email_subject', $mail->get('subject'), true);
                    $document->set('email_template', $mail->get('id'), true);
                    $document->set('add_signature', true, true);
                    $document->set('customer_email', $item['name'] . ' <' . $item['email'] . '>', true);

                    if ($document->sanitized) {
                        $document->unsanitize();
                        $sa = true;
                    }
                    $send_results = $document->sendAsMail();
                    if ($send_results['sent'] > 0) {
                        $result = true;
                    }
                    if ($sa) {
                        $document->sanitize();
                    }
                }

                //restore the setting
                $this->registry['config']->setInt('emails', 'do_not_send', $do_not_send, 0);
            }
        }

        return $result;
    }

    /**
     * Automation to check if fee can be created for the selected contract
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function checkAddingFee($params) {

        $included_vars = array($this->settings['fee_value'], $this->settings['fee_count_payments'], $this->settings['fee_policy_id'], $this->settings['fee_policy_num'], $this->settings['fee_policy_value'], $this->settings['fee_policy_from'], $this->settings['fee_policy_to'], $this->settings['fee_value'], $this->settings['fee_count_payments']);
        $sql = 'SELECT `name`, `id`, `model_type` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Contract" AND ((`model_type`=' . $params['start_model_type'] . ' AND `name` IN ("' . implode('","', $included_vars) . '")) OR (`model_type`="' . $this->settings['fee_type'] . '" AND `name`="' . $this->settings['fee_invoiced'] . '"))';
        $add_vars = $this->registry['db']->GetAll($sql);

        $policy_vars = array();
        $fee_vars = array();
        foreach ($add_vars as $add_var) {
            if ($add_var['model_type'] == $params['start_model_type']) {
                $policy_vars[$add_var['name']] = $add_var['id'];
            } else {
                $fee_vars[$add_var['name']] = $add_var['id'];
            }
        }

        // get main vars to check
        $sql = 'SELECT c_cstm_fee_value.value as fee_value, c_cstm_fee_count.value as fee_count' . "\n" .
               'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_value' . "\n" .
               'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_count' . "\n" .
               '  ON (c_cstm_fee_count.model_id=c_cstm_fee_value.model_id AND c_cstm_fee_count.var_id="' . $policy_vars[$this->settings['fee_count_payments']] . '")' . "\n" .
               'WHERE c_cstm_fee_value.model_id="' . $params['model']->get('id') . '" AND c_cstm_fee_value.var_id="' . $policy_vars[$this->settings['fee_value']] . '"' . "\n";
        $fee_data = $this->registry['db']->GetRow($sql);

        if (empty($fee_data)) {
            $fee_data = array('fee_value' => '', 'fee_count' => '');
        }

        // not allowed to have fee and commision so check that
        if ($this->registry['request']->get($this->settings['shared_commission_percent'])) {
            $shared_commission_rows = array_filter($this->registry['request']->get($this->settings['shared_commission_percent']));
        } else {
            $shared_commission_rows = array();
        }
        if ($this->registry['request']->get($this->settings['fee_value']) && $this->registry['request']->get($this->settings['fee_count_payments']) && count($shared_commission_rows)) {
            $params['model']->raiseError('error_fee_and_commission_not_allowed');
            return false;
        }

        if (($this->registry['request']->get($this->settings['fee_count_payments']) && !$this->registry['request']->get($this->settings['fee_value'])) ||
            (!$this->registry['request']->get($this->settings['fee_count_payments']) && $this->registry['request']->get($this->settings['fee_value']))) {
            $params['model']->raiseError('error_fee_both_fields_need_to_be_completed');
            return false;
        }

        if ($params['model']->get('id')) {
            // check the fee table
            $sql = 'SELECT c_cstm_fee_id.value as id, c_cstm_fee_num.value as num, c_cstm_fee_value.value as value, c_cstm_fee_from.value as from_period, c_cstm_fee_to.value as to_period, c_cstm_fee_inv.value as invoiced' . "\n" .
                   'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_id' . "\n" .
                   'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_num' . "\n" .
                   '  ON (c_cstm_fee_id.model_id=c_cstm_fee_num.model_id AND c_cstm_fee_id.num=c_cstm_fee_num.num AND c_cstm_fee_id.var_id="' . $policy_vars[$this->settings['fee_policy_num']] . '")' . "\n" .
                   'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_value' . "\n" .
                   '  ON (c_cstm_fee_id.model_id=c_cstm_fee_value.model_id AND c_cstm_fee_id.num=c_cstm_fee_value.num AND c_cstm_fee_value.var_id="' . $policy_vars[$this->settings['fee_policy_value']] . '")' . "\n" .
                   'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_from' . "\n" .
                   '  ON (c_cstm_fee_id.model_id=c_cstm_fee_from.model_id AND c_cstm_fee_id.num=c_cstm_fee_from.num AND c_cstm_fee_from.var_id="' . $policy_vars[$this->settings['fee_policy_from']] . '")' . "\n" .
                   'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_to' . "\n" .
                   '  ON (c_cstm_fee_id.model_id=c_cstm_fee_to.model_id AND c_cstm_fee_id.num=c_cstm_fee_to.num AND c_cstm_fee_to.var_id="' . $policy_vars[$this->settings['fee_policy_to']] . '")' . "\n" .
                   'LEFT JOIN ' .  DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_inv' . "\n" .
                   '  ON (c_cstm_fee_inv.model_id=c_cstm_fee_id.value AND c_cstm_fee_inv.var_id="' . $fee_vars[$this->settings['fee_invoiced']] . '")' . "\n" .
                   'WHERE c_cstm_fee_id.model_id="' . $params['model']->get('id') . '" AND c_cstm_fee_id.var_id="' . $policy_vars[$this->settings['fee_policy_id']] . '"' . "\n";
            $fee_rows = $this->registry['db']->GetAll($sql);

            $fee_invoiced_value = 0;
            $not_invoiced_fees = array();
            foreach ($fee_rows as $key => $row_values) {
                $fee_rows[$key] = array_filter($row_values);
                if (empty($fee_rows[$key])) {
                    unset($fee_rows[$key]);
                } else {
                    if ($row_values['invoiced']) {
                        $fee_invoiced_value += $row_values['value'];
                    } else {
                        $not_invoiced_fees[] = $row_values['id'];
                    }
                }
            }

            if (!empty($fee_data['fee_count']) && $this->registry['request']->get($this->settings['fee_count_payments']) != $fee_data['fee_count'] && !empty($fee_rows)) {
                $params['model']->raiseError('error_fee_already_generated');
                return false;
            }

            if (!empty($fee_rows) && $this->registry['request']->get($this->settings['fee_value']) < $fee_invoiced_value) {
                $params['model']->raiseError('error_fee_selected_value_less_than_invoiced', 'fee__value', null, array($fee_invoiced_value));
                return false;
            }

            if (!empty($fee_rows) && empty($not_invoiced_fees)) {
                // there are no fees that are not invoiced
                $params['model']->raiseError('error_fee_lack_of_non_invoiced_fees');
                return false;
            }
        }

        return true;
    }

    /**
     * Automation to add fee contracts
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function addFee($params) {
        require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
        require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
        require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';

        $filters_pol = array('where'      => array('co.id = ' . $params['model']->get('id')),
                             'model_lang' => $this->registry['lang']);
        $model = Contracts::searchOne($this->registry, $filters_pol);

        $model->getVars();
        $old_model = clone $model;
        $assoc_vars = $model->getAssocVars();

        // check for existing rows and if the value is changed
        $current_fee_total_value = $assoc_vars[$this->settings['policy_fee_value_bgn']]['value'];
        $current_table_fee_values = $assoc_vars[$this->settings['policy_fee_policy_value']]['value'];

        $current_table_fee_total = 0;
        if (!empty($current_table_fee_values)) {
            $current_table_fee_total = array_sum($current_table_fee_values);
        }

        $db = $this->registry['db'];

        if ($current_table_fee_total != 0 && $current_fee_total_value != $current_table_fee_total) {
            // the values has been changed so we need to update the values in the rows
            // get the invoiced rows
            $included_fees = $assoc_vars[$this->settings['policy_fee_policy_id']]['value'];
            $included_fees_values_var = $assoc_vars[$this->settings['policy_fee_policy_value']];

            // get the additional vars for fees
            $fee_add_vars = array($this->settings['fee_invoiced'], $this->settings['fee_value']);
            $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Contract" AND `model_type`=' . $this->settings['fee_type_id'] . ' AND `name` IN ("' . implode('","', $fee_add_vars) . '")';
            $fee_add_vars = $this->registry['db']->GetAssoc($sql);

            // get the fees which are invoiced
            $sql = 'SELECT c_cstm_fee_inv.model_id as model, c_cstm_fee_inv.value as invoiced' . "\n" .
                   'FROM ' . DB_TABLE_CONTRACTS_CSTM . ' AS c_cstm_fee_inv' . "\n" .
                   'WHERE c_cstm_fee_inv.model_id IN ("' . implode('","', $included_fees) . '") AND c_cstm_fee_inv.var_id="' . $fee_add_vars[$this->settings['fee_invoiced']] . '"' . "\n";
            $invoiced_fees = $this->registry['db']->GetAssoc($sql);

            $value_increase = $current_fee_total_value - $current_table_fee_total;

            // remove the invoiced rows
            foreach ($included_fees as $key => $inc_fee) {
                if (!empty($invoiced_fees[$inc_fee])) {
                    unset($included_fees[$key]);
                }
            }

            // define the new value per row
            $price_per_row = sprintf('%.2f', $value_increase/count($included_fees));
            $insert_query_template = '("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")';

            $db->StartTrans();
            $policy_queries = array();
            $rows_included = array_keys($included_fees);
            foreach ($included_fees as $key_row => $inc_fee) {
                if (end($rows_included) == $key_row) {
                    $current_row_value = $value_increase;
                } else {
                    $current_row_value = $price_per_row;
                }
                $current_row_value += $included_fees_values_var['value'][$key_row];
                $value_increase = $value_increase - $price_per_row;
                $policy_queries[] = sprintf($insert_query_template, $model->get('id'), $included_fees_values_var['id'], $key_row, sprintf('%.2f', ($current_row_value)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($included_fees_values_var['multilang'] ? $this->registry['lang'] : ""));

                // get the fee old fee model
                $filters_fee = array('where'      => array('co.id = ' . $inc_fee),
                                     'model_lang' => $this->registry['lang']);
                $model_fee = Contracts::searchOne($this->registry, $filters_fee);

                $assoc_vars_fee = $model_fee->getAssocVars();
                $old_model_fee = clone $model_fee;

                $fee_query = sprintf($insert_query_template, $model_fee->get('id'), $assoc_vars_fee[$this->settings['fee_value']]['id'], 1, sprintf('%.2f', ($current_row_value)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars_fee[$this->settings['fee_value']]['multilang'] ? $this->registry['lang'] : ""));
                $sql = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                       'VALUES ' . $fee_query . "\n" .
                       'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $db->Execute($sql);

                // save history for the fee
                $new_model_fee = Contracts::searchOne($this->registry, $filters_fee);
                $new_model_fee->getVars();
                Contracts_History::saveData($this->registry, array('model' => $new_model_fee, 'action_type' => 'edittopic', 'new_model' => $new_model_fee, 'old_model' => $old_model_fee));
            }

            $sql = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $policy_queries) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $db->Execute($sql);

            if (!$db->HasFailedTrans()) {
                // save history
                $new_model = Contracts::searchOne($this->registry, $filters_pol);
                Contracts_History::saveData($this->registry, array('model' => $new_model, 'action_type' => 'edittopic', 'new_model' => $new_model, 'old_model' => $old_model));

                $this->registry['messages']->setMessage($this->i18n('message_fee_edited_new_value_successfully'));
            } else {
                $this->registry['messages']->setError($this->i18n('error_fee_edit_new_value_failed'));
                $db->FailTrans();
            }

            $result = !$db->HasFailedTrans();
            $db->CompleteTrans();
        } else {
            $fee_policy_rows = array();

            $number_rows = $assoc_vars[$this->settings['policy_fee_count_payments']]['value'];
            $date_start = new DateTime($model->get('date_start'));
            $date_end = new DateTime($model->get('date_validity'));
            $interval = $date_start->diff($date_end);
            $total_months_in_period = ceil($interval->format('%a') / 30);

            $single_period_months = floor($total_months_in_period / $number_rows);
            if ($single_period_months == 0) {
                $single_period_months = 1;
            }

            // calculates the last date of payment for the first period
            $previous_month = strtotime(General::strftime('%Y-%m', strtotime($model->get('date_start'))));
            $previous_date = $model->get('date_start');
            $contract_day_of_month_validity = sprintf("%d", General::strftime('%d', strtotime($model->get('date_start'))));

            $sum_left = (!empty($assoc_vars[$this->settings['policy_fee_value_bgn']]) ? $assoc_vars[$this->settings['policy_fee_value_bgn']]['value'] : 0);
            $price_per_row = sprintf('%.2f', $sum_left/$number_rows);

            for ($iteration = 0; $iteration < $number_rows; $iteration++) {
                $fee_row = array(
                    'fee_id'  => '',
                    'fee_num' => '',
                    'value'   => '',
                    'from'    => '',
                    'to'      => '',
                    'num'     => $iteration+1
                );

                if ($iteration == 0) {
                    $fee_row['from'] = $model->get('date_start');
                } else {
                    // calculate the date depending on the date of payment
                    $current_period_month = strtotime('+' . $single_period_months . ' months', $previous_month);
                    $current_month = General::strftime('%Y-%m', $current_period_month);
                    $number_days_in_current_month = date('t', $current_period_month);
                    if ($number_days_in_current_month < $contract_day_of_month_validity) {
                        $current_date = $current_month . '-' . sprintf('%02s', $number_days_in_current_month);
                    } else {
                        $current_date = $current_month . '-' . sprintf('%02s', $contract_day_of_month_validity);
                    }
                    if ($current_date > $model->get('date_validity')) {
                        $fee_row['from'] = $previous_date;
                    } else {
                        $fee_row['from'] = $current_date;
                        $previous_month = $current_period_month;
                        $previous_date = $current_date;
                    }
                }

                $fee_row['to'] = General::strftime('%Y-%m-%d', strtotime('+' . $single_period_months . ' months -1 day', strtotime($fee_row['from'])));

                if ($iteration != $number_rows-1) {
                    $fee_row['value'] = $price_per_row;
                    $sum_left = $sum_left - $price_per_row;
                    if ($fee_row['to'] > $model->get('date_validity')) {
                        $fee_row['to'] = $model->get('date_validity');
                    }
                } else {
                    $fee_row['to'] = $model->get('date_validity');
                    $fee_row['value'] = sprintf('%.2f', $sum_left);
                    $sum_left = 0;
                }

                $fee_policy_rows[] = $fee_row;
            }

            $db->StartTrans();

            if (!empty($fee_policy_rows)) {
                // get the default name of the fee type
                $sql = 'SELECT `default_name` FROM ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' WHERE `parent_id`="' . $this->settings['fee_type_id'] . '" AND `lang`="' . $this->registry['lang'] . '"' . "\n";
                $fee_default_name = $db->GetOne($sql);

                // start creating fee contracts
                $new_contract_properties = array(
                    'name'                         => $fee_default_name,
                    'type'                         => $this->settings['fee_type_id'],
                    'customer'                     => $model->get('customer'),
                    'branch'                       => $model->get('branch'),
                    'contact_person'               => $model->get('contact_person'),
                    'office'                       => $model->get('office'),
                    'company'                      => $model->get('company'),
                    'date_sign'                    => date('Y-m-d'),
                    'department'                   => $model->get('department'),
                    'employee'                     => $this->registry['originalUser']->get('employee'),
                    'group'                        => $model->get('group'),
                    'change_formula_date_start'    => 'date_start',
                    'change_formula_date_sign'     => 'date_sign',
                    'change_formula_date_validity' => 'date_validity'
                );

                $current_user = $this->registry['currentUser'];
                $this->registry->set('currentUser', $this->registry['originalUser'], true);
                foreach ($fee_policy_rows as $key_fee => $fee_data) {
                    $old_contract = new Contract($this->registry, array('type' => $this->settings['fee_type_id']));
                    $old_contract->getVars();

                    $contract = new Contract($this->registry, $new_contract_properties);
                    $contract->set('date_start', $fee_data['from'], true);
                    $contract->set('date_validity', $fee_data['to'], true);
                    $contract->getVars();

                    $gt2_var = $contract->getGT2Vars();
                    $gt2_var['values'][] = array(
                        'article_id'                         => $model->get('id'),
                        'article_name'                       => $model->get('custom_num'),
                        'article_deliverer'                  => $model->get('employee'),
                        'article_deliverer_name'             => $model->get('employee_name'),
                        'article_alternative_deliverer'      => $model->get('customer'),
                        'article_alternative_deliverer_name' => $model->get('customer_name'),
                        'free_field3'                        => $assoc_vars[$this->settings['insurance_type_var_prefix'] . $model->get('type')]['value'],
                        'free_text4'                         => $model->get('date_start'),
                        'free_text1'                         => $model->get('date_validity'),
                        'free_field2'                        => $model->get('id'),
                        'price'                              => 0,
                        'quantity'                           => 1
                    );

                    $contract->set('grouping_table_2', $gt2_var, true);

                    $contract->calculateGT2();
                    $contract->set('table_values_are_set', true, true);

                    // change the vars values
                    $vars = $contract->get('vars');
                    foreach ($vars as $key => $var) {
                        switch ($var['name']) {
                            case $this->settings['fee_insured_id']:
                                $vars[$key]['value'] = $model->get('customer');
                                break;
                            case $this->settings['fee_insured_name']:
                                $vars[$key]['value'] = $model->get('customer_name');
                                break;
                            case $this->settings['fee_value']:
                                $vars[$key]['value'] = $fee_data['value'];
                                break;
                            case $this->settings['fee_business_type']:
                                $vars[$key]['value'] = $assoc_vars[$this->settings['policy_business_type']]['value'];
                                break;
                            case $this->settings['fee_client_type']:
                                $vars[$key]['value'] = $assoc_vars[$this->settings['policy_client_type']]['value'];
                                break;
                            default:
                                break;
                        }
                    }

                    $contract->set('vars', $vars, true);

                    if ($contract->save() && $contract->saveGT2Vars()) {
                        $filters = array('where' => array('co.id = ' . $contract->get('id')),
                                         'model_lang' => $contract->get('model_lang'));
                        $new_contract = Contracts::searchOne($this->registry, $filters);
                        $get_old_vars = $this->registry->get('get_old_vars');

                        $this->registry->set('get_old_vars', true, true);
                        $new_contract->getVars();

                        // write history
                        Contracts_History::saveData($this->registry, array('model' => $contract, 'action_type' => 'add', 'new_model' => $new_contract, 'old_model' => $old_contract));
                        $this->registry->set('get_old_vars', $get_old_vars, true);

                        // Execute action type automations for the Fee
                        if (!$this->executeActionAutomations($old_contract, $new_contract, 'add')) {
                            $db->FailTrans();
                        }

                        $old_contract = clone $new_contract;
                        $new_contract->set('status', 'closed', true);
                        $new_contract->set('substatus', '', true);

                        if ($new_contract->setStatus()) {
                            $filters = array('where' => array('co.id = ' . $new_contract->get('id')),
                                             'model_lang' => $new_contract->get('model_lang'));
                            $new_contract = Contracts::searchOne($this->registry, $filters);

                            Contracts_History::saveData($this->registry, array('model' => $new_contract, 'action_type' => 'status', 'new_model' => $new_contract, 'old_model' => $old_contract));
                        } else {
                            $this->registry['messages']->setError($this->i18n('error_fee_change_status_failed'));
                            $db->FailTrans();
                            break;
                        }

                        $fee_policy_rows[$key_fee]['fee_id'] = $new_contract->get('id');
                        $fee_policy_rows[$key_fee]['fee_num'] = $new_contract->get('num');
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_fee_add_failed'));
                        $db->FailTrans();
                        break;
                    }
                }
                $this->registry->set('currentUser', $current_user, true);

                if (!$db->HasFailedTrans()) {
                    $insert_queries = array();
                    $relatives_queries = array();
                    $insert_query_template = '("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")';
                    $insert_query_relatives = '("%d", "Contract", "%d", "Contract", "inherited", "0", "")';

                    $policy_vars = array(
                        'fee_id'  => $this->settings['policy_fee_policy_id'],
                        'fee_num' => $this->settings['policy_fee_policy_num'],
                        'value'   => $this->settings['policy_fee_policy_value'],
                        'from'    => $this->settings['policy_fee_policy_from'],
                        'to'      => $this->settings['policy_fee_policy_to'],
                        'num'     => $this->settings['policy_fee_num']
                    );

                    $rows_iterator = 0;
                    foreach ($fee_policy_rows as $key => $fee_data) {
                        if (empty($fee_data['fee_id'])) {
                            // if any of the data is empty don't record a row
                            continue;
                        }
                        $rows_iterator++;
                        $relatives_queries[] = sprintf($insert_query_relatives, $model->get('id'), $fee_data['fee_id']);

                        foreach ($policy_vars as $key_var => $pol_var) {
                            if (!empty($assoc_vars[$pol_var])) {
                                $insert_queries[] = sprintf($insert_query_template, $model->get('id'), $assoc_vars[$pol_var]['id'], $rows_iterator, General::slashesEscape($fee_data[$key_var]), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[$pol_var]['multilang'] ? $this->registry['lang'] : ""));
                            }
                        }
                    }

                    if (!empty($insert_queries)) {
                        // update the additional vars that are outside the GT2 vars
                        $sql = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                               'VALUES ' . implode(",\n", $insert_queries) . "\n" .
                               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                        $db->Execute($sql);
                    }

                    if ($relatives_queries) {
                        $sql = 'INSERT IGNORE INTO ' . DB_TABLE_CONTRACTS_RELATIVES . ' (`link_to`, `link_to_model_name`, `parent_id`, `parent_model_name`, `origin`, `multi_index`, `group_index`)' . "\n" .
                               'VALUES ' . implode(",\n", $relatives_queries) . "\n";
                        $db->Execute($sql);
                    }

                    if (!$db->HasFailedTrans()) {
                        // save history
                        $new_model = Contracts::searchOne($this->registry, $filters_pol);
                        Contracts_History::saveData($this->registry, array('model' => $new_model, 'action_type' => 'edittopic', 'new_model' => $new_model, 'old_model' => $old_model));

                        $this->registry['messages']->setMessage($this->i18n('message_fee_added_successfully'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_fee_save_data_to_policy'));
                        $db->FailTrans();
                    }
                }
            }
            $this->registry['messages']->insertInSession($this->registry);

            $result = !$db->HasFailedTrans();
            $db->CompleteTrans();

        }

        return $result;
    }

    /**
     * Validation to check if the tax or the GFO matches the entered data in the configurator and GT2
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function addCheckTaxGFO($params) {
        $request = $this->registry['request'];

        // check if the GT2 is precalculated
        $error = false;
        $model = clone($params['model']);

        $tax_value = $request->get($this->settings['tax_value']);
        $tax_value_bgn = $request->get($this->settings['tax_value_bgn']);
        $fee_value = $request->get($this->settings['fee_value']);
        $fee_value_bgn = $request->get($this->settings['fee_value_bgn']);
        $number_installments = $request->get($this->settings['number_rows']);

        if ($request->get($this->settings['contract_precalculated_gt2_field']) && $this->registry['session']->get($request->get($this->settings['contract_precalculated_gt2_field']))) {
            // check if the data from the request matches the data in gt2 table
            $session_data = $this->registry['session']->get($request->get($this->settings['contract_precalculated_gt2_field']));

            $gt2 = $session_data['gt2'];

            $tax_value_tbl = 0;
            $fee_value_tbl = 0;
            foreach ($gt2['values'] as $row_id => $row_values) {
                if ($row_values['free_field4'] == $this->settings['commission_type_standart']) {
                    $tax_value_tbl = round((floatval($row_values['last_delivery_price']) + $tax_value_tbl), 2);
                    $fee_value_tbl = round((floatval($row_values['article_width']) + floatval($row_values['article_code']) + floatval($row_values['article_weight']) + $fee_value_tbl), 2);
                }
            }

            if ($tax_value) {
                $calculated_value = $tax_value_bgn / $number_installments;
                $rounded_calculated_value = (round($calculated_value, 2) * $number_installments);
                $acceptable_difference = round(abs($tax_value_bgn-$rounded_calculated_value), 6);
                $difference_in_values = round(abs($tax_value_tbl - $tax_value_bgn), 6);

                if ($difference_in_values>$acceptable_difference) {
                    $this->registry['messages']->setError($this->i18n('error_sums_tax_bgn_does_not_match'));
                    $error = true;
                }
            }

            if ($fee_value) {
                if (abs($fee_value_bgn-$fee_value_tbl) > 0.01) {
                    $this->registry['messages']->setError($this->i18n('error_sums_fee_bgn_does_not_match'));
                    $error = true;
                }
            }
        } else {
            // get the taxes data from the customer
            if (!empty($fee_value)) {
                $customer_vars = array($this->settings['insurer_sticker'], $this->settings['insurer_green_card'], $this->settings['insurer_fond']);
                $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Customer" AND `model_type`=' . $this->settings['insurer_customer_type'] . ' AND `name` IN ("' . implode('","', $customer_vars) . '")';
                $customer_vars = $this->registry['db']->GetAssoc($sql);

                // get customer vars
                $sql = 'SELECT `var_id`, `value`' . "\n" .
                       'FROM ' . DB_TABLE_CUSTOMERS_CSTM . "\n" .
                       'WHERE `model_id`="' . $request->get($this->settings['insurer_id']) . '" AND `var_id` IN ("' . implode('","', $customer_vars) . '")' . "\n";
                $customer_vars_values = $this->registry['db']->GetAssoc($sql);

                $fee_value_tbl_bgn = ($number_installments * (!empty($customer_vars_values[$customer_vars[$this->settings['insurer_sticker']]]) ? floatval($customer_vars_values[$customer_vars[$this->settings['insurer_sticker']]]) : 0)) +
                                     ($number_installments * (!empty($customer_vars_values[$customer_vars[$this->settings['insurer_green_card']]]) ? floatval($customer_vars_values[$customer_vars[$this->settings['insurer_green_card']]]) : 0)) +
                                     (!empty($customer_vars_values[$customer_vars[$this->settings['insurer_fond']]]) ? floatval($customer_vars_values[$customer_vars[$this->settings['insurer_fond']]]) : 0);
                if ($fee_value_tbl_bgn != $fee_value_bgn) {
                    $this->registry['messages']->setError($this->i18n('error_sums_fee_bgn_does_not_match_customer_defaults'));
                    $error = true;
                }
            }
        }

        return !$error;
    }

    /**
     * Automation to detach debit note payment after the payment has been annulled
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function annulDebitNotePayment($params) {
        $model = $params['model'];

        $vars = $model->getAssocVars();
        $gt2 = $model->getGT2Vars();

        // get all the debit notes
        $debit_notes_list = array();
        $debit_note_rows = array();
        $debit_note_vars = array(
            'debit_note_gt2_id' => '',
            'payment_date'      => ''
        );
        if (isset($vars[$this->settings['debit_note_payment_policy_row_id']])) {
            $debit_note_vars['debit_note_gt2_id'] = $vars[$this->settings['debit_note_payment_policy_row_id']]['id'];
            $debit_note_vars['payment_date'] = (isset($vars[$this->settings['debit_note_payment_date']]['id']) ? $vars[$this->settings['debit_note_payment_date']]['id'] : '');
            foreach ($vars[$this->settings['debit_note_payment_policy_row_id']]['value'] as $row => $dn_row_id) {
                if (!empty($vars[$this->settings['debit_note_payment_policy_row_value']]['value'][$row])) {
                    $debit_note_rows[$dn_row_id] = $vars[$this->settings['debit_note_payment_policy_row_value']]['value'][$row];
                }
            }
        }

        $debit_note_rows = array_filter($debit_note_rows);

        if (!empty($debit_note_rows)) {
            // get the ids of the debit notes
            $sql = 'SELECT `id`, `model_id` FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE `id` IN ("' . implode('","', array_keys($debit_note_rows)) . '")' . "\n";
            $rows_dn_rels = $this->registry['db']->GetAssoc($sql);

            foreach ($rows_dn_rels as $dn_row_id => $dn_id) {
                if (!isset($debit_notes_list[$dn_id])) {
                    $debit_notes_list[$dn_id] = array();
                }
                $debit_notes_list[$dn_id][$dn_row_id] = $debit_note_rows[$dn_row_id];
            }
        }

        // get the models of the debit notes
        $this->registry['db']->StartTrans();
        $error_flag = false;
        if (!empty($debit_notes_list)) {
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';

            $included_reports_substatuses = array($this->settings['debit_note_status_partially_paid'], $this->settings['debit_note_status_fully_paid'], $this->settings['debit_note_status_overpaid']);


            $sql = 'SELECT CONCAT(`status`, "_", `id`), `name` FROM ' . DB_TABLE_DOCUMENTS_STATUSES . ' WHERE CONCAT(`status`, "_", `id`) IN ("' . implode('","', $included_reports_substatuses) . '")';
            $included_reports_substatuses = $this->registry['db']->GetAssoc($sql);

            $filters = array('where' => array('d.id IN ("' . implode('","', array_keys($debit_notes_list)) . '")'),
                             'model_lang' => $params['model']->get('model_lang'),
                             'skip_permissions_check' => true);
            $debit_notes = Documents::search($this->registry, $filters);

            $contract_rows_to_update = array();
            foreach ($debit_notes as $debit_note) {
                // process the GT2 table
                $this->registry->set('get_old_vars', true, true);
                $dn_assoc_vars = $debit_note->getAssocVars();
                $dn_gt2 = $debit_note->getGT2Vars();
                $old_debit_note = clone $debit_note;

                $debit_note_sum_correction = 0;
                foreach ($debit_notes_list[$debit_note->get('id')] as $key_row => $row_value) {
                    if (!empty($dn_gt2['values'][$key_row])) {
                        $dn_gt2['values'][$key_row]['free_field4'] = sprintf('%.2f', ($dn_gt2['values'][$key_row]['free_field4'] - $row_value));
                        $debit_note_sum_correction += $row_value;

                        if (!isset($contract_rows_to_update[$dn_gt2['values'][$key_row]['free_field2']])) {
                            $contract_rows_to_update[$dn_gt2['values'][$key_row]['free_field2']] = 0;
                        }
                        $contract_rows_to_update[$dn_gt2['values'][$key_row]['free_field2']] += $row_value;
                    }
                }

                $new_status = '';
                if ($debit_note_sum_correction) {
                    // update the GT2
                    $debit_note->set('grouping_table_2', $dn_gt2, true);
                    $debit_note->calculateGT2();
                    $debit_note->set('table_values_are_set', true, true);

                    if (!$debit_note->saveGT2Vars()) {
                        $this->registry['messages']->setError(
                            sprintf($this->i18n('error_annul_debit_note_payment_debit_note_gt2_edit_failed'),
                                "{$_SERVER['PHP_SELF']}?{$this->registry['module_param']}=documents&amp;documents=view&amp;view=" . $debit_note->get('id'),
                                $debit_note->get('full_num'))
                        );
                        $error_flag = true;

                        $this->registry['db']->FailTrans();
                    }

                    $new_paid = sprintf('%.2f', (floatval($dn_assoc_vars[$this->settings['debit_note_document_paid']]['value']) - $debit_note_sum_correction));

                    // update the balance var
                    $insert = sprintf('"%d", "%d", "1", "%.2f", NOW(), "%d", NOW(), "%d", "%s"',
                        $debit_note->get('id'),
                        $dn_assoc_vars[$this->settings['debit_note_document_paid']]['id'],
                        $new_paid,
                        $this->registry['currentUser']->get('id'),
                        $this->registry['currentUser']->get('id'),
                        ($dn_assoc_vars[$this->settings['debit_note_document_paid']]['multilang'] ? $this->registry['lang'] : '')
                    );

                    $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (model_id, var_id, num, value, added, added_by, modified, modified_by, lang) VALUES' . "\n" .
                           '(' . $insert . ')' . "\n" .
                           'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                    $this->registry['db']->Execute($sql);

                    if ($new_paid == 0) {
                        $new_status = $this->settings['debit_note_status_unpaid'];
                    } elseif ($new_paid<sprintf('%.2f', $dn_assoc_vars[$this->settings['debit_note_document_value']]['value'])) {
                        $new_status = $this->settings['debit_note_status_partially_paid'];
                    } elseif ($new_paid == sprintf('%.2f', $dn_assoc_vars[$this->settings['debit_note_document_value']]['value'])) {
                        $new_status = $this->settings['debit_note_status_fully_paid'];
                    } elseif ($new_paid > sprintf('%.2f', $dn_assoc_vars[$this->settings['debit_note_document_value']]['value'])) {
                        $new_status = $this->settings['debit_note_status_overpaid'];
                    }

                    // save history for the edit
                    $filters = array('where'      => array('d.id = ' . $debit_note->get('id')),
                                     'model_lang' => $debit_note->get('model_lang'));
                    $new_debit_note_model = Documents::searchOne($this->registry, $filters);
                    $new_debit_note_model->getVars();
                    $new_debit_note_model->getGT2Vars();

                    Documents_History::saveData($this->registry, array('model' => $new_debit_note_model, 'action_type' => 'edit', 'new_model' => $new_debit_note_model, 'old_model' => $old_debit_note));
                }

                if ($new_status && $new_status != ($debit_note->get('status') . ($debit_note->get('substatus') ? '_' . $debit_note->get('substatus') : '')) && !$this->registry['db']->HasFailedTrans()) {
                    // change the status
                    $filters = array('where'      => array('d.id = ' . $debit_note->get('id')),
                                     'model_lang' => $debit_note->get('model_lang'));
                    $new_debit_note_model = Documents::searchOne($this->registry, $filters);
                    $new_debit_note_model->getVars();

                    $old_debit_note_model = clone $new_debit_note_model;

                    $new_debit_note_model->set('status', preg_replace('#(.*)_(\d+)#', '$1', $new_status), true);
                    $new_debit_note_model->set('substatus', (preg_match('#(.*)_(\d+)#', $new_status) ? $new_status : ''), true);
                    $new_debit_note_model->set('substatus_name', (isset($included_reports_substatuses[$new_status]) ? $included_reports_substatuses[$new_status] : ''), true);

                    $update = array();
                    $update[] = '`status`="' . preg_replace('#(.*)_(\d+)#', '$1', $new_status) . '"';
                    $update[] = sprintf("status_modified_by=%d", $this->registry['currentUser']->get('id'));
                    $update[] = sprintf("modified=now()");
                    if (preg_match('#(.*)_(\d+)#', $new_status)) {
                        $update[] = '`substatus`="' . preg_replace('#(.*)_(\d+)#', '$2', $new_status) . '"';
                    } else {
                        $update[] = '`substatus`=0';
                    }
                    $sql = 'UPDATE ' . DB_TABLE_DOCUMENTS . ' SET ' . implode(', ', $update) . ' WHERE `id`="' . $new_debit_note_model->get('id') . '"';
                    $this->registry['db']->Execute($sql);

                    if (!$this->registry['db']->HasFailedTrans()) {
                        $filters = array('where'      => array('d.id = ' . $old_debit_note_model->get('id')),
                                         'model_lang' => $old_debit_note_model->get('model_lang'));
                        $new_debit_note_model = Documents::searchOne($this->registry, $filters);
                        Documents_History::saveData($this->registry, array('action_type' => 'status', 'new_model' => $new_debit_note_model, 'old_model' => $old_debit_note_model));
                    } else {
                        $this->registry['messages']->setError(
                            sprintf($this->i18n('error_annul_debit_note_payment_debit_note_status_change_failed'),
                                "{$_SERVER['PHP_SELF']}?{$this->registry['module_param']}=documents&amp;documents=view&amp;view=" . $debit_note->get('id'),
                                $debit_note->get('full_num'))
                        );
                        $error_flag = true;

                        $this->registry['db']->FailTrans();
                    }
                }

                $this->registry->set('get_old_vars', false, true);
            }

            // update the contracts
            if (!empty($contract_rows_to_update)) {
                require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
                require_once PH_MODULES_DIR . 'contracts/models/contracts.history.php';
                require_once PH_MODULES_DIR . 'contracts/models/contracts.audit.php';

                $contracts_list = array();

                // get the ids of the debit notes
                $sql = 'SELECT `id`, `model_id` FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE `id` IN ("' . implode('","', array_keys($contract_rows_to_update)) . '")' . "\n";
                $rows_contr_rels = $this->registry['db']->GetAssoc($sql);

                $new_deadlines = array();
                if ($debit_note_vars['debit_note_gt2_id'] && $debit_note_vars['payment_date']) {
                    $sql = 'SELECT gt2.free_field2 as contract_id, MAX(d_cstm_date.value) as payment_date' . "\n" .
                           'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
                           '  ON (d_cstm.var_id="' . $debit_note_vars['debit_note_gt2_id'] . '" AND d_cstm.model_id=d.id AND d_cstm.value IN ("' . implode('","', array_keys($debit_note_rows)) . '"))' . "\n" .
                           'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_date' . "\n" .
                           '  ON (d_cstm_date.var_id="' . $debit_note_vars['payment_date'] . '" AND d_cstm_date.model_id=d.id)' . "\n" .
                           'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
                           '  ON (d_cstm.value=gt2.id)' . "\n" .
                           'WHERE d.type="' . $model->get('type') . '" AND d.active=1 AND d.deleted_by=0 AND d.substatus!="' . $this->settings['debit_note_payment_status_annuled'] . '"' . "\n" .
                           'GROUP BY gt2.free_field2';
                    $new_deadlines = $this->registry['db']->GetAssoc($sql);
                }

                foreach ($rows_contr_rels as $contr_row_id => $contr_id) {
                    if (!isset($contracts_list[$contr_id])) {
                        $contracts_list[$contr_id] = array();
                    }
                    $contracts_list[$contr_id][$contr_row_id] = $contract_rows_to_update[$contr_row_id];
                }

                if (!empty($contracts_list)) {
                    $filters = array('where' => array('co.id IN ("' . implode('","', array_keys($contracts_list)) . '")'),
                                     'model_lang' => $params['model']->get('model_lang'),
                                     'skip_permissions_check' => true);
                    $contracts = Contracts::search($this->registry, $filters);

                    $this->registry->set('get_old_vars', true, true);
                    foreach ($contracts as $contract) {
                        // process the GT2 table
                        $contract_assoc_vars = $contract->getAssocVars();
                        $contract_gt2 = $contract->getGT2Vars();
                        $old_contract = clone $contract;

                        foreach ($contracts_list[$contract->get('id')] as $key_row => $row_value) {
                            if (!empty($contract_gt2['values'][$key_row])) {
                                $contract_gt2['values'][$key_row]['article_deliverer_name'] = sprintf('%.2f', ($contract_gt2['values'][$key_row]['article_deliverer_name'] - $row_value));
                                $contract_gt2['values'][$key_row]['article_description'] = (isset($new_deadlines[$key_row]) ? $new_deadlines[$key_row] : '');
                            }
                        }

                        // update the GT2
                        $contract->set('grouping_table_2', $contract_gt2, true);
                        $contract->calculateGT2();
                        $contract->set('table_values_are_set', true, true);

                        if (!$contract->saveGT2Vars()) {
                            $this->registry['messages']->setError(
                                sprintf($this->i18n('error_annul_debit_note_payment_contract_gt2_edit_failed'),
                                        "{$_SERVER['PHP_SELF']}?{$this->registry['module_param']}=contracts&amp;contracts=viewtopic&amp;viewtopic=" . $contract->get('id'),
                                        $contract->get('custom_num'))
                            );
                            $error_flag = true;
                            $this->registry['db']->FailTrans();
                        }

                        // save history for the edit
                        $filters = array('where'      => array('co.id = ' . $contract->get('id')),
                                         'model_lang' => $contract->get('model_lang'));
                        $new_contract = Contracts::searchOne($this->registry, $filters);
                        $new_contract->getVars();
                        $new_contract->getGT2Vars();

                        Contracts_History::saveData($this->registry, array('model' => $new_contract, 'action_type' => 'edittopic', 'new_model' => $new_contract, 'old_model' => $old_contract));
                    }

                    $this->registry->set('get_old_vars', false, true);
                }
            }
        }

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        if ($error_flag) {
            $this->registry['messages']->insertInSession($this->registry);
        }

        // writes history
        $this->updateAutomationHistory($params, $model, $result);

        return $result;

    }

    /**
     * Updates information of the report document (type 13) in add/edit mode
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function updateAONReport($params) {
        //the automation is executed only in add and edit actions
        if ($this->registry['action'] != 'add' && $this->registry['action'] != 'edit') {
            return true;
        }

        $model = $params['model'];

        $gt2 = $model->getGT2Vars();

        $old_document = $model->get('old_model');

        $old_vars = $old_document->getAssocVars();
        $old_gt2 = $old_vars['group_table_2'];

        $debit_notes = array_unique(array_column($gt2['values'], 'article_delivery_code'));
        $old_debit_notes = array_unique(array_column($old_gt2['values'], 'article_delivery_code'));

        $added_debit_notes = array_filter(array_diff($debit_notes, $old_debit_notes));
        $deleted_debit_notes = array_filter(array_diff($old_debit_notes, $debit_notes));

        $customer_updated = false;
        $debit_notes_updated = false;

        if ($this->registry['action'] == 'add') {
            $customer_updated = true;
            $debit_notes_updated = true;
        }
        if ($model->get('customer') != $old_document->get('customer')) {
            $customer_updated = true;
        }
        if (!empty($added_debit_notes) || !empty($deleted_debit_notes)) {
            $debit_notes_updated = true;
        }
        //start this automation only in add action or when the customer or GT2 are changed in edin action
        if (!$customer_updated && !$debit_notes_updated) {
            return true;
        }

        if ($customer_updated) {
            //update the custom_num
            $query = 'SELECT COUNT(`id`) as current_count' . "\n" .
                     'FROM ' . DB_TABLE_DOCUMENTS . "\n" .
                     'WHERE `type`="' . $params['start_model_type'] . '" AND ' . "\n" .
                     '      `customer` = ' . $model->get('customer') . ' AND ' . "\n" .
                     '      DATE_FORMAT(`added`, "%Y")=DATE_FORMAT(NOW(), "%Y") AND `id`!="' . $model->get('id') . '"';
            $last_number_for_the_year = $this->registry['db']->GetOne($query);

            $document_num = 0;
            if (!empty($last_number_for_the_year)) {
                $document_num = $last_number_for_the_year + 1;
            } else {
                $document_num = 1;
            }
            $custom_num = sprintf('%d/%d', $document_num, date('Y'));

            $query = 'UPDATE ' . DB_TABLE_DOCUMENTS . ' SET custom_num="' . $custom_num . '" WHERE id=' . $model->get('id');
            $this->registry['db']->Execute($query);

            // save the history
            $filters = array('where' => array('d.id = ' . $model->get('id')),
                             'model_lang' => $params['model']->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_doc = Documents::searchOne($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);
            $new_doc->getVars();
            Documents_History::saveData($this->registry,
                array('model'       => $new_doc,
                    'action_type' => 'edit',
                    'new_model'   => $new_doc,
                    'old_model'   => $model)
            );

            //set custom num so that it can be used further down
            $model->set('custom_num', $custom_num, true);
        }

        if ($debit_notes_updated) {
            //update the report num and date within the debit notes

            //get debit note var ids
            $report_vars = array('insurer_order_date', 'insurer_order_name', 'insurer_order_id');
            $query = 'SELECT name, id FROM ' . DB_TABLE_FIELDS_META . "\n".
                     'WHERE model="Document" AND model_type=' . $this->settings['debit_note_document_type'] . ' AND'. "\n".
                     '      name IN ("' . implode('", "', $report_vars) . '")';
            $debit_note_vars = $this->registry['db']->GetAssoc($query);

            //remove the report num in the deleted debit notes
            if (!empty($deleted_debit_notes)) {
                foreach($deleted_debit_notes as $debit_note_id) {
                    // get old debit note
                    $filters = array('where' => array('d.id = ' . $debit_note_id),
                        'model_lang' => $params['model']->get('model_lang'),
                        'skip_assignments' => true,
                        'skip_permissions_check' => true);
                    $old_debit_note = Documents::searchOne($this->registry, $filters);

                    $this->registry->set('get_old_vars', true, true);
                    $old_debit_note->getVars();

                    $query = 'DELETE dc3.*, dc2.*, dc1.*' . "\n" .
                             'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                             '  ON dc1.var_id="' . $debit_note_vars['insurer_order_id'] . '" AND' . "\n" .
                             '     dc2.var_id="' . $debit_note_vars['insurer_order_name'] . '" AND' . "\n" .
                             '     dc1.model_id=dc2.model_id AND' . "\n" .
                             '     dc1.num=dc2.num AND' . "\n" .
                             '     dc1.model_id=' . $debit_note_id . ' AND' . "\n" .
                             '     dc1.value=' . $model->get('id') . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc3' . "\n" .
                             '  ON dc3.var_id="' . $debit_note_vars['insurer_order_date'] . '" AND' . "\n" .
                             '     dc1.model_id=dc3.model_id AND' . "\n" .
                             '     dc1.num=dc3.num';
                    $this->registry['db']->Execute($query);


                    //save history only if there is a real deletion (affected rows)
                    if ($this->registry['db']->Affected_Rows()) {
                        // get new debit note
                        $filters = array('where' => array('d.id = ' . $debit_note_id),
                            'model_lang' => $params['model']->get('model_lang'),
                            'skip_assignments' => true,
                            'skip_permissions_check' => true);
                        $new_debit_note = Documents::searchOne($this->registry, $filters);

                        $this->registry->set('get_old_vars', true, true);
                        $new_debit_note->getVars();

                        Documents_History::saveData($this->registry,
                            array('model'       => $new_debit_note,
                                'action_type' => 'edit',
                                'new_model'   => $new_debit_note,
                                'old_model'   => $old_debit_note)
                        );

                    } else {
                        unset($old_debit_note);
                    }
                }
            }

            //add the report num in the added debit notes
            if (!empty($added_debit_notes)) {
                foreach($added_debit_notes as $debit_note_id) {
                    // get old debit note
                    $filters = array('where' => array('d.id = ' . $debit_note_id),
                        'model_lang' => $params['model']->get('model_lang'),
                        'skip_assignments' => true,
                        'skip_permissions_check' => true);
                    $old_debit_note = Documents::searchOne($this->registry, $filters);

                    $this->registry->set('get_old_vars', true, true);
                    $old_debit_note->getVars();

                    $query = 'SELECT dc1.num' . "\n" .
                             'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc2' . "\n" .
                             '  ON dc1.var_id="' . $debit_note_vars['insurer_order_id'] . '" AND' . "\n" .
                             '     dc2.var_id="' . $debit_note_vars['insurer_order_name'] . '" AND' . "\n" .
                             '     dc1.model_id=dc2.model_id AND' . "\n" .
                             '     dc1.num=dc2.num AND' . "\n" .
                             '     dc1.model_id=' . $debit_note_id . ' AND' . "\n" .
                             '     dc1.value=' . $model->get('id') . "\n" .
                             'JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc3' . "\n" .
                             '  ON dc3.var_id="' . $debit_note_vars['insurer_order_date'] . '" AND' . "\n" .
                             '     dc1.model_id=dc3.model_id AND' . "\n" .
                             '     dc1.num=dc3.num';
                    $already_added = $this->registry['db']->GetOne($query);

                    //save history only if there is a real deletion (affected rows)
                    if (!$already_added) {
                        $query = 'SELECT MAX(dc1.num)' . "\n" .
                                 'FROM ' . DB_TABLE_DOCUMENTS_CSTM . ' as dc1' . "\n" .
                                 'WHERE dc1.var_id="' . $debit_note_vars['insurer_order_id'] . '" AND' . "\n" .
                                 '      dc1.value!="" AND dc1.model_id=' . $debit_note_id;
                        $max_num = $this->registry['db']->GetOne($query);
                        $num = 0;
                        if (!empty($max_num)) {
                            $num = $max_num + 1;
                        } else {
                            $num = 1;
                        }

                        //insurer_order_id
                        $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . "\n" .
                                 'SET model_id=' . $debit_note_id . ',' . "\n" .
                                 '    var_id=' . $debit_note_vars['insurer_order_id'] . ',' . "\n" .
                                 '    num=' . $num . ',' . "\n" .
                                 '    value="' . $model->get('id') . '",' . "\n" .
                                 '    added=NOW(),' . "\n" .
                                 '    modified=NOW(),' . "\n" .
                                 '    added_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    modified_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    lang=""' . "\n" .
                                 ' ON DUPLICATE KEY UPDATE' . "\n" .
                                 '  `value` = VALUES(`value`)';
                        $this->registry['db']->Execute($query);

                        //insurer_order_name
                        $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . "\n" .
                                 'SET model_id=' . $debit_note_id . ',' . "\n" .
                                 '    var_id=' . $debit_note_vars['insurer_order_name'] . ',' . "\n" .
                                 '    num=' . $num . ',' . "\n" .
                                 '    value="' . $model->get('custom_num') . '",' . "\n" .
                                 '    added=NOW(),' . "\n" .
                                 '    modified=NOW(),' . "\n" .
                                 '    added_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    modified_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    lang=""' . "\n" .
                                 ' ON DUPLICATE KEY UPDATE' . "\n" .
                                 '  `value` = VALUES(`value`)';
                        $this->registry['db']->Execute($query);

                        //insurer_order_date
                        $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . "\n" .
                                 'SET model_id=' . $debit_note_id . ',' . "\n" .
                                 '    var_id=' . $debit_note_vars['insurer_order_date'] . ',' . "\n" .
                                 '    num=' . $num . ',' . "\n" .
                                 '    value="' . $model->get('date') . '",' . "\n" .
                                 '    added=NOW(),' . "\n" .
                                 '    modified=NOW(),' . "\n" .
                                 '    added_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    modified_by=' . $this->registry['currentUser']->get('id') . ',' . "\n" .
                                 '    lang=""' . "\n" .
                                 ' ON DUPLICATE KEY UPDATE' . "\n" .
                                 '  `value` = VALUES(`value`)';
                        $this->registry['db']->Execute($query);

                        // get new debit note
                        $filters = array('where' => array('d.id = ' . $debit_note_id),
                            'model_lang' => $params['model']->get('model_lang'),
                            'skip_assignments' => true,
                            'skip_permissions_check' => true);
                        $new_debit_note = Documents::searchOne($this->registry, $filters);

                        $this->registry->set('get_old_vars', true, true);
                        $new_debit_note->getVars();

                        Documents_History::saveData($this->registry,
                            array('model'       => $new_debit_note,
                                'action_type' => 'edit',
                                'new_model'   => $new_debit_note,
                                'old_model'   => $old_debit_note)
                        );

                    } else {
                        unset($old_debit_note);
                    }
                }
            }
        }

        return true;

    }

    /**
     * Updates policies (contracts type 1-6) with the report num and date
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function updateAONPoliciesFromReport($params) {
        $model = $params['model'];

        $this->registry->set('get_old_vars', true, true);
        $gt2 = $model->getGT2Vars();

        $policy_ids_gt2_rows = array_unique(array_column($gt2['values'], 'article_volume'));
        if (empty($policy_ids_gt2_rows)) {
            //nothing to update
            return true;
        }

        $this->registry['db']->StartTrans();

        $updated_policies = 0;
        foreach($policy_ids_gt2_rows as $gt2_row_id) {
            //get the date of report (article_delivery_code) and id (article_trademark)
            $query = 'SELECT article_delivery_code, article_trademark, model_id FROM ' . DB_TABLE_GT2_DETAILS . ' WHERE id=' . $gt2_row_id;
            $gt2_row = $this->registry['db']->getRow($query);

            if ($gt2_row['article_delivery_code'] == $model->get('date') && $gt2_row['article_trademark'] == $model->get('id')) {
                //the report has already been saved in the GT2 of the policy (contract)
                continue;
            }

            // get old policy (contract)
            $filters = array('where' => array('co.id = ' . $gt2_row['model_id']),
                             'model_lang' => $params['model']->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $old_policy = Contracts::searchOne($this->registry, $filters);
            $old_policy->getVars();

            //update report num and id
            $query = 'UPDATE ' . DB_TABLE_GT2_DETAILS . "\n".
                     'SET article_delivery_code="' . $model->get('date')  . '",' . "\n".
                     '    article_trademark=' . $model->get('id')  . "\n".
                     'WHERE id=' . $gt2_row_id;
            $this->registry['db']->Execute($query);

            // get new debit note
            $filters = array('where' => array('co.id = ' . $gt2_row['model_id']),
                'model_lang' => $params['model']->get('model_lang'),
                'skip_assignments' => true,
                'skip_permissions_check' => true);
            $new_policy = Contracts::searchOne($this->registry, $filters);
            $new_policy->getVars();

            Contracts_History::saveData($this->registry,
                array('model'       => $new_policy,
                      'action_type' => 'edit',
                      'new_model'   => $new_policy,
                      'old_model'   => $old_policy)
            );
            $updated_policies++;
        }

        $result = !$this->registry['db']->HasFailedTrans();
        if (!$result) {
            $this->registry['messages']->setError($this->i18n('error_updating_policies_from_report'));
        } elseif ($updated_policies > 0) {
            $this->registry['messages']->setMessage($this->i18n('message_updating_policies_from_report'));
        }
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /**
     * Validate if the Sticker and ZK field is completed
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function validateZKStickerField($params) {
        $error = false;
        $request = $this->registry['request'];

        $insurer_id = (!empty($request->get($this->settings['insurer_id']) ? $request->get($this->settings['insurer_id']) : ''));
        $premium_value = (!empty($request->get($this->settings['premium_value'])) ? $request->get($this->settings['premium_value']) : '');
        $commission = (!empty($request->get($this->settings['commission_percent'])) ? $request->get($this->settings['commission_percent']) : '');
        $number_rows = (!empty($request->get($this->settings['number_rows'])) ? $request->get($this->settings['number_rows']) : '');
        $contract_currency = (!empty($request->get($this->settings['premium_currency'])) ? $request->get($this->settings['premium_currency']) : '');
        $insurance_type = (!empty($request->get($this->settings['insurance_type'])) ? $request->get($this->settings['insurance_type']) : '');

        if ($request->get($this->settings['contract_precalculated_gt2_field']) && $this->registry['session']->get($request->get($this->settings['contract_precalculated_gt2_field']))) {
            // check the table

            $session_data = $this->registry['session']->get($request->get($this->settings['contract_precalculated_gt2_field']));
            $session_diffs = false;

            // check if the session base precalculation vars has the same values as currently completed
            foreach ($session_data['precalculation_data'] as $var_to_check => $value_to_check) {
                if ($session_diffs) {
                    break;
                }

                if (($var_to_check == 'date_start' && $value_to_check!=$request->get('date_start')) ||
                    ($var_to_check == 'date_validity' && $value_to_check!=$request->get('date_validity')) ||
                    ($var_to_check == 'insurer_id' && $value_to_check!=$insurer_id) ||
                    ($var_to_check == 'premium' && $value_to_check!=$premium_value) ||
                    ($var_to_check == 'commission_percent' && $value_to_check!=$commission) ||
                    ($var_to_check == 'installments' && $value_to_check!=$number_rows) ||
                    ($var_to_check == 'currency' && $value_to_check!=$contract_currency) ||
                    ($var_to_check == 'insurance_type' && $value_to_check!=$insurance_type)) {
                    $session_diffs = true;
                }
            }

            if ($session_diffs) {
                $error = true;
            }
        } else {
            $error = true;
        }

        if ($error) {
            $this->registry['messages']->setError($this->i18n($insurance_type == $this->settings['insurance_type_sticker'] ? 'error_no_sticker_selected' : 'error_no_zk_selected'));
        }

        return !$error;
    }
}

?>
