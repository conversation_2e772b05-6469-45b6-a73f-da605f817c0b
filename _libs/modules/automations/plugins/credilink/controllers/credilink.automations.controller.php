<?php

include_once __DIR__ . '/updateCollateralRegister.trait.php';

class Credilink_Automations_Controller extends Automations_Controller {
    use updateCollateralRegisterTrait;

    /**
     * current automation id
     */
    public $automation_id = 0;
    public $credit_line_defined = false;
    public $is_credit_line = false;
    public $contract_grace_periods = array();
    public $base_interest_rate = 0;
    public $interest_rate = 0;
    public $utilize_row = 0;
    public $utilize_sum = 0;
    public $mid_contract_recalculation = false;

    private $fee_management_month;
    private $commitment_fee;
    private $left_principal;
    private $not_covered_principal;
    private $previous_row_not_covered_principal;
    private $current_model_vars;

    /**
     * Automation to calculate penalty
     */
    public function calculatePenaltyInterest($params) {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $result = true;
        $settings = $this->settings;

        // check current day
        $current_day = new DateTime();
        if (in_array($current_day->format('w'), array(0, 6, 7))) {
            // if this day is in the weekend,
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        // define ignore tags and statuses
        $ignore_tags = preg_split('#\s*,\s*#', $settings['ignore_tags']);
        $ignore_statuses = preg_split('#\s*,\s*#', $settings['ignore_statuses']);

        // get the var that contains the currency
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name`="' . $settings['doc_currency'] . '"';
        $currency_var_id = $this->registry['db']->GetOne($sql);

        // get all the rows which have to be updated
        $sql = 'SELECT gt2.id, gt2.model_id, d_cstm.value as currency' ."\n" .
               'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (d.id=gt2.model_id AND gt2.model="Document" AND d.type="' . $params['start_model_type'] . '" AND d.active=1 AND d.deleted_by=0)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
               '  ON (d_cstm.model_id=d.id AND d_cstm.var_id="' . $currency_var_id . '" AND (d_cstm.lang="" OR d_cstm.lang="' . $this->registry['lang'] . '"))' . "\n";
        if (!empty($ignore_tags)) {
            $sql .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                    ' ON (d.id=tm.model_id AND tm.model="Document" AND tm.tag_id IN ("' . implode('","', $ignore_tags) . '"))' . "\n";
        }
        $sql .= 'WHERE ROUND(gt2.average_weighted_delivery_price, 2)>0 AND DATE_FORMAT(gt2.article_code, "%Y-%m-%d")<"' . date('Y-m-d') . '"' . "\n";
        if (!empty($ignore_tags)) {
            $sql .= ' AND tm.tag_id IS NULL' . "\n";
        }
        if (!empty($ignore_statuses)) {
            $sql .= ' AND CONCAT(d.status, "_", d.substatus) NOT IN ("' . implode('","', $ignore_statuses) . '")' . "\n";
        }
        $results = $this->registry['db']->GetAll($sql);

        // group the rows by model
        $rows_by_model = array();
        $filter_reasons = array();
        foreach ($results as $res) {
            if (!isset($rows_by_model[$res['model_id']])) {
                $rows_by_model[$res['model_id']] = array();
                $filter_reasons[] = sprintf('(frr.link_to="%d" AND fir.currency="%s")', $res['model_id'], $res['currency']);
            }
            $rows_by_model[$res['model_id']][] = $res['id'];
        }

        if (!empty($rows_by_model)) {
            // take the percent of the interest
            $doc_add_vars = array($settings['doc_interest_fixed'], $settings['doc_interest_floating'], $settings['doc_interest_minimal']);
            $query = 'SELECT fm.name, fm.id FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     'WHERE fm.model="Document" AND fm.model_type="' . $params['start_model_type'] . '" AND fm.name IN ("' . implode('","', $doc_add_vars) . '")' . "\n";
            $doc_add_vars = $this->registry['db']->GetAssoc($query);

            $sql = 'SELECT d.id as doc_id, d_cstm_fix.value as fixed_val, d_cstm_float.value as float_val, d_cstm_min.value as min_val' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_fix' . "\n" .
                   '  ON (d_cstm_fix.model_id=d.id AND d_cstm_fix.var_id="' . (isset($doc_add_vars[$settings['doc_interest_fixed']]) ? $doc_add_vars[$settings['doc_interest_fixed']] : '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_float' . "\n" .
                   '  ON (d_cstm_float.model_id=d.id AND d_cstm_float.var_id="' . (isset($doc_add_vars[$settings['doc_interest_floating']]) ? $doc_add_vars[$settings['doc_interest_floating']] : '') . '")' . "\n" .
                   'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_min' . "\n" .
                   '  ON (d_cstm_min.model_id=d.id AND d_cstm_min.var_id="' . (isset($doc_add_vars[$settings['doc_interest_minimal']]) ? $doc_add_vars[$settings['doc_interest_minimal']] : '') . '")' . "\n" .
                   'WHERE d.id IN ("' . implode('","', array_keys($rows_by_model)) . '")' . "\n";
            $interest_values = $this->registry['db']->GetAssoc($sql);

            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

            $filters = array(
               'where' => array(
                   'd.id IN (' . implode(',', array_keys($rows_by_model)) . ')'
                ),
                'sort' => array('d.id ASC')
            );
            $documents = Documents::search($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);

            // get the related incomes reasons
            $sql = 'SELECT frr.link_to, frr.parent_id' . "\n" .
                   'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                   '  ON (fir.id=frr.parent_id AND fir.type="' . $settings['incomes_reason_type_id'] . '")' . "\n" .
                   'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND (' . implode(' OR ', $filter_reasons) . ')' . "\n";
            $documents_incomes_reason_realations = $this->registry['db']->GetAssoc($sql);

            foreach ($documents as $document) {
                $document->unsanitize();
                $document->getVars();
                $gt2_var = $document->getGT2Vars();
                $old_document = clone $document;

                // get the interest
                if (!isset($interest_values[$document->get('id')])) {
                    continue;
                }

                if (!empty(floatval($interest_values[$document->get('id')]['fixed_val']))) {
                    $interest = floatval($interest_values[$document->get('id')]['fixed_val']);
                } else {
                    $interest = floatval((floatval($interest_values[$document->get('id')]['float_val']) > floatval($interest_values[$document->get('id')]['min_val']) ? $interest_values[$document->get('id')]['float_val'] : $interest_values[$document->get('id')]['min_val']));
                }

                if (empty($interest)) {
                    continue;
                }

                $total_extra_obligations = 0;
                foreach ($gt2_var['values'] as $row_key => $row_values) {
                    if (!in_array($row_values['id'], $rows_by_model[$document->get('id')])) {
                        continue;
                    }

                    // check the previous day
                    $temp_date = clone $current_day;
                    $days_multiplier = 1;
                    $temp_date->sub(new DateInterval('P1D'));
                    while(in_array($temp_date->format('w'), array(0, 6, 7))) {
                        if ($row_values['article_code'] < $temp_date->format('Y-m-d')) {
                            $days_multiplier++;
                        }
                        $temp_date->sub(new DateInterval('P1D'));
                    }

                    $current_obligations = floatval($row_values['average_weighted_delivery_price']);
                    $extra_obligation = round(((($interest/100)/360)*$current_obligations) * $days_multiplier, 2);
                    $total_extra_obligations += $extra_obligation;

                    $gt2_var['values'][$row_key]['free_text5'] = sprintf('%.2f', round(floatval($row_values['free_text5']) + $extra_obligation, 2));
                    $gt2_var['values'][$row_key]['free_field5'] = sprintf('%.2f', round(floatval($row_values['free_field5']) + $extra_obligation, 2));
                    $gt2_var['values'][$row_key]['article_second_code'] = sprintf('%.2f', round(floatval($row_values['article_second_code'])+$extra_obligation, 2));
                    $gt2_var['values'][$row_key]['free_text3'] = sprintf('%.2f', round(floatval($row_values['free_text3']) + $extra_obligation, 2));
                }

                $document->set('grouping_table_2', $gt2_var, true);
                $document->calculateGT2();
                $document->set('table_values_are_set', true, true);

                $this->registry['db']->StartTrans();

                if ($document->saveGT2Vars()) {
                    $filters = array(
                        'where' => array('d.id="' . $document->get('id') . '"')
                    );
                    $new_document = Documents::searchOne($this->registry, $filters);
                    $new_document->getVars();

                    $history_params = array(
                        'model'       => $document,
                        'action_type' => 'edit',
                        'new_model'   => $new_document,
                        'old_model'   => $old_document
                    );

                    Documents_History::saveData($this->registry, $history_params);

                    // issue correction document
                    if (isset($documents_incomes_reason_realations[$new_document->get('id')])) {
                        $incomes_reason_id = $documents_incomes_reason_realations[$new_document->get('id')];

                        if (!$this->issueFinIncomeReasonCorrection($incomes_reason_id, $total_extra_obligations, $params['id'], $settings['contract_owed_sum_nom_id'])) {
                            $this->executionErrors[] = 'Penalty interests for document with id ' . $document->get('id') . ' were not corrected because the realated incomes reason cannot be corrected!';
                            $this->registry['db']->FailTrans();
                        }
                    }
                } else {
                    $this->executionErrors[] = 'Penalty interests for document with id ' . $document->get('id') . ' were not corrected because an error occured!';
                }

                $result = !$this->registry['db']->HasFailedTrans();
                $this->registry['db']->CompleteTrans();
                $this->updateAutomationHistory($params, $document, $result);
            }
        }

        $unique_errors = array_unique($this->executionErrors);
        $this->executionErrors = array_values($unique_errors);

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Automation to calculate the penalty for overdue payments
     */
    public function calculatePenalty($params) {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $result = true;
        $settings = $this->settings;

        // check current day
        $current_day = new DateTime();
        if (in_array($current_day->format('w'), array(0, 6, 7))) {
            // if this day is in the weekend,
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }

        // define ignore tags and statuses
        $ignore_tags = preg_split('#\s*,\s*#', $settings['ignore_tags']);
        $ignore_statuses = preg_split('#\s*,\s*#', $settings['ignore_statuses']);

        // get the var that contains the currency
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name`="' . $settings['doc_currency'] . '"';
        $currency_var_id = $this->registry['db']->GetOne($sql);

        // get all the rows which have to be updated
        $sql = 'SELECT gt2.id, gt2.model_id, d_cstm.value as currency' ."\n" .
               'FROM ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
               ' ON (gt2i18n.parent_id=gt2.id AND gt2i18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (d.id=gt2.model_id AND gt2.model="Document" AND d.type="' . $params['start_model_type'] . '" AND d.active=1 AND d.deleted_by=0)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
               '  ON (d_cstm.model_id=d.id AND d_cstm.var_id="' . $currency_var_id . '" AND (d_cstm.lang="" OR d_cstm.lang="' . $this->registry['lang'] . '"))' . "\n";
        if (!empty($ignore_tags)) {
            $sql .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                    ' ON (d.id=tm.model_id AND tm.model="Document" AND tm.tag_id IN ("' . implode('","', $ignore_tags) . '"))' . "\n";
        }
        $sql .= 'WHERE (ROUND(gt2.average_weighted_delivery_price, 2)+ROUND(gt2.free_field3, 2)+ROUND(gt2.free_field1, 2)+ROUND(gt2.free_field2, 2)+ROUND(gt2i18n.article_description, 2))>0.01 AND DATE_FORMAT(gt2.article_code, "%Y-%m-%d")<"' . date('Y-m-d') . '"' . "\n";
        if (!empty($ignore_tags)) {
            $sql .= ' AND tm.tag_id IS NULL' . "\n";
        }
        if (!empty($ignore_statuses)) {
            $sql .= ' AND CONCAT(d.status, "_", d.substatus) NOT IN ("' . implode('","', $ignore_statuses) . '")' . "\n";
        }
        $results = $this->registry['db']->GetAll($sql);

        // group the rows by model
        $rows_by_model = array();
        $filter_reasons = array();
        foreach ($results as $res) {
            if (!isset($rows_by_model[$res['model_id']])) {
                $filter_reasons[] = sprintf('(frr.link_to="%d" AND fir.currency="%s")', $res['model_id'], $res['currency']);
                $rows_by_model[$res['model_id']] = array();
            }
            $rows_by_model[$res['model_id']][] = $res['id'];
        }

        if (!empty($rows_by_model)) {
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

            $filters = array(
               'where' => array(
                   'd.id IN (' . implode(',', array_keys($rows_by_model)) . ')'
                ),
                'sort' => array('d.id ASC')
            );
            $documents = Documents::search($this->registry, $filters);
            $this->registry->set('get_old_vars', true, true);

            // get the related incomes reasons
            $sql = 'SELECT frr.link_to, frr.parent_id' . "\n" .
                   'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                   '  ON (fir.id=frr.parent_id AND fir.type="' . $settings['incomes_reason_type_id'] . '")' . "\n" .
                   'WHERE frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document" AND (' . implode(' OR ', $filter_reasons) . ')' . "\n";
            $documents_incomes_reason_realations = $this->registry['db']->GetAssoc($sql);

            // calculate the days in the current year
            $first_day_of_the_year = new DateTime(date('Y-01-01'));
            $last_day_of_the_year = new DateTime(date('Y-12-31'));
            $days_in_the_year = $first_day_of_the_year->diff($last_day_of_the_year)->days + 1;

            foreach ($documents as $document) {
                $document->unsanitize();
                $document->getVars();
                $gt2_var = $document->getGT2Vars();
                $old_document = clone $document;

                $interest_percent_daily = floatval($document->getVarValue($settings['doc_interest_percent'])) / $days_in_the_year;
                $total_extra_obligations = 0;
                foreach ($gt2_var['values'] as $row_key => $row_values) {
                    if (!in_array($row_values['id'], $rows_by_model[$document->get('id')])) {
                        continue;
                    }

                    // check the previous day
                    $temp_date = clone $current_day;
                    $days_multiplier = 1;
                    $temp_date->sub(new DateInterval('P1D'));
                    while(in_array($temp_date->format('w'), array(0, 6, 7))) {
                        if ($row_values['article_code'] < $temp_date->format('Y-m-d')) {
                            $days_multiplier++;
                        }
                        $temp_date->sub(new DateInterval('P1D'));
                    }

                    $current_obligations = sprintf('%.6f', ($row_values['average_weighted_delivery_price'] + $row_values['free_field1'] + $row_values['free_field2'] + $row_values['article_description'] + $row_values['free_field3']));
                    $extra_obligation = sprintf('%.2f', round((($current_obligations*$interest_percent_daily)/100) * $days_multiplier, 2));
                    $total_extra_obligations += $extra_obligation;

                    $gt2_var['values'][$row_key]['free_field4'] = sprintf('%.2f', round(floatval($row_values['free_field4']) + $extra_obligation, 2));
                    $gt2_var['values'][$row_key]['free_field5'] = sprintf('%.2f', round(floatval($row_values['free_field5']) + $extra_obligation, 2));
                    $gt2_var['values'][$row_key]['article_second_code'] = sprintf('%.2f', round(floatval($row_values['article_second_code'])+$extra_obligation, 2));
                    $gt2_var['values'][$row_key]['article_delivery_code'] = sprintf('%.2f', round(floatval($row_values['article_delivery_code']) + $extra_obligation, 2));
                }

                $document->set('grouping_table_2', $gt2_var, true);
                $document->calculateGT2();
                $document->set('table_values_are_set', true, true);

                $this->registry['db']->StartTrans();

                if ($document->saveGT2Vars()) {
                    $filters = array(
                        'where' => array('d.id="' . $document->get('id') . '"')
                    );
                    $new_document = Documents::searchOne($this->registry, $filters);
                    $new_document->getVars();

                    $history_params = array(
                        'model'       => $document,
                        'action_type' => 'edit',
                        'new_model'   => $new_document,
                        'old_model'   => $old_document
                    );

                    Documents_History::saveData($this->registry, $history_params);

                    // issue correction document
                    if (isset($documents_incomes_reason_realations[$new_document->get('id')])) {
                        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
                        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';

                        $incomes_reason_id = $documents_incomes_reason_realations[$new_document->get('id')];
                        $filters = array(
                            'where' => array('fir.id = \'' . $incomes_reason_id . '\'')
                        );
                        $income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                        $income_reason->getVars();
                        $gt2_var = $income_reason->getGT2Vars();

                        $old_income_reason = clone $income_reason;

                        foreach ($gt2_var['values'] as $key_row => $row_vals) {
                            if ($row_vals['article_id'] == $settings['contract_owed_sum_id']) {
                                $gt2_var['values'][$key_row]['price'] = sprintf('%.2f', round(($row_vals['price'] + $total_extra_obligations), 2));
                                break;
                            }
                        }

                        $income_reason->set('description', $params['id'], true);
                        $income_reason->set('grouping_table_2', $gt2_var, true);
                        $income_reason->calculateGT2();
                        $income_reason->set('table_values_are_set', true, true);

                        if ($income_reason->validate('edit') && $income_reason->saveCorrect($old_income_reason)) {
                            $filters = array('where' => array('fir.id = ' . $income_reason->get('id'),
                                                              'fir.annulled_by = 0'));
                            $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                            $new_incomes_reason->getGT2Vars();

                            Finance_Incomes_Reasons_History::saveData(
                                $this->registry,
                                array(
                                    'action_type' => ($old_income_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                                    'new_model' => $new_incomes_reason,
                                    'old_model' => $old_income_reason
                                )
                            );
                        } else {
                            $this->registry['db']->FailTrans();
                            $this->executionErrors[] = 'Penalty interests for document with id ' . $document->get('id') . ' were not corrected because the realated incomes reason cannot be corrected!';
                        }
                    }
                } else {
                    $this->executionErrors[] = 'Penalty interests for document with id ' . $document->get('id') . ' were not corrected because an error occured!';
                }

                $result = !$this->registry['db']->HasFailedTrans();
                $this->registry['db']->CompleteTrans();
                $this->updateAutomationHistory($params, $document, $result);
            }
        }

        $unique_errors = array_unique($this->executionErrors);
        $this->executionErrors = array_values($unique_errors);

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Automation to calculate penalty
     */
    public function calculateTaxPenaltyInterest($params) {
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        $settings = $this->settings;

        // find the taxes with owed dues
        $reasons_types = array_filter(preg_split('#\s*,\s*#', $settings['reasons_types']));

        // define ignore tags and statuses
        $ignore_tags = array_filter(preg_split('#\s*,\s*#', $settings['ignore_tags']));
        $ignore_statuses = array_filter(preg_split('#\s*,\s*#', $settings['ignore_statuses']));

        $sql = 'SELECT fir.id FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' AS fir' . "\n";
        if (!empty($ignore_tags)) {
            $sql .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tm' . "\n" .
                    ' ON (fir.id=tm.model_id AND tm.model="Finance_Incomes_Reason" AND tm.tag_id IN ("' . implode('","', $ignore_tags) . '"))' . "\n";
        }
        $sql .= 'WHERE fir.active=1 AND fir.annulled_by=0 AND fir.payment_status!="paid" AND fir.payment_status!="nopay" AND fir.date_of_payment<"' . date('Y-m-d') . '" AND fir.type IN ("' . implode('","', $reasons_types) . '")';
        if (!empty($ignore_tags)) {
            $sql .= ' AND tm.tag_id IS NULL' . "\n";
        }
        if (!empty($ignore_statuses)) {
            $sql .= ' AND CONCAT(fir.status, "_", fir.substatus) NOT IN ("' . implode('","', $ignore_statuses) . '")' . "\n";
        }
        $reasons_to_change = $this->registry['db']->GetCol($sql);

        $single_partition = 10;
        $offset = 0;
        $total_results = count($reasons_to_change);
        do {
            $current_part = array_slice($reasons_to_change, $offset, $single_partition);
            $this->processIncomesReasonsChange($current_part, $params);
            $offset += $single_partition;
        } while ($offset<$total_results);

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /**
     * Function to change the incomes reasons with the extra interest where needed
     */
    public function processIncomesReasonsChange($current_part, $params) {
        $filters = array(
            'where'      => array('fir.id IN (\'' . implode('\',\'', $current_part) . '\')'),
            'model_lang' => $this->registry['lang']
        );
        $income_reasons = Finance_Incomes_Reasons::search($this->registry, $filters);

        // find the related contract of each of the incomes reasons
        $sql = 'SELECT frr.parent_id, frr.link_to' . "\n" .
               'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               ' ON (frr.link_to=d.id AND d.type="' . $this->settings['doc_type_loan'] . '" AND frr.parent_id IN ("' . implode('","', $current_part) . '") AND frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to_model_name="Document")' . "\n";
        $reasons_docs_relations = $this->registry['db']->GetAssoc($sql);

        if (!count($reasons_docs_relations)) {
            return true;
        }

        // calculate the days in the current year
        $first_day_of_the_year = new DateTime(date('Y-01-01'));
        $last_day_of_the_year = new DateTime(date('Y-12-31'));
        $days_in_the_year = $first_day_of_the_year->diff($last_day_of_the_year)->days + 1;

        // get the documents
        $loans_list = array_unique(array_values($reasons_docs_relations));
        $filters = array(
            'where'      => array('d.id IN (\'' . implode('\',\'', $loans_list) . '\')'),
            'model_lang' => $this->registry['lang']
        );
        $loans = Documents::search($this->registry, $filters);

        $loans_list = array();
        foreach ($loans as $loan) {
            $loans_list[$loan->get('id')] = $loan;
        }
        unset($loans);

        foreach ($income_reasons as $income_reason) {
            if (!array_key_exists($income_reason->get('id'), $reasons_docs_relations)) {
                continue;
            }
            $income_reason->getVars();
            $gt2 = $income_reason->getGT2Vars();

            // calculate the interest
            $total = 0;
            $interest = 0;
            foreach ($gt2['values'] as $gt2_row) {
                if ($gt2_row['article_id'] != $this->settings['contract_owed_sum_id']) {
                    continue;
                }
                $total += floatval($gt2_row['subtotal_with_vat_with_discount']);
                $interest += floatval($gt2_row['surplus_value']);
            }
            $due_without_interest = $total - $interest;

            $unpaid_part = $due_without_interest - $income_reason->getFullPaidAmount();
            if ($unpaid_part < 0) {
                continue;
            }
            $old_reason = clone $income_reason;

            // define the amount percent
            $related_loan = $reasons_docs_relations[$income_reason->get('id')];

            $interest_percent_daily = floatval($loans_list[$related_loan]->getVarValue($this->settings['doc_interest_percent'])) / $days_in_the_year;

            $current_obligations = sprintf('%.6f', $unpaid_part);
            $extra_obligation = sprintf('%.2f', round(($current_obligations*$interest_percent_daily)/100, 2));
            if (floatval($extra_obligation) <= 0) {
                continue;
            }

            foreach ($gt2['values'] as $k => $gt2_row) {
                if ($gt2_row['article_id'] != $this->settings['contract_owed_sum_id']) {
                    continue;
                }
                $gt2['values'][$k]['surplus_value'] += $extra_obligation;

                $gt2['values'][$k]['discount_surplus_field'] = 'surplus_value';
            }

            $this->registry['db']->StartTrans();
            // set the new values in the model
            $income_reason->unsanitize();
            $income_reason->set('name', '', true);
            $income_reason->set('description', $params['id'], true);
            $income_reason->set('grouping_table_2', $gt2, true);
            $income_reason->calculateGT2();
            $income_reason->set('table_values_are_set', true, true);
            //$this->registry->set('get_old_vars', $get_old_vars, true);

            if ($income_reason->validate('edit') && $income_reason->saveCorrect($old_reason)) {
                $filters = array('where' => array('fir.id = ' . $income_reason->get('id'),
                                                  'fir.annulled_by = 0'));
                $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
                $new_incomes_reason->getGT2Vars();

                Finance_Incomes_Reasons_History::saveData(
                    $this->registry,
                    array(
                        'action_type' => ($old_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                        'new_model' => $new_incomes_reason,
                        'old_model' => $old_reason
                    )
                );
            } else {
                $link = sprintf('%s/index.php?%s=finance&%s=incomes_reasons&incomes_reasons=view&view=%d',
                    $this->registry['config']->getParam('crontab', 'base_host'),
                    $this->registry['module_param'],
                    $this->registry['controller_param'],
                    $income_reason->get('id')
                );

                $this->executionErrors[] = sprintf($this->i18n('error_tax_interest_add_failed'), $link, $income_reason->get('type_name'), $income_reason->get('num'));
                $this->registry['db']->FailTrans();
            }

            $this->registry['db']->CompleteTrans();
        }

        return true;
    }


    /**
     * Automation to distribute a payment to a repayment schedule after it is added
     */
    public function distributePaymentByRepaymentSchedule($params) {
        $settings = $this->settings;
        $registry = &$this->registry;
        $get_old_vars = $registry->get('get_old_vars');

        // get the payment model
        if ($params['model']->modelName == 'Finance_Payment') {
            $payment_id = $params['model']->get('id');
            $payment_num = $params['model']->get('num');
            $payment_date = $params['model']->get('status_modified');
            $payment = clone $params['model'];
        } else {
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
            $filters = array('where'      => array('fp.id="' . $params['model']->get('new_payment_id') . '"'),
                             'model_lang' => $this->registry['lang']);
            $payment = Finance_Payments::searchOne($this->registry, $filters);
            $payment_id = $params['model']->get('new_payment_id');
            $payment_num = $payment->get('num');
            $payment_date = $payment->get('status_modified');
        }

        // get the var that contains the currency
        $sql = 'SELECT `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name`="' . $settings['schedule_var_credit_currency'] . '"';
        $currency_var_id = $registry['db']->GetOne($sql);

        // check the existing repayment schedules
        $sql = 'SELECT d.id, d.full_num, SUM(ROUND(gt2.free_field5, 2)) as left_to_pay, d_cstm.value as contract_currency' . "\n" .
               'FROM '. DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' AS gt2' . "\n" .
               '  ON (gt2.model="Document" AND gt2.model_id=d.id)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
               '  ON (d_cstm.model_id=d.id AND d_cstm.var_id="' . $currency_var_id . '" AND (d_cstm.lang="" OR d_cstm.lang="' . $registry['lang'] . '"))' . "\n" .
               'WHERE d.type="' . $settings['document_repayment_schedule_type_id'] . '" AND CONCAT(d.status, "_", d.substatus)="' . $settings['status_active'] . '" AND d.active=1 AND d.deleted_by="0" AND d.customer="' . $payment->get('customer') . '"' . "\n" .
               'GROUP BY d.id' . "\n" .
               'ORDER BY d.id ASC' . "\n";
        $schedules_list = $registry['db']->GetAll($sql);

        $set_not_distributed_tag = false;
        $distribute = true;
        $convert_rate = 1;

        $distributed_sum_original = $payment->get('not_distributed_amount');
        $distributed_sum_converted = $payment->get('not_distributed_amount');
        $contract = null;

        // check for tag
        if (!empty($settings['payment_tag_skip_auto_distribution'])) {
            // check if the payment has tag to skip auto distribution
            $payment->getTags();
            if (in_array($settings['payment_tag_skip_auto_distribution'], $payment->get('tags'))) {
                // get the tag name
                $sql = 'SELECT `name` FROM ' . DB_TABLE_TAGS_I18N . ' WHERE `parent_id`="' . $settings['payment_tag_skip_auto_distribution'] . '" AND `lang`="' . $registry['lang'] . '"';
                $tag_name = $registry['db']->GetOne($sql);

                $registry['messages']->setWarning(sprintf($this->i18n('warning_payment_not_distributed_tag_skip_distribution'), $tag_name));
                $distribute = false;
            }
        }

        if ($distribute) {
            if (count($schedules_list) > 1) {
                // check the owed sum by the first contract
                $contract = reset($schedules_list);
                $convert_rate = Finance_Currencies::getRate($registry, $payment->get('currency'), $contract['contract_currency']);
                $distributed_sum_converted = $convert_rate * $payment->get('not_distributed_amount');
                if ($distributed_sum_converted > $contract['left_to_pay']) {
                    $distributed_sum_converted = $distributed_sum_converted - $contract['left_to_pay'];
                    // do not distribute if there are more than one active contract and the sum is greater than the owed amount of the first contract
                    $contracts_nums = array();
                    foreach ($schedules_list as $sch_lst) {
                        $contracts_nums[] = $sch_lst['full_num'];
                    }

                    $sql = 'SELECT IF(is_company, eik, ucn) as num' . "\n" .
                           'FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $payment->get('customer') . '"' . "\n";
                    $customer_ucn = $this->registry['db']->GetOne($sql);
                    $report_link = sprintf(
                        '%s?%s=reports&reports=generate_report&report_type=credilink_customer_file&client=%d&client_autocomplete=%s',
                        $this->registry['config']->getParam('crontab', 'base_host'),
                        $this->registry['module_param'],
                        $payment->get('customer'),
                        urlencode(sprintf('%s (%s)', $payment->get('customer_name'), $customer_ucn)),
                        $payment->get('customer_name')
                    );

                    $registry['messages']->setError(sprintf($this->i18n('error_payment_is_not_distributed_plural'), implode(',', $contracts_nums)));
                    $set_not_distributed_tag = true;
                    $distribute = false;
                }
            } elseif (count($schedules_list) == 1) {
                $contract = reset($schedules_list);
                $convert_rate = Finance_Currencies::getRate($registry, $payment->get('currency'), $contract['contract_currency']);
                $distributed_sum_converted = $convert_rate * $payment->get('not_distributed_amount');

                if ($distributed_sum_converted > $contract['left_to_pay']) {
                    $distributed_sum_converted = $distributed_sum_converted - $contract['left_to_pay'];
                    // the payment will be partially distributed
                    $set_not_distributed_tag = true;

                    $sql = 'SELECT IF(is_company, eik, ucn) as num' . "\n" .
                           'FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $payment->get('customer') . '"' . "\n";
                    $customer_ucn = $this->registry['db']->GetOne($sql);
                    $report_link = sprintf(
                        '%s?%s=reports&reports=generate_report&report_type=credilink_customer_file&client=%d&client_autocomplete=%s',
                        $this->registry['config']->getParam('crontab', 'base_host'),
                        $this->registry['module_param'],
                        $payment->get('customer'),
                        urlencode(sprintf('%s (%s)', $payment->get('customer_name'), $customer_ucn)),
                        $payment->get('customer_name')
                    );
                    $registry['messages']->setWarning(sprintf($this->i18n('warning_payment_is_not_fully_distributed'), $contract['full_num'], $report_link));
                }
            } else {
                $distribute = false;
            }
        }

        if ($distribute) {
            // if the distribution has be executed, check if there is more than one relаted incomes reason
            $incomes_reason_check_types = array_filter(preg_split('/\s*,\s*/', $settings['incomes_reason_check_types']));
            $incomes_reason_check_types[] = $settings['incomes_reason_type_id'];

            $sql = 'SELECT COUNT(id)' . "\n" .
                   'FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . "\n" .
                   'WHERE `customer`="' . $payment->get('customer') . '" AND `type` IN ("' . implode('","', $incomes_reason_check_types) . '") AND `payment_status` IN ("unpaid", "partial") AND `active`=1 AND `annulled_by`=0' . "\n";
            $unfinished_incomes_reasons = $this->registry['db']->getOne($sql);

            if ($unfinished_incomes_reasons > 1) {
                $set_not_distributed_tag = true;
                $distribute = false;
                $registry['messages']->setWarning($this->i18n('warning_payment_not_distributed_multiple_incomes_reasons'));
            }
        }

        $registry['db']->StartTrans();
        if ($set_not_distributed_tag) {
            // set tag to the payment
            $payment->getTags();
            if (!in_array($settings['payment_tag_not_distributed_payment'], $payment->get('tags'))) {
                $payment->getModelTagsForAudit();
                $old_payment = clone($payment);
                $payment->set('tags', array_merge($payment->get('tags'), array($settings['payment_tag_not_distributed_payment'])), true);
                $payment->unsanitize();
                if ($payment->updateTags(array('skip_permissions' => true))) {
                    // get the updated payment
                    $filters = array('where' => array('fp.id="' . $payment->get('id') . '"'),
                                     'model_lang' => $this->registry['lang']);
                    $new_payment = Finance_Payments::searchOne($this->registry, $filters);
                    $new_payment->getModelTagsForAudit();
                    $new_payment->sanitize();
                    Finance_Payments_History::saveData($this->registry, array('model' => $new_payment, 'action_type' => 'tag', 'new_model' => $new_payment, 'old_model' => $old_payment));
                    $payment = clone($new_payment);

                    if (!empty($settings['email_id']) && !empty($settings['users'])) {
                        $users_to_receive_emails = preg_split('#\s*,\s*#', $settings['users']);
                        $users_to_receive_emails = array_filter($users_to_receive_emails);

                        if (!empty($users_to_receive_emails)) {
                            $sql = 'SELECT u.id, u.email, ui18n.firstname, ui18n.lastname' . "\n" .
                                   'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                                   'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                                   '  ON (ui18n.parent_id=u.id AND ui18n.lang="' . $this->registry['lang'] . '")' . "\n" .
                                   'WHERE u.id IN ("' . implode('","', $users_to_receive_emails) . '")' . "\n";
                            $users_data = $this->registry['db']->GetAll($sql);

                            // get the ucn of the selected customer
                            $sql = 'SELECT IF(is_company, eik, ucn) as num' . "\n" .
                                   'FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $payment->get('customer') . '"' . "\n";
                            $customer_ucn = $this->registry['db']->GetOne($sql);

                            // send an email
                            require_once PH_MODULES_DIR . 'emails/models/emails.factory.php';
                            $filters_mail = array('where' => array('e.id = ' . $settings['email_id']), 'sanitize' => true);
                            $email = Emails::searchOne($this->registry, $filters_mail);
                            $config = $this->registry['config'];

                            foreach ($users_data as $user_dat) {
                                $mail = new Mailer($this->registry);
                                $mail->template['sender'] = $config->getParam('emails', 'from_email');
                                $mail->template['from_name'] = $config->getParam('emails', 'from_name');
                                $mail->template['replyto_email'] = $config->getParam('emails', 'replyto_email') ?: $mail->template['from_email'];
                                $mail->template['replyto_name'] = $config->getParam('emails', 'replyto_name') ?: $mail->template['from_name'];
                                $mail->template['subject'] = $email->get('subject');
                                $mail->template['body'] = $email->get('body');

                                $mail->template['recipient'] = $user_dat['email'];
                                $mail->template['names_to'] = sprintf('%s %s', $user_dat['firstname'], $user_dat['lastname']);
                                $messages_link = sprintf(
                                    '<a target="_blank" href="%s?%s=reports&reports=generate_report&report_type=credilink_customer_file&client=%d&client_autocomplete=%s">%s</a>',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'],
                                    $payment->get('customer'),
                                    urlencode(sprintf('%s (%s)', $payment->get('customer_name'), $customer_ucn)),
                                    $payment->get('customer_name')
                                );
                                $mail->placeholder->add('customer_name_report_link', $messages_link);
                                $mail->placeholder->add('user_firstname', $user_dat['firstname']);
                                $mail->placeholder->add('user_lastname', $user_dat['lastname']);
                                $mail->templateName = 'custom_template';
                                $mail->send();
                            }
                        }
                    }
                } else {
                    $registry['messages']->setError($this->i18n('error_payment_tag_undistributed_failed'));
                    $registry['db']->FailTrans();
                }
            }
        }

        if ($distribute) {
            // check for reason in the currency of the contract
            $incomes_reason_exist = $this->checkExistingFinIncomesReason($contract['id'], $contract['contract_currency'], $this->settings['incomes_reason_type_id']);
            if ($incomes_reason_exist) {
                // fictive setting for this incomes reason
                $this->settings['incomes_reason_certain_id'] = $incomes_reason_exist;
            }

            $operation_result = $this->processIncomesReasons($payment, $contract['id'], $distributed_sum_original, $contract['contract_currency'], $params['id']);
            if ($operation_result['error']) {
                $registry['db']->FailTrans();
                foreach ($operation_result['messages'] as $msg) {
                    $registry['messages']->setError($msg);
                }
            }

            // AUTO DISTRIBUTE
            if ($payment->saveAutoBalance()) {
                // remove the tag not distributed payment if such is set
                $payment->getTags();
                if (in_array($settings['payment_tag_not_distributed_payment'], $payment->get('tags')) && !$set_not_distributed_tag) {
                    $payment->getModelTagsForAudit();
                    $tags = $payment->get('tags');
                    $old_payment = clone($payment);
                    foreach ($tags as $k => $t) {
                        if ($t == $settings['payment_tag_not_distributed_payment']) {
                            unset($tags[$k]);
                            break;
                        }
                    }
                    $payment->set('tags', array_values($tags), true);
                    $payment->unsanitize();

                    if ($payment->updateTags(array('skip_permissions' => true))) {
                        // get the updated document
                        $filters = array('where'      => array('fp.id="' . $payment->get('id') . '"'),
                                         'model_lang' => $this->registry['lang']);
                        $new_payment = Finance_Payments::searchOne($this->registry, $filters);
                        $new_payment->getModelTagsForAudit();
                        $new_payment->sanitize();
                        Finance_Payments_History::saveData($this->registry, array('model' => $new_payment, 'action_type' => 'tag', 'new_model' => $new_payment, 'old_model' => $old_payment));
                        $payment = clone($new_payment);
                    } else {
                        $registry['messages']->setError($this->i18n('error_payment_tag_undistributed_remove_failed'));
                        $registry['db']->FailTrans();
                    }
                }

                // query to get the needed data
                $sql = 'SELECT d.id, fb.paid_amount, fb.paid_currency as currency' . "\n" .
                       'FROM ' . DB_TABLE_FINANCE_BALANCE . ' as fb' . "\n" .
                       'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                       '  ON (fir.id=fb.paid_to AND fir.type="' . $settings['incomes_reason_type_id'] . '")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                       '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.parent_id=fir.id AND frr.link_to_model_name="Document")' . "\n" .
                       'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                       '  ON (frr.link_to=d.id AND d.type="' . $settings['document_repayment_schedule_type_id'] . '" AND d.active=1 AND d.deleted_by=0 AND d.substatus!="' . $settings['document_substatus_proceeding'] . '")' . "\n" .
                       'WHERE fb.parent_model_name="Finance_Payment" AND fb.paid_to_model_name="Finance_Incomes_Reason" AND fb.parent_id="' . $payment_id . '" AND fb.paid_amount>0' . "\n";
                $schedules_payments = $registry['db']->GetAssoc($sql);

                // get the repayment schedules
                require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
                require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

                $filters = array(
                    'where' => array(
                        'd.id IN ("' . implode('","', array_keys($schedules_payments)) . '")'
                    )
                );
                $schedules = Documents::search($registry, $filters);

                $registry->set('get_old_vars', true, true);
                $result = true;

                foreach ($schedules as $schedule) {
                    $distribution_result = $this->distributePaymentSumByContractItems($schedule, $payment, $schedules_payments[$schedule->get('id')]['paid_amount'], false);
                    $msg_method = 'setMessage';
                    if ($distribution_result['error']) {
                        $registry['db']->FailTrans();
                        $msg_method = 'setError';
                    }
                    foreach ($distribution_result['messages'] as $msg) {
                        $registry['messages']->$msg_method($msg);
                    }
                }
            } else {
                $registry['messages']->setError($this->i18n('error_payment_autodistribution_failed'));
                $registry['db']->FailTrans();
            }
        }
        $registry['db']->CompleteTrans();

        // insert messages in the session
        $registry['messages']->insertInSession($registry);
        $registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * Function to check if the repayment schedule is paid at full (and change tag and statuses if it is paid at full)
     *
     * @param object $contract - the contract which have to be checked
     * @param array $params - array of params containing status, the id of the type incomes reason and other custom options for the status change
     *
     * @return bool - result of the operation
     */
    public function checkFullPayment($contract, $params) {
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $completed_action = true;

        $gt2 = $contract->getGT2Vars();
        $full_paid = true;
        foreach ($gt2['values'] as $row_id => $gt2_data) {
            if ($gt2_data['free_field5'] > 0) {
                $full_paid = false;
            }
        }

        if ($full_paid && !$this->defineCreditLine($contract)) {
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';

            // change status
            $old_contract = clone $contract;

            @list($current_status, $current_substatus) = explode('_', $params['status']);
            $current_substatus_name = '';

            if ($current_substatus) {
                $query = 'SELECT name FROM ' . DB_TABLE_DOCUMENTS_STATUSES . "\n" .
                         'WHERE id = ' . $current_substatus . ' AND lang = "' . $contract->get('model_lang') . '"';
                $current_substatus_name = $this->registry['db']->GetOne($query);
            }

            $contract->set('status', $current_status, true);
            $contract->set('substatus', ($current_substatus ? $params['status'] : ''), true);
            $contract->set('substatus_name', $current_substatus_name, true);

            if ($contract->setStatus()) {
                Documents_History::saveData($this->registry, array('model' => $contract, 'action_type' => 'status', 'new_model' => $contract, 'old_model' => $old_contract));

                $assoc_vars = $contract->getAssocVars();
                $add_vars_query_values = array();

                if (isset($params['full_payment_var']) && isset($assoc_vars[$params['full_payment_var']])) {
                    $add_vars_query_values[] = sprintf('("%d", "%d", "1", "%s", NOW(), %d, NOW(), %d, "%s")', $contract->get('id'), $assoc_vars[$params['full_payment_var']]['id'], date('Y-m-d'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[$params['full_payment_var']]['multilang'] ? $this->registry['lang'] : ''));
                }

                if (isset($params['repayment_status']) && isset($assoc_vars[$params['repayment_status']])) {
                    $repayment_status = '';
                    $gt2 = $contract->getGT2Vars();
                    $last_row = end($gt2['values']);
                    $row_before_the_last = prev($gt2['values']);
                    if (!$row_before_the_last) {
                        $period_start = $contract->get('date');
                    } else {
                        $period_start = General::strftime('%Y-%m-%d', strtotime('+1 day', strtotime($row_before_the_last['article_code'])));
                    }

                    if (date('Y-m-d')<$period_start && isset($params['repayment_status_in_advance'])) {
                        $repayment_status = $params['repayment_status_in_advance'];
                    } elseif ($period_start<=date('Y-m-d') && isset($params['repayment_status_in_time'])) {
                        $repayment_status = $params['repayment_status_in_time'];
                    }

                    if ($repayment_status) {
                        $add_vars_query_values[] = sprintf('("%d", "%d", "1", "%d", NOW(), %d, NOW(), %d, "%s")', $contract->get('id'), $assoc_vars[$params['repayment_status']]['id'], $repayment_status, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[$params['repayment_status']]['multilang'] ? $this->registry['lang'] : ''));
                    }
                }

                if (!empty($add_vars_query_values)) {
                    // prepare the edit query
                    $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                           'VALUES ' . implode(',' . "\n", $add_vars_query_values) . "\n" .
                           'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                    $this->registry['db']->Execute($sql);

                    $old_contract = clone $contract;
                    $old_contract->sanitize();

                    $filters = array('where'      => array('d.id="' . $contract->get('id') . '"'),
                                     'model_lang' => $contract->get('model_lang'));
                    $new_contract = Documents::searchOne($this->registry, $filters);
                    $new_contract->getVars();

                    $history_params = array(
                        'model'       => $new_contract,
                        'action_type' => 'edit',
                        'new_model'   => $new_contract,
                        'old_model'   => $old_contract
                    );

                    Documents_History::saveData($this->registry, $history_params);
                }
            } else {
                $completed_action = false;
            }
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);
        return $completed_action;
    }

    /*
     * Function to create the incomes reason to be related to the loan contract
     */
    public function createContractIncomesReason($params) {
        $errors = array();

        // CREATE THE INCOMES REASON THAT WILL BE CONNECTED TO THIS
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';

        $income_reason = new Finance_Incomes_Reason($this->registry);
        $old_income_reason = clone $income_reason;
        $old_income_reason->sanitize();
        $income_reason->set('type', $params['type'], true);
        $income_reason->set('group', 1, true);
        $income_reason->set('status', 'finished', true);
        $income_reason->set('customer', $params['customer'], true);
        $income_reason->set('issue_date', $params['issue_date'], true);
        $income_reason->set('company', $params['company'], true);
        $income_reason->set('office', $params['office'], true);
        $income_reason->set('payment_type', $params['payment_type'], true);
        $income_reason->set('container_id', $params['container_id'], true);
        $income_reason->set('total', $params['total'], true);
        $income_reason->set('total_without_discount', $params['total'], true);
        $income_reason->set('total_with_vat', $params['total'], true);
        $income_reason->set('fiscal_total', $params['total'], true);
        $income_reason->set('currency', $params['currency'], true);
        // fin_field_1 contains the id of the related reason (reason from the same type but in different currency)
        $income_reason->set('fin_field_1', $params['parent_reason'], true);

        //get the type
        $filters = array('where' => array('fdt.id=' . $params['type']), 'sanitize' => true);

        //get the event type reminder
        $fin_doc_type = Finance_Documents_Types::searchOne($this->registry, $filters);
        $income_reason->set('date_of_payment_count', $fin_doc_type->get('default_date_of_payment_count'), true);
        $income_reason->set('date_of_payment_period_type', $fin_doc_type->get('default_date_of_payment_period_type'), true);
        $income_reason->set('date_of_payment_point', $fin_doc_type->get('default_date_of_payment_point'), true);
        if (isset($params['name'])) {
            $income_reason->set('name', $params['name'], true);
        }

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $gt2_new_model = $income_reason->getGT2Vars();

        $row_values = array();
        $row_values['quantity'] = 1;
        $row_values['article_measure_name'] = 1;
        $row_values['free_field2'] = '';
        $row_values['price'] = $params['total'];
        $row_values['article_id'] = $params['article_id'];

        $sql = 'SELECT `name` FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE `parent_id`="' . $params['article_id'] . '" AND `lang`="' . $this->registry['lang'] . '"';
        $row_values['article_name'] = $this->registry['db']->GetOne($sql);

        $gt2_new_model['values'][] = $row_values;
        $income_reason->set('grouping_table_2', $gt2_new_model, true);
        $income_reason->calculateGT2();
        $income_reason->set('table_values_are_set', true, true);

        $this->registry['db']->StartTrans();
        if ($income_reason->save()) {
            // Write history
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.audit.php';
            $filters = array(
                'where'      => array('fir.id = \'' . $income_reason->get('id') . '\''),
                'model_lang' => $income_reason->get('model_lang')
            );
            $new_income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $new_income_reason->getVars();

            Finance_Incomes_Reasons_History::saveData(
                $this->registry,
                array(
                    'model'       => $new_income_reason,
                    'action_type' => 'add',
                    'new_model'   => $income_reason,
                    'old_model'   => $old_income_reason
                )
            );

            $sql = 'INSERT INTO `fin_reasons_relatives` (`parent_id`, `parent_model_name`, `link_to`, `link_to_model_name`, `rows_links`, `changes`)' . "\n" .
                   '  VALUES ("%d", "%s", "%d", "%s", "", "")' . "\n";
            $sql = sprintf($sql, $new_income_reason->get('id'), 'Finance_Incomes_Reason', $params['document_id'], 'Document');
            $this->registry['db']->Execute($sql);

            if ($this->registry['db']->HasFailedTrans()) {
                $errors[] = 'relatives';
            }
        } else {
            $errors[] = 'add';
        }
        $this->registry->set('get_old_vars', $get_old_vars, true);

        $this->registry['db']->CompleteTrans();
        return $errors;
    }

    /*
     * Function to issue income reason correction
     */
    public function issueFinIncomeReasonCorrection ($income_reason_id, $change_sum, $automation_id, $nom_id) {
        if ($change_sum<0.01 && $change_sum>-0.01) {
            // if the change is less than 0.01 skip the try
            return true;
        }
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.history.php';
        $lang_files = array(PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance.ini',
                            PH_MODULES_DIR . 'finance/i18n/' . $this->registry['lang'] . '/finance_incomes_reasons.ini');
        $this->loadI18NFiles($lang_files);

        $result = true;
        $filters = array(
            'where' => array('fir.id = \'' . $income_reason_id . '\'')
        );
        $income_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $income_reason->getVars();
        $gt2_var = $income_reason->getGT2Vars();

        $old_income_reason = clone $income_reason;

        foreach ($gt2_var['values'] as $key_row => $row_vals) {
            if ($row_vals['article_id'] == $nom_id) {
                $new_price = round(($row_vals['price'] + $change_sum), 2);
                $gt2_var['values'][$key_row]['price'] = sprintf('%.2f', ($new_price<0 ? 0 : $new_price));
                if ($new_price<0 && $gt2_var['values'][$key_row]['surplus_value'] > 0) {
                    // check for interest if any sum has left
                    $leftover = round(($gt2_var['values'][$key_row]['surplus_value'] + $new_price), 2);
                    $gt2_var['values'][$key_row]['surplus_value'] = sprintf('%.2f', ($leftover<0 ? 0 : $leftover));
                }
                break;
            }
        }

        $income_reason->set('description', $automation_id, true);
        $income_reason->set('grouping_table_2', $gt2_var, true);
        $income_reason->calculateGT2();
        $income_reason->set('table_values_are_set', true, true);
        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($income_reason->validate('edit') && $income_reason->saveCorrect($old_income_reason)) {
            $filters = array('where' => array('fir.id = ' . $income_reason->get('id'),
                                              'fir.annulled_by = 0'));
            $new_incomes_reason = Finance_Incomes_Reasons::searchOne($this->registry, $filters);
            $new_incomes_reason->getGT2Vars();

            Finance_Incomes_Reasons_History::saveData(
                $this->registry,
                array(
                    'action_type' => ($old_income_reason->get('correction_id') ? 'addcorrect' : 'edit'),
                    'new_model' => $new_incomes_reason,
                    'old_model' => $old_income_reason
                )
            );
        } else {
            $result = false;
            $this->registry['db']->FailTrans();
        }

        return $result;
    }

    /**
     * Build the payments table
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function buildPaymentsTable($params) {
        // validate if the table can be build
        $result = false;
        $model = $params['model'];
        $status_key = sprintf('%s_%d', $model->get('status'), $model->get('substatus'));
        $this->defineCreditLine($model);

        $clear_table = true;
        if ($this->is_credit_line && $this->settings['contract_substatus_active'] == $model->get('substatus')) {
            $clear_table = false;
        } elseif ($status_key != 'opened_0') {
            $this->registry['messages']->setWarning($this->i18n('warning_repayment_plan_not_calculated_status_passed'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $not_completed_fields = $this->validateBuildPaymentsTable($model);
        if (!empty($not_completed_fields)) {
            $this->registry['messages']->setError(sprintf($this->i18n('error_repayment_plan_missing_completed_required_filed'), implode(', ', $not_completed_fields)));
        } else {
            $this->automation_id = $params['id'];
            $result = $this->calculatePayments($model, '', $clear_table);

            if ($result) {
                $this->registry['messages']->setMessage($this->i18n('repayment_plan_created_successfully'));
            } else {
                foreach ($this->executionErrors as $err) {
                    $this->registry['messages']->setError($err);
                }
                // clear the errors, so we will not mess the other functionalities
                $this->executionErrors = array();
            }
        }

        $this->registry['messages']->insertInSession($this->registry);
        $this->registry->set('get_old_vars', $get_old_vars, true);

        return $result;
    }

    /**
     * Calculate Year Interest Percent (GPR)
     *
     * @param array $params - array of automation settings (fetched from the DB)
     * @return bool - result of the operation
     */
    public function caulculateYearInterestPercent($params) {
        // validate if the table can be build
        $model = $params['model'];

        // get the settings from buildPaymentsTable automation
        $this->getCustomAutomationSettings('createPaymentPlan', 'credits');

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $assoc_vars = $model->getAssocVars();
        $gt2_var = $model->getGT2Vars();
        $old_model = clone $model;

        $gpr = $this->calculateGPR($gt2_var['values'], $model->get('id'));

        // set additional vars
        $update_additional_vars = array();
        $update_additional_vars[] = sprintf('("%d", "%d", 1, "%s", NOW(), %d, NOW(), %d, "%s")',
                                    $model->get('id'),
                                    $assoc_vars[$this->settings['credit_gpr']]['id'],
                                    sprintf('%.4f', round($gpr, 4)),
                                    $this->registry['currentUser']->get('id'),
                                    $this->registry['currentUser']->get('id'),
                                    ($assoc_vars[$this->settings['credit_gpr']]['multilang'] ? $this->registry['lang'] : ''));

        $this->registry['db']->StartTrans();

        // update the other additional vars
        $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
               'VALUES ' . implode(",\n", $update_additional_vars) . "\n" .
               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
        $this->registry['db']->Execute($sql);

        // save history for the change
        $filters = array(
            'where' => array('d.id="' . $model->get('id') . '"')
        );
        $new_model = Documents::searchOne($this->registry, $filters);
        $new_model->getVars();
        $new_model->getGT2Vars();

        $history_params = array(
            'model'       => $new_model,
            'action_type' => 'edit',
            'new_model'   => $new_model,
            'old_model'   => $old_model
        );
        Documents_History::saveData($this->registry, $history_params);

        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();


        // check if the customer is company or person and evaluate
        $sql = 'SELECT `is_company` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $model->get('customer') . '"' . "\n";
        $customer_is_company = $this->registry['db']->GetOne($sql);

        $gpr_warning = '';
        if (!$customer_is_company) {
            $base_interest_rate = 0;
            if (floatval($assoc_vars[$this->settings['credit_interest_rate_fixed']]['value'])) {
                $base_interest_rate = floatval($assoc_vars[$this->settings['credit_interest_rate_fixed']]['value']);
            } elseif (floatval($assoc_vars[$this->settings['credit_interest_rate_floating']]['value'])) {
                if (floatval($assoc_vars[$this->settings['credit_interest_rate_floating']]['value']) < floatval($assoc_vars[$this->settings['credit_interest_min_annual']]['value'])) {
                    $base_interest_rate = floatval($assoc_vars[$this->settings['credit_interest_min_annual']]['value']);
                } else {
                    $base_interest_rate = floatval($assoc_vars[$this->settings['credit_interest_rate_floating']]['value']);
                }
            }
            if ($gpr > ($base_interest_rate*5)) {
                $gpr_warning = sprintf($this->i18n('warning_repayment_plan_creation_failed_gpr_too_high'), $gpr, $base_interest_rate);
            }
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);

        if ($result) {
            $this->registry['messages']->setMessage($this->i18n('credilink_gpr_recalculated_successfully'));
            if (!empty($gpr_warning)) {
                $this->registry['messages']->setWarning($gpr_warning);
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_gpr_recalculated_failed'));
        }
        $this->registry['messages']->insertInSession($this->registry);

        return $result;
    }

    /*
     * Representation of PMT formula from Excel
     * @param float $interest_rate - Interest rate.
     * @param integer $months      - loan length in months.
     * @param float $loan_amount   - loan amount.
     */
    function calcPMT($interest_rate, $months, $loan_amount) {
        $amount = (-0 -$loan_amount * pow(1 + $interest_rate, $months)) / (1 + $interest_rate * 0) / ((pow(1 + $interest_rate, $months) - 1) / $interest_rate);
        return (-1) * $amount;
    }

    /*
     * Function to check if incomes reason in the required currency exists
     *
     * @param int $document_id - the id of the contract
     * @param string $currency - the currency of the contract
     * @param int incomes_reason_type - the id of the incomes reason type
     */
    function checkExistingFinIncomesReason($document_id, $currency, $incomes_reason_type) {
        $sql = 'SELECT fir.id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
               '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to=d.id AND frr.link_to_model_name="Document")' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
               '  ON (fir.id=frr.parent_id AND fir.type="' . $incomes_reason_type . '" AND fir.currency="' . $currency . '" AND fir.annulled_by=0 AND fir.active=1)' . "\n" .
               'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS_I18N . ' AS firi18n' . "\n" .
               '  ON (firi18n.parent_id=fir.id AND firi18n.lang="' . $this->registry['lang'] . '")' . "\n" .
               'WHERE d.id="' . $document_id . '"' . "\n";
        if (!empty($this->settings['incomes_reason_certain_id'])) {
            // search by certain reason (as fir.id) or related to certain incomes reason (firi18n.fin_field_1)
            $sql .= sprintf(' AND (fir.id="%s" OR firi18n.fin_field_1="%s")', $this->settings['incomes_reason_certain_id'], $this->settings['incomes_reason_certain_id']);
        }
        return $this->registry['db']->GetOne($sql);
    }

    /*
     * Automation to change the floating interest percent in the loan contracts where it is used
     */
    function changeFloatingInterestPercent($params) {
        // validate if the table can be build
        $err_messages = array();
        $model = $params['model'];
        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);
        $model->getVars();
        $assoc_vars = $model->getAssocVars();

        $index_percent = $assoc_vars[$this->settings['nom_interest_percent']]['value'];
        $index_date = $assoc_vars[$this->settings['nom_interest_date']]['value'];

        if (!$index_percent || !$index_date) {
            $this->registry['messages']->setWarning($this->i18n('warning_updating_document_floating_index'));
            $this->registry['messages']->insertInSession($this->registry);
            return false;
        }

        // get the needed addtional vars from the document
        $vars = array($this->settings['document_type_index'], $this->settings['document_index_date'], $this->settings['document_index_percent'],
                      $this->settings['document_index_overpercent'], $this->settings['document_percent_yearly'], $this->settings['document_percent_monthly'],
                      $this->settings['document_percent_max']);

        // get the var that contains the currency
        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . ' WHERE `model`="Document" AND `name` IN ("' . implode('","', $vars) . '") AND `model_type`="' . $this->settings['document_type_id'] . '"';
        $vars = $this->registry['db']->GetAssoc($sql);

        // get the statuses
        $document_statuses = preg_split('#\s*,\s*#', $this->settings['document_statuses']);
        $document_statuses = array_filter($document_statuses);

        // get the needed documents
        $sql = 'SELECT d.id' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm' . "\n" .
               ' ON (d_cstm.model_id=d.id AND d_cstm.var_id="' . $vars[$this->settings['document_type_index']] . '" AND d_cstm.value="' . $model->get('id') . '")' . "\n" .
               'WHERE d.type="' . $this->settings['document_type_id'] . '" AND d.substatus IN ("' . implode('","', $document_statuses) . '") AND d.deleted_by=0 AND d.active=1' . "\n";
        $documents_ids = $this->registry['db']->GetCol($sql);

        if (!empty($documents_ids)) {
            $filters = array(
               'where' => array(
                   'd.id IN (' . implode(',', $documents_ids) . ')'
                ),
                'sort' => array('d.id ASC')
            );
            $documents = Documents::search($this->registry, $filters);

            $min_max_percents_updated_documents = array();
            foreach ($documents as $document) {
                $min_max_percent_set = false;
                $document->getVars();
                $old_document = clone $document;

                $assoc_vars = $document->getAssocVars();

                $update_additional_vars = array();
                $update_additional_vars_template = '("%d", "%d", 1, "%s", NOW(), ' . $this->registry['currentUser']->get('id') . ', NOW(), ' . $this->registry['currentUser']->get('id') . ', "%s")';

                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $document->get('id'),
                    $assoc_vars[$this->settings['document_index_date']]['id'],
                    $index_date,
                    ($assoc_vars[$this->settings['document_index_date']]['multilang'] ? $this->registry['lang'] : ''));
                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $document->get('id'),
                    $assoc_vars[$this->settings['document_index_percent']]['id'],
                    $index_percent,
                    ($assoc_vars[$this->settings['document_index_percent']]['multilang'] ? $this->registry['lang'] : ''));
                $year_percent = $index_percent + floatval($assoc_vars[$this->settings['document_index_overpercent']]['value']);
                $max_percent = ($assoc_vars[$this->settings['document_percent_max']]['value'] ?: '');
                $min_percent = ($assoc_vars[$this->settings['document_percent_min']]['value'] ?: '');
                if ($max_percent && $year_percent > $max_percent) {
                    $year_percent = $max_percent;
                    $min_max_percent_set = true;
                } elseif ($min_percent && $year_percent < $min_percent) {
                    $year_percent = $min_percent;
                    $min_max_percent_set = true;
                }

                $source_fields = General::parseSettings($assoc_vars[$this->settings['document_percent_yearly']]['source']);
                $format = '%.3f';
                if (!empty($source_fields['format'])) {
                    $format = $source_fields['format'];
                }
                $round_value = preg_replace('#^\%\.([0-9])+.*$#', '$1', $format);
                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $document->get('id'),
                    $assoc_vars[$this->settings['document_percent_yearly']]['id'],
                    sprintf($format, round($year_percent, $round_value)),
                    ($assoc_vars[$this->settings['document_percent_yearly']]['multilang'] ? $this->registry['lang'] : ''));

                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $document->get('id'),
                    $assoc_vars[$this->settings['document_percent_monthly']]['id'],
                    sprintf('%.2f', round($year_percent/12, 2)),
                    ($assoc_vars[$this->settings['document_percent_monthly']]['multilang'] ? $this->registry['lang'] : ''));

                $this->registry['db']->StartTrans();

                // update the other additional vars
                $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                       'VALUES ' . implode(",\n", $update_additional_vars) . "\n" .
                       'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($sql);

                if ($this->registry['db']->HasFailedTrans()) {
                    $document_url = sprintf('%s://%s%s?%s=documents&documents=view&view=%d',
                                            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                            $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                            $document->get('id'));
                    $err_messages[] = sptinf($this->i18n('error_updating_document_floating_index'), $document_url, $document->get('name'));
                    $this->registry['db']->FailTrans();
                } else {
                    // write history
                    $filters = array(
                        'where' => array('d.id="' . $document->get('id') . '"')
                    );
                    $new_document = Documents::searchOne($this->registry, $filters);
                    $new_document->getVars();
                    $new_document->getGT2Vars();

                    $history_params = array(
                        'model'       => $new_document,
                        'action_type' => 'edit',
                        'new_model'   => $new_document,
                        'old_model'   => $old_document
                    );
                    Documents_History::saveData($this->registry, $history_params);

                    require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
                    $automations_controller = new Credits_Automations_Controller($this->registry);
                    $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');

                    // load automations i18n files
                    $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
                        false, '', '', true);
                    if (!empty($i18n_files)) {
                        $this->registry['translater']->loadFile($i18n_files);
                    }
                    $adapter = $automations_controller->getAdapter();
                    $adapter->automation_params = $params;
                    $adapter->current_model = clone $new_document;
                    $adapter->current_model_vars = $adapter->current_model->getAssocVars();
                    $adapter->calculate_from_date = $index_date;
                    $adapter->reason_for_change = $this->automation_id;
                    $adapter->mid_contract_recalculation = true;
                    $calculate_payment_result = $adapter->calculateInvestmentCreditPayments();
                    $adapter->mid_contract_recalculation = false;

                    if (!$calculate_payment_result) {
                        $err_messages = array_merge($err_messages, $this->executionErrors);

                        // clear the errors so we will not mess the other functionalities
                        $this->executionErrors = array();
                        $this->registry['db']->FailTrans();
                    } elseif ($min_max_percent_set) {
                        $min_max_percents_updated_documents[] = array(
                            'id'  => $document->get('id'),
                            'num' => $document->get('name')
                        );
                    }

                }
                $this->registry['db']->CompleteTrans();
            }
        }

        if (!empty($err_messages)) {
            foreach ($err_messages as $err) {
                $this->registry['messages']->setError($err);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
        if ($min_max_percents_updated_documents) {
            $max_msgs = array();
            $link = sprintf('%s/index.php?%s=documents&documents=view&view=',
                $this->registry['config']->getParam('crontab', 'base_host'),
                $this->registry['module_param']);
            foreach ($min_max_percents_updated_documents as $max_msg_data) {
                $max_msgs[] = sprintf('<a target="_blank" href="%s%d">%s</a>', $link, $max_msg_data['id'], $max_msg_data['num']);
            }
            $this->registry['messages']->setWarning(sprintf($this->i18n('warning_loan_contracts_set_to_maximum_interest'), implode(', ', $max_msgs)));
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);
        return true;
    }

    /*
     * Enforce annex to the related contract
     */
    function enforceAnnex($params) {
        // get the settings from buildPaymentsAutomation
        $this->getCustomAutomationSettings('createPaymentPlan', 'credits');

        // get the annexes which have to be enforced today
        $today_date_start = date('Y-m-d 00:00:00');
        $today_date_end = date('Y-m-d 23:59:59');
        $sql = 'SELECT d.id, dr.link_to as contract' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_RELATIVES . ' AS dr' . "\n" .
               ' ON (dr.parent_model_name="Document" AND dr.link_to_model_name="Document" AND dr.parent_id=d.id AND dr.origin="transformed")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS . ' AS d2' . "\n" .
               ' ON (d2.id=dr.link_to AND d2.type="' . $this->settings['document_type_contract'] . '" AND d2.active=1 AND d2.deleted_by=0 AND d2.status="opened" AND d2.substatus="' . $this->settings['document_contract_substatus_active'] . '")' . "\n" .
               'WHERE d.active=1 AND d.deleted_by=0 AND d.type="' . $this->settings['document_type_annex'] . '" AND d.status="opened" AND d.deadline>="' . $today_date_start . '" AND d.deadline<="' . $today_date_end . '"' . "\n" .
               'ORDER BY d.deadline' . "\n";
        $annex_contract_relations = $this->registry['db']->GetAssoc($sql);

        // get the models of all the needed documents
        if (empty($annex_contract_relations)) {
            $this->updateAutomationHistory($params, 0, 1);
            return true;
        }
        $model_ids = array_filter(array_merge(array_values($annex_contract_relations), array_keys($annex_contract_relations)));

        $filters = array(
            'where' => array(
                'd.id IN (' . implode(',', $model_ids) . ')'
            ),
            'sort' => array('d.id ASC')
        );
        $temp_documents = Documents::search($this->registry, $filters);

        $annexes = array();
        $contracts = array();

        foreach ($temp_documents as $tmp_doc) {
            if ($tmp_doc->get('type') == $this->settings['document_type_annex']) {
                $annexes[$tmp_doc->get('id')] = $tmp_doc;
            } elseif ($tmp_doc->get('type') == $this->settings['document_type_contract']) {
                $contracts[$tmp_doc->get('id')] = $tmp_doc;
            }
        }
        unset($temp_documents);

        // prepare the temp controller - some function there will be used
        $report_name = 'credilink_customer_file';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report_name . '/controllers/custom.report.controller.php';

        $report = Reports::getReports($this->registry, array('name' => $report_name, 'sanitize' => true));
        if (isset($report[0])) {
            $report = $report[0];
            Reports::getReportSettings($this->registry, $report->get('type'));
            $temp_controller = new Custom_Report_Controller($this->registry);
        }

        // begin processing the annexes
        foreach ($annex_contract_relations as $annex_id => $contract_id) {
            if (!isset($annexes[$annex_id]) || !isset($contracts[$contract_id])) {
                continue;
            }

            // validate the annex
            $not_completed_fields = $this->validateBuildPaymentsTable($annexes[$annex_id]);
            if (!empty($not_completed_fields)) {
                $this->executionErrors[] = sprintf($this->i18n('error_annex_enforce_missing_required_fields'), $annexes[$annex_id]->get('full_num'), implode(', ', $not_completed_fields));
                continue;
            }
            // CHANGE THE CONTRACT TO MATCH THE ANNEX
            $annex = clone $annexes[$annex_id];
            $contract = clone $contracts[$contract_id];

            $annex->unsanitize();
            $annex_vars = $annex->getAssocVars();

            $contract->unsanitize();
            $contract->getVars();
            $old_contract = clone $contract;

            // go through all the vars
            $contract->getVars();
            $contract_vars = $contract->get('vars');

            foreach ($contract_vars as $var_idx => $var_data) {
                if (isset($annex_vars[$var_data['name']]) && isset($annex_vars[$var_data['name']]['value'])) {
                    $contract_vars[$var_idx]['value'] = $annex_vars[$var_data['name']]['value'];
                }
            }

            $contract->set('vars', $contract_vars, true);
            $this->registry['db']->StartTrans();

            // TRANSFORM THE CURRENT DOCUMENT TO KEEP THE CURRENT VERSION
            $transform_doc = clone $contracts[$contract_id];
            $filters = array('where' => array('t.id = ' . $this->settings['transformation_id'],
                                              't.source_type = \'' . $transform_doc->get('type') . '\''),
                             'model_lang' => $this->registry['lang']);
            require_once PH_MODULES_DIR . 'transformations/models/transformations.factory.php';
            $transformation = Transformations::searchOne($this->registry, $filters);

            $transform = $transformation->getAll();

            $transform_doc->set('transform_name', $transform['name'], true);
            $transform_doc->set('transform_type', $transform['transform_type'], true);
            $transform_doc->set('transform_method', $transform['method'], true);
            require_once PH_MODULES_DIR . 'documents/controllers/documents.controller.php';
            $doc_controller = new Documents_Controller($this->registry);
            $this->registry->set('automation_transform', true, true);
            $transform_result = $doc_controller->{$transform['method']}($transform_doc, $transform);
            $this->registry->set('automation_transform', false, true);
            if (!$transform_result) {
                $this->executionErrors[] = sprintf($this->i18n('error_annex_enforce_calculate_payment_failed'), $contract->get('name'));
                $this->registry['db']->FailTrans();
            }

            // SAVE VARS
            if ($contract->saveVars()) {
                // calculate collateral
                $contract->getAssocVars();
                $contract->getGT2Vars();
                $this->calculateCollateralToOutstanding($contract);

                $filt = array(
                    'model_lang' => $this->registry['lang'],
                    'where'      => array(
                        'd.id=' . $contract->get('id')
                    ),
                    'skip_assignments' => true,
                    'skip_permissions_check' => true
                );

                $new_contract = Documents::searchOne($this->registry, $filt);
                $new_contract->getVars();
                Documents_History::saveData($this->registry, array('model' => $new_contract, 'action_type' => 'edit', 'new_model' => $new_contract, 'old_model' => $old_contract));

                /*
                 * Recalculate repayment plan
                 */
                $this->automation_id = $params['id'];
                // get the date from the annex
                $date_calculate = new DateTime($annex->get('deadline'));

                require_once PH_MODULES_DIR . 'automations/plugins/credits/controllers/credits.automations.controller.php';
                $automations_controller = new Credits_Automations_Controller($this->registry);
                $automations_controller->getCustomAutomationSettings('createPaymentPlan', 'credits');

                // load automations i18n files
                $i18n_files = FilesLib::readDir(PH_MODULES_DIR . 'automations/plugins/credits/i18n/' . $this->registry['lang'],
                    false, '', '', true);
                if (!empty($i18n_files)) {
                    $this->registry['translater']->loadFile($i18n_files);
                }
                $adapter = $automations_controller->getAdapter();
                $adapter->automation_params = $params;
                $adapter->current_model = clone $new_contract;
                $adapter->current_model_vars = $adapter->current_model->getAssocVars();
                $adapter->calculate_from_date = $date_calculate->format('Y-m-d');
                $adapter->reason_for_change = $this->automation_id;
                $adapter->mid_contract_recalculation = true;
                $calculate_payment_result = $adapter->calculateInvestmentCreditPayments();
                $adapter->mid_contract_recalculation = false;

                if (!$calculate_payment_result) {
                    $this->executionErrors[] = sprintf($this->i18n('error_annex_enforce_calculate_payment_failed'), $annexes[$annex_id]->get('full_num'));
                    $this->registry['db']->FailTrans();
                } else {
                    /*
                     * Recalculate payment table
                     */
                    $new_contract = Documents::searchOne($this->registry, $filt);
                    $new_contract->getVars();
                    $gt2 = $new_contract->getGT2Vars();
                    $old_contract = clone $new_contract;

                    $result_group_table_edit = $temp_controller->recalculateGroupingTable($new_contract, $gt2, 'edit');

                    if ($result_group_table_edit) {
                        $new_contract = Documents::searchOne($this->registry, $filt);
                        $new_contract->getVars();
                        $new_contract->sanitize();
                        Documents_History::saveData($this->registry, array('model' => $new_contract, 'action_type' => 'edit', 'new_model' => $new_contract, 'old_model' => $old_contract));
                    } else {
                        $this->executionErrors[] = sprintf($this->i18n('error_annex_enforce_distribution_table_calculation_failed'), $annexes[$annex_id]->get('full_num'));
                        $this->registry['db']->FailTrans();
                    }
                }

                if (!$this->registry['db']->HasFailedTrans()) {
                    // close the annex
                    $current_annex = clone $annexes[$annex_id];
                    $old_annex = clone $annexes[$annex_id];
                    $current_annex->set('status', 'closed', true);
                    $current_annex->setStatus();
                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $current_annex,
                            'action_type' => 'status',
                            'new_model' => $current_annex,
                            'old_model' => $old_annex
                        ));
                }
            } else {
                $this->executionErrors[] = sprintf($this->i18n('error_annex_enforce_transfer_data_failed'), $annexes[$annex_id]->get('full_num'));
                $this->registry['db']->FailTrans();
            }

            $this->registry['db']->CompleteTrans();
        }

        $this->updateAutomationHistory($params, 0, 1);
        return true;
    }

    /*
     * Function to take the settings from the buildPaymentsTable automation and add it to the current settings
     *
     * @param string $method_name - the method which we need the settings from
     * @param bool   $force - marks if the settings with the same name will be processed
     * @return void
     */
    function getCustomAutomationSettings($method_name, $plugin_name = '', $force = false) {
        //getBuildPaymentsTableAutomationSettings
        if (!$plugin_name) {
            $plugin_name = 'credilink';
        }
        // get the automation
        $sql = 'SELECT * FROM ' . DB_TABLE_AUTOMATIONS . ' WHERE `method` LIKE "%' . $plugin_name . '%" AND `method` LIKE "%' . $method_name . '%" AND `active`=1 ORDER BY `id` DESC';
        $record = $this->registry['db']->GetRow($sql);

        $settings = array();
        if ($record['settings']) {
            $settings = General::parseSettings($record['settings']);
        }

        foreach ($settings as $key => $val) {
            if (!isset($this->settings[$key]) || $force) {
                $this->settings[$key] = $val;
            }
        }

        return;
    }

    /*
     * Function to validate the building of the payments table
     *
     * @param object $model - Document object which contains the data which the table will based on
     * @return mixed array $not_completed_fields - list with the labels of the fields which must be completed
     */
    function validateBuildPaymentsTable($model) {
        $not_completed_fields = array();
        $assoc_vars = $model->getAssocVars();

        $required_fields = array(
            $this->settings['credit_amount'],
            $this->settings['credit_repayment_period'],
            $this->settings['credit_first_payment_date'],
            $this->settings['credit_premium_insurance_full'],
            $this->settings['credit_premium_insurance_partial'],
            $this->settings['credit_transfer_date'],
            $this->settings['credit_first_payment_date'],
            $this->settings['credit_currency'],
            array(
                $this->settings['credit_interest_rate_fixed'],
                $this->settings['credit_interest_rate_floating']
            )
        );

        foreach ($required_fields as $req_field) {
            if (is_array($req_field)) {
                $current_optional = array();
                $completed_optional = 0;
                foreach ($req_field as $rf) {
                    if (!isset($assoc_vars[$rf])) {
                        continue;
                    }
                    $current_optional[] = $assoc_vars[$rf]['label'];
                    if (!empty(floatval($assoc_vars[$rf]['value']))) {
                        $completed_optional++;
                    }
                }
                if ($completed_optional != 1) {
                    $not_completed_fields[] = sprintf($this->i18n('error_repayment_plan_optional_required_filed'), implode(' ' . $this->i18n('or') . ' ', $current_optional));
                }
            } else {
                if (!isset($assoc_vars[$req_field])) {
                    continue;
                }
                if (is_array($assoc_vars[$req_field]['value'])) {
                    $completed_rows = array_filter($assoc_vars[$req_field]['value']);
                    if (empty($completed_rows)) {
                        $not_completed_fields[] = $assoc_vars[$req_field]['label'];
                    }
                } elseif (empty($assoc_vars[$req_field]['value'])) {
                    $not_completed_fields[] = $assoc_vars[$req_field]['label'];
                }
            }
        }

        return $not_completed_fields;
    }

    /**
     * Calculate payments in the payments table
     *
     * @param object $model - Document object which contains the data which the table will based on
     * @param string $calc_from - the date that the calculation will start from
     * @param bool $clear_table - indicates if the current GT2 table (if existed) should be fully cleared before calculation
     * @param float $prepaid_principal - the prepaid principal if any
     * @return bool
     * @throws Exception
     */
    public function calculatePayments(object $model, string $calc_from = '', bool $clear_table = false, float $prepaid_principal = 0):bool
    {
        $model->unsanitize();
        $model->getVars();
        $old_model = clone $model;
        $this->defineCreditLine($model);
        $this->current_model_vars = $model->getAssocVars();

        $update_additional_vars = array();
        $update_additional_vars_template = '("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "%s")';
        $this->getGracePeriods($this->current_model_vars);

        // get the GT2
        $gt2 = $model->getGT2Vars();
        $this->getInterest($this->current_model_vars);

        $first_payment_date = $this->current_model_vars[$this->settings['credit_first_payment_date']]['value'];
        $calc_from = (!empty($calc_from) ? $calc_from : $first_payment_date);

        // check for credit line
        $this->left_principal = $this->not_covered_principal = $this->getPrincipal($this->current_model_vars);

        if ($this->utilize_row && !$prepaid_principal) {
            $calc_from = $this->current_model_vars[$this->settings['credit_transfer_date']]['value'][$this->utilize_row];
            $this->utilize_sum = $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$this->utilize_row];
            $update_additional_vars[] = sprintf($update_additional_vars_template,
                $model->get('id'),
                $this->current_model_vars[$this->settings['amount_utilized_yes']]['id'],
                $this->utilize_row,
                $this->settings['amount_utilized_yes_opt'],
                $this->registry['currentUser']->get('id'),
                $this->registry['currentUser']->get('id'),
                ($this->current_model_vars[$this->settings['credit_transfer_date']]['multilang'] ? $this->registry['lang'] : ''));
        }

        $payment_start = 0;
        $sum_before_change = 0;
        $gt2_rows = (!empty($gt2['values']) ? $gt2['values'] : array());
        foreach ($gt2_rows as $idx => $rw) {
            if ($rw['article_measure_name']>=$calc_from || $clear_table) {
                $gt2_rows[$idx]['deleted'] = 1;
            } else {
                $this->left_principal = $this->not_covered_principal = ($this->not_covered_principal - $rw['price']);
                $payment_start++;
            }
            $sum_before_change += (!empty($rw['article_second_code']) ? floatval($rw['article_second_code']) : 0);
        }

        // calculate the currency rate from BGN to contract currency
        $contract_currency = $this->current_model_vars[$this->settings['credit_currency']]['value'];
        $currency_rate = Finance_Currencies::getRate($this->registry, 'BGN', $contract_currency);

        // check if there is fee management
        $this->getFeeMangementMonths();

        // calculate the months without the grace periods
        $grace_periods_count = 0;
        $passed_graced_periods = 0;
        $last_not_grace_period = null;
        $first_payment_date = new DateTime($first_payment_date);
        $loan_contract_dates = array();
        for ($i=0; $i<$this->current_model_vars[$this->settings['credit_repayment_period']]['value']; $i++) {
            $period_end = $this->addMonths(clone($first_payment_date), $i);
            $customer_pay_date = clone $period_end;
            if (!Calendars_Calendar::getWorkingDays($this->registry, $customer_pay_date->format('Y-m-d'), $customer_pay_date->format('Y-m-d'))) {
                $customer_pay_date = new DateTime(Calendars_Calendar::calcDateOnWorkingDays($this->registry, $customer_pay_date->format('Y-m-d'), 1));
            }
            if ($i==0) {
                // for the first period, return one month
                $period_start = $this->subMonths(clone($period_end), 1);
                $split_period_date = clone $period_start;
            } else {
                // for all periods after the first, get the previous payment date and add one day
                $split_period_date = $this->addMonths(clone($first_payment_date), $i-1);
                $period_start = clone $split_period_date;
                $period_start->add(new DateInterval('P1D'));
            }
            $loan_contract_dates[$i] = array(
                'period_start'      => $period_start->format('Y-m-d'),
                'period_end'        => $period_end->format('Y-m-d'),
                'split_period_date' => $split_period_date->format('Y-m-d'),
                'customer_pay_date' => $customer_pay_date->format('Y-m-d'),
                'graced' => false
            );
            if (!empty($this->contract_grace_periods)) {
                foreach ($this->contract_grace_periods as $gp) {
                    if ($gp['from'] <= $loan_contract_dates[$i]['period_end'] && $loan_contract_dates[$i]['period_end'] <= $gp['to']) {
                        $grace_periods_count++;
                        $loan_contract_dates[$i]['graced'] = true;
                        if ($calc_from>=$loan_contract_dates[$i]['period_end']) {
                            $passed_graced_periods++;
                        }
                        break;
                    }
                }
            }
            if (!$loan_contract_dates[$i]['graced']) {
                $last_not_grace_period = $i;
            }
        }

        // define the default month payment
        // it is based on the total number of months minus all the graced periods
        $monthly_payments_months = intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value'])-$grace_periods_count;
        if ($this->mid_contract_recalculation) {
            $monthly_payments_months = (intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value'])-$payment_start)-($grace_periods_count-$passed_graced_periods);
        }
        $monthly_payment = $this->calcPMT($this->interest_rate, $monthly_payments_months, $this->not_covered_principal);

        /*
         * Start building rows
         */
        for ($i=$payment_start; $i<$this->current_model_vars[$this->settings['credit_repayment_period']]['value']; $i++) {
            $credit_utilize = false;
            $new_row = array(
                'article_code'                    => $loan_contract_dates[$i]['customer_pay_date'],
                'article_measure_name'            => $loan_contract_dates[$i]['period_end'],
                'article_id'                      => 0,
                'last_delivery_price'             => 0,
                'article_deliverer_name'          => 0,
                'article_barcode'                 => 0,
                'article_height'                  => 0,
                'article_name'                    => 0,
                'article_width'                   => 0,
                'free_text4'                      => 0,
                'article_weight'                  => 0,
                'article_volume'                  => 0,
                'average_weighted_delivery_price' => 0,
                'quantity'                        => 0,
                'free_text2'                      => 0,
                'article_description'             => 0,
                'free_text1'                      => 0,
                'free_field3'                     => 0,
                'article_trademark'               => 0,
                'free_field2'                     => 0,
            );

            if ($this->is_credit_line && $i==$payment_start &&
                $this->utilize_sum &&
                ($calc_from>=$loan_contract_dates[$i]['period_start'] && $calc_from<=$loan_contract_dates[$i]['period_end'])) {
                // for the first row process the data of the last active row
                $payments_list = array_keys($gt2_rows);
                if (isset($gt2_rows[$payments_list[$payment_start]]) &&
                    $gt2_rows[$payments_list[$payment_start]]['article_measure_name'] >= $loan_contract_dates[$i]['period_start'] &&
                    $gt2_rows[$payments_list[$payment_start]]['article_measure_name'] <= $loan_contract_dates[$i]['period_end']) {
                    $credit_utilize = true;
                    foreach ($new_row as $k => $val) {
                        $new_row[$k] = $gt2_rows[$payments_list[$payment_start]][$k];
                    }

                    $update_additional_vars[] = sprintf($update_additional_vars_template,
                        $model->get('id'),
                        $this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['id'],
                        $this->utilize_row,
                        $this->not_covered_principal,
                        $this->registry['currentUser']->get('id'),
                        $this->registry['currentUser']->get('id'),
                        ($this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['multilang'] ? $this->registry['lang'] : ''));
                }
            }

            // check the grace periods
            $inside_grace_period = $loan_contract_dates[$i]['graced'];

            // CALCULATE INTEREST
            $date_start_period = '';
            $full_month_interval = 30;
            if ($i == 0) {
                // for the first row
                $loan_transfer_dates = array_filter($this->current_model_vars[$this->settings['credit_transfer_date']]['value']);
                $loan_transfer = reset($loan_transfer_dates);

                $date_loan = $date_start_period = new DateTime($loan_transfer);
                $date_first = $first_payment_date;

                $full_month_interval = $date_loan->diff($date_first)->days + 1;
                $new_row['quantity'] = ($this->interest_rate / 30) * $full_month_interval * $this->not_covered_principal;
            } else {
                // for all rows after the first
                $new_row['quantity'] = $this->interest_rate * $this->not_covered_principal;
            }

            if ($this->is_credit_line && $credit_utilize) {
                if (!$date_start_period) {
                    $date_start_period = new DateTime($loan_contract_dates[$i]['split_period_date']);
                }
                $date_split_period = new DateTime($calc_from);

                $split_principal = 0;
                if ($i != 0) {
                    // get the previous row
                    $previous_row_gt2 = $payments_list[$payment_start-1];
                    $split_principal = $gt2_rows[$previous_row_gt2]['last_delivery_price'] + $gt2_rows[$previous_row_gt2]['average_weighted_delivery_price'];
                }

                $splits = array(
                    $date_start_period->format('Y-m-d') => $split_principal
                );

                foreach ($this->current_model_vars[$this->settings['credit_transfer_date']]['value'] as $trans_key => $transfer_date) {
                    $transfer_date = new DateTime($transfer_date);
                    if ($transfer_date < $date_start_period || $transfer_date > $date_split_period) {
                        continue;
                    }

                    $splits[$transfer_date->format('Y-m-d')] = $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key] + $split_principal;
                    $split_principal += $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$trans_key];
                }

                $split_dates = array_keys($splits);
                $interval_days_total = 0;
                $calculated_interest = 0;
                foreach ($splits as $dt_begin => $split_principal_current) {
                    $current_key = array_search($dt_begin, $split_dates);
                    if ($current_key === 0) {
                        continue;
                    }

                    // PROCESS THE DATA FROM THE PREVIOUS PERIOD
                    $split_start_date = new DateTime($split_dates[$current_key - 1]);
                    $split_end_date = new DateTime($dt_begin);
                    $split_real_date = clone $split_end_date;
                    $split_end_date = $split_end_date->sub(new DateInterval('P1D'));
                    $split_principal_calc = $splits[$split_dates[$current_key - 1]];
                    $split_interval = $split_start_date->diff($split_end_date)->d + 1;

                    // check if the real split date is in different month and
                    // if so perform calculation as if the current month has 30 days
                    // (add 1 or 2 for 29 and 28 days or subtract -1 if 31 days)
                    if ($split_start_date->format('Y-m') != $split_real_date->format('Y-m')) {
                         $split_start_date->modify('last day of this month');
                         $split_start_date->format('j');
                         $add_days = 30 - intval($split_start_date->format('j'));
                         $split_interval += $add_days;
                    }

                    $calculated_interest += $split_principal_calc * ($this->interest_rate / 30) * $split_interval;
                    $interval_days_total += $split_interval;

                    if ($current_key == count($split_dates)-1) {
                        // PROCESS THE DATA FOR THE LAST PERIOD
                        // get the remaining days according to the requirements from the client
                        $split_interval = $full_month_interval - $interval_days_total;
                        $calculated_interest += $split_principal_current * ($this->interest_rate / 30) * $split_interval;
                    }
                }

                $new_row['quantity'] = $calculated_interest;
            }

            $new_row['free_field1'] = sprintf('%.2f', round(($new_row['quantity'] - $new_row['article_barcode']), 2));

            // CALCULATE THE PRINCIPAL
            if ($inside_grace_period) {
                $new_row['price'] = 0.00;
            } elseif ($last_not_grace_period === $i) {
                $new_row['price'] = $this->left_principal;
            } elseif ($credit_utilize) {
                $first_pmt = $this->calcPMT($this->interest_rate, intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value'])-($payment_start)-$grace_periods_count, $new_row['average_weighted_delivery_price']+$new_row['last_delivery_price']+$this->utilize_sum);
                if ($i == 0) {
                    $new_row['price'] = $first_pmt - ($this->interest_rate * $this->not_covered_principal);
                } else {
                    $new_row['price'] = $first_pmt - $new_row['quantity'];
                }
                // recalculate monthly payments
                $monthly_payment = $this->calcPMT($this->interest_rate, intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value'])-($payment_start+1)-$grace_periods_count, ($this->not_covered_principal - $new_row['price']));
            } elseif ($i == 0) {
                $new_row['price'] = $monthly_payment - ($this->interest_rate * $this->not_covered_principal);
            } else {
                $new_row['price'] = $monthly_payment - $new_row['quantity'];
            }

            // add the prepaid principal
            if ($prepaid_principal &&
                ($calc_from>$loan_contract_dates[$i]['period_start'] && $calc_from<=$loan_contract_dates[$i]['period_end'])) {
                $new_row['price'] = $prepaid_principal;

                // recalculate monthly payments
                $monthly_payment = $this->calcPMT($this->interest_rate, intval($this->current_model_vars[$this->settings['credit_repayment_period']]['value'])-($payment_start+1)-$grace_periods_count, ($this->not_covered_principal - $new_row['price']));
            }

            $this->left_principal = $this->left_principal - round($new_row['price'], 2);
            $new_row['average_weighted_delivery_price'] = $new_row['price'] - $new_row['article_deliverer_name'];
            $new_row['average_weighted_delivery_price'] = sprintf('%.2f', round($new_row['average_weighted_delivery_price'], 2));

            // CALCULATE THE INSURANCE
            if ($i == 0) {
                // write the sum of the partial insurance in the first payment
                $new_row['article_trademark'] = $currency_rate * floatval($this->current_model_vars[$this->settings['insurance_partial']]['value']);
            }

            $chk_date = new DateTime($new_row['article_measure_name']);
            if ($chk_date->format('n') == $this->settings['insurance_month']) {
                // write the sum of the insurance in the month set in the settings
                $new_row['article_trademark'] = $currency_rate * floatval($this->current_model_vars[$this->settings['insurance_premium']]['value']);
            }
            $new_row['free_field2'] = $new_row['article_trademark'] - $new_row['article_height'];

            // RECALCULATE NOT COVERED PRINCIPAL
            $this->previous_row_not_covered_principal = $this->not_covered_principal;
            if ($inside_grace_period) {
                $new_row['last_delivery_price'] = $this->not_covered_principal;
            } else {
                $new_row['last_delivery_price'] = $this->not_covered_principal = ($last_not_grace_period === $i ? 0 : ($this->not_covered_principal - $new_row['price']));
            }

            // CALCULATE THE FEE MANAGEMENT
            $new_row['free_text2'] = $this->getManagementFee($new_row, $i);
            $new_row['article_description'] = $new_row['free_text2'] - $new_row['article_name'];

            // CALCULATE THE FEE COMMITMENT
            $new_row['free_text1'] = $this->getCommitmentFee();
            $new_row['free_field3'] = $new_row['free_text1'] - $new_row['article_width'];

            // total payment
            $new_row['article_second_code'] = sprintf('%.2f', round($new_row['price'],2) +
                                                              round($new_row['quantity'],2) + round($new_row['article_trademark'],2) +
                                                              round($new_row['free_text1'], 2) + round($new_row['free_text2'], 2));
            $new_row['free_field5'] = $new_row['article_second_code'] - $new_row['article_volume'];
            $gt2_rows[] = $new_row;
        }

        // go through all the rows and:
        // 1) calculate the average for the month payment
        // 2) get the total
        $total = 0;
        $gt2_rows_count = 0;

        foreach ($gt2_rows as $r => $gt2_r) {
            if (!empty($gt2_r['deleted'])) {
                continue;
            }
            $gt2_rows_count++;
            $total += floatval($gt2_rows[$r]['article_second_code']);
            foreach ($gt2_r as $gt2_var => $gt2_val) {
                if ($gt2_var == 'article_code' || $gt2_var == 'article_measure_name') {
                    continue;
                }
                $gt2_rows[$r][$gt2_var] = sprintf('%.2f', round($gt2_val, 2));
            }
        }

        $gpr = $this->calculateGPR($gt2_rows, $model->get('id'));

        // check if the customer is company or person
        $sql = 'SELECT `is_company` FROM ' . DB_TABLE_CUSTOMERS . ' WHERE `id`="' . $model->get('customer') . '"' . "\n";
        $customer_is_company = $this->registry['db']->GetOne($sql);

        // calculate avarage monthly payment
        $average_monthly_payment = sprintf('%.2f', round($total/$gt2_rows_count, 2));

        // set additional vars
        $update_additional_vars[] = sprintf($update_additional_vars_template,
                                    $model->get('id'),
                                    $this->current_model_vars[$this->settings['credit_gpr']]['id'],
                                    1,
                                    sprintf('%.4f', round($gpr, 4)),
                                    $this->registry['currentUser']->get('id'),
                                    $this->registry['currentUser']->get('id'),
                                    ($this->current_model_vars[$this->settings['credit_gpr']]['multilang'] ? $this->registry['lang'] : ''));
        $update_additional_vars[] = sprintf($update_additional_vars_template,
                                    $model->get('id'),
                                    $this->current_model_vars[$this->settings['monthly_payment']]['id'],
                                    1,
                                    $average_monthly_payment,
                                    $this->registry['currentUser']->get('id'),
                                    $this->registry['currentUser']->get('id'),
                                    ($this->current_model_vars[$this->settings['monthly_payment']]['multilang'] ? $this->registry['lang'] : ''));
        $update_additional_vars[] = sprintf($update_additional_vars_template,
                                    $model->get('id'),
                                    $this->current_model_vars[$this->settings['repayment_plan_currency']]['id'],
                                    1,
                                    $contract_currency,
                                    $this->registry['currentUser']->get('id'),
                                    $this->registry['currentUser']->get('id'),
                                    ($this->current_model_vars[$this->settings['repayment_plan_currency']]['multilang'] ? $this->registry['lang'] : ''));

        if ($clear_table) {
            // set all utilized sums to YES and update the uncovered principal
            $previously_utilized_sum = 0;
            foreach ($this->current_model_vars[$this->settings['amount_utilized_yes']]['value'] as $kr => $dat) {
                $previously_utilized_sum += (!empty($this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$kr]) ? $this->current_model_vars[$this->settings['amount_utilized_sum']]['value'][$kr] : 0);
                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $model->get('id'),
                    $this->current_model_vars[$this->settings['amount_utilized_yes']]['id'],
                    $kr,
                    $this->settings['amount_utilized_yes_opt'],
                    $this->registry['currentUser']->get('id'),
                    $this->registry['currentUser']->get('id'),
                    ($this->current_model_vars[$this->settings['amount_utilized_yes']]['multilang'] ? $this->registry['lang'] : ''));
                $update_additional_vars[] = sprintf($update_additional_vars_template,
                    $model->get('id'),
                    $this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['id'],
                    $kr,
                    $previously_utilized_sum,
                    $this->registry['currentUser']->get('id'),
                    $this->registry['currentUser']->get('id'),
                    ($this->current_model_vars[$this->settings['amount_utilized_uncovered_principal']]['multilang'] ? $this->registry['lang'] : ''));
            }
        }

        $gt2['values'] = $gt2_rows;
        $model->set('grouping_table_2', $gt2, true);
        $model->calculateGT2();
        $model->set('table_values_are_set', true, true);

        $this->registry['db']->StartTrans();

        // update the repayment plan
        if (!$model->saveGT2Vars()) {
            $this->registry['db']->FailTrans();
        }

        if (!$customer_is_company && $gpr > ($this->base_interest_rate*5)) {
            $this->executionErrors[] = sprintf($this->i18n('error_repayment_plan_creation_failed_gpr_too_high'), $model->get('name'), $gpr, $this->base_interest_rate);
            $this->registry['db']->FailTrans();
        }

        // update the other additional vars
        $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
               'VALUES ' . implode(",\n", $update_additional_vars) . "\n" .
               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
        $this->registry['db']->Execute($sql);

        $model->getGT2Vars();
        $this->calculateCollateralToOutstanding($model);

        if (!$this->registry['db']->HasFailedTrans()) {
            // write history
            $filters = array(
                'where' => array('d.id="' . $model->get('id') . '"')
            );
            $new_model = Documents::searchOne($this->registry, $filters);
            $new_model->getVars();

            $history_params = array(
                'model'       => $new_model,
                'action_type' => 'edit',
                'new_model'   => $new_model,
                'old_model'   => $old_model
            );
            Documents_History::saveData($this->registry, $history_params);

            // check the incomes reason and create it if needed
            $incomes_reason_exists = $this->checkExistingFinIncomesReason($new_model->get('id'), $contract_currency, $this->settings['incomes_reason_type_id']);

            if ($incomes_reason_exists) {
                // check the difference in values
                $change_sum = $total - $sum_before_change;

                // change the existing incomes reason
                if (!$this->issueFinIncomeReasonCorrection($incomes_reason_exists, $change_sum, $this->automation_id, $this->settings['nom_contract_obligations_id'])) {
                    $this->executionErrors[] = $this->i18n('error_failed_issue_correction_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            } else {
                // create a new incomes reason
                // incomes reason does not exist so we need to add it
                $params_reason = array(
                    'type'         => $this->settings['incomes_reason_type_id'],
                    'customer'     => $new_model->get('customer'),
                    'issue_date'   => $new_model->get('date'),
                    'company'      => $this->settings['incomes_reason_company'],
                    'office'       => $this->settings['incomes_reason_office'],
                    'payment_type' => '',
                    'container_id' => '',
                    'total'        => $total,
                    'article_id'   => $this->settings['nom_contract_obligations_id'],
                    'document_id'  => $new_model->get('id'),
                    'parent_reason'=> '',
                    'currency'     => $contract_currency
                );

                if (isset($this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])])) {
                    $container_data = explode('_', $this->settings['incomes_reason_container_' . strtolower($params_reason['currency'])]);
                    $params_reason['payment_type'] = $container_data[0];
                    $params_reason['container_id'] = $container_data[1];
                    if (!empty($this->createContractIncomesReason($params_reason))) {
                        $this->executionErrors[] = $this->i18n('error_repayment_plan_failed_adding_incomes_reason');
                        $this->registry['db']->FailTrans();
                    }
                } else {
                    $this->executionErrors[] = $this->i18n('error_repayment_plan_missing_data_for_incomes_reason');
                    $this->registry['db']->FailTrans();
                }
            }
        } else {
            $this->executionErrors[] = $this->i18n('error_repayment_plan_creation_failed');
            $this->registry['db']->FailTrans();
        }
        $result = !$this->registry['db']->HasFailedTrans();
        $this->registry['db']->CompleteTrans();

        return $result;
    }

    /*
      Function that defines if the credit is with credit line tag
     */
    function defineCreditLine($model, $force = false) {
        if ($this->credit_line_defined && !$force) {
            return $this->is_credit_line;
        }
        $model->getTags();
        $tags = $model->get('tags');
        $this->is_credit_line = in_array($this->settings['credit_line_tag'], $tags);
        $this->credit_line_defined = true;

        return $this->is_credit_line;
    }

    /**
     * Add months based on a date
     *
     * @param DateTime $dateObject - the date which we will base our calculations on
     * @param int $months - number of months to add
     * @return DateTime - the new date with the number of added months
     * @throws Exception
     */
    public function addMonths(DateTime $dateObject, int $months): DateTime
    {
        $next = clone $dateObject;
        $next->modify('last day of +' . $months . ' month');

        if($dateObject->format('d') > $next->format('d')) {
            return $dateObject->add($dateObject->diff($next));
        }
        return $dateObject->add(new DateInterval('P' . $months . 'M'));
    }

    /**
     * Sub months based on a date
     *
     * @param DateTime $dateObject - the date which we will base our calculations on
     * @param int $months - number of months to sub
     * @return DateTime - the new date with the number of subtracted months
     * @throws Exception
     */
    public function subMonths(DateTime $dateObject, int $months): DateTime
    {
        $prev = clone $dateObject;
        $prev->modify('last day of -' . $months . ' month');

        if($dateObject->format('d') > $prev->format('d')) {
            return $dateObject->sub($prev->diff($dateObject));
        }
        return $dateObject->sub(new DateInterval('P' . $months . 'M'));
    }

    /*
     * Calculate IRR (based on the Excel function of the same name)
     *
     * @param int $investment - the total that was given
     * @param array $flow - all the payments that are expected
     * @param float $precision - the precision of the calculation - set pretty low by default
     * @return float - the calculated IRR
     */
    function calcIRR($investment, $flow, $precision = 0.0000000001) {
        $min = 0;
        $max = 1;
        $net_present_value = 1;
        $max_iterations = 100000;
        $iterator = 0;

        while(abs($net_present_value - $investment) > $precision && $iterator<$max_iterations) {
            $iterator++;
            $net_present_value = 0;
            $guess = ($min + $max) / 2;
            foreach ($flow as $period => $cashflow) {
                $net_present_value += $cashflow / (1 + $guess) ** ($period + 1);
            }
            if ($net_present_value - $investment > 0) {
                $min = $guess;
            } else {
                $max = $guess;
            }
        }

        return $guess * 100;
    }

    /*
     * Function to calculate the GPR based on the gt2 rows and the data in the loan contract
     *
     * @param array $rows - rows of the gt2 table
     * @param array $model_vars - model id which we need the GPR for
     *
     * @return float - the calculated GPR
     */
    public function calculateGPR($rows, $model_id) {
        $total_principal= 0;
        $total_costs = 0;
        $flow = array();

        foreach ($rows as $gt2_r) {
            if (!empty($gt2_r['deleted'])) {
                continue;
            }
            $flow[] = $gt2_r['article_second_code'];
            $total_principal += floatval($gt2_r['price']);
        }

        // Get the current vars in the model
        // write history
        $filters = array(
            'where' => array('d.id="' . $model_id . '"')
        );
        $new_model = Documents::searchOne($this->registry, $filters);
        $model_vars = $new_model->getAssocVars();

        // calculate the additional costs
        $available_costs_types = array(
            $this->settings['credit_mortgage_cost'],
            $this->settings['credit_collateral_cost'],
            $this->settings['credit_review_documents_cost'],
            $this->settings['credit_other_expense_cost']
        );
        $available_costs_types = array_filter($available_costs_types);
        $costs_types = $model_vars[$this->settings['credit_cost_type']]['value'];
        $costs_amount = $model_vars[$this->settings['credit_cost_sum_converted']]['value'];

        foreach ($costs_types as $r => $cost_type) {
            if (!in_array($cost_type, $available_costs_types)) {
                continue;
            }
            $total_costs += floatval($costs_amount[$r]);
        }

        // calculate GPR
        $positive_values = $total_principal - $total_costs;
        $gpr_irr = $this->calcIRR($positive_values, $flow);
        $gpr = ((pow(($gpr_irr+100)/100, 12)) * 100)-100;

        return $gpr;
    }

    /*
     * Recalculate the cost based on the currency
     */
    public function convertCurrencyCosts($params) {
        $model = $params['model'];

        // get the settings from buildPaymentsTable automation
        $this->getCustomAutomationSettings('createPaymentPlan', 'credits');

        $get_old_vars = $this->registry->get('get_old_vars');
        $this->registry->set('get_old_vars', true, true);

        $assoc_vars = $model->getAssocVars();
        $old_model = clone $model;

        // get the data for the table
        $contract_currency = $assoc_vars[$this->settings['credit_currency']]['value'];
        $cost_amount = $assoc_vars[$this->settings['credit_cost_sum']]['value'];
        $cost_currency = $assoc_vars[$this->settings['credit_cost_currency']]['value'];
        $converted_currency_var_id = $assoc_vars[$this->settings['credit_cost_sum_converted']]['id'];

        $updates = array();
        foreach ($cost_amount as $k => $cost) {
            if (!floatval($cost) || empty($cost_currency[$k])) {
                continue;
            }
            $rate = Finance_Currencies::getRate($this->registry, $cost_currency[$k], $contract_currency);
            $updates[] = sprintf('(%d, %d, %d, "%.2f", NOW(), %d, NOW(), %d, "%s")', $model->get('id'), $converted_currency_var_id, $k, round($rate * $cost, 2), $this->registry['originalUser']->get('id'), $this->registry['originalUser']->get('id'), ($assoc_vars[$this->settings['credit_cost_sum_converted']]['multilang'] ? $this->registry['lang'] : ''));
        }

        if (!empty($updates)) {
            $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . "\n" .
                     '(`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`) VALUES' . "\n" .
                     implode(',' . "\n", $updates) . "\n" .
                     'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($query);

            // write history
            $filters = array(
                'where' => array('d.id="' . $model->get('id') . '"')
            );
            $new_model = Documents::searchOne($this->registry, $filters);
            $new_model->getVars();

            Documents_History::saveData($this->registry, array('model' => $new_model, 'action_type' => 'edit', 'new_model' => $new_model, 'old_model' => $old_model));
        }

        $this->registry->set('get_old_vars', $get_old_vars, true);
        return true;
    }

    /*
     * Explore the related incomes reasons and define if there is need to create new one or to extend the existing
     */
    public function processIncomesReasons($payment, $contract_id, $distributed_sum_original, $contract_currency, $reason) {
        $operation_result = array(
            'error'    => false,
            'messages' => array()
        );

        // check for incomes reason with the currency of the payment
        $incomes_reason_exist = $this->checkExistingFinIncomesReason($contract_id, $payment->get('currency'), $this->settings['incomes_reason_type_id']);
        $convert_rate = Finance_Currencies::getRate($this->registry, $payment->get('currency'), $contract_currency);

        $reduce_main_inc_reason_amount = false;
        $distributed_sum_converted = round($distributed_sum_original * $convert_rate, 2);

        if (!$incomes_reason_exist) {
            // incomes reason does not exist so we need to add it
            $params_reason = array(
                'type'         => $this->settings['incomes_reason_type_id'],
                'customer'     => $payment->get('customer'),
                'issue_date'   => $payment->get('issue_date'),
                'company'      => $payment->get('company'),
                'office'       => $payment->get('office'),
                'payment_type' => ($payment->get('container_type') == 'bank_account' ? 'bank' : 'cash'),
                'container_id' => $payment->get('container_id'),
                'total'        => $distributed_sum_original,
                'article_id'   => $this->settings['nom_contract_obligations_id'],
                'document_id'  => $contract_id,
                'parent_reason'=> (!empty($this->settings['incomes_reason_certain_id']) ? $this->settings['incomes_reason_certain_id'] : ''),
                'currency'     => $payment->get('currency')
            );
            $result = $this->createContractIncomesReason($params_reason);
            $reduce_main_inc_reason_amount = true;
            if (!empty($result)) {
                $operation_result['error'] = true;
                $operation_result['messages'][] = $this->i18n('error_failed_adding_incomes_reason');
            }
        } elseif ($incomes_reason_exist && $contract_currency != $payment->get('currency')) {
            // correct the existing document
            $reduce_main_inc_reason_amount = true;
            if (!$this->issueFinIncomeReasonCorrection($incomes_reason_exist, $distributed_sum_original, $reason, $this->settings['nom_contract_obligations_id'])) {
                $operation_result['error'] = true;
                $operation_result['messages'][] = $this->i18n('error_failed_issue_correction_incomes_reason');
            }
        }

        if ($reduce_main_inc_reason_amount) {
            // decrease the main incomes reason amount with the amount of the converted payment
            $sql = 'SELECT fir.id' . "\n" .
                   'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                   '  ON (frr.parent_model_name="Finance_Incomes_Reason" AND frr.link_to=d.id AND frr.link_to_model_name="Document"' . (!empty($this->settings['incomes_reason_certain_id']) ? ' AND frr.parent_id="' . $this->settings['incomes_reason_certain_id'] . '"' : '') . ')' . "\n" .
                   'INNER JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                   '  ON (fir.id=frr.parent_id AND fir.type="' . $this->settings['incomes_reason_type_id'] . '" AND fir.currency="' . $contract_currency . '")' . "\n" .
                   'WHERE d.id="' . $contract_id . '"' . "\n";
            $org_incomes_reason = $this->registry['db']->GetOne($sql);

            $changed_sum = $distributed_sum_converted * (-1);
            if ($this->settings['incomes_reason_type_id'] == $this->settings['incomes_reason_for_contract']) {
                $changed_sum = $this->recalculateMainIncomesReasonSum($contract_id, $org_incomes_reason, $distributed_sum_converted);
            }

            if (!$this->issueFinIncomeReasonCorrection($org_incomes_reason, $changed_sum, $reason, $this->settings['nom_contract_obligations_id'])) {
                $operation_result['error'] = true;
                $operation_result['messages'][] = $this->i18n('error_failed_adding_incomes_reason');
            }
        }

        return $operation_result;
    }

    /*
     * Distribute payment by contract items
     */
    public function distributePaymentSumByContractItems(&$contract, &$payment, $distributed_sum, $exclude_msg_tags = false) {
        $results = array(
            'error'    => false,
            'messages' => array()
        );

        $order_completion = array(
            'tax_ang' => array(
                'paid' => 'article_width',
                'left' => 'free_field3'
            ),
            'tax' => array(
                'paid' => 'article_name',
                'left' => 'article_description',
            ),
            'warranty' => array(
                'paid' => 'article_height',
                'left' => 'free_field2',
            ),
            'interest' => array(
                'paid' => 'article_barcode',
                'left' => 'free_field1',
            ),
            'principal' => array(
                'paid' => 'article_deliverer_name',
                'left' => 'average_weighted_delivery_price',
            )
        );

        $contract_url = sprintf('%s://%s%s?%s=documents&documents=view&view=%d',
                                (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                $_SERVER["HTTP_HOST"], $_SERVER['PHP_SELF'], $this->registry['module_param'],
                                $contract->get('id'));

        $contract->unsanitize();
        $contract->getVars();
        $gt2 = $contract->getGT2Vars();

        $current_payments = array();

        // get the currency of the schedule
        $contract_currency = $contract->getVarValue($this->settings['schedule_var_credit_currency']);
        $rate = Finance_Currencies::getRate($this->registry, $payment->get('currency'), $contract_currency);
        $amount_left = round($distributed_sum * $rate, 2);

        $first_covered_row = false;

        // START VERTICAL DISTRIBUTION
        foreach ($order_completion as $cover => $cover_var) {
            $row_num = 0;
            foreach ($gt2['values'] as $key => $vals) {
                if (!$amount_left) {
                    break;
                }

                $row_num++;
                if (isset($current_payments[$row_num])) {
                    $current_payments_data = $current_payments[$row_num];
                } else {
                    $current_payments_data = array(
                        'gt2_id'       => $vals['id'],
                        'gt2_row'      => $row_num,
                        'payment_id'   => $payment->get('id'),
                        'payment_num'  => $payment->get('num'),
                        'payment_date' => $payment->get('issue_date'),
                        'distribute_date' => $payment->get('status_modified'),
                        'interest'     => 0,
                        'warranty'     => 0,
                        'principal'    => 0,
                        'lpg'          => 0,
                        'penalty'      => 0,
                        'tax'          => 0,
                        'tax_ang'      => 0
                    );
                }

                if (isset($vals['free_field5'])) {
                    // subtract the penalty and lpg
                    $payment_left_substract = $vals['free_field5'] - (round(floatval($vals['free_text5']), 2) + round(floatval($vals['free_field4']), 2));
                    if ($payment_left_substract > 0 && $amount_left && $vals['article_code']<date('Y-m-d')) {
                        if ($vals[$cover_var['left']] > 0 && $amount_left) {
                            if ($vals[$cover_var['left']] > $amount_left) {
                                $current_payments_data[$cover] = $amount_left;
                                $gt2['values'][$key][$cover_var['left']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['left']] - $amount_left);
                                $gt2['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['paid']] + $amount_left);
                                $amount_left = 0;
                            } else {
                                $current_payments_data[$cover] = $vals[$cover_var['left']];
                                $gt2['values'][$key][$cover_var['left']] = sprintf('%.2f', 0);
                                $gt2['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['paid']] + $vals[$cover_var['left']]);
                                $amount_left = round($amount_left - $vals[$cover_var['left']], 2);
                            }

                            // update sum fields
                            $gt2['values'][$key]['article_volume'] = sprintf('%.2f', floatval($gt2['values'][$key]['article_name']) +
                                                                                     floatval($gt2['values'][$key]['article_width']) +
                                                                                     floatval($gt2['values'][$key]['article_weight']) +
                                                                                     floatval($gt2['values'][$key]['article_barcode']) +
                                                                                     floatval($gt2['values'][$key]['article_height']) +
                                                                                     floatval($gt2['values'][$key]['article_deliverer_name']) +
                                                                                     floatval($gt2['values'][$key]['free_text4']));

                            $gt2['values'][$key]['free_field5'] = sprintf('%.2f', $gt2['values'][$key]['article_second_code'] - $gt2['values'][$key]['article_volume']);
                            $gt2['values'][$key]['discount_surplus_field'] = 'discount_value';

                            $current_payments[$row_num] = $current_payments_data;
                        }
                    }
                } else {
                    $results['error'] = true;
                    $results['messages'][] = sprintf($this->i18n('error_payments_not_distributed_successfuly'), $contract_url, $contract->get('full_num'));
                }
            }
        }

        // START HORIZONTAL DISTRIBUTION
        $row_num = 0;
        foreach ($gt2['values'] as $key => $vals) {
            if (!$amount_left) {
                break;
            }
            $row_num++;
            $current_payments_data = array(
                'gt2_id'       => '',
                'gt2_row'      => '',
                'payment_id'   => '',
                'payment_num'  => '',
                'payment_date' => '',
                'distribute_date' => '',
                'interest'     => 0,
                'warranty'     => 0,
                'lpg'          => 0,
                'penalty'      => 0,
                'principal'    => 0,
                'tax'          => 0,
                'tax_ang'      => 0
            );

            if (isset($vals['free_field5'])) {
                // substract the penalty and lpg
                $payment_left_substract = $vals['free_field5'] - (round(floatval($vals['free_text5']), 2) + round(floatval($vals['free_field4']), 2));
                if ($payment_left_substract > 0 && $amount_left) {
                    foreach ($order_completion as $cover => $cover_var) {
                        if ($vals[$cover_var['left']] > 0 && $amount_left) {
                            if ($vals[$cover_var['left']] > $amount_left) {
                                $current_payments_data[$cover] = $amount_left;
                                $gt2['values'][$key][$cover_var['left']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['left']] - $amount_left);
                                $gt2['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['paid']] + $amount_left);
                                $amount_left = 0;
                            } else {
                                $current_payments_data[$cover] = $vals[$cover_var['left']];
                                $gt2['values'][$key][$cover_var['left']] = sprintf('%.2f', 0);
                                $gt2['values'][$key][$cover_var['paid']] = sprintf('%.2f', $gt2['values'][$key][$cover_var['paid']] + $vals[$cover_var['left']]);
                                $amount_left = round($amount_left - $vals[$cover_var['left']], 2);
                            }
                        }
                    }
                    if ($amount_left && !$first_covered_row) {
                        $first_covered_row = true;
                        $gt2['values'][$key]['article_alternative_deliverer_name'] = sprintf('%.2f', round($amount_left, 2));
                    }

                    // update sum fields
                    $gt2['values'][$key]['article_volume'] = sprintf('%.2f', floatval($gt2['values'][$key]['article_name']) +
                                                                             floatval($gt2['values'][$key]['article_width']) +
                                                                             floatval($gt2['values'][$key]['article_weight']) +
                                                                             floatval($gt2['values'][$key]['article_barcode']) +
                                                                             floatval($gt2['values'][$key]['article_height']) +
                                                                             floatval($gt2['values'][$key]['article_deliverer_name']) +
                                                                             floatval($gt2['values'][$key]['free_text4']));

                    $gt2['values'][$key]['free_field5'] = sprintf('%.2f', $gt2['values'][$key]['article_second_code'] - $gt2['values'][$key]['article_volume']);
                    $gt2['values'][$key]['discount_surplus_field'] = 'discount_value';

                    // complete the data for the payment row
                    $current_payments_data['gt2_id'] = $vals['id'];
                    $current_payments_data['gt2_row'] = $row_num;
                    $current_payments_data['payment_id'] = $payment->get('id');
                    $current_payments_data['payment_num'] = $payment->get('num');
                    $current_payments_data['payment_date'] = $payment->get('issue_date');
                    $current_payments_data['distribute_date'] = $payment->get('status_modified');

                    if (round($current_payments_data['interest'] +
                              $current_payments_data['warranty'] +
                              $current_payments_data['lpg'] +
                              $current_payments_data['penalty'] +
                              $current_payments_data['principal'] +
                              $current_payments_data['tax'] +
                              $current_payments_data['tax_ang'], 2) > 0) {
                        $current_payments[] = $current_payments_data;
                    }
                }
            } else {
                $results['error'] = true;
                $results['messages'][] = sprintf($this->i18n('error_payments_not_distributed_successfuly'), $contract_url, $contract->get('full_num'));
            }
        }

        if (!empty($current_payments)) {
            $old_schedule = clone $contract;

            // UPDATE THE GT2 AND THE GROUPING TABLE
            // update the grouping table
            $assoc_vars = $contract->getAssocVars();

            // get the last completed row taken by the last row completed with payment id
            $completed_payment_rows = $assoc_vars[$this->settings['schedule_var_payment_id']]['value'];
            $row_num = 1;
            foreach ($completed_payment_rows as $rn => $content) {
                if (empty($content)) {
                    $row_num = $rn;
                    break;
                } else {
                    $row_num++;
                }
            }

            $insert = array();
            foreach ($current_payments as $cp) {
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_gt2_id']]['id'], $row_num, $cp['gt2_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_gt2_row']]['id'], $row_num, $cp['gt2_row'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_payment_id']]['id'], $row_num, $cp['payment_id'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_payment_num']]['id'], $row_num, $cp['payment_num'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_distribute_date']]['id'], $row_num, $cp['distribute_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_payment_date']]['id'], $row_num, $cp['payment_date'], $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_principal']]['id'], $row_num, sprintf('%.2f', $cp['principal']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_warranty']]['id'], $row_num, sprintf('%.2f', $cp['warranty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_interest']]['id'], $row_num, sprintf('%.2f', $cp['interest']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_tax']]['id'], $row_num, sprintf('%.2f', $cp['tax']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_tax_ang']]['id'], $row_num, sprintf('%.2f', $cp['tax_ang']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_payment_currency']]['id'], $row_num, sprintf('%s', $payment->get('currency')), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_currency_rate']]['id'], $row_num, sprintf('%.6f', $rate), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_penalty']]['id'], $row_num, sprintf('%.2f', $cp['penalty']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                $insert[] = sprintf('("%d", "%d", "%d", "%s", NOW(), %d, NOW(), %d, "")', $contract->get('id'), $assoc_vars[$this->settings['schedule_var_lpg']]['id'], $row_num, sprintf('%.2f', $cp['lpg']), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));

                $row_num++;
            }

            // perform the insert queries
            $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                   'VALUES ' . implode(",\n", $insert) . "\n" .
                   'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
            $this->registry['db']->Execute($sql);

            // update the gt2
            $contract->set('grouping_table_2', $gt2, true);
            $contract->calculateGT2();
            $contract->set('table_values_are_set', true, true);

            if ($contract->saveGT2Vars()) {
                // update colletaral to outstanding
                $this->calculateCollateralToOutstanding($contract);

                $filters = array('where'      => array('d.id = ' . $contract->get('id')),
                                 'model_lang' => $contract->get('model_lang'));
                $new_schedule = Documents::searchOne($this->registry, $filters);
                $new_schedule->getVars();

                Documents_History::saveData($this->registry, array('model' => $new_schedule, 'action_type' => 'edit', 'new_model' => $new_schedule, 'old_model' => $old_schedule));

                $params_payment_check = array(
                    'status'                      => $this->settings['status_fully_paid'],
                    'full_payment_var'            => $this->settings['schedule_var_repayment_date'],
                    'repayment_status'            => $this->settings['schedule_var_type_repayment'],
                    'repayment_status_in_advance' => $this->settings['schedule_type_repayment_in_advance'],
                    'repayment_status_in_time'    => $this->settings['schedule_type_repayment_in_time']
                );

                $this->checkFullPayment($new_schedule, $params_payment_check);
            } else {
                $results['error'] = true;
            }

            if ($results['error']) {
                $results['messages'][] = sprintf($this->i18n('error_payments_not_distributed_successfuly'), $contract_url, $contract->get('full_num'));
            } else {
                $results['messages'][] = $this->i18n('payment_automation_distributed_successfuly');
            }
        }

        if ($exclude_msg_tags) {
            foreach ($results['messages'] as $idx => $msg) {
                $results['messages'][$idx] = strip_tags($msg);
            }
        }

        return $results;
    }

    /*
     * Function to recalculate the remaining sum for the incomes reason related with the contract
     */
    function recalculateMainIncomesReasonSum($contract_id, $incomes_reason_id, $new_sum) {
        $add_vars = array(
            $this->settings['schedule_var_principal'], $this->settings['schedule_var_warranty'], $this->settings['schedule_var_interest'],
            $this->settings['schedule_var_tax'], $this->settings['schedule_var_tax_ang'], $this->settings['schedule_var_lpg'],
            $this->settings['schedule_var_currency_rate'], $this->settings['schedule_var_penalty']
        );

        $sql = 'SELECT `type` FROM ' . DB_TABLE_DOCUMENTS . ' WHERE `id`=' . $contract_id;
        $document_type = $this->registry['db']->GetOne($sql);

        $sql = 'SELECT `name`, `id` FROM ' . DB_TABLE_FIELDS_META . "\n" .
               'WHERE `model`="Document" AND `model_type`="' . $document_type . '" AND `name` IN ("' . implode('","', $add_vars) . '")' . "\n";
        $add_vars = $this->registry['db']->GetAssoc($sql);

        // get the payments amounts in different currency
        $sql = 'SELECT SUM(d_cstm_princ.value + d_cstm_war.value + d_cstm_intr.value + IF(d_cstm_pen.value IS NULL, 0, d_cstm_pen.value) + IF(d_cstm_lpg.value IS NULL, 0, d_cstm_lpg.value) + d_cstm_tax.value + d_cstm_ang.value)' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_princ' . "\n" .
               '  ON (d_cstm_princ.model_id=d.id AND d_cstm_princ.var_id="' . $add_vars[$this->settings['schedule_var_principal']] . '")' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_war' . "\n" .
               '  ON (d_cstm_war.model_id=d.id AND d_cstm_war.var_id="' . $add_vars[$this->settings['schedule_var_warranty']] . '" AND d_cstm_war.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_intr' . "\n" .
               '  ON (d_cstm_intr.model_id=d.id AND d_cstm_intr.var_id="' . $add_vars[$this->settings['schedule_var_interest']] . '" AND d_cstm_intr.num=d_cstm_princ.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_pen' . "\n" .
               '  ON (d_cstm_pen.model_id=d.id AND d_cstm_pen.var_id="' . $add_vars[$this->settings['schedule_var_penalty']] . '" AND d_cstm_pen.num=d_cstm_princ.num)' . "\n" .
               'LEFT JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_lpg' . "\n" .
               '  ON (d_cstm_lpg.model_id=d.id AND d_cstm_lpg.var_id="' . $add_vars[$this->settings['schedule_var_lpg']] . '" AND d_cstm_lpg.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_tax' . "\n" .
               '  ON (d_cstm_tax.model_id=d.id AND d_cstm_tax.var_id="' . $add_vars[$this->settings['schedule_var_tax']] . '" AND d_cstm_tax.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_ang' . "\n" .
               '  ON (d_cstm_ang.model_id=d.id AND d_cstm_ang.var_id="' . $add_vars[$this->settings['schedule_var_tax_ang']] . '" AND d_cstm_ang.num=d_cstm_princ.num)' . "\n" .
               'INNER JOIN ' . DB_TABLE_DOCUMENTS_CSTM . ' AS d_cstm_rate' . "\n" .
               '  ON (d_cstm_rate.model_id=d.id AND d_cstm_rate.var_id="' . $add_vars[$this->settings['schedule_var_currency_rate']] . '" AND d_cstm_rate.num=d_cstm_princ.num)' . "\n" .
               'WHERE d.id="' . $contract_id . '" AND CAST(d_cstm_rate.value as DECIMAL(9,2))!=1' . "\n";
        $current_payments_different_currency = round(($this->registry['db']->GetOne($sql)), 2);

        // get the total owed
        $sql = 'SELECT SUM(article_second_code)' . "\n" .
               'FROM ' . DB_TABLE_DOCUMENTS . ' AS d' . "\n" .
               'INNER JOIN ' . DB_TABLE_GT2_DETAILS . ' as gt2' . "\n" .
               ' ON (d.id=gt2.model_id AND gt2.model="Document")' . "\n" .
               'WHERE d.id="' . $contract_id . '"' . "\n";
        $contracts_total_owed = round($this->registry['db']->GetOne($sql), 2);

        $sql = 'SELECT `total_with_vat` FROM ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' WHERE `id`="' . $incomes_reason_id . '"' . "\n";
        $incomes_reason_current_sum = $this->registry['db']->GetOne($sql);
        $new_incomes_reason_sum = $contracts_total_owed-($current_payments_different_currency + round($new_sum, 2));

        return round($new_incomes_reason_sum-$incomes_reason_current_sum, 2);
    }

    /*
     * Automation to save the contracts which will be included in the next CUCR file
     */
    function getContractsForCucr($params) {
        // get the needed data from the report
        $report_name = 'ckr_export';
        require_once PH_MODULES_DIR . 'reports/models/reports.factory.php';
        require_once PH_MODULES_DIR . 'reports/plugins/' . $report_name . '/custom.report.query.php';

        $report = Reports::getReports(
            $this->registry,
            array(
                'name' => $report_name,
                'sanitize' => true
            )
        );

        if (!isset($report[0])) {
            return true;
        }
        $report = reset($report);
        Reports::getReportSettings($this->registry, $report->get('type'));
        $current_time = strtotime('last day of this month');

        // get the class name of the related report
        $report_name_elements = explode('_', $report_name);
        $report_name_elements = array_map(function($value){
            return ucfirst($value);
        }, $report_name_elements);
        $report_class_name = implode('_', $report_name_elements);
        $contracts_list = $report_class_name::prepareCucrContractsList($this->registry, $current_time);

        // save the data in the database
        $sql = 'INSERT INTO ' . DB_TABLE_EXPORTS_LOG . ' SET `export_type`="cucr_contracts_' . General::strftime('%Y-%m', $current_time) . '", `file_name`="", `log`="' . implode(',', $contracts_list) . '", `exported`=NOW(), `exported_by`="' . $this->registry['currentUser']->get('id') . '"';
        $this->registry['db']->Execute($sql);
        $this->updateAutomationHistory($params, 0, true);

        return true;
    }

    /*
     * Function to calculate the field collateral_to_outstanding
     */
    function calculateCollateralToOutstanding($contract) {
        $this->getCustomAutomationSettings('createPaymentPlan', 'credits');
        $gt2 = $contract->get('grouping_table_2');

        $total_principal_left = 0;
        foreach ($gt2['values'] as $row) {
            if (empty($row['deleted'])) {
                $total_principal_left += $row['average_weighted_delivery_price'];
            }
        }

        $assoc_vars = $contract->getAssocVars();
        if (!isset($assoc_vars[$this->settings['credit_collateral_percent']])) {
            return true;
        }
        $currency = (!empty($assoc_vars[$this->settings['credit_currency']]['value']) ? $assoc_vars[$this->settings['credit_currency']]['value'] : 'BGN');
        $collateral_total = (!empty($assoc_vars[$this->settings['credit_collateral_total']]['value']) ? $assoc_vars[$this->settings['credit_collateral_total']]['value'] : 0);

        // convert the sum in the BGN
        $conversion_rate = Finance_Currencies::getRate($this->registry, $currency, 'BGN');
        $total_principal_left = $conversion_rate * $total_principal_left;
        $collateral = (!$total_principal_left ? 0 : ($collateral_total / $total_principal_left) * 100);

        $vals = array();
        $vals[] = sprintf('("%d", "%d", "1", "%.2f", NOW(), %d, NOW(), %d, "%s")', $contract->get('id'), $assoc_vars[$this->settings['credit_collateral_percent']]['id'], $collateral, $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'), ($assoc_vars[$this->settings['credit_collateral_percent']]['multilang'] ? $this->registry['lang'] : ''));

        // prepare the edit query
        $sql = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
               'VALUES ' . implode(',' . "\n", $vals) . "\n" .
               'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";

        // update the fields
        $this->registry['db']->Execute($sql);

        return true;
    }

    /**
     * @param $assoc_vars
     * @return array
     */
    private function getGracePeriods($assoc_vars): array {
        // get the grace periods
        if (!empty($assoc_vars[$this->settings['grace_period_from']]['value']) && !empty($assoc_vars[$this->settings['grace_period_to']]['value'])) {
            foreach ($assoc_vars[$this->settings['grace_period_from']]['value'] as $row_num => $period_from) {
                if (!empty($period_from) && !empty($assoc_vars[$this->settings['grace_period_to']]['value'][$row_num])) {
                    $this->contract_grace_periods[] = array(
                        'from' => $period_from,
                        'to' => $assoc_vars[$this->settings['grace_period_to']]['value'][$row_num],
                    );
                }
            }
        }
        return $this->contract_grace_periods;
    }

    /**
     * @param $assoc_vars
     * @return void
     */
    private function getInterest($assoc_vars): void {
        // define the interest
        if (floatval($assoc_vars[$this->settings['credit_interest_rate_fixed']]['value'])) {
            $this->base_interest_rate = $this->interest_rate = floatval($assoc_vars[$this->settings['credit_interest_rate_fixed']]['value']);
        } elseif (floatval($assoc_vars[$this->settings['credit_interest_rate_floating']]['value'])) {
            $this->base_interest_rate = $this->interest_rate = floatval($assoc_vars[$this->settings['credit_interest_rate_floating']]['value']);
        }

        $this->interest_rate = ($this->interest_rate / 12) / 100;
        return;
    }

    /**
     * @param $assoc_vars
     * @return float
     */
    private function getPrincipal($assoc_vars): float {
        $principal = floatval($assoc_vars[$this->settings['credit_amount']]['value']);
        if ($this->is_credit_line) {
            $principal = floatval($assoc_vars[$this->settings['credit_amount_utilized']]['value']);
            $utilized = !empty($assoc_vars[$this->settings['amount_utilized_yes']]['value']) ? $assoc_vars[$this->settings['amount_utilized_yes']]['value'] : array();
            foreach ($utilized as $row => $utlz) {
                if ($utlz != $this->settings['amount_utilized_yes_opt']) {
                    $this->utilize_row = $row;
                }
            }
        }
        return $principal;
    }

    /**
     * @return void
     */
    private function getFeeMangementMonths() : void
    {
        if (!empty($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value']) && !empty($this->current_model_vars[$this->settings['credit_fee_management_type']]['value'])) {
            switch ($this->current_model_vars[$this->settings['credit_fee_management_type']]['value']) {
                case $this->settings['nom_fee_management_month']:
                    $this->fee_management_month = 1;
                    break;
                case $this->settings['nom_fee_management_trimester']:
                    $this->fee_management_month = 3;
                    break;
                case $this->settings['nom_fee_management_year']:
                    $this->fee_management_month = 12;
                    break;
            }
        }
    }

    /**
     * Function to calculate the management fee
     *
     * @param array $current_payment
     * @param $payment_number
     * @return float
     */
    private function getManagementFee(array $current_payment, int $payment_number): float
    {
        if (empty($this->settings['management_fee_calculation']) ||
            !method_exists($this, $this->settings['management_fee_calculation'])) {
            return 0;
        }
        return $this->{$this->settings['management_fee_calculation']}($current_payment, $payment_number);
    }

    /**
     * @param array $current_payment
     * @param int $payment_number
     * @return float
     */
    private function calculateManagementFeeUncoveredPrincipal(array $current_payment, int $payment_number): float
    {
        $fee = 0;
        if ($this->fee_management_month && !($payment_number % $this->fee_management_month)) {
            if ($this->fee_management_month == 1) {
                $fee = $this->previous_row_not_covered_principal * ($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value'] / 100);
            } else {
                $fee = $current_payment['last_delivery_price'] * ($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value'] / 100);
            }
        }

        return $fee;
    }

    /**
     * @param array $current_payment
     * @param int $payment_number
     * @return float
     */
    private function calculateManagementFeeGrantCredit(array $current_payment, int $payment_number): float
    {
        $fee = 0;
        if ($this->fee_management_month && !($payment_number % $this->fee_management_month)) {
            $fee = floatval($this->current_model_vars[$this->settings['credit_amount']]['value']) *
                (floatval($this->current_model_vars[$this->settings['credit_fee_management_percent']]['value']) / 100);
        }
        return $fee;
    }

    /**
     * @return float
     */
    private function getCommitmentFee() : float
    {
        if ($this->commitment_fee === null) {
            if ($this->settings['commitment_fee_fixed'] && !empty($this->current_model_vars[$this->settings['commitment_fee_fixed']]['value'])) {
                $this->commitment_fee = $this->calculateCommitmentFeeFixed();
            } else {
                $this->commitment_fee = $this->calculateCommitmentFeePercent();
            }
        }
        return $this->commitment_fee;
    }

    /**
     * @return float|int
     */
    private function calculateCommitmentFeePercent() : float
    {
        $fee_commitment = 0;
        if (!empty($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value']) && !empty($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'])) {
            if ($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'] == $this->settings['nom_approved_amount']) {
                $calc_based_on = ($this->is_credit_line ? $this->current_model_vars[$this->settings['credit_amount_utilized']]['value'] : $this->current_model_vars[$this->settings['credit_amount']]['value']);
                $fee_commitment = $calc_based_on * ($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value'] / 100);
            } elseif ($this->current_model_vars[$this->settings['credit_fee_commitment_type']]['value'] == $this->settings['nom_not_utilized_amount']) {
                $fee_commitment = $this->current_model_vars[$this->settings['credit_amount_notutilize']]['value'] * ($this->current_model_vars[$this->settings['credit_fee_commitment_percent']]['value'] / 100);
            }
        }
        return $fee_commitment;
    }

    /**
     * @return float|int
     */
    private function calculateCommitmentFeeFixed() : float
    {
        $fee_commitment = $this->current_model_vars[$this->settings['commitment_fee_fixed']]['value'];
        return $fee_commitment;
    }
}

?>
