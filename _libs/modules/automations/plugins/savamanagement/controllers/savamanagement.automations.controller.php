<?php

class Savamanagement_Automations_Controller extends Automations_Controller {

    /**
     * Set unique number to the newly added nomenclature (type: company)
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    public function setUniqueNumber($params) {
        // get the documents already numbered
        $request = &$this->registry['request'];
        if (($this->action != 'add') || !$request->isPost() || $request->get('code')) {
            return true;
        }

        $query = 'SELECT MAX(code) FROM ' . DB_TABLE_NOMENCLATURES. ' WHERE type="' . $params['start_model_type'] . '" AND deleted_by=0 AND active=1';
        $max_number = $this->registry['db']->GetOne($query);

        if ($max_number < $this->settings['starting_counter_number']) {
            $this->registry['request']->set('code', $this->settings['starting_counter_number'], 'all', true);
        } else {
            $this->registry['request']->set('code', ($max_number + 1), 'all', true);
        }

        return true;
    }

    /**
     * Auto create company when a document is set to certain status
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    public function autoCreateCompany($params) {
        $settings = $this->settings;
        $document = $params['model'];
        $old_document = clone $document;

        $this->registry->set('get_old_vars', true, true);
        $doc_vars = $document->getAssocVars(true);
        $this->registry->set('get_old_vars', false, true);

        $result = false;
        if (!empty($doc_vars) &&
            ((!empty($doc_vars[$settings['company_name_1']]['value']) && !empty($doc_vars[$settings['company_name_check_1']]['value'])) ||
             (!empty($doc_vars[$settings['company_name_2']]['value']) && !empty($doc_vars[$settings['company_name_check_2']]['value'])) ||
             (!empty($doc_vars[$settings['company_name_3']]['value']) && !empty($doc_vars[$settings['company_name_check_3']]['value'])))) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.history.php';
            require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.audit.php';

            $nomenclature = new Nomenclature($this->registry);
            if (!empty($doc_vars[$settings['company_name_1']]['value']) && !empty($doc_vars[$settings['company_name_check_1']]['value'])) {
                $nomenclature->set('name', $doc_vars[$settings['company_name_1']]['value'], true);
            } elseif (!empty($doc_vars[$settings['company_name_2']]['value']) && !empty($doc_vars[$settings['company_name_check_2']]['value'])) {
                $nomenclature->set('name', $doc_vars[$settings['company_name_2']]['value'], true);
            } elseif (!empty($doc_vars[$settings['company_name_3']]['value']) && !empty($doc_vars[$settings['company_name_check_3']]['value'])) {
                $nomenclature->set('name', $doc_vars[$settings['company_name_3']]['value'], true);
            }
            $nomenclature->set('type', $settings['nom_company_type'], true);

            // define unique num
            $query = 'SELECT MAX(code) FROM ' . DB_TABLE_NOMENCLATURES. ' WHERE type="' . $settings['nom_company_type'] . '" AND deleted_by=0 AND active=1';
            $max_number = $this->registry['db']->GetOne($query);

            if ($max_number < $settings['nom_unique_number_start']) {
                $nomenclature->set('code', $settings['nom_unique_number_start'], true);
            } else {
                $nomenclature->set('code', ($max_number+1), true);
            }

            $this->registry['db']->StartTrans();
            $old_nomenclature = clone $nomenclature;
            if ($nomenclature->save()) {
                // write history
                $nomenclature_filters = array('where'      => array('n.id = \'' . $nomenclature->get('id') . '\''),
                                              'model_lang' => $nomenclature->get('model_lang'));
                $new_nomenclature = Nomenclatures::searchOne($this->registry, $nomenclature_filters);
                Nomenclatures_History::saveData($this->registry, array('model'       => $nomenclature,
                                                                       'action_type' => 'add',
                                                                       'new_model'   => $new_nomenclature,
                                                                       'old_model'   => $old_nomenclature));

                $this->registry->set('get_old_vars', true, true);
                $nom_vars = $new_nomenclature->getAssocVars(true);
                $this->registry->set('get_old_vars', false, true);
                $old_nomenclature = clone $new_nomenclature;
                $nomenclature = clone $new_nomenclature;

                $template_insert = '("' . $new_nomenclature->get('id') . '", "%d", "%d", "%s", NOW(), "%d", NOW(), "%d", "")';
                $related_settings = array(
                    $settings['document_jurisdiction']           => $settings['nomenclature_jurisdiction'],
                    $settings['document_jurisdiction_name']      => $settings['nomenclature_jurisdiction_name'],
                    $settings['document_reg_num']                => $settings['nomenclature_reg_num'],
                    $settings['document_reg_date']               => $settings['nomenclature_reg_date'],
                    $settings['document_capital_value']          => $settings['nomenclature_capital_value'],
                    $settings['document_capital_currency']       => $settings['nomenclature_capital_currency'],
                    $settings['document_director_name']          => $settings['nomenclature_director_name'],
                    $settings['document_director_id']            => $settings['nomenclature_director_id'],
                    $settings['document_director_nom_agent_id']  => $settings['nomenclature_director_nom_agent_id'],
                    $settings['document_director_nom_agent_name']=> $settings['nomenclature_director_nom_agent_name'],
                    $settings['document_director_agent_id']      => $settings['nomenclature_director_agent_id'],
                    $settings['document_director_agent_name']    => $settings['nomenclature_director_agent_name'],
                    $settings['document_director_second_id']     => $settings['nomenclature_director_second_id'],
                    $settings['document_director_second_name']   => $settings['nomenclature_director_second_name'],
                    $settings['document_director_names']         => $settings['nomenclature_director_names'],
                    $settings['document_director_address']       => $settings['nomenclature_director_address'],
                    $settings['document_director_paid']          => $settings['nomenclature_director_paid'],
                    $settings['document_director_attorney']      => $settings['nomenclature_director_attorney'],
                    $settings['document_share_name']             => $settings['nomenclature_share_name'],
                    $settings['document_share_id']               => $settings['nomenclature_share_id'],
                    $settings['document_share_address']          => $settings['nomenclature_share_address'],
                    $settings['document_share_value']            => $settings['nomenclature_share_value'],
                    $settings['document_share_procent']          => $settings['nomenclature_share_procent'],
                    $settings['document_share_name_two']         => $settings['nomenclature_share_name_two'],
                    $settings['document_share_name_two_id']      => $settings['nomenclature_share_name_two_id'],
                    $settings['document_share_name_three']       => $settings['nomenclature_share_name_three'],
                    $settings['document_share_name_three_id']    => $settings['nomenclature_share_name_three_id'],
                    $settings['document_share_name_four']        => $settings['nomenclature_share_name_four'],
                    $settings['document_share_name_four_id']     => $settings['nomenclature_share_name_four_id'],
                    $settings['document_share_agent_names']      => $settings['nomenclature_share_agent_names'],
                    $settings['document_register_agent']         => $settings['nomenclature_register_agent'],
                    $settings['document_register_agent_name']    => $settings['nomenclature_register_agent_name'],
                    $settings['document_register_agent_address'] => $settings['nomenclature_register_agent_address'],
                    $settings['document_shares']                 => $settings['nomenclature_shares'],
                    $settings['nomenclature_share_paid']         => $settings['document_share_paid'],
                    $settings['nomenclature_share_procent_all']  => $settings['document_share_procent_all'],
                );

                $customers_contacts_needed = array($document->get('customer'));
                $insert = array();
                foreach ($related_settings as $doc_var => $nom_var) {
                    if (empty($doc_vars[$doc_var]) || empty($nom_vars[$nom_var])) {
                        continue;
                    }
                    if (is_array($doc_vars[$doc_var]['value'])) {
                        $var_values = $doc_vars[$doc_var]['value'];
                    } else {
                        $var_values = array(1 => $doc_vars[$doc_var]['value']);
                    }

                    foreach ($var_values as $num => $num_val) {
                        $insert[] = sprintf($template_insert, $nom_vars[$nom_var]['id'], $num, General::slashesEscape($num_val), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    }

                    if ($doc_var == $settings['document_register_agent']) {
                        $first_value = reset($var_values);
                        if ($first_value) {
                            $customers_contacts_needed[] = $first_value;
                        }
                    }
                }

                // get the data for the customer
                $customers_filters = array('where'      => array('c.id IN (' . implode(',', $customers_contacts_needed) . ')'),
                                           'model_lang' => $document->get('model_lang'),
                                           'sanitize'   => true);
                $customers = Customers::search($this->registry, $customers_filters);

                $idx_row = 1;
                foreach ($customers as $customer) {
                    $customer_phone = ($customer->get('phone') ? $customer->get('phone') : array());
                    $customer_gsm = ($customer->get('gsm') ? $customer->get('gsm') : array());
                    $customer_email = ($customer->get('email') ? $customer->get('email') : array());
                    $insert[] = sprintf($template_insert, $nom_vars[$settings['nomenclature_contact_id']]['id'], $idx_row, $customer->get('id'), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    $insert[] = sprintf($template_insert, $nom_vars[$settings['nomenclature_contact_name']]['id'], $idx_row, General::slashesEscape(sprintf('[%s] %s %s', $customer->get('code'), $customer->get('name'), $customer->get('lastname'))), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    $insert[] = sprintf($template_insert, $nom_vars[$settings['nomenclature_contact_phone']]['id'], $idx_row, General::slashesEscape(implode(', ', $customer_phone)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    $insert[] = sprintf($template_insert, $nom_vars[$settings['nomenclature_contact_gsm']]['id'], $idx_row, General::slashesEscape(implode(', ', $customer_gsm)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    $insert[] = sprintf($template_insert, $nom_vars[$settings['nomenclature_contact_email']]['id'], $idx_row, General::slashesEscape(implode(', ', $customer_email)), $this->registry['currentUser']->get('id'), $this->registry['currentUser']->get('id'));
                    $idx_row++;
                }

                // prepare additional vars
                $query = 'INSERT INTO ' . DB_TABLE_NOMENCLATURES_CSTM . ' (`model_id`, `var_id`, `num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)' . "\n" .
                         'VALUES ' . implode(',' . "\n", $insert) . "\n" .
                         'ON DUPLICATE KEY UPDATE `value`=VALUES(`value`), `modified`=VALUES(`modified`), `modified_by`=VALUES(`modified_by`)' . "\n";
                $this->registry['db']->Execute($query);

                $new_nomenclature = Nomenclatures::searchOne($this->registry, $nomenclature_filters);
                $this->registry->set('get_old_vars', true, true);
                $new_nomenclature->getVars(true);
                $this->registry->set('get_old_vars', false, true);

                Nomenclatures_History::saveData($this->registry, array('model'       => $nomenclature,
                                                                       'action_type' => 'edit',
                                                                       'new_model'   => $new_nomenclature,
                                                                       'old_model'   => $old_nomenclature));

                // attach the trademarks
                $query = 'SELECT a.id as aid, a.* ' . "\n" .
                         'FROM ' . DB_TABLE_AUTOMATIONS . ' as a ' . "\n" .
                         'WHERE a.module="nomenclatures" AND a.automation_type="action" AND a.start_model_type="' . $settings['nom_company_type'] . '" AND a.method LIKE "%attachTrademarkToCustomer%"' . "\n" .
                         'ORDER BY a.position';
                $record = $this->registry['db']->GetRow($query);
                if (!empty($record)) {
                    $automation_settings = General::parseSettings($record['method']);

                    if (isset($automation_settings['customer_id_field']) && isset($automation_settings['set_as_default'])) {
                        $customers_list = $new_nomenclature->getVarValue($automation_settings['customer_id_field']);

                        if (!is_array($customers_list)) {
                            $customers_list = array($customers_list);
                        }
                        $customers_list = array_filter($customers_list);

                        foreach ($customers_list as $cstm_lst) {
                            if ($automation_settings['set_as_default']) {
                                // remove the default trademark is set_as_default option is checked
                                $query_update = 'UPDATE `' . DB_TABLE_CUSTOMERS_TRADEMARKS . '` SET `is_default`=0 WHERE `parent_id`=\'' . $cstm_lst . '\'';
                                $this->registry['db']->Execute($query_update);
                            }

                            // Attach this trademark to that customer
                            $query = 'INSERT INTO `' . DB_TABLE_CUSTOMERS_TRADEMARKS . '` (`parent_id`, `trademark_id`, `is_default`) VALUES ' . "\n" .
                                     '  (\'' . $cstm_lst . '\', \'' . $new_nomenclature->get('id') . '\', \'' . $automation_settings['set_as_default'] . '\')';
                            $this->registry['db']->Execute($query);
                        }
                    }
                }
                $result = !$this->registry['db']->HasFailedTrans();

                if ($result) {
                    // success message
                    $nom_view_url = sprintf('%s?%s=nomenclatures&amp;nomenclatures=view&amp;view=%s',
                                            $_SERVER['PHP_SELF'],
                                            $this->registry['module_param'],
                                            $new_nomenclature->get('id'));
                    $this->registry['messages']->setMessage(sprintf($this->i18n('plugin_company_added_successfuly'), $nom_view_url, $new_nomenclature->get('name')));
                    $this->registry['messages']->insertInSession($this->registry);
                }
            } else {
                $result = false;
            }

            $this->registry['db']->CompleteTrans();
        }
        return $result;
    }

    /**
     * Add documents of type "Request to customer"
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    public function addRequestsToCustomer($params) {
        // Prepare some basics
        $result = true;
        $messages = &$this->registry['messages'];
        $err_msgs = array();

        // Check required settings
        $settings = $this->settings;
        $required_settings = array(
            'source_field_execution_deadline',
            'source_field_jur_order_id',
            'source_field_jur_order',

            'destination_doc_type',
            'destination_field_from_order_id',
            'destination_field_from_order',
            'destination_field_contragent_id',
            'destination_field_contragent_name',
            'destination_field_jur_order_id',
            'destination_field_jur_order',

            'gt2_fields_to_copy'
        );
        if (count(array_filter(array_intersect_key($settings, array_flip($required_settings)))) != count($required_settings)) {
            $err_msgs[] = $this->i18n('error_automations_savamanagement_addrequeststocustomer_missing_required_settings');
        } else {
            // Parse some specific settings
            $settings['gt2_fields_to_copy'] = preg_split('/\s*,\s*/', $settings['gt2_fields_to_copy']);

            // Get the document model
            $document = clone $params['model'];
            if ($document->isSanitized()) {
                $document->unsanitize();
            }

            // Get the destination document type
            $filters = array(
                'where' => array("dt.id = '{$settings['destination_doc_type']}'"),
                'model_lang' => $document->get('model_lang'),
                'sanitize' => true
            );
            $destination_doc_type = Documents_Types::searchOne($this->registry, $filters);
            if (!$destination_doc_type) {
                $err_msgs[] = $this->i18n('error_automations_savamanagement_addrequeststocustomer_no_destination_doc_type');
            } else {
                // Get the document GT2
                $get_old_vars = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $gt2 = $document->getGT2Vars();
                $this->registry->set('get_old_vars', $get_old_vars, true);

                // Prepare the data for the destination document
                // Test comment
                $data = array();
                foreach ($gt2['values'] as $row) {
                    if (!empty($row['article_deliverer'])) {
                        if (!isset($data[$row['article_deliverer']])) {
                            $data[$row['article_deliverer']] = array(
                                'name' => $row['article_deliverer_name'],
                                'max_execution_deadline' => '',
                                'gt2_rows' => array()
                            );
                        }
                        if ($data[$row['article_deliverer']]['max_execution_deadline'] < $row[$settings['source_field_execution_deadline']]) {
                            $data[$row['article_deliverer']]['max_execution_deadline'] = $row[$settings['source_field_execution_deadline']];
                        }
                        $gt2_row = array();
                        foreach ($settings['gt2_fields_to_copy'] as $field) {
                            if (preg_match('/(.+)(<|>)(.+)/', $field, $matches)) {
                                if ($matches[2] == '<') {
                                    $source_field = $matches[3];
                                    $destination_field = $matches[1];
                                } else {
                                    $source_field = $matches[1];
                                    $destination_field = $matches[3];
                                }
                            } else {
                                $source_field = $field;
                                $destination_field = $field;
                            }
                            if (isset($row[$source_field])) {
                                $gt2_row[$destination_field] = $row[$source_field];
                            }
                        }
                        if (!isset($gt2_row['article_id'])) {
                            $err_msgs[] = $this->i18n('error_automations_savamanagement_addrequeststocustomer_missing_required_gt2_fields');
                            break;
                        }
                        if (!isset($gt2_row['price'])) {
                            $gt2_row['price'] = 0;
                        }
                        if (!isset($gt2_row['quantity'])) {
                            $gt2_row['quantity'] = 1;
                        }
                        $gt2_row['subtotal'] = $gt2_row['price'] * $gt2_row['quantity'];
                        $data[$row['article_deliverer']]['gt2_rows'][] = $gt2_row;
                    }
                }
                if (empty($err_msgs)) {
                    // If there is no destination data
                    if (empty($data)) {
                        $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_no_destination_data'), $destination_doc_type->get('name_plural'));
                    } else {
                        // Start a transaction
                        $this->registry['db']->StartTrans();

                        // Prepare some main data for the new destination document
                        $main_properties = array(
                            'type'                 => $destination_doc_type->get('id'),
                            'type_code'            => $destination_doc_type->get('code'),
                            'type_name'            => $destination_doc_type->get('name'),
                            'generate_system_task' => $destination_doc_type->get('generate_system_task'),
                            'direction'            => $destination_doc_type->get('direction'),
                            'department'           => $destination_doc_type->getDefaultDepartment(),
                            'group'                => $destination_doc_type->getDefaultGroup(),
                            'media'                => $destination_doc_type->get('default_media'),
                            'media_name'           => $destination_doc_type->get('default_media_name'),
                            'active'               => '1',
                            'name'                 => $document->get('name'),
                            'trademark'            => $document->get('trademark'),
                            'model_lang'           => $document->get('model_lang'),
                            'employee'             => $this->registry['originalUser']->get('employee'),
                            'branch'               => $document->get('branch'),
                            'contact_person'       => $document->get('contact_person')
                        );
                        $old_destination_document = new Document($this->registry);
                        $old_destination_document->sanitize();
                        $successfully_added_destination_documents_links = array();

                        // Go through the collected destination data
                        foreach ($data as $customer_id => $customer) {
                            // Build a new destination document
                            $properties = $main_properties;
                            $properties['deadline'] = $customer['max_execution_deadline'];
                            $properties['customer'] = $customer_id;
                            $destination_document = new Document($this->registry, $properties);

                            // Try to save the destination document
                            if ($destination_document->save()) {
                                // Write history
                                $filters = array(
                                    'where' => array("d.id = '{$destination_document->get('id')}'"),
                                    'model_lang' => $document->get('model_lang'),
                                    'sanitize' => false,
                                    'skip_assignments' => true,
                                    'skip_permissions_check' => true
                                );
                                $new_destination_document = Documents::searchOne($this->registry, $filters);
                                $audit_parent = Documents_History::saveData(
                                    $this->registry,
                                    array(
                                        'action_type' => 'add',
                                        'old_model' => $old_destination_document,
                                        'model' => $destination_document,
                                        'new_model' => $new_destination_document
                                    )
                                );

                                // If writing history is successfull
                                if ($audit_parent) {
                                    // Set additional vars for the destination document
                                    $get_old_vars = $this->registry->get('get_old_vars');
                                    $this->registry->set('get_old_vars', true, true);
                                    $new_destination_document->getVars();
                                    $destination_vars = $new_destination_document->get('vars');
                                    $this->registry->set('get_old_vars', $get_old_vars, true);
                                    $old_new_destination_document = clone $new_destination_document;
                                    $old_new_destination_document->sanitize();
                                    $cgt2 = array();
                                    foreach ($customer['gt2_rows'] as $cgrow) {
                                        foreach ($cgrow as $cgrow_key => $cgrow_value) {
                                            $cgt2[$cgrow_key][] = $cgrow_value;
                                        }
                                    }
                                    foreach ($destination_vars as $key => $var) {
                                        switch ($var['name']) {
                                            case $settings['destination_field_from_order_id']:
                                                $var['value'] = $document->get('id');
                                                break;
                                            case $settings['destination_field_from_order']:
                                                $var['value'] = "[{$document->get('full_num')}] {$document->get('name')}";
                                                break;
                                            case $settings['destination_field_contragent_id']:
                                                $var['value'] = $document->get('customer');
                                                break;
                                            case $settings['destination_field_contragent_name']:
                                                $var['value'] = $document->get('customer_name');
                                                break;
                                            case $settings['destination_field_company_num']:
                                                $var['value'] = $document->get('custom_num');
                                                break;
                                            case $settings['destination_field_jur_order_id']:
                                                if (!isset($source_field_jur_order_id)) {
                                                    $source_field_jur_order_id = $document->getPlainVarValue($settings['source_field_jur_order_id']);
                                                }
                                                $var['value'] = $source_field_jur_order_id;
                                                break;
                                            case $settings['destination_field_jur_order']:
                                                if (!isset($source_field_jur_order)) {
                                                    $source_field_jur_order = $document->getPlainVarValue($settings['source_field_jur_order']);
                                                }
                                                $var['value'] = $source_field_jur_order;
                                                break;
                                            case 'group_table_2':
                                                $var['values'] = $customer['gt2_rows'];
                                                $var['plain_values']['currency'] = $gt2['plain_values']['currency'];
                                                break;
                                        }
                                        if (preg_match('/^plus(.+)$/', $var['name'], $matches)) {
                                            if (array_key_exists($matches[1], $cgt2)) {
                                                $var['value'] = $cgt2[$matches[1]];
                                            }
                                        }
                                        $destination_vars[$key] = $var;
                                    }
                                    $new_destination_document->set('vars', $destination_vars, true);

                                    // This param is used to allow the save of GT2 using the model's saveVars method
                                    $this->registry['request']->set('gt2_requested', true, '', true);
                                    // This param defines that the GT2 is already prepared for save (no need to get it from the DB)
                                    $new_destination_document->set('table_values_are_set', true, true);

                                    // Try to save the document vars
                                    if ($new_destination_document->saveVars()) {
                                        // Write history
                                        $filters = array(
                                            'where' => array("d.id = '{$new_destination_document->get('id')}'"),
                                            'model_lang' => $document->get('model_lang'),
                                            'sanitize' => false,
                                            'skip_assignments' => true,
                                            'skip_permissions_check' => true
                                        );
                                        $new_new_destination_document = Documents::searchOne($this->registry, $filters);
                                        $get_old_vars = $this->registry->get('get_old_vars');
                                        $this->registry->set('get_old_vars', true, true);
                                        $new_new_destination_document->getVars();
                                        $this->registry->set('get_old_vars', $get_old_vars, true);
                                        $audit_parent = Documents_History::saveData(
                                            $this->registry,
                                            array(
                                                'action_type' => 'edit',
                                                'old_model'   => $old_new_destination_document,
                                                'model'       => $new_destination_document,
                                                'new_model'   => $new_new_destination_document
                                            )
                                        );

                                        // If writing history is successfull
                                        if ($audit_parent) {
                                            // Copy assignments from source document to destination document
                                            $old_new_new_destination_document = clone $new_new_destination_document;
                                            $old_new_new_destination_document->sanitize();
                                            $assignments_types = array('owner', 'responsible', 'observer', 'decision');
                                            foreach ($assignments_types as $assignments_type) {
                                                $new_new_destination_document->set('assignments_' . $assignments_type, $document->getAssignments($assignments_type), true);
                                            }
                                            if ($new_new_destination_document->assign(true, false)) {
                                                $filters = array(
                                                    'where' => array("d.id = '{$new_new_destination_document->get('id')}'"),
                                                    'model_lang' => $new_new_destination_document->get('model_lang'),
                                                    'sanitize' => false,
                                                    'skip_assignments' => true,
                                                    'skip_permissions_check' => true
                                                );
                                                $new_assigned_destination_document = Documents::searchOne($this->registry, $filters);
                                                foreach ($assignments_types as $assignments_type) {
                                                    $new_new_destination_document->getAssignments($assignments_type);
                                                    $new_assigned_destination_document->getAssignments($assignments_type);
                                                }
                                                $audit_id = Documents_History::saveData(
                                                    $this->registry,
                                                    array(
                                                        'action_type' => 'assign',
                                                        'old_model' => $old_new_new_destination_document,
                                                        'model' => $new_new_destination_document,
                                                        'new_model' => $new_assigned_destination_document
                                                    )
                                                );
                                                if ($audit_id) {
                                                    // Save relation between the source and the destination document
                                                    $query = "
                                                        INSERT IGNORE INTO " . DB_TABLE_DOCUMENTS_RELATIVES . "
                                                          SET parent_id = '{$new_destination_document->get('id')}',
                                                            parent_model_name = 'Document',
                                                            link_to = '{$document->get('id')}',
                                                            link_to_model_name = 'Document',
                                                            origin = 'inherited',
                                                            multi_index = 0,
                                                            group_index = ''";
                                                    $this->registry['db']->Execute($query);

                                                    // Collect this document as successfully added
                                                    $successfully_added_destination_documents_links[] = sprintf(
                                                        '<a target="_blank" href="%s?%s=documents&documents=view&view=%d">%s</a>',
                                                        $_SERVER['PHP_SELF'],
                                                        $this->registry['module_param'],
                                                        $destination_document->get('id'),
                                                        $destination_document->get('full_num')
                                                    );
                                                } else {
                                                    $this->registry['db']->FailTrans();
                                                    $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_additional_data_history_failed'), $destination_doc_type->get('name'), $customer['name']);
                                                    break;
                                                }
                                            } else {
                                                $this->registry['db']->FailTrans();
                                                $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_additional_data_save_failed'), $destination_doc_type->get('name'), $customer['name']);
                                                break;
                                            }
                                        } else {
                                            $this->registry['db']->FailTrans();
                                            $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_additional_data_history_failed'), $destination_doc_type->get('name'), $customer['name']);
                                            break;
                                        }
                                    } else {
                                        $this->registry['db']->FailTrans();
                                        $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_additional_data_save_failed'), $destination_doc_type->get('name'), $customer['name']);
                                        break;
                                    }
                                } else {
                                    $this->registry['db']->FailTrans();
                                    $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_history_failed'), $destination_doc_type->get('name'), $customer['name']);
                                    break;
                                }
                            } else {
                                $this->registry['db']->FailTrans();
                                $err_msgs[] = sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_destination_document_save_failed'), $destination_doc_type->get('name_plural'), $customer['name']);
                                break;
                            }
                        }

                        // Get the result from the transaction
                        $result = !$this->registry['db']->HasFailedTrans();

                        // Complete the transaction
                        $this->registry['db']->CompleteTrans();
                    }
                }
            }
        }

        // If there are no successfully added destination documents
        if (empty($successfully_added_destination_documents_links)) {
            // Fail the result
            $result = false;
        }

        // If the result is successfull
        if ($result) {
            // Set success message
            $messages->setMessage(sprintf($this->i18n('automations_savamanagement_addrequeststocustomer_success'), $destination_doc_type->get('name_plural'), implode(', ', $successfully_added_destination_documents_links)));
        } else {
            // Set error messages
            $messages->setWarning(sprintf($this->i18n('error_automations_savamanagement_addrequeststocustomer_failed'), isset($destination_doc_type) ? $destination_doc_type->get('name_plural') : $this->i18n('error_automations_savamanagement_addrequeststocustomer_failed_documents')));
            foreach ($err_msgs as $err_msg) {
                $messages->setWarning($err_msg);
            }
        }

        // Set messages in session
        $messages->insertInSession($this->registry);

        return $result;
    }

    /**
     * Copy additional vars data from customer to document
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    // TODO: save the after_action
    public function copyCompanyDataToOrder($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $request  = &$registry['request'];

        // Prepare to collect messages
        $msgs = array(
            'wrn' => array(),
            'msg' => array()
        );

        // Get the document
        $document = $params['model'];

        // Check if the automation should be executed
        if (in_array($registry['action'], array('add', 'edit'))
                && $request->isPost()
                && $document->get('customer')
                && $request->get('copy_company_data_to_order')) {
            $this->_copyCompanyDataToOrder($this->settings, $document, $msgs);
        }

        // Set messages
        if (!empty($msgs['wrn']) || !empty($msgs['msg'])) {
            $registry['translater']->loadFile(PH_MODULES_DIR . "automations/i18n/{$registry['lang']}/automations.ini");
            if (!empty($msgs['wrn'])) {
                $registry['messages']->setWarning(sprintf($this->i18n('automations_errors_in_automation'), ' ' . $this->name));
                foreach ($msgs['wrn'] as $wrn) {
                    $registry['messages']->setWarning($wrn);
                }
            }
            if (!empty($msgs['msg'])) {
                foreach ($msgs['msg'] as $msg) {
                    $registry['messages']->setMessage($msg);
                }
            }
            $registry['messages']->insertInSession($registry);
        }

        // $this->registry['request']->set('after_action', $this->registry['request']->get('original_after_action'), 'get', true);
        return empty($msgs['wrn']);
    }

    private function _copyCompanyDataToOrder($settings, &$document, &$msgs) {
        // Get the registry
        $registry = &$this->registry;

        // Prepare the settings
        $settings['copy_company_vars'] = empty($settings['copy_company_vars']) ? array() : preg_split('/\s*,\s*/', $settings['copy_company_vars']);

        // Check if the required settings are set
        if (!empty($settings['customer_type_company']) && !empty($settings['copy_company_vars'])) {
            // Get the customer
            $filters_customer = array(
                'where' => array("c.id = '{$document->get('customer')}'"),
                'model_lang' => $document->get('model_lang'),
                'sanitize' => false,
                'skip_permissions_check' => true
            );
            $customer = Customers::searchOne($registry, $filters_customer);

            // Check if this customer is with the correct type
            if ($customer->get('type') == $settings['customer_type_company']) {
                // Unsanitize the document
                if ($document->isSanitized()) {
                    $document->unsanitize();
                    $sanitize_after = true;
                } else {
                    $sanitize_after = false;
                }

                $registry->set('get_old_vars', true, true);
                $document->getVars();
                $document_old = clone $document;
                $document_old->sanitize();
                $vars = $document->get('vars');
                $dvars = $document->getAssocVars();

                // Get the customer vars
                $cvars = $customer->getAssocVars(true);
                $registry->set('get_old_vars', false, true);
                $customer->sanitize();

                // Prepare the vars to be copied
                $copy_vars = array_flip($settings['copy_company_vars']);

                // Check if the document have all vars that should be copied
                if (count(array_intersect_key($copy_vars, $dvars)) != count($copy_vars)) {
                    $msgs['wrn'][] = $this->i18n('automations_savamanagement_copycompanydatatoorder_missing_vars_in_document');
                }
                if (count(array_intersect_key($copy_vars, $cvars)) != count($copy_vars)) {
                    // trace(array_diff_key($copy_vars, array_intersect_key($copy_vars, $cvars))); exit;
                    $msgs['wrn'][] = $this->i18n('automations_savamanagement_copycompanydatatoorder_missing_vars_in_customer');
                }
                if (empty($msgs['wrn'])) {
                    // Copy the vars
                    foreach ($vars as $k => $v) {
                        if (array_key_exists($v['name'], $copy_vars)) {
                            if (array_key_exists('value', $cvars[$v['name']])) {
                                $vars[$k]['value'] = $cvars[$v['name']]['value'];
                            } else {
                                $msgs['wrn'][] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                                break;
                            }
                            unset($copy_vars[$v['name']]);
                            if (empty($copy_vars)) {
                                break;
                            }
                        }
                    }
                    // If there are no errors at this moment
                    if (empty($msgs['wrn'])) {
                        // Try to save the changed document vars
                        $document->set('vars', $vars, true);
                        $registry['db']->StartTrans();
                        if ($document->save()) {
                            // Write history
                            $filters_document_new = array(
                                'where' => array("d.id = '{$document->get('id')}'"),
                                'model_lang' => $document->get('model_lang'),
                                'sanitize' => false,
                                'skip_permissions_check' => true,
                                'skip_assignments' => true
                            );
                            $document_new = Documents::searchOne($registry, $filters_document_new);
                            $registry->set('get_old_vars', true, true);
                            $document_new->getVars();
                            $registry->set('get_old_vars', false, true);
                            $audit_parent = Documents_History::saveData(
                                $registry,
                                array(
                                    'action_type' => 'edit',
                                    'old_model'   => $document_old,
                                    'model'       => $document,
                                    'new_model'   => $document_new
                                )
                            );
                            if ($audit_parent) {
                                $msgs['msg'][] = sprintf($this->i18n('automations_savamanagement_copycompanydatatoorder_copy_success'), $customer->get('full_name'));
                            } else {
                                $msgs['wrn'][] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                            }
                        } else {
                            $msgs['wrn'][] = sprintf($this->i18n('automations_savamanagement_copycompanydatatoorder_copy_failed'), $customer->get('full_name'));
                        }
                        if (!empty($msgs['wrn'])) {
                            $registry['db']->FailTrans();
                        }
                        $registry['db']->CompleteTrans();
                    }
                }

                // Sanitize the document
                if ($sanitize_after) {
                    $document->sanitize();
                }
            }
        } else {
            $msgs['wrn'][] = $this->i18n('automations_savamanagement_copycompanydatatoorder_missing_required_settings');
        }
    }

    /**
     * Copy additional vars data from document to customer
     *
     * @param array $params - array of automation settings (fetched from the DB)
     */
    public function copyOrderDataToCompany($params) {
        // Prepare some basics
        $registry = &$this->registry;
        $request  = &$registry['request'];

        // Execute the automation only if the request is post
        if (!$request->isPost()) {
            return true;
        }

        // Get the document
        $document = $params['model'];

        // Prepare the settings
        $settings = $this->settings;
        $settings['copy_order_vars'] = empty($settings['copy_order_vars']) ? array() : preg_split('/\s*,\s*/', $settings['copy_order_vars']);

        // Prepare to collect messages
        $msgs = array(
            'err' => array(),
            'msg' => array()
        );

        // Check if the required settings are set
        if (empty($settings['customer_type_company']) || empty($settings['copy_order_vars'])) {
            $msgs['err'][] = $this->i18n('automations_savamanagement_copyorderdatatocompany_missing_required_settings');
        } else {
            // Get the customer
            $filters = array(
                'where' => array(
                    "c.id = '{$document->get('customer')}'",
                    "c.type = '{$settings['customer_type_company']}'"
                ),
                'model_lang' => $document->get('model_lang'),
                'sanitize' => false
            );
            $customer = Customers::searchOne($registry, $filters);

            // Execute the automation only if there is a customer from the required type
            if (!$customer) {
                return true;
            }

            // Get the customer vars and the document vars
            $registry->set('get_old_vars', true, true);
            $customer->getVars();
            $vars = $customer->get('vars');
            $cvars = $customer->getAssocVars();
            $dvars = $document->getAssocVars(true);
            $registry->set('get_old_vars', false, true);

            // Prepare an old copy of the customer
            $customer_old = clone $customer;
            $customer_old->sanitize();

            // Prepare the vars to be copied
            $copy_vars = array_flip($settings['copy_order_vars']);

            // Check if the customer have all vars that should be copied
            if (count(array_intersect_key($copy_vars, $cvars)) != count($copy_vars)) {
                $msgs['err'][] = $this->i18n('automations_savamanagement_copyorderdatatocompany_missing_vars_in_customer');
            }
            if (count(array_intersect_key($copy_vars, $dvars)) != count($copy_vars)) {
                // trace(array_diff_key($copy_vars, array_intersect_key($copy_vars, $dvars))); exit;
                $msgs['err'][] = $this->i18n('automations_savamanagement_copyorderdatatocompany_missing_vars_in_document');
            }
            if (empty($msgs['err'])) {
                // Copy the vars
                foreach ($vars as $k => $v) {
                    if (array_key_exists($v['name'], $copy_vars)) {
                        if (array_key_exists('value', $dvars[$v['name']])) {
                            $vars[$k]['value'] = $dvars[$v['name']]['value'];
                        } else {
                            $msgs['err'][] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                            break;
                        }
                        unset($copy_vars[$v['name']]);
                        if (empty($copy_vars)) {
                            break;
                        }
                    }
                }
                // If there are no errors at this moment
                if (empty($msgs['err'])) {
                    // Try to save the changed customer vars
                    $customer->set('vars', $vars, true);
                    $registry['db']->StartTrans();
                    if ($customer->save()) {
                        // Write history
                        $filters_customer_new = array(
                            'where' => array("c.id = '{$customer->get('id')}'"),
                            'model_lang' => $customer->get('model_lang'),
                            'sanitize' => false
                        );
                        $customer_new = Customers::searchOne($registry, $filters_customer_new);
                        $registry->set('get_old_vars', true, true);
                        $customer_new->getVars();
                        $registry->set('get_old_vars', false, true);
                        $audit_parent = Customers_History::saveData(
                            $registry,
                            array(
                                'action_type' => 'edit',
                                'old_model'   => $customer_old,
                                'model'       => $customer,
                                'new_model'   => $customer_new
                            )
                        );
                        if ($audit_parent) {
                            $msgs['msg'][] = sprintf($this->i18n('automations_savamanagement_copyorderdatatocompany_copy_success'), $customer->get('full_name'));
                        } else {
                            $msgs['err'][] = $this->i18n('error_technical_error_please_contact_nzoom_support');
                        }
                    } else {
                        $msgs['err'][] = sprintf($this->i18n('automations_savamanagement_copyorderdatatocompany_copy_failed'), $customer->get('full_name'));
                    }
                    if (!empty($msgs['err'])) {
                        $registry['db']->FailTrans();
                    }
                    $registry['db']->CompleteTrans();
                }
            }
        }

        // Set messages
        if (!empty($msgs['err']) || !empty($msgs['msg'])) {
            $registry['translater']->loadFile(PH_MODULES_DIR . "automations/i18n/{$registry['lang']}/automations.ini");
            if (!empty($msgs['err'])) {
                $registry['messages']->setError(sprintf($this->i18n('automations_errors_in_automation'), ' ' . $this->name));
                foreach ($msgs['err'] as $err) {
                    $registry['messages']->setError($err);
                }
            }
            if (!empty($msgs['msg'])) {
                foreach ($msgs['msg'] as $msg) {
                    $registry['messages']->setMessage($msg);
                }
            }
            $registry['messages']->insertInSession($registry);
        }

        // Write automation history
//         $this->updateAutomationHistory($params, $document, empty($msgs['err']));

        return empty($msgs['err']);
    }

    /**
     * Add orders for companies
     * This is a crontab automation
     *
     * @param array $params - automation params
     * @return bool - the result of the automation
     */
    public function addCompaniesOrders($params) {
        // Execute the automation only if it's used as a crontab
        if ($params['automation_type'] != 'crontab') {
            return false;
        }

        // Prepare the result
        $result = true;

        // Validate settings
        $required_settings = array(
            'documents_type_order',
            'jurisdictions',
            'days_before_end_of_year',
            'days_before_date_of_reg',
            'order_deadline_add_days',
            'order_payment_deadline_add_days',
            'nom_type_service',
            'service_tag_annual_maintenance',
            'orders_status',
            'orders_substatus'
        );
        $required_settings_set = array_intersect_key(array_filter($this->settings), array_flip($required_settings));
        if (count($required_settings_set) != count($required_settings)) {
            $this->executionErrors[] = 'Missing required settings: ' . implode(', ', array_diff($required_settings, array_keys($required_settings_set)));
            $result = false;
            $this->updateAutomationHistory($params, 0, intval($result));
            return $result;
        }

        // Prepare some basics
        $registry = &$this->registry;
        $db = &$registry['db'];
        $lang = $registry['lang'];
        $current_date = !empty($this->settings['current_date']) ? General::strftime($this->i18n('date_iso_short'), $this->settings['current_date']) : General::strftime($this->i18n('date_iso_short'));
        $registry['translater']->loadFile(PH_MODULES_DIR . 'documents/i18n/' . $registry['lang'] . '/documents.ini');

        // Get the companies
        $query = "
            SELECT name, id
              FROM " . DB_TABLE_FIELDS_META . "
              WHERE model = 'Customer'
                AND model_type = {$params['start_model_type']}
                AND name IN (
                  'jurisdiction_field_id',
                  'date_of_reg',
                  'active_yes_no')";
        $company_fields = $db->GetAssoc($query);
        $days_before_end_of_year = false;
        if ($current_date == General::strftime($this->i18n('date_iso_short'), strtotime("-{$this->settings['days_before_end_of_year']} days", strtotime('last day of December', strtotime($current_date))))) {
            $days_before_end_of_year = true;
        }
        $query = "
            SELECT c.id
              FROM " . DB_TABLE_CUSTOMERS . " AS c
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc3
                ON (c.deleted_by = 0
                  AND c.active = 1
                  AND c.`type` = {$params['start_model_type']}
                  AND cc3.model_id = c.id
                  AND cc3.var_id = {$company_fields['active_yes_no']}
                  AND cc3.value = '1'
                  AND cc3.num = 1
                  AND cc3.lang IN ('', '{$lang}'))
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc1
                ON (cc1.model_id = c.id
                  AND cc1.var_id = {$company_fields['jurisdiction_field_id']}
                  AND cc1.num = 1
                  AND cc1.lang IN ('', '{$lang}'))
              JOIN " . DB_TABLE_CUSTOMERS_CSTM . " AS cc2
                ON (cc2.model_id = c.id
                  AND cc2.var_id = {$company_fields['date_of_reg']}
                  AND cc2.num = 1
                  AND cc2.lang IN ('', '{$lang}')
                  AND (cc2.value != ''
                    AND DATE_FORMAT(cc2.value, '%m-%d') = DATE_FORMAT(DATE_ADD('{$current_date}', INTERVAL {$this->settings['days_before_date_of_reg']} DAY), '%m-%d')" .
                    ($days_before_end_of_year ? "
                    OR " : "
                    AND ") . "cc1.value " . ($days_before_end_of_year ? '' : "NOT ") . "IN ({$this->settings['jurisdictions']})))";
        $companies = $db->GetCol($query);
        if ($companies) {
            $companies = implode(',', $companies);
            $tblDocuments = DB_TABLE_DOCUMENTS;
            $query = <<<SQL
                SELECT `customer`, `id`
                  FROM {$tblDocuments}
                  WHERE `deleted_by` = 0
                    AND `active` = 1
                    AND `type` = {$this->settings['documents_type_order']}
                    AND `customer` IN ({$companies})
                  ORDER BY `customer`,
                    `added_by` = -1 DESC,
                    `id` DESC
                SQL;
            $customersOrders = $db->GetAll($query);
            $old_orders = [];
            foreach ($customersOrders as $customerOrder) {
                if (array_key_exists($customerOrder['customer'], $old_orders)) {
                    continue;
                }
                $old_orders[$customerOrder['customer']] = $customerOrder['id'];
            }
            $companies_orders = array();
            if ($old_orders) {
                $filters = array(
                    'where' => array("d.id IN (" . implode(', ', $old_orders) . ")"),
                    'skip_permissions_check' => true,
                    'skip_assignments' => true
                );
                $old_orders = Documents::search($registry, $filters);
                foreach ($old_orders as $o) {
                    $companies_orders[$o->get('customer')] = $o;
                }
            }

            $filters = array(
                'where' => array("c.id IN ({$companies})"),
                'skip_permissions_check' => true
            );
            $companies = Customers::search($registry, $filters);

            if ($companies) {
                $filters = array(
                    'where' => array("dt.id = {$this->settings['documents_type_order']}")
                );
                $order_type = Documents_Types::searchOne($registry, $filters);
                if ($order_type) {
                    $query = "
                        SELECT n.id
                          FROM " . DB_TABLE_NOMENCLATURES . " AS n
                          JOIN " . DB_TABLE_TAGS_MODELS . " AS tm
                            ON (n.deleted_by = 0
                              AND n.active = 1
                              AND n.`type` = {$this->settings['nom_type_service']}
                              AND tm.model = 'Nomenclature'
                              AND tm.model_id = n.id
                              AND tm.tag_id = {$this->settings['service_tag_annual_maintenance']})";
                    $copy_services = $db->GetCol($query);

                    $query = "
                        SELECT settings
                          FROM " . DB_TABLE_AUTOMATIONS . "
                          WHERE module = 'documents'
                            AND start_model_type = '{$this->settings['documents_type_order']}'
                            AND method LIKE '%copyCompanyDataToOrder%'
                            AND active = 1
                          LIMIT 1";
                    $automation_settings = General::parseSettings($db->GetOne($query));

                    // Start transaction
                    $db->StartTrans();

                    // Remove time limit
                    set_time_limit(0);

                    // Skip layout permissions
                    $registry->set('edit_all', true, true);

                    foreach ($companies as $company) {
                        $order = new Document($registry, array('type' => $this->settings['documents_type_order']));
                        $old_order = clone $order;
                        $old_order->sanitize();
                        $order->set('department', $order_type->get('default_department'), true);
                        $order->set('group', $order_type->get('default_group'), true);
                        $order->set('date', $current_date, true);
                        $order->set('name', $this->i18n('automations_savamanagement_addcompaniesorders_orders_name'), true);
                        $order->set('customer', $company->get('id'), true);
                        $order->set('custom_num', $company->get('code'), true);
                        $order->set('deadline', General::strftime($this->i18n('date_iso_short'), strtotime("+{$this->settings['order_deadline_add_days']} days", strtotime($current_date))), true);

                        $copy_vars = array(
                            'date_of_reg',
                            'number_of_reg',
                            'jurisdiction_field',
                            'jurisdiction_field_id',
                            'register_agent_name',
                            'register_agent_id',
                            'register_agent_address',
                            'director_paid',
                            'attorney_date',
                            'share_paid',
                            'capital_value__text',
                            'capital_second__text',
                        );
                        $registry->set('get_old_vars', true, true);
                        $order->getVars();
                        $vars = $order->get('vars');
                        foreach ($vars as $var_key => $var) {
                            if ($var['name'] == 'assigned_user_id') {
                                $vars[$var_key]['value'] = $company->get('assigned');
                            }
                            if (in_array($var['name'], $copy_vars)) {
                                $vars[$var_key]['value'] = $company->getPlainVarValue($var['name']);
                            }
                            if ($var['type'] == 'gt2' && isset($companies_orders[$company->get('id')])) {
                                $co = $companies_orders[$company->get('id')];
                                $co->unsanitize();
                                $registry->set('get_old_vars', true, true);
                                $co->getGT2Vars();
                                $co_gt2 = $co->get('grouping_table_2');
                                $var['values'] = array();
                                foreach ($co_gt2['values'] as $co_row) {
                                    if (in_array($co_row['article_id'], $copy_services)) {
                                        $var['values'][] = $co_row;
                                    }
                                }
                                if (!empty($var['values'])) {
                                    $var['plain_values'] = $co_gt2['plain_values'];
                                    $vars[$var_key] = $var;

                                    // This param is used to allow the save of GT2 using the model's saveVars method
                                    $registry['request']->set('gt2_requested', true, '', true);
                                    // This param defines that the GT2 is already prepared for save (no need to get it from the DB)
                                    $order->set('table_values_are_set', true, true);
                                }
                            }
                            if ($var['name'] == 'payment_deadline') {
                                $vars[$var_key]['value'] = General::strftime($this->i18n('date_iso_short'), strtotime("+{$this->settings['order_payment_deadline_add_days']} days", strtotime($current_date)));
                            }
                            if ($var['name'] == 'payment_type_info') {
                                $vars[$var_key]['value'] = array();
                                foreach ($var['options'] as $option) {
                                    $vars[$var_key]['value'][] = $option['option_value'];
                                }
                            }
                        }
                        $order->set('vars', $vars, true);

                        if ($order->save()) {
                            // Make relation between the old order and the new one
                            if (isset($companies_orders[$company->get('id')])) {
                                $query = "
                                    INSERT IGNORE INTO " . DB_TABLE_DOCUMENTS_RELATIVES . "
                                      SET parent_id = {$order->get('id')},
                                        parent_model_name = 'Document',
                                        link_to = {$companies_orders[$company->get('id')]->get('id')},
                                        link_to_model_name = 'Document',
                                        origin = 'inherited'";
                                if (!$db->Execute($query)) {
                                    $this->executionErrors[] = "Failed to add relation between {$order->get('id')} and {$co->get('id')}!";
                                }
                            }

                            if (empty($this->executionErrors)) {
                                // Write history
                                $filters = array(
                                    'where' => array("d.id = '{$order->get('id')}'"),
                                    'skip_permissions_check' => true,
                                    'skip_assignments' => true
                                );
                                $new_order = Documents::searchOne($registry, $filters);
                                $history_id = Documents_History::saveData(
                                    $registry,
                                    array(
                                        'action_type' => 'add',
                                        'old_model' => $old_order,
                                        'model' => $order,
                                        'new_model' => $new_order
                                    )
                                );
                                if ($history_id) {
                                    if (!empty($automation_settings)) {
                                        $settings = $this->settings;
                                        $msgs = array(
                                            'wrn' => array(),
                                            'msg' => array()
                                        );
                                        $this->_copyCompanyDataToOrder($automation_settings, $new_order, $msgs);
                                        if (!empty($msgs['wrn'])) {
                                            $this->executionErrors[] = "Copying data from customer {$company->get('id')} to order {$new_order->get('id')} failed with next errors:";
                                            foreach ($msgs['wrn'] as $wrn) {
                                                $this->executionErrors[] = $wrn;
                                            }
                                        }
                                    }

                                    if (empty($this->executionErrors)) {
                                        // Set status
                                        $filters = array(
                                            'where' => array("d.id = '{$order->get('id')}'"),
                                            'skip_permissions_check' => true,
                                            'skip_assignments' => true
                                        );
                                        $order = Documents::searchOne($registry, $filters);
                                        $status_params = array(
                                            'module' => 'documents',
                                            'model' => $order,
                                            'model_id' => $order->get('id'),
                                            'new_status' => $this->settings['orders_status'],
                                            'new_substatus' => $this->settings['orders_substatus'],
                                            'send_mail' => true
                                        );
                                        if (!$this->status($status_params)) {
                                            $this->executionErrors[] = "Failed to set status for order {$order->get('id')}!";
                                        }

                                        if (empty($this->executionErrors)) {
                                            // Assign users
                                            $assignments_types = array('owner', 'responsible', 'observer', 'decision');
                                            foreach ($assignments_types as $assignment_type) {
                                                if (!empty($this->settings["orders_{$assignment_type}s"])) {
                                                    $order->set("assignments_{$assignment_type}", preg_split('/\s*,\s*/', $this->settings["orders_{$assignment_type}s"]), true);
                                                }
                                            }
                                            $company_assigned = $company->get('assigned');
                                            if (!empty($company_assigned)) {
                                                $assignments_responsible = $order->get('assignments_responsible');
                                                if (!empty($assignments_responsible) && is_array($assignments_responsible)) {
                                                    if (!in_array($company_assigned, $assignments_responsible)) {
                                                        $assignments_responsible[] = $company_assigned;
                                                    }
                                                } else {
                                                    $assignments_responsible = array($company_assigned);
                                                }
                                                $order->set("assignments_responsible", $assignments_responsible, true);
                                            }
                                            $have_any_assignments = false;
                                            foreach ($assignments_types as $assignment_type) {
                                                $at = $order->get("assignments_{$assignment_type}");
                                                if (!empty($at) && is_array($at)) {
                                                    $have_any_assignments = true;
                                                    break;
                                                }
                                            }
                                            if ($have_any_assignments) {
                                                if ($order->assign(true, false)) {
                                                    // Write history
                                                    $filters = array(
                                                        'where' => array("d.id = '{$order->get('id')}'"),
                                                        'skip_permissions_check' => true,
                                                        'skip_assignments' => true
                                                    );
                                                    $new_order = Documents::searchOne($registry, $filters);
                                                    $history_id = Documents_History::saveData(
                                                        $registry,
                                                        array(
                                                            'action_type' => 'assign',
                                                            'old_model' => $old_order,
                                                            'model' => $order,
                                                            'new_model' => $new_order
                                                        )
                                                    );
                                                    if (!$history_id) {
                                                        $this->executionErrors[] = "Writing history for assignments for order {$order->get('id')} failed!";
                                                    }
                                                } else {
                                                    $this->executionErrors[] = "Assignments for order {$order->get('id')} failed!";
                                                }
                                            }
                                        }
                                    }
                                } else {
                                    $this->executionErrors[] = "Writing history for order {$order->get('id')} failed!";
                                }
                            }
                        } else {
                            $this->executionErrors[] = "Adding order for customer {$company->get('id')} failed!";
                            if ($errors = $registry['messages']->getErrors()) {
                                $this->executionErrors[] = 'Errors:<br />' . print_r($errors, true);
                                $registry['messages']->unset_vars('errors');
                            }
                            if ($warnings = $registry['messages']->getWarnings()) {
                                $this->executionErrors[] = 'Warnings:<br />' . print_r($warnings, true);
                                $registry['messages']->unset_vars('warnings');
                            }
                        }

                        // Stop the loop if there`s already a problem
                        if (!empty($this->executionErrors) || $db->HasFailedTrans()) {
                            break;
                        }
                    }

                    // Stop skipping layouts permissions
                    $registry->remove('edit_all');

                    if (!empty($this->executionErrors)) {
                        $db->FailTrans();
                    }
                    if ($db->HasFailedTrans() && empty($this->executionErrors)) {
                        $this->executionErrors[] = 'Failed to add orders!';
                    }
                    $db->CompleteTrans();
                } else {
                    $this->executionErrors[] = 'Can`t access orders type!';
                }
            } else {
                $this->executionErrors[] = 'Can`t access customers!';
            }
        }

        $result = empty($this->executionErrors);

        // Write automation history
        $this->updateAutomationHistory($params, 0, intval($result));

        return $result;
    }
}

?>
