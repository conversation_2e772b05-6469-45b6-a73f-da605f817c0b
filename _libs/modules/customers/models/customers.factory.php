<?php
require_once 'customers.model.php';

/**
 * Customers model class
 */
Class Customers extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Customer';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array(), $count_only = false) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        // model types will be filled in constructWhere()
        $model_types = array();

        //where clause
        $where = self::constructWhere($registry, $filters, $model_types);

        $sql['where'] = $where . "\n";

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        if (!$count_only) {
            if (isset($filters['group']) && !empty($filters['group'])) {
                $sql['group'] = 'GROUP BY ' . $filters['group'] . "\n";
            }

            //limit (for pagination)
            $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';

            //ORDER BY clause
            if (!empty($filters['sort'])) {
                //manage the additional sort params
                $i = 0;
                $sql_for_sort = '';
                foreach ($filters['sort'] as $idx => $sort_param) {
                    if (preg_match('#^a__#', $sort_param)) {
                        $var_name = preg_replace('#^a__(\w*) (ASC|DESC)#i', '$1', $sort_param);
                        $order = preg_replace('#^a__(\w*) (ASC|DESC)#i', '$2', $sort_param);

                        //get the additional variable ids
                        $query = 'SELECT model_type AS idx, id ' . "\n" .
                                 'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                 'WHERE model="Customer" ' .
                                 ($model_types ? 'AND model_type IN (' . implode(', ', $model_types) . ')' : '') . "\n" .
                                 'AND name="' . $var_name . '" AND sortable=1';

                        $var_ids = $registry['db']->GetAssoc($query);
                        if (!$var_ids) {
                            $var_ids = array(0 => 0);
                        }

                        //$additional_sort_params[] = $sort_param;
                        // extends the sorting param so the sorting can be done correctly
                        //no matter if the value is a string or a number
                        $filters['sort'][$idx] = sprintf('ccstmsort%d.value+0 %s, ccstmsort%d.value %s', ++$i, $order, $i, $order);
                        $sql_for_sort .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstmsort' . $i . "\n" .
                                         '  ON (c.id = ccstmsort' . $i . '.model_id AND ';
                        $var_id_chunks = array();
                        foreach ($var_ids as $model_type => $var_id) {
                            $var_id_chunks[] = 'c.type = ' . $model_type . ' AND ccstmsort' . $i . '.var_id = ' . $var_id;
                        }
                        $sql_for_sort .= '(' . implode(' OR ', $var_id_chunks) . '))' . "\n";
                    }
                }
                $sort = implode(', ', $filters['sort']);
                if (!preg_match('#c\.active#', $sort)) {
                    $sort = 'ORDER BY c.active DESC, ' . $sort;
                } else {
                    $sort = 'ORDER BY ' . $sort;

                }
            } else {
                $sort = 'ORDER BY c.active DESC';
            }
            $sort .= ', c.id DESC';

            $sql['order'] = $sort . "\n";
        } else {
            $sort = '';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT(c.id) ';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES . ' AS ct' . "\n" .
                       '  ON (c.type=ct.id AND ct.active=1 AND ct.deleted=0)' . "\n";
        if (preg_match('#ci18n\.#', $sort) || preg_match('#ci18n\.#', $where) || isset($filters['field']) && (preg_match('#ci18n\.#', $filters['field']) || !$filters['field'])) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#cti18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'cti18n.name') {
            //relate to customers_types
            $sql['from'] .=  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                             '  ON (c.type=cti18n.parent_id AND cti18n.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#depi18n\.name#', $sort)) {
            //relate to departments
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                            '  ON (c.department=depi18n.parent_id AND depi18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#ui18n4\.firstname#', $sort)) {
            //relate to customer to fetch assigned
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n4' . "\n" .
                            '  ON (c.assigned=ui18n4.parent_id AND ui18n4.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to customer to fetch added_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (c.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to customer to fetch modified_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (c.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n3\.firstname#', $sort)) {
            //relate to customer to fetch deleted_by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                            '  ON (c.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")' . "\n";
        }
        if (preg_match_all('#(ccstm(\d*)\.var_id IN \([\d,\s]*\) AND )#', $sql['where'], $matches)) {
            //additional variables filters JOINS
            //move the filter "ccstm<alias_num>.var_id IN (<var_id>)" from WHERE into the ON clause of the JOIN
            //this speeds up the SQL query
            foreach ($matches[2] as $idx => $alias_num) {
                $sql['where'] = str_replace($matches[1][$idx], '', $sql['where']);
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_CSTM . ' AS ccstm' . $alias_num . "\n" .
                                '  ON ' . $matches[1][$idx] . 'c.id = ccstm' . $alias_num . '.model_id' . "\n";
            }
        }
        if (preg_match_all('#tags\d+\.tag_id\s*!?=\s*(\'\d*\')#', $where, $matches)) {
            //relate to customers tags
            foreach ($matches[0] as $key => $value) {
               $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tags' . $key . "\n" .
                               '  ON (tags' . $key . '.model=\'' . self::$modelName . '\' AND tags' . $key . '.model_id=c.id AND tags' . $key . '.tag_id=' . $matches[1][$key] . ')' . "\n";
            }
        }
        // process search by tags keyword in the simple search
        if (preg_match('#tags\d*\.tag_id\s*LIKE\s*#', $where)) {
            $sql['where'] = preg_replace('#tags\d*\.tag_id#', 'ti18n_ss.name', $sql['where']);
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_TAGS_MODELS . ' AS tags_ss' . "\n" .
                            '  ON (tags_ss.model=\'' . self::$modelName . '\' AND tags_ss.model_id=c.id)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_TAGS_I18N . ' AS ti18n_ss' . "\n" .
                            '  ON (tags_ss.tag_id=ti18n_ss.parent_id AND ti18n_ss.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match_all('#ctm\d*\.trademark_id\s*!?=\s*(\'\d*\')#', $where, $matches)) {
            //relate to customers trademarks, search in all trademarks
            foreach ($matches[0] as $key => $value) {
               $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ctm' . $key . "\n" .
                               '  ON (ctm' . $key . '.parent_id=c.id AND ctm' . $key . '.trademark_id=' . $matches[1][$key] . ')' . "\n";
            }
        }
        // process search by trademark keyword in the simple search
        if (preg_match('#ctm\.trademark_id\s*LIKE\s*#', $where)) {
            $sql['where'] = preg_replace('#ctm\.trademark_id#', 'ctm_ss_ni18n.name', $sql['where']);
            //relate to nomenclatures, search by trademarks
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ctm_ss' . "\n" .
                            '  ON (ctm_ss.parent_id=c.id)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ctm_ss_ni18n' . "\n" .
                            '  ON (ctm_ss.trademark_id=ctm_ss_ni18n.parent_id AND ctm_ss_ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#\bf\.id#', $sql['where']) || preg_match('#\bf\.id#', $sort)) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                            '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                            Files::getAdditionalWhere($registry) . "\n";
        }
        if (preg_match_all('#\b(f\.filename)([^"\']+)((["\']).*?(?<!\\\)\4)#', $sql['where'], $matches)) {
            // matches keys: 0 - the whole expression, 1 - var_name, 2 - comparison operator, 3 - searched value with surrounding quotes, 4 - the quote character
            foreach ($matches[0] as $key => $value) {
                $sql['from'] .= preg_replace('#\bf(\.|\s)#', "f$key$1",
                                'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                                '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                                Files::getAdditionalWhere($registry)) . "\n";
                $negative_search = preg_match('#!=|NOT\s+LIKE#', $matches[2][$key]);
                // replace in WHERE clause only once (the last parameter is 1)
                $sql['where'] = preg_replace('#' . preg_quote($matches[0][$key], '#') . '#', ($negative_search ? "f$key.id IS NULL" : "fi18n$key.parent_id IS NOT NULL"), $sql['where'], 1);
                if ($negative_search) {
                    $falias = 'f_';
                    $sql['from'] .= "  AND f$key.id IN (SELECT $falias$key.id FROM " . DB_TABLE_FILES . " AS $falias$key" . "\n" . '  ';
                } else {
                    $falias = 'f';
                }
                $matches[0][$key] =
                    preg_replace('#^f\.#', $falias . $key . '.', $matches[1][$key]) .
                    ($negative_search ? preg_replace('#!|NOT\s+#', '', $matches[2][$key]) : $matches[2][$key]) .
                    $matches[3][$key];
                // expand search by name into filename, name and description fields
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS ' . $falias . 'i18n' . $key . "\n" .
                                '  ON ' . $falias . 'i18n' . $key . '.parent_id=' . $falias . $key . '.id AND ' . $falias . 'i18n' . $key . '.lang=\'' . $model_lang . '\'' . "\n" .
                                ($negative_search ? '  WHERE ' . $falias . $key . '.model=\'' . self::$modelName . '\'' . "\n" : '') .
                                '    AND (' . implode(' OR ', array(
                                    $matches[0][$key],
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.name', $matches[0][$key]),
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.description', $matches[0][$key]))) .
                                    ')' .($negative_search ? ')' : '') . "\n";
            }
        }
        if (preg_match('#ni18n\.name#', $sort) || preg_match('#ni18n\.name#', $where)) {
            //relate to nomenclatures, sort by main trademarks only
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ctm' . "\n" .
                            '  ON (ctm.parent_id=c.id AND ctm.is_default=1)' . "\n" .
                            'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                            '  ON (ctm.trademark_id=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#cl\.country_name#', $sort) || isset($filters['field']) && ($filters['field'] == 'cl.country_name' || !$filters['field'])) {
            //relate to countries
            $sql['from'] .=  'LEFT JOIN ' . DB_TABLE_COUNTRY_LIST . ' AS cl' . "\n" .
                             '  ON (c.country=cl.country_code AND cl.lang="' . $lang . '")' . "\n";
        }

        if (!$count_only) {
            if (!empty($sql_for_sort)) {
                $sql['from'] .= $sql_for_sort;
            }

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $ids = $registry['db']->GetCol($query);
        }
        if ($count_only) {
            //get the total number of records for this search
            $sql['select'] = 'SELECT COUNT(DISTINCT c.id) AS total';
            $sql['group'] = '';
            $sql['order'] = '';
            $sql['limit'] = '';
            if (!empty($sql_for_sort)) {
                $sql['from'] = preg_replace('#' . preg_quote($sql_for_sort) . '#', '', $sql['from']);
            }
            $query = implode("\n", $sql);
            $total = $registry['db']->GetOne($query);
            return $total;
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            //so getIds is not needed
            $ids = self::constructWhere($registry, $filters);
        } else {
            $ids = self::getIds($registry, $filters, $sql);
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql['select'] = 'SELECT DISTINCT(c.id), c.*, ci18n.*, IF(c.is_company, c.eik, c.ucn) as eik_ucn, TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname)) AS full_name, c.id as order_idx' . "\n" .
                         '  ,"' . $model_lang . '" as model_lang ' . "\n" .
                         '  ,cti18n.name as type_name ' . "\n" .
                         '  ,ct.type_section, ct.branch_icon_file' . "\n";

        // collect all conditional (conditional for list/search/filter) fields to get
        $select_conditional = array();
        // collect all conditional (conditional for list/search/filter) table joins
        $from_conditional = array();
        if (empty($filters['get_fields']) || in_array('department', $filters['get_fields'])) {
            //relate to departments
            $select_conditional[] = 'depi18n.name as department_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS depi18n' . "\n" .
                                  '  ON (c.department=depi18n.parent_id AND depi18n.lang="' . $model_lang . '")'. "\n";
        }
        if (empty($filters['get_fields']) || in_array('group', $filters['get_fields'])) {
            //relate to groups
            $select_conditional[] = 'gi18n.name as group_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                                  '  ON (gi18n.parent_id=c.group AND gi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (empty($filters['get_fields']) || in_array('main_branch', $filters['get_fields'])) {
            //relate to customer to fetch the main branch info
            $select_conditional[] = 'cbr.id as main_branch_id, ' . "\n" .
                                    'cbri18n.name as main_branch_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS cbr' . "\n" .
                                  '  ON (cbr.parent_customer=c.id AND cbr.is_main=1 AND cbr.subtype="branch")' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS cbri18n' . "\n" .
                                  '  ON (cbr.id=cbri18n.parent_id AND cbri18n.lang="' . $model_lang . '")' . "\n";

            // relate to contact person record only when ralating to branch record
            if (empty($filters['get_fields']) || in_array('contact_person', $filters['get_fields'])) {
                //relate to customer to fetch the contact person info
                $select_conditional[] = 'ccp.id as contact_person_id, ' . "\n" .
                                        'CONCAT(ccpi18n.name, " ", ccpi18n.lastname) as contact_person_name';
                $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS ccp' . "\n" .
                                      '  ON (ccp.parent_customer=cbr.id AND ccp.is_main=1 AND ccp.subtype="contact")' . "\n" .
                                      'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ccpi18n' . "\n" .
                                      '  ON (ccp.id=ccpi18n.parent_id AND ccpi18n.lang="' . $model_lang . '")' . "\n";
            }
        }
        if (empty($filters['get_fields']) || in_array('main_trademark', $filters['get_fields'])) {
            //relate to customers trademarks and nom_i18n to fetch main trademark info
            $select_conditional[] = 'IF (ctm.trademark_id > 0, ctm.trademark_id, "") as main_trademark, ' . "\n" .
                                    'ni18n.name as main_trademark_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ctm' . "\n" .
                                  '  ON (ctm.parent_id=c.id AND ctm.is_default=1)' . "\n" .
                                  'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                  '  ON (ctm.trademark_id=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (empty($filters['get_fields']) || in_array('country', $filters['get_fields'])) {
            //relate to country list to get the country name
            $select_conditional[] = 'cl.country_name as country_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_COUNTRY_LIST . ' AS cl' . "\n" .
                                  '  ON (c.country!="" AND c.country IS NOT NULL AND c.country=cl.country_code AND cl.lang="' . $model_lang . '")' . "\n";
        }
        if (empty($filters['get_fields']) || in_array('assigned', $filters['get_fields'])) {
            //relate to customer to fetch assigned
            $select_conditional[] = 'CONCAT(ui18n4.firstname, " ", ui18n4.lastname) as assigned_to_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n4' . "\n" .
                                  '  ON (c.assigned=ui18n4.parent_id AND ui18n4.lang="' . $lang . '")' . "\n";
        }

        if (empty($filters['ignore_fields']) || !in_array('added_by_name', $filters['ignore_fields'])) {
            //relate to customer to fetch added by info
            $select_conditional[] = 'CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                                  '  ON (c.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n";
        }
        if (empty($filters['ignore_fields']) || !in_array('modified_by_name', $filters['ignore_fields'])) {
            //relate to customer to fetch modified by info
            $select_conditional[] = 'CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                                  '  ON (c.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n";
        }
        if ((empty($filters['ignore_fields']) || !in_array('deleted_by_name', $filters['ignore_fields'])) || (!empty($filters['where']) && preg_match('#c\.deleted#', implode('', $filters['where'])))) {
            //relate to customer to fetch deleted by info
            $select_conditional[] = 'CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                                  '  ON (c.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")' . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('comments', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT comments.id) AS comments';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_COMMENTS . ' AS comments' . "\n" .
                '  ON comments.model = \'' . self::$modelName . '\' AND comments.model_id = c.id' .
                ($registry['currentUser'] && $registry['currentUser']->get('is_portal') ? ' AND comments.is_portal = "1"' : '') . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('emails', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT esb.code) AS emails';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_EMAILS_SENTBOX . ' AS esb' . "\n" .
                '  ON esb.model = \'' . self::$modelName . '\' AND esb.model_id = c.id AND esb.`system` = 0 AND esb.resent_mail_id = 0' . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('history_activity', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT ch.h_id) AS history_activity';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_CUSTOMERS_HISTORY . ' AS ch' . "\n" .
                '  ON ch.model = \'' . self::$modelName . '\' AND c.id = ch.model_id AND ch.action_type IN (\'' . implode('\', \'', History::$activity_actions) . '\')' . "\n";
        }

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES . ' AS ct' . "\n" .
                       '  ON (c.type=ct.id AND ct.active=1 AND ct.deleted=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_TYPES_I18N . ' AS cti18n' . "\n" .
                       '  ON (c.type=cti18n.parent_id AND cti18n.lang="' . $lang . '")' . "\n";

        if ($select_conditional) {
            $sql['select'] .= ', ' . "\n" . implode(', ' . "\n", $select_conditional);
        }

        if ($from_conditional) {
            $sql['from'] .= "\n" . implode('', $from_conditional);
        }

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE c.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        } else {
            //getIds returned empty result the search will not be performed
            if (!empty($filters['paginate'])) {
                return array(array(), 0);
            } else {
                return array();
            }
        }

        $sql['group'] = 'GROUP BY c.id';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        $models = self::createModels($registry, $records, self::$modelName, $sanitize);

        if (!empty($filters['paginate'])) {
            if (empty($filters['total'])) {
                $filters['total'] = 0;
            }
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @param array $model_types - model types fetched from the filters
     * @return string - the prepared where clause
     */
    public static function constructWhere(&$registry, &$filters = array(), &$model_types = array()) {

        $where[] = 'WHERE (';

        //this array stores the types of customers (their ids)
        //used for the additional vars search
        $model_types = array();

        // add additional filters based on current user's rights on module, type(s) and action
        self::prepareRightsFilters($registry, $filters, $model_types);

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $key_where = array();
                $fields = array();
                if (is_array($filters['field'])) {
                    $fields = $filters['field'];
                    $filters['field'] = implode(', ', $filters['field']);
                } else {
                    $fields[] = $filters['field'];
                }
                foreach ($fields as $field) {
                    $key_where[] = General::buildClause($field, trim($filters['key']), true, 'like');
                }
            } else {
                //search in all fields
                $key_where = array();
                $module = $registry->get('module');
                $controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
            }
            $where[] = '(' . implode(" OR \n\t", $key_where) . ')) AND (';
            if (!empty($filters['hidden_type_section']) && $filters['hidden_type_section']) {
                $where[] = '(ct.type_section = \'' . urldecode($filters['hidden_type_section']) . '\')) AND (';
            } elseif (!empty($filters['hidden_type']) && $filters['hidden_type']) {
                $where[] = '(c.type = \'' . urldecode($filters['hidden_type']) . '\')) AND (';
            }

            $previous_logical = 'AND';
        }
        if (isset($filters['where'])) {
            $alias_num = 0;
            $tags_index = 0;
            $current_user_id = $registry['currentUser'] ? $registry['currentUser']->get('id') : 0;
            $previous_var_name = '';
            foreach ($filters['where'] as $idx => $filter) {
                //set default value of the logical operator
                $logical = '';

                // replace reserved word "currentUser" with current user id
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);

                // replace reserved word "currUserDepartments" with list of all departments of current user
                if (preg_match('#currUserDepartments#', $filter)) {
                    $query1 = 'SELECT department_id AS id FROM ' . DB_TABLE_USERS_DEPARTMENTS . "\n" .
                              '  WHERE parent_id = ' . $current_user_id . "\n" .
                              '  ORDER BY id ASC';
                    $d_ids = $registry['db']->getCol($query1);
                    $current_user_departments = implode(',', $d_ids);
                    $filter = preg_replace(
                        array('#(\w+\.department)\s+!#', '#=\s+\'currUserDepartments\'#', '#(^\(.+\))#'),
                        array('($1 IS NULL OR $1 NOT ', 'IN (' . $current_user_departments . ')', '$1)'),
                        $filter);
                }

                if (preg_match('#^c\.assigned#', $filter) && preg_match('#no_user_assigned#', $filter)) {
                    // gets the logical operatior
                    $logical = preg_replace('#.*\s+(AND|OR)\s*$#', '$1', $filter);
                    // if negative search
                    $not = preg_match('#!=#', $filter) ? 'NOT ' : '';

                    $filter = $not . '(c.assigned IS NULL OR c.assigned=0) ' . $logical;
                }

                //exception for the search by gsm, fax and phone
                if (preg_match('#^c\.(gsm|fax|phone)#', $filter)) {
                    // prepare the filter fo GSM
                    list($field, $operator, $value, $logical) = self::parseFilter($filter);

                    // clear the searched value from all non-numeric symbols
                    $value = preg_replace('#\D#', '', $value);
                    $value_length = strlen($value);

                    // if there are no digits the filter is used as it was defined
                    if ($value_length) {
                        // construct the filter
                        $new_filter_value = '';
                        $reg_exp_clauses = '';
                        $reg_exp = '';
                        for ($j = 0; $j < $value_length; $j++) {
                            $reg_exp_clauses .= $value[$j];
                            if ($j != $value_length - 1) {
                                $reg_exp_clauses .= '([ \-,.\\\/\(\)])*';
                            }
                        }

                        $new_operator = preg_replace('#(NOT LIKE)|(LIKE)|(=)|(!=)| |\'#', '', $operator);

                        if (preg_match('#^%s#', $new_operator)) {
                            $reg_exp_clauses = '(^|\n)' . $reg_exp_clauses;
                        }
                        if (preg_match('#%s$#', $new_operator)) {
                            $reg_exp_clauses .= '($|\n|\\\|)';
                        }

                        if (preg_match('#NOT LIKE#', $operator) || preg_match('#!=#', $operator)) {
                            $operator = "NOT REGEXP '%s'";
                        } elseif (preg_match('#LIKE#', $operator) || preg_match('#=#', $operator)) {
                            $operator = "REGEXP '%s'";
                        }

                        $filter = $field . ' ' . sprintf($operator, $reg_exp_clauses) . ' ' . $logical;
                    }
                }

                if (preg_match('#^tags\.tag_id#', $filter)) {
                    $filter = preg_replace('#tags\.#', 'tags' . $tags_index . '.', $filter);
                    if (preg_match('#!=#', $filter)) {
                        $filter = '(' . preg_replace('#(\'\d*\')#', '$1 OR tags' . $tags_index . '.tag_id IS NULL)', $filter);
                    }
                    $tags_index++;
                }

                if (preg_match('#^ctm.trademark_id#', $filter)) {
                    $filter = preg_replace('#ctm\.#', 'ctm' . $tags_index . '.', $filter);
                    if (preg_match('#!=#', $filter)) {
                        $filter = preg_replace('#(\'\d+\')#', '$1 OR ctm' . $tags_index . '.trademark_id IS NULL', $filter);
                    }
                    $tags_index++;
                }

                if (preg_match('#^(DATE_FORMAT\()?a__.*#',$filter)) {
                    //additional var is requested
                    //so parse the filter definition
                    list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                    if (preg_match('#^DATE_FORMAT\(a__.*#',$field)) {
                        //field is formatted - get field name
                        $field_name = preg_replace('#^DATE_FORMAT\(a__([^,]*),.*#', '$1', $field);
                    } else {
                        $field_name = $field;
                    }

                    $var_name = preg_replace('#^a__*#', '', $field_name);
                    if (!isset($var_ids[$var_name])) {
                        //query to get var ids
                        $query = 'SELECT id, searchable' . "\n" .
                                 'FROM ' . DB_TABLE_FIELDS_META . "\n" .
                                 'WHERE name = \'' . $var_name . '\'' . "\n" .
                                   //((!preg_match('#^a____#', $field)) ? 'AND searchable IS NOT NULL' . "\n" : '') .
                                   'AND model = \'Customer\'' . "\n" .
                                   'AND model_type IN (' . implode(',', $model_types) . ')';
                        //cache the var ids into array in order to reduce the SQL queries
                        $search_vars = $registry['db']->GetAssoc($query);
                        $var_ids[$var_name] = array_keys($search_vars);
                    }

                    if (isset($previous_logical) && trim($previous_logical) == 'OR' && $previous_var_name == $var_name) {
                        //minimize the JOINs ONLY if the search is by one and the same additional variable and the logical operator is OR
                        //in the cases when the logical operator is AND the joins SHOULD NOT be minimized (only case the variable is a grouping table)

                        //cumulate OR clauses to the previous clause
                        $cumulate_OR_clause = true;
                        $filter = '';
                        $alias_num--;
                    } else {
                        $cumulate_OR_clause = false;
                        //start to rebuild the filter
                        $filter = 'ccstm' . $alias_num . '.var_id IN (' . implode(', ', $var_ids[$var_name]) . ')';
                    }

                    if (preg_match('#^DATE_FORMAT\(a__.*#',$field)) {
                        //field is date formatted
                        $filter .= preg_replace('#^(DATE_FORMAT\()a__[^,]*(,.*)#',
                                    (($cumulate_OR_clause) ? '': ' AND ') . '$1' . 'ccstm' . $alias_num . '.value' . '$2 ', $field);

                        if (preg_match('#DATE_#', $value)) {
                            //value part has additional filter definition for the date
                            if (preg_match('#^(.*AND\s+)DATE_FORMAT\(a__.*#', $value)) {
                                //rebuild the value
                                $value = preg_replace('#^(.*AND\s+DATE_FORMAT\()a__[^,]*(,.*)#',
                                                '$1' . 'ccstm' . $alias_num . '.value' . '$2 ', $value);
                            }
                            //add operator and value to the filter
                            $filter .= sprintf(str_replace("'", '', $operator), $value);
                        } else {
                            $filter .= sprintf($operator, $value);
                        }
                    } elseif (preg_match('#number#', implode(', ', array_values($search_vars)))) {
                        //number should be cast to DECIMAL
                        $filter .= (($cumulate_OR_clause) ? '': ' AND ') . 'CAST(ccstm' . $alias_num . '.value AS DECIMAL(25, 6)) ' . sprintf($operator, $value);
                    } else {
                        //normal additional variable
                        //just set the value
                        $filter .= (($cumulate_OR_clause) ? '': ' AND ') . 'ccstm' . $alias_num . '.value ' . sprintf($operator, $value);
                    }

                    $filter .= ' ' . $logical;
                    $alias_num ++;

                    //save the previous additional variable name so that the JOINs for additional variables could be minimized
                    $previous_var_name = $var_name;
                }

                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";

                //save the previous logical operator so that the JOINs for additional variables could be minimized
                $previous_logical = ($logical == '') ? preg_replace('#.*(OR|AND)\s*\n*$#', '$1', $filter) : $logical;
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        $where = preg_replace('/\s\(\)/', ' 1', $where);
        if (!preg_match('#c\.deleted#', $where)) {
            $where .= ' AND c.deleted = 0';
        }
        if (!preg_match('#c\.subtype#', $where)) {
            $where .= ' AND c.subtype = \'normal\'';
        }

        return $where;
    }

    /**
     * Adds additional conditions to WHERE clause based on current user's permissions for action
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @param array $model_types - this array stores the types of customers (their ids), used for the additional vars search
     * @return bool
     */
    private static function prepareRightsFilters(&$registry, &$filters, &$model_types) {
        $current_user_id = $registry['currentUser'] ? $registry['currentUser']->get('id') : 0;
        $rights = $registry['currentUser'] ? $registry['currentUser']->get('rights') : array();
        $module = 'customers';

        // get action to check permissions for according to action in registry
        if ($registry['action'] == 'filter' || $registry['action'] == 'dashlet') {
            $action = 'search';
        } elseif ($registry['action'] == 'subpanel' || $registry['action'] == 'ajax_sidepanel') {
            $action = 'list';
        } elseif ($registry['action'] == 'ajax_get_totals') {
            $action = preg_replace('#_customer$#', '', $filters['session_param']);
            if ($action == 'list' || $action == 'search') {
                //
            } elseif ($action == 'filter') {
                $action = 'search';
            } else {
                $action = 'list';
            }
        } elseif ($registry['module'] == 'customers') {
            if ($registry['action'] == 'export' || $registry['action'] == 'printlist') {
                if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                    $action = 'search';
                } else {
                    $action = 'list';
                }
            } else {
                $action = $registry['action'];
            }
        } elseif ($registry->get('getOneRequested')) {
            // looking for customer by id from some other module (keep action as is)
            $action = $registry['action'];
        } else {
            $action = 'list';
        }

        // When the action is ajax_select and there is a flag to check the permissions of the current user
        if ($action == 'ajax_select' && isset($filters['check_user_permissions']) && $filters['check_user_permissions'] == true) {
            // Use the same permissions as the search action
            $action = 'search';
        }

        // permissions for types are checked only for multiple actions
        // NOTE: permissions are not checked when searching customer e-mails from autocompleter in "communications"
        // NOTE: to allow autocomplete filter by additional variables accumulating $model_types allow 'ajax_select' action
        //       $model_types array is used to search by those additional variables
        if ($action != 'list' && $action != 'search' && $action != 'ajax_select') {
            return true;
        }

        $cust_types_filters = array();

        require_once PH_MODULES_DIR . 'customers/models/customers.sections.factory.php';
        // searches the filters for sections or types
        if (isset($filters['where'])) {
            $model_types = array();

            // use reflection to get static property of class
            $controller_name = General::singular2plural(self::$modelName) . '_Controller';
            require_once PH_MODULES_DIR . $module . '/controllers/' . $module . '.controller.php';
            $reflection_class = new ReflectionClass($controller_name);
            $prop = 'searchAdditionalVarsSwitch';
            $static_props = $reflection_class->getStaticProperties();
            $searchAdditionalVarsSwitch = array_key_exists($prop, $static_props) !== false ? $static_props[$prop] : '';
            unset($reflection_class);

            // keep logical operator from previous iteration
            $prev_logical_operator = '';
            // flag whether previous iteraton was positive search by type or section
            $prev_was_type_or_section = false;
            // flag whether there is a positive search by type or section in an OR clause
            $type_or_section_in_OR_clause = false;

            foreach ($filters['where'] as $key => $filter_where) {
                if (preg_match('/=\s*$/', $filter_where)) {
                    //clear the empty filters
                    unset($filters['where'][$key]);
                    continue;
                }

                //make sure all the types are fetched from the filters and only then manage the rest of the filters
                //this is necessary because the search by additional vars needs ALL the types (that's why we iterate $filters['where'] twice)
                if (preg_match('#^' . preg_quote($searchAdditionalVarsSwitch) . '#', $filter_where)) {
                    $parsed_filter = self::parseFilter($filter_where);

                    if (preg_match('#^=#', $parsed_filter[1]) && $parsed_filter[2] !== '') {
                        //check if the compare operator is EXACTLY "equals to" (=)
                        $model_types[] = $parsed_filter[2];
                    }
                }

                list($filter_name, $filter_compare, $filter_value, $logical_operator) = self::parseFilter($filter_where);
                if ($filter_name == 'c.type' && !preg_match('#(^| *)NOT( *)#', $filter_compare) && !preg_match('#(^| *)!=( *)#', $filter_compare)) {
                    $filtered_values = array();
                    $all_values = preg_split('# *, *#', $filter_value);
                    foreach ($all_values as $single_value) {
                        $filtered_values[] = $single_value;
                    }

                    // if a positive search is found then all the
                    // searched types are set in the separate array
                    $cust_types_filters[] = array(
                        'types'             => $filtered_values,
                        'idx'               => $key,
                        'logical_operator'  => $logical_operator
                    );

                    if ($prev_logical_operator == 'OR' && !$prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = true;
                } else if ($filter_name == 'ct.type_section' && !preg_match('#(^| *)NOT( *)#', $filter_compare) && !preg_match('#(^| *)!=( *)#', $filter_compare)) {
                    $filtered_values = array();
                    $all_values = preg_split('# *, *#', $filter_value);
                    foreach ($all_values as $single_value) {
                        $filtered_values[] = $single_value;
                    }

                    // if a positive search for section is found, then all
                    // active types in the section are found
                    $section_types = Customers_Sections::searchSectionWithTypesAdded($registry, $filtered_values);

                    $section_types_ids = array();
                    foreach ($section_types as $s_type) {
                        $section_types_ids[] = $s_type['type_id'];
                    }

                    $cust_types_filters[] = array(
                        'types'             => $section_types_ids,
                        'idx'               => $key,
                        'logical_operator'  => $logical_operator
                    );

                    if ($prev_logical_operator == 'OR' && !$prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = true;
                } else {
                    // not positive search by type or section
                    if ($prev_logical_operator == 'OR' && $prev_was_type_or_section) {
                        $type_or_section_in_OR_clause = true;
                    }
                    $prev_was_type_or_section = false;
                }
                // keep last logical operator for next iteration
                $prev_logical_operator = $logical_operator;
            }

            $inactive_model_types = array();
            if (!empty($cust_types_filters)) {
                if ($type_or_section_in_OR_clause) {
                    // take all inactive types
                    $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS_TYPES . ' WHERE active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                } elseif ($model_types) {
                    // take inactive types from model types
                    $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS_TYPES .
                             ' WHERE id IN (' . implode(', ', $model_types) . ') AND active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                }

                // if search by types, check if they are active and keep only active ones in types filters
                if ($model_types && $inactive_model_types) {
                    foreach ($cust_types_filters as $idx => $ctf) {
                        $active_model_types = array_diff($ctf['types'], $inactive_model_types);
                        if ($active_model_types) {
                            $cust_types_filters[$idx]['types'] = $active_model_types;
                        } else {
                            $cust_types_filters[$idx]['types'] = array('');
                        }
                    }
                }
            }
        }

        // if there are no filters set for section or a type, all active customers types are taken;
        // flag 'skip_permissions_check' is used for skipping check of permissions by type
        if (empty($cust_types_filters) && empty($filters['skip_permissions_check'])) {
            $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS_TYPES . ' WHERE active=1 AND deleted=0';
            $all_customer_types_ids = $registry['db']->GetCol($query);
            $cust_types_filters[] = array(
                'types'             => $all_customer_types_ids,
                'idx'               => '',
                'logical_operator'  => 'AND'
            );
        }

        if (!empty($cust_types_filters)) {
            foreach ($cust_types_filters as $ctf) {
                $current_rights_where = array();
                foreach ($ctf['types'] as $ct) {
                    $current_right = ($registry['module'] == $module || (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) &&
                                     isset($rights[$module . $ct][$action]) ? $rights[$module . $ct][$action] : '';

                    //additional 'where' for hiding not allowed models
                    if ($current_user_id && $current_right) {
                        if ($current_right == 'all') {
                            $current_rights_where[] = "c.type='" . $ct . "'";
                        } elseif ($current_right == 'mine') {
                            $current_rights_where[] = "(c.added_by=$current_user_id AND c.type='" . $ct . "')";
                        } elseif ($current_right == 'group') {
                            $user_groups = $registry['currentUser']->get('groups');
                            $user_departments = $registry['currentUser']->get('departments');
                            $current_rights_where[] = "((c.added_by=$current_user_id" .
                                                        (count($user_groups) ? ' OR c.`group` IN (' . implode(',', $user_groups) . ')' : '') .
                                                        (count($user_departments) ? ' OR c.department IN (' . implode(',', $user_departments) . ')' : '') .
                                                        ") AND c.type='" . $ct . "')";
                        } elseif ($current_right == 'none' || $current_right == '') {
                            $current_rights_where[] = '0';
                        }
                    }
                }
                if (!empty($current_rights_where)) {
                    $changed_filter = '(' . implode(' OR ' . "\n", $current_rights_where) . ') ' . ($ctf['logical_operator'] ? $ctf['logical_operator'] : 'AND');
                    if ($ctf['idx'] !== '') {
                        $filters['where'][$ctf['idx']] = $changed_filter;
                    } else {
                        if (! isset($filters['where'])) {
                            $filters['where'] = array();
                        }
                        array_unshift($filters['where'], $changed_filter);
                    }
                }
            }
        }

        return true;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {

        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        if ($registry['request']->isPost() && ($registry['action'] == 'subpanel' ||
        preg_match('#^' . General::plural2singular($registry['module']) . '\d+_ajax_(referent_)?$#', $sessionPrefix))) {
            // saving filters for records in subpanel
            // do not process filters switch keys if found in request
        } elseif ($registry['request']->isRequested('type_section') && $registry['request']->get('type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'ct.type_section = \'' . urldecode($registry['request']->get('type_section')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->get('type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'c.type = \'' . urldecode($registry['request']->get('type')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type_section') && $registry['request']->get('hidden_type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'ct.type_section = \'' . urldecode($registry['request']->get('hidden_type_section')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type') && $registry['request']->get('hidden_type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'c.type = \'' . urldecode($registry['request']->get('hidden_type')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->isRequested('type_section') &&
                  !$registry['request']->get('type') && !$registry['request']->get('type_section')) {
            $filters['display'] = $registry['session']->get($sessionParam)['display'] ?? '';
            $registry['session']->remove($sessionParam);
        }

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']       = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']     = sprintf("modified=now()");
        $set['modified_by']  = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();

        if ($status == 'activate') {
            $filters = array('where' => array('c.id IN (' . implode(', ', $ids) . ')',
                                              'COALESCE(c.num, \'\') = \'\'',
                                              'c.active = 0',
                                              'ct.counter > 0'),
                             'sanitize' => true);
            $customers = self::search($registry, $filters);
            foreach ($customers as $cust) {
                $cust->unsanitize();
                $cust->set('active', 1, true);
                $cust->setNumber();
                $cust->sanitize();
                unset($cust);
            }
        }

        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Adds multiple models
     *
     * @param Registry $registry - the main registry
     * @param array $vars - multiadd variables (basic + additional)
     * @param Customers_Controller $controller - controller object that method is called from
     * @return void
     */
    public static function multiAdd(&$registry, $vars, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];
        //first variable for check rows
        $first_var_name = $vars[0]['name'];
        //start number models for adding
        $num_rows = 1;

        if ($request->isPost()) {

            // clear extra whitespace
            $request->clearWhitespace(array('name', 'lastname'));

            $actionCompleted = true;
            $first_var = $request->get($first_var_name);
            $num_rows = count($first_var);

            // create blank model for audit
            $old_customer = new Customer($registry, array('type' => $request['type']));
            $registry->set('get_old_vars', true, true);
            $old_customer->getVars();
            $registry->set('get_old_vars', false, true);
            $old_customer->sanitize();

            $db->StartTrans();

            //get additional post values
            $add_vars = self::getAdditionalPostVars($request);
            $row_counter = 0;
            $old_model = $new_models = array();
            //build the model from the POST
            foreach ($first_var as $i => $value) {
                $row_counter++;
                $customers[$i] = Customers::buildModelIndex($registry, $i);
                $customers[$i]->set('type_name', $registry->get('type_name'), true);

                //save main variables
                self::multiSave($request, $actionCompleted, $customers[$i], $i, $add_vars, $row_counter);

                if ($actionCompleted) {
                    $customer = clone $customers[$i];
                    $customer->getVars();
                    $filters = array('where' => array('c.id = ' . $customer->get('id')),
                                     'model_lang' => $customer->get('model_lang'));
                    $new_customer = Customers::searchOne($registry, $filters);
                    $registry->set('get_old_vars', true, true);
                    $new_customer->getVars();
                    $registry->set('get_old_vars', false, true);

                    Customers_History::saveData($registry, array('model' => $customer, 'action_type' => 'multiadd', 'new_model' => $new_customer, 'old_model' => $old_customer));

                    $temp_model = clone $new_customer;
                    $new_models[] = $temp_model->sanitize();
                    $old_model[] = $temp_model->sanitize();
                }
            }

            if ($actionCompleted) {
            //the models were successfully saved
                $db->CompleteTrans();
                //set models for automations
                $controller->old_model = $old_model;
                $controller->new_models = $new_models;
                return true;
            } else {
            //some error occurred
                $db->FailTrans();
                foreach ($first_var as $i => $value) {
                    foreach ($vars as $k => $var) {
                        if (in_array($var['name'], $customers[$i]->contactParameters)) {
                            $contacts = array();
                            $notes = $customers[$i]->get($var['name'] . '_note');
                            foreach ($customers[$i]->get($var['name']) as $idx => $value) {
                                $contacts[] = sprintf('%s%s', $value, (!empty($notes[$idx])) ? '|'. $notes[$idx] : '');
                            }
                            $vars[$k]['val'] = implode(', ', $contacts);
                        } elseif ($var['name'] == 'code' && (!empty($var['hidden']) || !empty($var['readonly']))) {
                            // clear codes as user cannot edit them and due to
                            // action failure for some models they probably contain duplicates
                            $vars[$k]['val'] = '';
                        } else {
                            if ($vars[$k]['type'] == 'autocompleter') {
                                $vars[$k]['value'] = $customers[$i]->get($var['name']);
                                // linkification
                                if (!empty($vars[$k]['autocomplete']) && !empty($vars[$k]['autocomplete']['id_var']) &&
                                !empty($vars[$k]['autocomplete']['view_mode']) && $vars[$k]['autocomplete']['view_mode'] == 'link') {
                                    $vars[$k]['value_id'] =
                                        $customers[$i]->isDefined($vars[$k]['autocomplete']['id_var']) ?
                                        $customers[$i]->get($vars[$k]['autocomplete']['id_var']) :
                                        '';
                                }
                            } else {
                                $vars[$k]['val'] = $customers[$i]->get($var['name']);
                            }
                        }
                    }

                    $customers[$i]->set('multivars', $vars, true);
                    $customers[$i]->sanitize();
                }
                $registry->set('customers', $customers);
                return false;
            }

        } else {
            //create empty models
            for ($i = 0; $i < $num_rows; $i++) {
                $customers[$i] = Customers::buildModel($registry);
                $customers[$i]->set('multivars', $vars, true);
                $customers[$i]->sanitize();
            }
            $registry->set('customers', $customers);
        }
    }

    /**
     * Edit multiple models
     *
     * @param Registry $registry - the main registry
     * @param array $vars - multiedit variables (basic + additional)
     * @param string $type - type of all edited models
     * @param int $is_company - kind of customer (company - 1, person - 0)
     * @param Customers_Controller $controller - controller object that method is called from
     * @return void
     */
    public static function multiEdit(&$registry, $vars, $type, $is_company, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];

        // clear extra whitespace
        $request->clearWhitespace(array('name', 'lastname'));

        //ids of models to edit
        $ids = $request->get('items');
        //check to save models
        $actionCompleted = true;
        $db->StartTrans();
        //get additional post values
        $add_vars = self::getAdditionalPostVars($request);
        //build the models from the POST
        $row_counter = 0;
        $old_model = $new_models = array();
        foreach ($ids as $i => $id) {
            $customers[$i] = Customers::buildModelIndex($registry, $i);
            $customers[$i]->set('id', $id, true);
            $customers[$i]->set('is_company', $is_company, true);
            $customers[$i]->set('type', $type, true);
            $customers[$i]->set('type_name', $registry->get('type_name'), true);

            $row_counter++;
            $filters = array('where' => array('c.id = ' . $id),
                             'model_lang' => $registry->get('model_lang'));
            $old_customer = Customers::searchOne($registry, $filters);
            $registry->set('get_old_vars', true, true);
            $old_customer->getVars();
            $registry->set('get_old_vars', false, true);
            $old_customer->sanitize();

            self::multiSave($request, $actionCompleted, $customers[$i], $i, $add_vars, $row_counter);

            if ($actionCompleted) {
                $customer = clone $customers[$i];
                $customer->getVars();
                $filters = array('where' => array('c.id = ' . $id),
                                 'model_lang' => $registry->get('model_lang'));
                $new_customer = Customers::searchOne($registry, $filters);
                $registry->set('get_old_vars', true, true);
                $new_customer->getVars();
                $registry->set('get_old_vars', false, true);
                $new_customer->sanitize();

                Customers_History::saveData($registry, array('model' => $customer, 'action_type' => 'multiedit', 'new_model' => $new_customer, 'old_model' => $old_customer));

                $new_models[] = $new_customer;
                $old_model[] = $old_customer;

                // if updating emails targetlists is enabled
                if ($registry['config']->getParam('customers', 'update_emails_targetlists')) {
                    self::updateEmailsTargetlists($registry, $new_customer, $old_customer);
                }
            }
        }

        if ($actionCompleted) {
            //the models were successfully saved
            $db->CompleteTrans();
            //set models for automations
            $controller->old_model = $old_model;
            $controller->new_models = $new_models;
            return true;
        } else {
            //some error occurred
            //show corresponding error(s)
            $db->FailTrans();
            foreach ($ids as $i => $id) {
                foreach ($vars as $k => $var) {
                    if (in_array($var['name'], $customers[$i]->contactParameters)) {
                        $contacts = array();
                        $notes = $customers[$i]->get($var['name'] . '_note');
                        foreach ($customers[$i]->get($var['name']) as $idx => $value) {
                            $contacts[] = sprintf('%s%s', $value, (!empty($notes[$idx])) ? '|'. $notes[$idx] : '');
                        }
                        $vars[$k]['val'] = implode(', ', $contacts);
                    } else {
                        if ($vars[$k]['type'] == 'autocompleter') {
                            $vars[$k]['value'] = $customers[$i]->get($var['name']);
                            // linkification
                            if (!empty($vars[$k]['autocomplete']) && !empty($vars[$k]['autocomplete']['id_var']) &&
                            !empty($vars[$k]['autocomplete']['view_mode']) && $vars[$k]['autocomplete']['view_mode'] == 'link') {
                                $vars[$k]['value_id'] =
                                    $customers[$i]->isDefined($vars[$k]['autocomplete']['id_var']) ?
                                    $customers[$i]->get($vars[$k]['autocomplete']['id_var']) :
                                    $customers[$i]->getVarValue(preg_replace('#^' . preg_quote(PH_ADDITIONAL_VAR_PREFIX) . '#', '', $vars[$k]['autocomplete']['id_var']));
                            }
                        } elseif ($var['type'] == 'file_upload' && strpos($var['name'], PH_ADDITIONAL_VAR_PREFIX) === 0) {
                            $assoc_vars = $customers[$i]->getAssocVars();
                            $var_name = str_replace(PH_ADDITIONAL_VAR_PREFIX, '', $var['name']);
                            if (array_key_exists($var_name, $assoc_vars)) {
                                $vars[$k]['val'] = $assoc_vars[$var_name]['value'];
                                if (!empty($assoc_vars[$var_name]['deleteid'])) {
                                    $vars[$k]['deleteid'] = $assoc_vars[$var_name]['deleteid'];
                                }
                            } else {
                                $vars[$k]['val'] = '';
                            }
                            $vars[$k]['model_id'] = $id;
                        } else {
                            $vars[$k]['val'] = $customers[$i]->get($var['name']);
                        }
                    }
                }
                $customers[$i]->set('multivars', $vars, true);
                $customers[$i]->sanitize();
            }
            $registry->set('customers', $customers);
            return false;
        }
    }

    /**
     * Save basic and additional variables for index models
     *
     * @param Request $request - request object
     * @param bool $actionCompleted - flag if save was successful
     * @param Customer $customer - model to save
     * @param int $i - index of model in request (starting from 0)
     * @param array $add_vars - additional variables
     * @param int $index - index of model in array of models (starting from 1)
     */
    public static function multiSave(&$request, &$actionCompleted, &$customer, $i, $add_vars, $index = 0) {
        $customer->index_number = $i+1;

        // prepare additional vars for save
        foreach ($add_vars as $var_name => $post_value) {
            if (is_array($post_value)) {
                if (isset($post_value[$i])) {
                    $request->set($var_name, $post_value[$i], 'post', true);
                } elseif (isset($post_value['tmp_name'])) {
                    $file_upload = array();
                    foreach ($post_value as $k => $v) {
                        if (is_array($v) && array_key_exists($i, $v)) {
                            $file_upload[$k] = $v[$i];
                        }
                    }
                    $_FILES[$var_name] = $file_upload;
                } else {
                    $request->remove($var_name);
                }
            } else {
                $request->set($var_name, $post_value, 'post', true);
            }
        }
        $records = $customer->getFieldsMulti($customer->registry['action']);
        $customer->processFields($records);
        $customer->set('vars', $records, true);

        if ($customer->save()) {

            $customer->slashesStrip();
        } else {
            //some error occurred
            $actionCompleted = false;
        }

        return true;
    }

    /**
     * Get values of additional variables from POST and FILES (used in multi- actions)
     *
     * @param Request $request - request object
     * @return array - additional variables
     */
    public static function getAdditionalPostVars(&$request) {
        $post = $request->getAll('post');
        //get additional post values
        $add_vars = array();
        foreach ($post as $post_name => $post_value) {
            if (strpos($post_name, PH_ADDITIONAL_VAR_PREFIX) === 0) {
                $add_vars[str_replace(PH_ADDITIONAL_VAR_PREFIX, '', $post_name)] = $post_value;
            }
        }

        if (!empty($_FILES)) {
            foreach ($_FILES as $k => $v) {
                if (strpos($k, PH_ADDITIONAL_VAR_PREFIX) === 0) {
                    $add_vars[str_replace(PH_ADDITIONAL_VAR_PREFIX, '', $k)] = $v;
                    foreach (array('dbid_', 'deleteid_') as $prefix) {
                        if (!empty($post[$prefix . $k])) {
                            $add_vars[$prefix . str_replace(PH_ADDITIONAL_VAR_PREFIX, '', $k)] = $post[$prefix . $k];
                        }
                    }
                }
            }
        }

        return $add_vars;
    }

    /**
     * Check to delete specified models
     * if allow delete - return true
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function allowDelete(&$registry, $ids) {
        $db = $registry['db'];

        //get customers info
        $model_lang = $registry['lang'];
        $query = 'SELECT c.id, c.*, ci18n.* FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       ' ON (c.id=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                       ' WHERE c.id in (' . implode(',', $ids) . ')';
        $customers = $db->GetAssoc($query);

        $rids = array();

        //get related models
        $query = 'SELECT c.id, count(d.id) + count(ad.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
            ' LEFT JOIN ' . DB_TABLE_DOCUMENTS . ' as d ON c.id=d.customer AND d.deleted_by=0' . "\n" .
            ' LEFT JOIN `' . DB_TABLE_ARCHIVE_DOCUMENTS . '` as ad ON c.id=ad.customer AND ad.deleted_by=0' . "\n" .
            ' WHERE c.id in (' . implode(',', $ids) . ') GROUP BY c.id HAVING cnum > 0';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['documents'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_TURNOVERS . ' as t' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=t.customer GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['turnovers'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_TASKS . ' as t' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=t.customer AND t.deleted_by=0 GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['tasks'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_PROJECTS . ' as p' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=p.customer AND p.deleted_by=0 GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['projects'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_CONTRACTS . ' as co' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=co.customer AND co.subtype!="restore_contract" AND co.deleted_by=0 GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['contracts'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_FINANCE_EXPENSES_REASONS . ' as f' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=f.customer GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['finance_expenses_reasons'] = $cnt;
        }

        $query = 'SELECT DISTINCT c.id, f.type FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as f' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=f.customer ORDER BY c.id';
        $records = $db->GetAll($query);
        $inc_types = array();
        foreach ($records as $key => $row) {
            $rids[$row['id']]['finance_incomes_reasons'] = 1;
            $inc_types[$row['id']][] = $row['type'];
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                 ' ,' . DB_TABLE_FINANCE_PAYMENTS . ' as f' . "\n" .
                 ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=f.customer GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['finance_payments'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' as f' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=f.customer GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['finance_warehouses_documents'] = $cnt;
        }

        $query = 'SELECT c.id, count(c.id) as cnum FROM ' . DB_TABLE_CUSTOMERS . ' as c' . "\n" .
                    ' ,' . DB_TABLE_MINITASKS . ' as m' . "\n" .
                    ' WHERE c.id in (' . implode(',', $ids) . ') AND c.id=m.customer AND m.status="opened" GROUP BY c.id';
        $records = $db->GetAssoc($query);
        foreach ($records as $id => $cnt) {
            $rids[$id]['minitasks'] = $cnt;
        }

        if ($rids) {
            //prepare error messages
            $msg = '';
            foreach ($rids as $id => $rel) {
                $customer = $customers[$id];
                $customer_name = $customer['name'] . ($customer['lastname']?' ' . $customer['lastname']:'');
                $urlencoded_name = urlencode($customer_name);
                $urlencoded_code = urlencode($customer['code']);
                $msg .= '<br />' . $customer_name . ': ';
                $links = array();
                foreach ($rel as $module => $cnt) {
                    switch ($module) {
                    case 'documents':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=documents&documents=search&search_document=1&search_module=documents&search_controller=&filters_action=&search_fields[0]=d.customer&search_fields_prev[0]=d.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&logical_operator[0]=AND&search_fields[1]=d.archive&search_fields_prev[1]=d.archive&compare_options[1]=%3D+%27%25s%27&values[1]=all&sort[0]=d.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'turnovers':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=turnovers&turnovers=search&search_module=turnovers&search_controller=&filters_action=&search_fields[0]=t.customer&search_fields_prev[0]=t.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=t.day&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'tasks':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=tasks&tasks=search&search_module=tasks&search_controller=&filters_action=&search_fields[0]=t.customer&search_fields_prev[0]=t.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=t.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'projects':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=projects&projects=search&search_module=projects&search_controller=&filters_action=&search_fields[0]=p.customer&search_fields_prev[0]=p.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=p.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'events':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=events&events=search&search_module=events&search_controller=&filters_action=&search_fields[0]=e.customer&search_fields_prev[0]=e.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&logical_operator[0]=OR&search_fields[1]=ea2.participant_id&search_fields_prev[1]=ea2.participant_id&compare_options[1]=%3D+%27%25s%27&values[1]=" . $id . "&values_autocomplete[1]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=e.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'contracts':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=contracts&contracts=search&search_module=contracts&search_controller=&filters_action=&search_fields[0]=co.customer&search_fields_prev[0]=co.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=co.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'finance_expenses_reasons':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=finance&controller=expenses_reasons&expenses_reasons=search&search_module=finance&search_controller=expenses_reasons&filters_action=&search_fields[0]=fer.customer&search_fields_prev[0]=fer.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=fer.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'finance_incomes_reasons':
                        $types_search = '';
                        foreach ($inc_types[$id] as $key => $type) {
                            $types_search .= "&search_fields_prev[". ($key+1) ."]=fir.type&search_fields[". ($key+1) ."]=fir.type&compare_options[". ($key+1) ."]=%3D+'%25s'&values[". ($key+1) ."]=". $type ."&logical_operator[". ($key+1) ."]=OR";
                        }
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=finance&controller=incomes_reasons&incomes_reasons=search&search_module=finance&search_controller=incomes_reasons&filters_action=&search_fields[0]=fir.customer&search_fields_prev[0]=fir.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&logical_operator[0]=AND" . $types_search . "&sort[0]=fir.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'finance_payments':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=finance&controller=payments&payments=search&search_module=finance&search_controller=payments&filters_action=&search_fields[0]=fp.customer&search_fields_prev[0]=fp.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=fp.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'finance_warehouses_documents':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=finance&controller=warehouses_documents&warehouses_documents=search&search_module=finance&search_controller=warehouses_documents&filters_action=&search_fields[0]=fwd.customer&search_fields_prev[0]=fwd.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=fwd.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    case 'minitasks':
                        $links[] = '<a href="' . $_SERVER['PHP_SELF'] . "?launch=minitasks&minitasks=search&search_module=minitasks&search_controller=&filters_action=&search_fields[0]=m.customer&search_fields_prev[0]=m.customer&compare_options[0]=%3D+%27%25s%27&values[0]=" . $id . "&values_autocomplete[0]=[" .  $urlencoded_code . "]+" . $urlencoded_name ."&sort[0]=m.added&order[0]=DESC&display=5" . '" target="_blank">' . $registry['translater']->translate('menu_' . $module) . '</a>';
                        break;
                    }
                }
                $msg .= implode(', ', $links);
            }
            $registry['messages']->setError(sprintf($registry['translater']->translate('error_customers_delete_related_models'), $msg));

            return false;
        }

        return true;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        if (!self::allowDelete($registry, $ids)) {
            return false;
        }

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_CUSTOMERS);

        if (!$deleted) {
            $db->FailTrans();
        } else {
            //delete default values
            $query = 'UPDATE ' . DB_TABLE_DOCUMENTS_TYPES . ' SET default_customer=0 WHERE default_customer in (' . implode(',', $ids) . ')';
            $db->Execute($query);
            $query = 'UPDATE ' . DB_TABLE_USERS . ' SET default_customer=0 WHERE default_customer in (' . implode(',', $ids) . ')';
            $db->Execute($query);
            $query = 'DELETE FROM ' . DB_TABLE_EVENTS_DEFAULT_ASSIGNMENTS . ' WHERE assignee_type="customer" AND assignee in (' . implode(',', $ids) . ')';
            $db->Execute($query);
            //ToDo add additional queries to delete related records
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_CUSTOMERS);

        if (!$restored) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, DB_TABLE_CUSTOMERS);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * TEMPORARY METHOD
     * transfers the contacts from customers_hyperlinks to customers and
     * deletes customers_hyperlinks table
     */
    /*public static function updateContacts(&$registry) {
        $db = $registry['db'];
        // query to take the data from customers_hyperlinks
        $query = 'SELECT ch.parent_id, ch.link, c.email, c.web, c. phone1, c.phone2, c.phone3' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_HYPERLINKS . ' AS ch' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c ON (c.id=ch.parent_id)';
        $records = $db->GetAll($query);

        //construct the array for update
        $update_data = array();
        foreach ($records as $arr) {
            if (empty($update_data[$arr['parent_id']]['emails']) && !empty($arr['email'])) {
                $update_data[$arr['parent_id']]['emails'][] = $arr['email'];
            }
            if (empty($update_data[$arr['parent_id']]['web']) && !empty($arr['web'])) {
                $update_data[$arr['parent_id']]['web'][] = $arr['web'];
            }
            if ((strpos($arr['link'], 'http://') === 0) || (strpos($arr['link'], 'https://') === 0) || (strpos($arr['link'], 'www.') === 0)) {
                $update_data[$arr['parent_id']]['web'][] = $arr['link'];
            } elseif (strpos($arr['link'], '@') > 0) {
                $update_data[$arr['parent_id']]['emails'][] = $arr['link'];
            }
            if (empty($update_data[$arr['parent_id']]['phones']) && ((!empty($arr['phone1'])) || (!empty($arr['phone2'])) || (!empty($arr['phone3'])))) {
                if (!empty($arr['phone1'])) {
                    $update_data[$arr['parent_id']]['phones'][] = $arr['phone1'];
                }
                if (!empty($arr['phone2'])) {
                    $update_data[$arr['parent_id']]['phones'][] = $arr['phone2'];
                }
                if (!empty($arr['phone3'])) {
                    $update_data[$arr['parent_id']]['phones'][] = $arr['phone3'];
                }
            }
        }

        // start s transaction
        $db->StartTrans();
        // updating the customers table
        foreach ($update_data as $update_id => $data) {
            $update = array();
            if (!empty($data['web'])) {
                $update['links'] = implode(' | ', $data['web']);
                $update['links'] = sprintf("web='%s'", $update['links']);
            }
            if (!empty($data['emails'])) {
                $update['emails'] = implode(' | ', $data['emails']);
                $update['emails'] = sprintf("email='%s'", $update['emails']);
            }
            if (!empty($data['phones'])) {
                $update['phones'] = implode(' | ', $data['phones']);
                $update['phones'] = sprintf("phone1='%s'", $update['phones']);
            }

            $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                      'SET ' . implode(', ', $update) . "\n" .
                      'WHERE id=' . $update_id;

            $db->Execute($query1);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();
    }*/

    /**
     * function to fill the users for the department assigning
     */
    public static function fillUsersDropdown(&$registry) {
        $request = $registry['request']->getAll();

        $lang = $registry['lang'];
        $db = $registry['db'];
        // select clause
        $sql['select'] = 'SELECT u.id AS option_value, ' . "\n" .
                         '  IF(ui18n.firstname IS NULL OR ui18n.lastname IS NULL, "", CONCAT(ui18n.firstname, " ", ui18n.lastname)) AS label, ' . "\n" .
                         '  "' . $lang . '" as model_lang' . "\n";

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                       '  ON (u.id=ui18n.parent_id AND ui18n.lang="' . $lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_DEPARTMENTS . ' AS ud' . "\n" .
                       '  ON (u.id=ud.parent_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_DEPARTMENTS . ' AS dep' . "\n" .
                       '  ON (ud.department_id=dep.id)';
        $sql['where'] = 'WHERE dep.id="' . $request['department'] . '"' . "\n" .
                        '  AND u.hidden=0 AND u.active=1 AND u.is_portal!=1 AND (u.deleted="0000-00-00 00:00:00" OR u.deleted IS NULL)';
        $sql['order'] = 'ORDER BY ui18n.firstname, ui18n.lastname';

        $query = implode("\n", $sql);

        $records = $registry['db']->GetAll($query);

        return json_encode($records);
    }

    /**
     * TEMPORARY METHOD
     * sets default relative types between relative customers
     */
    /*public static function updateCustomersRelations() {
        $db = $registry['db'];
        // query to take the data from customers_hyperlinks
        $query = 'SELECT cr.*, c1.is_company AS parent_is_company, c2.is_company AS child_is_company ' . "\n" .
                 'FROM ' . DB_TABLE_CUSTOMERS_RELATIVES . ' AS cr' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c1' . "\n" .
                 '  ON (c1.id=cr.parent_id)' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c2' . "\n" .
                 '  ON (c2.id=cr.child_id)'. "\n";
        $records = $db->GetAll($query);

        $db->StartTrans();
        foreach ($records as $recs) {
            $update = array();
            $where = array();
            $query1 = '';
            $where['parent_id'] = sprintf("parent_id='%s'", $recs['parent_id']);
            $where['child_id']  = sprintf("child_id='%s'", $recs['child_id']);
            if ($recs['parent_is_company'] != $recs['child_is_company']) {
                $update['relative_type'] = sprintf("relative_type='employee'");
            } else {
                if ($recs['parent_is_company'] == 0) {
                    $update['relative_type'] = sprintf("relative_type='colleague'");
                } else {
                    $update['relative_type'] = sprintf("relative_type='filial_company'");
                }
            }

            $query1 = 'UPDATE ' . DB_TABLE_CUSTOMERS_RELATIVES . "\n" .
                      'SET ' . implode(', ', $update) . "\n" .
                      'WHERE ' . implode(' AND ', $where);

            $db->Execute($query1);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();
    }*/

    /**
     * Gets similar names
     *
     * @param Registry $registry - the main registry
     * @param string $check_name - value to check against
     * @param number $is_company - true if searching for companies, false when searching for people
     * @return array - found matching records
     */
    public static function checkName(&$registry, $check_name, $is_company = 1) {

        $records = array();
        if (!$is_company) {
            $is_company = 0;
        }
        if ($check_name) {
            //get all customer names
            $db = $registry['db'];
            $query = "SELECT id, " . ($is_company ? "name as `check`" : "CONCAT(name, ' ', lastname) as `check`") . " , lang
                        FROM " . DB_TABLE_CUSTOMERS_I18N . ", " . DB_TABLE_CUSTOMERS .
                        " WHERE id=parent_id
                        AND subtype='normal' AND deleted_by=0 AND is_company=" . $is_company;
            $results = $db->GetAll($query);

            $records = General::levenshtein($registry, $check_name, $results);
        }

        return $records;
    }

    /**
     * Custom function to call when searching for customers trademarks from nomenclatures autocompleter.
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array
     */
    public static function getTrademarks(&$registry, $filters = array()) {
        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $sql = array();
        $sql['select'] = 'SELECT ct.id AS id, ct.is_default, n.type, ' . "\n" .
                         'c.id AS customer, c.code AS customer_code, c.is_company AS customer_is_company, ' . "\n" .
                         'c.type AS customer_type, c.admit_VAT_credit,' . "\n" .
                         'TRIM(CONCAT(ci18n.name, \' \', ci18n.lastname)) AS customer_name, ' . "\n" .
                         'n.id AS trademark, n.code, ni18n.name ';
        $sql['from']  =  'FROM ' . DB_TABLE_CUSTOMERS_TRADEMARKS . ' AS ct' . "\n" .
                         'JOIN ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                         '  ON c.id = ct.parent_id' . "\n" .
                         'JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                         '  ON ct.trademark_id = n.id' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                         '  ON (ct.parent_id = ci18n.parent_id AND ci18n.lang = "' . $model_lang . '")' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                         '  ON (n.id = ni18n.parent_id AND ni18n.lang = "' . $model_lang . '")';
        $sql['where'] = self::constructWhere($registry, $filters);
        $sql['order'] = 'ORDER BY ' . implode(', ', $filters['sort']);
        $sql['limit'] = 'LIMIT 0, ' . $filters['display'];

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAll($query);

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        require_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php');
        $models = Nomenclatures::createModels($registry, $records, Nomenclatures::$modelName, $sanitize);

        $sql['select'] = 'SELECT COUNT(ct.id) AS total';
        $sql['order'] = '';
        $sql['group'] = '';
        $sql['limit'] = '';
        $query = implode("\n", $sql);
        $total = $registry['db']->GetOne($query);

        $results = array($models, $total);

        return $results;
    }

    /**
     * Gets ids and names of models to be used in distribution by elements in main centers
     * and in analysis distribution itself
     *
     * @param object $registry - the main registry
     * @param array $ids - array of model ids
     * @param int $item_id - optional parameter, used to get non-zero elements first (as displayed in distribution)
     * @return array - result of operation
     */
    public static function getElementsForDistribution(&$registry, $ids = array(), $item_id = 0) {
        $elements = array();

        if ($ids) {
            $db = $registry['db'];
            $model_lang = $registry['lang'];

            $query = 'SELECT c.id AS element, ' . "\n" .
                     '  IF(c.is_company = 0 AND ci18n.name IS NOT NULL AND ci18n.lastname IS NOT NULL, CONCAT(ci18n.name, " ", ci18n.lastname), ci18n.name) AS name' .
                     ($item_id ? ', IF(faid.percentage = 0 OR faid.percentage IS NULL, 0, 1) AS nonzero' : '') . "\n" .
                     '  FROM ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                     '  LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                     '  ON (c.id = ci18n.parent_id AND ci18n.lang = "' . $model_lang . '")' . "\n" .
                     ($item_id ? '  LEFT JOIN ' . DB_TABLE_FINANCE_ANALYSIS_ITEMS_DISTRIBUTION . ' AS faid' . "\n" .
                     '  ON faid.element=c.id AND faid.parent_id=' . $item_id . "\n" : '') .
                     '  WHERE c.id IN (' . implode(', ', $ids) . ')' . "\n" .
                     '  ORDER BY ' . ($item_id ? 'nonzero DESC, ' : '') . 'name ASC';
            $elements = $db->GetAll($query);
        }

        return $elements;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param object $registry - the main registry
     * @param object $controller - controller object
     * @return bool - result of operation
     */
    public static function multiPrint(&$registry, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];

        $model_lang = $request->isRequested('model_lang') ? $request->get('model_lang') : $registry['lang'];

        //IDs of models to print
        $ids = $request->get('items');

        $db->StartTrans();

        //set time limit and memory limit
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //get models
        $filters = array('where' => array('c.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $customers = Customers::search($registry, $filters);

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $pattern = Patterns::searchOne($registry, $filters);

        $query_type_name_plural = 'SELECT name_plural FROM ' . DB_TABLE_CUSTOMERS_TYPES_I18N . "\n" .
                                  'WHERE parent_id=' . $pattern->get('model_type') . ' AND lang="' . $model_lang . '"';
        $type_name_plural = $db->getOne($query_type_name_plural);
        if (!$type_name_plural) {
            $type_name_plural = $registry['translater']->translate(strtolower(General::singular2plural(self::$modelName)));
        }

        //check if already having generated files for models with selected pattern (get latest version)
        $query_files = 'SELECT model_id AS idx, f.*' . "\n" .
                       'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON f.id=fi18n.parent_id AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       'WHERE model="' . self::$modelName . '" AND model_id IN (' . implode(', ', $ids) . ') AND pattern_id=' . $pattern_id . ' AND origin="generated" AND deleted_by=0 AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       '  AND revision=(SELECT MAX(revision) FROM ' . DB_TABLE_FILES . ', ' . DB_TABLE_FILES_I18N . "\n" .
                       '  WHERE id=parent_id AND model="' . self::$modelName . '" AND model_id=f.model_id AND origin="generated" AND deleted_by=0 AND lang="' . $model_lang . '")' . "\n" .
                       'GROUP BY model_id' . "\n" .
                       'ORDER BY model_id ASC';
        $generated_files = $db->getAssoc($query_files);

        $file_ids = array();
        foreach ($customers as $customer) {
            if ($db->HasFailedTrans()) {
                //something is wrong, do not continue at all
                break;
            }
            //$old_model[] = $customer;
            if (!array_key_exists($customer->get('id'), $generated_files) ||
               (array_key_exists($customer->get('id'), $generated_files) && !file_exists($generated_files[$customer->get('id')]['path']))) {
                //registry needed for operations with model
                $customer->unsanitize();

                $patterns_vars = $customer->getPatternsVars();
                $customer->extender = new Extender();
                $customer->extender->model_lang = $model_lang;
                $customer->extender->module = 'Customers';
                foreach ($patterns_vars as $key => $value) {
                    $customer->extender->add($key, $value);
                }

                //generate file with selected pattern
                if ($file_id = $customer->generatePDF()) {
                    //save history
                    require_once(PH_MODULES_DIR . 'customers/models/customers.history.php');
                    Customers_History::saveData($registry,
                                                array('action_type' => 'print',
                                                      'model' => $customer,
                                                      'pattern' => $pattern->get('id'),
                                                      'generated_file' => $file_id
                                                ));
                    $file_ids[$customer->get('id')] = $file_id;
                }
                $customer->sanitize();

            } else {
                $file_ids[$customer->get('id')] = $generated_files[$customer->get('id')]['id'];
            }

            //$temp_model = clone $customer;
            //$new_models[] = $temp_model->sanitize();
            //unset($temp_model);
        }

        //set models for automations
        //$controller->old_model = $old_model;
        //$controller->new_models = $new_models;

        if ($file_ids) {
            $query_files = 'SELECT path' . "\n" .
                           '  FROM ' . DB_TABLE_FILES . "\n" .
                           '  WHERE id IN (' . implode(', ', $file_ids) . ')';
            $files = $db->getCol($query_files);

            //prepare the page properties
            if ($pattern->get('landscape')) {
                $page_orientation = 'L';
                $page_format_width = 841.82;
                $page_format_height = 598.63;
            } else {
                $page_orientation = 'P';
                $page_format_width = 598.63;
                $page_format_height = 841.82;
            }
            $page_format = array($page_format_width, $page_format_height);

            //merge files and send result file to browser
            $pdf = new Pdf_Merge($page_orientation, 'pt', $page_format);
            $pdf->setFiles($files);
            $pdf->concat();
            //remove any notices or warnings
            ob_clean();
            $pdf->Output_fdpf(sprintf('%s_%s_multiprint.pdf',
                                      General::strftime($registry['translater']->translate('date_short')),
                                      preg_replace('#\s#', '_', Transliterate::convert($type_name_plural))), 'D');

            //write lightweight history
            $current_user_id = $registry['currentUser']->get('id');
            $query_history = 'INSERT INTO ' . DB_TABLE_CUSTOMERS_HISTORY . ' (model, model_id, h_date, action_type, user_id, data, lang) VALUES' . "\n";
            $history_records = array();
            foreach ($file_ids as $model_id => $file_id) {
                $data = serialize(array('pattern' => $pattern_id, 'generated_file' => $file_id));
                $history_records[] = sprintf('(\'%s\', %d, NOW(), \'multiprint\', %d, \'%s\', \'%s\')',
                                             self::$modelName, $model_id, $current_user_id, $data, $model_lang);
            }
            $query_history .= implode(",\n", $history_records);
            $db->Execute($query_history);

        } else {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes all emails from all targetlists for deactivated or deleted
     * customers, branches or contact persons.
     *
     * @param object $registry - the main registry
     * @param array $models - array of objects of class Customer, Customer_Branch or Customer_Contactperson
     * @param string $model_name - model name, optional, used only for Customers
     * @return bool - result of the operation
     */
    public static function deleteEmailsFromTargetlists(&$registry, &$models, $model_name = '') {

        $db = $registry['db'];

        if ($model_name == 'Customer' && !empty($models)) {
            // $models are ids only, get necessary properties from database and create models
            $query = 'SELECT c.id, c.is_company, cbr.id AS main_branch_id' . "\n" .
                     'FROM ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS cbr' . "\n" .
                     '  ON (cbr.parent_customer=c.id AND cbr.is_main=1 AND cbr.subtype="branch")' . "\n" .
                     'WHERE c.id IN (' . implode(', ', $models) . ')';
            $records = $db->GetAll($query);

            $sanitize = true;

            $models = self::createModels($registry, $records, self::$modelName, $sanitize);
        }

        $where_clauses = array();
        foreach ($models as $model) {
            $where_clauses[] = self::prepareEmailsTargetlistsWhereClause($model);
        }

        if ($where_clauses) {
            $query = 'DELETE etd.*, etdi18n.*' . "\n" .
                     'FROM ' . DB_TABLE_EMAILS_TARGETLISTS_DATA . ' AS etd' . "\n" .
                     'JOIN ' . DB_TABLE_EMAILS_TARGETLISTS_DATA_I18N . ' AS etdi18n' . "\n" .
                     '  ON etd.id=etdi18n.parent_id' . "\n" .
                     'WHERE (' . implode(' OR ' . "\n", $where_clauses) . ')';
            $db->Execute($query);
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Updates (deletes and adds) emails for customer, branch or contact person
     * in all targetlists.
     *
     * @param object $registry - the main registry
     * @param object $model - model after update; object of class Customer, Customer_Branch or Customer_Contactperson
     * @param object $old_model - model before update; object of class Customer, Customer_Branch or Customer_Contactperson
     * @return bool - result of the operation
     */
    public static function updateEmailsTargetlists(&$registry, $model, $old_model) {

        if (empty($model) || empty($old_model)) {
            return true;
        }

        $db = $registry['db'];

        $old_emails = $old_model->get('email') ? $old_model->get('email') : array();
        $new_emails = $model->get('email') ? $model->get('email') : array();

        if ($model->get('active') === '0' && $old_model->get('active') === '1') {
            // if deactivating model from edit/translate
            $deleted_emails = $old_emails;
            $added_emails = array();
        } else {
            $deleted_emails = array_diff($old_emails, $new_emails);
            $added_emails = array_diff($new_emails, $old_emails);
        }

        // there are deleted emails
        if (!empty($deleted_emails)) {
            $do_delete = false;
            $add_targetlist_ids = array();

            // there are new emails
            if (!empty($added_emails)) {
                $query_etd = 'SELECT etd.* ' . "\n" .
                             'FROM ' . DB_TABLE_EMAILS_TARGETLISTS_DATA . ' AS etd' . "\n" .
                             'WHERE ' . self::prepareEmailsTargetlistsWhereClause($model);
                $etd_emails = $db->GetAll($query_etd);

                if ($etd_emails) {
                    $etd_data = array();
                    foreach ($etd_emails as $etd) {
                        $etd_data[$etd['targetlist_id']]['data'][] = $etd;
                        $etd_data[$etd['targetlist_id']]['emails'][] = $etd['email'];
                    }

                    foreach ($etd_data as $targetlist_id => $etd) {
                        $matching_emails = array_intersect($deleted_emails, $etd['emails']);

                        // some of deleted mails are found in targetlist data
                        if (!empty($matching_emails)) {
                            $do_delete = true;

                            $remaining_emails = array_diff($etd['emails'], $deleted_emails);

                            if (!empty($remaining_emails)) {
                                // do not insert new record
                            } elseif (!empty($added_emails)) {
                                // add new record for this targetlist
                                $add_targetlist_ids[] = $targetlist_id;
                            }
                        } else {
                            // do nothing
                        }
                    }
                } else {
                    // do nothing
                }
            } else {
                // no new emails
                $do_delete = true;
            }

            // delete old records
            if ($do_delete) {
                $query_delete = 'DELETE etd.*, etdi18n.*' . "\n" .
                                'FROM ' . DB_TABLE_EMAILS_TARGETLISTS_DATA . ' AS etd' . "\n" .
                                'JOIN ' . DB_TABLE_EMAILS_TARGETLISTS_DATA_I18N . ' AS etdi18n' . "\n" .
                                '  ON etd.id=etdi18n.parent_id' . "\n" .
                                'WHERE ' . self::prepareEmailsTargetlistsWhereClause($model) . "\n" .
                                '  AND etd.email IN ("' . implode('", "', $deleted_emails) . '")';
                $result = $db->Execute($query_delete);
            }

            // insert new records
            if (!empty($add_targetlist_ids)) {
                // prepare common values for all records to insert
                $contact_type = '';
                $contact_id = $model->get('id');
                $contact_name = $model->get('name');
                $email = reset($added_emails);

                switch ($model->modelName) {
                case 'Customer':
                    if ($model->get('is_company')) {
                        $contact_type = 'branch';
                        $contact_id = $model->get('main_branch_id');
                        $contact_name = $model->get('main_branch_name') ?
                                        $model->get('main_branch_name') :
                                        $model->getBranchLabels('customers_central_office');
                    } else {
                        $contact_type = 'customer';
                        $contact_name .= ' ' . $model->get('lastname');
                    }
                    break;
                case 'Customers_Branch':
                    $contact_type = 'branch';
                    break;
                case 'Customers_Contactperson':
                    $contact_type = 'contact';
                    $contact_name .= ' ' . $model->get('lastname');
                    break;
                }

                $add_records = array();
                foreach ($add_targetlist_ids as $targetlist_id) {
                    $add_records[] = sprintf('(NULL, %d, "%s", %d, "%s")',
                                     $targetlist_id, $contact_type, $contact_id, $email);
                }

                $query_insert = 'INSERT IGNORE INTO ' . DB_TABLE_EMAILS_TARGETLISTS_DATA . "\n" .
                                '  (id, targetlist_id, contact_type, contact_id, email) VALUES ' . "\n" .
                                implode(',' . "\n", $add_records);
                $result = $db->Execute($query_insert);

                $query_insert_i18n = 'INSERT IGNORE INTO ' . DB_TABLE_EMAILS_TARGETLISTS_DATA_I18N . "\n" .
                                     '  (parent_id, name, lang)' . "\n" .
                                     'SELECT etd.id, "' . trim($contact_name) . '", IF(eti18n.lang IS NOT NULL, eti18n.lang, "' . $model->get('model_lang') . '")' . "\n" .
                                     'FROM ' . DB_TABLE_EMAILS_TARGETLISTS_DATA . ' AS etd' . "\n" .
                                     'LEFT JOIN ' . DB_TABLE_EMAILS_TARGETLISTS_I18N . ' AS eti18n' . "\n" .
                                     '  ON etd.targetlist_id=eti18n.parent_id' . "\n" .
                                     'WHERE etd.targetlist_id IN (' . implode(', ', $add_targetlist_ids) . ') ' . "\n" .
                                     '  AND etd.contact_type="' . $contact_type . '" AND etd.contact_id="' . $contact_id . '"' . "\n" .
                                     '  AND etd.email="' . $email . '"' . "\n" .
                                     'GROUP BY etd.id';
                $result = $db->Execute($query_insert_i18n);
            }
        } else {
            // do nothing
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Build conditions for "where" clause for emails targetlist data
     * based on model name and other properties of model.
     *
     * @param object $model - object of class Customer, Customer_Branch or Customer_Contactperson
     * @return string - "where" conditions
     */
    private static function prepareEmailsTargetlistsWhereClause($model) {

        // use either clause for building the "where" conditions
        $where_clause_1 = 'etd.contact_type IN (%s) AND etd.contact_id=%d';
        $where_clause_2 = '(etd.contact_type="branch" AND etd.contact_id=%d OR etd.contact_type="custom" AND etd.contact_id=%d)';

        $where_clause = '';

        switch ($model->modelName) {
        case 'Customer':
            if ($model->get('is_company')) {
                $where_clause = sprintf($where_clause_2, $model->get('main_branch_id'), $model->get('id'));
            } else {
                $where_clause = sprintf($where_clause_1, '"customer", "custom"', $model->get('id'));
            }
            break;
        case 'Customers_Branch':
            if ($model->get('is_main')) {
                $where_clause = sprintf($where_clause_2, $model->get('id'), $model->get('parent_customer'));
            } else {
                $where_clause = sprintf($where_clause_1, '"branch"', $model->get('id'));
            }
            break;
        case 'Customers_Contactperson':
            $where_clause = sprintf($where_clause_1, '"contact"', $model->get('id'));
            break;
        default:
            $where_clause = '0';
            break;
        }

        return $where_clause;
    }
}

?>
