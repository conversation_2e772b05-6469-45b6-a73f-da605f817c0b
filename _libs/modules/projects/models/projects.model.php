<?php

require_once 'projects.dropdown.php';
require_once 'projects.validator.php';
require_once PH_MODULES_DIR . 'stages/models/stages.phases.factory.php';

/**
 * Projects model class
 */
Class Project extends Model {
    public $modelName = 'Project';

    public $counter;

    // available stages statuses
    public $stagesStatuses = array('planning', 'progress', 'control', 'finished');

    //flag defining whether to use status to define permissions
    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'code', 'customer_name', 'name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->get('id')) {
            $this->getAssignments();
        }
        $finalized_project = false;
        if ($this->get('finished') === '1' || $this->get('finished') === '0') {
            $finalized_project = true;
        }
        $this->set('finalized_project', $finalized_project, true);

        // get stages as array
        $stages_array = array();
        if ($this->get('stages')) {
            if (is_array($this->get('stages'))) {
                $stages_array = $this->get('stages');
            } else {
                $stages_array = preg_split('/\s*,\s*/', $this->get('stages'));
                $stages_array = array_filter($stages_array);
            }
        }

        $this->set('stages', $stages_array, true);
    }

    /**
     * Checks permissions for certain action
     *
     * @param array $action - action name
     * @param array $module_check - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkPermissions($action, $module_check = 'projects', $force = false) {

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        $rights = $this->setPermissions(array(), $force, $module_check);
        if ($this->get('type_rights')) {
            $type_rights = $this->get('type_rights');
        } elseif ($this->get('type')) {
            $sanitize_after = false;
            if (empty($this->registry)) {
                $this->unsanitize();
                $sanitize_after = true;
            }
            $user_permissions = $this->registry['currentUser']->getRights();
            $type_rights = $this->setPermissions(@array_keys($user_permissions[$module_check.$this->get('type')]), true, $module_check.$this->get('type'));
            if ($sanitize_after) {
                $this->sanitize();
            }
            $this->set('type_rights', $type_rights, true);
        }

        if (!isset($rights[$action]) && is_array($rights)) {
            //the action is not defined within the rights array
            $action_defs = array_keys($rights);
            $action_defs[] = $action;

            //try to get permission definition for this action
            $rights = $this->setPermissions($action_defs, true, $module_check);
        }

        // rights for REVISION action which does not depend on the status
        if ($action == 'revision') {
            if (!isset($this->registry)) {
                $registry = &$GLOBALS['registry'];
            } else {
                $registry = &$this->registry;
            }
            $current_user_id = $registry['currentUser']->get('id');

            // if the project is finished the users can't revision the project
            if ($this->get('finalized_project')) {
                return false;
            } else {
                if (array_key_exists($current_user_id, $this->get('users_assignments')) || $current_user_id == $this->get('manager')) {
                    if (isset($rights[$action])) {
                        return $rights[$action];
                    } else {
                        return false;
                    }
                } else {
                    //in any other case permission is allowed
                    return false;
                }
            }
        } elseif ($action == 'phases') {
            // if model has no stages, return false, otherwise continue with regular right checking
            if (!$this->checkStages()) {
                return false;
            }
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'finished') {
                //finished status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    //adding timesheets is forbidden for finished projects
                    return false;
                    break;
                case 'viewtimesheets':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }

                    //check if the project type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    } else {
                        $system_task_id = Projects::getSystemTask($registry, $this->get('id'));
                        if (!$system_task_id) {
                            return false;
                        }
                    }

                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the project or is manager
                    if ($this->get('users_assignments') && array_key_exists($current_user_id, $this->get('users_assignments')) ||
                        $current_user_id == $this->get('manager')) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                // if the project is finished (successfully or failed) the Status and Edit actions are disabled
                case 'setstatus':
                case 'edit':
                    if ($this->get('finalized_project')) {
                        return false;
                    }
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            return $type_rights[$action];
                        } else {
                            return $rights[$action];
                        }
                    } else {
                        return true;
                    }
                }
            } else {
                //any other status
                switch ($action) {
                //specific actions
                case 'addtimesheet':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }

                    //check if the project type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    } else {
                        $system_task_id = Projects::getSystemTask($registry, $this->get('id'));
                        $task = '';
                        if ($system_task_id) {
                            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
                            $task = Tasks::searchOne($registry,
                                                     array('where' => array('t.id = ' . $system_task_id,
                                                                            't.type = ' . PH_TASK_SYSTEM_TYPE,
                                                                            't.status = \'progress\''),
                                                           'sanitize' => true));
                        }
                        if (empty($task)) {
                            return false;
                        }
                        unset($task);
                    }

                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets']) &&
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE]['viewtimesheets'] == 'none') {
                        return false;
                    }

                    //check if the permission for adding timesheets for TASK type TIMESHEET is forbidden
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the project or is manager
                    if ($this->get('users_assignments') && array_key_exists($current_user_id, $this->get('users_assignments')) ||
                        $current_user_id == $this->get('manager')) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                case 'viewtimesheets':
                    if (!isset($this->registry)) {
                        $registry = $GLOBALS['registry'];
                    } else {
                        $registry = $this->registry;
                    }

                    //check if the project type requires timesheets
                    if (!$this->get('generate_system_task')) {
                        return false;
                    } else {
                        $system_task_id = Projects::getSystemTask($registry, $this->get('id'));
                        if (!$system_task_id) {
                            return false;
                        }
                    }

                    $current_user_id = $registry['currentUser']->get('id');
                    if (!isset($user_permissions)) {
                        $user_permissions = $registry['currentUser']->getRights();
                    }

                    //check if the permission for viewing timesheets for TASK type TIMESHEET is allowed
                    if (!isset($user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action]) ||
                        $user_permissions['tasks' . PH_TASK_SYSTEM_TYPE][$action] == 'none') {
                        return false;
                    }

                    //check if the user is assigned to the project or is manager
                    if ($this->get('users_assignments') && array_key_exists($current_user_id, $this->get('users_assignments')) ||
                        $current_user_id == $this->get('manager')) {
                        return true;
                    }
                    //in any other case permission is forbidden
                    return false;
                    break;
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            return $type_rights[$action];
                        } else {
                            return $rights[$action];
                        }
                    } else {
                        return true;
                    }
                }
            }
        } else {
            if (isset($rights[$action])) {
                if (isset($type_rights[$action])) {
                    return $type_rights[$action];
                } else {
                    return $rights[$action];
                }
            } else {
                return true;
            }
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Checks the validity of the model
     *
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        parent::validate();

        // Get some commonly used vars
        $id   = $this->get('id');
        $code = $this->get('code');

        if (!$this->get('name')) {
            $this->raiseError('error_no_name_specified', 'name', null, array($this->getLayoutName('name')));
        }

        //set counter
        $this->getCounter();

        if ((($action == 'add' || $action == 'edit') && $this->isActivated()) && ($this->counter && $this->counter->get('parent_num')) && !$this->get('parent_project')) {
            $this->raiseError('error_no_parent_project', 'parent_project', null, array($this->getLayoutName('parent_project')));
        }

        if ($this->isDefined('customer') && !$this->get('customer')) {
            $this->raiseError('error_no_customer', 'customer', null, array($this->getLayoutName('customer')));
        }

        if (!$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }

        if ($this->isDefined('manager') && !$this->get('manager')) {
            $this->raiseError('error_no_manager', 'manager', null, array($this->getLayoutName('manager')));
        }

        if ($this->get('date_start')) {
            if (!Validator::validDate($this->get('date_start'))) {
                $this->raiseError('error_date_start', 'date_start', null, array($this->getLayoutName('date_start', false)));
            }
            if ($this->get('date_end') && !Validator::validDate($this->get('date_end'))) {
                $this->raiseError('error_date_end', 'date_end', null, array($this->getLayoutName('date_end', false)));
            } elseif ($this->get('date_end') && $this->get('date_end') < $this->get('date_start')) {
                $this->raiseError('error_date_start_end', 'date_start, date_end', null,
                    array($this->getLayoutName('date_start', false), $this->getLayoutName('date_end', false)));
            }
        } elseif ($this->get('date_end')) {
            $this->raiseError('error_date_start', 'date_start', null, array($this->getLayoutName('date_start', false)));
            if (!Validator::validDate($this->get('date_end'))) {
                $this->raiseError('error_date_end', 'date_end', null, array($this->getLayoutName('date_end', false)));
            }
        }

        if (!$code) {
            if (!$id) {
                $code = $this->setCode();
            } elseif ($this->isDefined('code')) {
                $this->raiseError('error_no_code', 'code', null, array($this->getLayoutName('code')));
            }
        }
        if ($code) {
            // If there are any other projects with the same code
            $query = 'SELECT `id` FROM `' . DB_TABLE_PROJECTS .'` WHERE `code`= \'' . General::slashesEscape($code) . '\'';
            if ($id) {
                $query .= " AND `id` != {$id}";
            }
            $query .= ' LIMIT 1';
            $code_exists = $this->registry['db']->GetOne($query);
            if ($code_exists) {
                // Set an error
                $code_layout_name = $this->getLayoutName('code');
                $this->raiseError('error_code_not_unique', 'code', null, array($code_layout_name, $code_layout_name));
            }
        }

        // validate additional vars
        if ($action != 'translate') {
            $this->validateVars();
        }

        return $this->valid;
    }

    /**
     * Get project attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Project\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted = 0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }
        $this->set('attachments', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Project\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted = 0'),
                                          'archive' => $this->get('archived_by'),
                                          'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }


    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($action == 'translate') {
                $action = 'edit';
            }
            if ($this->$action()) {
                //generate system task if necessary
                if ($action == 'add' && $this->get('generate_system_task')) {
                    $this->slashesStrip();
                    $this->createSystemTask();
                }
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']         = sprintf("added=now()");
        $set['added_by']      = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified']     = sprintf("`status_modified`=now()");
        $set['status_modified_by']  = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['type']          = sprintf("type=%d", $this->get('type'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_PROJECTS . "\n" .
                  'SET ' . implode(",\n", $set) . "\n";

        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new project base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N(false, 'add');

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        if ($this->get('update_relatives')) {
            $this->updateRelatives(false);
        }

        //if create from event update event relatives
        if ($this->get('event_id')) {
            $origin = 'project';
            $link_type = 'child';
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                      'SET parent_id='. $this->get('event_id') . "\n" .
                      ', link_to=' . $this->get('id') . "\n" .
                      ', origin="' . $origin . '"' . "\n" .
                      ', link_type="' . $link_type . '"' . "\n" .
                      ', added=now()' . "\n";
            $db->Execute($query);
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if ($this->replaceVars()) {
                // replace temporary model_id with real id of model
                //$this->updateModelIdAfterAdd();
            } else {
                $db->FailTrans();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_PROJECTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');

        $db->Execute($query1);

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //update relatives
        if ($this->get('update_relatives')) {
            $this->updateRelatives();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if (!$this->replaceVars()) {
                $db->FailTrans();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Clone model
     *
     * @return bool
     */
    public function cloneModel() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //prepare the new code of the cloned project
        if (!$this->get('code_is_set')) {
            $this->set('code', '', true);
            $this->setCode();
        }

        $this->slashesEscape();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['type']     = sprintf("type=%d", $this->get('type'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_PROJECTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE RELATIVES TABLE
        $this->insertRelatives();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        $this->slashesStrip();

        // create the system task if necessary
        if ($this->get('id') && $this->get('generate_system_task')) {
            $this->createSystemTask();
        }

        //copy variables
        $this->copyVars();

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Copy variables when cloning a model
     * Copies vars from orig_vars to vars
     *
     * @param string $lang - when set, only multilang vars should be copied in current model lang
     * @return bool - result of the operation
     */
    public function copyVars($lang = '') {
        //gets vars assigned from the source (original) project
        $orig_vars = $this->get('orig_vars');
        // no additional variables
        if (!$orig_vars) {
            return true;
        }
        $assoc_vars = array();
        //convert variables array to associative
        foreach ($orig_vars as $var) {
            if (empty($lang) || $var['multilang']) {
                $assoc_vars[$var['name']] = $var;
            }
        }

        //gets vars of the destination project
        $this->unsetVars();
        $this->getVars();

        //compares variable names and removes the unnecessary variables
        $new_vars = $this->get('vars');

        foreach ($new_vars as $k => $var) {
            if (isset($assoc_vars[$var['name']]) && !empty($assoc_vars[$var['name']]['model_id'])) {
                $new_vars[$k]['value'] = $assoc_vars[$var['name']]['value'];
            } else {
                unset($new_vars[$k]);
            }
        }

        //store new vars in the DB
        if (!empty($new_vars)) {
            //assign newly defined vars
            $this->set('vars', $new_vars, true);
            $this->registry->set('edit_all', true, true);
            if ($lang) {
                // save vars as if action is translate (only multilang ones and only in current model lang)
                $this->set('translate_multilang', true, true);
            }
            return $this->replaceVars();
        } else {
            return true;
        }
    }

    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->get('parent_project')) {
            $set['parent_project']       = sprintf("parent_project=%d", $this->get('parent_project'));
        }
        if ($this->checkGetNum()) {
            $set['num']            = sprintf("num='%s'", $this->getNum());
        }
        if ($this->isDefined('customer')) {
            $set['customer']       = sprintf("customer=%d", $this->get('customer'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark']      = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('code')) {
            $set['code']           = sprintf("code='%s'", $this->get('code'));
        }
        if ($this->isDefined('date_start')) {
            $set['date_start']     = sprintf("date_start=%s", ($this->get('date_start')?"'".$this->get('date_start')."'":'null'));
        }
        if ($this->isDefined('date_end')) {
            $set['date_end']       = sprintf("date_end=%s", ($this->get('date_end')?"'".$this->get('date_end')."'":'null'));
        }
        if ($this->isDefined('priority')) {
            $set['priority']       = sprintf("priority='%s'", $this->get('priority'));
        }
        if ($this->isDefined('parent_project')) {
            $set['parent_project'] = sprintf("parent_project=%d", $this->get('parent_project'));
        }
        if ($this->isDefined('manager')) {
            $set['manager']        = sprintf("manager=%d", $this->get('manager'));
        }
        if ($this->isDefined('finished_part')) {
            $set['finished_part']  = sprintf("finished_part=%d", $this->get('finished_part'));
        }
        if ($this->isDefined('budget')) {
            $set['budget']         = sprintf("budget=%d", $this->get('budget'));
        }
        if ($this->isDefined('work_period')) {
            $set['work_period']    = sprintf("work_period=%d", $this->get('work_period'));
        }
        $set['modified']       = sprintf("modified=now()");
        $set['modified_by']    = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));

        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N($start_trans = false, $action = '') {
        $db = $this->registry['db'];

        //get the project`s name, description and notes
        $name        = $this->get('name');
        $description = $this->get('description');
        $notes       = $this->get('notes');

        if ($action == 'add') {
            //compose extender
            $extender = new Extender;

            //build a string from the project`s name, description and notes
            $name_description_notes = $name . $description . $notes;

            //get project details
            if (preg_match('#\[project_code\]#', $name_description_notes)) {
                if ($this->isActivated()) {
                    $extender->add('project_code', $this->get('code'));
                } else {
                    $extender->add('project_code', '[project_code]');
                }
            }

            //get customer details
            if ($this->get('customer') && preg_match('#\[customer_name\]#', $name_description_notes)) {
                if ($this->isActivated()) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                    $filters = array('where'      => array ('c.id = ' . $this->get('customer')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitaze'   => true);
                    $customer = Customers::searchOne($this->registry, $filters);
                    $extender->add('customer_name', General::slashesEscape(trim($customer->get('name') . ' ' . $customer->get('lastname'))));
                } else {
                    $extender->add('customer_name', '[customer_name]');
                }
            }
        }

        if ($start_trans) {
            $db->StartTrans();
        }
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            if ($action == 'add') {
                $name = $extender->expand($name, false);
            }
            $update['name'] = sprintf("name='%s'", $name);
        }
        if ($this->isDefined('description')) {
            if ($action == 'add' || $action == 'transform') {
                $description = $extender->expand($description, false);
            }
            $update['description'] = sprintf("description='%s'", $description);
        }
        if ($this->isDefined('notes')) {
            if ($action == 'add' || $action == 'transform') {
                $notes = $extender->expand($notes, false);
            }
            $update['notes']  = sprintf("notes='%s'", $notes);
        }

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        $query2 = 'INSERT INTO ' . DB_TABLE_PROJECTS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        if ($start_trans) {
            $db->CompleteTrans();
        }

        return !$db->HasFailedTrans();
    }

    /**
     * Update assignments table of the model
     *
     * @return bool - result of the operation
     */
    public function updateAssignments() {
        $db = $this->registry['db'];

        //take old assignments
        $query_old_assignments = 'SELECT assigned_to, assignments_type FROM ' . DB_TABLE_PROJECTS_ASSIGNMENTS .
                                 ' WHERE parent_id=' . $this->get('id');
        $records_old_assignments = $db->GetAll($query_old_assignments);
        $old_assignments = array();
        if (!empty($records_old_assignments)) {
            $old_assignments_type = '';
            $old_assignee = array();
            foreach ($records_old_assignments as $rec) {
                $old_assignee[] = $rec['assigned_to'];
                if (!$old_assignments_type) {
                    $old_assignments_type = $rec['assignments_type'];
                }
            }
            $old_assignee = array_unique($old_assignee);
            if ($old_assignments_type == 'Users') {
                // if the old assignments is for users then this is the required array
                $old_assignments = $old_assignee;
            } else {
                // if the old assignments are by Departments
                $query_old_departments = 'SELECT parent_id FROM ' . DB_TABLE_USERS_DEPARTMENTS .
                                         ' WHERE department_id IN (' . implode(',', $old_assignee) . ')';
                $old_records_departments_users = $db->GetCol($query_old_departments);
                $old_assignments = array_unique($old_records_departments_users);
            }
        }

        // delete old assignments
        $query = 'DELETE FROM ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' WHERE parent_id=' . $this->get('id');
        $db->Execute($query);
        $departments = false;
        $dbTransError1 = false;

        // email template
        $template = 'project_assign';
        $sent_to = array();

        //save ONLY departments OR ONLY users (not both)
        if ($this->get('departments_assignments') && $this->get('assignments_type') == 'Departments') {

            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';

            $inserts = array();
            $users = array();
            $assignments = $this->get('departments_assignments');
            foreach ($assignments as $assignment) {
                $inserts[] =  '(' . $this->get('id') . ', ' . $this->registry['currentUser']->get('id') . ', ' . $assignment . ', now(), \'Departments\')';
            }
            $query  = 'INSERT INTO ' . DB_TABLE_PROJECTS_ASSIGNMENTS .
                      ' (parent_id, assigned_by, assigned_to, assigned, assignments_type)' .
                      ' VALUES '.implode(',', $inserts);
            $db->Execute($query);

            $dbTransError1 = $db->HasFailedTrans();

            // PREPARE SENDING OF E-MAILS
            if (!$dbTransError1) {
                // take users from assigned departments
                $query_departments = 'SELECT parent_id FROM ' . DB_TABLE_USERS_DEPARTMENTS .
                                     ' WHERE department_id IN (' . implode(',', $assignments) . ')';
                $records_departments_users = $db->GetCol($query_departments);
                $records_departments_users = array_unique($records_departments_users);

                $new_assigned_users = array_diff($records_departments_users, $old_assignments);

                if (!empty($new_assigned_users)) {
                    // send assign notifications
                    //get ids of users who ignore this type of email
                    $not_users = Users::getUsersNoSend($this->registry, $template);

                    //get assignment user data
                    $query = 'SELECT ' . "\n" .
                             '  u2.id, u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname,' . "\n" .
                             '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name ' . "\n" .
                             'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                             '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="'
                             . $this->get('model_lang') . '")' . "\n" .
                             ' WHERE u2.id IN (' . implode(',', $new_assigned_users) . ') AND u2.active=1 AND u2.deleted_by=0 AND u2.hidden=0';

                    $users_records = $db->GetAssoc($query);

                    foreach ($new_assigned_users as $assignment) {
                        //do not notify currentUser
                        if ($assignment != $this->registry['currentUser']->get('id') && !in_array($assignment, $not_users) && isset($users_records[$assignment])) {
                            $assigned_user = $users_records[$assignment];

                            $send_result = $this->sendNotification($template, $assigned_user['assignment_email'], $assigned_user['assignment_name'], true);
                            if ($send_result) {
                                $sent_to[] = $assigned_user['assignment_name'];
                            }
                        }
                    }
                }
            }
        } elseif (($this->get('assignments_type') == 'Users' && $this->get('users_assignments'))) {

            $inserts = array();
            $assignments = $this->get('users_assignments');
            if (!empty($assignments)) {
                foreach ($assignments as $assignment) {
                    $inserts[] =  '(' . $this->get('id') . ',' . $this->registry['currentUser']->get('id'). ',' .$assignment.', now(), \'Users\')';
                }

                $query  = 'INSERT INTO ' . DB_TABLE_PROJECTS_ASSIGNMENTS .
                          ' (parent_id, assigned_by, assigned_to, assigned, assignments_type)' .
                          ' VALUES '.implode(',', $inserts);
                $db->Execute($query);

                // PREPARE SENDING OF E-MAILS
                if (!$db->HasFailedTrans()) {
                    $new_assigned_users = array_diff($assignments, $old_assignments);

                    if (!empty($new_assigned_users)) {
                        // send assign notifications
                        //get ids of users who ignore this type of email
                        $not_users = Users::getUsersNoSend($this->registry, $template);

                        //get assignment user data
                        $query = 'SELECT ' . "\n" .
                                 '  u2.id, u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname,' . "\n" .
                                 '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name ' . "\n" .
                                 'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                                 '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="'
                                 . $this->get('model_lang') . '")' . "\n" .
                                 ' WHERE u2.id IN (' . implode(',', $new_assigned_users) . ') AND u2.active=1 AND u2.deleted_by=0';

                        $users_records = $db->GetAssoc($query);

                        foreach ($new_assigned_users as $assignment) {
                            //do not notify currentUser
                            if ($assignment != $this->registry['currentUser']->get('id') && !in_array($assignment, $not_users) && isset($users_records[$assignment])) {
                                $assigned_user = $users_records[$assignment];
                                $send_result = $this->sendNotification($template, $assigned_user['assignment_email'], $assigned_user['assignment_name'], true);
                                if ($send_result) {
                                    $sent_to[] = $assigned_user['assignment_name'];
                                }
                            }
                        }
                    }
                }
            }
        }

        if (count($sent_to)) {
            $notify_for = $this->i18n('projects_' . $template . '_notify', array($this->getModelTypeName()));
            if (count($sent_to)) {
                if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                    $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
                } else {
                    $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        $dbTransError2 = $db->HasFailedTrans();
        $result = !$dbTransError1 && !$dbTransError2;

        return $result;
    }

    /**
     * Get assignments from database
     *
     * @return bool - result of the operation
     */
    public function getAssignments($assignment_type = '', $from_post = false) {
        if (!isset($this->registry)) {
            $registry = $GLOBALS['registry'];
        } else {
            $registry = $this->registry;
        }
        $db = $registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $registry['lang'];
        $assignments_type = '';

        //get the departments assigned to the project
        $query = 'SELECT da.assigned_to as idx, da.* ,'.
                '  di18n.name as assigned_to_name, ' . "\n" .
                '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assigned_by_name ' . "\n" .
                'FROM ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' AS da '.
                'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS d '.
                'ON da.parent_id=d.id '.
                'LEFT JOIN ' . DB_TABLE_DEPARTMENTS_I18N . ' AS di18n' . "\n" .
                '  ON (da.assigned_to=di18n.parent_id AND di18n.lang="' . $lang . '")' . "\n" .
                'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                '  ON (da.assigned_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                'WHERE da.parent_id=' . $this->get('id') . ' AND assignments_type=\'Departments\'';
        $departments_records = $db->GetAssoc($query);
        $this->set('departments_assignments', $departments_records, true);
        if ($departments_records) {
            $assignments_type = 'Departments';

            //get the users assigned to the project (ONLY DEPARTMENTS ARE ASSIGNED)
            include_once PH_MODULES_DIR . 'departments/models/departments.factory.php';

            //get department members
            $department_users_ids = array();
            foreach ($departments_records as $dep) {
                $department_user_members = Departments::getUsersIds($registry, array('where' => array('d.id =' . $dep['assigned_to'])));
                if ($department_user_members) {
                    $department_users_ids = array_merge($department_users_ids, $department_user_members);
                }
            }
            //remove duplicate users
            $department_users_ids = array_unique($department_users_ids);
            $users_records = array();

            if (!empty($department_users_ids)) {
                //get the users assigned to the project (ONLY USERS ARE ASSIGNED)
                $query = 'SELECT u.id as idx, ' . $dep['parent_id'] . ' as parent_id, ' . $dep['assigned_by'] . ' as assigned_by, ' . "\n" .
                         'u.id as assigned_to, "' . $dep['assigned'] . '" as assigned, "Users" as assignments_type, ' . "\n" .
                         '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as assigned_to_name, u.email, u.is_portal, ' . "\n" .
                         '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assigned_by_name ' . "\n" .
                         'FROM ' . DB_TABLE_USERS . ' AS u '.
                           'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                           '  ON (u.id=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                           'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                           '  ON (' . $dep['assigned_by'] . '=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                         'WHERE u.id IN (' . implode(', ', $department_users_ids) . ') AND hidden=0';

                $users_records = $db->GetAssoc($query);
            }
            $this->set('users_assignments', $users_records, true);
        } else {
            //get the users assign to the project (ONLY USERS ARE ASSIGNED)
            $query = 'SELECT da.assigned_to as idx, da.*, ' . "\n" .
                     '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as assigned_to_name, u.email, u.is_portal, ' . "\n" .
                     '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assigned_by_name ' . "\n" .
                     'FROM ' . DB_TABLE_PROJECTS_ASSIGNMENTS . ' AS da ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS d ' . "\n" .
                     'ON da.parent_id=d.id ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                       '  ON (da.assigned_to=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                       '  ON (da.assigned_to=u.id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                       '  ON (da.assigned_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")' . "\n" .
                     'WHERE da.parent_id=' . $this->get('id') . ' AND assignments_type=\'Users\'';
            $users_records = $db->GetAssoc($query);
            $this->set('users_assignments', $users_records, true);
            if ($users_records && !$assignments_type) {
                $assignments_type = 'Users';
            }
        }

       $this->set('assignments_type', $assignments_type);

        return true;
    }

    /**
     * Insert into relatives table of the model on clone, transform
     *
     * @return bool - result of the operation
     */
    public function insertRelatives($multi = 0) {
        // If it's set to skip the relation between the parent record and the current one
        if ($this->get('skip_relatives')) {
            // Skip the relation
            return true;
        }

        $db = $this->registry['db'];

        $query4 = 'INSERT INTO ' . DB_TABLE_PROJECTS_RELATIVES . "\n" .
                  ' (parent_id, link_to, origin, multi_index) VALUES ' . "\n" .
                  '(' . $this->get('id') . ', ' . $this->get('origin_id'). ', ' .
                  '\'' . $this->get('clone_transform') . '\', ' . $multi . ')';
        $db->Execute($query4);

        return !$db->HasFailedTrans();
    }

    /**
     * Update relatives table of the model
     *
     * @param bool $delete - whether to delete old relations first
     * @return bool - result of the operation
     */
    public function updateRelatives($delete = true) {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($delete) {
            // delete old relations
            $query3 = 'DELETE FROM ' . DB_TABLE_PROJECTS_RELATIVES . "\n" .
                      ' WHERE link_to=' . $this->get('id') . ' AND origin="inherited"';
            $db->Execute($query3);
        }

        // add new relations
        $tmp = array();
        if (is_array($this->get('referers')) && count($this->get('referers')) > 0) {
            // get parents and children of current project
            $tree_p = $tree_c = array();
            $this->getParentsTree($this->get('id'), 0, $tree_p);
            $this->getChildrenTree($this->get('id'), 0, $tree_c);

            foreach ($this->get('referers') as $ref) {
                $insert = true;
                //check if referer is an ancestor of current model
                foreach ($tree_p as $rec) {
                    if ($rec['id'] == $ref) {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_projects_relative_child'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                //check if referer is a direct descendant (cloned) of current model
                foreach ($tree_c as $rec) {
                    if ($rec['id'] == $ref && $rec['level'] == 0 && $rec['origin'] != 'inherited') {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_projects_relative_child'), '', -1);
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                if ($insert && $this->get('id') != $ref) {
                    $tmp[] = '(' . $ref . ', ' . $this->get('id') . ', "inherited")';
                }
            }
            $query4 = 'INSERT INTO ' . DB_TABLE_PROJECTS_RELATIVES . ' (parent_id, link_to, origin) ' . "\n" .
                      'VALUES ' . implode(', ', $tmp);
        }

        if (count($tmp)) {
            $db->Execute($query4);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Get parents from database
     *
     * @return bool - result of the operation
     */
    public function getParents() {
        $db   = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        // Get all cloned+inherited children projects of the current project
        $query  = 'SELECT `pr`.`parent_id` AS `idx`, `pi18n`.`name`, `pr`.`parent_id` AS `id`, `pr`.`origin` AS `origin` ' . "\n" .
                  '  FROM `' . DB_TABLE_PROJECTS_RELATIVES . '` AS `pr` ' . "\n" .
                  '  LEFT JOIN `' . DB_TABLE_PROJECTS_I18N . '` AS `pi18n` ' . "\n" .
                  '    ON (`pr`.`parent_id` = `pi18n`.`parent_id` ' . "\n" .
                  '      AND `pi18n`.`lang` = \'' . $lang . '\') ' . "\n" .
                  '  WHERE `pr`.`link_to` = \'' . $this->get('id') . '\' AND pr.`origin` IN (\'cloned\', \'inherited\')';
        $referers = $db->GetAssoc($query);

        return $this->set('referers', $referers, true);
    }

    public function getChildrenTree($parent, $level, &$tree) {
        // retrieve all children of $parent
        $query = 'SELECT pr.parent_id, pr.origin' . "\n" .
                 'FROM ' . DB_TABLE_PROJECTS_RELATIVES . ' as pr' . "\n" .
                 'WHERE link_to="' . $parent . '"';

        $records = $this->registry['db']->GetAll($query);
        // display each child
        foreach ($records as $k => $rec) {
            $tree[] = array('id' => $rec['parent_id'], 'level' => $level, 'origin' => $rec['origin']);
            if ($level < PH_MAX_TREE_LEVEL) {
                $this->getChildrenTree($rec['parent_id'], $level+1, $tree);
            }
        }
    }

    public function getParentsTree($child, $level, &$tree) {
        // retrieve all parents of child
        $query = 'SELECT pr.link_to, pr.origin' . "\n" .
                 'FROM ' . DB_TABLE_PROJECTS_RELATIVES . ' as pr' . "\n" .
                 'WHERE parent_id="' . $child . '"';

        $records = $this->registry['db']->GetAll($query);
        // display each parent
        foreach ($records as $k => $rec) {
            $tree[] = array('id' => $rec['link_to'], 'level' => $level, 'origin' => $rec['origin']);
            if ($level < PH_MAX_TREE_LEVEL) {
                $this->getParentsTree($rec['link_to'], $level+1, $tree);
            }
        }
    }

    /**
     * Get model names
     *
     * @param array $filter - ids of referers
     * @return bool - result of the operation
     */
    public function getParentNames($filter) {
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT p.id AS idx, pi18n.name, p.id,' . "\n" .
                 '  IF(pr.origin IS NOT NULL, pr.origin, "inherited") AS origin' . "\n" .
                 'FROM ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                 '  ON p.id=pi18n.parent_id AND pi18n.lang="' . $lang . '"' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_PROJECTS_RELATIVES . ' AS pr' . "\n" .
                 '  ON pr.link_to = \'' . $this->get('id') . '\' AND pr.parent_id = p.id' . "\n" .
                 'WHERE p.id IN (' . implode(',', $filter) . ')';
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('referers', $records, true);
    }

    /**
     * Get projects files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $sanitized = $this->isSanitized();
        if ($sanitized) {
            $this->unsanitize();
        }
        $files = array();

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
            $ids_where . "\n" .
            '  AND origin="generated"' . "\n" .
            'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitized) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Change status
     *
     * @return bool - result of the operation
     */
    public function setStatus() {
        $flag_error = false;

        // Get the database object
        $db = $this->registry['db'];

        // Start a transaction
        $db->StartTrans();

        if ($this->isDefined('status') && !$this->get('status')) {
            $flag_error = true;
        } else {
            $current_status = $this->getProjectStatus();
            $status_name = $this->get('status');
            if ($current_status == 'progress' && $status_name == 'planning') {
                $flag_error = true;
            } else if ($current_status == 'control' && ($status_name == 'progress' || $status_name == 'planning')) {
                $flag_error = true;
            //if project is in 'finished' status, no status change is possible
            } else if ($current_status == 'finished') {
                $flag_error = true;
            }
        }

        // check for completed related records if required
        if (!$flag_error && $status_name == 'finished' && !$this->checkCompletedRelatedRecords()) {
            $flag_error = true;
        }

        if ($flag_error) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            $multistatus = $this->registry['request']->get('multistatusSelect');
            if (!$multistatus) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }

            // Complete the transaction before exit the function
            $db->CompleteTrans();

            return false;
        } else {
            if ($status_name == 'finished') {
                $set['finished'] = sprintf("`finished`='%d'", $this->get('substatus'));
            }
        }

        $set['status'] = sprintf("`status`='%s'", $status_name);
        $set['modified'] = sprintf("`modified`=now()");
        $set['modified_by'] = sprintf("modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));

        $db = $this->registry['db'];

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_PROJECTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        // The result is true if the transaction hasn't failed
        $dbTransError = $db->HasFailedTrans();
        $db->CompleteTrans();
        $result = !$dbTransError;

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        return $result;
    }

    /**
     * gets status of a project
     */
    public function getProjectStatus() {
        $db = $this->registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_PROJECTS .
                 ' WHERE id="' . $this->get('id') . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Create system tasks if the project's type requires it
     *
     * @return int - id of task
     */
    public function createSystemTask () {
        //add system task
        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
        $task = new Task($this->registry);
        $task->set('id', null, true);
        $task->set('name', ($this->i18n('projects_system_task_type') . ' ' . $this->get('name')), true);
        $task->set('customer', $this->get('customer'), true);
        $task->set('trademark', $this->get('trademark'), true);
        $task->set('project', $this->get('id'), true);
        $task->set('status', 'progress', true);

        $task->set('planned_start_date', General::strftime($this->i18n('date_iso')), true);

        //set type task
        $task->set('type', PH_TASK_SYSTEM_TYPE, true);
        $task->set('active', 1, true);
        if ($task->save()) {
            $task->set('project_referer', $this->get('id'), true);
            $task->updateRelativesProject();
        }

        return $task->get('id');
    }

    /**
     * Check and (if necessary) complete project system task
     */
    public function checkCompleteSystemTask() {
        $system_task_id = Projects::getSystemTask($this->registry, $this->get('id'));
        if ($system_task_id) {
            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
            $task = Tasks::searchOne($this->registry, array('where' => array('t.id="' . $system_task_id . '"', 't.type = ' . PH_TASK_SYSTEM_TYPE)));
            if ($task) {
                $task->set('status', 'finished', true);
                $task->setStatus();
            }
        }

        return true;
    }

    /**
     * Get stages for of the project and assign them as property to the current model
     *
     * @param bool $force - forcing to take the stages list again
     * @return array - list of all the stages for this project
     */
    public function getStages($force = false) {
        // unsanitize model if needed
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // check if the project has already started
        if (!$this->get('stages_info') || $force) {
            require_once PH_MODULES_DIR . 'stages/models/stages.events.factory.php';
            require_once PH_MODULES_DIR . 'projects/models/projects.stages.model.php';

            $filters = array('model_lang' => $this->get('model_lang'),
                             'model_id'   => $this->get('id'),
                             'sort'       => array('s.status'),
                             'where'      => array('s.parent_id = 0',
                                 's.model = \'Project\'',
                                 's.model_specialized = 0 OR s.model_specialized = \'' . $this->get('id') . '\'',
                                 's.model_type = ' . $this->get('type')));
            if ($this->get('stages')) {
                // if the current model has stages then the data for the order is taken from the history
                $filters['sort'][] = 'sh.model_id DESC';
                $filters['sort'][] = 'sh.position';

                // include only the stages which are active OR which are not active but are already included in the porject
                $filters['where'][] = '(s.active=1 OR s.active=0 AND s.id IN (' . implode(', ', $this->get('stages')) . '))';
            } else {
                // include only active stages for the newly added projects (inactive stages should not be included)
                $filters['where'][] = 's.active = 1';
            }

            // last sort is by the default position of the stages
            $filters['sort'][] = 's.num';
            // do not sort by active column so that the positioning is respected properly
            $filters['sort'][] = 's.active IS NOT NULL';
            $stages_list = Stages_Events::search($this->registry, $filters);

            $stages = array();
            $started_project = false;
            $deadline_type = PH_DEADLINE_DAYS;

            // order the phases by status
            foreach ($stages_list as $stg) {
                if (!isset($stages[$stg->get('status')])) {
                    $stages[$stg->get('status')] = array();
                }
                $stages[$stg->get('status')][$stg->get('id')] = $stg;

                // check if the stage has alreadu started
                // this will give us the information if the project has already started
                if ($stg->get('started') && $stg->get('started_by')) {
                    $started_project = true;
                }

                // check the deadline type of the stage
                // all included stages have the same deadline type so it needs only one to define the deadline type
                if ($stg->get('stage_deadline_type')) {
                    $deadline_type = $stg->get('stage_deadline_type');
                }
            }

            $this->set('stages_info', $stages, true);
            $this->set('started_project', $started_project, true);
            if (!$this->get('deadline_type')) {
                $this->set('deadline_type', $deadline_type, true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
        return $this->get('stages_info');
    }

    /**
     * Get only the stages which are included in the project's execution
     *
     * @return array - true - accessible, false - inaccessible
     */
    public function getIncludedStages() {
        if ($this->get('stages_info')) {
            $stages_list = $this->get('stages_info');
        } else {
            $stages_list = $this->getStages();
        }

        foreach ($stages_list as $status => $status_stages) {
            foreach ($status_stages as $key => $stg) {
                if ($stg->get('model_id') != $this->get('id')) {
                    unset($stages_list[$status][$key]);
                    if (empty($stages_list[$status])) {
                        unset($stages_list[$status]);
                    }
                }
            }
        }

        $this->set('stages_info', $stages_list, true);
        return true;
    }

    /**
     * Process the information for the stages when the model is posted
     */
    public function processPostStages() {
        if ($this->get('stages_action')) {
            $this->getIncludedStages();

            if ($this->get('stages_action') == 'complete_stage_info') {
                $last_active_stage = false;
                $next_active_stage = false;

                foreach ($this->get('stages_info') as $status => $stages_status) {
                    foreach ($stages_status as $key => $stg) {
                        if ($stg->get('started_by')) {
                            $last_active_stage = clone $stg;
                        }
                        if ($last_active_stage->get('id') != $stg->get('id')) {
                            $next_active_stage = clone $stg;
                            break 2;
                        }
                    }
                }

                // check finish date
                $previous_stage_finished = '';
                if ($this->isDefined('stage_date_finish')) {
                    if (!$this->get('stage_date_finish')) {
                        $this->raiseError('error_projects_stage_date_finish_missing', 'stage_date_finish');
                    } elseif ($this->get('stage_date_finish') < $last_active_stage->get('started')) {
                        $this->raiseError('error_projects_stage_date_finish_before_start', 'stage_date_finish');
                    } else {
                        $previous_stage_finished = $this->get('stage_date_finish');
                    }
                } else {
                    $previous_stage_finished = $last_active_stage->get('finished');
                }

                // check start date
                if ($this->get('start_stage')) {
                    if (!$this->get('start_stage_date')) {
                        $this->raiseError('error_projects_stage_start_date_missing', 'start_stage_date');
                    } elseif ($previous_stage_finished && $previous_stage_finished > $this->get('start_stage_date')) {
                        $this->raiseError('error_projects_stage_date_start_before_previous_finish', '', 0, array(date(PH_LOGGER_DATE_FORMAT, strtotime($previous_stage_finished))));
                    }
                }
            }
        } else {
            if (!$this->get('stages_included')) {
                $this->raiseError('error_projects_complete_at_least_one_stage', 'start_stage_date');
                return true;
            }

            $stages_deadline = $this->get('stages_deadline');
            $stages_limit_days = $this->get('stages_limit_days');
            $stages_limit_hours = $this->get('stages_limit_hours');
            $stages_working_hours = $this->get('stage_planned_working_hours');
            $stages_planned_budget = $this->get('stage_planned_budget');
            $stages_current_responsible = $this->get('stage_current_responsible');
            $stages_names_new = $this->get('new_stage_name');

            // process stages deadline
            if (!empty($stages_deadline)) {
                $new_stages_deadline = array();
                foreach ($stages_deadline as $key => $stg_deadline) {
                    list($stat_name, $stg_deadln) = explode('_', $key);
                    $new_stages_deadline[$stat_name][$stg_deadln] = $stg_deadline;
                }
                $stages_deadline = $new_stages_deadline;
                $this->set('stages_deadline', $stages_deadline, true);
                unset($new_stages_deadline);
            }

            $stages = array();
            $stages_info_old = $this->get('stages_info');
            $stages_info = array();
            $post_included_stages = $this->get('stages_included');

            require_once PH_MODULES_DIR . 'stages/models/stages.events.factory.php';
            foreach ($this->stagesStatuses as $status) {
                $stages_info[$status] = array();
                if (isset($post_included_stages[$status])) {
                    $stages = array_merge($stages, array_keys($post_included_stages[$status]));
                    foreach ($post_included_stages[$status] as $stg_id => $stg) {
                        if (preg_match('#^new([0-9]*)$#', $stg_id)) {
                            $new_event = new Stages_Event($this->registry);
                            $stages_info[$status][$stg_id] = $new_event->sanitize();
                            $stages_info[$status][$stg_id]->set('id', $stg_id, true);
                            unset($new_event);
                        } else {
                            $stages_info[$status][$stg_id] = $stages_info_old[$status][$stg_id];
                            unset($stages_info_old[$status][$stg_id]);
                        }
                    }
                }
                if (isset($stages_info_old[$status])) {
                    $stages_info[$status] = $stages_info[$status] + $stages_info_old[$status];
                }
                if (empty($stages_info[$status])) {
                    unset($stages_info[$status]);
                }
            }

            $total_working_hours = 0;
            $total_budget = 0;

            foreach ($stages_info as $status => $status_stages) {
                foreach ($status_stages as $key => $stg) {
                    if (isset($stages_deadline[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_deadline', (in_array($stg->get('id'), $stages) ? $stages_deadline[$status][$key] : ''), true);
                    }
                    if (isset($stages_limit_days[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_limit_days', (in_array($stg->get('id'), $stages) ? $stages_limit_days[$status][$key] : ''), true);
                    }
                    if (isset($stages_limit_hours[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_limit_hours', (in_array($stg->get('id'), $stages) ? $stages_limit_hours[$status][$key] : ''), true);
                    }
                    if (isset($stages_working_hours[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_working_hours', (in_array($stg->get('id'), $stages) ? $stages_working_hours[$status][$key] : ''), true);
                    }
                    if (isset($stages_planned_budget[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_budget', (in_array($stg->get('id'), $stages) ? $stages_planned_budget[$status][$key] : ''), true);
                    }
                    if (isset($stages_current_responsible[$status][$key])) {
                        $stages_info[$status][$key]->set('stage_current_responsible', (in_array($stg->get('id'), $stages) ? $stages_current_responsible[$status][$key] : ''), true);
                    }
                    if (isset($stages_names_new[$status][$key])) {
                        $stages_info[$status][$key]->set('name', $stages_names_new[$status][$key], true);
                    }
                }
            }

            // calculate the stages deadlines, budget and working hours
            $latest_stage_time = 0;
            if ($this->get('perform_revision')) {
                foreach ($stages_info as $status => $status_stages) {
                    foreach ($status_stages as $key => $stg) {
                        if ($stg->get('started_by')) {
                            $latest_stage_time = strtotime($stages_info[$status][$key]->get('started'));
                        }
                        if ($stg->get('finished_by')) {
                            $latest_stage_time = time();
                        }
                        if (!$stg->get('name')) {
                            $this->raiseError('error_missed_stage_name', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status)), true);
                        }
                    }
                }
            } else {
                if ($this->isDefined('start_stage_date')) {
                    if ($this->get('start_stage_date')) {
                        // start time could not be after the current time
                        if (strtotime($this->get('start_stage_date'))>time()) {
                            $this->raiseError('error_projects_start_stage_date', 'start_stage_date');
                        } elseif (empty($stages)) {
                            $this->raiseError('error_projects_complete_at_least_one_stage', 'start_stage_date');
                        } else {
                            $latest_stage_time = strtotime($this->get('start_stage_date'));
                        }
                    } else {
                        $this->raiseError('error_projects_complete_start_stage_date', 'start_stage_date');
                    }
                } else {
                    $latest_stage_time = time();
                }
            }

            if ($this->valid) {
                $error_missing_dates = false;
                $error_latest_dates = false;
                foreach ($stages_info as $status => $status_stages) {
                    $stage_position = 0;
                    foreach ($status_stages as $key => $stg) {
                        if ($this->get('perform_revision') && $stg->get('finished_by')) {
                            $stage_position++;
                            continue;
                        }

                        if (in_array($stg->get('id'), $stages)) {
                            $stg->set('stage_deadline_type', $this->get('deadline_type'), true);
                            if ($this->get('deadline_type') == PH_DEADLINE_DAYS) {
                                if ($stg->get('stage_limit_days') || $stg->get('stage_limit_hours')) {
                                    if (!$error_missing_dates) {
                                        $latest_stage_time = $latest_stage_time + (floatval($stg->get('stage_limit_hours')) * 3600) + (floatval($stg->get('stage_limit_days')) * 3600 * 24);
                                        $stages_info[$status][$key]->set('stage_deadline', date(PH_LOGGER_DATE_FORMAT, $latest_stage_time), true);
                                    } else {
                                        $this->raiseError('error_projects_previously_missed_date', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status), $stg->get('name')), true);
                                    }
                                } else {
                                    $error_missing_dates = true;
                                    $this->raiseError('error_stages_hours_invalid_format', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status), $stg->get('name')), true);
                                }
                                $stage_position++;
                            } elseif ($this->get('deadline_type') == PH_DEADLINE_DATE) {
                                if ($stg->get('stage_deadline')) {
                                    if (!$error_missing_dates) {
                                        $current_stage_deadline = strtotime($stg->get('stage_deadline'));
                                        if ($current_stage_deadline <= $latest_stage_time) {
                                            $error_latest_dates = true;
                                            $this->raiseError('error_projects_latest_date_invalid', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status), $stg->get('name')), true);
                                        } elseif (!$error_latest_dates && !$error_missing_dates) {
                                            $current_stage_time = $current_stage_deadline - $latest_stage_time;
                                            $latest_stage_time = $current_stage_deadline;
                                            $stages_info[$status][$key]->set('stage_limit_days', sprintf("%d", $current_stage_time / (60 * 60 * 24)), true);
                                            $stages_info[$status][$key]->set('stage_limit_hours', sprintf("%d", ($current_stage_time % (60 * 60 * 24)) / 3600), true);
                                        }
                                    } else {
                                        $this->raiseError('error_projects_previously_missed_date', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status), $stg->get('name')), true);
                                    }
                                } else {
                                    $error_missing_dates = true;
                                    $this->raiseError('error_projects_date_filled', sprintf('stages_list_%s_%d', $status, $stg->get('id')), 0, array($this->i18n('projects_status_' . $status), $stg->get('name')), true);
                                }
                                $stage_position++;
                            } else {
                                $stages_info[$status][$key]->set('stage_limit_days', 0, true);
                                $stages_info[$status][$key]->set('stage_limit_hours', 0, true);
                                $stages_info[$status][$key]->set('stage_deadline', '', true);
                            }

                            if ($this->valid) {
                                $stages_info[$status][$key]->set('stage_limit', (24*floatval($stages_info[$status][$key]->get('stage_limit_days')) + floatval($stages_info[$status][$key]->get('stage_limit_hours'))), true);
                            }
                            if ($stg->get('stage_working_hours')) {
                                $total_working_hours += $stg->get('stage_working_hours');
                            }
                            if ($stg->get('stage_budget')) {
                                $total_budget += $stg->get('stage_budget');
                            }
                        }
                    }
                }
            }

            if ($this->valid) {
                if ($this->get('deadline_type') != PH_DEADLINE_NONE) {
                    $this->set('calculated_deadline', date(PH_LOGGER_DATE_FORMAT, $latest_stage_time), true);
                }
                if ($this->get('date_end') && $latest_stage_time > strtotime($this->get('date_end'))) {
                    $this->set('warning_over_deadline', 1, true);
                }
            }
            if ($this->get('budget') && $total_budget > $this->get('budget')) {
                $this->set('warning_over_budget', 1, true);
            }

            if ($this->get('work_period') && $total_working_hours > $this->get('work_period')) {
                $this->set('warning_over_working_hours', 1, true);
            }

            if ($this->get('available_planned_budget')) {
                $this->set('total_budget', $total_budget, true);
            }
            if ($this->get('available_working_hours')) {
                $this->set('total_working_hours', $total_working_hours, true);
            }

            $this->set('stages_info', $stages_info, true);
            $this->set('stages', $stages, true);
        }

        return true;
    }

    /**
     * Process the information for the stages when the model comes from the DB
     */
    public function processDBStages() {
        $deadline_type = '';

        if ($this->get('stages')) {
            $stages_info = $this->get('stages_info');
            $total_working_hours = 0;
            $total_budget = 0;
            $latest_stage_time = time();
            foreach ($stages_info as $status => $status_stages) {
                foreach ($status_stages as $key => $stg) {
                    if (in_array($stg->get('id'), $this->get('stages'))) {
                        $total_working_hours += $stg->get('stage_working_hours');
                        $total_budget += $stg->get('stage_budget');
                        if (!$deadline_type) {
                            $deadline_type = $stg->get('stage_deadline_type');
                        }

                        if ($stg->get('stage_deadline_type') == PH_DEADLINE_NONE) {
                            $latest_stage_time = 0;
                        } else {
                            $stages_info[$status][$key]->set('stage_limit_days', floor($stg->get('stage_limit')/24), true);
                            $stages_info[$status][$key]->set('stage_limit_hours', (int)($stg->get('stage_limit')%24), true);
                            if ($stg->get('stage_deadline_type') == PH_DEADLINE_DAYS) {
                                if ($stg->get('finished_by')) {
                                    $latest_stage_time = strtotime($stg->get('finished'));
                                } elseif ($stg->get('started_by')) {
                                    $latest_stage_time = strtotime($stg->get('started'));
                                    if (time() > ($latest_stage_time + 3600)) {
                                        $latest_stage_time = time();
                                    }
                                } else {
                                    $latest_stage_time = $latest_stage_time + ($stages_info[$status][$key]->get('stage_limit_hours') * 3600) + ($stages_info[$status][$key]->get('stage_limit_days') * 3600 * 24);
                                    $stages_info[$status][$key]->set('stage_deadline', date(PH_LOGGER_DATE_FORMAT, $latest_stage_time), true);
                                }
                            } else {
                                $latest_stage_time = strtotime($stg->get('stage_deadline'));
                            }
                        }

                        // check if the stage has already passed
                        $actual_durability_days = 0;
                        $actual_durability_hours = 0;
                        $late = false;
                        $expired_time = '';
                        $actual_deadline = '';

                        if ($stg->get('stage_recalculated_deadline') != '0000-00-00 00:00:00') {
                            $actual_deadline = $stg->get('stage_recalculated_deadline');
                        } else if ($stg->get('stage_deadline') != '0000-00-00 00:00:00') {
                            $actual_deadline = $stg->get('stage_deadline');
                        }

                        if ($stg->get('started_by') && $stg->get('finished_by')) {
                            $stages_info[$status][$key]->set('past_stage', true, true);

                            // cuts the minutes from the seconds and calculates the durability in minutes
                            $actual_durability_minutes = (strtotime(preg_replace('#\:[0-9]*$#', '', $stg->get('finished'))) - strtotime(preg_replace('#\:[0-9]*$#', '', $stg->get('started'))))/60;

                            // calculates the durability in days
                            $actual_durability_days = sprintf("%d", $actual_durability_minutes / (60 *24));

                            // calcultates the durability in minutes and hours
                            $durability_hours = intval(($actual_durability_minutes % (60 * 24)) / 60);
                            $durability_minutes = ($actual_durability_minutes % (60 * 24)) % 60;

                            // it is not possible to exist a stage with 0 durabillity so if
                            //days, hours and minutes are equal to zero the minutes are set to 1
                            if (!$actual_durability_days && !$durability_hours && $durability_minutes==0) {
                                $durability_minutes = 1;
                            }

                            // if the stage was exactly 0 hours and zero minutes long its durabillity will be shown only in days
                            if (!$durability_hours && !$durability_minutes) {
                                $actual_durability_hours = 0;
                            } else {
                                $actual_durability_hours = sprintf("%d:%02d", $durability_hours, $durability_minutes);
                            }

                            //check for lates
                            if ($actual_deadline) {
                                if (strtotime($actual_deadline) - strtotime($stg->get('finished')) < 0) {
                                    $late = true;
                                }
                            }
                        } elseif ($stg->get('started_by') && !$stg->get('finished_by')) {
                            if ($actual_deadline && date(PH_LOGGER_DATE_FORMAT) > $actual_deadline) {
                                $expired_time = $actual_deadline;
                            }
                        } else {
                            $stages_info[$status][$key]->set('past_stage', false, true);
                        }
                        $stages_info[$status][$key]->set('actual_durability_days', $actual_durability_days, true);
                        $stages_info[$status][$key]->set('actual_durability_hours', $actual_durability_hours, true);
                        $stages_info[$status][$key]->set('late', $late, true);
                        $stages_info[$status][$key]->set('deadline', $late, true);
                        $stages_info[$status][$key]->set('expired_time', $expired_time, true);
                        unset($actual_durability_days);
                        unset($actual_durability_hours);
                        unset($late);

                        // find out if the current phase is last
                        if ($stg->get('id') == $this->get('stage')) {
                            $position = array_search($stg->get('id'), $this->get('stages'));
                            if (($position+1) == count($this->get('stages'))) {
                                $stages_info[$status][$key]->set('last_stage', true, true);
                            }
                        }

                        if ($stages_info[$status][$key]->get('past_stage') || $stg->get('id') == $this->get('stage')) {
                            $stages_info[$status][$key]->set('activities', $stg->getStageEventActivities($this->registry, $stg->get('id'), $this->get('id')), true);
                        }
                    }
                }
            }

            $this->set('calculated_deadline', ($latest_stage_time ? date(PH_LOGGER_DATE_FORMAT, $latest_stage_time) : ''), true);

            if ($this->get('available_planned_budget')) {
                $this->set('total_budget', $total_budget, true);
            }
            if ($this->get('available_working_hours')) {
                $this->set('total_working_hours', $total_working_hours, true);
            }
            $this->set('stages_info', $stages_info, true);
        }

        if (!$deadline_type) {
            $deadline_type = PH_DEADLINE_DAYS;
        }
        $this->set('deadline_type', $deadline_type, true);

        return true;
    }

    /**
     * Function which completes the statuses without stages
     *   with a fictional stage so it can be used to add stages for theses statuses
     *
     * @return bool
     */
    public function processRevisionAdditionalStages() {
        $stages_info = $this->get('stages_info');
        $empty_statuses = array();
        foreach ($this->stagesStatuses as $prj_status) {
            if (empty($stages_info[$prj_status])) {
                $empty_statuses[] = $prj_status;
            }
        }

        if (empty($empty_statuses)) {
            // no firther operations needed
            return true;
        }

        // define the the first unused index
        $indexes = array();
        foreach ($stages_info as $status => $stages_statuses) {
            foreach ($stages_statuses as $stg_id => $stage_data) {
                if (preg_match('#^new([0-9]*)#', $stg_id)) {
                    $indexes[] = preg_replace('#^new([0-9]*)#', '$1', $stg_id);
                }
            }
        }

        $rearrange_statuses = array();
        foreach ($this->stagesStatuses as $prj_status) {
            if (empty($stages_info[$prj_status])) {
                $idx = 1;
                while (in_array($idx, $indexes)) {
                    $idx++;
                }

                $indexes[] = $idx;
                $new_stage = new Stages_Event($this->registry);
                $new_stage->sanitize();
                $new_stage->set('id', 'new' . $idx, true);
                $rearrange_statuses[$prj_status]['new' . $idx] = $new_stage;
                unset($new_stage);
            } else {
                $rearrange_statuses[$prj_status] = $stages_info[$prj_status];
            }
        }
        $this->set('stages_info', $rearrange_statuses, true);

        return true;
    }

    /**
     * Update projects stages and stages history
     *
     * @return bool
     */
    public function updateStages() {
        $db = $this->registry['db'];
        $db->StartTrans();

        if (!$this->get('started_project')) {
           /*
            * Save data for project which is not yest started
            * or is started right now
            */
            // delete the previous data for stages
            $query = 'DELETE FROM ' . DB_TABLE_STAGES_HISTORY . ' WHERE model_id=' . $this->get('id');
            $db->Execute($query);

            $stages_insert_data = array();
            $project_update_data = array();
            $phase_started = '';

            if ($this->get('stages')) {
                // UPDATE STAGES HISTORY
                // prepare the new data
                $current_position = 1;
                foreach ($this->get('stages_info') as $status => $stages_status) {
                    foreach ($stages_status as $stg) {
                        if (in_array($stg->get('id'), $this->get('stages'))) {
                            $set = array();
                            $set['position'] = $current_position;
                            $set['model_id'] = $this->get('id');
                            $set['stage_id'] = $stg->get('id');
                            $set['stage_deadline'] = "'" . $stg->get('stage_deadline') . "'";
                            $set['stage_deadline_type'] = $stg->get('stage_deadline_type');
                            $set['stage_limit'] = $stg->get('stage_limit');
                            $set['started'] = "'0000-00-00 00:00:00'";
                            $set['started_by'] = 0;
                            $set['group'] = $stg->get('group');
                            $set['stage_responsible'] = ($stg->get('stage_responsible') ? $stg->get('stage_responsible') : 0);
                            $set['stage_current_responsible'] = ($stg->get('stage_current_responsible') ? $stg->get('stage_current_responsible') : 0);
                            $set['stage_budget'] = ($stg->get('stage_budget') ? $stg->get('stage_budget') : 0);
                            $set['stage_working_hours'] = ($stg->get('stage_working_hours') ? $stg->get('stage_working_hours') : 0);

                            if ($this->get('start_stage') && $current_position == 1) {
                                $project_update_data['stage'] = sprintf("`stage`='%s'", $stg->get('id'));
                                $phase_started = $stg->get('id');
                                $project_update_data['status'] = sprintf("`status`='%s'", $stg->get('status'));
                                $project_update_data['date_start'] = sprintf("date_start='%s'", $this->get('start_stage_date'));
                                $project_update_data['date_end'] = sprintf("date_end='%s'", $this->get('calculated_deadline'));
                                $project_update_data['status_modified'] = sprintf("`status_modified`=now()");
                                $project_update_data['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
                                $set['started'] = sprintf("'%s'", $this->get('start_stage_date'));
                                $set['started_by'] = $this->registry['currentUser']->get('id');

                                if ($stg->get('status') == 'finished') {
                                    // complete the system task if the first stage status is finished
                                    $this->checkCompleteSystemTask();
                                }

                                // get the old and the new status to make possible sending of status notification
                                $this->set('old_project_status', $this->get('status'), true);
                                $this->set('new_project_status', $stg->get('status'), true);
                            }
                            $stages_insert_data[] = '(' . implode(', ', $set) . ')';
                            $current_position++;
                        }

                    }
                }
            }
            $project_update_data['stages'] = sprintf("`stages`='%s'", implode(',', $this->get('stages')));

            if (!empty($stages_insert_data)) {
                $query = 'INSERT INTO ' . DB_TABLE_STAGES_HISTORY .
                         ' (position, model_id, stage_id, stage_deadline, stage_deadline_type, stage_limit, started, started_by, stage_group, stage_responsible, stage_current_responsible, stage_budget, stage_working_hours)' . "\n" .
                         'VALUES '. implode(',' . "\n",$stages_insert_data);
                $db->Execute($query);
            }

            if (!empty($project_update_data)) {
                $project_update_data['modified'] = sprintf("`modified`=now()");
                $project_update_data['modified_by'] = sprintf("`modified_by`='%s'", $this->registry['currentUser']->get('id'));

                $query = 'UPDATE ' . DB_TABLE_PROJECTS . "\n" .
                         'SET ' . implode(',', $project_update_data) . "\n" .
                         'WHERE `id`=' . $this->get('id');
                $db->Execute($query);

                if ($this->get('start_stage') && $phase_started) {
                    $this->notifyStageUsers($phase_started);
                }
            }
        } else {
            /*
             * Manage with actions with phases which are active
            */
            if ($this->get('stages_action') == 'complete_stage_info') {
                $last_active_stage = false;
                $next_active_stage = false;

                foreach ($this->get('stages_info') as $status => $stages_status) {
                    foreach ($stages_status as $key => $stg) {
                        if ($stg->get('started_by')) {
                            $last_active_stage = clone $stg;
                        }

                        if ($last_active_stage->get('id') != $stg->get('id')) {
                            $next_active_stage = clone $stg;
                            break 2;
                        }
                    }
                }

                $phase_started = 0;
                $phase_finished = 0;

                if ($last_active_stage && $this->get('stage_date_finish')) {
                    if ($this->get('comment')) {
                        require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                        $comment = Comments::buildModel($this->registry);
                        $comment->set('content', $this->get('comment'), true);
                        $comment->set('subject', $this->i18n('projects_stage_finish'), true);
                        $comment->set('model', 'Project', true);
                        $comment->set('model_id', $this->get('id'), true);
                        $comment->unsetProperty('id', true);

                        if ($comment->save()) {
                            $comment->saveHistory($this);

                            //show corresponding message
                            $this->registry['messages']->setMessage($this->i18n('message_projects_comments_add_success'), '', -1);
                        } else {
                            //some error occurred
                            //show corresponding error(s)
                            $this->registry['messages']->setError($this->i18n('error_comments_add_failed'), '', -1);
                        }
                    }

                    //log finished stage
                    if (isset($comment) && $comment->get('id')) {
                        $comment_id = $comment->get('id');
                    } else {
                        $comment_id = 0;
                    }
                    Stages_Event::stagesLogEvent($this->registry, $this->get('id'), $last_active_stage->get('id'), 'finish', $this->get('stage_date_finish'), $comment_id);
                    $phase_finished = $last_active_stage->get('id');
                }

                if ($next_active_stage && $this->get('start_stage')) {
                    //log start stage
                    Stages_Event::stagesLogEvent($this->registry, $this->get('id'), $next_active_stage->get('id'), 'start', $this->get('start_stage_date'), 0);
                    $phase_started = $next_active_stage->get('id');
                }

                if ($phase_started || $phase_finished) {
                    $update_clauses = array();
                    // prepare the clauses to update the project
                    $update_clauses[] = '`modified`=now()';
                    $update_clauses[] = '`modified_by`="' . $this->registry['currentUser']->get('id') . '"';

                    // prepare updates concerning project status
                    if ($phase_started && $last_active_stage->get('status') != $next_active_stage->get('status')) {
                        // when new stage starts
                        $update_clauses[] = '`status_modified`=now()';
                        $update_clauses[] = '`status_modified_by`="' . $this->registry['currentUser']->get('id') . '"';
                        $update_clauses[] = '`status`="' . $next_active_stage->get('status') . '"';

                        // get the old and the new status to make possible sending of status notification
                        $this->set('old_project_status', $this->get('status'), true);
                        $this->set('new_project_status', $next_active_stage->get('status'), true);

                        // check if the new stage is in finished status
                        if ($next_active_stage->get('status') == 'finished') {
                            $this->checkCompleteSystemTask();
                        }
                    }

                    if ($phase_finished) {
                        // check if the current phase is the last one
                        $stages = $this->get('stages');
                        if ($stages[count($stages)-1] == $last_active_stage->get('id')) {
                            // if it is the last one few additional changes has to be made
                            $update_clauses[] = '`finished`="' . intval($this->get('finished')) . '"';
                            if ($this->get('status') != 'finished') {
                                $update_clauses[] = '`status_modified`=now()';
                                $update_clauses[] = '`status_modified_by`="' . $this->registry['currentUser']->get('id') . '"';
                                $update_clauses[] = '`status`="finished"';
                                $this->checkCompleteSystemTask();

                                // get the old and the new status to make possible sending of status notification
                                $this->set('old_project_status', $this->get('status'), true);
                                $this->set('new_project_status', 'finished', true);
                            }
                        }
                    }

                    if ($phase_started) {
                        $update_clauses[] = '`stage`="' . $phase_started . '"';
                    } else {
                        $update_clauses[] = '`stage`="0"';
                    }

                    //update stage
                    $query  = 'UPDATE ' . DB_TABLE_PROJECTS . ' SET ' . implode(', ', $update_clauses) . ' WHERE ' . General::buildClause('id', $this->get('id'));
                    $db->Execute($query);
                    if ($phase_started) {
                        $this->set('next_stage_name', $next_active_stage->get('name'), true);
                    }
                    if ($phase_finished) {
                        $this->set('current_stage_name', $last_active_stage->get('name'), true);
                    }
                }

                //send notifications
                $this->notifyStageUsers($phase_started, $phase_finished);
            } elseif ($this->get('stages_action') == 'complete_stage_activities') {
                $this->saveActivitiesHistory();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();
        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Saves activiies history
     *
     * @return bool - result of the operation
     */
    public function saveActivitiesHistory() {
        $db = $this->registry['db'];

        $set = array();

        $insert = array();
        $result = true;

        if ($this->get('activities')) {
            $set['model_id']            = sprintf("%d", $this->get('id'));
            $set['finished']            = sprintf("now()");
            $set['finished_by']         = sprintf("%d", $this->registry['currentUser']->get('id'));
            foreach ($this->get('activities') as $activity) {
                $set['stage_activity_id']   = sprintf("%d", $activity);
                $insert[] = '(' . implode(', ', $set) . ')';
            }
            $query = 'INSERT INTO ' . DB_TABLE_STAGES_ACTIVITIES_HISTORY . '(`model_id`, `finished`, `finished_by`, `stage_activity_id`) VALUES ' . "\n" .
                     implode(', ' . "\n", $insert) . ";";

            //start transaction
            $db->StartTrans();
            $db->Execute($query);

            if ($db->ErrorMsg()) {
                $this->registry['logger']->dbError('add new project base details', $db, $query1);
                $this->registry['messages']->setError($db->ErrorMsg());
            }

            //complete the transaction (commit/rollback whether SQL failed or not)
            $dbTransError = $db->HasFailedTrans();
            $db->CompleteTrans();

            //the result is true if there is no transaction error
            $result = !$dbTransError;
        } else {
            $this->registry['messages']->setWarning($this->i18n('message_projects_warning_no_activities_to_be_finished'));
            $result = false;
        }
        $this->set('activities_history_completed', $result, true);

        return $result;
    }

    /*
     * Function to notify the users who have any relation with the finished or started stage
     */
    function notifyStageUsers($start_stage_id = 0, $finish_stage_id = 0) {
        $db = $this->registry['db'];
        $records = array();

        //get stage current responsible for starting phase
        if ($start_stage_id) {
            $query = 'SELECT DISTINCT id, CONCAT(ui18n0.firstname, " ", ui18n0.lastname) as name, email, "stage_started" as template' . "\n" .
                     ' FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                     ' LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n0' . "\n" .
                     ' ON (u.id=ui18n0.parent_id AND ui18n0.lang="' . $this->registry->get('lang') . '")' . "\n" .
                     ' LEFT JOIN ' . DB_TABLE_STAGES_HISTORY . ' AS sh' . "\n" .
                     ' ON (sh.model_id=' . $this->get('id') . ' AND stage_id=' . $start_stage_id . ' AND stage_current_responsible=u.id)' . "\n" .
                     ' WHERE sh.model_id is not null AND u.active=1' . "\n";
            $records = $db->GetAssoc($query);
        }

        //get stage current responsible for finished phase
        if ($finish_stage_id) {
            $query = 'SELECT DISTINCT id, CONCAT(ui18n0.firstname, " ", ui18n0.lastname) as name, email, "stage_finished" as template' . "\n" .
                     ' FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                     ' LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n0' . "\n" .
                     ' ON (u.id=ui18n0.parent_id AND ui18n0.lang="' . $this->registry->get('lang') . '")' . "\n" .
                     ' LEFT JOIN ' . DB_TABLE_STAGES_HISTORY . ' AS sh' . "\n" .
                     ' ON (sh.model_id=' . $this->get('id') . ' AND stage_id=' . $finish_stage_id . ' AND stage_current_responsible=u.id)' . "\n" .
                     ' WHERE sh.model_id is not null AND u.active=1' . "\n";
            $records2 = $db->GetAssoc($query);
            foreach ($records2 as $user_id => $record) {
                if (isset($records[$user_id])) {
                    $records[$user_id]['template'] = 'stage_finished_started';
                } else {
                    $records[$user_id] = $record;
                }
            }
        }

        //set template
        $template = '';
        if ($start_stage_id && $finish_stage_id) {
            $template = 'stage_finished_started';
        } elseif ($start_stage_id) {
            $template = 'stage_started';
        } elseif ($finish_stage_id) {
            $template = 'stage_finished';
        } else {
            return true;
        }

        //get user assignments
        $users_records = $this->get('users_assignments');
        if (count($users_records)) {
            foreach ($users_records as $user_id => $user) {
                if (isset($records[$user_id])) {
                    $records[$user_id]['template'] = $template;
                } else {
                    $records[$user_id] = array('name' => $user['assigned_to_name'], 'email' => $user['email'], 'template' => $template);
                }
            }
        }

        //get project manager
        if (!isset($records[$this->get('manager')])) {
            $query = 'SELECT CONCAT(ui18n0.firstname, " ", ui18n0.lastname) as name, email, "' . $template . '" as template' . "\n" .
                     ' FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                     ' LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n0' . "\n" .
                     '   ON (u.id=ui18n0.parent_id AND ui18n0.lang="' . $this->registry->get('lang') . '")' . "\n" .
                     ' WHERE u.id=' . $this->get('manager') . "\n";
            $record = $db->GetRow($query);
            $records[$this->get('manager')] = $record;
        }

        if (count($records) && $template) {
            $not_users = Users::getUsersNoSend($this->registry, $template);

            $sent_to = array();
            foreach ($records as $user_id => $user) {
                if (!in_array($user_id, $not_users) &&
                    $user_id != $this->registry['currentUser']->get('id') &&
                    $this->sendNotification($user['template'], $user['email'], $user['name'], true)) {

                    // get which users has to receive notifications
                    $sent_to[] = $user['name'];
                }
            }

            if (count($sent_to)) {
                $notify_for = $this->i18n('projects_' . $template . '_notify', array($this->getModelTypeName()));
                if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                    $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
                } else {
                    $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
        }

        return true;
    }

    /**
     * Check if the project has stages (or is supposed to have)
     *
     * @return boolean - true if any stages found, otherwise false
     */
    public function checkStages() {
        if (!isset($this->registry)) {
            $registry = &$GLOBALS['registry'];
        } else {
            $registry = &$this->registry;
        }

        //check if the project has stages defined
        require_once PH_MODULES_DIR . 'stages/models/stages.phases.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('sp.parent_id = 0',
                                          'sp.model = \'' . $this->modelName . '\'',
                                          'sp.model_specialized = 0' . ($this->get('id') ? ' OR sp.model_specialized = \'' . $this->get('id') . '\'' : ''),
                                          'sp.model_type = \'' . $this->get('type') . '\''));
        $stages = Stages_Phases::getIds($registry, $filters);

        return !empty($stages);
    }

    /**
     * Update projects stages and stages history
     *
     * @return bool
     */
    public function updateRevisionedStages() {
        $db = $this->registry['db'];
        $db->StartTrans();

        //UPDATE THE PROJECTS TABLE
        $set = array();
        $stages_ids = array();

        $stages_info = $this->get('stages_info');
        $stages_info_rearrange = array();
        foreach ($stages_info as $status => $stages_status) {
            foreach ($stages_status as $stg_id => $stg) {
                if (in_array($stg_id, $this->get('stages'))) {
                    if (preg_match('#^new#', $stg_id)) {
                        // if the stage's id contains 'new' than it is a new stage and has to be added
                        require_once PH_MODULES_DIR . 'stages/models/stages.phases.factory.php';
                        $new_stage = Stages_Phases::buildModel($this->registry);
                        $new_stage->unsetProperty('id', true);
                        $new_stage->unsetProperty('parent_id', true);
                        $new_stage->set('status', $status, true);
                        $new_stage->set('model_type', $this->get('type'), true);
                        $new_stage->set('num', 0, true);
                        $new_stage->set('time_limit', 0, true);
                        $new_stage->set('active', 1, true);
                        $new_stage->set('model_specialized', $this->get('id'), true);
                        $new_stage->set('name', $stg->get('name'), true);
                        $new_stage->set('description', '', true);
                        if ($new_stage->save()) {
                            $stages_ids[] = $new_stage->get('id');
                            $stages_info_rearrange[$status][$new_stage->get('id')] = $stg;
                            $stages_info_rearrange[$status][$new_stage->get('id')]->set('id', $new_stage->get('id'), true);
                        }
                    } else {
                        $stages_ids[] = $stg_id;
                        $stages_info_rearrange[$status][$stg_id] = $stg;
                    }
                } else {
                    $stages_info_rearrange[$status][$stg_id] = $stg;
                }
            }
        }

        $this->set('stages', $stages_ids, true);
        $this->set('stages_info', $stages_info_rearrange, true);
        unset($stages_ids);
        unset($stages_info_rearrange);

        $set['stages'] = sprintf('stages="%s"', implode(', ', $this->get('stages')));
        if ($this->get('total_budget')) {
            $set['budget'] = sprintf("budget='%s'", $this->get('total_budget'));
        }
        if ($this->get('total_working_hours')) {
            $set['work_period'] = sprintf("work_period='%s'", $this->get('total_working_hours'));
        }
        $set['date_end'] = sprintf("date_end='%s'", $this->get('calculated_deadline'));

        if (count($set)) {
            //query to update the main table
            $query1 = 'UPDATE ' . DB_TABLE_PROJECTS . "\n" .
                      'SET ' . implode(', ', $set) . "\n" .
                      'WHERE id=' . $this->get('id');
            $db->Execute($query1);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
            }
        }

        //UPDATE STAGES_HISTORY TABLE
        if (count($this->get('stages'))) {
            // delete the old stages
            $query2 = 'DELETE FROM ' . DB_TABLE_STAGES_HISTORY . ' WHERE model_id="' . $this->get('id') . '"';
            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
            }

            //update array
            $set_for_stages_history = array();
            $insert_for_stages_history = array();

            //general sequence of the stages
            $general_sequence = 1;


            foreach ($this->get('stages_info') as $status => $stages_status) {
                foreach ($stages_status as $stg_id => $stage) {
                    if (in_array($stg_id, $this->get('stages'))) {
                        $set_for_stages_history['position'] = $general_sequence;
                        $general_sequence++;
                        $set_for_stages_history['model_id'] = $this->get('id');
                        $set_for_stages_history['stage_id'] = $stage->get('id');
                        $set_for_stages_history['stage_group'] = ($stage->get('stage_group') ? $stage->get('stage_group') : '0');
                        $set_for_stages_history['stage_responsible'] = ($stage->get('stage_responsible') ? $stage->get('stage_responsible') : '0');
                        $set_for_stages_history['stage_current_responsible'] = ($stage->get('stage_current_responsible') ? $stage->get('stage_current_responsible') : '0');
                        $set_for_stages_history['stage_budget'] = ($stage->get('stage_budget') ? $stage->get('stage_budget') : '0');
                        $set_for_stages_history['stage_working_hours'] = ($stage->get('stage_working_hours') ? $stage->get('stage_working_hours') : '0');
                        $set_for_stages_history['stage_deadline'] = ($stage->get('stage_deadline') ? "'" . $stage->get('stage_deadline') . "'" : 'NULL');
                        $set_for_stages_history['stage_recalculated_deadline'] = ($stage->get('stage_recalculated_deadline') ? "'" . $stage->get('stage_recalculated_deadline') . "'" : "'" . '0000-00-00 00:00:00' . "'");
                        $set_for_stages_history['stage_deadline_type'] = ($stage->get('stage_deadline_type') ? "'" . $stage->get('stage_deadline_type') . "'" : '0');
                        $set_for_stages_history['stage_limit'] = $stage->get('stage_limit');
                        $set_for_stages_history['stage_comment'] = ($stage->get('stage_comment') ? $stage->get('stage_comment') : '0');
                        $set_for_stages_history['started'] = ($stage->get('started') ? "'" . $stage->get('started') . "'" : "'" . '0000-00-00 00:00:00' . "'");
                        $set_for_stages_history['started_by'] = ($stage->get('started_by') ? "'" . $stage->get('started_by') . "'" : '0');
                        $set_for_stages_history['finished'] = ($stage->get('finished') ? "'" . $stage->get('finished') . "'" : "'" . '0000-00-00 00:00:00' . "'");
                        $set_for_stages_history['finished_by'] = ($stage->get('finished_by') ? "'" . $stage->get('finished_by') . "'" : '0');

                        $insert_for_stages_history[] = '(' . implode(',', $set_for_stages_history) . ')';
                    }
                }
            }

            if (count($insert_for_stages_history)) {
                $query3 = 'INSERT INTO ' . DB_TABLE_STAGES_HISTORY .
                          ' (position, model_id, stage_id, stage_group, stage_responsible, stage_current_responsible, stage_budget, stage_working_hours, stage_deadline, stage_recalculated_deadline, stage_deadline_type, stage_limit, stage_comment, started, started_by, finished, finished_by) VALUES ' . "\n" . implode(',' . "\n" ,$insert_for_stages_history);
                $db->Execute($query3);

                if ($db->ErrorMsg()) {
                    $this->registry['messages']->setError($db->ErrorMsg());
                }
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Gets the count of documents related to this project
     *
     * @return int - the number of the documents
     */
    public function getDocumentsCount() {
        if ($this->isDefined('count_documents')) {
            return $this->get('count_documents');
        }

        if ($this->get('id')) {
            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            //get the count of documents for this project
            $query = 'SELECT COUNT(id) FROM ' . DB_TABLE_DOCUMENTS . ' WHERE deleted=0 AND project=' . $this->get('id');
            $count = $this->registry['db']->GetOne($query);
            $this->set('count_documents', $count, true);

            if ($sanitize_after) {
                $this->sanitize();
            }
            return $this->get('count_documents');
        } else {
            return false;
        }
    }

    /**
     * Gets the count of tasks related to this project
     *
     * @return int - the number of the tasks
     */
    public function getTasksCount() {
        if ($this->isDefined('count_tasks')) {
            return $this->get('count_tasks');
        }

        if ($this->get('id')) {
            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            //get the count of tasks for this project, exclude the system tasks (those used for timesheets)
            $query = 'SELECT COUNT(id) FROM ' . DB_TABLE_TASKS . ' WHERE deleted=0 AND type!="' . PH_TASK_SYSTEM_TYPE . '" AND project=' . $this->get('id');
            $count = $this->registry['db']->GetOne($query);
            $this->set('count_tasks', $count, true);

            if ($sanitize_after) {
                $this->sanitize();
            }
            return $this->get('count_tasks');
        } else {
            return false;
        }
    }

    /**
     * Gets related to the model records
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        //gets which modules of optional related records should be displayed
        list($related_records_modules) = $this->getRelatedRecordsModules();

        // do not display modules that user has no access to
        $rights = $registry['currentUser']->get('rights');

        if (in_array('documents', $related_records_modules)) {
            //get related documents
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('d.project=' . $this->get('id')),
                             'check_module_permissions' => 'documents');
            $result = Documents::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['documents'] = array('name' => 'documents',
                                          'label' => $this->i18n('menu_documents'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        if (in_array('tasks', $related_records_modules)) {
            //get related tasks
            require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('t.project=' . $this->get('id'),
                                              't.type != ' . PH_TASK_SYSTEM_TYPE),
                             'check_module_permissions' => 'tasks');
            $result = Tasks::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['tasks'] = array('name' => 'tasks',
                                      'label' => $this->i18n('menu_tasks'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if (in_array('events', $related_records_modules)) {
            //get related events
            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('e.project=\'' . $this->get('id') . '\' OR',
                                              'erl2.link_to=\'' . $this->get('id') . '\' AND',
                                              'e.type!=' . PH_REMINDER_EVENT_TYPE),
                             'check_module_permissions' => 'events');
            $result = Events::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['events'] = array('name' => 'events',
                                       'label' => $this->i18n('menu_events'),
                                       'link' => $link,
                                       'ids' => is_array($result) ? $result : array());
        }

        if (in_array('contracts', $related_records_modules)) {
            //get related contracts
            require_once PH_MODULES_DIR . 'contracts/models/contracts.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('co.project=' . $this->get('id'),
                                              'co.subtype!="original"'),
                             'check_module_permissions' => 'contracts');
            $result = Contracts::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['contracts'] = array('name' => 'contracts',
                                          'label' => $this->i18n('menu_contracts'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_incomes_reasons', $related_records_modules)) {
            //get related finance incomes reasons
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fir.project=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Incomes_Reasons::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['finance_incomes_reasons'] = array('name' => 'finance_incomes_reasons',
                                                        'label' => $this->i18n('menu_finance_incomes_reasons'),
                                                        'link' => $link,
                                                        'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_expenses_reasons', $related_records_modules)) {
            //get related finance expenses reasons
            require_once PH_MODULES_DIR . 'finance/models/finance.expenses_reasons.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fer.project=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Expenses_Reasons::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['finance_expenses_reasons'] = array('name' => 'finance_expenses_reasons',
                                                         'label' => $this->i18n('menu_finance_expenses_reasons'),
                                                         'link' => $link,
                                                         'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_payments', $related_records_modules)) {
            //get related finance payments
            require_once PH_MODULES_DIR . 'finance/models/finance.payments.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fp.project=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Payments::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['finance_payments'] = array('name' => 'finance_payments',
                                                 'label' => $this->i18n('menu_finance_payments'),
                                                 'link' => $link,
                                                 'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_recurring_payments', $related_records_modules)) {
            //get related finance recurring payments
            require_once PH_MODULES_DIR . 'finance/models/finance.recurring_payments.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('frp.project=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Recurring_Payments::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['finance_recurring_payments'] = array('name' => 'finance_recurring_payments',
                                                           'label' => $this->i18n('menu_finance_recurring_payments'),
                                                           'link' => $link,
                                                           'ids' => is_array($result) ? $result : array());
        }

        if (in_array('finance_warehouses_documents', $related_records_modules)) {
            //get related finance warehouses documents
            require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('fwd.project=' . $this->get('id')),
                             'check_module_permissions' => 'finance');
            $result = Finance_Warehouses_Documents::getIds($registry, $filters);
            $link = '#related_subpanel_project' . $this->get('id');
            $related['warehouse'] = array('name' => 'finance_warehouses_documents',
                                          'label' => $this->i18n('menu_finance_warehouses_documents'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_COMMENTS . "\n" .
                         'WHERE deleted_by = 0 AND model = \'Project\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=projects&amp;';
                $link .= 'projects=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                $related['comments'] = array('name' => 'comments',
                                             'label' => $this->i18n('projects_comments'),
                                             'link' => $link,
                                             'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Project\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=projects&amp;';
                $link .= 'projects=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                $related['emails'] = array('name' => 'email',
                                           'label' => $this->i18n('projects_emails'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'Project\' AND model_id=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=projects&amp;';
                $link .= 'projects=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related attachments
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Project\' AND f.model_id = ' . $this->get('id');
            //check access permissions of files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $query .= Files::getAdditionalWhere($registry);

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=projects&amp;';
            $link .= 'projects=attachments&amp;attachments=' . $this->get('id');
            $related['files'] = array('name' => 'attachments',
                                      'label' => $this->i18n('attachments'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if ($this->checkPermissions('viewtimesheets')) {
            //get related timesheets
            $query = 'SELECT tt.id' . "\n" .
                     'FROM ' . DB_TABLE_TASKS_TIMESHEETS . " AS tt\n" .
                     'JOIN ' . DB_TABLE_TASKS . " AS t\n" .
                     '  ON t.id = tt.task_id' . "\n" .
                     'JOIN ' . DB_TABLE_TASKS_RELATIVES . " AS tr\n" .
                     '  ON tr.parent_id = tt.task_id' . "\n" .
                     'WHERE t.deleted_by = 0 AND t.type = ' . PH_TASK_SYSTEM_TYPE . "\n" .
                     '  AND tr.origin = \'project\'' . "\n" .
                     '  AND tr.link_to = ' . $this->get('id');

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=projects&amp;';
            $link .= 'projects=timesheets&amp;timesheets=' . $this->get('id');
            $related['timesheets'] = array('name' => 'timesheets',
                                           'label' => $this->i18n('timesheets'),
                                           'link' => $link,
                                           'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $related;
    }

    /**
     * Get all patterns variables - basic/system, currentUser, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Project", "CurrentUser", "Customer") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
            $pattern_format = ($pattern_format == 'docx2pdf') ? 'docx' : $pattern_format;
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        $translations = $this->getTranslations();
        foreach ($translations as $t_lang) {
            if ($t_lang != $this->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('p.id = ' . $this->get('id')));
                $t_project[$t_lang] = Projects::searchOne($this->registry, $filters);
                $t_project[$t_lang]->getVarsForTemplate();
                $t_a_vars[$t_lang] = $t_project[$t_lang]->get('vars');
            }
        }

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Project') {
                //project variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $this->get($placeholder->get('source'));
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                $vars[ $t_project[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')] =
                                (in_array($placeholder->get('source'),
                                    array('address', 'registration_address', 'address_by_personal_id'))) ?
                                nl2br($t_project[$t_lang]->get($placeholder->get('source'))) :
                                $t_project[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $this->get('model_lang') . '_' . $placeholder->get('varname')] =
                                (in_array($placeholder->get('source'),
                                    array('address', 'registration_address', 'address_by_personal_id'))) ?
                                nl2br($this->get($placeholder->get('source'))) :
                                $this->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                    //customer variables
                    if (!$placeholder->get('multilang')) {
                        if ($placeholder->get('source') == 'salutation') {
                            $salutation = '';
                            $vars[$placeholder->get('varname') . '_formal'] = '';

                            if ($customer->get('salutation')) {
                                $salutation = $customer->get('salutation');
                                $vars[$placeholder->get('varname') . '_formal'] =
                                $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $customer->get('lastname') ? $customer->get('lastname') : $customer->get('name');

                            $vars[$placeholder->get('varname')] = $salutation;
                        } else {
                            $vars[$placeholder->get('varname')] = $customer->get($placeholder->get('source'));
                        }
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[ $t_customer[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_customer[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $customer->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $customer->get($placeholder->get('source'));
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$placeholder->get('varname')] = $user->get($placeholder->get('source'));
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[ $t_user[$t_lang]->get('model_lang') . '_' . $placeholder->get('varname')]
                                = $t_user[$t_lang]->get($placeholder->get('source'));
                            } else {
                                $vars[ $user->get('model_lang') . '_' . $placeholder->get('varname')] =
                                $user->get($placeholder->get('source'));
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($placeholder->get('source'), '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $placeholder->get('source'));
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$placeholder->get('varname')] = $res;
                } else {
                    $vars[$placeholder->get('varname')] = $placeholder->get('source');
                }
            }
        }

        //prepare additional variables
        $this->getVarsForTemplate(false);

        //prepare BB variables
        $additional_vars = $this->get('vars');

        $bb_vars = $this->getBB(array('model_id' => $this->get('id')));

        $bb_elements = array();
        foreach ($additional_vars as $key => $var) {
            if (isset($var['bb']) && ($var['bb']) > 0 &&
                ($var['type'] == 'grouping' || $var['type'] == 'config' || $var['type'] == 'gt2')) {
                if ($var['type']!='gt2') {
                    $var['width'] = $var['width_print'];
                }
                $bb_elements[$var['id']] = $var;
                unset($additional_vars[$key]);
            }
        }

        // set additional vars back to model without the bb elements vars
        $this->set('vars', $additional_vars, true);

        $add_bb_vars = $this->getBBFields();
        foreach ($add_bb_vars as $bb_var_name => $bb_var_defs) {
            $add_bb_vars[$bb_var_name]['width'] = $bb_var_defs['width_print'];

            if ($bb_var_defs['type'] == 'file_upload') {
                if (!empty($bb_var_defs['value'])) {
                    foreach ($bb_var_defs['value'] as $bb_id => $file) {
                        // display thumbnail or file name
                        if (!empty($file) && is_object($file) && !$file->get('not_exist') && !$file->get('deleted_by')) {
                            $file = $this->getFileUploadForPrint($file, $bb_var_defs);
                        } else {
                            $file = '';
                        }
                        $add_bb_vars[$bb_var_name]['value'][$bb_id] = $file;
                    }
                }
            }
        }
        $this->set('add_bb_vars', $add_bb_vars, true);

        foreach ($bb_vars as $index => $var) {
            if (isset($bb_elements[$var['meta_id']])) {
                $bb_vars[$index] = $bb_elements[$var['meta_id']];
                $bb_vars[$index]['id'] = $var['id'];
                $bb_vars[$index]['meta_id'] = $var['meta_id'];
                $this->prepareBbVarValues($bb_vars[$index], $var['params'], true);

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (isset($bb_elements[$var['meta_id']]['names'])) {
                    foreach ($bb_elements[$var['meta_id']]['names'] as $bb_sub_elements) {
                        if (isset($bb_elements[$var['meta_id']][$bb_sub_elements]) && !empty($bb_elements[$var['meta_id']][$bb_sub_elements]['source'])) {
                            // parse the params
                            $source_fields = General::parseSettings($bb_elements[$var['meta_id']][$bb_sub_elements]['source']);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if (!empty($bb_vars[$index]['values'][$replaced_var])) {
                                    $bb_vars[$index]['values'][$bb_sub_elements] =
                                        str_replace('[a_' . $replaced_var . ']',
                                                    $bb_vars[$index]['values'][$bb_sub_elements],
                                                    $source_fields['replace_value']);

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if ($bb_vars[$index][$bb_sub_elements]['type'] == 'dropdown' || $bb_vars[$index][$bb_sub_elements]['type'] == 'radio' || $bb_vars[$index][$bb_sub_elements]['type'] == 'checkbox') {
                                        $bb_vars[$index][$bb_sub_elements]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->set('bb_vars', $bb_vars, true);
        //end of preparation of BB variables

        $additional_vars = $this->get('vars');

        foreach ($additional_vars as $k => $a_var) {
            if (isset($a_var['type']) && !in_array($a_var['type'], array('bb', 'grouping', 'config', 'table', 'gt2'))) {
                if (!$a_var['multilang']) {
                    if (isset($a_var['value']) && $a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['options'] as $opt) {
                            if ($opt['option_value'] == $a_var['value']) {
                                $vars['a_' . $a_var['name']] =
                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                break;
                            }
                        }
                    } elseif (isset($a_var['value']) && $a_var['value'] !== '' && is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['value'] as $val) {
                            foreach ($a_var['options'] as $opt) {
                                if ($opt['option_value'] == $val) {
                                    $vars['a_' . $a_var['name']][] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        }
                    } elseif ($a_var['type'] == 'textarea') {
                        $vars['a_' . $a_var['name']] = nl2br($a_var['value']);
                    } elseif ($a_var['type'] == 'file_upload') {
                        if (!empty($a_var['value']) && is_object($a_var['value']) && !$a_var['value']->get('not_exist') && !$a_var['value']->get('deleted_by')) {
                            if (isset($a_var['view_mode']) && $a_var['view_mode'] == 'thumbnail' && $a_var['value']->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['value']->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($a_var['thumb_width']) ? ("&maxwidth=" . $a_var['thumb_width']) : ''),
                                    (!empty($a_var['thumb_height']) ? ("&maxheight=" . $a_var['thumb_height']) : '')
                                );
                            } else {
                                $value = $a_var['value']->get('name');
                            }
                        } else {
                            $value = '';
                        }

                        $vars['a_' . $a_var['name']] = $value;
                    } else {
                        $vars['a_' . $a_var['name']] = isset($a_var['value']) ? $a_var['value'] : '';
                    }
                } else {
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                $t_a_vars[$t_lang][$k]['value'] = '';
                            }
                            if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                            && isset($t_a_vars[$t_lang][$k]['options'])) {
                                foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = nl2br($t_a_vars[$t_lang][$k]['value']);
                            } else {
                                $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = $t_a_vars[$t_lang][$k]['value'];
                            }
                        } else {
                            if ($a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                                foreach ($a_var['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                nl2br($a_var['value']);
                            } else {
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                $a_var['value'];
                            }
                        }
                    }
                }

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (!empty($a_var['source'])) {
                    // parse the params
                    $source_fields = General::parseSettings($a_var['source']);

                    // check if there is a var with the required name and replace it
                    if (!empty($source_fields['replace_value'])) {
                        if (!$a_var['multilang']) {
                            $vars['a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars['a_' . $a_var['name']], $source_fields['replace_value']);
                        } else {
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars[$this->get('model_lang') . '_a_' . $a_var['name']], $source_fields['replace_value']);
                        }
                    }
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'config' && isset($a_var['names']) && is_array($a_var['names'])) {
                //add containing variables to the list of replaceable variables
                $a_var['width'] = $a_var['width_print'];

                foreach ($a_var['names'] as $var_name) {
                    $ac_var = $a_var[$var_name];
                    $ac_var['value'] = isset($a_var['values'][$var_name]) ? $a_var['values'][$var_name] : '';

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a configurator (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } elseif ($ac_var['type'] == 'file_upload') {
                            if (!empty($ac_var['value']) && is_object($ac_var['value']) && !$ac_var['value']->get('not_exist') && !$ac_var['value']->get('deleted_by')) {
                                if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $ac_var['value']->isImage()) {
                                    $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                        $_SERVER['SCRIPT_NAME'],
                                        $this->registry['module_param'],
                                        rawurlencode(General::encrypt($ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                        (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                        (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                    );
                                } else {
                                    $value = $ac_var['value']->get('name');
                                }
                            } else {
                                $value = '';
                            }
                            $a_var['values'][$var_name] = $vars['a_'.$ac_var['name']] = $value;
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }
                }

                $configViewer = new Viewer($this->registry);
                if (isset($a_var['frankenstein'])) {
                    $a_var['frankenstein']['columns'] = @$a_var['columns'];
                    $configViewer->data['var'] = $a_var['frankenstein'];
                    $configViewer->data['pattern_id'] = $pattern_id;

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_vars.html');
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_configs.html');
                    $vars['a_'.@$a_var['name'].'_configs'] = $configViewer->fetch();
                } else {
                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'config_vars.html');
                    $configViewer->data['var'] = $a_var;
                    $configViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();
                }
                //ToDo - add multilang config variables

            } elseif (isset($a_var['type']) && $a_var['type'] == 'table' && isset($a_var['names']) && is_array($a_var['names'])) {
                // Skip this table if it's empty
                if (empty($a_var['values']) || count(array_filter($a_var['values'], function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                //add containing variables to the list of replaceable variables
                foreach ($a_var['names'] as $key => $var_name) {
                    $ac_var = $a_var[$var_name];
                    if ($ac_var['type'] == 'file_upload') {
                        if (!empty($a_var['values'][$key]) && is_object($a_var['values'][$key]) && !$a_var['values'][$key]->get('not_exist') && !$a_var['values'][$key]->get('deleted_by')) {
                            if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $a_var['values'][$key]->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['values'][$key]->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                    (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                );
                                $a_var['values'][$key] = $value;
                            } else {
                                $a_var['values'][$key] = $a_var['values'][$key]->get('name');
                                $value = $a_var['values'][$key];
                            }
                        } else {
                            $value = '';
                        }
                    } else {
                        $value = isset($a_var['values'][$key]) ? $a_var['values'][$key] : '';
                    }
                    $ac_var['value'] = $value;
                    $a_var['values'][$key] = $value;

                    $a_var['width'] = $a_var['width_print'];

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a table (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    if (!empty($a_var['source'][$var_name])) {
                        // parse the params
                        $source_fields = General::parseSettings($a_var['source'][$var_name]);

                        // checks if 'replace_value' param is set
                        if (!empty($source_fields['replace_value'])) {
                            // find the replacement variable name
                            $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                            // check if there is a var with the required name and replace it
                            if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                $additional_var_name = '[a_' . $replaced_var . ']';
                                $column_key_idx = array_search($replaced_var, $a_var['names']);
                                foreach ($a_var['values'] as $col_index => $col_value) {
                                    if ($col_index == $column_key_idx) {
                                        $new_value = str_replace($additional_var_name, $col_value, $source_fields['replace_value']);
                                        $a_var['values'][$col_index] = $new_value;
                                        $a_var[$replaced_var]['value'] = $new_value;
                                        if (!$ac_var['multilang']) {
                                            $vars['a_' . $replaced_var] = $new_value;
                                        } else {
                                            $vars[$this->get('model_lang') . '_a_' . $replaced_var] = $new_value;
                                        }

                                        // sets the option to overwrite a value and use it
                                        // but not searching for its corresponding label
                                        if ($ac_var['type'] == 'dropdown' || $ac_var['type'] == 'radio' || $ac_var['type'] == 'checkbox') {
                                            $a_var[$var_name]['overwrite_value'] = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $template_file = 'table_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if (!empty($a_var['multilang'])) {
                    foreach ($translations as $t_lang) {
                        $tableViewer = new Viewer($this->registry);
                        $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $tableViewer->data['pattern_id'] = $pattern_id;
                        $replacement = $tableViewer->fetch();
                        if ($pattern_format == 'docx') {
                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                        }
                        if ($t_lang != $this->get('model_lang')) {
                            $tableViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        } else {
                            $tableViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                } else {
                    $tableViewer = new Viewer($this->registry);
                    $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                    $tableViewer->data['var'] = $a_var;
                    $tableViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $tableViewer->fetch();
                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_'.$a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'grouping') {
                // Skip this grouping table if it has no rows or it has only one row which is empty
                if (empty($a_var['values']) || count($a_var['values']) == 1 && count(array_filter(reset($a_var['values']), function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                $template_file = 'grouping_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        if ($t_lang != $this->get('model_lang')) {
                            $groupingViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                            = $groupingViewer->fetch();
                        } else {
                            $groupingViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        }
                    }
                } else {
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    foreach ($a_var['names'] as $idx_var => $var_name) {
                        $a_var['width'] = $a_var['width_print'];

                        if (!empty($a_var['source'][$var_name])) {
                            // parse the params
                            $source_fields = General::parseSettings($a_var['source'][$var_name]);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                    $column_key_idx = array_search($replaced_var, $a_var['names']);
                                    foreach ($a_var['values'] as $row => $row_content) {
                                        $a_var['values'][$row][$column_key_idx] =
                                            str_replace('[a_' . $replaced_var . ']', $row_content[$column_key_idx], $source_fields['replace_value']);
                                    }

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if (in_array($a_var['types'][$column_key_idx], array('dropdown', 'radio', 'checkbox'))) {
                                        $a_var[$var_name]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }

                    //check if there are empty rows in the table and remove them
                    if (isset($a_var['values']) && is_array($a_var['values'])) {
                        $row_is_empty = true;
                        foreach ($a_var['values'] as $row_index => $row_content) {
                            foreach ($row_content as $cell_index => $cell_content) {
                                if ($cell_content || $cell_content === '0') $row_is_empty = false;
                            }
                            if ($row_is_empty) {
                                unset($a_var['values'][$row_index]);
                            } else {
                                $row_is_empty = true;
                            }
                        }
                    }

                    foreach ($a_var['types'] as $key_column => $var_type) {
                        if ($var_type == 'file_upload') {
                            $group_var_name = $a_var['names'][$key_column];
                            if (!empty($a_var['values'])) {
                                foreach ($a_var['values'] as $row => $row_values) {
                                    if (!empty($row_values[$key_column]) && is_object($row_values[$key_column]) && !$row_values[$key_column]->get('not_exist') && !$row_values[$key_column]->get('deleted_by')) {
                                        $file = $row_values[$key_column];
                                        if (isset($a_var[$group_var_name]['view_mode']) && $a_var[$group_var_name]['view_mode'] == 'thumbnail' && $row_values[$key_column]->isImage()) {
                                            $file_upload_properties = $a_var[$group_var_name];
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                                                (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                                            );
                                            $a_var['values'][$row][$key_column] = $value;
                                        } else {
                                            $a_var['values'][$row][$key_column] = $file->get('name');
                                        }
                                    } else {
                                        $a_var['values'][$row][$key_column] = '';
                                    }
                                }
                            }
                        }
                    }

                    $groupingViewer->data['var'] = $a_var;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $groupingViewer->fetch();

                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_' . $a_var['name']] = $replacement;
                }
            /* ToDo: add GT2 support for projects
            } elseif (isset($a_var['type']) && $a_var['type'] == 'gt2') {
                //get print settings for the 2nd type grouping table
                $print_properties = $this->getGT2PrintSettings($pattern_id);

                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        //prepare additional variables
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');

                        if ($t_lang != $this->get('model_lang')) {
                            $table = $t_a_vars[$t_lang][$k];
                        } else {
                            $table = $a_var;
                        }

                        //prepare files in GT2
                        if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                            foreach ($table['values'] as $ridx => $row) {
                                foreach ($row as $rkey => $rval) {
                                    if (!empty($rval) && is_object($rval)) {
                                        $file = $rval;
                                        if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                            $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                                        } else {
                                            $file = '';
                                        }
                                        $table['values'][$ridx][$rkey] = $file;
                                    }
                                }
                            }
                        }

                        $table_ordered = $table;
                        $table_ordered['vars'] = array();
                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            // style properties
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                            // label for table caption
                            if ($key == 'var_' . $table['id']) {
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered['label'] = $property['labels'][$t_lang];
                                }
                                continue;
                            }
                            foreach ($table['vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    $table_ordered['vars'][$idx] = $var;
                                    // label for field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                        } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                            unset($table_ordered['vars'][$idx]['agregate']);
                                        }
                                    }
                                    continue 2;
                                }
                            }
                            foreach ($table['plain_vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    // label for total field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    continue 2;
                                }
                            }
                        }
                        // calculate aggregates in GT2 table
                        $table_ordered = $this->calculateGT2Agregates($table_ordered);

                        $groupingViewer->data['styles'] = $styles_for_template;
                        $groupingViewer->data['table'] = $table_ordered;
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        $vars[$t_lang . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                    }
                } else {
                    //prepare additional variables
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');

                    $table = $a_var;
                    $table_ordered = $table;
                    $table_ordered['vars'] = array();
                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        // style properties
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                        // label for table caption
                        if ($key == 'var_' . $table['id']) {
                            if (!empty($property['labels'])) {
                                $table_ordered['label'] = reset($property['labels']);
                            }
                            continue;
                        }
                        foreach ($table['vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                $table_ordered['vars'][$idx] = $var;
                                // label for field
                                if (!empty($property['labels'])) {
                                    $table_ordered['vars'][$idx]['label'] = reset($property['labels']);
                                }
                                // aggregates
                                if (isset($property['agregate'])) {
                                    if ($property['agregate'] != 'none') {
                                        $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                    } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                        unset($table_ordered['vars'][$idx]['agregate']);
                                    }
                                }
                                continue 2;
                            }
                        }
                        foreach ($table['plain_vars'] as $idx => $var) {
                            if ($key == 'var_' . $var['id']) {
                                // label for total field
                                if (!empty($property['labels'])) {
                                    $table_ordered['plain_vars'][$idx]['label'] = reset($property['labels']);
                                }
                                continue 2;
                            }
                        }
                    }
                    // calculate aggregates in GT2 table
                    $table_ordered = $this->calculateGT2Agregates($table_ordered);

                    $groupingViewer->data['styles'] = $styles_for_template;
                    $groupingViewer->data['table'] = $table_ordered;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $groupingViewer->fetch();
                }

                //get the plain vars of the GT2
                $plain_values = $a_var['plain_values'];
                foreach ($plain_values as $plain_var => $plain_value) {
                    switch ($plain_var) {
                    case 'total_no_vat_reason_text':
                        if ($a_var['plain_vars'][$plain_var]['multilang']) {
                            foreach ($translations as $t_lang) {
                                $vars[$t_lang . '_a_' . $plain_var] =
                                    $a_var['multilang'] && $t_lang != $this->get('model_lang') ?
                                    $t_a_vars[$t_lang][$k]['plain_values'][$plain_var] :
                                    $plain_value;
                            }
                        } else {
                            $vars['a_' . $plain_var] = $plain_value;
                        }
                        break;
                    case 'total_vat_rate':
                        $vars['a_' . $plain_var] = $plain_value . ' %';
                        break;
                    default:
                        $vars['a_' . $plain_var] = $plain_value;
                    }
                }
            */
            } elseif (isset($a_var['type']) && $a_var['type'] == 'bb') {
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $bbViewer = new Viewer($this->registry);
                        $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');

                        /* ToDo: add GT2 support for projects

                        //get print settings for the 2nd type grouping table
                        $print_properties = $this->getGT2PrintSettings($pattern_id);

                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                        }

                        $bbViewer->data['styles'] = $styles_for_template;

                        $bbViewer->data['project'] = $this;

                        // complete the labels from the printing properties
                        $new_bb_vars = $this->get('bb_vars');
                        foreach ($new_bb_vars as $bb_idx => $bb_details) {
                            // if the variable is GT2
                            if ($bb_details['type'] == 'gt2') {
                                foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    if (array_key_exists($print_properties_key, $print_properties)) {
                                        $property = $print_properties[$print_properties_key];
                                        // label
                                        if (!empty($property['labels'][$t_lang])) {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = $property['labels'][$t_lang];
                                        }
                                        // aggregates
                                        if (isset($property['agregate'])) {
                                            if ($property['agregate'] != 'none') {
                                                $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                            } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                                unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                            }
                                        }
                                    }
                                }
                                foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    // label
                                    if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'][$t_lang])) {
                                        $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = $print_properties[$print_properties_key]['labels'][$t_lang];
                                    }
                                }
                                // calculate aggregates in GT2 table
                                $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                            }
                        }
                        $bbViewer->data['document']->set('bb_vars', $new_bb_vars, true); */

                        $bbViewer->data['pattern_id'] = $pattern_id;

                        if ($t_lang != $this->get('model_lang')) {
                            $bbViewer->data['var'] = @$t_a_vars[$t_lang][$k];
                            $vars[$t_project[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        } else {
                            $bbViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        }
                    }
                } else {
                    $bbViewer = new Viewer($this->registry);
                    $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                    $bbViewer->data['document'] = $this;
                    $bbViewer->data['var'] = $a_var;

                    /* ToDo: add GT2 support for projects

                    //get print settings for the 2nd type grouping table
                    $print_properties = $this->getGT2PrintSettings($pattern_id);

                    // complete the labels from the printing properties
                    $new_bb_vars = $this->get('bb_vars');
                    foreach ($new_bb_vars as $bb_idx => $bb_details) {
                        // if the variable is GT2
                        if ($bb_details['type'] == 'gt2') {
                            foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                if (array_key_exists($print_properties_key, $print_properties)) {
                                    $property = $print_properties[$print_properties_key];
                                    // label
                                    if (!empty($property['labels'])) {
                                        $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = reset($property['labels']);
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                        } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                            unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                        }
                                    }
                                }
                            }
                            foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                // label
                                if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'])) {
                                    $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = reset($print_properties[$print_properties_key]['labels']);
                                }
                            }
                            // calculate aggregates in GT2 table
                            $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                        }
                    }
                    $bbViewer->data['project']->set('bb_vars', $new_bb_vars, true);

                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                    }
                    $bbViewer->data['styles'] = $styles_for_template;
                    */

                    $bbViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $bbViewer->fetch();
                }
            }
        }

        return $vars;
    }

    /**
     * Function to check if all the required related records to the project are completed
     *
     * @return bool - true if check was successful, otherwise false.
     */
    public function checkCompletedRelatedRecords() {
        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];

        $check_result = true;

        // get the current type and its required completed models
        $project_required_finished_models_sql =
            'SELECT pt.requires_completed_documents as documents, pt.requires_completed_tasks as tasks, ' . "\n" .
            '  pt.requires_completed_minitasks as minitasks, pt.requires_completed_events as events ' . "\n" .
            'FROM ' . DB_TABLE_PROJECTS . ' AS p ' . "\n" .
            'JOIN ' . DB_TABLE_PROJECTS_TYPES . ' AS pt ' . "\n" .
            '  ON (pt.id=p.type AND pt.active=1 AND pt.deleted=0) ' . "\n" .
            'WHERE p.id="' . $this->get('id') . '"';
        $project_required_finished_models = $db->GetRow($project_required_finished_models_sql);

        // if not any selected required models to be finished then TRUE is returned
        if (in_array(1, $project_required_finished_models)) {
            foreach ($project_required_finished_models as $required_model => $is_required) {
                if ($check_result && $is_required) {
                    switch ($required_model) {
                        case 'documents':
                            $documents_query = 'SELECT COUNT(id) FROM ' . DB_TABLE_DOCUMENTS . "\n" .
                                               'WHERE project="' . $this->get('id') . '" AND active=1 AND deleted_by=0 AND status!="closed"';
                            if ($db->GetOne($documents_query)) {
                                $this->registry['messages']->setError($this->i18n('error_projects_uncompleted_documents'));
                                $check_result = false;
                            }
                            break;
                        case 'tasks':
                            $tasks_query = 'SELECT COUNT(id) FROM ' . DB_TABLE_TASKS . "\n" .
                                           'WHERE project="' . $this->get('id') . '" AND active=1 AND deleted_by=0 AND status!="finished"';
                            if ($db->GetOne($tasks_query)) {
                                $this->registry['messages']->setError($this->i18n('error_projects_uncompleted_tasks'));
                                $check_result = false;
                            }
                            break;
                        case 'minitasks':
                            $minitasks_query = 'SELECT COUNT(id) FROM ' . DB_TABLE_MINITASKS . "\n" .
                                               'WHERE model="' . $this->modelName . '" AND model_id=' . $this->get('id') . ' AND status="opened"';
                            if ($db->GetOne($minitasks_query)) {
                                $minitasks_link = sprintf('%s?%s=projects&projects=communications&communications=%d&communication_type=minitasks', $_SERVER['PHP_SELF'], $this->registry['module_param'], $this->get('id'));
                                $this->registry['messages']->setError(sprintf('<a href="%s">%s</a>',
                                                                              $minitasks_link,
                                                                              $this->i18n('error_projects_uncompleted_minitasks')));
                                $check_result = false;
                            }
                            break;
                        case 'events':
                            // unfinished events are all events which are not in status finished, unstarted, moved
                            $events_query = 'SELECT COUNT(id) FROM ' . DB_TABLE_EVENTS . "\n" .
                                            'WHERE project="' . $this->get('id') . '" AND active=1 AND deleted_by=0 AND status NOT IN ("finished", "unstarted", "moved")';
                            if ($db->GetOne($events_query)) {
                                $this->registry['messages']->setError($this->i18n('error_projects_uncompleted_events'));
                                $check_result = false;
                            }
                            break;
                    }
                }
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $check_result;
    }

    /**
     * Gets counter for this project
     *
     * @return object - counter for this model
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            if ($sanitize_after = $this->isSanitized()) {
                $this->unsanitize();
            }

            require_once 'projects.counters.factory.php';
            $filters = array('where' => array('pt.id = \'' . $this->get('type') . '\'',
                                              'pc.deleted IS NOT NULL'),
                             'sanitize' => true);
            $this->counter = Projects_Counters::searchOne($this->registry, $filters);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Sets number to model
     *
     * @return int - result of the operation
     */
    public function setNumber() {
        $this->set('added', '', true);

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_PROJECTS . "\n" .
                  'SET num = ?' . "\n" .
                  'WHERE id = ' . $this->get('id');
        $result = $this->registry['db']->Execute($query1, array($this->getNum()));

        return $result;
    }

    /**
     * Check whether model should get a number
     *
     * @return bool - result of the operation
     */
    public function checkGetNum() {
        if (!$this->isActivated() || !$this->get('type') || $this->get('num') ||
        !$this->registry['db']->GetOne('SELECT counter FROM ' . DB_TABLE_PROJECTS_TYPES . ' WHERE id = \'' . $this->get('type') . '\'')) {
            return false;
        }

        if (!$this->get('id')) {
            return true;
        } else {
            return $this->registry['db']->GetOne('SELECT active FROM ' . DB_TABLE_PROJECTS . ' WHERE id = \'' . $this->get('id') . '\'') === '0';
        }
    }

    /**
     * Gets number for model
     *
     * @param bool $force - force getting a number even when model has one
     * @return string - number of model or empty string when type does not use counter
     */
    public function getNum($force = false) {
        if ($this->get('num') && !$force) {
            return $this->get('num');
        }

        if (!$this->isActivated()) {
            return '';
        }

        //get the counter assigned to the type of financial document
        $this->getCounter();

        if (!$this->counter) {
            return '';
        }

        //create extender to expand the formula components
        $extender = new Extender;

        //set project number
        if ($this->counter->get('customer_num') && $this->get('customer')) {
            //get number of project for project customer
            $query = 'SELECT COUNT(p.id) ' . "\n" .
                     '  FROM ' . DB_TABLE_PROJECTS_TYPES . ' AS pt' . "\n" .
                     '  JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                     '    ON p.type=pt.id AND pt.active=1 AND pt.deleted=0 AND pt.counter=' . $this->counter->get('id') . "\n" .
                     '      AND p.customer="' . $this->get('customer') . '" AND p.num > 0' . ($this->counter->get('customer_year') ? ' AND YEAR(p.added)=YEAR(CURDATE())' : '') . "\n";
            $extender->add('customer_num', sprintf('%0' . $this->counter->get('leading_zeroes') . 'd', ($this->registry['db']->GetOne($query)+1)));
        } else {
            //get next number from db and lock the counter for update to guarantee unique next number
            $extender->add('num', sprintf('%0' . $this->counter->get('leading_zeroes') . 'd', $this->counter->getNextNumber()));
        }

        // increment the counter
        $this->counter->increment();

        if ($this->counter->get('prefix_used')) {
            $extender->add('prefix', $this->counter->get('prefix'));
        }

        if ($this->counter->get('customer_code') && $this->get('customer')) {
            $query = 'SELECT code FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id=' . $this->get('customer');
            $extender->add('customer_code', $this->registry['db']->GetOne($query));
        }

        if ($this->counter->get('code_used')) {
            $extender->add('code', $this->get('code'));
        }

        if ($this->counter->get('user_code')) {
            $extender->add('user_code', $this->registry['currentUser']->get('code'));
        }

        if ($this->counter->get('parent_num') && $this->get('parent_project')) {
            $query = 'SELECT num FROM ' . DB_TABLE_PROJECTS . ' WHERE id=' . $this->get('parent_project');
            $extender->add('parent_num', $this->registry['db']->GetOne($query));
        }

        if ($this->counter->get('added_used')) {
            $extender->add('added', General::strftime($this->counter->get('date_format'),
                ($this->get('added') ? strtotime($this->get('added')) : '')));
        }

        $num = $extender->expand($this->counter->get('formula'));

        $delimiter = $this->counter->get('delimiter');
        if ($delimiter) {
            //remove repeating delimiters
            $num = preg_replace('#' . preg_quote($delimiter . $delimiter) . '#', $delimiter, $num);
            $num = preg_replace('#' . preg_quote($delimiter) . '$#', '', $num);
            $num = preg_replace('#^' . preg_quote($delimiter) . '#', '', $num);
        }

        if ($this->slashesEscaped) {
            $num = General::slashesEscape($num);
        }

        $this->set('num', $num, true);

        return $this->get('num');
    }

    function sendNotification($template, $email, $user_name, $skip_message_set = false) {
        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //send email
        $mailer = new Mailer($this->registry, $template, $this);
        $mailer->placeholder->add('project_id', $this->get('id'));
        $mailer->placeholder->add('project_name', $this->get('name'));
        $mailer->placeholder->add('project_code', $this->get('code'));
        $mailer->placeholder->add('project_num', $this->get('num'));
        $mailer->placeholder->add('project_type', $this->get('type_name'));
        $mailer->placeholder->add('project_added_by', $this->get('added_by_name'));
        $mailer->placeholder->add('project_manager', $this->get('manager_name'));
        $mailer->placeholder->add('customer_name', $this->get('customer_name'));
        $mailer->placeholder->add('user_name', $user_name);

        $project_view_url = sprintf('%s/index.php?%s=projects&projects=view&view=%d',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], $this->get('id'));
        $add_comment_url = sprintf('%s/index.php?%s=projects&projects=communications&communications=%d&communication_type=comments#comments_add_form',
                                   $this->registry['config']->getParam('crontab', 'base_host'),
                                   $this->registry['module_param'], $this->get('id'));
        $project_phases_url = sprintf('%s/index.php?%s=projects&projects=phases&phases=%d',
                                      $this->registry['config']->getParam('crontab', 'base_host'),
                                      $this->registry['module_param'], $this->get('id'));

        $mailer->placeholder->add('project_finished_phase', $this->get('current_stage_name'));
        $mailer->placeholder->add('project_started_phase', $this->get('next_stage_name'));
        $mailer->placeholder->add('project_modified_by', $this->registry['currentUser']->get('firstname') . ' ' . $this->registry['currentUser']->get('lastname'));

        $mailer->placeholder->add('project_view_url', $project_view_url);
        $mailer->placeholder->add('project_phases_url', $project_phases_url);
        $mailer->placeholder->add('project_add_comment_url', $add_comment_url);
        $mailer->placeholder->add('to_email', $email);

        $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));
        $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));

        $mailer->template['model_name'] = $this->modelName;
        $mailer->template['model_id'] = $this->get('id');

        $result = $mailer->send();

        $notify_for = $this->i18n('projects_' . $template . '_notify', array($this->getModelTypeName()));
        if (!@in_array($email, $result['erred'])) {
            if (!$skip_message_set) {
                if ($this->registry['sent_email'] != 1) {
                    $this->registry['messages']->setMessage($this->i18n('message_projects_email_sent_success', array($notify_for)));
                    $this->registry->set('sent_email', 1, true);
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
            return true;
        } else {
            if (!$skip_message_set) {
                if ($this->registry['err_sent_email'] != 1) {
                    $this->registry['messages']->setWarning($this->i18n('error_projects_send_email', array($notify_for)), '', 10);
                    $this->registry->set('err_sent_email', 1, true);
                }
                $this->registry['messages']->insertInSession($this->registry);
            }
            return false;
        }
    }
}

?>
