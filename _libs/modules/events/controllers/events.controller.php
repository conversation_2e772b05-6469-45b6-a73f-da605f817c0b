<?php

use Nzoom\Mvc\ControllerTrait\GridBasedListTrait;

require_once PH_MODULES_DIR . 'events/models/events.history.php';
require_once PH_MODULES_DIR . 'events/models/events.audit.php';

class Events_Controller extends Controller {
    use GridBasedListTrait;
    /**
     * Model name of this controller
     */
    public $modelName = 'Event';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Events';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'adds', 'add', 'multiadd', 'export',
        'view', 'edit',
        'create', 'documents', 'projects', 'tasks', 'events', 'attachments',
        'setstatus', 'assign', 'remind', 'timesheets',
        'print', 'printlist', 'manage_outlooks',
        'relatives', 'history', 'comments', 'emails', 'communications'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'history', 'relatives', 'communications'
    );

    /**
     * Action definitions for the upper right menu
     */
    public $actionDefinitionsUpRight = array(
        'print', 'manage_outlooks', 'printlist'
    );

    public $actionListPageMenu = [
        /** Actions that serve navigational purpouse and have no direct relation on the current page */
        'general' => ['list', 'adds', 'add', 'multiadd', 'create'],

        /** Actions that are conextualy dependent on the page (document opened) */
        'context' => ['view', 'edit', 'assign', 'attachments', 'communications', 'relatives', 'history' ],

        /** Actions that should be quick to access but unabtrusive to the user */
        'quick' => ['print', 'remind'],

        /** Actions that are not very frequent to use, can be hidden behind a menu */
        'infriquent' => ['timesheets', 'manage_outlooks', 'printlist'],
    ];

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'view', 'edit', 'translate',
        'attachments', 'assign', 'remind', 'calendars'
    );

    /**
     * Basic variables for transformations
     */
    public $transformBasicDefinitions = array(
        'name', 'customer', 'branch', 'contact_person', 'trademark', 'project', 'group'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_count_reminder',
        'ajax_stop_reminder',
        'ajax_check_availability',
    );

    /**
     * Required basic variables for transformations
     */
    public $transformRequiredDefinitions = array('name', 'customer');

    /**
     * Copy basic variables from source model for transformations
     */
    public $transformFromSourceDefinitions = array(
        'customer', 'branch', 'contact_person', 'trademark', 'project'
    );

    /**
     * Copy basic variables from default values of model type for transformations
     */
    public $transformFromDefaultDefinitions = array('name', 'group');

    /**
     * Copy basic variables from current user for transformations
     */
    public $transformFromUserDefinitions = array();

    /**
     * Actions where side panels for model can be displayed
     */
    public static $actionsSidePanel = array('view', 'edit');

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        $this->checkReferer();
        switch ($this->action) {
        case 'translate':
            $this->_translate();
            break;
        case 'adds':
            $action = $this->registry['request']->get('operation');
            if (preg_match('#^(add|multiadd)$#', $action)) {
                $this->setAction($action);
            } else {
                $this->setAction('view');
            }
            $method = '_' . $action;
            $this->$method();
            break;
        case 'add':
            $this->_add();
            break;
        case 'multiadd':
            $this->_multiAdd();
            break;
        case 'multiedit':
            $this->_multiEdit();
            break;
        case 'edit':
            $this->_edit();
            break;
        case 'transformations':
            //takes the transforming action
            $action = $this->registry['request']->get('operation');
            if (empty($action)) {
                $action = 'transform';
            }
            //checks if the action is valid
            if ($action == 'clone') {
                $this->setAction($action);
            } elseif ($action != 'transform') {
                $this->setAction('view');
            }
            //sets the model id to the current action
            $this->registry['request']->set($action, $this->registry['request']->get('transformations'), true);
            //construct the name of the method
            $method = '_' . $action;
            //calls the method
            $this->$method();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assignments_configurator':
            $this->_assignments_configurator();
            break;
        case 'remind':
            $this->_remind();
            break;
        case 'ajax_stop_reminder':
            $this->_stopReminder();
            break;
        case 'ajax_count_reminder':
            $this->_countReminder();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'ajax_get_totals':
            $this->_getTotals();
            break;
        case 'view':
            $this->_view();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'calendars':
            $this->redirectToCalendar();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'search':
            $this->_search();
            break;
        case 'create':
            $this->_create();
            break;
        case 'notification':
            $this->_notification();
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'history':
            $this->_history();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'ajax_reply_invitation':
            $this->_replyInvitation();
            break;
        case 'timesheets':
            $this->_addTimesheet();
            break;
        case 'ajax_check_availability':
            $this->_checkAvailability();
            break;
        case 'ajax_load_assignments':
            $this->_loadAssignments();
            break;
        case 'ajax_show_customers_info':
            $this->_showCustomersInfo();
            break;
        case 'ajax_show_last_records':
            $this->_showLastRecords();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'finish':
            $this->_finish();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'print':
            $this->_print();
            break;
        case 'multiprint':
            $this->_multiPrint();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'ajax_sidepanel':
            $this->_sidePanel();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'getadvancedsearchoptions':
            $this->_getAdvancedSearchOptions();
            break;
        case 'getListColumnsDefinitions':
            $this->_getListColumnsDefinitions();
            break;
        case 'listIds':
            $this->_listIds();
            break;
        case 'getListTitle':
            $this->_getListTitle();
            break;
        case 'listData':
            $this->_listData();
            break;
        case 'getListMultiActionsPanel':
            $this->_getListMultiActionsPanel('', 'multistatus,multiprint');
            break;
        case 'getListActions':
            $this->_getListActions();
            break;
        case 'saveFilter':
            $this->_saveFilter();
            break;
        case 'loadFilter':
            $this->_loadFilter();
            break;
        case 'deleteFilter':
            $this->_deleteFilter();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _listData() {
        /** @var \Request $request */
        $request = $this->registry['request'];

        $accept = $request->getHeader('Accept');
        if (!preg_match("/.*json/i", $accept)
            || false !== stripos($accept, "html")) {
            return;
        }

        // The rights are cheked based on the action name. Documents::prepareRightsFilters() is called in the model
        $this->registry->set('action', 'list', true);

        $filters = $this->prepFiltersFromRequest();
        $this->registry->set('getTags', true, true);

        $outlook = $this->getCurrentOutlook($filters);

        $shouldCheckPermissions= [
            'view' => true,
            'edit' => true,
            'setStatus' => true,
        ];

        if (isset($outlook) && $outlook) {
            $modelFields = $outlook->get('current_custom_fields');
            $modelFieldsNames = array_column($modelFields, 'name');
            $filters['get_fields'] = $modelFieldsNames;

            if (!in_array('status', $modelFieldsNames)) {
                $shouldCheckPermissions['setStatus'] = false;
            }
        }

        list($records, $pagination) = $this->modelFactoryName::pagedSearch($this->registry, $filters);

        if (isset($outlook) && $outlook) {
            $additionalVars = $outlook->getModelAdditionalFields();
            $basicVars = $outlook->getModelFields();
            if ($additionalVars) {
                $outlook->clearNotPermittedVars();
            }

            /** @var Model $record */
            foreach ($records as $record) {
                $this->prepListRecordFileuploadAttributes($record, $additionalVars);

                if (array_key_exists('participants',$basicVars)) {
                    $record->unsanitize();
                    $record->getAssignments();
                    $record->set('participants', $record->get('users_participants'), true);
                }

                $record->sanitize();
                $record->properties['cached_assoc_vars'] = null;

                unset($record->properties['cached_assoc_vars']);
            }
        }



        if (in_array(true, $shouldCheckPermissions, true)) {
            foreach ($records as $record) {
                foreach ($shouldCheckPermissions as $action=>$test) {
                    switch ($action) {
                        default:
                            $record->checkPermissions($action);
                    }
                }

                // View and edit permision for events can be controlled from the record itself.
                // So calling the checkPermissions method ensures that the logic is executed.
                $record->rights['edit'] = $record->checkPermissions('edit');
                $record->rights['view'] = $record->checkPermissions('view');
            }
        }


        $data = [
            'pagination' => $pagination,
            'records' =>  $records,
        ];

        header('Content-Type: application/json');
        $this->actionCompleted = true;
        $this->registry->set('ajax_result', json_encode($data), true);
    }

    /**
     * search of models
     */
    private function _search() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Add new model
     */
    private function _add() {
        $request = &$this->registry['request'];

        if ($request->get('type') == PH_REMINDER_EVENT_TYPE) {
            $this->redirect($this->module, 'list');
        }

        require_once $this->modelsDir . 'events.types.factory.php';

        // get selected type from request, if not present, get from personal settings
        $type_id =
            $request->get('type') ?:
            $this->registry['currentUser']->getPersonalSettings('events', 'default_event_type');

        $type = $type_permission = false;

        //check validity of the type
        if (!empty($type_id)) {
            $filters = array(
                'where' => array('et.id = \'' . $type_id . '\'',
                                 'et.keyword NOT IN (\'reminder\', \'plannedtime\')',
                                 'et.active = 1'),
                'sanitize' => true);
            $type = Events_Types::searchOne($this->registry, $filters);
            $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'add') : false;
        } else {
            // select first available type
            $filters = array(
                'where' => array('et.keyword NOT IN (\'reminder\', \'plannedtime\')',
                                 'et.active = 1'),
                'sort' => array('eti18n.name ASC'),
                'sanitize' => true);
            $event_types = Events_Types::search($this->registry, $filters);
            foreach ($event_types as $type) {
                $type_permission = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
                if ($type_permission) {
                    $type_id = $type->get('id');
                    break;
                }
            }
            unset($event_types);
        }

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('event');
            $this->registry['messages']->setError($this->i18n('error_events_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        }

        //do not save if sent from change type
        $sent_from_change_type = $request->get('sent_from_change_type');

        //check if details are submitted via POST
        if (empty($sent_from_change_type) && $request->isPost()) {
            //build the model from the POST
            $event = Events::buildModel($this->registry);

            if ($event->save()) {
                $filters = array('where' => array('e.id = ' . $event->get('id')),
                                 'model_lang' => $event->get('model_lang'));
                $new_event = Events::searchOne($this->registry, $filters);
                $this->old_model = new Event($this->registry);
                $this->old_model->sanitize();

                $audit_parent = Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'add', 'new_model' => $new_event, 'old_model' => $this->old_model));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_events_add_success', array($new_event->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;

                if ($event->get('create_from_customer') && ($event->get('customer') == $event->get('create_from_customer'))) {
                    // write history for the customer
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                    $filters_customer = array('where' => array('c.id = ' . $event->get('customer')),
                                              'model_lang' => $request->get('model_lang'));
                    $customer = Customers::searchOne($this->registry, $filters_customer);

                    $empty_customer_model = Customers::buildModel($this->registry);
                    $empty_customer_model->sanitize();

                    $customer->set('created_event_name', $new_event->get('name'), true);

                    Customers_History::saveData($this->registry, array('model'        => $customer,
                                                                       'new_model'    => $customer,
                                                                       'old_model'    => $empty_customer_model,
                                                                       'action_type'  => 'create_event',
                                                                       'event_type'   => $new_event->get('type_name')));
                }

                if ($event->get('create_from_document')) {
                    if ($new_event->updateDocumentRelatives(array($event->get('create_from_document')))) {
                        $this->actionCompleted = true;
                        $related_model = clone $new_event;
                        Events_History::saveData($this->registry, array('model' => $related_model, 'action_type' => 'relatives'));
                        unset($related_model);
                    }
                }
            } else {
                $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('event');
                $event->set('type_name', $type_name, true);
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_events_add_failed', array($event->getModelTypeName())), '', -1);
            }

        } else {
            $event = Events::buildModel($this->registry);
            if (!$sent_from_change_type) {
                $event->set('priority', 'medium', true);
                $event->set('availability', 'busy', true);
                $event->set('visibility', 'public', true);
            }
            $event->set('type', $type_id, true);

            //set default values from the event type
            $event->set('group', $type->getDefaultGroup(), true);

            if ($event->get('customer') && $event->get('customer_create')) {
                // if customer is set in the GET string then the event has been created from customer
                $event->set('create_from_customer', $event->get('customer'), true);
            }
            if ($event->get('document') && $event->get('document_create')) {
                // if document is set in the GET string, then the event has been created from document
                $event->set('create_from_document', $event->get('document'), true);
            }
            //set default value of the project
            if (!$event->get('project') && $this->registry['currentUser']->get('default_project')) {
                $event->set('project', $this->registry['currentUser']->get('default_project'), true);
            }

            //set default after action
            $this->afterAction = 'assign';
        }

        if (!empty($event)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        }

        return true;
    }

    /**
     * Edits existing model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);


        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $event = Events::buildModel($this->registry);
            $filters = array('where' => array('e.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_event = Events::searchOne($this->registry, $filters);

            if ($event->save()) {
                $this->registry->set('getAssignments', true, true);
                $filters = array('where' => array('e.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_event = Events::searchOne($this->registry, $filters);

                $audit_parent = Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'edit', 'new_model' => $new_event, 'old_model' => $old_event));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_events_edit_success', array($new_event->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;

                //send notifications if changed some important parameters
                if ($old_event->get('event_start') != $new_event->get('event_start')
                        || $old_event->get('duration') != $new_event->get('duration')
                        || $old_event->get('location') != $new_event->get('location')
                        || ($old_event->get('description') != $new_event->get('description') && !$request->get('dont_notify'))) {
                    $new_event->sendOnEdit($audit_parent);
                }

            } else {
                if ($old_event->get('type') == $event->get('type')) {
                    $event->set('type_name', $old_event->get('type_name'), true);
                } else {
                    require_once($this->modelsDir . 'events.types.factory.php');
                    $filters = array('where' => array('et.id = "' . $event->get('type') . '"'),
                                     'sanitize' => true);
                    $type = Events_Types::searchOne($this->registry, $filters);
                    if ($type) {
                        $event->set('type_name', $type->get('name'), true);
                    }
                }
                foreach (array('access', 'ownership', 'status', 'substatus') as $prop) {
                    $event->set($prop, $old_event->get($prop), true);
                }
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_events_edit_failed', array($event->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            //set flag in registry, so that the assignments are derived from the DB
            $this->registry->set('getAssignments', true, true);

            //get the model from the DB
            $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
            $event = Events::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($event);
            if ($request->get('parent_event_id')) {
                $event->set('parent_event_id', $event->get('id'), true);
                $event->set('event_start',
                    substr_replace($event->get('event_start'), $request->get('new_recurrence_date'), 0, 10),
                    true);
                $event->set('event_end',
                    date('Y-m-d H:i:s', strtotime($event->get('event_start')) + $event->get('duration')*60),
                    true);
                $event->set('id', null, true);
            }
        }

        if (!empty($event)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to calendar
            $this->redirectToCalendar();
        }

        return true;
    }

    /**
     * assign event
     */
    protected function _assign() {
        $request = &$this->registry['request'];

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        //get the requested model ID
        $id = ($request->isPost()?$request->get('id'):$request->get($this->action));
        $filters = array('where' => array('e.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);

        if (empty($event)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to list
            $this->redirect($this->module, 'list');
        }

        $this->checkAccessOwnership($event);

        if ($request->isPost()) {
            //assigned customers
            $new_customer_ids = $request->get('customer_assign');
            //assigned users
            $new_user_ids = $request->get('user_assign');
            //other users - view or edit
            $new_user_access = $request->get('access');
            $new_users = array();
            $flag_edit = false;
            //combine assigned users - participants and observers
            if (is_array($new_user_ids)) {
                foreach ($new_user_ids as $u_id) {
                    $new_users[$u_id] = array('participant_id' => $u_id, 'ownership' => 'mine', 'access' => 'edit');
                }
            }
            if (is_array($new_user_access)) {
                foreach ($new_user_access as $u_id => $access) {
                    if ($access == 'edit') {
                        $flag_edit = true;
                    }
                    if (isset($new_users[$u_id])) {
                        $new_users[$u_id]['access'] = $access;
                    } else {
                        $new_users[$u_id] = array('participant_id' => $u_id, 'ownership' => 'other', 'access' => $access);
                    }
                }
            }
            $event->set('new_customer_ids', $new_customer_ids, true);
            $event->set('new_users', $new_users, true);

            if ($flag_edit) {
                $filters = array('where' => array('e.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $old_event = Events::searchOne($this->registry, $filters);

                if ($event->assign()) {
                    $this->registry['messages']->setMessage($this->i18n('message_events_assign_success'), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);
                    $this->actionCompleted = true;

                    $filters = array('where' => array('e.id = ' . $request->get('id')),
                                     'model_lang' => $request->get('model_lang'));
                    $new_event = Events::searchOne($this->registry, $filters);

                    Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'assign', 'new_model' => $new_event, 'old_model' => $old_event));
                } else {
                    $this->registry['messages']->setError($this->i18n('error_assignments'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_no_user_edit_event'));
            }
        }

        if (!$request->isPost() || !$flag_edit) {
            $assignments = $event->get('users_assignments');
            $customers_participants = $event->get('customers_participants');

            if (!$request->isPost() && !$event->get('first_assign')) {
                //if the assignments are made for the first time, a check about defaults is made
                $filters_search = array();
                $filters_search['type'] = $event->get('type');

                //takes the default assignments
                $default_assignments = $this->registry->get('currentUser')->getDefaultAssignments('events', $filters_search);

                //includes the needed factories
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                foreach ($default_assignments as $def_assign) {
                    //the array with info for the assigned person
                    $default_assign_info = array();

                    //checks the type of the default assignment
                    if ($def_assign['assignee_type'] == 'user') {
                        //if the user is not already in the list, a new array with the info for him is created
                        if (! array_key_exists($def_assign['assignee'], $assignments)) {
                            $default_assign_info['parent_id'] = $event->get('id');
                            $default_assign_info['participant_type'] = $def_assign['assignee_type'];
                            $default_assign_info['participant_id'] = $def_assign['assignee'];


                            if ($def_assign['assignment_type'] == 'participant') {
                                $default_assign_info['ownership'] = 'mine';
                            } else {
                                $default_assign_info['ownership'] = 'other';
                            }
                            $default_assign_info['access'] = 'view';
                            $default_assign_info['user_status'] = 'pending';
                            $default_assign_info['status_date'] = '0000-00-00 00:00:00';
                            $default_assign_info['last_invitation_date'] = '0000-00-00 00:00:00';
                            $default_assign_info['invitations_count'] = 0;
                            $default_assign_info['default'] = true;

                            //gets the name of the user
                            $user_filter = array('where'        => array('u.id="' . $def_assign['assignee'] . '"'),
                                                 'model_lang'   => $request->get('model_lang'),
                                                 'sanitize'     => true
                            );
                            $current_default_assigned_user = Users::searchOne($this->registry, $user_filter);
                            if ($current_default_assigned_user) {
                                //sets the name of user
                                $default_assign_info['assigned_to_name'] = $current_default_assigned_user->get('firstname') . ' ' . $current_default_assigned_user->get('lastname');
                            }

                            //sets the info into the assignments array
                            $assignments[$def_assign['assignee']] = $default_assign_info;
                        } else {
                            // sets the user as participant
                            if (isset($assignments[$def_assign['assignee']]['default']) && $assignments[$def_assign['assignee']]['default'] && $def_assign['assignment_type'] == 'participant') {
                                $assignments[$def_assign['assignee']]['ownership'] = 'mine';
                            }
                        }
                    } else if ($def_assign['assignee_type'] == 'customer') {
                        if (! array_key_exists($def_assign['assignee'], $customers_participants)) {
                            //if the customer is not already in the list, a new array with the info for him is created
                            $default_assign_info['parent_id'] = $event->get('id');
                            $default_assign_info['participant_type'] = $def_assign['assignee_type'];
                            $default_assign_info['participant_id'] = $def_assign['assignee'];
                            $default_assign_info['ownership'] = 'mine';
                            $default_assign_info['access'] = 'view';
                            $default_assign_info['user_status'] = 'pending';
                            $default_assign_info['status_date'] = '0000-00-00 00:00:00';
                            $default_assign_info['last_invitation_date'] = '0000-00-00 00:00:00';
                            $default_assign_info['invitations_count'] = 0;
                            $default_assign_info['default'] = true;

                            //gets the name of the user
                            $customer_filter = array('where'        => array('c.id="' . $def_assign['assignee'] . '"',
                                                                             "((c.subtype='normal' AND c.is_company=0) OR c.subtype='contact')"),
                                                     'model_lang'   => $request->get('model_lang'),
                                                     'sanitize'     => true
                            );
                            $current_default_assigned_customer = Customers::searchOne($this->registry, $customer_filter);

                            if ($current_default_assigned_customer) {
                                //sets the name of user
                                $default_assign_info['assigned_to_name'] = $current_default_assigned_customer->get('name') . ' ' . $current_default_assigned_customer->get('lastname');
                            }

                            //sets the info into the assignments array
                            $customers_participants[$def_assign['assignee']] = $default_assign_info;
                        }
                    }
                }
            }

            $participants = array();
            $users_edit = array();
            $users_view = array();
            foreach ($assignments as $assignment) {
                if ($assignment['ownership'] == 'mine') {
                    $participants[] = $assignment;
                }
                if ($assignment['access'] == 'edit') {
                    $users_edit[] = $assignment;
                } else {
                    $users_view[] = $assignment;
                }

            }
            $event->set('users_participants', $participants, true);
            $event->set('customers_participants', $customers_participants, true);
            $event->set('users_edit', $users_edit, true);
            $event->set('users_view', $users_view, true);
        }

        if (!empty($event)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to list
            $this->redirect($this->module, 'list');

        }

        return true;
    }

    /**
     * Add or delete events assignments configurator
     */
    private function _assignments_configurator() {

        $request = &$this->registry['request'];

        $assignment_type = $request->get('assignment_type');
        $config_action = $request->get('config_action');
        $config_id = $request->get('config_id');

        $errors = false;
        if ($config_id) {
            require_once PH_MODULES_DIR . 'events/models/events.assignments_configurators.factory.php';

            if ($config_action == 'save') {
                $configurator = Events_Assignments_Configurators::buildModel($this->registry, '');
                if (!$configurator->save()) {
                    $errors = true;
                }
            } elseif ($config_action == 'delete') {
                if (!Events_Assignments_Configurators::purge($this->registry, $config_id)) {
                    $this->registry['messages']->setError($this->i18n('error_events_assignments_configurator_delete'));
                    $errors = true;
                }
            }
        }

        $result = array();
        if ($errors) {
            $result['errors'] = $this->registry['messages']->getErrors();
        } else {
            // prepare saved events assignments configurations
            require_once PH_MODULES_DIR . 'events/models/events.assignments_configurators.factory.php';
            $config_templates = Events_Assignments_Configurators::search($this->registry,
                array('where' => array('eac.added_by = \'' . $this->registry['currentUser']->get('id') . '\'', 'eac.model = "Event"')));

            $viewer = new Viewer($this->registry);
            $viewer->data['assignment_type'] = $assignment_type;
            $viewer->data['config_templates'] = $config_templates;
            $viewer->data['exclude_div'] = 1;
            $viewer->setFrameset(PH_MODULES_DIR . 'events/templates/_assignments_configurator_panel.html');
            $result['data'] = $viewer->fetch();
        }

        print 'var result = ' . json_encode($result) . ';';
        exit;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $event = Events::buildModel($this->registry);

            $filters = array('where' => array('e.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $old_event = Events::searchOne($this->registry, $filters);

            if ($event->validate('translate')) {
                $event->slashesEscape();
                if ($event->updateI18N()) {
                    $this->actionCompleted = true;
                } else {
                    $event->slashesStrip();
                }
            }
            if ($this->actionCompleted) {
                $filters = array('where' => array('e.id = ' . $id),
                                 'model_lang' => $request->get('model_lang'));
                $new_event = Events::searchOne($this->registry, $filters);

                $audit_parent = Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'translate', 'new_model' => $new_event, 'old_model' => $old_event));

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_events_translate_success', array($old_event->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_events_translate_failed', array($old_event->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
            $event = Events::searchOne($this->registry, $filters);

            //check access and ownership of the model
            $this->checkAccessOwnership($event);
        }

        if (!empty($event)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to calendar
            $this->redirectToCalendar();
        }

        return true;
    }

    /**
     * Shows model in view mode
     */
    private function _view() {
        $request = &$this->registry['request'];

        //id of the model
        $id = $request->get($this->action);

        $event = false;
        if ($id > 0) {
            //set flag in registry, so that the assignments are derived from the DB
            $this->registry->set('getAssignments', true, true);

            $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
            $event = Events::searchOne($this->registry, $filters);
        }

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //the model was not found redirect to list
            $this->redirect($this->module, 'list');

        }

        return true;
    }

    /**
     * status of models
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        //get referer's action
        preg_match('/&events=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $request->set('id', $id, 'all', true);
        $event = Events::buildModel($this->registry);
        $filters = array('where' => array('e.id = ' . $request->get('id')),
                         'model_lang' => $request->get('model_lang'));
        $old_event = Events::searchOne($this->registry, $filters);
        $this->old_model = clone $old_event;
        $this->old_model->sanitize();

        if ($event->setStatus()) {
            //show message 'message_events_status_success'
            $this->registry['messages']->setMessage($this->i18n('message_events_status_success', array($old_event->getModelTypeName())), '', -2);

            $filters = array('where' => array('e.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $new_event = Events::searchOne($this->registry, $filters);
            $this->model = clone $new_event;
            $this->model->sanitize();

            $audit_parent = Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'status', 'new_model' => $new_event, 'old_model' => $old_event));

            //send notification
            //$this->sendStatusNotification($new_event, $audit_parent);

            //set after action to view if have not permission
            if (!$new_event->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_events_status_failed', array($old_event->getModelTypeName())), '', -1);
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            //set parameters in registry - check them in router
            //set redirect url
            $this->registry->set('redirect_to_url', $_SERVER['HTTP_REFERER'], true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
        }

        return true;
    }

    /**
     * History of the event
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                'e.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $event = Events::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($event && $this->checkAccessOwnership($event, false)) {
                if (!$this->registry->isRegistered('event')) {
                    $this->registry->set('event', $event->sanitize());
                }

                require_once $this->viewersDir . 'events.history.viewer.php';
                $viewer = new Events_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('event')) {
                $this->registry->set('event', $event->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Replies to invitation via events module
     * The invitation could be confirmed or denied
     * This method is used via ajax
     */
    private function _replyInvitation() {
        $request = &$this->registry['request'];

        $event_id = $request->get('event_id');
        $status = $request->get('status');

        if (!$event_id || ($status != 'confirmed' && $status != 'denied' && $status != 'not_sure')) {
            //the request data is inaccurate
            exit;
        }

        $user_status = array($event_id => $status);

        //update invitations status
        require_once PH_MODULES_DIR . 'events/models/events.factory.php';
        Events::updateInvitations($this->registry, $user_status);

        $this->registry->set('getAssignments', true, true);

        $filters = array('where' => array('e.id = ' . $event_id),
                         'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);

        $event->prepareParticipationNotification('user', $this->registry['currentUser']->get('id'));

        if ($status == 'confirmed') {
            $current_image_name = 'message';
            $opposite_status_1 = 'not_sure';
            $opposite_status_image_name_1 = 'not_sure';
            $opposite_action_name_1 = $this->i18n('events_status_not_sure');
            $opposite_status_2 = 'denied';
            $opposite_status_image_name_2 = 'error';
            $opposite_action_name_2 = $this->i18n('events_status_deny');
        } elseif ($status == 'not_sure') {
            $current_image_name = 'not_sure';
            $opposite_status_1 = 'confirmed';
            $opposite_status_image_name_1 = 'message';
            $opposite_action_name_1 = $this->i18n('events_status_confirm');
            $opposite_status_2 = 'denied';
            $opposite_status_image_name_2 = 'error';
            $opposite_action_name_2 = $this->i18n('events_status_deny');
        } else {
            $current_image_name = 'error';
            $opposite_status_1 = 'not_sure';
            $opposite_status_image_name_1 = 'not_sure';
            $opposite_action_name_1 = $this->i18n('events_status_not_sure');
            $opposite_status_2 = 'confirmed';
            $opposite_status_image_name_2 = 'message';
            $opposite_action_name_2 = $this->i18n('events_status_confirm');
        }

        //This method is used via ajax
        $response = array(
            'event_id'                  => $event_id,
            'status'                    => $status,
            'status_name'               => $this->i18n('events_participant_status_' . $status),
            'status_date'               => General::strftime($this->i18n('date_mid')),
            'image_src'                 => sprintf('%s%s.png', $this->registry['theme']->imagesUrl, $current_image_name),
            'opposite_status_1'         => $opposite_status_1,
            'opposite_status_image_src_1' => sprintf('%s%s.png', $this->registry['theme']->imagesUrl, $opposite_status_image_name_1),
            'opposite_action_name_1'    => $opposite_action_name_1,
            'opposite_status_2'         => $opposite_status_2,
            'opposite_status_image_src_2' => sprintf('%s%s.png', $this->registry['theme']->imagesUrl, $opposite_status_image_name_2),
            'opposite_action_name_2'    => $opposite_action_name_2
        );

        print json_encode($response);
        exit;
    }

    /**
     * communication concerning the event (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('e.id = ' . $id), 'status' => '');
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $event = Events::searchOne($this->registry, $filters);

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            if ($request->get('new_recurrence_date')) {
                $event->set('event_date', $request->get('new_recurrence_date'), true);
            }

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('event')) {
                $this->registry->set('event', $event->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
            // include current module scripts
            $this->viewer->customLibs[0]['js'] = $this->viewer->getFilesList($this->viewer->scriptsDir, 'js');
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Add timesheet for selected record (document, project, task)
     */
    private function _addTimesheet() {

        $request = &$this->registry['request'];
        $event_id = $request->get($this->action);
        $filters = array('where' => array('e.id = ' . $event_id), 'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);

        if ($event && $event->get('type_keyword') == 'plannedtime') {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            if (!$this->registry->isRegistered('event')) {
                $this->registry->set('event', $event->sanitize());
            }
            return;
        }

        require_once PH_MODULES_DIR . 'tasks/models/tasks.factory.php';

        //get model id (document, project or task)
        $id = 0;
        if ($request->get('resource') == 'document') {
            list($model_id) = $request->get('referers');
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            $id = Documents::getSystemTask($this->registry, $model_id);
        } elseif ($request->get('resource') == 'project') {
            list($model_id) = $request->get('referers');
            require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
            $id = Projects::getSystemTask($this->registry, $model_id);
        } elseif ($request->get('resource') == 'task') {
            list($model_id) = $request->get('referers');
            $id = $model_id;
        }
        if ($id) {
            $filters = array('where' => array('t.id = ' . $id, 't.type IS NOT NULL'));
            if ($model_lang = $this->registry['request']->get('model_lang')) {
                $filters['model_lang'] = $model_lang;
            }
            //get system task model
            $task = Tasks::searchOne($this->registry, $filters);
            //set timesheet data
            $url_params = array();
            $url_params[] = 'subject=' . $event->get('name');
            $url_params[] = 'period_type=dates';
            $url_params[] = 'startperiod=' . $event->get('event_start');
            $url_params[] = 'endperiod=' . date('Y-m-d H:i:00', strtotime($event->get('event_start')) + 60*$event->get('duration'));
            // get activity according to event type
            $activities = Dropdown::getTasksTimesheetsActivities(array($this->registry));
            $activity = PH_TIMESHEET_EVENT_TYPE;
            foreach ($activities as $a) {
                if ($a['class_name'] && $a['class_name'] = $event->get('type')) {
                    $activity = $a['option_value'];
                    break;
                }
            }
            $url_params[] = 'activity=' . $activity;
            $url_params[] = 'event_id=' . $event_id;
            $add_query_string = implode('&', $url_params);

            //redirect to addtimesheet
            $module = General::singular2plural($request->get('resource'));
            $redirect_url = sprintf('%s://%s%sindex.php?%s=%s&%s=timesheets&timesheets=%d&%s#add_timesheet',
                            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                            $_SERVER["HTTP_HOST"], PH_BASE_URL, $this->registry['module_param'],
                            $module, $module,
                            $model_id, $add_query_string);
            header('Location: ' . $redirect_url);
            exit;
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect('events', 'view', 'view=' . $event_id);
        }

        return true;
    }

    /**
     * Relatives of the event
     */
    private function _relatives() {
        $request = &$this->registry['request'];

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('e.id = ' . $id));
        if ($model_lang = $request->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $event = Events::searchOne($this->registry, $filters);

        if (!empty($event)) {

            //check access and ownership of the model
            $this->checkAccessOwnership($event);
            if ($request->isPost()) {
                if ($event->updateDocumentRelatives($request->get('documents_referers'))) {
                    $this->registry['messages']->setMessage($this->i18n('message_relatives_success'), '', -2);
                    $this->registry['messages']->insertInSession($this->registry);

                    $model = clone $event;
                    Events_History::saveData($this->registry, array('model' => $model, 'action_type' => 'relatives'));

                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_relatives_failed'), '', -1);
                }
            }
            // get document relatives with neutral relation (i.e. manually connected)
            $event->getRelatives(array('get_document_referers' => true));
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('event')) {
                $this->registry->set('event', $event->sanitize());
            }

            //get the relatives tree
            $this->registry->set('relatives_tree', $event->getRelativesTree());
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete events
        $result = Events::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage($this->i18n('message_items_deleted'));

            if (!is_array($ids)) {
                $ids = array($ids);
            }
            foreach ($ids as $id) {
                $event = new Event($this->registry);
                $event->set('id', $id, true);
                Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'delete'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError($this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'calendars')) {
            header('Location: ' . $_SERVER['HTTP_REFERER']);
            exit;
        }

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore events
        $result = Events::restore($this->registry, $ids);

        if ($result) {
            //restore successful
            $this->registry['messages']->setMessage($this->i18n('message_items_restored'));

            if (!is_array($ids)) {
                $ids = array($ids);
            }
            foreach ($ids as $id) {
                $event = new Event($this->registry);
                $event->set('id', $id, true);
                Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'restore'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError($this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Remind
     */
    private function _remind() {
        $request = &$this->registry['request'];

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        //get the requested model ID
        if ($request->get('id')) {
            $id = $request->get('id');
        } else {
            $id = $request->get($this->action);
        }

        $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);

        //check if details are submitted via POST
        if ($request->isPost()) {

            if ($event->remind()) {
                //show corresponding message
                if ($request->get('reminder_exists')) {
                    $this->registry['messages']->setMessage($this->i18n('message_events_reminder_edit_success'), '', -1);
                } else {
                    $this->registry['messages']->setMessage($this->i18n('message_events_reminder_add_success'), '', -1);
                }
                $this->registry['messages']->insertInSession($this->registry);
                $this->actionCompleted = true;
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_events_reminder_edit_failed'), '', -1);
                $reminder = array(
                        'type' => $request->get('reminder_type'),
                        'offset' => $request->get('reminder_offset'),
                        'offset_from' => $request->get('reminder_offset_from'),
                        'repeats' => $request->get('reminder_repeats'),
                        'repeat_interval' => $request->get('reminder_repeat_interval'),
                        );
                $event->set('reminder', $reminder, true);
            }

        } elseif ($id > 0 && !empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);
            if ($this->registry['request']->get('new_recurrence_date')) {
                $new_recurrence_date = $this->registry['request']->get('new_recurrence_date');
            } else {
                $new_recurrence_date = '';
            }
            $reminder = $event->getReminder($this->registry['currentUser']->get('id'), $new_recurrence_date);
            $event->set('reminder', $reminder, true);
        }

        if (!empty($event)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('event', $event->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to calendar
            $this->redirectToCalendar();
        }

        return true;
    }

    /**
     * stop reminder
     */
    private function _stopReminder() {

        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);
        $event->set('event_date', $request->get('event_date'), true);
        $event->set('user_md5', md5($this->registry['currentUser']->get('id')), true);
        $event->set('reminder_source', $request->get('reminder_source'), true);

        if ($event->stopReminder()) {

        } else {

        }

        exit;
    }

    /**
     * count reminder
     */
    private function _countReminder() {

        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');

        //set flag in registry, so that the assignments are derived from the DB
        $this->registry->set('getAssignments', true, true);

        $filters = array('where' => array('e.id = ' . $id), 'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);
        $event->set('event_date', $request->get('event_date'), true);
        $event->set('remind_after', $request->get('remind_after'), true);
        $event->set('reminder_source', $request->get('reminder_source'), true);

        if ($event->countReminder()) {

        } else {

        }

        exit;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Events::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            if (!is_array($ids)) {
                $ids = array($ids);
            }
            foreach ($ids as $id) {
                $event = new Event($this->registry);
                $event->set('id', $id, true);
                Events_History::saveData($this->registry, array('model' => $event, 'action_type' => $this->action));
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Redirect to specified module at ADD section and auto complete data
     */
    private function _create() {
        $request = &$this->registry['request'];

        $event_id = $request->get('create_from_event_id');
        $filters = array('where' => array('e.id = ' . $event_id));
        $event = Events::searchOne($this->registry, $filters);
        $customer_id = 0;
        $project_id = 0;
        if ($event) {
            $customer_id = $event->get('customer');
            $project_id = $event->get('project');
        }

        $module = $request->get('operation');
        switch ($module) {
            case 'documents':
                if ($request->get('document_type') > 0) {
                    $add_link = sprintf('operation=add&type=%d&event_id=%d',
                                        $request->get('document_type'), $event_id);
                    if ($customer_id) {
                        $add_link .= sprintf('&customer=%d', $customer_id);
                    }
                    if ($project_id) {
                        $add_link .= sprintf('&project=%d', $project_id);
                    }
                    $this->redirect($module, 'add', $add_link);
                } else {
                    $add_link = sprintf('transformations=%d&transform=%d',
                                        $event_id, -1*$request->get('document_type'));
                    $this->redirect('events', 'transformations', $add_link);
                }
                break;
            case 'tasks':
                $add_link = sprintf('operation=add&type=%d&event_id=%d',
                                    $request->get('task_type'), $event_id);
                if ($customer_id) {
                    $add_link .= sprintf('&customer=%d', $customer_id);
                }
                if ($project_id) {
                    $add_link .= sprintf('&project=%d', $project_id);
                }
                $this->redirect($module, 'add', $add_link);
                break;
            case 'events':
                $add_link = sprintf('operation=add&type=%d&event_id=%d',
                                    $request->get('event_type'), $event_id);
                if ($customer_id) {
                    $add_link .= sprintf('&customer=%d', $customer_id);
                }
                if ($project_id) {
                    $add_link .= sprintf('&project=%d', $project_id);
                }
                $this->redirect($module, 'add', $add_link);
                break;
            case 'projects':
                $add_link = sprintf('operation=add&type=%d&event_id=%d',
                                    $request->get('project_type'), $event_id);
                if ($customer_id) {
                    $add_link .= sprintf('&customer=%d', $customer_id);
                }
                $this->redirect($module, 'add', $add_link);
                break;
        }
        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Events::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * check for calendar referer
     */
    public function checkReferer() {
        if (isset($_SERVER['HTTP_REFERER']) && strpos($_SERVER['HTTP_REFERER'], 'calendars')) {
            //$redirect_url = str_replace('&', '&amp;', $_SERVER['HTTP_REFERER']);
            //$this->registry['session']->set('redirect_url', $redirect_url, '', true);
            $this->registry['session']->set('redirect_url', $_SERVER['HTTP_REFERER'], '', true);
        }
    }

    /**
     * Redirect to calendar
     */
    public function redirectToCalendar() {
        if ($this->registry['session']->get('redirect_url')) {
            //$redirect_url = str_replace('&amp;', '&', $this->registry['session']->get('redirect_url'));
            $redirect_url = $this->registry['session']->get('redirect_url');
            header('Location: ' . $redirect_url);
            exit;
        } else {
            $this->redirect('calendars');
        }
    }

    /**
     * Sets custom actions definitions
     */
    public function getActions($action_defs = array()) {
        $this->getModel();
        $actions = parent::getActions($action_defs);

        //prepare add options types
        require_once($this->modelsDir . 'events.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sanitize' => true,
                         'where' => array('et.keyword NOT IN (\'reminder\', \'plannedtime\')',
                                          'et.active = 1'),
                         'sort' => array('eti18n.name ASC'));
        $eventTypes = Events_Types::search($this->registry, $filters);

        $theme = $this->registry['theme'];
        $ThemeCanProvideIcons = is_callable([$theme, 'getIconForAction']);

        //set calendar action if user has permissions to calendar module
        if ($this->checkAccessModule(false, 'calendars', '_access_', 'calendars')) {
            if ($this->registry['session']->get('redirect_url')) {
                $url = $this->registry['session']->get('redirect_url');
            } else {
                $url = sprintf('%s?%s=%s',
                               $_SERVER['PHP_SELF'],
                               $this->registry['module_param'], 'calendars');
            }
            $actions['calendars'] = array(
                'module_param'     => $this->registry['module_param'],
                'module'           => 'calendars',
                'name'             => 'calendar',
                'label'            => $this->i18n('calendars'),
                'url'              => $url,
                'icon'             => $ThemeCanProvideIcons ? $theme->getIconForRecord('calendars') : '',
            );
        }

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                $actions['list']['options']['previous_list']['img'] = 'list';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['previous_list']['icon'] = $theme->getIconForAction('list');;
                }
                $actions['list']['options']['previous_list']['label'] = $this->i18n('previous_list');
                $actions['list']['options']['previous_list']['url'] = $actions['list']['url'];

                if (isset($actions['search'])) {
                    $actions['list']['options']['previous_search']['img'] = 'search';
                    if ($ThemeCanProvideIcons) {
                        $actions['list']['options']['previous_search']['icon'] = $theme->getIconForAction('search');
                    }
                    $actions['list']['options']['previous_search']['label'] = $this->i18n('previous_search');
                    $actions['list']['options']['previous_search']['url'] = $actions['search']['url'];
                }

                $actions['list']['options']['all_events']['img'] = 'events';
                if ($ThemeCanProvideIcons) {
                    $actions['list']['options']['all_events']['icon'] = $theme->getIconForRecord('events');
                }
                $actions['list']['options']['all_events']['label'] = $this->i18n('events_all_events');
                $actions['list']['options']['all_events']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';

                $actions['list']['url'] = $actions['list']['url'] . sprintf('&amp;type=%d&amp;type_section=', $this->model->get('type'));
            } else {
                $actions['list']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';
            }
        }

        //check edit action
        if ($this->model && $this->model->get('id') && isset($actions['edit']) && ($this->model->get('access') != 'edit' || $this->model->get('status') == 'finished')) {
            unset($actions['edit']);
        }

        //check assign action
        if ($this->model && $this->model->get('id') && isset($actions['assign']) && ($this->model->get('access') != 'edit' || !$this->model->checkPermissions('assign'))) {
            unset($actions['assign']);
        }

        $_optgroups_add = array();
        $_optgroups_multiadd = array();

        $types_to_add = array();
        $types_to_multiadd = array();

        foreach ($eventTypes as $type) {
            $type_permission = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
            $type_permission_multi = $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd');

            // Define OPTGROUP parameter
            if ($type_permission) {
                $_optgroups_add[] = array(
                    'label' => $type->get('name'),
                    'option_value' => $type->get('id'));
                $types_to_add[] = $type->get('id');
            }
            if ($type_permission_multi) {
                // Define OPTGROUP parameter
                $_optgroups_multiadd[] = array(
                    'label' => $type->get('name'),
                    'option_value' => $type->get('id'));
                $types_to_multiadd[] = $type->get('id');
            }
        }

        //check add action
        if (isset($actions['add']) && !empty($_optgroups_add)) {
            //prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'type_',
                    'name' => 'type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('events_type'),
                    'help' => $this->i18n('events_add_legend'),
                    'options' => $_optgroups_add,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') :
                                $this->registry['currentUser']->getPersonalSettings('events', 'default_event_type')));
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        //check multiadd action
        if (isset($actions['multiadd']) && !empty($_optgroups_multiadd)) {
            //prepare multiadd options
            $multiadd_options = array (
                array (
                    'custom_id' => 'type__',
                    'name' => 'type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('events_type'),
                    'help' => $this->i18n('events_add_legend'),
                    'options' => $_optgroups_multiadd,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : ''),
            );
            $actions['multiadd']['options'] = $multiadd_options;
        } else {
            unset($actions['multiadd']);
        }

        //adds action
        if (isset($actions['adds'])) {
            $actions['adds']['label'] = $this->i18n('add');
            $actions['adds']['ajax_no'] = 1;
            $actions['adds']['template'] = PH_MODULES_DIR . 'events/templates/' . '_action_add.html';
            if (isset($actions['add'])) {
                $actions['adds']['options']['add'] = $actions['add'];
                unset ($actions['add']);
            }
            if (isset($actions['multiadd'])) {
                $actions['adds']['options']['multiadd'] = $actions['multiadd'];
                unset ($actions['multiadd']);
            }
            if (empty($actions['adds']['options'])) {
                unset ($actions['adds']);
            } else {
                // check if there is only one selected type and redirects the add link to it
                $selected_action = '';
                $selected_type = '';
                if (isset($actions['adds']['options']['add']) && !isset($actions['adds']['options']['multiadd'])) {
                    // if only ADD action is available
                    if ($this->registry['request']->get('type') && in_array($this->registry['request']->get('type'), $types_to_add)) {
                        $selected_action = 'add';
                        $selected_type = $this->registry['request']->get('type');
                    }
                } else if (!isset($actions['adds']['options']['add']) && isset($actions['adds']['options']['multiadd'])) {
                    // if only MULTIADD action is available
                    if ($this->registry['request']->get('type') && in_array($this->registry['request']->get('type'), $types_to_multiadd)) {
                        $selected_action = 'multiadd';
                        $selected_type = $this->registry['request']->get('type');
                    }
                }

                // change the link if necessary
                if ($selected_action && $selected_type) {
                    $actions['adds']['url'] .= '&amp;operation=' . $selected_action . '&amp;type=' . $selected_type;
                    $actions['adds']['options'] = '';
                    unset($actions['adds']['ajax_no']);
                    unset($actions['adds']['template']);
                }
            }
        }

        // start of actions for access-only events
        if ($this->model && $this->model->get('id') && $this->model->get('access') != '') {

            // reminder action
            if (isset($actions['remind']) && in_array($this->model->get('status'), array('planning', 'progress'))) {

            } else {
                unset($actions['remind']);
            }

            // timesheets action
            if (isset($actions['timesheets'])) {
                if ($this->model->get('type_keyword') == 'plannedtime') {
                    // action is allowed only if: event is not finished, date is
                    // not in the future,  current user is participant;
                    // link points to adding a timesheet to parent task
                    $currentUserId = $this->registry['currentUser']->get('id');
                    if (array_filter($this->model->get('users_participants'),
                                 function($a) use ($currentUserId) { return $a['participant_id'] == $currentUserId; })) {

                        if (in_array($this->model->get('status'), array('planning', 'progress')) &&
                        $this->model->get('event_start_date') <= date('Y-m-d')) {
                            // search for parent task and check if timesheet can be added to it
                            $parent_task = $this->model->getPlannedTimeTask();
                            if ($parent_task && $parent_task->checkPermissions('addtimesheet', 'tasks')) {
                                // prepare timesheet data
                                $timesheet_data = sprintf(
                                    'user_id=%d&event_id=%d&period_type=%s&startperiod=%s&endperiod=%s&duration=%d&event_start_date=%s&event_name=%s',
                                    $currentUserId,
                                    $this->model->get('id'),
                                    ($this->model->get('allday_event') ? 'period' : 'dates'),
                                    $this->model->get('event_start'),
                                    $this->model->get('event_end'),
                                    $this->model->get('duration'),
                                    $this->model->get('event_start_date'),
                                    // prepare event display name in case it is not available as a dropdown option
                                    urlencode(sprintf('%s (%s)',
                                        General::strftime($this->i18n('date_short')),
                                        ($this->model->get('allday_event') ?
                                        $this->model->get('duration') . ' ' . $this->i18n('minute' . (abs($this->model->get('duration')) != 1 ? 's' : '')) :
                                        $this->model->get('event_start_time') . ' - ' . $this->model->get('event_end_time'))
                                    ))
                                );
                                $addtimesheet_options = array(
                                    'label' => $this->i18n('events_addtimesheet_btn'),
                                    'img'   => 'addtimesheet',
                                    'url'   => sprintf('%s://%s%sindex.php?%s=tasks&tasks=timesheets&timesheets=%d&%s#add_timesheet',
                                            (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] == 'on') ? 'https' : 'http',
                                            $_SERVER["HTTP_HOST"], PH_BASE_URL,
                                            $this->registry['module_param'], $parent_task->get('id'),
                                            $timesheet_data)
                                );
                                if ($this->model->get('status') == 'planning') {
                                    $actions['timesheets'] = $addtimesheet_options + $actions['timesheets'];
                                } else {
                                    $actions['timesheets']['ajax_no'] = 1;
                                    $actions['timesheets']['drop_menu'] = true;
                                    $actions['timesheets']['options']['addtimesheet'] = $addtimesheet_options;
                                }
                            } else {
                                unset($actions['timesheets']);
                            }

                            unset($parent_task);
                        } elseif ($this->model->get('status') == 'finished') {
                            // display timesheets
                        } else {
                            unset($actions['timesheets']);
                        }
                    } elseif ($this->model->get('status') == 'planning') {
                        // no timesheets yet
                        unset($actions['timesheets']);
                    }
                } else {
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.types.factory.php';
                    $filters = array('where' => array ('tt.active = 1',
                                                       'tt.system IS NOT NULL'),
                                     'sanitize' => true);
                    $tasks_types_ids = Tasks_Types::getIds($this->registry, $filters);

                    // check if user is allowed to add timesheets for at least one type of task
                    $addtimesheet = false;
                    foreach ($tasks_types_ids as $tid) {
                        if ($this->checkActionPermissions('tasks' . $tid, 'addtimesheet')) {
                            $addtimesheet = true;
                            break;
                        }
                    }

                    if ($addtimesheet) {
                        $actions['timesheets']['options'] = array('label' => $this->i18n('events_addtimesheet_btn'));
                        $actions['timesheets']['ajax_no'] = 1;
                        $actions['timesheets']['template'] = PH_MODULES_DIR . 'events/templates/_action_addtimesheet.html';
                    } else {
                        unset($actions['timesheets']);
                    }
                }
            }

            //check status action
            if (isset($actions['setstatus'])) {
                $actions['setstatus']['options'] = array('label' => $this->i18n('events_status_btn'), 'form_method' => 'post');
                $actions['setstatus']['ajax_no'] = 1;
                $actions['setstatus']['template'] = PH_MODULES_DIR . 'events/templates/_action_status.html';
                $actions['setstatus']['model_id'] = $this->model->get('id');
            }

            if (isset($actions['create'])) {
                $actions['create']['label'] = $this->i18n('create');
                $actions['create']['ajax_no'] = 1;
                $actions['create']['template'] = PH_MODULES_DIR . 'events/templates/_action_create.html';

                if (isset($actions['documents']) && $this->checkActionPermissions('documents', 'add')) {

                    $direction_labels[PH_DOCUMENTS_INCOMING] = $this->i18n('events_documents_incoming');
                    $direction_labels[PH_DOCUMENTS_OUTGOING] = $this->i18n('events_documents_outgoing');
                    $direction_labels[PH_DOCUMENTS_INTERNAL] = $this->i18n('events_documents_internal');

                    require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                    $filters = array('where' => array('dt.active = 1',
                                                      'dt.inheritance = 0'),
                                     'model_lang' => $this->getModelLang(),
                                     'sanitize' => true,
                                     'sort' => array('dti18n.name ASC'));
                    $types_all = Documents_Types::search($this->registry, $filters);

                    $_options = array();
                    foreach ($types_all as $type) {
                        if ($this->checkActionPermissions('documents' . $type->get('id'), 'add')) {
                            $_options[$direction_labels[$type->get('direction')]][] = array(
                                'label' => $type->get('name'),
                                'option_value' => $type->get('id'));
                        }
                    }

                    require_once PH_MODULES_DIR . 'transformations/models/transformations.factory.php';
                    $filters = array('where' => array('t.source_model = \'' . $this->modelName . '\'',
                                                      't.source_type = ' . $this->model->get('type')),
                                     'model_lang' => $this->getModelLang(),
                                     'sanitize' => true,
                                     'sort' => array('t.position ASC'));
                    $transformations = Transformations::search($this->registry, $filters);

                    $_transform_optgroups = array();
                    foreach ($transformations as $transformation) {
                        $destinations = explode(',', $transformation->get('destination_type'));
                        $show = true;
                        foreach ($destinations as $destination) {
                            if (!$this->checkActionPermissions(strtolower(General::singular2plural($transformation->get('destination_model'))) . $destination, 'add')) {
                                $show = false;
                                break;
                            }

                            //do not allow transformation to model of disabled or deleted type
                            $destination_model = $transformation->get('destination_model');
                            $type = null;
                            if ($destination_model == 'Document') {
                                require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                                $filters = array('where' => array('dt.id = ' . $destination,
                                                                  'dt.active = 1'),
                                                 'sanitize' => true);
                                $type = Documents_Types::searchOne($this->registry, $filters);
                            }
                            if (empty($type)) {
                                $show = false;
                                break;
                            }
                        }
                        if ($show) {
                            $_transform_optgroups[$this->i18n('events_transformations')][] = array(
                                'label' => $transformation->get('name'),
                                'option_value' => -1*$transformation->get('id'));
                        }
                    }

                    $_options = array_merge($_options, $_transform_optgroups);

                    if (!empty($_options)) {
                        $first_key = array_keys($_options);
                        $first_key = reset($first_key);
                        //prepare documents options
                        $actions['documents']['options'] = array (
                            array (
                                'custom_id'     => 'document_type__',
                                'name'          => 'document_type',
                                'type'          => 'dropdown',
                                'required'      => 1,
                                'label'         => $this->i18n('events_document_type'),
                                'help'          => $this->i18n('events_document_type'),
                                'optgroups'     => $_options,
                                'value'         => $this->registry['request']->get('document_type') ?: $_options[$first_key][0]['option_value']
                            )
                        );
                        $actions['documents']['label'] = $this->i18n('events_document');
                    }
                }

                if (!empty($actions['documents']['options'])) {
                    $actions['create']['options']['documents'] = $actions['documents'];
                }
                unset ($actions['documents']);

                if (isset($actions['tasks']) && $this->checkActionPermissions('tasks', 'add')) {
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.types.factory.php';
                    $filters = array('where' => array('tt.active = 1'),
                                     'model_lang' => $this->getModelLang(),
                                     'sanitize' => true,
                                     'sort' => array('tti18n.name ASC'));
                    $types_all = Tasks_Types::search($this->registry, $filters);

                    $_options = array();
                    foreach ($types_all as $type) {
                        if ($this->checkActionPermissions('tasks' . $type->get('id'), 'add')) {
                            $_options[] = array(
                                'label' => $type->get('name'),
                                'option_value' => $type->get('id'));
                        }
                    }

                    if (!empty($_options)) {
                        //prepare task options
                        $actions['tasks']['options'] = array (
                            array (
                                'custom_id'     => 'task_type__',
                                'name'          => 'task_type',
                                'type'          => 'dropdown',
                                'required'      => 1,
                                'label'         => $this->i18n('events_type'),
                                'help'          => $this->i18n('events_type'),
                                'options'       => $_options,
                                'value'         => $this->registry['request']->get('task_type') ?: $_options[0]['option_value']
                            )
                        );
                        $actions['tasks']['label'] = $this->i18n('events_task');
                    }
                }

                if (!empty($actions['tasks']['options'])) {
                    $actions['create']['options']['tasks'] = $actions['tasks'];
                }
                unset ($actions['tasks']);

                if (isset($actions['events']) && $this->checkActionPermissions('events', 'add')) {
                    if (!empty($_optgroups_add)) {
                        //prepare event options
                        $actions['events']['options'] = array (
                            array (
                                'custom_id'     => 'event_type__',
                                'name'          => 'event_type',
                                'type'          => 'dropdown',
                                'required'      => 1,
                                'label'         => $this->i18n('events_type'),
                                'help'          => $this->i18n('events_type'),
                                'options'       => $_optgroups_add,
                                'value'         => $this->registry['request']->get('event_type') ?: $_optgroups_add[0]['option_value']
                            )
                        );
                        $actions['events']['label'] = $this->i18n('event');
                    }
                }

                if (!empty($actions['events']['options'])) {
                    $actions['create']['options']['events'] = $actions['events'];
                }
                unset ($actions['events']);

                if (isset($actions['projects']) && $this->checkActionPermissions('projects', 'add')) {
                    require_once PH_MODULES_DIR . 'projects/models/projects.types.factory.php';
                    $filters = array('where' => array('pt.active = 1'),
                                     'model_lang' => $this->getModelLang(),
                                     'sanitize' => true,
                                     'sort' => array('pti18n.name ASC'));
                    $types_all = Projects_Types::search($this->registry, $filters);

                    $_options = array();
                    foreach ($types_all as $type) {
                        if ($this->checkActionPermissions('projects' . $type->get('id'), 'add')) {
                            $_options[] = array(
                                'label' => $type->get('name'),
                                'option_value' => $type->get('id'));
                        }
                    }

                    if (!empty($_options)) {
                        //prepare project options
                        $actions['projects']['options'] = array (
                            array (
                                'custom_id'     => 'project_type__',
                                'name'          => 'project_type',
                                'type'          => 'dropdown',
                                'required'      => 1,
                                'label'         => $this->i18n('events_type'),
                                'help'          => $this->i18n('events_type'),
                                'options'       => $_options,
                                'value'         => $this->registry['request']->get('project_type') ?: $_options[0]['option_value']
                            )
                        );
                        $actions['projects']['label'] = $this->i18n('events_project');
                    }
                }

                if (!empty($actions['projects']['options'])) {
                    $actions['create']['options']['projects'] = $actions['projects'];
                }
                unset ($actions['projects']);

                if (!$actions['create']['options']) {
                    unset ($actions['create']);
                } else {
                    // GET EVENT's ID
                    $actions['create']['event'] = $this->model->get('id');
                }
            } else {
                unset ($actions['create']);

                if (isset($actions['documents'])) {
                    unset ($actions['documents']);
                }
                if (isset($actions['tasks'])) {
                    unset($actions['tasks']);
                }
                if (isset($actions['events'])) {
                    unset($actions['events']);
                }
                if (isset($actions['projects'])) {
                    unset ($actions['projects']);
                }
            }

            // TODO should there be or not be communications for recurrent events?
            /*if ($this->model && $this->model->get('recurrence_type') && $this->action != 'comments') {
                unset($actions['comments']);
                unset($actions['emails']);
            }*/
            // communications action
            if (isset($actions['communications']) && $this->model->checkPermissions('communications')) {
                $actions['communications']['ajax_no'] = 1;
                $actions['communications']['drop_menu'] = true;
                $actions['communications']['hide_label'] = true;

                if (isset($actions['emails'])  && $this->model->checkPermissions('emails')
                  || isset($actions['comments']) && $this->model->checkPermissions('comments')) {
                    if (isset($actions['comments']) && $this->model->checkPermissions('comments')) {
                        $actions['communications']['options']['comments']['img'] = 'comments';
                        if ($ThemeCanProvideIcons) {
                            $actions['communications']['options']['comments']['icon'] = $theme->getIconForAction('comments');
                        }
                        $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                        $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    }
                    unset($actions['comments']);
                    if (isset($actions['emails']) && $this->model->checkPermissions('emails')) {
                        $actions['communications']['options']['emails']['img'] = 'email';
                        if ($ThemeCanProvideIcons) {
                            $actions['communications']['options']['emails']['icon'] = $theme->getIconForAction('emails');
                        }
                        $actions['communications']['options']['emails']['label'] = $this->i18n('events_action_email');
                        $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    }
                    unset($actions['emails']);
                } else {
                    unset($actions['communications']);
                }
            } else {
                unset($actions['communications']);
                unset($actions['emails']);
                unset($actions['comments']);
            }

            if (isset($actions['print'])) {
                $patterns_options = array();
                if ($this->model->get('type')) {
                    $filters_type = array(
                        'where' => array(
                            'et.id="' . $this->model->get('type') . '"'
                        ),
                        'model_lang' => $this->getModelLang(),
                        'sanitize' => true
                    );
                    $event_type = Events_Types::searchOne($this->registry, $filters_type);

                    //get the id of the default event type print template
                    $default_pattern_id = 0;
                    if ($event_type && $event_type->get('default_pattern')) {
                        $default_pattern_id = $event_type->get('default_pattern');
                    }

                    //get all generate/print patterns for this type
                    require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
                    $filters_patterns = array(
                        'where' => array(
                            'p.model = \'Event\'',
                            'p.model_type = \'' . $this->model->get('type') . '\'',
                            'p.active = 1',
                            'p.list = 0'
                        ),
                        'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                        'model_lang' => $this->registry['lang'],
                        'sanitize' => true
                    );
                    $patterns = Patterns::search($this->registry, $filters_patterns);

                    $available_patterns = array();
                    foreach ($patterns as $pattern) {
                        $available_patterns[] = $pattern->get('id');
                        $patterns_options[] = array(
                            'id'        => $pattern->get('id'),
                            'label'     => $pattern->get('name'),
                            'img'       => $pattern->getIcon(),
                            'url'       => $actions['print']['url'] . '&amp;pattern=' . $pattern->get('id'),
                            'target'    => '_blank',
                        );
                    }
                }

                if (empty($patterns_options)) {
                    unset($actions['print']);
                } else {
                    if ($default_pattern_id && in_array($default_pattern_id, $available_patterns)) {
                        $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $default_pattern_id;
                    } elseif (count($available_patterns) == 1) {
                        // sets the first pattern in the list as default and assigns a link to the direct print
                        list($first_pattern) = $patterns_options;
                        $actions['print']['url'] = $actions['print']['url'] . '&amp;pattern=' . $first_pattern['id'];
                    } else {
                        $actions['print']['url'] = '#';
                    }
                    $actions['print']['drop_menu'] = true;
                    $actions['print']['no_tab'] = true;
                    $actions['print']['label'] = '';
                    $actions['print']['target'] = '_blank';
                    $actions['print']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'print';

                    //do not set options if the pattern is only one
                    if (count($patterns_options) <= 1) {
                        $patterns_options = array();
                    } else {
                        $actions['print']['img'] .= '_plus';
                    }
                    $actions['print']['options'] = $patterns_options;
                }
            }
        } else {
            unset($actions['assign']);
            unset($actions['setstatus']);
            unset($actions['remind']);
            unset($actions['attachments']);
            unset($actions['timesheets']);
            unset($actions['history']);
            unset($actions['relatives']);
            unset($actions['create']);
            unset($actions['documents']);
            unset($actions['projects']);
            unset($actions['tasks']);
            unset($actions['events']);
            unset($actions['print']);
            unset($actions['communications']);
            unset($actions['emails']);
            unset($actions['comments']);
        }
        // end of actions for access-only events

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {

            $customize = '';
            $found = 0;

            $custom_filters = array();
            if ($this->registry['request']->get('type')) {
                $customize = 'et.id="' . $this->registry['request']->get('type') . '"';
                $found++;
            } else if ($this->registry['request']->get('type_section')) {
                $customize = 'et.type_section="' . $this->registry['request']->get('type_section') . '"';
                $found++;
            } else if ($this->registry['request']->isRequested('search_fields')) {
                $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
                $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
                $custom_filters['values'] = $this->registry['request']->get('values');
            } else if ($this->registry['session']->isRequested($this->action . '_event')) {
                $custom_filters = $this->registry['session']->get($this->action . '_event');
            }

            if (!empty($custom_filters)) {
                // shows if there is a type defined and if so doesn't add the type section filter
                $type_defined = false;
                if (isset($custom_filters['search_fields'])) {
                    foreach ($custom_filters['search_fields'] as $key => $where) {
                        if (preg_match('#e\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            $customize = 'et.id="' . $custom_filters['values'][$key] . '"';
                            if ($type_defined) {
                                $found++;
                            } else {
                                $type_defined = true;
                                $found = 1;
                            }
                        } else if (preg_match('#et.type_section#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                            if (! $type_defined) {
                                $customize = 'et.type_section="' . $custom_filters['values'][$key] . '"';
                                $found++;
                            }
                        }
                    }
                } elseif (isset($custom_filters['hidden_type'])) {
                    $customize = 'et.id = ' . $custom_filters['hidden_type'];
                    $found++;
                } elseif (isset($custom_filters['hidden_type_section'])) {
                    $customize = 'et.type_section = ' . $custom_filters['hidden_type_section'];
                    $found++;
                }
            }

            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^et\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^et\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }
        }

        // check the current action and sets the alternative actions
        // for view and edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } elseif ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit nor view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } elseif (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && !empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['export'])) {
                unset ($actions['export']);
            }
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
            if ($this->model->get('type') == PH_REMINDER_EVENT_TYPE) {
                unset($actions['edit']);
                unset($actions['assign']);
                unset($actions['remind']);
            }
        }
        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);

        $modernizedActions = [
            'list',
//            'add',
//            'adds',
//            'multiadd',
//            'multiedit',
//            'view',
//            'edit',
//            'assign',
//            'relatives',
//            'attachments',
//            'timesheets',
//            'communications',
//            'history',
        ];
        if (!$theme->isModern() || !in_array($this->action, $modernizedActions)) {
            // Not used in Evolution theme, made available for compatibility reasons in some actions
            $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);
        }

        return $actions;
    }

    /**
     * Gets after actions available for the current user
     * These actions are performed after successful process of the previous action
     *
     * @param array $defs - custom action definitions
     * @return array $actions - all available actions with their details
     */
    public function getAfterActions($action_defs = array()) {
        $this->getModel();
        $actions = parent::getAfterActions($action_defs);
        if ($this->model && $this->model->get('id') && isset($actions['edit'])
            && $this->model->get('access') != 'edit') {
            unset($actions['edit']);
        }

        return $actions;
    }

    public function checkAccessOwnership($model, $redirect = true, $action = '') {
        $result = parent::checkAccessOwnership($model);

        if ($result) {
            if (is_object($model) && ($this->action == 'edit' || $this->action == 'translate' || $this->action == 'delete' || $this->action == 'multiedit')
                && ($model->get('access') != 'edit' || $model->get('status') == 'finished')) {
                if ($redirect) {
                    $this->redirect('events', 'view', 'view='. $model->get('id'));
                } else {
                    return false;
                }
            }
            return true;
        }
    }

    /**
     * Check for confirm/deny participation of user/customer
     *
     */
    public function _notification() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');

        $params['id'] = $request->get('id');
        $params['notification'] = $request->get('notification');
        $params['event_date'] = $request->get('event_date');
        $params['p_md5'] = $request->get('p_id');
        $params['p_type'] = $request->get('p_type');

        $results = Events::saveInvitationAnswer($this->registry, $params);

        if ($results['affected_rows'] && $results['sql']) {
            $this->registry->set('getAssignments', true, true);

            $filters = array('where' => array('e.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'),
                             'participant_type' => $params['p_type'],
                             'participant_id' => $results['participant_id']);
            $event = Events::searchOne($this->registry, $filters);

            $event->prepareParticipationNotification($params['p_type'], $params['p_md5']);
        }

        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset('frameset_message.html');
        $this->viewer->setTemplate('message.html');
        $this->viewer->data['results'] = $results;

        $this->viewer->display();

        exit;
    }

    /**
     * Finishes expired event(s) on submission by event participant/observer.
     */
    public function _finish() {
        $request = &$this->registry['request'];

        $ids = explode(',', $request->get('finish'));
        foreach ($ids as $idx => $id) {
            if (is_numeric($id)) {
                $ids[$idx] = intval($id);
            } else {
                unset($ids[$idx]);
            }
        }
        $ids = array_values($ids);

        $params['p_md5'] = $request->get('p_id');

        $results = Events::finishExpiredEvents($this->registry, $ids, $params);

        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset('frameset_message.html');
        $this->viewer->setTemplate('message_finish.html');
        $this->viewer->data['results'] = $results;

        $this->viewer->display();

        exit;
    }

    /**
     * Load data for selected users to be assigned as participants/observers to event
     */
    public function _loadAssignments() {

        $request = &$this->registry['request'];

        $id = $request->get($this->action);
        $assignment_type = $request->get('assignment_type');
        $participant_type = $request->get('participant_type');
        $user_ids = $request->get('user_ids');

        if (!$id || !$user_ids
            || !in_array($assignment_type, array('participants', 'observers'))
            || !in_array($participant_type, array('user', 'customer'))) {
            exit;
        }

        $filters = array('where' => array('e.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $parent_event = Events::searchOne($this->registry, $filters);
        if (!$parent_event) {
            exit;
        }

        $participant_info = array();

        if ($participant_type == 'user') {
            require_once PH_MODULES_DIR . 'users/models/users.factory.php';
            $user_ids = explode(',', $user_ids);

            $users_filters = array('where' => array('u.id IN (\'' . implode('\', \'', $user_ids) . '\')'),
                                   'sort' => array('u.is_portal ASC, CONCAT(ui18n.firstname, \' \', ui18n.lastname) ASC'),
                                   'model_lang' => $request->get('model_lang'),
                                   'sanitize' => true);
            $users = Users::search($this->registry, $users_filters);

            // get event assignments
            $parent_event->getAssignments();
            $users_assignments = $parent_event->get('users_assignments');
            if (!is_array($users_assignments)) {
                $users_assignments = array();
            }

            // get assignments
            $this->registry->set('getAssignments', true, true);

            foreach ($users as $user) {
                $user_id = $user->get('id');
                $user_info = array (
                    'participant_id' => $user_id,
                    'participant_type' => $participant_type,
                    'user_name' => $user->get('firstname') . ' ' . $user->get('lastname'),
                    'is_portal' => $user->get('is_portal'),
                    'ownership' => ($assignment_type == 'participants' ? 'mine' : 'other')
                );
                // get info if user is already assigned to event and has responded etc.
                if (array_key_exists($user_id, $users_assignments)) {
                    foreach (array('access', 'user_status', 'status_date', 'last_invitation_date', 'invitations_count') as $param) {
                        $user_info[$param] = $users_assignments[$user_id][$param];
                    }
                } else {
                    $user_info['access'] = 'view';
                    $user_info['user_status'] = 'pending';
                    $user_info['status_date'] = $user_info['last_invitation_date'] = '0000-00-00 00:00:00';
                    $user_info['invitations_count'] = 0;
                }
                if ($assignment_type == 'participants') {
                    // set property in order to instruct method that user should be treated as a participant even if not yet assigned
                    $parent_event->set('assigned_to_name', $user_info['user_name'], true);
                    $availability = $parent_event->checkAvailability($user_id, $participant_type, 0);

                    $user_info['availability'] = $availability['availability'];

                    $viewer = new Viewer($this->registry);
                    $viewer->setFrameset('frameset_blank.html');
                    $viewer->setTemplate('_participant_status.html');
                    $viewer->data['participant'] = $user_info;

                    $user_info['participant_status'] = $viewer->fetch();
                    $user_info['availability_table'] = $availability['availability_table'];
                }
                $participant_info[] = $user_info;
            }

            $this->registry->remove('getAssignments');
        }

        print json_encode($participant_info);
        exit;
    }

    /**
     * Check participant availability
     *
     */
    public function _checkAvailability() {

        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);
        $participant_id = $request->get('participant_id');
        $participant_type = $request->get('participant_type');
        $show_availability_header = $request->isRequested('show_availability_header') ? $request->get('show_availability_header') : 0;

        // get assignments
        $this->registry->set('getAssignments', true, true);

        if ($id) {
            // get model from db
            $filters = array('where' => array('e.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $parent_event = Events::searchOne($this->registry, $filters);
        } else {
            // build the model from request
            if ($request->get('allday_event') == 1) {
                $request->set('duration', 0, 'all', true);
                $request->set('event_duration_hour', 0, 'all', true);
                $request->set('event_duration_minute', 0, 'all', true);
                $request->set('event_start_time', '00:00', 'all', true);
                $request->set('event_end_time', '23:59', 'all', true);
            } elseif ($request->get('allday_event') == -1) {
                $request->set('event_start_time', '00:00', 'all', true);
                $request->set('event_end_time',
                              date_add(date_create($request->get('event_start_date') . ' 00:00:00'),
                                  new DateInterval(sprintf('PT%dM', $request->get('duration'))))->format('H:i'),
                              'all', true);
            }
            $parent_event = Events::buildModel($this->registry);
        }

        if (!$parent_event || !$participant_id || !in_array($participant_type, array('user', 'customer'))) {
            print '';
            exit;
        }

        $response = $parent_event->checkAvailability($participant_id, $participant_type, $show_availability_header);

        $this->registry->remove('getAssignments');

        print json_encode($response);
        exit;
    }

    /**
     * Get customer's info
     */
    private function _showCustomersInfo() {
        $request = &$this->registry['request'];

        $customerInfoViewer = new Viewer($this->registry);

        $i18n_files[] = PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini';
        $customerInfoViewer->loadCustomI18NFiles($i18n_files);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customer  = array('sanitize'  => true,
                                       'model_lang' => $request->get('model_lang'),
                                       'where' => array('c.id = ' . $customer_id,
                                                        'c.subtype = \'normal\''));
            $customer = Customers::searchOne($this->registry, $filters_customer);

            if ($customer) {
                if ($customer->get('main_branch_id')) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                    $filters_branches = array('sanitize' => true,
                                              'model_lang' => $request->get('model_lang'),
                                              'where' => array('c.id = ' . $customer->get('main_branch_id'),
                                                               'c.subtype = \'branch\''));
                    $customer_main_branch = Customers_Branches::searchOne($this->registry, $filters_branches);
                    $customer->set('branch_address', ($customer_main_branch ? $customer_main_branch->get('address') : ''), true);
                } else {
                    $customer->set('branch_address', '', true);
                }

                $customerInfoViewer->data['customers_info'] = $customer;
            } else {
                $customerInfoViewer->data['hide_side_panel'] = 1;
            }
        } else {
            $customerInfoViewer->data['hide_side_panel'] = 1;
        }

        $customerInfoViewer->setFrameset('_customers_info_side_panel.html');
        $customerInfoViewer->display();
        exit;
    }

    /**
     * Get last five records having same customer and type as current model
     */
    private function _showLastRecords() {
        $request = &$this->registry['request'];

        $lastRecordsViewer = new Viewer($this->registry);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            $filters_records = array('where' => array('e.customer = ' . $customer_id,
                                                      'e.type = \'' . $request->get('model_type') . '\''),
                                     'sort' => array('e.event_start DESC'),
                                     'limit' => 5,
                                     'sanitize'  => true,
                                     'check_module_permissions' => 'events',
                                     'get_fields' => array('name', 'added_by_name'));

            $last_records = Events::search($this->registry, $filters_records);
            // get files count to display "paper clip"
            foreach ($last_records as $rec) {
                $rec->getFilesCount();
            }

            $last_events = array();
            foreach ($last_records as $key => $obj) {
                $last_events[] = $obj->getAll();
            }
            $last_records = $last_events;

            $lastRecordsViewer->data['last_records'] = $last_records;

            //get calendar settings
            require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
            $calendar_settings = Calendars_Calendar::getSettings($this->registry);
            $lastRecordsViewer->data['calendar_settings'] = $calendar_settings;

        } else {
            $lastRecordsViewer->data['hide_side_panel'] = 1;
        }

        $lastRecordsViewer->setFrameset(PH_MODULES_DIR . 'events/templates/_last_records_side_panel.html');
        $lastRecordsViewer->display();
        exit;
    }

    /**
     * status of models
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');
        $filters = array('where' => array('e.id = ' . $request->get('id')),
                         'model_lang' => $request->get('model_lang'), 'sanitize' => true);
        $event = Events::searchOne($this->registry, $filters);

        $setstatus['options'] = array('label' => $this->i18n('events_status_btn'), 'form_method' => 'post');
        $setstatus['model_id'] = $event->get('id');
        $setstatus['module'] = 'events';
        $setstatus['action'] = 'setstatus';
        $setstatus['module_param'] = $this->registry['module_param'];
        $setstatus['show_form'] = 1;
        $this->viewer = new Viewer($this->registry);
        $this->viewer->setFrameset(PH_MODULES_DIR . 'events/templates/_action_status.html');
        $this->viewer->data['event'] = $event;
        $this->viewer->data['available_action'] = $setstatus;
        $this->viewer->data['hide_status_label'] = true;
        $this->viewer->display();
        exit;
    }

    /**
     * change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('e.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $events = Events::search($this->registry, $filters);

        //if no events or checked deleted events
        if (empty($events) || count($ids) != count($events)) {
            $this->registry['messages']->setError($this->i18n('error_no_events_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_event = $events[0];

        $type = ($first_event->get('type'));
        foreach ($events as $event) {
            $type_i = $event->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'events.types.factory.php';
        $filters = array('where' => array('et.id = ' . $type),
                         'sanitize' => true);
        $eventtype = Events_Types::searchOne($this->registry, $filters);
        $type_name_plural = $eventtype && $eventtype->get('name_plural') ? $eventtype->get('name_plural') : $this->i18n('events');

        $result = Events::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_events_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $type_name_plural)), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_events_change_status_not_all', array($type_name_plural)));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_events_multistatus_failed', array($type_name_plural)), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * multiadd models
     */
    private function _multiAdd() {
        $request = &$this->registry['request'];
        require_once $this->modelsDir . 'events.types.factory.php';

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            $filters = array('where' => array('et.id = ' . $type_id,
                                              'et.active = 1',
                                              'et.keyword NOT IN (\'reminder\', \'plannedtime\')'),
                             'sanitize' => true);
            $type = Events_Types::searchOne($this->registry, $filters);
        }

        $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('event');
        $type_name_plural = $type && $type->get('name_plural') ? $type->get('name_plural') : $this->i18n('events');

        $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd') : false;

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $this->registry['messages']->setError($this->i18n('error_events_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        } else {
            //assign the type to the registry
            $this->registry['event_type'] = $type;
        }

        $event = new Event($this->registry, array('type' => $type_id));
        //get main variables
        $vars = $this->getBasicVarsDefs($event);

        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_events_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if (!$request->isPost()) {
            $filters = array();
            $event = Events::buildModel($this->registry, $filters);
        } elseif ($request->get('items') && !$request->get($vars[0]['name'])) {
            //multiadd from customers
            $customer_ids = $request->get('items');
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters = array('model_lang' => $this->registry->get('model_lang'),
                             'sanitize' => true,
                             'where' => array('c.id IN (' . implode(',', $customer_ids) . ')'));
            $customers = Customers::search($this->registry, $filters);
            foreach ($customers as $key => $c_obj) {
                $customer[] = $c_obj->get('id');
                $customer_autocomplete[] = sprintf('[%s] %s', $c_obj->get('code'), $c_obj->get('name'));
                $first_var_request[] = '';
            }
            $request->set('customer', $customer, 'post', true);
            $request->set('customer_autocomplete', $customer_autocomplete, 'post', true);
            $request->set($vars[0]['name'], $first_var_request, 'post', true);
        }

        $this->actionCompleted = Events::multiAdd($this->registry, $vars, $this);
        if ($request->isPost()) {
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_events_multiadd_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list', array('type' => $type->get('id')));
            } else {
                $this->registry['messages']->setError($this->i18n('error_events_multiadd_failed', array($type_name_plural)), '', -1);
                if (!$request->get($vars[1]['name'])) {
                //unset messages if comes from customers module
                    $this->registry['messages']->unset_vars();
                }
            }
        }

        return true;
    }

    /**
     * multiedit models
     */
    private function _multiEdit($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('e.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => false);
        $events = Events::search($this->registry, $filters);

        //if no events or checked deleted events
        if (empty($events) || count($ids) != count($events)) {
            $this->registry['messages']->setError($this->i18n('error_no_events_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_event = $events[0];

        $type = ($first_event->get('type'));
        foreach ($events as $event) {
            $type_i = $event->get('type');
            //different type
            if ($type != $type_i || $type_i == PH_REMINDER_EVENT_TYPE) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'events.types.factory.php';
        if (!empty($type)) {
            $filters = array('where' => array('et.id = ' . $type),
                             'sanitize' => true);
            $event_type = Events_Types::searchOne($this->registry, $filters);
        }
        $this->registry['event_type'] = $event_type;
        $this->registry->set('type_name', $event_type->get('name'));

        //get main variables
        $vars = $this->getBasicVarsDefs($first_event);

        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_events_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';

        //check if details are submitted via checkboxes (items)
        if (!$request->get('multisave')) {
            // the models from the DB
            foreach ($ids as $i => $id) {
                //get values
                foreach ($vars as $k => $var) {
                    if ($var['name'] == 'customer' || $var['name'] == 'project') {
                        $value_name = $var['name'] . '_name';
                        $value_code = $var['name'] . '_code';
                        $vars[$k]['value'] = $events[$i]->get($var['name']);
                        $vars[$k]['value_code'] = $events[$i]->get($value_code);
                        $vars[$k]['value_name'] = $events[$i]->get($value_name);
                    } elseif ($var['name'] == 'trademark') {
                        $vars[$k]['value'] = $events[$i]->get($var['name']);
                        $vars[$k]['value_autocomplete'] = $events[$i]->get($var['name'] . '_name');
                    } else {
                        $vars[$k]['val'] = $events[$i]->get($var['name']);
                    }
                }
                $events[$i]->set('multivars', $vars, true);
                $events[$i]->sanitize();
            }
            $this->registry->set('events', $events);
        } else {

            //save models
            foreach ($vars as $var) {
                if (!$this->registry['request']->isRequested($var['name'])) {
                    $this->registry['request']->set($var['name'], array(), 'post', true);
                }
            }
            $this->actionCompleted = Events::multiEdit($this->registry, $vars, $type, $this);

            $type_name_plural = !empty($event_type) && $event_type->get('name_plural') ? $event_type->get('name_plural') : $this->i18n('events');
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_events_multiedit_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->registry['messages']->setError($this->i18n('error_events_multiedit_failed', array($type_name_plural)), '', -1);
            }
        }

        return true;
    }

    /**
     * Get main variables for multi add/edit - default or from config
     *
     * @param object $model - event model
     * @return array - data for basic vars
     */
    private function getBasicVarsDefs($model = null) {
        $type = $this->registry['event_type'];

        $current_options = array();
        $current_action = $this->action;

        if ($this->registry['config']->getParam('events', $current_action . '_' . $type->get('id'))) {
            $current_options = explode(', ', $this->registry['config']->getParam('events', $current_action . '_' . $type->get('id')));
        } else {
            $current_options = explode(', ', $this->registry['config']->getParam('events', $current_action));
        }

        $layouts = array();
        if ($model) {
            $layouts = $model->getLayoutsDetails();
        }

        //prepare name
        if (in_array('name', $current_options) || $this->action == 'multiadd') {
            $vars['name'] = array(
                'name' => 'name',
                'type' => 'text',
                'label' => $this->i18n('events_name'),
                'val' => $type->get('default_name'),
                'required'  => 1
            );

            if (!empty($layouts['name'])) {
                //get the label from the layout name
                $vars['name']['label'] = $layouts['name']['name'];
                $vars['name']['help'] = $layouts['name']['description'];
                if (!$layouts['name']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['name']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['name']['readonly'] = !$layouts['name']['edit'];
                }
            }
        }

        //prepare event_start
        if (in_array('event_start', $current_options)) {
            $vars['event_start'] = array(
                'name'          => 'event_start',
                'type'          => 'datetime',
                'standalone'    => true,
                'label'         => $this->i18n('events_start_date'),
                'required'      => 1
            );
        }

        //prepare event_end
        if (in_array('event_end', $current_options)) {
            $vars['event_end'] = array(
                'name'          => 'event_end',
                'type'          => 'datetime',
                'standalone'    => true,
                'label'         => $this->i18n('events_end_date'),
                'required'      => 1
            );
        }

        if (in_array('allday_event', $current_options)) {
            $vars['allday_event'] = array(
                'name' => 'allday_event',
                'type' => 'dropdown',
                'standalone' => true,
                'required' => 1,
                'label' => $this->i18n('events_allday_event'),
                'options' => Events_Dropdown::getAllDay(array($this->registry)),
            );
        }

        //prepare customers
        if (in_array('customer', $current_options) || in_array('trademark', $current_options)) {
            $vars['customer'] = array(
                'custom_id' => 'customer',
                'name'      => 'customer',
                'type'      => 'autocompleter',
                'autocomplete' => array(
                    'type' => 'customers',
                    'buttons_hide' => 'add search',
                    'clear'        => 1,
                    'fill_options' => array('$customer => <id>',
                                           '$customer_autocomplete => [<code>] <name> <lastname>',
                                           '$trademark => <main_trademark>',
                                           '$trademark_autocomplete => <main_trademark_name>',
                                           '$trademark_oldvalue => <main_trademark_name>'),
                    'filters' => array('<type>' => ($type->get('related_customers_types') ? implode(', ', $type->get('related_customers_types')) : '0')),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers')
                ),
                'double'    => 'true',
                'required'  => 0,
                'width'     => '200',
                'label'     => $this->i18n('events_customer'),
                'help'      => $this->i18n('events_customer')
            );

            if (!empty($layouts['customer'])) {
                //get the label from the layout name
                $vars['customer']['label'] = $layouts['customer']['name'];
                $vars['customer']['help'] = $layouts['customer']['description'];
                if (!$layouts['customer']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['customer']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['customer']['readonly'] = !$layouts['customer']['edit'];
                }
            }
        }

        //prepare trademark
        if (in_array('trademark', $current_options)) {
            $vars['trademark'] = array(
                'custom_id' => 'trademark',
                'name'      => 'trademark',
                'type'      => 'autocompleter',
                'double'    => 'true',
                'autocomplete'      => array(
                    'search' => array('<name>', '<customer_name>'),
                    'sort' => array('<name>', '<customer_name>'),
                    'buttons_hide' => 'search',
                    'clear' => 1,
                    'type' => 'nomenclatures',
                    'suggestions' => '<name> [<customer_name>]',
                    'fill_options' => array('$trademark => <trademark>',
                                            '$trademark_autocomplete => <name>',
                                            '$customer => <customer>',
                                            '$customer_autocomplete => [<customer_code>] <customer_name>',
                                            '$customer_oldvalue => [<customer_code>] <customer_name>'),
                    'filters' => array('<type_keyword>' => 'trademark',
                                       '<customer_trademark>' => '1'),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'nomenclatures', 'nomenclatures')
                ),
                'width'     => '200',
                'label'     => $this->i18n('trademark'),
                'help'      => $this->i18n('trademark')
            );

            if (!empty($layouts['trademark'])) {
                //get the label from the layout name
                $vars['trademark']['label'] = $layouts['trademark']['name'];
                $vars['trademark']['help'] = $layouts['trademark']['description'];
                if (!$layouts['trademark']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['trademark']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['trademark']['readonly'] = !$layouts['trademark']['edit'];
                }
            }
        }

        //prepare projects
        if (in_array('project', $current_options)) {
            $vars['project'] = array(
                'name'                  => 'project',
                'type'                  => 'autocompleter',
                'autocomplete'          => array(
                    'type' => 'projects',
                    'buttons_hide' => 'add search',
                    'buttons' => 'clear',
                    'fill_options' => array('$project => <id>',
                                            '$project_autocomplete => [<code>] <name>'),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects')
                ),
                'double'                => 'true',
                'project_autocompleter' => true,
                'hide_all_button'       => true,
                'required'              => 0,
                'width'                 => '200',
                'label'                 => $this->i18n('events_project'));

            if (!empty($layouts['project'])) {
                //get the label from the layout name
                $vars['project']['label'] = $layouts['project']['name'];
                $vars['project']['help'] = $layouts['project']['description'];
                if (!$layouts['project']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['project']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['project']['readonly'] = !$layouts['project']['edit'];
                }
            }
        }

        //prepare description
        if (in_array('description', $current_options)) {
            $vars['description'] = array(
                'name' => 'description',
                'type' => 'textarea',
                'label' => $this->i18n('events_description'),
                'help' => $this->i18n('events_description'));

            if (!empty($layouts['description'])) {
                //get the label from the layout name
                $vars['description']['label'] = $layouts['description']['name'];
                $vars['description']['help'] = $layouts['description']['description'];
                if (!$layouts['description']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['description']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['description']['readonly'] = !$layouts['description']['edit'];
                }
            }
        }

        //prepare location
        if (in_array('location', $current_options)) {
            $vars['location'] = array(
                'name' => 'location',
                'type' => 'text',
                'label' => $this->i18n('events_location'),
                'help' => $this->i18n('events_location'));

            if (!empty($layouts['location'])) {
                //get the label from the layout name
                $vars['location']['label'] = $layouts['location']['name'];
                $vars['location']['help'] = $layouts['location']['description'];
                if (!$layouts['location']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['location']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['location']['readonly'] = !$layouts['location']['edit'];
                }
            }
        }

        //prepare active/inactive
        if (in_array('active', $current_options)) {
            $vars['active'] = array(
                'name' => 'active',
                'type' => 'checkbox',
                'label' => $this->i18n('activate'),
                'help' => $this->i18n('activated') . '/' . $this->i18n('deactivated'),
                'option_value' => '1',
                'val' => '1');
        }

        //prepare group
        if (in_array('group', $current_options)) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters_groups = array('sanitize' => true);
            $groups = Groups::getTree($this->registry, $filters_groups);

            $_options_groups = array();
            foreach ($groups as $group) {
                $label = str_repeat('-', $group->get('level'));
                $_options_groups[] = array(
                    'label' => $label . $group->get('name'),
                    'option_value' => $group->get('id'));
            }

            $vars['group'] = array(
                'name'      => 'group',
                'type'      => 'dropdown',
                'label'     => $this->i18n('events_group'),
                'options'   => $_options_groups,
                'val'       => $type->get('default_group'));
        }

        // the prepared sorted array
        $sorted_vars_options = array();

        foreach ($current_options as $opt) {
            if (isset($vars[$opt])) {
                $sorted_vars_options[] = $vars[$opt];
                unset ($vars[$opt]);
            }
        }

        foreach ($vars as $var) {
            $sorted_vars_options[] = $var;
        }

        $vars_count = count($sorted_vars_options);
        for ($i = $vars_count-1; $i > 0; $i--) {
            if (!@$sorted_vars_options[$i]['hidden']) {
                $sorted_vars_options[$i]['last_visible'] = true;
                break;
            }
        }

        return $sorted_vars_options;
    }

    /**
     * Generates a file using specified pattern, header and footer
     */
    private function _print() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = ($_POST ? $request->get('id') : $request->get('print'));
        $filters = array('where' => array('e.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $event = Events::searchOne($this->registry, $filters);

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            if (!$request->get('pattern')) {
                //show error no such model
                $this->registry['messages']->setError($this->i18n('error_events_print_no_default_pattern'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }

            //get the pattern
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters = array('where' => array('p.id = ' . $request->get('pattern')),
                             'sanitize' => true,
                             'model_lang' => $request->get('model_lang'));
            $pattern = Patterns::searchOne($this->registry, $filters);

            if (!empty($pattern)) {
                $patterns_vars = $event->getPatternsVars();
                $event->extender = new Extender();
                $event->extender->model_lang = $event->get('model_lang');
                $event->extender->module = $this->module;
                foreach ($patterns_vars as $key => $value) {
                    $event->extender->add($key, $value);
                }

                if ($pattern->get('force_generate')) {
                    //generate and save file and get its id
                    $this->old_model = clone $event;

                    $result = false;
                    if ($pattern->get('not_regenerate_finished_record') && $event->get('status')=='finished') {
                        $previous_generated_file = $event->getLastGeneratedFile($pattern);

                        if ($previous_generated_file) {
                            $result = $previous_generated_file->get('id');
                        }
                    }

                    if (!$result) {
                        if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                            $result = $event->generateXLS();
                        } elseif (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                            $result = $event->generateDOCX();
                        } else {
                            $result = $event->generatePDF();
                        }
                    }
                    if ($result) {
                        $event->set('file_id', $result, true);
                        if (!$this->registry->isRegistered('event')) {
                            $this->registry->set('event',  $event->sanitize());
                        }

                        //save history
                        require_once PH_MODULES_DIR . 'events/models/events.history.php';
                        Events_History::saveData($this->registry, array('model' => $event,
                                                                        'action_type' => 'print',
                                                                        'pattern' => $pattern->get('id'),
                                                                        'generated_file' => $result));

                        // show the file
                        require_once PH_MODULES_DIR . 'files/models/files.factory.php';

                        $file_id = $request->get('file');
                        $filters = array('where'    => array('f.id = ' . $result),
                                         'sanitize' => true);
                        $file = Files::searchOne($this->registry, $filters);

                        $file->viewFile();
                        exit;
                    }
                } else {

                    //save history
                    require_once PH_MODULES_DIR . 'events/models/events.history.php';
                    Events_History::saveData($this->registry, array('model' => clone $event,
                                                                    'action_type' => 'print',
                                                                    'pattern' => $pattern->get('id'),
                                                                    'generated_file' => false));

                    if (preg_match('#^(xls|xlsx)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $event->generateXLS('browser_mode');
                    } if (preg_match('#^(docx|docx2pdf)$#', $pattern->get('format'))) {
                        //generate file to the browser window
                        $result = $event->generateDOCX('browser_mode');
                    } else {
                        //generate file to the browser window
                        $result = $event->generatePDF('browser_mode');
                    }
                }
            } else {
                $result = false;
            }

            if ($result) {
                //the content is displayed in the browser window
                exit;
            } else {
                $this->registry['messages']->setError($this->i18n('error_events_print_document'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'view', array('view' => $event->get('id')));
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param mixed $ids Array of ids or crypted string with array of ids
     *                  or crypted string containing from_id and to_id
     */
    private function _multiPrint($ids = '') {
        $request = &$this->registry['request'];

        if (isset($_SERVER['HTTP_REFERER'])) {
            preg_match('/&events=([^&]*)(&.+)?$/', $_SERVER['HTTP_REFERER'], $matches);
        }
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'list';
        }

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        if (!is_array($ids)) {
            Events::decryptIdsMultiprint($this->registry, $ids);
            //set to request as decrypted array
            $request->set('items', $ids, 'all', true);
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $events = array();
        if ($ids) {
            $filters = array('where' => array('e.id IN (' . implode(', ', $ids) . ')'),
                             'model_lang' => $this->registry->get('lang'),
                             'sanitize' => true);
            $events = Events::search($this->registry, $filters);
        }

        //if no models
        if (empty($events) || count($ids) != count($events)) {
            $this->registry['messages']->setError($this->i18n('error_no_events_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, $after_action);
        }

        $type = $events[0]->get('type');
        foreach ($events as $event) {
            $type_i = $event->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, $after_action);
                return true;
            }
        }

        require_once $this->modelsDir . 'events.types.factory.php';
        $filters = array('where' => array('et.id = ' . $type),
                         'sanitize' => true);
        $event_type = Events_Types::searchOne($this->registry, $filters);
        $type_name_plural = $event_type && $event_type->get('name_plural') ? $event_type->get('name_plural') : $this->i18n('events');

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        if (empty($pattern_id)) {
            $this->registry['messages']->setError($this->i18n('error_events_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_events_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        //get the pattern
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1',
                                          'p.format = "pdf"'),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        if (empty($pattern) || ($this->registry['currentUser']->get('is_portal') && !$pattern->get('is_portal'))) {
            $this->registry['messages']->setError($this->i18n('error_events_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_events_print_invalid_pattern'));
            $this->registry['messages']->insertInSession($this->registry);

            //no pattern specified, redirect to list
            $this->redirect($this->module, $after_action);
            return true;
        }

        $result = Events::multiPrint($this->registry, $this);
        if ($result) {
            //the pdf content is displayed in the browser window
            exit;
        } else {
            $this->registry['messages']->setError($this->i18n('error_events_multiprint_failed', array($type_name_plural)), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
        }

        $this->redirect($this->module, $after_action);
    }

    /**
     * Attaches files to the event
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);

        $filters['where'] = array('e.id = ' . $id);
        $filters['model_lang'] = $request->get('model_lang');
        $event = Events::searchOne($this->registry, $filters);
        if (!empty($event)) {
            $event->getAttachments();
        }

        $added_files = array(0 => array());

        if ($request->isPost()) {

            $modified_files    = array();
            $modified_genfiles = array();

            $erred_modified_files    = array();
            $erred_modified_genfiles = array();
            $erred_added_files       = array();
            $success_added_files     = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //edit existing attachments
            $names        = $request->get('file_names');
            $descriptions = $request->get('file_descriptions');
            $permissions  = $request->get('file_permissions');
            $revisions    = $request->get('file_revisions');
            $filenames    = $request->get('file_filenames');
            $files        = !empty($_FILES['file_paths']) ? $_FILES['file_paths'] : array();
            $indices      = $request->get('file_indices');

            if (!empty($names)) {
                foreach ($names as $idx => $name) {
                    $index = $indices[$idx];

                    if (!empty($files['tmp_name'][$idx])) {
                        $file = array(
                            'name'     => $files['name'][$idx],
                            'type'     => $files['type'][$idx],
                            'tmp_name' => $files['tmp_name'][$idx],
                            'error'    => $files['error'][$idx],
                            'size'     => $files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($names[$idx])) {
                        $names[$idx] = $files['name'][$idx];
                    }
                    $params = array(
                        'id'          => $idx,
                        'name'        => $names[$idx],
                        'filename'    => $filenames[$idx],
                        'description' => $descriptions[$idx],
                        'revision'    => $revisions[$idx],
                        'permission'  => $permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $event->sanitize())) {
                        $erred_modified_files[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_edit') . ' ' . $index, 'edit_attachment_' . $idx);

                        //explain the failed upload with more details
                        foreach (FilesLib::$_errors as $err) {
                            $this->registry['messages']->setError($err);
                        }
                        FilesLib::$_errors = array();
                    }
                    $event->unsanitize();
                    $modified_files[$idx] = $params;
                }
            }

            //edit existing generated files
            $generated_names        = $request->get('g_file_names');
            $generated_descriptions = $request->get('g_file_descriptions');
            $generated_permissions  = $request->get('g_file_permissions');
            $generated_revisions    = $request->get('g_file_revisions');
            $generated_indices      = $request->get('g_file_indices');

            if (!empty($generated_names)) {
                foreach ($generated_names as $idx => $name) {
                    $index = $generated_indices[$idx];

                    $file = array();
                    $params = array(
                        'id'          => $idx,
                        'name'        => $generated_names[$idx],
                        'description' => $generated_descriptions[$idx],
                        'permission'  => $generated_permissions[$idx]);

                    if (!Files::attachFile($this->registry, $file, $params, $event->sanitize())) {
                        $erred_modified_genfiles[] = $idx;
                        $this->registry['messages']->setError($this->i18n('error_attachments_gen_edit') . ' ' . $index, 'edit_gen_attachment_' . $idx);
                        FilesLib::$_errors = array();
                    }
                    $event->unsanitize();
                    $modified_genfiles[$idx] = $params;
                }
            }

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            $additional_descriptions = $request->get('a_file_descriptions');
            $additional_permissions  = $request->get('a_file_permissions');
            $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files['name'])) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx] ?? '',
                        'description' => $additional_descriptions[$idx] ?? '',
                        'revision'    => $additional_revisions[$idx] ?? '',
                        'permission'  => $additional_permissions[$idx] ?? 'all');
                    if (empty($params['name']) && !empty($file)) {
                        $params['name'] = $file['name'];
                    }

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $event->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setError($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }
                    $event->unsanitize();
                    $added_files[$idx] = $params;
                }
            }

            if ($modified_files && empty($erred_modified_files)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                Events_History::saveData($this->registry, array('model' => $event,
                                                                'action_type' => 'modified_attachments'));
            } elseif (!empty($modified_files)) {
                $this->registry['modified_files'] = $modified_files;
                $this->registry['erred_modified_files'] = $erred_modified_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($modified_genfiles && empty($erred_modified_genfiles)) {
                $this->registry['messages']->setMessage($this->i18n('message_attachments_gen_modified'));
                $this->registry['messages']->insertInSession($this->registry);

                Events_History::saveData($this->registry, array('model' => $event,
                                                                'action_type' => 'modified_gen'));
            } elseif (!empty($modified_genfiles)) {
                $this->registry['modified_genfiles'] = $modified_genfiles;
                $this->registry['erred_modified_genfiles'] = $erred_modified_genfiles;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $filters = array('where' => array('e.id = ' . $event->get('id')),
                                 'model_lang' => $event->get('model_lang'));
                $event_attached_files = Events::searchOne($this->registry, $filters);
                $event_attached_files->getAttachments();
                $event_attached_files->sanitize();

                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                Events_History::saveData($this->registry, array('model' => $event, 'action_type' => 'add_attachments', 'new_model' => $event_attached_files, 'old_model' => $event));
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files) && empty($erred_modified_files) && empty($erred_modified_genfiles)) {
                $this->actionCompleted = true;
            }
        }

        $this->registry['added_files'] = $added_files;

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            if ($event->isSanitized()) {
                $event->unsanitize();
            }

            $event->getAttachments();
            $event->getGeneratedFiles();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('event')) {
                $this->registry->set('event', $event->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Fetches generated event file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'attachments' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters['where'] = array('e.id = ' . $id);
        $filters['model_lang'] = $request->get('model_lang');
        $event = Events::searchOne($this->registry, $filters);

        if (!empty($event)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($event);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters['where'] = array('f.id = ' . $request->get('file'));
            $filters['sanitize'] = true;
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                    }
                    break;
                case 'delfile':
                    // get the files info needed for the audit
                    $old_event = clone $event;
                    if ($file->get('origin') == 'attached') {
                        $old_event->getAttachments();
                    } else {
                        $old_event->getGeneratedFiles();
                    }

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('e.id = ' . $event->get('id')),
                                         'model_lang' => $event->get('model_lang'));
                        $event_del_files = Events::searchOne($this->registry, $filters);
                        if ($file->get('origin') == 'attached') {
                            $event_del_files->getAttachments();
                        } else {
                            $event_del_files->getGeneratedFiles();
                        }
                        $event_del_files->sanitize();

                        Events_History::saveData($this->registry,
                                                 array('model' => $event,
                                                       'action_type' => ($file->get('origin') == 'attached' ? 'del_attachments' : 'generate_delete'),
                                                       'new_model' => $event_del_files,
                                                       'old_model' => $old_event));

                        $this->registry['messages']->setMessage($this->i18n('message_events_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_events_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    // get the files info needed for the audit
                    $old_event = clone $event;
                    if ($file->get('origin') == 'attached') {
                        $old_event->getAttachments();
                    } else {
                        $old_event->getGeneratedFiles();
                    }

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('e.id = ' . $event->get('id')),
                                         'model_lang' => $event->get('model_lang'));
                        $event_del_files = Events::searchOne($this->registry, $filters);
                        if ($file->get('origin') == 'attached') {
                            $event_del_files->getAttachments();
                        } else {
                            $event_del_files->getGeneratedFiles();
                        }
                        $event_del_files->sanitize();

                        Events_History::saveData($this->registry,
                                                 array('model' => $event,
                                                       'action_type' => ($file->get('origin') == 'attached' ? 'del_attachments' : 'generate_delete'),
                                                       'new_model' => $event_del_files,
                                                       'old_model' => $old_event));

                        $this->registry['messages']->setMessage($this->i18n('message_events_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_events_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'attachments', 'attachments=' . $event->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Transform event
     */
    private function _transform() {
        $request = &$this->registry['request'];
        //get the requested model ID
        if ($request->isPost()) {
            $id = $request->get('id');
        } else {
            $id = $request->get('transformations');
        }

        $filters = array('where' => array('e.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        //temporarily set flag in order to call prepare() method for model
        $this->registry->set('prepareModels', true, true);
        $event = Events::searchOne($this->registry, $filters);
        $this->registry->set('prepareModels', false, true);
        //$old_event = clone $event;

        if (empty($event)) {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_event'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $transform_id = $request->get('transform');
        $filters = array('where' => array('t.id = ' . $transform_id,
                                          't.source_model = \'' . $this->modelName . '\'',
                                          't.source_type = \'' . $event->get('type') . '\''),
                         'model_lang' => $request->get('model_lang'));
        require_once PH_MODULES_DIR . 'transformations/models/transformations.factory.php';
        $transformation = Transformations::searchOne($this->registry, $filters);

        if (empty($transformation)) {
            //show error no such transformation
            $this->registry['messages']->setError($this->i18n('error_no_such_transformation'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such transformation, redirect to the view
            $this->redirect($this->module, 'view', 'view=' . $id);
        }

        $transform = $transformation->getAll();

        //check access and ownership of the model
        $this->action = 'transform';
        $this->checkAccessOwnership($event);
        $this->action = 'transformations';

        $event->set('transform_name', $transform['name'], true);
        $event->set('transform_type', $transform['transform_type'], true);
        $event->set('transform_method', $transform['method'], true);

        $result = $this->{$transform['method']}($event, $transform);
    }

    /**
     * Set transform settings
     */
    private function setTransformSettings($params = array()) {
        $rows = $params['settings'];
        //settings additional to additional, additional to basic, basic to additional variables
        $transform_settings = array();
        //ignore variables
        $transform_ignore = array();
        //ignore basic variables
        $transform_ignore_basic = array();
        //allowed filters
        $transform_definitions = array();
        //predefined filters
        $transform_filters = array();
        //allowed grouping
        $transform_grouping_definitions = array();
        //predefined grouping
        $transform_grouping = array();
        //allowed edit variables in group
        $transform_edit_group = array();
        //allowed edit variables in gt
        $transform_edit_gt2 = array();
        //allowed edit plain variables
        $transform_edit_fields = array();
        //status after transform
        $status_after_transform = '';
        //substatus after transform
        $substatus_after_transform = '';
        //after transformation location
        $after_transformation_location = '';
        //set new transform variable to value
        $transform_equals = array();
        //calculate value for transform variable
        $transform_calc = '';
        //skip intermediate screen
        $skip_intermediate_screen = '';
        if (!empty($rows)) {
            //parse rows
            $func_arr = preg_split('/(\n|\r|\r\n)/', $rows);
            foreach ($func_arr as $f_row) {
                //row started with # is comment
                if (empty($f_row) || $f_row[0]=='#') {
                    continue;
                }
                list($key, $value) = preg_split('/\s*\:=\s*/', $f_row);
                if (preg_match('/^transform_(a|b|dt|usr)_(.+)/', $key, $s_matches) &&
                    preg_match('/^([ab])_(type)*([0-9]+)*_*(.+)/', $value, $d_matches)) {
                    //variable settings
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like transform_a_total := a_type2_contracted_total
                        $transform_settings[$s_matches[1].$d_matches[1].$d_matches[3]][$s_matches[2]] = $d_matches[4];
                    } else {
                        //like transform_a_total := a_contracted_total
                        $transform_settings[$s_matches[1].$d_matches[1]][$s_matches[2]] = $d_matches[4];
                    }
                } elseif ($key == 'ignore_transform_vars') {
                    //ignore variables list
                    $transform_ignore = preg_split('/\s*\,\s*/', $value);
                } elseif ($key == 'ignore_transform_basic') {
                    //ignore basic variables list
                    $transform_ignore_basic = preg_split('/\s*\,\s*/', $value);
                } elseif (preg_match('/^filter_definitions_(.+)/', $key, $s_matches)) {
                    //allowed filter fields
                    $fields = preg_split('/\s*\,\s*/', $value);
                    if ($fields[0] == 'all') {
                        //like filter_definitions_group1 := all
                        $transform_definitions[$s_matches[1]] = 'all';
                    } else {
                        //like filter_definitions_group1 := var1, var2, var3
                        foreach ($fields as $field) {
                            $transform_definitions[$s_matches[1]][] = $field;
                        }
                    }
                } elseif (preg_match('/^filter_(.+)/', $key, $s_matches)) {
                    $tmp = preg_match("/^\s*([^\s]+)\s+([^\s]+)\s+'(.*)'\s*/", $value, $fields);
                    if (count($fields) == 4) {
                        //predefined filters
                        //like filter_group1 := quality le '3'
                        $transform_filters[$s_matches[1]][] = array_slice($fields, 1);
                    }
                } elseif (preg_match('/^group_definitions_(.+)/', $key, $s_matches)) {
                    $tmp = preg_split("/\s*\,\s*/", trim($value));
                    //grouping definitions
                    //like group_definitions_group1 := var1, var2|var3, var4
                    $transform_grouping_definitions[$s_matches[1]] = $tmp;
                } elseif (preg_match('/^group_(.+)/', $key, $s_matches)) {
                    $tmp = preg_split("/\s*\|\s*/", trim($value));
                    //predefined grouping
                    //like group_group1 := articles_name|articles_customer
                    $transform_grouping[$s_matches[1]] = $tmp;
                } elseif (preg_match('/^edit_group_fields_(.+)/', $key, $s_matches)) {
                    $tmp = preg_split("/\s*\,\s*/", trim($value));
                    //edit group fields
                    //like edit_group_fields_group1 := articles_name,articles_customer
                    $transform_edit_group[$s_matches[1]] = $tmp;
                } elseif ($key == 'edit_gt2') {
                    $tmp = preg_split("/\s*\,\s*/", trim($value));
                    //edit gt2 fields
                    //like edit_gt2 := group_table_2
                    foreach ($tmp as $gt2_name) {
                        $transform_edit_gt2[$gt2_name] = true;
                    }
                } elseif (preg_match('/^edit_fields/', $key, $s_matches)) {
                    $tmp = preg_split("/\s*\,\s*/", trim($value));
                    //predefined grouping
                    //like group_group1 := articles_name,articles_customer
                    $transform_edit_fields = $tmp;
                } elseif (preg_match('/^status_after_transform/', $key, $s_matches)) {
                    $tmp = trim($value);
                    //status after transform
                    //like status_after_transform := opened
                    $status_after_transform = $tmp;
                } elseif (preg_match('/^substatus_after_transform/', $key, $s_matches)) {
                    $tmp = trim($value);
                    //substatus after transform
                    //like substatus_after_transform := opened_5
                    $substatus_after_transform = $tmp;
                } elseif (preg_match('/^after_transformation_location/', $key, $s_matches)) {
                    $tmp = trim($value);
                    //location after transform
                    //like after_transformation_location := edit
                    $after_transformation_location = $tmp;
                } elseif (preg_match('/^equals_([ab])_(type([0-9]+))?_*(.+)/', $key, $d_matches)) {
                    $tmp = trim($value);
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like equals_b_type2_status := closed
                        $transform_equals[$d_matches[1].$d_matches[3]][$d_matches[4]] = $tmp;
                    } else {
                        //like equals_b_status := closed
                        $transform_equals[$d_matches[1]][$d_matches[4]] = $tmp;
                    }
                } elseif (preg_match('/^calc_([ab])_(type)*([0-9]+)*_*(.+)/', $key, $d_matches)) {
                    $tmp = trim($value);
                    if (!empty($d_matches[2]) && !empty($d_matches[3])) {
                        //like calc_b_type2_deadline := date("Y-m-d H:i:s", strtotime("+7 day"))
                        $transform_calc[$d_matches[1].$d_matches[3]][$d_matches[4]] = $tmp;
                    } else {
                        //like calc_b_deadline := date("Y-m-d H:i:s", strtotime("+7 day"))
                        $transform_calc[$d_matches[1]][$d_matches[4]] = $tmp;
                    }
                } elseif (preg_match('/^skip_intermediate_screen/', $key, $s_matches)) {
                    $tmp = trim($value);
                    //skip intermediate screen
                    //skip_intermediate_screen := 1
                    $skip_intermediate_screen = $tmp;
                }
            }
        }

        //default status of the source document after transformation is 'locked'
        if (!$status_after_transform) {
            $status_after_transform = 'locked';
        }
        //check if the substatus setting is for the correct status
        if (!empty($substatus_after_transform) && !preg_match('#^' . preg_quote($status_after_transform) . '_\d+$#', $substatus_after_transform)) {
            $substatus_after_transform = '';
        }

        $this->registry->set('transform_settings', $transform_settings, true);
        $this->registry->set('transform_ignore', $transform_ignore, true);
        $this->registry->set('transform_ignore_basic', $transform_ignore_basic, true);
        $this->registry->set('transform_definitions', $transform_definitions, true);
        $this->registry->set('transform_filters', $transform_filters, true);
        $this->registry->set('transform_grouping_definitions', $transform_grouping_definitions, true);
        $this->registry->set('transform_grouping', $transform_grouping, true);
        $this->registry->set('transform_edit_group', $transform_edit_group, true);
        $this->registry->set('transform_edit_gt2', $transform_edit_gt2, true);
        $this->registry->set('transform_edit_fields', $transform_edit_fields, true);
        $this->registry->set('status_after_transform', $status_after_transform, true);
        $this->registry->set('substatus_after_transform', $substatus_after_transform, true);
        $this->registry->set('after_transformation_location', $after_transformation_location, true);
        $this->registry->set('transform_equals', $transform_equals, true);
        $this->registry->set('transform_calc', $transform_calc, true);
        $this->registry->set('skip_intermediate_screen', $skip_intermediate_screen, true);

        return true;
    }

    /**
     * Transform document from event
     *
     * @param Event $document - source Event object
     * @param array $params - array with transformation properties
     * @return bool - result of the operation
     */
    public function transformSimple(&$document, $params = array()) {
        $request = &$this->registry['request'];
        //get settings relations
        $this->setTransformSettings($params);
        $transform_settings = $this->registry->get('transform_settings');
        $transform_ignore = $this->registry->get('transform_ignore');
        $transform_ignore_basic = $this->registry->get('transform_ignore_basic');
        $transform_definitions = $this->registry->get('transform_definitions');
        $transform_filters = $this->registry->get('transform_filters');
        $transform_edit_fields = $this->registry->get('transform_edit_fields');
        $transform_edit_group = $this->registry->get('transform_edit_group');
        $transform_edit_gt2 = $this->registry->get('transform_edit_gt2');
        $after_transformation_location = $this->registry->get('after_transformation_location');
        $transform_equals = $this->registry->get('transform_equals');
        $transform_calc = $this->registry->get('transform_calc');
        $skip_intermediate_screen = $this->registry->get('skip_intermediate_screen');

        require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
        require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';

        //transform after post
        $new_type = $params['destination_type'];

        //check validity of the type
        $type = '';
        if (!empty($new_type)) {
            if (preg_match('#^document$#i', $params['destination_model'])) {
                $filters = array('where' => array('dt.id = ' . $new_type,
                                                  'dt.active = 1'),
                                 'sanitize' => true);
                $type = Documents_Types::searchOne($this->registry, $filters);
            }
        }
        //prepare source and destination basic, additional variables
        $source_basic_vars = $document->getAll();

        $source_additional_vars = array();

        //prepare destination variables
        $destination_properties = array('type' => $new_type);
        if (preg_match('#^document$#i', $params['destination_model'])) {
            $destination_document = new Document($this->registry, $destination_properties);
            //generates a system task if generating is allowed
            if ($type->get('generate_system_task')) {
                $destination_document->set('generate_system_task', 1, true);
            }
            $destination_document->set('type_name', $type->get('name'), true);
            $destination_basic_vars = $document->getAll();
            $destination_document->getVars();
            $tmp_vars = $destination_document->get('vars');

            //load lang file
            $i18n_files = array(PH_MODULES_DIR . 'documents/i18n/' . $this->registry['lang'] . '/documents.ini');
            $this->registry['translater']->loadFile($i18n_files);
        }

        $destination_additional_vars = array();
        foreach ($tmp_vars as $key => $var) {
            // if destination variable is in bb, skip it
            if (!empty($var['bb'])) {
                continue;
            }
            //add default empty value for empty row
            if (!empty($var['grouping']) && $var['type'] != 'group') {
                $var['value'] = array(0 => '');
            }
            $destination_additional_vars[$var['name']] = $var;
        }
        //plain variable names for transformation
        $simple_vars_to_show = array();
        //grouping num values for transformation
        $group_var_ids_to_show = array();
        //multilanguage variable names
        $destination_multilang_vars = array();
        //set destination additional var values
        foreach ($destination_additional_vars as $name => $var) {
            $source_name = '';
            $basic_source_name = '';
            if (isset($transform_settings['ba']) && in_array($name, $transform_settings['ba'])) {
                //basic to additional
                $basic_source_name = array_search($name, $transform_settings['ba']);
            }
            if ((isset($transform_equals['a'][$name]) || isset($transform_equals['a' . $new_type][$name])) &&
            empty($var['bb']) && empty($var['gt2'])) {
                //hard-coded value to additional
                $value = isset($transform_equals['a' . $new_type][$name]) ?
                         $transform_equals['a' . $new_type][$name] :
                         $transform_equals['a'][$name];
                if (!empty($var['grouping'])) {
                    // plain value to array
                    $value = array($value);
                }
                if ($var['type'] == 'checkbox_group') {
                    $value = array($value);
                }
                $destination_additional_vars[$name]['value'] = $value;
            } elseif ($basic_source_name) {
                //basic to additional
                $destination_additional_vars[$name]['value'] = $source_basic_vars[$basic_source_name];
            }
        }

        // prepare data for saving relation and history of models
        $transform_params = array(
            'origin_method' => 'Transformation',
            'origin_method_id' => isset($params['id']) ? $params['id'] : '0',
            'origin_model' => $document->modelName,
            'origin_id' => $document->get('id'),
            'origin_full_num' => $document->get('type_name'),
            'origin_name' => $document->get('name'),
        );

        //set destination additional variables
        if (preg_match('#^document$#i', $params['destination_model'])) {
            $destination_document->set('vars', $destination_additional_vars, true);

            //set destination basic var values
            $filters = array('model_lang' => $request->get('model_lang'),
                             'where' => array('dt.active = 1',
                                              'dt.id = ' . $new_type),
                             'sanitize' => true);
            $documentType = Documents_Types::searchOne($this->registry, $filters);
            //set direction, the source (original) id and model name
            $destination_document->set('direction', $documentType->get('direction'), true);

            foreach ($transform_params as $key => $value) {
                $destination_document->set($key, $value, true);
            }
        }

        //get current user
        if ($this->registry->get('originalUser')) {
            $user = $this->registry->get('originalUser');
        } else {
            $user = $this->registry->get('currentUser');
        }

        //variable used to store the relatives type
        $destination_document->set('clone_transform', 'transformed', true);
        //copy additional to basic variables
        $destination_document->set('assignments_owner', array(), true);
        $destination_document->set('assignments_responsible', array(), true);
        $destination_document->set('assignments_observer', array(), true);
        $destination_document->set('assignments_decision', array(), true);

        //set default values for basic
        foreach ($this->transformBasicDefinitions as $name) {
            if (!in_array($name, $transform_ignore_basic) || in_array($name, $this->transformRequiredDefinitions)) {
                if (in_array($name, $this->transformFromSourceDefinitions)) {
                    $destination_document->set($name, $document->get($name), true);
                } elseif (in_array($name, $this->transformFromDefaultDefinitions)) {
                    if ($name == 'group' && $documentType->modelName != 'Finance_Documents_Type') {
                        $destination_document->set($name, $documentType->getDefaultGroup(), true);
                    } elseif ($name == 'department' && $documentType->modelName != 'Finance_Documents_Type' && $documentType->modelName != 'Contracts_Type') {
                        $destination_document->set($name, $documentType->getDefaultDepartment(), true);
                    } else {
                        $destination_document->set($name, $documentType->get('default_'.$name), true);
                    }
                } elseif (in_array($name, $this->transformFromUserDefinitions)) {
                    $destination_document->set($name, $user->get($name), true);
                }
            }
        }

        //copy basic to basic
        if (isset($transform_settings['bb'])) {
            foreach ($transform_settings['bb'] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $document->get($source_name), true);
            }
        }
        if (isset($transform_settings['bb'.$new_type])) {
            foreach ($transform_settings['bb'.$new_type] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $document->get($source_name), true);
            }
        }

        //copy from type default to basic
        if (isset($transform_settings['dtb'])) {
            foreach ($transform_settings['dtb'] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $documentType->get('default_'.$source_name), true);
            }
        }
        if (isset($transform_settings['dtb'.$new_type])) {
            foreach ($transform_settings['dtb'.$new_type] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $documentType->get('default_'.$source_name), true);
            }
        }

        //copy from current user variables to basic
        if (isset($transform_settings['usrb'])) {
            foreach ($transform_settings['usrb'] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $user->get($source_name), true);
            }
        }
        if (isset($transform_settings['usrb'.$new_type])) {
            foreach ($transform_settings['usrb'.$new_type] as $source_name => $destination_name) {
                $destination_document->set($destination_name, $user->get($source_name), true);
            }
        }

        //set basic to value
        if (isset($transform_equals['b'])) {
            foreach ($transform_equals['b'] as $destination_name => $value) {
                $destination_document->set($destination_name, $value, true);
            }
        }
        if (isset($transform_equals['b'.$new_type])) {
            foreach ($transform_equals['b'.$new_type] as $destination_name => $value) {
                $destination_document->set($destination_name, $value, true);
            }
        }

        //calculate basic value
        foreach (array('b', 'b' . $new_type) as $key) {
            if (isset($transform_calc[$key])) {
                foreach ($transform_calc[$key] as $destination_name => $value) {
                    $destination_document->set(
                        $destination_name,
                        $this->replaceCalcVars(
                            $value,
                            $document,
                            $source_additional_vars,
                            $documentType,
                            $user,
                            isset($params['id']) ? $params['id'] : '0'
                        ),
                        true
                    );
                }
            }
        }

        //calculate additional value (only for non-grouping variables)
        foreach (array('a', 'a' . $new_type) as $key) {
            if (isset($transform_calc[$key])) {
                //get the additional variables of the destination document in assoc array
                $destination_additional_vars = $destination_document->getAssocVars();
                foreach ($transform_calc[$key] as $destination_name => $value) {
                    if (isset($destination_additional_vars[$destination_name])) {
                        $destination_var_value = $this->replaceCalcVars(
                            $value,
                            $document,
                            $source_additional_vars,
                            $documentType,
                            $user,
                            isset($params['id']) ? $params['id'] : '0'
                        );
                        if (is_array($destination_additional_vars[$destination_name]['value'])) {
                            //store the group variables and checkbox groups
                            $destination_additional_vars[$destination_name]['value'] =
                                is_array($destination_var_value) ?
                                    $destination_var_value : array($destination_var_value);

                        } else {
                            $destination_additional_vars[$destination_name]['value'] = $destination_var_value;
                        }
                    }
                }

                //store the updated variables back to the destination document
                $destination_document->set('vars', array_values($destination_additional_vars), true);
            }
        }

        //set ownership
        if ($destination_document->get('department') && $destination_document->get('assignments_owner')) {
            $destination_document->set('ownership', 'assigned', true);
        } elseif ($destination_document->get('department')) {
            $destination_document->set('ownership', 'forwarded', true);
        } else {
            $destination_document->set('ownership', 'unforwarded', true);
        }

        //do the transform
        if ($destination_document->validate('add') && $destination_document->transform()) {
            $langs = $document->get('translations');
            $model_lang = $document->get('model_lang');
            foreach ($langs as $t_lang) {
                //copy other translations
                if ($model_lang != $t_lang) {
                    $filters = array('where' => array('d.id = ' . $document->get('id'),
                                                      'd.active = 1'),
                                     'model_lang' => $t_lang);
                    $t_document = Documents::searchOne($this->registry, $filters);
                    if (!empty($t_document)) {
                        $filters = array('model_lang' => $t_lang,
                                         'where' => array('dt.active = 1',
                                                          'dt.id = ' . $new_type));
                        $t_documentType = Documents_Types::searchOne($this->registry, $filters);
                        $t_destination_properties = array('type' => $new_type,
                            'id' => $destination_document->get('id'), 'model_lang' => $t_lang);
                        $t_destination_document = new Document($this->registry, $t_destination_properties);
                        if ($t_documentType->get('default_name')) {
                            $t_destination_document->set('name', $t_documentType->get('default_name'), true);
                        } elseif ($t_documentType->get('name')) {
                            $t_destination_document->set('name', $t_documentType->get('name'), true);
                        } else {
                            $t_destination_document->set('name', $documentType->get('name'), true);
                        }
                        $t_destination_document->updateI18N('transform');
                        //save multilang additional variables
                        if (count($destination_multilang_vars)) {
                            //don't get post vars
                            $this->registry->set('get_old_vars', true, true);
                            $t_document->getVars();
                            $tmp_vars = $t_document->get('vars');
                            $destination_additional_vars = array();
                            foreach ($tmp_vars as $key => $var) {
                                if (isset($destination_multilang_vars[$var['name']])) {
                                    $destination_additional_vars[$var['name']] =
                                        $destination_multilang_vars[$var['name']];
                                    $destination_additional_vars[$var['name']]['value'] = $var['value'];
                                }
                            }
                            $t_destination_document->set('vars', $destination_additional_vars, true);
                            $t_destination_document->replaceVars();
                        }
                    }
                }
            }

            $sanitize_after = false;
            if ($document->sanitized) {
                $document->unsanitize();
                $sanitize_after = true;
            }
            //update relatives of parent model
            $document->updateRelatives(array('link_to' => $destination_document->get('id'),
                                             'link_type' => 'child',
                                             'origin' => strtolower($destination_document->modelName)));
            if ($sanitize_after) {
                $document->sanitize();
            }

            //write history for destination document
            $filters = array('where' => array('d.id = ' . $destination_document->get('id')),
                             'model_lang' => $destination_document->get('model_lang'),
                             'skip_assignments' => true,
                             'skip_permissions_check' => true);
            $new_document = Documents::searchOne($this->registry, $filters);
            $new_document->set('transform_params', serialize($transform_params), true);

            require_once PH_MODULES_DIR . 'documents/models/documents.history.php';
            require_once PH_MODULES_DIR . 'documents/models/documents.audit.php';
            $audit_parent = Documents_History::saveData(
                $this->registry,
                array (
                    'model' => $new_document,
                    'action_type' => 'transform',
                    'new_model' => $new_document,
                    'old_model' => $destination_document
                ));

            $destination_params = array(
                'origin_method' => !empty($transform_params['origin_method']) ? $transform_params['origin_method'] : '',
                'origin_method_id' => !empty($transform_params['origin_method_id']) ? $transform_params['origin_method_id'] : '',
                'destination_model' => $destination_document->modelName,
                'destination_id' => $new_document->get('id'),
                'destination_full_num' => $new_document->get('full_num'),
                'destination_name' => $new_document->get('name') ?: $new_document->get('type_name'),
            );
            $document->set('destination_params', $destination_params, true);

            // write history for source model
            Events_History::saveData(
                $this->registry,
                array(
                    'model' => $document,
                    'action_type' => 'create',
                    'new_model' => $document,
                    'old_model' => $document
                ));

            unset($new_document);

            //transform is successful
            $this->registry['messages']->setMessage($this->i18n('message_events_transform_success', array($destination_document->getModelTypeName())), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            $document->set('transformed_id', $destination_document->get('id'), true);

            //redirect to view or edit of transformed model
            if ($after_transformation_location == 'edit') {
                $this->redirect('documents', $after_transformation_location, $after_transformation_location . '=' . $destination_document->get('id'));
            } elseif ($this->registry->get('automation_transform') && !$after_transformation_location) {
                return true;
            } else {
                $this->redirect('documents', 'view', 'view=' . $destination_document->get('id'));
            }
        } else {
            //error...
            if ($this->registry->get('automation_transform')) {
                return false;
            } else {
                $this->registry['messages']->setError($this->i18n('error_events_transform', array($destination_document->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', 'view=' . $document->get('id'));
            }
        }

        if (!$this->registry->isRegistered('document')) {
            $this->registry->set('document', $document->sanitize());
        }

        return true;
    }
}

?>
