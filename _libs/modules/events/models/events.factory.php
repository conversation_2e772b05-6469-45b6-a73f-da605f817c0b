<?php

require_once('events.model.php');

/**
 * Events factory class
 */
Class Events extends Model_Factory {
    /**
     * Name of the model
     */
    public static $modelName = 'Event';

    /**
     * Defines number of results shown per page
     */
    public static $itemsPerPage = 10;

    /**
     * Searches(prepare) IDs for the models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  IDs
     */
    public static function getIds(&$registry, &$filters = array(), &$sql = array(), $count_only = false) {

        if (empty($sql)) {
            $sql = array('select' => '',
                         'from' => '',
                         'where' => '',
                         'group' => '',
                         'order' => '',
                         'limit' => '');
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //where clause
        $where = self::constructWhere($registry, $filters);

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        if (!$count_only) {
            //ORDER BY clause
            if (!empty($filters['sort'])) {
                $sort = implode(', ', $filters['sort']);
                if (!preg_match('#e\.active#', $sort)) {
                    $sort = 'ORDER BY e.active DESC, ' . $sort;
                } else {
                    $sort = 'ORDER BY ' . $sort;

                }
                if (preg_match('#e\.event_end#', $sort)) {
                    $sort = preg_replace('#e\.event_end#', 'event_end', $sort);
                }
            } else {
                $sort = 'ORDER BY e.active DESC, e.event_start DESC';
            }

            $sort .= ', e.id DESC';
        } else {
            $sort = '';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT (e.id) ';

        if (isset($filters['participant_id'])) {
            $participant_id = $filters['participant_id'];
        } elseif (isset($registry['currentUser'])) {
            $participant_id = $registry['currentUser']->get('id');
        } else {
            $participant_id = 'all';
        }

        if (isset($filters['participant_type'])) {
            $participant_type = $filters['participant_type'];
        } else {
            $participant_type = 'user';
        }

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                       'JOIN ' . DB_TABLE_EVENTS_TYPES . ' AS et' . "\n" .
                       '  ON (e.type=et.id AND et.active=1 AND et.deleted=0)' . "\n";
        if (preg_match('#ei18n\.#', $sort) || preg_match('#ei18n\.#', $where) ||
            isset($filters['field']) && ($filters['field'] == 'ei18n.name' || !$filters['field'])) {
            //relate to events_i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                            '  ON (e.id=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")'. "\n";
        }
        if (preg_match('#ci18n\.name#', $sort) || isset($filters['field']) && (preg_match('#ci18n\.name#', $filters['field']) || !$filters['field'])) {
            //relate to customers
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                            '  ON (e.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#pi18n\.name#', $sort) || isset($filters['field']) && ($filters['field'] == 'pi18n.name' || !$filters['field'])) {
            //relate to projects
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                            '  ON (e.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#eti18n\.name#', $sort) || isset($filters['field']) && $filters['field'] == 'eti18n.name') {
            //relate to event types i18n
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                '  ON (e.type=eti18n.parent_id AND eti18n.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#er\.#', $sort) || preg_match('#er\.#', $where)) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_RECURRENCE . ' AS er' . "\n" .
                            '  ON (e.id=er.parent_id)'. "\n";
        }
        if ($participant_id != 'all' && preg_match('#ea\.#', $where)) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' as ea' . "\n" .
                            '  ON (e.id=ea.parent_id AND ea.participant_type="' . $participant_type . '" AND ea.participant_id IN (' . $participant_id . '))' . "\n";
        }
        if (preg_match('#ni18n\.name#', $sort)) {
            //relate to nomenclatures
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                            '  ON (e.trademark=ni18n.parent_id AND ni18n.lang="' . $lang . '")' . "\n";
        }
        if (preg_match('#ui18n1\.firstname#', $sort)) {
            //relate to users - added by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                            '  ON (e.added_by=ui18n1.parent_id AND ui18n1.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n2\.firstname#', $sort)) {
            //relate to users - modified by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                            '  ON (e.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match('#ui18n3\.firstname#', $sort)) {
            //relate to users - deleted by
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                            '  ON (e.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $model_lang . '")' . "\n";
        }
        if (preg_match_all('#ea\d+\.#', $where, $matches)) {
            //relate to events assignments
            $matches = array_unique($matches[0]);
            foreach ($matches as $key => $value) {
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' AS ' . str_replace('.', '', $value) . "\n" .
                                '  ON (' . $value . 'parent_id=e.id)' . "\n";
            }
        }
        if (preg_match_all('#erl\d+\.#', $where, $matches)) {
            // relate to events relatives
            $matches = array_unique($matches[0]);
            foreach ($matches as $key => $value) {
                $value_alias = str_replace('.', '', $value);
                switch ($value_alias) {
                    case 'erl1':
                        $value .= 'origin = \'document\' AND ' . $value;
                        break;
                    case 'erl2':
                        $value .= 'origin = \'project\' AND ' . $value;
                        break;
                    default:
                        // invalid case
                        $value = '0 AND ' . $value;
                        break;
                }
                $sql['from'] .=
                    'LEFT JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS ' . $value_alias . "\n" .
                    '  ON (' . $value . 'parent_id = e.id)' . "\n";
            }
        }
        if (preg_match('#\bf\.id#', $where) || preg_match('#\bf\.id#', $sort)) {
            $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                            '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                            Files::getAdditionalWhere($registry) . "\n" .
                            // require assignment of current user to event
                            (!empty($registry['currentUser']) ?
                                ' AND (SELECT parent_id FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS .
                                ' WHERE participant_type=\'user\' AND participant_id=\'' . $registry['currentUser']->get('id') . '\' AND parent_id=e.id) IS NOT NULL' . "\n" :
                                '');
        }
        if (preg_match_all('#\b(f\.filename)([^"\']+)((["\']).*?(?<!\\\)\4)#', $where, $matches)) {
            // matches keys: 0 - the whole expression, 1 - var_name, 2 - comparison operator, 3 - searched value with surrounding quotes, 4 - the quote character
            foreach ($matches[0] as $key => $value) {
                $sql['from'] .= preg_replace('#\bf(\.|\s)#', "f$key$1",
                                'LEFT JOIN ' . DB_TABLE_FILES . ' AS f' . "\n" .
                                '  ON f.model=\'' . self::$modelName . '\' AND f.model_id=' . self::getAlias(self::$modelName, '') . '.id AND f.deleted_by=0' . "\n" .
                                Files::getAdditionalWhere($registry)) . "\n" .
                                // require assignment of current user to event
                                (!empty($registry['currentUser']) ?
                                    ' AND (SELECT parent_id FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS .
                                    ' WHERE participant_type=\'user\' AND participant_id=\'' . $registry['currentUser']->get('id') . '\' AND parent_id=e.id) IS NOT NULL' . "\n" :
                                    '');
                $negative_search = preg_match('#!=|NOT\s+LIKE#', $matches[2][$key]);
                // replace in WHERE clause only once (the last parameter is 1)
                $where = preg_replace('#' . preg_quote($matches[0][$key], '#') . '#', ($negative_search ? "f$key.id IS NULL" : "fi18n$key.parent_id IS NOT NULL"), $where, 1);
                if ($negative_search) {
                    $falias = 'f_';
                    $sql['from'] .= "  AND f$key.id IN (SELECT $falias$key.id FROM " . DB_TABLE_FILES . " AS $falias$key" . "\n" . '  ';
                } else {
                    $falias = 'f';
                }
                $matches[0][$key] =
                    preg_replace('#^f\.#', $falias . $key . '.', $matches[1][$key]) .
                    ($negative_search ? preg_replace('#!|NOT\s+#', '', $matches[2][$key]) : $matches[2][$key]) .
                    $matches[3][$key];
                // expand search by name into filename, name and description fields
                $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS ' . $falias . 'i18n' . $key . "\n" .
                                '  ON ' . $falias . 'i18n' . $key . '.parent_id=' . $falias . $key . '.id AND ' . $falias . 'i18n' . $key . '.lang=\'' . $model_lang . '\'' . "\n" .
                                ($negative_search ? '  WHERE ' . $falias . $key . '.model=\'' . self::$modelName . '\'' . "\n" : '') .
                                '    AND (' . implode(' OR ', array(
                                    $matches[0][$key],
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.name', $matches[0][$key]),
                                    preg_replace('#^' . $falias . $key . '\.filename#', $falias . 'i18n' . $key . '.description', $matches[0][$key]))) .
                                    ')' .($negative_search ? ')' : '') . "\n";
            }
        }

        $sql['where'] = $where;

        $sql['order'] = $sort . "\n";

        //limit (for pagination)
        $sql['limit'] = (!empty($filters['limit'])) ? 'LIMIT ' . $filters['limit'] . "\n" : '';
        if ($count_only) {
            //get the total count
            //get the total number of records for this search
            $sql['select'] = 'SELECT COUNT(DISTINCT e.id) AS total';
            $sql['order'] = '';
            $sql['limit'] = '';
            $query = implode("\n", $sql);
            $total = $registry['db']->GetOne($query);
            return $total;
        }
        if (!$count_only) {
            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $ids = $registry['db']->GetCol($query);
        } else {
            //get the total count
            //get the total number of records for this search
            $sql['select'] = 'SELECT COUNT(DISTINCT e.id) AS total';
            $sql['order'] = '';
            $sql['limit'] = '';
            $query = implode("\n", $sql);

            return $registry['db']->GetOne($query);
        }

        return $ids;
    }

    /**
     * Searches models with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array -  1. normal search - array of all models found
     *                  2. paged search  - array of array of all models found and their count
     */
    public static function search(&$registry, $filters = array()) {

        $sql = array('select' => '',
                     'from' => '',
                     'where' => '',
                     'group' => '',
                     'order' => '',
                     'limit' => '');

        if ($registry->get('getOneRequested')) {
            //one model is searched(searchOne)
            //so getIds is not needed
            $ids = self::constructWhere($registry, $filters);
        } else {
            $ids = self::getIds($registry, $filters, $sql);
        }

        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        if (isset($filters['participant_id'])) {
            $participant_id = $filters['participant_id'];
        } elseif (isset($registry['currentUser'])) {
            $participant_id = $registry['currentUser']->get('id');
        } else {
            $participant_id = 'all';
        }

        if (isset($filters['participant_type'])) {
            $participant_type = $filters['participant_type'];
        } else {
            $participant_type = 'user';
        }

        if (isset($filters['participant_id'])) {
            if ($filters['participant_id'] != 'all' && isset($registry['currentUser'])) {
                $current_time_zone = $registry['currentUser']->getPersonalSettings('interface', 'timezone');
            } else {
                $current_time_zone = $registry['config']->getParam('calendars', 'default_timezone');
            }
        } else {
            //get time zone of the current user
            $current_time_zone = $registry['currentUser']->getPersonalSettings('interface', 'timezone');
        }

        if (! $current_time_zone) {
            $current_time_zone = 'Europe/Sofia';
        }

        //select clause
        $sql['select'] = 'SELECT DISTINCT(e.id), e.*, ei18n.*, e.id AS order_idx, e.event_start as event_start_utc, ' . "\n" .
                         '  IF(allday_event, e.event_start + INTERVAL e.duration MINUTE - INTERVAL IF(e.duration < 1440, 0, 1) SECOND, CONVERT_TZ(e.event_start, "UTC", "' . $current_time_zone . '") + INTERVAL e.duration MINUTE) as event_end, ' . "\n" .
                         '  IF(allday_event, e.event_start, CONVERT_TZ(e.event_start, "UTC", "' . $current_time_zone . '")) as event_start,' . "\n" .
                         '  "' . $model_lang . '" as model_lang, er.* ' . "\n" .
                         '  , eti18n.name as type_name, et.keyword AS type_keyword' . "\n" .
                        (isset($filters['participant_id']) && empty($filters['has_calendar_filters'])?'':', erm.type as reminder_type') . "\n" .
                         (($participant_id != 'all') ? ', ea.access, ea.ownership' : '') .
                         (!empty($filters['display_reminders']) ? "\n" . ', erl.link_to AS related_id, erl.origin AS related_origin' : '');

        // collect all conditional (conditional for calendar and event listings) fields to get
        $select_conditional = array();
        // collect all conditional (conditional for calendar and event listings) table joins
        $from_conditional = array();
        if (empty($filters['get_fields']) || in_array('customer_name_code', $filters['get_fields'])) {
            //relate to customers
            $select_conditional[] = 'c.code AS customer_code';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c' . "\n" .
                                  '  ON (e.customer=c.id)' . "\n";
        }
        //relate to customers i18n - always, for info bubbles
        $select_conditional[] = 'CONCAT(ci18n.name, " ", ci18n.lastname) as customer_name';
        $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                              '  ON (e.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n";
        if (empty($filters['get_fields']) || in_array('project', $filters['get_fields']) || in_array('project_name_code', $filters['get_fields'])) {
            if (empty($filters['get_fields']) || in_array('project_name_code', $filters['get_fields'])) {
                //relate to projects
                $select_conditional[] = 'p.code AS project_code';
                $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_PROJECTS . ' AS p' . "\n" .
                                      '  ON (e.project=p.id)' . "\n";
            }
            //relate to projects i18n
            $select_conditional[] = 'pi18n.name as project_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                                  '  ON (e.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        }
        if (empty($filters['get_fields']) || in_array('trademark', $filters['get_fields']) || in_array('trademark_name_code', $filters['get_fields'])) {
            if (empty($filters['get_fields']) || in_array('trademark_name_code', $filters['get_fields'])) {
                //relate to trademarks
                $select_conditional[] = 'IF (e.trademark > 0, n.code, "") AS trademark_code';
                $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES . ' AS n' . "\n" .
                                      '  ON (e.trademark=n.id)' . "\n";
            }
            //relate to trademarks i18n
            $select_conditional[] = 'IF (e.trademark > 0, e.trademark, "") AS trademark, ni18n.name AS trademark_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_NOMENCLATURES_I18N . ' AS ni18n' . "\n" .
                                  '  ON (e.trademark=ni18n.parent_id AND ni18n.lang="' . $model_lang . '")' . "\n";
        }
        if (empty($filters['get_fields']) || in_array('added_by_name', $filters['get_fields']) || isset($filters['session_param'])) {
            //relate to user to fetch added by info
            $select_conditional[] = 'CONCAT(ui18n1.firstname, " ", ui18n1.lastname) as added_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                                  '  ON (e.added_by=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")'. "\n";
        }
        if (empty($filters['get_fields']) || in_array('modified_by_name', $filters['get_fields']) || isset($filters['session_param'])) {
            //relate to user to fetch modified by info
            $select_conditional[] = 'CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as modified_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                                  '  ON (e.modified_by=ui18n2.parent_id AND ui18n2.lang="' . $lang . '")'. "\n";
        }
        if (empty($filters['get_fields']) || in_array('status_modified_by_name', $filters['get_fields']) || isset($filters['session_param'])) {
            //relate to user to fetch status modified by info
            $select_conditional[] = 'CONCAT(ui18n4.firstname, " ", ui18n4.lastname) as status_modified_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n4' . "\n" .
                                  '  ON (e.status_modified_by=ui18n4.parent_id AND ui18n4.lang="' . $lang . '")'. "\n";
        }
        if (!empty($filters['where']) && preg_match('#e\.deleted#', implode('', $filters['where']))) {
            //relate to user to fetch deleted by info
            $select_conditional[] = 'CONCAT(ui18n3.firstname, " ", ui18n3.lastname) as deleted_by_name';
            $from_conditional[] = 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n3' . "\n" .
                                  '  ON (e.deleted_by=ui18n3.parent_id AND ui18n3.lang="' . $lang . '")'. "\n";
        }
        if (!empty($filters['where']) && preg_match_all('#erl\d+\.#', implode('', $filters['where']), $matches)) {
            // relate to events relatives
            $matches = array_unique($matches[0]);
            foreach ($matches as $key => $value) {
                $value_alias = str_replace('.', '', $value);
                switch ($value_alias) {
                    case 'erl1':
                        $value .= 'origin = \'document\' AND ' . $value;
                        break;
                    case 'erl2':
                        $value .= 'origin = \'project\' AND ' . $value;
                        break;
                    default:
                        // invalid case
                        $value = '0 AND ' . $value;
                        break;
                }
                $from_conditional[] =
                    'LEFT JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS ' . $value_alias . "\n" .
                    '  ON (' . $value . 'parent_id = e.id)' . "\n";
            }
        }

        if (!empty($filters['get_fields']) && in_array('comments', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT comments.id) AS comments';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_COMMENTS . ' AS comments' . "\n" .
                '  ON comments.model = \'' . self::$modelName . '\' AND comments.model_id = e.id' .
                ($registry['currentUser'] && $registry['currentUser']->get('is_portal') ? ' AND comments.is_portal = "1"' : '') . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('emails', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT esb.code) AS emails';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_EMAILS_SENTBOX . ' AS esb' . "\n" .
                '  ON esb.model = \'' . self::$modelName . '\' AND esb.model_id = e.id AND esb.`system` = 0 AND esb.resent_mail_id = 0' . "\n";
        }

        if (!empty($filters['get_fields']) && in_array('history_activity', $filters['get_fields'])) {
            $select_conditional[] = 'COUNT(DISTINCT eh.h_id) AS history_activity';
            $from_conditional[] =
                'LEFT JOIN ' . DB_TABLE_EVENTS_HISTORY . ' AS eh' . "\n" .
                '  ON eh.model = \'' . self::$modelName . '\' AND e.id = eh.model_id AND eh.action_type IN (\'' . implode('\', \'', History::$activity_actions) . '\')' . "\n";
        }

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_EVENTS . ' AS e' . "\n" .
                       'JOIN ' . DB_TABLE_EVENTS_TYPES . ' AS et' . "\n" .
                       '  ON (e.type=et.id AND et.active=1 AND et.deleted=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_EVENTS_I18N . ' AS ei18n' . "\n" .
                       '  ON (e.id=ei18n.parent_id AND ei18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_EVENTS_RECURRENCE . ' as er' . "\n" .
                       '  ON (e.id=er.parent_id)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_EVENTS_TYPES_I18N . ' AS eti18n' . "\n" .
                       '  ON (e.type=eti18n.parent_id AND eti18n.lang="' . $lang . '")' . "\n" .
                       (($participant_id != 'all') ?
                       'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' as ea' . "\n" .
                       '  ON (e.id=ea.parent_id AND ea.participant_type="' . $participant_type . '" AND ea.participant_id IN(' . $participant_id .'))'. "\n" : '') .
                       (isset($filters['participant_id']) && empty($filters['has_calendar_filters']) ? '' :
                       'LEFT JOIN ' . DB_TABLE_EVENTS_REMINDERS . ' AS erm' . "\n" .
                       '  ON (e.id=erm.parent_id AND erm.user_id=' . $registry['currentUser']->get('id') . ')' . "\n") .
                       (!empty($filters['display_reminders']) ?
                       'LEFT JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' AS erl' . "\n" .
                       '  ON e.id = erl.parent_id AND e.type = ' . PH_REMINDER_EVENT_TYPE . "\n" :
                       '');

        if ($select_conditional) {
            $sql['select'] .= ', ' . "\n" . implode(', ' . "\n", $select_conditional);
        }

        if ($from_conditional) {
            $sql['from'] .= "\n" . implode('', $from_conditional);
        }

        if (is_array($ids) && count($ids)) {
            //ids are returned form getIds so search and sort by them
            $sql['where'] = 'WHERE e.id in ('.@implode(',',$ids).')';
            $sql['order'] = 'ORDER BY find_in_set(order_idx, "'.@implode(',',$ids).'")';
            $sql['limit'] = '';
        } elseif ($registry->get('getOneRequested') && !empty($ids)) {
            //one model is searched(searchOne)
            $sql['where'] = $ids;
            $sql['order'] = '';
            $sql['limit'] = 'LIMIT 1';
        }

        if (!empty($ids)) {
            $sql['group'] = 'GROUP BY e.id';
            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $records = $registry['db']->GetAll($query);
        } else {
            $records = array();
            $filters['total'] = 0;
        }

        if (in_array($registry['module'], array('calendars', 'dashlets')) &&
        ($registry['action'] == 'month' || $registry['action'] == 'ajax_getdayevents') &&
        isset($filters['return_array']) && $filters['return_array'] && empty($filters['get_recurrence'])) {
            $records = self::setAssignments($registry, $records);
            return $records;
        }

        // get occurrences of recurrent events within period only when
        // these two parameters are passed in search filters
        if (!empty($filters['event_start']) && !empty($filters['event_end'])) {

            $predefined_events = array();
            if (!empty($records)) {
                foreach ($records as $key => $record) {
                    if ($record['parent_event_id']) {
                        //get predefined recurrence event ids
                        $predefined_events[$record['parent_event_id']][] = substr($record['event_start'], 0, 10);
                    }
                }
            }

            $byday_names = array ( 0 => 'SU', 1 => 'MO', 2 => 'TU', 3 => 'WE', 4 => 'TH', 5 => 'FR', 6 => 'SA' );

            // some processing of the filters similar to Events::constructWhere - used for "Display events of..."
            $filters_where = isset($filters['where']) ? $filters['where'] : array();
            if (!empty($filters['has_calendar_filters'])) {
                $assignments_index = 0;
                foreach ($filters_where as $idx => $filter) {
                    if (preg_match('#^ea[0-9]*.participant_id#', $filter)) {
                        if (preg_match('#(user)-#i', $filter)) {
                            $filter = preg_replace('#(user)-#i', '', $filter);
                            list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                            $filter = 'ea.participant_type = \'user\' AND ea.ownership = \'mine\' AND ea.participant_id ' . sprintf($operator, $value) . ' ' . $logical;
                        }
                        $filters_where[$idx] = preg_replace('#ea\.#', 'ea' . $assignments_index . '.', $filter);
                        $sql['from'] .= 'LEFT JOIN ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' AS ea' . $assignments_index . "\n" .
                                        '  ON (ea' . $assignments_index . '.parent_id=e.id)' . "\n";
                        $assignments_index ++;
                    }
                }
            }
            $query = $sql['select'] . "\n" .
                     preg_replace('#(e\.id=erm\.parent_id)#', '$1 AND erm.recurrence_date=\'0000-00-00\'', $sql['from']) . "\n" .
                     'WHERE e.deleted=0 AND e.id=er.parent_id' . ($filters_where ? ' AND ' . implode(' AND ', $filters_where) : '');
            if (!empty($filters['event_start'])) {
                $query .= " AND (er.repeat_end_date='0000-00-00 00:00:00' OR DATE(er.repeat_end_date) >= DATE('" . $filters['event_start'] . "'))";
            }
            if (!empty($filters['event_end'])) {
                $query .= " AND event_start <= '" . $filters['event_end'] . "'";
            }
            if (isset($registry['currentUser'])) {
                //check rights
                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $module = $registry['module'];
                $action = $registry['action'];
                if ($current_user_id &&
                (!isset($filters['participant_id']) || $filters['participant_id'] != 'all') &&
                ($module != 'events' && (in_array($module, array('calendars', 'dashlets')) ||
                (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == 'events')) ||
                $module == 'events' && in_array($action, array('search', 'list', 'view', 'subpanel')))) {
                    //check list, search rights
                    if ($module != 'events' || $action == 'subpanel') {
                        $current_right = isset($rights['events']['list']) ? $rights['events']['list'] : '';
                        // if there are no filters for other users or searching from calendar dashlet
                        if (empty($filters['has_calendar_filters']) && $registry['module'] == 'calendars' || $registry['module'] == 'dashlets') {
                            //check user's events_to_display calendar setting
                            if (empty($filters['events_to_display']) || $filters['events_to_display'] == 'participant') {
                                $query .= ' AND ea.ownership = "mine"';
                            } elseif ($filters['events_to_display'] == 'observer') {
                                $query .= ' AND ea.ownership = "other"';
                            } elseif ($filters['events_to_display'] == 'observer_or_participant') {
                                $query .= ' AND ea.ownership in ("mine", "other")';
                            }
                        }
                    } else {
                        $current_right = isset($rights[$module][$action]) ? $rights[$module][$action] : '';
                    }
                    if ($current_right == 'none') {
                        $query .= ' AND 0';
                    } elseif ($current_right == 'mine') {
                        $query .= ' AND (e.added_by = ' . $current_user_id . ' OR ea.access IS NOT NULL)';
                    } elseif ($current_right == 'group') {
                        $user_groups = $registry['currentUser']->get('groups');
                        $query .= ' AND (e.added_by = ' . $current_user_id . (count($user_groups)?' OR e.`group` IN ('.implode(',', $user_groups).')':'') . ' OR ea.access IS NOT NULL)';
                    }
                }
            }
            $recurrence_records = $registry['db']->GetAll($query);

            foreach ($recurrence_records as $key => $record) {
                // recurrent events should not have such data
                unset($record['event_start_utc']);

                if ($record['recurrence_type'] == 'monthlyByDay') {
                    $record['byday'] = array(ceil ( (int)date ( 'd', strtotime($record['event_start'])) / 7 ) . $byday_names[ date ( 'w', strtotime($record['event_start']) ) ]);
                }
                $record = self::nearestRecurrenceRecord($record, $filters);

                // start from the nearest recurrence, not from the initial date
                $event_start = $record['event_start'];

                $i = 0;
                //ToDo check repeat_count, frequency, bymonth, bymonthday, byday, bysetpos, byweekno, byyearday
                while ($record['event_start'] <= $filters['event_end']) {
                    if ($record['repeat_end_date'] != '0000-00-00 00:00:00' && $record['event_start'] > $record['repeat_end_date']) {
                        //after repeat_end_date
                        break;
                    }
                    if ($record['event_start'] <= $filters['event_end'] &&
                        $record['event_end'] > $filters['event_start'] &&
                        (!isset($predefined_events[$record['id']]) ||
                        !in_array(substr($record['event_start'], 0, 10), $predefined_events[$record['id']]))) {
                        $records[] = $record;
                    }
                    $i++;
                    $record = self::nextRecurrenceRecord($record, $event_start, $i);
                }
            }
        }

        //create array of model instances
        if (isset($filters['sanitize'])) {
            $sanitize = $filters['sanitize'];
        } elseif (count($records) == 1) {
            $sanitize = false;
        } else {
            $sanitize = true;
        }

        if (isset($filters['return_array']) && $filters['return_array']) {
            $records = self::setAssignments($registry, $records);
            $models = $records;
        } else {
            $models = self::createModels($registry, $records, self::$modelName, $sanitize);
        }

        if (!empty($filters['paginate'])) {
            if (empty($filters['total'])) {
                $filters['total'] = 0;
            }
            $results = array($models, $filters['total']);
        } else {
            //no pagination required return only the models
            $results = $models;
        }

        return $results;
    }

    /**
     * Prepare user filters ("Display events of...") and My nZoom preferences
     * to be applied to search filters for events in Calendar
     *
     * @param object $registry - the main registry
     * @param array $calendar_settings - calendar settings
     * @param mixed $calendar_types - user filters for calendar
     * @return array - prepared data
     */
    public static function prepareUserFilters(&$registry, $calendar_settings = false, $calendar_types = false) {
        $user_filters = array();

        // do not continue
        if (!in_array($registry['module'], array('calendars', 'dashlets')) || !$registry['currentUser']) {
            return $user_filters;
        }

        require_once PH_MODULES_DIR . 'calendars/models/calendars.calendar.class.php';
        // get caledar settings if not passed as an input parameter
        if ($calendar_settings === false) {
            $calendar_settings = Calendars_Calendar::getSettings($registry);
        }
        // get calendar filters if not passed as an input parameter
        if ($calendar_types === false) {
            $calendar_types = Calendars_Calendar::getUserFilters($registry);
        }

        // set a flag that events for other users are searched and personal preferences from My nZoom should not be applied
        if (!empty($calendar_types['user_ids']) || !empty($calendar_types['department_ids'])) {
            $user_filters['has_calendar_filters'] = true;
        } else {
            // My nZoom preference - participant, observer_or_participant, all
            $user_filters['events_to_display'] = $calendar_settings['events_to_display'];
        }

        // if there are filter preferences, prepare search filters for other events
        if (!empty($user_filters['has_calendar_filters']) && $calendar_types != 'all') {
            $user_ids = isset($calendar_types['user_ids']) ? $calendar_types['user_ids'] : array();
            if (empty($user_ids)) {
                $users_ids = array();
            }
            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
            $department_ids = isset($calendar_types['department_ids']) ? $calendar_types['department_ids'] : array();
            if (!empty($department_ids)) {
                foreach ($department_ids as $department_id) {
                    $user_ids = array_merge($user_ids, Departments::getUsersIds($registry,
                        array('where' => array('d.id =\'' . $department_id . '\''))));
                }
            }
            $user_ids = array_unique($user_ids);

            // display events according to current user's role to them
            $user_filters['participant_id'] = $registry['currentUser']->get('id');
            // filters to search only for events that specific users are participants in:
            // do this cheat because the processing works fine, it just removes
            // "user-" and adds extra conditions for ownership and participant type
            $user_filters['where'] = array(!empty($user_ids) ? 'ea.participant_id IN user-(\'' . implode('\', \'', $user_ids) . '\')' : '0');
        }

        if (isset($calendar_types['events_types'])) {
            $user_filters['where'][] = !empty($calendar_types['events_types']) ?
                'e.type IN ('. implode(', ', $calendar_types['events_types']) . ')' :
                //funny but the events list is empty
                'e.type=0';
        }

        // apply personal setting if reminders should be displayed in calendar
        if (!empty($calendar_settings['display_reminders'])) {
            // flag to get model name and id of related records - checked in Events::search()
            // also checked in Events::prepareRightsFilters() - whether to search reminders or not
            $user_filters['display_reminders'] = true;
        }

        return $user_filters;
    }

    /**
     * Searches exactly one model with specified filters
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return mixed - searched object model or false
     */
    public static function searchOne(&$registry, $filters = array()) {
        return self::getOne($registry, $filters, __CLASS__);
    }

    /**
     * Searches models for page with specified filters and params
     *
     * @param object $registry - the main registry
     * @param array $filters - search filters
     * @return array - with all necessary data for pagination of models
     */
    public static function pagedSearch(&$registry, &$filters = array()) {
        return self::paginatedSearch($registry, $filters, __CLASS__);
    }

    /**
     * Construct the where clause
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @return array - the prepared where array
     */
    public static function constructWhere(&$registry, $filters = array()) {

        $where[] = 'WHERE (';

        // add additional filters based on current user's rights on module, type(s) and action
        self::prepareRightsFilters($registry, $filters, $model_types);

        if (isset($registry['currentUser'])) {
            $current_user_id = $registry['currentUser']->get('id');
            $current_time_zone = $registry['currentUser']->getPersonalSettings('interface', 'timezone');
        } else {
            $current_user_id = 0;
            $current_time_zone = $registry['config']->getParam('calendars', 'default_timezone');
        }

        if (! $current_time_zone) {
            $current_time_zone = 'Europe/Sofia';
        }

        if (!empty($filters['event_start'])) {
            $where[] = "((DATE_ADD(e.event_start, INTERVAL e.duration MINUTE) > CONVERT_TZ('" . $filters['event_start'] . "', '" . $current_time_zone . "', 'UTC') AND e.allday_event = 0) OR \n" .
                       "         (DATE(DATE_ADD(e.event_start, INTERVAL e.duration - 1 MINUTE)) >= DATE('" . $filters['event_start'] . "') AND e.allday_event != 0)) AND ";
        }
        if (!empty($filters['event_end'])) {
            $where[] = "((e.event_start <= CONVERT_TZ('" . $filters['event_end'] . "', '" . $current_time_zone . "', 'UTC') AND e.allday_event = 0) OR \n" .
                       "         (DATE(e.event_start) <= DATE('" . $filters['event_end'] . "') AND e.allday_event != 0)) AND ";
        }
        if (!empty($filters['event_start']) && !empty($filters['event_end'])) {
            $where[] = 'er.recurrence_type is NULL AND ';
        }

        //remove recurrence events
        if (!empty($filters['event_start'])) {
            $where[] = 'er.parent_id IS NULL AND ';
        }

        //remove events that have to be hidden in the calendar
        if (isset($filters['show_in_calendar'])) {
            $where[] = 'et.show_in_calendar=' . intval($filters['show_in_calendar']) . ' AND ';
        }

        if (!empty($filters['key'])) {
            if (!empty($filters['field'])) {
                $where[] = sprintf('(%s)',
                                    General::buildClause($filters['field'], trim($filters['key']), true, 'like'));
            } else {//search in all fields
                //$module = $registry->get('module');
                //$controller = $registry->get('controller');
                require_once(PH_MODULES_DIR . 'filters/models/filters.factory.php');
                $vars = Filters::getSimpleSearchDefinitions($registry);
                foreach ($vars as $row) {
                    $var = $row['option_value'];
                    $key_where[] = General::buildClause($var, trim($filters['key']), true, 'like');
                }
                $where[] = '(' . implode(" OR \n\t", $key_where) . ')';
            }
            if (!empty($filters['hidden_type_section']) && $filters['hidden_type_section']) {
                $where[] = ' AND (et.type_section = \'' . urldecode($filters['hidden_type_section']) . '\')';
            } elseif (!empty($filters['hidden_type']) && $filters['hidden_type']) {
                $where[] = 'AND (e.type = \'' . urldecode($filters['hidden_type']) . '\')';
            }
        } elseif (isset($filters['where'])) {
            $additional_index = 0;
            $assignments_index = 0;
            foreach ($filters['where'] as $filter) {
                if (preg_match('/=\s*$/', $filter)) {
                    continue;
                }
                $filter = preg_replace('#currentUser#', $current_user_id, $filter);
                if (preg_match('#^ea[0-9]*.participant_id#', $filter)) {
                    if (preg_match('#(user)-#i', $filter)) {
                        //$assignment = preg_replace('#[^\'].*\'(user).*#i', '$1', $filter);
                        $filter = preg_replace('#(user)-#i', '', $filter);
                        list($field, $operator, $value, $logical) = parent::parseFilter($filter);

                        $filter = 'ea.participant_type = \'user\' AND ea.ownership = \'mine\' AND ea.participant_id ' . sprintf($operator, $value) . ' ' . $logical;
                    } elseif (preg_match('#(observer)-#i', $filter)) {
                        //$assignment = preg_replace('#[^\'].*\'(user).*#i', '$1', $filter);
                        $filter = preg_replace('#(observer)-#i', '', $filter);
                        list($field, $operator, $value, $logical) = parent::parseFilter($filter);

                        $filter = 'ea.participant_type = \'user\' AND (ea.access = \'edit\' OR ea.ownership = \'other\') AND ea.participant_id ' . sprintf($operator, $value) . ' ' . $logical;
                    } else {
                        list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                        $filter = 'ea.participant_type = \'customer\' AND ea.participant_id ' . sprintf($operator, $value) . ' ' . $logical;
                    }
                    $filter = preg_replace('#ea\.#', 'ea' . $assignments_index . '.', $filter);
                    $assignments_index ++;
                }
                if (preg_match('#^(erl\d+)\.link_to#', $filter, $matches)) {
                    $value_alias = $matches[1];
                    switch ($value_alias) {
                        case 'erl1':
                        case 'erl2':
                            list($field, $operator, $value, $logical) = parent::parseFilter($filter);
                            if (preg_match('#!=#', $operator) && $value) {
                                $operator .= ' OR ' . $field . ' IS NULL';
                            } elseif (!preg_match('#!=#', $operator) && !$value) {
                                $operator = 'IS NULL';
                            }
                            $filter = '(' . $field . ' ' . sprintf($operator, $value) . ') ' . $logical;
                            break;
                        default:
                            $filter = '1';
                    }
                }
                if (!preg_match('/(AND|OR)\s*$/', $filter)) {
                    //filters are custom (e.g. somewhere in the code)
                    $filter = $filter . ' AND ' . "\n";
                }
                $where[] = preg_replace('/\sAND$/', ') AND (', $filter) . "\n";
            }
        }

        $where = implode("\n\t", $where);

        $where = preg_replace('/\)\s(AND|OR)\s\(\n*$/', '', $where);
        $where = preg_replace('/\s(AND|OR)\s\n*$/', '', $where);
        $where .= ')';
        if (!preg_match('#e.deleted#', $where)) {
            $where .= ' AND e.deleted = 0';
        }
        // add condition to exclude reminders:
        // when in "events" module, unless action is reminder-specific
        if (
        (in_array($registry['module'], array('events')) &&
        !preg_match('#^ajax_(count|stop)_reminder$#', $registry['action'])) ||
        // when there are no 'type' search conditions or there are negative ones
        (!preg_match('#e\.id\s*(=|IN|LIKE|IS\sNOT\sNULL)#i', $where) &&
        (!preg_match('#e\.type\s*(=|IN|LIKE|IS\sNOT\sNULL)#i', $where) || preg_match('#e\.type\s*(!=|<>|NOT)#i', $where)) &&
        !preg_match('#e\.type\s?!=\s?"?' . PH_REMINDER_EVENT_TYPE. '"?#', $where))
        ) {
            $where .= ' AND e.type != ' . PH_REMINDER_EVENT_TYPE;
        }

        /*if ($current_user_id) {
            $rights = $registry['currentUser']->getRights();
            $module = 'events';
            if ($registry['action'] == 'subpanel' || $registry['action'] == 'ajax_sidepanel' || $registry['action'] == 'ajax_show_last_records') {
                $action = 'list';
            } elseif ($registry['action'] == 'dashlet') {
                $action = 'search';
            } elseif ($registry['action'] == 'export' || $registry['action'] == 'printlist') {
                if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                    $action = 'search';
                } else {
                    $action = 'list';
                }
            } else {
                $action = $registry['action'];
            }
        }

        if ($current_user_id && ($registry['module'] != $module || in_array($action, array('search', 'list', 'view', 'ajax_get_totals')))) {
            //check list, search rights
            if ($registry['module'] != $module && !(isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) {
                // actions for modification of reminders
                if (in_array($action, array('remind', 'ajax_save_reminds'))) {
                    $current_right = 'all';
                } else {
                    $current_right = isset($rights[$module]['list']) ? $rights[$module]['list'] : '';
                    // if there are no filters for other users or searching from calendar dashlet
                    if (empty($filters['has_calendar_filters']) && $registry['module'] == 'calendars' || $registry['module'] == 'dashlets') {
                        //check user's events_to_display calendar setting
                        if (empty($filters['events_to_display']) || $filters['events_to_display'] == 'participant') {
                            $where .= ' AND ea.ownership = "mine"';
                        } elseif ($filters['events_to_display'] == 'observer') {
                            $where .= ' AND ea.ownership = "other"';
                        } elseif ($filters['events_to_display'] == 'observer_or_participant') {
                            $where .= ' AND ea.ownership in ("mine", "other")';
                        }
                    }
                }
            } else {
                if ($registry['action'] == 'ajax_get_totals') {
                    $action = preg_replace('#_event$#', '', $filters['session_param']);
                    if ($action == 'list' || $action == 'search') {
                        //
                    } elseif ($action == 'filter') {
                        $action = 'search';
                    } else {
                        $action = 'list';
                    }
                }
                $current_right = ($registry['module'] == $module || (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) &&
                                 isset($rights[$module][$action]) ? $rights[$module][$action] : '';
            }

            if ($current_right == 'none') {
                $where .= ' AND 0';
            } elseif ($current_right == 'mine') {
                $where .= ' AND (e.added_by = ' . $current_user_id . ' OR ea.access IS NOT NULL)';
            } elseif ($current_right == 'group') {
                $user_groups = $registry['currentUser']->getGroups();
                $where .= ' AND (e.added_by = ' . $current_user_id . (count($user_groups)?' OR e.`group` IN ('.implode(',', $user_groups).')':'') . ' OR ea.access IS NOT NULL)';
            }
        } elseif (!preg_match('#ea.access#', $where) && (!isset($filters['participant_id']) || $filters['participant_id'] != 'all')) {
            $where .= ' AND ea.access IS NOT NULL';
        }*/
        $where = preg_replace('/\s\(\)/', ' 1', $where);

        return $where;
    }

    /**
     * Adds additional conditions to WHERE clause based on current user's permissions for action
     *
     * @param Registry $registry - the main registry
     * @param array $filters - search filters
     * @param array $model_types - this array stores the types of events (their ids); not used, addded to keep method same as in other modules
     * @return bool
     */
    private static function prepareRightsFilters(&$registry, &$filters, &$model_types) {
        $current_user_id = '';
        if ($registry->get('currentUser')) {
            $current_user_id = $registry['currentUser']->get('id');
        }
        $action = '';
        if ($current_user_id) {
            $rights = $registry['currentUser']->get('rights');
            $module = 'events';
            if ($registry['action'] == 'subpanel' || $registry['action'] == 'ajax_sidepanel' || $registry['action'] == 'ajax_show_last_records') {
                $action = 'list';
            } elseif ($registry['action'] == 'dashlet') {
                $action = 'search';
            } elseif ($registry['action'] == 'ajax_get_totals') {
                $action = preg_replace('#_event$#', '', $filters['session_param']);
                if ($action == 'list' || $action == 'search') {
                    //
                } elseif ($action == 'filter') {
                    $action = 'search';
                } else {
                    $action = 'list';
                }
            } elseif ($registry['action'] == 'export' || $registry['action'] == 'printlist') {
                if (!empty($filters['session_param']) && $filters['session_param'] == 'search_' . strtolower(self::$modelName)) {
                    $action = 'search';
                } else {
                    $action = 'list';
                }
            } elseif ($registry['module'] != $module && !in_array($registry['action'], array('list', 'search')) &&
            (in_array($registry['module'], array('calendars', 'dashlets')) ||
            (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module))) {
                // when searching from another module and rights should be checked
                $action = 'list';
            } else {
                $action = $registry['action'];
            }

            // When the action is ajax_select and there is a flag to check the permissions of the current user
            if ($action == 'ajax_select' && isset($filters['check_user_permissions']) && $filters['check_user_permissions'] == true) {
                // Use the same permissions as the search action
                $action = 'search';
            }
        }

        // permissions for types are checked only for multiple actions
        // NOTE: to allow autocomplete filter by additional variables accumulating $model_types allow 'ajax_select' action
        //       $model_types array is used to search by those additional variables
        if ($action != 'list' && $action != 'search' && $action != 'ajax_select') {
            return true;
        }

        $types_filters = array();

        // searches the filters for types
        if (isset($filters['where'])) {
            $model_types = array();

            // keep logical operator from previous iteration
            $prev_logical_operator = '';
            // flag whether previous iteraton was positive search by type or section
            $prev_was_type = false;
            // flag whether there is a positive search by type or section in an OR clause
            $type_in_OR_clause = false;

            $match_access_rule = false;
            foreach ($filters['where'] as $key => $filter_where) {
                if (preg_match('/=\s*$/', $filter_where)) {
                    //clear the empty filters
                    unset($filters['where'][$key]);
                    continue;
                } elseif (preg_match('#ea\.access#', $filter_where)) {
                    $match_access_rule = true;
                }

                //make sure all the types are fetched from the filters and only then manage the rest of the filters
                if (preg_match('#^e\.type#', $filter_where)) {
                    $parsed_filter = self::parseFilter($filter_where);

                    if (preg_match('#^=#', $parsed_filter[1]) && $parsed_filter[2] !== '') {
                        //check if the compare operator is EXACTLY "equals to" (=)
                        $model_types[] = $parsed_filter[2];
                    }
                }

                list($filter_name, $filter_compare, $filter_value, $logical_operator) = self::parseFilter($filter_where);
                if ($filter_name == 'e.type' && !preg_match('#(^| *)NOT( *)#', $filter_compare) && !preg_match('#(^| *)!=( *)#', $filter_compare)) {
                    $filtered_values = array();
                    $all_values = preg_split('# *, *#', $filter_value);
                    foreach ($all_values as $single_value) {
                        $filtered_values[] = $single_value;
                    }

                    // if a positive search is found then all the
                    // searched types are set in the separate array
                    $types_filters[] = array(
                        'types'             => $filtered_values,
                        'idx'               => $key,
                        'logical_operator'  => $logical_operator
                    );

                    if ($prev_logical_operator == 'OR' && !$prev_was_type) {
                        $type_in_OR_clause = true;
                    }
                    $prev_was_type = true;
                } else {
                    // not positive search by type
                    if ($prev_logical_operator == 'OR' && $prev_was_type) {
                        $type_in_OR_clause = true;
                    }
                    $prev_was_type = false;
                }
                // keep last logical operator for next iteration
                $prev_logical_operator = $logical_operator;
            }

            $inactive_model_types = array();
            if (!empty($types_filters)) {
                if ($type_in_OR_clause) {
                    // take all inactive types
                    $query = 'SELECT id FROM ' . DB_TABLE_EVENTS_TYPES . ' WHERE active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                } elseif ($model_types) {
                    // take inactive types from model types
                    $query = 'SELECT id FROM ' . DB_TABLE_EVENTS_TYPES .
                             ' WHERE id IN (' . implode(', ', $model_types) . ') AND active=0 OR deleted!=0';
                    $inactive_model_types = $registry['db']->GetCol($query);
                }

                // if search by types, check if they are active and keep only active ones in types filters
                if ($model_types && $inactive_model_types) {
                    foreach ($types_filters as $idx => $ctf) {
                        $active_model_types = array_diff($ctf['types'], $inactive_model_types);
                        if ($active_model_types) {
                            $types_filters[$idx]['types'] = $active_model_types;
                        } else {
                            $types_filters[$idx]['types'] = array('');
                        }
                    }
                }
            }
        }

        // if there are no filters set for type, all active events types are taken;
        // flag 'skip_permissions_check' is used for skipping check of permissions by type
        if (empty($types_filters) && empty($filters['skip_permissions_check'])) {
            $query = 'SELECT id FROM ' . DB_TABLE_EVENTS_TYPES . ' WHERE active=1 AND deleted=0';
            // display reminders only when specified - applies for calendar and dashlet
            if (empty($filters['display_reminders']) && (!isset($filters['where']) || !preg_match('#e\.id#', implode(' ', $filters['where'])))) {
                $query .= ' AND id != ' . PH_REMINDER_EVENT_TYPE;
            }
            $all_types_ids = $registry['db']->GetCol($query);
            $types_filters[] = array(
                'types'             => $all_types_ids,
                'idx'               => '',
                'logical_operator'  => 'AND'
            );
        }

        if (!empty($types_filters)) {
            foreach ($types_filters as $tf) {
                $current_rights_where = array();
                foreach ($tf['types'] as $t) {
                    $type_filter_set = '';
                    if ($current_user_id && ($registry['module'] != $module || in_array($action, array('search', 'list')))) {
                        // searching from another module
                        if ($registry['module'] != $module && !(isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) {
                            // actions for modification of reminders
                            if (in_array($action, array('remind', 'ajax_save_reminds'))) {
                                $current_right = 'all';
                            } else {
                                $current_right = isset($rights[$module . $t]['list']) ? $rights[$module . $t]['list'] : '';
                                // if there are no filters for other users or searching from calendar dashlet
                                if (empty($filters['has_calendar_filters']) && $registry['module'] == 'calendars' || $registry['module'] == 'dashlets') {
                                    //check user's events_to_display calendar setting
                                    if (empty($filters['events_to_display']) || $filters['events_to_display'] != 'all') {
                                        if ($current_right == 'none') {
                                            $type_filter_set = ' AND 0';
                                        } else {
                                            $type_filter_set = " AND e.type='" . $t . "'";
                                        }
                                    }
                                    if (empty($filters['events_to_display']) || $filters['events_to_display'] == 'participant') {
                                        $current_rights_where[] = 'ea.ownership = "mine"' . $type_filter_set;
                                    } elseif ($filters['events_to_display'] == 'observer') {
                                        $current_rights_where[] = 'ea.ownership = "other"' . $type_filter_set;
                                    } elseif ($filters['events_to_display'] == 'observer_or_participant') {
                                        $current_rights_where[] = 'ea.ownership in ("mine", "other")' . $type_filter_set;
                                    }
                                }
                            }
                        } else {
                            //check list, search rights
                            $current_right = ($registry['module'] == $module || (isset($filters['check_module_permissions']) && $filters['check_module_permissions'] == $module)) &&
                                             isset($rights[$module . $t][$action]) ? $rights[$module . $t][$action] : '';
                        }
                        if (!$type_filter_set) {
                            if ($current_right == 'none') {
                                $current_rights_where[] = '0';
                            } elseif ($current_right == 'mine') {
                                $current_rights_where[] = '((e.added_by = ' . $current_user_id . ' OR ea.access IS NOT NULL) AND e.type = ' . $t . ')';
                            } elseif ($current_right == 'group') {
                                $user_groups = $registry['currentUser']->getGroups();
                                $current_rights_where[] = '((e.added_by = ' . $current_user_id . (count($user_groups)?' OR e.`group` IN ('.implode(',', $user_groups).')':'') . ' OR ea.access IS NOT NULL) AND e.type = ' . $t . ')';
                            } elseif ($current_right == 'all') {
                                $current_rights_where[] = "e.type='" . $t . "'";
                            }
                        }
                    } elseif (!$match_access_rule && (!isset($filters['participant_id']) || $filters['participant_id'] != 'all')) {
                        $current_rights_where[] = 'ea.access IS NOT NULL AND e.type = ' . $t;
                    }
                }
                if (!empty($current_rights_where)) {
                    $changed_filter = '(' . implode(' OR ' . "\n", $current_rights_where) . ') ' . ($tf['logical_operator'] ? $tf['logical_operator'] : 'AND');
                    if ($tf['idx'] !== '') {
                        $filters['where'][$tf['idx']] = $changed_filter;
                    } else {
                        if (! isset($filters['where'])) {
                            $filters['where'] = array();
                        }
                        array_unshift($filters['where'], $changed_filter);
                    }
                }
            }
        }

        // If the module is not "calendars"
        if ($registry['module'] != 'calendars' && $current_user_id) {
            // Show events only if they are not private or they are aded by the current user or the current user have edit acces to them
            $filters['where'][] = "(e.visibility != 'private' || e.added_by = '{$current_user_id}' || ea.access = 'edit') AND";
        }

        return true;
    }

    /**
     * Saves search params in the session
     */
    public static function saveSearchParams(&$registry, $filters = array(), $sessionPrefix = 'list_') {
        $sessionParam = strtolower($sessionPrefix . self::$modelName);

        if ($registry['request']->isPost() && ($registry['action'] == 'subpanel' ||
        preg_match('#^' . General::plural2singular($registry['module']) . '\d+_ajax_$#', $sessionPrefix))) {
            // saving filters for records in subpanel
            // do not process filters switch keys if found in request
        } elseif ($registry['request']->isRequested('type_section') && $registry['request']->get('type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'et.type_section = \'' . urldecode($registry['request']->get('type_section')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->get('type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'e.type = \'' . urldecode($registry['request']->get('type')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type_section') && $registry['request']->get('hidden_type_section')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'et.type_section = \'' . urldecode($registry['request']->get('hidden_type_section')) . '\'';
        } elseif ($registry['request']->isRequested('hidden_type') && $registry['request']->get('hidden_type')) {
            $registry['session']->remove($sessionParam);
            $filters['where'][] = 'e.type = \'' . urldecode($registry['request']->get('hidden_type')) . '\'';
        } elseif ($registry['request']->isRequested('type') && $registry['request']->isRequested('type_section') &&
                  !$registry['request']->get('type') && !$registry['request']->get('type_section')) {
            $filters['display'] = $registry['session']->get($sessionParam)['display'] ?? '';
            $registry['session']->remove($sessionParam);
        }

        $search = self::saveSearchFilters($registry, $sessionParam, $filters);

        return $search;
    }

    /**
     * Builds a model object
     */
    public static function buildModel(&$registry) {
        $model = self::buildFromRequest($registry, self::$modelName);

        return $model;
    }

    /**
     * Builds a model object
     */
    public static function buildModelIndex(&$registry, $index) {
        $model = self::buildFromRequestIndex($registry, self::$modelName, $index);

        return $model;
    }

    /**
     * Changes status of specified models
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be changed
     * @param string $status - activate or deactivate
     * @return bool - result of the operations
     */
    public static function changeStatus(&$registry, $ids, $status) {
        $db = $registry['db'];

        if (empty($ids)) {
            return false;
        }

        $where = array();
        $where[] = General::buildClause('id', $ids);

        //INSERT INTO THE MAIN TABLE OF THE MODEL
        $set = array();
        $set['status']      = sprintf("active=%d", ($status == 'activate') ? 1 : 0);
        $set['modified']    = sprintf("modified=now()");
        $set['modified_by'] = sprintf("modified_by=%d", $registry['currentUser']->get('id'));

        //query to insert into the main table
        $query = 'UPDATE ' . DB_TABLE_EVENTS . "\n" .
                 'SET ' . implode(', ', $set) . "\n" .
                 'WHERE ' . implode(' AND ', $where);

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models
     * Deletion is fake only mark records as deleted
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function delete(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $deleted = self::deleteMultiple($registry, $ids, DB_TABLE_EVENTS);

        if (!$deleted) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Restores deleted records (only those that are marked as deleted)
     * ATTENTION: Purged models cannot be restored!
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function restore(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple deletion is part of the transaction
        $restored = self::restoreMultiple($registry, $ids, DB_TABLE_EVENTS);

        if (!$restored) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Deletes specified models. Deletion is real.
     * ATTENTION: deletion has no restore
     *
     * @param object $registry - the main registry
     * @param array $ids - ids of the models to be deleted
     * @return bool - result of the operations
     */
    public static function purge(&$registry, $ids) {
        $db = $registry['db'];

        //start transaction
        $db->StartTrans();

        //multiple purge is part of the transaction
        $purged = self::purgeMultiple($registry, $ids, DB_TABLE_EVENTS);

        if (!$purged) {
            $db->FailTrans();
        }

        //ToDo add additional queries to delete related records

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     *
     * @param array $record - current record of recurrence event
     * @param array $filters - contains date filters
     * @return array $record - first record of recurrence event
     */
    public static function nearestRecurrenceRecord($record, $filters) {

        if ($record['event_start'] >= $filters['event_start']) {
            return $record;
        }
        if ($record['recurrence_type'] == 'daily') {
            $record['event_start'] = substr_replace($record['event_start'], substr($filters['event_start'], 0, 10), 0, 10);
        } elseif ($record['recurrence_type'] == 'weekly') {
            $weeks = (int)((strtotime($filters['event_start']) - strtotime($record['event_start']))/(60*60*24*7));
            $event_start_arr = getdate(strtotime($record['event_start']));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'], $event_start_arr['mday'] + 7*$weeks, $event_start_arr['year']));
        } elseif ($record['recurrence_type'] == 'yearly') {
            $years = (int)((strtotime($filters['event_start']) - strtotime($record['event_start']))/(60*60*24*365));
            $event_start_arr = getdate(strtotime($record['event_start']));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'], $event_start_arr['mday'], $event_start_arr['year'] + $years));
        } elseif ($record['recurrence_type'] == 'monthlyByDate') {
            $months = (int)((strtotime($filters['event_start']) - strtotime($record['event_start']))/(60*60*24*31));
            $event_start_arr = getdate(strtotime($record['event_start']));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'] + $months, $event_start_arr['mday'], $event_start_arr['year']));
        } elseif ($record['recurrence_type'] == 'monthlyByDay') {
            $filters_event_start = General::strftime('%Y-%m-%d %H:%M:%S', strtotime('-4 day', strtotime($filters['event_start'])));
            $dw = ceil((int)date('d', strtotime($record['event_start']))/7) . ' ' . date('l', strtotime($record['event_start']));
            $tt = General::strftime('%Y-%m-%d 00:00:00', strtotime($dw, strtotime(date('Y-m-01',strtotime($filters['event_start'])))));
            if ($tt > date('Y-m-d 00:00:00', strtotime($filters['event_start']))) {
                $prev_month = strtotime('- 1 month', strtotime($filters['event_start']));
                $tt = General::strftime('%Y-%m-%d 00:00:00', strtotime($dw, strtotime(date('Y-m-01', $prev_month ))));
            }
            $record['event_start'] = substr($tt, 0, 11) . substr($record['event_start'], 11,8);
        }

        $record['event_end'] = date('Y-m-d H:i:s', strtotime($record['event_start']) + 60*$record['duration'] - 1*($record['allday_event'] == 1));

        return $record;
    }

    /**
     *
     * @param array $record - current record of recurrence event
     * @return array $record - next record of recurrence event
     */
    public static function nextRecurrenceRecord($record, $event_start, $i=0) {
        //ToDo - calculate for every recurrence type, calculate summer - winter time

        $offset = 7*24*60*60;
        if ($record['recurrence_type'] == 'daily') {
            //daily
            $event_start_arr = getdate(strtotime($record['event_start']));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'], $event_start_arr['mday'] + 1, $event_start_arr['year']));
        } elseif ($record['recurrence_type'] == 'weekly') {
            //weekly
            $event_start_arr = getdate(strtotime($record['event_start']));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'], $event_start_arr['mday'] + 7, $event_start_arr['year']));
        } elseif ($record['recurrence_type'] == 'yearly') {
            //yearly
            $event_start_arr = getdate(strtotime($event_start));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'], $event_start_arr['mday'], $event_start_arr['year'] + $i));
        } elseif ($record['recurrence_type'] == 'monthlyByDate') {
            //monthlyByDate
            $event_start_arr = getdate(strtotime($event_start));
            $record['event_start'] = date('Y-m-d H:i:s', mktime($event_start_arr['hours'], $event_start_arr['minutes'], $event_start_arr['seconds'], $event_start_arr['mon'] + $i, $event_start_arr['mday'], $event_start_arr['year']));
        } elseif ($record['recurrence_type'] == 'monthlyByDay') {
            //monthlyByDay
            $res = array();
            $date_check = strtotime($record['event_start']);
            while (!$res) {
                $date_check = strtotime('+28 day', $date_check);
                $res = self::getByDay ( $record['byday'], $date_check, 'month', strtotime($record['event_start']));
                if ($res) {
                    $record['event_start'] = date('Y-m-d H:i:s', $res[0]);
                }
            }
        } else {
            $record['event_start'] = date('Y-m-d H:i:s', strtotime($record['event_start']) + $offset);
        }

        $record['event_end'] = date('Y-m-d H:i:s', strtotime($record['event_start']) + 60*$record['duration'] - 1*($record['allday_event'] == 1));

        return $record;
    }

    /**
     * Get the dates the correspond to the byday values.
     *
     * @param array $byday   ByDay values to process (MO,TU,-1MO,20MO...)
     * @param string $cdate  First day of target search (Unix timestamp)
     * @param string $type   Month, Year, Week (default = month)
     * @param string $date   First day of event (Unix timestamp)
     *
     * @return array  Dates that match ByDay (YYYYMMDD format).
     */
    public static function getByDay ( $byday, $cdate, $type = 'month', $date ) {
        $byday_names = array ( 0 => 'SU', 1 => 'MO', 2 => 'TU', 3 => 'WE', 4 => 'TH', 5 => 'FR', 6 => 'SA' );
        $byday_values = array ( 'SU' => 0, 'MO' => 1, 'TU' => 2, 'WE' => 3, 'TH' => 4, 'FR' => 5, 'SA' => 6 );
        //$byday = array ( 0 => '5WE' );

        if ( empty ( $byday ) )
            return;

        $ret = array ();
        $hour = date ( 'H', $cdate );
        $minute = date ( 'i', $cdate );
        $mth = date ( 'm', $cdate );
        $yr = date ( 'Y', $cdate );
        if ( $type == 'month' ) {
            $ditype = date ( 't', $cdate ); //Days in month.
            $fday = mktime ( 0, 0, 0, $mth, 1, $yr ); //First day of month.
            $lday = mktime ( 0, 0, 0, $mth + 1, 0, $yr ); //Last day of month.
            $month = $mth;
        } elseif ( $type == 'year' ) {
            $ditype = date ( 'L', $cdate ) + 365; //Days in year.
            $fday = mktime ( 0, 0, 0, 1, 1, $yr ); //First day of year.
            $lday = mktime ( 0, 0, 0, 12, 31, $yr ); //Last day of year.
            $month = 1;
        } elseif ( $type == 'daily' ) {
            $fday = $lday = $cdate;
            $month = $mth;
        } else
            // We'll see if this is needed.
            return;

        $fdow = date ( 'w', $fday ); //Day of week first day of $type.
        $ldow = date ( 'w', $lday ); //Day of week last day of $type
        foreach ( $byday as $day ) {
            $byxxxDay = '';
            $dayTxt = substr ( $day, -2, 2 );
            $dayOffset = substr_replace ( $day, '', -2, 2 );
            $dowOffset = ( ( -1 * $byday_values[$dayTxt] ) + 7 ) % 7; //SU=0, MO=6, TU=5...
            if ( is_numeric ( $dayOffset ) && $dayOffset > 0 ) {
                // Offset from beginning of $type.
                $dayOffsetDays = ( ( $dayOffset - 1 ) * 7 ); //1 = 0, 2 = 7, 3 = 14...
                $forwardOffset = $byday_values[$dayTxt] - $fdow;
                if ( $forwardOffset < 0 )
                    $forwardOffset += 7;

                $domOffset = ( 1 + $forwardOffset + $dayOffsetDays );
                if ( $domOffset <= $ditype ) {
                    $byxxxDay = mktime ( $hour, $minute, 0, $month, $domOffset, $yr );
                    if ( $mth == date ( 'm', $byxxxDay ) && $byxxxDay > $date )
                    $ret[] = $byxxxDay;
                }
            } else if ( is_numeric ( $dayOffset ) ) { // Offset from end of $type.
                $dayOffsetDays = ( ( $dayOffset + 1 ) * 7 ); //-1 = 0, -2 = 7, -3 = 14...
                $byxxxDay = mktime ( $hour, $minute, 0, $month + 1,
                    ( 0 - ( ( $ldow + $dowOffset ) % 7 ) + $dayOffsetDays ), $yr );
                if ( $mth == date ( 'm', $byxxxDay ) && $byxxxDay > $date )
                    $ret[] = $byxxxDay;
            } else {
                if ( $type == 'daily' ) {
                    if ( ( date ( 'w', $cdate ) == $byday_values[$dayTxt] ) && $cdate > $date )
                        $ret[] = $cdate;
                } else {
                    for ( $i = 1; $i <= $ditype; $i++ ) {
                        $loopdate = mktime ( $hour, $minute, 0, $month, $i, $yr );
                        if ( ( date ( 'w', $loopdate ) == $byday_values[$dayTxt] ) &&
                            $loopdate > $date ) {
                            $ret[] = $loopdate;
                            $i += 6; //Skip to next week.
                        }
                    }
                }
            }
        }

        return $ret;
    }

    /**
     * getEvents() - get events for reminders from DB
     *
     * @param $registry
     * @param $userId
     * @param null $timezone
     * @return array with results
     */
    public static function getReminderEvents($registry, $userId, $timezone=null): array
    {
        $lang = $registry['lang'];
        $current_time_zone = $timezone ?? $registry['config']->getParam('calendars', 'default_timezone');

        if (! $current_time_zone) {
            $current_time_zone = 'Europe/Sofia';
        }

        $sql = array(
            'select' => "
                SELECT e.id AS idx,
                    er.user_id AS user_id,
                    eti18n.name AS type_name,
                    e.*,
                    er.*,
                    er.type AS reminder_type,
                    ei18n.*,
                    IF(allday_event,
                      e.event_start + INTERVAL e.duration MINUTE - INTERVAL IF(e.duration < 1440, 0, 1) SECOND,
                      CONVERT_TZ(e.event_start + INTERVAL e.duration MINUTE, 'UTC', '{$current_time_zone}')) AS event_end,
                    IF(allday_event,
                      e.event_start,
                      CONVERT_TZ(e.event_start, 'UTC', '{$current_time_zone}')) AS event_start,
                    ec.recurrence_type,
                    CONCAT(ci18n.name, ' ', ci18n.lastname) AS customer_name,
                    e.type AS event_type,
                    ea.access,
                    ea.ownership,
                    IF(er.toaster_times_sent = 0,
                      IF(er.date IS NOT NULL,
                        er.date,
                        IF(allday_event,
                          e.event_start,
                          CONVERT_TZ(e.event_start, 'UTC', '{$current_time_zone}')) - INTERVAL offset MINUTE),
                      er.toaster_last_sent + INTERVAL er.repeat_interval MINUTE) AS reminder_date
                  FROM " . DB_TABLE_EVENTS . " AS e
                  LEFT JOIN " . DB_TABLE_EVENTS_I18N . " AS ei18n
                    ON (e.id = ei18n.parent_id
                      AND ei18n.lang = '{$lang}')
                  LEFT JOIN " . DB_TABLE_EVENTS_TYPES_I18N . " AS eti18n
                    ON (e.type = eti18n.parent_id
                      AND eti18n.lang = '{$lang}')
                  LEFT JOIN " . DB_TABLE_EVENTS_REMINDERS . " AS er
                    ON (e.id = er.parent_id)
                  LEFT JOIN " . DB_TABLE_EVENTS_RECURRENCE . " AS ec
                    ON (e.id = ec.parent_id)
                  LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci18n
                    ON (e.customer = ci18n.parent_id
                      AND ci18n.lang = '{$lang}')
                  LEFT JOIN " . DB_TABLE_EVENTS_ASSIGNMENTS . " AS ea
                    ON (e.id = ea.parent_id
                      AND ea.participant_type = 'user'
                      AND ea.participant_id IN ({$userId}))",
            'where' => "
                  WHERE e.status IN ('planning', 'progress')
                    AND e.deleted = 0
                    AND (ec.parent_id IS NULL
                      OR er.recurrence_date != '0000-00-00')
                    AND er.toaster_times_sent < er.repeats
                    AND er.type != 'email'
                    AND user_id = {$userId}",
            // Select reminders, which currently should be executed
            'having' => "
                  HAVING NOW() BETWEEN reminder_date AND reminder_date + INTERVAL " . PH_PERIODICAL_REMINDER_INTERVAL . " SECOND"
        );
        $query = implode('', $sql);
        $records = $registry['db']->GetAssoc($query);

        //get recurrence events reminders
        $byday_names = array ( 0 => 'SU', 1 => 'MO', 2 => 'TU', 3 => 'WE', 4 => 'TH', 5 => 'FR', 6 => 'SA' );
        //ToDo set maximum offset for recurrence records
        $recurrence_max_offset = (30*24*60*60);
        $filters['event_start'] = date('Y-m-d H:i:s', time() - $recurrence_max_offset);
        $filters['event_end'] = date('Y-m-d H:i:s', time() + $recurrence_max_offset);
        if (!is_array($records)) {
            $records = array();
        }
        if (count($records)) {
            $not_ids = array_keys($records);
        }
        $predefined_events = array();
        foreach ($records as $key => $record) {
            if ($record['parent_event_id']) {
                //get predefined recurrence event ids
                $predefined_events[$record['parent_event_id']][] = substr($record['event_start'], 0, 10);
            }
        }
        $sql['where'] = "
              WHERE er.type != 'email'
                AND ec.parent_id is not null
                AND er.recurrence_date = '0000-00-00'
                AND user_id = {$userId}" .
                (isset($not_ids) ? "
                AND e.id NOT IN (" . implode(',', $not_ids) . ")" : '') . "
                AND e.deleted = 0";

        $query = implode('', $sql);
        $recurrence_records = $registry['db']->GetAssoc($query);
        if (!is_array($recurrence_records)) {
            $recurrence_records = array();
        }
        foreach ($recurrence_records as $key => $record) {

            if ($record['recurrence_type'] == 'monthlyByDay') {
                $record['byday'] = array(ceil ( (int)date ( 'd', strtotime($record['event_start'])) / 7 ) . $byday_names[ date ( 'w', strtotime($record['event_start']) ) ]);
            }
            $record = self::nearestRecurrenceRecord($record, $filters);

            // start from the nearest recurrence, not from the initial date
            $event_start = $record['event_start'];

            $i = 0;
            //ToDo check repeat_count, frequency, bymonth, bymonthday, byday, bysetpos, byweekno, byyearday
            while ($record['event_start'] <= $filters['event_end']) {
                if ($record['event_end'] >= $filters['event_start'] &&
                    strtotime($record['event_start']) - 60*$record['offset'] < time() &&
                    strtotime($record['event_end']) > time() &&
                    (!isset($predefined_events[$record['id']]) ||
                    !in_array(substr($record['event_start'], 0, 10), $predefined_events[$record['id']]))) {
                    //flag for add new predefined reminder
                    $query = "SELECT * FROM " . DB_TABLE_EVENTS_REMINDERS . " AS er \n" .
                             " WHERE parent_id=" . $record['id'] .
                             " AND user_id=" . $userId .
                             " AND recurrence_date='" . substr($record['event_start'], 0, 10) . "'";
                    $exist_row = $registry['db']->GetRow($query);
                    if (!$exist_row) {
                        $record['add_row'] = 1;
                        $records[$key] = $record;
                    } elseif ($exist_row['repeats'] > $exist_row['toaster_times_sent'] &&
                        strtotime($exist_row['toaster_last_sent']) + 60*$exist_row['repeat_interval'] <= time()) {
                        $record['recurrence_date'] = substr($record['event_start'], 0, 10);
                        $records[$key] = $record;
                    }
                }
                $i++;
                $record = self::nextRecurrenceRecord($record, $event_start, $i);
            }
        }

        $records = array_merge($records, Events::getReminderEventsFromSettings($registry, 'toaster'));

        return $records;
    }

    /**
     * Get events to remind for, depending on reminders from personal settings
     *
     * @param mixed $registry - the registry
     * @param string $reminder_type - 'email' or 'toaster' (default is 'email')
     * @return array - the events to remind for
     */
    public static function getReminderEventsFromSettings(&$registry, $reminder_type = 'email') {
        $records = array();

        /*
         * Append reminders from personal settings
         */
        // Get the personal settings for reminders for all users
        $query = "
            SELECT ps.user_id                                AS idx,
                ps.value                                     AS setting_value,
                u.active                                     AS user_active,
                u.email                                      AS user_email,
                TRIM(CONCAT(ui.firstname, ' ', ui.lastname)) AS user_name
              FROM " . DB_TABLE_PERSONAL_SETTINGS . " AS ps
              JOIN " . DB_TABLE_USERS . " AS u
                ON (u.id = ps.user_id
                  AND u.deleted_by = 0
                  AND u.active = 1)
              LEFT JOIN " . DB_TABLE_USERS_I18N . " AS ui
                ON (ui.parent_id = u.id
                  AND ui.lang = '{$registry['lang']}')
              WHERE ps.section = 'events'
                AND ps.name = 'reminders'";
        // If the reminder type is toaster
        if ($reminder_type == 'toaster') {
            // Check for reminders only for the current user
            $query .= "
                AND ps.user_id = '{$registry['currentUser']->get('id')}'";
        }
        $users_reminders = $registry['db']->GetAssoc($query);
        $events_types_reminders = array();
        foreach ($users_reminders as $user_id => $user_data) {
            $reminders = unserialize($user_data['setting_value']);
            $users_reminders[$user_id]['setting_value'] = $reminders;
            foreach ($reminders as $reminder) {
                if (in_array($reminder['reminders_types'], array($reminder_type, 'both'))) {
                    $events_types_reminders[$reminder['events_types']][$user_id][] = array(
                        'reminders_types'   => $reminder['reminders_types'],
                        'minutes'           => $reminder['minutes'],
                        'assignments_types' => $reminder['assignments_types'],
                        'participants'      => empty($reminder['participants']) || !is_array($reminder['participants']) ? array() : $reminder['participants']
                    );
                }
            }
        }

        if (isset($registry['currentUser'])) {
            $current_time_zone = $registry['currentUser']->getPersonalSettings('interface', 'timezone');
        } else {
            $current_time_zone = $registry['config']->getParam('calendars', 'default_timezone');
        }
        if (!$current_time_zone) {
            $current_time_zone = 'Europe/Sofia';
        }
        // Get all events with types for which there are personal settings for reminders
        $query = "
            SELECT e.id                                        AS idx,
                e.*,
                e.id                                           AS parent_id,
                eti18n.name                                    AS type_name,
                ei18n.name                                     AS name,
                IF(
                  allday_event,
                  e.event_start + INTERVAL e.duration MINUTE - INTERVAL IF(e.duration < 1440, 0, 1) SECOND,
                  CONVERT_TZ(e.event_start + INTERVAL e.duration MINUTE, 'UTC', '{$current_time_zone}')
                )                                              AS event_end,
                IF(
                  allday_event,
                  e.event_start,
                  CONVERT_TZ(e.event_start, 'UTC', '{$current_time_zone}')
                )                                              AS event_start,
                ec.recurrence_type                             AS recurrence_type,
                CONCAT(ci18n.name, ' ', ci18n.lastname)        AS customer_name,
                erl.link_to                                    AS related_id,
                erl.origin                                     AS related_origin
              FROM " . DB_TABLE_EVENTS . " AS e
              LEFT JOIN " . DB_TABLE_EVENTS_I18N . " AS ei18n
                ON (e.id = ei18n.parent_id
                  AND ei18n.lang = '{$registry['lang']}')
              LEFT JOIN " . DB_TABLE_EVENTS_TYPES_I18N . " AS eti18n
                ON (e.type = eti18n.parent_id
                  AND eti18n.lang = '{$registry['lang']}')
              LEFT JOIN " . DB_TABLE_EVENTS_RECURRENCE . " as ec
                ON (e.id = ec.parent_id)
              LEFT JOIN " . DB_TABLE_CUSTOMERS_I18N . " AS ci18n
                ON (e.customer = ci18n.parent_id
                  AND ci18n.lang = '{$registry['lang']}')
              LEFT JOIN " . DB_TABLE_EVENTS_RELATIVES . " AS erl
                ON (e.id = erl.parent_id
                  AND e.type = " . PH_REMINDER_EVENT_TYPE . ")
              WHERE e.deleted_by = 0
                AND e.active = 1
                AND e.status IN ('planning', 'progress')
                AND e.type IN ('" . implode("', '", array_keys($events_types_reminders)) . "')
              HAVING NOW() <= event_start";
        $reminder_events = $registry['db']->GetAll($query);
        $reminder_events_ids = array();
        foreach ($reminder_events as $reminder_event) {
            $reminder_events_ids[] = $reminder_event['id'];
        }

        // Get the assignments of this events
        $query = "
            SELECT parent_id, participant_id, ownership, access
              FROM " . DB_TABLE_EVENTS_ASSIGNMENTS . "
              WHERE participant_type = 'user'
                AND parent_id IN ('" . implode("', '", $reminder_events_ids) . "')";
        $reminder_events_assignments_array = $registry['db']->GetAll($query);
        $reminder_events_assignments = array();
        foreach ($reminder_events_assignments_array as $reminder_events_assignment) {
            $reminder_events_assignments[$reminder_events_assignment['parent_id']][$reminder_events_assignment['ownership']][] = $reminder_events_assignment['participant_id'];
        }

        // Get the events reminders history
        $query = "
            SELECT event_id, user_id, type, sent
              FROM " . DB_TABLE_REMINDERS_HISTORY . "
              WHERE event_id IN ('" . implode("', '", $reminder_events_ids) . "')";
        $reminders_history = $registry['db']->GetAll($query);
        $events_users_reminders_history = array();
        foreach ($reminders_history as $reminder_history) {
            $events_users_reminders_history[$reminder_history['event_id']][$reminder_history['user_id']][$reminder_history['type']][] = $reminder_history['sent'];
        }

        // Collect events which should be reminded
        $current_date = General::strftime($registry['translater']->translate('date_iso'));
        foreach ($reminder_events as $reminder_event) {
            foreach ($events_types_reminders[$reminder_event['type']] as $event_type_user_id => $event_type_user_reminders) {
                foreach ($event_type_user_reminders as $event_type_user_reminder) {
                    $reminder_date = General::strftime($registry['translater']->translate('date_iso'), strtotime("-{$event_type_user_reminder['minutes']} minutes", strtotime($reminder_event['event_start'])));
                    // If the moment from which te event should be reminded (start date - time to remind before the start date) has come
                    if ($reminder_date <= $current_date
                            // and the event start date didn't occurred yet
                            && $current_date <= $reminder_event['event_start']
                            // and the reminder should be done when the user is participant and he is one of the participants
                            && ($event_type_user_reminder['assignments_types'] == 'participant'
                                && isset($reminder_events_assignments[$reminder_event['id']]['mine'])
                                && in_array($event_type_user_id, $reminder_events_assignments[$reminder_event['id']]['mine'])
                                ||
                                // or the reminder should be done when the user is observer and he is one of the observers or the participants
                                $event_type_user_reminder['assignments_types'] == 'observer'
                                && (isset($reminder_events_assignments[$reminder_event['id']]['other'])
                                    && in_array($event_type_user_id, $reminder_events_assignments[$reminder_event['id']]['other'])
                                  ||
                                    isset($reminder_events_assignments[$reminder_event['id']]['mine'])
                                    && in_array($event_type_user_id, $reminder_events_assignments[$reminder_event['id']]['mine']))
                                // and there is no matter who are the participants
                                && (empty($event_type_user_reminder['participants'])
                                    ||
                                    // or there is a specified list of participants who should be participants and they all are participants
                                    isset($reminder_events_assignments[$reminder_event['id']]['mine'])
                                    && count($event_type_user_reminder['participants']) == count(array_intersect($reminder_events_assignments[$reminder_event['id']]['mine'], $event_type_user_reminder['participants']))))) {
                        // Skip this reminder if it's already done
                        if (isset($events_users_reminders_history[$reminder_event['id']][$event_type_user_id][$reminder_type])) {
                            foreach ($events_users_reminders_history[$reminder_event['id']][$event_type_user_id][$reminder_type] as $reminder_sent) {
                                if ($reminder_date <= $reminder_sent && $reminder_sent <= $reminder_event['event_start']) {
                                    continue 2;
                                }
                            }
                        }
                        // Add record like it comes from the DB
                        $reminder_event['user_id'] = $event_type_user_id;
                        $reminder_event['user_name'] = $users_reminders[$event_type_user_id]['user_name'];
                        $reminder_event['user_active'] = $users_reminders[$event_type_user_id]['user_active'];
                        $reminder_event['user_email'] = $users_reminders[$event_type_user_id]['user_email'];
                        if ($reminder_type == 'toaster') {
                            $reminder_event['event_type'] = $reminder_event['type'];
                            // TODO: These might be needed
                            // ei18n.*,
                            // ea.access,
                            // ea.ownership
                        }
                        $reminder_event['reminder_type'] = $event_type_user_reminder['reminders_types'];
                        // the 'type' param here is correct (it's the type of the event) but everywhere else the code gets it wrong - the 'type' is the reminder type
                        // next line can simulate this wrong functionality
                        // $reminder_event['type'] = $event_type_user_reminder['reminders_types'];
                        $reminder_event['recurrence_date'] = '0000-00-00';
                        $reminder_event['date'] = '';
                        $reminder_event['offset'] = $event_type_user_reminder['minutes'];
                        $reminder_event['offset_from'] = 'start';
                        $reminder_event['repeats'] = '1';
                        $reminder_event['repeat_interval'] = '0';
                        $reminder_event['crontab_last_sent'] = '0000-00-00 00:00:00';
                        $reminder_event['crontab_times_sent'] = '0';
                        $reminder_event['toaster_last_sent'] = '0000-00-00 00:00:00';
                        $reminder_event['toaster_times_sent'] = '0';
                        $reminder_event['custom_message'] = '';
                        $reminder_event['reminder_source'] = 'personal_settings';
                        $records[] = $reminder_event;
                    }
                }
            }
        }

        return $records;
    }

   /**
    * Gets info for model of the reminder (document, project, task)
    *
    * @param object $registry - the main registry
    * @param array $reminder - reminder data
    * @return array - array with results for found model
    */
    public static function getParentInfo(&$registry, $reminder = array()) {
        $result = array();
        if ($reminder['id']) {
            $query = 'SELECT * FROM ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                     'WHERE parent_id=' . $reminder['id'] . "\n" .
                     '  AND link_type="parent"';
            $relative = $registry['db']->GetRow($query);
            if (!empty($relative['origin']) && in_array($relative['origin'], array('document', 'project', 'task', 'contract', 'nomenclature'))) {
                $table = constant('DB_TABLE_' . strtoupper($relative['origin']) . 'S');
                $table_i18n = constant('DB_TABLE_' . strtoupper($relative['origin']) . 'S_I18N');
                $query = 'SELECT t.*, ti18n.*, tti18n.name AS type_name' . "\n" .
                         'FROM ' . $table . ' AS t' . "\n" .
                         'LEFT JOIN ' . $table_i18n . ' AS ti18n ' . "\n" .
                         '  ON t.id=ti18n.parent_id AND ti18n.lang="' . $registry['lang'] . '"' . "\n" .
                         'LEFT JOIN ' . constant('DB_TABLE_' . strtoupper(General::singular2plural($relative['origin'])) . '_TYPES_I18N') . ' AS tti18n' . "\n" .
                         '  ON t.type=tti18n.parent_id AND tti18n.lang = \'' . $registry['lang'] . '\'' . "\n" .
                         'WHERE id=' . $relative['link_to'];
                $result = $registry['db']->GetRow($query);
                if ($result) {
                    $result['module'] = General::singular2plural($relative['origin']);
                    $result['model_name'] = $registry['translater']->translate('lr_model_' . $relative['origin']);
                }
            }
        }

        return $result;
    }

    /**
     * Function that searches for reminders for a record
     *
     * @param object $registry - the main registry
     * @param array $filters - filter parameters
     * @return array - array with results
     */
    public static function searchReminders(&$registry, $filters = array()) {
        //set interface lang filter
        $lang = $registry['lang'];

        //set model lang filter
        if (!empty($filters['model_lang'])) {
            $model_lang = $filters['model_lang'];
        } else {
            //default model language is the interface language
            $model_lang = $registry['lang'];
        }

        $limit = '';
        if (isset($filters['paginated']) && $filters['paginated'] && empty($filters['remind_event_id'])) {
            if ($registry['currentUser']->getPersonalSettings('interface', 'list_reminders')) {
                $default_rpp = $registry['currentUser']->getPersonalSettings('interface', 'list_reminders');
            } else {
                $default_rpp = 10;
            }
            $resultsPerPage = (!empty($filters['display']) ? $filters['display'] : $default_rpp);
            $page = (!empty($filters['page']) ? $filters['page'] : 1);

            $offset = ($page - 1) * $resultsPerPage;
            $limit = sprintf("%d, %d", $offset, $resultsPerPage);
        }

        if (!empty($filters['sort'])) {
            $sorted_by = implode(',', $filters['sort']);
        } else {
            $sorted_by = 'e.added DESC';
        }

        // prepare the query
        $sql = array();
        $sql['select'] = 'SELECT e.id as idx, e.id as reminder_event_id, er.type, er.date, er.custom_message, CONCAT(ui18n.firstname, " ", ui18n.lastname) as added_by_name, e.added as date_added, e.added_by as added_by' . "\n";
        $sql['from']   = 'FROM ' .  DB_TABLE_EVENTS . ' as e' . "\n" .
                         'INNER JOIN ' . DB_TABLE_EVENTS_TYPES . ' as et' .  "\n" .
                         '  ON (e.type=et.id AND et.keyword="reminder" AND et.active=1 AND et.deleted=0)' . "\n" .
                         'INNER JOIN ' . DB_TABLE_EVENTS_RELATIVES . ' as el' . "\n" .
                         '  ON (el.origin="' . $filters['model_name'] . '" AND el.link_to="' . $filters['model_id'] . '" AND el.link_type="parent" AND el.parent_id=e.id)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_EVENTS_REMINDERS . ' as er' . "\n" .
                         '  ON (el.parent_id=er.parent_id)' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                         '  ON (ui18n.parent_id=e.added_by AND ui18n.lang="' . $model_lang . '")' . "\n";

        $where = array();
        $where[] = '(er.user_id="' . $filters['user_id'] . '" OR e.added_by=' . $filters['user_id'] . ')';
        $where[] = 'e.deleted_by=0';
        if (!empty($filters['remind_event_id'])) {
            $where[] = 'e.id="' . $filters['remind_event_id'] . '"';
        }

        $sql['where']  = 'WHERE ' . implode(' AND ', $where) . "\n";

        $sql['group'] = 'GROUP BY e.id';
        $sql['sort']  = 'ORDER BY ' . $sorted_by;
        $sql['limit'] = (!empty($limit)) ? 'LIMIT ' . $limit . "\n" : '';

        $query = implode("\n", $sql);
        $records = $registry['db']->GetAssoc($query);

        $final_records = array();

        $events_reminders_ids = array_keys($records);
        if (!empty($events_reminders_ids)) {
            $query_assignments = 'SELECT ea.participant_id as user_id, ea.parent_id as parent_event, CONCAT(ui18n.firstname, " ", ui18n.lastname) as user_name ' . "\n" .
                                 'FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' AS ea' . "\n" .
                                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                                 '  ON (ui18n.parent_id=ea.participant_id AND ui18n.lang="' . $model_lang . '")' . "\n" .
                                 'WHERE ea.parent_id IN (' . implode(',', $events_reminders_ids) . ') AND ea.participant_type="user"';
            $assignments = $registry['db']->GetAll($query_assignments);

            $assignments_by_event = array();
            foreach ($assignments as $assign) {
                if (!isset($assignments_by_event[$assign['parent_event']])) {
                    $assignments_by_event[$assign['parent_event']] = array();
                }
                $assignments_by_event[$assign['parent_event']][$assign['user_id']] = $assign['user_name'];
            }
        }

        foreach ($records as $rec_key => $rec) {
            if (isset($assignments_by_event[$rec['reminder_event_id']])) {
                $current_assigned = $assignments_by_event[$rec['reminder_event_id']];
            } else {
                $current_assigned = array();
            }
            $records[$rec_key]['assignments'] = $current_assigned;
        }

        $final_records = array_values($records);

        if (!empty($filters['paginated']) && $filters['paginated']) {
            $total_query = array();
            $total_query['select'] = 'SELECT e.id AS total';
            $total_query['from'] = $sql['from'];
            $total_query['where'] = $sql['where'];
            $total_query['group'] = $sql['group'];

            $query_total = implode("\n", $total_query);
            $all_ids = $registry['db']->GetCol($query_total);
            $total = count($all_ids);

            //construct pagination
            $pagination = array(
                'page'          => $page,
                'pages'         => ceil($total/$resultsPerPage),
                'found'         => count($final_records),
                'total'         => $total,
                'rpp'           => $resultsPerPage,
                'start'         => $offset,
                'session_param' => 'reminds_ajax_' . $filters['model_name'] . 's_' . $filters['model_id']
            );

            $results = array($final_records, $pagination);
        } else {
            $results = $final_records;
        }

        return $results;
    }

    /**
     * Sorts array of events by their start day
     *
     * @param array $a - previous event to compare
     * @param array $b - next event to compare
     * @return int - result of the comparison
     */
    public static function sortEventsByStartDate($a, $b) {
        if ($a['start'] == $b['start']) {
            return ($a['end'] > $b['end']) ? -1 : 1;
        } else {
            return ($a['start'] < $b['start']) ? -1 : 1;
        }
    }

    /**
     * Sorts array of events by their start day
     *
     * @param array $a - previous event to compare
     * @param array $b - next event to compare
     * @return int - result of the comparison
     */
    public static function sortMonthEventsByStartDate($a, $b) {
        if ($a['event_start'] == $b['event_start']) {
            if ($a['event_end'] == $b['event_end']) {
                // display event with greater id first to keep default sorting by id desc
                return ($a['id'] > $b['id']) ? -1 : 1;
            } else {
                return ($a['event_end'] > $b['event_end']) ? -1 : 1;
            }
        } else {
            return ($a['event_start'] < $b['event_start']) ? -1 : 1;
        }
    }

    /**
     * Adds multiple models
     *
     * @param object $registry - the main registry
     * @param array $vars - multiadd variables
     * @param Events_Controller $controller - controller object that method is called from
     * @return bool - result of operation
     */
    public static function multiAdd(&$registry, $vars, $controller) {
        $db = $registry['db'];
        $request = $registry['request'];
        //first variable for check rows
        $first_var_name = $vars[0]['name'];
        //start number models for adding
        $num_rows = 1;
        if ($request->isPost()) {
            $actionCompleted = true;
            $first_var = $request->get($first_var_name);
            $num_rows = count($first_var);

            //taking type's info
            $type = '';
            if ($registry['event_type']) {
                $type = $registry['event_type'];
            } elseif ($registry['request']->get('type')) {
                require_once 'events.types.factory.php';
                $type_id = $registry['request']->get('type');
                $filters = array('where' => array('et.id = ' . $type_id,
                                                  'et.active = 1',
                                                  'et.keyword NOT IN (\'reminder\', \'plannedtime\')'),
                                 'sanitize' => true);
                $type = Events_Types::searchOne($registry, $filters);
            }

            $db->StartTrans();
            $row_counter = 0;
            $old_event = new Event($registry);
            $old_event->sanitize();
            //build the model from the POST
            foreach ($first_var as $i => $value) {
                $row_counter++;
                $events[$i] = Events::buildModelIndex($registry, $i);
                if (!empty($type)) {
                    $events[$i]->set('type_keyword', $type->get('keyword'), true);
                    $events[$i]->set('type_name', $type->get('name'), true);
                }
                //save main variables
                $add_vars = array();
                self::multiSave($request, $actionCompleted, $events[$i], $i, $add_vars, $row_counter);
                if ($actionCompleted) {
                    $event = clone $events[$i];
                    $filters = array('where' => array('e.id = ' . $event->get('id')),
                                     'model_lang' => $registry->get('model_lang'));
                    $new_event = Events::searchOne($registry, $filters);
                    $audit_parent = Events_History::saveData($registry, array('model' => $event, 'action_type' => 'multiadd', 'new_model' => $new_event, 'old_model' => $old_event));
                }
            }

            if ($actionCompleted) {
            //the models were successfully saved
                $db->CompleteTrans();
                return true;
            } else {
            //some error occurred
                $db->FailTrans();
                foreach ($first_var as $i => $value) {
                    foreach ($vars as $k => $var) {
                        if (in_array($var['name'], array('customer', 'trademark', 'project'))) {
                            $vars[$k]['value'] = $events[$i]->get($var['name']);
                            $vars[$k]['value_autocomplete'] = $events[$i]->get($var['name'] . '_autocomplete');
                        } else {
                            if ($vars[$k]['type'] == 'autocompleter') {
                                $vars[$k]['value'] = $events[$i]->get($var['name']);
                            } else {
                                $vars[$k]['val'] = $events[$i]->get($var['name']);
                            }
                        }
                    }

                    $events[$i]->set('multivars', $vars, true);
                    $events[$i]->sanitize();
                }
                $registry->set('events', $events);
                return false;
            }

        } else {
            //create empty models
            for ($i = 0; $i < $num_rows ; $i++) {
                $events[$i] = Events::buildModel($registry);
                $events[$i]->set('multivars', $vars, true);
                $events[$i]->sanitize();
            }
            $registry->set('events', $events);
        }
    }

    /**
     * Edits multiple models
     *
     * @param object $registry - the main registry
     * @param array $vars - multiedit variables
     * @param string $type - type of all edited models
     * @param Events_Controller $controller - controller object that method is called from
     * @return bool - result of operation
     */
    public static function multiEdit(&$registry, $vars, $type, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];

        //ids of models to edit
        $ids = $request->get('items');
        //check to save models
        $actionCompleted = true;
        $db->StartTrans();
        //build the models from the POST
        $row_counter = 0;
        foreach ($ids as $i => $id) {
            $events[$i] = Events::buildModelIndex($registry, $i);
            $events[$i]->set('id', $id, true);
            $events[$i]->set('type', $type, true);

            $row_counter++;
            $filters = array('where' => array('e.id = ' . $id),
                             'model_lang' => $registry->get('model_lang'));
            $old_event = Events::searchOne($registry, $filters);
            if ($old_event) {
                $events[$i]->set('type_keyword', $old_event->get('type_keyword'), true);
                $events[$i]->set('type_name', $old_event->get('type_name'), true);
            }

            $add_vars = array();
            self::multiSave($request, $actionCompleted, $events[$i], $i, $add_vars, $row_counter);

            if ($actionCompleted) {
                $event = clone $events[$i];
                $filters = array('where' => array('e.id = ' . $id),
                                 'model_lang' => $registry->get('model_lang'));
                $new_event = Events::searchOne($registry, $filters);
                $temp_model = clone $new_event;
                $new_models[] = $temp_model->sanitize();
                $old_model[] = $old_event->sanitize();
                $audit_parent = Events_History::saveData($registry, array('model' => $event, 'action_type' => 'multiedit', 'new_model' => $new_event, 'old_model' => $old_event));

                //send notification
                //$controller->sendNotification($new_event, $audit_parent);
            }
        }

        if ($actionCompleted) {
            //the models were successfully saved
            $db->CompleteTrans();
            //set models for automations
            $controller->old_model = $old_model;
            $controller->new_models = $new_models;
            return true;
        } else {
            //some error occurred
            //show corresponding error(s)
            $db->FailTrans();
            foreach ($ids as $i => $id) {
                foreach ($vars as $k => $var) {
                    if (in_array($var['name'], array('customer', 'trademark', 'project'))) {
                        $vars[$k]['value'] = $events[$i]->get($var['name']);
                        $vars[$k]['value_autocomplete'] = $events[$i]->get($var['name'] . '_autocomplete');
                    } else {
                        if ($vars[$k]['type'] == 'autocompleter') {
                            $vars[$k]['value'] = $events[$i]->get($var['name']);
                        } else {
                            $vars[$k]['val'] = $events[$i]->get($var['name']);
                        }
                    }
                }
                $events[$i]->set('multivars', $vars, true);
                $events[$i]->sanitize();
            }
            $registry->set('events', $events);
            return false;
        }
    }

    /**
     * Saves basic and additional variables for multiple models
     *
     * @param array $request - data from request
     * @param bool $actionCompleted - flag if save was successful
     * @param Event $event - model to save
     * @param int $i - index of model in request (starting from 0)
     * @param array $add_vars - additional variables (module has none)
     * @param int $index - index of model in array of models (starting from 1)
     * @return bool - result of operation
     */
    public static function multiSave(&$request, &$actionCompleted, &$event, $i, $add_vars, $index = 0) {
        $event->index_number = $index;
        if ($event->save()) {
            $event->slashesStrip();
            $actionCompleted = true;
        } else {
            //some error occurred
            $actionCompleted = false;
        }

        return true;
    }

    /**
     * Change status of multiple models
     *
     * @param object $registry - the main registry
     * @param Controller $controller - controller object
     * @return boolean|int - result of operation: false on error, true if all were updated, N if some were updated, -1 if none were updated
     */
    public static function multiStatus(&$registry, $controller) {
        $request = &$registry['request'];

        $status = $request->get('multistatusSelect');
        if (empty($status)) {
            return false;
        }

        //IDs of models to edit
        $ids = $request->get('items');

        $db = &$registry['db'];
        $db->StartTrans();

        $old_model = $new_models = array();
        foreach ($ids as $id) {
            $filters = array('where' => array('e.id = ' . $id),
                             'model_lang' => $registry->get('lang'));
            $old_event = Events::searchOne($registry, $filters);

            $event = Events::searchOne($registry, $filters);
            $event->set('status', $status, true);

            if ($event->setStatus()) {
                $new_event = Events::searchOne($registry, $filters);
                $temp_model = clone $new_event;
                $new_models[] = $temp_model->sanitize();
                $old_model[] = $old_event->sanitize();
                $audit_parent = Events_History::saveData($registry,
                                                         array('model' => $event,
                                                               'action_type' => 'multistatus',
                                                               'new_model' => $new_event,
                                                               'old_model' => $old_event));
            }
            unset($event);
        }

        //set models for automations
        $controller->old_model = $old_model;
        $controller->new_models = $new_models;

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        if ($result && count($ids) > count($new_models)) {
            $result = count($new_models) ?: -1;
        }

        return $result;
    }

    /**
     * gets status of a model
     */
    public static function getEventStatus(&$registry, $id) {
        $db = $registry['db'];
        $query = 'SELECT status FROM ' . DB_TABLE_EVENTS .
                 ' WHERE id="' . $id . '"';
        $stuses = $db->GetAll($query);
        $current_status = $stuses[0]['status'];

        return $current_status;
    }

    /**
     * Set assignments from database
     *
     * @param object $registry - main registry
     * @param array $records - arrays of event data
     * @return array - arrays of event data with set assignments
     */
    public static function setAssignments(&$registry, $records) {

        if (!$records) {
            return $records;
        }

        $db = $registry['db'];
        $lang = $registry['lang'];

        $ids = array();
        foreach ($records as $key => $record) {
            // if event is recurrent, it may be present in records array more than once
            if (!isset($ids[$record['id']])) {
                $ids[$record['id']] = array();
            }
            $ids[$record['id']][] = $key;
            // initialize arrays
            $records[$key]['customers_participants'] =
            $records[$key]['users_participants'] =
            $records[$key]['users_edit'] =
            $records[$key]['users_view'] =
            $records[$key]['users_assignments'] = array();
        }

        $query = 'SELECT ea.*, ' . "\n" .
                 '  CONCAT(ci18n.name, " ", ci18n.lastname) AS assigned_to_name ' . "\n" .
                 'FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' AS ea ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                 '  ON (ea.participant_id=ci18n.parent_id AND ci18n.lang="' . $lang . '")' . "\n" .
                 'WHERE ea.parent_id IN (' . implode(', ', array_keys($ids)) . ') AND ea.participant_type = "customer"' . "\n" .
                 'ORDER BY assigned_to_name ASC';
        $customers_participants = $registry['db']->GetAll($query);

        foreach ($customers_participants as $cp) {
            foreach ($ids[$cp['parent_id']] as $evt_idx) {
                $records[$evt_idx]['customers_participants'][$cp['participant_id']] = $cp;
            }
        }

        $query = 'SELECT ea.*, ' . "\n" .
                 '  CONCAT(ui18n1.firstname, " ", ui18n1.lastname) AS assigned_to_name, ' . "\n" .
                 '  u.email, u.is_portal, u.active ' . "\n" .
                 'FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . ' AS ea ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n1' . "\n" .
                 '  ON (ea.participant_id=ui18n1.parent_id AND ui18n1.lang="' . $lang . '")' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS . ' AS u' . "\n" .
                 '  ON (ea.participant_id=u.id)' . "\n" .
                 'WHERE ea.parent_id IN (' . implode(', ', array_keys($ids)) . ') AND ea.participant_type = "user"' . "\n" .
                 'ORDER BY ' . ($registry->isRegistered('currentUser') ? 'ea.participant_id!=' . $registry['currentUser']->get('id') . ', ' : '') . 'u.is_portal ASC, assigned_to_name ASC';
        $assignments = $registry['db']->GetAll($query);

        foreach ($assignments as $assignment) {
            foreach ($ids[$assignment['parent_id']] as $evt_idx) {
                $evt = &$records[$evt_idx];
                $evt['users_assignments'][$assignment['participant_id']] = $assignment;
                if ($assignment['ownership'] == 'mine') {
                    $evt['users_participants'][] = $assignment;
                    if ($assignment['access'] == 'edit') {
                        $evt['users_edit'][] = $assignment;
                    }
                } else {
                    if ($assignment['access'] == 'edit') {
                        $evt['users_edit'][] = $assignment;
                    } else {
                        $evt['users_view'][] = $assignment;
                    }
                }
            }
        }

        return $records;
    }

    /**
     * Update user status in assignments
     *
     * @param object $registry - main registry with global variables
     * @param array $user_status - associative array for events_id => status
     * @param user $user_id - user id (participant_id) default is 0, in this case the current user id is used
     */
    public static function updateInvitations(&$registry, $user_status, $user_id = 0) {
        $db = $registry['db'];

        if (empty($user_id)) {
            //set default user id of the current user
            $user_id = $registry['currentUser']->get('id');
        }

        $events = array();
        foreach ($user_status as $parent_id => $status) {
            $query = 'UPDATE ' . DB_TABLE_EVENTS_ASSIGNMENTS .
                     ' SET user_status="' . $status . '", status_date=now() WHERE parent_id=' . $parent_id
                     . ' AND participant_type="user" AND participant_id=' . $user_id;
            $db->Execute($query);
            $events[] = $parent_id;

            //set access to view for denied users
            //removed set access to view for denied users
            /*if ($status == 'denied') {
                $query2 = 'SELECT * FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                          'WHERE parent_id=' . $parent_id . "\n" .
                          'AND participant_id ="' . $user_id . '"' . "\n" .
                          ' AND participant_type ="user"';
                $row = $db->GetRow($query2);
                if ($row && $row['access'] == 'edit') {
                    $query3 = 'UPDATE ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                              'SET access="view" ' . "\n" .
                              'WHERE parent_id=' . $parent_id . "\n" .
                              'AND participant_id ="' . $user_id . '"' . "\n" .
                              ' AND participant_type ="user"';
                    $db->Execute($query3);
                }
            }*/
        }

        if (!empty($events)) {
            $query = 'UPDATE ' . DB_TABLE_EVENTS . ' SET modified = NOW(), modified_by = ' . $user_id . "\n" .
                     'WHERE id IN (' . implode(', ', $events) . ')';
            $db->Execute($query);
        }

        return true;
    }

    /**
     * @param Registry $registry
     * @param int $userId
     * @param array $params
     * @return void
     */
    public static function insertNextReminder(Registry $registry, int $userId, array $params = array()): void
    {
        $db = $registry['db'];
        //add new row for recurrence event reminder
        $insert = [];
        $insert['type'] = sprintf("type='%s'", $params['reminder_type']);
        $insert['offset'] = sprintf("offset='%d'", $params['offset']);
        $insert['repeats'] = sprintf("repeats='%d'", $params['repeats']);
        $insert['repeat_interval'] = sprintf("repeat_interval='%d'", $params['repeat_interval']);
        $insert['recurrence_date'] = sprintf("recurrence_date='%s'", $params['event_start']);
        $insert['user_id'] = sprintf("user_id='%s'", $userId);
        $insert['parent_id'] = sprintf("parent_id='%s'", $params['id']);

        $query = 'INSERT IGNORE INTO ' . DB_TABLE_EVENTS_REMINDERS . "\n" .
            'SET ' . implode(', ', $insert) . "\n";
        $db->Execute($query);
    }

    /**
     * Insert/update reminder record
     *
     * @param Registry $registry - main registry with global variables
     * @param int $userId
     * @param array $params - parameters of reminder record
     * @return void
     */
    public static function updateReminders(Registry $registry, int $userId, array $params = array()): void
    {
        $db = $registry['db'];
        if (isset($params['add_row'])) {
            //add new row for recurrence event reminder
            self::insertNextReminder($registry, $userId, $params);
            $params['recurrence_date'] = date('Y-m-d', strtotime($params['event_start']));
        }
        if (isset($params['toaster_last_sent'])) {
            // Update last sent date
            $eventsTbl = DB_TABLE_EVENTS_REMINDERS;
            $whereRecurrence_date = $params['recurrence_date'] !== '0000-00-00' ? " AND recurrence_date='{$params['recurrence_date']}'" : '';
            $query = <<<SQL
                UPDATE  $eventsTbl
                  SET toaster_last_sent=now(), toaster_times_sent=toaster_times_sent+1
                  WHERE
                    parent_id='{$params['id']}' $whereRecurrence_date
                    AND user_id='{$userId}'
                SQL;
            $db->Execute($query);
        }
    }

    /**
     * Save user/customer answer for event participation
     *
     * @return array results - result of the operation
     */
    public static function saveInvitationAnswer(&$registry, $params = array()) {
        $db = $registry['db'];

        //ToDo - check notification, check previous status
        $query1 = 'UPDATE ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                  'SET user_status="' . $params['notification'] . '", ' . "\n" .
                  'status_date=now() ' . "\n" .
                  'WHERE parent_id="' . $params['id'] . '"' . "\n" .
                  'AND md5(participant_id) ="' . $params['p_md5'] . '"' . "\n" .
                  'AND user_status ="pending"' . "\n" .
                  ' AND participant_type ="' . $params['p_type'] . '"';
        $db->Execute($query1);

        $results = array();
        $results['affected_rows'] = $db->Affected_Rows();
        $results['sql'] = !$db->ErrorMsg();
        $results['notification'] = $params['notification'];

        //set access to view for denied users
        if ($params['notification'] == 'denied' && $params['p_type'] == 'user') {
            $query2 = 'SELECT * FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                      'WHERE parent_id="' . $params['id'] . '"' . "\n" .
                      'AND md5(participant_id) ="' . $params['p_md5'] . '"' . "\n" .
                      ' AND participant_type ="' . $params['p_type'] . '"';
            $row = $db->GetRow($query2);
            if ($row && $row['access'] == 'edit') {
                $query3 = 'UPDATE ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                          'SET access="view" ' . "\n" .
                          'WHERE parent_id=' . $params['id'] . "\n" .
                          'AND md5(participant_id) ="' . $params['p_md5'] . '"' . "\n" .
                          ' AND participant_type ="' . $params['p_type'] . '"';
                $db->Execute($query3);
            }
        }

        //get id of participant user/customer (needed when replying invitation from e-mail)
        $query3 = 'SELECT participant_id FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                  'WHERE parent_id="' . $params['id'] . '"' . "\n" .
                  'AND md5(participant_id) ="' . $params['p_md5'] . '"' . "\n" .
                  'AND participant_type ="' . $params['p_type'] . '"';
        $participant_id = $db->getOne($query3);
        if ($participant_id) {
            $results['participant_id'] = $participant_id;
        }

        return $results;
    }

    /**
     * Sets status to finished for expired event(s).
     *
     * @param object $registry - the main registry
     * @param array $ids - array of ids of models
     * @param array $params - parameters to update
     * @return array - results of the operations
     */
    public static function finishExpiredEvents(&$registry, $ids = array(), $params = array()) {
        $db = &$registry['db'];

        $results = array();

        if (empty($ids)) {
            return $results;
        }

        $modified_by = 0;

        foreach ($ids as $id) {
            $query1 = 'SELECT * FROM ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                      'WHERE parent_id=' . $id . "\n" .
                      '  AND md5(participant_id) =\'' . $params['p_md5'] . '\'' . "\n" .
                      '  AND access =\'edit\'' . "\n" .
                      '  AND participant_type =\'user\'';
            $records = $db->GetRow($query1);
            if (!count($records)) {
                unset($ids[array_search($id, $ids)]);
            } else {
                if ($modified_by == 0) {
                   $modified_by = $records['participant_id'];
                }
            }
        }
        // user has edit rights for at least one event
        if (count($ids)) {

            $db->StartTrans();

            //set pending participant assignments to denied
            self::denyPendingParticipations($registry, $ids);

            $where = array();
            $where[] = General::buildClause('id', $ids);
            $where[] = 'status != \'finished\'';

            $set = array();
            $set['status']      = sprintf("status='finished'");
            $set['modified']    = sprintf("modified=now()");
            $set['modified_by'] = sprintf("modified_by=%d", $modified_by);

            $query = 'SELECT id, status, priority FROM ' . DB_TABLE_EVENTS . ' WHERE ' . implode(' AND ', $where);
            $old_data = $db->GetAssoc($query);

            //query to update the main table
            $query = 'UPDATE ' . DB_TABLE_EVENTS . "\n" .
                     'SET ' . implode(', ', $set) . "\n" .
                     'WHERE ' . implode(' AND ', $where);
            $db->Execute($query);

            $results['affected_rows'] = $db->Affected_Rows();
            $results['sql'] = !$db->ErrorMsg();
            $results['already_finished'] = count($ids) - $results['affected_rows'];

            // write history and audit
            $finished_label = General::slashesEscape($registry['translater']->translate('events_status_finished'));
            foreach ($old_data as $id => $data) {
                $query =
                    'INSERT INTO ' . DB_TABLE_EVENTS_HISTORY .
                    ' (model, model_id, h_date, action_type, user_id, data, lang)' . "\n" .
                    'VALUES (\'Event\', \'' . $id . '\', NOW(), \'status\', \'' . $modified_by . '\', \'' .
                    General::slashesEscape(serialize(array(
                        'priority' => $data['priority'],
                        'status' => 'finished',
                        'lang' => $registry['lang']))) . '\', \'' . $registry['lang'] . '\')';
                $db->Execute($query);

                if ($h_id = $db->Insert_Id()) {
                    $query =
                        'INSERT INTO ' . DB_TABLE_EVENTS_AUDIT .
                        ' (parent_id, field_name, field_value, old_value, label, var_type, is_array)' . "\n" .
                        'VALUES (' . $h_id . ', \'status\', \'finished\', \'' .
                        General::slashesEscape($registry['translater']->translate('events_status_' . $data['status'])) .
                        '\', \'' . $finished_label . '\', 1, 0)';
                    $db->Execute($query);
                }
            }

            if ($db->HasFailedTrans()) {
                $results['sql'] = 0;
            }

            $db->CompleteTrans();
        }

        return $results;
    }

    /**
     * Update all participants with pending participation to denied for specified events
     *
     * @param object $registry - the main registry
     * @param array $ids - event ids
     * @return bool - result of the operation
     */
    public static function denyPendingParticipations(&$registry, $ids = array()) {
        if (!$ids) {
            return true;
        }

        $db = &$registry['db'];
        $query2 = 'UPDATE ' . DB_TABLE_EVENTS_ASSIGNMENTS . "\n" .
                  'SET user_status=\'denied\', status_date=NOW()' . "\n" .
                  'WHERE user_status=\'pending\' AND ownership=\'mine\' AND parent_id IN (' . implode(',', $ids) . ')';
        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * Generates pseudo-merged file for multiple models using specified pattern, header and footer
     *
     * @param object $registry - the main registry
     * @param object $controller - controller object
     * @return bool - result of operation
     */
    public static function multiPrint(&$registry, $controller) {
        $db = $registry['db'];
        $request = &$registry['request'];

        $model_lang = $request->isRequested('model_lang') ? $request->get('model_lang') : $registry['lang'];

        //IDs of models to print
        $ids = $request->get('items');

        $db->StartTrans();

        //set time limit and memory limit
        set_time_limit(0);
        ini_set('memory_limit', '2048M');

        //get models
        $filters = array('where' => array('e.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $events = Events::search($registry, $filters);

        //get the specified pattern
        $pattern_id = $request->get('pattern');
        require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
        $filters = array('where' => array('p.id = ' . $pattern_id,
                                          'p.active = 1'),
                         'model_lang' => $model_lang,
                         'sanitize' => true);
        $pattern = Patterns::searchOne($registry, $filters);

        $pattern_modify_dates = array();
        $pattern_modify_dates[] = $pattern->get('modified');
        if ($pattern->get('header') || $pattern->get('footer')) {
            require_once PH_MODULES_DIR . 'patterns/models/patterns.parts.factory.php';
            if ($pattern->get('header')) {
                $filters_header = array(
                    'where'     => array('pp.id ="' . $pattern->get('header') . '"'),
                    'model_lang'=> $registry->get('model_lang'),
                    'sanitize'  => true
                );
                $header = Patterns_Parts::searchOne($registry, $filters_header);
                $pattern_modify_dates[] = $header->get('modified');
            }
            if ($pattern->get('footer')) {
                $filters_footer = array(
                    'where'     => array('pp.id ="' . $pattern->get('footer') . '"'),
                    'model_lang'=> $registry->get('model_lang'),
                    'sanitize'  => true
                );
                $footer = Patterns_Parts::searchOne($registry, $filters_footer);
                $pattern_modify_dates[] = $footer->get('modified');
            }
        }
        rsort($pattern_modify_dates);
        $latest_pattern_modify_date = reset($pattern_modify_dates);

        $query_type_name_plural = 'SELECT name_plural FROM ' . DB_TABLE_EVENTS_TYPES_I18N . "\n" .
                                  'WHERE parent_id=' . $pattern->get('model_type') . ' AND lang="' . $model_lang . '"';
        $type_name_plural = $db->getOne($query_type_name_plural);
        if (!$type_name_plural) {
            $type_name_plural = $registry['translater']->translate(strtolower(General::singular2plural(self::$modelName)));
        }

        //check if already having generated files for models with selected pattern (get latest version)
        $query_files = 'SELECT model_id AS idx, f.*' . "\n" .
                       'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON f.id=fi18n.parent_id AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       'WHERE model="' . self::$modelName . '" AND model_id IN (' . implode(', ', $ids) . ') AND pattern_id=' . $pattern_id . ' AND origin="generated" AND deleted_by=0 AND fi18n.lang="' . $model_lang . '"' . "\n" .
                       '  AND revision=(SELECT MAX(revision) FROM ' . DB_TABLE_FILES . ', ' . DB_TABLE_FILES_I18N . "\n" .
                       '  WHERE id=parent_id AND model="' . self::$modelName . '" AND model_id=f.model_id AND origin="generated" AND deleted_by=0 AND lang="' . $model_lang . '")' . "\n" .
                       'GROUP BY model_id' . "\n" .
                       'ORDER BY model_id ASC';
        $generated_files = $db->getAssoc($query_files);

        $file_ids = array();
        foreach ($events as $event) {
            if ($db->HasFailedTrans()) {
                //something is wrong, do not continue at all
                break;
            }
            //$old_model[] = $event;
            if (!array_key_exists($event->get('id'), $generated_files) ||
                $generated_files[$event->get('id')]['added'] < $latest_pattern_modify_date ||
                (array_key_exists($event->get('id'), $generated_files) && !file_exists($generated_files[$event->get('id')]['path']))) {
                //registry needed for operations with model
                $event->unsanitize();

                $patterns_vars = $event->getPatternsVars();
                $event->extender = new Extender();
                $event->extender->model_lang = $model_lang;
                $event->extender->module = 'Events';
                foreach ($patterns_vars as $key => $value) {
                    $event->extender->add($key, $value);
                }

                //generate file with selected pattern
                if ($file_id = $event->generatePDF()) {
                    //save history
                    require_once(PH_MODULES_DIR . 'events/models/events.history.php');
                    Events_History::saveData($registry,
                                             array('action_type' => 'print',
                                                   'model' => $event,
                                                   'pattern' => $pattern->get('id'),
                                                   'generated_file' => $file_id
                                             ));
                    $file_ids[$event->get('id')] = $file_id;
                }
                $event->sanitize();

            } else {
                $file_ids[$event->get('id')] = $generated_files[$event->get('id')]['id'];
            }

            //$temp_model = clone $event;
            //$new_models[] = $temp_model->sanitize();
            //unset($temp_model);
        }

        //set models for automations
        //$controller->old_model = $old_model;
        //$controller->new_models = $new_models;

        if ($file_ids) {
            $query_files = 'SELECT path' . "\n" .
                           '  FROM ' . DB_TABLE_FILES . "\n" .
                           '  WHERE id IN (' . implode(', ', $file_ids) . ')';
            $files = $db->getCol($query_files);

            //prepare the page properties
            if ($pattern->get('landscape')) {
                $page_orientation = 'L';
                $page_format_width = 841.82;
                $page_format_height = 598.63;
            } else {
                $page_orientation = 'P';
                $page_format_width = 598.63;
                $page_format_height = 841.82;
            }
            $page_format = array($page_format_width, $page_format_height);

            //merge files and send result file to browser
            $pdf = new Pdf_Merge($page_orientation, 'pt', $page_format);
            $pdf->setFiles($files);
            $pdf->concat();
            //remove any notices or warnings
            ob_clean();
            $pdf->Output_fdpf(sprintf('%s_%s_multiprint.pdf',
                                      General::strftime($registry['translater']->translate('date_short')),
                                      preg_replace('#\s#', '_', Transliterate::convert($type_name_plural))), 'D');

            //write lightweight history
            $current_user_id = $registry['currentUser']->get('id');
            $query_history = 'INSERT INTO ' . DB_TABLE_EVENTS_HISTORY . ' (model, model_id, h_date, action_type, user_id, data, lang) VALUES' . "\n";
            $history_records = array();
            foreach ($file_ids as $model_id => $file_id) {
                $data = serialize(array('pattern' => $pattern_id, 'generated_file' => $file_id));
                $history_records[] = sprintf('(\'%s\', %d, NOW(), \'multiprint\', %d, \'%s\', \'%s\')',
                                             self::$modelName, $model_id, $current_user_id, $data, $model_lang);
            }
            $query_history .= implode(",\n", $history_records);
            $db->Execute($query_history);

        } else {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }
}

?>
