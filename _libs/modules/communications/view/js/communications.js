/* Functions to manage communications panels */
/* ================= */

/**
 * Function to manage the tabs in communication screen
 */
function manageCommunicationTabs(element, model_id) {
    var parentCell = element.parentNode;
    var links = parentCell.getElementsByTagName('a');

    var active_link = element.id;
    var communication_type = active_link.replace(/^communication_tab_/, '');

    if (!communication_type) {
        communication_type = 'all';
    }
    var addpanel_id = communication_type.replace(/s$/, '');
    if (addpanel_id == 'all') {
        addpanel_id = 'comment';
    }

    for (var i = 0; i < links.length; i++) {
        if (links[i].id != active_link) {
            removeClass(links[i], 'communication_tab_selected');
        } else {
            addClass(links[i], 'communication_tab_selected');
            $('title_section_communications').innerHTML = i18n['labels']['title_section_communications_' + communication_type];
        }
    }

    listCommunicationRecords(model_id, communication_type);
    manageCommunicationAddPanels(addpanel_id, 'load_empty', model_id);
}

/**
 * Function to manage the panels to add communication records (emails and comments)
 */
function manageCommunicationAddPanels(selected_add_panel, action, model_id, edit_id, subject, parent_id) {
    nzShowLoading();

    const errorsContainerEl = document.querySelector('#communications_errors');
    const messagesContainerEl = document.querySelector('#communication_messages_container');
    const addPannelEl = document.querySelector('#add_panel');
    const commentTabEl = document.querySelector('#comment_tab');
    const emailTabEl = document.querySelector('#email_tab');

    errorsContainerEl.innerHTML = '';
    if (action !== 'load_empty') {
        messagesContainerEl.innerHTML = '';
    }

    // check for existing add tabs (if no tab exists then the user does not have permissions for it)
    if (selected_add_panel === 'comment') {
        if (!commentTabEl) {
            selected_add_panel = commentTabEl ? 'comment' : '';
        }
    } else if (selected_add_panel === 'email') {
        if (!emailTabEl) {
            selected_add_panel = emailTabEl ? 'email' : '';
        }
    } else if (selected_add_panel === 'minitask') {
    } else {
        selected_add_panel = '';
    }

    if (selected_add_panel === 'minitask') {
        addPannelEl.innerHTML = '';
        nzHideLoading();
        return true;
    }

    const opt = {
        method: 'get',
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                nzHideLoading();
                return;
            }
            let result = !!t.responseText ? eval('(' + t.responseText + ')') : {};
            if (result.messages) {
                errorsContainerEl.innerHTML = result.messages;
                new Effect.ScrollTo('communications_errors');
                nzHideLoading();
                return;
            }

            addPannelEl.innerHTML = result;
            if (subject) {
                addPannelEl.querySelector('#subject').value = subject;
            }

            const parentIdEl = addPannelEl.querySelector('#parent_id');
            if (parent_id !== undefined && parent_id !== null && parentIdEl) {
                parentIdEl.value = parent_id;
            }

            if (CKEDITOR) {
                var instance = CKEDITOR.instances['body'];
                if (instance) {
                    CKEDITOR.remove(instance);
                }
            }

            Nz.loadJSFromHTMLString(addPannelEl.innerHTML);

            if (action !== 'load_empty') {
                new Effect.ScrollTo('add_panel');
            }
            nzHideLoading();
        },
        on404 : function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            nzHideLoading();
        },
        onFailure : function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            nzHideLoading();
        }
    };

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'communications');
    url.searchParams.set('communications', 'ajax_load_communication_add_panel');
    url.searchParams.set('type_record', selected_add_panel);
    url.searchParams.set('action', action);
    url.searchParams.set('model_id', model_id);
    url.searchParams.set('communication_type', selected_add_panel);
    url.searchParams.set('module', env.modelName.toLowerCase() + 's');

    if (selected_add_panel === 'email' && (action === 'reply' || action === 'replyAll')) {
        //add the reply/replyAll with the communication id
        url.searchParams.set(action, parent_id);
    }

    if (edit_id) {
        url.searchParams.set('edit_id', edit_id);
    }

    new Ajax.Request(url.toString(), opt);
}

/**
 * AJAX functionality SAVES the comment/email for communications screen
 * TODO: refactor this function to use fetch instead of Ajax.Request and atach it to ewent without markup!
 */
function saveCommunicationRecord(form, action, model_id, communication_type) {
    nzShowLoading();

    const errorsContainerEl = document.querySelector('#communications_errors');
    const messagesContainerEl = document.querySelector('#communication_messages_container');


    errorsContainerEl.innerHTML = '';
    if (action !== 'load_empty') {
        messagesContainerEl.innerHTML = '';
    }

    let params = Form.serialize(form);

    const urlActions = {
        'comment': 'ajax_save_communication_comment',
        'email': 'ajax_send_communication_email',
    }
    const url_action = urlActions[action];

    if (action === 'email') {
        // set content as taken from the editor's instance
        const oEditor = CKEDITOR.instances['body'];
        const editor_content = oEditor.getData().replace('&nbsp;', '&#160;');

        // find how many recipients the letter will be sent to
        const recipients_inputs = document.querySelector('#table_to input.autocompletebox');
        let count_recipient_mails = 0;
        for (let i = 0; i < recipients_inputs.length; i++) {
            if (recipients_inputs[i].value !== '') {
                count_recipient_mails++;
            }
        }

        // check if the recipients mails are more than one and there are placeholders for recipient
        // If it is like this, alerts the user that the personalization will be removed
        if (editor_content.match(/\[recipient_[^\]]*\]/) && count_recipient_mails > 1) {
            if (!confirm(i18n['messages']['confirm_personalization_remove'])) {
                nzHideLoading();
                return false;
            }
        }
        if (params.match(/&body=/)) {
            params = params.replace(/&body=([^&]*)/, '&body=' + encodeURIComponent(editor_content));
        } else {
            params += '&body=' + escape(editor_content);
        }
    }

    const model = form.querySelector('#model').value.toLowerCase();

    const buttons = document.querySelectorAll('#' + form.id + ' button');
    buttons.forEach((el) => {
        el.classList.add('button_inactive');
        el.disabled = true;
    });

    // function to perform actions on success - it can be passed as optional last parameter
    const success_func = arguments.length > 4 && typeof arguments[4] === 'function' ? arguments[4] : null;

    const opt = {
        method: 'post',
        parameters: params,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                if (action === 'comment') {
                    alert(i18n['messages']['error_comment_not_added']);
                } else if (action === 'email') {
                    alert(i18n['messages']['error_email_not_sent']);
                }
                buttons.forEach((el) => {
                    el.classList.remove('button_inactive');
                    el.disabled = false;
                });
                form.submitted = false;
                nzHideLoading();
                return;
            }

            const result_operation = eval('(' + t.responseText + ')');

            nzHideLoading();

            if (form.closest('.lb_content')) {
                // form is in lightbox

                // show success or error messages
                displayNotificationFixed(result_operation.messages);

                if (result_operation.result) {
                    // prepare parameters
                    result_operation.click_action = 'communications';
                    result_operation.click_additional_params = '&communication_type=' + action + 's';
                    result_operation.mouseenter_function = showCommunicationsInfo;
                    result_operation.mouseenter_function_args = [action + 's'];
                    // update totals in page
                    updateInlineTotals(action + 's', model, model_id, result_operation);

                    // function to perform actions on success instead of default ones
                    if (success_func) {
                        success_func();
                        form.submitted = false;
                        removeResubmitPrevention();
                        return;
                    }

                    lb.deactivate();
                } else {
                    form.submitted = false;
                    buttons.forEach((el) => {
                        el.classList.remove('button_inactive');
                        el.disabled = false;
                    });
                }
            } else {
                // form is in communications action
                if (result_operation.result) {
                    errorsContainerEl.innerHTML = '';

                    listCommunicationRecords(model_id, communication_type);
                    manageCommunicationAddPanels(action, 'load_empty', model_id);
                } else {
                    if (result_operation.messages) {
                        errorsContainerEl.innerHTML = result_operation.messages;
                        new Effect.ScrollTo('communications_errors');
                        setTimeout(nzHeaderHide, 1200);
                    }
                    form.submitted = false;
                    buttons.forEach((el) => {
                        el.classList.remove('button_inactive');
                        el.disabled = false;
                    });
                }
            }
            removeResubmitPrevention();
        },
        on404 : function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            nzHideLoading();
            form.submitted = false;
            buttons.forEach((el) => {
                el.classList.remove('button_inactive');
                el.disabled = false;
            })
            removeResubmitPrevention();
        },
        onFailure : function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            nzHideLoading();
            form.submitted = false;
            buttons.forEach((el) => {
                el.classList.remove('button_inactive');
                el.disabled = false;
            });
            removeResubmitPrevention();
        }
    };

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'communications');
    url.searchParams.set('communications', url_action);
    url.searchParams.set('module', model + 's');

    // set flags when communication is added from lightbox opened from a list
    // of records
    if (form.closest('.lb_content')) {
        url.searchParams.set('inline_add', 1);
        if (document.querySelector(`.history_activity_${model}_${model_id}`).length) {
            url.searchParams.set('history_total', 1);
        }
    }

    new Ajax.Request(url.toString(), opt);
}

/**
 * AJAX functionality LIST the communications of the record
 */
function listCommunicationRecords(model_id, communication_type) {
    nzShowLoading();

    const module = env.modelName.toLowerCase() + 's';
    const communication_panel = document.querySelector('#communications_ajax_' + module + '_' + model_id);
    if (!communication_panel) {
        nzHideLoading();
        return false;
    }

    const opt = {
        method: 'get',
        asynchronous: false,
        onSuccess: function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }

            communication_panel.innerHTML = t.responseText;
            Nz.loadJSFromHTMLString(communication_panel.innerHTML);

            nzHideLoading();
            new Effect.ScrollTo('communication_messages_container');
        },
        on404 : function(t) {
            nzHideLoading();
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure : function(t) {
            nzHideLoading();
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    const url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'communications');
    url.searchParams.set('communications', 'ajax_list_communications');
    url.searchParams.set('model_id', model_id);
    url.searchParams.set('communication_type', communication_type);
    url.searchParams.set('module', module);

    new Ajax.Request(url.toString(), opt);
}

function setCommunicationsBodyVisibilityAndPopout() {
    const container = document.getElementById('communications_container');
    const type = container.dataset.type;
    const expandableTitle = i18n._[`full_${type.substring(0, type.length - 1)}`];

    document.querySelectorAll('.communication_message').forEach((el, i)=> {
        const body = el.querySelector('.communication_message_body');

        // If contents are larger than the container create a popout
        if (el.offsetHeight !== body.offsetHeight) {
            const popout = document.createElement('div');
            popout.id = 'communication_full_' + i;
            popout.classList.add('communication_message_popout');
            popout.innerHTML = body.innerHTML;
            container.appendChild(popout);

            el.title = expandableTitle;
            el.dataset.popoutPosition = "panel: left middle at: left middle -10 0";
            el.dataset.popoutElement = `#${popout.id}`;
            el.dataset.containerAttr = '{"class": "nz-pointer-left-middle nz-modal"}';
            el.classList.add('nz--less', 'nz-popout-trigger', 'nz-popout-autoinit')
        }
    })
}

/**
 * Function to show or hide certain CC mail table
 */
function toggleMailScreenTables(element, tabSelector) {
    const table_row = document.querySelector(tabSelector);
    var table_mail_copy = $(element.id.replace(/mail_add_/, 'table_'));

    if (table_row && table_mail_copy) {
        if (element.classList.contains('communication_tab_selected')
                || element.classList.contains('communication_tab_selected_hov')) {
            element.classList.remove('communication_tab_selected', 'communication_tab_selected_hov');
            table_row.style.display = 'none';
            for (var k = 1; k < table_mail_copy.rows.length; k++) {
                disableMailTables(table_mail_copy.id, k);
            }
        } else {
            element.classList.add('communication_tab_selected');
            table_row.style.display = 'table-row';
            for (var j = 1; j < table_mail_copy.rows.length; j++) {
                disableMailTables(table_mail_copy.id, j);
            }
        }
    } else {
        return false;
    }
}

/**
 * Function to disable all elements inside table for sending mails
 */
function disableMailTables(table, num) {
    var tbl = $(table);
    var row = tbl.rows[num];

    var row_innerHTML = row.innerHTML;
    var items = row_innerHTML.match(/id="?[\d\w_]*"?\s/gi);

    for (var i = 0; i < items.length; i++) {
        var field = items[i].replace(/^id="?([\d\w_]*)"?\s$/gi, '$1');

        field = $(field);
        if (!field) {
            continue;
        }
        if (field.disabled || field.tagName == 'A') {
            field.disabled = false;
        } else {
            field.disabled = true;
        }
    }
    //hide the row
    row = tbl.rows[num];
    if (row.className.match(/input_inactive/)) {
        removeClass(row, 'input_inactive');
    } else {
        addClass(row, 'input_inactive');
    }
}

/**
 * Function to change the body of an e-mail
 * in function of the template chosen
 *
 * @param {Object} select - dropdown for selection of email templates
 * @return void
 */
function changeEmailBody(select) {

    var form = select.up('form');
    if (!form) {
        alert(i18n['messages']['error_changing_email_body']);
        return;
    }
    form = $(form);

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
        method: 'get',
        onSuccess: function(t) {
            var content = t.responseText;
            if (!checkAjaxResponse(content)) {
                alert(i18n['messages']['error_changing_email_body']);
                Effect.Fade('loading');
                return;
            }
            var container = CKEDITOR.instances['body'];

            container.setData(content);
            Effect.Fade('loading');
        },
        on404 : function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
        },
        onFailure : function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
        }
    };

    var url = env.base_url + '?' + env.module_param + '=communications&communications=ajax_change_email&ajax_change_email=' + form.down('#model_id').value +
        '&email_content=' + select.value + '&module=' + form.down('#model').value.toLowerCase() + 's';

    new Ajax.Request(url, opt);
}

/**
 *  Send the data from ckeditor to the textarea before posting the comment.
 *
 *  @param {string} elementId - id attribute of the element that will be filled with the data from the CKEditor example: send2TextArea("content");
 */
function send2TexArea(elementId) {
    // check if param elementId is set if not set default 'content'
    elementId = elementId ? elementId : 'content';
    var content = CKEDITOR.instances[elementId].document.getBody().getHtml().split("\n").join("<br>");

    // escaped code highlighting
    var parser = new DOMParser();
    var doc = parser.parseFromString(content, "text/html");

    $(doc.body).select("pre.bgs_code").each(function(match){
        var partContent = match.innerHTML;
        content = content.replace(partContent, prettyPrintOne(partContent));
    });

    $(elementId).value = content;
}

/**
 * if the current page is communication page and the current tab is comments tab this code will execute and will turn the textarea into CKEDITOR with custom configuration.
 */
function initCommentsCKEditor(skinName) {
    var comments_check = $$('#comment_container textarea#content')[0];
    if (comments_check.nodeType === 1) {
        CKEDITOR.replace('content', {
            customConfig: 'comments_config.js',
            enterMode: CKEDITOR.ENTER_BR,
            skin: skinName
        });
    }
}

/**
 * Toggles e-mails for financial persons for customer in To: table of e-mail form
 *
 * @param {object} element - checkbox toggle
 */
function toggleFinancialPersonRecipients(element) {
    var table_to = $('table_to');
    if (!table_to) {
        return;
    }

    Effect.Center('loading');
    Effect.Appear('loading');

    var opt = {
       method: 'get',
       onSuccess: function(t) {
           if (!checkAjaxResponse(t.responseText)) {
               return;
           }
           eval('var recipients = ' + t.responseText + ';');
           if (typeof recipients != 'object') {
               recipients = {};
           }

           // stop any working autocompleter for recipients (to/cc/bcc)
           Element.select('input.autocomplete_customers.working').each(function(ac) {
               var autocomplete = window['params_' + ac.getAttribute('uniqid')];
               cancelAutocompleter(autocomplete);
               $$(autocomplete.scope + ' #suggestions_' + autocomplete.uniqid).each(function(div) {
                   div.innerHTML = '';
                   div.style.display = 'none';
               });
           });

           if (element.checked) {
               var r;
               // find last empty row in table
               for (r = table_to.rows.length - 1; r > 0; r--) {
                   var ac = table_to.rows[r].select('input.autocomplete_customers')[0];
                   // break on first non-empty, not deleted row
                   if (ac.value && table_to.rows[r].style.display == '') {
                       break;
                   }
                   ac.value = '';
               }
               r++;

               for (var recipient in recipients) {
                   recipients[recipient].each(function(el) {
                       var val = recipient + ' <' + el['label'] + '>';

                       // check if recipient is already loaded
                       var found = false;
                       table_to.select('input.autocomplete_customers').each(function(ac) {
                           if (ac.value == val && ac.parentNode.parentNode.style.display != 'none') {
                               if (ac.parentNode.parentNode.className.match(/input_inactive/)) {
                                   disableField('table_to', ac.parentNode.parentNode.rowIndex);
                               }
                               // highlight all occurrences - do not break immediately
                               new Effect.Highlight(ac, {duration: 1, keepBackgroundImage: 1, startcolor: '#C8FFBF', endcolor: '#C8FFBF'});
                               found = true;
                           }
                       });
                       if (found) {
                           return;
                       }

                       if (table_to.rows.length < r + 1) {
                           addField('table_to', false, false, true);
                       } else {
                           // process row to be reused
                           table_to.rows[r].style.display = '';
                           if (table_to.rows[r].className.match(/input_inactive/)) {
                               disableField('table_to', r);
                           };
                       }

                       var li_data = document.createElement('input');
                       li_data.type = 'hidden';
                       li_data.id = 'customers_' + r + '_data';
                       li_data.value = (typeof JSON != 'undefined') ? JSON.stringify({
                           row: r,
                           '$customer_email': val
                       }) : '{"row":' + r + ',"$customer_email":"' + val + '"}';

                       var li = document.createElement('div');
                       li.id = 'customers_' + r;
                       li.appendChild(li_data);
                       var ul = document.createElement('div');
                       ul.appendChild(li);

                       var uniqid = $('customer_email_' + r).getAttribute('uniqid');

                       var suggestions_div = $('suggestions_' + uniqid);
                       suggestions_div.innerHTML = '';
                       suggestions_div.style.display = 'none';
                       suggestions_div.appendChild(ul);

                       selectAutocompleteItems(li, window['params_' + uniqid]);
                       new Effect.Highlight($('customer_email_' + r), {duration: 1, keepBackgroundImage: 1, startcolor: '#C8FFBF', endcolor: '#C8FFBF'});

                       r++;
                   });
               }

               // define if delete button in last row should be visible
               var visible_rows = 0;
               table_to.select('tr').each(function(el) {
                   if (el.style.display != 'none') {
                       visible_rows++;
                   }
               });
               if (visible_rows > 2) {
                   table_to.rows[table_to.rows.length - 1].select('img.hide_row').each(function(el) {
                       el.style.visibility = 'visible';
                   });
               }
           } else {
               for (var recipient in recipients) {
                   recipients[recipient].each(function(el) {
                       var val = recipient + ' <' + el['label'] + '>';

                       table_to.select('input.autocomplete_customers').each(function(ac) {
                           if (ac.value == val && ac.parentNode.parentNode.style.display != 'none') {
                               hideField('table_to', ac.parentNode.parentNode.rowIndex);
                           }
                       });
                   });
               }

               // there should always be at least one visible row
               if (table_to.rows.length == 2 && table_to.rows[1].style.display == 'none') {
                   table_to.rows[1].select('input.autocomplete_customers').each(function(ac) {
                       ac.value = '';
                   });
                   if (table_to.rows[1].className.match(/input_inactive/)) {
                       disableField('table_to', 1);
                   }
                   table_to.rows[1].style.display = '';
               }
           }

           Effect.Fade('loading');
       },
       on404 : function(t) {
           alert('Error 404: location "' + t.statusText + '" was not found.');
       },
       onFailure : function(t) {
           alert('Error ' + t.status + ' -- ' + t.statusText);
       }
    };

    var url = env.base_url + '?' + env.module_param + '=customers&customers=ajax_get_all_emails';
    url += '&ajax_get_all_emails=' + (element.value || 0) + '&financial_persons=1';

    new Ajax.Request(url, opt);
}

/**
 * Function to prepare the menu for resending e-mails
 *
 * @param {object} element          - image element that triggers the menu
 * @param integer model_id          - the id of the model
 * @param string communication_type - the type of the communication record
 * @param integer communication_id  - the type of the communication record
 */
function openResendMenu(element, model_id, module, communication_type, communication_id, dashlet_id) {
    Effect.Center('loading');
    Effect.Appear('loading');

    var div_id = 'resend_panel_' + communication_type + '_' + communication_id;
    var td = element.parentNode;

    if (!$(div_id)) {
        var opt = {
            method: 'get',
            asynchronous: false,
            onSuccess: function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    Effect.Fade('loading');
                    return false;
                }

                if ($('resend_options')) {
                    closeCommunicationsPanel();
                }

                var div = document.createElement('div');
                div.id = 'resend_options';
                div.innerHTML = t.responseText;
                div.classList.add('communications_resend');
                div.classList.add('nz-elevation--z3');
                const current_document_width = document.documentElement.offsetWidth;
                td.appendChild(div);
                if (div.offsetLeft + div.offsetWidth > current_document_width) {
                    div.style.left = (current_document_width - div.offsetWidth) + 'px';
                }

                Effect.Fade('loading');
            },
            on404 : function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
            },
            onFailure : function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
            }
        };

        var url = env.base_url + '?' + env.module_param + '=communications&communications=ajax_prepare_resend' +
                  '&model_id=' + model_id +
                  '&communication_type=' + communication_type +
                  '&communication_module=' + module +
                  '&communication_id=' + communication_id +
                  '&dashlet_id=' + dashlet_id;

        new Ajax.Request(url, opt);
    }
}

/*
 * Function to close the communication resend panel
 */
function closeCommunicationsPanel() {
    if ($('resend_options')) {
        $('resend_options').parentNode.removeChild($('resend_options'));
    }
}

/*
 * Function to resend the emails
 */
function resendEmails(model_id) {
    let completed_checkboxes = document.querySelectorAll('input:checked[type="checkbox"][id^="resend_id_"]');
    if (completed_checkboxes.length === 0) {
        Nz.alert(i18n._.resend_email, i18n.messages.error_communications_complete_resend_recipients);
        return;
    }

    let url = new URL(env.base_host + env.base_url);
    url.searchParams.set(env.module_param, 'communications');
    url.searchParams.set('communications', 'ajax_resend');
    completed_checkboxes.forEach((el) => {
        url.searchParams.set('resend_mail_ids[]', el.value);
    });

    NzPopout.closeAll();

    fetch(url)
        .then((response) => response.json())
        .then((data) => {
            if (env.module_name !== 'index') {
                listCommunicationRecords(model_id, 'emails');
            } else {
                const container = $('content_dashlet_' + model_id);
                container.innerHTML = '';
                dashletsLoad(container, 'plugin', 'undelivered_mails', model_id);
            }
            Nz.alert(i18n._.resend_email, data);
        })
        .catch((e) => {
            Nz.alert(i18n._.resend_email, i18n.messages.error_email_not_sent);
        })
        .finally(() => {
            nzHideLoading();
        });
}
