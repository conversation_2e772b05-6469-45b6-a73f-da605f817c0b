<?php

/**
 * Outlooks model class
 */
Class Outlook extends Model {
    public $modelName = 'Outlook';

    /**
     * The number of the static columns in a template
     */
    public $staticColumns = 3;

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        if ($this->origin == 'request') {
            if ($this->isDefined('module_controller')) {
                //set module and controller properties of model
                $m_c_array = explode('|', $this->get('module_controller'));
                $this->set('module', $m_c_array[0]);
                $this->set('controller', $m_c_array[1]);
            }
            if ($this->isDefined('type_section')) {
                $type_section_array = explode('_', $this->get('type_section'));
                if ($type_section_array[0] == 'section') {
                    $this->set('section', true, true);
                }
                if (!empty($type_section_array[1])) {
                    $this->set('model_id', $type_section_array[1], true);
                }
            }
        }

        // get name of type or section
        $this->getTypeSectionName();

        if ($this->get('module')) {
            //set module_controller property
            $this->set('module_controller', $this->get('module') . (($this->get('module') != $this->get('controller')) ? '_' . $this->get('controller') : ''), true);

            //set model property
            $m_c_words = explode('_', General::plural2singular($this->get('module_controller')));
            foreach ($m_c_words as $k => $v) {
                $m_c_words[$k] = ucfirst($v);
            }
            $this->set('model', implode('_', $m_c_words), true);

            //set model name from translation files
            $this->set('model_name', $this->i18n('outlooks_module_' . $this->get('module_controller')), true);
        }

        //additional custom settings
        if ($this->origin == 'database') {
            $this->getOutlookSettings();
            $this->getAssignments();
        } elseif ($this->origin == 'request') {
            $this->getRequestOutlookSettings();
            $this->getAssignments('', true);
        }

        if ($this->get('section')) {
            $this->set('section_type_name', $this->i18n('outlooks_section'), true);
        } elseif ($this->get('model_id')) {
            $this->set('section_type_name', $this->i18n('outlooks_type'), true);
        } else {
            $this->set('section_type_name', $this->i18n('outlooks_no_section_type'), true);
        }
    }

    /**
     * Gets name of type or section that outlook is for and sets it to model
     *
     * @return bool - result of the operation
     */
    public function getTypeSectionName() {

        if (!$this->get('model_id')) {
            return $this->set('type_name', '', true);
        }

        $name = '';
        $module_controller = $this->get('module') . ($this->get('module') != $this->get('controller') ? '_' . $this->get('controller') : '');

        if ($this->isSanitized()) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        if ($this->get('section')) {
            $table_name = 'DB_TABLE_' . strtoupper($module_controller) . '_SECTIONS_I18N';
            if (!defined($table_name)) {
                if ($this->get('module') == 'finance') {
                    $table_name = 'DB_TABLE_FINANCE_DOCUMENTS_SECTIONS';
                } else {
                    $table_name = 'DB_TABLE_' . strtoupper($module_controller) . '_SECTIONS';
                }
                $id = 'id';
            } else {
                $id = 'parent_id';
            }

            $query = 'SELECT name FROM ' . constant($table_name) . "\n" .
                     'WHERE ' . $id . ' = \'' . $this->get('model_id') . '\' AND lang = \'' . $this->get('model_lang') . '\'';
            $name = $this->registry['db']->GetOne($query);
        } else {
            $table_name = 'DB_TABLE_' . strtoupper($module_controller) . '_TYPES_I18N';
            if (defined($table_name)) {
                $table_name = constant($table_name);
                $id = 'parent_id';
            } elseif ($this->get('module') == 'finance') {
                if ($this->get('controller') != 'repayment_plans') {
                    $table_name = DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N;
                    $id = 'parent_id';
                }
            } else {
                $table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES');
                $id = 'id';
            }

            if (isset($id)) {
                $query  = 'SELECT name FROM ' . $table_name . "\n" .
                          'WHERE ' . $id . ' = \'' . $this->get('model_id') . '\' AND lang = \'' . $this->get('model_lang') . '\'';
                $name = $this->registry['db']->GetOne($query);
            }

            // also get type keyword
            if ($module_controller == 'nomenclatures') {
                $query = 'SELECT keyword FROM ' . constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES') . ' WHERE id = \'' . $this->get('model_id') . '\'';
                $this->set('type_keyword', $this->registry['db']->GetOne($query), true);
            }
        }

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        return $this->set('type_name', $name, true);
    }

    /**
     * Get outlook's assignments from the DB
     *
     * @param string $assignment_type - not used
     * @param bool $from_post - if true get assignments from POSTed data, otherwised get from DB
     * @return array - array with assignments
     */
    public function getAssignments($assignment_type = '', $from_post = false) {
        // outlook is not saved in db
        if (!$this->get('id') && !$from_post) {
            return array();
        }

        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];
        $records = array();

        //get the roles or users assigned to the outlook
        if ($from_post) {
            if ($this->get('assignments_type') == 'Roles') {
                $query = 'SELECT r.id AS idx, r.id AS assigned_to, ri18n.name AS assigned_to_name, \'Roles\' AS assignments_type' . "\n" .
                         'FROM ' . DB_TABLE_ROLES . ' AS r' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_ROLES_I18N . ' AS ri18n' . "\n" .
                         '  ON r.id=ri18n.parent_id AND ri18n.lang=\'' . $lang . '\'' . "\n" .
                         'WHERE r.id IN (\'' . implode('\', \'', $this->get('roles') ?: array()) . '\') AND r.deleted_by=0';
            } elseif ($this->get('assignments_type') == 'Users') {
                $query = 'SELECT u.id AS idx, u.id AS assigned_to, CONCAT(ui18n.firstname, " ", ui18n.lastname) AS assigned_to_name, \'Users\' AS assignments_type' . "\n" .
                         'FROM ' . DB_TABLE_USERS . ' AS u' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n' . "\n" .
                         '  ON u.id=ui18n.parent_id AND ui18n.lang=\'' . $lang . '\'' . "\n" .
                         'WHERE u.id IN (\'' . implode('\', \'', $this->get('users') ?: array()) . '\') AND u.deleted_by=0';
            } else {
                $records = array(array('assigned_to' => '0', 'assigned_to_name' => '', 'assignments_type' => 'All'));
            }
        } else {
            $query = 'SELECT oa.assigned_to as idx, oa.* ,' . "\n" .
                     '  IF (oa.assignments_type = "Roles", ri18n.name,' . "\n" .
                     '      IF(oa.assignments_type = "Users", CONCAT(ui18n.firstname, " ", ui18n.lastname), NULL)' . "\n" .
                     '  ) as assigned_to_name ' . "\n" .
                     'FROM ' . DB_TABLE_OUTLOOKS_ASSIGNMENTS . ' AS oa ' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_ROLES_I18N . ' AS ri18n ' . "\n" .
                     '  ON oa.assigned_to=ri18n.parent_id AND ri18n.lang = "' . $lang . '"' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n ' . "\n" .
                     '  ON oa.assigned_to=ui18n.parent_id AND ui18n.lang = "' . $lang . '"' . "\n" .
                     'WHERE oa.parent_id= \'' . $this->get('id') . '\'';
        }
        if (!empty($query)) {
            $records = $db->GetAssoc($query);
        }

        if (!$records) {
            return array();
        }

        $this->set('assignments', $records, true);
        $first = reset($records);
        $this->set('assignments_type', $first['assignments_type'], true);
        return $records;
    }

    /**
     * Get outlook's settings from the database
     *
     * @return bool - result of the operation
     */
    public function getOutlookSettings() {
        $lang = $this->registry['lang'];

        if ($this->get ('id')) {
            //for list, search, edit action
            // load needed lang file
            $this->set('module_controller', $this->get('module') . (($this->get('module') != $this->get('controller')) ? '_' . $this->get('controller') : ''), true);
            $lang_file = sprintf('%s%s/i18n/%s/%s.ini', PH_MODULES_DIR, $this->get('module'), $lang, $this->get('module_controller'));
            $this->loadI18NFiles($lang_file);

            // gets the model translated name
            $this->set('model_name', $this->i18n('outlooks_module_' . $this->get('module_controller')), true);

            // gets the model translated section or type name
            if ($this->get('section')) {
                $this->set('section_type_name', $this->i18n('outlooks_section'), true);
            } elseif ($this->get('model_id')) {
                $this->set('section_type_name', $this->i18n('outlooks_type'), true);
            } else {
                $this->set('section_type_name', $this->i18n('outlooks_no_section_type'), true);
            }

            if (!$this->isDefined('skip_settings')) {
                $formatted_fields = $this->getTemplateVars();

                $formatted_fields = $this->prepareModelFields($formatted_fields);

                //sort the fields by position
                usort($formatted_fields, array('self', 'sortFunction'));

                //setting the array containg positions and names to the model
                $this->set('current_custom_fields', $formatted_fields, true);
            }
        } else {
            $this->set('current_custom_fields', array(), true);
        }
    }

    /**
     * Get outlook's settings from the post
     *
     * @return bool - result of the operation
     */
    public function getRequestOutlookSettings() {
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        // the model will have id when in EDIT mode
        // but will not have id in ADD mode
        // in both cases it has positions
        if ($this->get ('id') || $this->get('positions')) {
            //for edit action
            $current_custom_fields = array();

            $positions     = $this->get('positions');
            $origins       = $this->get('origins');
            $labels        = $this->get('labels');
            $field_types   = $this->get('field_types');
            $column_widths = $this->get('column_widths');

            $current_position = 1;
            $type_names = array();

            foreach ($origins as $key_name => $origin) {
                list($name, $model_type) = explode('|', $key_name);
                if ($origins[$key_name] == 'additional' && !array_key_exists($model_type, $type_names)) {
                    $query = 'SELECT name FROM ' . constant('DB_TABLE_' . strtoupper($this->get('module') .
                        ($this->get('module') != $this->get('controller') ? '_' .
                            ($this->get('controller') == 'payments' ? $this->get('controller') : 'documents') : '')) . '_TYPES_I18N') . "\n" .
                             'WHERE parent_id = \'' . $model_type . '\' AND lang = \'' . $lang . '\'';
                    $type_names[$model_type] = $this->registry['db']->GetOne($query);
                }
                $current_custom_fields[$name]['name']         = $name;
                $current_custom_fields[$name]['label']        = $labels[$key_name];
                $current_custom_fields[$name]['position']     = array_key_exists($key_name, $positions) ? $current_position++ : '';
                $current_custom_fields[$name]['origin']       = $origins[$key_name];
                $current_custom_fields[$name]['field_type']   = $field_types[$key_name];
                $current_custom_fields[$name]['column_width'] = $column_widths[$key_name];
                $current_custom_fields[$name]['model_type']   = $model_type;
                $current_custom_fields[$name]['type_name']    = $origins[$key_name] == 'additional' ? $type_names[$model_type] : '';
            }

            // load needed lang file
            $lang_files = array(sprintf('%s%s/i18n/%s/%s.ini', PH_MODULES_DIR, $this->get('module'), $this->get('model_lang'), $this->get('module_controller')));
            $lang_files[] = sprintf('%sassignments/i18n/%s/assignments.ini', PH_MODULES_DIR, $this->get('model_lang'));
            //load extra files for finance documents
            if ($this->get('module') == 'finance') {
                $lang_files[] = sprintf('%sfinance/i18n/%s/%s.ini', PH_MODULES_DIR, $this->get('model_lang'), $this->get('module'));
                $lang_files[] = sprintf('%s%s/gt2.ini', PH_I18N_DIR, $this->get('model_lang'));
            }
            foreach ($lang_files as $lang_file) {
                $this->loadI18NFiles($lang_file);
            }

            $current_custom_fields = $this->prepareModelFields($current_custom_fields);

            $this->set('current_custom_fields', $current_custom_fields, true);
            $this->unsetProperty('positions', true);
            $this->unsetProperty('origins', true);
            $this->unsetProperty('labels', true);
        } else {
            // Entering the ADD menu without POST

            // takes the standard list of fields
            $fields_list = $this->getModelFields();

            //takes the additional vars list
            $additional_vars = $this->getModelAdditionalFields();

            $full_fields_list = array_merge($fields_list, $additional_vars);

            $full_fields_list = array_values($full_fields_list);

            $full_fields_list = $this->prepareModelFields($full_fields_list);

            $this->set('current_custom_fields', $full_fields_list, true);
        }
    }

    /**
     * Gets names from layouts for basic vars and sets them for fields of outlook;
     * Sets additional labels for fields.
     *
     * @param array $formatted_fields - array with fields of outlook
     * @return array - array with fields of outlook after modification
     */
    private function prepareModelFields($formatted_fields) {
        $lang = $this->registry['lang'];
        $model_type = 0;
        //if outlook is for a section, get first model type in section
        if ($this->get('section')) {
            $module = $this->get('module');
            if ($module == 'finance') {
                require_once PH_MODULES_DIR . $module . '/models/' . $module . '.documents_types.factory.php';
                $module_factory_name = ucfirst($module) . '_Documents_Types';
                $filters = array('sanitize' => true,
                                 'where' => array('fdt.model="' . $this->get('model') . '"', 'fdt.type_section="' . $this->get('model_id') . '"'),
                                 'sort' => array('fdt.id ASC'));
            } else {
                require_once PH_MODULES_DIR . $module . '/models/' . $module . '.types.factory.php';
                $module_factory_name = ucfirst($module) . '_Types';
                $alias = $module_factory_name::getAlias($module, 'types');
                $filters = array(
                    'sanitize' => true,
                    'where' => array($alias . '.type_section="' . $this->get('model_id') . '"'),
                    'sort' => array($alias . '.id ASC')
                );
            }
            $first_model_type = $module_factory_name::searchOne($this->registry, $filters);
            if ($first_model_type) {
                $model_type = $first_model_type->get('id');
            }
        } else {
            //get model type from outlook
            $model_type = $this->get('model_id');
        }
        //set the model type id (or if section the first type id)
        $this->set('model_type_id', $model_type, true);

        // list of gt2 fields which label will be defined specially for finance documents
        $gt2_fields = array(
            'total',
            'total_vat_rate',
            'total_vat',
            'total_with_vat',
            'total_no_vat_reason_text'
        );

        if (preg_match('#Finance_((Incomes|Expenses)_Reason|Warehouses_Document|Annulment)#', $this->get('model'))) {
            //some of the GT2 fields are processed as basic variables in finance and warehouses documents
            $query = 'SELECT fm.name, IF(fm.hidden OR NOT fm.outlooks, 1, 0) AS hidden, fi.content AS label' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi' . "\n" .
                     '  ON (fm.id=fi.parent_id AND content_type="label" AND fi.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'WHERE ' . (
                         $model_type ?
                         'model="' . $this->get('model') . '" AND model_type="' . $model_type . '"' :
                         'model="GT2_Sample"'
                     ) . "\n" .
                     '  AND name IN ("' . implode('", "', $gt2_fields) . '")' . "\n";
            $gt2_total_labels = $this->registry['db']->GetAssoc($query);

            //exception for fiscal fields (Bug: 2557)
            foreach (array('total', 'total_vat', 'total_with_vat') as $f) {
                if (!empty($gt2_total_labels[$f])) {
                    $gt2_fields[] = 'fiscal_' . $f;
                    $gt2_total_labels['fiscal_' . $f] = array(
                        'hidden' => $gt2_total_labels[$f]['hidden'],
                        'label' => (!empty($gt2_total_labels[$f]['label']) ?
                                    sprintf('%s (%s)',
                                           $gt2_total_labels[$f]['label'],
                                           $this->registry['translater']->translate('fiscal')) :
                                    '')
                    );
                }
            }
        }

        require_once PH_MODULES_DIR . 'layouts/models/layouts.factory.php';
        //prepare labels and help

        foreach ($formatted_fields as $key => $field) {
            if ($field['label'] == '' || $field['origin'] == 'basic') {
                $label = '';
                $filters = array('model_lang'   => $lang,
                                 'sanitize'     => true,
                                 'sort' => array('l.model_type'),
                                 'where'        => array('l.`system` = 1',
                                                         'l.model = \'' . $this->get('model') . '\'',
                                                         'l.model_type = \'' . $model_type . '\'',
                                                         'l.keyname = \'' . $field['name'] . '\''));
                $layout_info = Layouts::searchOne($this->registry, $filters);

                $module = $this->get('module');
                $module_controller = $this->get('module_controller');

                if (preg_match('/trademark/', $field['name'])) {
                    $lang_var_name = $field['name'];
                    $help_var_name = 'help_' . $field['name'];
                } elseif (preg_match('/owner|responsible|decision|observer/', $field['name'])) {
                    $lang_var_name = 'assignments_assign_' . $field['name'];
                    $help_var_name = '';
                } elseif ($this->get('module') == 'finance') {
                    if (in_array($field['name'], $gt2_fields)) {
                        if (strpos($field['name'], 'fiscal_') === 0) {
                            $lang_var_name = $module_controller . '_' . $field['name'];
                            $help_var_name = 'help_' . $module_controller . '_' . $field['name'];
                        } else {
                            $lang_var_name = 'gt2_' . $field['name'];
                            $help_var_name = 'help_gt2_' . $field['name'];
                        }
                        if (!empty($gt2_total_labels[$field['name']])) {
                            if ($gt2_total_labels[$field['name']]['hidden']) {
                                // hidden field for totals
                                unset($formatted_fields[$key]);
                                continue;
                            }
                            $label = $gt2_total_labels[$field['name']]['label'];
                        }
                    } else {
                        $lang_var_name = $module_controller . '_' . $field['name'];
                        $help_var_name = 'help_' . $module_controller . '_' . $field['name'];
                        //if not in model's .ini file, load labels from main .ini file of module
                        $label = $this->i18n($lang_var_name);
                        if (empty($label)) {
                            $lang_var_name = $module . '_' . $field['name'];
                            $help_var_name = 'help_' . $module . '_' . $field['name'];
                        }
                    }
                } else {
                    $lang_var_name = $module_controller . '_' . $field['name'];
                    $help_var_name = 'help_' . $module_controller . '_' . $field['name'];
                }

                $formatted_fields[$key]['label'] = ($label) ? $label : (($layout_info && $layout_info->get('name')) ?
                    $layout_info->get('name') : $this->i18n($lang_var_name));
                $formatted_fields[$key]['help']  = ($label) ? $label : (($layout_info && $layout_info->get('description')) ?
                    $layout_info->get('description') : ($help_var_name ? $this->i18n($help_var_name) : ''));
            }

            //prepare the additional label
            if ($field['origin'] == 'basic') {
                $formatted_fields[$key]['additional_label'] = $this->i18n('outlooks_basic_var');
            } else {
                $formatted_fields[$key]['additional_label'] = sprintf('%s (%s: %s)',
                    $this->i18n('outlooks_additional_var'),
                    $this->get('model_name') ? $this->get('model_name') : $this->i18n('outlooks_module_' . $this->get('module_controller')),
                    $field['type_name']);
            }
        }

        $matches = $matches2 = array();
        // replace layout names in the [Key/Num] Name fields
        foreach ($formatted_fields as $key => $field) {
            if ($model_type && $field['origin'] == 'basic' &&
            preg_match('/^(((.*)(_))?name)_(code|(full_)?num)$/', $field['name'], $matches) &&
            preg_match('/^(\[.*\]\s)/', $field['label'], $matches2)) {
                $main_field = $matches[3] ?: $matches[1];
                if ($module_controller == 'finance_payments' && $main_field == 'container') {
                    $main_field .= '_id';
                }
                $main_cstm_field = array_filter($formatted_fields,
                    function ($a) use ($main_field) {
                        return $a['origin'] == 'basic' && $a['label'] && $a['name'] == $main_field;
                    }
                );
                if ($main_cstm_field) {
                    $main_cstm_field = reset($main_cstm_field);
                    $formatted_fields[$key]['label'] = $matches2[1] . $main_cstm_field['label'];
                }
            }
        }

        return $formatted_fields;
    }

    /**
     * Get standard fields for outlook
     *
     * @return array - array with columns
     */
    public function getModelFields() {
        $module = $this->get('module');
        $controller = $this->get('controller');
        $module_controller = $this->get('module_controller');

        // get all available basic/system fields for outlook for module
        $query = 'SELECT field_name,' . "\n" .
                 '       IF((depends_on IS NULL OR depends_on = \'\'), field_name, depends_on) as depends_on,' . "\n" .
                 '       standard' . "\n" .
                 'FROM ' . DB_TABLE_MODULES_FIELDS . "\n" .
                 'WHERE module = \'' . $module . '\'' . "\n" .
                 '  AND controller = \'' . $controller. '\'' . "\n" .
                 'ORDER BY standard DESC';
        $db = $this->registry['db'];
        $records = $db->GetAssoc($query);

        if (!$records) {
            return array();
        }

        // load needed lang file for labels/helps
        $lang_files = array(sprintf('%s%s/i18n/%s/%s.ini', PH_MODULES_DIR, $module, $this->get('model_lang'), $module_controller));
        $lang_files[] = sprintf('%sassignments/i18n/%s/assignments.ini', PH_MODULES_DIR, $this->get('model_lang'));
        //load extra files for finance documents
        if ($module == 'finance') {
            $lang_files[] = sprintf('%sfinance/i18n/%s/%s.ini', PH_MODULES_DIR, $this->get('model_lang'), $module);
            $lang_files[] = sprintf('%s%s/gt2.ini', PH_I18N_DIR, $this->get('model_lang'));
        }
        foreach ($lang_files as $lang_file) {
            $this->loadI18NFiles($lang_file);
        }

        $fields_to_exclude = array();

        //some of the basic vars are excluded under some circumstances
        //here are the exceptions
        switch ($module) {
        case 'contracts':
            // if outlook is for one contract type
            if ($this->get('model_id') && !$this->get('section')) {
                //get the contract type settings
                $query = 'SELECT include_date_sign, include_date_start, include_date_validity, include_date_end' . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS_TYPES . "\n" .
                         'WHERE id = \'' . $this->get('model_id') . '\'';
                $contract_type_settings = $this->registry['db']->GetRow($query);

                //prepare array with excluded fields disabled in the contract type
                if (!empty($contract_type_settings)) {
                    if (!$contract_type_settings['include_date_sign']) {
                        $fields_to_exclude[] = 'date_sign';
                    }
                    if (!$contract_type_settings['include_date_start']) {
                        $fields_to_exclude[] = 'date_start';
                    }
                    if (!$contract_type_settings['include_date_validity']) {
                        $fields_to_exclude[] = 'date_validity';
                    }
                    if (!$contract_type_settings['include_date_end']) {
                        $fields_to_exclude[] = 'date_end';
                    }
                }
            }
            break;
        default:
            //do nothing
            break;
        }

        $gt2_fields = array(
            'total',
            'total_vat_rate',
            'total_vat',
            'total_with_vat',
            'total_no_vat_reason_text'
        );
        if ($module == 'finance' && preg_match('#(incomes_reasons|expenses_reasons|warehouses_documents|annulments)#', $controller)) {
            //some of the GT2 fields are processed as basic variables in finance and warehouses documents
            $model_type = 0;
            if ($this->get('model') && $this->get('model_id')) {
                if ($this->get('section')) {
                    require_once PH_MODULES_DIR . 'finance/models/finance.documents_types.factory.php';
                    $module_factory_name = 'Finance_Documents_Types';
                    $filters = array('sanitize' => true,
                                     'where' => array('fdt.model="' . $this->get('model') . '"',
                                                      'fdt.type_section="' . $this->get('model_id') . '"'),
                                     'sort' => array('fdt.id ASC'));
                    $first_model_type = $module_factory_name::searchOne($this->registry, $filters);
                    if ($first_model_type) {
                        $model_type = $first_model_type->get('id');
                    }
                } else {
                    //get model type from outlook
                    $model_type = $this->get('model_id');
                }
            }

            // prepare labels of GT2 totals according to type
            $query = 'SELECT fm.name, IF(fm.hidden OR NOT fm.outlooks, 1, 0) AS hidden, fi.content AS label' . "\n" .
                     'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                     'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fi' . "\n" .
                     '  ON (fm.id=fi.parent_id AND content_type="label" AND fi.lang="' . $this->registry['lang'] . '")' . "\n" .
                     'WHERE ' . (
                         $model_type ?
                         'model="' . $this->get('model') . '" AND model_type="'. $model_type . '"' :
                         'model="GT2_Sample"'
                     ) . "\n" .
                     '  AND name IN ("' . implode('", "', $gt2_fields) . '")' . "\n";
            $gt2_total_labels = $this->registry['db']->GetAssoc($query);

            //exception for fiscal fields (Bug: 2557)
            foreach (array('total', 'total_vat', 'total_with_vat') as $f) {
                if (!empty($gt2_total_labels[$f])) {
                    $gt2_fields[] = 'fiscal_' . $f;
                    $gt2_total_labels['fiscal_' . $f] = array(
                        'hidden' => $gt2_total_labels[$f]['hidden'],
                        'label' => (!empty($gt2_total_labels[$f]['label']) ?
                                   sprintf('%s (%s)',
                                           $gt2_total_labels[$f]['label'],
                                           $this->registry['translater']->translate('fiscal')) :
                                   '')
                    );
                }
            }
        }

        // format fields for display
        $formatted_fields = array();
        $position = 1;
        foreach ($records as $key => $field) {
            $label = '';
            if (preg_match('/trademark/', $key)) {
                $lang_var_name = $key;
                $help_var_name = 'help_' . $key;
            } elseif (preg_match('/owner|responsible|decision|observer/', $key)) {
                $lang_var_name = 'assignments_assign_' . $key;
                $help_var_name = '';
            } else {
                switch ($module) {
                    case 'contracts':
                        //exclude some fields, that are disabled in the contract type
                        if (in_array($field['depends_on'], $fields_to_exclude)) {
                            continue 2;
                        }
                        $lang_var_name = $module_controller . '_' . $key;
                        $help_var_name = 'help_' . $module_controller . '_' . $key;
                        break;
                    case 'finance':
                        if (in_array($key, $gt2_fields)) {
                            if (strpos($key, 'fiscal_') === 0) {
                                $lang_var_name = $module_controller . '_' . $key;
                                $help_var_name = 'help_' . $module_controller . '_' . $key;
                            } else {
                                $lang_var_name = 'gt2_' . $key;
                                $help_var_name = 'help_gt2_' . $key;
                            }
                            if (!empty($gt2_total_labels[$key])) {
                                if ($gt2_total_labels[$key]['hidden']) {
                                    // hidden field for totals
                                    continue 2;
                                }
                                $label = $gt2_total_labels[$key]['label'];
                            }
                        } else {
                            $lang_var_name = $module_controller . '_' . $key;
                            $help_var_name = 'help_' . $module_controller . '_' . $key;
                            //if not in model's .ini file, load labels from main .ini file of module
                            $label = $this->i18n($lang_var_name);
                            if (empty($label)) {
                                $lang_var_name = $module . '_' . $key;
                                $help_var_name = 'help_' . $module . '_' . $key;
                            }
                        }
                        break;
                    default:
                        $lang_var_name = $module_controller . '_' . $key;
                        $help_var_name = 'help_' . $module_controller . '_' . $key;
                        break;
                }
            }
            $formatted_fields[$key]['name']              = $key;
            $formatted_fields[$key]['label']             = ($label) ? $label : $this->i18n($lang_var_name);
            $formatted_fields[$key]['help']              = $help_var_name ? $this->i18n($help_var_name) : '';
            if ($field['standard']) {
                $formatted_fields[$key]['position']      = $position;
                $position ++;
            } else {
                $formatted_fields[$key]['position']      = 0;
            }
            $formatted_fields[$key]['origin']            = 'basic';
            $formatted_fields[$key]['field_type']        = '';
            $formatted_fields[$key]['column_width']      = '';
            $formatted_fields[$key]['type_name']         = '';
            $formatted_fields[$key]['model_type']        = 0;
            $formatted_fields[$key]['depends_on']        = $field['depends_on'];
            $formatted_fields[$key]['additional_label']  = $this->i18n('outlooks_basic_var');
        }

        return $formatted_fields;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {

        if ($this->get('id')) {
            //edit mode
            $action = 'edit';
        } else {
            $action = 'add';
        }

        $this->slashesEscape();
        if ($this->$action()) {
            return true;
        } else {
            $this->slashesStrip();
            return false;
        }
    }

    /**
     * Validate model before save
     *
     * @see Model::validate()
     * @param string $action - current action executed with model
     * @return bool - true if valid, otherwise false
     */
    public function validate($action = '') {

        $at = $this->get('assignments_type');
        $invalid_assignments = array();
        $error = '';

        //check if same model exists
        //the check is here because we dont have unique key in the table as it must contain
        //also the type of assignment and the assigned_to id but they are in a different table
        $filters = array ('where' => array('o.module = \'' . $this->get('module') . '\'',
                                           'o.controller = \'' . $this->get('controller') . '\'',
                                           'o.model_id = \'' . ($this->get('model_id') ?: '0') . '\'',
                                           'o.section = \'' . ($this->get('section') ?: '0') . '\''),
                          'sanitize' => true,
                          'skip_settings' => true);
        if ($this->get('id')) {
            $filters['where'][] = 'o.id != ' . $this->get('id');
        }

        if ($this->get('roles') && is_array($this->get('roles'))) {
            $this->set('roles', array_filter($this->get('roles')), true);
        }
        if ($this->get('users') && is_array($this->get('users'))) {
            $this->set('users', array_filter($this->get('users')), true);
        }

        if ($at == 'Roles' && !$this->get('roles') || $at == 'Users' && !$this->get('users')) {
            $filters['where'][] = 'oa.assigned_to = \'All-0\'';
            $outlook = Outlooks::searchOne($this->registry, $filters);
            if ($outlook && $outlook->get('id')) {
                $this->valid = false;
            }
        } elseif ($at == 'Users' || $at == 'Roles') {
            $assignments = array_map(function($a) use ($at) { return $at . '-' . $a; }, $this->get(strtolower($at)));
            $filters['where'][] = 'oa.assigned_to IN (\'' . implode('\', \'', $assignments) . '\')';
            $outlooks = Outlooks::search($this->registry, $filters);
            foreach ($outlooks as $outlook) {
                foreach ($outlook->get('assignments') as $oa) {
                    if (in_array($oa['assignments_type'] . '-' . $oa['assigned_to'], $assignments) && !array_key_exists($oa['assigned_to'], $invalid_assignments)) {
                        if ($at == 'Users' && !$this->get('id')) {
                            // keep id in order to redirect to edit of found outlook
                            $this->set('other_id', $outlook->get('id'), true);
                        }
                        $invalid_assignments[$oa['assigned_to']] = $oa;
                        $this->valid = false;
                    }
                }
            }
        }

        // set error message
        if ($invalid_assignments) {
            $error = $this->i18n('error_invalid_assignments') . ' ' . $this->i18n('outlooks_' . strtolower($at)) . ':';
            $error .= '<ul>' . implode("\n", array_map(function($ia) { return '<li>' . $ia['assigned_to_name'] . '</li>'; }, $invalid_assignments)) . '</ul>';
        } elseif (!$this->valid) {
            $error = $this->i18n($at == 'Roles' && !$this->get('roles') || $at == 'Users' && !$this->get('users') ?
                'error_outlooks_edit_exist':
                'error_outlooks_add_exist');
        }
        if ($error) {
            $this->registry['messages']->setError($error);
        }

        return $this->valid;
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = &$this->registry['db'];

        if (!$this->validate()) {
            return false;
        }

        //query to insert the main table
        $query = 'INSERT INTO ' . DB_TABLE_OUTLOOKS . "\n" .
                 ' SET module="' . strtolower($this->get('module')) . '", ' . "\n" .
                 '     controller="' . strtolower($this->get('controller')) . '", ' . "\n" .
                 '     model_id="' . ($this->get('model_id') ? $this->get('model_id') : '0') . '", ' . "\n" .
                 '     section="' . $this->get('section') . '"' . "\n";

        //start transaction
        $db->StartTrans();
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new outlook base details', $db, $query);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);

            //save settings
            $this->saveSettings();

            //save assignments
            $this->saveAssignments();

            //UPDATE THE I18N TABLE OF THE MODEL
            $this->updateI18N();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        //if the transaction was succsesful a template is created
        if ($result) {
            $this->buildTemplate();
        }

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = &$this->registry['db'];

        if (!$this->validate()) {
            return false;
        }

        //getting the old model as we need to delete its templates
        $filters = array('where' => array('o.id = ' . $this->get('id')),
                         'sanitize' => true);
        $old_model = Outlooks::searchOne($this->registry, $filters);

        //start transaction
        $db->StartTrans();

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        if ($this->get('current_custom_fields')) {
            //save settings
            $this->saveSettings();

            //save assignments
            $this->saveAssignments();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        //if the transaction is successful the template is rebuilding
        if ($result && $this->get('current_custom_fields')) {
            //delete old templates of the model
            foreach ($old_model->get('assignments') as $key => $assignment) {
                $base_name = 'list';
                if ($old_model->get('model_id')) {
                    $base_name .= '_' . ($old_model->get('section') ? 'section' : 'type') . '_' . $old_model->get('model_id');
                }
                $base_name .= ($old_model->get('assignments_type') != 'All' ? (!empty($base_name) ? '_' : '') .  $old_model->get('assignments_type') . '_' . $assignment['assigned_to'] : '');
                $base_name .= '.html';

                $base_name_ex = 'export';
                if ($old_model->get('model_id')) {
                    $base_name_ex .= '_' . ($old_model->get('section') ? 'section' : 'type') . '_' . $old_model->get('model_id');
                }
                $base_name_ex .= ($old_model->get('assignments_type') != 'All') ? '_' . $old_model->get('assignments_type') . '_' . $assignment['assigned_to'] : '';
                $base_name_ex .= '.html';

                if (!$old_model->get('model_id')) {
                    $base_name_sp = 'subpanel';
                    $base_name_sp .= ($old_model->get('assignments_type') != 'All') ? '_' . $old_model->get('assignments_type') . '_' . $assignment['assigned_to'] : '';
                    $base_name_sp .= '.html';
                    @unlink(PH_OUTLOOKS_DIR . $old_model->get('module_controller') . '/' . $base_name_sp);
                }
                @unlink(PH_OUTLOOKS_DIR . $old_model->get('module_controller') . '/' . $base_name);
                @unlink(PH_OUTLOOKS_DIR . $old_model->get('module_controller') . '/' . $base_name_ex);
            }

            $this->buildTemplate();
        }

        return $result;
    }

    /**
     * save assignments of the outlook
     *
     */
    public function saveAssignments() {

        $db = &$this->registry['db'];

        if ($parent_id = $this->get('id')) {
            //delete the current assignments
            $query_delete = 'DELETE FROM ' . DB_TABLE_OUTLOOKS_ASSIGNMENTS . ' WHERE parent_id=' . $parent_id;
            $db->Execute($query_delete);
        }

        //prepare assignment
        $assignments = array();
        if ($this->get('assignments_type') == 'Roles') {
            $assignments = $this->get('roles');
        } elseif ($this->get('assignments_type') == 'Users') {
            $assignments = $this->get('users');
        }

        $add_values = array();
        if ($assignments) {
            foreach ($assignments as $assignment) {
                $add_values[] = '("' . $parent_id . '", "' . $assignment . '", "' . $this->get('assignments_type') . '")';
            }
        } else {
            $add_values[] = '("' . $parent_id . '", 0, "All")';
        }

        $add_values = implode(",\n", $add_values);

        //query to insert in the assignment table
        $query = 'INSERT INTO ' . DB_TABLE_OUTLOOKS_ASSIGNMENTS . '(`parent_id`, `assigned_to`, `assignments_type`)' . "\n" .
                 'VALUES ' . $add_values;
        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        $this->getAssignments();
        $this->unsetProperty('roles', true);
        $this->unsetProperty('users', true);
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N() {
        $db = $this->registry['db'];
        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        $update['title'] = sprintf("title='%s'", $this->get('title'));
        $update['subtitle']  = sprintf("subtitle='%s'", $this->get('subtitle'));

        $insert = $update;
        $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
        $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
        $insert['translated'] = sprintf("translated=now()");

        $query2 = 'INSERT INTO ' . DB_TABLE_OUTLOOKS_I18N . "\n" .
                  'SET ' . implode(', ', $insert) . "\n" .
                  'ON DUPLICATE KEY UPDATE ' . "\n" .
                  implode(', ', $update);

        $db->Execute($query2);

        return !$db->HasFailedTrans();
    }

    /**
     * save columns settings
     *
     */
    public function saveSettings() {

        $db = &$this->registry['db'];

        if ($parent_id = $this->get('id')) {
            //delete the current settings for this outlook
            $query_delete = 'DELETE FROM ' . DB_TABLE_OUTLOOKS_SETTINGS . ' WHERE parent_id=' . $parent_id;
            $db->Execute($query_delete);
        }

        $add_values = array();

        foreach ($this->get('current_custom_fields') as $field) {
            $add_values[] = '("' . $parent_id . '", "' . $field['name'] . '", "' . $field['column_width'] . '", "' . $field['origin'] . '", "' . $field['model_type'] . '", "' . $field['position'] . '")';
        }

        $add_values = implode(",\n", $add_values);

        //query to insert the settings table
        $query = 'INSERT INTO ' . DB_TABLE_OUTLOOKS_SETTINGS . '(`parent_id`, `column_name`, `column_width`, `origin`, `model_type`, `position`)' . "\n" .
                 'VALUES ' . $add_values;

        $db->Execute($query);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }
    }

    /**
     * Get model's additional fields
     *
     * @return array - additional variables that can be displayed in list view (outlook)
     */
    public function getModelAdditionalFields() {
        $module = $this->get('module');
        $controller = $this->get('controller');
        $module_controller = $this->get('module_controller');
        $model = $this->get('model');
        $model_id = $this->get('model_id');
        $section_flag = $this->get('section');
        $model_lang = $this->get('model_lang');

        $add_fields = array();
        $sql = array();

        if ($module_controller == 'finance_repayment_plans') {
            return $add_fields;
        }

        $table_name = 'DB_TABLE_' . strtoupper($module_controller) . '_TYPES_I18N';
        if (defined($table_name)) {
            $table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES_I18N');
            $type_join_on_clause = '  ON (ti18n.parent_id=fm.model_type AND fm.model="' . $model . '" AND ti18n.lang="' . $model_lang . '")' . "\n";
        } elseif ($this->get('module') == 'finance') {
            $table_name = DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N;
            $type_join_on_clause = '  ON (ti18n.parent_id=fm.model_type AND fm.model="' . $model . '" AND ti18n.lang="' . $model_lang . '")' . "\n";
        } else {
            $table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES');
            $type_join_on_clause = '  ON (ti18n.id=fm.model_type AND fm.model="' . $model . '" AND ti18n.lang="' . $model_lang . '")' . "\n";
        }

        // check if the outlook is for type or for section
        if ($section_flag) {
            if ($this->get('module') == 'finance') {
                $types_table_name = DB_TABLE_FINANCE_DOCUMENTS_TYPES;
            } else {
                $types_table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES');
            }

            // sql to take the types to current section
            $sql['select']  = 'SELECT id AS type_id' . "\n";
            $sql['from']    = 'FROM ' . $types_table_name . "\n";
            $sql['where']   = 'WHERE type_section="' . $model_id . '"' . "\n";

            $query = implode("\n", $sql);
            $records = $this->registry['db']->GetAll($query);

            $type_ids = array();
            foreach ($records as $recs) {
                $type_ids[] = $recs['type_id'];
            }

            //  building the sql query to take the additional vars for the certain model
            //  and model_types included in current section
            $sql['select']  = 'SELECT fm.name as idx, fm.name as name, 0 AS position, \'\' AS column_width, fm.type as field_type, ' . "\n" .
                              'IF((fm.configurator >0 AND fmi18n2.content), fmi18n2.content, fmi18n.content) AS label, ' . "\n" .
                              'fmi18n3.content AS help, "additional" as origin, fm.model_type,' . "\n" .
                              'GROUP_CONCAT(IFNULL(ti18n.name, \'\') SEPARATOR \', \') AS type_name,' . "\n" .
                              'fm.layout_id, l.place as layout_position, fm.source' . "\n";

            $sql['from']    = 'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .

                              //special LEFT JOIN to define whether there is a grouping variable with a matching name
                              //such variables should not be displayed in list of columns for the outlook
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_META . ' AS fm2' . "\n" .
                              '  ON fm.name=fm2.name AND ' .
                                (($this->get('module') == 'finance') ?
                                   '(fm2.grouping!=0 OR fm2.bb!=0 OR fm.gt2!=0) AND ' :
                                   '(fm2.grouping!=0 OR fm2.bb!=0 OR (fm2.gt2!=0 AND fm2.name NOT LIKE "total%" AND fm2.name!="currency")) AND ') .
                                (sprintf('fm2.model="%s"', $model) . ' AND ') .
                                ((!empty($type_ids)) ? sprintf('fm2.model_type IN (%s)', implode(', ', $type_ids)) : 'fm2.model_type=0') . "\n" .

                              '  LEFT JOIN ' . $table_name . ' AS ti18n' . "\n" .
                              $type_join_on_clause .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n' . "\n" .
                              '  ON (fm.id=fmi18n.parent_id AND fmi18n.content_type="label" AND fmi18n.lang="' . $model_lang . '")' . "\n" .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n2' . "\n" .
                              '  ON (fm.id=fmi18n2.parent_id AND fmi18n2.content_type="description" AND fmi18n2.lang="' . $model_lang . '")' . "\n" .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n3' . "\n" .
                              '  ON (fm.id=fmi18n3.parent_id AND fmi18n3.content_type="help" AND fmi18n3.lang="' . $model_lang . '")' . "\n" .
                              '  LEFT JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                              '  ON (fm.layout_id=l.layout_id)' . "\n";

            //construct where
            $where = array();
            $where[] = sprintf('fm.model = "%s"', $model);
            if (!empty($type_ids)) {
                $where[] = sprintf('fm.model_type IN (%s)', implode(', ', $type_ids));
            } else {
                $where[] = 'fm.model_type = 0';
            }
            $where[] = 'fm.type NOT IN ("button", "group", "gt2", "config", "table", "bb")';
            $where[] = 'fm.grouping = 0';
            $where[] = 'fm.hidden = 0';
            $where[] = 'fm.bb = 0';
            if ($this->get('module') == 'finance') {
                //exclude total variables because they are in the list of basic vars
                $where[] = 'fm.gt2 = 0';
            } else {
                //include total variables for normal documents and contracts
                $where[] = '(fm.gt2 = 0 OR fm.gt2 != 0 AND (fm.name LIKE "total%" OR fm.name = "currency"))';
            }
            $where[] = 'fm.outlooks = 1';
            $where[] = '(l.`system` = 0 OR l.system IS NULL)';
            $where[] = 'fm2.id IS NULL';

            $sql['where']    = 'WHERE ' . implode(' AND ', $where);
            $sql['group_by'] = 'GROUP BY fm.name';
            // require that field has label
            $sql['having'] = 'HAVING TRIM(label) != \'\'';
        } else {
            //building the sql query to take the additional vars for the certain model and model_type
            $sql['select']  = 'SELECT fm.name as idx, fm.name as name, ti18n.name AS type_name, 0 AS position, \'\' AS column_width, fm.type as field_type, ' . "\n" .
                              'IF((fm.configurator >0 AND fmi18n2.content), fmi18n2.content, fmi18n.content) AS label, ' . "\n" .
                              'fmi18n3.content AS help, "additional" as origin, fm.model_type,' . "\n" .
                              'fm.layout_id, l.place as layout_position, fm.source' . "\n";

            $sql['from']    = 'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                              'LEFT JOIN ' . $table_name . ' AS ti18n' . "\n" .
                              $type_join_on_clause .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n' . "\n" .
                              '  ON (fm.id=fmi18n.parent_id AND fmi18n.content_type="label" AND fmi18n.lang="' . $model_lang . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n2' . "\n" .
                              '  ON (fm.id=fmi18n2.parent_id AND fmi18n2.content_type="description" AND fmi18n2.lang="' . $model_lang . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N . ' AS fmi18n3' . "\n" .
                              '  ON (fm.id=fmi18n3.parent_id AND fmi18n3.content_type="help" AND fmi18n3.lang="' . $model_lang . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                              '  ON (fm.layout_id=l.layout_id)' . "\n";

            $where = array();
            $where[] = sprintf('fm.model = "%s"', $model);
            $where[] = 'fm.model_type = "' . $model_id . '"';
            $where[] = 'fm.type NOT IN ("button", "group", "gt2", "config", "table", "bb")';
            $where[] = 'fm.grouping = 0';
            $where[] = 'fm.hidden = 0';
            $where[] = 'fm.bb = 0';
            if ($this->get('module') == 'finance') {
                //exclude total variables because they are in the list of basic vars
                $where[] ='fm.gt2 = 0';
            } else {
                //include total variables for normal documents and contracts
                $where[] ='(fm.gt2 = 0 OR fm.gt2 != 0 AND (fm.name LIKE "total%" OR fm.name = "currency"))';
            }
            $where[] = 'fm.outlooks = 1';
            $where[] = '(l.`system` = 0 OR l.system IS NULL)';

            $sql['where'] = 'WHERE ' . implode("\n" . '  AND ', $where);

            // require that field has label
            $sql['having'] = 'HAVING TRIM(label) != \'\'';
        }

        $query = implode("\n", $sql);

        $add_fields_tmp = $this->registry['db']->GetAssoc($query);

        // Filter the fields
        $add_fields = array();
        $current_user_groups = $this->registry['currentUser']->get('groups')?: array();
        foreach ($add_fields_tmp as $k => $v) {
            // Get current field settings from its source
            $source_settings = General::parseSettings($v['source']);

            // Filter by view permissions
            if (array_key_exists('permissions_view', $source_settings)) {
                $source_settings['permissions_view'] = preg_split('/\s*,\s*/', $source_settings['permissions_view']);
                $permitted_view = array_intersect($current_user_groups, $source_settings['permissions_view']);
                if (empty($permitted_view)) {
                    // Skip current field
                    continue;
                }
            }

            // Collect filtered fields
            $add_fields[$k] = $v;
        }
        unset($add_fields_tmp);

        //some of the additional vars are excluded under some circumstances
        //here are the exceptions
        switch ($module) {
        case 'contracts':
            // if outlook is for one contract type
            if ($this->get('model_id') && !$this->get('section')) {
                //get the contract type settings
                $query = 'SELECT include_section_about, include_section_clause' . "\n" .
                         'FROM ' . DB_TABLE_CONTRACTS_TYPES . "\n" .
                         'WHERE id = \'' . $this->get('model_id') . '\'';
                $contract_type_settings = $this->registry['db']->GetRow($query);

                //check if the contract type disables the the subject tab (section_about) or terms tab (section clause)
                if (!empty($contract_type_settings) && (!$contract_type_settings['include_section_about'] || !$contract_type_settings['include_section_clause'])) {
                    $excluded_subject = !$contract_type_settings['include_section_about'];
                    $excluded_terms   = !$contract_type_settings['include_section_clause'];
                    foreach ($add_fields as $field_name => $field) {
                        //remove additional vars in the subject because the subject tab is disabled
                        if ($excluded_subject && $field['layout_position'] >= PH_CONTRACTS_TAB1_FROM && $field['layout_position'] <= PH_CONTRACTS_TAB1_TO) {
                            unset($add_fields[$field_name]);
                            continue;
                        }

                        //remove additional vars in the terms because the terms tab is disabled
                        if ($excluded_terms && $field['layout_position'] >= PH_CONTRACTS_TAB2_FROM && $field['layout_position'] <= PH_CONTRACTS_TAB2_TO) {
                            unset($add_fields[$field_name]);
                        }
                    }
                }
            }
            break;
        default:
            //do nothing
        }

        return $add_fields;
    }

    /**
     * Adding additional variables for a certain outlook
     *
     * @return array - merged basic and additional fields ordered by their saved positions for current outlook
     */
    public function getTemplateVars() {
        $id = $this->get('id');
        $module = $this->get('module');
        $controller = $this->get('controller');
        $model_id = $this->get('model_id');
        $section_flag = $this->get('section');

        $fields = array();

        //standard fields (basic variables) for outlook
        $fields = $this->getModelFields();

        //the array with additional variables for outlook
        $add_fields = $this->getModelAdditionalFields();

        if ($add_fields) {
            $fields = array_merge($fields, $add_fields);
        }

        //building the sql query to take the positions of the selected vars in current outlook
        $sql_position = array();
        $sql_position['select'] = 'SELECT os.column_name, os.column_name AS name, os.column_width, os.position AS position, os.model_type' . "\n";

        $sql_position['from']   = 'FROM ' . DB_TABLE_OUTLOOKS . ' AS o' . "\n" .
                                  '  LEFT JOIN ' . DB_TABLE_OUTLOOKS_SETTINGS . ' AS os' . "\n" .
                                  '  ON os.parent_id=o.id' . "\n";

        $sql_position['where']  = 'WHERE o.id="' . $id . '" AND o.module="' . $module . '" AND o.controller="' . $controller . '" AND o.model_id="' . $model_id . '" AND o.section="' . $section_flag . '"';

        $query = implode("\n", $sql_position);
        $records = $this->registry['db']->GetAssoc($query);

        // checks if the field name is set and adds the position number.
        // If not that means that the variable was deleted and will not be
        // shown in the list of fields
        foreach ($fields as $key => $field) {
            if (array_key_exists($key, $records)) {
                $fields[$key]['position']     = $records[$key]['position'];
                $fields[$key]['column_width'] = $records[$key]['column_width'];
            } else {
                $fields[$key]['position'] = 0;
                $fields[$key]['column_width'] = '';
            }
        }

        return $fields;
    }

    /**
     * Takes all the vars and clears all which current user doesn't have permissions to view
     * according to layouts.
     * Sets layout labels as labels of basic variables.
     */
    public function clearNotPermittedVars() {
        $module_controller = $this->get('module_controller');

        // unsanitize model if it is sanitized
        $sanitized = $this->isSanitized();
        if ($sanitized) {
            $this->unsanitize();
        }

        $current_custom_fields = $this->get('current_custom_fields');
        $model_lang = $this->get('model_lang');

        $table_name = 'DB_TABLE_' . strtoupper($module_controller) . '_TYPES_I18N';
        if (defined($table_name)) {
            $table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES_I18N');
        } elseif ($this->get('module') == 'finance') {
            $table_name = DB_TABLE_FINANCE_DOCUMENTS_TYPES_I18N;
        } else {
            $table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES');
        }
        $add_fields = array();

        // check if the outlook is for type or for section
        if ($this->get('section')) {
            if ($this->get('module') == 'finance') {
                $types_table_name = DB_TABLE_FINANCE_DOCUMENTS_TYPES;
            } else {
                $types_table_name = constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES');
            }

            // sql to take all active types to current section
            $sql = array();
            $sql['select']  = 'SELECT id AS type_id';
            $sql['from']    = 'FROM ' . $types_table_name;
            $sql['where']   = 'WHERE type_section="' . $this->get('model_id') . '" AND active=1';
            $sql['group']   = '';
            $sql['sort']    = 'ORDER BY id ASC';

            $query = implode("\n", $sql);
            $type_ids = $this->registry['db']->GetCol($query);

            //  building the sql query to take information about the layout which every var is into
            $sql['select']  = 'SELECT fm.name as idx, fm.name as name, GROUP_CONCAT(fm.layout_id) AS layout, fmi18n3.content AS help' . "\n";

            $type_join_on_clause = '  ON (ti18n.parent_id=fm.model_type AND fm.model="' . $this->get('model') . '" AND ti18n.lang="' . $model_lang . '")' . "\n";
            $sql['from']    = 'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                              '  LEFT JOIN ' . $table_name . ' AS ti18n' . "\n" .
                              $type_join_on_clause .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n' . "\n" .
                              '  ON (fm.id=fmi18n.parent_id AND fmi18n.content_type="label" AND fmi18n.lang="' . $model_lang . '")' . "\n" .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n2' . "\n" .
                              '  ON (fm.id=fmi18n2.parent_id AND fmi18n2.content_type="description" AND fmi18n2.lang="' . $model_lang . '")' . "\n" .
                              '  LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n3' . "\n" .
                              '  ON (fm.id=fmi18n3.parent_id AND fmi18n3.content_type="help" AND fmi18n3.lang="' . $model_lang . '")' . "\n";

            //construct where
            $where = array();
            $where[] = sprintf('fm.model = "%s"', $this->get('model'));
            if (!empty($type_ids)) {
                $where[] = sprintf('fm.model_type IN (%s)', implode(', ', $type_ids));
            } else {
                $where[] = 'fm.model_type = 0';
            }
            $where[] = 'fm.type NOT IN ("button", "group", "gt2", "config", "table", "bb")';
            $where[] = 'fm.grouping = 0';
            $where[] = 'fm.hidden = 0';
            $where[] = 'fm.bb = 0';
            if ($this->get('module') == 'finance') {
                //exclude total variables because they are in the list of basic vars
                $where[] = 'fm.gt2 = 0';
            } else {
                //include total variables for normal documents and contracts
                $where[] = '(fm.gt2 = 0 OR fm.gt2 != 0 AND (fm.name LIKE "total%" OR fm.name = "currency"))';
            }
            $where[] = 'fm.outlooks = 1';
            $sql['where']   = 'WHERE ' . implode("\n" . ' AND ', $where);
            $sql['group']   = 'GROUP BY fm.name';
        } else {
            //  building the sql query to take information about the layout which every var is into
            $sql['select']  = 'SELECT fm.name as idx, fm.name as name, fm.layout_id AS layout, fmi18n3.content AS help' . "\n";

            $type_join_on_clause = '  ON (ti18n.parent_id=fm.model_type AND fm.model="' . $this->get('model') . '" AND ti18n.lang="' . $model_lang . '")' . "\n";
            $sql['from']    = 'FROM ' . DB_TABLE_FIELDS_META . ' AS fm' . "\n" .
                              'LEFT JOIN ' . $table_name . ' AS ti18n' . "\n" .
                              $type_join_on_clause .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n' . "\n" .
                              '  ON (fm.id=fmi18n.parent_id AND fmi18n.content_type="label" AND fmi18n.lang="' . $model_lang . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n2' . "\n" .
                              '  ON (fm.id=fmi18n2.parent_id AND fmi18n2.content_type="description" AND fmi18n2.lang="' . $model_lang . '")' . "\n" .
                              'LEFT JOIN ' . DB_TABLE_FIELDS_I18N  . ' AS fmi18n3' . "\n" .
                              '  ON (fm.id=fmi18n3.parent_id AND fmi18n3.content_type="help" AND fmi18n3.lang="' . $model_lang . '")' . "\n";

            $where = array();
            $where[] = sprintf('fm.model = "%s"', $this->get('model'));
            $where[] = 'fm.model_type = "' . $this->get('model_id') . '"';
            $where[] = 'fm.type NOT IN ("button", "group", "gt2", "config", "table", "bb")';
            $where[] = 'fm.grouping = 0';
            $where[] = 'fm.hidden = 0';
            $where[] = 'fm.bb = 0';
            if ($this->get('module') == 'finance') {
                //exclude total variables because they are in the list of basic vars
                $where[] ='fm.gt2 = 0';
            } else {
                //include total variables for normal documents and contracts
                $where[] ='(fm.gt2 = 0 OR fm.gt2 != 0 AND (fm.name LIKE "total%" OR fm.name = "currency"))';
            }
            $where[] = 'fm.outlooks = 1';
            $sql['where']   = 'WHERE ' . implode("\n" . ' AND ', $where);
        }

        $query = implode("\n", $sql);

        $add_fields = $this->registry['db']->GetAssoc($query);

        $not_permitted_vars = array();

        // all permitted layouts for all model types
        $view_permitted_layouts = $this->getPermittedLayouts('view', $this->get('model'));

        // if layout is not permitted, all additional variables in it are not permitted
        // in case of same variable name in multiple types - variable is not permitted when not permitted for all types
        foreach ($add_fields as $add_field) {
            $layout_ids = explode(',', $add_field['layout']);
            foreach ($layout_ids as $lidx => $layout_id) {
                if (!in_array($layout_id, $view_permitted_layouts)) {
                    unset($layout_ids[$lidx]);
                }
            }
            if (!$layout_ids) {
                $not_permitted_vars[] = $add_field['name'];
            }
        }

        $model_types = array();
        if ($this->get('section')) {
            if (!empty($type_ids)) {
                $model_types = $type_ids;
            }
        } elseif ($this->get('model_id')) {
            $model_types = array($this->get('model_id'));
        }

        // hide not permitted fields and change labels

        // get system layouts for one type, for all types in section
        require_once PH_MODULES_DIR . 'layouts/models/layouts.factory.php';
        $filters = array(
            'model_lang' => $model_lang,
            'sanitize' => true,
            'sort' => array('l.model_type'),
            'where' => array(
                'l.`system` = 1',
                'l.model = \'' . $this->get('model') . '\''
            )
        );

        //the outlook for common list does not specify any model types
        if (!empty($model_types)) {
            $filters['where'][] = 'l.model_type IN ("' . implode('", "', $model_types) . '")';
        }
        $layouts = Layouts::search($this->registry, $filters);

        foreach ($layouts as $k => $layout) {
            // collect distinct labels from every type for this layout
            $all_labels = !empty($layouts[$layout->get('keyname')]) ?
                $layouts[$layout->get('keyname')]->get('all_labels') :
                array();

            if (!isset($layouts[$layout->get('keyname')]) ||
                !in_array($layouts[$layout->get('keyname')]->get('id'), $view_permitted_layouts) && in_array($layout->get('id'), $view_permitted_layouts)
            ) {
                $layouts[$layout->get('keyname')] = $layout;
            }

            if (!in_array($layout->get('name'), $all_labels)) {
                $all_labels[] = $layout->get('name');
            }
            $layouts[$layout->get('keyname')]->set('all_labels', $all_labels, true);

            unset($layouts[$k]);
        }

        // if labels of different types do not match, do not use layout name but default label
        foreach ($layouts as $keyname => $layout) {
            if (is_array($layout->get('all_labels')) && count($layout->get('all_labels')) > 1) {
                $layouts[$keyname]->unsetProperty('name', true);
            }
            $layouts[$keyname]->unsetProperty('all_labels', true);
        }

        //check layout permissions of basic vars
        $matches = array();
        foreach ($current_custom_fields as $idx => $cstm_field) {
            if ($cstm_field['origin'] == 'basic') {
                // exception for fields from composite layout 'company_data' in financial records
                if (in_array($cstm_field['name'], array('company', 'office', 'payment_type', 'container_id', 'container_code', 'container_name_code')) &&
                    isset($layouts['company_data']) && $layouts['company_data']->get('name')
                ) {
                    if (!in_array($layouts['company_data']->get('id'), $view_permitted_layouts)) {
                        $not_permitted_vars[] = $cstm_field['name'];
                    } else {
                        // NOTE: Do not change the label of the basic variable with layout name
                        // because layout contains more than one fields and they would all have the same label
                        if ($cstm_field['name'] == 'container_id') {
                            $current_custom_fields[$idx]['label'] = $layouts['company_data']->get('name');
                        } elseif ($cstm_field['name'] == 'container_code') {
                            $current_custom_fields[$idx]['label'] = sprintf('%s %s %s', $this->i18n('code'), $this->i18n('of'), $layouts['company_data']->get('name'));
                        }
                    }
                } elseif (preg_match('#^(to_)?(company|office|warehouse((_name)?_code)?)$#', $cstm_field['name'], $matches) &&
                    isset($layouts[$matches[1] . 'warehouse_data']) && $layouts[$matches[1] . 'warehouse_data']->get('name')
                ) {
                    // exception for fields from composite layouts in warehouse documents:
                    // 'warehouse_data' and 'to_warehouse_data'
                    if (!in_array($layouts[$matches[1] . 'warehouse_data']->get('id'), $view_permitted_layouts)) {
                        $not_permitted_vars[] = $cstm_field['name'];
                    } else {
                        // NOTE: Do not change the label of the basic variable with layout name
                        // because layout contains more than one fields and they would all have the same label
                        if ($cstm_field['name'] == $matches[1] . 'warehouse') {
                            $current_custom_fields[$idx]['label'] = $layouts[$matches[1] . 'warehouse_data']->get('name');
                        } elseif ($cstm_field['name'] == $matches[1] . 'warehouse_code') {
                            $current_custom_fields[$idx]['label'] = sprintf('%s %s %s', $this->i18n('code'), $this->i18n('of'), $layouts[$matches[1] . 'warehouse_data']->get('name'));
                        }
                    }
                } elseif (in_array($cstm_field['name'], array('has_batch')) && isset($layouts['batch_options'])) {
                    if (!in_array($layouts['batch_options']->get('id'), $view_permitted_layouts)) {
                        $not_permitted_vars[] = $cstm_field['name'];
                    }
                } elseif (in_array($cstm_field['name'], array('timesheet_time')) && in_array($this->get('model'), array('Document', 'Project'))) {
                    // check if any types have timesheets enabled
                    if (!$this->registry['db']->GetOne("
                        SELECT 1 FROM " . constant('DB_TABLE_' . strtoupper($module_controller) . '_TYPES') . "
                        WHERE active = 1 AND deleted_by = 0 AND generate_system_task = 1" .
                        (!empty($model_types) ? " AND id IN ('" . implode("', '", $model_types) . "')" : '')
                    )) {
                        $not_permitted_vars[] = $cstm_field['name'];
                    }
                } elseif (isset($layouts[$cstm_field['name']])) {
                    if (!in_array($layouts[$cstm_field['name']]->get('id'), $view_permitted_layouts)) {
                        $not_permitted_vars[] = $cstm_field['name'];
                    } elseif ($layouts[$cstm_field['name']]->get('name')) {
                        //change the label of the basic variable (get it from the layout)
                        $current_custom_fields[$idx]['label'] = $layouts[$cstm_field['name']]->get('name');
                    }
                } else {
                    if (preg_match('#.*_and_currency$#', $cstm_field['name']) && $layouts[str_replace('_and_currency', '', $cstm_field['name'])]->get('name')) {
                        //special occasion for nomenclature price + currency fields
                        $current_custom_fields[$idx]['label'] = $layouts[str_replace('_and_currency', '', $cstm_field['name'])]->get('name') . '+' . $this->i18n('currency');
                    }
                }
            }
        }

        //some of the columns are a combination of fields
        //check the permissions of the related fields
        switch ($module_controller) {
            case 'nomenclatures':
                if (in_array('sell_price', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'sell_price_and_currency';
                }
                if (in_array('last_delivery_price', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'last_delivery_price_and_currency';
                }
                if (in_array('average_weighted_delivery_price', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'average_weighted_delivery_price_and_currency';
                }
                break;
            case 'customers':
                if (in_array('name', $not_permitted_vars) && in_array('code', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'name_code';
                }
                break;
            case 'documents':
            case 'events':
            case 'tasks':
            case 'projects':
            case 'contracts':
                if (in_array($module_controller, array('documents', 'tasks')) && in_array('name', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'name_full_num';
                }
                if ($module_controller == 'contracts' && in_array('name', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'name_num';
                }
                if (in_array('customer', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'customer_name_code';
                }
                if (in_array('trademark', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'trademark_name_code';
                }
                if ($module_controller == 'documents' && in_array('contract', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'contract_name_num';
                }
                if ($module_controller != 'projects' && in_array('project', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'project_name_code';
                }
                // exception for fields from composite layout 'event_start' in events
                if ($module_controller == 'events' && in_array('event_start', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'event_end';
                }
                break;
            case 'finance_incomes_reasons':
            case 'finance_expenses_reasons':
            case 'finance_annulments':
            case 'finance_warehouses_documents':
            case 'finance_payments':
                if ($module_controller != 'finance_payments' && in_array('name', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'name_num';
                }
                if (in_array('customer', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'customer_name_code';
                }
                if (in_array('trademark', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'trademark_name_code';
                }
                if (in_array('project', $not_permitted_vars)) {
                    $not_permitted_vars[] = 'project_name_code';
                }
                if (empty($model_types)) {
                    // outlook without model types => only financial documents of type > 100
                    if ($module_controller == 'finance_incomes_reasons') {
                        $not_permitted_vars[] = 'invoice_code';
                        $not_permitted_vars[] = 'cd_reason';
                        $not_permitted_vars[] = 'date_of_receive';
                        $not_permitted_vars[] = 'fiscal_event_date';
                    } elseif ($module_controller == 'finance_expenses_reasons') {
                        $not_permitted_vars[] = 'admit_VAT_credit';
                        $not_permitted_vars[] = 'accounting_period';
                        $not_permitted_vars[] = 'fiscal_event_date';
                        $not_permitted_vars[] = 'reason';
                    }
                } else {
                    // hide some fields if outlook is for specific types
                    if ($module_controller == 'finance_incomes_reasons') {
                        if (!in_array(PH_FINANCE_TYPE_INVOICE, $model_types)) {
                            $not_permitted_vars[] = 'date_of_receive';
                        }
                        if (!(array_intersect(array(PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE), $model_types))) {
                            $not_permitted_vars[] = 'cd_reason';
                            if (!in_array(PH_FINANCE_TYPE_INVOICE, $model_types)) {
                                $not_permitted_vars[] = 'fiscal_event_date';
                                if (!in_array(PH_FINANCE_TYPE_PRO_INVOICE, $model_types)) {
                                    $not_permitted_vars[] = 'invoice_code';
                                }
                            }
                        }
                        if (end($model_types) < PH_FINANCE_TYPE_MAX) {
                            $not_permitted_vars[] = 'invoice_amount';
                            $not_permitted_vars[] = 'invoice_status';
                        }
                    } elseif ($module_controller == 'finance_expenses_reasons') {
                        if (!(array_intersect(array(PH_FINANCE_TYPE_EXPENSES_INVOICE, PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE), $model_types))) {
                            $not_permitted_vars[] = 'admit_VAT_credit';
                            $not_permitted_vars[] = 'accounting_period';
                            $not_permitted_vars[] = 'fiscal_event_date';
                            if (!array_diff($model_types, array(PH_FINANCE_TYPE_EXPENSES_PRO_INVOICE, PH_FINANCE_TYPE_EXPENSES_CORRECT_REASON))) {
                                $not_permitted_vars[] = 'allocated_status';
                            }
                        }
                        if (!(array_intersect(array(PH_FINANCE_TYPE_EXPENSES_CREDIT_NOTICE, PH_FINANCE_TYPE_EXPENSES_DEBIT_NOTICE), $model_types))) {
                            $not_permitted_vars[] = 'reason';
                        }
                    } elseif ($module_controller == 'finance_warehouses_documents') {
                        if (!array_intersect(array(PH_FINANCE_TYPE_HANDOVER, PH_FINANCE_TYPE_COMMODITIES_RESERVATION, PH_FINANCE_TYPE_COMMODITIES_RELEASE), $model_types)) {
                            $not_permitted_vars[] = 'customer';
                            $not_permitted_vars[] = 'customer_name_code';
                            $not_permitted_vars[] = 'trademark';
                            $not_permitted_vars[] = 'trademark_name_code';
                            $not_permitted_vars[] = 'project';
                            $not_permitted_vars[] = 'project_name_code';
                        }
                        if (!array_intersect(array(PH_FINANCE_TYPE_HANDOVER, PH_FINANCE_TYPE_COMMODITIES_TRANSFER), $model_types)) {
                            $not_permitted_vars[] = 'from';
                            $not_permitted_vars[] = 'to';
                        }
                        if (!array_intersect(array(PH_FINANCE_TYPE_HANDOVER), $model_types)) {
                            $not_permitted_vars[] = 'location';
                        }
                        if (!array_intersect(array(PH_FINANCE_TYPE_WASTE, PH_FINANCE_TYPE_INSPECTION), $model_types)) {
                            $not_permitted_vars[] = 'employees';
                        }
                        if (!array_intersect(array(PH_FINANCE_TYPE_COMMODITIES_TRANSFER), $model_types)) {
                            $not_permitted_vars[] = 'to_company';
                            $not_permitted_vars[] = 'to_office';
                            $not_permitted_vars[] = 'to_warehouse';
                            $not_permitted_vars[] = 'to_warehouse_code';
                            $not_permitted_vars[] = 'to_warehouse_name_code';
                            $not_permitted_vars[] = 'to_from';
                            $not_permitted_vars[] = 'to_to';
                            $not_permitted_vars[] = 'to_date';
                            $not_permitted_vars[] = 'to_description';
                        }
                    }
                }
                break;
        }

        $positioned_fields = array();
        $unpositioned_fields = array();

        // clear the not permitted vars
        $user_permissions = $this->registry['currentUser']->getRights();

        /**
         * fields that represent summary information from an action/tab
         * and their visibility depends on user permissions to action
         *
         * @var array $action_fields - field name is key, action is value
         */
        $action_fields = array(
            'comments' => 'comments',
            'emails' => 'emails',
            'tags' => 'tags_view',
            'history_activity' => 'history',
        );

        foreach ($current_custom_fields as $idx => $cstm_field) {
            // check the permissions for field, not in the layouts but in the currentUser permissions
            if ($cstm_field['origin'] == 'basic' && array_key_exists($cstm_field['name'], $action_fields)) {
                $action = $action_fields[$cstm_field['name']];

                if ($this->get('model_id') && !$this->get('section') &&
                !empty($user_permissions[$module_controller . $this->get('model_id')]) &&
                in_array($action, $user_permissions[$module_controller . $this->get('model_id')])) {
                    //get permissions for the type (or first type in the section)
                    $permissions = $user_permissions[$module_controller . $this->get('model_id')];
                } else {
                    //get permissions in the module
                    $permissions = $user_permissions[$module_controller];
                }

                if (array_key_exists($action, $permissions) && ($permissions[$action] == '' || $permissions[$action] == 'none')) {
                    $not_permitted_vars[] = $cstm_field['name'];
                }
            }

            $matches2 = array();
            // replace layout names in the [Key/Num] Name fields
            if ($cstm_field['origin'] == 'basic' &&
            preg_match('/^(((.*)(_))?name)_(code|(full_)?num)$/', $cstm_field['name'], $matches) &&
            preg_match('/^(\[.*\]\s)/', $cstm_field['label'], $matches2)) {
                $main_field = $matches[3] ?: $matches[1];
                if ($module_controller == 'finance_payments' && $main_field == 'container') {
                    $main_field .= '_id';
                }
                $main_cstm_field = array_filter($current_custom_fields,
                    function ($a) use ($main_field) {
                        return $a['origin'] == 'basic' && $a['label'] && $a['name'] == $main_field;
                    }
                );
                if ($main_cstm_field) {
                    $main_cstm_field = reset($main_cstm_field);

                    $num_code_field = '';
                    // if the main field is 'name', use the label of the code/num/full_num layout
                    if ($main_field == 'name') {
                        $num_code_field = $matches[5];
                        $num_code_field = array_filter($current_custom_fields,
                            function ($a) use ($num_code_field) {
                                return $a['origin'] == 'basic' && $a['label'] && $a['name'] == $num_code_field;
                            }
                        );
                        if ($num_code_field) {
                            $num_code_field = reset($num_code_field);
                            $num_code_field = sprintf('[%s] ', $num_code_field['label']);
                        }
                    }
                    if (!$num_code_field) {
                        $num_code_field = $matches2[1];
                    }

                    $cstm_field['label'] = $num_code_field . $main_cstm_field['label'];
                }
            }

            if (in_array($cstm_field['name'], $not_permitted_vars)) {
                unset($current_custom_fields[$idx]);
            } else {
                // restore help of additional vars after POST
                if ($cstm_field['origin'] == 'additional' && !empty($add_fields[$cstm_field['name']]['help']) && empty($cstm_field['help'])) {
                    $cstm_field['help'] = $add_fields[$cstm_field['name']]['help'];
                }
                if ($cstm_field['position']) {
                    $positioned_fields[] = $cstm_field;
                } else {
                    $unpositioned_fields[] = $cstm_field;
                }
            }
        }

        $current_custom_fields = array_merge($positioned_fields, $unpositioned_fields);

        $this->set('current_custom_fields', $current_custom_fields, true);
    }

    /**
     * Creating template
     *
     * @return bool - result of the operation
     */
    public function buildTemplate() {

        $current_custom_fields = $this->get('current_custom_fields');
        $content_template = '';
        $content_template_export = '';
        $count_added_fields = 0;
        $columns_styles = $columns_styles_subpanel = '';
        $ths = '';
        $tds = '';
        $model_type = strtolower($this->get('model'));
        $object_name = $model_type . 's';

        $standard_fields = $this->getModelFields();

        // adding blank template to be filled with values
        $file_name_blank = PH_MODULES_DIR . 'outlooks/templates/_blank.html';
        $file_name_blank_export = PH_MODULES_DIR . 'outlooks/templates/_blank_export.html';
        $content_template = FilesLib::readContent($file_name_blank);
        $content_template_export = FilesLib::readContent($file_name_blank_export);

        $save_subpanel = !$this->get('model_id') || $this->get('type_keyword') == 'trademark';
        if ($save_subpanel) {
            $file_name_blank_subpanel = PH_MODULES_DIR . 'outlooks/templates/_blank_subpanel.html';
            $content_template_subpanel = FilesLib::readContent($file_name_blank_subpanel);
        }

        $extender = new Extender;

        /*
         * Prepare for building the columns styles
         */
        // Get a template file with column styles
        $column_styles_file = PH_MODULES_DIR . 'outlooks/templates/_column_styles.html';
        // If the file doesn't exist
        if (!file_exists($column_styles_file)) {
            // Set that the file doesn't exist
            $column_styles_file = false;
        } else {
            // Get the file content
            $column_styles_file = FilesLib::readContent($column_styles_file);
            // Set a columns counter starting from 2 (it should starts from 2 because there are 2 default columns in outlooks (see: _libs/modules/outlooks/templates/_blank.html))
            $current_column_num = 2;
        }

        // building title row and columns styles
        foreach ($current_custom_fields as $field) {
            if ($field['position']) {
                $field_name = '';
                if (is_array($field)) {
                    $field_name = $field['name'];
                } else {
                    $field_name = $field;
                }

                if (array_key_exists($field_name, $standard_fields)) {
                    $file_name_field = PH_MODULES_DIR . 'outlooks/templates/th/' . $field_name . '.html';
                } else {
                    $file_name_field = PH_MODULES_DIR . 'outlooks/templates/th/_additional_var.html';
                    $extender->add('add_var_name', '{$add_vars_labels.' . $field['model_type']. '.'. $field_name .'}');
                    $extender->add('field_name',   'a__' . $field_name);
                }

                // checks if the required file exists and if not
                // adds a generic file
                if (! file_exists($file_name_field)) {
                    $file_name_field = PH_MODULES_DIR . 'outlooks/templates/th/_default_field.html';
                    $extender->add('field_name', $field_name);
                }

                $field_template = FilesLib::readContent($file_name_field);
                $extender->add('object_name',        $object_name);
                $extender->add('model_type',         $model_type);
                $field_template = $extender->expand($field_template, false);
                $ths = $ths . $field_template;
                $extender->flush();
                $file_name_field = '';
                $field_template = '';
                $count_added_fields++;

                // If there is a column styles template file
                if (!empty($column_styles_file)) {
                    // Calculate the number of the current column
                    $current_column_num++;

                    // If there is a width set for the current column
                    if (!empty($field['column_width'])) {
                        // Set the placeholders for width and num of the current column
                        $extender->add('column_width', $field['column_width']);
                        $extender->add('current_column_num', $current_column_num);

                        // Expand the styles template using the expander
                        $columns_styles .= $extender->expand($column_styles_file, false);

                        if ($save_subpanel) {
                            $extender->add('current_column_num', ($this->get('type_keyword') == 'trademark' ? $current_column_num + 1 : $current_column_num - 1));
                            $columns_styles_subpanel .= $extender->expand($column_styles_file, false);
                        }

                        // Clear all placeholders of the expander
                        $extender->flush();
                    }
                }
            }
        }

        // building the rows for values
        // TODO: this loop (for td cells) can be skipped if the functionality is moved to the previous loop (for th cells)
        foreach ($current_custom_fields as $field) {
            if ($field['position']) {
                $field_name = '';
                if (is_array($field)) {
                    $field_name = $field['name'];
                } else {
                    $field_name = $field;
                }

                if (array_key_exists($field_name, $standard_fields)) {
                    $file_name_field         = PH_MODULES_DIR . 'outlooks/templates/td/' . $model_type . '_' . $standard_fields[$field_name]['depends_on'] . '.html';
                    $default_file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/' . 'default_'        . $standard_fields[$field_name]['depends_on'] . '.html';
                } else {
                    if ($field['field_type'] == 'checkbox_group') {
                        $file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/_additional_var_checkbox_group.html';
                    } else {
                        $file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/_additional_var.html';
                    }
                    $extender->add('add_var_name',       $field_name);
                    $extender->add('add_var_back_label', '{$add_vars_back_labels.' . $field['model_type']. '.'. $field_name .'}');
                    $extender->add('add_var_type',       $field['field_type']);
                }

                // checks if the required file exists and if not
                // adds a generic file
                if (! file_exists($file_name_field) && ! file_exists($default_file_name_field)) {
                    $alternative_field_name = $standard_fields[$field_name]['depends_on'];
                    if (preg_match('#^(owner|responsible|observer|decision)$#', $field['name'])) {
                        //special template for assignments
                        $file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/_assignments.html';
                    } elseif (preg_match('#\[\]$#', $alternative_field_name)) {
                        $alternative_field_name = str_replace('[]', '', $alternative_field_name);
                        $alternative_template_file = PH_MODULES_DIR . 'outlooks/templates/td/' . $model_type . '_' . $alternative_field_name . '.html';
                        if (file_exists($alternative_template_file)) {
                            $file_name_field = $alternative_template_file;
                        } else {
                            $file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/_default_field_array.html';
                        }
                    } else {
                        $file_name_field = PH_MODULES_DIR . 'outlooks/templates/td/_default_field.html';
                    }
                    $extender->add('field_name', $field_name);
                    $extender->add('alternative_field_name', $alternative_field_name);
                } elseif (! file_exists($file_name_field)) {
                    $file_name_field = $default_file_name_field;
                }

                $field_template = FilesLib::readContent($file_name_field);
                $extender->add('object_name', $object_name);
                $extender->add('model_type', $model_type);
                $field_template = $extender->expand($field_template, false);
                $tds = $tds . $field_template;
                $extender->flush();
                $file_name_field = '';
                $field_template = '';
            }
        }

        // adding help file for the module
        $help_file = PH_MODULES_DIR . 'outlooks/templates/td/' . $model_type . '_help.html';
        $help = FilesLib::readContent($help_file);
        $extender->add('object_name', $object_name);
        $extender->add('model_type', $model_type);
        $help = $extender->expand($help, false);
        $extender->flush();

        // adding filter select button and pagination link
        $filter_select_button_file = PH_MODULES_DIR . 'outlooks/templates/td/' . $model_type . '_filter_select_button.html';
        $filter_pagination_link_file = PH_MODULES_DIR . 'outlooks/templates/td/' . $model_type . '_filter_pagination_link.html';
        if (file_exists($filter_select_button_file)) {
            $filter_select_button = FilesLib::readContent($filter_select_button_file);
        } else {
            $filter_select_button = '';
        }
        if (file_exists($filter_pagination_link_file)) {
            $filter_pagination_link = FilesLib::readContent($filter_pagination_link_file);
        } else {
            $filter_pagination_link = '';
        }

        //exclude buttons from the multiple actions if necessary
        if ($model_type == 'project' || $model_type == 'contract' || $model_type == 'nomenclature' || $model_type == 'finance_repayment_plan') {
            $exclude_buttons = "multiedit";
        } elseif ($model_type == 'finance_incomes_reason' || $model_type == 'finance_expenses_reason') {
            $exclude_buttons = "multiedit,delete,restore";
        } elseif ($model_type == 'finance_warehouses_document' ||
                  $model_type == 'finance_annulment' || $model_type == 'finance_payment') {
            $exclude_buttons = 'multiedit,delete,restore,activate,deactivate';
        } else {
            $exclude_buttons = '';
        }

        //include buttons from the multiple actions if necessary
        $include_buttons = array();
        if (in_array($model_type, array('document'))) {
            $include_buttons[] = 'purge';
        }
        if (in_array($model_type, array('contract', 'document', 'event',
                                        'finance_incomes_reason', 'finance_expenses_reason',
                                        'project', 'task'))) {
            $include_buttons[] = 'multistatus';
        }
        if (in_array($model_type, array('contract', 'customer', 'document', 'event', 'project',
                                        'finance_annulment', 'finance_expenses_reason', 'finance_incomes_reason',
                                        'finance_payment', 'finance_warehouses_document'))) {
            $include_buttons[] = 'multiprint';
        }
        if (in_array($model_type, array('contract', 'customer', 'document',
                                        'finance_annulment', 'finance_expenses_reason', 'finance_incomes_reason',
                                        'finance_payment', 'finance_warehouses_document',
                                        'nomenclature', 'project', 'task'))) {
            $include_buttons[] = 'tags';
        }
        if ($model_type == 'finance_expenses_reason') {
            $include_buttons[] = 'multiaddinvoice';
        }
        if ($model_type == 'contract') {
            $include_buttons[] = 'change_templates_observer';
        }
        $include_buttons = implode(',', $include_buttons);

        // include severity legend for tasks
        if ($model_type == 'task') {
            $severity_legend_module = 'tasks';
        } else {
            $severity_legend_module = '';
        }

        $total_fields = $count_added_fields + $this->staticColumns;

        //$extender->add('sub_menu', $sub_menu);
        $extender->add('total_fields', $total_fields);
        $extender->add('count_added_fields', $count_added_fields);
        //$extender->add('object_icon', $object_icon);
        $extender->add('object_name', $object_name);
        $extender->add('model_type', $model_type);
        $extender->add('columns_styles', $columns_styles);
        $extender->add('ths', $ths);
        $extender->add('tds', $this->stripActionTags($tds, 'list'));
        $extender->add('exclude_buttons', $exclude_buttons);
        $extender->add('include_buttons', $include_buttons);
        $extender->add('help', $help);
        $extender->add('filter_select_button', $filter_select_button);
        $extender->add('filter_pagination_link', $filter_pagination_link);
        $extender->add('severity_legend_module', $severity_legend_module);

        //filling the values in the prepared template
        $content_template = $extender->expand($content_template, false);
        if ($save_subpanel) {
            $extender->add('total_fields', ($this->get('type_keyword') == 'trademark' ? $total_fields + 1 : $total_fields - 1));
            $extender->add('columns_styles', $columns_styles_subpanel);
            $content_template_subpanel = $extender->expand($content_template_subpanel, false);
        }
        $extender->flush();

        //defining export files' paths
        if (!$this->get('assignments') || $this->get('assignments_type') == 'All') {
            $assignments = array(array('assigned_to' => '', 'assignments_type' => ''));
        } else {
            $assignments = $this->get('assignments');
        }

        foreach ($assignments as $assignment) {
            //defining files' paths
            if ($this->get('model_id')) {
                $file_path = sprintf('%s%s/%s%s_%s%s%s%s',
                    PH_OUTLOOKS_DIR,
                    $object_name,
                    'list_',
                    ($this->get('section') ? 'section' : 'type'),
                    ($this->get('model_id') ? $this->get('model_id') : '0'),
                    ($assignment['assignments_type']) ? '_' . $assignment['assignments_type'] : '',
                    ($assignment['assigned_to']) ? '_' . $assignment['assigned_to'] : '',
                    '.html');
            } else {
                $file_path = sprintf('%s%s/%s%s%s%s',
                    PH_OUTLOOKS_DIR,
                    $object_name,
                    'list',
                    ($assignment['assignments_type']) ? '_' . $assignment['assignments_type'] : '',
                    ($assignment['assigned_to']) ? '_' . $assignment['assigned_to'] : '',
                    '.html');
            }

            //create folder for the custom template
            $dir_path = dirname($file_path);
            FilesLib::createDir($dir_path);

            //writing file in the selected destination
            $result = FilesLib::writeFile($file_path, $content_template, 'w+');
            if ($result) {
                //chmod the file so that it could be accessible via FTP
                @chmod($file_path, 0777);
            }
        }

        if ($result && $save_subpanel) {
            //filling the values in the prepared template
            //defining export files' paths
            if (!$this->get('assignments') || $this->get('assignments_type') == 'All') {
                $assignments = array(array('assigned_to' => '', 'assignments_type' => ''));
            } else {
                $assignments = $this->get('assignments');
            }

            foreach ($assignments as $assignment) {
                //defining files' paths
                $file_path = sprintf('%s%s/%s%s%s%s%s',
                    PH_OUTLOOKS_DIR,
                    $object_name,
                    'subpanel',
                    ($this->get('model_id') ? '_' . ($this->get('section') ? 'section' : 'type') . '_' . $this->get('model_id') : ''),
                    ($assignment['assignments_type']) ? '_' . $assignment['assignments_type'] : '',
                    ($assignment['assigned_to']) ? '_' . $assignment['assigned_to'] : '',
                    '.html');

                //create folder for the custom template
                $dir_path = dirname($file_path);
                FilesLib::createDir($dir_path);

                //writing file in the selected destination
                $result = FilesLib::writeFile($file_path, $content_template_subpanel, 'w+');
                if ($result) {
                    //chmod the file so that it could be accessible via FTP
                    @chmod($file_path, 0777);
                }
            }
        }

        if ($result) {
            // start to build the export template
            $tds = $this->stripActionTags($tds, 'export');

            // fix for strip_tags to work for us
            $tds = preg_replace('#->#', '[object_arrow]', $tds);

            // replace img tags with the containing of ALT attribute
            $tds = preg_replace('#\<img (.*)\s*alt=\"([^\"]*)\"(.*)\>#', '$2', $tds);

            // prepare tds
            $tds = strip_tags($tds, '<th><tr><td><table><br>');
            //replace the break tags with commas
            $tds = preg_replace('#\<br( )*\/\>#', ', ', $tds);

            //remove some of the attributes of the tds
            $tds = preg_replace('#( class=\"[^\"\{]*(\{if is_numeric\(\$var_value\)\})?\s?hright(\{\/if\})?[^\"]*\")#', '\2 align="right"\3', $tds);
            //remove some of the attributes of the tds
            $tds = preg_replace('# class=\"[^\"]*\"#', '', $tds);
            //remove the row link
            $tds = preg_replace('#\{\$row_link\}#', '', $tds);
            //add excel formatting
            $tds = preg_replace('#<td>#', '<td style="mso-number-format: \@;">', $tds);
            $tds = preg_replace('#<td align="right">#', '<td style="mso-number-format: \@;" align="right">', $tds);

            // prepare ths
            $ths = strip_tags($ths, '<th><tr><td>');
            //remove some of the attributes of the ths
            $ths = preg_replace('# class=\"[^\"]*\"#', '', $ths);

            // prepare the extender
            $extender->add('total_fields', $total_fields);
            $extender->add('object_name', $object_name);
            $extender->add('model_type', $model_type);
            $extender->add('ths', $ths);
            $extender->add('tds', $tds);

            //adds the values for the template
            $content_template_export = $extender->expand($content_template_export);
            $extender->flush();

            //replace the [object_arrow] with real arrow
            $extender->add('object_arrow', '->');
            //replace the [lt]/[gt] with </>
            $extender->add('lt', '<');
            $extender->add('gt', '>');
            $content_template_export = $extender->expand($content_template_export);
            $extender->flush();

            foreach ($assignments as $assignment) {
                //defining export files' paths
                if ($this->get('model_id')) {
                    $file_path_export = sprintf('%s%s/%s%s_%s%s%s%s',
                    PH_OUTLOOKS_DIR,
                    $object_name,
                    'export_',
                    ($this->get('section') ? 'section' : 'type'),
                    ($this->get('model_id') ? $this->get('model_id') : '0'),
                    ($assignment['assignments_type']) ? '_' . $assignment['assignments_type'] : '',
                    ($assignment['assigned_to']) ? '_' . $assignment['assigned_to'] : '',
                    '.html');
                } else {
                    $file_path_export = sprintf('%s%s/%s%s%s%s',
                    PH_OUTLOOKS_DIR,
                    $object_name,
                    'export',
                    ($assignment['assignments_type']) ? '_' . $assignment['assignments_type'] : '',
                    ($assignment['assigned_to']) ? '_' . $assignment['assigned_to'] : '',
                    '.html');
                }

                //writing file in the selected destination
                $result = FilesLib::writeFile($file_path_export, $content_template_export, 'w+');

                if ($result) {
                    //chmod the file so that it could be accessible via FTP
                    @chmod($file_path_export, 0777);
                }
            }
        }

        $file_paths = array(
            'list_template' => $file_path,
            'export_template' => $file_path_export,
        );
        $this->set('file_paths', $file_paths, true);

        return $result;
    }

    /**
     * Function to perform sorting of columns
     *
     * @return mixed - false or array with sorted columns
     */
    public function sortColumns() {
        if (!$this->get('current_custom_fields')) {
            return false;
        }

        //sort the fields by position
        $columns = $this->get('current_custom_fields');
        usort($columns, array('self', 'sortFunction'));
        $this->set('current_custom_fields', $columns, true);

        return $columns;
    }

    /**
     * Function for sorting an array
     */
    public function sortFunction($a, $b) {
        if ($a['position'] == $b['position']) {
            return (preg_replace('#[\[\]]#', '', strip_tags($a['label'])) < preg_replace('#[\[\]]#', '', strip_tags($b['label']))) ? -1 : 1;
        }
        if ($a['position'] && $b['position']) {
            return ($a['position'] < $b['position']) ? -1 : 1;
        } else if ($a['position'] && !$b['position']) {
            return -1;
        } else if (!$a['position'] && $b['position']) {
            return 1;
        }
    }

    /**
     * Strips some tags from the prepared smarty template to reduce the smarty code
     *
     * @param string $source - the smarty code to be stripped
     * @param string $action - the action that defines which tags to be stripped (this is not necessarily the registry action)
     * @return string $source - the stripped code
     */
    public function stripActionTags($source, $action) {
        switch ($action) {
            case 'export':
                //remove all other action tags
                $source = preg_replace('#<list>.*?</list>(\n|\r)?#s', '', $source);
                //remove the opening/closing tag
                $source = preg_replace('#</?export>(\n|\r)?#s', '', $source);
            break;
            case 'list':
            default:
                //remove all other action tags
                $source = preg_replace('#<export>.*?</export>(\n|\r)?#s', '', $source);
                //remove the opening/closing tag
                $source = preg_replace('#</?list>(\n|\r)?#s', '', $source);
            break;
        }

        return $source;
    }
}

?>
