<?php

/**
 * Custom plugin to prepare data for print/genaration for Advance Address
 */

use Nzoom\I18n\I18n;

include 'ucbbResumeTrait.php';
include 'ubbResumeTrait.php';
include_once 'DocxTableViewer.php';

/**
 *
 */
class Custom_Factory extends Model_Factory
{
    use UniCreditResumeTrait;
    use ubbResumeTrait;

    /**
     * @var string
     */
    public string $folderName = 'advanceaddress';
    /**
     * @var
     */
    public static $registry;
    /**
     * @var
     */
    public static $model;
    /**
     * @var
     */
    public static $pattern;
    /**
     * @var
     */
    public static $paths;
    /**
     * @var
     */
    public static $settings;
    /**
     * @var
     */
    public static $pagePortraitWidth;
    /**
     * @var
     */
    public static $pagePortraitPrintableWidth;
    /**
     * @var
     */
    public static $pagePortraitHeight;
    /**
     * @var
     */
    public static $pagePortraitPrintableHeight;
    /**
     * @var
     */
    public static $pageLandscapeWidth;
    /**
     * @var
     */
    public static $pageLandscapePrintableWidth;
    /**
     * @var
     */
    public static $pageLandscapeHeight;
    /**
     * @var
     */
    public static $pageLandscapePrintableHeight;
    /**
     * @var
     */
    public static $tablesWidthFullPage;
    /**
     * @var bool
     */
    public static $landscapeCostlyApproachComparativeTables = false;
    /**
     * @var bool
     */
    public static $landscapeRevenueApproachComparativeTables = false;

    /**
     * @var PHPWord_Template $templateDocx
     */
    public static PHPWord_Template $templateDocx;
    /**
     * @var
     */
    public static $objPHPExcel;
    /**
     * @var
     */
    public static $objReader;
    /**
     * @var
     */
    public static $filePath;
    /**
     * @var string
     */
    private static string $_password = 'Oc3nk1';

    /**
     * @var bool
     */
    private static $isOps = false;

    /**
     * Code name for the form, the standard form is Advance Address, and the other are UBB and PIRAEUS
     * This is used to define which set of templates should be used when composing the spreadsheet
     */
    public static $form;

    /**
     * List of building types (nomenclatures) in id => name format
     */
    public static $building_types;

    /**
     * List of construction types (nomenclatures) in id => name format
     */
    public static $construction_types;

    /**
     * Relations of the properties. Some of the properties (main, parent) have subproperties (children)
     * e.g.: a basement could be subproperty of an apartment
     * This is stored in the bb_variant variable
     */
    public static $property_relations;

    /**
     * A detailed array of all MAIN properties and their children
     */
    public static $main_properties;

    /**
     * A list of ALL property names (including children properties)
     */
    public static $all_properties;

    /**
     * A list of ALL MAIN property types (excluding children properties)
     */
    public static $property_types;

    /**
     * For each report there should be only one mask (configurator)
     */
    public static $used_mask;
    /**
     * @var
     */
    public static $used_mask_id;

    /**
     * Evaluation approaches. Each approach (sound like method in Bulgarian) has code name
     */
    public static $approaches;

    /**
     * Active spreadsheet number
     */
    public static int $active_sheet_index = -1;

    /**
     * All approach spreadsheets are named attachments. Store the current attachment/approach
     */
    public static int $attachments_index = 0;

    /**
     * Supervisors - employee that signed as row type = 2 (supervisor)
     */
    public static array $supervisors = array();

    /**
     * Assessors - employee that signed as row type = 5 (assessor)
     */
    public static array $assessors = array();

    /**
     * Variable suffixes for different masks
     */
    public static array $mask_suffixes = array(
        'residential_property' => '',
        'plot' => '_one',
        'hotel' => '_two',
        'gas_station' => '_three',
        'energy_facility' => '_four',
        'state_municipal_property' => '_five',
        'infrastructure' => '_six',
        'machine' => '_seven',
        'business' => '_eight',
        'land' => '_nine',
        'assets' => '_ten',
        'forest' => '_eleven',
        'financial_asset' => '_twelve',
    );

    /**
     * Decimal delimiter - comma
     */
    public const DECIMAL_DELIMITER = ',';

    /**
     * @var string Main section properties, detected from the template and used to return to portrait
     */
    private static string $sectPr = '';

    /**
     * @var string Footer and header string extrakted from main section properties, so they can be added to the landscape sections
     */
    private static string $footersAndHeaders = '';

    /**
     * @var string Appendix template
     */
    private static string $ADX_template = '[content]';

    /*
     * Ranges within the worksheets and their placeholders
     * Placeholders are those named ranges/cells that should be numbered and replaced with nZoom values
     */
    /**
     * @var array
     */
    private static array $_ranges = array(
        'PRICE_REPORT' => array(
            'MAIN_PROPERTY_DATA' => array(
                'MAIN_PROPERTY_ADDRESS',
                'MAIN_PROPERTY_AREA',
                'MAIN_PROPERTY_CITY',
                'MAIN_PROPERTY_DESCRIPTION',
                'MAIN_PROPERTY_MUNICIPALITY',
                'MAIN_PROPERTY_POST_CODE',
                'MAIN_PROPERTY_TYPE',
                'MAIN_PROPERTY_USAGE'),
            'PROPERTY_DETAILS' => array(
                'PROPERTY_DETAILS_NUMBER',
                'PROPERTY_DETAILS_TYPE',
                'PROPERTY_DETAILS_FLOOR',
                'PROPERTY_DETAILS_UP_AREA',
                'PROPERTY_DETAILS_IDEAL_AREA',
                'PROPERTY_DETAILS_REAL_UP_AREA',
                'PROPERTY_DETAILS_ADOPTED_AREA'),
            'APPROACH_DESCRIPTION' => array(
                'APPROACH_DESCRIPTION'),
            'APPROACHES_DATA' => array(
                'APPROACH_NAME',
                'APPROACH_VALUE_BGN',
                'APPROACH_VALUE_EUR',
                'APPROACH_WEIGHT'),
        ),
        'COST_APPROACH' => array(
            'COST_APPROACH_ROW_DATA' => array(
                'COST_APPROACH_ROW_NUMBER',
                'COST_APPROACH_ROW_TYPE',
                'COST_APPROACH_ROW_UP_AREA',
                'COST_APPROACH_ROW_ADOPTED_AREA',
                'COST_APPROACH_ROW_IDEAL_AREA',
                'COST_APPROACH_ROW_ADOPTED_AREA_TOTAL',
                'COST_APPROACH_ROW_CONSTRUCTION_YEAR',
                'COST_APPROACH_ROW_LAND_AREA',
                'COST_APPROACH_ROW_SQM_BGN',
                'COST_APPROACH_ROW_SQM_EUR'),
            'COST_APPROACH_PROPERTY_DATA' => array(
                'COST_APPROACH_PROPERTY_TYPE',
                'COST_APPROACH_PROPERTY_USAGE'),
            'COST_APPROACH_ANALOGUES_LAND_DATA' => array(
                'COST_APPROACH_ANALOGUE_LAND_AREA',
                'COST_APPROACH_ANALOGUE_LAND_PRICE_EUR'),
        ),
        'INCOME_APPROACH' => array(
            'INCOME_APPROACH_ROW_DATA' => array(
                'INCOME_APPROACH_ROW_NUMBER',
                'INCOME_APPROACH_ROW_TYPE',
                'INCOME_APPROACH_ROW_UP_AREA',
                'INCOME_APPROACH_ROW_ADOPTED_AREA',
                'INCOME_APPROACH_ROW_PRICE_SQM_EUR',
                'INCOME_APPROACH_ROW_SQM_BGN',
                'INCOME_APPROACH_ROW_SQM_EUR'),
            'INCOME_APPROACH_PROPERTY_DATA' => array(
                'INCOME_APPROACH_PROPERTY_TYPE',
                'INCOME_APPROACH_PROPERTY_USAGE'),
            'INCOME_APPROACH_ANALOGUES_DATA' => array(
                'INCOME_APPROACH_ANALOGUE_AREA',
                'INCOME_APPROACH_ANALOGUE_PRICE_SQM_EUR'),
        ),
        'SALES_COMPARISON_APPROACH' => array(
            'SALES_COMPARISON_APPROACH_PROPERTY_DATA' => array(
                'SALES_COMPARISON_APPROACH_PROPERTY_TYPE',
                'SALES_COMPARISON_APPROACH_PROPERTY_USAGE'),
            'SALES_COMPARISON_APPROACH_ANALOGUE_DATA' => array(
                'SALES_COMPARISON_APPROACH_ANALOGUE_AREA',
                'SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_BGN',
                'SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_AREA',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_AREA',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_AREA',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION1_',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION1_',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION1_',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION2_',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION2_',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION2_',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION3_',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION3_',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION3_',
                'SALES_COMPARISON_APPROACH_ANALOGUE1_FINAL_PRICE_SQM_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE2_FINAL_PRICE_SQM_EUR',
                'SALES_COMPARISON_APPROACH_ANALOGUE3_FINAL_PRICE_SQM_EUR'),
            'SALES_COMPARISON_APPROACH_ANALOGUE_DESCRIPTION' => array(),
        ),
        'NROCZZ' => array(
            'NROCZZ_PROPERTY_DATA' => array(
                'NROCZZ_PROPERTY_TYPE',
                'NROCZZ_PROPERTY_LOCATION'
            ),
            'NROCZZ_ROW_DATA' => array(
                'NROCZZ_ROW_NUMBER',
                'NROCZZ_ROW_AREA',
                'NROCZZ_ROW_PRICE_BGN',
            ),
        ),
        'PIRAEUS1' => array(
            'PIRAEUS1_MAIN_PROPERTY_DATA' => array(
                'PIRAEUS1_MAIN_PROPERTY_ADDRESS',
                'PIRAEUS1_MAIN_PROPERTY_AREA',
                'PIRAEUS1_MAIN_PROPERTY_CITY',
                'PIRAEUS1_MAIN_PROPERTY_MUNICIPALITY'),
            'PIRAEUS1_PROPERTY_DETAILS' => array(
                'PIRAEUS1_PROPERTY_DETAILS_LAND',
                'PIRAEUS1_PROPERTY_DETAILS_UP_AREA',
                'PIRAEUS1_PROPERTY_DETAILS_REAL_UP_AREA'),
            'PIRAEUS1_SALES_COMPARISON_ANALOGUES_DATA' => array(
                'PIRAEUS1_ANALOGUE1_PRICE_EUR',
                'PIRAEUS1_ANALOGUE2_PRICE_EUR',
                'PIRAEUS1_ANALOGUE3_PRICE_EUR',
                'PIRAEUS1_ANALOGUE1_AREA',
                'PIRAEUS1_ANALOGUE2_AREA',
                'PIRAEUS1_ANALOGUE3_AREA',
                'PIRAEUS1_ANALOGUE1_PRICE_SQM_EUR',
                'PIRAEUS1_ANALOGUE2_PRICE_SQM_EUR',
                'PIRAEUS1_ANALOGUE3_PRICE_SQM_EUR',
                'PIRAEUS1_ANALOGUE1_CORRECTION1_',
                'PIRAEUS1_ANALOGUE2_CORRECTION1_',
                'PIRAEUS1_ANALOGUE3_CORRECTION1_',
                'PIRAEUS1_ANALOGUE1_CORRECTION2_',
                'PIRAEUS1_ANALOGUE2_CORRECTION2_',
                'PIRAEUS1_ANALOGUE3_CORRECTION2_',
                'PIRAEUS1_ANALOGUE1_CORRECTION3_',
                'PIRAEUS1_ANALOGUE2_CORRECTION3_',
                'PIRAEUS1_ANALOGUE3_CORRECTION3_',
                'PIRAEUS1_ANALOGUE1_FINAL_PRICE_SQM_EUR',
                'PIRAEUS1_ANALOGUE2_FINAL_PRICE_SQM_EUR',
                'PIRAEUS1_ANALOGUE3_FINAL_PRICE_SQM_EUR',
                'PIRAEUS1_AVERAGE_PRICE_SQM_EUR'),
        ),
        'PIRAEUS2' => array(
            'PIRAEUS2_MAIN_PROPERTY_DATA' => array(
                'PIRAEUS2_MAIN_PROPERTY_ADDRESS',
                'PIRAEUS2_MAIN_PROPERTY_AREA',
                'PIRAEUS2_MAIN_PROPERTY_CITY',
                'PIRAEUS2_MAIN_PROPERTY_MUNICIPALITY'),
        ),
        'UBB' => array(
            'UBB_MAIN_PROPERTY_DATA' => array(
                'UBB_MAIN_PROPERTY_ADDRESS',
                'UBB_MAIN_PROPERTY_CITY',
                'UBB_MAIN_PROPERTY_MUNICIPALITY',
                'UBB_MAIN_PROPERTY_POST_CODE',
                'UBB_MAIN_PROPERTY_TYPE'),
            'UBB_COST_APPROACH_ROW_DATA' => array(
                'UBB_COST_APPROACH_ROW_TYPE',
                'UBB_COST_APPROACH_ROW_ADOPTED_AREA_TOTAL'),
            'UBB_INCOME_APPROACH_ROW_DATA' => array(
                'UBB_INCOME_APPROACH_ROW_TYPE',
                'UBB_INCOME_APPROACH_ROW_ADOPTED_AREA',
                'UBB_INCOME_APPROACH_ROW_PRICE_SQM_EUR'),
            'UBB_INCOME_APPROACH_ANALOGUES_DATA' => array(
                'UBB_INCOME_APPROACH_ANALOGUE1_PRICE_SQM_EUR',
                'UBB_INCOME_APPROACH_ANALOGUE2_PRICE_SQM_EUR',
                'UBB_INCOME_APPROACH_ANALOGUE3_PRICE_SQM_EUR'),
            'UBB_SALES_COMPARISON_APPROACH_ANALOGUES_DATA' => array(
                'UBB_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR',
                'UBB_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR',
                'UBB_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR'),
            'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_DATA' => array(
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_AREA',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_BGN',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE1_AREA',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE2_AREA',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE3_AREA',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE1_FINAL_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE2_FINAL_PRICE_SQM_EUR',
                'UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE3_FINAL_PRICE_SQM_EUR'),
            'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_DATA' => array(
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_AREA',
                'UBB_SALES_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_BGN',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE1_AREA',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE2_AREA',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE3_AREA',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE1_FINAL_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE2_FINAL_PRICE_SQM_EUR',
                'UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE3_FINAL_PRICE_SQM_EUR'),
        )
    );

    /**
     * @var null|array
     */
    private static ?array $vars = null;
    /**
     * @var null|array
     */
    private static ?array $report0Vars = null;
    /**
     * @var null|string
     */
    private static ?string $rating_type = null;
    /**
     * @var
     */
    private static $hierarchy_group_idx;
    /**
     * @var
     */
    private static $hierarchy_group_parents;

    /**
     * @var array
     */
    private static $related_records = [];

    /**
     * @var null|DocxTableViewer
     */
    private static ?DocxTableViewer $tblViewer = null;

    /**
     * This is a list of disaster fields with numeric values
     * which should be concatenated with 'a_'
     * so to be similar as tags
     * @var array|string[]
     */
    private static array $disasterFields = [
        'disas_check_floods',
        'disas_check_flash_flood',
        'disas_check_storm',
        'disas_check_sea_waves',
        'disas_check_fire',
        'disas_check_earthquakes',
        'disas_landslide',
    ];

    /**
     * @var array|string[]
     */
    private static array $riskLevelMessage = [
        1 => 'Много нисък',
        2 => 'Нисък',
        3 => 'Среден',
        4 => 'Висок',
        5 => 'Много висок',
    ];

    /**
     * Translates custom labels defined in the i18n of the plugin
     *
     * @param $param
     * @param string $lang
     * @return void
     */
    public static function i18n($param, string $lang = ''): string
    {
        if (empty($lang)) {
            $lang = self::$model->get('model_lang');
        }
        $customFile = realpath(dirname(__FILE__) . '/../i18n/' . $lang) . '/print.ini';
        $i18n = new I18n($lang, $customFile);

        return $i18n->translate($param);
    }

    /**
     * Generates price report of a property (or properties) in XLS format
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     * @return array $file_path - temp file path of the generated file
     */
    public static function generatePriceReport(&$registry, &$model, &$pattern, &$params = array())
    {
        self::$registry = &$registry;
        self::$model = &$model;
        self::$pattern = &$pattern;

        set_time_limit(0);
        ini_set('memory_limit', '2048M');
        require_once PH_PHPEXCEL_DIR . 'PHPExcel/IOFactory.php';
        //ToDo: define reader type (depending on the MS Office version used by Advance Address company)
        self::$objReader = PHPExcel_IOFactory::createReader('Excel2007');

        self::$objPHPExcel = new PHPExcel();

        //set default font anf font size (Arial, 11)
        self::$objPHPExcel->getDefaultStyle()->getFont()->setName('Arial')->setSize(11);

        //remove default sheet
        self::$objPHPExcel->removeSheetByIndex(0);

        self::_prepareBasicData();

        switch (self::$form) {
            case 'UBB':
                //special template for UBB bank
                if (!self::_prepareUBBMainSheet()) {
                    return false;
                }
                break;
            case 'PIRAEUS':
                self::_preparePiraeusSheets();
                //IMPORTANT: do not break this case!!!
                //PIRAEUS form has exactly the same form as the default one (the default case)
                //with two additional sheets prepared in self::_preparePiraeusSheets()
                // no break
            default:
                //SHEET: "PROPERTY ASSESSMENT REPORT"
                self::_prepareMainSheet();

                //SHEET: "NROCZZ"
                if (array_key_exists('NROCZZ', self::$approaches)) {
                    //prepares sheet only for mask land!
                    self::_prepareNROCZZSheet();
                }

                //SHEET: "REMANENT APPROACH"
                if (array_key_exists('REMANENT_APPROACH', self::$approaches)) {
                    //prepares sheet only for mask plot!
                    self::_prepareRemanentApproachSheet();
                }

                //SHEET: "COST APPROACH"
                if (array_key_exists('COST_APPROACH', self::$approaches)) {
                    self::_prepareCostApproachSheet();
                }

                //SHEET: "INCOME APPROACH"
                if (array_key_exists('INCOME_APPROACH', self::$approaches)) {
                    self::_prepareIncomeApproachSheet();
                }

                //SHEET: "SALES COMPARISON APPROACH"
                if (array_key_exists('SALES_COMPARISON_APPROACH', self::$approaches)) {
                    self::_prepareSalesComparisonApproachSheet();
                }

                //SHEET: "STAGE OF COMPLETION"
                //prepares this sheet only for masks: 'residential_property', 'hotel' & 'state_municipal_property'
                self::_prepareStageOfCompletionSheet();
        }

        //set first sheet as active
        if (self::$objPHPExcel->getActiveSheetIndex() > -1) {
            self::$objPHPExcel->setActiveSheetIndex(0);
        }

        $objWriter = PHPExcel_IOFactory::createWriter(self::$objPHPExcel, 'Excel2007');

        //file path is to a temp file
        self::$filePath = tempnam(ini_get('upload_tmp_dir'), 'xls');

        try {
            //IMPORTANT: do not perform calculations
            $objWriter->setPreCalculateFormulas(false);
            $objWriter->save(self::$filePath);
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Prepares some basic data used in all the excel sheets
     *
     */
    private static function _prepareBasicData()
    {

        //check which of the forms should be used
        //forms differ depending on bank stated
        switch (self::$model->getValueByName('bank_id')) {
            case '6':
                self::$form = 'UBB';
                break;
            case '4':
                self::$form = 'PIRAEUS';
                break;
            default:
                self::$form = 'DEFAULT';
                break;
        }

        //building types
        $query = 'SELECT n.id, ni18n.name ' . "\n" .
            'FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni18n' . "\n" .
            'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
            ' ON  ni18n.parent_id=n.id ' . "\n" .
            'WHERE n.type=9 AND ni18n.lang="' . self::$model->get('model_lang') . '"';
        self::$building_types = self::$registry['db']->GetAssoc($query);

        //construction types
        $query = 'SELECT n.id, ni18n.name ' . "\n" .
            'FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni18n' . "\n" .
            'INNER JOIN ' . DB_TABLE_NOMENCLATURES . ' as n' . "\n" .
            ' ON  ni18n.parent_id=n.id ' . "\n" .
            'WHERE n.type=11 AND ni18n.lang="' . self::$model->get('model_lang') . '"';
        self::$construction_types = self::$registry['db']->GetAssoc($query);

        //masks (configurators)
        $config_types = self::$model->getBBElements();
        $masks = array();
        foreach ($config_types as $ctype) {
            $masks[$ctype['id']] = str_replace('_config', '', $ctype['name']);
        }

        //get property relations (stored in bb_variant)
        self::$property_relations = self::$model->getValueByName('bb_variant');

        //variant names (for all properties including children)
        self::$all_properties = self::$model->getValueByName('bb_name');

        //get property BB data
        $properties_bb_data = self::$model->getBB(array('model_id' => self::$model->get('id'),
            'order_by' => 'meta_id'));

        //prepare property details
        self::$main_properties = array();
        self::$property_types = array();
        self::$approaches = array();

        $main_idx = 0;
        foreach ($properties_bb_data as $data) {
            //ToDo: remove this code, just after automation is launched
            //an automation should restrict that only one mask should be used per report
            if (!empty(self::$used_mask) && self::$used_mask != $masks[$data['meta_id']]) {
                //this does not allow different masks, allow only first!!!
                unset(self::$all_properties[$data['id']]);
                continue;
            } else {
                //define used mask
                self::$used_mask_id = $data['meta_id'];
                self::$used_mask = $masks[$data['meta_id']];
            }

            $data['params']['id'] = $data['id'];
            $data['params']['mask'] = $masks[$data['meta_id']];
            $data['params']['parent'] = self::$property_relations[$data['id']];
            //children are prepared further down the code
            $data['params']['children'] = array();
            $data['params']['children_ids'] = array_keys(self::$property_relations, $data['id']);
            $data['params']['is_main'] = empty(self::$property_relations[$data['id']]);
            $data['params']['variant_name'] = self::$all_properties[$data['id']];
            self::$main_properties[$data['id']] = $data['params'];

            //suffix
            $suffix = self::$mask_suffixes[$data['params']['mask']];


            if ($data['params']['is_main']) {
                $main_idx++;
            }

            //prepare property types (of main properties only) as they are used in some of the sheets
            if (
                $data['params']['is_main'] && isset($data['params']['type_object' . $suffix]) &&
                !in_array($data['params']['type_object' . $suffix], self::$property_types)
            ) {
                self::$property_types[$main_idx] = $data['params']['type_object' . $suffix];
            }

            //prepare approaches
            if (!empty($data['params']['metod_one' . $suffix . '_id'])) {
                self::$approaches[] = $data['params']['metod_one' . $suffix . '_id'];
            }
            if (!empty($data['params']['metod_two' . $suffix . '_id'])) {
                self::$approaches[] = $data['params']['metod_two' . $suffix . '_id'];
            }
            if (!empty($data['params']['metod_three' . $suffix . '_id'])) {
                self::$approaches[] = $data['params']['metod_three' . $suffix . '_id'];
            }
            if (!empty($data['params']['metod_four' . $suffix . '_id'])) {
                self::$approaches[] = $data['params']['metod_four' . $suffix . '_id'];
            }
        }

        //get some more data for approaches
        if (!empty(self::$approaches)) {
            //approaches (method) names and descriptions
            self::$approaches = array_unique(self::$approaches);
            $query = 'SELECT ' . "\n" .
                ' (SELECT IF(value!="" AND value IS NOT NULL, value, ni.name) ' . "\n" .
                '  FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as n, ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                '  WHERE fm.name="code_name" AND fm.id=n.var_id AND n.model_id=ni.parent_id) as code_name,' . "\n" .
                ' (SELECT value' . "\n" .
                '  FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as n, ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                '  WHERE fm.name="method_description" AND fm.id=n.var_id AND n.model_id=ni.parent_id) as description,' . "\n" .
                ' ni.parent_id, ni.name ' . "\n" .
                'FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' as ni ' . "\n" .
                'WHERE ni.parent_id IN (' . implode(', ', self::$approaches) . ') AND lang="' . self::$model->get('model_lang') . '"';
            self::$approaches = self::$registry['db']->GetAssoc($query);
            if (empty(self::$approaches)) {
                self::$approaches = array();
            }
        }

        //prepare all children and remove them from the list
        foreach (self::$main_properties as $id => $data) {
            if (!$data['is_main']) {
                //child, push it to the parent
                self::$main_properties[$data['parent']]['children'][$id] = $data;
                unset(self::$main_properties[$id]);
            }
        }

        //prepare employees data: names, phones, emails, signature
        $vars = self::$model->getAssocVars();
        //get the index of the recording_role_type variable
        $recording_role_type_idx = array_search('recording_role_type', $vars['recording_role_group']['names']);
        //get he index of the employee that signs (recording_role_id)
        $employee_idx = array_search('recording_role_id', $vars['recording_role_group']['names']);
        //get the row with the employee that signed as role type = 2 (supervisor) and row type = 5 (assessor)
        //these to variables should be used ro caching only
        $certificates = array();
        $employees = array();
        foreach ($vars['recording_role_group']['values'] as $row) {
            $employee_id = $row[$employee_idx];
            if (!empty($row[$recording_role_type_idx]) && ($row[$recording_role_type_idx] == 2 || $row[$recording_role_type_idx] == 5)) {
                if (!isset($employees[$employee_id])) {
                    //cache the employee
                    $employee = Customers::searchOne(self::$registry, array('where' => array('c.id=' . $employee_id), 'sanitize' => true));
                    if ($employee) {
                        $employees[$employee_id] = array(
                            'name' => $employee->get('name') . ' ' . $employee->get('lastname'),
                            'email' => $employee->get('email'),
                            'phone' => $employee->get('phone'),
                            'vars' => $employee->getAssocVars(),
                        );
                        unset($employee);
                    } else {
                        //something went wrong, skip this row
                        continue;
                    }
                }
                foreach ($employees[$employee_id]['vars']['type_sertificate_id']['value'] as $idx => $certificate_id) {
                    //get and cache the certificate
                    if (!isset($certificates[$certificate_id])) {
                        //get the masks assigned to the certificate
                        include_once(PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.factory.php');
                        $certificate = Nomenclatures::searchOne(self::$registry, array('where' => array('n.id=\'' . $certificate_id . '\''),
                            'sanitize' => true));
                        if ($certificate) {
                            //cache only masks
                            $cvars = $certificate->getAssocVars();
                            $certificates[$certificate_id] = $cvars['mask']['value'];
                            unset($certificate);
                        } else {
                            //something went wrong, skip this certificate
                            continue;
                        }
                    }
                    //check relation between certificate and used masks
                    //GET THE FIRST RELATED CERTIFICATE
                    //not very subtle, but the admin should be responsible for the data integrity!!!
                    //2 (supervisor)
                    if (in_array(self::$used_mask_id, $certificates[$certificate_id]) && $row[$recording_role_type_idx] == 2) {
                        self::$supervisors[$employee_id] = array(
                            'name' => $employees[$employee_id]['name'],
                            'email' => $employees[$employee_id]['email'],
                            'phone' => $employees[$employee_id]['phone'],
                            'signature' => (!empty($employees[$employee_id]['vars']['file_signature']['value'][$idx])) ? $employees[$employee_id]['vars']['file_signature']['value'][$idx]->getFileURL('viewfile') : '',
                        );
                    }
                    //5 (assessor)
                    if (in_array(self::$used_mask_id, $certificates[$certificate_id]) && $row[$recording_role_type_idx] == 5) {
                        self::$assessors[$employee_id] = array(
                            'name' => $employees[$employee_id]['name'],
                            'email' => $employees[$employee_id]['email'],
                            'phone' => $employees[$employee_id]['phone'],
                            'signature' => (!empty($employees[$employee_id]['vars']['signature_file']['value'][$idx])) ? $employees[$employee_id]['vars']['signature_file']['value'][$idx]->getFileURL('viewfile') : '',
                        );
                    }
                }
            }
        }
    }

    /**
     * Prepares Piraeus bank two sheets (the other use the default form)
     *
     */
    private static function _preparePiraeusSheets()
    {
        $files = self::$pattern->getAttachments(array('fi18n.name = \'PIRAEUS\''));
        if (!$files) {
            //no file, nothing to do!
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));

        //Sheet Piraeus 1
        $template->setActiveSheetIndex(0);
        $sheet = $template->getActiveSheet();

        self::_copyRanges($template, 'PIRAEUS1', array('PIRAEUS1_MAIN_PROPERTY_DATA'), (count(self::$main_properties)) ? count(self::$main_properties) : 1);
        self::_copyRanges($template, 'PIRAEUS1', array('PIRAEUS1_PROPERTY_DETAILS'), (count(self::$all_properties)) ? count(self::$all_properties) : 1);
        self::_copyRanges($template, 'PIRAEUS1', array('PIRAEUS1_SALES_COMPARISON_ANALOGUES_DATA'), (count(self::$main_properties)) ? count(self::$main_properties) : 1);

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        $average = array('cost' => array(), 'sales_comparison' => array(), 'income' => array());
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            $sheet->getCell('PIRAEUS1_MAIN_PROPERTY_AREA' . $idx)->setValue('=MAIN_PROPERTY_AREA' . $idx);
            $sheet->getCell('PIRAEUS1_MAIN_PROPERTY_CITY' . $idx)->setValue('=MAIN_PROPERTY_CITY' . $idx);
            $sheet->getCell('PIRAEUS1_MAIN_PROPERTY_MUNICIPALITY' . $idx)->setValue('=MAIN_PROPERTY_MUNICIPALITY' . $idx);
            $sheet->getCell('PIRAEUS1_MAIN_PROPERTY_ADDRESS' . $idx)->setValue('=MAIN_PROPERTY_ADDRESS' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_AREA' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_AREA' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_AREA' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_AREA' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_AREA' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_AREA' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_PRICE_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_PRICE_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_PRICE_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_EUR' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_CORRECTION1_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION1_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_CORRECTION1_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION1_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_CORRECTION1_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION1_' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_CORRECTION2_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION2_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_CORRECTION2_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION2_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_CORRECTION2_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION2_' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_CORRECTION3_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_CORRECTION3_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_CORRECTION3_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_CORRECTION3_' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_CORRECTION3_' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_CORRECTION3_' . $idx);

            $sheet->getCell('PIRAEUS1_ANALOGUE1_FINAL_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE1_FINAL_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE2_FINAL_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE2_FINAL_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('PIRAEUS1_ANALOGUE3_FINAL_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE3_FINAL_PRICE_SQM_EUR' . $idx);

            $sheet->getCell('PIRAEUS1_AVERAGE_PRICE_SQM_EUR' . $idx)->setValue('=SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR' . $idx);

            $average['cost'][] = 'COST_APPROACH_ROW_SQM_EUR' . $idx;
            $average['sales_comparison'][] = 'SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR' . $idx;
            $average['income'][] = 'INCOME_APPROACH_ROW_SQM_EUR' . $idx;
        }

        if (!empty(self::$all_properties)) {
            $sheet->getCell('PIRAEUS1_AVERAGE_COST_APPROACH_SQM_EUR')->setValue('=(' . implode('+', $average['cost']) . ')/' . count($average['cost']));
            $sheet->getCell('PIRAEUS1_AVERAGE_COST_APPROACH_TOTAL_EUR')->setValue('=COST_APPROACH_TOTAL_EUR');
            $sheet->getCell('PIRAEUS1_AVERAGE_SALES_COMPARISON_APPROACH_SQM_EUR')->setValue('=(' . implode('+', $average['sales_comparison']) . ')/' . count($average['sales_comparison']));
            $sheet->getCell('PIRAEUS1_AVERAGE_SALES_COMPARISON_APPROACH_TOTAL_EUR')->setValue('=SALES_COMPARISON_APPROACH_TOTAL_EUR');
            $sheet->getCell('PIRAEUS1_AVERAGE_INCOME_APPROACH_SQM_EUR')->setValue('=(' . implode('+', $average['income']) . ')/' . count($average['income']));
            $sheet->getCell('PIRAEUS1_AVERAGE_INCOME_APPROACH_TOTAL_EUR')->setValue('=INCOME_APPROACH_TOTAL_EUR');

            $sheet->getCell('PIRAEUS1_LIQUIDATION_VALUE_SQM_EUR')->setValue('=LIQUIDATION_VALUE_SQM_EUR');
            $sheet->getCell('PIRAEUS1_LIQUIDATION_VALUE_TOTAL_EUR')->setValue('=LIQUIDATION_VALUE_TOTAL_EUR');

            $sheet->getCell('PIRAEUS1_FAIR_MARKET_PRICE_SQM_EUR')->setValue('=FAIR_MARKET_PRICE_SQM_EUR');
            $sheet->getCell('PIRAEUS1_FAIR_MARKET_PRICE_TOTAL_EUR')->setValue('=FAIR_MARKET_PRICE_TOTAL_EUR');
        }

        $idx = 0;
        foreach (self::$all_properties as $id => $pd) {
            $idx++;
            $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_UP_AREA' . $idx)->setValue('=PROPERTY_DETAILS_UP_AREA' . $idx);
            $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_REAL_UP_AREA' . $idx)->setValue('=PROPERTY_DETAILS_REAL_UP_AREA' . $idx);
            if (self::$used_mask == 'residential_property' || self::$used_mask == 'hotel' || self::$used_mask == 'state_municipal_property') {
                $coordinates = $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_LAND' . $idx)->getCoordinate();
                $sheet->getStyle($coordinates)->getProtection()->setLocked(PHPExcel_Style_Protection::PROTECTION_UNPROTECTED);
            }
        }
        //merge area total
        if (count(self::$all_properties) > 0) {
            $merge = sprintf(
                '%s%d:%s%d',
                $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_AREA_TOTAL')->getColumn(),
                $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_UP_AREA1')->getRow(),
                $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_AREA_TOTAL')->getColumn(),
                $sheet->getCell('PIRAEUS1_PROPERTY_DETAILS_UP_AREA1')->getRow() + count(self::$all_properties) - 1
            );
            $sheet->mergeCells($merge);
        }

        $sheet->getCell('PIRAEUS1_CUSTOMER')->setValue(self::$model->get('customer_name'));

        //save sheet 1
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_piraeus1_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);

        //Sheet Piraeus 2
        $template->setActiveSheetIndex(0);
        $sheet = $template->getActiveSheet();

        self::_copyRanges($template, 'PIRAEUS2', array('PIRAEUS2_MAIN_PROPERTY_DATA'), (count(self::$main_properties)) ? count(self::$main_properties) : 1);

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            $sheet->getCell('PIRAEUS2_MAIN_PROPERTY_AREA' . $idx)->setValue('=MAIN_PROPERTY_AREA' . $idx);
            $sheet->getCell('PIRAEUS2_MAIN_PROPERTY_CITY' . $idx)->setValue('=MAIN_PROPERTY_CITY' . $idx);
            $sheet->getCell('PIRAEUS2_MAIN_PROPERTY_MUNICIPALITY' . $idx)->setValue('=MAIN_PROPERTY_MUNICIPALITY' . $idx);
            $sheet->getCell('PIRAEUS2_MAIN_PROPERTY_ADDRESS' . $idx)->setValue('=MAIN_PROPERTY_ADDRESS' . $idx);
        }

        $sheet->getCell('PIRAEUS2_CUSTOMER')->setValue(self::$model->get('customer_name'));

        //save sheet 2
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_piraeus2_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares UBB bank main sheet (property assessment sheet)
     *
     */
    private static function _prepareUBBMainSheet()
    {
        //ToDo: define when to include such sheet
        //for now only masks residential_property and hotel have this sheet
        if (self::$used_mask != 'residential_property' && self::$used_mask != 'hotel' && self::$used_mask != 'state_municipal_property') {
            self::$registry['messages']->setError(self::i18n('error_ubb_workbook_cannot_be_created'));
            self::$registry['messages']->insertInSession(self::$registry);
            return false;
        }

        $files = self::$pattern->getAttachments(array('fi18n.name = \'UBB\''));
        if (!$files) {
            //no file, nothing to do!
            return false;
        }

        //Main SheetUBB
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        $sheet->getCell('REPORT_NUM')->setValue(self::$model->get('full_num'));

        //check if the customer is company or person
        $query = 'SELECT ci18n.name, IF(c.is_company, CONCAT("ЕИК ", c.eik), ci18n.lastname) as family ' . "\n" .
            'FROM ' . DB_TABLE_CUSTOMERS . ' as c ' . "\n" .
            'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' as ci18n ' . "\n" .
            '  ON c.id=ci18n.parent_id AND ci18n.lang="' . self::$registry['lang'] . '"' . "\n" .
            'WHERE c.id = ' . self::$model->get('customer');
        $customer_data = self::$registry['db']->GetRow($query);
        $sheet->getCell('REPORT_CUSTOMER')->setValue($customer_data['name']);
        $sheet->getCell('REPORT_CUSTOMER_FAMILY')->setValue($customer_data['family']);

        if (!empty(self::$assessors)) {
            $signed_employee = reset(self::$assessors);
            $sheet->getCell('REPORT_ASSESSOR')->setValue($signed_employee['name']);
            //$sheet->getCell('REPORT_ASSESSOR_EMAIL')->setValue($signed_employee['email']);
        }

        $sheet->getCell('REPORT_ASSESSMENT_REQUEST_DATE')->setValue(General::strftime('%d.%m.%Y', self::$model->get('award_date')));
        $sheet->getCell('REPORT_ORDER_DATE')->setValue(General::strftime('%d.%m.%Y', self::$model->getVarValue('order_date')));
        $sheet->getCell('REPORT_DATE')->setValue(General::strftime('%d.%m.%Y', self::$model->get('date')));
        $sheet->getCell('REPORT_OWNER_NAME')->setValue(self::$model->getVarValue('owner_name'));
        $sheet->getCell('REPORT_ASSESSMENT_PURPOSE')->setValue(self::$model->getVarValue('assessment_purpose'));

        self::_copyRanges($template, 'UBB', array('UBB_MAIN_PROPERTY_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'UBB', array('UBB_COST_APPROACH_ROW_DATA'), count(self::$all_properties));
        self::_copyRanges($template, 'UBB', array('UBB_INCOME_APPROACH_ROW_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'UBB', array('UBB_INCOME_APPROACH_ANALOGUES_DATA'), count(self::$property_types));
        self::_copyRanges($template, 'UBB', array('UBB_SALES_COMPARISON_APPROACH_ANALOGUES_DATA'), count(self::$main_properties));

        //array that saves the formula of the UBB_COST_APPROACH_LAND_TOTAL_EUR cell
        $land_area = array();

        //formula that saves the sales comparisan approach (sales) in case the properties are more than one
        $sales_comparison_average = array();

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //set values for the property (a table per main property)
            if (isset($pd['type_object' . $suffix]) && !empty(self::$building_types[$pd['type_object' . $suffix]])) {
                $sheet->getCell('UBB_MAIN_PROPERTY_TYPE' . $idx)->setValue($pd['variant_name']);
            }
            if (isset($pd['city' . $suffix])) {
                preg_match('#\[?(\d*)\]? *(.*)#', $pd['city' . $suffix], $matches);
                $post_code = $matches[1];
                $city = $matches[2];
                $sheet->getCell('UBB_MAIN_PROPERTY_CITY' . $idx)->setValue($city);
                $sheet->getCell('UBB_MAIN_PROPERTY_POST_CODE' . $idx)->setValue($post_code);
            }
            if (isset($pd['municipality' . $suffix])) {
                $sheet->getCell('UBB_MAIN_PROPERTY_MUNICIPALITY' . $idx)->setValue($pd['municipality' . $suffix]);
            }
            if (isset($pd['quarter' . $suffix]) && isset($pd['address' . $suffix]) && isset($pd['address_number' . $suffix])) {
                $address = array();
                if (!empty($pd['quarter' . $suffix])) {
                    $address[] = $pd['quarter' . $suffix];
                }
                if (!empty($pd['address' . $suffix])) {
                    $address[] = $pd['address' . $suffix];
                }
                if (!empty($pd['address_number' . $suffix])) {
                    $address[] = $pd['address_number' . $suffix];
                }
                $sheet->getCell('UBB_MAIN_PROPERTY_ADDRESS' . $idx)->setValue(implode(', ', $address));
            }

            //construction values
            if ($idx == 1 && isset($pd['construction' . $suffix])) {
                //the table BUILDING should always be JUST ONE, for the first main property found (it's silly but this is the client's request)
                $construction = array();
                foreach ($pd['construction' . $suffix] as $constr) {
                    $construction[] = self::$construction_types[$constr];
                }
                $sheet->getCell('UBB_MAIN_PROPERTY_CONSTRUCTION_TYPE')->setValue(implode(', ', $construction));
                $sheet->getCell('UBB_MAIN_PROPERTY_CONSTRUCTION_YEAR')->setValue($pd['construction_year']);
            }

            //INCOME APPROACH (RENTS)
            $sheet->getCell('UBB_INCOME_APPROACH_ROW_TYPE' . $idx)->setValue($pd['variant_name']);
            //correction as per attachment 3089 (Bug 3745, comment 13)
            //$sheet->getCell('UBB_INCOME_APPROACH_ROW_ADOPTED_AREA' . $idx)->setValue($pd['adopted_area' . $suffix]);
            //calculate type index
            if (!isset($type_idx)) {
                $type_idx = 1;
            } elseif (isset($prev_type) && $prev_type != $pd['type_object' . $suffix]) {
                $type_idx++;
            }
            $prev_type = $pd['type_object' . $suffix];
            $sheet->getCell('UBB_INCOME_APPROACH_ROW_PRICE_SQM_EUR' . $idx)->setValue('=UBB_RENTS_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR' . $type_idx);
            //END INCOME APPROACH

            //SALES COMPARISON APPROACH (SALES)
            $sheet->getCell('UBB_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR' . $idx)->setValue('=UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('UBB_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR' . $idx)->setValue('=UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('UBB_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR' . $idx)->setValue('=UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR' . $idx);
            $sales_comparison_average[] = 'UBB_SALES_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR' . $idx;
            //END SALES COMPARISON APPROACH (SALES)
        }

        $range_to_merge = $sheet->getCell()->rangeBoundaries($template->getNamedRange('UBB_COST_APPROACH_ROW_DATA_TO_MERGE')->getRange());
        //preserve the horizontal merge of cells within the vertical merge
        $merged_cells = $sheet->getMergeCells();
        $merged_columns = array();
        foreach ($merged_cells as $merged_range) {
            $r = PHPExcel_Cell::rangeBoundaries($merged_range);
            //get only those cells that are merged within the named range
            if (
                $r[0][0] >= $range_to_merge[0][0] &&
                $r[0][1] >= $range_to_merge[0][1] &&
                $r[1][0] <= $range_to_merge[1][0] &&
                $r[1][1] <= $range_to_merge[1][1]
            ) {
                //columns that should stay merged within the vertical merge
                $merged_columns[$r[0][0]] = $r[1][0];
            }
        }
        asort($merged_columns);
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];
            //COST APPROACH
            $main_property_row = $sheet->getCell('UBB_COST_APPROACH_ROW_TYPE' . $idx)->getRow();
            $main_idx = $idx;
            $adopted_area_formula = array($pd['adopted_area' . $suffix] . '*' . (!empty($pd['adopted_area_plus']) ? $pd['adopted_area_plus'] : '0') . '/100');
            $sheet->getCell('UBB_COST_APPROACH_ROW_TYPE' . $idx)->setValue($pd['variant_name']);
            //put all children below its parent
            foreach ($pd['children'] as $cd) {
                $idx++;
                $sheet->getCell('UBB_COST_APPROACH_ROW_TYPE' . $idx)->setValue(self::$building_types[$cd['type_object' . $suffix]]);
                $adopted_area_formula[] = $cd['adopted_area' . $suffix] . '*' . (!empty($cd['adopted_area_plus']) ? $cd['adopted_area_plus'] : '0') . '/100';
            }

            //the "corrected real up area" (COST_APPROACH_ROW_ADOPTED_AREA_TOTAL) should be SUM of the parent and children areas
            //correction as per attachment 3089 (Bug 3745, comment 13)
            //$sheet->getCell('UBB_COST_APPROACH_ROW_ADOPTED_AREA_TOTAL' . $main_idx)->setValue('=' . implode('+', $adopted_area_formula));

            //somes cells should be merged vertically
            $children_count = count($pd['children']);
            if ($children_count > 0) {
                for ($i = $range_to_merge[0][0] - 1; $i <= $range_to_merge[1][0] - 1; $i++) {
                    $scol = PHPExcel_Cell::stringFromColumnIndex($i);
                    $ecol = PHPExcel_Cell::stringFromColumnIndex($i);
                    if (array_key_exists($i + 1, $merged_columns)) {
                        $i = $merged_columns[$i + 1] - 1;
                        $ecol = PHPExcel_Cell::stringFromColumnIndex($i);
                        //first unmerge the merged cells
                        $unmerge = sprintf(
                            '%s%d:%s%d',
                            $scol,
                            $main_property_row,
                            $ecol,
                            $main_property_row
                        );
                        $sheet->unmergeCells($unmerge);
                        for ($j = 1; $j <= $children_count; $j++) {
                            $unmerge = sprintf(
                                '%s%d:%s%d',
                                $scol,
                                $main_property_row + $j,
                                $ecol,
                                $main_property_row + $j
                            );
                            //first unmerge the merged cells
                            $sheet->unmergeCells($unmerge);
                        }
                    }
                    $merge = sprintf(
                        '%s%d:%s%d',
                        $scol,
                        $main_property_row,
                        $ecol,
                        $main_property_row + $children_count
                    );
                    $sheet->mergeCells($merge);
                }
            }

            //define land or permit to build (from the first main property!!!)
            if (!empty($pd['land_area' . $suffix]) && !isset($land_or_permit)) {
                //value 1 means land, value 2 means "permit to build"
                if ($pd['land_area' . $suffix] == 1) {
                    $land_or_permit = 'land';
                    $land_area_meters = $pd['land_area_meters' . $suffix];
                } else {
                    $land_or_permit = 'permit';
                }
            }
            if (isset($land_or_permit) && $land_or_permit == 'permit') {
                $land_area[] = 'UBB_COST_APPROACH_ROW_ADOPTED_AREA_TOTAL' . $main_idx . '*0.2';
            }
            //END COST APPROACH
        }

        //set COST APPROACH LAND cell
        if (isset($land_or_permit)) {
            if ($land_or_permit == 'permit') {
                //permit to build 20% (*0,2) of the total cost of the property
                $sheet->getCell('UBB_COST_APPROACH_LAND_TOTAL_LABEL')->setValue(self::i18n('ubb_permit_label'));
            //correction as per attachment 3089 (Bug 3745, comment 13)
            //$sheet->getCell('UBB_COST_APPROACH_LAND_TOTAL_EUR')->setValue('=' . implode('+', $land_area));
            } else {
                //land from different sheet
                $sheet->getCell('UBB_COST_APPROACH_LAND_TOTAL_LABEL')->setValue(self::i18n('ubb_land_label'));
                $sheet->getCell('UBB_MAIN_LAND_AREA_DOCS')->setValue($land_area_meters);
                $sheet->getCell('UBB_COST_APPROACH_LAND_SQM_EUR')->setValue('=UBB_LAND_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR');
            }
        }

        //income and sales comparison approaches data linked with the additional sheets
        $idx = 0;
        foreach (self::$property_types as $main_idx => $ptype) {
            $idx++;
            //there is a big analogue table for each main property to define its price
            $sheet->getCell('UBB_INCOME_APPROACH_ANALOGUE1_PRICE_SQM_EUR' . $idx)->setValue('=UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE1_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('UBB_INCOME_APPROACH_ANALOGUE2_PRICE_SQM_EUR' . $idx)->setValue('=UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE2_PRICE_SQM_EUR' . $idx);
            $sheet->getCell('UBB_INCOME_APPROACH_ANALOGUE3_PRICE_SQM_EUR' . $idx)->setValue('=UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE3_PRICE_SQM_EUR' . $idx);
        }

        //set average PRICE per SQM for sales comparison approach
        if (count($sales_comparison_average) == 1) {
            $average_formula = '=UBB_SALES_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR1';
        } else {
            $average_formula = '=(' . implode('+', $sales_comparison_average) . ')/' . count($sales_comparison_average);
        }
        $sheet->getCell('UBB_SALES_COMPARISON_AVERAGE_PRICE_SQM_EUR')->setValue($average_formula);

        //save sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_ubb_main_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);


        //SHEET SALES COMPARISON RENTS
        $template->setActiveSheetIndex(0);
        $sheet = $template->getActiveSheet();

        //the analogues table
        self::_copyRanges($template, 'UBB', array('UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_DATA'), count(self::$property_types));

        $idx = 0;
        $main_idx = null;
        foreach (self::$property_types as $main_idx => $ptype) {
            $idx++;
            if (is_null($main_idx)) {
                $main_idx = $idx;
            }
            //set the area of each property type in each big analogue table
            $sheet->getCell('UBB_RENTS_SALES_COMPARISON_APPROACH_ANALOGUE_AREA' . $idx)->setValue('=UBB_INCOME_APPROACH_ROW_ADOPTED_AREA' . $main_idx);
            //$sheet->getCell('UBB_RENTS_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR' . $idx)->setValue('=HLOOKUP(MIN(INDIRECT("D"&ROW()-2):INDIRECT("F"&ROW()-2));INDIRECT("D"&ROW()-2):INDIRECT("F"&ROW()-1);2;FALSE)');
            //calculate main_idx
            $main_idx += count($pd['children']);
        }

        //save sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_ubb_sales_comparison_rents_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);


        //SHEET SALES COMPARISON SALES
        $template->setActiveSheetIndex(0);
        $sheet = $template->getActiveSheet();

        //the analogues table
        self::_copyRanges($template, 'UBB', array('UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_DATA'), count(self::$main_properties));

        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;
            //set the area of each property in each big analogue table
            $sheet->getCell('UBB_SALES_SALES_COMPARISON_APPROACH_ANALOGUE_AREA' . $idx)->setValue('=UBB_INCOME_APPROACH_ROW_ADOPTED_AREA' . $idx);
            //$sheet->getCell('UBB_SALES_SALES_COMPARISON_APPROACH_AVERAGE_PRICE_SQM_EUR' . $idx)->setValue('=HLOOKUP(MIN(INDIRECT("D"&ROW()-2):INDIRECT("F"&ROW()-2));INDIRECT("D"&ROW()-2):INDIRECT("F"&ROW()-1);2;FALSE)');
        }

        //save sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_ubb_sales_comparison_sales_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);

        //SHEET SALES COMPARISON LAND
        if (isset($land_or_permit) && $land_or_permit == 'land') {
            $template->setActiveSheetIndex(0);
            $sheet = $template->getActiveSheet();

            //save sheet
            self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
            self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
            self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_ubb_sales_comparison_land_title'));
            self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
            self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
        }

        return true;
    }

    /**
     * Prepares data for cost approach sheet
     *
     */
    private static function _prepareMainSheet()
    {
        //ToDo: define the the template according to the client (bank or other institution) if it is necessary
        $files = self::$pattern->getAttachments(array('fi18n.name = \'PROPERTY_ASSESSMENT_REPORT\''));
        if (!$files) {
            //no file, nothing to do!
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        //office
        $office_name = '';
        $office_address = '';
        if (self::$model->get('office')) {
            include_once(PH_MODULES_DIR . 'offices/models/offices.factory.php');
            $office = Offices::searchOne(self::$registry, array('where' => array('o.id=' . self::$model->get('office')),
                'sanitize' => true));
            if ($office) {
                $office_name = $office->get('name');
                $office_address = preg_replace('#[\r\n|\r|\n]#', ' ', $office->get('description'));
                unset($office);
            }
        }

        $sheet->getCell('REPORT_NUM')->setValue(self::$model->get('full_num'));
        $sheet->getCell('REPORT_CUSTOM_NUM')->setValue(self::$model->get('custom_num'));
        $sheet->getCell('REPORT_DATE')->setValue(General::strftime('%d.%m.%Y', self::$model->get('date')));
        $sheet->getCell('REPORT_ORDER_DATE')->setValue(General::strftime('%d.%m.%Y', self::$model->getVarValue('order_date')));
        $sheet->getCell('REPORT_OFFICE')->setValue($office_name);
        $sheet->getCell('REPORT_OFFICE_ADDRESS')->setValue($office_address);
        $sheet->getCell('REPORT_RATING_TYPE')->setValue(self::$model->getVarValue('rating_type'));
        if (!empty(self::$assessors)) {
            $signed_employee = reset(self::$assessors);
            $sheet->getCell('REPORT_ASSESSOR')->setValue($signed_employee['name']);
            //as per Bug 3507, comment 22 the email should be hardcoded in the template
            //$sheet->getCell('REPORT_ASSESSOR_EMAIL')->setValue($signed_employee['email']);
            //the phone is hardcoded in the template
            //$sheet->getCell('REPORT_ASSESSOR')->setValue($signed_employee['phone']);
        }
        if (!empty(self::$supervisors)) {
            $signed_employee = reset(self::$supervisors);
            $sheet->getCell('REPORT_SUPERVISOR')->setValue($signed_employee['name']);
        }

        self::_copyRanges($template, 'PRICE_REPORT', array('MAIN_PROPERTY_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'PRICE_REPORT', array('PROPERTY_DETAILS'), count(self::$all_properties));

        $show_land_table = false;

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //define the flag for land table
            if (self::$used_mask == 'residential_property' && isset($pd['land_area' . $suffix]) && $pd['land_area' . $suffix] == 1) {
                $show_land_table = true;
            }

            //fill in the LAND area
            //IMPORTANT: the first property defines the main land area
            //discussed in skype conversation (19.06.2012 15:30 with Yana)
            if (!isset($main_land_area)) {
                if (self::$used_mask == 'land') {
                    $main_land_area = true;
                    $sheet->getCell('MAIN_LAND_AREA')->setValue($pd['land_area_nine']);
                } elseif (self::$used_mask == 'plot') {
                    $main_land_area = true;
                    $sheet->getCell('MAIN_LAND_AREA')->setValue($pd['up_area_one']);
                } elseif ($show_land_table) {
                    $main_land_area = true;
                    $sheet->getCell('MAIN_LAND_AREA')->setValue($pd['land_area_meters']);
                }
            }

            //set values for the property (a table per main property)
            if (isset($pd['type_object' . $suffix]) && !empty(self::$building_types[$pd['type_object' . $suffix]])) {
                $sheet->getCell('MAIN_PROPERTY_TYPE' . $idx)->setValue(self::$building_types[$pd['type_object' . $suffix]]);
            }
            if (!empty(self::$all_properties[$pd['id']])) {
                $sheet->getCell('MAIN_PROPERTY_DESCRIPTION' . $idx)->setValue(self::$all_properties[$pd['id']]);
            }
            if (isset($pd['used_method' . $suffix])) {
                $sheet->getCell('MAIN_PROPERTY_USAGE' . $idx)->setValue($pd['used_method' . $suffix]);
            }
            if (isset($pd['city' . $suffix])) {
                @list($post_code, $city) = explode(',', preg_replace('#\[?(\d+)\]? (.*)#', '\1,\2', $pd['city' . $suffix]));
                $sheet->getCell('MAIN_PROPERTY_CITY' . $idx)->setValue($city);
                $sheet->getCell('MAIN_PROPERTY_POST_CODE' . $idx)->setValue($post_code);
            }
            if (isset($pd['municipality' . $suffix])) {
                $sheet->getCell('MAIN_PROPERTY_MUNICIPALITY' . $idx)->setValue($pd['municipality' . $suffix]);
            }
            if (isset($pd['area' . $suffix])) {
                $sheet->getCell('MAIN_PROPERTY_AREA' . $idx)->setValue($pd['area' . $suffix]);
            }
            if (isset($pd['quarter' . $suffix]) && isset($pd['address' . $suffix]) && isset($pd['address_number' . $suffix])) {
                $address = array();
                if (!empty($pd['quarter' . $suffix])) {
                    $address[] = $pd['quarter' . $suffix];
                }
                if (!empty($pd['address' . $suffix])) {
                    $address[] = $pd['address' . $suffix];
                }
                if (!empty($pd['address_number' . $suffix])) {
                    $address[] = $pd['address_number' . $suffix];
                }
                $sheet->getCell('MAIN_PROPERTY_ADDRESS' . $idx)->setValue(implode(', ', $address));
            }

            //construction values
            if ($idx == 1 && isset($pd['construction' . $suffix])) {
                //the table BUILDING should always be JUST ONE, for the first main property found (it's silly but this is the client's request)
                $construction = array();
                foreach ($pd['construction' . $suffix] as $constr) {
                    $construction[] = self::$construction_types[$constr];
                }
                $sheet->getCell('MAIN_PROPERTY_CONSTRUCTION_TYPE')->setValue(implode(', ', $construction));
                $sheet->getCell('MAIN_PROPERTY_CONSTRUCTION_YEAR')->setValue($pd['construction_year']);
            }

            //property details (for main properties and their children)
            $sheet->getCell('PROPERTY_DETAILS_NUMBER' . $idx)->setValue($idx);
            if (isset($pd['type_object' . $suffix]) && !empty(self::$building_types[$pd['type_object' . $suffix]])) {
                $sheet->getCell('PROPERTY_DETAILS_TYPE' . $idx)->setValue(self::$building_types[$pd['type_object' . $suffix]]);
            }
            if (isset($pd['floor' . $suffix])) {
                $sheet->getCell('PROPERTY_DETAILS_FLOOR' . $idx)->setValue($pd['floor' . $suffix]);
            }
            if (isset($pd['up_area' . $suffix])) {
                $sheet->getCell('PROPERTY_DETAILS_UP_AREA' . $idx)->setValue($pd['up_area' . $suffix]);
            }
            if (isset($pd['ideal_area' . $suffix])) {
                $sheet->getCell('PROPERTY_DETAILS_IDEAL_AREA' . $idx)->setValue($pd['ideal_area' . $suffix]);
            }
            if (isset($pd['real_up_area' . $suffix])) {
                $sheet->getCell('PROPERTY_DETAILS_REAL_UP_AREA' . $idx)->setValue($pd['real_up_area' . $suffix]);
            }
            if (isset($pd['adopted_area' . $suffix])) {
                $sheet->getCell('PROPERTY_DETAILS_ADOPTED_AREA' . $idx)->setValue($pd['adopted_area' . $suffix]);
            }
            //put all children below its parent
            foreach ($pd['children'] as $cd) {
                $idx++;
                $sheet->getCell('PROPERTY_DETAILS_NUMBER' . $idx)->setValue($idx);
                if (isset($cd['type_object' . $suffix]) && !empty(self::$building_types[$cd['type_object']])) {
                    $sheet->getCell('PROPERTY_DETAILS_TYPE' . $idx)->setValue(self::$building_types[$cd['type_object' . $suffix]]);
                }
                if (isset($cd['floor' . $suffix])) {
                    $sheet->getCell('PROPERTY_DETAILS_FLOOR' . $idx)->setValue($cd['floor' . $suffix]);
                }
                if (isset($cd['up_area' . $suffix])) {
                    $sheet->getCell('PROPERTY_DETAILS_UP_AREA' . $idx)->setValue($cd['up_area' . $suffix]);
                }
                if (isset($cd['ideal_area' . $suffix])) {
                    $sheet->getCell('PROPERTY_DETAILS_IDEAL_AREA' . $idx)->setValue($cd['ideal_area' . $suffix]);
                }
                if (isset($cd['real_up_area' . $suffix])) {
                    $sheet->getCell('PROPERTY_DETAILS_REAL_UP_AREA' . $idx)->setValue($cd['real_up_area' . $suffix]);
                }
                if (isset($cd['adopted_area' . $suffix])) {
                    $sheet->getCell('PROPERTY_DETAILS_ADOPTED_AREA' . $idx)->setValue($cd['adopted_area' . $suffix]);
                }
            }
        }

        //table LAND
        //it is visible in the template and should be hidden if at least one of main properties is defined with this option (land_area)
        //OR if the mask is LAND
        if (!$show_land_table && self::$used_mask != 'land' && self::$used_mask != 'plot') {
            $range_to_unhide = $sheet->getCell()->rangeBoundaries($template->getNamedRange('MAIN_LAND')->getRange());
            for ($i = $range_to_unhide[0][1]; $i <= $range_to_unhide[1][1]; $i++) {
                $sheet->getRowDimension($i)->setVisible(false);
            }
        }

        //table BUILDING
        //it is visible in the template and should be visible only if the mask is residential property
        if (self::$used_mask != 'residential_property') {
            $range_to_unhide = $sheet->getCell()->rangeBoundaries($template->getNamedRange('MAIN_PROPERTY_CONSTRUCTION')->getRange());
            for ($i = $range_to_unhide[0][1]; $i <= $range_to_unhide[1][1]; $i++) {
                $sheet->getRowDimension($i)->setVisible(false);
            }
        }

        //approaches data
        self::_copyRanges($template, 'PRICE_REPORT', array('APPROACH_DESCRIPTION', 'APPROACHES_DATA'), count(self::$approaches));

        $idx = 0;
        $totals = array('BGN' => array(), 'EUR' => array());
        foreach (self::$approaches as $approach_code_name => $approach) {
            $idx++;

            //do the formatting of the approach (method) description: approach (method) name (bold) concatenated with approach description
            $objRichText = new PHPExcel_RichText();
            $objPayable = $objRichText->createTextRun($approach['name'] . '. ');
            $objPayable->getFont()->setBold(true)->setName('Arial')->setSize(11);
            $objRichText->createText($approach['description']);
            $sheet->getCell('APPROACH_DESCRIPTION' . $idx)->setValue($objRichText);

            //replace the placeholders of the the approach prices
            $sheet->getCell('APPROACH_NAME' . $idx)->setValue($approach['name']);

            //set formula for the value of each approach in BGN and EUR
            $sheet->getCell('APPROACH_VALUE_BGN' . $idx)->setValue('=' . strtoupper($approach_code_name) . '_TOTAL_BGN');
            $sheet->getCell('APPROACH_VALUE_EUR' . $idx)->setValue('=' . strtoupper($approach_code_name) . '_TOTAL_EUR');

            //set data validation of the APPROACH_WEIGHT cell (sum of percentages should not exceed 100%)
            $validation = $sheet->getCell('APPROACH_WEIGHT' . $idx)->getDataValidation();
            $validation->setType(PHPExcel_Cell_DataValidation::TYPE_CUSTOM);
            $validation->setErrorStyle(PHPExcel_Cell_DataValidation::STYLE_STOP);
            $validation->setAllowBlank(true);
            $validation->setShowInputMessage(true);
            $validation->setShowErrorMessage(true);
            $validation->setErrorTitle(self::i18n('input_error_percentage_title'));
            $validation->setError(self::i18n('input_error_percentage_text'));
            $validation->setPromptTitle(self::i18n('prompt_percentage_title'));
            $validation->setPrompt(self::i18n('prompt_percentage_text'));
            $validation_formula = array();
            for ($i = 1; $i <= count(self::$approaches); $i++) {
                $validation_formula[] = 'APPROACH_WEIGHT' . $i;
            }
            $validation->setFormula1('=(' . implode('+', $validation_formula) . ')<=100%');

            //set totals formulas
            $totals['BGN'][] = 'APPROACH_VALUE_BGN' . $idx . '*' . 'APPROACH_WEIGHT' . $idx;
            $totals['EUR'][] = 'APPROACH_VALUE_EUR' . $idx . '*' . 'APPROACH_WEIGHT' . $idx;
        }

        //set total fields formulas
        $sheet->getCell('PROPERTY_PRICE_BGN')->setValue('=' . implode('+', $totals['BGN']));
        $sheet->getCell('PROPERTY_PRICE_EUR')->setValue('=' . implode('+', $totals['EUR']));

        //set liquidation value and net_realizable_value
        //hide rows if no values set in nzoom
        //IMPORTANT: if stage of completion is 100% the rows should be hidden,
        //           but the stage of completion is filled in separate sheet
        //           Using macros is the only way hide rows depending on cell value and PHPExcel does not support macros yet!!!
        $liquidation_value_percent = self::$model->getVarValue('liquidation_value_percent');
        $net_realizable_value_percent = self::$model->getVarValue('net_realizable_value'); //IMPORTANT: the variables has been configured confusingly!!!!
        if ($liquidation_value_percent > 0) {
            $sheet->getCell('PROPERTY_LIQUIDATION_VALUE1')->setValue($liquidation_value_percent / 100);
            $sheet->getCell('PROPERTY_LIQUIDATION_VALUE2')->setValue($liquidation_value_percent / 100);
        } else {
            //hide rows
            $row_to_hide = $sheet->getCell('PROPERTY_LIQUIDATION_VALUE1')->getRow();
            $sheet->getRowDimension($row_to_hide)->setVisible(false);
            $row_to_hide = $sheet->getCell('PROPERTY_LIQUIDATION_VALUE2')->getRow();
            $sheet->getRowDimension($row_to_hide)->setVisible(false);
        }
        if ($net_realizable_value_percent > 0) {
            $sheet->getCell('PROPERTY_NET_REALIZABLE_VALUE1')->setValue($net_realizable_value_percent / 100);
            $sheet->getCell('PROPERTY_NET_REALIZABLE_VALUE2')->setValue($net_realizable_value_percent / 100);
        } else {
            //hide rows
            $row_to_hide = $sheet->getCell('PROPERTY_NET_REALIZABLE_VALUE1')->getRow();
            $sheet->getRowDimension($row_to_hide)->setVisible(false);
            $row_to_hide = $sheet->getCell('PROPERTY_NET_REALIZABLE_VALUE2')->getRow();
            $sheet->getRowDimension($row_to_hide)->setVisible(false);
        }

        //attachments note
        $note = sprintf(self::i18n('attachments_note'), implode(', ', range(1, count(self::$approaches))));
        $sheet->getCell('ATTACHMENTS_NOTE')->setValue($note);

        //valid term
        $sheet->getCell('REPORT_VALID_TERM')->setValue(self::$model->getVarValue('valid_term'));

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_property_assessment_report_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for cost approach sheet
     *
     */
    private static function _prepareCostApproachSheet()
    {

        //get the file from the attached pattern files
        $files = self::$pattern->getAttachments(array('fi18n.name = \'COST_APPROACH\''));
        if (empty($files)) {
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        //array that contains main properties that require land subtables
        $main_properties_with_land = array();

        self::_copyRanges($template, 'COST_APPROACH', array('COST_APPROACH_PROPERTY_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'COST_APPROACH', array('COST_APPROACH_ROW_DATA'), count(self::$all_properties));
        $range_to_merge = $sheet->getCell()->rangeBoundaries($template->getNamedRange('COST_APPROACH_ROW_DATA_TO_MERGE')->getRange());

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //property type and usage are at the top of the spreadsheet
            $sheet->getCell('COST_APPROACH_PROPERTY_TYPE' . $idx)->setValue('=MAIN_PROPERTY_DESCRIPTION' . $idx); //changed from type to description as per Bug 3507, comment 22
            $sheet->getCell('COST_APPROACH_PROPERTY_USAGE' . $idx)->setValue('=MAIN_PROPERTY_USAGE' . $idx);

            //property data in the main table
            $main_property_row = $sheet->getCell('COST_APPROACH_ROW_NUMBER' . $idx)->getRow();
            $main_idx = $idx;
            $adopted_area_formula = array('COST_APPROACH_ROW_ADOPTED_AREA' . $idx . '*' . (!empty($pd['adopted_area_plus']) ? $pd['adopted_area_plus'] : '0') . '/100');

            $sheet->getCell('COST_APPROACH_ROW_NUMBER' . $idx)->setValue('=PROPERTY_DETAILS_NUMBER' . $idx);
            $sheet->getCell('COST_APPROACH_ROW_TYPE' . $idx)->setValue('=COST_APPROACH_PROPERTY_TYPE' . $idx);//changed from type to description as per Bug 3507, comment 22
            $sheet->getCell('COST_APPROACH_ROW_UP_AREA' . $idx)->setValue('=PROPERTY_DETAILS_UP_AREA' . $idx);
            $sheet->getCell('COST_APPROACH_ROW_IDEAL_AREA' . $idx)->setValue('=PROPERTY_DETAILS_IDEAL_AREA' . $idx);
            $sheet->getCell('COST_APPROACH_ROW_ADOPTED_AREA' . $idx)->setValue('=PROPERTY_DETAILS_ADOPTED_AREA' . $idx);
            if (isset($pd['construction_year' . $suffix])) {
                $sheet->getCell('COST_APPROACH_ROW_CONSTRUCTION_YEAR' . $idx)->setValue($pd['construction_year' . $suffix]);
            }

            //put all children below its parent
            foreach ($pd['children'] as $cd) {
                $idx++;
                $sheet->getCell('COST_APPROACH_ROW_NUMBER' . $idx)->setValue('=PROPERTY_DETAILS_NUMBER' . $idx);
                $sheet->getCell('COST_APPROACH_ROW_TYPE' . $idx)->setValue('=PROPERTY_DETAILS_TYPE' . $idx);
                $sheet->getCell('COST_APPROACH_ROW_UP_AREA' . $idx)->setValue('=PROPERTY_DETAILS_UP_AREA' . $idx);
                $sheet->getCell('COST_APPROACH_ROW_IDEAL_AREA' . $idx)->setValue('=PROPERTY_DETAILS_IDEAL_AREA' . $idx);
                $sheet->getCell('COST_APPROACH_ROW_ADOPTED_AREA' . $idx)->setValue('=PROPERTY_DETAILS_ADOPTED_AREA' . $idx);
                if (isset($child_pd['construction_year' . $suffix])) {
                    $sheet->getCell('COST_APPROACH_ROW_CONSTRUCTION_YEAR' . $idx)->setValue($child_pd['construction_year' . $suffix]);
                }
                $adopted_area_formula[] = 'COST_APPROACH_ROW_ADOPTED_AREA' . $idx . '*' . (!empty($cd['adopted_area_plus']) ? $cd['adopted_area_plus'] : '0') . '/100';
            }

            //the "corrected real up area" (COST_APPROACH_ROW_ADOPTED_AREA_TOTAL) should be SUM of the parent and children areas
            $sheet->getCell('COST_APPROACH_ROW_ADOPTED_AREA_TOTAL' . $main_idx)->setValue('=' . implode('+', $adopted_area_formula));

            //somes cells should be merged vertically
            $children_count = count($pd['children']);
            if ($children_count > 0) {
                for ($i = $range_to_merge[0][0] - 1; $i <= $range_to_merge[1][0] - 1; $i++) {
                    $merge = sprintf(
                        '%s%d:%s%d',
                        PHPExcel_Cell::stringFromColumnIndex($i),
                        $main_property_row,
                        PHPExcel_Cell::stringFromColumnIndex($i),
                        $main_property_row + $children_count
                    );
                    $sheet->mergeCells($merge);
                }
            }
            //land cell should have specific preparation - value 2 means "permit to build"
            if (isset($pd['land_area' . $suffix]) && $pd['land_area' . $suffix] == 2) {
                //remove protection if the option land_area is set to 2
                $coordinates = $sheet->getCell('COST_APPROACH_ROW_LAND_AREA' . $main_idx)->getCoordinate();
                $sheet->getStyle($coordinates)->getProtection()->setLocked(PHPExcel_Style_Protection::PROTECTION_UNPROTECTED);

                //table LAND should be HIDDEN
                //it is visible in the template and should be hidden if at least one of main properties is defined with this option (land_area = 1)
                $range_to_unhide = $sheet->getCell()->rangeBoundaries($template->getNamedRange('COST_APPROACH_ANALOGUES_LAND_DATA')->getRange());
                for ($i = $range_to_unhide[0][1]; $i <= $range_to_unhide[1][1]; $i++) {
                    $sheet->getRowDimension($i)->setVisible(false);
                }
            } elseif (isset($pd['land_area' . $suffix]) && $pd['land_area' . $suffix] == 1) {
                //land cell should have specific preparation - value 1 means "land"
                //only main properties with option 1 should have a subtable that defines land price
                $main_properties_with_land[] = $idx;
            }
        }

        //the analogues land table (the second table in the sheet) should be copied count($main_properties_with_land) times
        self::_copyRanges($template, 'COST_APPROACH', array('COST_APPROACH_ANALOGUES_LAND_DATA'), count($main_properties_with_land));

        $idx = 0;
        foreach ($main_properties_with_land as $main_idx) {
            $idx++;
            //set dynamic relation in the main table
            $sheet->getCell('COST_APPROACH_ROW_LAND_AREA' . $main_idx)->setValue('=COST_APPROACH_ANALOGUE_LAND_PRICE_EUR' . $idx);
            //set relation to land area in each of the subtables
            $sheet->getCell('COST_APPROACH_ANALOGUE_LAND_AREA' . $idx)->setValue('=MAIN_LAND_AREA');
        }

        if (!empty($main_properties_with_land) && count($main_properties_with_land) == count(self::$main_properties)) {
            //all the rows are with land_area option 1, so change the label to "LAND"
            $sheet->getCell('COST_APPROACH_LAND_PERMIT_LABEL')->setValue(self::i18n('worksheet_cost_approach_land'));
        }

        //set values of some fields in the sheet
        $sheet->getCell('COST_APPROACH_CUSTOMER')->setValue(self::$model->get('customer_name'));
        $sheet->getCell('COST_APPROACH_CURRENT_YEAR')->setValue(General::strftime('%Y'));

        //set attachment number
        $sheet->getCell('COST_APPROACH_ATTACHMENT_NUMBER')->setValue(self::i18n('attachment_number') . ' ' . ++self::$attachments_index);

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_cost_approach_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for income approach sheet
     *
     */
    private static function _prepareIncomeApproachSheet()
    {

        //get the file from the attached pattern files
        $files = self::$pattern->getAttachments(array('fi18n.name = \'INCOME_APPROACH\''));
        if (empty($files)) {
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        self::_copyRanges($template, 'INCOME_APPROACH', array('INCOME_APPROACH_PROPERTY_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'INCOME_APPROACH', array('INCOME_APPROACH_ROW_DATA'), count(self::$all_properties));
        //the analogues table (the second table in the sheet) should be copied count(self::$property_types) times
        self::_copyRanges($template, 'INCOME_APPROACH', array('INCOME_APPROACH_ANALOGUES_DATA'), count(self::$property_types));

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //property type and usage are at the top of the spreadsheet
            $sheet->getCell('INCOME_APPROACH_PROPERTY_TYPE' . $idx)->setValue('=MAIN_PROPERTY_DESCRIPTION' . $idx); //changed from type to description as per Bug 3507, comment 22
            $sheet->getCell('INCOME_APPROACH_PROPERTY_USAGE' . $idx)->setValue('=MAIN_PROPERTY_USAGE' . $idx);

            $sheet->getCell('INCOME_APPROACH_ROW_NUMBER' . $idx)->setValue('=PROPERTY_DETAILS_NUMBER' . $idx);
            $sheet->getCell('INCOME_APPROACH_ROW_TYPE' . $idx)->setValue('=INCOME_APPROACH_PROPERTY_TYPE' . $idx);//changed from type to description as per Bug 3507, comment 22
            $sheet->getCell('INCOME_APPROACH_ROW_UP_AREA' . $idx)->setValue('=PROPERTY_DETAILS_UP_AREA' . $idx);
            $sheet->getCell('INCOME_APPROACH_ROW_ADOPTED_AREA' . $idx)->setValue('=PROPERTY_DETAILS_ADOPTED_AREA' . $idx);

            $sheet->getCell('INCOME_APPROACH_ROW_PRICE_SQM_EUR' . $idx)->setValue('=INCOME_APPROACH_ANALOGUE_PRICE_SQM_EUR' . array_search($pd['type_object' . $suffix], self::$property_types));
        }

        $idx = 0;
        foreach (self::$property_types as $main_idx => $ptype) {
            $idx++;
            $sheet->getCell('INCOME_APPROACH_ANALOGUE_AREA' . $idx)->setValue('=INCOME_APPROACH_ROW_UP_AREA' . $main_idx);
        }

        //set values of some fields in the sheet
        $sheet->getCell('INCOME_APPROACH_CUSTOMER')->setValue(self::$model->get('customer_name'));

        //set attachment number
        $sheet->getCell('INCOME_APPROACH_ATTACHMENT_NUMBER')->setValue(self::i18n('attachment_number') . ' ' . ++self::$attachments_index);

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_income_approach_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for sales comparison approach sheet
     *
     */
    private static function _prepareSalesComparisonApproachSheet()
    {

        //get the file from the attached pattern files
        $file_code_name = 'SALES_COMPARISON_APPROACH';
        //do not use specifi template for land
        //if (self::$used_mask == 'land') {
        //    $file_code_name = 'SALES_COMPARISON_APPROACH_LAND';
        //}
        $files = self::$pattern->getAttachments(array('fi18n.name = \'' . $file_code_name . '\''));
        if (empty($files)) {
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        self::_copyRanges($template, 'SALES_COMPARISON_APPROACH', array('SALES_COMPARISON_APPROACH_PROPERTY_DATA'), count(self::$main_properties));
        //the analogues table (the seond table in the sheet) should be copied count(self::$main_properties) times (a big analogue table for each main property)
        self::_copyRanges($template, 'SALES_COMPARISON_APPROACH', array('SALES_COMPARISON_APPROACH_ANALOGUE_DATA'), count(self::$main_properties));
        //the analogues table (the third table in the sheet) should be copied count(self::$property_types) times
        self::_copyRanges($template, 'SALES_COMPARISON_APPROACH', array('SALES_COMPARISON_APPROACH_ANALOGUE_DESCRIPTIONS'), count(self::$property_types));

        $total_formula = array('BGN' => array(), 'EUR' => array());

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //property type and usage are at the top of the spreadsheet
            $sheet->getCell('SALES_COMPARISON_APPROACH_PROPERTY_TYPE' . $idx)->setValue('=MAIN_PROPERTY_DESCRIPTION' . $idx); //changed from type to description as per Bug 3507, comment 22
            $sheet->getCell('SALES_COMPARISON_APPROACH_PROPERTY_USAGE' . $idx)->setValue('=MAIN_PROPERTY_USAGE' . $idx);

            //there is a big analogue table for each main property
            $sheet->getCell('SALES_COMPARISON_APPROACH_ANALOGUE_AREA' . $idx)->setValue('=PROPERTY_DETAILS_ADOPTED_AREA' . $idx);

            $total_formula['BGN'][] = 'SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_BGN' . $idx;
            $total_formula['EUR'][] = 'SALES_COMPARISON_APPROACH_ANALOGUE_PRICE_EUR' . $idx;
        }

        //IMPORTANT: set total price in BGN and EUR as sum of all main property prices
        $sheet->getCell('SALES_COMPARISON_APPROACH_TOTAL_BGN')->setValue('=' . implode('+', $total_formula['BGN']));
        $sheet->getCell('SALES_COMPARISON_APPROACH_TOTAL_EUR')->setValue('=' . implode('+', $total_formula['EUR']));

        //set values of some fields in the sheet
        $sheet->getCell('SALES_COMPARISON_APPROACH_CUSTOMER')->setValue(self::$model->get('customer_name'));

        //set attachment number
        $sheet->getCell('SALES_COMPARISON_APPROACH_ATTACHMENT_NUMBER')->setValue(self::i18n('attachment_number') . ' ' . ++self::$attachments_index);

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_sales_comparison_approach_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for NROCZZ sheet
     *
     */
    private static function _prepareNROCZZSheet()
    {
        //ToDo: define when to include such sheet
        //for now only masks LAND have this sheet
        if (self::$used_mask != 'land') {
            return false;
        }

        //get the file from the attached pattern files
        $files = self::$pattern->getAttachments(array('fi18n.name = \'NROCZZ\''));
        if (empty($files)) {
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        self::_copyRanges($template, 'NROCZZ', array('NROCZZ_PROPERTY_DATA'), count(self::$main_properties));
        self::_copyRanges($template, 'NROCZZ', array('NROCZZ_ROW_DATA'), count(self::$main_properties));

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //location is constructed of area, municipality and city
            $location = array();
            if (isset($pd['area' . $suffix])) {
                $location[] = $pd['area' . $suffix];
            }
            if (isset($pd['municipality' . $suffix])) {
                $location[] = $pd['municipality' . $suffix];
            }
            if (isset($pd['city' . $suffix])) {
                $location[] = $pd['city' . $suffix];
            }

            //property type and usage are at the top of the spreadsheet
            $sheet->getCell('NROCZZ_PROPERTY_TYPE' . $idx)->setValue('=MAIN_PROPERTY_TYPE' . $idx);
            $sheet->getCell('NROCZZ_PROPERTY_LOCATION' . $idx)->setValue(implode(', ', $location));

            $sheet->getCell('NROCZZ_ROW_NUMBER' . $idx)->setValue($idx);
            $sheet->getCell('NROCZZ_ROW_AREA' . $idx)->setValue($pd['land_area' . $suffix]);
            $sheet->getCell('NROCZZ_ROW_PRICE_BGN' . $idx)->setValue($pd['price_square_meter' . $suffix]);
        }

        //set attachment number
        $sheet->getCell('NROCZZ_ATTACHMENT_NUMBER')->setValue(self::i18n('attachment_number') . ' ' . ++self::$attachments_index);

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_NROCZZ_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for Remanent Approach sheet
     *
     */
    private static function _prepareRemanentApproachSheet()
    {
        //ToDo: define when to include such sheet
        //for now only masks LAND have this sheet
        if (self::$used_mask != 'plot') {
            return false;
        }

        //get the file from the attached pattern files
        $files = self::$pattern->getAttachments(array('fi18n.name = \'REMANENT_APPROACH\''));
        if (empty($files)) {
            return false;
        }
        //get the latest file
        $file = array_pop($files);
        if ($file->get('not_exist')) {
            return false;
        }
        $template = self::$objReader->load($file->get('path'));
        $sheet = $template->getActiveSheet();

        //set attachment number
        $sheet->getCell('REMANENT_APPROACH_ATTACHMENT_NUMBER')->setValue(self::i18n('attachment_number') . ' ' . ++self::$attachments_index);

        //save the sheet
        self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
        self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
        self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_remanent_approach_title'));
        self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
        self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
    }

    /**
     * Prepares data for cost approach sheet
     *
     */
    private static function _prepareStageOfCompletionSheet()
    {
        //ToDo: define when to include such sheet
        //for now only masks residential_property and hotel have this sheet
        if (self::$used_mask != 'residential_property' && self::$used_mask != 'hotel' && self::$used_mask != 'state_municipal_property') {
            return false;
        }

        //get the file from the attached pattern files
        $files = self::$pattern->getAttachments(array('fi18n.name = \'STAGE_OF_COMPLETION\''));
        if ($files) {
            //get the latest file
            $file = array_pop($files);
            if ($file->get('not_exist')) {
                return false;
            }
            $template = self::$objReader->load($file->get('path'));

            //save the sheet
            self::$objPHPExcel->addExternalSheet($template->getSheet(0), ++self::$active_sheet_index);
            self::$objPHPExcel->setActiveSheetIndex(self::$active_sheet_index);
            self::$objPHPExcel->getActiveSheet()->setTitle(self::i18n('worksheet_stage_of_completion_title'));
            self::$objPHPExcel->getActiveSheet()->getProtection()->setSheet(true);
            self::$objPHPExcel->getActiveSheet()->getProtection()->setPassword(self::$_password);
        }
    }

    /**
     * Copies all designated ranges within a worksheet
     *
     * @param object $workbook - PHPExcel workbook
     * @param string $template_name - name of the excel workbook
     * @param array $range_names - array of named ranges
     * @param int $count - count of the main properties (count of copies)
     */
    private static function _copyRanges(&$workbook, $template_name, $range_names = array(), $count)
    {
        if (!empty($range_names)) {
            $ranges = array();
            foreach (self::$_ranges[$template_name] as $range_name => $placeholders) {
                if (in_array($range_name, $range_names)) {
                    $ranges[$range_name] = $placeholders;
                }
            }
        } else {
            $ranges = self::$_ranges[$template_name];
        }
        foreach ($ranges as $range_name => $placeholders) {
            //get the named range as object
            $range = $workbook->getNamedRange($range_name);
            //get the coordinates of the named range
            $range_coordinates = $range->getRange();
            if (preg_match('#([^\d]+)(\d+):([^\d]+)(\d+)#', $range_coordinates)) {
                list($start_col, $start_row, $end_col, $end_row) = explode(',', preg_replace('#([^\d]+)(\d+):([^\d]+)(\d+)#', '\1,\2,\3,\4', $range_coordinates));
            } else {
                list($start_col, $start_row) = explode(',', preg_replace('#([^\d]+)(\d+)#', '\1,\2', $range_coordinates));
                $end_col = $start_col;
                $end_row = $start_row;
            }
            //calculate how many rows have to be copied
            $rows_to_copy = $end_row - $start_row + 1;

            //get the merged cells
            $merged_cells = $workbook->getActiveSheet()->getMergeCells();
            $to_merge = array();

            //the loop is one iteration shorter than the count, because the table should be cloned (and the first copy is already there)
            for ($idx = 1; $idx < $count; $idx++) {
                //insert new rows and copy the table into calculated designation
                $workbook->getActiveSheet()->insertNewRowBefore($start_row + ($rows_to_copy) * $idx, $rows_to_copy);
                //copy the table from the named range
                $array = $workbook->getActiveSheet()->namedRangeToArray($range_name, null, false);
                $workbook->getActiveSheet()->fromArray($array, null, $start_col . ($start_row + ($rows_to_copy) * $idx));

                //handle merged cells
                foreach ($merged_cells as $merged_range) {
                    list($s_col, $s_row, $e_col, $e_row) = explode(',', preg_replace('#([^\d]+)(\d+):([^\d]+)(\d+)#', '\1,\2,\3,\4', $merged_range));
                    //get only those cells that are merged within the named range
                    if (
                        PHPExcel_Cell::columnIndexFromString($s_col) >= PHPExcel_Cell::columnIndexFromString($start_col) && $s_row >= $start_row &&
                        PHPExcel_Cell::columnIndexFromString($s_col) <= PHPExcel_Cell::columnIndexFromString($end_col) && $s_row <= $end_row
                    ) {
                        //the range of the merged cells with precisely calculated offset
                        $to_merge[] = $s_col . ($s_row + ($rows_to_copy) * $idx) . ':' . $e_col . ($e_row + ($rows_to_copy) * $idx);
                    }
                }
                //handle row heights and styles
                for ($i = $start_row; $i <= $end_row; $i++) {
                    //handle row heights
                    $row_height = $workbook->getActiveSheet()->getRowDimension($i)->getRowHeight();
                    $workbook->getActiveSheet()->getRowDimension($i + ($rows_to_copy) * $idx)->setRowHeight($row_height);

                    //handle styles
                    $row = new PHPExcel_Worksheet_Row($workbook->getActiveSheet(), $i);
                    $cell_iterator = $row->getCellIterator();
                    foreach ($cell_iterator as $cell) {
                        $cell_row = $cell->getRow();
                        $cell_col = $cell->getColumn();
                        $style = $workbook->getActiveSheet()->getStyle($cell_col . $cell_row);
                        $workbook->getActiveSheet()->duplicateStyle($style, $cell_col . ($cell_row + ($rows_to_copy) * $idx));
                    }
                }
            }

            //set the merged cells of the newly added tables
            //IMPORTANT: get the merged cells array just after the rows have been inserted
            if (!empty($to_merge)) {
                $merged_cells = array_merge($workbook->getActiveSheet()->getMergeCells(), array_combine($to_merge, $to_merge));
                $workbook->getActiveSheet()->setMergeCells($merged_cells);
            }

            //add placeholders and set numbers of the placeholders
            foreach ($placeholders as $placeholder) {
                list($s_col, $s_row) = explode(',', preg_replace('#([^\d]+)(\d+)#', '\1,\2', $workbook->getNamedRange($placeholder)->getRange()));
                //set number 1 to the template placeholder
                $workbook->getNamedRange($placeholder)->setName($placeholder . '1');
                for ($idx = 1; $idx < $count; $idx++) {
                    //add placeholder to the newly inserted rows
                    $workbook->addNamedRange(new PHPExcel_NamedRange($placeholder . ($idx + 1), $workbook->getActiveSheet(), $s_col . ($s_row + ($rows_to_copy) * $idx)));
                }
            }
        }
    }

    /**
     * Prints resume with specific data
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     * @return array $file_path - temp file path of the generated file
     */
    public static function printResume(&$registry, &$model, &$pattern, &$params = array())
    {
        self::$model = $model;
        self::$registry = $registry;

        //office
        $office_address = '';
        if ($model->get('office')) {
            include_once(PH_MODULES_DIR . 'offices/models/offices.factory.php');
            $office = Offices::searchOne(self::$registry, array('where' => array('o.id=' . $model->get('office')),
                'sanitize' => true));
            if ($office) {
                $office_address = preg_replace('#[\r\n|\r|\n]#', ' ', $office->get('description'));
                self::$model->extender->add('office_address', $office_address);
                unset($office);
            }
        }

        //prepare properties
        self::_prepareBasicData();

        $main_properties = array();

        //the different type of mask are saved in different configurators
        //IMPORTANT: for now use suffixes as a way to customize each mask
        $idx = 0;
        foreach (self::$main_properties as $id => $pd) {
            $idx++;

            $suffix = self::$mask_suffixes[$pd['mask']];

            //absolutely SILLY code, responsible: <EMAIL>
            //get the first stage of completion in the main properties
            if (!isset($stage_of_completion)) {
                if (isset($pd['degree_completion' . $suffix])) {
                    $stage_of_completion = $pd['degree_completion' . $suffix];
                }
            }

            //this is a main property
            $main_property = array();
            if (isset($pd['city' . $suffix])) {
                @list($post_code, $city) = explode(',', preg_replace('#\[?(\d+)\]? (.*)#', '\1,\2', @$pd['city' . $suffix]));
                $main_property['city'] = $city;
                $main_property['post_code'] = $post_code;
            }
            if (isset($pd['type_object' . $suffix]) && !empty(self::$building_types[$pd['type_object' . $suffix]])) {
                $main_property['type'] = self::$building_types[$pd['type_object' . $suffix]];
            }
            if (isset(self::$all_properties[$id])) {
                $main_property['description'] = self::$all_properties[$id];
            }
            if (isset($pd['used_method' . $suffix])) {
                $main_property['usage'] = $pd['used_method' . $suffix];
            }
            if (isset($pd['municipality' . $suffix])) {
                $main_property['municipality'] = $pd['municipality' . $suffix];
            }
            if (isset($pd['area' . $suffix])) {
                $main_property['area'] = $pd['area' . $suffix];
            }
            if (isset($pd['quarter' . $suffix]) && isset($pd['address' . $suffix]) && isset($pd['address_number' . $suffix])) {
                $address = array();
                if (!empty($pd['quarter' . $suffix])) {
                    $address[] = $pd['quarter' . $suffix];
                }
                if (!empty($pd['address' . $suffix])) {
                    $address[] = $pd['address' . $suffix];
                }
                if (!empty($pd['address_number' . $suffix])) {
                    $address[] = $pd['address_number' . $suffix];
                }
                $main_property['address'] = implode(', ', $address);
            }
            $main_properties[] = $main_property;
        }

        //tables with all main properties come from a template
        $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . self::$model->get('model_lang')) . '/print.ini';
        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/main_property_data.html';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->loadCustomI18NFiles($i18n_file);
        $viewer->data['main_properties'] = $main_properties;
        self::$model->extender->add('main_property_data', $viewer->fetch());

        //table with values
        //tables with all main properties come from a template
        $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . self::$model->get('model_lang')) . '/print.ini';
        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/evaluation_data.html';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->loadCustomI18NFiles($i18n_file);
        if (!isset($stage_of_completion)) {
            $stage_of_completion = 0;
        }
        $liquidation_value = (int)self::$model->getVarValue('liquidation_value_percent') ?? 0;
        $net_realizable_value = (int)self::$model->getVarValue('net_realizable_value');
        $viewer->data['stage_of_completion'] = $stage_of_completion;
        $viewer->data['liquidation_value'] = $liquidation_value;
        $viewer->data['net_realizable_value'] = $net_realizable_value;
        //some of the values are rounded (round(/100)*100) because this is the expected format (Excel equivalent is round(value, -2))
        $market_value1_BGN = round(self::$model->getVarValue('fair_market_value') / 100) * 100;
        $market_value2_BGN = round($market_value1_BGN * $stage_of_completion / 100 / 100) * 100;
        $market_value3_BGN = round($market_value1_BGN * $liquidation_value / 100 / 100) * 100;
        $market_value4_BGN = round($market_value2_BGN * $liquidation_value / 100 / 100) * 100;
        $market_value5_BGN = round($market_value1_BGN * $net_realizable_value / 100 / 100) * 100;
        $market_value6_BGN = round($market_value2_BGN * $net_realizable_value / 100 / 100) * 100;
        $viewer->data['market_value1_BGN'] = number_format($market_value1_BGN, 0, '.', ' ');
        $viewer->data['market_value2_BGN'] = number_format($market_value2_BGN, 0, '.', ' ');
        $viewer->data['market_value3_BGN'] = number_format($market_value3_BGN, 0, '.', ' ');
        $viewer->data['market_value4_BGN'] = number_format($market_value4_BGN, 0, '.', ' ');
        $viewer->data['market_value5_BGN'] = number_format($market_value5_BGN, 0, '.', ' ');
        $viewer->data['market_value6_BGN'] = number_format($market_value6_BGN, 0, '.', ' ');
        require_once PH_MODULES_DIR . 'finance/models/finance.currencies.factory.php';
        $eur_rate = Finance_Currencies::getRate(self::$registry, 'EUR');
        $market_value1_EUR = round($market_value1_BGN / $eur_rate / 100) * 100;
        $market_value2_EUR = round($market_value2_BGN / $eur_rate / 100) * 100;
        $market_value3_EUR = round($market_value3_BGN / $eur_rate / 100) * 100;
        $market_value4_EUR = round($market_value4_BGN / $eur_rate / 100) * 100;
        $market_value5_EUR = round($market_value5_BGN / $eur_rate / 100) * 100;
        $market_value6_EUR = round($market_value6_BGN / $eur_rate / 100) * 100;
        $viewer->data['market_value1_EUR'] = number_format($market_value1_EUR, 0, '.', ' ');
        $viewer->data['market_value2_EUR'] = number_format($market_value2_EUR, 0, '.', ' ');
        $viewer->data['market_value3_EUR'] = number_format($market_value3_EUR, 0, '.', ' ');
        $viewer->data['market_value4_EUR'] = number_format($market_value4_EUR, 0, '.', ' ');
        $viewer->data['market_value5_EUR'] = number_format($market_value5_EUR, 0, '.', ' ');
        $viewer->data['market_value6_EUR'] = number_format($market_value6_EUR, 0, '.', ' ');
        self::$model->extender->add('evaluation_data', $viewer->fetch());

        if (!empty(self::$supervisors)) {
            //tables with all main properties come from a template
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/signatures.html';
            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset($template_file);
            $viewer->data['signatures'] = self::$supervisors;
            self::$model->extender->add('signature1', $viewer->fetch());
        }
        if (!empty(self::$assessors)) {
            //tables with all main properties come from a template
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/signatures.html';
            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset($template_file);
            $viewer->data['signatures'] = self::$assessors;
            self::$model->extender->add('signature2', $viewer->fetch());

            //get the first assessor that signed the entire report
            $signed_employee = reset(self::$assessors);
            self::$model->extender->add('signed_employee_name', $signed_employee['name']);
            self::$model->extender->add('signed_employee_email', $signed_employee['email']);
        }

        //signature date is the date when the document got status locked, substatus 5
        $query = 'SELECT h_date FROM ' . DB_TABLE_DOCUMENTS_HISTORY . ' WHERE data LIKE "%\"substatus\";s:1:\"5\"%" AND model="Document" AND model_id=' . self::$model->get('id') . ' ORDER BY h_date LIMIT 1';
        $signature_date = self::$registry['db']->GetOne($query);
        if ($signature_date) {
            self::$model->extender->add('signature_date', General::strftime('%d.%m.%Y %H:%M', $signature_date));
        }
    }

    /**
     * Present the attached documents as numerated list and rated properties as list devided by commas
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function authorizationLetter(&$registry, &$model, &$pattern, array &$params = [])
    {
        self::$registry = &$registry;
        self::$model = &$model;
        self::$pattern = &$pattern;

        // PREPARE THE GROUPING TABLE
        $attached_documents_var = 'attached_doc';
        $attached_documents_value = [];

        $grouping_vars = $model->get('group_vars');
        foreach ($grouping_vars as $gv) {
            if ($gv['name'] == $attached_documents_var) {
                $attached_documents_value = $gv['value'];
                break;
            }
        }

        $var_string = '';
        if (!empty($attached_documents_value)) {
            $list_values = [];
            $counter_docs = 1;
            foreach ($attached_documents_value as $adv) {
                if (!empty($adv)) {
                    $list_values[] = sprintf('%d. %s', $counter_docs, $adv);
                    $counter_docs++;
                }
            }

            $var_string = implode('<br />', $list_values);
        }
        $model->extender->add('attached_document_list', $var_string);

        // PREPARE THE GT2 descriptions (stored in free_text1, not in article_description)
        $rated_properties_list = [];
        $gt2 = $model->getGT2Vars();
        $index = 1;

        foreach ($gt2['values'] as $gt2_row) {
            $counter = (count($gt2['values']) > 1) ? $index . '. ' : '';
            $rated_properties_list[] = $counter . $gt2_row['free_text1'];
            $index++;
        }
        $model->extender->add('rated_properties_list', implode('<br/>', $rated_properties_list));

        // PREPARE assessor name
        $assessor_name = '';
        $signatory_name = '';
        $vars = self::$model->getAssocVars();
        //get the index of the recording_role_type variable
        $recording_role_type_idx = array_search('recording_role_type', $vars['recording_role_group']['names']);
        //get the index of the employee that signs (recording_role)
        $recording_role_idx = array_search('recording_role', $vars['recording_role_group']['names']);
        //get the row with the employee that signed asrow type = 4 (assessor)
        foreach ($vars['recording_role_group']['values'] as $row) {
            if (!empty($row[$recording_role_idx]) && !empty($row[$recording_role_type_idx])) {
                if (empty($assessor_name) && $row[$recording_role_type_idx] == 4) {
                    //get the first with role 4
                    $assessor_name = $row[$recording_role_idx];
                } elseif (empty($signatory_name) && $row[$recording_role_type_idx] == 5) {
                    //get the first with role 5
                    $signatory_name = $row[$recording_role_idx];
                }
            }
        }
        $model->extender->add('assessor_name', $assessor_name);
        $model->extender->add('signatory_name', $signatory_name);
    }

    /**
     * @param $registry
     * @param $model
     * @param $pattern
     * @param array $params
     * @return void
     */
    public static function subjectToOffer(&$registry, &$model, &$pattern, array $params = []): void
    {
        $gt2 = $model->getGT2Vars();
        $text = ' лв. без ДДС';
        $index = 1;
        $subject_to_offer_list = [];
        foreach ($gt2['values'] as $gt2_row) {
            $counter = (count($gt2['values']) > 1) ? $index . '. ' : '';
            $subject_to_offer_list[] = $counter . $gt2_row['free_text1'] . ' - ' . $gt2_row['subtotal'] . $text;
            $index++;
        }
        $model->extender->add('subject_to_offer_list', implode('<br/>', $subject_to_offer_list));
    }


    /**
     * Parse some custom placeholders for document type 10 (business trips)
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     */
    public static function businessTrips(&$registry, &$model, &$pattern, &$params = array())
    {
        self::$registry = &$registry;
        self::$model = &$model;
        self::$pattern = &$pattern;

        //GET VARS
        $vars = self::$model->getAssocVars();

        //employees list
        $employees_list = array();
        if (!empty($vars['employee_group']['values'])) {
            foreach ($vars['employee_group']['values'] as $idx => $row) {
                $employees_list[] = sprintf('%d. %s, %s', $idx, $row[1], $row[2]);
            }
        }
        $model->extender->add('employees_list', implode('<br />', $employees_list));

        //trip list
        $trip_list = array();
        if (!empty($vars['business_trip_group']['values'])) {
            foreach ($vars['business_trip_group']['values'] as $idx => $row) {
                $trip_list[] = sprintf(
                    self::i18n('trip_list'),
                    $row[1],
                    preg_replace('#([0-9])+ (.*)#', '\2', $row[3]),
                    $row[4],
                    self::i18n($row[4] == 1 ? 'trip_list_duration_day' : 'trip_list_duration_days'),
                    General::strftime('%d.%m.%Y', $row[5]),
                    General::strftime('%d.%m.%Y', $row[6])
                );
            }
        }
        $model->extender->add('trip_list', implode('<br />', $trip_list));

        //reports list
        $document_list = array();
        if (!empty($vars['document_group']['values'])) {
            foreach ($vars['document_group']['values'] as $idx => $row) {
                $document_list[] = $row[1];
            }
        }
        $model->extender->add('document_list', implode(', ', $document_list));
    }


    /**
     * Parse some custom placeholders for document type 13
     *
     * @param object $registry - registry object
     * @param object $model - finance_incomes_reason model (the invoice)
     * @param object $pattern - the pattern to print/generate with
     * @param array $params - some custom params that might be used
     *
     * @return bool
     *
     * @todo getPatternsVars should be decorated too
     * Get the vars names from the array keys, get their properties from _fields_meta and make the same modifications as in _decorateVars.
     * When this is done, the modifier print_number_format can be removed from the template.
     */
    public static function prepareReport($registry, $model, $pattern, array &$params = []): bool
    {
        // Prepare some basics
        if (!$pattern->getDOCXTemplate()) {
            //the DOCX patterns use the latest DOCX attachment file as content
            //if there is no such file, the generate process should cease
            return false;
        }
        self::$templateDocx = $pattern->get('docx_template');
        self::$pattern = $pattern;
        self::$registry = &$registry;
        self::$model = $model;
        $db = $registry['db'];

        // Setup
        self::$paths = $paths = array(
            'plugin' => realpath(dirname(__FILE__) . '/..'),
            'templates' => realpath(dirname(__FILE__) . '/../templates'),
            'i18n' => realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')),
        );
        self::$pagePortraitWidth = 12240;
        self::$pagePortraitPrintableWidth = self::$pagePortraitWidth - 720 - 720;
        self::$pagePortraitHeight = 15840;
        self::$pagePortraitPrintableHeight = self::$pagePortraitHeight - 1191 - 777;
        self::$pageLandscapeWidth = 15840;
        self::$pageLandscapePrintableWidth = self::$pageLandscapeWidth - 720 - 720;
        self::$pageLandscapeHeight = 12240;
        self::$pageLandscapePrintableHeight = self::$pageLandscapeHeight - 1134 - 1134;
        self::$tablesWidthFullPage = 10790;
        define('TABLES_WIDTH_FULL_PAGE', self::$tablesWidthFullPage);

        /*
         * Settings
         */
        self::$settings = array(
            'customer_unicredit_id' => 32,
            'customer_ubb_id' => 6,
            'report_role_supervisor_option_value' => 2,
            // If comparative tables have more than 5 columns (1 for labels, 1 for assessment objects and 3 for 3 analogues)
            // then apply landscape for them
            'comparative_tables_landscape_cols' => 5
        );
        define('CUSTOMER_ADVANCE_ID', 23);
        define('DOCUMENTS_TYPE_RESULTS', 39);
        define('DOCUMENTS_TYPE_ANALYTICS', 38);
        define('DOCUMENTS_TYPE_VIEW_MOBILE_APP_COLLAGE', 32);
        define('DOCUMENTS_TYPE_MARKET_APPROACH', 33);
        define('DOCUMENTS_TYPE_REVENUE_APPROACH1', 34);
        define('DOCUMENTS_TYPE_REVENUE_APPROACH2', 37);
        define('DOCUMENTS_TYPE_REVENUE_APPROACH_MARKET_NORMS', 40);
        define('DOCUMENTS_TYPE_REVENUE_APPROACH_ANALOGUES', 36);
        define('DOCUMENTS_TYPE_COSTLY_APPROACH', 35);
        define('DOCUMENTS_TYPE_COSTLY_APPROACH_ANALOGUES', 41);
        define('COLOR_LIGHT_GREEN', 'EBF1DE');
        define('COLOR_DARK_GREEN', 'C4D79B');
        define('COLOR_DARK_MIDDLE', 'AAC9B5');
        $params['settings']['doc_types_level_two'] = preg_split('/\s*,\s*/', $params['settings']['doc_types_level_two']);
        $params['settings']['doc_types_level_three'] = preg_split('/\s*,\s*/', $params['settings']['doc_types_level_three']);
        self::$settings = array_merge(self::$settings, $params['settings']);
        $upi_types = [
            $params['settings']['doc_type_upi_empty'],
            $params['settings']['doc_type_pi_building']
        ];

        //define whether it is supervision or not
        $isSupervision = !empty($_GET['supervision']) ? true : false;

        // define the ALVIS URL according to the referer
        $alvis_url = 'https://alvis.ocenki.bg';
        $referer = !empty($_SERVER['HTTP_REFERER']) ? parse_url($_SERVER['HTTP_REFERER']) : array();
        if (!empty($referer) && preg_match('#alvis#i', $referer['host'])) {
            $alvis_url = $referer['scheme'] . '://' . $referer['host'];
        }

        //GET VARS
        $vars = self::getModelVars();

        // Prepare main and alternative currencies
        $main_currency = $vars['currency_methods']['value'];
        $alt_currency = $vars['ref_currencies']['value'];

        /*
         * Define precisions as constants so they can be accesses from functions outside the class
         */
        // Precision (default: 2)
        define('PRINT_PRECISION', ($vars['currency_rounding']['value'] === '' ? 2 : intval($vars['currency_rounding']['value'])));
        // Precision out (default: 0)
        define('PRINT_PRECISION_OUT', ($vars['currency_rounding_out']['value'] === '' ? 0 : (intval($vars['currency_rounding_out']['value']) * -1)));

        //get own customer (ADVANCE)
        $customer = Customers::searchOne($registry, array('where' => array('c.id=' . CUSTOMER_ADVANCE_ID), 'sanitize' => true));
        $customer_vars = self::_decorateVars($customer->getVarsForTemplateAssoc());

        //get the employee
        if ($model->get('employee')) {
            $employee = Customers::searchOne($registry, array('where' => array('c.id=' . $model->get('employee')), 'sanitize' => true));
            $employee_vars = self::_decorateVars($employee->getVarsForTemplateAssoc());
        }

        //get the related documents
        //document_relat_grp, get only rows with selected document type (document_relat_type) and selected child (document_relat_id)
        $related_records = array();
        if (!empty($vars['document_relat_grp']['values'])) {
            $relatives_group_idx = array_flip($vars['document_relat_grp']['names']);
            $vars['document_relat_grp']['values'] = array_filter($vars['document_relat_grp']['values'], function ($a) use ($relatives_group_idx) {
                return !empty($a[$relatives_group_idx['document_relat_type']]) &&
                    !empty($a[$relatives_group_idx['document_relat_id']]);
            });
        }
        if (!empty($vars['document_relat_grp']['values'])) {
            foreach ($vars['document_relat_grp']['values'] as $row) {
                $related_records[$row[$relatives_group_idx['document_relat_type']]][] = $row[$relatives_group_idx['document_relat_id']];
            }
        }
        self::$related_records = $related_records;

        /*
         * Prepare hierarchy records
         */
        $hierarchy_records = array();
        $hierarchy_records_sorted = array();
        $hierarchy_parents = array();
        $buildings_ids = [];
        $upis_ids = [];
        if (!empty($vars['hierarchy_document_relat_grp']['values'])) {
            $hierarchy_group_idx = array_flip($vars['hierarchy_document_relat_grp']['names']);
            $vars['hierarchy_document_relat_grp']['values'] = array_filter($vars['hierarchy_document_relat_grp']['values'], function ($a) use ($hierarchy_group_idx) {
                return !empty($a[$hierarchy_group_idx['hierarchy_document_relat_type']]) &&
                    !empty($a[$hierarchy_group_idx['hierarchy_document_relat_id']]) &&
                    !empty($a[$hierarchy_group_idx['hierarchy_document_parent_id']]);
            });
        }
        if (!empty($vars['hierarchy_document_relat_grp']['values'])) {
            foreach ($vars['hierarchy_document_relat_grp']['values'] as $row) {
                $hierarchy_records_sorted[] = $row[$hierarchy_group_idx['hierarchy_document_relat_id']];
                $hierarchy_records[$row[$hierarchy_group_idx['hierarchy_document_relat_type']]][] = $row[$hierarchy_group_idx['hierarchy_document_relat_id']];
                $hierarchy_parents[$row[$hierarchy_group_idx['hierarchy_document_parent_id']]][] = $row[$hierarchy_group_idx['hierarchy_document_relat_id']];

                if (in_array($row[$hierarchy_group_idx['hierarchy_document_relat_type']], $params['settings']['doc_types_level_two'])) {
                    $buildings_ids[] = $row[$hierarchy_group_idx['hierarchy_document_relat_id']];
                }
                if (in_array($row[$hierarchy_group_idx['hierarchy_document_relat_type']], $upi_types)) {
                    $upis_ids[] = $row[$hierarchy_group_idx['hierarchy_document_relat_id']];
                }
            }
        }

        // Check if there's an OPS
        $has_OPS = array_key_exists($params['settings']['doc_type_ops'], $hierarchy_records);

        // Prepare landscape
        // First extract the documents section properties.
        $sectProps_pos1 = strrpos(self::$templateDocx->_documentXML, '<w:sectPr');
        $sectProps_pos2 = strrpos(self::$templateDocx->_documentXML, '</w:sectPr>');
        self::$sectPr = substr(self::$templateDocx->_documentXML, $sectProps_pos1, $sectProps_pos2 - $sectProps_pos1 + 11);

        // Then find the headers and footers used, so we can tranfer them to the landscape sectin without guessing
        preg_match_all('/<w:(footer|header)Reference[^\/>]*\/>/', self::$sectPr, $matches);
        self::$footersAndHeaders = empty($matches[0]) ? '' : implode('', $matches[0]);

        $viewer = new Viewer($registry);
        $viewer->setFrameset(realpath(dirname(__FILE__) . '/../templates') . '/_start_landscape.xml');
        // Just use the original section string!
        $viewer->data['sectPr'] = self::$sectPr;
        $model->extender->add('start_landscape', $viewer->fetch());
        $viewer = new Viewer($registry);
        $viewer->setFrameset(realpath(dirname(__FILE__) . '/../templates') . '/_end_landscape.xml');
        $viewer->data['paths'] = self::$paths;
        $viewer->data['footersAndHeaders'] = self::$footersAndHeaders;
        $model->extender->add('end_landscape', $viewer->fetch());

        $ADX_temp = self::_getDocumentXMLSnippet('APPENDIX');
        if (!empty($ADX_temp)) {
            self::$ADX_template = $ADX_temp;

            $tClosingString = '</w:t></w:r></w:p>'; // 18
            $tOpeningString = '<w:t><w:r><w:p>'; // 15

            // Try to figure out and sanitize the envelop
            if (strpos(self::$ADX_template, $tClosingString) === 0) {
                $tClosingString_len = strlen($tClosingString);
                $cutOff = strrpos(self::$ADX_template, '<w:p>');
                $xml_len = ($cutOff !== false ? $cutOff : strlen(self::$ADX_template)) - $tClosingString_len;
                self::$ADX_template = substr(self::$ADX_template, $tClosingString_len, $xml_len);
                unset($tClosingString_len, $cutOff, $xml_len);
            }

            // Try to remove any unececery stuff before and after the envelop
            // This assumes the appendix starts and/or ends with a table
            $startApendix = strpos(self::$ADX_template, '<w:tbl>');
            $endApendix = strrpos(self::$ADX_template, '</w:tbl><w:p');
            if ($startApendix !== false || $endApendix !== false) {
                $startApendix = intval($startApendix ?? 0);
                self::$ADX_template = substr(
                    self::$ADX_template,
                    $startApendix,
                    $endApendix ? ($endApendix + 8 - $startApendix) : null
                );
            }
            unset($startApendix, $endApendix);

            // Remove the apendix template as it will be used elswhere
            self::_replaceDocumentXMLSnippet('APPENDIX', '');
        }
        self::$model->extender->add('appendix_number', '[appendix_number]');

        self::$model->extender->add('custom_num', $model->get('custom_num'));

        self::$model->extender->add('g_document_date', $model->extender->placeholders['document_date']);
        $rating_type = self::getRatingTypeLabel($vars['rating_type']['value'], $params['settings']);

        $model->extender->add('a_rating_type', $rating_type);

        // Get the collage
        if (!empty($related_records[DOCUMENTS_TYPE_VIEW_MOBILE_APP_COLLAGE])) {
            //get the first if more than one found
            $collage_id = $related_records[DOCUMENTS_TYPE_VIEW_MOBILE_APP_COLLAGE][0];
            $collage = Documents::searchOne($registry, array('where' => array('d.id = ' . $collage_id)));
            $imagesByObjId = array();
            if ($collage) {
                $collage_vars = self::_decorateVars($collage->getVarsForTemplateAssoc());
                // Process collage images
                if (!empty($collage_vars['images_grp']['values'])) {
                    $images_grp_idx = array_flip($collage_vars['images_grp']['names']);
                    $images = array();
                    $i = 0;
                    foreach ($collage_vars['images_grp']['values'] as $images_row) {
                        if (!empty($images_row[$images_grp_idx['image_upload']])) {
                            // Get the image file
                            $image_file = $images_row[$images_grp_idx['image_upload']];
                            if (($images_row[$images_grp_idx['image_hierarchy_object']] ?? '') != '' && !$image_file->get('not_exist')) {
                                $imagesByObjId[$images_row[$images_grp_idx['image_hierarchy_object']]] = $image_file;
                            }

                            // Skip images, which are not marked tobe used into the print
                            if ($images_row[$images_grp_idx['image_main']] != '1') {
                                unset($image_file);
                                continue;
                            }

                            // Check if it's a File class and the image exists on the server
                            if (is_a($image_file, 'File') && !$image_file->get('not_exist')) {
                                // Prepare the main image
                                if (!empty($images_row[$images_grp_idx['image_upload_main']])) {
                                    $model->extender->add('a_main_image', '<img src="' . $image_file->getFileURL('viewfile') . '&maxwidth=300px&maxheight=300px" alt="" />');
                                }
                                $pos = floatval($images_row[$images_grp_idx['image_position']]);
                                if (empty($pos)) {
                                    $pos = $i;
                                }
                                $i = $pos + 1;
                                // Collect all images
                                $images[] = array(
                                    'position' => $pos,
                                    'img_src' => $image_file->getFileURL('viewfile'),
                                    'description' => $images_row[$images_grp_idx['image_desc']],
                                );
                            }
                            unset($image_file);
                        }
                    }

                    /*
                     * Prepare all images
                     */
                    if (!empty($images)) {
                        // Sort by position: '1', '2', '3', '' (images without position will be at the end)
                        usort($images, function ($item1, $item2) {
                            if ($item1['position'] === $item2['position']) {
                                $result = 0;
                            } elseif ($item1['position'] === '' || $item2['position'] !== '' && $item1['position'] > $item2['position']) {
                                $result = 1;
                            } else {
                                $result = -1;
                            }
                            return $result;
                        });
                        $viewer = new Viewer($registry);
                        $viewer->setFrameset($paths['templates'] . '/C1.1_collage.xml');
                        // $viewer->loadCustomI18NFiles($paths['templates'] . '/print.ini');
                        $viewer->data['images'] = $images;
                        $viewer->data['max_columns'] = 2;
                        $viewer->data['max_rows'] = 3;
                        $viewer->data['table_width'] = self::$tablesWidthFullPage;
                        $viewer->data['grid_width'] = intval(self::$tablesWidthFullPage / $viewer->data['max_columns']);
                        $viewer->data['img_cells_height'] = 3533;
                        $viewer->data['img_description_cells_height'] = 250;
                        $viewer->data['img_max_width'] = 300;
                        $viewer->data['img_max_height'] = 200;
                        $C1_1_collage = $viewer->fetch();
                        $model->extender->add('C1_1_collage', $C1_1_collage);
                    }
                }
            }
        }

        //owner_name (add it as table row)
        if (!empty($model->extender->placeholders['a_owner_name'])) {
            $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')) . '/print.ini';
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_row.xml';
            $viewer = new Viewer($registry);
            $viewer->setFrameset($template_file);
            $viewer->loadCustomI18NFiles($i18n_file);
            $viewer->data['label'] = self::i18n('owner_name');
            $viewer->data['value'] = $model->extender->placeholders['a_owner_name'];
            $viewer->data['noBorders'] = true;

            $owner_name = $viewer->fetch();
            $model->extender->add('a_owner_name_row', $owner_name);
        }

        //loan_applicant_name (add it as table row)
        if (!empty($model->extender->placeholders['a_loan_applicant_name'])) {
            $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')) . '/print.ini';
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_row.xml';
            $viewer = new Viewer($registry);
            $viewer->setFrameset($template_file);
            $viewer->loadCustomI18NFiles($i18n_file);
            $viewer->data['label'] = self::i18n('loan_applicant_name');
            $viewer->data['value'] = $model->extender->placeholders['a_loan_applicant_name'];
            $viewer->data['noBorders'] = true;

            $loan_applicant_name = $viewer->fetch();
            $model->extender->add('a_loan_applicant_name_row', $loan_applicant_name);
        }

        // office address
        $office_address = '';
        $office_name = $model->extender->placeholders['bg_document_office'];
        if (preg_match('#партньор#ui', $office_name)) {
            $office_address = self::i18n('office_address_partner');
        } elseif ($model->get('office')) {
            $office = Offices::searchOne($registry, array('where' => array('o.id=' . $model->get('office')), 'sanitize' => true));
            if ($office) {
                $office_address = $office->get('description');
            }
        }
        $model->extender->add('a_office_address', $office_address);

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_owner_loan_applicant.xml';
        $viewer = new Viewer($registry);
        $viewer->setFrameset($template_file);
        $viewer->data['owner_name'] = $model->extender->placeholders['a_owner_name'];
        $viewer->data['loan_applicant_name'] = $model->extender->placeholders['a_loan_applicant_name'];
        $viewer->data['customer_name'] = trim($model->extender->placeholders['bg_customer_name'] . ' ' . $model->extender->placeholders['bg_customer_lastname']);
        $viewer->data['assignor_name'] = trim($model->extender->placeholders['a_assignor_name']);
        $viewer->data['bank_name'] = false;
        $viewer->data['color_dark'] = COLOR_DARK_GREEN;
        $viewer->data['color_light'] = COLOR_LIGHT_GREEN;

        $table_owner_loan_applicant = $viewer->fetch();
        $model->extender->add('table_owner_loan_applicant', $table_owner_loan_applicant);

        // Prepare the assessment purpose full description
        if (!empty($vars['assessment_purpose']['value'])) {
            $query = "
                SELECT nc.value
                  FROM " . DB_TABLE_NOMENCLATURES . " AS n
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (n.id = {$vars['assessment_purpose']['value']}
                      AND fm.model = 'Nomenclature'
                      AND fm.model_type = n.type
                      AND fm.name = 'desc_full_additional')
                  JOIN " . DB_TABLE_NOMENCLATURES_CSTM . " AS nc
                    ON (nc.model_id = n.id
                      AND nc.var_id = fm.id
                      AND nc.num = 1
                      AND nc.lang = '')";
            $assessment_purpose = $db->GetOne($query);
            $model->extender->add('assessment_purpose_desc_full_additional', $assessment_purpose);
        }

        //bulletize the checkboxes: a_transport_check, a_construction_check, a_public_service_check, a_infrastructure_check
        $checkboxes = array('transport_check', 'construction_check', 'public_service_check', 'infrastructure_check');
        foreach ($checkboxes as $checkbox) {
            if (!empty($vars[$checkbox]['value'])) {
                $template_file = realpath(dirname(__FILE__) . '/../templates') . '/bullets.xml';
                $viewer = new Viewer($registry);
                $viewer->setFrameset($template_file);
                $viewer->data['items'] = preg_split('#<br />\n?#mi', $model->extender->placeholders['a_' . $checkbox]);
                $model->extender->add('a_' . $checkbox, $viewer->fetch());
            }
        }

        // Risk description
        if ($vars['additional_desc_full']['value'] === '') {
            $model->extender->add('a_additional_desc_full', self::i18n('additional_desc_full_default_text'));
        }

        //bank logo and bank_fin_institution_text
        $bank_logo = '';
        $bank_id = $vars['bank_id']['value'];
        if (!empty($bank_id)) {
            $bank = Customers::searchOne($registry, array('where' => array('c.id=' . $bank_id), 'sanitize' => true));
            if ($bank) {
                $bank_vars = self::_decorateVars($bank->getVarsForTemplateAssoc());
                $model->extender->add('international_standards_deviations', $bank_vars['international_standards_deviations']['value']);
                if ($bank_vars['bank_logo']['value'] && is_a($bank_vars['bank_logo']['value'], 'File') && !$bank_vars['bank_logo']['value']->get('not_exist')) {
                    $bank_logo = '<img src="' . $bank_vars['bank_logo']['value']->getFileURL('viewfile') . '&maxwidth=300px&maxheight=150px" alt="" />';
                }

                //bank_fin_institution_text
                if (!empty($bank_vars['bank_type']['value'])) {
                    $model->extender->add('bank_fin_institution_text', self::i18n('bank_fin_institution_text'));
                }
            }
        }
        $model->extender->add('bank_logo', $bank_logo);

        //object address
        $report_address = implode(', ', array_filter([
            $vars['populated_place']['value'],
            $vars['analog_quarter_name']['value'],
            $vars['street_name']['value'],
            $vars['street_number']['value'],
            empty($vars['apartment_building']['value']) ? null : 'бл. ' . $vars['apartment_building']['value'],
            empty($vars['entrance']['value']) ? null : 'вх. ' . $vars['entrance']['value'],
            $vars['address_more_desc']['value'],
        ]));
        $model->extender->add('g_object_address', $report_address);

        //object methods (market, revenue, costly)
        $object_group_table = $vars['object_group_table'];
        // This is a fix, because some times the "values" key does not exist
        $object_group_table['values'] = $object_group_table['values'] ?? [];
        $level_one_elements_ids = array_filter(
            array_column(
                $object_group_table['values'],
                array_search('level_one_element_id', $object_group_table['names'])
            )
        );

        $market_approach_methods = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('market_approach', $object_group_table['names'])
            )
        ));
        $market_approach_method_ids = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('market_approach_id', $object_group_table['names'])
            )
        ));
        $revenue_approach_methods = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('revenue_approach', $object_group_table['names'])
            )
        ));
        $revenue_approach_method_ids = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('revenue_approach_id', $object_group_table['names'])
            )
        ));
        $costly_approach_methods = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('costly_approach', $object_group_table['names'])
            )
        ));
        $costly_approach_method_ids = array_filter(array_unique(
            array_column(
                $object_group_table['values'],
                array_search('costly_approach_id', $object_group_table['names'])
            )
        ));

        //get the standard nomenclature which stores the approaches texts and method names
        $standard_nom = false;
        if (!empty($vars['standart_nom_id']['value'])) {
            $standard_nom = Nomenclatures::searchOne(
                $registry,
                array(
                    'where' => array(
                        'n.id = ' . $vars['standart_nom_id']['value']
                    )
                )
            );
        }
        $approach_methods_texts = $approach_texts = array(
            'market' => array(),
            'revenue' => array(),
            'costly' => array(),
        );
        if ($standard_nom) {
            $standard_nom_vars = $standard_nom->getAssocVars();
            foreach (array('market', 'revenue', 'costly') as $method_type) {
                //loop market_approach_method_ids, revenue_approach_method_ids, costly_approach_method_ids
                $method_ids_var = "{$method_type}_approach_method_ids";
                $method_ids = $$method_ids_var;
                foreach ($method_ids as $method_nom_id) {
                    $stnidx = array_search($method_nom_id, $standard_nom_vars['method_id']['value']);
                    $method_text = $standard_nom_vars['method_text']['value'][$stnidx] ?? false;
                    if ($method_text) {
                        $approach_methods_texts[$method_type][] = $method_text;
                    }

                    switch ($method_type) {
                        case 'market':
                            $approach_nom_id = $params['settings']['nom_d_approach_market_approach'];
                            break;
                        case 'revenue':
                            $approach_nom_id = $params['settings']['nom_d_approach_revenue_approach'];
                            break;
                        case 'costly':
                            $approach_nom_id = $params['settings']['nom_d_approach_costly_approach'];
                            break;
                    }

                    $stnidx = array_search($approach_nom_id, $standard_nom_vars['approach_id']['value']);
                    $approach_text = $standard_nom_vars['approach_text']['value'][$stnidx];
                    if ($approach_text) {
                        $approach_texts[$method_type][] = $approach_text;
                    }
                }
            }
        }

        $model->extender->add('m_x', self::_drawSquare(!empty($market_approach_methods)));
        $model->extender->add('r_x', self::_drawSquare(!empty($revenue_approach_methods)));
        $model->extender->add('c_x', self::_drawSquare(!empty($costly_approach_methods)));

        $model->extender->add('market_methods', implode('<w:br/>', $approach_methods_texts['market']));
        $model->extender->add('revenue_methods', implode('<w:br/>', $approach_methods_texts['revenue']));
        $model->extender->add('costly_methods', implode('<w:br/>', $approach_methods_texts['costly']));

        $model->extender->add('market_approach_text', !empty($market_approach_methods) && !empty($approach_texts['market']) ? $approach_texts['market'][0] : '');
        $model->extender->add('revenue_approach_text', !empty($revenue_approach_methods) && !empty($approach_texts['revenue']) ? $approach_texts['revenue'][0] : '');
        $model->extender->add('costly_approach_text', !empty($costly_approach_methods) && !empty($approach_texts['costly']) ? $approach_texts['costly'][0] : '');

        $model->extender->add('standard_description', !empty($standard_nom_vars) ? $standard_nom_vars['standard_description']['value'] : '');

        // own_comp_certificate from customer ADVANCE var
        $model->extender->add('own_comp_certificate', $customer_vars['own_comp_certificate']['value']);

        //map images
        $map_one = '';
        if ($vars['map_one']['value'] && is_a($vars['map_one']['value'], 'File') && !$vars['map_one']['value']->get('not_exist')) {
            $map_one = '<img src="' . $vars['map_one']['value']->getFileURL('viewfile') . '&maxwidth=300px&maxheight=300px" alt="" />';
        }
        $model->extender->add('a_map_one', $map_one);
        $map_two = '';
        if ($vars['map_two']['value'] && is_a($vars['map_two']['value'], 'File') && !$vars['map_two']['value']->get('not_exist')) {
            $map_two = '<img src="' . $vars['map_two']['value']->getFileURL('viewfile') . '&maxwidth=300px&maxheight=300px" alt="" />';
        }
        $model->extender->add('a_map_two', $map_two);

        //graphics image
        $graphics = '';
        if ($vars['graphics']['value'] && is_a($vars['graphics']['value'], 'File') && !$vars['graphics']['value']->get('not_exist')) {
            $graphics = '<img src="' . $vars['graphics']['value']->getFileURL('viewfile') . '&maxwidth=500px&maxheight=300px" alt="" />';
        }
        $model->extender->add('a_graphics', $graphics);

        //table market assessment
        $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')) . '/print.ini';
        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_market_assessment.xml';
        $viewer = new Viewer($registry);
        $viewer->setFrameset($template_file);
        $viewer->loadCustomI18NFiles($i18n_file);
        $viewer->data['graphics'] = $graphics;
        $viewer->data['market_desc_info'] = isset($model->extender->placeholders['a_market_desc_info']) ? $model->extender->placeholders['a_market_desc_info'] : '';
        $viewer->data['color_dark'] = COLOR_DARK_GREEN;
        $viewer->data['color_light'] = COLOR_LIGHT_GREEN;

        $table_market_assessment = $viewer->fetch();
        $model->extender->add('table_market_assessment', $table_market_assessment);

        //Variables from results document (39)
        if (!empty($related_records[DOCUMENTS_TYPE_RESULTS])) {
            //get the first if more than one found
            $results_doc_id = $related_records[DOCUMENTS_TYPE_RESULTS][0];
            $results_doc = Documents::searchOne($registry, array('where' => array('d.id = ' . $results_doc_id)));

            if ($results_doc) {
                $getVarsForTemplateAssoc = $results_doc->getVarsForTemplateAssoc();
                $results_vars = self::_decorateVars($getVarsForTemplateAssoc);

                // Prepare table R1.1
                $model->extender->add('results_table_R1_1', self::_prepareResultsTableR11($results_vars));

                // Prepare table R1.3
                $model->extender->add('results_table_R1_3', self::_prepareResultsTableR13($results_vars));

                // Prepare table R1.2
                $model->extender->add('results_table_R1_2', self::_prepareResultsTableR12($results_vars));

                // Prepare table R1.6
                $model->extender->add('results_table_R1_6', self::_prepareResultsTableR16($results_vars));

                // Prepare table R1.8
                $model->extender->add('results_table_R1_8', self::_prepareResultsTableR18($results_vars));

                // Prepare table R1.4
                $model->extender->add('results_table_R1_4', self::_prepareResultsTableR14($results_vars));

                //R1.1 employee comments
                if (!empty($results_vars['employee_comments']['value'])) {
                    $model->extender->add('R1_1_comments', $results_vars['employee_comments']['value']);
                }
            }
        }

        $report0_vars = self::getReport0Vars();

        // C1.2 documents
        // get only rows with selected document type (doc_type)
        if (!empty($report0_vars['attached_group']['values'])) {
            $table = array();
            $attached_group_idx = array_flip($report0_vars['attached_group']['names']);
            foreach ($report0_vars['attached_group']['values'] as $row) {
                if (empty($row[$attached_group_idx['doc_type']]) || $row[$attached_group_idx['doc_issued_by']] !== '1') {
                    continue;
                }

                $label = '';
                foreach ($report0_vars['attached_group']['doc_type']['options'] as $opt) {
                    if ($opt['option_value'] == $row[$attached_group_idx['doc_type']]) {
                        $label = $opt['label'];
                        break;
                    }
                }

                $table[] = array(
                    $label,
                    $row[$attached_group_idx['attached_doc_notes']] ?? ''
                );
            }

            if (!empty($table)) {
                $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')) . '/print.ini';
                $template_file = realpath(dirname(__FILE__) . '/../templates') . '/C1.2_documents.xml';
                $viewer = new Viewer($registry);
                $viewer->setFrameset($template_file);
                $viewer->loadCustomI18NFiles($i18n_file);
                $viewer->data['table'] = $table;
                $viewer->data['color_dark'] = COLOR_DARK_GREEN;
                $viewer->data['color_light'] = COLOR_LIGHT_GREEN;

                $C1_2_documents = $viewer->fetch();
                $model->extender->add('C1_2_documents', $C1_2_documents);
            }
        }

        // Evaluators a_evaluators, a_employee_mobile, sertificate_num
        $recording_role_names = array_flip($report0_vars['recording_role_group']['names']);
        $signerId = null;
        $signer_vars = [];
        foreach ($report0_vars['recording_role_group']['values'] ?? '' as $v) {
            if ($v[$recording_role_names['recording_role_type']] == 5) { // Подписващ оценката
                $signerId = $v[$recording_role_names['recording_role_id']];
                $signer = Customers::searchOne($registry, array('where' => array("c.id={$signerId}"), 'sanitize' => true));
                $signer_vars = self::_decorateVars($signer->getVarsForTemplateAssoc());
                $signer_vars['a_employee_mobile'] = $signer->get('gsm')[0] ?? '';
                $sertificate_group_names = array_flip($signer_vars['sertificate_group']['names']);
                foreach ($signer_vars['sertificate_group']['values'] as $v1) {
                    if ($v1[$sertificate_group_names['type_sertificate_id']] == 265) {
                        $signer_vars['sertificate_num'] = $v1[$sertificate_group_names['sertificate_num']];
                        $model->extender->add('sertificate_num', $signer_vars['sertificate_num']);
                        break;
                    }
                }
                $model->extender->add('a_evaluators', "{$signer->get('name')} {$signer->get('lastname')}");
                $model->extender->add('a_employee_mobile', $signer_vars['a_employee_mobile']);
                $model->extender->add('sertificate_num', $signer_vars['sertificate_num'] ?? '');
                break;
            }
        }

        //Variables from ANALYTICS document (38)
        if (!empty($related_records[DOCUMENTS_TYPE_ANALYTICS])) {
            //get the first if more than one found
            $analytics_doc_id = $related_records[DOCUMENTS_TYPE_ANALYTICS][0];
            $analytics_doc = Documents::searchOne($registry, array('where' => array('d.id = ' . $analytics_doc_id)));

            if ($analytics_doc) {
                $analytics_vars = self::_decorateVars($analytics_doc->getVarsForTemplateAssoc());

                //some single variables
                $model->extender->add('established_differences', $analytics_vars['established_differences']['value']);
                $model->extender->add('collateral', $analytics_vars['collateral']['value']);
                $model->extender->add('collateral_comment', $analytics_vars['collateral_comment']['value']);
                $model->extender->add('illegal_construction', $analytics_vars['illegal_construction']['value']);
                $model->extender->add('illegal_construction_comment', $analytics_vars['illegal_construction_comment']['value']);
                $model->extender->add('special_assumptions_swot', $analytics_vars['special_assumptions_swot']['value']);
                $model->extender->add('spec_asumptions_name_swot', $analytics_vars['spec_asumptions_name_swot']['value']);
                $model->extender->add('legal_compliance', $analytics_vars['swot_config']['values']['legal_compliance']);
                $model->extender->add('technical_compliance', $analytics_vars['swot_config']['values']['technical_compliance']);
                $model->extender->add('financial_justification', $analytics_vars['swot_config']['values']['financial_justification']);
                $model->extender->add('conclusion_def', $analytics_vars['swot_config']['values']['conclusion_def']);

                //D1.1 assumptions tables
                //gen_assumptions_group, get only rows with description (gen_assumptions_name)
                if (!empty($analytics_vars['gen_assumptions_group']['values'])) {
                    $gen_assumptions_group_idx = array_flip($analytics_vars['gen_assumptions_group']['names']);
                    $analytics_vars['gen_assumptions_group']['values'] = array_filter($analytics_vars['gen_assumptions_group']['values'], function ($a) use ($gen_assumptions_group_idx) {
                        return !empty($a[$gen_assumptions_group_idx['gen_assumptions_name']]);
                    });
                }
                if (!empty($analytics_vars['spec_assumptions_group']['values'])) {
                    //spec_assumptions_group, get only rows with description (spec_asumptions_name)
                    $spec_assumptions_group_idx = array_flip($analytics_vars['spec_assumptions_group']['names']);
                    $analytics_vars['spec_assumptions_group']['values'] = array_filter($analytics_vars['spec_assumptions_group']['values'], function ($a) use ($spec_assumptions_group_idx) {
                        return !empty($a[$spec_assumptions_group_idx['spec_asumptions_name']]);
                    });
                }
                $table = array(
                    'gen' => array(),
                    'spec' => array(),
                );
                $br = '<w:br/>';
                if (!empty($analytics_vars['gen_assumptions_group']['values'])) {
                    foreach ($analytics_vars['gen_assumptions_group']['values'] as $row) {
                        $rowContent = trim($row[$gen_assumptions_group_idx['gen_assumptions_name']]);
                        $rowContent_split = explode("\n", str_replace("\r", '', $rowContent));
                        $rowContent_clean_ar = [];
                        foreach ($rowContent_split as $k => $v) {
                            $temp = self::trimStr($v, $br);
                            if (!empty($temp)) {
                                $rowContent_clean_ar[] = $temp;
                            }
                        }
                        $table['gen'][] = $rowContent_clean_ar;
                    }
                }

                if (!empty($analytics_vars['spec_assumptions_group']['values'])) {
                    foreach ($analytics_vars['spec_assumptions_group']['values'] as $row) {
                        $rowContent = trim($row[$spec_assumptions_group_idx['spec_asumptions_name']]);
                        $rowContent_split = explode("\n", str_replace("\r", '', $rowContent));
                        $rowContent_clean_ar = [];
                        foreach ($rowContent_split as $k => $v) {
                            $temp = self::trimStr($v, $br);
                            if (!empty($temp)) {
                                $rowContent_clean_ar[] = $temp;
                            }
                        }
                        $table['spec'][] = $rowContent_clean_ar;
                    }
                }
                unset($br, $temp, $rowContent_clean_ar);
                $table = array_filter($table);

                if (!empty($table)) {
                    $i18n_file = realpath(dirname(__FILE__) . '/../i18n/' . $model->get('model_lang')) . '/print.ini';
                    $template_file = realpath(dirname(__FILE__) . '/../templates') . '/D1.1_assumptions.xml';
                    $viewer = new Viewer($registry);
                    $viewer->setFrameset($template_file);
                    $viewer->loadCustomI18NFiles($i18n_file);
                    $viewer->data['table'] = $table;
                    $viewer->data['color_dark'] = COLOR_DARK_GREEN;
                    $viewer->data['color_light'] = COLOR_LIGHT_GREEN;

                    $assumptions = $viewer->fetch();
                    $model->extender->add('D1_1_assumptions', $assumptions);
                }

                //employee_comments
                $model->extender->add('a_employee_comments', $analytics_vars['employee_comments']['value']);

                //established_differences
                if ($vars['rating_type']['value'] != $params['settings']['nom_type_of_service_new_assessment']) {
                    //do not display the table for new assessments (rating_type 1)
                    $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_established_differences.xml';
                    $viewer = new Viewer($registry);
                    $viewer->setFrameset($template_file);
                    $viewer->data['established_differences'] = $analytics_vars['established_differences']['value'];
                    $viewer->data['color_dark'] = COLOR_DARK_GREEN;
                    $viewer->data['color_light'] = COLOR_LIGHT_GREEN;

                    $table_established_differences = $viewer->fetch();
                    $model->extender->add('table_established_differences', $table_established_differences);
                }
            }
        }

        $assessmentObjects = [];

        // Variables from level one objects
        $doc_types = array(
            $params['settings']['doc_type_upi_empty'],
            $params['settings']['doc_type_pi_building'],
            $params['settings']['doc_type_ops'],
        );
        foreach ($doc_types as $doc_type) {
            $tag = 'UPI' . $doc_type;
            if ($doc_type == $params['settings']['doc_type_ops']) {
                $tag = 'OPS';
            }
            //get the snippet from the document XML
            $UPI_template = self::_getDocumentXMLSnippet($tag);
            if (!empty($UPI_template)) {
                $UPI_data = array();
                if (!empty($hierarchy_records[$doc_type])) {
                    //fetch the documents
                    $UPI_docs = Documents::search($registry, array(
                        'where' => array(
                            'd.type = ' . $doc_type,
                            'd.id IN (' . implode(', ', $hierarchy_records[$doc_type]) . ')',
                        ),
                        'sort' => array(
                            'find_in_set(d.id, "' . implode(',', $hierarchy_records[$doc_type]) . '")',
                        )
                    ));
                    if ($UPI_docs) {
                        if ($tag == 'OPS') {
                            self::$isOps = true;
                        }
                        foreach ($UPI_docs as $UPI_doc) {
                            $assessmentObjects[$UPI_doc->get('id')] = $UPI_doc;
                            $extender = new Extender();
                            //add placeholders to replace them in the snippet
                            $UPI_doc->unsanitize();
                            $pattern_vars = $UPI_doc->getPatternsVars();
                            if (!isset($pattern_vars['a_other_info'])) {
                                //specific placeholder (if no checkboxes selected in other_info)
                                $pattern_vars['a_other_info'] = self::i18n('none');
                            }

                            $extender->merge($pattern_vars);
                            if (isset($imagesByObjId[$UPI_doc->get('id')])) {
                                $extender->add(
                                    'a_picture_desc',
                                    '<img src="' .
                                    $imagesByObjId[$UPI_doc->get('id')]->getFileURL('viewfile') .
                                    '&maxwidth=220px&maxheight=220px" alt="" />'
                                );
                            }
                            //only add this snippet specific variables for hyperlinks
                            //the global variable (document_id, alvis_url) should be replaced separately
                            $extender->add('object_id', $UPI_doc->get('id'));

                            $UPI_template = self::_handleIfData($UPI_template, $extender);
                            $UPI_template_replaced = self::_manageAlvisHyperlinks(
                                $UPI_template,
                                $isSupervision,
                                $extender
                            );


                            //replace placeholders
                            $UPI_data[] = $extender->expand($UPI_template_replaced);
                        }
                    }
                }
                //replace the snippet with the replaced placeholders
                self::_replaceDocumentXMLSnippet($tag, $UPI_data);
            }
        }

        // Variables from level two objects
        $tag = 'BUILDING';
        //get the snippet from the document XML
        $BUILDING_template = self::_getDocumentXMLSnippet($tag);
        $BUILDING_specific_teplates = [];
        foreach ($params['settings']['doc_types_level_two'] as $v) {
            $BUILDING_specific_teplates[$v] = self::_getDocumentXMLSnippet($tag . $v);
            if (!empty($BUILDING_specific_teplates[$v])) {
                self::_replaceDocumentXMLSnippet($tag . $v, '');
            }
        }
        if (!empty($BUILDING_template)) {
            $BUILDING_data = array();
            $buildingNum = 0;
            foreach ($hierarchy_records_sorted as $doc_id) {
                foreach ($params['settings']['doc_types_level_two'] as $doc_type) {
                    if (!empty($hierarchy_records[$doc_type]) && in_array($doc_id, $hierarchy_records[$doc_type])) {
                        //fetch the documents
                        $BUILDING_doc = Documents::searchOne($registry, array(
                            'where' => array(
                                'd.type = ' . $doc_type,
                                'd.id = ' . $doc_id,
                            )
                        ));
                        if ($BUILDING_doc) {
                            $assessmentObjects[$BUILDING_doc->get('id')] = $BUILDING_doc;

                            $extender = new Extender();
                            //add placeholders to replace them in the snippet
                            $pattern_vars = $BUILDING_doc->getPatternsVars();
                            $extender->merge($pattern_vars);

                            // If the building is NOT an end assessment object (i.e. has children objects)
                            if (!empty($hierarchy_parents[$BUILDING_doc->get('id')])) {
                                $pattern_vars['building_title'] = self::i18n('building_title_containing_properties');
                            } else {
                                // If the building is an end assessment object (i.e. has NO children objects)
                                $pattern_vars['building_title'] = self::i18n('building_title_not_containing_properties');
                                // Prepare rooms table
                                if (isset($pattern_vars['a_econ_group_table'])) {
                                    $pattern_vars['a_econ_group_table'] = self::_prepareRoomsTable($BUILDING_doc);
                                }

                                if (isset($pattern_vars['a_aggregated_areas_group_table'])) {
                                    // prep area by approaches table
                                    $pattern_vars['a_area_appr_group_table'] = self::_prepareAreaByApproachBuildingsTable($BUILDING_doc);
                                    $pattern_vars['a_aggregated_areas_group_table'] = $pattern_vars['a_area_appr_group_table'];
                                }

                                // Prepare areas table
                                self::_prepareAreasTable($BUILDING_doc, $pattern_vars, $extender, true);
                            }
                            if (isset($pattern_vars['a_desc_level_group'])) {
                                $building_vars = $BUILDING_doc->getVarsForTemplateAssoc();
                                if (array_key_exists('desc_level_group', $building_vars) && !empty($building_vars['desc_level_group']['values'])) {
                                    $pattern_vars['a_desc_level_group'] = self::_prepareGroupingTable($building_vars['desc_level_group'], [], self::i18n('desc_level_group_header'));
                                }
                            }
                            //add object_desc_long to the list of patterns taking it from document type "Report (1)"
                            $pattern_vars['a_object_desc_long'] = $model->extender->placeholders['a_object_desc_long'];

                            $propTemp = !empty($BUILDING_specific_teplates[$doc_type]) ? $BUILDING_specific_teplates[$doc_type] : $BUILDING_template;
                            $propTemp = self::_handleIfData($propTemp, $extender);

                            $green_building_certificate = 0;

                            // Fix for wrong empty values of construction_year and last_repair_year
                            $bv = $BUILDING_doc->get('vars');
                            $has_harmful_emissions = false;
                            $harmful_emissions_info = '';
                            foreach ($bv as $k => $v) {
                                if (($v['name'] == 'construction_year' || $v['name'] == 'last_repair_year') && $v['value'] == '0') {
                                    $pattern_vars['a_' . $v['name']] = '';
                                }
                                if ($v['name'] === 'green_building_certificate') {
                                    $green_building_certificate = $v['value'];
                                }
                                if ($v['name'] === 'harmful_emissions' && $v['value'] === '40830') {
                                    $has_harmful_emissions = true;
                                }
                                if ($v['name'] === 'harmful_emissions_info') {
                                    $harmful_emissions_info = $v['value'];
                                }
                            }

                            if ($has_harmful_emissions) {
                                $pattern_vars['harmful_emissions_info'] = $harmful_emissions_info;
                            } else {
                                $pattern_vars['harmful_emissions_info'] = self::i18n('harmful_emissions_info_default_text');
                            }

                            if ($green_building_certificate != 1) {
                                $propTemp = self::_replaceDocumentXMLSnippet('GREENBUILDING', '', $propTemp);
                            }

                            // Add a formated variant of the uve date, that will not need a modifyer in the template, so the '-' can be shown if the var i s empty
                            $pattern_vars['a_date_uve_rp_formatted'] = empty($pattern_vars['a_date_uve_rp'])
                                ? ''
                                : General::strftime($registry->get('translater')->translate('date_short'), strtotime($pattern_vars['a_date_uve_rp']));

                            // Set default value for empty vars
                            foreach ($pattern_vars as $pattern_var_name => $pattern_var_value) {
                                if ($pattern_var_value === '') {
                                    // Default: -
                                    $pattern_vars[$pattern_var_name] = '-';
                                }
                            }
                            $extender->merge($pattern_vars);
                            if (isset($imagesByObjId[$BUILDING_doc->get('id')])) {
                                $extender->add('a_picture_desc', '<img src="' . $imagesByObjId[$BUILDING_doc->get('id')]->getFileURL('viewfile') . '&maxwidth=220px&maxheight=220px" alt="" />');
                            }
                            //only add this snippet specific variables for hyperlinks
                            //the global variable (document_id, alvis_url) should be replaced separately
                            $extender->add('object_id', $BUILDING_doc->get('id'));

                            $propTemp = self::_manageAlvisHyperlinks($propTemp, $isSupervision, $extender);

                            //replace placeholders
                            $BUILDING_data[] = $extender->expand($propTemp);

                            if ($buildingNum === 0) {
                                $firsBuildingExtender = $extender;
                            }
                            $buildingNum++;
                        }
                    }
                }
            }
            //replace the snippet with the replaced placeholders
            self::_replaceDocumentXMLSnippet($tag, $BUILDING_data);
        }
        $model->extender->add('BUILDING_incompleteness_info', self::_prepareIncompletenessTables($buildings_ids, $assessmentObjects));
        $model->extender->add('results_table_risk_note', self::_prepareRiskNoteTable($buildings_ids, $assessmentObjects));

        // Variables from level three objects
        $tag = 'PROPERTY';
        //get the snippet from the document XML
        $PROPERTY_template = self::_getDocumentXMLSnippet($tag);
        $PROPERTY_specific_teplates = [];
        foreach ($params['settings']['doc_types_level_three'] as $v) {
            $PROPERTY_specific_teplates[$v] = self::_getDocumentXMLSnippet($tag . $v);
            if (!empty($PROPERTY_specific_teplates[$v])) {
                self::_replaceDocumentXMLSnippet($tag . $v, '');
            }
        }
        if (!empty($PROPERTY_template)) {
            $PROPERTY_data = array();
            foreach ($hierarchy_records_sorted as $doc_id) {
                foreach ($params['settings']['doc_types_level_three'] as $doc_type) {
                    if (!empty($hierarchy_records[$doc_type]) && in_array($doc_id, $hierarchy_records[$doc_type])) {
                        //fetch the documents
                        $PROPERTY_doc = Documents::searchOne($registry, array(
                            'where' => array(
                                'd.type = ' . $doc_type,
                                'd.id = ' . $doc_id,
                            )
                        ));
                        if ($PROPERTY_doc) {
                            $assessmentObjects[$PROPERTY_doc->get('id')] = $PROPERTY_doc;
                            $extender = new Extender();
                            //add placeholders to replace them in the snippet
                            $pattern_vars = $PROPERTY_doc->getPatternsVars();
                            $extender->merge($pattern_vars);

                            // Prepare rooms table
                            if (isset($pattern_vars['a_econ_group_table'])) {
                                $pattern_vars['a_econ_group_table'] = self::_prepareRoomsTable($PROPERTY_doc);
                            }

                            // Prepare areas table
                            self::_prepareAreasTable($PROPERTY_doc, $pattern_vars, $extender);

                            if (isset($pattern_vars['a_area_appr_group_table'])) {
                                // prep area by approaches table
                                $pattern_vars['a_area_appr_group_table'] = self::_prepareAreaByApproachTable($PROPERTY_doc);
                            }

                            if (!isset($pattern_vars['a_other_rooms'])) {
                                //specific placeholder (if no checkboxes selected in other_info)
                                $pattern_vars['a_other_rooms'] = self::i18n('none');
                            }
                            $extender->merge($pattern_vars);
                            if (isset($imagesByObjId[$PROPERTY_doc->get('id')])) {
                                $extender->add('a_picture_desc', '<img src="' . $imagesByObjId[$PROPERTY_doc->get('id')]->getFileURL('viewfile') . '&maxwidth=220px&maxheight=220px" alt="" />');
                            }
                            //only add this snippet specific variables for hyperlinks
                            //the global variable (document_id, alvis_url) should be replaced separately
                            $extender->add('object_id', $PROPERTY_doc->get('id'));

                            $propTemp = !empty($PROPERTY_specific_teplates[$doc_type]) ? $PROPERTY_specific_teplates[$doc_type] : $PROPERTY_template;
                            $propTemp = self::_handleIfData($propTemp, $extender);
                            $propTemp = self::_manageAlvisHyperlinks($propTemp, $isSupervision, $extender);

                            //replace placeholders
                            $PROPERTY_data[] = $extender->expand($propTemp);
                        }
                    }
                }
            }
            //replace the snippet with the replaced placeholders
            self::_replaceDocumentXMLSnippet($tag, $PROPERTY_data);
        }

        $tag = 'MARKET_APPROACH';
        //get the snippet from the document XML
        $MA_template = self::_getDocumentXMLSnippet($tag);

        if (!empty($MA_template)) {
            $MA_data = array();
            if (!empty($related_records[DOCUMENTS_TYPE_MARKET_APPROACH])) {
                //fetch the documents
                $MA_docs = Documents::search($registry, array(
                    'where' => array(
                        'd.type = ' . DOCUMENTS_TYPE_MARKET_APPROACH,
                        'd.id IN (' . implode(', ', $related_records[DOCUMENTS_TYPE_MARKET_APPROACH]) . ')',
                    ),
                    'sort' => array(
                        'find_in_set(d.id, "' . implode(',', $related_records[DOCUMENTS_TYPE_MARKET_APPROACH]) . '")',
                    ),
                    'sanitize' => false,
                ));

                $market_approach_analogues = '';
                foreach ($MA_docs as $MA_doc) {
                    $current_template = $MA_template;
                    $MA_vars = self::_decorateVars($MA_doc->getVarsForTemplateAssoc());
                    $ma_comparative_table = self::_prepareComparativeTable($MA_vars);
                    if ($ma_comparative_table->xml === '') {
                        //if the first table is empty, no need to include the document
                        // (this is an old logic applied before the current edit)
                        continue;
                    }
                    $extender = new Extender();
                    //add placeholders to replace them in the snippet
                    $pattern_vars = $MA_doc->getPatternsVars();
                    $pattern_vars['market_approach_comparative_table'] = $ma_comparative_table->xml;
                    $pattern_vars['market_approach_comparative_table_summary'] = self::_prepareComparativeTableSummary($MA_vars);

                    $ga_names = array_flip($MA_vars['group_analogue']['names']);
                    $object_name = '';
                    if (isset($MA_vars['group_analogue']['values']) && isset($MA_vars['group_analogue']['values'][1])) {
                        $object_name = $MA_vars['group_analogue']['values'][1][$ga_names['comparison_element']] ?? '';
                    }

                    $extender->merge(self::$model->extender->placeholders);
                    $extender->add('object_name', $object_name);
                    if ($isSupervision) {
                        $url = '[alvis_url]/market-approach-comparativetable/' . self::$model->get('id') . '/' . $MA_doc->get('id');
                        $extender->add('title', self::_addLink($url, self::i18n('comparative_table_title_marketApproach')));
                    } else {
                        $extender->add('title', self::i18n('comparative_table_title_marketApproach'));
                    }

                    $analogs = self::_getAnalogsFromCompTable($MA_doc);
                    if (isset($MA_doc->get('assoc_vars')['group_analogue']['values']) && count($MA_doc->get('assoc_vars')['group_analogue']['values']) > 1) {
                        $names_idx = array_flip($MA_doc->get('assoc_vars')['group_analogue']['names']);
                        $objectName = $MA_doc->get('assoc_vars')['group_analogue']['values'][1][$names_idx['comparison_element']];
                        $analogRefTitle = sprintf(self::i18n('analog_ref_marketApproach'), $objectName);
                        $market_approach_analogues .= self::_prepareAnalogsTable($analogRefTitle, $analogs);
                    }

                    $extender->merge($pattern_vars);

                    //comments
                    $comments_template = self::_getDocumentXMLSnippet('MARKET_APPROACH_COMMENTS');
                    if ($comments_template) {
                        $object_id = $MA_vars['object_report_name_id']['value'];
                        $method_nom_id = '';
                        $method_text = '';
                        $object_group_table_names = array_flip($vars['object_group_table']['names']);
                        foreach (($vars['object_group_table']['values'] ?? []) as $ridx => $row) {
                            foreach (array('three', 'two', 'one') as $f) {
                                if ($row[$object_group_table_names['level_' . $f . '_element_id']] == $object_id) {
                                    $method_nom_id = $row[$object_group_table_names['market_approach_id']];
                                    break 2;
                                }
                            }
                        }
                        if ($standard_nom && $method_nom_id) {
                            $stnidx = array_search($method_nom_id, $standard_nom_vars['method_id']['value']);
                            $method_text = $standard_nom_vars['method_text']['value'][$stnidx];
                        }
                        $object_doc = Documents::searchOne($registry, array(
                            'where' => array(
                                'd.id = ' . $object_id,
                            )
                        ));
                        if (!is_object($object_doc)) {
                            continue;
                        }
                        if ($object_doc->get('type') == $params['settings']['doc_type_upi_empty']) {
                            // UPI EMPTY documents should not have comments
                            $comments_data = array();
                        } else {
                            $object_vars = $object_doc->getAssocVars();
                            $object_name = '';
                            if (isset($object_vars['object_name'])) {
                                $object_name = $object_vars['object_name']['value'];
                            } elseif (isset($object_vars['property_num'])) {
                                $object_name = $object_vars['property_num']['value'];
                            }
                            if (isset($object_vars['perfect_market']) && count(array_filter($object_vars['perfect_market']['value'])) > 0) {
                                //with ОЧС
                                $comment_level_template = self::i18n('comment_level_template1');
                            } else {
                                //without ОЧС
                                $comment_level_template = self::i18n('comment_level_template2');
                            }
                            $comment_levels = array();
                            //trace($object_name . ': ' . $object_id, '<hr />' . __LINE__ . 'parent: ' . $MA_doc->get('id'));
                            if (in_array($object_doc->get('type'), $params['settings']['doc_types_level_two'])) {
                                foreach ($object_vars['aggregated_level_name_group']['value'] as $idx => $val) {
                                    $comment_levels[] = sprintf(
                                        $comment_level_template,
                                        $object_vars['aggregated_market_approach']['value'][$idx],
                                        '',
                                        $val
                                    );
                                }
                            } else {
                                foreach ($object_vars['level_name_appr']['value'] as $idx => $val) {
                                    $comment_levels[] = sprintf(
                                        $comment_level_template,
                                        $object_vars['object_market']['value'][$idx],
                                        $object_vars['perfect_market']['value'][$idx],
                                        $val
                                    );
                                }
                            }
                            $ext = new Extender();
                            $ext->add('a_comment_method_text', $method_text);
                            $ext->add('a_comment_object_name', $object_name);
                            $ext->add('a_comment_object_levels', implode(', ', $comment_levels));
                            $ext->add('a_comment_land', $object_vars['total_land_meters']['value']);

                            //trace($object_vars['total_land_meters']['value'], __LINE__ . ': total_land_meters');
                            $comments_data = array($ext->expand($comments_template));
                        }

                        $current_template = self::_replaceDocumentXMLSnippet('MARKET_APPROACH_COMMENTS', $comments_data, $current_template);
                    }

                    $current_template = self::_handleIfData($current_template, $extender);
                    //replace placeholders
                    $MA_page = new stdClass();
                    $MA_page->xml = self::_addAppendixEnvelop(
                        $extender->expand($current_template),
                        $extender->placeholders
                    );
                    $MA_page->isLandscape = $ma_comparative_table->grid_cols_count > self::$settings['comparative_tables_landscape_cols'];
                    $MA_data[] = $MA_page;
                }
                $model->extender->add('market_approach_analogues', $market_approach_analogues);
            }

            self::_replaceDocumentXMLSnippet($tag, self::_placeSectionsInXml($MA_data));
            unset(
                $MA_page,
                $MA_data,
                $market_approach_analogues,
                $ma_comparative_table,
                $extender,
                $current_template,
                $comment_level_template,
                $object_vars,
                $comments_data,
                $ext
            );
        }

        $tag = 'REVENUE_APPROACH';
        //get the snippet from the document XML
        $RA_template = self::_getDocumentXMLSnippet($tag);
        if (!empty($RA_template)) {
            $RA_data = array();
            $RA_doc_ids = array();
            if (!empty($related_records[DOCUMENTS_TYPE_REVENUE_APPROACH1])) {
                $RA_doc_ids = $related_records[DOCUMENTS_TYPE_REVENUE_APPROACH1];
            } elseif (!empty($related_records[DOCUMENTS_TYPE_REVENUE_APPROACH2])) {
                $RA_doc_ids = $related_records[DOCUMENTS_TYPE_REVENUE_APPROACH2];
            }
            if (!empty($RA_doc_ids)) {
                $RA_doc_id = reset($RA_doc_ids);

                //fetch the document
                $RA_doc = Documents::searchOne($registry, array(
                    'where' => array(
                        'd.id  = ' . $RA_doc_id,
                    ),
                    'sanitize' => false,
                ));

                if ($RA_doc) {
                    $RA_vars = self::_decorateVars($RA_doc->getVarsForTemplateAssoc());
                    $extender = new Extender();
                    $extender->merge(self::$model->extender->placeholders);

                    $iac_names = array_flip($RA_vars['income_approach_calc_group_table']['names']);
                    if (isset($RA_vars['income_approach_calc_group_table']['values'])) {
                        $ra_objectNames = [];
                        foreach ($RA_vars['income_approach_calc_group_table']['values'] as $v) {
                            if (isset($assessmentObjects[$v[$iac_names['object_calc_name_id']]])) {
                                $ao = $assessmentObjects[$v[$iac_names['object_calc_name_id']]];
                                $ra_objectVars = $ao->get('assoc_vars');
                                $ra_objectNames[] = trim(isset($ra_objectVars['object_name']) ? ($ra_objectVars['object_name']['value'] ?? '') : ($ra_objectVars['property_num']['value'] ?? ''));
                            }
                        }
                        $object_name = implode(', ', $ra_objectNames);
                    } else {
                        $object_name = '';
                    }
                    $extender->add('object_name', $object_name);

                    //add placeholders to replace them in the snippet
                    $pattern_vars = $RA_doc->getPatternsVars();
                    $pattern_vars['a_income_approach_calc_group_table'] = self::_prepareRevenueApproachTable1And2(
                        $RA_vars,
                        $RA_doc->get('type') == DOCUMENTS_TYPE_REVENUE_APPROACH1
                    );

                    if (!empty($related_records[DOCUMENTS_TYPE_REVENUE_APPROACH_ANALOGUES])) {
                        //fetch the documents
                        $revenue_analogue_doc_ids = implode(',', $related_records[DOCUMENTS_TYPE_REVENUE_APPROACH_ANALOGUES]);
                        $RAD_docs = Documents::search(self::$registry, array(
                            'where' => array(
                                "d.id IN ({$revenue_analogue_doc_ids})",
                            ),
                            'sort' => array(
                                "find_in_set(d.id, '{$revenue_analogue_doc_ids}')",
                            ),
                            'sanitize' => false,
                        ));

                        $revenue_approach_analogues = '';
                        $pattern_vars['revenue_approach_comparative_tables'] = '';
                        if ($RAD_docs) {
                            $compTablePages = [];
                            foreach ($RAD_docs as $v) {
                                $analogs = self::_getAnalogsFromCompTable($v);
                                if (isset($v->get('assoc_vars')['group_analogue']['values']) && count($v->get('assoc_vars')['group_analogue']['values']) > 1) {
                                    $names_idx = array_flip($v->get('assoc_vars')['group_analogue']['names']);
                                    $objectName = $v->get('assoc_vars')['group_analogue']['values'][1][$names_idx['comparison_element']];

                                    $analogRefTitle = sprintf(self::i18n('analog_ref_revenueApproach'), $objectName);
                                    $revenue_approach_analogues .= self::_prepareAnalogsTable($analogRefTitle, $analogs);

                                    $compTablePages[] = self::_prepareRevenueApproachTable4($v, $isSupervision);
                                }
                            }
                            $pattern_vars['revenue_approach_comparative_tables'] = self::_placeSectionsInXml($compTablePages);
                        }

                        $model->extender->add('revenue_approach_analogues', $revenue_approach_analogues);
                    }
                    $extender->merge($pattern_vars);

                    //sub tables for revenue approach: market (advance), bank, other
                    $RAM_data = $RAB_data = $RAO_data = '';
                    switch ($RA_vars['first_setting']['value']) {
                        case 1:
                            //market
                            $RAM_template = self::_getDocumentXMLSnippet('REVENUE_APPROACH_MARKET', $RA_template);
                            //get coefficients directly from the DB
                            $query = 'SELECT nc.model_id, nc.value' . "\n" .
                                'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc' . "\n" .
                                'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                                ' ON nc.var_id=fm.id AND fm.name="coef_value" AND model="Nomenclature"' . "\n" .
                                'WHERE nc.model_id IN ('
                                . $params['settings']['nom_coefficients_hard_values_real_estate'] . ', '
                                . $params['settings']['nom_coefficients_hard_values_offices'] . ', '
                                . $params['settings']['nom_coefficients_hard_values_stores'] . ', '
                                . $params['settings']['nom_coefficients_hard_values_industrial'] . ')';
                            $coefficients = $db->GetAssoc($query);
                            $extender->add('a_offices_coef', $coefficients[$params['settings']['nom_coefficients_hard_values_offices']] ?? '');
                            $extender->add('a_stores_coef', $coefficients[$params['settings']['nom_coefficients_hard_values_stores']] ?? '');
                            $extender->add('a_industrial_coef', $coefficients[$params['settings']['nom_coefficients_hard_values_industrial']] ?? '');
                            $extender->add('a_real_estate_coef', $coefficients[$params['settings']['nom_coefficients_hard_values_real_estate']] ?? '');

                            $capital_rates_table = '';
                            if (!empty($related_records[DOCUMENTS_TYPE_REVENUE_APPROACH_MARKET_NORMS])) {
                                $capital_rates_table = self::_prepareRevenueApproachTable3($related_records[DOCUMENTS_TYPE_REVENUE_APPROACH_MARKET_NORMS]);
                            }
                            $extender->add('a_capital_rates_table', $capital_rates_table);
                            $RAM_template = self::_handleIfData($RAM_template, $extender);
                            $RAM_data = $extender->expand($RAM_template);
                            break;
                        case 2:
                            //bank
                            break;
                        case 3:
                            //other
                            break;
                    }
                    $RA_template = self::_replaceDocumentXMLSnippet('REVENUE_APPROACH_MARKET', $RAM_data, $RA_template);
                    $RA_template = self::_replaceDocumentXMLSnippet('REVENUE_APPROACH_BANK', $RAB_data, $RA_template);
                    $RA_template = self::_replaceDocumentXMLSnippet('REVENUE_APPROACH_OTHER', $RAO_data, $RA_template);

                    //comments
                    $comments_template = self::_getDocumentXMLSnippet('REVENUE_APPROACH_COMMENTS', $RA_template);
                    if ($comments_template) {
                        $comments_data = array();
                        //get the objects/properties from the income_approach_calc_group_table table - object_calc_name_id
                        $object_names_idx = array_flip($RA_vars['income_approach_calc_group_table']['names']);
                        foreach (($RA_vars['income_approach_calc_group_table']['values'] ?? []) as $iacgt_row) {
                            $object_id = $iacgt_row[$object_names_idx['object_calc_name_id']];

                            $method_nom_id = '';
                            $method_text = '';
                            $object_group_table_names = array_flip($vars['object_group_table']['names']);
                            foreach (($vars['object_group_table']['values'] ?? []) as $ridx => $ogt_row) {
                                foreach (array('three', 'two', 'one') as $f) {
                                    if ($ogt_row[$object_group_table_names['level_' . $f . '_element_id']] == $object_id) {
                                        $method_nom_id = $ogt_row[$object_group_table_names['revenue_approach_id']];
                                        break 2;
                                    }
                                }
                            }
                            if ($standard_nom && $method_nom_id) {
                                $stnidx = array_search($method_nom_id, $standard_nom_vars['method_id']['value']);
                                $method_text = $standard_nom_vars['method_text']['value'][$stnidx];
                            }
                            $object_doc = Documents::searchOne($registry, array(
                                'where' => array(
                                    'd.id = ' . $object_id,
                                )
                            ));
                            if (!is_object($object_doc)) {
                                continue;
                            }
                            $object_vars = $object_doc->getAssocVars();
                            $object_name = '';
                            if (isset($object_vars['object_name'])) {
                                $object_name = $object_vars['object_name']['value'];
                            } elseif (isset($object_vars['property_num'])) {
                                $object_name = $object_vars['property_num']['value'];
                            }
                            if (isset($object_vars['perfect_income']) && count(array_filter($object_vars['perfect_income']['value'])) > 0) {
                                //with ОЧС
                                $comment_level_template = self::i18n('comment_level_template1');
                            } else {
                                //without ОЧС
                                $comment_level_template = self::i18n('comment_level_template2');
                            }
                            $comment_levels = array();
                            //trace($object_name . ': ' . $object_id, '<hr />' . __LINE__ . 'parent: ' . $RA_doc->get('id'));
                            if (in_array($object_doc->get('type'), $params['settings']['doc_types_level_two'])) {
                                foreach ($object_vars['aggregated_level_name_group']['value'] as $idx => $val) {
                                    $comment_levels[] = sprintf(
                                        $comment_level_template,
                                        $object_vars['aggregated_income_approach']['value'][$idx],
                                        '',
                                        $val
                                    );
                                }
                            } else {
                                foreach ($object_vars['level_name_appr']['value'] as $idx => $val) {
                                    $comment_levels[] = sprintf(
                                        $comment_level_template,
                                        $object_vars['object_income']['value'][$idx],
                                        $object_vars['perfect_income']['value'][$idx],
                                        $val
                                    );
                                }
                            }
                            $ext = new Extender();
                            $ext->add('a_comment_method_text', $method_text);
                            $ext->add('a_comment_object_name', $object_name);
                            $ext->add('a_comment_object_levels', implode(', ', $comment_levels));
                            $ext->add('a_comment_land', $object_vars['total_land_meters']['value']);
                            //trace($object_vars['total_land_meters']['value'], __LINE__ . ': total land meters');
                            $comments_template = self::_handleIfData($comments_template, $ext);
                            $comments_data[] = $ext->expand($comments_template);
                        }
                        $RA_template = self::_manageAlvisHyperlinks($RA_template, $isSupervision, $extender);
                        $RA_template = self::_replaceDocumentXMLSnippet('REVENUE_APPROACH_COMMENTS', $comments_data, $RA_template);
                    }

                    //replace placeholders
                    //IMPORTANT: get the snippet again as it has been replaced with subelements already
                    //$RA_data[] = $extender->expand(self::_getDocumentXMLSnippet($tag));
                    $RA_data[] = $extender->expand($RA_template);
                }
            }
            self::_replaceDocumentXMLSnippet($tag, $RA_data);
        }

        $buildings = array();
        if (!empty($buildings_ids)) {
            $building_docs = Documents::search($registry, array(
                'where' => array(
                    'd.id IN (' . implode(',', $buildings_ids) . ')',
                )
            ));

            if (!empty($building_docs)) {
                foreach ($building_docs as $k => $v) {
                    $v->unsanitize();
                    $v->getAssocVars();
                    $buildings[$v->get('id')] = $v;
                }
            }
        }

        $tag = 'COSTLY_APPROACH_COMPARATIVE_TABLES_DATA';
        $costly_approach_comparative_tables_data_xml = self::_getDocumentXMLSnippet($tag);
        $costly_approach_comparative_tables_data_xml_replacement = '';
        if (!empty($related_records[DOCUMENTS_TYPE_COSTLY_APPROACH_ANALOGUES])) {
            $costAproach_compTables_ids = implode(', ', $related_records[DOCUMENTS_TYPE_COSTLY_APPROACH_ANALOGUES]);
            $CAD_docs = Documents::search(self::$registry, array(
                'where' => array(
                    "d.id IN ({$costAproach_compTables_ids})",
                ),
                'sort' => array(
                    "find_in_set(d.id, '{$costAproach_compTables_ids}')",
                ),
                'sanitize' => false,
            ));
            if ($CAD_docs) {
                $costly_approach_analogues = '';
                $costly_approach_comparative_tables = '';
                $compTablePages = [];
                foreach ($CAD_docs as $v) {
                    $analogs = self::_getAnalogsFromCompTable($v);
                    if (isset($v->get('assoc_vars')['group_analogue']['values']) && count($v->get('assoc_vars')['group_analogue']['values']) > 1) {
                        $names_idx = array_flip($v->get('assoc_vars')['group_analogue']['names']);
                        $objectName = $v->get('assoc_vars')['group_analogue']['values'][1][$names_idx['comparison_element']];

                        $analogRefTitle = sprintf(self::i18n('analog_ref_costApproach'), $objectName);
                        $costly_approach_analogues .= self::_prepareAnalogsTable($analogRefTitle, $analogs);

                        $compTablePages[] = self::_prepareCostlyApproachTable3($v, $isSupervision);
                    }
                }

                $costly_approach_comparative_tables = self::_placeSectionsInXml($compTablePages);

                $model->extender->add('costly_approach_analogues', $costly_approach_analogues);

                if ($costly_approach_comparative_tables !== '') {
                    $extender = new Extender();
                    $extender->add(
                        'costly_approach_comparative_tables',
                        $costly_approach_comparative_tables
                    );
                    $costly_approach_comparative_tables_data_xml_replacement = $extender->expand($costly_approach_comparative_tables_data_xml);
                }
            }
        }
        self::_replaceDocumentXMLSnippet($tag, $costly_approach_comparative_tables_data_xml_replacement);

        $tag = 'COSTLY_APPROACH';
        //get the snippet from the document XML
        $CA_template = self::_getDocumentXMLSnippet($tag);
        if (!empty($CA_template)) {
            $CA_data = array();
            $CA_doc_ids = array();
            if (!empty($related_records[DOCUMENTS_TYPE_COSTLY_APPROACH])) {
                $CA_doc_ids = $related_records[DOCUMENTS_TYPE_COSTLY_APPROACH];
            }
            if (!empty($CA_doc_ids)) {
                $CA_doc_id = reset($CA_doc_ids);

                //fetch the document
                $CA_doc = Documents::searchOne($registry, array(
                    'where' => array(
                        'd.id  = ' . $CA_doc_id,
                    ),
                    'sanitize' => false,
                ));

                if ($CA_doc) {
                    $CA_vars = self::_decorateVars($CA_doc->getVarsForTemplateAssoc());
                    $landDocs = self::getLandDocsByObjectId($CA_vars, $assessmentObjects, $params['settings']);
                    $CA_table1 = self::_prepareCostlyApproachTable1($CA_vars, $landDocs);

                    if (!empty($CA_table1)) {
                        //if the first table is empty, no need to include the document
                        $extender = new Extender();
                        $extender->merge(self::$model->extender->placeholders);
                        //add placeholders to replace them in the snippet
                        $pattern_vars = $CA_doc->getPatternsVars();
                        $pattern_vars['a_rated_object_group'] = $CA_table1;
                        // $pattern_vars['a_object_summary'] = self::_prepareCostlyApproachObjectSummaryTable($CA_vars);
                        $pattern_vars['a_object_summary'] = self::_prepareCostlyApproachObjectSummaryTable($CA_vars);
                        $pattern_vars['a_object_improvement_grp'] = self::_prepareCostlyApproachTable2($CA_vars);
                        $extender->merge($pattern_vars);

                        $ro_names = array_flip($CA_vars['rated_object_group']['names']);
                        $object_name = implode(', ', array_column($CA_vars['rated_object_group']['values'], $ro_names['rated_object_name']));
                        $extender->add('object_name', $object_name);

                        //comments
                        $comments_template_general = self::_getDocumentXMLSnippet('COSTLY_APPROACH_COMMENTS');
                        if ($comments_template_general) {
                            $comments_data = array();
                            $usedBuildings = array();

                            //get the objects/properties from the income_approach_calc_group_table table - object_calc_name_id
                            $object_names_idx = array_flip($CA_vars['rated_object_group']['names']);
                            foreach (($CA_vars['rated_object_group']['values'] ?? []) as $rog_row) {
                                $comments_template = $comments_template_general;
                                $object_id = $rog_row[$object_names_idx['rated_object_name_id']];
                                $object_doc = Documents::searchOne($registry, array(
                                    'where' => array(
                                        'd.id = ' . $object_id,
                                    )
                                ));
                                if (!is_object($object_doc)) {
                                    continue;
                                }
                                $object_vars = self::_decorateVars($object_doc->getAssocVars());
                                $object_name = '';
                                if (isset($object_vars['object_name'])) {
                                    $object_name = $object_vars['object_name']['value'];
                                } elseif (isset($object_vars['property_num'])) {
                                    $object_name = $object_vars['property_num']['value'];
                                }

                                //FIRST COMMENT
                                $method_nom_id = '';
                                $method_text = '';
                                $object_group_table_names = array_flip($vars['object_group_table']['names']);
                                foreach (($vars['object_group_table']['values'] ?? []) as $ridx => $ogt_row) {
                                    foreach (array('three', 'two', 'one') as $f) {
                                        if ($ogt_row[$object_group_table_names['level_' . $f . '_element_id']] == $object_id) {
                                            $method_nom_id = $ogt_row[$object_group_table_names['costly_approach_id']];
                                            break 2;
                                        }
                                    }
                                }
                                if ($standard_nom && $method_nom_id) {
                                    $stnidx = array_search($method_nom_id, $standard_nom_vars['method_id']['value']);
                                    $method_text = $stnidx === false ? '' : $standard_nom_vars['method_text']['value'][$stnidx];
                                }
                                if (isset($object_vars['perfect_expense']) && count(array_filter($object_vars['perfect_expense']['value'])) > 0) {
                                    //with ОЧС
                                    $comment_level_template = self::i18n('comment_level_template1');
                                } else {
                                    //without ОЧС
                                    $comment_level_template = self::i18n('comment_level_template2');
                                }
                                $comment_levels = array();
                                //trace($object_name . ': ' . $object_id, '<hr />' . __LINE__ . 'parent: ' . $CA_doc->get('id'));
                                if (in_array($object_doc->get('type'), $params['settings']['doc_types_level_two'])) {
                                    foreach ($object_vars['aggregated_level_name_group']['value'] as $idx => $val) {
                                        $comment_levels[] = sprintf(
                                            $comment_level_template,
                                            $object_vars['aggregated_spending_approach']['value'][$idx],
                                            '',
                                            $val
                                        );
                                    }
                                } else {
                                    foreach ($object_vars['level_name_appr']['value'] as $idx => $val) {
                                        $comment_levels[] = sprintf(
                                            $comment_level_template,
                                            $object_vars['object_expense']['value'][$idx],
                                            $object_vars['perfect_expense']['value'][$idx],
                                            $val
                                        );
                                    }
                                }
                                $hierarchy_document_relat_grp_values = $vars['hierarchy_document_relat_grp']['values'] ?? [];
                                $first_row_hierarchy = reset($hierarchy_document_relat_grp_values);

                                if (in_array($first_row_hierarchy[$hierarchy_group_idx['hierarchy_document_relat_type']], array($params['settings']['doc_type_upi_empty'], $params['settings']['doc_type_pi_building']))) {
                                    //with UPI
                                    $comment_land_template = self::i18n('comment_level_template3');
                                } else {
                                    //without OPS
                                    $comment_land_template = self::i18n('comment_level_template4');
                                    $opsId = $first_row_hierarchy[$hierarchy_group_idx['hierarchy_document_relat_id']];
                                }

                                //second comment
                                //get the building
                                $SEK_number = '';
                                $SEK_name = '';
                                $assessment_category = '';
                                $standard_price_eur = '';
                                $additional_costs_percent = '';
                                $total_suitability = '';
                                $amortization_type = '';
                                if (in_array($object_doc->get('type'), $params['settings']['doc_types_level_two'])) {
                                    //the object IS BUILDING
                                    $building_vars = $object_vars;
                                    $building_id = $object_id;
                                } else {
                                    //get the parent building from document type "Report (1)"
                                    $building_id = 0;
                                    $building_vars = array();
                                    $building_id = self::getObjectParent($object_id);
                                    if ($building_id) {
                                        $building_vars = self::_decorateVars($buildings[$building_id]->getAssocVars());
                                    }
                                }
                                if (!empty($building_vars)) {
                                    if (in_array($building_id, $usedBuildings)) {
                                        continue;
                                    }
                                    $usedBuildings[] = $building_id;

                                    $SEK_nom_id = $building_vars['standard_SEK']['value'];
                                    if ($SEK_nom_id) {
                                        //get the nomenclature var `sek_number` value
                                        //get standard number directly from the database
                                        $query = 'SELECT nc.value' . "\n" .
                                            'FROM ' . DB_TABLE_NOMENCLATURES_CSTM . ' as nc' . "\n" .
                                            'JOIN ' . DB_TABLE_FIELDS_META . ' as fm' . "\n" .
                                            ' ON nc.var_id=fm.id AND fm.name="sek_number" AND model="Nomenclature"' . "\n" .
                                            'WHERE nc.model_id = ' . $SEK_nom_id;
                                        $SEK_number = $db->GetOne($query);
                                    }
                                    foreach ($building_vars['standard_SEK']['options'] as $option) {
                                        if ($building_vars['standard_SEK']['value'] == $option['option_value']) {
                                            $SEK_name = $option['label'];
                                            break;
                                        }
                                    }
                                    foreach ($building_vars['assessment_category']['options'] as $option) {
                                        if ($building_vars['assessment_category']['value'] == $option['option_value']) {
                                            $assessment_category = $option['label'];
                                            break;
                                        }
                                    }
                                    foreach ($building_vars['sek_version']['options'] as $option) {
                                        if ($building_vars['sek_version']['value'] == $option['option_value']) {
                                            $sek_version = $option['label'];
                                            break;
                                        }
                                    }

                                    $standard_price_eur = $building_vars['sek_expen_value_eur']['value'];
                                    $additional_costs_percent = $building_vars['additional_costs_percent']['value'];
                                    $total_suitability = $building_vars['total_suitability']['value'];
                                    $amortization_type = '';
                                    foreach ($building_vars['amortization_type']['options'] as $option) {
                                        if ($building_vars['amortization_type']['value'] == $option['option_value']) {
                                            $amortization_type = $option['label'];
                                            break;
                                        }
                                    }
                                }

                                $oneCalcConstructionNames = array_flip($CA_vars['one_calc_construction_right_grp']['names']);
                                $oneCalcConstructionValues = $CA_vars['one_calc_construction_right_grp']['values'] ?? [];
                                $oneCalcConstructionComment = $oneCalcConstructionValues[1][$oneCalcConstructionNames['one_calc_construction_right_build']] ?? [];
                                $twoCalcConstructionNames = array_flip($CA_vars['two_calc_construction_right_grp']['names']);
                                $twoCalcConstructionValues = $CA_vars['two_calc_construction_right_grp']['values'] ?? [];
                                $twoCalcConstructionComment = $twoCalcConstructionValues[1][$twoCalcConstructionNames['two_calc_construction_right_build']] ?? [];
                                $calcConstructionRightValue = isset($opsId)
                                    ? $assessmentObjects[$opsId]->getAssocVars()['calc_construction_right']['value']
                                    : 0;

                                $ext = new Extender();
                                //first comment
                                $ext->add('a_comment_method_text', $method_text);
                                $ext->add('a_comment_object_name', $object_name);
                                $ext->add('a_comment_building_name', $building_vars['object_name']['value'] ?? '');
                                $ext->add('a_comment_object_levels', implode(', ', $comment_levels));
                                $ext->add('a_comment_land_text', sprintf($comment_land_template, $object_vars['total_land_meters']['value']));
                                //trace($object_vars['total_land_meters']['value'], __LINE__ . ': total land meters');
                                //second comment
                                $ext->add('a_comment_standard_number', $SEK_number);
                                $ext->add('a_comment_standard_name', $SEK_name);
                                $ext->add('a_comment_sek_version', $sek_version ?? '');
                                $ext->add('a_comment_assessment_category', $assessment_category);
                                $ext->add('a_comment_standard_price_eur', $standard_price_eur);
                                $ext->add('a_comment_additional_costs_percent', $additional_costs_percent);
                                $ext->add('a_comment_total_suitability', $total_suitability);
                                $ext->add('a_comment_amortization_type', $amortization_type);
                                if ($calcConstructionRightValue == 1) {
                                    $ext->add('a_comment_one_calc_construction_right_build', $oneCalcConstructionComment);
                                }
                                if ($calcConstructionRightValue == 2) {
                                    $ext->add('a_comment_two_calc_construction_right_build', $twoCalcConstructionComment);
                                }
                                $comments_template = self::_handleIfData($comments_template, $ext);
                                $comments_data[] = $ext->expand($comments_template);
                            }
                            $CA_template = self::_replaceDocumentXMLSnippet('COSTLY_APPROACH_COMMENTS', $comments_data, $CA_template);
                        }

                        $CA_template = self::_handleIfData($CA_template, $extender);
                        //replace placeholders
                        $CA_data[] = $extender->expand($CA_template);
                    }
                }
            }
            self::_replaceDocumentXMLSnippet($tag, $CA_data);
        }

        $tag = 'SEK';
        //get the snippet from the document XML
        $SEK_template = self::_getDocumentXMLSnippet($tag);
        if (!empty($SEK_template)) {
            $SEK_data = array();
            $SEK_object_names = [];
            if (!empty($buildings)) {
                foreach ($buildings as $building_id => $building_doc) {
                    $building_vars = self::_decorateVars($building_doc->getVarsForTemplateAssoc(true));
                    $extender = new Extender();

                    $SEK_object_names[] = $building_vars['object_name']['value'];
                    $extender->add('a_building_name', $building_vars['object_name']['value']);

                    $secTable = self::_prepareSEKTable($building_vars);
                    // If the $secTable this means no sec table in serailized data, so just continue with the next building
                    if ($secTable == '') {
                        continue;
                    }
                    $extender->add('SEK_table', $secTable);

                    foreach ($building_vars['sek_version']['options'] as $option) {
                        if ($building_vars['sek_version']['value'] == $option['option_value']) {
                            $sek_version = $option['label'];
                            break;
                        }
                    }
                    $extender->add('sek_version', $sek_version ?? '');

                    //only add this snippet specific variables for hyperlinks
                    //the global variable (document_id, alvis_url) should be replaced separately
                    $extender->add('object_id', $building_doc->get('id'));

                    $SEK_template_replaced = self::_manageAlvisHyperlinks($SEK_template, $isSupervision, $extender);

                    //replace placeholders
                    $SEK_data[] = $extender->expand($SEK_template_replaced);
                }
            }
            self::_replaceDocumentXMLSnippet($tag, $SEK_data);

            $tag = 'SEK_HEADER';
            //get the snippet from the document XML
            $SEKH_template = self::_getDocumentXMLSnippet($tag);
            $SEKH_data = '';
            if (!empty($SEK_data)) {
                $model->extender->add('SEK_object_names', implode(', ', $SEK_object_names));
                $SEKH_data = $model->extender->expand($SEKH_template);
            }
            self::_replaceDocumentXMLSnippet($tag, $SEKH_data);

            $tag = 'SEK_FOOTER';
            //get the snippet from the document XML
            $SEKF_template = self::_getDocumentXMLSnippet($tag);
            $SEKF_data = '';
            if (!empty($SEK_data)) {
                $SEKF_data = $model->extender->expand($SEKF_template);
            }
            self::_replaceDocumentXMLSnippet($tag, $SEKF_data);
        }

        // [assessment_objects_names]
        $object_group_table = $model->get('assoc_vars')['object_group_table'];
        $og_names = array_flip($object_group_table['names']);
        $assessment_objects_names_arr = [];
        foreach ($object_group_table['values'] ?? [] as $v) {
            $ass_id = empty($v[$og_names['level_three_element_id']])
                ? (empty($v[$og_names['level_two_element_id']]) ? $v[$og_names['level_one_element_id']] : $v[$og_names['level_two_element_id']])
                : $v[$og_names['level_three_element_id']];
            if (isset($assessmentObjects[$ass_id])) {
                $assObjectVars = $assessmentObjects[$ass_id]->getAssocVars();
                $assessment_objects_names_arr[] = trim(isset($assObjectVars['object_name']) ? ($assObjectVars['object_name']['value'] ?? '') : ($assObjectVars['property_num']['value'] ?? ''));
            }
        }
        $model->extender->add('assessment_objects_names', implode(', ', $assessment_objects_names_arr));

        //replace currency placeholders
        $model->extender->add('осн валута', $main_currency);
        $model->extender->add('алт валута', $alt_currency);

        $ops_land = (self::$isOps ? 'Право на строеж' : 'Земя');
        $model->extender->add('ops_land', $ops_land);

        //get the file revision number
        $revision = Files::getLatestRevision(
            $registry,
            array(
                'model' => $model->modelName,
                'model_id' => $model->get('id'),
                'pattern_id' => $pattern->get('id'),
                'origin' => 'generated'
            )
        );
        $model->extender->add('revision', $revision);

        //get the bank abbreviation
        $model->extender->add('a_bank_code', $bank->getVarValue('abbreviation'));

        // Tags: [start_RESUME_UNICREDIT] ... [end_RESUME_UNICREDIT] and [start_RESUME_NOT_UNICREDIT] ... [end_RESUME_NOT_UNICREDIT]
        $resume_unicredit_xml = $resume_ubb_xml = $resume_notUcbb_xml = '';
        if ($bank_id == self::$settings['customer_unicredit_id']) {
            $resume_unicredit_table_xml = self::ucbbFetchXml(
                $buildings_ids,
                $hierarchy_records_sorted,
                $upis_ids,
                $hierarchy_parents,
                $object_group_table,
                $params['settings'],
                $results_doc,
                $results_vars,
                $main_currency,
                $alt_currency,
                $assessmentObjects
            );
            $energyTbmlXml = self::prepareEnergyTableXml($buildings_ids, $assessmentObjects);

            // Show tag for Unicredit
            $resume_unicredit_xml = self::_getDocumentXMLSnippet('RESUME_UNICREDIT');
            // Hide Unicredit resume table description
            if ($has_OPS) {
                $resume_unicredit_xml = self::_replaceDocumentXMLSnippet(
                    'RESUME_UNICREDIT_TABLE_DESCRIPTION',
                    '',
                    $resume_unicredit_xml
                );
            }
            $extender = new Extender();
            $ucbbExtender = new Extender();
            $extender->add('resume_unicredit_table', $resume_unicredit_table_xml);
            $ucbbExtender->add('greenbuilding_unicredit', $energyTbmlXml);
            $ucbbExtender->add('resume_unicredit_table', $resume_unicredit_table_xml);
            $resume_unicredit_xml = $extender->expand($resume_unicredit_xml);

            foreach (self::$disasterFields as $field) {
                $tag = 'a_' . $field;
                $level = trim($vars[$field]['value']);
                $levelMessage = trim(self::$riskLevelMessage[$vars[$field]['value']] ?? '');
                $replacement = trim($level . '. ' . $levelMessage) !== '.' ? $level . '. ' . $levelMessage : '';
                $ucbbExtender->add($tag, $replacement);
            }

            $ifUnicredit = self::_getDocumentXMLSnippet('IFUNICREDIT');
            $ifUnicredit = $ucbbExtender->expand($ifUnicredit, false);
            unset($ucbbExtender);
        } elseif ($bank_id == self::$settings['customer_ubb_id']) {
            $resume_ubb_table_xml = self::ubbFetchXml(
                $buildings_ids,
                $model->get('model_lang'),
                $hierarchy_records_sorted,
                $db,
                $upis_ids,
                $hierarchy_parents,
                $params['settings'],
                $results_doc,
                $results_vars,
                $main_currency,
                $alt_currency,
                $UPI_docs,
                $has_OPS,
                $buildings,
                $assessmentObjects,
                $analytics_doc
            );

            // Show tag for UBB
            $resume_ubb_xml = self::_getDocumentXMLSnippet('RESUME_UBB');

            // Hide Unicredit resume table description
            if ($has_OPS) {
                $resume_ubb_xml = self::_replaceDocumentXMLSnippet('RESUME_UBB_TABLE_DESCRIPTION', '', $resume_ubb_xml);
            }
            $extender = new Extender();
            // Add First building placeholders in UBB_RESUME. Green building etc.
            if (isset($firsBuildingExtender)) {
                $extender->merge($firsBuildingExtender->placeholders);
            }
            // Add Report placeholders in UBB_RESUME. Collateral, legal_compliance etc.
            $extender->merge($model->extender->placeholders);
            $extender->add('resume_ubb_table', $resume_ubb_table_xml);

            // Replace the specific UBB placeholders in UBB_RESUME.
            $resume_ubb_xml = $extender->expand($resume_ubb_xml);

            $ifUbb = self::_getDocumentXMLSnippet('IFUBB');
        } else {
            $resume_notUcbb_xml = self::_getDocumentXMLSnippet('RESUME_NOT_UNICREDIT');
        }

        self::_replaceDocumentXMLSnippet('RESUME_NOT_UNICREDIT', $resume_notUcbb_xml);
        self::_replaceDocumentXMLSnippet('RESUME_UNICREDIT', $resume_unicredit_xml);
        self::_replaceDocumentXMLSnippet('RESUME_UBB', $resume_ubb_xml);
        self::_replaceDocumentXMLSnippet('IFUNICREDIT', $ifUnicredit ?? '');
        self::_replaceDocumentXMLSnippet('IFUBB', $ifUbb ?? '');

        $model->extender->add('a_description_natural_risks', $vars['disas_check_desc_hidden']['value']);
        // Tag: [start_SINGLE_BUILDING] ... [end_SINGLE_BUILDING]
        // IMPORTANT: this code should be after the code for the UNICREDIT RESUME tag,
        // because before that it exists into the template twice
        $tag = 'SINGLE_BUILDING';
        $single_building_html = '';
        if (count($buildings_ids) === 1) {
            $single_building = Documents::searchOne($registry, array(
                'where' => array(
                    'd.id = ' . reset($buildings_ids)
                )
            ));
            if (is_object($single_building)) {
                $extender = new Extender();
                $patterns_vars = $single_building->getPatternsVars();
                $object_completed = $single_building->getPlainVarValue('object_completed');
                if ($object_completed === '1' || $object_completed === '2') {
                    $patterns_vars['implement_value_exp'] = '100';
                } else {
                    $patterns_vars['implement_value_exp'] = $patterns_vars['a_implement_value_exp'] ?? 0;
                }
                $extender->merge($patterns_vars);
                $single_building_xml = self::_getDocumentXMLSnippet($tag);
                $single_building_html = $extender->expand($single_building_xml);
            }
        }
        self::_replaceDocumentXMLSnippet($tag, $single_building_html);

        // Placeholder: standard_primary_description
        if (
            $standard_nom
            && isset(
                $standard_nom_vars['primary_standard']['value'],
                $vars['standard_value_check_one']['value'],
                $standard_nom_vars['primary_description']['value']
            )
            && is_array($standard_nom_vars['primary_standard']['value'])
        ) {
            $primary_standard_row = array_search($vars['standard_value_check_one']['value'], $standard_nom_vars['primary_standard']['value']);
            if ($primary_standard_row !== false && array_key_exists($primary_standard_row, $standard_nom_vars['primary_description']['value'])) {
                $model->extender->add('standard_primary_description', $standard_nom_vars['primary_description']['value'][$primary_standard_row]);
            }
        }

        // Placeholder: currency_rounding_out_text
        if (empty($vars['currency_rounding_out']['value'])) {
            $currency_rounding_out_text = self::i18n('currency_rounding_out_text_whole_number');
        // currency_rounding_out_text_whole_number = цяло число
        // currency_rounding_out_text_number_multiple_of = число кратно на
        } elseif ($vars['currency_rounding_out']['value'] > 0) {
            $currency_rounding_out_text = sprintf(
                self::i18n('currency_rounding_out_text_number_multiple_of'),
                '1' . str_repeat('0', $vars['currency_rounding_out']['value'])
            );
        } else {
            $currency_rounding_out_text = '';
        }
        $model->extender->add('currency_rounding_out_text', $currency_rounding_out_text);

        // Placeholder: report_supervisors_names
        if (isset($report0_vars['recording_role_group'], $report0_vars['recording_role_group']['values']) && is_array($report0_vars['recording_role_group']['values'])) {
            $recording_role_group_names = array_flip($report0_vars['recording_role_group']['names']);
            $report_supervisors_names = array();
            foreach ($report0_vars['recording_role_group']['values'] as $recording_role_group_row) {
                if ($recording_role_group_row[$recording_role_group_names['recording_role_type']] == self::$settings['report_role_supervisor_option_value']) {
                    $report_supervisors_names[] = $recording_role_group_row[$recording_role_group_names['recording_role']];
                }
            }
            $report_supervisors_names = implode("\n", $report_supervisors_names);
            $model->extender->add('report_supervisors_names', $report_supervisors_names);
        }

        // Preserve sign placeholders
        $model->extender->add('evaluator_sign', '[evaluator_sign]');
        $model->extender->add('supervisor_sign', '[supervisor_sign]');

        // Replace application number
        self::$templateDocx->_documentXML = preg_replace('/\[application_number\]/', '1', self::$templateDocx->_documentXML, 1);
        self::$templateDocx->_documentXML = preg_replace('/\[application_number\]/', '2', self::$templateDocx->_documentXML, 1);
        self::$templateDocx->_documentXML = preg_replace('/\[application_number\]/', '3', self::$templateDocx->_documentXML, 1);

        self::$templateDocx->_documentXML = self::_handleIfData(self::$templateDocx->_documentXML, $model->extender);

        self::$templateDocx->_documentXML = self::_manageAlvisHyperlinks(self::$templateDocx->_documentXML, $isSupervision, $model->extender);

        //expand with variables
        self::$templateDocx->_documentXML = $model->extender->expand($pattern->get('docx_template')->_documentXML);

        // Only the first page should have no header
        // so only the first section should have tag for different first page
        // https://docs.microsoft.com/en-us/dotnet/api/documentformat.openxml.wordprocessing.titlepage?view=openxml-2.8.1
        $different_first_page_tag = '<w:titlePg/>';
        self::$templateDocx->_documentXML = str_replace($different_first_page_tag, '', self::$templateDocx->_documentXML);
        $section_closing_tag = '</w:sectPr>';
        $first_section_closing_tag_position = strpos(self::$templateDocx->_documentXML, $section_closing_tag);
        if ($first_section_closing_tag_position !== false) {
            self::$templateDocx->_documentXML = substr_replace(self::$templateDocx->_documentXML, $different_first_page_tag . $section_closing_tag, $first_section_closing_tag_position, strlen($section_closing_tag));
        }

        // variables used for supervision hyperlinks
        $model->extender->add('document_id', $model->get('id'));
        $model->extender->add('alvis_url', $alvis_url);

        self::$templateDocx->_relationships = $model->extender->expand(rawurldecode(self::$templateDocx->_relationships));

        // Clear custom tags ([start_PROPERTY], [end_PROPERTY], etc.) artifacts
        self::_removeRepeatingParagraphs();

        // Generate proper sequential numbers in [appendix_number] tags
        self::_appendixSetNumbers();

        // Remove any blank pages that are result of processing
        self::_removeBlankPages();

        return true;
    }

    /**
     * @param array $buildingsIds
     * @param $assessmentObjects
     * @return string
     */
    protected static function prepareEnergyTableXml(array $buildingsIds = [], $assessmentObjects): string
    {
        $energyTblXml = self::getBuildingEnergyTblXml('', [], []);

        if (!empty($buildingsIds)) {
            $energyTblXml = '';
            foreach ($buildingsIds as $v) {
                $energyTblXml .= self::prepBuildingEnergyTblXml($assessmentObjects[$v]);
            }
        }

        return self::inverseWrapXml($energyTblXml);
    }

    /**
     * @return array
     */
    protected static function getModelVars(): array
    {
        if (is_null(self::$vars)) {
            self::$vars = self::_decorateVars(self::$model->getVarsForTemplateAssoc());
            self::$vars['lang'] = self::$model->get('model_lang');
        }

        return self::$vars;
    }

    /**
     * @return array
     */
    protected static function getReport0Vars(): array
    {
        if (is_null(self::$report0Vars)) {
            $vars = self::getModelVars();
            $report0 = Documents::searchOne(self::$registry, array(
                'where' => array('d.id = ' . $vars['created_from_document_id']['value']),
                'model_lang' => $vars['lang']
            ));
            self::$report0Vars = self::_decorateVars($report0->getVarsForTemplateAssoc());
        }
        return self::$report0Vars;
    }

    /**
     * This function handles any [start_ifData] tags in a givven string.
     * In the return string any fragments between start/end_ifData that contain tags,
     * and don't have any data in the $data for them, are removed from the string.
     * So only fragmensts for witch there is data remain.
     *
     * @param string $heystack The XML string to be treated
     * @param array|Extender|Extender $data the data source the tags will be checked against. This can be a simple assoc array or an object of type Extender or Viewer
     * @return string
     *
     */
    private static function _handleIfData(string $heystack, $data): string
    {
        if (is_object($data)) {
            if (is_a($data, 'Extender')) {
                $data = $data->placeholders;
            } elseif (is_a($data, 'Extender')) {
                $data = $data->data;
            }
        }
        $watchdog = 1000;
        do {
            $conditional_tmpl = self::_getDocumentXMLSnippet(
                'ifData',
                $heystack
            );

            if (empty($conditional_tmpl)) {
                break;
            }

            preg_match_all('/\[([a-zA-Z_\-0-9]+)\]/', $conditional_tmpl, $matches);
            $availableData = false;
            foreach ($matches[1] as $k => $v) {
                if (array_key_exists($v, $data) && !empty($data[$v])) {
                    $availableData = true;
                    break;
                }
            }

            $heystack = self::_replaceDocumentXMLSnippet(
                'ifData',
                ($availableData ? $conditional_tmpl : ''),
                $heystack
            );
        } while ($watchdog-- > 0);

        return $heystack;
    }

    /**
     * Searches for and removes any blank sections that are result of procesig
     */
    private static function _removeBlankPages(): void
    {
        $sectPrPos = 0;
        $watchdog = 100;
        while ($sectPrPos !== false) {
            // Any closing sectPr position
            $sectPrPos = strpos(self::$templateDocx->_documentXML, "</w:sectPr>", $sectPrPos + 11);
            if ($sectPrPos === false) {
                break;
            }
            // The next opening sectPr position
            $nextSectPrPos = strpos(self::$templateDocx->_documentXML, "<w:sectPr", $sectPrPos + 11);
            // If there is a next openine sectPr AND the closing sectPr and the next opening sectPr are close together
            // Than the section after the  $sectPrPos is probably empty, e.g. empty page
            if ($nextSectPrPos !== false && $nextSectPrPos - $sectPrPos < 1200) {
                // The opening P after closing sectPr is the begining of the next section
                $nextOpeningP = strpos(self::$templateDocx->_documentXML, "<w:p>", $sectPrPos);
                // If no opening P, something is wrong and we should just leave it alone
                if ($nextOpeningP === false) {
                    break;
                }
                // The position of the closing p tag imediatly after the next sections sectPr + 6 chars (the length of the tag itself)
                // This is efectivly the end of the section
                $nextClosingP = strpos(self::$templateDocx->_documentXML, "</w:p>", $nextSectPrPos) + 6;
                // The sections string length
                $strLen = $nextClosingP - $nextOpeningP;
                // Remove the sections string with start position and the length
                self::$templateDocx->_documentXML = substr_replace(self::$templateDocx->_documentXML, '', $nextOpeningP, $strLen);
            }

            if ($watchdog-- < 0) {
                break;
            }
        }
    }

    /**
     * @param $vars
     * @return stdClass
     */
    private static function _prepareComparativeTable($vars): stdClass
    {
        $table_object = new stdClass();
        $table_object->xml = '';
        $table_object->grid_cols_count = 0;

        if (!empty($vars['serial_data']['value']['comparative_table'])) {
            $table_serialized_object = $vars['serial_data']['value']['comparative_table'];

            /*
             * Custom changes
             */
            $properties = array(
                'keepNext' => true,
                'gridWidths' => array()
            );
            $table_object->grid_cols_count = self::_getSerializedTableGridColsCount($table_serialized_object);
            for ($i = 0; $i < $table_object->grid_cols_count; $i++) {
                if ($table_object->grid_cols_count > self::$settings['comparative_tables_landscape_cols']) {
                    if ($i == 0) {
                        $width = 3310;
                    } else {
                        $width = 2070;
                    }
                } else {
                    if ($i == 0) {
                        $width = 2998;
                    } else {
                        $width = 1948;
                    }
                }
                $properties['gridWidths'][] = $width;
            }

            // Custom formatting
            foreach ($table_serialized_object->rows as $row_index => $row) {
                foreach ($row->cells as $cell_index => $cell) {
                    // If this is the head row (i.e. the first row)
                    if ($row_index === 0) {
                        if ($cell_index > 1) {
                            if (!isset($analogue_number)) {
                                $analogue_number = 0;
                            }
                            $analogue_number++;
                            $cell->text = sprintf(
                                self::i18n('pattern_plugin_advanceaddress_preparereport_analogue'),
                                $analogue_number
                            );
                        }
                    } else {
                        // Remove default formatting
                        unset(
                            $row->backgroundColor,
                            $cell->backgroundColor,
                            $cell->bold,
                            $cell->hAlign
                        );

                        // Main formatting for the first cells
                        if ($cell_index === 0) {
                            // Remove indexes
                            $cell->text = self::_removeLabelsIndexes([$cell->text])[0];
                            // Align left
                            $cell->hAlign = 'left';
                        }

                        if (property_exists($row->cells[2], 'name')) {
                            switch ($row->cells[2]->name) {
                                case 'date_analogue':
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'center';
                                    }
                                    break;
                                case 'property_area':
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'center';
                                        $cell->bold = true;
                                    }
                                    break;
                                case 'price_analog_eur':
                                    $cell->bold = true;
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'right';
                                    }
                                    break;
                                case 'price_analog_eur_sqm':
                                    $cell->bold = true;
                                    $cell->backgroundColor = COLOR_DARK_GREEN;
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'right';
                                    }
                                    break;
                                case 'param_analog1':
                                case 'fparam_analog1':
                                    $cell->bold = true;
                                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'center';
                                    }
                                    break;
                                case 'param_analog1_corr':
                                case 'fparam_analog1_corr':
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'center';
                                    }
                                    break;
                                case 'correction_analogue_price_eur_sqm':
                                    $cell->bold = true;
                                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'right';
                                    }
                                    break;
                                case 'correction_absolute_price':
                                case 'correction_percentage':
                                case 'correction_absolute_value':
                                case 'correction_analogue_weight':
                                    if ($cell_index > 0) {
                                        $cell->hAlign = 'center';
                                    }
                                    break;
                            }
                        }
                    }
                }
            }
            $compTbl_xml = self::_convertSerializedTableToXML($table_serialized_object, $properties);

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/comparative_table.xml');
            $viewer->data['paths'] = self::$paths;
            $viewer->data['table_xml'] = $compTbl_xml;
            $table_object->xml = $viewer->fetch();
        }

        return $table_object;
    }

    /**
     * @param $vars
     * @return array|string|string[]|null
     */
    private static function _prepareComparativeTableSummary($vars)
    {
        $xml = '';

        if (!empty($vars['serial_data']['value']['summary'])) {
            $table_object = $vars['serial_data']['value']['summary'];

            /*
             * Custom changes
             */
            $properties = array(
                'keepNext' => true,
                'gridWidths' => array(4000, 1896, 1896)
            );
            // Remove labels indexes from first column
            foreach ($table_object->rows as $row) {
                if (property_exists($row, 'options') && in_array('highlight', $row->options)) {
                    $row->backgroundColor = COLOR_DARK_GREEN;
                    $row->bold = true;
                    $row->hAlign = 'center';
                } else {
                    $row->cells[0]->backgroundColor = COLOR_LIGHT_GREEN;
                }
                foreach ($row->cells as $cell) {
                    $cell->text = self::_removeLabelsIndexes([$cell->text])[0];
                }
            }

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/comparative_table_summary.xml');
            $viewer->data['table_xml'] = self::_convertSerializedTableToXML($table_object, $properties);

            return $viewer->fetch();
        }

        return $xml;
    }

    /**
     * @param $vars
     * @return array|string|string[]|null
     */
    private static function _prepareCostApproachBuildCoefficientTable($vars)
    {
        $xml = '';

        if (!empty($vars['serial_data']['value']['build_coefficient'])) {
            $table_object = $vars['serial_data']['value']['build_coefficient'];

            /*
             * Custom changes
             */
            $properties = array(
                'gridWidths' => array(4000, 3792)
            );
            foreach ($table_object->rows as $row) {
                if ($row->type == 'body') {
                    $row->cells[0]->backgroundColor = COLOR_LIGHT_GREEN;
                }
            }

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/cost_approach_build_coefficient_table.xml');
            $viewer->data['table_xml'] = self::_convertSerializedTableToXML($table_object, $properties);
            return $viewer->fetch();
        }

        return $xml;
    }

    /**
     * @param $vars
     * @return array|string|string[]|null
     */
    private static function _prepareCostApproachObjectLandGroup($vars)
    {
        $xml = '';

        if (!empty($vars['serial_data']['value']['object_land_group'])) {
            $table_object = $vars['serial_data']['value']['object_land_group'];
            if (property_exists($table_object, 'rows') && count($table_object->rows)) {
                /*
                 * Custom changes
                 */
                $colNum = count($table_object->rows[0]->cells);
                switch ($colNum) {
                    case 2:
                        $gridWidths = [
                            4000,
                            3792
                        ];
                        break;
                    case 3:
                        $gridWidths = [
                            4000,
                            957,
                            2835
                        ];
                        break;
                    case 4:
                        $gridWidths = [
                            4000,
                            957,
                            1275,
                            1560
                        ];
                        break;
                    default:
                        $gridWidths = [
                            7792
                        ];
                }
                $properties = array(
                    'width' => 7792,
                    'gridWidths' => $gridWidths,
                );
                foreach ($table_object->rows as $row) {
                    if ($row->type == 'body') {
                        $row->cells[0]->backgroundColor = COLOR_LIGHT_GREEN;
                    } elseif ($row->type == 'foot') {
                        $row->cells[0]->backgroundColor = COLOR_DARK_GREEN;
                        $row->cells[0]->hAlign = 'right';
                        $row->cells[0]->bold = true;
                    }
                }

                $viewer = new Viewer(self::$registry);
                $viewer->setFrameset(self::$paths['templates'] . '/cost_approach_object_land_group.xml');
                $viewer->data['table_xml'] = self::_convertSerializedTableToXML($table_object, $properties);
                $xml = $viewer->fetch();
            }
        }

        return $xml;
    }

    /**
     * Returns an array of analogs nomenclatures
     *
     * @param Document $comparativeTable The comparative table type document to extract the info from.
     * @return array of type Nomenclature
     * @see  _prepareAnalogsTable()
     */
    private static function _getAnalogsFromCompTable(Document $comparativeTable): array
    {
        $vars = $comparativeTable->getVarsForTemplateAssoc();

        //group_analogue
        $names_idx = array_flip($vars['group_analogue']['names']);
        $values = $vars['group_analogue']['values'] ?? [];

        // Less than or equal to 1, that means no analogs in the tbl.
        if (count($values) <= 1) {
            return [];
        }

        $analogIds = array_filter(array_column($values, $names_idx['comparison_element_id']));
        if (empty($analogIds)) {
            return [];
        }

        // Remove the first row, as it is a ID of the assessment object
        $analogIds = array_splice($analogIds, 1);

        $analogIds_str = implode(',', $analogIds);
        $noms = Nomenclatures::search(
            self::$registry,
            array(
                'where' => array("n.id IN ({$analogIds_str})"),
                'sanitize' => true,
                'sort' => array(
                    "find_in_set(n.id, '{$analogIds_str}')",
                ),
            )
        );
        return $noms ? $noms : [];
    }

    /**
     * Generates table of analogs
     *
     * @param string $label Some text to place in the very first row
     * @param Nomenclature[] $analogs An array of analogs
     * @return string XML snippet of the table
     * @see _getAnalogsFromCompTable()
     */
    private static function _prepareAnalogsTable(string $label, array $analogs)
    {
        include_once realpath(dirname(__FILE__) . '/DocxTable.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableRow.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableCell.php');

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/analogs_list.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['paths'] = self::$paths;

        $analogsTbl = new DocxTable('analogs', [TABLES_WIDTH_FULL_PAGE]);

        $analogsTbl->addRow($r1 = new DocxTableRow(true));
        $r1->addCell($c0 = new DocxTableCell($label));
        $c0->setBackgroundColor(COLOR_DARK_GREEN);
        $c0->setBold(true);

        $index = 1;
        foreach ($analogs as $k => $v) {
            $vars = $v->getVarsForTemplateAssoc();

            $title = sprintf(
                self::i18n('analog_ref_title'),
                $index,
                $v->get('name') ?? ''
            );
            $analogsTbl->addRow($r1 = new DocxTableRow(true));
            $r1->addCell($c1 = new DocxTableCell($title, 1, 'center'));
            $c1->setBackgroundColor(COLOR_DARK_GREEN);
            $c1->setBold(true);

            $analogsTbl->addRow($r2 = new DocxTableRow());
            $r2->addCell(new DocxTableCell($vars['analog_desc']['value'] ?? '-'));

            $analogsTbl->addRow($r3 = new DocxTableRow());
            $url = $vars['analog_source']['value'] ?? '';
            $urlText = wordwrap($url, self::$settings['analogues_url_wrap_length'], "\n", true);
            $sourceLinkSnippet = ' </w:t></w:r>' . self::_addLink($url, $urlText) . '<w:r><w:t>';
            $r3->addCell(new DocxTableCell(sprintf(self::i18n('analog_ref_source'), $sourceLinkSnippet)));

            $index++;
        }

        $viewer->data['analogsTbl'] = $analogsTbl;

        return $viewer->fetch();
    }

    /**
     * Adds a link relation to the document and returns an XML snippet to be placed in the document
     * Make sure to place this snippet outside <w:r> tag
     *
     * @param string $targetUrl The URL
     * @param string $text The text that will be shown in the document
     * @param int $fontSize Font size for the link
     * @return string Returns an XML snippet to be placed in the document.
     */
    private static function _addLink(string $targetUrl, string $text, int $fontSize = 18): string
    {
        $targetUrl = self::_encodeEntities(htmlspecialchars($targetUrl));
        $text = self::_encodeEntities(htmlspecialchars($text));
        // Add the link in the rels.
        $rId = self::$templateDocx->addHyperlink($targetUrl);

        // Render an XML snippet for inserting the link in the document
        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/_hyperlink.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['rid'] = "rId{$rId}";
        $viewer->data['text'] = $text;
        $viewer->data['font_size'] = $fontSize;
        $viewer->data['bold'] = true;

        return $viewer->fetch();
    }

    /**
     * @param string $string
     * @return string
     */
    private static function _encodeEntities(string $string): string
    {
        return preg_replace(
            '#&(?!amp;|quot;|nbsp;|gt;|lt;|laquo;|raquo;|copy;|reg;|bul;|rsquo;)#',
            '&amp;',
            $string
        );
    }

    /**
     * Combine/Remove space between tables
     * income_approach_calc_group_table - _prepareRevenueApproachTable1
     * income_approach_calc_table - _prepareRevenueApproachTable2
     *
     * @param array $vars
     * @param bool $showTable2
     *
     * @return string
     */
    private static function _prepareRevenueApproachTable1And2(array $vars, bool $showTable2): string
    {
        $xml = self::_prepareRevenueApproachTable1($vars);
        if ($showTable2) {
            $xml .= self::_prepareRevenueApproachTable2($vars);
        }

        return self::inverseWrapXml($xml);
    }

    /**
     * Table income_approach_calc_group_table
     *
     * @param array $vars
     *
     * @return string Xml table
     */
    private static function _prepareRevenueApproachTable1(array $vars): string
    {
        if (empty($vars['serial_data']['value']['income_approach_calc_group_table'])) {
            return '';
        }
        $table_object = $vars['serial_data']['value']['income_approach_calc_group_table'];
        $columns_count = count($table_object->rows[0]->cells) - 1;

        // $optimalColWidth = 1446;
        $columnsWidth = ($columns_count === 1) ? 5000 : 2000;
        // fix rounding erros
        $labelWidth = self::$tablesWidthFullPage - $columnsWidth * $columns_count;

        $properties = [
            'keepNext' => true,
            'gridWidths' => [
                $labelWidth,
                ...array_fill(0, $columns_count, $columnsWidth)
            ],
        ];

        $table_object->rows[0]->bold = true;

        // Remove the type from the column headings
        foreach ($table_object->rows[0]->cells as $cell) {
            preg_match('/^[^\(]*\s*\((.+)\)\s*$/', $cell->text, $matches);
            if (isset($matches[1])) {
                $cell->text = $matches[1];
            }
        }

        // Remove labels indexes from first column
        foreach ($table_object->rows as $k => $row) {
            foreach ($row->cells as $cell) {
                if (in_array('highlight', $cell->options)) {
                    $cell->backgroundColor = COLOR_DARK_GREEN;
                    $cell->hAlign = 'left';
                } else {
                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                    $cell->hAlign = $k > 0 ? 'right' : 'center';
                }
                $cell->text = self::_removeLabelsIndexes([$cell->text])[0];
            }
        }

        return self::_convertSerializedTableToXML($table_object, $properties);
    }

    /**
     * Table income_approach_calc_table
     *
     * @param array $vars
     *
     * @return string Xml table
     */
    private static function _prepareRevenueApproachTable2(array $vars): string
    {
        if (empty($vars['income_approach_calc_table']['values'])) {
            return '';
        }

        //group income_approach_calc_group_table
        $names_idx = array_flip($vars['income_approach_calc_table']['names']);
        $labels = array_combine($vars['income_approach_calc_table']['names'], $vars['income_approach_calc_table']['labels']);
        $hidden = array_combine($vars['income_approach_calc_table']['names'], $vars['income_approach_calc_table']['hidden']);
        $values = $vars['income_approach_calc_table']['values'];

        foreach ($names_idx as $name => $nidx) {
            //transpose the table
            if ($hidden[$name]) {
                continue;
            }
            //the first column is always the label
            $columns = [$labels[$name]];
            $columns[] = $values[$nidx];
            $table_rows[] = $columns;
        }

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/market_approach_table1.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['rows'] = $table_rows;
        $viewer->data['bg_color_th'] = COLOR_DARK_GREEN;
        $viewer->data['bg_color_td'] = COLOR_LIGHT_GREEN;
        $columns_count = count($values) + 1;

        $firstTableObject = $vars['serial_data']['value']['income_approach_calc_group_table'];
        $firstTableColumnsCount = count($firstTableObject->rows[0]->cells) - 1;

        $viewer->data['bgcolors'] = array_merge(
            [COLOR_DARK_GREEN],
            array_fill(0, $columns_count, COLOR_LIGHT_GREEN)
        );

        // $optimalColWidth = 1446;
        $columnsWidth = ($firstTableColumnsCount === 1) ? 5000 : 2000;
        // fix rounding erros
        $labelWidth = self::$tablesWidthFullPage - $columnsWidth * $firstTableColumnsCount;
        $tblWidth = $labelWidth + $columnsWidth;

        $viewer->data['table_width'] = $tblWidth;
        $viewer->data['grid_widths'] = [$labelWidth, $columnsWidth];
        $viewer->data['alignment'] = array_merge(
            ['left'],
            array_fill(0, $columns_count, 'right')
        );
        $viewer->data['bold'] = [];
        $viewer->data['bold'][count($table_rows) - 2] = array_fill(0, count($columns), true);
        $viewer->data['bold'][count($table_rows) - 1] = array_fill(0, count($columns), true);

        return $viewer->fetch();
    }

    /**
     * @param $market_norm_doc_ids
     * @return string
     */
    private static function _prepareRevenueApproachTable3($market_norm_doc_ids)
    {
        if (empty($market_norm_doc_ids)) {
            return '';
        }
        //fetch the documents
        $MN_docs = Documents::search(self::$registry, array(
            'where' => array(
                'd.id IN (' . implode(', ', $market_norm_doc_ids) . ')',
            ),
            'sort' => array(
                'find_in_set(d.id, "' . implode(',', $market_norm_doc_ids) . '")',
            ),
            'sanitize' => false,
        ));

        if (!empty($MN_docs)) {
            $table_rows = array();
            foreach ($MN_docs as $MN_doc) {
                $MN_vars = self::_decorateVars([
                    'assessment_object' => $MN_doc->getVar('assessment_object'),
                    'capital_rate' => $MN_doc->getVar('capital_rate'),
                ]);
                foreach ($MN_vars['assessment_object']['options'] as $option) {
                    if ($MN_vars['assessment_object']['value'] == $option['option_value']) {
                        $capital_rate_name = $option['label'];
                        break;
                    }
                }

                $table_rows[] = array(
                    'Норма на капитализация - ' . $capital_rate_name,
                    $MN_vars['capital_rate']['value'] . '%',
                );
            }
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/market_approach_table1.xml';
            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset($template_file);
            $viewer->data['rows'] = $table_rows;
            $viewer->data['bg_color_td'] = 'FFFFFF';
            $column_count = count($table_rows[0]);
            $colWidth = round(self::$tablesWidthFullPage / $column_count);
            $viewer->data['widths'] = [
                (self::$tablesWidthFullPage - $colWidth * ($column_count - 1)) / 20,
                ...array_fill(0, $column_count - 1, $colWidth / 20)
            ];
            $viewer->data['alignment'] = array_fill(0, $column_count, 'left');

            return self::inverseWrapXml($viewer->fetch());
        }

        return '';
    }

    /**
     * Adds an appendix template around the content if it is present in the document
     * The replacement is directly in self::$templateDocx->_documentXML
     *
     * @param string $xmlContent the XML content it should be clean of opening and closing paragraphs
     * @param array $data any aditional data to add.
     * @return string resulting clean XML
     */
    private static function _addAppendixEnvelop(string $xmlContent, array $data = [])
    {
        // Add the appendix envelop (header and footer)
        $extenderAdx = new Extender();
        $extenderAdx->add('appendix_number', '[appendix_number]');
        $extenderAdx->add('content', $xmlContent);

        foreach ($data as $k => $v) {
            $extenderAdx->add($k, $v);
        }

        return $extenderAdx->expand(self::$ADX_template);
    }

    /**
     * Parses the document and replaces a tag with a sequential numberig starting from 1
     * The replacement is directly in self::$templateDocx->_documentXML
     *
     * @param string $tag the tag or any string realy, to be searched and replaced with the numbers. The default is '[appendix_number]'
     */
    private static function _appendixSetNumbers($tag = '[appendix_number]')
    {
        $tagLen = strlen($tag);
        $pos = 0;
        $number = 1;
        do {
            $pos = strpos(self::$templateDocx->_documentXML, $tag, $pos);
            if ($pos !== false) {
                self::$templateDocx->_documentXML = substr_replace(self::$templateDocx->_documentXML, $number++, $pos, $tagLen);
            }
        } while ($pos !== false);
    }

    /**
     * Takes an array of objects containing XML fragments and flag if they need to be in landscape and produces a XML string with page brakes in between the fragments.
     *
     * @param array $xmlPages an array of objects with 2 properties: $obj->xml - the clean xml fragment; $obj->isLandscape - a bolean flag to indicate if the page should be landskape
     * @param false $skipFirstPb If the first break should be skipped.
     * @return string the resulting clean XML
     */
    private static function _placeSectionsInXml(array $xmlPages, $skipFirstPb = false)
    {
        if (empty($xmlPages)) {
            return '';
        }

        $viewerPbP = new Viewer(self::$registry);
        $viewerPbP->setFrameset(self::$paths['templates'] . '/_page_break.xml');
        $viewerPbP->data['paths'] = self::$paths;
        $viewerPbP->data['footersAndHeaders'] = self::$footersAndHeaders;
        $viewerPbP->data['orientation'] = 'portrait';
        $portraitSect = $viewerPbP->fetch();

        $viewerPbL = new Viewer(self::$registry);
        $viewerPbL->setFrameset(self::$paths['templates'] . '/_page_break.xml');
        $viewerPbL->data['paths'] = self::$paths;
        $viewerPbL->data['footersAndHeaders'] = self::$footersAndHeaders;
        $viewerPbL->data['orientation'] = 'landscape';
        $landscapeSect = $viewerPbL->fetch();
        unset($viewerPbP, $viewerPbL);

        // Start off with page brake
        $struct = $skipFirstPb ? '' : $portraitSect;
        $isPrevLandscape = false;
        // Structure all the pages witht the proper greaks between them
        foreach ($xmlPages as $v) {
            $struct .= $v->xml;
            $struct .= ($v->isLandscape ?? false) ? $landscapeSect : $portraitSect;
        }

        return self::inverseWrapXml($struct);
    }

    /**
     * @param Document $RAD_doc
     * @param $isSupervision
     * @return stdClass
     */
    private static function _prepareRevenueApproachTable4(Document $RAD_doc, $isSupervision)
    {
        $obj = new stdClass();
        $vars = self::_decorateVars($RAD_doc->getVarsForTemplateAssoc());
        $ra_comparative_table = self::_prepareComparativeTable($vars);

        $pageXml = $ra_comparative_table->xml . self::_prepareComparativeTableSummary($vars);

        if (!empty(trim($vars['object_comment']['value']))) {
            $grid = [7792];
            $tbl = new DocxTable('КОМЕНТАР', $grid);

            $tbl->addRow($commentHeader = new DocxTableRow(true, true));
            $commentHeader->addCell($comH = new DocxTableCell('КОМЕНТАР', 1, 'center'));
            $comH->setBackgroundColor(COLOR_DARK_GREEN);
            $comH->setBold(true);

            $tbl->addRow($commentBody = new DocxTableRow(true, true));
            $commentBody->addCell($comB = new DocxTableCell(trim($vars['object_comment']['value']), 1, 'left'));

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/_table.xml');
            $viewer->data['paths'] = self::$paths;
            $viewer->data['table'] = $tbl;
            $pageXml .= self::inverseWrapXml($viewer->fetch());
        }

        $ga_names = array_flip($vars['group_analogue']['names']);
        $object_name = '';

        if (isset($vars['group_analogue']['values']) && isset($vars['group_analogue']['values'][1])) {
            $object_name = $vars['group_analogue']['values'][1][$ga_names['comparison_element']] ?? '';
        }

        $vars = array_merge(self::$model->extender->placeholders, $vars);
        $vars['object_name'] = $object_name;
        if ($isSupervision) {
            $url = '[alvis_url]/revenue-approach-comparativetable/' . self::$model->get('id') . '/' . $RAD_doc->get('id');
            $vars['title'] = self::_addLink($url, self::i18n('comparative_table_title_revenueApproach'));
        } else {
            $vars['title'] = self::i18n('comparative_table_title_revenueApproach');
        }

        $obj->xml = self::_addAppendixEnvelop(
            $pageXml,
            $vars
        );
        $obj->isLandscape = $ra_comparative_table->grid_cols_count > self::$settings['comparative_tables_landscape_cols'];

        return $obj;
    }

    /**
     *
     * @param array $groupTblVar
     * @param array $assesmentObjects
     * @param array $settings
     *
     * @return array
     */
    private static function getLandDocsByObjectId(array $groupTblVar, array $assesmentObjects, array $settings): array
    {
        $groupTblVar['rated_object_group'] = self::_convertDropdownValues2Labels_gt2($groupTblVar['rated_object_group']);
        $values = $groupTblVar['rated_object_group']['values'];
        $names_idx = array_flip($groupTblVar['rated_object_group']['names']);
        $lands = [];
        foreach ($values as $row => $val) {
            $landId = $val[$names_idx['rated_object_name_id']];
            $objectType = isset($assesmentObjects[$landId]) ? $assesmentObjects[$landId]->get('type') : 0;

            switch (true) {
                case in_array($objectType, $settings['doc_types_level_three']):
                    $landId = self::getObjectParent($landId) ?? 0;
                    // no break
                case in_array($objectType, $settings['doc_types_level_two']):
                    $landId = self::getObjectParent($landId) ?? 0;
            }
            $lands[$val[$names_idx['rated_object_name_id']]] = $assesmentObjects[$landId] ?? [];
            $assesmentObjects[$landId]->getAssocVars();
        }
        return $lands;
    }

    /**
     * @param array $vars
     * @param array $lands
     *
     * @return array|string|string[]|null
     */
    private static function _prepareCostlyApproachTable1(array $vars, array $lands)
    {
        if (empty($vars['rated_object_group']['values'])) {
            return '';
        }
        // Use labels instead of values of dropdowns
        $vars['rated_object_group'] = self::_convertDropdownValues2Labels_gt2($vars['rated_object_group']);

        //group income_approach_calc_group_table
        $names_idx = array_flip($vars['rated_object_group']['names']);
        $labels = array_combine($vars['rated_object_group']['names'], $vars['rated_object_group']['labels']);
        $hidden = array_combine($vars['rated_object_group']['names'], $vars['rated_object_group']['hidden']);
        $values = $vars['rated_object_group']['values'];
        $header2 = [];

        $hidden['residual_life_motivation'] = empty(array_filter(array_column($values, $names_idx['residual_life_motivation']))) ? '1' : '';
        $labels['build_land_value_eur'] = (self::$isOps ? self::i18n('ubb_permit_label') : self::i18n('ubb_land_label')) . ', [осн валута]';

        foreach ($names_idx as $name => $nidx) {
            //transpose the table
            if ($hidden[$name]) {
                continue;
            }

            if ($name == 'rated_object_name') {
                $header2[] = $labels[$name];
                foreach ($values as $k => $row) {
                    $header2[] = $row[$nidx];
                }
                continue;
            }

            if (in_array($name, ['build_land_value_eur', 'build_land_improvement_eur']) && !self::$isOps) {
                $objectId = $values[1][$names_idx['rated_object_name_id']];
                $landDistribution = $lands[$objectId]->get('assoc_vars')['land_distribution'];
                if ($landDistribution['value'] == 2) {
                    continue;
                }
            }

            //the first column is always the label
            $columns = array($labels[$name]);
            foreach ($values as $row) {
                $columns[] = $row[$nidx];
            }
            $table_rows[] = $columns;
        }

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/market_approach_table1.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['header2'] = $header2;
        $viewer->data['rows'] = $table_rows;
        $viewer->data['bg_color_th'] = COLOR_DARK_GREEN;
        $viewer->data['bg_color_td'] = COLOR_LIGHT_GREEN;
        $columns_count = count($values) + 1;
        $viewer->data['bgcolors'] = array_merge(
            array(COLOR_DARK_GREEN),
            array_fill(0, $columns_count, COLOR_LIGHT_GREEN)
        );
        $viewer->data['widths'] = array_merge(
            array(180),
            array_fill(0, $columns_count, 400 / $columns_count)
        );
        $viewer->data['alignment'] = array_merge(
            array('left'),
            array_fill(0, $columns_count, 'center')
        );

        return self::inverseWrapXml($viewer->fetch());
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareCostlyApproachObjectSummaryTable($vars)
    {
        if (self::$isOps) {
            return '';
        }

        include_once realpath(dirname(__FILE__) . '/DocxTable.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableRow.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableCell.php');

        $tbl = new DocxTable('', []);

        $tbl->addRow($tr = new DocxTableRow(true, true));
        $tr->addCell($td = new DocxTableCell(''));
        $td->setBackgroundColor(COLOR_DARK_GREEN);
        $tr->addCell($td = new DocxTableCell(self::i18n('costly_approach_object_summary_th_main_currency')));
        $td->setBackgroundColor(COLOR_DARK_GREEN);
        $td->setBold(true);
        $tr->addCell($td = new DocxTableCell(self::i18n('costly_approach_object_summary_th_alt_currency')));
        $td->setBackgroundColor(COLOR_DARK_GREEN);
        $td->setBold(true);

        $hide_total_build_land_value_eur = false;
        if (array_key_exists(DOCUMENTS_TYPE_COSTLY_APPROACH_ANALOGUES, self::$related_records)) {
            $CA_analogues_ids_list = implode(', ', self::$related_records[DOCUMENTS_TYPE_COSTLY_APPROACH_ANALOGUES]);
            $query = "
                SELECT dc1.value
                  FROM " . DB_TABLE_DOCUMENTS . " AS d
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm
                    ON (NOT d.deleted
                      AND d.id IN ({$CA_analogues_ids_list})
                      AND fm.model = 'Document'
                      AND fm.model_type = d.type
                      AND fm.name = 'object_report_name_id')
                  JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc
                    ON (dc.model_id = d.id
                      AND dc.var_id = fm.id
                      AND dc.num = 1
                      AND dc.lang = '')
                  JOIN " . DB_TABLE_DOCUMENTS . " AS d1
                    ON (NOT d1.deleted
                      AND d1.id = dc.value)
                  JOIN " . DB_TABLE_FIELDS_META . " AS fm1
                    ON (fm1.model = 'Document'
                      AND fm1.model_type = d1.type
                      AND fm1.name = 'land_distribution')
                  JOIN " . DB_TABLE_DOCUMENTS_CSTM . " AS dc1
                    ON (dc1.model_id = d1.id
                      AND dc1.var_id = fm1.id
                      AND dc1.num = 1
                      AND dc1.lang = ''
                      AND dc1.value = 1)
                  ORDER BY d1.id ASC
                  LIMIT 1";
            if (self::$registry['db']->GetOne($query)) {
                $hide_total_build_land_value_eur = true;
            }
        }
        if (!$hide_total_build_land_value_eur) {
            $tbl->addRow($tr = new DocxTableRow());
            $tr->addCell($td = new DocxTableCell($vars['total_build_land_value_eur']['label']));
            $td->setBackgroundColor(COLOR_LIGHT_GREEN);
            $tr->addCell($td = new DocxTableCell($vars['total_build_land_value_eur']['value']));
            $td->setBackgroundColor(COLOR_LIGHT_GREEN);
            $tr->addCell($td = new DocxTableCell(''));
            $td->setBackgroundColor(COLOR_LIGHT_GREEN);
        }

        $tbl->addRow($tr = new DocxTableRow());
        $tr->addCell($td = new DocxTableCell($vars['total_market_value_cost_approach_eur']['label']));
        $td->setBackgroundColor(COLOR_LIGHT_GREEN);
        $tr->addCell($td = new DocxTableCell($vars['total_market_value_cost_approach_eur']['value']));
        $td->setBackgroundColor(COLOR_LIGHT_GREEN);
        $tr->addCell($td = new DocxTableCell($vars['total_market_value_cost_approach_bgn']['value']));
        $td->setBackgroundColor(COLOR_LIGHT_GREEN);

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/_table.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['paths'] = self::$paths;
        $viewer->data['table'] = $tbl;

        return self::inverseWrapXml($viewer->fetch());
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareCostlyApproachTable2($vars)
    {
        //group income_approach_calc_group_table
        $names_idx = array_flip($vars['object_improvement_grp']['names']);
        $labels = array_combine($vars['object_improvement_grp']['names'], $vars['object_improvement_grp']['labels']);
        $hidden = array_combine($vars['object_improvement_grp']['names'], $vars['object_improvement_grp']['hidden']);
        if (empty($vars['object_improvement_grp']['values'])) {
            return '';
        }
        $values = $vars['object_improvement_grp']['values'];
        $object_improvement_name_values = array_filter(array_column($values, $names_idx['object_improvement']));
        if (empty($object_improvement_name_values)) {
            return '';
        }

        foreach ($names_idx as $name => $nidx) {
            //transpose the table
            if ($hidden[$name]) {
                continue;
            }
            //the first column is always the label
            $columns = array($labels[$name]);
            foreach ($values as $row) {
                $columns[] = $row[$nidx];
            }
            $table_rows[] = $columns;
        }

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/market_approach_table1.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['rows'] = $table_rows;
        $viewer->data['bg_color_th'] = COLOR_DARK_GREEN;
        $viewer->data['bg_color_td'] = COLOR_LIGHT_GREEN;
        $columns_count = count($values) + 1;
        $viewer->data['bgcolors'] = array_merge(
            array(COLOR_DARK_GREEN),
            array_fill(0, $columns_count, COLOR_LIGHT_GREEN)
        );
        $viewer->data['widths'] = array_merge(
            array(180),
            array_fill(0, $columns_count, 400 / $columns_count)
        );
        $viewer->data['alignment'] = array_merge(
            array('left'),
            array_fill(0, $columns_count, 'center')
        );

        return self::inverseWrapXml($viewer->fetch());
    }

    /**
     * @param string $title
     * @param string $text
     * @return string
     */
    private static function _prepareSimpleTable(string $title, string $text)
    {
        $grid = [self::$tablesWidthFullPage];
        $tbl = new DocxTable($title, $grid);

        $tbl->addRow($commentHeader = new DocxTableRow(true, true));
        $commentHeader->addCell($comH = new DocxTableCell($title, 1, 'left'));
        $comH->setBackgroundColor(COLOR_DARK_GREEN);
        $comH->setBold(true);

        $tbl->addRow($commentBody = new DocxTableRow(true, true));
        $commentBody->addCell($comB = new DocxTableCell(trim($text), 1, 'left'));

        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset(self::$paths['templates'] . '/_table.xml');
        $viewer->data['paths'] = self::$paths;
        $viewer->data['table'] = $tbl;
        return self::inverseWrapXml($viewer->fetch());
    }

    /**
     * @param Document $CAD_doc
     * @param $isSupervision
     * @return stdClass
     */
    private static function _prepareCostlyApproachTable3(Document $CAD_doc, $isSupervision)
    {
        $obj = new stdClass();
        $vars = self::_decorateVars($CAD_doc->getVarsForTemplateAssoc());
        $ca_comparative_table = self::_prepareComparativeTable($vars);

        $pageXml = $ca_comparative_table->xml;
        $pageXml .= self::_prepareComparativeTableSummary($vars);
        $pageXml .= self::inverseWrapXml('<w:p><w:r><w:br w:type="page" /></w:r></w:p>');
        $pageXml .= self::_prepareCostApproachBuildCoefficientTable($vars);
        $pageXml .= self::_prepareCostApproachObjectLandGroup($vars);

        if (!empty(trim($vars['object_comment']['value']))) {
            $grid = [7792];
            $tbl = new DocxTable('КОМЕНТАР', $grid);

            $tbl->addRow($commentHeader = new DocxTableRow(true, true));
            $commentHeader->addCell($comH = new DocxTableCell('КОМЕНТАР', 1, 'center'));
            $comH->setBackgroundColor(COLOR_DARK_GREEN);
            $comH->setBold(true);

            $tbl->addRow($commentBody = new DocxTableRow(true, true));
            $commentBody->addCell($comB = new DocxTableCell(trim($vars['object_comment']['value']), 1, 'left'));

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/_table.xml');
            $viewer->data['paths'] = self::$paths;
            $viewer->data['table'] = $tbl;
            $pageXml .= self::inverseWrapXml($viewer->fetch());
        }

        $ga_names = array_flip($vars['group_analogue']['names']);
        $object_name = '';
        if (isset($vars['group_analogue']['values']) && isset($vars['group_analogue']['values'][1])) {
            $object_name = $vars['group_analogue']['values'][1][$ga_names['comparison_element']] ?? '';
        }
        $vars = array_merge(self::$model->extender->placeholders, $vars);
        $vars['object_name'] = $object_name;
        if ($isSupervision) {
            $url = '[alvis_url]/cost-approach-comparativetable/' . self::$model->get('id') . '/' . $CAD_doc->get('id');
            $vars['title'] = self::_addLink($url, self::i18n('comparative_table_title_costApproach'));
        } else {
            $vars['title'] = self::i18n('comparative_table_title_costApproach');
        }

        $obj->xml = self::_addAppendixEnvelop(
            $pageXml,
            $vars
        );
        $obj->isLandscape = $ca_comparative_table->grid_cols_count > self::$settings['comparative_tables_landscape_cols'];
        return $obj;
    }

    /**
     * @param array $gt2Var
     * @return array
     */
    private static function _convertDropdownValues2Labels_gt2(array $gt2Var): array
    {
        foreach ($gt2Var['names'] ?? [] as $nidx => $name) {
            //transpose the table
            if ($gt2Var['hidden'][$nidx]) {
                continue;
            }
            if ($gt2Var['types'][$nidx] == 'dropdown') {
                foreach ($gt2Var['values'] ?? [] as $k => $v) {
                    if (empty($gt2Var[$name])) {
                        continue;
                    }
                    foreach ($gt2Var[$name]['options'] ?? [] as $k1 => $v1) {
                        if ($v1['option_value'] == $v[$nidx]) {
                            $gt2Var['values'][$k][$nidx] = $v1['label'];
                            break;
                        }
                    }
                }
            }
        }
        return $gt2Var;
    }

    /**
     * @param stdClass $table_object
     * @param array $properties
     * @return string
     */
    private static function _convertSerializedTableToXML(stdClass $table_object, array $properties = array()): string
    {
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset(self::$paths['templates'] . '/serialized_table.xml');
        $viewer->data['properties'] = $properties;
        $viewer->data['table'] = $table_object;
        return $viewer->fetch();
    }

    /**
     * @param stdClass $table_object
     * @return int
     */
    private static function _getSerializedTableGridColsCount(stdClass $table_object): int
    {
        $max_columns_count = 0;
        foreach ($table_object->rows as $row) {
            $row_cells_count = count($row->cells);
            if ($row_cells_count > $max_columns_count) {
                $max_columns_count = $row_cells_count;
            }
        }
        return $max_columns_count;
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR11($vars): string
    {
        if (empty($vars['serial_data']['value']['market_approach_group'])) {
            return '';
        }
        $table_object = $vars['serial_data']['value']['market_approach_group'];

        $properties = array(
            'fontSize' => '16',
            'width' => self::$tablesWidthFullPage,
            'gridWidths' => array(
                1724,
                1120,
                1120,
                782,
                1120,
                1120,
                782,
                1120,
                1120,
                782,
            )
        );
        foreach ($table_object->rows as $row_index => $row) {
            if ($row->type == 'head') {
                foreach ($row->cells as $cell) {
                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                }
            } elseif ($row->type == 'body') {
                $row->cells[3]->text .= '%';
                $row->cells[6]->text .= '%';
                $row->cells[9]->text .= '%';
                $row->cells[3]->hAlign = 'right';
                $row->cells[6]->hAlign = 'right';
                $row->cells[9]->hAlign = 'right';
            }
        }

        return self::inverseWrapXml(self::prepResultTable($table_object, $properties));
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR13($vars): string
    {
        if (empty($vars['serial_data']['value']['forced_sale_group'])) {
            return '';
        }

        $table_object = $vars['serial_data']['value']['forced_sale_group'];

        $properties = array(
            'fontSize' => '16',
            'width' => self::$tablesWidthFullPage,
            'gridWidths' => array(
                1724,
                1300,
                1180,
                1132,
                1391,
                1132,
                1132,
                1799,
            )
        );
        foreach ($table_object->rows as $row) {
            if ($row->type == 'head') {
                foreach ($row->cells as $cell) {
                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                }
            } elseif ($row->type === 'body') {
                $row->cells[1]->text .= ' мес.';
                $row->cells[1]->hAlign = 'right';
                $row->cells[4]->text .= '%';
                $row->cells[4]->hAlign = 'right';
                $row->cells[5]->text .= '%';
                $row->cells[5]->hAlign = 'right';
                $row->cells[6]->text .= '%';
                $row->cells[6]->hAlign = 'right';
            }
        }

        return self::inverseWrapXml(self::prepResultTable($table_object, $properties));
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR12($vars): string
    {
        if (empty($vars['serial_data']['value']['suggest_group'])) {
            return '';
        }

        $table_object = $vars['serial_data']['value']['suggest_group'];
        self::processColorsForTableR12R16AndR18($table_object);
        $propertires = self::prepResultTableGridWidths($table_object, [2700, 800]);
        return self::inverseWrapXml(self::prepResultTable($table_object, $propertires));
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR16($vars): string
    {
        if (empty($vars['serial_data']['value']['performance_group'])) {
            return '';
        }

        $table_object = $vars['serial_data']['value']['performance_group'];
        self::processColorsForTableR12R16AndR18($table_object);
        $propertires = self::prepResultTableGridWidths($table_object, [1800, 900, 800]);
        return self::inverseWrapXml(self::prepResultTable($table_object, $propertires));
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR18($vars): string
    {
        if (empty($vars['serial_data']['value']['performance7_group'])) {
            return '';
        }

        $table_object = $vars['serial_data']['value']['performance7_group'];
        self::processColorsForTableR12R16AndR18($table_object);
        $propertires = self::prepResultTableGridWidths($table_object, [1800, 900, 800]);
        return self::inverseWrapXml(self::prepResultTable($table_object, $propertires));
    }

    /**
     * @param $vars
     * @return string
     */
    private static function _prepareResultsTableR14($vars): string
    {
        if (empty($vars['serial_data']['value']['mortgage_value_group'])) {
            return '';
        }

        $table_object = $vars['serial_data']['value']['mortgage_value_group'];
        foreach ($table_object->rows as $row_index => $row) {
            if ($row->type == 'head') {
                $cellCount = count($row->cells);
                foreach ($row->cells as $cell_index => $cell) {
                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                }
            }
        }

        $propertires = self::prepResultTableGridWidths($table_object, [2000, 1500]);

        return self::inverseWrapXml(self::prepResultTable($table_object, $propertires));
    }

    /**
     * @param string $xml
     * @return string
     */
    private static function inverseWrapXml(string $xml): string
    {
        return '</w:t></w:r></w:p>' . $xml . '<w:p><w:r><w:t>';
    }

    /**
     * @param stdClass $table_object
     * @return void
     */
    private static function processColorsForTableR12R16AndR18(stdClass $table_object): void
    {
        foreach ($table_object->rows as $row_index => $row) {
            if ($row->type == 'head') {
                $cellCount = count($row->cells);
                foreach ($row->cells as $cell_index => $cell) {
                    if ($row_index == 0 && $cell_index == $cellCount - 1 || $row_index == 1 && $cell_index > $cellCount - 3 || $row_index == 2 && $cell_index > $cellCount - 5) {
                        $cell->backgroundColor = COLOR_DARK_GREEN;
                    } else {
                        $cell->backgroundColor = COLOR_LIGHT_GREEN;
                    }
                }
            } elseif (property_exists($row, 'options') && in_array('highlight', $row->options)) {
                foreach ($row->cells as $cell_index => $cell) {
                    if ($cell_index == 0) {
                        $cell->hAlign = 'center';
                    }
                    $cell->backgroundColor = COLOR_DARK_GREEN;
                    $cell->bold = true;
                }
            }
        }
    }

    /**
     * @param stdClass $table_object
     * @param array $gridWiths
     * @return array
     */
    private static function prepResultTableGridWidths(stdClass $table_object, array $gridWiths): array
    {
        $properties = [
            'fontSize' => '16',
            'width' => self::$tablesWidthFullPage,
        ];
        $gridColsCount = self::_getSerializedTableGridColsCount($table_object);
        $otherColumnsCount = $gridColsCount - count($gridWiths);
        $otherColumnWidth = intval((self::$tablesWidthFullPage - array_sum($gridWiths)) / $otherColumnsCount);
        $otherColumnsWidths = array_fill(0, $otherColumnsCount, $otherColumnWidth);
        $properties['gridWidths'] = array_merge($gridWiths, $otherColumnsWidths);

        return $properties;
    }

    /**
     * @param stdClass $table_object
     * @param array $properties
     * @return string
     */
    private static function prepResultTable(stdClass $table_object, array $properties): string
    {
        $headingCell = new stdClass();
        $headingCell->text = self::_removeLabelsIndexes([$table_object->label])[0];
        $headingCell->backgroundColor = COLOR_DARK_GREEN;
        $headingCell->bold = true;
        $headingCell->colspan = self::_getSerializedTableGridColsCount($table_object);
        $headingRow = new stdClass();
        $headingRow->cells = array($headingCell);
        array_splice($table_object->rows, 0, 0, array($headingRow));
        return self::_convertSerializedTableToXML($table_object, $properties);
    }

    /**
     * @param $vars
     * @return array|string|string[]|null
     */
    private static function _prepareSEKTable($vars)
    {
        $xml = '';

        if (!empty($vars['serial_data']['value']['sek_table'])) {
            $table_object = $vars['serial_data']['value']['sek_table'];

            /*
             * Custom changes
             */
            $firstColWidth = 3000;
            $otherColsCount = self::_getSerializedTableGridColsCount($table_object) - 1;
            // Calculate printable area: full width - left margin - right margin
            $otherColsWidth = intval((self::$tablesWidthFullPage - $firstColWidth) / $otherColsCount);
            $gridWidths = array_fill(0, (self::_getSerializedTableGridColsCount($table_object) - 1), $otherColsWidth);
            array_unshift($gridWidths, $firstColWidth);
            $properties = array(
                'width' => self::$tablesWidthFullPage,
                'gridWidths' => $gridWidths
            );
            foreach ($table_object->rows as $row) {
                if ($row->type == 'body' && (!property_exists($row, 'options') || !in_array('shaded', $row->options))) {
                    $row->cells[0]->backgroundColor = COLOR_LIGHT_GREEN;
                }
            }

            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset(self::$paths['templates'] . '/SEK_table.xml');
            $viewer->data['table_xml'] = self::_convertSerializedTableToXML($table_object, $properties);
            $xml = $viewer->fetch();
        }

        return $xml;
    }

    /**
     * Replaces hyperlinks
     *
     * @param string $snippet
     * @param boolean $isSupervision
     * @param Extender $extender
     * @return string|string[]
     */
    private static function _manageAlvisHyperlinks($snippet, $isSupervision, Extender $extender)
    {
        //get the hyperlinks
        preg_match_all('#<w:hyperlink[^>]*r:id="rId([0-9]*)"[^>]*>(.+?)</w:hyperlink>#', $snippet, $hyperlink_matches);
        $hyperlinks = array();
        foreach ($hyperlink_matches[0] as $midx => $notimportant) {
            $rId = $hyperlink_matches[1][$midx];
            $outerHyperlink = $hyperlink_matches[0][$midx];
            $innerHyperlink = $hyperlink_matches[2][$midx];
            $relationShip = self::$templateDocx->getReleationship($rId);
            $hyperlinks[$rId] = array(
                'rId' => $rId,
                'outer' => $outerHyperlink,
                'inner' => $innerHyperlink,
                'target' => $relationShip['Target'] ?? null,
                'type' => $relationShip['Type'] ?? null,
            );
        }
        if (!$isSupervision) {
            //IMPORTANT: remove only the hyperlinks pointing to ALVIS
            foreach ($hyperlinks as $hyperlink) {
                if (preg_match('#alvis_url#', $hyperlink['target'])) {
                    $snippet = str_replace(
                        $hyperlink['outer'],
                        str_replace('<w:rStyle w:val="Hyperlink"/>', '', $hyperlink['inner']),
                        $snippet
                    );
                }
            }
        } else {
            foreach ($hyperlinks as $rId => $hyperlink) {
                //IMPORTANT: replace only the hyperlinks pointing to ALVIS
                if (preg_match('#alvis_url#', $hyperlink['target'])) {
                    $replaced_target = $extender->expand(rawurldecode($hyperlink['target']), false);
                    $rIDNew = self::$templateDocx->addHyperlink($replaced_target);
                    $snippet = str_replace(
                        'r:id="rId' . $rId . '"',
                        'r:id="rId' . $rIDNew . '"',
                        $snippet
                    );
                }
            }
        }

        return $snippet;
    }

    /**
     * Finds a tag in a string and returns the contents of it, without the tag itself
     * the tag should look like this '[start_tagName]something something[end_tagName]'
     *
     * @param string $tag the tagName
     * @param string|null $haystack the string in which to search. If omitted the function will search in self::$templateDocx->_documentXML
     * @return false|string
     *
     * @see _replaceDocumentXMLSnippet()
     * <AUTHOR>
     */
    private static function _getDocumentXMLSnippet(string $tag, ?string $haystack = null)
    {
        if (is_null($haystack)) {
            $haystack = self::$templateDocx->_documentXML;
        }

        $startTag = "[start_{$tag}]";
        $startPos = strpos($haystack, $startTag);
        if ($startPos === false) {
            return '';
        }
        $startPos += strlen($startTag);

        $endTag = "[end_{$tag}]";
        $endPos = strpos($haystack, $endTag, $startPos);
        if ($endPos === false) {
            return '';
        }
        return substr($haystack, $startPos, $endPos - $startPos);
    }

    /**
     * Replaces the string between start/end tags with a 'replacement' string
     * The tags themselves are removed too
     * The tags should look like '[start_tagName]something something[end_tagName]'
     *
     * @param string $tag the name of the tag
     * @param string|array $replacement a replacement string, or an array that will be merged before the replacement.
     * @param string|null $haystack the string in which to search. If omitted the function will search in self::$templateDocx->_documentXML
     * @return string the resulting string with the replacement
     *
     * @see _getDocumentXMLSnippet();
     * <AUTHOR>
     */
    private static function _replaceDocumentXMLSnippet(string $tag, $replacement, string $haystack = null)
    {
        if (is_array($replacement)) {
            //$replacement = implode('<w:p><w:r><w:t></w:t></w:r></w:p>', $replacement);
            $replacement = implode('', $replacement);
        }

        $startTag = "[start_{$tag}]";
        $startPos = strpos(is_null($haystack) ? self::$templateDocx->_documentXML : $haystack, $startTag);
        if ($startPos === false) {
            return $haystack;
        }

        $endTag = "[end_{$tag}]";
        $endPos = strpos(is_null($haystack) ? self::$templateDocx->_documentXML : $haystack, $endTag, $startPos);
        if ($endPos === false) {
            return $haystack;
        }
        $endPos += strlen($endTag);

        if (!is_null($haystack)) {
            return substr($haystack, 0, $startPos)
                . $replacement
                . substr($haystack, $endPos);
        }

        self::$templateDocx->_documentXML = substr(self::$templateDocx->_documentXML, 0, $startPos)
            . $replacement
            . substr(self::$templateDocx->_documentXML, $endPos);
    }

    /*
     * @todo move $title and $bg_color into $properties
     */
    /**
     * @param $snippet
     * @param $title
     * @param $bg_color
     * @param array $properties
     * @return array|string|string[]
     */
    private static function _addDocumentXMLTableHeader($snippet, $title, $bg_color, array $properties = array())
    {
        //get the number of columns, used in the span
        preg_match_all('#<w:gridCol#', $snippet, $matches);
        if (!empty($matches)) {
            $colspan = count($matches[0]);
        }

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/table_header.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['title'] = $title;
        $viewer->data['bg_color'] = $bg_color;
        $viewer->data['colspan'] = $colspan;
        $viewer->data['keepnext'] = (array_key_exists('keepnext', $properties) && $properties['keepnext'] === true);

        $header = $viewer->fetch();
        $snippet = str_replace(
            '</w:tblGrid>',
            '</w:tblGrid>' . $header,
            $snippet
        );

        return $snippet;
    }

    /**
     * @return void
     */
    private static function _removeRepeatingParagraphs()
    {
        $subpattern_inner = '(</?w:(pPr|rPr|spacing|jc|lang|i|color|rFonts|br|bookmarkStart|bookmarkEnd)\s?[^>]*?>)*';
        $subpattern_wp = "<w:p(\s[^>]*?>|>){$subpattern_inner}(?:<w:r\s*[^>]*?>{$subpattern_inner}<w:t\s*[^>]*?>[\s\r\n]*</w:t>{$subpattern_inner}</w:r>)*{$subpattern_inner}</w:p>";

        self::$templateDocx->_documentXML = preg_replace(
            "#({$subpattern_wp})({$subpattern_wp})+#",
            '$1',
            self::$templateDocx->_documentXML
        );
    }

    /**
     * Draws a character with wingdings font of square or crossed square
     * @param bool $cross
     * @return mixed|null|string|string[]|void
     */
    private static function _drawSquare(bool $cross)
    {
        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/square_cross.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['cross'] = $cross;

        return $viewer->fetch();
    }

    /**
     * Number format the value of a var, depending on it's js filter and source
     *
     * @param string $value the var value tobe formatted
     * @param string $js_filter the js_filter settings of the var
     * @param string|array $source the source array or the custom_class of the var
     * @return string the formatted value of the var
     */
    private static function _decorateVarValueNumberFormat(string $value, string $js_filter, $source): string
    {
        $precision = 2;
        if (is_array($source)) {
            $custom_class = $source['custom_class'] ?? '';
        } else {
            $custom_class = $source;
        }

        if (strpos($custom_class, 'alvis-format-number') !== false) {
            if (is_string($source)) {
                $source = General::parseSettings($source);
            }
            $precision = $source['alvis_number_round'] ?? '2';
        } elseif ($js_filter == 'insertOnlyFloats' && strpos($custom_class, 'alvis-format-floats') !== false) {
            $precision = PRINT_PRECISION;
        } elseif ($js_filter == 'insertOnlyDigits' && strpos($custom_class, 'alvis-format-unprecise') !== false) {
            $precision = PRINT_PRECISION_OUT;
        }
        return print_alvis_format_number($value, $precision);
    }

    /**
     * Insert line break tag before every new line of a given string
     *
     * @param string $string - the string tobe edited
     *
     * @return string - the string with added line breaks
     */
    private static function _decorateStringNl2Br(string $string): string
    {
        return preg_replace('/(\r\n|\n|\r)/', '<w:br/>$1', $string);
    }

    /**
     * Process assoc template vars
     *
     * @param array $vars - getVarsForTemplateAssoc
     *
     * @return array - the processed vars
     */
    private static function _decorateVars(array $vars): array
    {
        foreach ($vars as $key => $var) {
            if ($var['type'] == 'grouping') {
                $var['labels'] = self::_removeLabelsIndexes($var['labels']);

                if (!empty($var['values'])) {
                    foreach ($var['values'] as $row_index => $row) {
                        foreach ($row as $subvar_index => $subvar_value) {
                            if (gettype($subvar_value) === 'string') {
                                if ($var['types'][$subvar_index] == 'text') {
                                    if (!empty($var['validate'][$subvar_index])) {
                                        // Number format
                                        $subvar_validate = General::parseSettings($var['validate'][$subvar_index]);
                                        if (!empty($subvar_validate['js_filter']) && !empty($var['custom_class'][$subvar_index])) {
                                            $var['values'][$row_index][$subvar_index] = self::_decorateVarValueNumberFormat($subvar_value, $subvar_validate['js_filter'], $var['custom_class'][$subvar_index]);
                                        }
                                    }
                                } elseif ($var['types'][$subvar_index] == 'textarea') {
                                    // Add tag <w:br/> before every new line in textareas
                                    $var['values'][$row_index][$subvar_index] = self::_decorateStringNl2Br($subvar_value);
                                }
                            }
                        }
                    }
                }
            } elseif ($var['type'] == 'table') {
                if (isset($var['labels'])) {
                    $var['labels'] = self::_removeLabelsIndexes($var['labels']);
                }

                if (!empty($var['values'])) {
                    foreach ($var['values'] as $subvar_index => $subvar_value) {
                        if (gettype($subvar_value) === 'string') {
                            $subvar = $var[$var['names'][$subvar_index]];
                            if ($subvar['type'] == 'text') {
                                if (!empty($subvar['validate'])) {
                                    // Number format
                                    $subvar_validate = General::parseSettings($subvar['validate']);
                                    $source = General::parseSettings($subvar['source']);
                                    if (!empty($subvar_validate['js_filter']) && !empty($source['custom_class'])) {
                                        $var['values'][$subvar_index] = self::_decorateVarValueNumberFormat($subvar_value, $subvar_validate['js_filter'], $source);
                                    }
                                }
                            } elseif ($subvar['type'] == 'textarea') {
                                // Add tag <w:br/> before every new line in textareas
                                $var['values'][$subvar_index] = self::_decorateStringNl2Br($subvar_value);
                            }
                        }
                    }
                }

                foreach ($var['names'] ?? [] as $subvar_name) {
                    if (array_key_exists('label', $var[$subvar_name])) {
                        $var[$subvar_name]['label'] = self::_removeLabelsIndexes([$var[$subvar_name]['label']])[0];
                    }
                    if (array_key_exists('value', $var[$subvar_name]) && gettype($var[$subvar_name]['value']) == 'string') {
                        if ($var[$subvar_name]['type'] == 'text') {
                            if (!empty($var[$subvar_name]['validate'])) {
                                // Number format
                                $subvar_validate = General::parseSettings($var[$subvar_name]['validate']);
                                $source = General::parseSettings($var[$subvar_name]);
                                if (!empty($subvar_validate['js_filter']) && !empty($source['custom_class'])) {
                                    $var[$subvar_name]['value'] = self::_decorateVarValueNumberFormat($var[$subvar_name]['value'], $subvar_validate['js_filter'], $source);
                                }
                            }
                        } elseif ($var[$subvar_name]['type'] == 'textarea') {
                            // Add tag <w:br/> before every new line in textareas
                            $var[$subvar_name]['value'] = self::_decorateStringNl2Br($var[$subvar_name]['value']);
                        }
                    }
                }
            } else {
                if (isset($var['label'])) {
                    $var['label'] = self::_removeLabelsIndexes(array($var['label']))[0];
                }

                if (array_key_exists('value', $var) && gettype($var['value']) === 'string') {
                    // Add tag <w:br/> before every new line in textareas
                    if ($var['type'] == 'textarea') {
                        $var['value'] = self::_decorateStringNl2Br($var['value']);
                    } elseif ($var['type'] == 'text') {
                        if (!empty($var['validate'])) {
                            // Number format
                            $validate = General::parseSettings($var['validate']);
                            $source = General::parseSettings($var['source']);
                            if (array_key_exists('js_filter', $validate) && is_string($validate['js_filter']) && array_key_exists('custom_class', $source)) {
                                $var['value'] = self::_decorateVarValueNumberFormat($var['value'], $validate['js_filter'], $source);
                            }
                        }
                    }
                }
            }

            $vars[$key] = $var;
        }

        // Prepare the serialized data
        if (!empty($vars['serial_data']['value'])) {
            // Decode the data
            $vars['serial_data']['value'] = json_decode($vars['serial_data']['value']);

            // Process serialized data
            $serialized_vars_assoc = array();
            foreach ($vars['serial_data']['value'] as $var) {
                if (isset($var->type) && $var->type == 'table') {
                    foreach ($var->rows as $row_index => $row) {
                        // Default row styles
                        if (property_exists($row, 'type') && $row->type == 'head') {
                            $row->backgroundColor = COLOR_DARK_GREEN;
                            $row->hAlign = 'center';
                            $row->bold = true;
                        }
                        if (property_exists($row, 'options')) {
                            if (in_array('shaded', $row->options)) {
                                $row->backgroundColor = COLOR_DARK_MIDDLE;
                            } elseif (in_array('highlight', $row->options)) {
                                $row->backgroundColor = COLOR_LIGHT_GREEN;
                            }
                        }

                        foreach ($row->cells as $cell_index => $cell) {
                            // Normalize tables
                            if (property_exists($cell, 'rowspan') && $cell->rowspan > 1) {
                                for ($i = 2; $i <= $cell->rowspan; $i++) {
                                    $new_rowspan_cell_row_index = $row_index + $i - 1;
                                    $new_rowspan_cell = new stdClass();
                                    $new_rowspan_cell->type = 'rowspan_continue';
                                    array_splice($var->rows[$new_rowspan_cell_row_index]->cells, $cell_index, 0, array($new_rowspan_cell));
                                }
                            }

                            // Default cell styles
                            if (property_exists($cell, 'options')) {
                                if (in_array('shaded', $cell->options)) {
                                    $cell->backgroundColor = COLOR_DARK_MIDDLE;
                                } elseif (in_array('highlight', $cell->options)) {
                                    $cell->backgroundColor = COLOR_LIGHT_GREEN;
                                }
                                if (in_array('number', $cell->options)) {
                                    $cell->hAlign = 'right';
                                }
                            }
                        }
                    }
                    $serialized_vars_assoc[$var->name] = $var;
                }
            }
            $vars['serial_data']['value'] = $serialized_vars_assoc;
        }

        return $vars;
    }

    /**
     * @param array $labels
     * @return array
     */
    private static function _removeLabelsIndexes(array $labels): array
    {
        $labels = array_map(
            function ($label) {
                //return preg_replace('/^[^\s]+\d\s+(.+)$/', '$1', $label);
                // This is aimed to remove strings like МА2.12, A1.3, etc. from the begining of all strings.
                // It should be as strict as posible, as this is applyed to many string including numbers and values
                return preg_replace('/^[^\s\d\.\-_]{1,2}.?\d+[^\s\d]?\d*\s+(.+)$/', '$1', $label);
            },
            $labels
        );
        return $labels;
    }

    /**
     * Prepare a grouping table
     *
     * @param $var - the table var prepared for template
     * @param array $grid - table grid widths, if omitted, all columns will be with same width
     * @param string $title - main title for the table, if omitted, there will be no title
     *
     * @return string - the table XML or empty
     */
    private static function _prepareGroupingTable($var, $grid = array(), $title = '')
    {
        $table_xml = '';
        if (!empty($var['values'])) {
            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/grouping_table.xml';
            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset($template_file);
            $viewer->data['var'] = $var;
            if (empty($grid)) {
                $columns_total_width = self::$tablesWidthFullPage;
                if (empty($var['hide_row_numbers'])) {
                    $rows_numbers_column_width = 300;
                    $columns_total_width -= $rows_numbers_column_width;
                    $grid = array($rows_numbers_column_width);
                }
                $visible_columns_count = count(array_filter($var['hidden'], function ($val) {
                    return $val == 0;
                }));
                $grid = array_merge($grid, array_fill(0, $visible_columns_count, intval($columns_total_width / $visible_columns_count)));
            }
            $viewer->data['grid'] = $grid;
            $viewer->data['title'] = $title;
            $viewer->data['paths'] = self::$paths;
            $table_xml = $viewer->fetch();
        }
        return $table_xml;
    }

    /**
     * @param $object
     * @return string
     */
    private static function _prepareRoomsTable($object)
    {
        $table_xml = '';
        $vars = $object->getVarsForTemplateAssoc();
        if (array_key_exists('econ_group_table', $vars) && !empty($vars['econ_group_table']['values'])) {
            $var = $vars['econ_group_table'];
            $table_xml = self::_prepareGroupingTable($var, [], self::i18n('econ_group_table_header'));
        }
        return $table_xml;
    }

    /**
     * @param $object
     * @return array|string|string[]|null
     */
    private static function _prepareAreaByApproachTable($object)
    {
        $table_xml = '';
        $vars = $object->getVarsForTemplateAssoc();

        if (array_key_exists('area_appr_group_table', $vars) && !empty($vars['area_appr_group_table']['values'])) {
            $vars = self::_decorateVars([
                'area_appr_group_table' => $vars['area_appr_group_table'],
                'total_area_market' => $vars['total_area_market'],
                'total_area_income' => $vars['total_area_income'],
                'total_area_expense' => $vars['total_area_expense'],
                'area_appr_comment' => $vars['area_appr_comment'],
            ]);
            $var = $vars['area_appr_group_table'];
            $var['label'] = self::i18n('area_appr_group_table_header');
            $var['headers'] = [
                ['label' => $vars['object_name']['value'] ?? '', 'colspan' => 1, 'fontsize' => '16'],
                ['label' => self::i18n('area_appr_group_table_header_area'), 'colspan' => 2, 'fontsize' => '16'],
                ['label' => self::i18n('area_appr_group_table_header_market'), 'colspan' => 3, 'fontsize' => '16'],
                ['label' => self::i18n('area_appr_group_table_header_income'), 'colspan' => 3, 'fontsize' => '16'],
                ['label' => self::i18n('area_appr_group_table_header_cost'), 'colspan' => 3, 'fontsize' => '16'],
            ];
            $tmlNames = array_flip($var['names']);
            $var['hidden'][$tmlNames['level_appr']] = '1';
            foreach ($var['labels'] as $k => $v) {
                if (($brackPos = strpos($var['labels'][$k], '(')) !== false) {
                    $var['labels'][$k] = trim(substr($v, 0, $brackPos));
                }
            }

            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/area_appr_group_table.xml';
            $viewer = new Viewer(self::$registry);
            $viewer->setFrameset($template_file);
            $viewer->data['paths'] = self::$paths;
            $viewer->data['var'] = $var;

            $firstColWidth = 1440;
            $columns_total_width = self::$tablesWidthFullPage - $firstColWidth;
            $visible_columns_count = 12;
            $grid = array_fill(0, $visible_columns_count, intval($columns_total_width / ($visible_columns_count - 1)));
            $grid[0] = $firstColWidth;
            $viewer->data['header_fontsize'] = 16;
            $viewer->data['grid'] = $grid;
            $viewer->data['title'] = self::i18n('area_appr_group_table_header');
            $viewer->data['footer'] = [
                ['value' => self::i18n('area_appr_group_table_footer'), 'colspan' => 3, 'bold' => true, 'text_align' => 'right', 'fontsize' => '16'],
                ['value' => '', 'colspan' => 1],
                ['value' => '', 'colspan' => 1],
                ['value' => $vars['total_area_market']['value'], 'colspan' => 1, 'bold' => true, 'text_align' => 'right'],
                ['value' => self::i18n('area_appr_group_table_footer_rentarea'), 'colspan' => 2, 'bold' => true, 'text_align' => 'right', 'fontsize' => '16'],
                ['value' => $vars['total_area_income']['value'], 'colspan' => 1, 'bold' => true, 'text_align' => 'right'],
                ['value' => '', 'colspan' => 1],
                ['value' => '', 'colspan' => 1],
                ['value' => $vars['total_area_expense']['value'], 'colspan' => 1, 'bold' => true, 'text_align' => 'right'],
            ];
            $viewer->data['area_appr_comment'] = $vars['area_appr_comment']['value'];
            $table_xml = $viewer->fetch();
        }
        return $table_xml;
    }

    /**
     * @param $object
     * @return string
     */
    private static function _prepareAreaByApproachBuildingsTable($object)
    {

        $vars = $object->getVarsForTemplateAssoc();
        $tableName = 'aggregated_areas_group_table';

        if (!array_key_exists($tableName, $vars) || empty($vars[$tableName]['values'])) {
            return '';
        }

        include_once realpath(dirname(__FILE__) . '/DocxTable.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableRow.php');
        include_once realpath(dirname(__FILE__) . '/DocxTableCell.php');

        $vars = self::_decorateVars([
            'object_name' => $vars['object_name'],
            $tableName => $vars[$tableName],
            'total_aggregated_zp_accepted_area' => $vars['total_aggregated_zp_accepted_area'],
            'total_aggregated_rzp_accepted_area' => $vars['total_aggregated_rzp_accepted_area'],
            'total_aggregated_market_approach_value' => $vars['total_aggregated_market_approach_value'],
            'total_aggregated_income_approach_value' => $vars['total_aggregated_income_approach_value'],
            'total_aggregated_spending_approach_value' => $vars['total_aggregated_spending_approach_value'],
            'aggregated_area_comment' => $vars['aggregated_area_comment'],
        ]);
        $areaTable = $vars[$tableName];

        $firstColWidth = 1600;
        $columns_total_width = self::$tablesWidthFullPage - $firstColWidth;
        $visible_columns_count = 8;
        $grid = array_fill(0, $visible_columns_count, intval($columns_total_width / ($visible_columns_count - 1)));
        $grid[0] = $firstColWidth;

        $tbl = new DocxTable(self::i18n('area_appr_group_table_header'), $grid);

        $tbl->addRow($head0 = new DocxTableRow(true, true));
        $head0->addCell($comH = new DocxTableCell(self::i18n('area_appr_group_table_header'), 8, 'center'));
        $comH->setBackgroundColor(COLOR_DARK_GREEN);
        $comH->setBold(true);

        $head1 = new DocxTableRow(true, true);
        $tbl->addRow($head1);
        $head1->addCell($c11 = new DocxTableCell($vars['object_name']['value'] ?? '', 1, 'center'));
        $c11->setBackgroundColor(COLOR_DARK_GREEN);
        $c11->setBold(true);
        $head1->addCell($c12 = new DocxTableCell(self::i18n('area_appr_group_table_header_area'), 1, 'center'));
        $c12->setBackgroundColor(COLOR_DARK_GREEN);
        $c12->setBold(true);
        $head1->addCell($c13 = new DocxTableCell(self::i18n('area_appr_group_table_header_market'), 2, 'center'));
        $c13->setBackgroundColor(COLOR_DARK_GREEN);
        $c13->setBold(true);
        $head1->addCell($c14 = new DocxTableCell(self::i18n('area_appr_group_table_header_income'), 2, 'center'));
        $c14->setBackgroundColor(COLOR_DARK_GREEN);
        $c14->setBold(true);
        $head1->addCell($c15 = new DocxTableCell(self::i18n('area_appr_group_table_header_cost'), 2, 'center'));
        $c15->setBackgroundColor(COLOR_DARK_GREEN);
        $c15->setBold(true);
        $head2 = new DocxTableRow(true, true);
        $tbl->addRow($head2);

        $tblNames = array_flip($areaTable['names']);
        $areaTable['hidden'][$tblNames['addition_object_aggregated_level']] = '1';
        $areaTable['hidden'][$tblNames['aggregated_level_group']] = '1';

        foreach ($areaTable['labels'] ?? [] as $k => $v) {
            if ($areaTable['hidden'][$k] == '1') {
                continue;
            }
            if (($brackPos = strpos($v, '(')) !== false) {
                $v = trim(substr($v, 0, $brackPos));
            }
            $head2->addCell($c21 = new DocxTableCell($v, 1, $k == 1 ? 'left' : 'center'));
            $c21->setBackgroundColor(COLOR_LIGHT_GREEN);
            $c21->setBold(true);
        }

        foreach ($areaTable['values'] ?? [] as $k => $v) {
            $tbl->addRow($body = new DocxTableRow(false, true));
            foreach ($v ?? [] as $k1 => $v1) {
                if ($areaTable['hidden'][$k1] == '1') {
                    continue;
                }
                $align = $tblNames['aggregated_zp_accepted_area'] >= $k1 ? 'right' : 'left';
                $body->addCell($cb = new DocxTableCell($v1, 1, $align));
            }
        }

        $footer1 = new DocxTableRow(false, true);
        $tbl->addRow($footer1);
        $footer1->addCell($cf1 = new DocxTableCell(self::i18n('aggregated_areas_group_table_footer1'), 1, 'right'));
        $cf1->setBackgroundColor(COLOR_DARK_GREEN);
        $cf1->setBold(true);
        $footer1->addCell($cf2 = new DocxTableCell($vars['total_aggregated_zp_accepted_area']['value'], 1, 'right'));
        $cf2->setBackgroundColor(COLOR_DARK_GREEN);
        $cf2->setBold(true);
        $footer1->addCell($cf3 = new DocxTableCell('', 6));
        $cf3->setBackgroundColor(COLOR_DARK_GREEN);

        $footer2 = new DocxTableRow(false, true);
        $tbl->addRow($footer2);
        $footer2->addCell($cf21 = new DocxTableCell(self::i18n('aggregated_areas_group_table_footer2'), 1, 'right'));
        $cf21->setBackgroundColor(COLOR_DARK_GREEN);
        $cf21->setBold(true);
        $footer2->addCell($cf22 = new DocxTableCell($vars['total_aggregated_rzp_accepted_area']['value'], 1, 'right'));
        $cf22->setBackgroundColor(COLOR_DARK_GREEN);
        $cf22->setBold(true);
        $footer2->addCell($cf23 = new DocxTableCell('', 1));
        $cf23->setBackgroundColor(COLOR_DARK_GREEN);
        $footer2->addCell($cf24 = new DocxTableCell($vars['total_aggregated_market_approach_value']['value'], 1, 'right'));
        $cf24->setBackgroundColor(COLOR_DARK_GREEN);
        $cf24->setBold(true);
        $footer2->addCell($cf25 = new DocxTableCell(self::i18n('area_appr_group_table_footer_rentarea'), 1, 'right'));
        $cf25->setBackgroundColor(COLOR_DARK_GREEN);
        $cf25->setBold(true);
        $footer2->addCell($cf26 = new DocxTableCell($vars['total_aggregated_income_approach_value']['value'], 1, 'right'));
        $cf26->setBackgroundColor(COLOR_DARK_GREEN);
        $cf26->setBold(true);
        $footer2->addCell($cf27 = new DocxTableCell('', 1));
        $cf27->setBackgroundColor(COLOR_DARK_GREEN);
        $footer2->addCell($cf28 = new DocxTableCell($vars['total_aggregated_spending_approach_value']['value'], 1, 'right'));
        $cf28->setBackgroundColor(COLOR_DARK_GREEN);
        $cf28->setBold(true);

        if (!empty(trim($vars['aggregated_area_comment']['value']))) {
            $tbl->addRow($commentHeader = new DocxTableRow(true, true));
            $commentHeader->addCell($comH = new DocxTableCell('КОМЕНТАР', 8, 'center'));
            $comH->setBackgroundColor(COLOR_DARK_GREEN);
            $comH->setBold(true);

            $tbl->addRow($commentBody = new DocxTableRow(true, true));
            $commentBody->addCell($comB = new DocxTableCell($vars['aggregated_area_comment']['value'], 8, 'left'));
        }

        $template_file = realpath(dirname(__FILE__) . '/../templates') . '/_table.xml';
        $viewer = new Viewer(self::$registry);
        $viewer->setFrameset($template_file);
        $viewer->data['paths'] = self::$paths;
        $viewer->data['table'] = $tbl;

        return '</w:t></w:r></w:p>' . $viewer->fetch() . '<w:p><w:r><w:t>';
    }

    /**
     * @param $OBJECT_doc
     * @param $pattern_vars
     * @param $extender
     * @param $hide_header_row
     * @return void
     */
    private static function _prepareAreasTable($OBJECT_doc, &$pattern_vars, $extender, $hide_header_row = false)
    {
        if (isset($pattern_vars['a_area_group_table'])) {
            //special transformation of this table
            $OBJECT_vars = $OBJECT_doc->get('vars');
            $var_idx = array_flip(array_combine(array_keys($OBJECT_vars), array_column($OBJECT_doc->get('vars'), 'name')));
            $area_group_table = $OBJECT_vars[$var_idx['area_group_table']];
            $area_group_table['values'] = $area_group_table['values'] ?? [];
            $area_group_table_names = array_flip($area_group_table['names']);
            //hide the number column
            if (array_key_exists('level_group', $area_group_table_names)) {
                $area_group_table['hidden'][$area_group_table_names['level_group']] = 1;
            }
            if (array_key_exists('level_name_group', $area_group_table_names)) {
                $area_group_table['hidden'][$area_group_table_names['level_name_group']] = 0;
            }
            $explicitly_visible = array();
            foreach ($area_group_table['names'] as $idx => $name) {
                if ((preg_match('#^zp_(.*)$#', $name, $matches) || $name == 'level_name_group') && $area_group_table['hidden'][$idx] == 0) {
                    $column1 = array_filter(array_column($area_group_table['values'], $idx));
                    // level_name_group has no second column
                    $col2_name = $name == 'level_name_group' ? '' : 'ochs_' . $matches[1];

                    if (array_key_exists($col2_name, $area_group_table_names)) {
                        $column2 = array_filter(array_column($area_group_table['values'], $area_group_table_names[$col2_name]));
                    }

                    if (!empty($column1) || !empty($column2)) {
                        $area_group_table['headers'][$idx] = array(
                            'label' => $extender->expand(self::i18n('area_group_table_header_' . $name)),
                            'colspan' => (empty($column2) ? 1 : 2),
                        );
                        $custom_label = self::i18n('area_group_table_' . $name);
                        if ($custom_label) {
                            $area_group_table['labels'][$idx] = $custom_label;
                        }

                        // level_name_group has no second column, so this is done only when there is s second column
                        if (!empty($column2)) {
                            $area_group_table['hidden'][$area_group_table_names[$col2_name]] = 0;
                            $explicitly_visible[] = $area_group_table_names[$col2_name];
                            $custom_label2 = self::i18n('area_group_table_' . $col2_name);
                            if ($custom_label2) {
                                $area_group_table['labels'][$area_group_table_names[$col2_name]] = $custom_label2;
                            }
                        }
                    } else {
                        $area_group_table['hidden'][$idx] = 1;
                        if (array_key_exists($col2_name, $area_group_table_names)) {
                            $area_group_table['hidden'][$area_group_table_names[$col2_name]] = 1;
                        }
                    }
                } elseif (!in_array($idx, $explicitly_visible)) {
                    $area_group_table['hidden'][$idx] = 1;
                }
            }

            $template_file = realpath(dirname(__FILE__) . '/../templates') . '/area_group_table.xml';
            $groupingViewer = new Viewer(self::$registry);
            $groupingViewer->setFrameset($template_file);
            $groupingViewer->data['hide_header_row'] = $hide_header_row;
            $groupingViewer->data['var'] = $area_group_table;
            $visible_columns_count = count(array_filter($area_group_table['hidden'], function ($val) {
                return $val == 0;
            }));
            $grid_width = $visible_columns_count === 0 ? 0 : intval(self::$tablesWidthFullPage / $visible_columns_count);
            $groupingViewer->data['grid'] = array();
            foreach ($area_group_table['hidden'] as $hidden) {
                if (!$hidden) {
                    $groupingViewer->data['grid'][] = $grid_width;
                }
            }
            if ($visible_columns_count > 1) {
                array_pop($groupingViewer->data['grid']);
                $groupingViewer->data['grid'][] = self::$tablesWidthFullPage - array_sum($groupingViewer->data['grid']);
            }
            $pattern_vars['a_area_group_table'] = $groupingViewer->fetch();

            //add table header/title
            $pattern_vars['a_area_group_table'] = self::_addDocumentXMLTableHeader(
                $pattern_vars['a_area_group_table'],
                self::i18n('area_group_table_header'),
                COLOR_DARK_GREEN,
                ['keepnext' => true]
            );
        }
    }

    /**
     * @param string $input
     * @param string $trimStr
     * @return string
     */
    private static function trimStr(string $input, string $trimStr = '<w:br/>'): string
    {
        $temp = trim($input);
        $trimStrLen = strlen($trimStr);
        while (strpos($temp, $trimStr) === 0) {
            $temp = trim(substr($temp, $trimStrLen));
        }
        while (strrpos($temp, $trimStr) + $trimStrLen === strlen($temp)) {
            $temp = trim(substr($temp, 0, -$trimStrLen));
        }
        return $temp;
    }

    /**
     * @param $value
     * @param $settings
     * @return mixed|string
     */
    private static function getRatingTypeLabel($value, $settings)
    {
        if (!is_null(self::$rating_type)) {
            return self::$rating_type;
        }

        switch ($value) {
            case $settings['nom_type_of_service_update']:
                $rating_type = sprintf(
                    self::i18n('rating_type_update'),
                    self::$model->extender->placeholders['a_rating_type'],
                    self::$model->extender->placeholders['a_previous_report_name']
                );
                break;
            case $settings['nom_type_of_service_followup'] ?? '':
                $rating_type = sprintf(
                    self::i18n('rating_type_followup'),
                    self::$model->extender->placeholders['a_rating_type'],
                    self::$model->extender->placeholders['a_previous_report_name']
                );
                break;
            case $settings['nom_type_of_service_diffbank'] ?? '':
                $rating_type = sprintf(
                    self::i18n('rating_type_diffbank'),
                    self::$model->extender->placeholders['a_rating_type'],
                    self::$model->extender->placeholders['a_previous_report_name']
                );
                break;
            default:
                $rating_type = self::$model->extender->placeholders['a_rating_type'];
        }

        return self::$rating_type = $rating_type;
    }

    /**
     * Display incompleteness_note table if has value and incompleteness_coef is not empty
     *
     * @param array $buildings_ids
     * @param array $assessmentObjects
     *
     * @return string
     */
    public static function _prepareIncompletenessTables(array $buildings_ids, array $assessmentObjects): string
    {
        $incompleteness_note = '';
        foreach ($buildings_ids as $building_id) {
            $incompletenessInfoValue = $assessmentObjects[$building_id]->getAssocVars()['incompleteness_note']['value'];
            $incompletenessCoefValue = $assessmentObjects[$building_id]->getAssocVars()['incompleteness_coef']['value'];

            if (!empty($incompletenessInfoValue) && !empty($incompletenessCoefValue)) {
                $incompleteness_note .= self::_prepareSimpleTable('ПОЯСНЕНИЕ ЗА НЕЗАВЪРШЕНОСТ', $incompletenessInfoValue);
            }
        }
        return $incompleteness_note;
    }

    /**
     * Risk note table
     *
     * @param array $buildings_ids
     * @param array $assessmentObjects
     * @return string
     */
    private static function _prepareRiskNoteTable(array $buildings_ids, array $assessmentObjects): string
    {
        $riskNote = '';
        foreach ($buildings_ids as $building_id) {
            $riskNoteValue = $assessmentObjects[$building_id]->getAssocVars()['risk_note']['value'];
            $riskCoefValue = $assessmentObjects[$building_id]->getAssocVars()['risk_coef']['value'];

            if (!empty($riskNoteValue) && !empty($riskCoefValue)) {
                $riskNote .= self::_prepareSimpleTable('ПОЯСНЕНИЕ ЗА РИСК ПРИ НЕЗАВЪРШЕНОСТ', $riskNoteValue);
            }
        }
        return $riskNote;
    }

    /**
     * @param array $varArray
     * @return mixed|string
     */
    protected static function findOptinLabel(array $varArray)
    {
        foreach ($varArray['options'] as $option) {
            if ($varArray['value'] == $option['option_value']) {
                return $option['label'];
            }
        }
        return '';
    }

    /**
     * @param $objectId
     * @return mixed|null
     */
    protected static function getObjectParent($objectId)
    {
        if (is_null(self::$hierarchy_group_parents)) {
            $reportVars = self::getModelVars();
            if (is_null(self::$hierarchy_group_idx)) {
                self::$hierarchy_group_idx = array_flip($reportVars['hierarchy_document_relat_grp']['names']);
            }

            self::$hierarchy_group_parents = [];
            foreach (($reportVars['hierarchy_document_relat_grp']['values'] ?? []) as $hdrg_row) {
                self::$hierarchy_group_parents[$hdrg_row[self::$hierarchy_group_idx['hierarchy_document_relat_id']]]
                    = $hdrg_row[self::$hierarchy_group_idx['hierarchy_document_parent_id']];
            }
        }

        return self::$hierarchy_group_parents[$objectId] ?? null;
    }

    /**
     * @return DocxTableViewer
     */
    private static function getTableViewer(): DocxTableViewer
    {
        if (is_null(self::$tblViewer)) {
            self::$tblViewer = new \DocxTableViewer(self::$registry, self::$paths);
        }
        return self::$tblViewer;
    }

    /**
     * @param DocxTable $tbl
     * @return array|string|string[]|null
     */
    private static function renderTableXml(DocxTable $tbl)
    {
        $viewer = self::getTableViewer();
        return $viewer->renderTableXml($tbl);
    }
}

/**
 * Formats a number in accordance with the alvis specs
 *
 * @param $number The number to format
 * @param int $precision The precision to witch the number should be formatted. (positive means after the decimal point '123.45', negative means before the decimal point '12 300')
 * @param string $dec_point The decimal point character to be used
 * @param string $thousands_sep The thousands separator character
 * @return string The formatted number as a string
 */
function print_alvis_format_number(
    $number,
    int $precision = 2,
    string $dec_point = '.',
    string $thousands_sep = ' '
): string {
    $number = round(floatval($number), $precision);
    $formatDecimals = $precision < 0 ? 0 : $precision;
    return number_format($number, $formatDecimals, $dec_point, $thousands_sep);
}

// Format numbers (used as modifier into the templates)
/**
 * @param $number
 * @param int $decimals
 * @param string $dec_point
 * @param string $thousands_sep
 * @return string
 */
function print_number_format(
    $number,
    int $decimals = PRINT_PRECISION,
    string $dec_point = '.',
    string $thousands_sep = ' '
): string {
    return print_alvis_format_number($number, $decimals, $dec_point, $thousands_sep);
}

// Format out numbers (used as modifier into the templates)
/**
 * @param $number
 * @param int $round_precision
 * @param int $decimals
 * @param string $dec_point
 * @param string $thousands_sep
 * @return string
 */
function print_numberout_format(
    $number,
    int $round_precision = PRINT_PRECISION_OUT,
    int $decimals = 0,
    string $dec_point = '.',
    string $thousands_sep = ' '
): string {
    $number = floatval($number);
    $number = round($number, $round_precision);
    return number_format($number, $decimals, $dec_point, $thousands_sep);
}
