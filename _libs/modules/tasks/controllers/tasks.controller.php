<?php

require_once PH_MODULES_DIR . 'tasks/models/tasks.history.php';
require_once PH_MODULES_DIR . 'tasks/models/tasks.audit.php';

class Tasks_Controller extends Controller {
    use \Nzoom\Mvc\ControllerTrait\GridBasedListTrait;
    /**
     * Model name of this controller
     */
    public $modelName = 'Task';

    /**
     * Model factory name of this controller
     */
    public $modelFactoryName = 'Tasks';

    /**
     * Action definitions for this controller
     */
    public $actionDefinitions = array(
        'list', 'search', 'myassigned', 'myresponsible', 'myobserver', 'myrecords',
        'adds', 'add', 'multiadd', 'export', 'statements',
        'view', 'edit',
        'create', 'documents', 'events',
        'attachments', 'setstatus', 'assign', 'tag', 'remind',
        'observer', 'manage_outlooks', 'printlist',
        'allocate', 'timesheets', 'dependencies', 'relatives', 'history',
        'comments', 'emails', 'minitasks', 'communications'
    );

    /**
     * Action definitions for the left menu
     */
    public $actionDefinitionsLeft = array(
        'view', 'edit'
    );

    /**
     * Action definitions for the right menu
     */
    public $actionDefinitionsRight = array(
        'view', 'edit', 'allocate', 'timesheets', 'addtimesheet',
        'dependencies', 'relatives', 'history', 'communications'
    );

    public $actionDefinitionsUpRight = array(
        'observer', 'manage_outlooks', 'printlist'
    );

    public $actionFilterDefinitions = array(
        'myassigned', 'myresponsible', 'myobserver', 'myrecords'
    );

    /**
     * After action definitions for this controller
     */
    public $afterActionDefinitions = array(
        'list', 'search', 'add', 'multiadd', 'view', 'edit', 'translate',
        'assign', 'allocate', 'timesheets', 'dependencies', 'relatives'
    );

    /**
     * Actions that require valid login but don't require access to module
     */
    public $permittedActions = array(
        'ajax_fill_configurator_options',
        'ajax_select',
    );

    /**
     * Actions where side panels for model can be displayed
     */
    public static $actionsSidePanel = array('view', 'edit');

    public $actionListPageMenu = [
        /** Actions that serve navigational purpouse and have no direct relation on the current page */
        'general' => [/*'list',*/ 'adds', 'add', 'multiadd', 'create'],

        /** Actions that are conextualy dependant on the page (document opened) */
        'context' => ['view', 'edit'/*, 'setstatus'*/, 'assign', 'communications', 'timesheets', 'addtimesheet', 'relatives', 'history'],

        /** Actions that should be quick to access but unabtrusive to the user */
        'quick' => [/*'observer', */'print', 'remind', 'tag', 'attachments'],

        /** Actions that are not very frequent to use, can be hidden behind a menu */
        'infriquent' => [ /*, 'archive'*/ 'manage_outlooks', 'printlist', 'allocate', 'dependencies'],
    ];

    public static $searchAdditionalVarsSwitch = 'ta.type';

    /**
     * Generic action dispatcher routing
     * according to the requested action
     */
    public function execute() {
        // EXECUTE TEMPORARY METHOD
        //Tasks::insertTasks($this->registry);
        switch ($this->action) {
        case 'adds':
            //takes the add action
            $action = $this->registry['request']->get('operation');
            //checks if the action is valid
            if (preg_match('#^(add|multiadd)$#', $action)) {
                $this->setAction($action);
            } else {
                $this->setAction('view');
            }
            //construct the name of the method
            $method = '_' . $action;
            //calls the method
            $this->$method();
            break;
        case 'add':
        case 'ajax_add':
            $this->_add();
            break;
        case 'multiadd':
            $this->_multiAdd();
            break;
        case 'translate':
            $this->_translate();
            break;
        case 'edit':
        case 'ajax_edit':
            $this->_edit();
            break;
        case 'multiedit':
            $this->_multiEdit();
            break;
        case 'view':
            $this->_view();
            break;
        case 'setstatus':
            $this->_status();
            break;
        case 'multistatus':
            $this->_multiStatus();
            break;
        case 'ajax_status':
            $this->_getStatus();
            break;
        case 'remind':
            $this->_remind();
            break;
        case 'activate':
        case 'deactivate':
            $this->_changeStatus($this->action);
            break;
        case 'delete':
            $this->_delete($this->registry['request'][$this->action]);
            break;
        case 'restore':
            $this->_restore($this->registry['request'][$this->action]);
            break;
        case 'filter':
            $this->_filter();
            break;
        case 'calculate':
            $this->_calculate();
            break;
        case 'insertids':
            $this->_insertIds();
            break;
        case 'subpanel':
            $this->_subpanel();
            break;
        case 'getoptions':
            $this->_getOptions();
            break;
        case 'ajax_getfiles':
            $this->_getFiles();
            break;
        case 'attachments':
            $this->_attachments();
            break;
        case 'delfile':
        case 'getfile':
        case 'viewfile':
            $this->_manageFile();
            break;
        case 'assign':
            $this->_assign();
            break;
        case 'ajax_assign':
            $this->_getAssignments();
            break;
        case 'ajax_get_assign_params':
            $this->_getAssignmentsParams();
            break;
        case 'relatives':
            $this->_relatives();
            break;
        case 'dependencies':
            $this->_dependencies();
            break;
        case 'ajax_configurator':
            $this->_configurator();
            break;
        case 'history':
            $this->_history();
            break;
        case 'audit':
            $this->_audit();
            break;
        case 'communications':
            $this->_communications();
            break;
        case 'myassigned':
            $this->_myassigned();
            break;
        case 'myresponsible':
            $this->_myresponsible();
            break;
        case 'myobserver':
            $this->_myobserver();
            break;
        case 'myrecords':
            $this->_myrecords();
            break;
        case 'observer':
            $this->_observer();
            break;
        case 'create':
            $this->_create();
            break;
        case 'ajax_fill_configurator_options':
            $this->_fillConfiguratorOptions();
            break;
        case 'ajax_show_customers_info':
            $this->_showCustomersInfo();
            break;
        case 'ajax_show_last_records':
            $this->_showLastRecords();
            break;
        case 'ajax_change_assignments_multiadd_records':
            $this->_changeAssignmentsMultiaddRecords();
            break;
        case 'search':
            $this->_search();
            break;
        case 'dashlet':
            $this->_dashlet();
            break;
        case 'ajax_get_totals':
            $this->_getTotals();
            break;
        case 'export':
        case 'printlist':
            $this->_export();
            break;
        case 'ajax_select':
            $this->_select();
            break;
        case 'timesheets':
            $this->_timesheets();
            break;
        case 'statements':
            $this->_statements();
            break;
        case 'ajax_switch_statements_subpanel':
            $this->_switchStatementsSubpanel();
            break;
        case 'ajax_watch':
            $this->_manageStopWatch();
            break;
        case 'allocate':
            $this->_allocate();
            break;
        case 'tag':
            $this->_tag();
            break;
        case 'multitag':
            $this->_multiTag();
            break;
        case 'multiremovetag':
            $this->_multiRemoveTag();
            break;
        case 'ajax_tag':
            $this->_getTags();
            break;
        case 'ajax_sidepanel':
            $this->_sidePanel();
            break;
        case 'button_link_prepare':
            $this->_buttonLinkPrepare();
            break;
        case 'prepare_map':
            $this->_prepareMap();
            break;
        case 'getadvancedsearchoptions':
            $this->_getAdvancedSearchOptions();
            break;
        case 'getListColumnsDefinitions':
            $this->_getListColumnsDefinitions();
            break;
        case 'listIds':
            $this->_listIds();
            break;
        case 'getListTitle':
            $this->_getListTitle();
            break;
        case 'listData':
            $this->_listData();
            break;
        case 'getListMultiActionsPanel':
            $this->_getListMultiActionsPanel('', 'tags,multistatus');
            break;
        case 'getListActions':
            $this->_getListActions();
            break;
        case 'saveFilter':
            $this->_saveFilter();
            break;
        case 'loadFilter':
            $this->_loadFilter();
            break;
        case 'deleteFilter':
            $this->_deleteFilter();
            break;
        case 'list':
        default:
            $this->setAction('list');
            $this->redirectToAddNotFinishedFilter2ListForModernTheme();
            $this->_list();
        }
    }

    /**
     * listing of all models
     */
    private function _listData() {
        /** @var \Request $request */
        $request = $this->registry['request'];

        $accept = $request->getHeader('Accept');
        if (!preg_match("/.*json/i", $accept)
            || false !== stripos($accept, "html")) {
            return;
        }

        // The rights are cheked based on the action name. Documents::prepareRightsFilters() is called in the model
        $this->registry->set('action', 'list', true);

        $filters = $this->prepFiltersFromRequest();
        $this->registry->set('getTags', true, true);

        $outlook = $this->getCurrentOutlook($filters);

        $shouldCheckPermissions = [
            'view' => true,
            'edit' => true,
            'setstatus' => true,
        ];

        if (isset($outlook) && $outlook) {
            $modelFields = $outlook->get('current_custom_fields');
            $modelFieldsNames = array_column($modelFields, 'name');
            $filters['get_fields'] = $modelFieldsNames;


            if (in_array('tags', $modelFieldsNames)) {
                //set flag to get tags for current model
                $this->registry->set('getTags', true, true);
            }

            if (!in_array('status', $modelFieldsNames)) {
                $shouldCheckPermissions['setstatus'] = false;
            }

            $shouldCheckAssignmentsPermissions = in_array('owner', $modelFieldsNames)
                || in_array('observer', $modelFieldsNames)
                || in_array('responsible', $modelFieldsNames)
                || in_array('decision', $modelFieldsNames);
        }

        list($records, $pagination) = $this->modelFactoryName::pagedSearch($this->registry, $filters);

        if (isset($outlook) && $outlook) {
            $additionalVars = $outlook->getModelAdditionalFields();
            $basicVars = $outlook->getModelFields();
            if ($additionalVars) {
                $outlook->clearNotPermittedVars();
            }

            /** @var Model $record */
            foreach ($records as $record) {
                $this->prepListRecordFileuploadAttributes($record, $additionalVars);

                if (array_key_exists('timesheet_time', $basicVars)) {
                    $shouldCheckPermissions['viewtimesheets'] = true;
                    $shouldCheckPermissions['addtimesheet'] = true;
                    if ($record->checkPermissions('viewtimesheets')) {
                        // Sets timesheet_time and timesheet_time_formatted
                        $record->set('timesheet_time_formatted', General::minutes2Human($this->registry, $record->getTimesheetTime(), false, true), true);
                    } else {
                        $record->set('timesheet_time', '', true);
                        $record->set('timesheet_time_formatted', '', true);
                    }
                }

                if (in_array('tags', $modelFieldsNames)) {
                    $shouldCheckPermissions['tags_view'] = true;
                    $shouldCheckPermissions['tags_edit'] = true;
                }

                if ($shouldCheckAssignmentsPermissions) {
                    $shouldCheckPermissions['assign'] = true;
                }

                $record->sanitize();
                $record->properties['cached_assoc_vars'] = null;

                unset($record->properties['cached_assoc_vars']);
            }
        }

        if (in_array(true, $shouldCheckPermissions, true)) {
            foreach ($records as $record) {
                $record->unsanitize();
                $rightsProper = [];
                foreach ($shouldCheckPermissions as $action=>$test) {
                    if (! $test) {
                        continue;
                    }
                    switch ($action) {
                        case 'addtimesheet':
                            $rightsProper[$action] = $record->get('status') !== 'closed' && $record->checkPermissions($action);
                            break;
                        default:
                            $rightsProper[$action] = $record->checkPermissions($action);
                    }
                }

                $record->rights = array_merge($record->rights, $rightsProper);
                $record->set('type_rights', array_merge($record->get('type_rights'), $rightsProper), true);
            }
        }
        $data = [
            'pagination' => $pagination,
            'records' =>  $records,
        ];

        header('content-type: application/json');
        $this->actionCompleted = true;
        $this->registry->set('ajax_result', json_encode($data), true);
    }

    /**
     * my assigned models
     */
    private function _myassigned() {
        $this->redirectFilterLink2ListForModernTheme($this->registry['request']->getGet($this->registry['action_param']));
        //all the actions are within the viewer
        return true;
    }

    /**
     * my responsible models
     */
    private function _myresponsible() {
        $this->redirectFilterLink2ListForModernTheme($this->registry['request']->getGet($this->registry['action_param']));
        //all the actions are within the viewer
        return true;
    }

    /**
     * my observer models
     */
    private function _myobserver() {
        $this->redirectFilterLink2ListForModernTheme($this->registry['request']->getGet($this->registry['action_param']));
        //all the actions are within the viewer
        return true;
    }

    /**
     * my models
     */
    private function _myrecords() {
        $this->redirectFilterLink2ListForModernTheme($this->registry['request']->getGet($this->registry['action_param']));
        //all the actions are within the viewer
        return true;
    }

    /**
     * Redirect the special task filters to proper list page with search
     * @param string $type
     * @return void
     */
    private function redirectFilterLink2ListForModernTheme($type): void
    {
        // Prottect from redirecting to list page when the request accepts JSON, because of the REST API
        if (!$this->registry['theme']->isModern() || \Auth::$is_rest) {
            return;
        }
        /** @var \Request $request */
        $request = $this->registry['request'];
        $requestUri = $request->getServer()['REQUEST_URI'];
        $requestQuery = preg_replace("/^[^?]+\??(.*)$/", '$1', $requestUri);
        parse_str($requestQuery, $queryParams);
        // Make sure any extra parameters are not lost
        unset($queryParams['launch'], $queryParams[$this->module]);

        $requestVal2compare = [
            'myassigned' =>  "owner",
            'myresponsible' =>  "responsible",
            'myobserver' =>  "observer",
        ];

        $filterQueryData = [$this->getSearchParamsForNotFinished()];

        if ($type === 'myrecords') {
            $filterQueryData[] = $this->getSearchParamsForBasic('added_by', 'currentUser');
        }

        if (array_key_exists($type, $requestVal2compare)) {
            $filterQueryData[] = $this->getSearchParamsForAssignment($requestVal2compare[$type], 'currentUser');
        }

        $queryParams = array_merge_recursive(
            $queryParams,
            ...$filterQueryData
        );

        // Ensure page is set in the url
        if (!array_key_exists('page', $queryParams)) {
            $queryParams['page'] = 1;
        }

        $this->redirect($this->module, 'list', $queryParams);
    }

    /**
     * If no search params, redirects to a search for 'Not finished tasks'
     * This imitates the different behaviour of list and search actions
     * @return void
     */
    public function redirectToAddNotFinishedFilter2ListForModernTheme(): void
    {
        // Protect from redirecting to list page when the request accepts JSON, because of the REST API
        if (! $this->registry['theme']->isModern() || \Auth::$is_rest) {
            return;
        }

        /** @var \Request $request */
        $request = $this->registry['request'];

        // No redirect if search params are defined
        $searchFieldsReq = $request->getGet('search_fields', false);
        if ($searchFieldsReq) {
            return;
        }

        $requestUri = $request->getServer()['REQUEST_URI'];
        $requestQuery = preg_replace("/^[^?]+\??(.*)$/", '$1', $requestUri);
        parse_str($requestQuery, $queryParams);

        unset($queryParams['launch'], $queryParams[$this->module]);

        $filterQueryData = [$this->getSearchParamsForNotFinished()];

        if ($typeSection = ($queryParams['type_section'] ?? null)) {
            $filterQueryData[] = $this->getSearchParamsForTypeSection((int) $typeSection);
            unset($queryParams['type_section']);
        }

        if ($type = ($queryParams['type'] ?? null)) {
            $filterQueryData[] = $this->getSearchParamsForType((int) $type);
            unset($queryParams['type']);
        }

        $queryParams = array_merge_recursive(
            $queryParams,
            ...$filterQueryData
        );

        // Ensure page is set in the url
        if (!array_key_exists('page', $queryParams)) {
            $queryParams['page'] = 1;
        }

        $this->redirect($this->module, 'list', $queryParams);
    }

    /**
     * Generates params for search url - search for "status - not - finished"
     * @return array
     */
    public function getSearchParamsForNotFinished(): array
    {
        return $this->getSearchParamsForBasic('status', 'finished', "!= '%s'");
    }

    /**
     * search of models
     */
    private function _search() {
        $this->redirectSearch2ListForModernTheme();
        //all the actions are within the viewer
        return true;
    }

    /**
     * Manage (save/delete) saved configurations for tasks
     */
    private function _configurator() {
        $request = &$this->registry['request'];
        $task = Tasks::buildModel($this->registry);
        $conf_id = '';
        if ($request->get('saved_tasks_configurators')) {
            require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
            $config_action = $request->get('config_action');
            $this->registry->set('task', $task->sanitize());

            if ($config_action == 'save') {
                $configurator = Tasks_Configurators::buildModel($this->registry, '');
                if ($configurator->save()) {
                    $conf_id = $configurator->get('id');
                }
            } elseif ($config_action == 'delete') {
                if (Tasks_Configurators::purge($this->registry, $request->get('saved_tasks_configurators'))) {

                }
            }
        }

        $task->set('configurator', $conf_id, true);

        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            require_once PH_MODULES_DIR . 'tasks/models/tasks.types.factory.php';
            $filters = array('where' => array('tt.id = ' . $type_id,
                                              'tt.active = 1'),
                             'sanitize' => true);
            $type = Tasks_Types::searchOne($this->registry, $filters);
        }

        //prepare tasks configurators
        require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
        $config_task_patterns = Tasks_Configurators::search($this->registry,
                                array('where' => array('tc.model_type = ' . $task->get('type'),
                                                       'tc.user_id IN (0, ' . $this->registry['currentUser']->get('id') . ')',
                                                       'tc.lang = \'' . $task->get('model_lang') . '\'')));
        $config_task_patterns_options = array();
        $all = $this->i18n('all');
        $mine = $this->i18n('mine');
        foreach ($config_task_patterns as $configurator) {
            if ($configurator->get('user_id')) {
                $optgroup = $mine;
            } else {
                $optgroup = $all;
            }
            $config_task_patterns_options[$optgroup][] = array(
            'label' => $configurator->get('name'),
            'option_value' => $configurator->get('id'));
        }

        $viewer = new Viewer($this->registry);
        if ($task->get('id')) {
            $viewer->data['action'] = 'edit';
        } else {
            $viewer->data['action'] = 'add';
        }
        $viewer->data['task'] = $task;
        $viewer->data['task_type'] = $type;
        $viewer->data['configTaskPatterns'] = $config_task_patterns_options;

        if ($viewer->theme->isModern()) {
            $viewer->data['dont_wrap_content'] = true;
            $viewer->templatesDir = PH_MODULES_DIR . $this->module . '/view/templates/';
        } else {
            $viewer->templatesDir = PH_MODULES_DIR . $this->module . '/templates/';
        }
        $viewer->setFrameset($viewer->templatesDir . '_tasks_configurator_panel.html');
        $viewer->display();
        exit;
    }

    /**
     * Get customer's info
     */
    private function _showCustomersInfo() {
        $request = &$this->registry['request'];

        $customerInfoViewer = new Viewer($this->registry);

        $i18n_files[] = PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers.ini';
        $customerInfoViewer->loadCustomI18NFiles($i18n_files);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
            $filters_customer  = array('sanitize' => true,
                                       'model_lang' => $request->get('model_lang'),
                                       'where' => array('c.id = ' . $customer_id,
                                                        'c.subtype = \'normal\''));
            $customer = Customers::searchOne($this->registry, $filters_customer);

            if ($customer) {
                if ($customer->get('main_branch_id')) {
                    require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
                    $filters_branches = array('sanitize' => true,
                                              'model_lang' => $request->get('model_lang'),
                                              'where' => array('c.id = ' . $customer->get('main_branch_id'),
                                                               'c.subtype = \'branch\''));
                    $customer_main_branch = Customers_Branches::searchOne($this->registry, $filters_branches);
                    $customer->set('branch_address', ($customer_main_branch ? $customer_main_branch->get('address') : ''), true);
                } else {
                    $customer->set('branch_address', '', true);
                }

                $customerInfoViewer->data['customers_info'] = $customer;
            } else {
                $customerInfoViewer->data['hide_side_panel'] = 1;
            }
        } else {
            $customerInfoViewer->data['hide_side_panel'] = 1;
        }

        $customerInfoViewer->setFrameset('_customers_info_side_panel.html');
        $customerInfoViewer->display();
        exit;
    }

    /**
     * Get last five records having same customer and type as current model
     */
    private function _showLastRecords() {
        $request = &$this->registry['request'];

        $lastRecordsViewer = new Viewer($this->registry);

        $customer_id = $request->get('customer_id');
        if ($customer_id) {
            $filters_records = array('where' => array('t.customer = ' . $customer_id,
                                                      't.type = \'' . $request->get('model_type') . '\''),
                                     'sort' => array('t.added DESC'),
                                     'limit' => 5,
                                     'sanitize' => true,
                                     'check_module_permissions' => 'tasks',
                                     'get_fields' => array('name', 'status'));

            $last_records = Tasks::search($this->registry, $filters_records);
            // get files count to display "paper clip"
            foreach ($last_records as $rec) {
                $rec->getFilesCount();
            }

            $lastRecordsViewer->data['last_records'] = $last_records;
        } else {
            $lastRecordsViewer->data['hide_side_panel'] = 1;
        }

        $lastRecordsViewer->setFrameset(PH_MODULES_DIR . 'tasks/templates/_last_records_side_panel.html');
        $lastRecordsViewer->display();
        exit;
    }

    /**
     * Get owner assignment options depending on the
     * selected department in MultiAdd action
     */
    private function _changeAssignmentsMultiaddRecords() {
        $request = &$this->registry['request'];
        $department_id = $request->get('department_id');

        $records_assignments = array();

        require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';

        //take the department users
        $department_users = Departments::getUsersIds($this->registry, array('where' => array('d.id ="' . $department_id . '"')));

        $users_owners_obj = array();

        if (! empty($department_users)) {
            //take users for this department
            $filters = array('model_lang'   => $this->registry->get('lang'),
                             'where'        => array('u.id IN (' . implode(',', $department_users) . ')',
                                                     'u.hidden = 0',
                                                     'u.active = 1'),
                             'sort'         => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                             'sanitize'     => true
                            );
            $users_owners_obj = Users::search($this->registry, $filters);
        }

        // collect user assignment permissions per role
        // (as they are the same for all users with the same role)
        // and reuse them to reduce multiple fetching of same data from database
        $user_available_assignments_per_role = array();

        foreach ($users_owners_obj as $user) {
            // get assignment permissions for this user and set them
            // to the array per role
            if (empty($user_available_assignments_per_role[$user->get('role')])) {
                $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Task');
            }
            $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

            if (in_array($request->get('type'), $user_available_assignments['owner'])) {
                $records_assignments[] = array(
                    'value'         => $user->get('id'),
                    'label'         => $user->get('firstname') . ' ' . $user->get('lastname')
                );
            }
        }

        print json_encode($records_assignments);
    }

    /**
     * Set status of model
     */
    private function _status() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get($this->action);

        $matches = array();
        if (empty($_SERVER['HTTP_REFERER'])) {
            $redirect_to_url = sprintf(
                '%s?%s=%s&%s=view&view=%d',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'], $this->registry['module'],
                $this->registry['action_param'], $id
            );
        } else {
            $redirect_to_url = $_SERVER['HTTP_REFERER'];
            preg_match('/&tasks=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        }

        //get referer's action
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $filters = array('where' => array('t.id = ' . $id, 't.status IS NOT NULL'),
                         'model_lang' => $request->get('model_lang'));
        $old_task = Tasks::searchOne($this->registry, $filters);
        $this->old_model = clone $old_task;
        $this->old_model->sanitize();
        $request->set('id', $id, 'all', true);
        $task = Tasks::buildModel($this->registry);

        $allow_user = $task->checkStatusSettings($old_task);

        $allow_minitasks = true;
        if ($task->get('status') == 'finished' && $old_task->get('requires_completed_minitasks') && $old_task->hasUncompletedMinitasks()) {
            $minitasks_link = sprintf('%s?%s=tasks&tasks=communications&communications=%d&communication_type=minitasks',
                                      $_SERVER['PHP_SELF'], $this->registry['module_param'], $old_task->get('id'));
            $this->registry['messages']->setError(sprintf('<a href="%s">%s</a>',
                                                          $minitasks_link,
                                                          $this->i18n('error_tasks_uncompleted_minitasks')));
            $allow_minitasks = false;
        }

        if ($allow_user && $allow_minitasks && $task->setStatus()) {
            //show message 'message_tasks_status_success'
            $this->registry['messages']->setMessage($this->i18n('message_tasks_status_success', array($old_task->getModelTypeName())), '', -2);

            $comment = '';
            if ($request->get('comment')) {
                require_once PH_MODULES_DIR . 'comments/models/comments.factory.php';
                $comment = Comments::buildModel($this->registry);

                $comment->set('content', $request->get('comment'), true);
                $comment->set('subject', $this->i18n('tasks_status_change_comment'), true);
                $comment->set('model', 'Task', true);
                $comment->set('model_id', $id, true);
                $comment->set('is_portal', ($request->get('is_portal') ? '1' : '0'), true);
                $comment->set('skip_send_email', true, true);
                $comment->unsetProperty('id', true);

                if ($comment->save()) {
                    $comment->slashesStrip();

                    //show corresponding message
                    $this->registry['messages']->setMessage($this->i18n('message_tasks_comments_add_success'), '', -1);
                } else {
                    //some error occurred
                    //show corresponding error(s)
                    $this->registry['messages']->setError($this->i18n('error_comments_add_failed'), '', -1);
                }
            }

            $filters = array(
                'where' => array(
                    't.id = ' . $id,
                    't.status IS NOT NULL'
                ),
                'model_lang' => $request->get('model_lang')
            );
            $new_task = Tasks::searchOne($this->registry, $filters);

            $new_task->set('comment', $comment, true);


            $this->model = clone $new_task;
            $this->model->sanitize();

            $audit_parent = Tasks_History::saveData(
                $this->registry,
                array(
                    'model' => $task,
                    'action_type' => 'status',
                    'new_model' => $new_task,
                    'old_model' => $old_task
                )
            );

            if ($comment && $comment->get('id')) {
                $comment->saveHistory($new_task);
            }

            //send notification
            $this->sendStatusNotification($new_task, $audit_parent);

            //set after action to view if not having permission
            if (!$new_task->checkPermissions($after_action)) {
                $after_action = 'view';
            }

            //the model was successfully saved set action as completed
            $this->actionCompleted = true;
        } else {
            //some error occurred
            $this->registry['messages']->setError($this->i18n('error_tasks_status_failed', array($old_task->getModelTypeName())), '', -1);
            $this->registry['messages']->insertInSession($this->registry);
            header("Location: " . $redirect_to_url);
            exit;
        }

        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        if (!isset($matches[1]) || $matches[1] == 'search' || $matches[1] == 'list') {
            //set parameters in registry - check them in router
            //set redirect url
            $this->registry->set('redirect_to_url', $redirect_to_url, true);
            //set exit parameter
            $this->registry->set('exit_after', true, true);
            //header("Location: " . $_SERVER['HTTP_REFERER']);exit;
        }

        return true;
    }

    /**
     * add a single model
     */
    private function _add() {
        $request = &$this->registry['request'];

        if ($this->action != 'ajax_add') {
            // make sure action in registry is 'add'
            $this->setAction('add');
        }

        require_once $this->modelsDir . 'tasks.types.factory.php';
        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id)) {
            $filters = array('where' => array('tt.id = ' . $type_id,
                                              'tt.active = 1'),
                             'sanitize' => true);
            $type = Tasks_Types::searchOne($this->registry, $filters);
        }

        if ($this->action == 'ajax_add') {
            //prepare available task types
            $filters = array('where' => array('tt.active=1'),
                             'sort' => array('tti18n.name ASC'),
                             'sanitize' => true);
            if ($request->get('allowed_types')) {
                $filters['where'][] = 'tt.id IN (' . $request->get('allowed_types') . ')';
            }
            $tasks_types = Tasks_Types::search($this->registry, $filters);

            $allowed_types = array();
            foreach ($tasks_types as $key => $task_type) {
                if (!$this->checkActionPermissions($this->module . $task_type->get('id'), 'add')) {
                    unset($tasks_types[$key]);
                } else {
                    $allowed_types[] = $task_type->get('id');
                }
            }
            // set first available type
            if (!$request->isPost() && !$request->get('type') && $tasks_types) {
                $request->set('type', $tasks_types[0]->get('id'), 'all', true);
                $type = $tasks_types[0];
            }
            $request->set('allowed_types', implode(',', $allowed_types), 'all', true);

            $this->registry['tasks_types'] = $tasks_types;
        }

        if (!$type || !$this->checkActionPermissions($this->module . $type->get('id'), 'add')) {
            //invalid type, redirect to list
            $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('task');
            $this->registry['messages']->setError($this->i18n('error_tasks_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            if ($this->registry['stop_redirects']) {
                return false;
            }
            $this->registry['messages']->insertInSession($this->registry);
            if ($this->action != 'ajax_add' && !$request->get('reload')) {
                $this->redirect($this->module, 'list');
            }
        }

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $task = Tasks::buildModel($this->registry);
            $task->set('type_name', ($type ? $type->get('name') : ''), true);

            if ($task->save()) {
                $filters = array('where' => array('t.id = ' . $task->get('id')),
                                 'model_lang' => $task->get('model_lang'));
                $new_task = Tasks::searchOne($this->registry, $filters);
                $this->old_model = new Task($this->registry);
                $this->old_model->sanitize();

                $audit_parent = Tasks_History::saveData($this->registry,
                                                        array('model' => $task,
                                                              'action_type' => 'add',
                                                              'new_model' => $new_task,
                                                              'old_model' => $this->old_model));

                if ($task->get('create_from_customer') && ($task->get('customer') == $task->get('create_from_customer'))) {
                    // write history for the customer
                    require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                    require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';

                    $filters_customer = array('where' => array('c.id = ' . $task->get('customer')),
                                              'model_lang' => $request->get('model_lang'));
                    $customer = Customers::searchOne($this->registry, $filters_customer);

                    $empty_customer_model = Customers::buildModel($this->registry);
                    $empty_customer_model->sanitize();

                    $customer->set('created_task_num', $new_task->get('full_num'), true);
                    $customer->set('created_task_name', $new_task->get('name'), true);

                    Customers_History::saveData($this->registry, array('model'        => $customer,
                                                                       'new_model'    => $customer,
                                                                       'old_model'    => $empty_customer_model,
                                                                       'action_type'  => 'create_task',
                                                                       'task_type'    => $new_task->get('type_name')));
                }
                if ($task->get('create_from_document')) {
                    $related_model = clone $new_task;
                    $related_model->set('referers', array($task->get('create_from_document')), true);
                    if ($related_model->updateRelativesDocuments()) {
                        Tasks_History::saveData($this->registry, array('model' => $related_model, 'action_type' => 'relatives'));
                    }
                    unset($related_model);
                }

                // get assignments from model built from request, if any
                foreach (array('owner', 'responsible', 'observer', 'decision') as $atype) {
                    if ($task->get('assignments_' . $atype)) {
                        $new_task->set('assignments_' . $atype, $task->get('assignments_' . $atype), true);
                    }
                }
                // save assignments
                $new_task->defaultAssign();

                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_tasks_add_success', array($new_task->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                //get the task from previously saved object
                $task = $new_task;

                if ($this->action == 'ajax_add') {
                    $this->registry['messages']->removeFromSession($this->registry);
                    $v = new Viewer($this->registry);
                    $v->setFrameset('message.html');
                    $v->data['display'] = 'message';
                    $v->data['items'] = $this->registry['messages']->getMessages();
                    echo ('var result = ' . json_encode(array('messages' => $v->fetch(), 'id' => $task->get('id'))) . ';');
                    //set exit parameter
                    $this->registry->set('exit_after', true, true);
                }
            } else {
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_tasks_add_failed', array($task->getModelTypeName())), '', -1);
            }
        } else {
            //create empty task model
            $task = Tasks::buildModel($this->registry);

            $task->set('severity', 'normal', true);
            if ($task->get('customer') && $task->get('customer_create')) {
                // if customer is set in the GET string, then the task has been created from customer
                $task->set('create_from_customer', $task->get('customer'), true);
            }
            if ($task->get('document') && $task->get('document_create')) {
                // if document is set in the GET string, then the task has been created from document
                $task->set('create_from_document', $task->get('document'), true);
            }
            if ($request->get('configurator') > 0) {
                //get default values from configurator
                require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
                $configigurator = Tasks_Configurators::searchOne($this->registry,
                                        array('where' => array('tc.model_type = \'' . $type_id . '\'',
                                                               'tc.id = ' . $request->get('configurator'))));
                if ($configigurator) {
                    $params = unserialize($configigurator->get('params'));
                    foreach ($params as $key => $param) {
                        $task->set($key, $param, true);
                    }
                    if ($task->get('planned_time')) {
                        $task->set('planned_finish_date',
                        date('Y-m-d H:i:00', time() + 60*$task->get('planned_time')),
                        true);
                    }
                    if (!array_key_exists('trademark', $params) || !array_key_exists('customer', $params)) {
                        $task->set('trademark', '', true);
                    }
                }
            }
            if (!$task->get('department')) {
                $task->set('department', $type->getDefaultDepartment(), true);
            }
            if (!$task->get('group')) {
                $task->set('group', $type->getDefaultGroup(), true);
            }
            //set default value of the project
            if (!$task->get('project') && $this->registry['currentUser']->get('default_project')) {
                $task->set('project', $this->registry['currentUser']->get('default_project'), true);
            }
            // if invalid value is passed, remove it from model
            if (empty($configigurator) && $task->get('configurator')) {
                $task->unsetProperty('configurator', true);
            }
        }

        if (!empty($task)) {
            //get counter for the task and sanitize it
            $task_counter = $task->getCounter();
            if ($task_counter) {
                $task->counter->sanitize();

                if (!$this->actionCompleted) {
                    $buttons = $task->getButtons();
                    $task->set('buttons', $buttons['general'], true);
                }

                //register the model (sanitized)
                //so that it could be used further by the viewer
                $this->registry->set('task', $task->sanitize());
            } else {
                //show error no valid counter
                $this->registry['messages']->setError($this->i18n('error_no_counter_for_this_task_type'));
                if ($this->registry['stop_redirects']) {
                    return false;
                }
                $this->registry['messages']->insertInSession($this->registry);
                //there is no such model, redirect to the listing
                if ($this->action != 'ajax_add' && !$request->get('reload')) {
                    $this->redirect($this->module, 'list');
                }
            }
        }

        // adding from lightbox, type change or reloading data from saved configuration
        if (!$this->actionCompleted && ($this->action == 'ajax_add' || $request->get('reload'))) {
            if ($this->registry['messages']->getErrors()) {
                $this->registry['messages']->removeFromSession($this->registry);
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['display'] = 'error';
                $v->data['items'] = $this->registry['messages']->getErrors();
                echo ('var result = ' . json_encode(array('errors' => $v->fetch())) . ';');
                exit;
            }
            require_once PH_MODULES_DIR . 'tasks/viewers/tasks.add.viewer.php';
            $this->viewer = new Tasks_Add_Viewer($this->registry);
            $this->viewer->model = $this->registry['task'];
            $this->viewer->setFrameset('frameset_blank.html');
        }

        return true;
    }

    /**
     * multiadd models
     */
    private function _multiAdd() {
        $request = &$this->registry['request'];
        require_once $this->modelsDir . 'tasks.types.factory.php';

        //check validity of the type
        $type_id = $request->get('type');
        $type = '';
        if (!empty($type_id) && $type_id != PH_TASK_SYSTEM_TYPE) {
            $filters = array('where' => array('tt.id = ' . $type_id,
                                              'tt.active = 1'),
                             'sanitize' => true);
            $type = Tasks_Types::searchOne($this->registry, $filters);
        }
        $type_name = $type && $type->get('name') ? $type->get('name') : $this->i18n('task');
        $type_name_plural = $type && $type->get('name_plural') ? $type->get('name_plural') : $this->i18n('tasks');

        $type_permission = ($type) ? $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd') : false;

        if (!$type || !$type_permission) {
            //invalid type, redirect to list
            $this->registry['messages']->setError($this->i18n('error_tasks_add_failed', array($type_name)), '', -1);
            $this->registry['messages']->setError($this->i18n('error_invalid_type'));
            $this->registry['messages']->insertInSession($this->registry);
            $this->redirect($this->module, 'list');
        } else {
            //assign the type to the registry
            $this->registry['tasktype'] = $type;
        }

        $filters = array('type' => $request->get('type'));
        $task = Tasks::buildModel($this->registry, $filters);

        //get main variables
        $vars = $this->getBasicVarsDefs($task);

        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_tasks_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        $this->actionCompleted = Tasks::multiAdd($this->registry, $vars, $this);
        if ($request->isPost()) {
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_tasks_multiadd_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'list', array('type' => $type->get('id')));
            } else {
                $this->registry['messages']->setError($this->i18n('error_tasks_multiadd_failed', array($type_name_plural)), '', -1);
            }
        }
        return true;
    }

    /**
     * edit of a single model
     */
    private function _edit() {
        $request = &$this->registry['request'];

        if ($this->action != 'ajax_edit') {
            // make sure action in registry is 'edit'
            $this->setAction('edit');
        }

        //get the requested model ID
        $id = $request->get($this->action);

        //check if details are submitted via POST
        if ($request->isPost()) {

            //get the old model (used for automations and audit)
            $filters = array('where' => array('t.id = ' . $id),
                             'model_lang' => $request->get('model_lang'),
                             'sanitize' => true);
            $this->old_model = Tasks::searchOne($this->registry, $filters);

            //build the model from the POST
            $this->registry->set('getAssignments', true, true);
            $task = Tasks::buildModel($this->registry);
            $filters = array('where' => array('t.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_task = Tasks::searchOne($this->registry, $filters);
            $task->set('status', $old_task->get('status'), true);
            $task->set('substatus_name', $old_task->get('substatus_name'), true);
            $task->set('substatus', $old_task->get('substatus'), true);
            $task->set('type_name', $old_task->get('type_name'), true);

            if ($task->save()) {
                //show message 'message_tasks_edit_success'
                $this->registry['messages']->setMessage($this->i18n('message_tasks_edit_success', array($old_task->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);

                $filters = array('where' => array('t.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_task = Tasks::searchOne($this->registry, $filters);

                $audit_parent = Tasks_History::saveData($this->registry,
                                                        array('model' => $task,
                                                              'action_type' => 'edit',
                                                              'new_model' => $new_task,
                                                              'old_model' => $old_task));

                //send notification for edit
                $this->sendNotification($new_task, $audit_parent);

                $assign = false;
                $task_before_assign = clone $new_task;
                $task_before_assign->sanitize();

                //delete old owner assignments
                if ($new_task->get('department') != $old_task->get('department')) {
                    $new_task->set('assignments_owner', array(), true);
                    $assign = true;
                }

                // get assignments from request, if any
                foreach (array('owner', 'responsible', 'observer', 'decision') as $atype) {
                    if ($request->get('assignments_' . $atype)) {
                        $new_task->set('assignments_' . $atype, $request->get('assignments_' . $atype), true);
                        $assign = true;
                    }
                }

                if ($assign) {
                    if ($new_task->assign()) {
                        $filters = array('where' => array('t.id = ' . $request->get('id')),
                                         'model_lang' => $request->get('model_lang'));
                        $new_task = Tasks::searchOne($this->registry, $filters);

                        Tasks_History::saveData($this->registry,
                                                array('model' => $new_task,
                                                      'action_type' => 'assign',
                                                      'new_model' => $new_task,
                                                      'old_model' => $task_before_assign));
                    } else {
                        // assign failed, display error messages
                        $this->registry['messages']->insertInSession($this->registry);
                    }
                }

                unset($task_before_assign);

                //get the task from previously saved object
                $task = $new_task;

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;

                if ($this->action == 'ajax_edit') {
                    $this->registry['messages']->removeFromSession($this->registry);
                    $v = new Viewer($this->registry);
                    $v->setFrameset('message.html');
                    $v->data['display'] = 'message';
                    $v->data['items'] = $this->registry['messages']->getMessages();
                    echo ('var result = ' . json_encode(array('messages' => $v->fetch(), 'id' => $task->get('id'))) . ';');
                    //set exit parameter
                    $this->registry->set('exit_after', true, true);
                    // unlock model
                    if ($this->registry['locking_records']) {
                        $new_task->unlock();
                    }
                }
            } else {
                //some error occurred
                $this->registry['messages']->setError($this->i18n('error_tasks_edit_failed', array($task->getModelTypeName())), '', -1);

                //register the model, with all the posted details
                $this->registry->set('task', $task);
            }
        } elseif ($id > 0) {
            // the model from the DB
            $filters = array('where' => array('t.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $task = Tasks::searchOne($this->registry, $filters);

            if ($task) {
                //check access and ownership of the model
                $this->checkAccessOwnership($task, $this->action != 'ajax_edit' && !$request->get('reload'), 'edit');

                if ($request->get('configurator')) {
                    //get default values from configurator
                    require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
                    $configigurator = Tasks_Configurators::searchOne($this->registry,
                                            array('where' => array('tc.model_type = ' . $task->get('type'),
                                                                   'tc.id = ' . $request->get('configurator'))));
                    if ($configigurator) {
                        $params = unserialize($configigurator->get('params'));
                        foreach ($params as $key => $param) {
                            $task->set($key, $param, true);
                        }
                        if (!array_key_exists('trademark', $params) || !array_key_exists('customer', $params)) {
                            $task->set('trademark', '', true);
                        }
                    }
                    $task->set('configurator', $request->get('configurator'), true);
                }
            }
        }

        if (!empty($task)) {
            if (!$this->actionCompleted) {
                $buttons = $task->getButtons();
                $task->set('buttons', $buttons['general'], true);
            }
            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('task')) {
                $this->registry->set('task', $task->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            if ($this->action != 'ajax_edit' && !$request->get('reload')) {
                $this->redirect($this->module, 'list');
            }
        }

        // editing from lightbox or reloading data from saved configuration
        if (!$this->actionCompleted && ($this->action == 'ajax_edit' || $request->get('reload'))) {
            if ($this->registry['messages']->getErrors()) {
                $this->registry['messages']->removeFromSession($this->registry);
                $v = new Viewer($this->registry);
                $v->setFrameset('message.html');
                $v->data['display'] = 'error';
                $v->data['items'] = $this->registry['messages']->getErrors();
                echo ('var result = ' . json_encode(array('errors' => $v->fetch())) . ';');
                exit;
            }
            require_once PH_MODULES_DIR . 'tasks/viewers/tasks.edit.viewer.php';
            $this->viewer = new Tasks_Edit_Viewer($this->registry);
            $this->viewer->model = $this->registry['task'];
            $this->viewer->setFrameset('frameset_blank.html');
        }

        return true;
    }

    /**
     * multiedit models
     */
    private function _multiEdit($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('t.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $request->get('model_lang'),
                         'sanitize' => false);
        $tasks = Tasks::search($this->registry, $filters);

        //if no tasks or checked deleted tasks
        if (empty($tasks) || count($ids) != count($tasks)) {
            $this->registry['messages']->setError($this->i18n('error_no_tasks_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_task = $tasks[0];

        $this->registry->set('type_name', $first_task->get('type_name'));

        $type = ($first_task->get('type'));
        foreach ($tasks as $task) {
            $type_i = $task->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'tasks.types.factory.php';
        if (!empty($type)) {
            $filters = array('where' => array('tt.id = ' . $type),
                             'sanitize' => true);
            $tasktype = Tasks_Types::searchOne($this->registry, $filters);
        }
        $this->registry['tasktype'] = $tasktype;

        //get main variables
        $vars = $this->getBasicVarsDefs($task);

        if (count($vars) == 0) {
            //show error no variables
            $this->registry['messages']->setError($this->i18n('error_tasks_no_multi_operation_variables'));
            $this->registry['messages']->insertInSession($this->registry);

            //redirect to the listing
            $this->redirect($this->module, 'list');
        }

        //check if details are submitted via checkboxes (items)
        if (!$request->get('multisave')) {
            // the models from the DB
            foreach ($ids as $i => $id) {
                //$filters = array('id' => $id, 'model_lang' => $request->get('model_lang'));
                //get values
                foreach ($vars as $k => $var) {
                    //if the var is 'customer' the data is formatted for
                    //passing to autocompleter
                    if (in_array($var['name'], array('customer', 'project'))) {
                        $value_name = $var['name'] . '_name';
                        $value_code = $var['name'] . '_code';
                        $vars[$k]['value']  = $tasks[$i]->get($var['name']);
                        $vars[$k]['value_code'] = $tasks[$i]->get($value_code);
                        $vars[$k]['value_name'] = $tasks[$i]->get($value_name);
                    } elseif ($var['name'] == 'trademark') {
                        $vars[$k]['value'] = $tasks[$i]->get($var['name']);
                        $vars[$k]['value_autocomplete'] = $tasks[$i]->get($var['name'] . '_name');
                    } else {
                        $vars[$k]['val'] = $tasks[$i]->get($var['name']);
                    }
                }
                $tasks[$i]->set('multivars', $vars, true);
                $tasks[$i]->sanitize();
            }
            $this->registry->set('tasks', $tasks);
        } else {

            //save models
            foreach ($vars as $var) {
                if (!$this->registry['request']->isRequested($var['name'])) {
                    $this->registry['request']->set($var['name'], array(), 'post', true);
                }
            }

            $type_name_plural = !empty($tasktype) && $tasktype->get('name_plural') ? $tasktype->get('name_plural') : $this->i18n('tasks');

            $this->actionCompleted = Tasks::multiEdit($this->registry, $vars, $type, $this);
            if ($this->actionCompleted) {
                $this->registry['messages']->setMessage($this->i18n('message_tasks_multiedit_success', array($type_name_plural)), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            } else {
                $this->registry['messages']->setError($this->i18n('error_tasks_multiedit_failed', array($type_name_plural)), '', -1);
            }
        }

        return true;
    }

    /**
     * Translates existing model
     */
    private function _translate() {
        $request = &$this->registry['request'];

        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('translate');

        //check if details are submitted via POST
        if ($request->isPost()) {
            //build the model from the POST
            $this->registry->set('getAssignments', true, true);
            $task = Tasks::buildModel($this->registry);
            $filters = array('where' => array('t.id = ' . $request->get('id')),
                             'model_lang' => $request->get('model_lang'));
            $old_task = Tasks::searchOne($this->registry, $filters);

            if ($task->save()) {
                //show corresponding message
                $this->registry['messages']->setMessage($this->i18n('message_tasks_translate_success', array($old_task->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $filters = array('where' => array('t.id = ' . $request->get('id')),
                                 'model_lang' => $request->get('model_lang'));
                $new_task = Tasks::searchOne($this->registry, $filters);

                $audit_parent = Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'translate', 'new_model' => $new_task, 'old_model' => $old_task));

                //the model was successfully saved set action as completed
                $this->actionCompleted = true;
            } else {
                if ($old_task) {
                    $task->set('type_name', $old_task->get('type_name'), true);
                }
                //some error occurred
                //show corresponding error(s)
                $this->registry['messages']->setError($this->i18n('error_tasks_translate_failed', array($task->getModelTypeName())), '', -1);
            }

        } elseif ($id > 0) {
            //get the model from the DB
            $filters = array('where' => array('t.id = ' . $id),
                             'model_lang' => $request->get('model_lang'));
            $task = Tasks::searchOne($this->registry, $filters);

            if ($task) {
                //check access and ownership of the model
                $this->checkAccessOwnership($task);
            }
        }

        if (!empty($task)) {
            //register the model (sanitized)
            //so that it could be used further by the viewer
            $this->registry->set('task', $task->sanitize());
        } else {
            //no such record
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * view model
     */
    private function _view() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $task = Tasks::searchOne($this->registry, $filters);
        if (!empty($task)) {
            $buttons = $task->getButtons();
            $task->set('buttons', $buttons['general'], true);

            //check access and ownership of the model
            $this->checkAccessOwnership($task);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('task')) {
                $this->registry->set('task', $task->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Attaches files to the task
     */
    private function _attachments() {
        $request = &$this->registry['request'];
        //get the requested model ID
        //$id = $request->get($this->action);
        $id = $request->get('attachments');

        $filters = array('where' => array('t.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $task = Tasks::searchOne($this->registry, $filters);
        if ($task) {
            $task->getAttachments();
        }

        $added_files = array(0 => array());

        if ($request->isPost()) {
            $erred_added_files = array();
            $success_added_files = array();

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            require_once 'transliterate.class.php';

            //add new attachments
            $additional_names        = $request->get('a_file_names');
            // $additional_descriptions = $request->get('a_file_descriptions');
            // $additional_permissions  = $request->get('a_file_permissions');
            // $additional_revisions    = $request->get('a_file_revisions');
            $additional_files        = !empty($_FILES['a_file_paths']) ? $_FILES['a_file_paths'] : array();

            if (!empty($additional_files)) {
                foreach ($additional_files['name'] as $idx => $name) {
                    if ($additional_files['tmp_name'][$idx]) {
                        $file = array(
                            'name'     => $additional_files['name'][$idx],
                            'type'     => $additional_files['type'][$idx],
                            'tmp_name' => $additional_files['tmp_name'][$idx],
                            'error'    => $additional_files['error'][$idx],
                            'size'     => $additional_files['size'][$idx]);
                    } else {
                        $file = array();
                    }
                    if (empty($additional_names[$idx])) {
                        $additional_names[$idx] = $additional_files['name'][$idx];
                    }
                    $params = array(
                        'name'        => $additional_names[$idx]
                    );

                    if (!empty($file) || $params['name']) {
                        if (!Files::attachFile($this->registry, $file, $params, $task->sanitize())) {
                            $error_type = '';
                            if (empty($file)) {
                                $error_type = $error_type . $this->i18n('error_attachments_file');
                            }
                            if ((! $params['name']) && empty($file)) $error_type = $error_type . " \ ";
                            if (! $params['name']) $error_type = $error_type . $this->i18n('error_attachments_file_name');
                            $erred_added_files[] = $idx;
                            $this->registry['messages']->setError($this->i18n('error_attachments_add') . ' ' . ($idx+1) . ' ' . ($error_type), 'add_attachment_' . ($idx+1));

                            //explain the failed upload with more details
                            foreach (FilesLib::$_errors as $err) {
                                $this->registry['messages']->setError($err);
                            }
                            FilesLib::$_errors = array();
                        } else {
                            $success_added_files[] = $idx;
                        }
                    }

                    $added_files[$idx] = $params;
                }
            }

            if ($added_files && empty($erred_added_files) && !empty($success_added_files)) {
                $filters = array('where' => array('t.id = ' . $task->get('id')),
                                 'model_lang' => $task->get('model_lang'));
                $task_attached_files = Tasks::searchOne($this->registry, $filters);
                $task_attached_files->getAttachments();
                $task_attached_files->sanitize();

                $this->registry['messages']->setMessage($this->i18n('message_attachments_added'));
                $this->registry['messages']->insertInSession($this->registry);

                Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'add_attachments', 'new_model' => $task_attached_files, 'old_model' => $task));
            } elseif ($added_files && !empty($erred_added_files)) {
                $this->registry['erred_added_files'] = $erred_added_files;
                $this->registry['messages']->setError($this->i18n('error_attachments_all'), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
            }

            if (empty($erred_added_files)) {
                $this->actionCompleted = true;
            }

        } else {
            //when coming from a link in list/search/outlook,
            //redirect to "view" of model with attachments panel toggled open
            $this->redirect($this->module, 'view', array('view' => $id, 'toggle_attachments' => 1));
        }

        $this->registry['added_files'] = $added_files;

        if (!empty($task)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($task);

            $task->getAttachments();

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('task')) {
                $this->registry->set('task', $task->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if ($this->registry['session']->isRequested('return_link')) {
            $redirect_link = 'Location: ' . $this->registry['session']->get('return_link');
            $this->registry['session']->remove('return_link');
            header($redirect_link);
            exit;
        }
        return true;
    }

    /**
     * Manage task file
     */
    private function _manageFile() {
        $request = &$this->registry['request'];

        //check if the 'attachments' action is allowed
        $this->checkAccessModule(true, $this->module, 'attachments');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id),
                         'model_lang' => $request->get('model_lang'));
        $task = Tasks::searchOne($this->registry, $filters);

        if (!empty($task)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($task);

            require_once PH_MODULES_DIR . 'files/models/files.factory.php';

            $filters = array('where' => array('f.id = ' . $request->get('file')),
                             'sanitize' => true);
            $file = Files::searchOne($this->registry, $filters);

            if ($file && file_exists($file->get('path'))) {
                switch ($this->action) {
                case 'getfile':
                    $result = $file->sendFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'view', 'view=' . $task->get('id'));
                    }
                    break;
                case 'viewfile':
                    $result = $file->viewFile();
                    if (!$result) {
                        $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                        $this->registry['messages']->insertInSession($this->registry);

                        //there is no such model, redirect to the listing
                        $this->redirect($this->module, 'view', 'view=' . $task->get('id'));
                    }
                    break;
                case 'delfile':
                    // get the files info needed for the audit
                    $old_task = clone $task;
                    $old_task->getAttachments();

                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $filters = array('where' => array('t.id = ' . $task->get('id')),
                                         'model_lang' => $task->get('model_lang')
                                        );
                        $task_del_files = Tasks::searchOne($this->registry, $filters);
                        $task_del_files->getAttachments();
                        $task_del_files->sanitize();

                        Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'del_attachments', 'new_model' => $task_del_files, 'old_model' => $old_task));

                        $this->registry['messages']->setMessage($this->i18n('message_tasks_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_tasks_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to referer
                    if ($this->registry['session']->isRequested('return_link')) {
                        $redirect_link = 'Location: ' . $this->registry['session']->get('return_link');
                        $this->registry['session']->remove('return_link');
                        header($redirect_link);
                        exit;
                    }
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'view', 'view=' . $task->get('id'));
                }
            } elseif ($file && $this->action == 'delfile') {
                switch ($this->action) {
                case 'delfile':
                    $result = Files::delete($this->registry, array($file->get('id')));
                    if ($result) {
                        $this->registry['messages']->setMessage($this->i18n('message_tasks_file_deleted_success'));
                    } else {
                        $this->registry['messages']->setError($this->i18n('error_tasks_file_deleted_failed'));
                    }
                    $this->registry['messages']->insertInSession($this->registry);

                    //redirect to referer
                    if ($this->registry['session']->isRequested('return_link')) {
                        $redirect_link = 'Location: ' . $this->registry['session']->get('return_link');
                        $this->registry['session']->remove('return_link');
                        header($redirect_link);
                        exit;
                    }
                    break;
                default:
                    //redirect to view mode
                    $this->redirect($this->module, 'view', 'view=' . $task->get('id'));
                }
            } else {
                $this->registry['messages']->setError($this->i18n('error_file_doesnot_exist'));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'view', 'view=' . $task->get('id'));
            }

        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Relatives of the task
     */
    private function _relatives() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id));
        if ($model_lang = $request->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $task = Tasks::searchOne($this->registry, $filters);
        $this->checkAccessOwnership($task);
        if (!empty($task)) {
            if ($request->isPost()) {
                $task->set('tasks_referers', $request->get('tasks_referers'), true);
                $task->set('referers', $request->get('referers'), true);
                if ($task->updateAllRelatives()) {
                    $this->registry['messages']->setMessage($this->i18n('message_relatives_success'), '', -2);
                    $this->registry['messages']->insertInSession($this->registry);

                    Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'relatives'));

                    //the model was successfully saved set action as completed
                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_relatives_failed'), '', -1);
                }
            } else {
                $task->getAllParents();
                //$task->getAssignments();

                //get the relatives tree
                $this->registry->set('relatives_tree', $task->getRelativesTree());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if (!$this->registry->isRegistered('task')) {
            $this->registry->set('task', $task->sanitize());
        }

        return true;
    }

    /**
     * Dependencies of the task
     */
    private function _dependencies() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id));
        if ($model_lang = $request->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        /** @var Task $task */
        $task = Tasks::searchOne($this->registry, $filters);
        $this->checkAccessOwnership($task);
        if (!empty($task)) {
            if ($request->isPost()) {
                $task->set('tasks_dependencies', $request->get('tasks_referers'), true);
                $task->set('tasks_dependencies_origin', $request->get('origin'), true);
                if ($task->updateDependencies()) {
                    $this->registry['messages']->setMessage($this->i18n('message_tasks_dependencies_success', array($task->getModelTypeName())), '', -2);
                    $this->registry['messages']->insertInSession($this->registry);

                    Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'dependencies'));

                    if (!$this->registry->isRegistered('task')) {
                        $this->registry->set('task', $task->sanitize());
                    }

                    $this->actionCompleted = true;

                    return true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_tasks_dependencies_failed', array($task->getModelTypeName())), '', -1);
                }
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }
        //check access and ownership of the model

        $task->getDependencies();

        $viewer = $this->getViewer();
        $viewer->model = $task;
        $viewer->data['children_tree'] = $task->addTreeRoot($task->getChildrenTreeDependencies($task->get('id')));
        $viewer->data['parents_tree'] = $task->addTreeRoot($task->getParentsTreeDependencies($task->get('id')));

        if (!$this->registry->isRegistered('task')) {
            $this->registry->set('task', $task->sanitize());
        }

        return true;
    }

    /**
     * History of the task
     */
    private function _history() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $filters = array(
            'where' => array(
                't.id = \'' . $request->get($this->action) . '\'',
            ),
            'model_lang' => $request->get('model_lang') ?: $this->registry['lang'],
        );
        $task = Tasks::searchOne($this->registry, $filters);

        if ($request->get('source') == 'ajax') {
            if ($task && $this->checkAccessOwnership($task, false)) {
                if (!$this->registry->isRegistered('task')) {
                    $this->registry->set('task', $task->sanitize());
                }

                require_once $this->viewersDir . 'tasks.history.viewer.php';
                $viewer = new Tasks_History_Viewer($this->registry);
                $viewer->prepare();
                if ($request->get('history_activity')) {
                    if ($request->get('page') <= 1) {
                        $viewer->prepareTitleBar();
                    }
                    $viewer->setFrameset('_history_activity.html');
                } else {
                    $viewer->setFrameset('_history.html');
                }
                $viewer->display();
            }
            exit;
        }

        if (!empty($task)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($task);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('task')) {
                $this->registry->set('task', $task->sanitize());
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * communication concerning the task (comments and emails)
     */
    private function _communications() {
        $request = &$this->registry['request'];

        //check the request for selected communication type
        $communication_type = $request->get('communication_type');

        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $task = Tasks::searchOne($this->registry, $filters);
        if (!empty($task)) {
            //check access and ownership of the model
            $this->checkAccessOwnership($task);

            $this->registry->set('communication_type', $communication_type, true);

            //register the model,
            //so that it could be used further by the viewer
            if (!$this->registry->isRegistered('task')) {
                $this->registry->set('task', $task->sanitize());
            }

            require_once PH_MODULES_DIR . 'communications/viewers/communications.viewer.php';
            $this->viewer = new Communications_Viewer($this->registry, true);
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Timesheets for the task
     */
    private function _timesheets() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = ' . $id));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $task = Tasks::searchOne($this->registry, $filters);
        if (!empty($task)) {
            if ($task->checkPermissions('viewtimesheets')) {
                //register the model,
                //so that it could be used further by the viewer
                if (!$this->registry->isRegistered('task')) {
                    $this->registry->set('task', $task->sanitize());
                }
            } else {
                //show error can't write timesheets
                $this->registry['messages']->setError($this->i18n('error_cant_write_timesheets', array($task->getModelTypeName())));
                $this->registry['messages']->insertInSession($this->registry);

                //there is no such model, redirect to the listing
                $this->redirect($this->module, 'list');
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        return true;
    }

    /**
     * Timesheets screen (calendar view)
     */
    private function _statements() {
        //all the actions are within the viewer
        return true;
    }

    /**
     * Switch the tabs in the statements screen
     */
    private function _switchStatementsSubpanel() {
        $request = $this->registry['request'];

        $this->registry->set('statements_subpanel', $request->get('selected_subpanel'), true);

        include_once $this->viewersDir . 'tasks.statements.viewer.php';
        $this->viewer = new Tasks_Statements_Viewer($this->registry);
        $this->viewer->prepare();
        $frameset = $this->viewer->template;
        $this->viewer->setFrameset(PH_MODULES_DIR . 'tasks' . ($this->viewer->theme->isModern() ? '/view' : '') . '/templates/' . $frameset);
        $this->viewer->display();
        exit;
    }

    /**
     * Allocate task execution to users
     *
     * @return boolean
     */
    private function _allocate() {
        $request = &$this->registry['request'];
        //get the requested model ID
        $id = $request->get($this->action);
        $filters = array('where' => array('t.id = \'' . $id . '\''));
        if ($model_lang = $this->registry['request']->get('model_lang')) {
            $filters['model_lang'] = $model_lang;
        }
        $task = Tasks::searchOne($this->registry, $filters);

        if (!empty($task)) {
            $this->checkAccessOwnership($task);

            // check validity of event type
            require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
            $filters = array('where' => array('et.active = 1',
                                              'et.keyword = \'plannedtime\''),
                             'sanitize' => true);
            $type = Events_Types::searchOne($this->registry, $filters);

            if (!$type) {
                $this->registry['messages']->setError($this->i18n('error_invalid_type'));
                $this->registry['messages']->setError($this->i18n('error_tasks_allocate_failed', array($task->getModelTypeName())), '', -1);
                $this->registry['messages']->insertInSession($this->registry);
                $this->redirect($this->module, 'view', array('view' => $id));
            }

            // we will store the type here
            $task->set('event_type', $type, true);

            if ($request->isPost()) {
                if ($task->savePlannedTime()) {
                    $filters = array('where'      => array('t.id = ' . $task->get('id')),
                                     'model_lang' => $task->get('model_lang'));
                    $new_task = Tasks::searchOne($this->registry, $filters);

                    Tasks_History::saveData($this->registry,
                                            array('model' => $new_task,
                                                  'action_type' => 'allocate',
                                                  'new_model' => $new_task,
                                                  'old_model' => $task->sanitize()));

                    $task = $new_task;

                    $this->registry['messages']->setMessage($this->i18n('message_tasks_allocate_success', array($task->getModelTypeName())), '', -1);
                    $this->registry['messages']->insertInSession($this->registry);

                    $this->actionCompleted = true;
                } else {
                    $this->registry['messages']->setError($this->i18n('error_tasks_allocate_failed', array($task->getModelTypeName())), '', -1);
                }
            }
        } else {
            //show error no such model
            $this->registry['messages']->setError($this->i18n('error_no_such_task'));
            $this->registry['messages']->insertInSession($this->registry);

            //there is no such model, redirect to the listing
            $this->redirect($this->module, 'list');
        }

        if (!$this->registry->isRegistered('task')) {
            $this->registry->set('task', $task->sanitize());
        }

        return true;
    }

    /**
     * Redirect to specified module at ADD section and auto complete task's data
     */
    private function _create() {
        $request = &$this->registry['request'];

        $module = $request->get('operation');
        switch ($module) {
            case 'documents':
                $add_link = sprintf('operation=add&type=%d&task_id=%d&customer=%d&project=%d',
                                    $request->get('document_type'), $request->get('create_from_task_id'),
                                    $request->get('create_from_task_customer_id'), $request->get('create_from_task_project_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'events':
                $add_link = sprintf('type=%d&customer=%d&project=%d',
                                    $request->get('event_type'), $request->get('create_from_task_customer_id'), $request->get('create_from_task_project_id'));
                $this->redirect($module, 'add', $add_link);
                break;
            case 'minitasks':
                $add_link = sprintf('communications=%d&communication_type=minitasks&operation=add#communications_container',
                                    $request->get('create_from_task_id'));
                $this->redirect($this->module, 'communications', $add_link);
                break;
        }
        return true;
    }

    /**
     * Activates or deactives the selected models
     *
     * @param string $status - activate or deactivate
     * @param mixed $ids - list of ids to be changed
     */
    private function _changeStatus($status, $ids = '') {
        //ids of the models to be activated/deactivated
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //activate/deactivate
        $result = Tasks::changeStatus($this->registry, $ids, $status);

        if ($result) {
            //change status successful
            $text = ($this->action == 'activate') ?
                      $this->i18n('message_items_activated') :
                      $this->i18n('message_items_deactivated');
            $this->registry['messages']->setMessage($text);

            foreach ($ids as $id) {
                $task = new Task($this->registry);
                $task->set('id', $id, true);
                Tasks_History::saveData($this->registry, array('model' => $task,'action_type' => $this->action));
            }
        } else {
            //change status failed
            $text = ($this->action == 'activate') ?
                      $this->i18n('error_items_not_activated') :
                      $this->i18n('error_items_not_deactivated');
            $this->registry['messages']->setError($text);

        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * status of models
     */
    private function _getStatus() {
        $request = &$this->registry['request'];

        //get the requested model ID
        $id = $request->get('id');
        $filters = array(
            'where' => array('t.id = ' . $request->get('id')),
            'model_lang' => $request->get('model_lang'),
            'sanitize' => true
        );
        $task = Tasks::searchOne($this->registry, $filters);

        //prepare substatuses
        require_once PH_MODULES_DIR . 'tasks/models/tasks.substatuses.factory.php';
        $filters = array(
            'model_lang' => $task->get('model_lang'),
            'where' => array(
                'ts.task_type = ' . $task->get('type'),
                'ts.active = 1'
            )
        );
        $substatuses = Tasks_Substatuses::search($this->registry, $filters);
        $task->set('substatuses', $substatuses, true);
        //get status settings for the current user
        $task->getInactiveStatuses();

        $setstatus = array();
        $setstatus['options'] = array('label' => $this->i18n('tasks_status_btn'), 'form_method' => 'post');
        $setstatus['model_id'] = $task->get('id');
        $setstatus['module'] = 'tasks';
        $setstatus['action'] = 'setstatus';
        $setstatus['module_param'] = $this->registry['module_param'];
        $setstatus['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
        $setstatus['show_form'] = 1;
        $this->viewer = new Viewer($this->registry);
        $viewDir = PH_MODULES_DIR . 'tasks/';
        if ($this->viewer->theme->isModern()) {
            $viewDir .= 'view/';
        }
        $this->viewer->setFrameset("{$viewDir}templates/_action_status.html");
        $this->viewer->data['task'] = $task;
        $this->viewer->data['available_action'] = $setstatus;
        $this->viewer->data['hide_status_label'] = true;
        $this->viewer->display();
        exit;
    }

    /**
     * change status of multiple models
     */
    private function _multiStatus($ids = '') {
        $request = &$this->registry['request'];

        //get the requested models ID
        if (empty($ids)) {
            $ids = $request->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //check for the same type models
        $filters = array('where' => array('t.id IN (' . implode(', ', $ids) . ')'),
                         'model_lang' => $this->registry->get('lang'),
                         'sanitize' => false);
        $tasks = Tasks::search($this->registry, $filters);

        //if no tasks or checked deleted tasks
        if (empty($tasks) || count($ids) != count($tasks)) {
            $this->registry['messages']->setError($this->i18n('error_no_tasks_or_deleted'));
            $this->registry['messages']->insertInSession($this->registry);

            $this->redirect($this->module, 'list');
        }

        $first_task = $tasks[0];

        $type = ($first_task->get('type'));
        foreach ($tasks as $task) {
            $type_i = $task->get('type');
            //different type
            if ($type != $type_i) {
                $this->registry['messages']->setError($this->i18n('error_different_types'));
                $this->registry['messages']->insertInSession($this->registry);

                $this->redirect($this->module, 'list');
                return true;
            }
        }

        require_once $this->modelsDir . 'tasks.types.factory.php';
        $filters = array('where' => array('tt.id = ' . $type),
                         'sanitize' => true);
        $task_type = Tasks_Types::searchOne($this->registry, $filters);
        $type_name_plural = $task_type && $task_type->get('name_plural') ? $task_type->get('name_plural') : $this->i18n('tasks');

        $result = Tasks::multiStatus($this->registry, $this);
        if ($result) {
            $this->actionCompleted = true;
            if ($result > 0) {
                $this->registry['messages']->setMessage($this->i18n('message_tasks_multistatus_success',
                    array(($result === true ? '' : $this->i18n('num_of_selected_items', array($result)) . ' ') . $type_name_plural)), '', -1);
            }
            if ($result !== true) {
                $this->registry['messages']->setWarning($this->i18n('warning_tasks_change_status_not_all', array($type_name_plural)));
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_tasks_multistatus_failed', array($type_name_plural)), '', -1);
        }
        $this->registry['messages']->insertInSession($this->registry);

        return true;
    }

    /**
     * Remind
     */
    private function _remind() {
        $request = &$this->registry['request'];

        //get the requested model ID
        if ($request->get('id')) {
            $id = $request->get('id');
        } else {
            $id = $request->get($this->action);
        }

        //get referer's action
        preg_match('/&tasks=([^&]*)&/', $_SERVER['HTTP_REFERER'], $matches);
        if (isset($matches[1])) {
            $after_action = $matches[1];
        } else {
            $after_action = 'view';
        }

        $filters = array('where' => array('t.id = ' . $id), 'model_lang' => $request->get('model_lang'));
        $task = Tasks::searchOne($this->registry, $filters);

        $saved_reminder = false;
        //check if details are submitted via POST
        if ($request->get('reminder_date')) {

            require_once PH_MODULES_DIR . 'events/models/events.factory.php';
            require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';

            //get reminder type
            $filters = array('where' => array('et.keyword=\'reminder\''), 'sanitize' => true);
            //get the event type reminder
            $event_type = Events_Types::searchOne($this->registry, $filters);
            $duration = $event_type->getDefaultDuration();

            if ($event_type) {
                if ($request->get('reminder_event_id')) {
                    //edit existing event
                    $filters = array('where' => array('e.id = ' . $request->get('reminder_event_id')));
                    $event = Events::searchOne($this->registry, $filters);
                    $event->set('name', sprintf($this->i18n('tasks_reminder_event_name'), $task->get('name')), true);
                    $event->set('description', $request->get('custom_message'), true);
                    $event->set('event_start', $request->get('reminder_date'), true);
                    $event->set('duration', $duration, true);
                    $event->set('event_end', date('Y-m-d H:i:00', strtotime($request->get('reminder_date')) + 60*$duration), true);
                    if ($event->save()) {

                    }
                } else {
                    //create new event
                    $event = Events::buildModel($this->registry);
                    $event->set('id', null, true);
                    $event->set('name', sprintf($this->i18n('tasks_reminder_event_name'), $task->get('name')), true);
                    $event->set('description', $request->get('custom_message'), true);
                    $event->set('event_start', $request->get('reminder_date'), true);
                    $event->set('duration', $duration, true);
                    $event->set('event_end', date('Y-m-d H:i:00', strtotime($request->get('reminder_date')) + 60*$duration), true);
                    $event->set('type', $event_type->get('id'), true);
                    $event->set('availability', 'available', true);
                    $event->set('customer', $task->get('customer'), true);
                    $event->set('branch', $task->get('branch'), true);
                    $event->set('contact_person', $task->get('contact_person'), true);
                    $event->set('trademark', $task->get('trademark'), true);
                    $event->set('project', $task->get('project'), true);

                    if ($event->save()) {
                        $record['link_to'] = $task->get('id');
                        $record['origin'] = 'task';
                        $record['link_type'] = 'parent';
                        $event->updateRelatives($record);
                    }
                }
            }
            if ($event && $event->get('id')) {
                //save reminder
                $request->set('selected_panel', 'date', true);
                if ($event->remind()) {
                    $saved_reminder = true;
                }
            }
        }

        if ($saved_reminder) {
            if ($request->get('reminder_event_id')) {
                $this->registry['messages']->setMessage($this->i18n('message_tasks_reminder_edit_success'), '', -1);
            } else {
                $this->registry['messages']->setMessage($this->i18n('message_tasks_reminder_add_success'), '', -1);
            }
        } else {
            $this->registry['messages']->setError($this->i18n('error_tasks_reminder_failed'), '', -1);
        }
        //manually set custom after action so that the navigation is redirected to previous action or view mode
        $this->registry['messages']->insertInSession($this->registry);
        $request->set('after_action', $after_action, 'get', true);
        $this->actionCompleted = true;
        if (!$this->registry->isRegistered('task')) {
            $this->registry->set('task', $task->sanitize());
        }

        return true;
    }

    /**
     * Deletes selected models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _delete($ids = '') {
        //ids of the models to be deleted
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //delete tasks
        $result = Tasks::delete($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_deleted'));

            foreach ($ids as $id) {
                $task = new Task($this->registry);
                $task->set('id', $id, true);
                Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'delete'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_deleted'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Restores selected deleted models
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _restore($ids = '') {
        //ids of the models to be restored
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //restore items
        $result = Tasks::restore($this->registry, $ids);

        if ($result) {
            //delete successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_restored'));

            foreach ($ids as $id) {
                $task = new Task($this->registry);
                $task->set('id', $id, true);
                Tasks_History::saveData($this->registry, array('model' => $task, 'action_type' => 'restore'));
            }
        } else {
            //delete failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_restored'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * Purges selected models
     * Attention: purge has no restore!
     *
     * @param mixed $ids - list of ids to be changed
     */
    private function _purge() {
        //ids of the models to be purged
        if (empty($ids)) {
            $ids = $this->registry['request']->get('items');
        }

        //check access
        $this->checkAccessOwnershipMultiple($ids);

        //purge items
        $result = Tasks::purge($this->registry, $ids);

        if ($result) {
            //purge successful
            $this->registry['messages']->setMessage( $this->i18n('message_items_purged'));
        } else {
            //purge failed
            $this->registry['messages']->setError( $this->i18n('error_items_not_purged'), '', -1);
        }

        $this->registry['messages']->insertInSession($this->registry);

        //set action as completed
        $this->actionCompleted = true;

        return true;
    }

    /**
     * filter for references
     */
    private function _filter() {
        $this->viewer = $this->getViewer();

        $autocomplete_filter = $this->registry['request']->get('autocomplete_filter');
        if ($autocomplete_filter) {
            if ($autocomplete_filter != 'session') {
                $filters = $this->_select();
                $this->viewer->data['autocomplete_filters'] = $filters;
            }
        }
        $this->viewer->setFrameset('frameset_pop.html');

        return true;
    }

    /**
     * fill configurator options
     */
    private function _fillConfiguratorOptions() {
        require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
        $config_task_patterns = array();
        if ($this->registry['request']->get('type')) {
            $config_task_patterns = Tasks_Configurators::search($this->registry,
                array('where' => array('tc.model_type = \'' . $this->registry['request']->get('type') . '\'',
                                       'tc.user_id IN (0, ' . ($this->registry['request']->get('user_id') ?: $this->registry['currentUser']->get('id')) . ')',
                                       'tc.lang = \'' . $this->registry->get('lang') . '\'')));
        }
        $config_task_patterns_options = array();
        $all = $this->i18n('all');
        $mine = $this->i18n('mine');
        foreach ($config_task_patterns as $configurator) {
            if ($configurator->get('user_id')) {
                $optgroup = $mine;
            } else {
                $optgroup = $all;
            }
            $config_task_patterns_options[$optgroup][] = array(
            'label' => $configurator->get('name'),
            'option_value' => $configurator->get('id'));
        }

        $action['options']['configTaskPatterns'] = $config_task_patterns_options;
        $cnfViewer = new Viewer($this->registry);
        $cnfViewer->setFrameset('_conf_select.html');
        $cnfViewer->data['available_action'] = $action;
        print $cnfViewer->fetch();
        exit;
    }

    /**
     * Sets custom actions definitions
     *
     * @param array $action_defs - custom action definitions
     * @return array - all available actions with their details
     */
    public function getActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        $this->settings_assign = array();
        if ($this->model && $this->model->get('type')) {
            $this->settings_assign = $this->registry['config']->getParamAsArray('tasks', 'assignment_types_' . $this->model->get('type'));
        }

        //prepare add options types
        require_once($this->modelsDir . 'tasks.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sanitize' => true,
                         'sort' => array('tti18n.name ASC'),
                         'where' => array('tt.active = 1'));

        $customize = '';
        $found = 0;

        $custom_filters = array();
        if ($this->registry['request']->get('type')) {
            $customize = 'tt.id="' . $this->registry['request']->get('type') . '"';
            $found++;
        } else if ($this->registry['request']->get('type_section')) {
            $customize = 'tt.type_section="' . $this->registry['request']->get('type_section') . '"';
            $found++;
        } else if ($this->registry['request']->isRequested('search_fields')) {
            $custom_filters['search_fields'] = $this->registry['request']->get('search_fields');
            $custom_filters['compare_options'] = $this->registry['request']->get('compare_options');
            $custom_filters['values'] = $this->registry['request']->get('values');
        } else if ($this->registry['session']->isRequested($this->action . '_task')) {
            $custom_filters = $this->registry['session']->get($this->action . '_task');
        }

        if (!empty($custom_filters)) {
            // shows if there is a type defined and if so doesn't add the type section filter
            $type_defined = false;
            if (isset($custom_filters['search_fields'])) {
                foreach ($custom_filters['search_fields'] as $key => $where) {
                    if (preg_match('#^t\.type#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                        $customize = 'tt.id="' . $custom_filters['values'][$key] . '"';
                        if ($type_defined) {
                            $found++;
                        } else {
                            $type_defined = true;
                            $found = 1;
                        }
                    } else if (preg_match('#tt\.type_section#', $where) && !preg_match('#\!\=#', $custom_filters['compare_options'][$key])) {
                        if (! $type_defined) {
                            $customize = 'tt.type_section="' . $custom_filters['values'][$key] . '"';
                            $found++;
                        }
                    }
                }
            } elseif (isset($custom_filters['hidden_type'])) {
                $customize = 'tt.id = ' . $custom_filters['hidden_type'];
                $found++;
            } elseif (isset($custom_filters['hidden_type_section'])) {
                $customize = 'tt.type_section = ' . $custom_filters['hidden_type_section'];
                $found++;
            }
        }

        // if there is a model the only available options for add and multiadd
        // will be the options for the current type or the current section
        if ($found == 1 && $customize) {
            $filters['where'][] = $customize;
        }

        $taskTypes = Tasks_Types::search($this->registry, $filters);

        $_options_types = array();
        $_options_types_multiadd = array();
        $first_type = $first_type_multiadd = (int)$this->registry['currentUser']->getPersonalSettings('tasks', 'default_task_type');
        $first_type_config = (int)$this->registry['currentUser']->getPersonalSettings('tasks', 'default_task_type_config');

        foreach ($taskTypes as $type) {
            $type_permition_add = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
            $type_permition_multiadd = $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd');
            if ($type_permition_add) {
                $_options_types[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
            }
            if ($type_permition_multiadd) {
                $_options_types_multiadd[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
            }
        }

        // check if default type is among available options,
        // otherwise set first available option as selected type
        if ($_options_types) {
            $first_type_found = false;
            if ($first_type) {
                foreach ($_options_types as $ot) {
                    if ($first_type == $ot['option_value']) {
                        $first_type_found = true;
                        break;
                    }
                }
            }
            if (!$first_type_found) {
                $first_type = $_options_types[0]['option_value'];
                $first_type_config = '';
            }
        } else {
            $first_type = $first_type_config = '';
        }
        if ($_options_types_multiadd) {
            $first_type_found = false;
            if ($first_type_multiadd) {
                foreach ($_options_types_multiadd as $ot) {
                    if ($first_type_multiadd == $ot['option_value']) {
                        $first_type_found = true;
                        break;
                    }
                }
            }
            if (!$first_type_found) {
                $first_type_multiadd = $_options_types_multiadd[0]['option_value'];
            }
        } else {
            $first_type_multiadd = '';
        }

        //check if the model is defined
        //in order to remove some of the actions if necessary

        if ($this->action == 'filter') {
            $this->actionDefinitions = array('filter');
            $this->afterActionDefinitions = array();
        }

        $actions = parent::getActions($action_defs);

        //change some search actions
        if ($this->action == 'myrecords') {
            $actions['search']['action'] = 'myrecords';
        } elseif ($this->action == 'myobserver') {
            $actions['search']['action'] = 'myobserver';
        } elseif ($this->action == 'myresponsible') {
            $actions['search']['action'] = 'myresponsible';
        } elseif ($this->action == 'myassigned') {
            $actions['search']['action'] = 'myassigned';
        }

        if ($this->model) {
            unset($actions['myassigned']);
            unset($actions['myresponsible']);
            unset($actions['myobserver']);
            unset($actions['myrecords']);
        }

        $theme = $this->registry['theme'];
        $ThemeCanProvideIcons = is_callable([$theme, 'getIconForAction']);

        // list action
        if (isset($actions['list'])) {
            // extend the link for list to clear type sections and types
            if ($this->model && $this->model->get('id')) {
                $actions['list']['ajax_no'] = 1;
                $actions['list']['drop_menu'] = true;

                $actions['list']['options']['previous_list']['img'] = 'list';
                $actions['list']['options']['previous_list']['label'] = $this->i18n('previous_list');
                $actions['list']['options']['previous_list']['url'] = $actions['list']['url'];
                if (isset($actions['search'])) {
                    $actions['list']['options']['previous_search']['img'] = 'search';
                    if ($ThemeCanProvideIcons) {
                        $actions['list']['options']['previous_search']['icon'] = $theme->getIconForAction('search');
                    }
                    $actions['list']['options']['previous_search']['label'] = $this->i18n('previous_search');
                    $actions['list']['options']['previous_search']['url'] = $actions['search']['url'];
                }

                $actions['list']['options']['all_tasks']['img'] = 'tasks';
                $actions['list']['options']['all_tasks']['label'] = $this->i18n('tasks_all_tasks');
                $actions['list']['options']['all_tasks']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';

                $actions['list']['url'] = $actions['list']['url'] . sprintf('&amp;type=%d&amp;type_section=', $this->model->get('type'));
            } else {
                $actions['list']['url'] = $actions['list']['url'] . '&amp;type=&amp;type_section=';
            }
        }

        if ($this->model && $this->model->get('id') && isset($actions['remind']) && $this->checkActionPermissions('events' . PH_REMINDER_EVENT_TYPE, 'add')) {

            $reminderTypes = array(
                'toaster' => $this->i18n('tasks_reminder_toaster'),
                'email' => $this->i18n('tasks_reminder_email'),
                'both' => $this->i18n('tasks_reminder_both')
            );

            $reminder = $this->registry['currentUser']->getReminderModel(array('model_name' => 'task',
                                                            'model_id' => $this->model->get('id')));
            $actions['remind']['options'] = array('label' => $this->i18n('remind'));
            $actions['remind']['ajax_no'] = 1;
            $actions['remind']['template'] = '_action_remind.html';
            $actions['remind']['model_id'] = $this->model->get('id');
            $actions['remind']['options']['reminderTypes'] = $reminderTypes;
            $actions['remind']['options']['reminder'] = $reminder;
        } else {
            unset($actions['remind']);
        }

        if (isset($actions['add']) && !empty($_options_types)) {
            //prepare tasks configurators
            require_once PH_MODULES_DIR . 'tasks/models/tasks.configurators.factory.php';
            $config_task_patterns = Tasks_Configurators::search($this->registry,
                array('where' => array('tc.model_type = \'' . $first_type . '\'',
                                       'tc.user_id IN (0, ' . $this->registry['currentUser']->get('id') . ')',
                                       'tc.lang = \'' . $this->registry->get('lang') . '\'')));
            $config_task_patterns_options = array();
            $all = $this->i18n('all');
            $mine = $this->i18n('mine');
            foreach ($config_task_patterns as $configurator) {
                if ($configurator->get('user_id')) {
                    $optgroup = $mine;
                } else {
                    $optgroup = $all;
                }
                $config_task_patterns_options[$optgroup][] = array(
                    'label' => $configurator->get('name'),
                    'option_value' => $configurator->get('id')
                );
            }

            $actions['add']['options']['types'] = $_options_types;
            $actions['add']['options']['configTaskPatterns'] = $config_task_patterns_options;
            $actions['add']['options']['default_task_type'] = $first_type;
            $actions['add']['options']['default_task_type_config'] = $first_type_config;
        } else {
            unset($actions['add']);
        }

        //multiadd action
        if (isset($actions['multiadd']) && !empty($_options_types_multiadd)) {
            $actions['multiadd']['options']['types'] = $_options_types_multiadd;
            $actions['multiadd']['options']['default_task_type'] = $first_type_multiadd;
            $actions['multiadd']['ajax_no'] = 1;
        } else {
            unset($actions['multiadd']);
        }

        //add action
        if (isset($actions['adds'])) {
            $actions['adds']['label'] = $this->i18n('add');
            $actions['adds']['ajax_no'] = 1;

            if($theme->isModern()) {
                $actions['adds']['template'] = PH_MODULES_DIR . $this->module . '/view/templates/_action_add.html';
            } else {
                $actions['adds']['template'] = PH_MODULES_DIR . $this->module . '/templates/_action_add.html';
            }
            if (isset($actions['add'])) {
                $actions['adds']['options']['add'] = $actions['add'];
                unset ($actions['add']);
            }
            if (isset($actions['multiadd'])) {
                $actions['adds']['options']['multiadd'] = $actions['multiadd'];
                unset ($actions['multiadd']);
            }
            if (empty($actions['adds']['options'])) {
                unset ($actions['adds']);
            } else {
                // check if there is only one selected type and redirects the add link to it
                $selected_action = '';
                $selected_type = '';
                $selected_configurator = '';
                if (isset($actions['adds']['options']['add']) && !isset($actions['adds']['options']['multiadd'])) {
                    // if only ADD action is available
                    if (count($actions['adds']['options']['add']['options']['types'])==1 && isset($actions['adds']['options']['add']['options']['types'][0])) {
                        if ($actions['adds']['options']['add']['options']['default_task_type'] == $actions['adds']['options']['add']['options']['types'][0]['option_value'] && $actions['adds']['options']['add']['options']['default_task_type_config']) {
                            $selected_configurator = $actions['adds']['options']['add']['options']['default_task_type_config'];
                        }

                        $selected_action = 'add';
                        $selected_type = $actions['adds']['options']['add']['options']['types'][0]['option_value'];
                    }
                } else if (!isset($actions['adds']['options']['add']) && isset($actions['adds']['options']['multiadd'])) {
                    // if only MULTIADD action is available
                    if (count($actions['adds']['options']['multiadd']['options']['types'])==1 && isset($actions['adds']['options']['multiadd']['options']['types'][0])) {
                        if ($this->registry['currentUser']->getPersonalSettings('tasks', 'default_task_type') == $actions['adds']['options']['multiadd']['options']['types'][0]['option_value'] && $this->registry['currentUser']->getPersonalSettings('tasks', 'default_task_type_config')) {
                            $selected_configurator = $this->registry['currentUser']->getPersonalSettings('tasks', 'default_task_type_config');
                        }
                        $selected_action = 'multiadd';
                        $selected_type = $actions['adds']['options']['multiadd']['options']['types'][0]['option_value'];
                    }
                }

                // change the link if necessary
                if ($selected_action && $selected_type) {
                    $actions['adds']['url'] .= '&amp;operation=' . $selected_action . '&amp;type=' . $selected_type . '&amp;configurator=' . $selected_configurator;
                    $actions['adds']['options'] = '';
                    unset($actions['adds']['ajax_no']);
                    unset($actions['adds']['template']);
                }
            }
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['assign']) && !empty($this->settings_assign)) {
            $actions['assign']['options'] = 0;
        } else {
            unset($actions['assign']);
        }

        if (isset($actions['observer']) && in_array('observer', $this->settings_assign)) {
            $actions['observer']['options'] = 0;

            if (Tasks::checkObserver($this->registry, $this->model)) {
                $actions['observer']['img'] = 'stopobserve';
                if ($ThemeCanProvideIcons) {
                    $actions['observer']['icon'] = $theme->getIconForAction('stopobserve');
                }
                $actions['observer']['label'] = $this->i18n('stopobserve');
            } else {
                $actions['observer']['img'] = 'observe';
                if ($ThemeCanProvideIcons) {
                    $actions['observer']['icon'] = $theme->getIconForAction('observe');
                }
                $actions['observer']['label'] = $this->i18n('observe');
            }
        } else {
            unset($actions['observer']);
        }

        //if the current user can add timesheets or has exclusive permission for list then the current user can list the timesheets
        if (isset($actions['timesheets']) && $this->model && $this->model->get('id') && $this->model->checkPermissions('viewtimesheets')) {
            if ($this->model->checkPermissions('addtimesheet')) {
                require_once PH_MODULES_DIR . 'tasks/models/tasks.timesheetsconfigurators.factory.php';
                $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                        array('where' => array('tc.added_by=' . $this->registry['currentUser']->get('id'),
                                               'tc.model_type=\'' . $this->model->get('type') . '\'',
                                               'tc.model=\'' . strtolower($this->model->modelName) . '\''),
                              'model_lang' => $this->registry['lang'],
                              'sanitize' => true));
                $configTimesheetPatterns = array();
                if (!$timesheetsConfigurators) {
                    $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                            array('where' => array('tc.added_by=' . $this->registry['currentUser']->get('id'),
                                                   'tc.model=\'all\''),
                                  'sanitize' => true));
                }
                if (!$timesheetsConfigurators) {
                    $timesheetsConfigurators = Tasks_TimesheetsConfigurators::search($this->registry,
                            array('where' => array('tc.added_by=0',
                                                   'tc.model=\'all\''),
                                  'sanitize' => true));
                }
                foreach ($timesheetsConfigurators as $timesheetConfigurator) {
                    $configTimesheetPatterns[] = array(
                        'label' => $timesheetConfigurator->get('name'),
                        'option_value' => $timesheetConfigurator->get('id'),
                        'img' => 'addtimesheet',
                        'icon' => $ThemeCanProvideIcons?$theme->getIconForAction('addtimesheet'):'',
                        'url' => $actions['timesheets']['url'] . '&amp;configurator=' . $timesheetConfigurator->get('id') . '#add_timesheet');
                }
                if ($configTimesheetPatterns) {
                    General::injectInArray(array('addtimesheet' => array()), $actions, 'after', 'timesheets');
                    $actions['addtimesheet']['action'] = 'addtimesheet';
                    $actions['addtimesheet']['name'] = 'addtimesheet';
                    $actions['addtimesheet']['label'] = $this->i18n('addtimesheet');
                    $actions['addtimesheet']['icon'] = $ThemeCanProvideIcons?$theme->getIconForAction('addtimesheet'):'';
                    $actions['addtimesheet']['url'] = $configTimesheetPatterns[0]['url'];
                    if (count($configTimesheetPatterns) > 1) {
                        $actions['addtimesheet']['ajax_no'] = 1;
                        $actions['addtimesheet']['drop_menu'] = true;
                        $actions['addtimesheet']['hide_label'] = true;
                        $actions['addtimesheet']['icon'] = $ThemeCanProvideIcons?$theme->getIconForAction('addtimesheet'):'';
                        $actions['addtimesheet']['options'] = $configTimesheetPatterns;
                    }
                }
            }
        } else {
            unset($actions['timesheets']);
        }

        // communications action
        if (isset($actions['communications'])) {
            $actions['communications']['ajax_no'] = 1;
            $actions['communications']['drop_menu'] = true;
            $actions['communications']['hide_label'] = true;

            if (isset($actions['emails']) || isset($actions['comments']) || isset($actions['minitasks'])) {
                if (isset($actions['comments'])) {
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['comments']['icon'] = $theme->getIconForAction('comments');
                    }
                    $actions['communications']['options']['comments']['img'] = 'comments';
                    $actions['communications']['options']['comments']['label'] = $actions['comments']['label'];
                    $actions['communications']['options']['comments']['url'] = $actions['communications']['url'] . '&amp;communication_type=comments';
                    unset($actions['comments']);
                }
                if (isset($actions['emails'])) {
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['emails']['icon'] = $theme->getIconForAction('emails');
                    }
                    $actions['communications']['options']['emails']['img'] = 'email';
                    $actions['communications']['options']['emails']['label'] = $this->i18n('tasks_action_email');
                    $actions['communications']['options']['emails']['url'] = $actions['communications']['url'] . '&amp;communication_type=emails';
                    unset($actions['emails']);
                }
                if (isset($actions['minitasks'])) {
                    if ($ThemeCanProvideIcons) {
                        $actions['communications']['options']['minitasks']['icon'] = $theme->getIconForAction('minitasks');
                    }
                    $actions['communications']['options']['minitasks']['img'] = 'minitasks';
                    $actions['communications']['options']['minitasks']['label'] = $actions['minitasks']['label'];
                    $actions['communications']['options']['minitasks']['url'] = $actions['communications']['url'] . '&amp;communication_type=minitasks';
                    // do not unset yet, action is used in "Create" as well
                    //unset($actions['minitasks']);
                }
            } else {
                unset($actions['communications']);
            }
        } else {
            if (isset($actions['emails'])) {
                unset($actions['emails']);
            }
            if (isset($actions['comments'])) {
                unset($actions['comments']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if ($this->model && $this->model->get('id') && isset($actions['setstatus'])) {
            //prepare substatuses
            require_once PH_MODULES_DIR . 'tasks/models/tasks.substatuses.factory.php';
            $filters = array(
                'model_lang' => $this->model->get('model_lang'),
                'where' => array(
                    'ts.task_type = ' . $this->model->get('type'),
                    'ts.active = 1'
                )
            );
            $substatuses = Tasks_Substatuses::search($this->registry, $filters);
            $this->model->set('substatuses', $substatuses, true);
            //get status settings for the current user
            $this->model->getInactiveStatuses();

            $actions['setstatus']['options'] = array('label' => $this->i18n('tasks_status_btn'), 'form_method' => 'post');
            $actions['setstatus']['ajax_no'] = 1;
            $actions['setstatus']['template'] = PH_MODULES_DIR . 'tasks/templates/_action_status.html';
            $actions['setstatus']['default_portal_comment'] = $this->registry['config']->getParamFromDB('comments', 'default_portal');
            $actions['setstatus']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['setstatus']);
        }

        if ($this->model && $this->model->get('id') && isset($actions['attachments'])) {
            //prepare attachments
            $actions['attachments']['options'] = array('form_method' => 'post');
            $actions['attachments']['template'] = PH_MODULES_DIR . $this->module . ($theme->isModern() ? '/view':'') . '/templates/_action_attachments.html';
            $actions['attachments']['model_id'] = $this->model->get('id');
        } else {
            unset($actions['attachments']);
        }

        if ($this->model && $this->model->get('id') && isset($actions['create'])) {
            $actions['create']['label'] = $this->i18n('create');
            $actions['create']['ajax_no'] = 1;
            $actions['create']['template'] = PH_MODULES_DIR . 'tasks/templates/_action_create.html';

            if (isset($actions['documents']) && $this->checkActionPermissions('documents', 'add')) {

                $direction_labels[PH_DOCUMENTS_INCOMING] = $this->i18n('tasks_documents_incoming');
                $direction_labels[PH_DOCUMENTS_OUTGOING] = $this->i18n('tasks_documents_outgoing');
                $direction_labels[PH_DOCUMENTS_INTERNAL] = $this->i18n('tasks_documents_internal');

                require_once PH_MODULES_DIR . 'documents/models/documents.types.factory.php';
                $filters = array('where' => array('dt.active = 1',
                                                  'dt.inheritance = 0'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('dti18n.name ASC'));
                $types_all = Documents_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('documents' . $type->get('id'), 'add')) {
                        $_options[$direction_labels[$type->get('direction')]][] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    $first_key = array_keys($_options);
                    $first_key = reset($first_key);
                    //prepare documents options
                    $actions['documents']['options'] = array (
                        array (
                            'custom_id'     => 'document_type__',
                            'name'          => 'document_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('tasks_document_type'),
                            'help'          => $this->i18n('tasks_document_type'),
                            'optgroups'     => $_options,
                            'value'         => $this->registry['request']->get('document_type') ?: $_options[$first_key][0]['option_value']
                        )
                    );
                    $actions['documents']['label'] = $this->i18n('tasks_document');
                }
            }

            if (!empty($actions['documents']['options'])) {
                $actions['create']['options']['documents'] = $actions['documents'];
            }
            unset ($actions['documents']);

            if (isset($actions['events']) && $this->checkActionPermissions('events', 'add')) {
                require_once PH_MODULES_DIR . 'events/models/events.types.factory.php';
                $filters = array('where' => array('et.keyword NOT IN (\'reminder\', \'plannedtime\')',
                                                  'et.active = 1'),
                                 'model_lang' => $this->getModelLang(),
                                 'sanitize' => true,
                                 'sort' => array('eti18n.name ASC'));
                $types_all = Events_Types::search($this->registry, $filters);

                $_options = array();
                foreach ($types_all as $type) {
                    if ($this->checkActionPermissions('events' . $type->get('id'), 'add')) {
                        $_options[] = array(
                            'label' => $type->get('name'),
                            'option_value' => $type->get('id'));
                    }
                }

                if (!empty($_options)) {
                    //prepare event options
                    $actions['events']['options'] = array (
                        array (
                            'custom_id'     => 'event_type__',
                            'name'          => 'event_type',
                            'type'          => 'dropdown',
                            'required'      => 1,
                            'label'         => $this->i18n('tasks_event_type'),
                            'help'          => $this->i18n('tasks_event_type'),
                            'options'       => $_options,
                            'value'         => $this->registry['request']->get('event_type') ?: $_options[0]['option_value']
                        )
                    );
                    $actions['events']['label'] = $this->i18n('tasks_event');
                }
            }

            if (!empty($actions['events']['options'])) {
                $actions['create']['options']['events'] = $actions['events'];
            }
            unset ($actions['events']);

            if (isset($actions['minitasks'])) {
                if ($this->registry['currentUser']->checkRights('minitasks', 'add') &&
                !($this->model->get('status') == 'finished' && $this->model->get('requires_completed_minitasks'))) {
                    $actions['minitasks']['label'] = $this->i18n('tasks_minitask');
                    $actions['create']['options']['minitasks'] = $actions['minitasks'];
                }
                unset ($actions['minitasks']);
            }

            if (!$actions['create']['options']) {
                unset ($actions['create']);
            } else {
                // get customer's, projects's and task's ID
                $actions['create']['customer'] = $this->model->get('customer');
                $actions['create']['project'] = $this->model->get('project');
                $actions['create']['task'] = $this->model->get('id');
            }
        } else {
            unset ($actions['create']);

            if (isset($actions['documents'])) {
                unset($actions['documents']);
            }
            if (isset($actions['events'])) {
                unset($actions['events']);
            }
            if (isset($actions['minitasks'])) {
                unset($actions['minitasks']);
            }
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['manage_outlooks'])) {
            $actions['manage_outlooks']['options'] = 1;
        } else {
            unset($actions['manage_outlooks']);
        }

        if (!$this->model && ($this->registry->get('action') == 'list' || $this->registry->get('action') == 'search') &&
        isset($actions['printlist'])) {
            //get all print list patterns
            require_once PH_MODULES_DIR . 'patterns/models/patterns.factory.php';
            $filters_patterns = array(
                'where' => array(
                    'p.model = \'' . $this->modelName . '\'',
                    'p.active = 1',
                    'p.list = 1'
                ),
                'sort' => array('p.position != 0 DESC', 'p.position ASC', 'p.id ASC'),
                'model_lang' => $this->registry['lang'],
                'sanitize' => true
            );

            if ($found == 1 && $customize) {
                $ts_id = preg_replace('#.*=\s*(\'|\")?(\d+)(\'|\")?#', '$2', $customize);
                if (preg_match('#^tt\.id#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 0';
                } elseif (preg_match('#^tt\.type_section#', $customize)) {
                    $filters_patterns['where'][] = 'p.model_type = "' . $ts_id . '"';
                    $filters_patterns['where'][] = 'p.section = 1';
                }
            } else {
                $filters_patterns['where'][] = 'CONVERT(p.model_type, SIGNED INTEGER) = 0';
            }
            if ($this->registry['currentUser']->get('is_portal')) {
                $filters_patterns['where'][] = 'p.is_portal = 1';
            }
            $patterns_list = Patterns::search($this->registry, $filters_patterns);

            $additional_query_string = '&amp;session_param=' . $this->registry->get('action') . '_' . strtolower($this->modelName);

            $patterns_options = array();
            foreach ($patterns_list as $pattern) {
                $patterns_options[] = array(
                    'id'        => $pattern->get('id'),
                    'label'     => $pattern->get('name'),
                    'img'       => $pattern->getIcon(),
                    'url'       => $actions['printlist']['url'] . '&amp;pattern=' . $pattern->get('id') . $additional_query_string,
                    'target'    => '_blank',
                    'onclick'   => 'return confirmPrintlist();'
                );
            }

            if (empty($patterns_options)) {
                unset($actions['printlist']);
            } else {
                if (count($patterns_options) == 1) {
                    // if there is only one pattern, its options are taken for the button
                    list($first_pattern) = $patterns_options;
                    $actions['printlist']['url'] = $first_pattern['url'];
                    $actions['printlist']['onclick'] = $first_pattern['onclick'];
                    $actions['printlist']['img'] = isset($first_pattern['img']) ? $first_pattern['img'] : 'printlist';
                } else {
                    $actions['printlist']['url'] = '#';
                    $actions['printlist']['img'] = 'printlist';
                }

                $actions['printlist']['drop_menu'] = true;
                $actions['printlist']['no_tab'] = true;
                $actions['printlist']['label'] = '';
                $actions['printlist']['target'] = '_blank';

                //do not set options if the pattern is only one
                if (count($patterns_options) <= 1) {
                    $patterns_options = array();
                } else {
                    $actions['printlist']['img'] .= '_plus';
                }
                $actions['printlist']['options'] = $patterns_options;
            }
        } else {
            unset($actions['printlist']);
        }

        if ($this->registry->get('action') == 'statements') {
            if (isset($actions['export'])) {
                unset ($actions['export']);
            }
        }

        if (isset($actions['tag']) && $this->model && $this->model->get('id') && $this->model->getAvailableTags()) {

            $this->model->getTags();

            if ($this->model->checkPermissions('tags_view') && ($this->model->get('tags') && array_intersect($this->model->get('tags'), array_keys($this->model->get('available_tags'))) || $this->model->checkPermissions('tags_edit'))) {
                $actions['tag']['options'] = array('label' => $this->i18n('confirm_tags'));
                $actions['tag']['ajax_no'] = 1;
                $actions['tag']['template'] = '_action_tag.html';
                $actions['tag']['model_id'] = $this->model->get('id');
            } else {
                unset($actions['tag']);
            }
        } else {
            unset($actions['tag']);
        }

        //sets the actions for the right and left submenu
        $_left_menu = array();
        $_right_menu = array();
        $_upper_right_menu = array();
        $_filter_menu = array();

        $_page_menu = [
            'general' => [],
            'context' => [],
            'quick' => [],
            'infriquent' => [],
        ];

        foreach ($actions as $key => $action) {
            $flag_match = false;
            if (in_array($key, $this->actionDefinitionsLeft)) {
                $_left_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsRight)) {
                $_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionDefinitionsUpRight)) {
                $_upper_right_menu[$key] = $action;
                $flag_match = true;
            }
            if (in_array($key, $this->actionFilterDefinitions)) {
                if ($key == 'myassigned') {
                    $action['img'] = 'tasks_myassigned';
                } elseif ($key == 'myresponsible') {
                    $action['img'] = 'tasks_myresponsible';
                } elseif ($key == 'myobserver') {
                    $action['img'] = 'tasks_myobserver';
                } elseif ($key == 'myrecords') {
                    $action['img'] = 'tasks_mytasks';
                }
                if ($ThemeCanProvideIcons) {
                    $action['icon'] = $theme->getIconForAction($key);
                }
                $_filter_menu[$key] = $action;
                $flag_match = true;
            }
            if ($flag_match) {
                unset($actions[$key]);
            }

            foreach ($this->actionListPageMenu as $k2=>$v2) {
                if (in_array($key, $v2)) {
                    $_page_menu[$k2][$key] = $action;
                }
            }
        }

        if (!empty($_page_menu['infriquent'])) {
            $_page_menu['context']['infriquent'] = [
                'name' => 'infriquent_menu',
                'icon' => 'more_vert',
                'url' => '#',
                'drop_menu' => true,
                'options' => $_page_menu['infriquent'],
            ];
        }

        // check the current action and sets the alternative actions
        // for view, edit
        if ($this->registry->get('action') == 'view') {
            //if the current action is 'view'
            if (array_key_exists('view', $_right_menu)) {
                unset ($_right_menu['view']);
            }
            if (array_key_exists('edit', $_left_menu)) {
                unset ($_left_menu['edit']);
            }
        } else if ($this->registry->get('action') == 'edit') {
            //if the current action is 'edit'
            if (array_key_exists('edit', $_right_menu)) {
                unset ($_right_menu['edit']);
            }
            if (array_key_exists('view', $_left_menu)) {
                unset ($_left_menu['view']);
            }
        } else {
            //if the current action is not edit or view
            if (array_key_exists('view', $_left_menu)) {
                if (array_key_exists('view', $_right_menu)) {
                    unset ($_right_menu['view']);
                }
                if (array_key_exists('edit', $_left_menu)) {
                    unset ($_left_menu['edit']);
                }
            } else if (array_key_exists('edit', $_left_menu)) {
                if (array_key_exists('edit', $_right_menu)) {
                    unset ($_right_menu['edit']);
                }
                if (array_key_exists('view', $_left_menu)) {
                    unset ($_left_menu['view']);
                }
            }
        }

        //sets custom icon and label for view and edit
        if ($this->model && ! empty($_left_menu)) {
            foreach ($_left_menu as $key => $action_def) {
                if ($key == 'view' || $key == 'edit') {
                    $_left_menu[$key]['label'] = $this->i18n('data');
                    $_left_menu[$key]['img'] = 'custom_data';
                }
            }
        }

        if ($this->model && $this->model->get('id')) {
            if (isset($actions['export'])) {
                unset ($actions['export']);
            }
            if (isset($actions['search'])) {
                unset ($actions['search']);
            }
            if (isset($actions['statements'])) {
                unset ($actions['statements']);
            }
        }

        $this->registry->set('available_actions_left', $_left_menu, true);
        $this->registry->set('available_actions_right', $_right_menu, true);
        $this->registry->set('available_page_actions', $_page_menu, true);
        if ($this->registry['available_actions_filter'] && is_array($this->registry['available_actions_filter'])) {
            $_filter_menu = $_filter_menu + $this->registry['available_actions_filter'];
        }
        $this->registry->set('available_actions_filter', $_filter_menu, true);

        $modernizedActions = [
            'add',
            'adds',
            'multiadd',
            'list',
            'view',
            'edit',
            'assign',
            'relatives',
            'allocate',
            'timesheets',
            'communications',
            'dependencies',
            'history',
        ];

        if (!$theme->isModern() || !in_array($this->action, $modernizedActions)) {
            // Not used in Evolution theme, made available for compatibility reasons in some actions
            $this->registry->set('available_actions_upper_right', $_upper_right_menu, true);
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     *
     * @param string $action_name - action name
     * @return array - all available actions with their details
     */
    public function getActionOptions($action_name = '') {
        //get model for this class
        $this->getModel();

        if ($this->action == 'filter') {
            $this->actionDefinitions = array('filter');
            $this->afterActionDefinitions = array();
        }

        //get permissions of the currently logged user
        $this->getUserPermissions();

        $actions = parent::getActions(array($action_name));

        //prepare add options types
        require_once($this->modelsDir . 'tasks.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang());
        $taskTypes = Tasks_Types::search($this->registry, $filters);

        $_options_types = array();
        foreach ($taskTypes as $type) {
            $_options_types[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
        }

        if (isset($actions['observer'])) {
            if (Tasks::checkObserver($this->registry, $this->model->get('id'))) {
                $actions['observer']['img'] = 'stopobserve';
                $actions['observer']['label'] = $this->i18n('stopobserve');
            } else {
                $actions['observer']['img'] = 'observe';
                $actions['observer']['label'] = $this->i18n('observe');
            }
        }

        if (isset($actions['add'])) {
            //prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'type__',
                    'name' => 'type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('tasks_type'),
                    'help' => $this->i18n('tasks_add_legend'),
                    'options' => $_options_types,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : @$_options_types[0]['option_value']),
            );
            $actions['add']['options'] = $add_options;
        }

        return $actions;
    }

    /**
     * Sets custom actions definitions
     *
     * @param array $action_defs - custom action definitions
     * @return array - all available actions with their details
     */
    public function getAfterActions($action_defs = array()) {
        //get model for this class
        $this->getModel();

        //get the after actions
        $actions = parent::getAfterActions($action_defs);

        //prepare add options types
        require_once($this->modelsDir . 'tasks.types.factory.php');
        $filters = array('model_lang' => $this->getModelLang(),
                         'sanitize' => true,
                         'sort' => array('tti18n.name ASC'),
                         'where' => array('tt.active = 1'));
        $taskTypes = Tasks_Types::search($this->registry, $filters);

        //get permissions of the currently logged user
        $this->getUserPermissions();

        if (isset($actions['assign']) && !empty($this->settings_assign)) {
            $actions['assign']['options'] = 0;
        } else {
            unset($actions['assign']);
        }

        $_options_add = array();
        foreach ($taskTypes as $type) {
            $type_permition = $this->checkActionPermissions($this->module . $type->get('id'), 'add');
            $type_permition_multiadd = $this->checkActionPermissions($this->module . $type->get('id'), 'multiadd');
            if ($type_permition) {
                $_options_add[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
            }
            if ($type_permition_multiadd) {
                $_options_types_multiadd[] = array(
                'label' => $type->get('name'),
                'option_value' => $type->get('id'));
            }
        }

        if (isset($actions['add']) && !empty($_options_add)) {
            //prepare add options
            $add_options = array (
                array (
                    'custom_id' => 'type____',
                    'name' => 'aa1_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('tasks_type'),
                    'help' => $this->i18n('tasks_add_legend'),
                    'options' => $_options_add,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : ''),
            );
            $actions['add']['options'] = $add_options;
        } else {
            unset($actions['add']);
        }

        //multiadd action
        if (isset($actions['multiadd']) && !empty($_options_types_multiadd)) {
            //prepare multiadd_options options
            $multiadd_options = array (
                array (
                    'custom_id' => 'type_____',
                    'name' => 'aa2_type',
                    'type' => 'dropdown',
                    'required' => 1,
                    'label' => $this->i18n('tasks_type'),
                    'help' => $this->i18n('tasks_add_legend'),
                    'options' => $_options_types_multiadd,
                    'value' => ($this->registry['request']->get('type')) ?
                                $this->registry['request']->get('type') : ''),
            );
            $actions['multiadd']['options'] = $multiadd_options;
        } else {
            unset($actions['multiadd']);
        }

        if (isset($actions['timesheets']) && $this->model && $this->model->get('id') && $this->model->checkPermissions('viewtimesheets')) {

        } else {
            unset($actions['timesheets']);
        }

        return $actions;
    }

    /**
     * Get main variables for multi add/edit - default or from config
     *
     * @param object $model - the first model in the list
     * @return array - data for basic vars
     */
    private function getBasicVarsDefs($model = null) {
        $type = $this->registry['tasktype'];

        //gets the counter
        require_once $this->modelsDir . 'tasks.counters.factory.php';
        $filters = array('where' => array('tc.id = ' . $type->get('counter'),
                                          'tc.deleted IS NOT NULL'),
                         'sanitize' => true);
        $counter = Tasks_Counters::searchOne($this->registry, $filters);

        //get the fields (columns) for the multi action
        $this->multiFields = array();
        if ($this->registry['config']->getParam('tasks', $this->action . '_' . $type->get('id'))) {
            //get settings for this type
            $this->multiFields = explode(', ', $this->registry['config']->getParam('tasks', $this->action . '_' . $type->get('id')));
        } elseif ($this->registry['config']->getParam('tasks', $this->action)) {
            //get settings for all tasks
            $this->multiFields = explode(', ', $this->registry['config']->getParam('tasks', $this->action));
        } else {
            //hard-coded options
            $this->multiFields = array('name', 'customer');
        }

        $layouts = array();
        if ($model) {
            $layouts = $model->getLayoutsDetails();
        }

        // required basic variables per type
        $required_fields = array_filter(
            $this->registry['config']->getParamAsArray($this->module, 'validate_' . $type->get('id')),
            function($a) { return $a != 'current_year' && strpos($a, 'unique_') !== 0; }
        );

        //prepare name
        if (in_array('name', $this->multiFields) || $this->action == 'multiadd') {
            $vars['name'] = array(
                'name' => 'name',
                'type' => 'text',
                'required'  => 1,
                'label' => $this->i18n('tasks_name'),
                'val' => $type->get('default_name'));

            if (!empty($layouts['name'])) {
                //get the label from the layout name
                $vars['name']['label'] = $layouts['name']['name'];
                $vars['name']['help'] = $layouts['name']['description'];
                if (!$layouts['name']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['name']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['name']['readonly'] = !$layouts['name']['edit'];
                }
            }
        }

        //prepare customer
        if (in_array('customer', $this->multiFields) || $this->action == 'multiadd' || in_array('trademark', $this->multiFields)) {
            $vars['customer'] = array(
                'custom_id' => 'customer',
                'name'      => 'customer',
                'type'      => 'autocompleter',
                'autocomplete' => array(
                    'type' => 'customers',
                    'buttons_hide' => 'add search',
                    'clear'        => 1,
                    'fill_options' => array('$customer => <id>',
                                           '$customer_autocomplete => [<code>] <name> <lastname>',
                                           '$trademark => <main_trademark>',
                                           '$trademark_autocomplete => <main_trademark_name>',
                                           '$trademark_oldvalue => <main_trademark_name>'),
                    'filters' => array('<type>' => ($type->get('related_customers_types') ? implode(', ', $type->get('related_customers_types')) : '0')),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'customers', 'customers')
                ),
                'double'    => 'true',
                'required'  => 1,
                'width'     => '200',
                'label'     => $this->i18n('tasks_customer'),
                'help'      => $this->i18n('tasks_customer')
            );

            if (!empty($layouts['customer'])) {
                //get the label from the layout name
                $vars['customer']['label'] = $layouts['customer']['name'];
                $vars['customer']['help'] = $layouts['customer']['description'];
                if (!$layouts['customer']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['customer']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['customer']['readonly'] = !$layouts['customer']['edit'];
                }
            }
        }

        //prepare trademark
        if (in_array('trademark', $this->multiFields)) {
            $vars['trademark'] = array(
                'custom_id' => 'trademark',
                'name'      => 'trademark',
                'type'      => 'autocompleter',
                'double'    => 'true',
                'autocomplete'      => array(
                    'search' => array('<name>', '<customer_name>'),
                    'sort' => array('<name>', '<customer_name>'),
                    'buttons_hide' => 'search',
                    'clear'        => 1,
                    'type' => 'nomenclatures',
                    'suggestions' => '<name> [<customer_name>]',
                    'fill_options' => array('$trademark => <trademark>',
                                            '$trademark_autocomplete => <name>',
                                            '$customer => <customer>',
                                            '$customer_autocomplete => [<customer_code>] <customer_name>',
                                            '$customer_oldvalue => [<customer_code>] <customer_name>'),
                    'filters' => array('<type_keyword>' => 'trademark',
                                       '<customer_trademark>' => '1'),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'nomenclatures', 'nomenclatures'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'nomenclatures', 'nomenclatures')
                ),
                'required' => in_array('trademark', $required_fields),
                'width'     => '200',
                'label'     => $this->i18n('trademark'),
                'help'      => $this->i18n('trademark')
            );

            if (!empty($layouts['trademark'])) {
                //get the label from the layout name
                $vars['trademark']['label'] = $layouts['trademark']['name'];
                $vars['trademark']['help'] = $layouts['trademark']['description'];
                if (!$layouts['trademark']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['trademark']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['trademark']['readonly'] = !$layouts['trademark']['edit'];
                }
            }
        }

        //prepare projects
        if (in_array('project', $this->multiFields) || $counter->get('project_code')) {
            $vars['project'] = array(
                'name'                  => 'project',
                'type'                  => 'autocompleter',
                'autocomplete'          => array(
                    'type' => 'projects',
                    'buttons_hide' => 'add search',
                    'buttons' => 'clear',
                    'fill_options' => array('$project => <id>',
                                            '$project_autocomplete => [<code>] <name>'),
                    'url' => sprintf('%s?%s=%s&%s=ajax_select', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects'),
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=%s&%s=view&view=', $_SERVER['PHP_SELF'], $this->registry['module_param'], 'projects', 'projects')
                ),
                'double'                => 'true',
                'project_autocompleter' => true,
                'hide_all_button'       => true,
                'required'              => ($counter->get('project_code') || in_array('project', $required_fields) ? 1 : 0),
                'width'                 => '200',
                'label'                 => $this->i18n('tasks_project')
            );

            if (!empty($layouts['project'])) {
                //get the label from the layout name
                $vars['project']['label'] = $layouts['project']['name'];
                $vars['project']['help'] = $layouts['project']['description'];
                if (!$layouts['project']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['project']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['project']['readonly'] = !$layouts['project']['edit'];
                }
            }
        }

        //prepare planned_start_date
        if (in_array('planned_start_date', $this->multiFields) || $this->action == 'multiadd') {
            $vars['planned_start_date'] = array(
                'name'      => 'planned_start_date',
                'type'      => 'datetime',
                'val'       => ($this->registry['request']->isPost()) ? '' : General::strftime($this->i18n('date_iso')),
                'required'  => 1,
                'label'     => $this->i18n('tasks_planned_start_date'),
                'help'      => $this->i18n('tasks_planned_start_date')
            );

            if (!empty($layouts['planned_start_date'])) {
                //get the label from the layout name
                $vars['planned_start_date']['label'] = $layouts['planned_start_date']['name'];
                $vars['planned_start_date']['help'] = $layouts['planned_start_date']['description'];
                if (!$layouts['planned_start_date']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['planned_start_date']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['planned_start_date']['readonly'] = !$layouts['planned_start_date']['edit'];
                }
            }
        }

        //prepare planned_finish_date
        if (in_array('planned_finish_date', $this->multiFields) || $this->action == 'multiadd') {
            $vars['planned_finish_date'] = array(
                'name'      => 'planned_finish_date',
                'type'      => 'datetime',
                'required'  => 1,
                'label'     => $this->i18n('tasks_planned_finish_date'),
                'help'      => $this->i18n('tasks_planned_finish_date')
            );

            if (!empty($layouts['planned_finish_date'])) {
                //get the label from the layout name
                $vars['planned_finish_date']['label'] = $layouts['planned_finish_date']['name'];
                $vars['planned_finish_date']['help'] = $layouts['planned_finish_date']['description'];
                if (!$layouts['planned_finish_date']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['planned_finish_date']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['planned_finish_date']['readonly'] = !$layouts['planned_finish_date']['edit'];
                }
            }
        }

        //prepare planned_time
        if (in_array('planned_time', $this->multiFields)) {
            $vars['planned_time'] = array(
                'name' => 'planned_time',
                'type' => 'text',
                'required' => in_array('planned_time', $required_fields),
                'label' => $this->i18n('tasks_planned_time')
            );

            if (!empty($layouts['planned_time'])) {
                //get the label from the layout name
                $vars['planned_time']['label'] = $layouts['planned_time']['name'];
                $vars['planned_time']['help'] = $layouts['planned_time']['description'];
                if (!$layouts['planned_time']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['planned_time']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['planned_time']['readonly'] = !$layouts['planned_time']['edit'];
                }
            }
        }

        //prepare severity
        if (in_array('severity', $this->multiFields)) {
            $_options_severities = array(
                array(
                    'label'         => $this->i18n('tasks_verylight'),
                    'option_value'  => 'verylight'
                ),
                array(
                    'label'         => $this->i18n('tasks_light'),
                    'option_value'  => 'light'
                ),
                array(
                    'label'         => $this->i18n('tasks_normal'),
                    'option_value'  => 'normal'
                ),
                array(
                    'label'         => $this->i18n('tasks_heavy'),
                    'option_value'  => 'heavy'
                ),
                array(
                    'label'         => $this->i18n('tasks_veryheavy'),
                    'option_value'  => 'veryheavy'
                )
            );

            $vars['severity'] = array(
                'name' => 'severity',
                'type' => 'dropdown',
                'required' => in_array('severity', $required_fields),
                'label' => $this->i18n('tasks_severity'),
                'options' => $_options_severities
            );

            if (!empty($layouts['severity'])) {
                //get the label from the layout name
                $vars['severity']['label'] = $layouts['severity']['name'];
                $vars['severity']['help'] = $layouts['severity']['description'];
                if (!$layouts['severity']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['severity']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['severity']['readonly'] = !$layouts['severity']['edit'];
                }
            }
        }

        //prepare progress
        if (in_array('progress', $this->multiFields)) {
            $_options_progress = array();
            for ($j=0; $j<=100; $j=$j+10) {
                $_options_progress[] = array(
                    'label'         => $j . ' %',
                    'option_value'  => $j
                );
            }

            $vars['progress'] = array(
                'name' => 'progress',
                'type' => 'dropdown',
                'required' => in_array('progress', $required_fields),
                'label' => $this->i18n('tasks_progress'),
                'options' => $_options_progress
            );

            if (!empty($layouts['progress'])) {
                //get the label from the layout name
                $vars['progress']['label'] = $layouts['progress']['name'];
                $vars['progress']['help'] = $layouts['progress']['description'];
                if (!$layouts['progress']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['progress']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['progress']['readonly'] = !$layouts['progress']['edit'];
                }
            }
        }

        //prepare equipment
        if (in_array('equipment', $this->multiFields)) {
            $vars['equipment'] = array(
                'name' => 'equipment',
                'type' => 'text',
                'required' => in_array('equipment', $required_fields),
                'label' => $this->i18n('tasks_equipment')
            );

            if (!empty($layouts['equipment'])) {
                //get the label from the layout name
                $vars['equipment']['label'] = $layouts['equipment']['name'];
                $vars['equipment']['help'] = $layouts['equipment']['description'];
                if (!$layouts['equipment']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['equipment']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['equipment']['readonly'] = !$layouts['equipment']['edit'];
                }
            }
        }

        //prepare task_field
        if (in_array('task_field', $this->multiFields)) {
            $vars['task_field'] = array(
                'name' => 'task_field',
                'type' => 'text',
                'required' => in_array('task_field', $required_fields),
                'label' => $this->i18n('tasks_task_field')
            );

            if (!empty($layouts['task_field'])) {
                //get the label from the layout name
                $vars['task_field']['label'] = $layouts['task_field']['name'];
                $vars['task_field']['help'] = $layouts['task_field']['description'];
                if (!$layouts['task_field']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['task_field']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['task_field']['readonly'] = !$layouts['task_field']['edit'];
                }
            }
        }

        //prepare source
        if (in_array('source', $this->multiFields)) {
            $_options_sources = array(
                array(
                    'label'         => $this->i18n('tasks_email'),
                    'option_value'  => 'email'
                ),
                array(
                    'label'         => $this->i18n('tasks_phone'),
                    'option_value'  => 'phone'
                ),
                array(
                    'label'         => $this->i18n('tasks_meeting'),
                    'option_value'  => 'meeting'
                ),
                array(
                    'label'         => $this->i18n('tasks_inside'),
                    'option_value'  => 'inside'
                ),
                array(
                    'label'         => $this->i18n('tasks_other'),
                    'option_value'  => 'other'
                )
            );

            $vars['source'] = array(
                'name' => 'source',
                'type' => 'dropdown',
                'required' => in_array('source', $required_fields),
                'label' => $this->i18n('tasks_source'),
                'options' => $_options_sources
            );

            if (!empty($layouts['source'])) {
                //get the label from the layout name
                $vars['source']['label'] = $layouts['source']['name'];
                $vars['source']['help'] = $layouts['source']['description'];
                if (!$layouts['source']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['source']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['source']['readonly'] = !$layouts['source']['edit'];
                }
            }
        }

        //prepare description
        if (in_array('description', $this->multiFields)) {
            $vars['description'] = array(
                'name'  => 'description',
                'type'  => 'textarea',
                'required' => in_array('description', $required_fields),
                'label' => $this->i18n('tasks_description'),
                'help'  => $this->i18n('tasks_description'));

            if (!empty($layouts['description'])) {
                //get the label from the layout name
                $vars['description']['label'] = $layouts['description']['name'];
                $vars['description']['help'] = $layouts['description']['description'];
                if (!$layouts['description']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['description']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['description']['readonly'] = !$layouts['description']['edit'];
                }
            }
        }

        //prepare notes
        if (in_array('notes', $this->multiFields)) {
            $vars['notes'] = array(
                'name'  => 'notes',
                'type'  => 'textarea',
                'required' => in_array('notes', $required_fields),
                'label' => $this->i18n('tasks_notes'),
                'help'  => $this->i18n('tasks_notes'));

            if (!empty($layouts['notes'])) {
                //get the label from the layout name
                $vars['notes']['label'] = $layouts['notes']['name'];
                $vars['notes']['help'] = $layouts['notes']['description'];
                if (!$layouts['notes']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['notes']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['notes']['readonly'] = !$layouts['notes']['edit'];
                }
            }
        }

        //prepare active/inactive
        if (in_array('active', $this->multiFields)) {
            $vars['active'] = array(
                'name' => 'active',
                'type' => 'checkbox',
                'label' => $this->i18n('activate'),
                'help' => $this->i18n('activated') . '/' . $this->i18n('deactivated'),
                'option_value' => '1',
                'val' => '1'
            );
        }

        //prepare group
        if (in_array('group', $this->multiFields)) {
            require_once PH_MODULES_DIR . 'groups/models/groups.factory.php';
            $filters_groups = array('sanitize' => true);
            $groups = Groups::getTree($this->registry, $filters_groups);

            $_options_groups = array();
            foreach ($groups as $group) {
                $label = str_repeat('-', $group->get('level'));
                $_options_groups[] = array(
                    'label' => $label . $group->get('name'),
                    'option_value' => $group->get('id'));
            }

            $vars['group'] = array(
                'name'      => 'group',
                'type'      => 'dropdown',
                'label'     => $this->i18n('tasks_group'),
                'options'   => $_options_groups
            );
        }

        //gets the available assignment types for the selected type
        $available_assignment_types = $this->registry['config']->getParamAsArray('tasks', 'assignment_types_' . $type->get('id'));

        //prepare department
        if (in_array('department', $this->multiFields) ||
        (($this->registry->get('action') == 'multiadd') && in_array('owner', $available_assignment_types) && in_array('assignments_owner', $this->multiFields))) {
            require_once PH_MODULES_DIR . 'departments/models/departments.factory.php';
            $filters_departments = array('sanitize' => true);
            $departments = Departments::getTree($this->registry, $filters_departments);

            $_options_departments = array();
            foreach ($departments as $department) {
                $label = str_repeat('-', $department->get('level'));
                $_options_departments[] = array(
                    'label' => $label . $department->get('name'),
                    'option_value' => $department->get('id'));
            }

            $vars['department'] = array(
                'name'      => 'department',
                'type'      => 'dropdown',
                'label'     => $this->i18n('tasks_department'),
                'options'   => $_options_departments
            );

            if (($this->registry->get('action') == 'multiadd') && in_array('owner', $available_assignment_types) && in_array('assignments_owner', $this->multiFields)) {
                $vars['department']['onchange'] = 'changeAssignmentsMultiAddRecords(this)';
            }

            if (!empty($layouts['department'])) {
                //get the label from the layout name
                $vars['department']['label'] = $layouts['department']['name'];
                $vars['department']['help'] = $layouts['department']['description'];
                if (!$layouts['department']['view']) {
                    //there are no permissions for this layout remove the field from the list
                    $vars['department']['hidden'] = 1;
                } else {
                    //if the layout is not editable the field should be readonly
                    $vars['department']['readonly'] = !$layouts['department']['edit'];
                }
            }
        }

        //prepare assignments
        if ($this->registry->get('action') == 'multiadd' &&
            (in_array('assignments_owner', $this->multiFields) ||
             in_array('assignments_responsible', $this->multiFields) ||
             in_array('assignments_decision', $this->multiFields) ||
             in_array('assignments_observer', $this->multiFields))) {

            if (in_array('assignments_owner', $this->multiFields) && in_array('owner', $available_assignment_types)) {
                $vars['assignments_owner'] = array(
                    'name'      => 'assignments_owner',
                    'type'      => 'dropdown',
                    'label'     => $this->i18n('tasks_assign_owner'),
                    'options'   => array()
                );
            }

            if (in_array('assignments_responsible', $this->multiFields) ||
            in_array('assignments_decision', $this->multiFields) ||
            in_array('assignments_observer', $this->multiFields)) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';

                //take users for all the other assignments except 'owner'
                $filters = array('model_lang'   => $this->registry->get('lang'),
                                 'where'        => array('u.hidden = 0',
                                                         'u.active = 1'),
                                 'sort'         => array('CONCAT(ui18n.firstname, \' \', ui18n.lastname)'),
                                 'sanitize'     => true
                                );
                $users_all_obj = Users::search($this->registry, $filters);

                // collect user assignment permissions per role
                // (as they are the same for all users with the same role)
                // and reuse them to reduce multiple fetching of same data from database
                $user_available_assignments_per_role = array();

                if (in_array('assignments_responsible', $this->multiFields) && in_array('responsible', $available_assignment_types)) {
                    $users_manage = array();
                    foreach ($users_all_obj as $user) {
                        // get assignment permissions for this user and set them
                        // to the array per role
                        if (empty($user_available_assignments_per_role[$user->get('role')])) {
                            $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Task');
                        }
                        $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

                        if (in_array($type->get('id'), $user_available_assignments['responsible'])) {
                            $users_manage[] = array(
                                'option_value'  => $user->get('id'),
                                'label'         => $user->get('firstname') . ' ' . $user->get('lastname')
                            );
                        }
                    }

                    $vars['assignments_responsible'] = array(
                        'name'      => 'assignments_responsible',
                        'type'      => 'dropdown',
                        'label'     => $this->i18n('tasks_assign_responsible'),
                        'options'   => $users_manage
                    );
                }

                if (in_array('assignments_decision', $this->multiFields) && in_array('decision', $available_assignment_types)) {
                    $users_decision = array();
                    foreach ($users_all_obj as $user) {
                        // get assignment permissions for this user and set them
                        // to the array per role
                        if (empty($user_available_assignments_per_role[$user->get('role')])) {
                            $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Task');
                        }
                        $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

                        if (in_array($type->get('id'), $user_available_assignments['decision'])) {
                            $users_decision[] = array(
                                'option_value'  => $user->get('id'),
                                'label'         => $user->get('firstname') . ' ' . $user->get('lastname')
                            );
                        }
                    }

                    $vars['assignments_decision'] = array(
                        'name'      => 'assignments_decision',
                        'type'      => 'dropdown',
                        'label'     => $this->i18n('tasks_assign_decision'),
                        'options'   => $users_decision
                    );
                }

                if (in_array('assignments_observer', $this->multiFields) && in_array('observer', $available_assignment_types)) {
                    $users_observer = array();
                    foreach ($users_all_obj as $user) {
                        // get assignment permissions for this user and set them
                        // to the array per role
                        if (empty($user_available_assignments_per_role[$user->get('role')])) {
                            $user_available_assignments_per_role[$user->get('role')] = $user->getAssignmentPermissions('Task');
                        }
                        $user_available_assignments = $user_available_assignments_per_role[$user->get('role')];

                        if (in_array($type->get('id'), $user_available_assignments['observer'])) {
                            $users_observer[] = array(
                                'option_value'  => $user->get('id'),
                                'label'         => $user->get('firstname') . ' ' . $user->get('lastname')
                            );
                        }
                    }

                    $vars['assignments_observer'] = array(
                        'name'      => 'assignments_observer',
                        'type'      => 'dropdown',
                        'label'     => $this->i18n('tasks_assign_observer'),
                        'options'   => $users_observer
                    );
                }
            }
        }

        //prepare is_portal
        if (in_array('is_portal', $this->multiFields)) {
            $vars['is_portal'] = array(
                'name' => 'is_portal',
                'type' => 'checkbox',
                'label' => $this->i18n('is_portal'),
                'option_value' => '1',
                'val' => '0');
        }

        // the prepared sorted array
        $sorted_vars_options = array();

        foreach ($this->multiFields as $opt) {
            if (isset($vars[$opt])) {
                $sorted_vars_options[] = $vars[$opt];
                unset ($vars[$opt]);
            }
        }

        foreach ($vars as $var) {
            $sorted_vars_options[] = $var;
        }

        $vars_count = count($sorted_vars_options);
        for ($i=$vars_count-1;$i>0;$i--) {
            if (!@$sorted_vars_options[$i]['hidden']) {
                $sorted_vars_options[$i]['last_visible'] = true;
                break;
            }
        }

        return $sorted_vars_options;
    }

    /**
     * Send notification for task modification to observer assignees
     *
     * @param Task $new_task - task object
     * @param int $audit_parent - id of history record
     */
    public function sendNotification($new_task, $audit_parent) {
        $template = 'task_edit';

        if (!$new_task->shouldSendEmail($template)) {
            return true;
        }

        $not_users = Users::getUsersNoSend($this->registry, $template);

        $query = 'SELECT ' . "\n" .
                 '  u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname, u2.id,' . "\n" .
                 '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name ' . "\n" .
                 'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                 '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_task->get('model_lang') . '")' . "\n" .
                 'JOIN ' . DB_TABLE_TASKS_ASSIGNMENTS . ' AS da' . "\n" .
                 '  ON (da.parent_id=' . $new_task->get('id') . "\n" .
                 '    AND u2.id=da.assigned_to AND da.assignments_type=' . PH_ASSIGNMENTS_OBSERVER . ')' . "\n" .
                 'WHERE u2.active=1 AND u2.deleted_by=0';
        $records = $this->registry['db']->GetAll($query);

        //prepare audit data
        $this->registry['request']->set('audit', $audit_parent, 'all', true);
        require_once PH_MODULES_DIR . 'tasks/viewers/tasks.audit.viewer.php';
        $configViewer = new Tasks_Audit_Viewer($this->registry);
        $configViewer->templatesDir = $this->registry['theme']->templatesDir;
        $configViewer->setTemplate('_audit_email.html');

        //prepare the title for the audit table
        $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_task->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_task->get('modified'))));
        $configViewer->data['audit_title'] = $audit_title;
        $configViewer->prepare();
        $audit_data = $configViewer->fetch();
        // CHECK if there is audit data and compare the settings for each user from personal settings table
        $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
        $new_task->set('last_audit', $audit_data, true);

        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
        $users_send_always = Users::getUsersSendSettings($this->registry, 'document_edit_send_when_audit', 'emails');

        $mailer = null;
        $sent_to = array();
        foreach ($records as $record) {
            //check if the assignee wants to receive email
            if ($record['assignment_email']) {
                $send = false;
                if (in_array($record['id'], $not_users) || $record['id'] == $this->registry['currentUser']->get('id')) {
                    //the user does not want to receive notifications when the task is edited
                    continue;
                }
                if (empty($audit_data) && in_array($record['id'], $users_send_always)) {
                    $send = true;
                } else if (!empty($audit_data)) {
                    $send = true;
                }

                if ($send) {
                    if (!$mailer) {
                        $mailer = new Mailer($this->registry, $template, $new_task);
                        $mailer->placeholder->merge($new_task->getNotificationEmailsVars($template));
                    }
                    // set the user-specific placeholders
                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                    $mailer->placeholder->add('user_name', $record['assignment_name']);

                    $mailer->template['model_name'] = $new_task->modelName;
                    $mailer->template['model_id'] = $new_task->get('id');

                    //send email
                    $result = $mailer->send();
                    if (!@in_array($record['assignment_email'], $result['erred'])) {
                        $sent_to[] = $record['assignment_name'];
                        //$this->registry['messages']->setMessage($this->i18n('tasks_email_sent_success'), '', 10);
                        //$this->registry['messages']->insertInSession($this->registry);
                    } else {
                        //$this->registry['messages']->setWarning($this->i18n('error_tasks_send_email'), '', 10);
                        //$this->registry['messages']->insertInSession($this->registry);
                    }
                }
            }
        }
        if (count($sent_to)) {
            $notify_for = $this->i18n('tasks_' . $template . '_notify', array($new_task->getModelTypeName()));
            if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
            } else {
                $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Send status change notification to assignees
     *
     * @param Task $new_task - task object
     * @param int $audit_parent - id of history record
     */
    public function sendStatusNotification($new_task, $audit_parent) {
        $template = 'task_status';

        if (!$new_task->shouldSendEmail($template)) {
            return true;
        }

        $not_users = Users::getUsersNoSend($this->registry, $template);

        $query = 'SELECT DISTINCT(u2.id),' . "\n" .
                 '  u2.email as assignment_email, ui18n2.firstname, ui18n2.lastname,' . "\n" .
                 '  CONCAT(ui18n2.firstname, " ", ui18n2.lastname) as assignment_name, u2.is_portal ' . "\n" .
                 'FROM ' . DB_TABLE_USERS . ' AS u2 ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_USERS_I18N . ' AS ui18n2' . "\n" .
                 '  ON (u2.id=ui18n2.parent_id AND ui18n2.lang="' . $new_task->get('model_lang') . '")' . "\n" .
                 'JOIN ' . DB_TABLE_TASKS_ASSIGNMENTS . ' AS da' . "\n" .
                 '  ON (da.parent_id=' . $new_task->get('id') . ' AND u2.id=da.assigned_to)' . "\n" .
                 'WHERE u2.active=1 AND u2.deleted_by=0';
        $records = $this->registry['db']->GetAll($query);

        //prepare audit data
        $this->registry['request']->set('audit', $audit_parent, 'all', true);
        require_once PH_MODULES_DIR . 'tasks/viewers/tasks.audit.viewer.php';
        $configViewer = new Tasks_Audit_Viewer($this->registry);
        $configViewer->templatesDir = $this->registry['theme']->templatesDir;
        $configViewer->setTemplate('_audit_email.html');

        //prepare the title for the audit table
        $audit_title = sprintf($this->i18n('record_changed_by_at'), $new_task->get('modified_by_name'), date('d.m.Y, H:i', strtotime($new_task->get('modified'))));
        $configViewer->data['audit_title'] = $audit_title;
        $configViewer->prepare();
        $audit = $configViewer->data['audit'];
        $statuses = array();
        foreach ($audit['vars'] as $var) {
            $statuses[$var['field_name']] = $var['label'];
            $statuses['old_' . $var['field_name']] = $var['old_value'];
        }
        $audit_data = $configViewer->fetch();
        // CHECK if there is audit data and compare the settings for each user from personal settings table
        $audit_data = preg_replace('/(\n|\r)/', '', $audit_data);
        $new_task->set('last_audit', $audit_data, true);

        // Setting 'document_edit_send_when_audit' is used for all modules, section is 'emails'!!!
        $users_send_always = Users::getUsersSendSettings($this->registry, 'document_edit_send_when_audit', 'emails');

        $mailer = null;
        $sent_to = array();
        foreach ($records as $record) {
            //check if the assignee wants to receive email
            if ($record['assignment_email']) {
                $send = false;
                if (in_array($record['id'], $not_users)
                  || $record['id'] == $this->registry['currentUser']->get('id')) {
                    //the user does not want to receive notifications when the task is edited
                    continue;
                }

                // force sending of comment if no status has been changed but comment is added
                $force_send_comment_email = false;
                if ($new_task->get('comment') && !($record['is_portal'] && !$new_task->get('comment')->get('is_portal'))) {
                    $force_send_comment_email = true;
                }

                if (empty($audit_data) && in_array($record['id'], $users_send_always)) {
                    $send = true;
                } else if (!empty($audit_data) || $force_send_comment_email) {
                    $send = true;
                }
                if ($send) {
                    if (!$mailer) {
                        $mailer = new Mailer($this->registry, $template, $new_task);
                        $mailer->placeholder->merge($new_task->getNotificationEmailsVars($template));

                        if (empty($statuses['status'])) {
                            $mailer->placeholder->add('old_status', $this->i18n('tasks_status_' . $new_task->get('status')));
                            $mailer->placeholder->add('status', $this->i18n('tasks_status_' . $new_task->get('status')));
                        } else {
                            $mailer->placeholder->add('old_status', $statuses['old_status']);
                            $mailer->placeholder->add('status', $statuses['status']);
                        }
                        if (isset($statuses['old_substatus']) && $statuses['old_substatus'] != '-') {
                            $mailer->placeholder->add('old_substatus', ' (' . $statuses['old_substatus'] . ')');
                        } elseif (empty($statuses) && $new_task->get('substatus')) {
                            $mailer->placeholder->add('old_substatus', ' (' . $new_task->get('substatus_name') . ')');
                        }
                        if (isset($statuses['substatus']) && $statuses['substatus'] != '-') {
                            $mailer->placeholder->add('substatus', ' (' . $statuses['substatus'] . ')');
                        } elseif (empty($statuses) && $new_task->get('substatus')) {
                            $mailer->placeholder->add('substatus', ' (' . $new_task->get('substatus_name') . ')');
                        }
                    }
                    // set the user-specific placeholders
                    $mailer->placeholder->add('to_email', $record['assignment_email']);
                    $mailer->placeholder->add('user_name', $record['assignment_name']);

                    $comment = $new_task->get('comment');
                    if ($comment && $comment->get('content') && !($record['is_portal'] && !$comment->get('is_portal'))) {
                        $mailer->placeholder->add('model_id', $comment->get('id'));
                        $mailer->placeholder->add('model', 'Comment');
                        $mailer->placeholder->add('task_comment', nl2br($comment->get('content')));

                        $mailer->template['model_name'] = 'Comment';
                        $mailer->template['model_id'] = $comment->get('id');
                        $mailer->template['system_flag'] = 0;
                    } else {
                        // portal user should not get non-portal comment
                        $mailer->placeholder->flush('model_id');
                        $mailer->placeholder->flush('model');
                        $mailer->placeholder->flush('task_comment');

                        $mailer->template['model_name'] = $new_task->modelName;
                        $mailer->template['model_id'] = $new_task->get('id');
                        unset($mailer->template['system_flag']);
                    }

                    //send email
                    $result = $mailer->send();
                    if (!@in_array($record['assignment_email'], $result['erred'])) {
                        $sent_to[] = $record['assignment_name'];
                    } else {
                    }
                }
            }
        }
        if (count($sent_to)) {
            $notify_for = $this->i18n('tasks_' . $template . '_notify', array($new_task->getModelTypeName()));
            if (count($sent_to) > MAX_NOTIFIED_USERS_SHOW) {
                $this->registry['messages']->setMessage($this->i18n('count_users_notified', array($notify_for, count($sent_to))));
            } else {
                $this->registry['messages']->setMessage($this->i18n('names_users_notified', array($notify_for, implode(', ', $sent_to))));
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Get action options
     *
     * {@inheritDoc}
     * @see Controller::_getOptions()
     */
    public function _getOptions() {

        $request = &$this->registry['request'];

        $action_name = $request->get($this->action);
        $factory_name = $this->modelFactoryName;
        $model_name = strtolower($this->modelName);
        $alias = $factory_name::getAlias($model_name, '');
        if ($request->get('model_id')) {
            $filters = array(
                'where' => array(
                    $alias . '.id =' . intval($request->get('model_id')),
                    $alias . '.status IS NOT NULL'
                ),
                'model_lang' => $request->get('model_lang')
            );
            $model = $factory_name::searchOne($this->registry, $filters);
            if (!empty($model)) {
                //register the model,
                //so that it could be used further by the viewer
                if (!$this->registry->isRegistered($model_name)) {
                    $this->registry->set($model_name, $model->sanitize());
                }
            }
        }

        $action_arr = $this->getActions(array($action_name));
        $params['action'] = $action_name;
        if ($action_name == 'search' || $action_name == 'filter' || $action_name == 'filters_ajax_load' || $this->action == 'getoptions' && !$action_name) {
            parent::_getOptions();
            exit;
        } elseif ($action_name == 'attachments') {
            $action_arr = @$action_arr['attachments'];
            $model->unsanitize();
            $model->getAttachments();
            $model->sanitize();
            $this->viewer = new Viewer($this->registry);
            $this->viewer->data['task'] = $model;
            $this->viewer->setFrameset($action_arr['template']);
        } else {
            if (method_exists($this, 'getActionOptions')) {
                $action_arr = $this->getActionOptions($action_name);
            }
            $action_arr = @$action_arr[$action_name];
            $this->viewer = new Viewer($this->registry);
            $this->viewer->data['available_action'] = $action_arr;
            $this->viewer->setFrameset('_action_common_options.html');
        }
        $this->viewer->display();

        exit;
    }

    public function _select($autocomplete = array()) {

        $request = &$this->registry['request'];

        // get/set fields to search by
        $search_fields = array();
        if (!$search_fields = $request->get('search')) {
            $search_fields = array('<full_num>', '<name>');
        }
        if (!is_array($search_fields)) {
            $search_fields = array($search_fields);
        }
        $i18n_columns = array_keys($this->registry['db']->MetaColumns(DB_TABLE_TASKS_I18N, false));
        $main_alias = Tasks::getAlias('tasks', 'tasks');
        foreach ($search_fields as $idx => $field) {
            $field = preg_replace('#<|>#', '', $field);
            if (preg_match('#^a__*#', $field)) {
                //search by additional variable (NOT APPLICABLE IN this module)
                unset($search_fields[$idx]);
                continue;
            } else {
                //search by main field
                $alias = $main_alias;
                if (in_array(strtoupper($field), $i18n_columns)) {
                    //search by main field in i18n table
                    $alias .= 'i18n';
                }
                $search_fields[$idx] = $alias . '.' . $field;
            }
        }

        //prepare sort if is requested
        $sort = array();
        if (!$r_sort = $request->get('sort')) {
            $r_sort = array('<name>');
        }
        foreach ($r_sort as $key => $field) {
            preg_match('#<([^>]*)>(\s+(ASC|DESC))?#i', $field, $sort_matches);
            if (!empty($sort_matches[1])) {
                //$order: ASC/DESC
                $order = (!empty($sort_matches[3]) ? $sort_matches[3] : 'ASC');
                if (preg_match('#^a__*#', $sort_matches[1])) {
                    //sort by additional variable (NOT APPLICABLE IN this module)
                    continue;
                } else {
                    //sort by main field
                    $alias = $main_alias;
                    if (in_array(strtoupper($sort_matches[1]), $i18n_columns)) {
                        //sort by main field in i18n table
                        $alias .= 'i18n';
                    }
                    $sort[] = $alias . '.' . $sort_matches[1] . ' ' . $order;
                }
            }
        }

        $additional_where = array();
        if ($req_filters = $request->get('filters')) {
            foreach ($req_filters as $filter => $value) {
                $alias = $main_alias . '.';
                $field = preg_replace('#<|>#', '', $filter);
                // escape value for the SQL search query
                $value = General::slashesEscape($value);

                switch ($filter) {
                    case '<tag>':
                        $alias = 'tags.';
                        $field = 'tag_id';
                        break;
                    default:
                        if (preg_match('#^a__*#', $field)) {
                            //search by additional variable (NOT APPLICABLE IN this module)
                            continue 2;
                        } elseif (in_array(strtoupper($field), $i18n_columns)) {
                            //search by main field in i18n table
                            $alias = $main_alias . 'i18n.';
                        }
                        break;
                }

                if (preg_match('#^\s*(!?=|<=?|>=?)(.*)#', $value, $matches)) {
                    // search expression for a single value
                    $additional_where[] =
                        sprintf('%s%s %s \'%s\' AND',
                                $alias, $field, trim($matches[1]), trim($matches[2]));
                    continue;
                } elseif (preg_match('#^\s*((not\s+)?in\s*)\((.*)\)\s*#i', $value, $matches)) {
                    // search expression for multiple values
                    $negative_search = preg_match('#not#i', $matches[1]);
                    $compare_operator = $negative_search ? '!=' : '=';
                    $amatches = preg_split('#\s*,\s*#', trim($matches[3]));
                    $count_or_clauses = count($amatches);
                    foreach ($amatches as $idx => $amatch) {
                        $logical_operator = $negative_search || $idx == ($count_or_clauses - 1) ? 'AND' : 'OR';
                        $additional_where[] =
                            sprintf('%s%s %s \'%s\' %s',
                                    $alias, $field, $compare_operator, $amatch, $logical_operator);
                    }
                    continue;
                }

                $vals = preg_split('#\s*,\s*#', $value);
                if (count($vals) > 1) {
                    $count_or_clauses = count($vals);
                    foreach ($vals as $idx => $val) {
                        $clause = $alias . $field . ' = \'' . $val . '\'';
                        if ($idx < $count_or_clauses - 1) {
                            $clause .= ' OR';
                        } else {
                            $clause .= ' AND';
                        }
                        $additional_where[] = $clause;
                    }
                } else {
                    $additional_where[] = $alias . $field . ' = \'' . $vals[0] . '\' AND';
                }
            }
        }

        //prepare suggestions format
        if (!$suggestions_format = $request->get('suggestions')) {
            $suggestions_format = '[<full_num>] <name>';
        }

        $s_field = $request->get('field');
        //prepare fill option definitions
        if (!$fill_options = $request->get('fill_options')) {
            //we must be in the basic vars
            //so get the autocomplete field
            $fill_options = array(
                '$' . $s_field . ' => [<full_num>] <name>',
                '$' . preg_replace('#_autocomplete$#', '', $s_field) . '_oldvalue' . ' => [<full_num>] <name>');
            if (preg_match('#_autocomplete$#', $s_field)) {
                $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . ' => <id>';
            }
        } else {
            $fill_oldvalue = '[<full_num>] <name>';
            foreach ($fill_options as $fill_option) {
                if (preg_match('#^\$' . $s_field . '#', $fill_option)) {
                    @list($notimportant, $fill_oldvalue) = preg_split('#\s*=>\s*#', $fill_option);
                }
            }
            $fill_options[] = '$' . preg_replace('#_autocomplete$#', '', $s_field) . '_oldvalue' . '=>' . $fill_oldvalue;
        }

        $autocomplete = array(
            'search' => $search_fields,
            'sort' => $sort,
            'suggestions_format' => $suggestions_format,
            'fill_options' => $fill_options,
            'additional_where' => $additional_where,
            'type' => 'tasks'
        );

        if ($request->get('autocomplete_filter')) {
            $filters = parent::_select($autocomplete);
            return $filters;
        }
        parent::_select($autocomplete);

        exit;

    }
}

?>
