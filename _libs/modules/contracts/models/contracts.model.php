<?php

require_once 'contracts.dropdown.php';
require_once 'contracts.validator.php';

/**
 * Contracts model class
 */
Class Contract extends Model {
    public $modelName = 'Contract';

    public $counter;

    //formula fields dependencies
    public $_formulas = array('date_sign_formula' => 'date_sign', 'date_start_formula' => 'date_start',
                              'date_validity_formula' => 'date_validity', 'date_end_formula' => 'date_end');

    //flag defining whether to use status to define permissions
    public $checkPermissionsByStatus = true;

    /**
     * Placeholders used by the generate and print output filename
     */
    public $outputFileNamePlaceholders = array('num', 'customer_name', 'name', 'added', 'modified', 'current_date', 'rev');

    public function __construct(&$registry, $params = '') {
        parent::__construct($registry, $params);

        //additional custom settings
        if ($this->get('id') && $registry->get('getAssignments')) {
            $this->getAssignments();
            $this->getAssignments('responsible');
            $this->getAssignments('observer');
            $this->getAssignments('decision');
        }

        //get CC recipients for contacts of contract
        if ($this->get('id') && $registry->get('getContactCc')) {
            $this->getContactCc();
        }
    }

    /**
     * Checks permissions for certain action
     *
     * @param array $action - action name
     * @param array $module_check - module name (+controller)
     * @param bool $force - forcing to rewrite current permissions
     * @return bool - true - accessible, false - inaccessible
     */
    public function checkPermissions($action, $module_check = 'contracts', $force = false) {

        //Allow list, search actions. Restrictions are in search method for factory class
        if ($action == 'list' || $action == 'search') {
            return true;
        }

        $rights = $this->setPermissions(array(), $force, $module_check);
        if ($this->get('type_rights')) {
            $type_rights = $this->get('type_rights');
        } elseif ($this->get('type')) {
            $sanitize_after = false;
            if (empty($this->registry)) {
                $this->unsanitize();
                $sanitize_after = true;
            }
            $user_permissions = $this->registry['currentUser']->getRights();
            $type_rights = $this->setPermissions(@array_keys($user_permissions[$module_check.$this->get('type')]), true, $module_check.$this->get('type'));
            if ($sanitize_after) {
                $this->sanitize();
            }
            $this->set('type_rights', $type_rights, true);
        }

        if (!isset($rights[$action]) && is_array($rights)) {
            //the action is not defined within the rights array
            $action_defs = array_keys($rights);
            $action_defs[] = $action;

            //try to get permission definition for this action
            $rights = $this->setPermissions($action_defs, true, $module_check);
        }

        //special permissions defined by the status of the contract (activated/deactivated)
        if (!$this->isActivated() && !$this->get('num')) {
            //do not allow transform, multitransform and clone of initially deactivated contracts (contracts without numbers)
            if ($action == 'transform' || $action == 'multitransform' || $action == 'clone') {
                return false;
            }
        }

        if ($action == 'parties') {
            return ($this->get('subtype') == 'contract');
        }

        //set permissions depending on model status
        if ($this->checkPermissionsByStatus && $this->get('id')) {
            if ($this->get('status') == 'locked') {
                //locked status
                switch ($action) {
                //forbidden actions
                case 'edit':
                case 'edittopic':
                case 'editclause':
                case 'editfinance':
                case 'addhandover':
                   return false;
                    break;
                //allowed actions
                case 'view':
                case 'assign':
                case 'attachments':
                case 'clone':
                case 'transform':
                case 'multitransform':
                case 'generate':
                case 'print':
                case 'multiprint':
                case 'translate':
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            return $type_rights[$action];
                        } else {
                            return $rights[$action];
                        }
                    } else {
                        return true;
                    }
                }
            } elseif ($this->get('status') == 'closed') {
            //closed status
                switch ($action) {
                //forbidden actions
                case 'edit':
                case 'edittopic':
                case 'editclause':
                case 'editfinance':
                case 'translate':
                    return false;
                    break;
                //allowed actions
                case 'listhandovers':
                    $sanitize_after = false;
                    if (empty($this->registry)) {
                        $this->unsanitize();
                        $sanitize_after = true;
                    }
                    $query = 'SELECT commodity FROM ' . DB_TABLE_CONTRACTS_TYPES . ' WHERE id = ' . $this->get('type');
                    $c = $this->registry['db']->GetOne($query);
                    $this->set('commodity', $c, true);
                    if ($sanitize_after) {
                        $this->sanitize();
                    }
                    if (!$c || $c == 'none') {
                        return false;
                    }
                case 'assign':
                case 'attachments':
                case 'view':
                case 'clone':
                case 'transform':
                case 'multitransform':
                case 'generate':
                case 'print':
                case 'multiprint':
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            return $type_rights[$action];
                        } else {
                            return $rights[$action];
                        }
                    } else {
                        return true;
                    }
                }
            } else {
                //opened status
                switch ($action) {
                case 'addhandover':
                    return false;
                    break;
                default:
                    if (isset($rights[$action])) {
                        if (isset($type_rights[$action])) {
                            return $type_rights[$action];
                        } else {
                            return $rights[$action];
                        }
                    } else {
                        return true;
                    }
                }
            }
        } else {
            if (isset($rights[$action])) {
                if (isset($type_rights[$action])) {
                    return $type_rights[$action];
                } else {
                    return $rights[$action];
                }
            } else {
                return true;
            }
        }

        //no restrictions are specified, allow action
        return true;
    }

    /**
     * Checks the validity of the model
     *
     * @param string $action - action with the model
     * @return bool - true if valid, false if invalid
     */
    public function validate($action = '') {
        parent::validate($action);
        if ((!$this->get('id') || $this->isDefined('name')) && !$this->get('name')) {
            $this->raiseError('error_no_name', 'name', null, array($this->getLayoutName('name', false)));
        }

        if (!$this->get('id') && !$this->get('type')) {
            $this->raiseError('error_no_type', 'type');
        }

        //set counter
        $this->getCounter();

        if ($this->isDefined('customer') && !$this->get('customer')) {
            $this->raiseError('error_no_customer', 'customer', null, array($this->getLayoutName('customer')));
        }
        if ($this->isDefined('trademark') && !$this->hasValidTrademark()) {
            $this->raiseError('error_invalid_trademark', 'trademark', null, array($this->getLayoutName('trademark')));
        }
        if ($this->isDefined('office') && !$this->get('office') && $action != 'translate') {
            $this->raiseError('error_invalid_office', 'office', null, array($this->getLayoutName('office')));
        }

        if ($this->isDefined('company') && !$this->get('company')) {
            $this->raiseError('error_no_company', 'company', null, array($this->getLayoutName('company')));
        }
        if ($this->isDefined('subtype') && $this->get('subtype') || $this->registry['request']->get('action') == 'add') {
            //fine
        } elseif ($this->isDefined('subtype') && !$this->get('subtype')) {
            //try to detect strange problem with missing subtype
            $this->raiseError('error_no_subtype');
        }

        $this->checkDateSign($action);

        // validate additional vars
        if ($action != 'translate') {
            $this->validateVars();
        }

        return $this->valid;
    }

    public function validateDates() {
        // we can allow validity dates to be empty
        // IMPORTANT!!! This is only for contracts which
        // will not issue fiscal documents
        require_once PH_MODULES_DIR . 'contracts/models/contracts.types.factory.php';
        $filters = array('where' => array('cot.id = ' . $this->get('type')), 'sanitize' => true);
        $type = Contracts_Types::searchOne($this->registry, $filters);
        $fiscal = $type->get('fiscal');

        if ($this->get('subtype') == 'annex') {
            if (!$this->get('date_sign_subtype') || $this->get('date_sign_subtype') == '0000-00-00') {
                $this->raiseError('error_no_date_sign_subtype', 'date_sign_subtype');
                $result = false;
            } else {
                $this->checkDateSign();
            }
            if (!$this->get('date_start_subtype') || $this->get('date_start_subtype') == '0000-00-00') {
                $this->raiseError('error_no_date_start_subtype', 'date_start_subtype');
            }
            if ($this->get('date_end_subtype') && $this->get('date_end_subtype') > '0000-00-00') {
                //check if date_validity has been changed
                $query = 'SELECT date_validity, date_validity_formula FROM ' . DB_TABLE_CONTRACTS . "\n" .
                         'WHERE id = ' . $this->get('parent_record');
                list($dv, $dvf) = array_values($this->registry['db']->GetRow($query));
                if ($dv != $this->get('date_validity') || $dvf != $this->get('date_validity_formula')) {
                    $this->raiseError('error_change_date_validity_end_subtype', 'date_end_subtype', null, array($this->getLayoutName('date_validity')));
                    //set back the date validity
                    if ($dv && $dv > '0000-00-00' || !$dvf) {
                        $this->set('date_validity', $dv, true);
                        $this->set('change_formula_date_validity', 'date_validity', true);
                    } else {
                        $this->set('date_validity_formula', $dvf, true);
                        $this->set('change_formula_date_validity', 'date_validity_formula', true);
                    }
                }
                if ($this->get('date_validity') && $this->get('date_end_subtype') > $this->get('date_validity')) {
                    $this->raiseError('error_contracts_date_end_subtype', 'date_end_subtype', null, array($this->getLayoutName('date_end_subtype', false), $this->getLayoutName('date_validity')));
                }
                $add_where = sprintf('(recurrent = 0 AND issue_date > "0000-00-00" AND (issue_date < "%s" OR issue_date > "%s")' . "\n" .
                                     'OR recurrent > 0 AND (periods_start > "0000-00-00" %s OR periods_end > "0000-00-00" %s))',
                     $this->get('date_start_subtype'),
                     $this->get('date_end_subtype'),
                     $this->get('date_end_subtype') ? "AND periods_start > \"{$this->get('date_end_subtype')}\"" : "",
                     $this->get('date_start_subtype') ? "AND periods_end < \"{$this->get('date_start_subtype')}\"" : ""
                );
            } else {
                $add_where = sprintf('(recurrent = 0 AND issue_date > "0000-00-00" AND (issue_date < "%s" OR issue_date > "%s")' . "\n" .
                                     'OR recurrent > 0 AND (periods_start > "0000-00-00" %s OR periods_end > "0000-00-00" %s))',
                    $this->get('date_start_subtype'),
                    $this->get('date_validity'),
                    $this->get('date_validity') ? "AND periods_start > \"{$this->get('date_validity')}\"" : "",
                    $this->get('date_start_subtype') ? "AND periods_end < \"{$this->get('date_start_subtype')}\"" : ""
                );
            }
            //validate dates of templates already created
            $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                    'WHERE deleted_by = 0 AND type != ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL .
                    ' AND contract_id = ' . $this->get('id') . ' AND ' . "\n" . $add_where;
            $tpl = $this->registry['db']->GetCol($query);

            if (!empty($tpl)) {
                $this->raiseError('error_contracts_templates_invalid_dates');
            }
        } else {
            //validate dates of templates already created
            $query = 'SELECT id FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . "\n" .
                    'WHERE deleted_by = 0 AND type != ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL . ' AND contract_id = ' . $this->get('id') . ' AND ' . "\n" .
                    sprintf('(recurrent = 0 AND issue_date > "0000-00-00" AND (issue_date < "%s" OR issue_date > "%s")' . "\n" .
                            'OR recurrent > 0 AND (periods_start > "0000-00-00" %s OR periods_end > "0000-00-00" %s))',
                            $this->get('date_start'),
                            $this->get('date_validity'),
                            $this->get('date_validity') ? "AND periods_start > \"{$this->get('date_validity')}\"" : "",
                            $this->get('date_start') ? "AND periods_end < \"{$this->get('date_start')}\"" : ""
                    );
            $tpl = $this->registry['db']->GetCol($query);
            if (!empty($tpl)) {
                $this->raiseError('error_contracts_templates_invalid_dates');
            }
        }

        //validate date start
        if ((!$this->get('date_start') || $this->get('date_start') == '0000-00-00') && !$this->get('date_start_formula')) {
            $this->raiseError('error_no_date_start', 'date_start', null, array($this->getLayoutName('date_start')));
        }
        if ($fiscal && (!$this->get('date_validity') || $this->get('date_validity') == '0000-00-00') && !$this->get('date_validity_formula')) {
            $this->raiseError('error_no_date_validity', 'date_validity', null, array($this->getLayoutName('date_validity')));
        }
        if ($this->get('date_end') && $this->get('date_end') > '0000-00-00' && $this->get('date_validity') && $this->get('date_validity') > '0000-00-00' &&
            $this->get('date_end') > $this->get('date_validity')) {
            $this->raiseError('error_contracts_date_end_subtype', 'date_end', null, array($this->getLayoutName('date_end', false), $this->getLayoutName('date_validity')));
        }
        if ($this->get('date_start') && $this->get('date_start') > '0000-00-00' && $this->get('date_validity') && $this->get('date_validity') > '0000-00-00' &&
            $this->get('date_start') > $this->get('date_validity')) {
            $this->raiseError('error_contracts_date_start_validity', 'date_start', 0, array($this->get('date_start'), $this->get('date_validity')));
        }

        return $this->valid;
    }

    public function validateTemplates() {
        // check if company and office of the contract match company and office of its templates
        $query = 'SELECT c.company as cc, c.office as co, fit.company as tc, fit.office as `to`' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' c' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' fit' . "\n" .
                 '  ON c.id = fit.contract_id AND (c.office != fit.office OR c.company != fit.company)' . "\n".
                 'WHERE c.id = ' . $this->get('id');
        $data = $this->registry['db']->GetRow($query);
        if (empty($data)) {
            return true;
        }
        return false;
    }

    /**
     * Checks the date sign of the agreement
     *
     * @param string $action - action with the model
     * @return bool - true if valid, false if invalid
     */
    public function checkDateSign($action = '') {
        $result = true;

        if ($this->get('date_sign_subtype')) {
            $query = 'SELECT date_sign_subtype as max_date_sign_subtype, co.subtype, co.num' . "\n" .
                        ' FROM ' . DB_TABLE_CONTRACTS .  ' as co ' . "\n" .
                        ' WHERE co.subtype = "annex"' . "\n" .
                        ' AND co.parent_record=' . $this->get('parent_record') . "\n" .
                        ' AND co.subtype_status!="waiting"' . "\n" .
                        ' AND co.status="closed"' . "\n" .
                        ' AND co.annulled_by=0' . "\n" .
                        ($this->get('id') ? ' AND co.id<>' . $this->get('id') : '') . "\n" .
                        'ORDER BY date_sign_subtype DESC' . "\n" .
                        'LIMIT 1';
            $data = $this->registry['db']->GetRow($query);
            if (isset($data['max_date_sign_subtype']) && $data['max_date_sign_subtype'] > $this->get('date_sign_subtype')) {
                $placeholders = array(
                    $this->i18n('contracts_' . $data['subtype']) . ($data['num'] ? ' ' . ($data['num'] == 'system' ? $this->i18n('contracts_system_num') : $data['num']) : ''),
                    General::strftime($this->i18n('date_short'), $data['max_date_sign_subtype']),
                );
                $this->raiseError('error_date_sign_subtype_exist', 'date_sign_subtype');
                $this->raiseError('error_date_sign_subtype_exist2', 'date_sign_subtype', 0, $placeholders);
                $result = false;
            }
        }

        return $result;
    }

    /**
     * Saves the model into the database
     *
     * @return bool - result of the operation
     */
    public function save() {
        if ($this->get('id')) {
            if ($this->registry['action'] == 'translate') {
                $action = 'translate';
            } else {
                //edit mode
                $action = 'edit';
            }
        } else {
            $action = 'add';
        }

        if ($this->validate($action)) {
            //escape the quotes and double quotes
            //in the properties recursively
            $this->slashesEscape();
            if ($action == 'translate') {
                $action = 'edit';
            }
            if ($this->$action()) {
                return true;
            } else {
                $this->slashesStrip();

                return false;
            }

        } else {
            return false;
        }
    }

    /**
     * Add model
     *
     * @return bool - result of the operation
     */
    public function add() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();
        $set['added']    = sprintf("added=now()");
        $set['added_by'] = sprintf("added_by=%d", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified`=now()");
        $set['status_modified_by'] = sprintf("status_modified_by='%s'", $this->registry['currentUser']->get('id'));
        $set['type']     = sprintf("type=%d", $this->get('type'));

        //query to insert the main table
        $query1 = 'INSERT INTO ' . DB_TABLE_CONTRACTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n";
        $db->Execute($query1);
        if ($db->ErrorMsg()) {
            $this->registry['logger']->dbError('add new contract base details', $db, $query1);
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //get the id of the record
        if ($id = $db->Insert_Id()) {
            $this->set('id', $id, true);
        } else {
            //rollback the transaction
            $db->FailTrans();
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N('add');

        //UPDATE THE RELATIVES TABLE
        if ($this->get('update_relatives')) {
            $this->updateRelatives(false);
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //if create from event update event relatives
        if ($this->get('event_id')) {
            $origin = 'contract';
            $link_type = 'child';
            $query = 'INSERT IGNORE INTO ' . DB_TABLE_EVENTS_RELATIVES . "\n" .
                      'SET parent_id='. $this->get('event_id') . "\n" .
                      ', link_to=' . $this->get('id') . "\n" .
                      ', origin="' . $origin . '"' . "\n" .
                      ', link_type="' . $link_type . '"' . "\n" .
                      ', added=now()' . "\n";
            $db->Execute($query);
        }

        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            //save additional variables
            $result = $this->replaceVars();
            if (!$result) {
                //rollback the transaction
                $db->FailTrans();
            }
        }

        if ($this->get('transform_params')) {
            //update the relatives table
            $this->saveTransformationDetails();
        }

        if (!$this->createSysfit('add')) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        // If the transaction has failed
        if ($dbTransError) {
            // Remove the id
            $this->set('id', '', true);
        }

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function edit() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();
        //INSERT/UPDATE THE MAIN TABLE OF THE MODEL
        $set = $this->prepareMainData();

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CONTRACTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $this->updateI18N();

        //update basic vars of templates (customer etc.) if any templates
        $copy_basic_vars = array('customer', 'trademark', 'project');
        foreach ($copy_basic_vars as $idx => $var) {
            if ($this->isDefined($var)) {
                $copy_basic_vars[$idx] = "$var = '{$this->get($var)}'";
            } else {
                unset($copy_basic_vars[$idx]);
            }
        }
        if ($copy_basic_vars) {
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES .
                     ' SET ' . implode(', ', $copy_basic_vars) .
                     ' WHERE contract_id = ' . $this->get('id');
            $db->Execute($query);
        }

        // update relations
        if ($this->get('update_relatives')) {
            $this->updateRelatives();
        }

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        // save additional variables
        if (!$db->HasFailedTrans() && $this->isDefined('vars')) {
            if (!$this->replaceVars()) {
                $db->FailTrans();
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Annul existing model
     *
     * @return bool - result of the operation
     */
    public function annul() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($this->get('subtype_status') == 'started') {
            //get the current contract state
            $filters = array('where' => array('co.id = ' . $this->get('parent_record'), 'co.subtype = "contract"'));
            $current = Contracts::searchOne($this->registry, $filters);

            //we have to find the last executed annex before the current with date_end_subtype
            //not set or greater than the date of annulment of the current annex
            // OOORRR we get the original if annex has not been found
            $date_end = $this->registry['session']->get('date_end_subtype');
            if (!$date_end || $date_end == '0000-00-00') {
                $date_end = date('Y-m-d');
            }
            $this->registry['session']->remove('date_end_subtype', true);
            if ($this->registry['session']->get('into_force')) {
                //get the agreement we have to return into force
                $filters = array('where' => array('co.id = ' . $this->registry['session']->get('into_force')));
                $this->registry['session']->remove('into_force', true);
            } else {
                //we have not to come here but.....
                $filters = array(
                    'where' => array(
                        'co.parent_record = ' . $current->get('id'),
                        'co.annulled_by = 0',
                        '(co.subtype = "original" OR ',
                        'co.subtype = "annex"',
                        'co.subtype_status = "executed"',
                        '(co.date_end_subtype IS NULL OR co.date_end_subtype = "0000-00-00" OR co.date_end_subtype > "' . $date_end . '"))',
                    ),
                    'sort' => array('co.added DESC')
                );
            }
            $agreement = Contracts::searchOne($this->registry, $filters);
            //set the start date for the last executed agreement(or original) to be date after the annul date of the agreement
            $agreement->set('date_start_subtype', date_add(date_create($date_end), new DateInterval('P1D'))->format('Y-m-d'), true);

            $result = Contracts::pushIntoForce($agreement, $current);
            if (!$result) {
                $db->FailTrans();
            }
            //set the new agreement status to started
            if ($agreement->get('subtype') == 'annex') {
                //if we have original, we do not have to change its status
                $query = 'UPDATE ' . DB_TABLE_CONTRACTS . ' SET subtype_status = "started" WHERE id = ' . $agreement->get('id');
                $db->Execute($query);
            }
        }

        $set = array();
        $set['annulled'] = sprintf("annulled=now()");
        $set['annulled_by'] = sprintf("annulled_by=%d", $this->registry['currentUser']->get('id'));
        $set['status'] = "status='closed'";
        if ($this->get('subtype_status') == 'waiting') {
            $set['subtype_status'] = "subtype_status='failed'";
        } elseif ($this->get('subtype_status') == 'started') {
            $set['subtype_status'] = "subtype_status='executed'";
            $set['date_end_subtype'] = sprintf("date_end_subtype = '%s'", $date_end);
        }
        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CONTRACTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        if ($db->ErrorMsg()) {
            $this->registry['messages']->setError($db->ErrorMsg());
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        //IMPORTANT!!!:
        //we send mails to the customer after all the other work is done
        //of course we will do this job in a background mode
        //so the user will not wait for the slow process of PDF generation and so on
        if (!empty($current)) {
            $send_mails = $current->get('send_mails');
            if ($result && !empty($send_mails)) {
                $params = base64_encode(gzdeflate(json_encode($send_mails)));
                $url = sprintf('%s/index.php?%s=contracts&contracts=background_send&background_send=%d&user=%d&params=%s',
                                $this->registry['config']->getParam('crontab', 'base_host'), $this->registry['module_param'],
                                $current->get('id'), $this->registry['currentUser']->get('id'), rawurlencode($params));
                if (preg_match('#WIN#i', PHP_OS)) {
                    // IMPORTANT: In every windows nzoom installation there should be a wget.exe inside _libs/inc/ext/wget/
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = PH_EXT_DIR . 'wget\wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate "' . $url . '"';
                    //silence the output of command line
                    $command .= ' > nul &';
                    pclose(popen($command, 'r'));
                } else {
                    $url = preg_replace('#\&#', '\&', $url);
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = 'wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate ' . $url;
                    //silence the output of command line
                    $command .= ' > /dev/null 2>&1 & ';
                    exec($command, $output, $status);
                }
                $this->registry['messages']->setMessage($this->i18n('message_contracts_email_for_sent_invoices'));
            }
        }

        return $result;
    }

    public function getAllInvoices($check_permissions = false) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];

        //get filters  - we need pagination only
        $filts = Contracts::saveSearchParams($this->registry, array(), $sessionPrefix = 'list_invoices_');
        $filts['display'] = 5;
        //prepare permissions for the invoices
        $this->registry->set('module', 'finance', true);
        $this->registry->set('controller', 'incomes_reasons', true);
        $this->registry->set('action', 'list', true);
        $types = array();
        $on_clause = array('where' => array('fir.type = ' . PH_FINANCE_TYPE_PRO_INVOICE));
        require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
        Finance_Incomes_Reasons::prepareRightsFilters($this->registry, $on_clause, $types);
        $on_clause1 = array('where' => array('fir.type = ' . PH_FINANCE_TYPE_INVOICE));
        Finance_Incomes_Reasons::prepareRightsFilters($this->registry, $on_clause1, $types);

        //get issued invoices ids
        $query = 'SELECT SQL_CALC_FOUND_ROWS IF (fir2.id, fir2.id, fir.id) as inv_id' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' as fiti' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir' . "\n" .
                 '  ON fir.id = fiti.invoice_id AND ' . "\n" .
                 '     ((' . implode($on_clause['where']) . ' 1) OR' . "\n" .
                 '     (' . implode($on_clause1['where']) . ' 1))' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                 '  ON frr.link_to = fiti.invoice_id AND frr.link_to_model_name="Finance_Incomes_Reason"  AND frr.parent_model_name = "Finance_Incomes_Reason" ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' as fir2' . "\n" .
                 '  ON ' . preg_replace('#fir\.#', 'fir2.', implode($on_clause1['where'])) . ' fir2.id = frr.parent_id' . "\n" .
                 'WHERE fiti.contract_id = ' . $this->get('id') . "\n" .
                 '  AND fiti.invoice_id > 0' . "\n" .
                 'GROUP BY inv_id' . "\n" .
                 'HAVING inv_id IS NOT NULL' . "\n" .
                 'ORDER BY fiti.invoice_id DESC' . "\n" .
                 'LIMIT ' . ($filts['page'] - 1) * $filts['display'] . ', ' . $filts['display'];
        $inv_ids = $db->GetCol($query);
        $all_rows = $db->GetOne('SELECT FOUND_ROWS()');
        $cd_ids = array();
        if ($inv_ids) {
            //prepare permissions for the credit/debit
            $on_clause = array('where' => array('fir.type = ' . PH_FINANCE_TYPE_CREDIT_NOTICE));
            Finance_Incomes_Reasons::prepareRightsFilters($this->registry, $on_clause, $types);
            $on_clause1 = array('where' => array('fir.type = ' . PH_FINANCE_TYPE_DEBIT_NOTICE));
            Finance_Incomes_Reasons::prepareRightsFilters($this->registry, $on_clause1, $types);
            //get credit/debit notices ids ordered
            $query = 'SELECT frr.parent_id, frr.link_to' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' frr' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_INCOMES_REASONS . ' fir' . "\n" .
                     '  ON fir.id = frr.parent_id AND (' .
                     implode($on_clause['where']) . ' 1 OR ' . implode($on_clause1['where']) . ' 1)' . "\n" .
                     'WHERE frr.parent_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to_model_name = "Finance_Incomes_Reason"' . "\n" .
                     '  AND frr.link_to IN (' . implode(', ', $inv_ids) . ')' . "\n" .
                     'ORDER BY frr.parent_id';
            $cd_ids = $db->GetAssoc($query);
        }
        $this->registry->set('module', 'contracts', true);
        $this->registry->set('controller', 'contracts', true);
        $this->registry->set('action', 'listinvoices', true);

        // the model should be prepared in order to get files and other data
        $prepareModels = @intval($this->registry['prepareModels']);
        $this->registry->set('prepareModels', true, true);

        $models = array();
        if ($inv_ids) {
            //get invoices(and proformas if they don't have invoices issued) with their credit/debit notes
            $filters = array('where' => array('fir.id in (' . implode(',', array_merge($inv_ids, array_keys($cd_ids))) .')'),
                             'sort' => array('find_in_set(fir.id,"' . implode(',', array_merge($inv_ids, array_keys($cd_ids))) . '")'),
                             'sanitize' => true);
            $models = Finance_Incomes_Reasons::search($this->registry, $filters);
        }

        $invoices = array();
        foreach ($models as $m) {
            $t = $m->getGT2Vars();
            $m->getInvoicePaid();
            if (in_array($m->get('type'), array(PH_FINANCE_TYPE_CREDIT_NOTICE, PH_FINANCE_TYPE_DEBIT_NOTICE))) {
                //set credit/debit note into the invoice they are issued for
                $cd = $invoices[$cd_ids[$m->get('id')]]->get('credit_debit');
                $cd[$m->get('id')] = $m;
                $invoices[$cd_ids[$m->get('id')]]->set('credit_debit', $cd, true);
            } else {
                //current model is an invoice
                $m->set('credit_debit', array(), true);
                $invoices[$m->get('id')] = $m;
            }
        }

        $this->registry->set('module', 'finance', true);
        $this->registry->set('controller', 'invoices_templates', true);
        $this->registry->set('action', 'list', true);
        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
        $on_clause = array('where' => array(), 'search_fcp_placeholder' => true);
        Finance_Invoices_Templates::prepareRightsFilters($this->registry, $on_clause);
        if (!empty($on_clause['where'])) {
            $on_clause = '(' . implode(' AND ', $on_clause['where']) . ')';
        } else {
            $on_clause = '1';
        }
        $this->registry->set('module', 'contracts', true);
        $this->registry->set('controller', 'contracts', true);
        $this->registry->set('action', 'listinvoices', true);

        //prepare preview of templates not issued(approved,disapproved and not answered)
        $query = 'SELECT fiti.id AS idx, fit.id, fiti.issue_date, fiti.observer_response' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '  ON fit.id = fiti.parent_id AND fit.deleted_by = 0' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                 '  ON co.id = fiti.contract_id AND co.subtype="contract" AND co.status="closed"' . "\n";
        if (preg_match('#vcstm\.#', $on_clause)) {
            $query .= 'LEFT JOIN ' . DB_TABLE_VARIABLES_META . ' vm' . "\n" .
                      '  ON vm.model = "Contract" AND vm.model_type IN (0, co.type) AND vm.name = "default_financial_contact_person"' . "\n" .
                      'LEFT JOIN ' . DB_TABLE_VARIABLES_CSTM . ' vcstm' . "\n" .
                      '  ON vcstm.model = "Contract" AND vcstm.var_id = vm.id' . "\n" .
                      ' AND vcstm.model_id IN (0, co.id)' . "\n";
        }
        $query .= 'WHERE fiti.invoice_id = 0' . "\n" .
                  '  AND fiti.contract_id = ' . $this->get('id') . ' AND ' . $on_clause;
        $records = $db->GetAssoc($query);

        $templates = array();
        if (!empty($records)) {
            $results = Finance_Invoices_Templates::issueInvoices($this->registry, $records, true);
            foreach ($results as $info) {
                //check if any errors occurred
                if (!empty($info['errors'])) {
                    continue;
                }

                foreach ($info['invoices'] as $invoice) {
                    if (is_object($invoice)) {
                        $invoice->set('observer_response', $records[$invoice->get('id')]['observer_response'], true);
                        $invoice->set('template_issue_date', $records[$invoice->get('id')]['issue_date'], true);
                        $templates[] = $invoice;
                    }
                }
            }
        }
        // unset flag after all models are prepared (get files, translations and other data)
        if (!$prepareModels) {
            //restore the previous state of this switch in the registry
            $this->registry->remove('prepareModels');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
        return array(
            'invoices' => $invoices,
            'templates' => $templates,
            'all_count' => $all_rows,
            'rpp' => $filts['display'],
            'pages' => ceil($all_rows / $filts['display']),
            'page' => $filts['page']);
    }

    public function getAllHandovers($check_permissions = false) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];

        //get filters  - we need pagination only
        $filts = Contracts::saveSearchParams($this->registry, array(), $sessionPrefix = 'list_handovers_');
        $filts['display'] = 5;
        //prepare permissions for the invoices
        $this->registry->set('module', 'finance', true);
        $this->registry->set('controller', 'warehouses_documents', true);
        $this->registry->set('action', 'list', true);
        $types = array();
        $on_clause = array('where' => array('fwd.type = ' . PH_FINANCE_TYPE_HANDOVER));
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        Finance_Warehouses_Documents::prepareRightsFilters($this->registry, $on_clause, $types);

        //get issued handovers ids
        $query = 'SELECT SQL_CALC_FOUND_ROWS frr.parent_id' . "\n" .
                'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' as frr' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' as fwd' . "\n" .
                '  ON frr.parent_id = fwd.id AND ' . "\n" .
                '     (' . implode($on_clause['where']) . ' 1)' . "\n" .
                'WHERE frr.parent_model_name = "Finance_Warehouses_Document"' . "\n" .
                '  AND frr.link_to_model_name = "Contract"' . "\n" .
                '  AND frr.link_to = ' . $this->get('id') . "\n" .
                'ORDER BY frr.parent_id DESC' . "\n" .
                'LIMIT ' . ($filts['page'] - 1) * $filts['display'] . ', ' . $filts['display'];
        $wd_ids = $db->GetCol($query);
        $all_rows = $db->GetOne('SELECT FOUND_ROWS()');

        $this->registry->set('module', 'contracts', true);
        $this->registry->set('controller', 'contracts', true);
        $this->registry->set('action', 'listhandovers', true);

        // the model should be prepared in order to get files and other data
        $prepareModels = @intval($this->registry['prepareModels']);
        $this->registry->set('prepareModels', true, true);
        $gov = $this->registry->get('get_old_vars');
        if (!$gov) {
            $this->registry->set('get_old_vars', true, true);
        }

        $models = array();
        if ($wd_ids) {
            //get handovers
            $filters = array('where' => array('fwd.id in (' . implode(',', $wd_ids) .')'),
                'sort' => array('find_in_set(fwd.id,"' . implode(',', $wd_ids) . '")'),
                'sanitize' => true);
            $models = Finance_Warehouses_Documents::search($this->registry, $filters);
        }

        $handovers = array();
        foreach ($models as $m) {
            $m->getGT2Vars();
            $m->getBatchesData(true);
            $handovers[$m->get('id')] = $m;
        }

        // unset flag after all models are prepared (get files, translations and other data)
        if (!$prepareModels) {
            //restore the previous state of this switch in the registry
            $this->registry->remove('prepareModels');
        }
        if (!$gov) {
            $this->registry->remove('get_old_vars');
        }

        if ($sanitize_after) {
            $this->sanitize();
        }
        return array(
            'handovers' => $handovers,
            'all_count' => $all_rows,
            'rpp' => $filts['display'],
            'pages' => ceil($all_rows / $filts['display']),
            'page' => $filts['page']);
    }

    /**
     * Edits existing model
     *
     * @return bool - result of the operation
     */
    public function editTabs() {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        //save additional variables
        $result = $this->saveVars();
        if (!$result) {
            //rollback the transaction
            $db->FailTrans();
        }

        if ($this->registry['action'] == 'edittopic' && !$this->createSysfit('edit')) {
            $db->FailTrans();
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Change status/substatus
     *
     * @param mixed $parent_contract - parent contract when changing status of agreement or false when changing status of contract
     * @return bool - result of the operation
     */
    public function setStatus($parent_contract = false) {
        $flag_error = false;
        $flag_error_substatus = false;

        $permission_unlock = $this->checkPermissions('setstatus_unlock');

        $db = $this->registry['db'];
        //start transaction
        $db->StartTrans();

        if (!$this->get('status')) {
            $flag_error = true;
        } else {
            if ($this->get('id')) {
                $id_contract = $this->get('id');
            }
            $current_status = Contracts::getContractStatus($this->registry, $id_contract);
            $status_info = explode ('_', $this->get('status'));
            $status_name = $status_info[0];
            if ($current_status == 'locked' && $status_name == 'opened') {
                if (! $permission_unlock) {
                    $flag_error = true;
                }
            } else if ($current_status == 'closed' && ($status_name == 'opened' || $status_name == 'locked')) {
                $flag_error = true;
            }

            //takes the status properties based
            //if the status is defined in a dropdown it's a single
            // value containing the main status and the id of the substatus if such is defined
            @ $status_properties = explode('_', $this->get('status'));
            $new_status = $status_properties[0];

            if ($this->get('substatus')) {
                $substatus_properties = explode('_', $this->get('substatus'));
                if ($substatus_properties[0] != $new_status) {
                    $flag_error_substatus = true;
                } else {
                    $new_substatus = $substatus_properties[1];
                }
            } elseif (isset($status_properties[1])) {
                $new_substatus = $status_properties[1];
            }
        }

        if ($flag_error || $flag_error_substatus) {
            if (isset($current_status)) {
                $this->set('status', $current_status, true);
            }
            if (isset($current_substatus)) {
                $this->set('substatus', $current_substatus, true);
            }
            if ($flag_error) {
                $this->raiseError('error_invalid_status_change', 'status', -2);
            }
            if ($flag_error_substatus) {
                $this->raiseError('error_invalid_substatus_change', 'substatus', -3);
            }
            $db->CompleteTrans();
            return false;
        }

        if ($current_status != 'closed' && $new_status != 'opened') {
            if (!$this->validateDates()) {
                $this->set('has_invalid_dates', true, true);
                $db->FailTrans();
            }
            if (!$this->validateCustomerFinData()) {
                $db->FailTrans();
            }
            if (!$this->validateTemplates()) {
                $this->raiseError('error_invalid_company_data');
                $db->FailTrans();
            }
            if ($db->HasFailedTrans()) {
                $db->CompleteTrans();
                return false;
            }
        }

        $set = array();
        $set['status'] = sprintf("`status` = '%s'", $new_status);
        if (isset($new_substatus)) {
            $set['substatus'] = sprintf("`substatus` = %d", $new_substatus);
        } else {
            $set['substatus'] = "`substatus` = 0";
        }

        $set['modified'] = sprintf("`modified` = now()");
        $set['modified_by'] = sprintf("modified_by = '%s'", $this->registry['currentUser']->get('id'));
        $set['status_modified'] = sprintf("`status_modified` = now()");
        $set['status_modified_by'] = sprintf("status_modified_by = '%s'", $this->registry['currentUser']->get('id'));

        if ((!$this->get('num') || $this->get('num') == 'system') && $new_status == 'closed') {
            //set num
            if ($this->get('subtype') == 'contract') {
                $set['num'] = sprintf("num='%s'", $this->getNum());
                if (!$this->counter) {
                    $this->registry['messages']->setError($this->i18n('error_contracts_no_counter',
                                                          array($this->get('type_name'), $this->get('company_name'))));
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                } else {
                    $this->counter->increment();
                }
            } elseif ($current_status != 'closed' && $this->get('subtype_status') == 'waiting') {
                $set['num'] = sprintf("num='%s'", $this->getSubNum());
                if ($this->get('date_end_subtype') && $this->get('date_end_subtype') != '0000-00-00' && $this->get('date_end_subtype') <= date('Y-m-d')) {
                    $set['subtype_status'] = 'subtype_status = "executed"';
                } elseif ($this->get('date_start_subtype') <= date('Y-m-d')) {
                    $set['subtype_status'] = 'subtype_status = "started"';
                }
            }
        }

        //query to update the main table
        $query1 = 'UPDATE ' . DB_TABLE_CONTRACTS . "\n" .
                  'SET ' . implode(', ', $set) . "\n" .
                  'WHERE id=' . $this->get('id');
        $db->Execute($query1);

        $send = false;
        if ($current_status != $new_status && $new_status == 'closed') {
            if ($this->get('subtype') == 'annex' && $this->get('date_start_subtype') <= date('Y-m-d')) {
                if (empty($parent_contract)) {
                    //get the current contract
                    $filters = array('where' => array('co.id = ' . $this->get('parent_record')),
                                     'sanitize' => true);
                    $parent_contract = Contracts::searchOne($this->registry, $filters);
                }
                //push the new agreement into force
                if (!Contracts::pushIntoForce($this, $parent_contract)) {
                    $this->registry['messages']->setError($this->i18n('error_update_from_annex_unsuccessful', array($parent_contract->getModelTypeName())));
                    $db->FailTrans();
                    $db->CompleteTrans();
                    return false;
                } else {
                    $send = true;
                    //send mail to observer for started annex
                    $assignments = $parent_contract->get('assignments_observer');
                    $users_ids = array_keys($assignments);
                    $this->set('parent_num', $parent_contract->get('num'), true);
                    $this->set('customer_name', $parent_contract->get('customer_name'), true);
                    if (count($users_ids)) {
                        $template = 'contract_start_subtype';
                        $not_users = Users::getUsersNoSend($this->registry, $template);
                        $query = 'SELECT id, email FROM ' . DB_TABLE_USERS .
                                 ' WHERE id in (' . implode(',', $users_ids) . ') AND active=1';
                        $emails = $this->registry['db']->GetAssoc($query);
                        foreach ($emails as $u_id => $email) {
                            if ($u_id != $this->registry['currentUser']->get('id') && !in_array($u_id, $not_users)) {
                                $this->sendNotification($template, $email, $assignments[$u_id]['assigned_to_name']);
                            }
                        }
                    }
                }
            } elseif ($this->get('subtype') == 'contract' && !$this->saveOriginal()) {
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            }
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        //IMPORTANT!!!:
        //we send mails to the customer after all the other work is done
        //of course we will do this job in a background mode
        //so the user will not wait for the slow process of PDF generation and so on
        if (!empty($parent_contract)) {
            $send_mails = $parent_contract->get('send_mails');
            if ($result && $send && !empty($send_mails)) {
                $params = base64_encode(gzdeflate(json_encode($send_mails)));
                $url = sprintf('%s/index.php?%s=contracts&contracts=background_send&background_send=%d&user=%d&params=%s',
                                $this->registry['config']->getParam('crontab', 'base_host'), $this->registry['module_param'],
                                $parent_contract->get('id'), $this->registry['currentUser']->get('id'), rawurlencode($params));
                if (preg_match('#WIN#i', PHP_OS)) {
                    // IMPORTANT: In every windows nzoom installation there should be a wget.exe inside _libs/inc/ext/wget/
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = PH_EXT_DIR . 'wget\wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate "' . $url . '"';
                    //silence the output of command line
                    $command .= ' > nul &';
                    pclose(popen($command, 'r'));
                } else {
                    $url = preg_replace('#\&#', '\&', $url);
                    //--spider  - don't download anything
                    //--no-check-certificate - don't validate the server's certificate
                    //--append-output - append messages to FILE (saves the log file into designated file)
                    $command = 'wget --tries=1 --timeout=10800 --background --append-output="' . PH_LOGGER_DIR . 'wget/wget.log" --spider --no-check-certificate ' . $url;
                    //silence the output of command line
                    $command .= ' > /dev/null 2>&1 & ';
                    exec($command, $output, $status);
                }
                $this->registry['messages']->setMessage($this->i18n('message_contracts_email_for_sent_invoices'));
            }
        }
        return $result;
    }

    /**
     * Creates final invoices template for the contract
     * with included all NOT recurrent articles
     * which are not present in other templates
     *
     * @param string $action - current action performed with model
     * @return boolean - result of the operation
     */
    public function createSysfit($action = '') {
        $db = &$this->registry['db'];

        //check if the type is fiscal
        $query = 'SELECT `fiscal` FROM ' . DB_TABLE_CONTRACTS_TYPES . ' WHERE id = ' . $this->get('type');
        $fiscal = $db->GetOne($query);
        if (!$fiscal) {
            return true;
        }
        $db->StartTrans();
        if ($this->get('date_end') > '0000-00-00' && $this->get('date_start') > $this->get('date_end')) {
            //contract termination
            //set the current final template of the contract(if any) as deleted
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET deleted = NOW(), deleted_by = ' . PH_AUTOMATION_USER . "\n" .
                     'WHERE contract_id = ' . $this->get('id') . ' AND type = ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL;
            $db->Execute($query);
            $db->CompleteTrans();
            return true;
        }

        $templates = $cValues = array();
        //get the unique key for templates rows
        $unique_key = $this->getUniqueKey();

        //as we have to be here only after all the data for the model has been saved
        //we get the data directly from the DB
        $old_vars_option = $this->registry->get('get_old_vars');

        $this->registry->set('get_old_vars', true, true);
        $cgt2 = $this->getGT2Vars();
        foreach ($cgt2['values'] as $key => $values) {
            if ($values['current'] != 0) {
                //remove recurrent rows
                unset($cgt2['values'][$key]);
                continue;
            }
            //calculate the key for the row
            $row_key = array();
            foreach ($unique_key as $uk) {
                $row_key[] = $values[$uk] ?? '';
            }
            $row_key = implode('^^^', $row_key);
            $cValues[$row_key] = $values;
        }
        if (empty($cValues)) {
            //there are not any non-recurrent articles
            $this->registry->set('get_old_vars', $old_vars_option, true);
            //set the current final template of the contract(if any) as deleted
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET deleted = NOW(), deleted_by = ' . PH_AUTOMATION_USER . "\n" .
                     'WHERE contract_id = ' . $this->get('id') . ' AND type = ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL;
            $db->Execute($query);
            $db->CompleteTrans();
            return true;
        }

        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';

        $sysfit = array();
        if ($action != 'add') {
            //get invoices templates of the agreement sorted by periods
            $params = array(
                //'status' => array('opened', 'locked'),
                'exclude_invoices' => true,
                'sanitize' => true,
                'sort' => array('fit.periods_start', 'fit.periods_end', 'fit.issue_date'),
            );
            $this->getInvoicesTemplates($params);
            $templates = $this->get('invoices_templates');
            $this->unsetProperty('invoices_templates', true);
            $this->unsetProperty('invoice_types', true);

            //remove recurrent rows from each template
            //as we don't need to make calculation for them
            //and check for changes
            $advanced = array();
            foreach ($templates as $t => $template) {
                if ($template->get('type') == PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE) {
                    //advances will be processed later
                    $advanced[] = $template;
                    unset($templates[$t]);
                    continue;
                } elseif ($template->get('type') == PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL) {
                    //exists final template which we will rewrite now
                    $final_template = $template->unsanitize();
                    unset($templates[$t]);
                    continue;
                }
                $tgt2 = $template->get('grouping_table_2');
                $tValues = array();
                foreach ($tgt2['values'] as $key => $values) {
                    //calculate the key for the row
                    $row_key = array();
                    foreach ($unique_key as $uk) {
                        $row_key[] = $values[$uk] ?? '';
                    }
                    $row_key = implode('^^^', $row_key);

                    if (empty($cValues[$row_key])) {
                        //this row is not in the non-recurrent rows of the contract
                        unset($tgt2['values'][$key]);
                        continue;
                    }
                    $tValues[$row_key] = $values;
                }
                if (empty($tValues)) {
                    //this template has not any non-recurrent articles so remove it
                    unset($templates[$t]);
                    continue;
                }

                //check what has been changed between the contract row and the template rows
                foreach ($tValues as $key => $values) {
                    //get current changes state
                    if (!empty($cValues[$key]['changes'])) {
                        $changes = $cValues[$key]['changes'];
                    } else {
                        $changes = array();
                    }

                    //check for quantity changes
                    if ($values['quantity'] != $cValues[$key]['quantity'] && !in_array('quantity', $changes)) {
                        $changes[] = 'quantity';
                    }
                    //check for price changes
                    if ($values[$cgt2['calculated_price']] != $cValues[$key][$cgt2['calculated_price']] && !in_array($cgt2['calculated_price'], $changes)) {
                        $changes[] = $cgt2['calculated_price'];
                    }
                    //check for discount/surplus changes
                    if ($values['subtotal_with_discount'] != $cValues[$key]['subtotal_with_discount']) {
                        if (empty($changes)) {
                            //only surplus or discount has been made
                            $changes[] = 'discount';
                        } elseif (count($changes) > 1) {
                            //more than one changes...we'll not care about the discount/surplus changes
                        } else {
                            $change = reset($changes);
                            //set the template changed field in the contract row
                            $tmp = $cValues[$key];
                            $tmp[$change] = $values[$change];
                            //recalculate the contract row
                            list($tmp) = Contracts::calculateGT2Rows($this->registry, array($tmp), $cgt2['plain_values']['total_vat_rate']);
                            if ($values['subtotal_with_discount'] != $tmp['subtotal_with_discount'] && !in_array('discount', $changes)) {
                                //discount/surplus has been changed
                                $changes[] = 'discount';
                            }
                        }
                    }
                    $cValues[$key]['changes'] = $changes;
                }
                $tgt2['values'] = $tValues;
                $templates[$t]->set('grouping_table_2', $tgt2, true);
            }
            //start to create the data for the sysfit
            $error = false;
            foreach ($cValues as $key => $values) {
                if (empty($values['changes'])) {
                    $values['changes'] = array();
                }
                $change = '';
                $sysfit[$key] = $values;
                if (count($values['changes']) > 1) {
                    //multiple changes has been made
                    //rebuild row to be compatible with the changes
                    //e.g. 1 quantity and price = subtotal_with_discount
                    $sysfit[$key][$cgt2['calculated_price']] = $values['subtotal_with_discount'];
                    $sysfit[$key]['quantity'] = 1;
                    $sysfit[$key]['discount_value'] = $sysfit[$key]['discount_percentage'] =
                    $sysfit[$key]['surplus_value'] = $sysfit[$key]['surplus_percentage'] = 0;
                    //recalculate the row
                    list($sysfit[$key]) = Contracts::calculateGT2Rows($this->registry, array($sysfit[$key]), $cgt2['plain_values']['total_vat_rate']);
                } elseif (count($values['changes']) == 1) {
                    $change = reset($values['changes']);
                    if ($change == 'discount') {
                        $change = $cgt2['calculated_price'];
                    }
                }
                $found = false;
                foreach ($templates as $t => $template) {
                    $tgt2 = $template->get('grouping_table_2');

                    if (!isset($tgt2['values'][$key])) {
                        //the article is not present in this template
                        continue;
                    }
                    $found = true;
                    if ($template->get('recurrent')) {
                        //we will calculate only the row we need
                        $tmp = $tgt2;
                        $tmp['values'] = array($tgt2['values'][$key]);
                        $template->set('grouping_table_2', $tmp, true);
                        if (count($values['changes']) > 1 || in_array($change, array($cgt2['calculated_price'], ''))) {
                            //multiple changes have been made or price/discount/nothing has been changed
                            //we will make a small trick with the template to get only one row result
                            $template->set('single_period_rows', 'one_one', true);
                        } else {
                            //quantity has been changed
                            //we will make a small trick with the template to get only one row result
                            $template->set('single_period_rows', 'one_all', true);
                        }
                        $from = $template->get('periods_start');
                        $template->set('status', 'opened', true);
                        //calculate total amount the template will issue for this row
                        while ($from <= $template->get('periods_end')) {
                            list($tmp, $to) = $template->calculateRecurrence(array('date_from' => $from));
                            //apply indexes for the rows if any indexes are present
                            $tmp = Contracts::getIndexedValues($this->registry, $tmp);
                            $tmp = array_pop($tmp);
                            $template->set('status', 'locked', true);
                            $from = date_add(date_create($to), new DateInterval('P1D'))->format('Y-m-d');
                            if (count($values['changes']) > 1) {
                                $sysfit[$key][$cgt2['calculated_price']] -= $tmp['subtotal_with_discount'];
                            } elseif (count($values['changes']) == 1) {
                                $sysfit[$key][$change] -= $tmp[$change];
                            } else {
                                //nothing has been changed
                                $sysfit[$key][$cgt2['calculated_price']] -= $tmp[$cgt2['calculated_price']];
                                $values['changes'] = array($cgt2['calculated_price']);
                                $change = $cgt2['calculated_price'];
                            }
                        }
                    } else {
                        if (count($values['changes']) > 1) {
                            //multiple changes has been made
                            $sysfit[$key][$cgt2['calculated_price']] -= $tgt2['values'][$key]['subtotal_with_discount'];
                        } elseif ($change) {
                            //only one field has been changed
                            $sysfit[$key][$change] -= $tgt2['values'][$key][$change];
                        }
                    }
                    $template->set('grouping_table_2', $tgt2, true);
                }
                if ($found && empty($values['changes'])) {
                    //the article is present in the templates
                    //but nothig is changed, e.g. everithing will be invoiced
                    unset($sysfit[$key]);
                    continue;
                }
                list($sysfit[$key]) = Contracts::calculateGT2Rows($this->registry, array($sysfit[$key]), $cgt2['plain_values']['total_vat_rate']);
                if ($sysfit[$key]['subtotal_with_discount'] == 0) {
                    //this row is invoiced
                    unset($sysfit[$key]);
                } elseif ($sysfit[$key]['subtotal_with_discount'] < 0) {
                    //this row is over invoiced
                    $this->registry['messages']->setError($this->i18n('error_non_current_article_over_invoiced', array($sysfit[$key]['article_name'])));
                    $error = true;
                    unset($sysfit[$key]);
                }
            }
            if ($error) {
                //there are overtemplated/overinvoiced articles
                $this->registry->set('get_old_vars', $old_vars_option, true);
                $db->FailTrans();
                $db->CompleteTrans();
                return false;
            }
            foreach ($advanced as $key => $template) {
                $tgt2 = $template->getGT2Vars();
                if ($template->get('recurrent')) {
                    $from = $template->get('periods_start');
                    $template->set('status', 'opened', true);
                    //calculate total amount the template will issue for this row
                    while ($from <= $template->get('periods_end')) {
                        list($tmp, $to) = $template->calculateRecurrence(array('date_from' => $from));
                        //apply indexes for the rows if any indexes are present
                        $tmp = Contracts::getIndexedValues($this->registry, $tmp);
                        $template->set('status', 'locked', true);
                        $from = date_add(date_create($to), new DateInterval('P1D'))->format('Y-m-d');
                        foreach ($tmp as $k => $v) {
                            //set negative prices for the advanced rows
                            $v[$cgt2['calculated_price']] *= -1;
                            $sysfit[] = $v;
                        }
                    }
                } else {
                    foreach ($tgt2['values'] as $k => $v) {
                        //set negative prices for the advanced rows
                        $v[$cgt2['calculated_price']] *= -1;
                        $sysfit[] = $v;
                    }
                }
            }
            if ($action == 'into_force' && !empty($sysfit)) {
                //we have to subtract already invoiced non recurrent articles
                $invoices = $this->getIssuedFinance();
                $gov = $this->registry->get('get_old_vars');
                $this->registry->set('get_old_vars', true, true);
                $advanced = array();
                //check for advances first
                foreach ($invoices as $i => $invoice) {
                    $invoice->getGT2Vars();
                    $invoice->checkAddingCredit(true, 'Contract');
                    $igt2 = $invoice->get('grouping_table_2');
                    //check for the currency first
                    $igt2['values'] = Contracts::changeGT2RowsCurrency($this->registry, $igt2['values'], $cgt2['calculated_price'], $igt2['plain_values']['currency'], $cgt2['plain_values']['currency']);
                    $igt2['values'] = Contracts::calculateGT2Rows($this->registry, $igt2['values'], $igt2['plain_values']['total_vat_rate']);
                    if ($invoice->get('template_type') == PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE) {
                        foreach ($igt2['values'] as $v => $vals) {
                            //invoice from advanced template - add it to the sysfit with negative price
                            $vals[$cgt2['calculated_price']] *= -1;
                            $advanced[] = $vals;
                        }
                        unset($invoices[$i]);
                        continue;
                    } else {
                        //check what has been changed between the contract row and the invoices rows
                        foreach ($igt2['values'] as $v => $vals) {
                            //check for negative row(excluded row from advanced/medial invoice)
                            if ($vals['subtotal_with_discount'] < 0) {
                                unset($igt2['values'][$v]);
                                continue;
                            }
                            //calculate the key for the row
                            $row_key = array();
                            foreach ($unique_key as $uk) {
                                $row_key[] = $vals[$uk] ?? '';
                            }
                            $row_key = implode('^^^', $row_key);
                            if (!isset($sysfit[$row_key])) {
                                unset($igt2['values'][$v]);
                                continue;
                            }
                            //get current changes state
                            if (!empty($sysfit[$row_key]['changes'])) {
                                $changes = $sysfit[$row_key]['changes'];
                            } else {
                                $changes = array();
                            }

                            //check for quantity changes
                            if ($vals['quantity'] != $sysfit[$row_key]['quantity'] && !in_array('quantity', $changes)) {
                                $changes[] = 'quantity';
                            }
                            //check for price changes
                            if ($vals[$cgt2['calculated_price']] != $sysfit[$row_key][$cgt2['calculated_price']] && !in_array($cgt2['calculated_price'], $changes)) {
                                $changes[] = $cgt2['calculated_price'];
                            }
                            //check for discount/surplus changes
                            if ($vals['subtotal_with_discount'] != $sysfit[$row_key]['subtotal_with_discount']) {
                                if (empty($changes)) {
                                    //only surplus or discount has been made
                                    $changes[] = 'discount';
                                } elseif (count($changes) > 1) {
                                    //more than one change...we'll not care about the discount/surplus changes
                                } else {
                                    $change = reset($changes);
                                    //set the invoice changed field in the sysfit row
                                    $tmp = $sysfit[$row_key];
                                    $tmp[$change] = $vals[$change];
                                    //recalculate the sysfit row
                                    list($tmp) = Contracts::calculateGT2Rows($this->registry, array($tmp), $cgt2['plain_values']['total_vat_rate']);
                                    if ($vals['subtotal_with_discount'] != $tmp['subtotal_with_discount'] && !in_array('discount', $changes)) {
                                        //discount/surplus has been changed
                                        $changes[] = 'discount';
                                    }
                                }
                            }
                            $sysfit[$row_key]['changes'] = $changes;
                        }
                        if (empty($igt2['values'])) {
                            unset($invoices[$i]);
                            continue;
                        }
                        $invoices[$i]->set('grouping_table_2', $igt2, true);
                    }
                }
                foreach ($invoices as $i => $invoice) {
                    $igt2 = $invoice->get('grouping_table_2');
                    foreach ($igt2['values'] as $v => $vals) {
                        //calculate the key for the row
                        $row_key = array();
                        foreach ($unique_key as $uk) {
                            $row_key[] = $vals[$uk] ?? '';
                        }
                        $row_key = implode('^^^', $row_key);
                        if (!isset($sysfit[$row_key])) {
                            continue;
                        }
                        if (empty($sysfit[$row_key]['changes'])) {
                            //fully invoiced row
                            unset($sysfit[$row_key]);
                            continue;
                        }
                        $change = '';
                        if (count($sysfit[$row_key]['changes']) > 1) {
                            //multiple changes have been made
                            //rebuild row to be compatible with the changes
                            //e.g. 1 quantity and price = subtotal_with_discount
                            $sysfit[$row_key][$cgt2['calculated_price']] = $sysfit[$row_key]['subtotal_with_discount'];
                            $sysfit[$row_key]['quantity'] = 1;
                            $sysfit[$row_key]['discount_value'] = $sysfit[$row_key]['discount_percentage'] =
                            $sysfit[$row_key]['surplus_value'] = $sysfit[$row_key]['surplus_percentage'] = 0;
                            //recalculate the row
                            list($sysfit[$row_key]) = Contracts::calculateGT2Rows($this->registry, array($sysfit[$row_key]), $cgt2['plain_values']['total_vat_rate']);
                        } elseif (count($sysfit[$row_key]['changes']) == 1) {
                            $change = reset($sysfit[$row_key]['changes']);
                            if ($change == 'discount') {
                                $change = $cgt2['calculated_price'];
                            }
                        }
                        if (count($sysfit[$row_key]['changes']) > 1) {
                            //multiple changes have been made
                            $sysfit[$row_key][$cgt2['calculated_price']] -= $vals['subtotal_with_discount'];
                        } elseif ($change) {
                            //only one field has been changed
                            $sysfit[$row_key][$change] -= $vals[$change];
                        }
                        list($sysfit[$row_key]) = Contracts::calculateGT2Rows($this->registry, array($sysfit[$row_key]), $cgt2['plain_values']['total_vat_rate']);
                        if ($sysfit[$key]['subtotal_with_discount'] <= 0) {
                            //this row is invoiced
                            unset($sysfit[$key]);
                        }
                    }
                }

                $sysfit = array_merge($sysfit, $advanced);
                $this->registry->set('get_old_vars', $gov, true);
            }
        } else {
            //get all not recurrent articles from the contract
            $sysfit = array_values($cValues);
        }

        if (!empty($sysfit)) {
            //get contract from the database to be sure that all of its properties are filled
            $filters = array('where' => array('co.id = ' . $this->get('id')), 'sanitize' => true);
            $contract = Contracts::searchOne($this->registry, $filters);

            if (empty($final_template)) {
                //check if we have sysfit already created
                $filters = array('where' => array('fit.contract_id = ' . $contract->get('id'), 'fit.type = ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL, 'fit.deleted IS NOT NULL'));
                $final_template = Finance_Invoices_Templates::searchOne($this->registry, $filters);
                if (empty($final_template)) {
                    $final_template = new Finance_Invoices_Template($this->registry, array());
                    $final_template->set('type', PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL, true);
                    $final_template->set('contract_name', $contract->get('name'), true);
                    $final_template->set('contract_id', $contract->get('id'), true);
                    $final_template->set('company', $contract->get('company'), true);
                    $final_template->set('office', $contract->get('office'), true);
                    $final_template->set('customer', $contract->get('customer'), true);
                    $final_template->set('trademark', $contract->get('trademark'), true);
                    $final_template->set('project', $contract->get('project'), true);
                } elseif ($final_template->get('deleted_by')) {
                    //we have to restore the template
                    Finance_Invoices_Templates::restore($this->registry, array($final_template->get('id')));
                }
            }
            $final_template->set('link_to_model_name', 'Contract', true);
            $final_template->set('link_to', $contract->get('id'), true);
            //some settings have to be made for the currency, generate pattern, email pattern,
            //date of payment, fiscal event date, etc.
            //these settings we will make at the moment of template issue

            $tmp = $final_template->getGT2Vars();
            $tmp['values'] = array_values($sysfit);
            $final_template->set('total_vat_rate', $cgt2['plain_values']['total_vat_rate'], true);
            $tmp['plain_values']['total_vat_rate'] = $cgt2['plain_values']['total_vat_rate'];
            $final_template->set('currency', $cgt2['plain_values']['currency'], true);
            $tmp['plain_values']['currency'] = $cgt2['plain_values']['currency'];
            /*$vals = array();
            foreach ($tmp['values'] as $k => $v) {
                $vals[$v['id']] = $v;
            }
            $tmp['values'] = $vals;*/
            $final_template->set('grouping_table_2', $tmp, true);
            $final_template->calculateGT2();
            $tmp = $final_template->get('grouping_table_2');

            if ($tmp['plain_values']['total_with_vat'] < 0) {
                //the template total is less than zero...
                //there is calculation error
                //so notify the user
                $this->registry['messages']->setError($this->i18n('error_contracts_negative_sysfit'));
                $db->FailTrans();
            } else {
                $final_template->set('table_values_are_set', true, true);

                $final_template->unsetProperty('layouts_view', true);
                $final_template->unsetProperty('layouts_edit', true);
                if (!$final_template->save()) {
                    $db->FailTrans();
                }
            }
        } else {
            //set the current final template of the contract(if any) as deleted
            $query = 'UPDATE ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' SET deleted = NOW(), deleted_by = ' . PH_AUTOMATION_USER . "\n" .
                     'WHERE contract_id = ' . $this->get('id') . ' AND type = ' . PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL;
            $db->Execute($query);
        }

        $this->registry->set('get_old_vars', $old_vars_option, true);
        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();
        return $result;
    }

    /**
     * Creates exact copy of the current contract
     * to be used for data recovery and for information
     *
     * @return boolean - result of the operation
     */
    public function saveOriginal() {
        /** @var ADODB_mysqli $db */
        $db = $this->registry['db'];

        /*
        $contract_cstm_var_ids = $db->getCol(
            'SELECT id FROM ' . DB_TABLE_FIELDS_META .
            ' WHERE model = "Contract" AND model_type = "' . $this->get('type') . '"' .
            '   AND gt2 = 1 AND name RLIKE "^(article|free)_"'
        );
        $fin_invoice_cstm_var_ids = $db->getCol(
            'SELECT id FROM ' . DB_TABLE_FIELDS_META .
            ' WHERE model = "Finance_Invoices_Template" AND gt2 = 1 AND name RLIKE "^(article|free)_"'
        );
        */

        $db->StartTrans();

        //copy from contracts tables data
        $defs = array(
            'table' => DB_TABLE_CONTRACTS,
            'method' => 'insert',
            'condition' => 'id = ' . $this->get('id'),
            'ignore' => array('id'),
            'set' => array(
                'subtype' => '"original"',
                'parent_record' => sprintf('"%d"', $this->get('id')),
                'added' => 'NOW()',
                'modified' => 'NOW()',
                'added_by' => $this->registry['currentUser']->get('id'),
                'modified_by' => $this->registry['currentUser']->get('id'),
            ),
            'copy' => 'all',
            'dependences' => array(
                array(
                    'table' => DB_TABLE_CONTRACTS_I18N,
                    'condition' => 'parent_id = ' . $this->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'parent_id' => '<insert_id>',
                        'translated' => 'NOW()',
                    ),
                    'copy' => 'all',
                ),
                array(
                    'table' => DB_TABLE_CONTRACTS_CSTM,
                    'condition' => 'model_id = ' . $this->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'model_id' => '<insert_id>',
                        'added' => 'NOW()',
                        'modified' => 'NOW()',
                        'added_by' => $this->registry['currentUser']->get('id'),
                        'modified_by' => $this->registry['currentUser']->get('id'),
                    ),
                    'copy' => 'all',
                ),
                /* no need to save cstm relatives for the original contract
                array(
                    'table' => DB_TABLE_CSTM_RELATIVES,
                    'condition' => 'model = "Contract" AND model_id = ' . $this->get('id'),
                    'method' => 'insert',
                    'set' => array(
                        'model_id' => '<insert_id>',
                    ),
                    'copy' => 'all',
                ),
                */
                //copy GT2 rows one-by-one
                array(
                    'table' => DB_TABLE_GT2_DETAILS,
                    'condition' => 'model = "Contract" AND model_id = ' . $this->get('id'),
                    'unique' => 'id',
                    'method' => 'insert-one',
                    'ignore' => array('id'),
                    'set' => array(
                        'model_id' => '<insert_id>',
                        'added' => 'NOW()',
                        'modified' => 'NOW()',
                        'added_by' => $this->registry['currentUser']->get('id'),
                        'modified_by' => $this->registry['currentUser']->get('id'),
                    ),
                    'copy' => 'all',
                    'dependences' => array(
                        array(
                            'table' => DB_TABLE_GT2_DETAILS_I18N,
                            'replace_condition' => 'parent_id',
                            'set' => array(
                                'parent_id' => '<insert_id>',
                                'translated' => 'NOW()',
                                'translated_by' => $this->registry['currentUser']->get('id'),
                            ),
                            'copy' => 'all',
                        ),
                        array(
                            'table' => DB_TABLE_GT2_INDEXES,
                            'replace_condition' => 'parent_id',
                            'set' => array(
                                'parent_id' => '<insert_id>',
                            ),
                            'copy' => 'all',
                        ),
                        /* no need to save cstm relatives for the original contract
                        array(
                            'table' => DB_TABLE_CSTM_RELATIVES,
                            'replace_condition' =>
                                'model = "Contract"' .
                                ' AND model_id !="' . $this->get('id') . '"' .
                                ' AND var_id IN (' . implode(', ', $contract_cstm_var_ids) . ')' .
                                ' AND num',
                            'method' => 'update',
                            'set' => array(
                                'num' => '<insert_id>',
                            ),
                        ),
                       */
                    ),
                ),
                array(
                    'table' => DB_TABLE_FINANCE_INVOICES_TEMPLATES,
                    'condition' => 'contract_id = ' . $this->get('id'),
                    'method' => 'insert-one',
                    'unique' => 'id',
                    'ignore' => array('id'),
                    'set' => array(
                        'contract_id' => '<insert_id>',
                        'added' => 'NOW()',
                        'modified' => 'NOW()',
                        'added_by' => $this->registry['currentUser']->get('id'),
                        'modified_by' => $this->registry['currentUser']->get('id'),
                    ),
                    'copy' => 'all',
                    'dependences' => array(
                        array(
                            'table' => DB_TABLE_FINANCE_INVOICES_TEMPLATES_I18N,
                            'replace_condition' => 'parent_id',
                            'set' => array(
                                'parent_id' => '<insert_id>',
                                'translated' => 'NOW()',
                                'translated_by' => $this->registry['currentUser']->get('id'),
                            ),
                            'copy' => 'all',
                        ),
                        /* no need to save cstm relatives for the original contract
                        array(
                            'table' => DB_TABLE_CSTM_RELATIVES,
                            'replace_condition' => 'model = "Finance_Invoices_Template" AND model_id',
                            'set' => array(
                                'model_id' => '<insert_id>',
                            ),
                            'copy' => 'all',
                        ),
                        */
                        array(
                            'table' => DB_TABLE_GT2_DETAILS,
                            'replace_condition' => 'model = "Finance_Invoices_Template" AND model_id',
                            'unique' => 'id',
                            'method' => 'insert-one',
                            'ignore' => array('id'),
                            'set' => array(
                                'model_id' => '<insert_id>',
                                'added' => 'NOW()',
                                'modified' => 'NOW()',
                                'added_by' => $this->registry['currentUser']->get('id'),
                                'modified_by' => $this->registry['currentUser']->get('id'),
                            ),
                            'copy' => 'all',
                            'dependences' => array(
                                array(
                                    'table' => DB_TABLE_GT2_DETAILS_I18N,
                                    'replace_condition' => 'parent_id',
                                    'set' => array(
                                        'parent_id' => '<insert_id>',
                                        'translated' => 'NOW()',
                                        'translated_by' => $this->registry['currentUser']->get('id'),
                                    ),
                                    'copy' => 'all',
                                ),
                                array(
                                    'table' => DB_TABLE_GT2_INDEXES,
                                    'replace_condition' => 'parent_id',
                                    'set' => array(
                                        'parent_id' => '<insert_id>',
                                    ),
                                    'copy' => 'all',
                                ),
                                /* no need to save cstm relatives for the original contract
                                array(
                                    'table' => DB_TABLE_CSTM_RELATIVES,
                                    'replace_condition' =>
                                        'model = "Finance_Invoices_Template"' .
                                        ' AND model_id = (SELECT MAX(fit.id) FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' .
                                        ' JOIN ' . DB_TABLE_CONTRACTS . ' AS co ON fit.contract_id = co.id' .
                                        ' WHERE co.parent_record = "' . $this->get('id') . '" AND co.subtype = "original")' .
                                        ' AND var_id IN (' . implode(', ', $fin_invoice_cstm_var_ids) . ')' .
                                        ' AND num',
                                    'method' => 'update',
                                    'set' => array(
                                        'num' => '<insert_id>',
                                    ),
                                ),
                                */
                            ),
                        ),
                    ),
                ),
            ),
        );
        Contracts::copyTableData($this->registry, $defs);

        $result = !$db->HasFailedTrans();
        $db->CompleteTrans();

        return $result;
    }


    /**
     * Prepare data for insert/update in main table
     *
     * @return array - data
     */
    public function prepareMainData() {
        $set = array();
        if ($this->isDefined('custom_num')) {
            $set['custom_num'] = sprintf("custom_num='%s'", $this->get('custom_num'));
        }
        if ($this->isDefined('office')) {
            if ($this->get('office')) {
                $office = $this->get('office');
            } else {
                $office = 0;
            }
            $set['office'] = sprintf("office=%d", $office);
        }
        if ($this->isDefined('employee')) {
            $set['employee'] = sprintf("employee=%d", $this->get('employee'));
        }
        if ($this->isDefined('company')) {
            $set['company'] = sprintf("company=%d", $this->get('company'));
        }
        if ($this->isDefined('customer')) {
            $set['customer'] = sprintf("customer=%d", $this->get('customer'));
        }
        if ($this->isDefined('branch')) {
            $set['branch'] = sprintf("branch=%d", $this->get('branch'));
        }
        if ($this->isDefined('contact_person')) {
            $set['contact_person'] = sprintf("contact_person=%d", $this->get('contact_person'));
        }
        if ($this->isDefined('trademark')) {
            $set['trademark'] = sprintf("trademark=%d", $this->get('trademark'));
        }
        if ($this->isDefined('project')) {
            $set['project'] = sprintf("project=%d", $this->get('project'));
        }
        if ($this->isDefined('group')) {
            $set['group'] = sprintf("`group`=%d", $this->get('group'));
        }
        if ($this->isDefined('department')) {
            $set['department'] = sprintf("`department`=%d", $this->get('department'));
        }
        if ($this->isDefined('subtype') && $this->get('subtype')) {
            $set['subtype'] = sprintf("`subtype`='%s'", $this->get('subtype'));
        } elseif ($this->registry['request']->get('action') == 'add') {
            //add contract action
            $set['subtype'] = sprintf("`subtype`='%s'", 'contract');
        }
        if ($this->isDefined('parent_record')) {
            $set['parent_record'] = sprintf("`parent_record`=%d", $this->get('parent_record'));
        }
        $fields = array($this->get('change_formula_date_sign'),
                       $this->get('change_formula_date_start'),
                       $this->get('change_formula_date_validity'),
                       $this->get('change_formula_date_end'));

        foreach ($fields as $field) {
            if ($field && $this->isDefined($field)) {
                if (!isset($fs)) {
                    if ($this->get('id')) {
                        $query = 'SELECT formula_status FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $this->get('id');
                        $fs = $this->registry['db']->GetOne($query);
                    } else {
                        $fs = 'ready';
                    }
                    $new_fs = 'ready';
                    $formulas = $this->getFormulas();
                    $formula_vars = $this->getFormulaVars();
                    $replace_vars = array();
                    foreach ($formula_vars as $var) {
                        if ($var['model_id']) {
                            $replace_vars[$var['name']] = $var['value'];
                        }
                    }
                    $extender = new Extender();
                    $extender->placeholders = $replace_vars;
                }
                if ($this->get($field)) {
                    $set[$field] = sprintf("%s='%s'", $field, $this->get($field));
                } else {
                    $set[$field] = sprintf("%s=%s", $field, 'null');
                }
                $tmp_val = 'null';
                if (preg_match('#_formula$#', $field)) {
                    if (!empty($formulas[$this->get($field)])) {
                        if ($this->calculateFormulaValue(
                            $extender,
                            $formulas[$this->get($field)]['formula'],
                            $tmp_val,
                            array(
                                'id' => $this->get($field),
                                'var' => $field,
                            )
                        )) {
                            $tmp_val = "'" . $tmp_val . "'";
                        } else {
                            $tmp_val = 'null';
                            $new_fs = 'basic';
                        }
                    }
                    $other_field = preg_replace('#_formula$#', '', $field);
                } else {
                    $other_field = $field . '_formula';
                }
                $set[$other_field] = sprintf("%s=%s", $other_field, $tmp_val);
            }
        }
        //define the formula status of the model
        if (isset($fs)) {
            if ($fs == 'ready' || $fs == 'basic') {
                //new status
                $set['formula_status'] = sprintf("formula_status='%s'", $new_fs);
                $this->set('formula_status', $new_fs, true);
            } elseif ($fs == 'both' || $fs == 'additional') {
                if ($new_fs == 'ready') {
                    //no more formulas in the basic vars
                    $set['formula_status'] = "formula_status='additional'";
                    $this->set('formula_status', 'additional', true);
                } else {
                    $set['formula_status'] = sprintf("formula_status='%s'", 'both');
                    $this->set('formula_status', 'both', true);
                }
            }
            $this->unsetProperty('formula_status_additional', true);
        }
        if ($this->isDefined('date_start_subtype')) {
            $set['date_start_subtype'] = sprintf("date_start_subtype='%s'", $this->get('date_start_subtype'));
        }
        if ($this->isDefined('date_end_subtype')) {
            $set['date_end_subtype'] = sprintf("date_end_subtype='%s'", $this->get('date_end_subtype'));
        }
        if ($this->isDefined('date_sign_subtype')) {
            $set['date_sign_subtype'] = sprintf("date_sign_subtype='%s'", $this->get('date_sign_subtype'));
        }
        if ($this->isDefined('active')) {
            $set['active'] = sprintf("active=%d", $this->get('active'));
        } elseif (!$this->get('id')) {
            $set['active'] = sprintf("active=%d", 1);
        }
        $set['modified']        = sprintf("modified=now()");
        $set['modified_by']     = sprintf("modified_by=%d", $this->registry['currentUser']->get('id'));
        if ($this->registry['currentUser']->get('is_portal')) {
            $set['is_portal'] = "is_portal=1";
        } elseif ($this->isDefined('is_portal')) {
            $set['is_portal'] = sprintf("is_portal=%d", $this->get('is_portal'));
        }
        if ($this->isDefined('status')) {
            $set['status'] = sprintf("status='%s'", $this->get('status'));
        }

        if ($this->get('subtype') == 'annex') {
            if ($this->get('system')) {
                $set['num'] = 'num = "system"';
            } elseif (preg_match('#add|edit#', $this->registry['action'])) {
                $set['num'] = 'num = ""';
            }
        }

        // complete custom subtype status if any is set
        if ($this->isDefined('custom_subtype_status')) {
            $set['subtype_status'] = sprintf('`subtype_status`="%s"', $this->get('custom_subtype_status'));
        }

        return $set;
    }

    /**
     * Update I18N table of the model
     *
     * @return bool - result of the operation
     */
    public function updateI18N($action = '') {
        $db = $this->registry['db'];
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        require_once PH_MODULES_DIR . 'projects/models/projects.factory.php';
        require_once PH_MODULES_DIR . 'offices/models/offices.factory.php';

        if ($action == 'add' || $action == 'transform') {
            //compose extender
            $extender = new Extender;

            //get customer details
            if (preg_match('#\[customer_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes')))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array ('c.id = ' . $this->get('customer')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitaze' => true);
                    $customer = Customers::searchOne($this->registry, $filters);
                    $extender->add('customer_name', General::slashesEscape(trim($customer->get('name') . ' ' . $customer->get('lastname'))));
                } else {
                    $extender->add('customer_name', '[customer_name]');
                }
            }

            //get project details
            if ($this->get('project') && (preg_match('#\[project_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes'))))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array('p.id = ' . $this->get('project')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitize' => true);
                    $project = Projects::searchOne($this->registry, $filters);
                    $extender->add('project_name', General::slashesEscape(trim($project->get('name'))));
                } else {
                    $extender->add('project_name', '[project_name]');
                }
            }

            //get office details
            if ($this->get('office') && (preg_match('#\[office_name\]#', ($this->get('name') . $this->get('description') . $this->get('notes'))))) {
                if ($this->isActivated()) {
                    $filters = array('where' => array('o.id = ' . $this->get('office')),
                                     'model_lang' => $this->get('model_lang'),
                                     'sanitize' => true);
                    $office = Offices::searchOne($this->registry, $filters);
                    $extender->add('office_name', General::slashesEscape(trim($office->get('name'))));
                } else {
                    $extender->add('office_name', '[office_name]');
                }
            }
        }

        //UPDATE THE I18N TABLE OF THE MODEL
        $update = array();
        if ($this->isDefined('name')) {
            if ($action == 'add' || $action == 'transform') {
                $name = $extender->expand($this->get('name'), false);
            } else {
                $name = $this->get('name');
            }
            $update['name'] = sprintf("name='%s'", $name);
        }
        if ($this->isDefined('description')) {
            if ($action == 'add' || $action == 'transform') {
                $description = $extender->expand($this->get('description'), false);
            } else {
                $description = $this->get('description');
            }
            $update['description']  = sprintf("description='%s'", $description);
        }
        if ($this->isDefined('notes')) {
            if ($action == 'add' || $action == 'transform') {
                $notes = $extender->expand($this->get('notes'), false);
            } else {
                $notes = $this->get('notes');
            }
            $update['notes']  = sprintf("notes='%s'", $notes);
        }

        $insert = $update;
        if (count($update)) {
            $insert['parent_id']  = sprintf("parent_id=%d", $this->get('id'));
            $insert['lang']       = sprintf("lang='%s'", $this->get('model_lang'));
            $insert['translated'] = sprintf("translated=now()");

            //query to insert/update the i18n table for the selected model language
            $query2 = 'INSERT INTO ' . DB_TABLE_CONTRACTS_I18N . "\n" .
                      'SET ' . implode(', ', $insert) . "\n" .
                      'ON DUPLICATE KEY UPDATE ' . "\n" .
                      implode(', ', $update);

            $db->Execute($query2);

            if ($db->ErrorMsg()) {
                $this->registry['messages']->setError($db->ErrorMsg());
                $this->registry['logger']->dbError('editing contract i18n details', $db, $query2);
            }

            return !$db->HasFailedTrans();
        }
    }

    /**
     * Get contracts sub elements info (annexes, original)
     *
     * @return array with records
     */
    public function getContractsInfo() {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        $db = $this->registry['db'];
        if ($this->get('subtype') == 'contract') {
            $parent_id = $this->get('id');
        } else {
            $parent_id = $this->get('parent_record');
        }

        if (empty($parent_id)) {
            return array();
        }

        $subelements = $this->get('subelements');
        if (is_array($subelements)) {
            //return subelements if they are set
            if ($sanitized) {
                $this->sanitize();
            }

            return $subelements;
        }

        //get contract and original info
        $query = 'SELECT co.*, coi18n.name, cos.name AS substatus_name, cos.icon_name ' . "\n" .
                 '  FROM ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n ' . "\n" .
                 '  ON co.id=coi18n.parent_id AND coi18n.lang="' . $this->get('model_lang') . '"' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CONTRACTS_STATUSES . ' AS cos ' . "\n" .
                 '  ON co.substatus=cos.id AND cos.lang="' . $this->get('model_lang') . '"' . "\n" .
                 '  WHERE (co.parent_record=' . $parent_id . ' OR co.id=' . $parent_id . ')' . "\n" .
                 '  AND co.subtype in ("contract", "original") ' . "\n" .
                 '  ORDER BY co.subtype';
        $records = $db->GetAll($query);
        //get annexes info
        $query = 'SELECT co.*, coi18n.name, cos.name AS substatus_name, cos.icon_name, co1.date_start_subtype as rc_date_start_subtype, co1.date_end_subtype as rc_date_end_subtype' . "\n" .
                 '  FROM ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n ' . "\n" .
                 '  ON co.id=coi18n.parent_id AND coi18n.lang="' . $this->get('model_lang') . '"' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CONTRACTS . ' AS co1 ' . "\n" .
                 '  ON co.id=co1.parent_record AND co1.subtype="restore_contract"' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CONTRACTS_STATUSES . ' AS cos ' . "\n" .
                 '  ON co.substatus=cos.id AND cos.lang="' . $this->get('model_lang') . '"' . "\n" .
                 '  WHERE (co.parent_record=' . $parent_id . ' OR co.id=' . $parent_id . ')' . "\n" .
                 '  AND co.subtype = "annex" ' . "\n" .
                 ' ORDER BY co.added DESC';
        $records2 = $db->GetAll($query);
        $records = array_merge($records, $records2);
        $this->set('subelements', $records, true);

        if ($sanitized) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Get last annex
     */
    public function getLastAnnex() {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        $db = $this->registry['db'];
        //get last annex data
        $query = 'SELECT * FROM ' . DB_TABLE_CONTRACTS . "\n" .
                    ' WHERE parent_record=' . $this->get('id') . "\n" .
                    ' AND subtype="annex" ' . "\n" .
                    ' AND status="closed" ' . "\n" .
                    ' AND subtype_status="executed" ' . "\n" .
                    ' ORDER BY date_start_subtype DESC, id DESC LIMIT 1';
        $record = $db->GetRow($query);
        $this->set('last_annex', $record, true);

        if ($sanitized) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Get last started annex/temporary agreement
     */
    public function getLastStarted() {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        $db = $this->registry['db'];
        //get last annex data
        $query = 'SELECT * FROM ' . DB_TABLE_CONTRACTS . "\n" .
                    ' WHERE parent_record=' . $this->get('id') . "\n" .
                    ' AND subtype = "annex" ' . "\n" .
                    ' AND status="closed" ' . "\n" .
                    ' AND subtype_status="started" ' . "\n" .
                    ' LIMIT 1';
        $record = $db->GetRow($query);
        $this->set('last_started', $record, true);

        if ($sanitized) {
            $this->sanitize();
        }

        return $record;
    }

    /**
     * Get last waiting annexes/temporary agreements
     */
    public function getWaitingSubtypes() {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        $db = $this->registry['db'];
        //get last annex data
        $query = 'SELECT * FROM ' . DB_TABLE_CONTRACTS . "\n" .
                    ' WHERE parent_record=' . $this->get('id') . "\n" .
                    ' AND subtype = "annex" ' . "\n" .
                    ' AND subtype_status="waiting"';
        $records = $db->GetAll($query);
        $this->set('waiting_subtypes', $records, true);

        if ($sanitized) {
            $this->sanitize();
        }

        return $records;
    }

    /**
     * Update relatives table of the model
     *
     * @param bool $delete - whether to delete old relations first
     * @return bool - result of the operation
     */
    public function updateRelatives($delete = true) {
        $db = $this->registry['db'];

        //start transaction
        $db->StartTrans();

        if ($delete) {
            // delete old relations
            $query3 = 'DELETE FROM ' . DB_TABLE_CONTRACTS_RELATIVES .
                      ' WHERE parent_id=' . $this->get('id') . ' AND origin="inherited"';
            $db->Execute($query3);
        }

        // add new relations
        $tmp = array();
        if (is_array($this->get('referers')) && count($this->get('referers')) > 0) {
            // get parents and children of current model
            $tree_p = $tree_c = array();
            $this->getParentsTree($this->get('id'), 0, $tree_p);
            $this->getChildrenTree($this->get('id'), 0, $tree_c);

            foreach ($this->get('referers') as $ref) {
                $insert = true;
                //check if referer is an ancestor of current model
                foreach ($tree_p as $rec) {
                    if ($rec['id'] == $ref && $rec['model'] == $this->modelName) {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_contracts_relative_child'));
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                //check if referer is a direct descendant (transformed, cloned) of current model
                foreach ($tree_c as $rec) {
                    if ($rec['id'] == $ref && $rec['model'] == $this->modelName && $rec['level'] == 0 && $rec['origin'] != 'inherited') {
                        $insert = false;
                        $this->registry['messages']->setWarning($this->i18n('warning_contracts_relative_child'));
                        $this->registry['messages']->insertInSession($this->registry);
                        continue;
                    }
                }
                if ($insert && $this->get('id') != $ref) {
                    $tmp[] = '(' . $this->get('id') . ', ' . $ref . ', "Contract", "inherited")';
                }
            }
            $query4 = 'INSERT INTO ' . DB_TABLE_CONTRACTS_RELATIVES .
                      ' (parent_id, link_to, link_to_model_name, origin) ' .
                      'VALUES ' . implode(', ', $tmp);
        }

        if (count($tmp)) {
            $db->Execute($query4);
        }

        //the transaction status could be checked only before CompleteTrans()
        $dbTransError = $db->HasFailedTrans();

        //complete the transaction (commit/rollback whether SQL failed or not)
        $db->CompleteTrans();

        //the result is true if there is no transaction error
        $result = !$dbTransError;

        return $result;
    }

    /**
     * Saves data related to the transformation made
     * from document to contract
     *
     * @return bool - result of the operation
     */
    public function saveTransformationDetails() {

        $params = unserialize(General::slashesStrip($this->get('transform_params')));

        $destination_params = array(
            'origin_method' => !empty($params['origin_method']) ? $params['origin_method'] : '',
            'origin_method_id' => !empty($params['origin_method_id']) ? $params['origin_method_id'] : '',
            'destination_model' => $this->modelName,
            'destination_id' => $this->get('id'),
            'destination_full_num' => $this->get('num'),
            'destination_name' => $this->get('name') ?: $this->get('type_name'),
        );

        $this->set('link_to', $params['origin_id'], true);
        $this->set('link_to_model_name', $params['origin_model'], true);

        $set['parent_id'] = sprintf('`parent_id` = "%d"', $this->get('id'));
        $set['link_to'] = sprintf('`link_to` = "%d"', $params['origin_id']);
        $set['link_to_model_name'] = sprintf('`link_to_model_name` = "%s"', $params['origin_model']);
        $set['origin'] = '`origin` = "transformed"';

        $query = 'INSERT INTO ' . DB_TABLE_CONTRACTS_RELATIVES . " SET \n" . implode(",\n", $set);

        $this->registry['db']->Execute($query);

        if ($params['origin_model'] == 'Document') {
            $set['parent_model_name'] = '`parent_model_name` = "Contract"';
            $query = 'INSERT INTO ' . DB_TABLE_DOCUMENTS_RELATIVES . " SET \n" . implode(",\n", $set);
            $this->registry['db']->Execute($query);
        }

        // save history for source model
        $factory_name = General::singular2plural($params['origin_model']);
        $module = strtolower($factory_name);
        if (strpos($module, '_') !== false) {
            list($module, $controller) = explode('_', $module, 2);
        } else {
            $controller = $module;
        }

        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.factory.php';

        $alias = $factory_name::getAlias($module, $controller);

        $filters = array('where' => array($alias . '.id = ' . $params['origin_id']));

        $model = $factory_name::searchOne($this->registry, $filters);
        if (!$model) {
            return false;
        }

        $old_model = clone $model;
        $old_model->sanitize();

        $model->set('destination_params', $destination_params, true);

        $this->loadI18NFiles(PH_MODULES_DIR . $module . '/i18n/' . $this->registry['lang'] . '/' . $module . '.ini');
        require_once PH_MODULES_DIR . $module . '/models/' . $module . ($module != $controller ? '.' . $controller : '') . '.history.php';
        $history_name = $factory_name . '_History';

        $history_name::saveData(
            $this->registry,
            array(
                'action_type' => 'create',
                'model' => $model,
                'old_model' => $old_model,
                'new_model' => $model
            ));

        // change status of source model
        if (!empty($params['origin_status']) && ($model->modelName != 'Contract' || $model->get('subtype') == 'contract' || $params['origin_status'] != 'closed')) {
            $model->set('status', $params['origin_status'], true);
            $model->set('substatus', (isset($params['origin_substatus']) ? $params['origin_substatus'] : ''), true);

            if (!$model->setStatus()) {
                return false;
            } else {
                $model = $factory_name::searchOne($this->registry, $filters);
                $model->sanitize();

                $history_name::saveData(
                    $this->registry,
                    array(
                        'action_type' => 'status',
                        'model' => $model,
                        'old_model' => $old_model,
                        'new_model' => $model
                    ));
            }
        }

        if ($this->registry['db']->ErrorMsg()) {
            $this->registry['messages']->setError($this->registry['db']->ErrorMsg());
            return false;
        }

        return true;
    }

    /**
     * Checks permitted layouts
     *
     * @param string $mode - action name
     * @param string $model - model name, not used in overwrite method
     * @return array - 'view' layouts or 'edit' layouts or array of both
     */
    public function getPermittedLayouts($mode = '', $model = '') {
        // check if the model is sanitized
        if ($this->sanitized) {
            $sanitized = true;
            $this->unsanitize();
        } else {
            $sanitized = false;
        }

        //if validLogin or automation user for crontab
        if ($this->registry['validLogin'] || ($this->registry['currentUser'] && $this->registry['currentUser']->get('id') == PH_AUTOMATION_USER)) {
            if (!$this->isDefined('layouts_view')) {
                $groups = $this->registry['currentUser']->getGroups();
                if (count($groups)) {
                    // make sure model has all types of assignments
                    if ($this->get('id')) {
                        if (!$this->isDefined('assignments_owner')) {
                            $this->getAssignments();
                        }
                        if (!$this->isDefined('assignments_responsible')) {
                            $this->getAssignments('responsible');
                        }
                        if (!$this->isDefined('assignments_decision')) {
                            $this->getAssignments('decision');
                        }
                        if (!$this->isDefined('assignments_observer')) {
                            $this->getAssignments('observer');
                        }
                    }

                    //get rights for layouts view
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="view")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND (l.model_type="' . intval($this->get('type')) . '" OR l.model_type=0) AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="view"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_view_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_view_assignments[$recs['layout_id']])) {
                            $layouts_view_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_view = array();
                    foreach ($layouts_view_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_view[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_view[] = $layout_id;
                                }
                            } else {
                                if (in_array('added', $lva)) {
                                    $layouts_view[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_view', $layouts_view);

                    //get rights for layouts edit
                    $query = 'SELECT lp.parent_id as layout_id, lap.assignment ' . "\n" .
                             'FROM ' . DB_TABLE_LAYOUTS_PERMISSIONS . ' AS lp' . "\n" .
                             'JOIN ' . DB_TABLE_LAYOUTS . ' AS l' . "\n" .
                             '  ON (l.layout_id=lp.parent_id)' . "\n" .
                             'LEFT JOIN ' . DB_TABLE_LAYOUTS_ASSIGN_PERMISSIONS . ' AS lap' . "\n" .
                             '  ON (l.layout_id=lap.parent_id AND lap.action="edit")' . "\n" .
                             'WHERE lp.group_id IN (' . implode(',', $groups) . ') AND (l.model_type="' . intval($this->get('type')) . '" OR l.model_type=0) AND l.model="' . $this->modelName . '"' . "\n" .
                             '  AND lp.action_type="edit"' . "\n";

                    $records = $this->registry['db']->GetAll($query);
                    $layouts_edit_assignments = array();
                    foreach ($records as $recs) {
                        if (! isset($layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']] = array();
                        }
                        if (!empty($recs['assignment']) && !in_array($recs['assignment'], $layouts_edit_assignments[$recs['layout_id']])) {
                            $layouts_edit_assignments[$recs['layout_id']][] = $recs['assignment'];
                        }
                    }

                    $layouts_edit = array();
                    foreach ($layouts_edit_assignments as $layout_id => $lva) {
                        if (empty($lva)) {
                            $layouts_edit[] = $layout_id;
                        } else {
                            if ($this->get('id')) {
                                if ((in_array('owner', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_owner'))) ||
                                    (in_array('responsible', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_responsible'))) ||
                                    (in_array('observer', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_observer'))) ||
                                    (in_array('decision', $lva) && array_key_exists($this->registry['currentUser']->get('id'), $this->get('assignments_decision'))) ||
                                    (in_array('added', $lva) && ($this->registry['currentUser']->get('id') == $this->get('added_by')))) {
                                    $layouts_edit[] = $layout_id;
                                }
                            } else {
                                if (in_array('added', $lva)) {
                                    $layouts_edit[] = $layout_id;
                                }
                            }
                        }
                    }

                    $this->set('layouts_edit', $layouts_edit);
                } else {
                    $this->set('layouts_view', array());
                    $this->set('layouts_edit', array());
                }
            }
        } else {
            $this->set('layouts_view', array());
            $this->set('layouts_edit', array());
        }

        if ($sanitized) {
            $this->sanitize();
        }

        if ($mode) {
            return $this->get('layouts_' . $mode);
        } else {
            return array($this->get('layouts_view'), $this->get('layouts_edit'));
        }
    }

    /**
     * Get parents from database
     *
     * @return bool - result of the operation
     */
    public function getParents() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $sanitize_after = true;
            $this->unsanitize();
        }

        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT cor.link_to as idx, co.num, cot.party, coi18n.name, cor.link_to as id ' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_RELATIVES . ' AS cor ' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                 '  ON (cor.link_to=co.id) ' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS_TYPES . ' AS cot ' . "\n" .
                 '  ON (co.type=cot.id AND cot.active=1 AND cot.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n ' . "\n" .
                 '  ON (cor.link_to=coi18n.parent_id AND coi18n.lang="' . $lang . '") ' . "\n" .
                 'WHERE cor.parent_id=' . $this->get('id') .
                 '  AND cor.origin="inherited" AND cor.link_to_model_name="Contract"' . "\n" .
                 'ORDER BY cot.party';
        $records = $this->registry['db']->GetAssoc($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $this->set('referers', $records, true);
    }

    /**
     * Get contracts names
     *
     * @return bool - result of the operation
     */
    public function getParentNames($filter) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $query = 'SELECT co.id as idx, co.num, cot.party, coi18n.name, coi18n.parent_id as id ' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . ' AS co ' . "\n" .
                 'JOIN ' . DB_TABLE_CONTRACTS_TYPES . ' AS cot ' . "\n" .
                 '  ON (co.type=cot.id AND cot.active=1 AND cot.deleted=0) ' . "\n" .
                 'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n ' . "\n" .
                 '  ON (co.id=coi18n.parent_id AND coi18n.lang="' . $lang . '")' . "\n" .
                 'WHERE co.id in (' . implode(',', $filter) . ')';
        $records = $this->registry['db']->GetAssoc($query);

        return $this->set('referers', $records, true);
    }

    /**
     * Get contracts files
     *
     * @return bool - result of the operation
     */
    public function getFiles() {
        $files = array();

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        //select clause
        $sql['select'] = 'SELECT f.*, fi18n.*, ' . "\n" .
                         '  "' . $this->get('model_lang') . '" AS model_lang';

        //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_FILES_I18N . ' AS fi18n' . "\n" .
                       '  ON (f.id=fi18n.parent_id AND fi18n.lang="' . $this->get('model_lang') . '")';

        //common where clause
        $sql['where'] = '';

        $sql['order'] = 'ORDER BY id' . "\n";

        //where clause to get ids of latest versions of files
        $where = array(
            'model'    => sprintf('model="%s"', $this->modelName),
            'model_id' => sprintf('model_id="%d"', $this->get('id')),
            'deleted'  => 'deleted=0');
        if ($this->registry->get('searched_files_ids')) {
            $where['files_ids'] = 'id IN (' . $this->registry->get('searched_files_ids') . ')';
        }
        $ids_where = 'WHERE ' . (($where) ? implode(' AND ', $where) : '1') . "\n";

        //check access permissions of files
        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $ids_where .= Files::getAdditionalWhere($this->registry);

        //get the generated files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="generated"' . "\n" .
                  'GROUP BY pattern_id';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $generated = $this->registry['db']->GetAll($query);

            $files['generated'] = $generated;
        } else {
            $files['generated'] = array();
        }

        //get the attached files

        // gets max ids to use for selection of latest versions of files
        $query1 = 'SELECT MAX(id) FROM ' . DB_TABLE_FILES . ' AS f' . "\n" .
                  $ids_where . "\n" .
                  '  AND origin="attached"' . "\n" .
                  'GROUP BY filename';
        $res1 = $this->registry['db']->GetCol($query1);

        if ($res1) {
            $sql['where'] = 'WHERE id IN (' . implode(', ', $res1) . ')';

            $query = implode("\n", $sql);
            $attachments = $this->registry['db']->GetAll($query);

            $files['attachments'] = $attachments;
        } else {
            $files['attachments'] = array();
        }

        if (empty($attachments) && empty($generated)) {
            $files = array();
        }

        $this->set('files', $files, true);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $files;
    }

    /**
     * Get contract attachments
     *
     * @return array - attached files and their revisions
     */
    public function getAttachments() {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Contract\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'attached\'',
                                          'f.deleted =  0'),
                         'model_lang' => $this->get('model_lang'),
                         'sanitize' => 1);
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
        }

        $this->set('attachments', $files, true);

        return $files;
    }

    /**
     * Get generated files details
     *
     * @param array $params - filtering params
     * @return array - generated files and their revisions
     */
    public function getGeneratedFiles($params = array()) {
        $db = $this->registry['db'];
        $lang = ($this->get('model_lang')) ? $this->get('model_lang') : $this->registry['lang'];

        $prepareModels = $this->registry->get('prepareModels');
        $this->registry->set('prepareModels', true, true);

        require_once PH_MODULES_DIR . 'files/models/files.factory.php';
        $filters = array('where' => array('f.model = \'Contract\'',
                                          'f.model_id = ' . $this->get('id'),
                                          'f.origin = \'generated\'',
                                          'f.deleted =  0'),
                         'sanitize' => 1);

        if (isset($params['pattern_id'])) {
            $filters['where'][] = 'f.pattern_id = ' . $params['pattern_id'];
        }
        $files = Files::search($this->registry, $filters);

        $this->registry->set('prepareModels', $prepareModels, true);

        //hide deleted files
        foreach ($files as $k => $file) {
            if ($file->get('deleted_by')) {
                unset($files[$k]);
            }
            $icon_name = $file->getIconName($file->get('filename'));
            $file->set('icon_name', $icon_name);
            $files[$k] = $file;
        }
        $this->set('genfiles', $files, true);

        return $files;
    }

    /**
     * Get number for contract
     *
     * @param bool $force - false to get number only if not set, true to get it anyway
     * @return string - contract number
     */
    public function getNum($force = false) {
        if ($this->get('num') != 'system') {
            if (!$this->get('num') || $force) {

                //get the counter assigned to the contract type
                $this->getCounter();

                if ($this->counter) {
                    //define some the counter's formula components
                    $formula = $this->counter->get('formula');
                    $prefix = $this->counter->get('prefix');
                    $delimiter = $this->counter->get('delimiter');
                    $zeroes = $this->counter->get('leading_zeroes');
                    $date_format = $this->counter->get('date_format');

                    //create extender to expand the formula components
                    $extender = new Extender;

                    //lock the counter for update to guarantee unique next number
                    $query = 'SELECT next_number FROM ' . DB_TABLE_CONTRACTS_COUNTERS . ' WHERE id="' . $this->counter->get('id') . '" FOR UPDATE';
                    $this->counter->set('next_number', $this->registry['db']->GetOne($query), true);

                    //set contract number
                    $num = sprintf('%0' . $zeroes . 'd', $this->counter->get('next_number'));
                    $extender->add('num', $num);

                    // if contract without package deal has subnum component,
                    // it should be replaced with num
                    if ($this->counter->get('subnum')) {
                        //add this component to the extender
                        $extender->add('subnum', $num);
                    }

                    if ($this->counter->get('prefix')) {
                        //add this component to the extender
                        $extender->add('prefix', $prefix);
                    }

                    if ($this->counter->get('company_code') && $this->get('company')) {
                        //get company code
                        $query = 'SELECT code FROM ' . DB_TABLE_FINANCE_COMPANIES . ' WHERE id=' . $this->get('company');
                        $company_code = $this->registry['db']->GetOne($query);

                        //add this component to the extender
                        $extender->add('company_code', $company_code);
                    }

                    if ($this->counter->get('office_code') && $this->get('office')) {
                        //get office code
                        $query = 'SELECT code FROM ' . DB_TABLE_OFFICES . ' WHERE id=' . $this->get('office');
                        $office_code = $this->registry['db']->GetOne($query);

                        //add this component to the extender
                        $extender->add('office_code', $office_code);
                    }

                    if ($this->counter->get('user_code')) {
                        //get user code
                        //add this component to the extender
                        $extender->add('user_code', $this->registry['currentUser']->get('code'));
                    }

                    if ($this->counter->get('customer_code') && $this->get('customer')) {
                        //get customer code
                        $query = 'SELECT code FROM ' . DB_TABLE_CUSTOMERS . ' WHERE id=' . $this->get('customer');
                        $customer_code = $this->registry['db']->GetOne($query);

                        //add this component to the extender
                        $extender->add('customer_code', $customer_code);
                    }

                    if ($this->counter->get('customer_num') && $this->get('customer')) {
                        //get number of contracts for contract customer
                        $query = 'SELECT COUNT(id)+1 FROM ' . DB_TABLE_CONTRACTS . "\n" .
                                 'WHERE type="' . $this->get('type') . '" AND customer="' . $this->get('customer') . '"' . "\n" .
                                 '  AND company="' . $this->get('company') . '"' .
                                 ($this->counter->get('office') ? ' AND office="' . $this->get('office') . '"' : '') .
                                 ($this->counter->get('customer_year') ? ' AND YEAR(status_modified)=YEAR(CURDATE())' : '') . "\n" .
                                 '  AND subtype="contract" AND status="closed"';
                        $customer_num = sprintf('%0' . $zeroes . 'd', $this->registry['db']->GetOne($query));
                        $extender->add('customer_num', $customer_num);
                    }

                    if ($this->counter->get('document_date')) {
                        //replace the date
                        $date = ($this->get('added')) ? General::strftime($date_format, strtotime($this->get('added'))) : General::strftime($date_format);

                        //add this component to the extender
                        $extender->add('document_date', $date);
                    }

                    $num = $extender->expand($formula);
                    if ($delimiter) {
                        //remove repeating delimiters
                        $num = preg_replace('#'. preg_quote($delimiter . $delimiter) .'#', $delimiter, $num);
                        $num = preg_replace('#'. preg_quote($delimiter) .'$#', '', $num);
                        $num = preg_replace('#^'. preg_quote($delimiter) .'#', '', $num);
                    }

                    $this->set('num', $num, true);
                }
            }
        }

        return $this->get('num');
    }

    /**
     * Get number for annex
     *
     * @param bool $force - false to get number only if not set, true to get it anyway
     * @return string - contract number
     */
    public function getSubNum($force = false) {
        if ($this->get('num') != 'system') {
            if (!$this->get('num') || $force) {
                // get counter of the parent contract
                $this->getCounter();

                $db = $this->registry['db'];

                //define some the counter's formula components
                if ($this->get('subtype') == 'annex') {
                    $formula = $this->registry['config']->getParam('contracts', 'formula_annex');
                }

                //create extender to expand the formula components
                $extender = new Extender;

                if ($this->counter) {
                    $extender->add('prefix_annex', $this->counter->get('prefix_annex'));
                }

                if ($this->get('parent_record')) {
                    $query = 'SELECT num FROM ' . DB_TABLE_CONTRACTS . "\n" .
                             ' WHERE id=' . $this->get('parent_record');
                    $parent_num = $db->GetOne($query);
                    $extender->add('parent_num', $parent_num);
                }

                $zeroes = 3;
                //set number
                $query = 'SELECT count(*) FROM ' . DB_TABLE_CONTRACTS . "\n" .
                         ' WHERE parent_record=' . $this->get('parent_record') . "\n" .
                         ' AND subtype="' . $this->get('subtype') . '"' . "\n" .
                         ' AND status="closed" AND num !="system"';
                $num = $db->GetOne($query);
                $num = sprintf('%0' . $zeroes . 'd', $num + 1);
                $extender->add('num', $num);

                $num = $extender->expand($formula);

                $this->set('num', $num, true);

            }
        }

        return $this->get('num');
    }

    /**
     * Get all patterns variables - basic/system, additional
     *
     * @return array $vars - variables
     */
    public function getPatternsVars() {
        require_once PH_MODULES_DIR . 'placeholders/models/placeholders.factory.php';
        $filters = array(
            'model_lang' => $this->get('model_lang'),
            'where' => array(
                'p.usage = \'patterns\'',
                'p.model IN ("Contract", "Customer", "CurrentUser") OR p.type = "system"'
            )
        );
        $basic_placeholders = Placeholders::search($this->registry, $filters);

        //prepare customer variables
        //set flag to get contact person name
        $this->registry->set('getContactPersonInfo', true, true);
        require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('c.id = ' . $this->get('customer')));
        $customer = Customers::searchOne($this->registry, $filters);

        $customer_translations = $customer->getTranslations();
        foreach ($customer_translations as $t_lang) {
            if ($t_lang != $customer->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array('c.id = ' . $customer->get('id')));
                $t_customer[$t_lang] = Customers::searchOne($this->registry, $filters);
            }
        }

        //get the pattern
        $pattern_id = $this->registry['request']->get('pattern');
        $filters = array('where' => array('p.id = ' . $pattern_id),
                         'sanitize' => true);
        $pattern = Patterns::searchOne($this->registry, $filters);
        $pattern_format = '';
        if ($pattern) {
            $pattern_format = $pattern->get('format');
            $pattern_format = ($pattern_format == 'docx2pdf') ? 'docx' : $pattern_format;
        }

        //prepare current user variables
        require_once PH_MODULES_DIR . 'users/models/users.factory.php';
        $filters = array('model_lang' => $this->get('model_lang'),
                         'where' => array('u.id = ' . $this->registry['currentUser']->get('id'), 'u.hidden IS NOT NULL'));
        $user = Users::searchOne($this->registry, $filters);

        $user_translations = $user->getTranslations();
        foreach ($user_translations as $t_lang) {
            if ($t_lang != $user->get('model_lang')) {
                $filters = array('model_lang' => $t_lang,
                                 'where' => array(
                                    'u.id = ' . $user->get('id'),
                                    'u.hidden IS NOT NULL'
                                 ),
                                 'sanitize' => true);
                $t_user[$t_lang] = Users::searchOne($this->registry, $filters);
            }
        }

        //prepare contact person
        if ($this->get('contact_person')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
            $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                       'c.subtype = \'contact\''),
                                      'sanitize' => true,
                                      'model_lang' => $this->get('model_lang'));
            $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
            if ($contactperson) {
                $this->set('contact_person_name',
                    $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                $this->set('contact_person_lastname', $contactperson->get('lastname'), true);
                $this->set('contact_person_salutation', $contactperson->get('salutation'), true);
            }
        }

        //prepare customer branch
        if ($this->get('branch')) {
            require_once PH_MODULES_DIR . 'customers/models/customers.branches.factory.php';
            $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                     'c.subtype = \'branch\''),
                                    'sanitize' => true,
                                    'model_lang' => $this->get('model_lang'));
            $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
            if ($branch) {
                $this->set('branch_name', $branch->get('name'), true);
            }
        }

        // get number of "current" contract
        $parent_num = '';
        if ($this->get('subtype') == 'annex') {
            $query = 'SELECT num FROM ' . DB_TABLE_CONTRACTS . ' WHERE id=' . $this->get('parent_record');
            $parent_num = $this->registry['db']->GetOne($query);
        } else {
            $parent_num = $this->get('num');
        }
        $this->set('parent_num', $parent_num, true);

        $translations = $this->getTranslations();

        //save the previous registry lang
        $registry_lang_old = $this->registry['lang'];

        foreach ($translations as $t_lang) {
            $this->registry->set('lang', $t_lang, true);
            $this->registry['translater']->reloadFiles($t_lang);

            $filters = array('model_lang' => $t_lang,
                             'where' => array('co.id = ' . $this->get('id')));
            $t_contract[$t_lang] = Contracts::searchOne($this->registry, $filters);
            $t_contract[$t_lang]->set('parent_num', $parent_num, true);
            $t_contract[$t_lang]->getVarsForTemplate();
            $t_a_vars[$t_lang] = $t_contract[$t_lang]->get('vars');
            if ($this->get('contact_person')) {
                $filters_contacts = array('where' => array('c.id = ' . $this->get('contact_person'),
                                                           'c.subtype = \'contact\''),
                                          'sanitize' => true,
                                          'model_lang' => $t_lang);
                $contactperson = Customers_Contactpersons::searchOne($this->registry, $filters_contacts);
                if ($contactperson) {
                    $t_contract[$t_lang]->set('contact_person_name',
                        $contactperson->get('name') . ' ' . $contactperson->get('lastname'), true);
                }
            }
            if ($this->get('branch')) {
                $filters_branch = array('where' => array('c.id = ' . $this->get('branch'),
                                                         'c.subtype = \'branch\''),
                                        'sanitize' => true,
                                        'model_lang' => $t_lang);
                $branch = Customers_Branches::searchOne($this->registry, $filters_branch);
                if ($branch) {
                    $t_contract[$t_lang]->set('branch_name', $branch->get('name'), true);
                }
            }
        }

        $this->registry->set('lang', $registry_lang_old, true);
        $this->registry['translater']->reloadFiles($registry_lang_old);

        //prepare basic/system variables
        $vars = array();

        foreach ($basic_placeholders as $placeholder) {
            $pl_source = $placeholder->get('source');
            $pl_varname = $placeholder->get('varname');
            if ($placeholder->get('type') == 'basic') {
                if ($placeholder->get('model') == 'Contract') {
                //contract variables
                    if (!$placeholder->get('multilang')) {
                        if ($pl_source == 'contact_person_salutation') {
                            $salutation = '';
                            $vars[$pl_varname . '_formal'] = '';

                            if ($this->get('contact_person_salutation')) {
                                $salutation = $this->get('contact_person_salutation');
                                $vars[$pl_varname . '_formal'] =
                                    $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $this->get('contact_person_lastname') ?
                                $this->get('contact_person_lastname') : $this->get('contact_person_name');

                            $vars[$pl_varname] = $salutation;
                        } elseif (preg_match('#^company_#', $pl_source)) {
                            $vars[$pl_varname] = $this->getCompanyData(str_replace('company_', '', $pl_source));
                        } else {
                            $vars[$pl_varname] = $this->get($pl_source);
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if (preg_match('#^company_#', $pl_source)) {
                                $vars[$t_contract[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_contract[$t_lang]->getCompanyData(str_replace('company_', '', $pl_source));
                            } else {
                                $vars[$t_contract[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_contract[$t_lang]->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'Customer') {
                //customer variables
                    if (!$placeholder->get('multilang')) {
                        if ($pl_source == 'salutation') {
                            $salutation = '';
                            $vars[$pl_varname . '_formal'] = '';

                            if ($customer->get('salutation')) {
                                $salutation = $customer->get('salutation');
                                $vars[$pl_varname . '_formal'] =
                                    $salutation ? ($salutation == 'mr' ? $this->i18n('dear_m') : $this->i18n('dear_f')) : '';
                                $salutation = $this->i18n('salutation_vocative_' . $salutation) . ' ';
                            }
                            $salutation .= $customer->get('lastname') ? $customer->get('lastname') : $customer->get('name');

                            $vars[$pl_varname] = $salutation;
                        } else {
                            $vars[$pl_varname] = $customer->get($pl_source);
                        }
                    } else {
                        foreach ($customer_translations as $t_lang) {
                            if ($t_lang != $customer->get('model_lang')) {
                                $vars[$t_customer[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_customer[$t_lang]->get($pl_source);
                            } else {
                                $vars[$customer->get('model_lang') . '_' . $pl_varname] =
                                $customer->get($pl_source);
                            }
                        }
                    }
                } elseif ($placeholder->get('model') == 'CurrentUser') {
                //user variables
                    if (!$placeholder->get('multilang')) {
                        $vars[$pl_varname] = $user->get($pl_source);
                    } else {
                        foreach ($user_translations as $t_lang) {
                            if ($t_lang != $user->get('model_lang')) {
                                $vars[$t_user[$t_lang]->get('model_lang') . '_' . $pl_varname]
                                = $t_user[$t_lang]->get($pl_source);
                            } else {
                                $vars[$user->get('model_lang') . '_' . $pl_varname] =
                                $user->get($pl_source);
                            }
                        }
                    }
                }
            } elseif ($placeholder->get('type') == 'system') {
            //system variables
                if (strpos($pl_source, '::')) {
                    list($method, $value) = preg_split('/\s*\::\s*/', $pl_source);
                    if (!empty($value)) {
                        $var = $this->i18n($value);
                        if (empty($var)) {
                            $var = $value;
                        }
                        $res = General::$method($this->registry, $var);
                    } else {
                        $res = General::$method($this->registry);
                    }
                    $vars[$pl_varname] = $res;
                } else {
                    $vars[$pl_varname] = $pl_source;
                }
            }
        }

        //prepare additional variables
        $this->getVarsForTemplate(false);

        /*
        //prepare BB variables
        $additional_vars = $this->get('vars');

        $bb_vars = $this->getBB(array('model_id' => $this->get('id')));

        $bb_elements = array();
        foreach ($additional_vars as $key => $var) {
            if (isset($var['bb']) && ($var['bb']) > 0 &&
                ($var['type'] == 'grouping' || $var['type'] == 'config' || $var['type'] == 'gt2')) {
                if ($var['type']!='gt2') {
                    $var['width'] = $var['width_print'];
                }
                $bb_elements[$var['id']] = $var;
                unset($additional_vars[$key]);
            }
        }

        // set additional vars back to model without the bb elements vars
        $this->set('vars', $additional_vars, true);

        $add_bb_vars = $this->getBBFields();
        foreach ($add_bb_vars as $bb_var_name => $bb_var_defs) {
            $add_bb_vars[$bb_var_name]['width'] = $bb_var_defs['width_print'];

            if ($bb_var_defs['type'] == 'file_upload') {
                if (!empty($bb_var_defs['value'])) {
                    foreach ($bb_var_defs['value'] as $bb_id => $file) {
                        // display thumbnail or file name
                        if (!empty($file) && is_object($file) && !$file->get('not_exist') && !$file->get('deleted_by')) {
                            $file = $this->getFileUploadForPrint($file, $bb_var_defs);
                        } else {
                            $file = '';
                        }
                        $add_bb_vars[$bb_var_name]['value'][$bb_id] = $file;
                    }
                }
            }
        }
        $this->set('add_bb_vars', $add_bb_vars, true);

        foreach ($bb_vars as $index => $var) {
            if (isset($bb_elements[$var['meta_id']])) {
                $bb_vars[$index] = $bb_elements[$var['meta_id']];
                $bb_vars[$index]['id'] = $var['id'];
                $bb_vars[$index]['meta_id'] = $var['meta_id'];
                $this->prepareBbVarValues($bb_vars[$index], $var['params'], true);

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (isset($bb_elements[$var['meta_id']]['names'])) {
                    foreach ($bb_elements[$var['meta_id']]['names'] as $bb_sub_elements) {
                        if (isset($bb_elements[$var['meta_id']][$bb_sub_elements]) && !empty($bb_elements[$var['meta_id']][$bb_sub_elements]['source'])) {
                            // parse the params
                            $source_fields = General::parseSettings($bb_elements[$var['meta_id']][$bb_sub_elements]['source']);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if (!empty($bb_vars[$index]['values'][$replaced_var])) {
                                    $bb_vars[$index]['values'][$bb_sub_elements] =
                                        str_replace('[a_' . $replaced_var . ']',
                                                    $bb_vars[$index]['values'][$bb_sub_elements],
                                                    $source_fields['replace_value']);

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if ($bb_vars[$index][$bb_sub_elements]['type'] == 'dropdown' || $bb_vars[$index][$bb_sub_elements]['type'] == 'radio' || $bb_vars[$index][$bb_sub_elements]['type'] == 'checkbox') {
                                        $bb_vars[$index][$bb_sub_elements]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        $this->set('bb_vars', $bb_vars, true);
        //end of preparation of BB variables
        */


        $additional_vars = $this->get('vars');

        foreach ($additional_vars as $k => $a_var) {
            if (isset($a_var['type']) && !in_array($a_var['type'], array('bb', 'grouping', 'config', 'table', 'gt2'))) {
                if (!$a_var['multilang']) {
                    if (isset($a_var['value']) && $a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['options'] as $opt) {
                            if ($opt['option_value'] == $a_var['value']) {
                                $vars['a_' . $a_var['name']] =
                                    ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                break;
                            }
                        }
                    } elseif (isset($a_var['value']) && $a_var['value'] !== '' && is_array($a_var['value']) && isset($a_var['options'])) {
                        foreach ($a_var['value'] as $val) {
                            foreach ($a_var['options'] as $opt) {
                                if ($opt['option_value'] == $val) {
                                    $vars['a_' . $a_var['name']][] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        }
                    } elseif ($a_var['type'] == 'textarea') {
                        $vars['a_' . $a_var['name']] = nl2br($a_var['value']);
                    } elseif ($a_var['type'] == 'file_upload') {
                        if (!empty($a_var['value']) && is_object($a_var['value']) && !$a_var['value']->get('not_exist') && !$a_var['value']->get('deleted_by')) {
                            if (isset($a_var['view_mode']) && $a_var['view_mode'] == 'thumbnail' && $a_var['value']->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['value']->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($a_var['thumb_width']) ? ("&maxwidth=" . $a_var['thumb_width']) : ''),
                                    (!empty($a_var['thumb_height']) ? ("&maxheight=" . $a_var['thumb_height']) : '')
                                );
                            } else {
                                $value = $a_var['value']->get('name');
                            }
                        } else {
                            $value = '';
                        }

                        $vars['a_' . $a_var['name']] = $value;
                    } else {
                        $vars['a_' . $a_var['name']] = isset($a_var['value']) ? $a_var['value'] : '';
                    }
                } else {
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                $t_a_vars[$t_lang][$k]['value'] = '';
                            }
                            if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                            && isset($t_a_vars[$t_lang][$k]['options'])) {
                                foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = nl2br($t_a_vars[$t_lang][$k]['value']);
                            } else {
                                $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                                = $t_a_vars[$t_lang][$k]['value'];
                            }
                        } else {
                            if ($a_var['value'] !== '' && !is_array($a_var['value']) && isset($a_var['options'])) {
                                foreach ($a_var['options'] as $opt) {
                                    if ($opt['option_value'] == $a_var['value']) {
                                        $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            } elseif ($a_var['type'] == 'textarea') {
                                //special behaviour for the textarea, they need new lines with breaks (<br />)
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                nl2br($a_var['value']);
                            } else {
                                $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                $a_var['value'];
                            }
                        }
                    }
                }

                // checks if source contains 'replace_value' params and
                // changes the name content with the required replacements
                if (!empty($a_var['source'])) {
                    // parse the params
                    $source_fields = General::parseSettings($a_var['source']);

                    // check if there is a var with the required name and replace it
                    if (!empty($source_fields['replace_value'])) {
                        if (!$a_var['multilang']) {
                            $vars['a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars['a_' . $a_var['name']], $source_fields['replace_value']);
                        } else {
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] =
                                str_replace('[a_' . $a_var['name'] . ']', $vars[$this->get('model_lang') . '_a_' . $a_var['name']], $source_fields['replace_value']);
                        }
                    }
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'config' && isset($a_var['names']) && is_array($a_var['names'])) {
                //add containing variables to the list of replaceable variables
                $a_var['width'] = $a_var['width_print'];

                foreach ($a_var['names'] as $var_name) {
                    $ac_var = $a_var[$var_name];
                    $ac_var['value'] = isset($a_var['values'][$var_name]) ? $a_var['values'][$var_name] : '';

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a configurator (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } elseif ($ac_var['type'] == 'file_upload') {
                            if (!empty($ac_var['value']) && is_object($ac_var['value']) && !$ac_var['value']->get('not_exist') && !$ac_var['value']->get('deleted_by')) {
                                if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $ac_var['value']->isImage()) {
                                    $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                        $_SERVER['SCRIPT_NAME'],
                                        $this->registry['module_param'],
                                        rawurlencode(General::encrypt($ac_var['value']->get('id'), '_viewfile_', 'xtea')),
                                        (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                        (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                    );
                                } else {
                                    $value = $ac_var['value']->get('name');
                                }
                            } else {
                                $value = '';
                            }
                            $a_var['values'][$var_name] = $vars['a_'.$ac_var['name']] = $value;
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }
                }

                $configViewer = new Viewer($this->registry);
                if (isset($a_var['frankenstein'])) {
                    $a_var['frankenstein']['columns'] = @$a_var['columns'];
                    $configViewer->data['var'] = $a_var['frankenstein'];
                    $configViewer->data['pattern_id'] = $pattern_id;

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_vars.html');
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();

                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'franky_configs.html');
                    $vars['a_'.@$a_var['name'].'_configs'] = $configViewer->fetch();
                } else {
                    $configViewer->setFrameset($this->registry['theme']->templatesDir . 'config_vars.html');
                    $configViewer->data['var'] = $a_var;
                    $configViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_'.@$a_var['name']] = $configViewer->fetch();
                }
                //ToDo - add multilang config variables

            } elseif (isset($a_var['type']) && $a_var['type'] == 'table' && isset($a_var['names']) && is_array($a_var['names'])) {
                // Skip this table if it's empty
                if (empty($a_var['values']) || count(array_filter($a_var['values'], function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                //add containing variables to the list of replaceable variables
                foreach ($a_var['names'] as $key => $var_name) {
                    $ac_var = $a_var[$var_name];
                    if ($ac_var['type'] == 'file_upload') {
                        if (!empty($a_var['values'][$key]) && is_object($a_var['values'][$key]) && !$a_var['values'][$key]->get('not_exist') && !$a_var['values'][$key]->get('deleted_by')) {
                            if (isset($ac_var['view_mode']) && $ac_var['view_mode'] == 'thumbnail' && $a_var['values'][$key]->isImage()) {
                                $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                    $_SERVER['SCRIPT_NAME'],
                                    $this->registry['module_param'],
                                    rawurlencode(General::encrypt($a_var['values'][$key]->get('id'), '_viewfile_', 'xtea')),
                                    (!empty($ac_var['thumb_width']) ? ("&maxwidth=" . $ac_var['thumb_width']) : ''),
                                    (!empty($ac_var['thumb_height']) ? ("&maxheight=" . $ac_var['thumb_height']) : '')
                                );
                                $a_var['values'][$key] = $value;
                            } else {
                                $a_var['values'][$key] = $a_var['values'][$key]->get('name');
                                $value = $a_var['values'][$key];
                            }
                        } else {
                            $value = '';
                        }
                    } else {
                        $value = isset($a_var['values'][$key]) ? $a_var['values'][$key] : '';
                    }
                    $ac_var['value'] = $value;
                    $a_var['values'][$key] = $value;

                    $a_var['width'] = $a_var['width_print'];

                    if (!$ac_var['multilang']) {
                        if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['options'] as $opt) {
                                if ($opt['option_value'] == $ac_var['value']) {
                                    $vars['a_'.$ac_var['name']] =
                                        ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                    break;
                                }
                            }
                        } elseif ($ac_var['value'] !== '' && is_array($ac_var['value']) && isset($ac_var['options'])) {
                            foreach ($ac_var['value'] as $val) {
                                foreach ($ac_var['options'] as $opt) {
                                    if ($opt['option_value'] == $val) {
                                        $vars['a_'.$ac_var['name']][] =
                                            ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                        break;
                                    }
                                }
                            }
                            //special behaviour for checkboxes in a table (used as independent placeholders)
                            if ($ac_var['type'] == 'checkbox_group' && !empty($vars['a_'.$ac_var['name']]) && is_array($vars['a_'.$ac_var['name']])) {
                                if (!empty($ac_var['options_align']) && $ac_var['options_align'] == 'horizontal') {
                                    //separator is a space when options are aligned horizontally (options_align := horizontal)
                                    $vars['a_'.$ac_var['name']] = implode('&nbsp;', $vars['a_'.$ac_var['name']]);
                                } else {
                                    //separator is a break when options are aligned vertically (default)
                                    $vars['a_'.$ac_var['name']] = implode('<br />', $vars['a_'.$ac_var['name']]);
                                }
                            }
                        } elseif ($ac_var['type'] == 'textarea') {
                            //special behaviour for the textarea, they need new lines with breaks (<br />)
                            $vars['a_'.$ac_var['name']] = nl2br($ac_var['value']);
                        } else {
                            $vars['a_'.$ac_var['name']] = $ac_var['value'];
                        }
                    } else {
                        foreach ($translations as $t_lang) {
                            if ($t_lang != $this->get('model_lang')) {
                                if (!isset($t_a_vars[$t_lang][$k]['value'])) {
                                    $t_a_vars[$t_lang][$k]['value'] = '';
                                }
                                if ($t_a_vars[$t_lang][$k]['value'] !== '' && !is_array($t_a_vars[$t_lang][$k]['value'])
                                && isset($t_a_vars[$t_lang][$k]['options'])) {
                                    foreach ($t_a_vars[$t_lang][$k]['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = nl2br($t_a_vars[$t_lang][$k]['value']);
                                } else {
                                    $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $ac_var['name']]
                                    = $t_a_vars[$t_lang][$k]['value'];
                                }
                            } else {
                                if ($ac_var['value'] !== '' && !is_array($ac_var['value']) && isset($ac_var['options'])) {
                                    foreach ($ac_var['options'] as $opt) {
                                        if ($opt['option_value'] == $ac_var['value']) {
                                            $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                                ((!empty($opt['extended_value']))?$opt['extended_value']:$opt['label']);
                                            break;
                                        }
                                    }
                                } elseif ($ac_var['type'] == 'textarea') {
                                    //special behaviour for the textarea, they need new lines with breaks (<br />)
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    nl2br($ac_var['value']);
                                } else {
                                    $vars[$this->get('model_lang') . '_a_' . $ac_var['name']] =
                                    $ac_var['value'];
                                }
                            }
                        }
                    }

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    if (!empty($a_var['source'][$var_name])) {
                        // parse the params
                        $source_fields = General::parseSettings($a_var['source'][$var_name]);

                        // checks if 'replace_value' param is set
                        if (!empty($source_fields['replace_value'])) {
                            // find the replacement variable name
                            $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                            // check if there is a var with the required name and replace it
                            if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                $additional_var_name = '[a_' . $replaced_var . ']';
                                $column_key_idx = array_search($replaced_var, $a_var['names']);
                                foreach ($a_var['values'] as $col_index => $col_value) {
                                    if ($col_index == $column_key_idx) {
                                        $new_value = str_replace($additional_var_name, $col_value, $source_fields['replace_value']);
                                        $a_var['values'][$col_index] = $new_value;
                                        $a_var[$replaced_var]['value'] = $new_value;
                                        if (!$ac_var['multilang']) {
                                            $vars['a_' . $replaced_var] = $new_value;
                                        } else {
                                            $vars[$this->get('model_lang') . '_a_' . $replaced_var] = $new_value;
                                        }

                                        // sets the option to overwrite a value and use it
                                        // but not searching for its corresponding label
                                        if ($ac_var['type'] == 'dropdown' || $ac_var['type'] == 'radio' || $ac_var['type'] == 'checkbox') {
                                            $a_var[$var_name]['overwrite_value'] = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                $template_file = 'table_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if (!empty($a_var['multilang'])) {
                    foreach ($translations as $t_lang) {
                        $tableViewer = new Viewer($this->registry);
                        $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $tableViewer->data['pattern_id'] = $pattern_id;
                        $replacement = $tableViewer->fetch();
                        if ($pattern_format == 'docx') {
                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                        }
                        if ($t_lang != $this->get('model_lang')) {
                            $tableViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        } else {
                            $tableViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                } else {
                    $tableViewer = new Viewer($this->registry);
                    $tableViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                    $tableViewer->data['var'] = $a_var;
                    $tableViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $tableViewer->fetch();
                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_'.$a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'grouping') {
                // Skip this grouping table if it has no rows or it has only one row which is empty
                if (empty($a_var['values']) || count($a_var['values']) == 1 && count(array_filter(reset($a_var['values']), function($a) {return !empty($a) && (is_object($a) || is_array($a) || !preg_match('#^0\.0+$#', $a));})) == 0) {
                    continue;
                }

                $template_file = 'grouping_vars' . ($pattern_format == 'docx' ? '_docx':'') . '.html';
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $groupingViewer = new Viewer($this->registry);
                        $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);
                        $groupingViewer->data['pattern_id'] = $pattern_id;
                        if ($t_lang != $this->get('model_lang')) {
                            $groupingViewer->data['var'] = $t_a_vars[$t_lang][$k];
                            $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']]
                            = $groupingViewer->fetch();
                        } else {
                            $groupingViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        }
                    }
                } else {
                    $groupingViewer = new Viewer($this->registry);
                    $groupingViewer->setFrameset($this->registry['theme']->templatesDir . $template_file);

                    // checks if source contains 'replace_value' params and
                    // changes the name content with the required replacements
                    foreach ($a_var['names'] as $idx_var => $var_name) {
                        $a_var['width'] = $a_var['width_print'];

                        if (!empty($a_var['source'][$var_name])) {
                            // parse the params
                            $source_fields = General::parseSettings($a_var['source'][$var_name]);

                            // checks if 'replace_value' param is set
                            if (!empty($source_fields['replace_value'])) {
                                // find the replacement variable name
                                $replaced_var = preg_replace('#^.*\[a_(.*)\].*$#', '$1', $source_fields['replace_value']);

                                // check if there is a var with the required name and replace it
                                if ($replaced_var && in_array($replaced_var, $a_var['names'])) {
                                    $column_key_idx = array_search($replaced_var, $a_var['names']);
                                    foreach ($a_var['values'] as $row => $row_content) {
                                        $a_var['values'][$row][$column_key_idx] =
                                            str_replace('[a_' . $replaced_var . ']', $row_content[$column_key_idx], $source_fields['replace_value']);
                                    }

                                    // sets the option to overwrite a value and use it
                                    // but not searching for its corresponding label
                                    if (in_array($a_var['types'][$column_key_idx], array('dropdown', 'radio', 'checkbox'))) {
                                        $a_var[$var_name]['overwrite_value'] = true;
                                    }
                                }
                            }
                        }
                    }

                    //check if there are empty rows in the table and remove them
                    if (isset($a_var['values']) && is_array($a_var['values'])) {
                        $row_is_empty = true;
                        foreach ($a_var['values'] as $row_index => $row_content) {
                            foreach ($row_content as $cell_index => $cell_content) {
                                if ($cell_content || $cell_content === '0') $row_is_empty = false;
                            }
                            if ($row_is_empty) {
                                unset($a_var['values'][$row_index]);
                            } else {
                                $row_is_empty = true;
                            }
                        }
                    }

                    foreach ($a_var['types'] as $key_column => $var_type) {
                        if ($var_type == 'file_upload') {
                            $group_var_name = $a_var['names'][$key_column];
                            if (!empty($a_var['values'])) {
                                foreach ($a_var['values'] as $row => $row_values) {
                                    if (!empty($row_values[$key_column]) && is_object($row_values[$key_column]) && !$row_values[$key_column]->get('not_exist') && !$row_values[$key_column]->get('deleted_by')) {
                                        $file = $row_values[$key_column];
                                        if (isset($a_var[$group_var_name]['view_mode']) && $a_var[$group_var_name]['view_mode'] == 'thumbnail' && $row_values[$key_column]->isImage()) {
                                            $file_upload_properties = $a_var[$group_var_name];
                                            $value = sprintf ('<img src="%s?%s=files&files=viewfile&viewfile=%s%s%s" />',
                                                $_SERVER['SCRIPT_NAME'],
                                                $this->registry['module_param'],
                                                rawurlencode(General::encrypt($file->get('id'), '_viewfile_', 'xtea')),
                                                (!empty($file_upload_properties['thumb_width']) ? ("&maxwidth=" . $file_upload_properties['thumb_width']) : ''),
                                                (!empty($file_upload_properties['thumb_height']) ? ("&maxheight=" . $file_upload_properties['thumb_height']) : '')
                                            );
                                            $a_var['values'][$row][$key_column] = $value;
                                        } else {
                                            $a_var['values'][$row][$key_column] = $file->get('name');
                                        }
                                    } else {
                                        $a_var['values'][$row][$key_column] = '';
                                    }
                                }
                            }
                        }
                    }

                    $groupingViewer->data['var'] = $a_var;
                    $groupingViewer->data['pattern_id'] = $pattern_id;
                    $replacement = $groupingViewer->fetch();

                    if ($pattern_format == 'docx') {
                        //remove whitespaces
                        $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                    }
                    $vars['a_' . $a_var['name']] = $replacement;
                }
            } elseif (isset($a_var['type']) && $a_var['type'] == 'gt2') {
                // Skip the gt2 if it's empty (GT2 row could not be saved with empty row. The empty GT2 table has an empty array the $a_var['values'][0])
                if (empty($a_var['values']) || count(array_filter($a_var['values'])) == 0) {
                    continue;
                }

                //get print settings for the 2nd type grouping table
                $print_properties = $this->getGT2PrintSettings($pattern_id);

                if ($a_var['multilang']) {
                    $a_var_idx = array();
                    $a_var_name = $a_var['name'];
                    foreach ($translations as $t_lang) {
                        if ($t_lang != $this->get('model_lang')) {
                            // get index of gt2 variable in each set of variables
                            $a_var_idx[$t_lang] = array_keys(array_filter($t_a_vars[$t_lang], function($tv) use ($a_var_name) { return $tv['type'] == 'gt2' && $tv['name'] == $a_var_name; }));
                            if ($a_var_idx[$t_lang]) {
                                $a_var_idx[$t_lang] = reset($a_var_idx[$t_lang]);
                            } else {
                                $a_var_idx[$t_lang] = false;
                                continue;
                            }
                            $table = $t_a_vars[$t_lang][$a_var_idx[$t_lang]];
                        } else {
                            $table = $a_var;
                        }

                        //prepare files in GT2
                        if (in_array('file_upload', array_unique(array_column($table['vars'], 'type')))) {
                            foreach ($table['values'] as $ridx => $row) {
                                foreach ($row as $rkey => $rval) {
                                    if (!empty($rval) && is_object($rval)) {
                                        $file = $rval;
                                        if (!$file->get('not_exist') && !$file->get('deleted_by')) {
                                            $file = $this->getFileUploadForPrint($file, $table['vars'][$rkey]);
                                        } else {
                                            $file = '';
                                        }
                                        $table['values'][$ridx][$rkey] = $file;
                                    }
                                }
                            }
                        }
                        $table_ordered = $table;
                        $table_ordered['vars'] = array();
                        $styles_for_template = array();
                        $styles_for_docx_template = array();

                        foreach ($print_properties as $key => $property) {
                            // style properties
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];

                                if ($pattern_format == 'docx') {
                                    //prepare styles for DOCX word template
                                    $style_rows = preg_split('(\n|\r)', $property['style']);
                                    $styles = array('hide' => false);
                                    foreach ($style_rows as $sr) {
                                        list($attribute, $attribute_value) = preg_split('#\s*:\s*#', $sr);
                                        $attribute_value = str_replace('!important', '', $attribute_value);
                                        $attribute_value = str_replace(';', '', $attribute_value);
                                        switch ($attribute) {
                                            case 'font-family':
                                                //get the first font in the family
                                                $styles['font'] = preg_replace('#([^,]),.*#', '\1', $attribute_value);
                                                break;
                                            case 'font-size':
                                                //convert pixels to points (it's all in the modifier convert2docx_fontsize)
                                                $styles['size'] = $attribute_value;
                                                break;
                                            case 'color':
                                                //remove the hash for hex number
                                                $styles['color'] = str_replace('#', '', $attribute_value);
                                                break;
                                            case 'font-style':
                                                $styles['italic'] = strpos($attribute_value, 'italic') !== false;
                                                break;
                                            case 'font-weight':
                                                $styles['bold'] = strpos($attribute_value, 'bold') !== false;
                                                break;
                                            case 'width':
                                                // (it's all in the modifier convert2docx_width)
                                                $styles['width'] = $attribute_value;
                                                break;
                                            case 'text-align':
                                                $styles['align'] = $attribute_value;
                                                break;
                                            case 'display':
                                                $styles['hide'] = true;
                                                break;
                                        }
                                    }
                                    $styles_for_docx_template[$key] = $styles;
                                }
                            }
                            // label for table caption
                            if ($key == 'var_' . $table['id']) {
                                if (isset($property['labels'][$t_lang])) {
                                    $table_ordered['label'] = $property['labels'][$t_lang];
                                }
                                continue;
                            }
                            foreach ($table['vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    $table_ordered['vars'][$idx] = $var;
                                    // label for field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $table_ordered['vars'][$idx]['agregate'] = $property['agregate'];
                                        } elseif (isset($table_ordered['vars'][$idx]['agregate'])) {
                                            unset($table_ordered['vars'][$idx]['agregate']);
                                        }
                                    }
                                    continue 2;
                                }
                            }
                            foreach ($table['plain_vars'] as $idx => $var) {
                                if ($key == 'var_' . $var['id']) {
                                    // label for total field
                                    if (isset($property['labels'][$t_lang])) {
                                        $table_ordered['plain_vars'][$idx]['label'] = $property['labels'][$t_lang];
                                    }
                                    continue 2;
                                }
                            }
                        }

                        // calculate aggregates in GT2 table
                        $table_ordered = $this->calculateGT2Agregates($table_ordered);

                        if ($pattern_format != 'docx') {
                            //prepare GT2 for HTML/PDF
                            $groupingViewer = new Viewer($this->registry);
                            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars.html');
                            $groupingViewer->data['styles'] = $styles_for_template;
                            $groupingViewer->data['table'] = $table_ordered;
                            $groupingViewer->data['pattern_id'] = $pattern_id;
                            $vars[$t_lang . '_a_' . $a_var['name']] = $groupingViewer->fetch();
                        } else {
                            //prepare GT2 for DOCX
                            $groupingViewer = new Viewer($this->registry);
                            $groupingViewer->setFrameset($this->registry['theme']->templatesDir . '_gt2_vars_docx.html');
                            $groupingViewer->data['styles'] = $styles_for_docx_template;
                            $groupingViewer->data['table'] = $table_ordered;
                            $groupingViewer->data['pattern_id'] = $pattern_id;
                            $replacement = $groupingViewer->fetch();

                            //remove whitespaces
                            $replacement = trim(preg_replace(array("#>\s+#", "#\s+<#u"), array(">", "<"), $replacement));
                            $vars[$t_lang . '_a_' . $a_var['name']] = $replacement;
                        }
                    }
                }

                //get the plain vars of the GT2
                $plain_values = $a_var['plain_values'];
                foreach ($plain_values as $plain_var => $plain_value) {
                    switch ($plain_var) {
                    case 'total_no_vat_reason_text':
                        if ($a_var['plain_vars'][$plain_var]['multilang']) {
                            foreach ($translations as $t_lang) {
                                $vars[$t_lang . '_a_' . $plain_var] =
                                    $a_var['multilang'] && $t_lang != $this->get('model_lang') ?
                                    ($a_var_idx[$t_lang] !== false ? $t_a_vars[$t_lang][$a_var_idx[$t_lang]]['plain_values'][$plain_var] : '') :
                                    $plain_value;
                            }
                        } else {
                            $vars['a_' . $plain_var] = $plain_value;
                        }
                        break;
                    case 'total_vat_rate':
                        $vars['a_' . $plain_var] = $plain_value . ' %';
                        break;
                    default:
                        $vars['a_' . $plain_var] = $plain_value;
                    }
                }
            }/* elseif (isset($a_var['type']) && $a_var['type'] == 'bb') {
                if ($a_var['multilang']) {
                    foreach ($translations as $t_lang) {
                        $bbViewer = new Viewer($this->registry);
                        $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                        //get print settings for the 2nd type grouping table
                        $print_properties = $this->getGT2PrintSettings($pattern_id);

                        $styles_for_template = array();

                        foreach ($print_properties as $key => $property) {
                            if (!empty($property['style'])) {
                                $styles_for_template[$key] = $property['style'];
                            }
                        }

                        $bbViewer->data['styles'] = $styles_for_template;
                        $bbViewer->data['pattern_id'] = $pattern_id;
                        $bbViewer->data['contract'] = $this;

                        // complete the labels from the printing properties
                        $new_bb_vars = $this->get('bb_vars');
                        foreach ($new_bb_vars as $bb_idx => $bb_details) {
                            // if the variable is GT2
                            if ($bb_details['type'] == 'gt2') {
                                foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    if (array_key_exists($print_properties_key, $print_properties)) {
                                        $property = $print_properties[$print_properties_key];
                                        // label
                                        if (!empty($property['labels'][$t_lang])) {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = $property['labels'][$t_lang];
                                        }
                                        // aggregates
                                        if (isset($property['agregate'])) {
                                            if ($property['agregate'] != 'none') {
                                                $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                            } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                                unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                            }
                                        }
                                    }
                                }
                                foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                    $print_properties_key = 'var_' . $var_details['id'];
                                    // label
                                    if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'][$t_lang])) {
                                        $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = $print_properties[$print_properties_key]['labels'][$t_lang];
                                    }
                                }
                                // calculate aggregates in GT2 table
                                $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                            }
                        }
                        $bbViewer->data['contract']->set('bb_vars', $new_bb_vars, true);

                        if ($t_lang != $this->get('model_lang')) {
                            $bbViewer->data['var'] = @$t_a_vars[$t_lang][$k];
                            $vars[$t_contract[$t_lang]->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        } else {
                            $bbViewer->data['var'] = $a_var;
                            $vars[$this->get('model_lang') . '_a_' . $a_var['name']] = $bbViewer->fetch();
                        }
                    }
                } else {
                    $bbViewer = new Viewer($this->registry);
                    $bbViewer->setFrameset($this->registry['theme']->templatesDir . 'bb_vars.html');
                    $bbViewer->data['contract'] = $this;
                    $bbViewer->data['var'] = $a_var;

                    //get print settings for the 2nd type grouping table
                    $print_properties = $this->getGT2PrintSettings($pattern_id);

                    // complete the labels from the printing properties
                    $new_bb_vars = $this->get('bb_vars');
                    foreach ($new_bb_vars as $bb_idx => $bb_details) {
                        // if the variable is GT2
                        if ($bb_details['type'] == 'gt2') {
                            foreach ($bb_details['vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                if (array_key_exists($print_properties_key, $print_properties)) {
                                    $property = $print_properties[$print_properties_key];
                                    // label
                                    if (!empty($property['labels'])) {
                                        $new_bb_vars[$bb_idx]['vars'][$var_nm]['label'] = reset($property['labels']);
                                    }
                                    // aggregates
                                    if (isset($property['agregate'])) {
                                        if ($property['agregate'] != 'none') {
                                            $new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'] = $property['agregate'];
                                        } elseif (isset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate'])) {
                                            unset($new_bb_vars[$bb_idx]['vars'][$var_nm]['agregate']);
                                        }
                                    }
                                }
                            }
                            foreach ($bb_details['plain_vars'] as $var_nm => $var_details) {
                                $print_properties_key = 'var_' . $var_details['id'];
                                // label
                                if (array_key_exists($print_properties_key, $print_properties) && !empty($print_properties[$print_properties_key]['labels'])) {
                                    $new_bb_vars[$bb_idx]['plain_vars'][$var_nm]['label'] = reset($print_properties[$print_properties_key]['labels']);
                                }
                            }
                            // calculate aggregates in GT2 table
                            $new_bb_vars[$bb_idx] = $this->calculateGT2Agregates($new_bb_vars[$bb_idx]);
                        }
                    }
                    $bbViewer->data['contract']->set('bb_vars', $new_bb_vars, true);

                    $styles_for_template = array();

                    foreach ($print_properties as $key => $property) {
                        if (!empty($property['style'])) {
                            $styles_for_template[$key] = $property['style'];
                        }
                    }
                    $bbViewer->data['styles'] = $styles_for_template;
                    $bbViewer->data['pattern_id'] = $pattern_id;
                    $vars['a_' . $a_var['name']] = $bbViewer->fetch();
                }
            }*/
        }

        if ($this->get('subtype') == 'contract') {
            //get executor's, principal's administrative and financial names
            $administrative_data = $financial_data = $self_administrative_data = $self_financial_data = array();
            foreach ($translations as $t_lang) {
                $administrative_data[$t_lang] = $t_contract[$t_lang]->getCstmAdministrativeData();
                $financial_data[$t_lang] = $t_contract[$t_lang]->getCstmFinancialData();
                $self_administrative_data[$t_lang] = $t_contract[$t_lang]->getSelfAdministrativeData();
                $self_financial_data[$t_lang] = $t_contract[$t_lang]->getSelfFinancialData();
            }
            if ($this->get('party') == 'executor') {
                foreach ($translations as $t_lang) {
                    $vars[$t_lang . '_contract_executor_administrative_name'] = (!empty($self_administrative_data[$t_lang]['name'])) ?
                                                                                $self_administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_executor_financial_name'] = (!empty($self_financial_data[$t_lang]['name'])) ?
                                                                            $self_financial_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_administrative_name'] = (!empty($administrative_data[$t_lang]['name'])) ?
                                                                                 $administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_financial_name'] = (!empty($financial_data[$t_lang]['name'])) ?
                                                                            $financial_data[$t_lang]['name'] : '';
                }
            } elseif ($this->get('party') == 'principal') {
                foreach ($translations as $t_lang) {
                    $vars[$t_lang . '_contract_executor_administrative_name'] = (!empty($administrative_data[$t_lang]['name'])) ?
                                                                                $administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_executor_financial_name'] = (!empty($financial_data[$t_lang]['name'])) ?
                                                                           $financial_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_administrative_name'] = (!empty($self_administrative_data[$t_lang]['name'])) ?
                                                                                 $self_administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_financial_name'] = (!empty($self_financial_data[$t_lang]['name'])) ?
                                                                            $self_financial_data[$t_lang]['name'] : '';
                }
            }
        } else {
            //get executor's, principal's administrative and financial names from current contract
            //save the previous registry lang
            $registry_lang_old = $this->registry['lang'];

            foreach ($translations as $t_lang) {
                $this->registry->set('lang', $t_lang, true);
                $this->registry['translater']->reloadFiles($t_lang);

                $filters = array('model_lang' => $t_lang,
                                 'where' => array('co.id = ' . $this->get('parent_record')));
                $t_contract[$t_lang] = Contracts::searchOne($this->registry, $filters);
            }

            $this->registry->set('lang', $registry_lang_old, true);
            $this->registry['translater']->reloadFiles($registry_lang_old);

            $administrative_data = $financial_data = $self_administrative_data = $self_financial_data = array();
            foreach ($translations as $t_lang) {
                $administrative_data[$t_lang] = $t_contract[$t_lang]->getCstmAdministrativeData();
                $financial_data[$t_lang] = $t_contract[$t_lang]->getCstmFinancialData();
                $self_administrative_data[$t_lang] = $t_contract[$t_lang]->getSelfAdministrativeData();
                $self_financial_data[$t_lang] = $t_contract[$t_lang]->getSelfFinancialData();
            }
            if ($this->get('party') == 'executor') {
                foreach ($translations as $t_lang) {
                    $vars[$t_lang . '_contract_executor_administrative_name'] = (!empty($self_administrative_data[$t_lang]['name'])) ?
                                                                                $self_administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_executor_financial_name'] = (!empty($self_financial_data[$t_lang]['name'])) ?
                                                                            $self_financial_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_administrative_name'] = (!empty($administrative_data[$t_lang]['name'])) ?
                                                                                 $administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_financial_name'] = (!empty($financial_data[$t_lang]['name'])) ?
                                                                            $financial_data[$t_lang]['name'] : '';
                }
            } elseif ($this->get('party') == 'principal') {
                foreach ($translations as $t_lang) {
                    $vars[$t_lang . '_contract_executor_administrative_name'] = (!empty($administrative_data[$t_lang]['name'])) ?
                                                                                $administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_executor_financial_name'] = (!empty($financial_data[$t_lang]['name'])) ?
                                                                           $financial_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_administrative_name'] = (!empty($self_administrative_data[$t_lang]['name'])) ?
                                                                                 $self_administrative_data[$t_lang]['name'] : '';
                    $vars[$t_lang . '_contract_principal_financial_name'] = (!empty($self_financial_data[$t_lang]['name'])) ?
                                                                            $self_financial_data[$t_lang]['name'] : '';
                }
            }
        }

        return $vars;
    }

    public function getChildrenTree($parent, $level, &$tree) {
        // retrieve all children of $parent
        $query = 'SELECT cor.parent_id, cor.origin, cor.parent_model_name AS model ' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_RELATIVES . ' AS cor ' . "\n" .
                 'WHERE cor.link_to="' . $parent . '" AND cor.link_to_model_name = "Contract"';
        $records = $this->registry['db']->GetAll($query);

        // display each child
        foreach ($records as $k => $rec) {
            $tree[] = array('id' => $rec['parent_id'], 'level' => $level,
                            'origin' => $rec['origin'], 'model' => $rec['model']);
            if ($rec['model'] == 'Contract' && $level < PH_MAX_TREE_LEVEL) {
                $this->getChildrenTree($rec['parent_id'], $level+1, $tree);
            }
        }
    }

    public function getParentsTree($child, $level, &$tree) {
        // retrieve all parents of $child
        $query = 'SELECT cor.link_to, cor.origin, cor.link_to_model_name AS model ' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS_RELATIVES . ' AS cor ' . "\n" .
                 'WHERE cor.parent_id="' . $child . '" AND cor.parent_model_name="Contract" ' . "\n" .
                 '  AND NOT (cor.link_to="' . $child . '" AND cor.link_to_model_name="Contract")';
        $records = $this->registry['db']->GetAll($query);

        // display each parent
        foreach ($records as $k => $rec) {
            $tree[] = array('id' => $rec['link_to'], 'level' => $level,
                            'origin' => $rec['origin'], 'model' => $rec['model']);
            if ($rec['model'] == 'Contract' && $level < PH_MAX_TREE_LEVEL) {
                $this->getParentsTree($rec['link_to'], $level+1, $tree);
            } elseif ($rec['model'] == 'Document' && $level < PH_MAX_TREE_LEVEL) {
                require_once PH_MODULES_DIR . 'documents/models/documents.model.php';
                $d = new Document($this->registry);
                $d->getParentsTree($rec['link_to'], $level+1, $tree);
                unset($d);
            }
        }
    }

    /**
     * Sends notification e-mail.
     *
     * @param string $template - template name
     * @param string $email - recipient e-mail
     * @param string $user_name - recipient name and last name
     */
    function sendNotification($template, $email, $user_name) {
        if (!$this->shouldSendEmail($template)) {
            return true;
        }

        //send email
        $mailer = new Mailer($this->registry, $template, $this);
        $mailer->placeholder->add('contract_id', $this->get('id'));
        $mailer->placeholder->add('contract_num', $this->getNum());
        $mailer->placeholder->add('contract_name', $this->get('name'));
        $mailer->placeholder->add('contract_type', $this->get('type_name'));
        $mailer->placeholder->add('contract_added_by', $this->get('added_by_name'));
        $mailer->placeholder->add('customer_name', $this->get('customer_name'));
        $mailer->placeholder->add('user_name', $user_name);
        $mailer->template['model_name'] = $this->modelName;
        $mailer->template['model_id'] = $this->get('id');
        $mailer->template['recipient_name'] = $user_name;

        $contract_view_url = sprintf('%s/index.php?%s=contracts&contracts=view&view=%d',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], $this->get('id'));
        $mailer->placeholder->add('contract_view_url', $contract_view_url);

        $add_comment_url = sprintf('%s/index.php?%s=contracts&contracts=communications&communications=%d&communication_type=comments#comments_add_form',
                                    $this->registry['config']->getParam('crontab', 'base_host'),
                                    $this->registry['module_param'], $this->get('id'));
        $mailer->placeholder->add('contract_add_comment_url', $add_comment_url);

        $mailer->placeholder->add('user_lastname', $this->registry['currentUser']->get('lastname'));
        $mailer->placeholder->add('user_firstname', $this->registry['currentUser']->get('firstname'));

        $mailer->placeholder->add('to_email', $email);

        if ($template == 'contract_start_subtype') {
            $mailer->placeholder->add('contract_subtype', $this->i18n('contracts_' . $this->get('subtype')));
            $mailer->placeholder->add('for_contract', ' ' . sprintf($this->i18n('contracts_for_contract'), $this->get('parent_num')));
            $mailer->placeholder->add('contract_date_start', $this->get('date_start_subtype'));
        }

        $result = $mailer->send();
        if (!@in_array($email, $result['erred'])) {
            if ($this->registry['sent_email'] != 1) {
                $this->registry['messages']->setMessage($this->i18n('contracts_email_sent_success'));
                $this->registry->set('sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        } else {
            if ($this->registry['err_sent_email'] != 1) {
                $this->registry['messages']->setWarning($this->i18n('error_contracts_send_email'), '', 10);
                $this->registry->set('err_sent_email', 1, true);
            }
            $this->registry['messages']->insertInSession($this->registry);
        }
    }

    /**
     * Translates some i18n fields
     *
     * @return bool - result of the operation
     */
    public function getDefaultTranslations() {
        $db = $this->registry['db'];
        $model_lang = $this->registry['lang'];

        //select clause
        $sql['select'] = 'SELECT co.*, coi18n.*, ' . "\n" .
                         '  "' . $model_lang . '" as model_lang, ' . "\n" .
                         '  coti18n.name as type_name, cot.party as party, ' . "\n" .
                         '  gi18n.name as group_name, ' . "\n" .
                         '  pi18n.name as project_name ' . "\n";
       //from clause
        $sql['from'] = 'FROM ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                        //relate to contract types
                       'JOIN ' . DB_TABLE_CONTRACTS_TYPES . ' AS cot' . "\n" .
                       '  ON (co.type=cot.id AND cot.active=1 AND cot.deleted=0)' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                       '  ON (co.id=di18n.parent_id AND coi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to contract group
                       'LEFT JOIN ' . DB_TABLE_GROUPS_I18N . ' AS gi18n' . "\n" .
                       '  ON (co.group=gi18n.parent_id AND gi18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to customers
                       'LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n' . "\n" .
                       '  ON (co.customer=ci18n.parent_id AND ci18n.lang="' . $model_lang . '")' . "\n" .
                       'LEFT JOIN ' . DB_TABLE_CONTRACTS_TYPES_I18N . ' AS coti18n' . "\n" .
                       '  ON (co.type=dti18n.parent_id AND coti18n.lang="' . $model_lang . '")' . "\n" .
                        //relate to contract types i18n
                       'LEFT JOIN ' . DB_TABLE_PROJECTS_I18N . ' AS pi18n' . "\n" .
                       '  ON (co.project=pi18n.parent_id AND pi18n.lang="' . $model_lang . '")' . "\n";
        $sql['where'] = 'WHERE co.id=' . $this->get('id') . "\n";

        $query = implode("\n", $sql);
        $records = $db->GetRow($query);

        $this->set('default_translations', $records, true);

        return $records;
    }

    /**
     * Get counter for this model
     *
     * @return object - Contracts_Counter object
     */
    public function getCounter() {
        if (!isset($this->counter)) {
            $registry_added = false;
            if (!isset($this->registry)) {
                $registry_added = true;
                $this->unsanitize();
            }

            require_once 'contracts.counters.factory.php';
            $filters = array('where' => array(
                                        'coc.model_type = "' . $this->get('type') . '"',
                                        '(coc.office = "' . $this->get('office') . '" OR coc.office = 0)',
                                        'coc.company = "' . $this->get('company') . '"',
                                        'coc.deleted_by = 0',
                                        'coc.active = 1'),
                             'sort' => array('coc.office DESC')
                            );
            $this->counter = Contracts_Counters::searchOne($this->registry, $filters);

            if ($registry_added) {
                $this->sanitize();
            }
        }

        return $this->counter;
    }

    /**
     * Gets invoicing templates of contract
     *
     * @param array $params - filtering parameters
     * @return Finance_Invoices_Template[] - found models
     */
    public function getInvoicesTemplates($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $templates_types = array(
            'type' => 'dropdown',
            'readonly' => 0,
            'hidden' => 0,
            'width' => '',
            'height' => '',
            'label' => $this->i18n('contracts_invoice_type'),
            'help' => '',
            'id' => '',
            'options' => array( 0 => array('label' => $this->i18n('contracts_advance_invoice'),
                                            'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_ADVANCE),
                                1 => array('label' => $this->i18n('contracts_invoice'),
                                            'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_NORMAL),
                                /*2 => array('label' => $this->i18n('contracts_finish_invoice'),
                                            'option_value' => PH_FINANCE_TYPE_INVOICE_TEMPLATE_FINAL),*/
                            ),
            'value' => array(),
            );
        $this->set('templates_types', $templates_types, true);
        $id = $this->get('id');
        $sanitize = false;
        if (!empty($params['sanitize'])) {
            $sanitize = $params['sanitize'];
        }
        $where =  array('fit.contract_id = ' . $id);
        if (!empty($params['date_to'])) {
            $where[] = sprintf('fit.periods_start < "%s"', $params['date_to']);
        }
        if (!empty($params['recurrent'])) {
            $where[] = sprintf('fit.recurrent = %d', $params['recurrent']);
        }
        if (!empty($params['type'])) {
            $where[] = sprintf('fit.type IN (%s)', is_array($params['type']) ? implode(', ', $params['type']) : $params['type']);
        }
        if (!empty($params['status'])) {
            if (!is_array($params['status'])) {
                $params['status'] = array($params['status']);
            }
            $where[] = sprintf('fit.status IN ("%s")', implode('", ",', $params['status']));
        }
        $sort = !empty($params['sort']) ? $params['sort'] : array('fit.id DESC');
        array_unshift($sort, 'fit.type desc');
        require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
        $filters = array('where' => $where,
                         'sort' => $sort,
                         'sanitize' => $sanitize);
        if (!empty($params['exclude_automation_templates'])) {
            $filters['where'][] = 'fit.added_by != ' . PH_AUTOMATION_USER;
        }
        $invoices_templates = Finance_Invoices_Templates::search($this->registry, $filters);

        foreach ($invoices_templates as $key => $invoices_template) {
            // set properties from contract so we can get GT2 data even when
            // there are no GT2 rows and no relatives record between contract and template
            $invoices_templates[$key]->set('parent_type', $this->get('type'), true);
            $invoices_templates[$key]->set('link_to_model_name', 'Contract', true);

            $gt2 = $invoices_templates[$key]->getGT2Vars();
            //hide columns for indexes if the invoice is not recurrent
            if (!$invoices_templates[$key]->get('recurrent')) {
                if (isset($gt2['vars']['price_index'])) {
                    $gt2['vars']['price_index']['hidden'] = 1;
                }
                if (isset($gt2['vars']['quantity_index'])) {
                    $gt2['vars']['quantity_index']['hidden'] = 1;
                }
                if (isset($gt2['vars']['discount_value_index'])) {
                    $gt2['vars']['discount_value_index']['hidden'] = 1;
                }
                $invoices_templates[$key]->set('grouping_table_2', $gt2, true);
            }
            if (empty($params['exclude_invoices'])) {
                $invoices_templates[$key]->getRelatives(array('get_invoices' => true));
                $invoices_templates[$key]->sanitize();
            }
        }

        if (!empty($params['get_assoc'])) {
            $tmp = array();
            foreach ($invoices_templates as $t) {
                $tmp[$t->get('id')] = $t;
            }
            $invoices_templates = $tmp;
        }

        $this->set('invoices_templates', $invoices_templates, true);
        if ($sanitize_after) {
            $this->sanitize();
        }

        return $invoices_templates;
    }

    /**
     * Checks whether a handover could be added
     *
     * @param string $direction - 'incoming' or 'outgoing'
     * @param bool $set_table_back - sets the calculated quantities back in the model
     * @return bool - result of the operation
     */
    public function checkAddingHandover($direction, $set_table_back = false) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        //get the unique key for the contract
        $unique_key = $this->getUniqueKey();

        $fields = array();
        $gt2_columns = array_map('strtolower', array_keys($db->MetaColumns(DB_TABLE_GT2_DETAILS, true)));
        $gt2_i18n_columns = array_map('strtolower', array_keys($db->MetaColumns(DB_TABLE_GT2_DETAILS_I18N, true)));
        foreach($unique_key as $uk) {
            if (in_array($uk, $gt2_columns)) {
                $fields[] = 'fdd.`' . $uk . '`';
            } elseif (in_array($uk, $gt2_i18n_columns)) {
                $fields[] = 'fddi.`' . $uk . '`';
            }
        }

        //get quantities in the previous handovers
        $query = 'SELECT CONCAT_WS("^^^", ' . implode(', ', $fields) . ') as `key`, fwd.direction, fdd.quantity, fdd.id as row_id' . "\n" .
                'FROM ' . DB_TABLE_GT2_DETAILS . ' AS fdd' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr' . "\n" .
                '  ON fdd.model_id = frr.parent_id AND frr.link_to = ' . $this->get('id') . "\n" .
                '  AND frr.parent_model_name = "Finance_Warehouses_Document"' . "\n" .
                '  AND frr.link_to_model_name = "Contract"' . "\n" .
                'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                '  ON fdd.model_id = fwd.id AND fwd.annulled_by = 0 AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER . "\n" .
                'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS fddi' . "\n" .
                '  ON fdd.id = fddi.parent_id AND fddi.lang = "' . $this->get('model_lang')  . '"'. "\n" .
                'WHERE fdd.model = "Finance_Warehouses_Document"';
        $handovered = $db->GetAll($query);

        $new_values = array();
        if ($this->get('party') == 'executor' && $direction == 'outgoing') {
            //get templates with commodity articles
            $query = 'SELECT fit.id, GROUP_CONCAT(gt2.article_id)' . "\n" .
                     'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . " fit \n" .
                     'JOIN ' . DB_TABLE_GT2_DETAILS . " gt2 \n" .
                     '    ON gt2.model = "Finance_Invoices_Template" AND gt2.model_id = fit.id' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES . " n \n" .
                     '    ON n.id = gt2.article_id AND n.subtype = "commodity"' . "\n" .
                     'WHERE fit.deleted_by = 0 AND fit.contract_id = ' . $this->get('id') . "\n" .
                     'GROUP BY fit.id';
            $articles = $db->GetAssoc($query);
            $templates = array_unique(array_keys($articles));
            $unique_articles = array();
            foreach ($articles as $a) {
                if ($a) {
                    $a = preg_split('#\s*,\s*#', $a);
                    $unique_articles = array_merge($unique_articles, $a);
                }
            }
            $articles = array_unique($unique_articles);
            if (empty($templates) || empty($articles)) {
                //no commodity articles
                $this->set('grouping_table_2', array(), true);
                return false;
            }
            //get templates
            $filters = array('where' => array(sprintf('fit.id IN (%s)', implode(', ', $templates))));
            require_once PH_MODULES_DIR . 'finance/models/finance.invoices_templates.factory.php';
            $templates = Finance_Invoices_Templates::search($this->registry, $filters);

            foreach ($templates as $t) {
                $gt2 = $t->getGT2Vars();
                foreach ($gt2['values'] as $k => $v) {
                    if (!in_array($v['article_id'], $articles)) {
                        //remove non-commodity articles
                        unset($gt2['values'][$k]);
                    }
                }
                if ($t->get('recurrent')) {
                    $t->set('grouping_table_2', $gt2, true);
                    $t->set('status', 'opened', true);
                    $next_from = $t->get('periods_start');
                    //calculate template recurrence until we reach the periods_end
                    while ($next_from <= $t->get('periods_end')) {
                        list($gt2, $to) = $t->calculateRecurrence(array('date_from' => $next_from));
                        //apply indexes for the rows if any indexes are present
                        $gt2 = Contracts::getIndexedValues($this->registry, $gt2);
                        $t->set('status', 'locked', true);
                        $next_from = date_add(date_create($to), new DateInterval('P1D'))->format('Y-m-d');
                        foreach ($gt2 as $k => $v) {
                            //create unique key for the row
                            $uk = array();
                            foreach ($unique_key as $u) {
                                // this won't always work because of partial periods, indexes etc. but let's give it a try
                                if (in_array($u, array('price'))) {
                                    $v[$u] = sprintf('%.6F', $v[$u]);
                                }
                                $uk[] = $v[$u];
                            }
                            $uk = implode('^^^', $uk);
                            if (isset($new_values[$uk])) {
                                $new_values[$uk]['quantity'] += $v['quantity'];
                            } else {
                                $new_values[$uk] = $v;
                            }
                        }
                    }
                } else {
                    foreach ($gt2['values'] as $k => $v) {
                        //create unique key for the row
                        $uk = array();
                        foreach ($unique_key as $u) {
                            // this won't always work because of partial periods, indexes etc. but let's give it a try
                            if (in_array($u, array('price'))) {
                                $v[$u] = sprintf('%.6F', $v[$u]);
                            }
                            $uk[] = $v[$u];
                        }
                        $uk = implode('^^^', $uk);
                        if (isset($new_values[$uk])) {
                            $new_values[$uk]['quantity'] += $v['quantity'];
                        } else {
                            $new_values[$uk] = $v;
                        }
                    }
                }
            }
            // here we have the quantities for all the commodity articles
            // combined in function of the unique key
        } elseif ($this->get('party') == 'executor' && $direction == 'incoming') {

        }

        //into this IF statement we have the normal direction of handovers
        $articles_rows = array();
        foreach ($handovered as $k => $v) {
            if ($this->get('party') == 'executor' && $direction == 'incoming') {
                $articles_rows[] = $v['row_id'];
            }
            if (!empty($new_values[$v['key']])) {
                if ($v['direction'] == $direction) {
                    //same kind of protocol so decrease what we can handover
                    $new_values[$v['key']]['quantity'] -= $v['quantity'];
                } else {
                    // counter kind of protocol so increase what we can handover
                    $new_values[$v['key']]['quantity'] += $v['quantity'];
                }
                if ($new_values[$v['key']]['quantity'] <= 0) {
                    if ($new_values[$v['key']]['quantity'] < 0) {
                        $more = $this->get('more_handovered');
                        if (empty($more)) {
                            $more = array();
                        }
                        $more[$new_values[$v['key']]['article_id']] = $new_values[$v['key']]['article_name'];
                        $this->set('more_handovered', $more , true);
                    }
                    unset($new_values[$v['key']]);
                }
            } else {
                //fetch the whole row
                $query = 'SELECT gt2.*, gt2i18n.* ' . "\n" .
                         'FROM ' . DB_TABLE_GT2_DETAILS . ' gt2' . "\n" .
                         'LEFT JOIN ' . DB_TABLE_GT2_DETAILS_I18N . ' AS gt2i18n' . "\n" .
                         '  ON gt2.id = gt2i18n.parent_id AND gt2i18n.lang = "' . $this->get('model_lang') . '"' . "\n" .
                         'WHERE gt2.id = ' . $v['row_id'];
                $new_values[$v['key']] = $this->registry['db']->GetRow($query);
            }
        }

        if ($this->get('get_existing_batches') && !empty($articles_rows)) {
            $query = 'SELECT b.id as option_value, b.code as label, g.article_id' . "\n" .
                     'FROM ' . DB_TABLE_GT2_DETAILS . ' g' . "\n" .
                     'JOIN ' . DB_TABLE_NOMENCLATURES . ' n' .
                     '  ON g.article_id = n.id AND n.has_batch > 0' . "\n" .
                     'JOIN ' . DB_TABLE_GT2_BATCHES_DATA . ' bd' . "\n" .
                     '  ON bd.parent_id = g.id' . "\n" .
                     'JOIN ' . DB_TABLE_FINANCE_BATCHES . ' b' . "\n" .
                     '  ON b.id = bd.batch_id' . "\n" .
                     'WHERE g.id IN (' . implode(', ', $articles_rows) . ')';
            $records = $this->registry['db']->GetAll($query);
            $existing_batches = array();
            foreach ($records as $r) {
                if (empty($existing_batches[$r['article_id']][$r['option_value']])) {
                    $existing_batches[$r['article_id']][$r['option_value']] = $r;
                }
            }

            $this->set('existing_batches', $existing_batches, true);
        }

        if ($set_table_back) {
            if (!empty($new_values)) {
                $gt2 = $this->get('grouping_table_2');
                $gt2['values'] = $new_values;
                $this->set('grouping_table_2', $gt2 , true);
            } else {
                $this->set('grouping_table_2', array(), true);
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        if (!empty($new_values)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * Gets related to the model records
     *
     * @return array - array with related records
     */
    public function getRelatedRecords() {

        $sanitize_after = false;
        if (empty($this->registry)) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $related = array();
        $registry = &$this->registry;
        $db = &$registry['db'];

        //gets which modules of optional related records should be displayed
        list($related_records_modules) = $this->getRelatedRecordsModules();

        // do not display modules that user has no access to
        $rights = $registry['currentUser']->get('rights');

        if (in_array('documents', $related_records_modules)) {
            //get related documents
            require_once PH_MODULES_DIR . 'documents/models/documents.factory.php';
            $filters = array('sanitize' => true,
                             'where' => array('d.contract=' . $this->get('id')),
                             'check_module_permissions' => 'documents');
            $result = Documents::getIds($registry, $filters);
            $link = '#related_subpanel_contract' . $this->get('id');
            $related['documents'] = array('name' => 'documents',
                                          'label' => $this->i18n('menu_documents'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
        }

        if ($this->isDefined('commodity') && $this->get('commodity') != 'none' && $this->checkPermissions('listhandovers')) {
            //get related warehouse documents (handovers)
            $query = 'SELECT parent_id FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . "\n" .
                     '  WHERE parent_model_name=\'Finance_Warehouses_Document\' AND ' . "\n" .
                     '  link_to=' . $this->get('id') . ' AND link_to_model_name=\'Contract\'';
            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=contracts&amp;';
            $link .= 'contracts=listhandovers&amp;listhandovers=' . $this->get('id');
            $related['finance_warehouses_documents'] = array('name' => 'finance_warehouses_documents',
                                                             'label' => $this->i18n('menu_finance_warehouses_documents'),
                                                             'link' => $link,
                                                             'ids' => is_array($result) ? $result : array());
        }

        if (isset($rights[$this->module]['communications']) && $rights[$this->module]['communications'] != 'none' && $this->checkPermissions('communications')) {
            if (isset($rights[$this->module]['comments']) && $rights[$this->module]['comments'] != 'none' && $this->checkPermissions('comments')) {
                //get related comments
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_COMMENTS . "\n" .
                         'WHERE deleted_by = 0 AND model = \'Contract\' AND model_id = \'' . $this->get('id') . '\'' . ($registry['currentUser']->get('is_portal') ? ' AND is_portal = \'1\'' : '');

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=contracts&amp;';
                $link .= 'contracts=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=comments';
                $related['comments'] = array('name' => 'comments',
                                              'label' => $this->i18n('contracts_comments'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }

            if (isset($rights[$this->module]['emails']) && $rights[$this->module]['emails'] != 'none' && $this->checkPermissions('emails')) {
                //get related e-mails
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_EMAILS_SENTBOX . "\n" .
                         'WHERE model = \'Contract\' AND model_id = ' . $this->get('id') . ' AND `system`=\'0\'' . "\n" .
                         'GROUP BY code';

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=contracts&amp;';
                $link .= 'contracts=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=emails';
                $related['emails'] = array('name' => 'email',
                                          'label' => $this->i18n('contracts_emails'),
                                          'link' => $link,
                                          'ids' => is_array($result) ? $result : array());
            }

            if ($this->checkPermissions('minitasks')) {
                //get related mini tasks
                $query = 'SELECT id' . "\n" .
                         'FROM ' . DB_TABLE_MINITASKS . ' AS m' . "\n" .
                         'WHERE model = \'Contract\' AND model_id=' . $this->get('id');

                $current_user_id = $registry['currentUser']->get('id');
                $rights = $registry['currentUser']->get('rights');
                $current_right = isset($rights['minitasks']['list']) ? $rights['minitasks']['list'] : '';
                unset($rights);
                if ($current_right == 'mine') {
                    $query .= " AND m.assigned_to=$current_user_id ";
                } elseif ($current_right == 'group') {
                    $query .= " AND (m.added_by=$current_user_id OR m.assigned_to=$current_user_id) ";
                } elseif ($current_right != 'all') {
                    $query .= ' AND 0';
                }

                $result = $db->GetCol($query);
                $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=contracts&amp;';
                $link .= 'contracts=communications&amp;communications=' . $this->get('id') . '&amp;communication_type=minitasks';
                $related['minitasks'] = array('name' => 'minitasks',
                                              'label' => $this->i18n('menu_minitasks'),
                                              'link' => $link,
                                              'ids' => is_array($result) ? $result : array());
            }
        }

        if ($this->checkPermissions('attachments')) {
            //get related files
            $query = 'SELECT f.id' . "\n" .
                     'FROM ' . DB_TABLE_FILES . ' AS f ' . "\n" .
                     'WHERE f.deleted_by = 0 AND f.model = \'Contract\' AND f.model_id = ' . $this->get('id');
            //check access permissions of files
            require_once PH_MODULES_DIR . 'files/models/files.factory.php';
            $query .= Files::getAdditionalWhere($registry);

            $result = $db->GetCol($query);
            $link = $_SERVER['SCRIPT_NAME'] . '?' . $registry['module_param'] . '=contracts&amp;';
            $link .= 'contracts=attachments&amp;attachments=' . $this->get('id');
            $related['files'] = array('name' => 'attachments',
                                      'label' => $this->i18n('attachments'),
                                      'link' => $link,
                                      'ids' => is_array($result) ? $result : array());
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $related;
    }

    /**
     * Gets related records for contracts
     *
     * @param array $params
     */
    public function getRelatives($params = array()) {
        $db = $this->registry['db'];
        if (isset($params['get_handovers']) && $params['get_handovers'] || isset($params['get_all']) && $params['get_all']) {
            //get handover relatives to this incomes reason
            $query = 'SELECT frr.parent_id' . "\n" .
                    'FROM ' . DB_TABLE_FINANCE_REASONS_RELATIVES . ' AS frr ' . "\n" .
                    'JOIN ' . DB_TABLE_FINANCE_WAREHOUSES_DOCUMENTS . ' AS fwd' . "\n" .
                    '    ON frr.parent_id = fwd.id AND fwd.type = ' . PH_FINANCE_TYPE_HANDOVER .
                    (!empty($params['handovers_direction']) ? sprintf(' AND direction = "%s"', $params['handovers_direction']) : '') ."\n" .
                    'WHERE frr.link_to = ' . $this->get('id') . "\n" .
                    '  AND frr.link_to_model_name ="Contract"' . "\n" .
                    '  AND frr.parent_model_name = "Finance_Warehouses_Document"';
            if (!empty($params['get_handovers']) && is_array($params['get_handovers'])) {
                foreach ($params['get_handovers'] as $clause) {
                    $query .= "\n   AND " . $clause;
                }
            }
            if ($handovers = $db->GetCol($query)) {
                $filters = array(
                    'where' => array('fwd.id IN (' . implode(', ', $handovers) . ')'),
                    'sort' => array('fwd.id ASC'),
                    'sanitize' => true
                );
                require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
                $handovers = Finance_Warehouses_Documents::search($this->registry, $filters);
                $this->set('handovers', $handovers, true);
            }
        }
    }

    /**
     * Saves data for contacts of parties of contract
     *
     * @return boolean - result of the operation
     */
    function savePartiesData() {
        $request = &$this->registry['request'];
        $db = $this->registry['db'];
        $db->StartTrans();

        //check if we have customly entered administrative
        //and financial contact for the customer's side
        foreach (array('cstm_administrative', 'cstm_financial') as $contact) {
            $saved = false;
            $email = preg_replace('#(cstm_(fin|adm)).*#', '$1_email', $contact);

            if ($request->get($contact)) {

                $contact_type_label = $this->i18n('contracts_party_' . (preg_match('#^cstm_adm.*#', $contact) ? 'administrative' : 'financial'));
                $contact_type_label = mb_strtolower($contact_type_label, mb_detect_encoding($contact_type_label));

                if ($request->isRequested($contact . '_isCustom') && $request->get($contact . '_isCustom')) {
                    //first we need to save the new contact person
                    //all custom contact persons we save as a contact of the main branch

                    //get the main branch
                    $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS . "\n" .
                             '  WHERE subtype=\'branch\' AND is_main = 1' . "\n" .
                             '  AND parent_customer = ' . $this->get('customer');
                    $branch = $db->GetOne($query);

                    require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.model.php';
                    $cp = new Customers_Contactperson($this->registry, array());
                    $cp->set('parent_customer_id', $this->get('customer'), true);
                    $cp->set('parent_branch', $branch, true);
                    //get the email entered for the new contact
                    $cp->set('email', array($request->get($email)), true);
                    //get the name for the new contact person
                    $name = preg_split('#\s+#', $request->get($contact), 2);

                    if (isset($name[1])) {
                        // The name contains at least on space, split it
                        $cp->set('name', $name[0], true);
                        $cp->set('lastname', isset($name[1]) ? $name[1] : '', true);
                    } else {
                        // The name DOES NOT contain a space, save the name as family (lastname)
                        $cp->set('lastname', $name[0]);
                    }

                    $cp->set('financial_person', ($contact == 'cstm_financial'), true);
                    //validate contact person and set specific error messages
                    $cp_valid = true;
                    foreach ($cp->get('email') as $index => $cp_email) {
                        if (!Validator::validEmail($cp_email)) {
                            $this->raiseError('error_invalid_email_contact', $email, '', array($contact_type_label));
                            $cp_valid = false;
                        }
                    }

                    if ($cp_valid) {
                        $cp->slashesEscape();
                        if ($cp->add()) {
                            $request->remove($contact . '_isCustom');
                            $request->remove($email . '_isCustom');
                            $request->set($contact, $cp->get('id'), 'post', true);
                            //set to model
                            $this->set($contact, '' . $cp->get('id'), true);
                            $this->set($email, $request->get($email), true);

                            // write history for the customer
                            require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                            require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                            require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                            require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                            $this->loadI18NFiles(PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers_contactpersons.ini');

                            $old_contact_person = new Customers_Contactperson($this->registry);
                            $old_contact_person->sanitize();

                            $filters = array('where' => array('c.id = ' . $cp->get('id'),
                                                              'c.subtype = \'contact\''),
                                             'model_lang' => $this->registry['request']->get('model_lang'),
                                             'sanitize' => true);
                            $new_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                            $filters_customer =  array('where'      => array('c.id = ' . $this->get('customer')),
                                                       'model_lang' => $this->registry['request']->get('model_lang'),
                                                       'sanitize'   => true);
                            $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                            Customers_History::saveData($this->registry, array('model'               => $parent_customer,
                                                                               'new_model'           => $new_contact_person,
                                                                               'old_model'           => $old_contact_person,
                                                                               'action_type'         => 'add_contact_person',
                                                                               'contact_person_name' => trim($new_contact_person->get('name') . ' ' . $new_contact_person->get('lastname'))));
                        }
                    }
                    if (!$cp_valid || !$cp->get('id')) {
                        $db->FailTrans();
                    }
                    $saved = 'all';
                } else {
                    $this->set($contact, $request->get($contact), true);
                    $this->set($email, $request->get($email), true);
                    //if contact person is selected, email is required
                    if ($contact && !Validator::validEmail($request->get($email))) {
                        $this->raiseError('error_select_email_contact_cstm', $email, '', array($contact_type_label));
                        $db->FailTrans();
                    }
                }

                if ($request->isRequested($email . '_isCustom') && $request->get($email . '_isCustom')) {
                    //we have new email for the contact
                    if ($saved != 'all' && $saved != 'email') {
                        if (!Validator::validEmail($request->get($email))) {
                            $this->raiseError('error_invalid_email_contact', $email, '', array($contact_type_label));
                            $db->FailTrans();
                        } else {
                            //update contact emails with the new one
                            $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                                     'SET email = CONCAT(email, \'\n' . $request->get($email) . '\')' . "\n" .
                                     'WHERE id = ' . $request->get($contact);
                            $db->Execute($query);
                            if (!$db->ErrorMsg()) {
                                $saved = 'email';
                            }
                        }
                    }
                } else {
                    $this->set($email, $request->get($email), true);
                }
            } else {
                $this->set($contact, $request->get($contact), true);
                $this->set($email, $request->get($email), true);
            }
        }

        //set self contacts in the model
        foreach (array('self_administrative', 'self_financial') as $contact) {
            if ($request->get($contact)) {
                $email = preg_replace('#(self_(fin|adm)).*#', '$1_email', $contact);
                $this->set($contact, $request->get($contact), true);
                $this->set($email, $request->get($email), true);

                if ($contact == 'self_administrative') {
                    $contact_type_label = $this->i18n('contracts_party_administrative');
                    $contact_type_label = mb_strtolower($contact_type_label, mb_detect_encoding($contact_type_label));

                    if (!Validator::validEmail($request->get($email))) {
                        $this->raiseError('error_select_email_contact_self', $email, '', array($contact_type_label));
                        $db->FailTrans();
                    }
                }
            //self financial contact is required
            } elseif ($contact == 'self_financial' && !$request->get($contact)) {
                $contact_type_label = $this->i18n('contracts_party_financial');
                $this->raiseError('error_select_email_contact_self', $email, '', array($contact_type_label));
                $db->FailTrans();
            }
        }

        $update = array();
        $update['cstm_administrative'] = sprintf('cstm_administrative = \'%s\'', General::slashesEscape($request->get('cstm_administrative')));
        $update['cstm_adm_email'] = sprintf('cstm_adm_email = \'%s\'', General::slashesEscape($request->get('cstm_adm_email')));
        $update['cstm_financial'] = sprintf('cstm_financial = \'%s\'', General::slashesEscape($request->get('cstm_financial')));
        $update['cstm_fin_email'] = sprintf('cstm_fin_email = \'%s\'', General::slashesEscape($request->get('cstm_fin_email')));
        $update['self_administrative'] = sprintf('self_administrative = \'%s\'', General::slashesEscape($request->get('self_administrative')));
        $update['self_adm_email'] = sprintf('self_adm_email = \'%s\'', General::slashesEscape($request->get('self_adm_email')));
        $update['self_financial'] = sprintf('self_financial = \'%s\'', General::slashesEscape($request->get('self_financial')));
        $update['modified'] = 'modified = NOW()';
        $update['modified_by'] = sprintf('modified_by = \'%s\'', $this->registry['currentUser']->get('id'));

        $query = 'UPDATE ' . DB_TABLE_CONTRACTS . ' SET ' . "\n" . implode(",\n", $update) . "\n" .
                 'WHERE id = ' . $this->get('id');
        $db->Execute($query);

        if ($this->get('skip_cc')) {
            $error = $db->HasFailedTrans();
            $db->CompleteTrans();
            return !$error;
        }

        //delete old cc-s
        $query_delete_cc = 'DELETE FROM ' . DB_TABLE_CONTRACTS_CONTACTS_CC . ' WHERE parent_id=' . $this->get('id');
        $db->Execute($query_delete_cc);

        $set = array();

        //get the main branch of customer
        $query = 'SELECT id FROM ' . DB_TABLE_CUSTOMERS . "\n" .
                 'WHERE subtype=\'branch\' AND is_main = 1' . "\n" .
                 '  AND parent_customer = ' . $this->get('customer');
        $branch = $db->GetOne($query);

        //check if we have customly entered contact persons for cc
        //administrative and financial contact for the customer's side
        foreach (array('cstm_administrative_cc', 'cstm_financial_cc') as $type_contact_cc) {
            $purpose = preg_replace('#(cstm_(fin|adm)).*#', '$2', $type_contact_cc);
            $type_email_cc = preg_replace('#(cstm_(fin|adm)).*#', '$1_email_cc', $type_contact_cc);

            $contacts_cc = $request->get($type_contact_cc);
            if (empty($contacts_cc)) {
                continue;
            }
            $emails_cc = $request->get($type_email_cc);

            $contacts_cc_isCustom = $request->isRequested($type_contact_cc . '_isCustom') ? $request->get($type_contact_cc . '_isCustom') : array();
            $emails_cc_isCustom = $request->isRequested($type_email_cc . '_isCustom') ? $request->get($type_email_cc . '_isCustom') : array();

            $errors_lastname = array();
            $errors_email = array();

            foreach ($contacts_cc as $idx => $contact_cc) {
                if (empty($contact_cc)) {
                    continue;
                }
                //get email for contact
                $email_cc = isset($emails_cc[$idx]) ? $emails_cc[$idx] : '';

                if ($contact_cc || $email_cc) {
                    $cc_ok_to_save = true;

                    if (array_key_exists($idx, $contacts_cc_isCustom) && $contacts_cc_isCustom[$idx] ||
                        array_key_exists($idx, $emails_cc_isCustom) && $emails_cc_isCustom[$idx]) {
                        $cc_ok_to_save = false;
                        //first we need to save the new contact person
                        //all custom contact persons are saved as contacts of the main branch
                        require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.model.php';
                        $cp = new Customers_Contactperson($this->registry, array());
                        $cp->set('parent_customer_id', $this->get('customer'), true);
                        $cp->set('parent_branch', $branch, true);
                        //get the email entered for the new contact
                        $cp->set('email', array($email_cc), true);
                        //get the name for the new contact person
                        $name = preg_split('#\s+#', $contact_cc, 2);
                        $cp->set('name', $name[0], true);
                        $cp->set('lastname', (isset($name[1]) ? $name[1] : ''), true);
                        $cp->set('financial_person', ($type_contact_cc == 'cstm_financial_cc'), true);

                        //validate contact person and set error messages per row
                        $errors_email[$idx] = $errors_lastname[$idx] = 0;
                        foreach ($cp->get('email') as $index => $cp_email) {
                            if (!Validator::validEmail($cp_email)) {
                                $errors_email[$idx] = 1;
                            }
                        }
                        if (!$cp->get('lastname')) {
                            $errors_lastname[$idx] = 1;
                        }
                        if (!$errors_email[$idx] && !$errors_lastname[$idx]) {
                            $cp->slashesEscape();
                            if ($cp->add()) {
                                unset($contacts_cc_isCustom[$idx]);
                                unset($emails_cc_isCustom[$idx]);
                                $contact_cc = $contacts_cc[$idx] = '' . $cp->get('id');
                                $cc_ok_to_save = true;

                                // write history for the customer
                                require_once PH_MODULES_DIR . 'customers/models/customers.history.php';
                                require_once PH_MODULES_DIR . 'customers/models/customers.audit.php';
                                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                                require_once PH_MODULES_DIR . 'customers/models/customers.contactpersons.factory.php';
                                $this->loadI18NFiles(PH_MODULES_DIR . 'customers/i18n/' . $this->registry['lang'] . '/customers_contactpersons.ini');

                                $old_contact_person = new Customers_Contactperson($this->registry);
                                $old_contact_person->sanitize();

                                $filters = array('where' => array('c.id = ' . $cp->get('id'),
                                                                  'c.subtype = \'contact\''),
                                                 'model_lang' => $this->registry['request']->get('model_lang'),
                                                 'sanitize' => true);
                                $new_contact_person = Customers_Contactpersons::searchOne($this->registry, $filters);

                                $filters_customer =  array('where'      => array('c.id = ' . $this->get('customer')),
                                                           'model_lang' => $this->registry['request']->get('model_lang'),
                                                           'sanitize'   => true);
                                $parent_customer = Customers::searchOne($this->registry, $filters_customer);

                                Customers_History::saveData($this->registry, array('model'               => $parent_customer,
                                                                                   'new_model'           => $new_contact_person,
                                                                                   'old_model'           => $old_contact_person,
                                                                                   'action_type'         => 'add_contact_person',
                                                                                   'contact_person_name' => trim($new_contact_person->get('name') . ' ' . $new_contact_person->get('lastname'))));
                            }
                        }
                    } else {
                        $errors_email[$idx] = $errors_lastname[$idx] = 0;
                        if (!Validator::validEmail($email_cc)) {
                            $errors_email[$idx] = 2;
                            $cc_ok_to_save = false;
                        }
                    }
                    if ($cc_ok_to_save) {
                        $params = array('parent_id' => $this->get('id'),
                                        'relation' => '"cstm"',
                                        'purpose'  => '"' . $purpose . '"',
                                        'customer' => $contact_cc,
                                        'email'    => '"' . $email_cc . '"');
                        $set[] = '(' . implode(', ', $params) . ')';
                    }

                    /*if ($request->isRequested($email . '_isCustom') && $request->get($email . '_isCustom')) {
                        //we have new email for the contact
                        if ($saved != 'all' && $saved != 'email') {
                            if (!Validator::validEmail($request->get($email))) {
                                $this->raiseError('error_invalid_email', '');
                                $db->FailTrans();
                            } else {
                                //update contact emails with the new one
                                $query = 'UPDATE ' . DB_TABLE_CUSTOMERS . "\n" .
                                         'SET email = CONCAT(email, \'\n' . $request->get($email) . '\')' . "\n" .
                                         'WHERE id = ' . $request->get($contact);
                                $db->Execute($query);
                                if (!$db->ErrorMsg()) {
                                    $saved = 'email';
                                }
                            }
                        }
                    } else {
                        $this->set($email, $request->get($email), true);
                    }*/
                } else {
                    //remove empty rows
                    if (count($contacts_cc) > 1) {
                        unset($contacts_cc[$idx]);
                        unset($emails_cc[$idx]);
                        unset($contacts_cc_isCustom[$idx]);
                        unset($emails_cc_isCustom[$idx]);
                    }
                }
            }

            $errors_email = array_values($errors_email);
            $errors_lastname = array_values($errors_lastname);

            $contact_type_label = $this->i18n('contracts_party_' . ($purpose == 'adm' ? 'administrative' : 'financial'));
            $contact_type_label = mb_strtolower($contact_type_label, mb_detect_encoding($contact_type_label));

            foreach ($errors_email as $idx => $error) {
                if ($errors_email[$idx] == 1) {
                    $this->raiseError('error_invalid_email_contact_cstm_cc', $type_email_cc . '|' . $idx, '', array($contact_type_label), 1);
                } elseif ($errors_email[$idx] == 2) {
                    $this->raiseError('error_select_email_contact_cstm_cc', $type_email_cc . '|' . $idx, '', array($contact_type_label), 1);
                }
                if ($errors_lastname[$idx]) {
                    $this->raiseError('error_no_lastname_specified_contact_cstm_cc', $type_contact_cc . '|' . $idx, '', array($contact_type_label), 1);
                }
            }
            if (in_array(1, $errors_email) || in_array(2, $errors_email) || in_array(1, $errors_lastname)) {
                $db->FailTrans();
            }

            //set updated arrays to request (with invalid data for new contact persons)
            $request->set($type_contact_cc . '_isCustom', $contacts_cc_isCustom, 'post', true);
            $request->set($type_email_cc . '_isCustom', $emails_cc_isCustom, 'post', true);
            //properties coming from POST
            $request->set($type_contact_cc, $contacts_cc, 'post', true);
            $request->set($type_email_cc, $emails_cc, 'post', true);

            //set to model
            $data = array();
            foreach ($contacts_cc as $idx => $contact_cc) {
                $data[] = array('customer' => $contact_cc, 'email' => (isset($emails_cc[$idx]) ? $emails_cc[$idx] : ''));
            }
            $this->set('cstm_' . $purpose . '_cc', $data, true);
        }

        //set employees for cc of administrative self contact
        if ($request->get('self_administrative_cc')) {
            $contacts_cc = $request->get('self_administrative_cc');
            $emails_cc = $request->get('self_adm_email_cc');

            $contact_type_label = $this->i18n('contracts_party_administrative');
            $contact_type_label = mb_strtolower($contact_type_label, mb_detect_encoding($contact_type_label));

            foreach ($contacts_cc as $idx => $contact_cc) {
                if ($contact_cc) {
                    //get email for contact
                    $email_cc = isset($emails_cc[$idx]) ? $emails_cc[$idx] : '';
                    if (!Validator::validEmail($email_cc)) {
                        $this->raiseError('error_select_email_contact_self_cc', 'self_adm_email_cc|' . $idx, '', array($contact_type_label), 1);
                        $db->FailTrans();
                    }

                    $params = array('parent_id' => $this->get('id'),
                                    'relation' => '"self"',
                                    'purpose'  => '"adm"',
                                    'customer' => $contact_cc,
                                    'email'    => '"' . $email_cc . '"');
                    $set[] = '(' . implode(', ', $params) . ')';
                } else {
                    //remove empty rows
                    if (count($contacts_cc) > 1) {
                        unset($contacts_cc[$idx]);
                        unset($emails_cc[$idx]);
                    }
                }
            }

            //set to model
            $data = array();
            foreach ($contacts_cc as $idx => $contact_cc) {
                $data[] = array('customer' => $contact_cc, 'email' => (isset($emails_cc[$idx]) ? $emails_cc[$idx] : ''));
            }
            $this->set('self_adm_cc', $data, true);
        }

        //insert all new cc-s
        if ($set) {
            $query_insert_cc = 'INSERT INTO ' . DB_TABLE_CONTRACTS_CONTACTS_CC . ' (parent_id, relation, purpose, customer, email) VALUES ' . "\n" .
            implode(",\n", $set);
            $db->Execute($query_insert_cc);
        }

        $error = $db->HasFailedTrans();
        $db->CompleteTrans();

        return !$error;
    }

    /**
     * Function to get customer administrative contact
     */
    public function getCstmAdministrativeData() {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $data = array();
        if ($this->get('id')) {
            $query = 'SELECT cstm_administrative AS id, cstm_adm_email AS email FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $this->get('id');
            $data = $this->registry['db']->GetRow($query);

            $c = '';
            if ($data['id']) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters = array('where' => array('c.id = ' . $data['id'], 'c.subtype is not null'), 'model_lang' => $this->get('model_lang'));
                $c = Customers::searchOne($this->registry, $filters);
            }

            if ($c && ($c->get('subtype') == 'normal' && !$c->get('is_company') || $c->get('subtype') == 'contact')) {
                //if customer is not company return the data
                $data['name'] = $c->get('name') . ' ' . $c->get('lastname');
                $data['active'] = $c->get('active');
            } else {
                $data = array();
            }
        } else {
            $data = array();
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $data;
    }

    /**
     * Function to get customer financial contact
     */
    public function getCstmFinancialData() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $data = array();
        if ($this->get('id')) {
            $query = 'SELECT cstm_financial AS id, cstm_fin_email AS email FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $this->get('id');
            $data = $this->registry['db']->GetRow($query);

            $c = '';
            if ($data['id']) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters = array('where' => array('c.id = ' . $data['id'], 'c.subtype is not null'), 'model_lang' => $this->get('model_lang'));
                $c = Customers::searchOne($this->registry, $filters);
            }

            if ($c && ($c->get('subtype') == 'normal' && !$c->get('is_company') || $c->get('subtype') == 'contact')) {
                //if customer is not company return the data
                $data['name'] = $c->get('name') . ' ' . $c->get('lastname');
                $data['active'] = $c->get('active');
            } else {
                $data = array();
            }
        } else {
            $data = array();
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $data;
    }

    /**
     * Function to get self administrative contact
     */
    public function getSelfAdministrativeData() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $data = array();
        if ($this->get('id')) {
            $query = 'SELECT self_administrative AS id, self_adm_email AS email FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $this->get('id');
            $data = $this->registry['db']->GetRow($query);

            $c = '';
            if ($data['id']) {
                require_once PH_MODULES_DIR . 'customers/models/customers.factory.php';
                $filters = array('where' => array('c.id = ' . $data['id'], 'c.subtype is not null'), 'model_lang' => $this->get('model_lang'));
                $c = Customers::searchOne($this->registry, $filters);
            }

            if ($c && ($c->get('subtype') == 'normal' && !$c->get('is_company') || $c->get('subtype') == 'contact')) {
                //if customer is not company return the data
                $data['name'] = $c->get('name') . ' ' . $c->get('lastname');
                $data['active'] = $c->get('active');
            } else {
                $data = array();
            }
        } else {
            $data = array();
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $data;
    }

    /**
     * Function to get self financial contact
     *
     * @return array - array with data of self financial contact if found, otherwise an empty array
     */
    public function getSelfFinancialData() {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $data = array();
        if ($this->get('id')) {
            $query = 'SELECT self_financial AS id FROM ' . DB_TABLE_CONTRACTS . ' WHERE id = ' . $this->get('id');
            $data = $this->registry['db']->GetRow($query);

            //if value is placeholder "[default_financial_contact_person]", find user id from variables tables
            if ($data['id'] == '[default_financial_contact_person]') {
                $data['id'] = Finance_Invoices_Templates::getDefaultFinancialContactPerson($this->registry, $this->get('type'), true);
            }

            if (!empty($data['id'])) {
                require_once PH_MODULES_DIR . 'users/models/users.factory.php';
                $filters = array('where' => array('u.id = ' . $data['id']),
                                 'sanitize' => true,
                                 'model_lang' => $this->get('model_lang'));
                $u = Users::searchOne($this->registry, $filters);
                if ($u) {
                    $data['email'] = $u->get('email');
                    $data['name'] = $u->get('firstname') . ' ' . $u->get('lastname');
                    $data['active'] = $u->get('active');
                    $data['invoice_code'] = $u->get('invoice_code');
                } else {
                    $data = array();
                }
            } else {
                $data = array();
            }
        } else {
            $data = array();
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $data;
    }

    /**
     * Gets data for customers to receive copies of mails sent to certain contact of contract
     * and sets data as properties of model.
     *
     * @param string $relation - 'cstm' or 'self' - relation to contract - customer or own company
     * @param string $purpose - 'adm' or 'fin' - purpose of contact - administrative or financial
     * @return bool - result of operation
     */
    public function getContactCc($relation = '', $purpose = '') {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $data = array('cstm_adm_cc' => array(), 'cstm_fin_cc' => array(), 'self_adm_cc' => array());
        $data_empty = array(array('customer' => '', 'email' => ''));

        $query = 'SELECT * FROM ' . DB_TABLE_CONTRACTS_CONTACTS_CC . "\n" .
                 '  WHERE parent_id = \'' . $this->get('id') . '\'';
        if ($relation) {
            $query .= ' AND relation="' . $relation . '"';
        }
        if ($purpose) {
            $query .= ' AND purpose="' . $purpose . '"';
        }
        $records = $db->GetAll($query);

        if ($records) {
            foreach ($records as $record) {
                $data[$record['relation'] . '_' . $record['purpose'] . '_cc'][] =
                    array('customer' => $record['customer'], 'email' => $record['email']);
            }
        }
        //set to model
        foreach ($data as $type_contact_cc => $type_data) {
            $this->set($type_contact_cc, ($data[$type_contact_cc] ? $data[$type_contact_cc] : $data_empty), true);
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return true;
    }

    /**
     * Get data for customers to receive copies of mails sent to certain contact of contract.
     *
     * @param string $relation - 'cstm' or 'self' - relation to contract - customer or own company
     * @param string $purpose - 'adm' or 'fin' - purpose of contact - administrative or financial
     * @return array - data for customers
     */
    public function getContactCcData($relation = '', $purpose = '') {
        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];
        $lang = $this->registry['lang'];

        $data = array();
        $query = 'SELECT c.id, cc.email, CONCAT(ci18n.name, " ", ci18n.lastname) AS name, c.active ' . "\n" .
                 '  FROM ' . DB_TABLE_CONTRACTS_CONTACTS_CC . ' AS cc' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CUSTOMERS . ' AS c ' . "\n" .
                 '  ON cc.customer=c.id AND (c.is_company=0 AND c.subtype="normal" OR c.subtype="contact")' . "\n" .
                 '  LEFT JOIN ' . DB_TABLE_CUSTOMERS_I18N . ' AS ci18n ' . "\n" .
                 '  ON ci18n.parent_id=c.id AND ci18n.lang="' . $lang . '"' . "\n" .
                 '  WHERE cc.parent_id=' . $this->get('id') . ' AND cc.email!=""';
        if ($relation) {
            $query .= ' AND cc.relation="' . $relation . '"';
        }
        if ($purpose) {
            $query .= ' AND cc.purpose="' . $purpose . '"';
        }
        $data = $db->GetAll($query);

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $data;
    }

    /**
     * Gets invoices/proformas issued for a contract
     *
     * @param array $params - filtering parameters
     * @return Finance_Incomes_Reason[] - found models
     */
    public function getIssuedFinance($params = array()) {

        $sanitize_after = false;
        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        $db = $this->registry['db'];

        $query = 'SELECT fiti.invoice_id, fiti.`from`, fiti.`to`, fit.recurrent, fit.id as template_id, fit.type as template_type, fiti.advanced' . "\n" .
                 'FROM ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES_INFO . ' AS fiti' . "\n" .
                 'JOIN ' . DB_TABLE_FINANCE_INVOICES_TEMPLATES . ' AS fit' . "\n" .
                 '    ON fit.id = fiti.parent_id' . "\n" .
                 'WHERE fiti.invoice_id > 0 ' . "\n" .
                 '  AND fiti.contract_id = ' . $this->get('id') . "\n";

        if (!empty($params['from']) || !empty($params['to'])) {
            if (!empty($params['force_not_recurrent'])) {
                $query .= 'AND (recurrent = 0 OR ';
            } else {
                $query .= ' AND ';
            }
        }
        //get invoices in defined period
        if (!empty($params['from'])) {
            $query .= sprintf('`to` >= "%s"', $params['from']) . "\n";
            if (!empty($params['to'])) {
                $query .= ' AND ';
            }
        }
        if (!empty($params['to'])) {
            $query .= sprintf('`from` <= "%s"', $params['to']) . "\n";
        }
        if ((!empty($params['from']) || !empty($params['to'])) && !empty($params['force_not_recurrent'])) {
            $query .= ')';
        }
        if (empty($params['from']) && empty($params['to']) && !empty($params['force_not_recurrent'])) {
            $query .= ' AND recurrent = 0';
        }

        if (!empty($params['exclude_advanced'])) {
            $query .= sprintf('  AND advanced NOT IN ("%s")', implode('", "', $params['exclude_advanced'])) . "\n";
        }
        $query .= 'ORDER BY `from`, `to`';
        $records = $db->GetAssoc($query);
        $result = array();
        if (!empty($records)) {
            $filters = array('where' => array('fir.id IN (' . implode(', ', array_keys($records)) . ')'),
                             'sort' => array('find_in_set(fir.id, "' . @implode(',',array_keys($records)) . '")'));
            require_once PH_MODULES_DIR . 'finance/models/finance.incomes_reasons.factory.php';
            $models = Finance_Incomes_Reasons::search($this->registry, $filters);

            foreach ($models as $k => $v) {
                $v->set('date_from', $records[$v->get('id')]['from'], true);
                $v->set('date_to', $records[$v->get('id')]['to'], true);
                $v->set('recurrent', $records[$v->get('id')]['recurrent'], true);
                $v->set('template_id', $records[$v->get('id')]['template_id'], true);
                $v->set('template_type', $records[$v->get('id')]['template_type'], true);
                $v->set('advanced', $records[$v->get('id')]['advanced'], true);
                $result[$v->get('id')] = $v;
            }
        }

        if ($sanitize_after) {
            $this->sanitize();
        }

        return $result;
    }

    /**
     * Gets the model type name from the database
     *
     * @return string - Name of type of model
     */
    public function getModelTypeName() {
        if (!$this->get('type_name')) {
            if (!$this->get('type')) {
                return '';
            }

            $sanitize_after = false;
            if ($this->sanitized) {
                $this->unsanitize();
                $sanitize_after = true;
            }

            $query = 'SELECT name FROM ' . DB_TABLE_CONTRACTS_TYPES_I18N . "\n" .
                     'WHERE parent_id=' . $this->get('type') . ' AND lang="' . $this->get('model_lang') . '"';
            $type_name = $this->registry['db']->GetOne($query);
            $this->set('type_name', $type_name, true);

            if ($sanitize_after) {
                $this->sanitize();
            }
        }

        return $this->get('type_name');
    }

    /**
     * Get contract timeline, e.g. which agreement is in force when
     *
     * @param array $params - 'from' and 'to' dates to search for
     * @return array - start and end date, id and subtype for each period
     */
    public function getTimeline($params) {

        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }
        $db = $this->registry['db'];
        //get all the agreements for the contract with their periods
        $query = 'SELECT id, subtype,' . "\n" .
                 'IF (subtype = "annex", date_start_subtype, date_start) as `from`,' . "\n" .
                 'IF (subtype = "annex", IF(date_end_subtype, date_end_subtype, date_validity), date_validity) as `to`' . "\n" .
                 'FROM ' . DB_TABLE_CONTRACTS . "\n" .
                 'WHERE parent_record = ' . $this->get('id') . ' AND (subtype_status IN ("started", "executed") OR subtype = "original")' . "\n" .
                 '  AND annulled_by = 0' . "\n" .
                 'ORDER BY id ASC';
        $agreements = $db->GetAssoc($query);

        if (!empty($params['use_end_date'])) {
            foreach ($agreements as $k => $agr) {
                if (!$agr['to']) {
                    $agreements[$k]['to'] = $params['use_end_date'];
                }
            }
        }

        $timeline = array();
        foreach ($agreements as $k => $v) {
            //check if the agreement(or part of it) is into the requested period
            if (!empty($params['from']) && $params['from'] > $v['to']) {
                continue;
            }
            if (!empty($params['to']) && $params['to'] < $v['from']) {
                continue;
            }
            if ($v['from'] > $v['to']) {
                continue;
            }
            if (empty($timeline)) {
                $timeline[] = array(
                    'from' => $v['from'],
                    'to' => $v['to'],
                    'agreement' => $k,
                    'subtype' => $v['subtype'],
                );
                continue;
            }
            $tmp = array();
            again:
            $iterate_again = false;
            foreach ($timeline as $idx => $data) {
                $tmp = array(
                    'from' => $v['from'],
                    'to' => $v['to'],
                    'agreement' => $k,
                    'subtype' => $v['subtype'],
                );
                if ($v['from'] <= $data['from']) {
                    if ($v['to'] < $data['from']) {
                        //agreement before the current one
                        array_unshift($tmp, $timeline);
                        continue 2;
                    } elseif ($v['to'] >= $data['to']) {
                        //agreement ends after or with the current
                        $tmp['to'] = $data['to'];
                        $timeline[$idx] = $tmp;
                        $v['from'] = date_add(date_create($data['to']), new DateInterval('P1D'))->format('Y-m-d');
                        if ($v['from'] > $v['to']) {
                            continue 2;
                        }
                    } else {
                        //current agreement ends before the agreement in the timeline
                        //change TL agreement start date
                        $timeline[$idx]['from'] = date_add(date_create($v['to']), new DateInterval('P1D'))->format('Y-m-d');
                        //inject the current agreement into the TL
                        array_splice($timeline, $idx, count($timeline), array_merge(array($tmp), array_slice($timeline, $idx)));
                        continue 2;
                    }
                } else {
                    if ($v['from'] > $data['to']) {
                        if (empty($timeline[$idx + 1])) {
                            array_push($timeline, $tmp);
                        } else {
                            continue;
                        }
                    }
                    //current agreement starts after the agreement in the timeline
                    //change TL agreement end date
                    $timeline[$idx]['to'] = date_sub(date_create($v['from']), new DateInterval('P1D'))->format('Y-m-d');
                    if ($v['to'] < $data['to']) {
                        //agreement ends before the current
                        $data['from'] = date_add(date_create($v['to']), new DateInterval('P1D'))->format('Y-m-d');
                        array_splice($timeline, $idx + 1, count($timeline), array_merge(array($tmp, $data), array_slice($timeline, $idx + 1)));
                        continue 2;
                    } else {
                        if (!empty($timeline[$idx + 1])) {
                            $tmp['to'] = $data['to'];
                            $v['from'] = date_add(date_create($data['to']), new DateInterval('P1D'))->format('Y-m-d');
                        }
                        array_splice($timeline, $idx + 1, count($timeline), array_merge(array($tmp), array_slice($timeline, $idx + 1)));
                        if ($v['from'] > $v['to']) {
                            continue 2;
                        }
                        goto again;
                    }
                }
            }
        }
        $tmp_timeline = $timeline;
        $timeline = array();
        $current = false;

        //merge periods for one and the same agreement
        //and clean periods we don't need
        foreach ($tmp_timeline as $idx => $data) {
            if (!empty($params['from'])) {
                if ($params['from'] > $data['to']) {
                    continue;
                } elseif ($params['from'] > $data['from']) {
                    $data['from'] = $params['from'];
                }
            }
            if (!empty($params['to'])) {
                if ($params['to'] < $data['from']) {
                    continue;
                } elseif ($params['to'] < $data['to']) {
                    $data['to'] = $params['to'];
                }
            }
            if ($data['agreement'] == $current) {
                $timeline[count($timeline) - 1]['to'] = $data['to'];
            } else {
                $timeline[] = $data;
                $current = $data['agreement'];
            }
        }

        return $timeline;
    }

    /**
     * Function for taking the child contracts from first level
     * It is used from custom outlooks and front page dashlets.
     *
     * @param string $relation - relation type may be 'child' or 'parent'
     * @return array - all related contracts from first level
     */
    public function getFirstLevelRelatedContracts($relation) {
        $children_tree = array();
        $this->unsanitize();

        // retrieve all children from first level
        $query = 'SELECT ' . (($relation == 'child') ? ('dr.parent_id') : ('dr.link_to')) . ' as related_to, dr.origin ' . "\n" .
                'FROM ' . DB_TABLE_CONTRACTS_RELATIVES . ' AS dr ' . "\n" .
                'WHERE ' . (($relation == 'child') ? ('link_to') : ('parent_id')) . '="' . $this->get('id') . '"';

        $records = $this->registry['db']->GetAll($query);

        $related_contract_ids = array();

        foreach ($records as $record) {
            if (!in_array($record['related_to'], $related_contract_ids)) {
                $related_contract_ids[] = $record['related_to'];
            }
        }

        $contract_relations = array();
        if (!empty($related_contract_ids)) {
            $sql = array();
            $sql['select'] = 'SELECT co.id, coi18n.name ' . "\n";
            $sql['from']   = 'FROM ' . DB_TABLE_CONTRACTS . ' AS co' . "\n" .
                    'LEFT JOIN ' . DB_TABLE_CONTRACTS_I18N . ' AS coi18n' . "\n" .
                    '  ON (coi18n.parent_id=co.id AND coi18n.lang="' . $this->get('model_lang') . '")' . "\n";
            $where = array();
            $where[] = 'co.id IN (' . implode(', ', $related_contract_ids) . ')';
            $where[] = 'co.deleted IS NOT NULL';

            $sql['where'] = 'WHERE ' . implode(' AND ', $where);

            //search basic details with current lang parameters
            $query = implode("\n", $sql);
            $contract_relations = $this->registry['db']->GetAll($query);
        }
        $this->sanitize();

        return $contract_relations;
    }

    /*
     * UTF-8 version of ord
     */
    public function uniord($c) {
        $h = ord($c[0]);
        if ($h <= 0x7F) {
            return $h;
        } else if ($h < 0xC2) {
            return false;
        } else if ($h <= 0xDF) {
            return ($h & 0x1F) << 6 | (ord($c[1]) & 0x3F);
        } else if ($h <= 0xEF) {
            return ($h & 0x0F) << 12 | (ord($c[1]) & 0x3F) << 6
                                     | (ord($c[2]) & 0x3F);
        } else if ($h <= 0xF4) {
            return ($h & 0x0F) << 18 | (ord($c[1]) & 0x3F) << 12
                                     | (ord($c[2]) & 0x3F) << 6
                                     | (ord($c[3]) & 0x3F);
        } else {
            return false;
        }
    }

    public function prepareDifferenceView($result) {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset(PH_MODULES_DIR . 'contracts/templates/_preview_new_agreement_cd.html');
        require_once PH_MODULES_DIR . 'nomenclatures/models/nomenclatures.dropdown.php';
        $viewer->data['cd_reasons'] = Nomenclatures_Dropdown::getCDReasons(array($this->registry));
        $viewer->data['agreement_subtype'] = $this->get('subtype');
        $viewer->data['model_id'] = $this->get('id');
        $viewer->data['diff_options'] = Contracts_Dropdown::getDiffOptions(array($this->registry));
        if ($this->registry['request']->get('annul') == 'annul') {
            $viewer->data['annul'] = 1;
        }
        if (!empty($result)) {
            $viewer->data['result'] = $result;
            if (!empty($result['old_invoices'])) {
                //we have to get patterns for generation and emails
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_CREDIT_NOTICE, 'company'=> $this->get('company'));
                $viewer->data['credit_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
                $viewer->data['credit_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);

                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_DEBIT_NOTICE, 'company'=> $this->get('company'));
                $viewer->data['debit_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['debit_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
            }
            if (!empty($result['new_invoices'])) {
                //we have to get patterns for generation and emails
                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_INVOICE, 'company'=> $this->get('company'));
                $viewer->data['invoice_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
                $viewer->data['invoice_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);

                $params = array($this->registry, 'model_name' => 'Finance_Incomes_Reason', 'model_type' => PH_FINANCE_TYPE_PRO_INVOICE, 'company'=> $this->get('company'));
                $viewer->data['proforma_emails'] = Contracts_Dropdown::getTemplateEmailsPatterns($params);
                $viewer->data['proforma_patterns'] = Contracts_Dropdown::getTemplatePatterns($params);
            }
        } else {
            $this->registry['session']->remove('into_force', true);
            $this->registry['session']->remove('date_end_subtype', true);
        }
        return $viewer;
    }
}

?>
