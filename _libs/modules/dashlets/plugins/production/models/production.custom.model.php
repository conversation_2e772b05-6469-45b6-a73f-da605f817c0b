<?php

/**
 * Production plugin custom model class
 *
 * @category Production
 * @package Production
 */
class Production_Custom_Model extends Model {
    /**
     * Name of model
     * @var string
     */
    public $modelName = 'Dashlet';

    /**
     * Name of plugin - same as plugin folder
     * @var string
     */
    public $plugin_name;

    /**
     * Prefix added to all config fields in setup interface of dashlet
     * @var string
     */
    CONST CONFIG_PREFIX = 'config_';

    /**
     * Reference to Messages object in registry
     * @var Messages
     */
    protected $messages;

    /**
     * Reference to Request object in registry
     * @var Request
     */
    protected $request;

    /**
     * ID of "Num." measure that exists by default in all installations
     * @var int
     */
    const MEASURE_NUM = 1;

    /**
     * Dashlet plugin constructor
     *
     * @param string $dashlet_settings - dashlets plugin settings
     * @return boolean - result of the operation
     */
    public function __construct($dashlet_settings = '') {

        require_once 'production.autoloader.php';

        if (!isset($this->registry)) {
            $this->registry = $GLOBALS['registry'];
        }

        $this->messages = &$this->registry['messages'];
        $this->request = &$this->registry['request'];

        parent::__construct($this->registry);

        // plugin name matches plugin folder name
        $pn = explode(DIRECTORY_SEPARATOR, realpath(dirname(__FILE__)));
        $this->plugin_name = $pn[count($pn)-2];

        if ($dashlet_settings) {
            $this->parseDashletPluginSettings($dashlet_settings);
        }

        $dashlet = $this->registry['dashlet'] ?:
            // if requested url does not contain dashlet id, get dashlet model
            // (there should be no more than one in the installation)
            Production_Custom_Factory::getDashlet($this->registry);

        // set data from dashlet model into plugin model
        if ($dashlet && !$dashlet->get('deleted_by')) {
            if ($dashlet->get('filters')) {
                foreach ($dashlet->get('filters') as $k => $v) {
                    $this->set($k, $v, true);
                }
            }
            $this->set('dashlet_id', $dashlet->get('id'), true);
        } elseif (!($this->request->get('module_name') == 'plugin_' . $this->plugin_name)) {
            echo $this->prepareMessages(
                array($this->i18n('error_plugin_production_module')),
                'error'
            );
            exit;
        }

        return true;
    }

    /**
     * Process dashlet-specific data before save and set it into 'filters'
     * property of dashlet
     *
     * @param Dashlet $model - dashlet model for current plugin
     */
    public function prepareCustomData(Dashlet &$model) {
        // prepare custom filters
        $filters = array();

        // save dashlet configuration settings without the self::CONFIG_PREFIX prefix
        $prefix_regex = '#^' . preg_quote(self::CONFIG_PREFIX, '#') . '(.*)$#';
        $matches = array();
        foreach ($model->getAll() as $key => $value) {
            if (preg_match($prefix_regex, $key, $matches)) {
                $filters[$matches[1]] = $value;
            }
        }

        // do not set to model on translate
        if ($filters) {
            $model->set('filters', $filters, true);
        }
    }

    /**
     * Validate plugin-specific data before save of dashlet
     *
     * @param Dashlet $model - dashlet model for current plugin
     */
    public function validateCustomData(Dashlet &$model) {
        // do not validate
        if ($this->registry['action'] == 'translate') {
            // IMPORTANT: filters should not be modifed
            $model->unsetProperty('filters', true);
            return true;
        }

        // do not allow more than one dashlet for plugin
        if (!$model->get('id') && $dashlet = Production_Custom_Factory::getDashlet($this->registry)) {
            $model->raiseError('error_plugin_single_dashlet');
            $this->messages->insertInSession($this->registry);

            $redirectUrl = sprintf('%s?%s=%s&%s=%s&%s=%s&%s=%s',
                $_SERVER['PHP_SELF'],
                $this->registry['module_param'], $this->registry['module'],
                $this->registry['controller_param'], $this->registry['controller'],
                $this->registry['controller'], 'edit',
                'edit', $dashlet->get('id')
            );
            header('Location: ' . $redirectUrl);
            exit;
        }

        $plugin_fields = $this->getPluginFields($model);
        foreach ($plugin_fields as $k => $var) {
            //check whether required field lacks value
            $valid_var = true;
            if ($var['required'] != 0 && empty($var['hidden'])) {
                if ($var['type'] == 'file_upload') {
                    if (!Validator::notEmptyFile($var['value'])) {
                        $valid_var = false;
                    }
                } elseif ($var['required'] == 1 && !Validator::notEmpty($var['value'])) {
                    //does not allow zero
                    $valid_var = false;
                } elseif ($var['required'] == 2 && !Validator::notEmpty($var['value'], true)) {
                    //allows zero
                    $valid_var = false;
                }
            }
            if (!$valid_var) {
                $model->raiseError('error_empty_field', $var['name'], 0, array('var_label' => $var['label']));
            }
        }

        // check values of progress percentage settings
        $condition_msg = array();
        if ($model->get(self::CONFIG_PREFIX . 'progress_late') >= $model->get(self::CONFIG_PREFIX . 'progress_normal')) {
            $condition_msg[] = sprintf('%% %s < %% %s', $this->i18n('plugin_late'), $this->i18n('plugin_normal'));
        }
        if ($model->get(self::CONFIG_PREFIX . 'progress_normal') >= $model->get(self::CONFIG_PREFIX . 'progress_early')) {
            $condition_msg[] = sprintf('%% %s < %% %s', $this->i18n('plugin_normal'), $this->i18n('plugin_early'));
        }
        if ($condition_msg) {
            $model->raiseError('error_compareVar', 'progress', 0, array('conditions' => implode(', ', $condition_msg)));
        }

        if ($model->valid) {
            /**
             * IMPORTANT: save settings into db ('production' section)
             * @see Config::setInt
             * @see Config::setString
             */
            $this->registry['db']->StartTrans();
            foreach ($model->get('filters') as $param => $value) {
                $method = preg_match('#^\d*$#', $value) ? 'setInt' : 'setString';
                if (!method_exists($this->registry['config'], $method) ||
                !$this->registry['config']->$method($this->plugin_name, $param, $value, '', 'db')) {
                    $model->valid = false;
                    $this->registry['db']->FailTrans();
                    break;
                }
            }
            $this->registry['db']->CompleteTrans();
        }
    }

    /**
     * Dashlet-specific data is expanded from 'filters' property of dashlet
     * model and set back into it
     *
     * @param Dashlet $model - dashlet model for current plugin
     */
    public function expandCustomData(Dashlet &$model) {
        if ($model->get('filters')) {
            foreach ($model->get('filters') as $k => $v) {
                $model->set(self::CONFIG_PREFIX . $k, $v, true);
            }
            $model->unsetProperty('filters', true);
        }
    }

    /**
     * Gets plugin fields (settings defined in dashlet) and prepares them as
     * additional variables for setup (add/edit action) of dashlet in interface
     *
     * @param Dashlet $model - dashlet model for current plugin
     * @param string $regexp - optional regular expression for filtering
     * @return array - plugin fields
     */
    public function getPluginFields(Dashlet $model, $regexp = '') {
        $config = require 'config.php';
        $plugin_fields = array();

        if ($this->sanitized) {
            $this->unsanitize();
            $sanitize_after = true;
        }

        // defaults for missing common settings
        $default_settings = array(
            'type' => 'dropdown',   // default type if not specified
            'bb' => 0,
            'gt2' => 0,
            'grouping' => 0,
            'configurator' => 0,
            'hidden' => 0,
            'required' => 1,
            'js_methods' => array(),
            'js_filter' => '',
            'custom_class' => '',
            'back_label' => ' ',    // non-existent label
        );

        $restrict =
            $this->registry['dashlet'] && $this->registry['dashlet']->get('id') &&
            Production_Custom_Factory::isProductionInProgress($this->registry);

        // collect settings options per model
        $settings_options = array();

        foreach ($config as $name => $setting) {
            if ($regexp && !preg_match($regexp, $name)) {
                continue;
            }

            $var_name = self::CONFIG_PREFIX . $name;

            // specific settings that cannot be represented as standard fields
            // will be custom-handled in the template, so don't process them
            if (!empty($setting['custom'])) {
                // set default value as current value
                if (isset($setting['value']) && !$model->isDefined($var_name)) {
                    $model->set($var_name, $setting['value'], true);
                }
                continue;
            }

            $setting = $setting + $default_settings;
            if (empty($setting['help'])) {
                $setting['help'] = 'help_plugin_' . $name;
            }

            $setting['readonly'] = $restrict && preg_match('#company|currency|type|status|calc_price#', $name);

            $contains_optgroups = false;
            if ($setting['type'] == 'dropdown') {
                if (isset($setting['options'])) {
                    $setting['model'] = $name;
                    $settings_options[$setting['model']] = $setting['options'];
                }
                if (empty($setting['model'])) {
                    continue;
                }

                if (!isset($settings_options[$setting['model']])) {
                    $dropdown = 'Dropdown';
                    $params = array($this->registry);

                    if (!empty($setting['method'])) {
                        $method = $setting['method'];
                        if (preg_match('#::#', $method)) {
                            list($dropdown, $method) = explode('::', $method);
                        }
                    } elseif (method_exists($dropdown, 'get' . General::singular2plural($setting['model']) . 'Types')) {
                        $method = 'get' . General::singular2plural($setting['model']) . 'Types';
                    } else {
                        $method = 'getCustomDropdown';
                        $params['table'] = 'DB_TABLE_' . strtoupper(General::singular2plural($setting['model']));
                        if (defined($params['table'] . '_I18N')) {
                            $params['table_i18n'] = $params['table'] . '_I18N';
                        }
                    }
                    if (!empty($setting['params'])) {
                        $params = $params + $setting['params'];
                    }
                    $settings_options[$setting['model']] = $dropdown::$method($params);
                }

                if (isset($settings_options[$setting['model']]['contain_optgroups'])) {
                    $contains_optgroups = $settings_options[$setting['model']]['contain_optgroups'];
                    unset($settings_options[$setting['model']]['contain_optgroups']);
                } elseif (!empty($settings_options[$setting['model']])) {
                    $opt = reset($settings_options[$setting['model']]);
                    if ($opt) {
                        $opt = reset($opt);
                        $contains_optgroups = is_array($opt);
                    }
                }
            }

            $plugin_fields[$var_name] = array(
                'name'          => $var_name,
                'options'       => $setting['type'] == 'dropdown' ? ($contains_optgroups ? null : $settings_options[$setting['model']]) : null,
                'optgroups'     => $setting['type'] == 'dropdown' ? ($contains_optgroups ? $settings_options[$setting['model']] : null) : null,
                'autocomplete'  => !empty($setting['autocomplete']) ? $setting['autocomplete'] : null,
                'label'         => $this->i18n('plugin_' . $name),
                'help'          => $this->i18n($setting['help']),
                'back_label'    => $this->i18n($setting['back_label']),
                'value'         => $model->get($var_name),
            ) + $setting;

            if ($setting['type'] == 'dropdown' && !empty($setting['statuses'])) {
                if (!isset($settings_options[$setting['model'] . '_' . $name])) {
                    $settings_options[$setting['model'] . '_' . $name] = Production_Custom_Factory::getStatuses($this->registry, array(
                        'model' => $setting['model'],
                        'model_types' => $model->get($var_name) ? array($model->get($var_name)) : false
                    ));
                }
                foreach ($setting['statuses'] as $status => $default_value) {
                    $var_name_status = $var_name . '_' . $status;
                    $plugin_fields[$var_name_status] = array(
                        'type'          => 'dropdown',
                        'name'          => $var_name_status,
                        'options'       => $settings_options[$setting['model'] . '_' . $name][$default_value],
                        'required'      => true,
                        'label'         => $this->i18n('plugin_' . $name . '_' . $status),
                        'help'          => $this->i18n('help_plugin_' . $name . '_' . $status),
                        'value'         => $model->get($var_name_status) ?: $default_value,
                        'custom_class'  => $var_name . ' ' . $default_value,
                    );
                }
            }
        }

        if (!empty($sanitize_after)) {
            $this->sanitize();
        }

        return $plugin_fields;
    }

    /**
     * Parse settings for current plugin and set them as properties to model
     *
     * @param string $dashlet_settings - settings field of dashlet
     */
    public function parseDashletPluginSettings($dashlet_settings) {

        $settings_rows = preg_split('/(\r\n|\n|\r)/', $dashlet_settings);

        foreach ($settings_rows as $s_row) {
            if (empty($s_row) || $s_row[0] == '#') {
                continue;
            }
            list($key, $value) = preg_split('/\s*\:=\s*/', $s_row);
            $value = trim($value);

            // set them as properties of model in the usual way
            $this->set($key, $value, true);
        }
    }

    /**
     * Prepare data for display in interface (index page) and set it into model
     *
     * @param Dashlet $dashlet - dashlet model that is assigned to viewer
     */
    public function prepareDashletData(Dashlet $dashlet) {

        // flag whether current user has access to any part of the dashlet
        $no_access = true;
        /** @var User $user */
        $user = &$this->registry['currentUser'];

        // get started production schedules available to current user
        $schedules_search_allowed = $user->checkRights('documents' . $this->get('type_production_schedule'), 'search');
        $schedules_progress = $schedules_search_allowed ? $this->getSchedules() : array();

        // 1. Manage production:
        // if there are started production schedules and current user can add daily timesheets
        if ($schedules_progress && $user->checkRights('documents' . $this->get('type_production_timesheet'), 'add')) {

            // date constraint
            $dashlet->set(
                'disallow_date_before',
                date_sub(
                    date_create(),
                    new DateInterval(sprintf('P%dD', $this->get('production_timesheet_days_before')))
                )->format('Y-m-d'),
                true
            );

            // get route cards of started production schedules, then find all possible workplaces according to them
            $route_card_ids = array();
            foreach ($schedules_progress as $schedule) {
                if ($schedule->get('transform_num') && !in_array($schedule->get('transform_num'), $route_card_ids)) {
                    $route_card_ids[] = $schedule->get('transform_num');
                }
            }
            if ($route_card_ids) {
                $route_card_ids = Production_Custom_Factory::search(
                    $this->registry,
                    array(
                        'action' => 'custom',
                        'filters' => array(
                            'where' => array(
                                "d.type = '{$this->get('type_route_card')}'",
                                "d.id IN ('" . implode("', '", $route_card_ids) . "')",
                            ),
                            'sanitize' => true,
                        ),
                        'model_name' => 'Route_Card',
                    )
                );
            }
            $type_workplace_ids = array();
            $route_card_filter = "a__type_workplace IN ('%s')";
            if ($route_card_ids) {
                // field in route card GT to get ids to filter by from
                $type_workplace_var = 'card_type_workplace';
                // if route card GT has field for workplace id, we will filter by its values
                if ($route_card_ids[0]->getGroupingVarValues(array('card_workplace_id'), true)) {
                    $type_workplace_var = 'card_workplace_id';
                    $route_card_filter = "n.id IN ('%s')";
                }
                foreach ($route_card_ids as $route_card) {
                    // get all values for types of working place from card
                    $route_card_values = $route_card->getGroupingVarValues(array($type_workplace_var), false);
                    $route_card_values = array_map(
                        function($a) use ($type_workplace_var) {
                            return $a[$type_workplace_var];
                        },
                        $route_card_values
                    );
                    $type_workplace_ids = array_unique(array_merge($type_workplace_ids, $route_card_values));
                }
            }
            $route_card_filter = sprintf($route_card_filter, implode("', '", $type_workplace_ids));

            // available workplaces
            $dashlet->set(
                'workplace_options',
                Production_Custom_Factory::search($this->registry, array(
                    'module' => 'nomenclatures',
                    'controller' => 'nomenclatures',
                    'action' => 'list',
                    'options' => 1,
                    'filters' => array(
                        'where' => array(
                            "n.type = '{$this->get('type_workplace')}'",
                            'n.active = 1',
                            $route_card_filter,
                        ),
                        'sort' => array('ni18n.name ASC'),
                    ),
                )),
                true
            );
            // get default workplace from personal settings
            $ps = $user->getPersonalSettings('dashlets_plugins', 'dashlet_' . $this->get('dashlet_id'));
            if ($ps) {
                $ps = unserialize($ps);
                if (!empty($ps['default_workplace_id'])) {
                    $dashlet->set('workplace_id', $ps['default_workplace_id'], true);
                }
            }

            // all workshifts
            $dashlet->set(
                'workshift_options',
                Dropdown::getCustomDropdown(array(
                    $this->registry,
                    'table' => 'DB_TABLE_NOMENCLATURES',
                    'table_i18n' => 'DB_TABLE_NOMENCLATURES_I18N',
                    'where' => 't.type = \'' . $this->get('type_workshift') . '\'',
                )),
                true
            );

            if ($dashlet->get('workplace_options') && $dashlet->get('workshift_options')) {
                // 'manage' part should be displayed
                $dashlet->set('manage', true, true);
                $no_access = false;
            }
        }

        // 2.1. Start production:
        // if current user can search and start production schedules
        if ($schedules_search_allowed && $user->checkRights('documents', 'setstatus')) {

            // get unstarted production schedules available to current user
            $schedules_planning = $this->getSchedules('planning');

            $measures = Dropdown::getMeasures(array($this->registry));
            $deadline_label = $this->i18n('plugin_deadline');
            $schedule_label = $this->i18n('plugin_production_schedule_short');

            // prepare models as dropdown options
            array_walk($schedules_planning, function(&$opt) use ($measures, $deadline_label, $schedule_label) {
                /** @var Production_Schedule $opt */
                $opt->unsanitize();
                if (!$opt->checkPermissions('setstatus')) {
                    $opt = null;
                    return;
                }
                $plain_vars = $opt->getFields();
                foreach ($plain_vars as $idx => $var) {
                    unset($plain_vars[$idx]);
                    if ($var['name'] == 'product_measure') {
                        foreach ($measures as $m) {
                            if ($m['option_value'] == $var['value']) {
                                $var['value'] = $m['label'];
                                break;
                            }
                        }
                    }
                    $plain_vars[$var['name']] = $var['value'];
                }
                $plain_vars = $plain_vars + array_fill_keys(array('product_name', 'total_quantity', 'product_measure'), '');
                $opt->sanitize();
                $opt = array(
                    'option_value' => $opt->get('id'),
                    'label' => sprintf(
                        '%s %s/%s %s (%s %s), %s: %s',
                        $schedule_label,
                        $opt->get('full_num'),
                        General::strftime('%d.%m.%Y', $opt->get('added')),
                        $plain_vars['product_name'],
                        $plain_vars['total_quantity'],
                        $plain_vars['product_measure'],
                        $deadline_label,
                        General::strftime('%d.%m.%Y', $opt->get('deadline'))
                    ),
                    'active_option' => $opt->get('active'),
                    // if schedule is expired, highlight option
                    'class_name' => ($opt->get('deadline') < General::strftime('%Y-%m-%d') ? 'attention' : '')
                );
            });
            $schedules_planning = array_filter($schedules_planning);
            if ($schedules_planning) {
                $dashlet->set('start_schedule_options', $schedules_planning, true);
                // 'start' part should be displayed
                $dashlet->set('start', true, true);
            }
            $no_access = false;
        }

        // 2.2. Create production:
        // if current user can search production requests and create production schedules
        if ($user->checkRights('documents' . $this->get('type_production_request'), 'search') &&
        $user->checkRights('documents' . $this->get('type_production_schedule'), 'add')) {

            $requests = $this->getReadyRequests();

            // get unique products from found requests
            $request_products = array();
            foreach ($requests as $request) {
                $request->unsanitize();
                $product_id = $request->getFields('product_id');
                if ($product_id) {
                    $product_id = reset($product_id);
                }
                if (!empty($product_id['value']) && !in_array($product_id['value'], $request_products)) {
                    $request_products[] = $product_id['value'];
                }
                $request->sanitize();
            }

            // check that products have route card for their production
            if ($request_products) {
                $route_cards = $this->getRouteCards($request_products);

                $request_products = array();
                foreach ($route_cards as $route_card) {
                    $product_id = $route_card->getPlainVarValue('product_id');
                    if (!isset($request_products[$product_id])) {
                        $request_products[$product_id] = 0;
                    }
                    $request_products[$product_id]++;
                }
                // keep only products that have exactly one route card
                $request_products = array_keys(array_filter($request_products,
                    function($a) { return $a == 1; }));
            }

            // prepare products as optgroups, group by type
            if ($request_products) {
                // allowed types of nomenclatures
                $product_types = array($this->get('type_product'), $this->get('type_semiproduct'));

                $request_products = Dropdown::getCustomDropdown(array(
                    $this->registry,
                    'table' => 'DB_TABLE_NOMENCLATURES',
                    'table_i18n' => 'DB_TABLE_NOMENCLATURES_I18N',
                    'where' => 't.type IN (\'' . implode('\', \'' , $product_types) . '\')' .
                        ' AND t.id IN (\'' . implode('\', \'', $request_products) . '\')',
                    'class_name' => 't.type'
                ));
                $type_names = Dropdown::getCustomDropdown(array(
                    $this->registry,
                    'table' => 'DB_TABLE_NOMENCLATURES_TYPES',
                    'table_i18n' => 'DB_TABLE_NOMENCLATURES_TYPES_I18N',
                    'where' => 't.active=1 AND t.deleted=0 AND t.id IN (\'' . implode('\', \'', $product_types) . '\')',
                    'order_by' => 'FIND_IN_SET(id, \'' . implode(',', $product_types) . '\')',
                    'label' => 'ti18n.name_plural',
                    'assoc' => 1
                ));

                // prepare optgroup titles
                $request_products = $request_products +
                    array_combine(
                        array_keys(array_flip(array_map(function(&$a) { return $a['label']; }, $type_names))),
                        array_fill(0, count($type_names), array())
                    );

                foreach ($request_products as $idx => $request) {
                    if (!isset($request['class_name']) || !isset($type_names[$request['class_name']]['label'])) {
                        // remove redundant data
                        if (empty($request_products[$idx]) || isset($request['class_name'])) {
                            unset($request_products[$idx]);
                        }
                        continue;
                    }
                    unset($request_products[$idx]);
                    $request_products[$type_names[$request['class_name']]['label']][] = $request;
                }
            }

            if ($request_products) {
                $dashlet->set('create_schedule_options', $request_products, true);
                // 'create' part should be displayed
                $dashlet->set('create', true, true);
            }
            $no_access = false;
        }

        // 3. Review (and finish/terminate) production:
        // also: add partial transfers of materials for started productions into their production warehouse
        // if current user can review started production schedules
        if ($schedules_progress) {
            foreach ($schedules_progress as $idx => &$schedule) {
                // get quantities and define progress
                $schedule->getProgress();
            }
            unset($schedule);

            $dashlet->set('review_schedules', $schedules_progress, true);
            // 'review' part should be displayed
            $dashlet->set('review', true, true);
            $no_access = false;
        }

        if ($no_access) {
            $dashlet->set('no_access', $no_access, true);
        }

        // data that will be set into production javascript opject
        $dashlet->set('data', array(
            'measure_num' => self::MEASURE_NUM,
        ), true);
    }

    /************************** private methods ******************************/

    /**
     * Searches for valid route cards for specified products
     *
     * @param array|int $product_ids - one or more ids of products
     * @return Route_Card[] - models of type Route_Card, there should be one per product
     */
    public function getRouteCards($product_ids) {
        if (!is_array($product_ids)) {
            $product_ids = array($product_ids);
        }
        return Production_Custom_Factory::search($this->registry, array(
            'action' => 'custom',
            'filters' => array(
                'where' => array(
                    'd.type = \'' . $this->get('type_route_card') . '\'',
                    'd.active = 1',
                    'd.status != \'closed\'',
                    'a__product_id IN (\'' . implode('\', \'', $product_ids) . '\')',
                ),
                'sort' => array(
                    'd.id ASC',
                ),
            ),
            'model_name' => 'Route_Card',
        ));
    }

    /**
     * Gets production schedules in specified status (started ones, by default)
     * THAT ARE AVAILABLE TO CURRENT USER (this is why search permission is checked)
     *
     * @param string $status - code term for status (planning, progress etc.)
     * @param int $id - id of document (optional) when searching for specific model
     * @return Production_Schedule[] - array of Production_Schedule models
     */
    private function getSchedules($status = 'progress', $id = 0) {
        $status = preg_replace('#^\w+(_\d+)$#', 'substatus$1',
            $this->get('type_production_schedule_status_' . $status));
        return Production_Custom_Factory::search($this->registry, array(
            'filters' => array(
                'where' => array(
                    ($id ? 'd.id = \'' . $id . '\'' : 'd.id IS NOT NULL'),
                    'd.type = \'' . $this->get('type_production_schedule') . '\'',
                    'd.status = \'' . $status . '\'',
                    'd.active = 1',
                    'd.transform_num > 0',
                ),
                'sort' => array(
                    'd.deadline ASC',
                    'd.id ASC',
                ),
            ),
            'model_name' => 'Production_Schedule',
        ));
    }

    /**
     * Gets production schedule model by id and status
     *
     * @param int $schedule - production schedule id
     * @param string $status - status of schedule, usually it is in progress
     * @return Production_Schedule|false - production schedule object or
     *  false on error
     */
    private function getProductionSchedule($schedule, $status = 'progress') {
        if (!$schedule) {
            return false;
        }
        $schedule = $this->getSchedules($status, $schedule);
        if ($schedule) {
            $schedule = reset($schedule);
        }
        return $schedule ?: false;
    }

    /**
     * Searches for production requests that can be included in production schedule
     *
     * @param int $product_id - product id, if not specified, searches for all
     * @return Document[] - found production request documents
     */
    private function getReadyRequests($product_id = 0) {
        return Production_Custom_Factory::search($this->registry, array(
            'filters' => array(
                'where' => array(
                    'd.type = \'' . $this->get('type_production_request') . '\'',
                    'd.status = \'' . preg_replace('#^\w+(_\d+)$#', 'substatus$1',
                        $this->get('type_production_request_status_ready_with_subrequests')) . '\' OR ',
                    'd.status = \'' . preg_replace('#^\w+(_\d+)$#', 'substatus$1',
                        $this->get('type_production_request_status_ready_without_subrequests')) . '\'',
                    'd.active = 1',
                    ($product_id ? 'a__product_id = \'' . $product_id . '\'' : '1 = 1'),
                    // there is some quantity to be produced AND
                    'a__remaining_quantity > 0',
                    // are not currently in production
                    'd.transform_num = 0',
                ),
                'sort' => array('d.id ASC'),
            )
        ));
    }

    /************************** specific methods *****************************/

    /**
     * Loads initial page of dashlet
     *
     * @return string - fetched content and/or operation result
     */
    public function home() {
        $dashlet = $this->registry['dashlet'];
        if (!$dashlet) {
            return;
        }

        // clear all previous POST data from Request in order not to confuse
        // preparation of home screen
        foreach (array_keys($this->request->getAll('post')) as $key) {
            $this->request->remove($key);
        }

        $this->prepareDashletData($dashlet);

        return $this->fetch('dashlet.html',
            array(
                'dashlet' => $dashlet,
                'home' => 1,
            ));
    }

    /**
     * Loads available schedules to add timesheet for. Date, workplace and
     * workshift are specified as parameters.
     *
     * @return string - fetched content and/or operation result
     */
    public function loadSchedules() {

        $schedules = array();

        if ($this->request['date'] && $this->request['workplace_id'] &&
        $this->registry['currentUser']->checkRights('documents' . $this->get('type_production_schedule'), 'search')) {
            // get started production schedules available to current user
            $schedules = $this->getSchedules();

            // get workplace model
            $workplace = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'module' => 'nomenclatures',
                    'controller' => 'nomenclatures',
                    'action' => 'view',
                    'filters' => array(
                        'where' => array(
                            "n.id = '{$this->request['workplace_id']}'",
                        ),
                    ),
                ));
            if ($workplace) {
                $workplace = reset($workplace);
            }

            $route_card_ids = array();
            if ($schedules && $workplace) {
                // define the type of workplace to search for in route cards
                /** @var Nomenclature $workplace */
                $workplace->unsanitize();
                $type_workplace = $workplace->getFields('type_workplace');
                $workplace->sanitize();
                $type_workplace = $type_workplace ? $type_workplace[0]['value'] : '';

                // get ids of route cards for production, saved in transform_num variable
                foreach ($schedules as $idx => $schedule) {
                    if (General::strftime('%Y-%m-%d', $schedule->getPlainVarValue('started_date')) > $this->request['date']) {
                        // cannot add timesheets before start date of production
                        unset($schedules[$idx]);
                    } elseif ($schedule->get('transform_num') && !in_array($schedule->get('transform_num'), $route_card_ids)) {
                        $route_card_ids[] = $schedule->get('transform_num');
                    }
                }

                // filter found route cards by presence of this type of workplace
                if ($route_card_ids) {
                    $filters = array(
                        'where' => array(
                            "d.type = '{$this->get('type_route_card')}'",
                            'd.id IN (' . implode(', ', $route_card_ids) . ')',
                            "a__card_type_workplace = '{$type_workplace}'",
                        )
                    );
                    $route_card_ids = Documents::getIds($this->registry, $filters);
                }
                // filter schedules only for route cards that workplace is
                // applicable for
                if ($route_card_ids) {
                    foreach ($schedules as $idx => $schedule) {
                        if (in_array($schedule->get('transform_num'), $route_card_ids)) {
                            // prepare data for existing timesheet if schedule already has such
                            $schedule->getWorkplaceTimesheet($this->request->getAll('post'));
                        } else {
                            unset($schedules[$idx]);
                        }
                    }
                }
            }
            if (empty($route_card_ids)) {
                $schedules = array();
            }
        }

        // fetch schedules
        return $this->fetch('_schedules.html', array('schedules' => $schedules));
    }

    /**
     * Loads screen for creation of production schedule
     *
     * @return string - fetched content and/or operation result
     */
    public function loadCreate() {

        $product_id = intval($this->request['product_id']);

        // search for requests for product that can be included in a schedule
        $requests = $product_id > 0 ? $this->getReadyRequests($product_id) : array();

        if ($requests) {
            // create a blank model
            $schedule = new Production_Schedule(
                $this->registry,
                array('type' => $this->get('type_production_schedule'))
            );
            $this->registry->set('edit_all', true, true);
            // change action to prepare validation of fields
            $custom_action = $this->registry['action'];
            $this->registry->set('action', 'add', true);
            $schedule->getVarsForTemplate();
            $this->registry->set('action', $custom_action, true);
            $schedule->getLayoutVars();

            $requested_quantity = 0;

            // use values of first request
            $rq = reset($requests);
            $product_measure_name = $rq->getVarValue('product_measure');

            $vars_layouts = $schedule->isDefined('vars') ? $schedule->get('vars') : array();
            foreach ($vars_layouts as $layout_id => &$layout_vars) {
                // get custom layouts
                $schedule->getLayoutsDetails($layout_id);

                foreach ($layout_vars as $var_idx => &$var) {
                    if (in_array($var['type'], array('button')) || empty($var['name'])) {
                        // do not display
                        unset($layout_vars[$var_idx]);
                    } elseif (in_array($var['name'], array('requested_quantity', 'additional_quantity', 'total_quantity'))) {
                        // visible
                        if ($var['name'] == 'additional_quantity') {
                            // add javascript processing functionality
                            $var['js_methods']['onblur'] = 'production.calculateCreateQuantity(this);';
                            // set validation rule according to product measure
                            $var['js_filter'] = $rq->getPlainVarValue('product_measure') == self::MEASURE_NUM ? 'insertOnlyDigits' : 'insertOnlyFloats';
                        }
                        $var['back_label'] = $product_measure_name;
                    } elseif (in_array($var['name'], array('product_name', 'product_id', 'product_measure'))) {
                        // hidden but have values
                        $var['hidden'] = 1;
                        $var['readonly'] = 1;
                        // get values
                        $var['value'] = $rq->getPlainVarValue($var['name']);
                        $schedule->set($var['name'], $var['value'], true);
                    } elseif (!empty($var['grouping']) && $var['name'] == 'request_group' &&
                    !empty($var['autocomplete']['request_name']['fill_options'])) {
                        // process the GT of production requests
                        $var['t_width'] = '100%';
                        $var['t_custom_class'] = 'reports_table';
                        $var['values'] = array();

                        // define content and order of fields in GT
                        $fill_options = array();
                        foreach ($var['autocomplete']['request_name']['fill_options'] as $opt) {
                            $opt = preg_split('#\s*=>\s*#', $opt);
                            if (count($opt) < 2) {
                                continue;
                            }
                            $opt[0] = preg_replace('#^\$#', '', $opt[0]);
                            $opt[1] = preg_replace('#^<(.*)>$#', '$1', $opt[1]);

                            $pos = array_search($opt[0], $var['names']);
                            if ($pos !== false) {
                                $fill_options[$pos] = $opt[1];
                            }
                        }

                        $matches = array();
                        // fill in rows with data from requests using autocompleter settings
                        foreach ($requests as $idx => $rq) {
                            // values from request form one row of GT of schedule
                            $row_values = array();
                            foreach ($fill_options as $pos => $val) {
                                if (preg_match('#^a_+(.*)#', $val, $matches)) {
                                    $val = $rq->getPlainVarValue($matches[1]);
                                    if ($matches[1] == 'remaining_quantity') {
                                        $requested_quantity += $val;
                                    }
                                } else {
                                    $val = $rq->get($val);
                                }
                                $row_values[$pos] = $val;
                            }
                            $var['values'][$idx] = $row_values;
                        }

                        // move some layouts to the beginning
                        $first_layouts = array();
                        $layouts_details = $schedule->get('layouts_details');
                        if (!empty($layouts_details['deadline'])) {
                            $first_layouts['deadline'] = $layouts_details['deadline'];
                            unset($layouts_details['deadline']);
                        }
                        $lkey = 'keyname_' . $layout_id;
                        $first_layouts[$lkey] = $layouts_details[$lkey];
                        unset($layouts_details[$lkey]);
                        $layouts_details = $first_layouts + $layouts_details;
                        $schedule->set('layouts_details', $layouts_details, true);
                    } else {
                        // hidden by default
                        $var['hidden'] = 1;
                        $var['readonly'] = 1;
                    }
                }
            }
            // set initial values for total quantities
            foreach ($vars_layouts as $layout_id => &$layout_vars) {
                foreach ($layout_vars as $var_idx => &$var) {
                    if (!empty($var['name']) && in_array($var['name'], array('requested_quantity', 'total_quantity'))) {
                        $var['value'] = $requested_quantity;
                    }
                 }
            }
            $schedule->set('vars', $vars_layouts, true);
            $schedule->sanitize();

            return $this->fetch(
                '_create.html',
                array('document' => $schedule)
            );
        }
    }

    /**
     * Creates production schedule from production requests
     *
     * @return string - fetched content and/or operation result
     */
    public function create() {

        // id of selected product
        $product_id = intval($this->request['product_id']);

        if (!($product_id > 0)) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_invalid_data'),
                $this->i18n('error_regexpCompare', array('var_label' => $this->i18n('plugin_product'), 'var_help' => '!')),
            ));
        }

        // load i18n files for documents
        $this->loadI18NFiles(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $this->registry['lang'], '/documents.ini')
        );

        /** @var Documents_Type $type */
        $type = Documents_Types::searchOne($this->registry,
            array(
                'where' => array(
                    'dt.id = ' . $this->get('type_production_schedule'),
                    'dt.active = 1',
                    'dt.inheritance = 0',
                ),
                'sanitize' => true,
            ));

        if (!$type) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_documents_add_failed', array($this->i18n('plugin_production_schedule'))),
                $this->i18n('error_invalid_type'),
            ));
        }

        $data_valid = true;

        // prepare model for save (add)
        $schedule = new Production_Schedule($this->registry, $this->request->getAll());
        $schedule->set('type', $type->get('id'), true);
        $schedule->set('type_name', $type->get('name'), true);
        $schedule->set('name', $type->get('default_name'), true);
        $schedule->set('group', $type->getDefaultGroup(), true);
        $schedule->set('department', $type->getDefaultDepartment(), true);
        $schedule->set('customer', $this->get('own_company'), true);
        $schedule->set('customer_name', $this->get('own_company_name'), true);
        $schedule->set('office', $this->registry['currentUser']->get('office'), true);
        $schedule->set('employee', $this->registry['currentUser']->get('employee'), true);

        // filter data in request only from checked rows
        $grouping = $schedule->getGroupingFields('request_group');
        if ($grouping) {
            $grouping = reset($grouping);
            $grouping = $grouping['grouping'];
        }
        $grouping = intval($grouping);
        if ($grouping > 0 && ($checked = $this->request->get("items_{$grouping}_"))) {
            $checked = array_flip($checked);
            foreach ($this->request->getAll('post') as $key => $value) {
                if (is_array($value)) {
                    $this->request->set($key, array_intersect_key($value, $checked), 'post', true);
                }
            }
        } else {
            $data_valid = false;
            $this->messages->setError($this->i18n('plugin_error_requests_invalid_data'));
        }

        // get id of route card for product - there should be exactly one
        $filters = array(
            'where' => array(
                'd.type = \'' . $this->get('type_route_card') . '\'',
                'd.active = 1',
                'd.status != \'closed\'',
                "a__product_id = '{$product_id}'",
            )
        );
        $card_id = Documents::getIds($this->registry, $filters);
        if (count($card_id) == 1) {
            $card_id = reset($card_id);
        } else {
            $data_valid = false;
            $product_name = Nomenclatures::searchOne(
                $this->registry,
                array('where' => array("n.id = '{$product_id}'"))
            );
            $product_name = $product_name ? $product_name->get('name') : '';
            $this->messages->setError(
                $this->i18n('plugin_error_single_route_card',
                            array($product_name, count($card_id)))
            );
        }

        // additional vars of model, get values from request
        $schedule->getVars();

        //allow save all variables
        $this->registry->set('edit_all', true, true);

        $this->registry['db']->StartTrans();

        if ($data_valid && $schedule->save()) {

            // save route card id into production schedule
            $schedule->setBasicVar(array('transform_num'), array($card_id));

            // set status to ready for production
            if ($this->get('type_production_schedule_status_planning') != 'opened') {
                $schedule->set('status', $this->get('type_production_schedule_status_planning'), true);
                if (!$schedule->setStatus()) {
                    $data_valid = false;
                }
            }

            $old_document = new Document($this->registry, array('type' => $schedule->get('type')));
            $this->registry->set('get_old_vars', true, true);
            $old_document->getVars();
            $old_document->sanitize();

            $filters = array(
                'where' => array('d.id = ' . $schedule->get('id')),
                'model_lang' => $schedule->get('model_lang'),
                'skip_assignments' => true,
                'skip_permissions_check' => true
            );
            $new_document = Documents::searchOne($this->registry, $filters);
            $new_document->getVars();
            $new_document->sanitize();
            $this->registry->set('get_old_vars', false, true);

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $new_document,
                    'action_type' => 'add',
                    'new_model' => $new_document,
                    'old_model' => $old_document,
                ));

            // insert transformation relations (many2one) between requests
            // and schedule; mark requests as "in production"
            // (not available for another schedule)
            $schedule->set('origin_model', 'Document', true);
            $schedule->set('clone_transform', 'transformed', true);
            $schedule->set('multi', false, true);
            $schedule->set('group_index', false, true);
            $rq = new Production_Request(
                $this->registry,
                array(
                    'type' => $this->get('type_production_request'),
                    'transform_num' => $schedule->get('id'),
                )
            );

            // iterate ids of selected production requests
            foreach ($this->request['request_id'] as $request_id) {
                $schedule->set('origin_id', $request_id, true);
                $rq->set('id', $request_id, true);
                if (!$schedule->insertTransformedRelatives() || !$rq->setBasicVar(array('transform_num'))) {
                    $data_valid = false;
                    break;
                }
            }
        } else {
            $data_valid = false;
        }

        // there were some errors during save, fail everything
        if (!$data_valid) {
            $this->registry['db']->FailTrans();
        }

        $this->registry['db']->CompleteTrans();

        // adding production schedule failed, do not continue
        if (!$data_valid) {
            $this->messages->setError(
                $this->i18n('error_documents_add_failed',
                            array($schedule->getModelTypeName())
            ), '', -1);
            return $this->prepareErrorResponse(
                $this->messages->getErrors(),
                true
            );
        }

        $result = array(
            'messages' => $this->prepareMessages(array(
                $this->i18n('message_documents_add_success',
                    array(sprintf(' %s %s (%s)',
                        $schedule->getModelTypeName(),
                        $schedule->get('full_num'),
                        $schedule->getPlainVarValue('product_name')
                )))
            ))
        );
        if ($this->request->get('after_action') == 'start') {
            // add schedule id in request
            $this->request->set('schedule_id', $schedule->get('id'), 'post', true);
            // prepare screen for start of production
            $result['content'] = $this->loadStart();
            // display errors
            if (strpos($result['content'], '{"errors":') === 0) {
                $result = array_merge($result, json_decode($result['content'], true));
                $result['content'] = $this->home();
            }
        } else {
            $result['content'] = $this->home();
        }

        return json_encode($result);
    }

    /**
     * Prepares commodity transfer model for material transfer screen in
     * interface - applies custom changes to variables and sets custom data
     *
     * @param Production_Schedule $schedule - production schedule
     * @param Route_Card $route_card - route card of schedule
     * @param Finance_Warehouse $warehouse - warehouse to transfer from
     * @return Finance_Warehouses_Document - prepared model with set vars and data
     */
    private function prepareLoadMaterialsTransfer(Production_Schedule $schedule, Route_Card $route_card, Finance_Warehouse $warehouse) {

        $prec = $this->registry['config']->getSectionParams('precision');
        $quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);
        $quantity_num_format = '%.0F';
        /** @var User $user */
        $user = $this->registry['currentUser'];
        $wid = $warehouse->get('id');

        $params = array(
            'type' => PH_FINANCE_TYPE_COMMODITIES_TRANSFER,
            'warehouse_data' => sprintf('%d_%d_%d',
                $warehouse->get('company'),
                $warehouse->get('office'),
                $wid
            ),
            'currency' => $this->get('currency'),
        );

        $transfer = new Finance_Warehouses_Document($this->registry, $params);
        $transfer->getGT2Vars();
        $transfer->getBatchesData();

        $gt2 = $transfer->get('grouping_table_2');
        $gt2['hide_delete'] = 1;
        $gt2['hide_multiple_rows_buttons'] = 1;
        $gt2['show_refresh_buttons'] = 0;
        $gt2['show_select_buttons'] = 0;
        $gt2['width'] = '100%';

        foreach ($gt2['vars'] as $var_name => &$var) {
            if ($var_name == 'quantity') {
                $var['readonly'] = $var['hidden'] = 0;
                $var['custom_class'] = 'quantity commodities_transfer_quantity';
                $var['js_methods']['onkeyup'] = 'gt2calc(this); production.processMaterialsRow(this);';
            } elseif ($var_name == 'available_quantity') {
                $var['readonly'] = 1;
                $var['hidden'] = 0;
            } elseif ($var_name == 'article_height') {
                $var['readonly'] = 1;
                $var['hidden'] = 0;
                $var['label'] = $this->i18n('plugin_planned_necessary_quantity');
            } elseif (!$var['hidden']) {
                $var['readonly'] = 1;
                $var['custom_class'] = !empty($var['custom_class']) ? $var['custom_class'] . ' viewmode' : 'viewmode';
                // hide the arrow of the dropdown
                if ($var['type'] == 'dropdown') {
                    $var['back_label'] = '&nbsp;&nbsp;&nbsp;&nbsp;';
                    $var['back_label_style'] = 'background-color: inherit; margin: 0 0 -6px -20px; font-size: 1.7em;';
                }
            }
        }
        unset($var);

        foreach ($gt2['plain_vars'] as $var_name => &$var) {
            $var['hidden'] = 1;
        }
        unset($var);

        $vars_settings = $transfer->getBatchVarsSettings();
        $visible_batch_vars = array('available_quantity', 'expire', 'serial');
        foreach ($vars_settings as $var_name => $var) {
            $vars_settings[$var_name]['hidden'] = !in_array($var_name, $visible_batch_vars);
        }

        // get all rows of GT because we have to prepare the step deviations
        $gt = $route_card->getGroupingVarValues(array(''), false, 'card_group');
        $deviation_steps = $route_card->getStepDeviation($gt);

        $article_ids = array_map(
            function(&$a) {
                return $a['card_article_id'];
            },
            $gt
        );
        // set flag for currency
        $this->registry->set('nomenclatures_currency', $this->get('currency'), true);
        /** @var Nomenclature[] $recipe_materials */
        $recipe_materials = Nomenclatures::search(
            $this->registry,
            array(
                'where' => array(
                    'n.id IN (\'' . implode('\', \'', $article_ids) . '\')',
                    'n.subtype = \'commodity\'',
                    'n.active = 1',
                ),
                'sort' => array('n.id ASC'),
                'sanitize' => true
            ));
        // unset flag from registry
        $this->registry->remove('nomenclatures_currency');

        // get only the ids of commodities
        $article_ids = array_map(function($a) { return $a->get('id'); }, $recipe_materials);

        // get quantities and measures of commodity articles from GT of route card
        $article_info = array();
        foreach ($gt as $row) {
            if (in_array($row['card_article_id'], $article_ids)) {
                if (!isset($article_info[$row['card_article_id']])) {
                    $article_info[$row['card_article_id']] = array(
                        //'product_name' => $row['card_article_name'],
                        'q' => 0,
                        'm' => $row['card_measure'],
                    );
                }
                $dev_step =
                    isset($deviation_steps[$row['card_tech_id'] . '_' . $row['card_step']]) ?
                    $deviation_steps[$row['card_tech_id'] . '_' . $row['card_step']] :
                    1;
                // these quantities are for production of quantity specified in route card
                $article_info[$row['card_article_id']]['q'] += $row['card_total_quantity'] * $dev_step;
            }
        }

        // prepare available quantities in materials warehouse
        $available_articles = $warehouse->getAvailableQuantity(array('nom_id' => $article_ids));
        $warehouse->unsetProperty('available_quantities', true);
        // some articles might be unavailable
        foreach ($article_ids as $pid) {
            if (empty($available_articles[$pid])) {
                $available_articles[$pid] = array ('quantity' => 0, 'batch_data' => array());
            }
        }

        $existing_batches = $existing_serials = $existing_custom = array();
        $gt2['values'] = array();

        // calculate multiplier: divide planned quantity to quantity in route card
        $mul = $schedule->getPlainVarValue('total_quantity') / ($route_card->getPlainVarValue('product_quantity') ?: 1);

        // if this is not the first transfer for current production process:
        // get what has been previously transfered and reduce suggested and
        // necessary quantities accordingly
        $transfers = $schedule->getWhDocs($schedule->getTransferIds());
        $transfers_gt2_values = array();
        if ($transfers) {
            // prepare aggregated values of transfered quantities - article ids as keys, merged GT2 rows as values
            $transfers_gt2_values = $schedule->prepareMergedGT2Values($transfers);
        }

        foreach ($recipe_materials as $ridx => $rp) {
            $pid = $rp->get('id');
            $available = $available_articles[$pid];
            // check for previously transfered quantity for material
            $transfered_quantity = array_key_exists($pid, $transfers_gt2_values) ? $transfers_gt2_values[$pid]['quantity'] : 0;

            $key = -($ridx+1);
            // necessary quantity for each article
            if ($article_info[$pid]['m'] == self::MEASURE_NUM) {
                // round fractions up to next integer
                $quantity = ceil($mul * $article_info[$pid]['q'] - $transfered_quantity);
            } else {
                // round according to GT2 quantity precision
                $quantity = sprintf($quantity_prec_format, round($mul * $article_info[$pid]['q'] - $transfered_quantity, $prec['gt2_quantity']));
            }
            // if previously transfered quantity is equal to or greater than
            // calculated necessary quantity, suggest 0 and let user specify otherwise
            if (bccomp($quantity, 0, $prec['gt2_quantity']) < 1) {
                $quantity = sprintf($quantity_prec_format, 0);
            }

            $gt2['values'][$key] = array(
                'article_id' => $pid,
                'article_code' => $rp->get('code'),
                'article_name' => $rp->get('name'),
                'article_measure_name' => $article_info[$pid]['m'],
                'price' => $rp->get('sell_price'),
                'last_delivery_price' => $rp->get('last_delivery_price'),
                'average_weighted_delivery_price' => $rp->get('average_weighted_delivery_price'),
                'quantity' => $rp->get('has_batch') /*&& !empty($available['batch_data'])*/ ? 0 : $quantity,
                // keep the initially set quantity in this field and use it for validation
                'article_height' => $quantity,
                'available_quantity' => sprintf($quantity_prec_format, $available['quantity']),
                'has_batch' => $rp->get('has_batch'),
                'available_in' => !empty($available['quantity']) ? array($wid) : array(),
                // set flag for kind of record
                'article_second_code' => 'material',
            );

            // batch row data preparation
            if ($gt2['values'][$key]['has_batch']) {
                $gt2['values'][$key]['vars_settings'] = $vars_settings;
                $gt2['values'][$key]['has_serial'] = $rp->get('has_serial');
                $gt2['values'][$key]['has_expire'] = $rp->get('has_expire');
                $gt2['values'][$key]['has_delivery_price'] = 2;
                $gt2['values'][$key]['has_batch_code'] = empty($available['quantity']) ? $rp->get('has_batch_code') : true;
                $gt2['values'][$key]['batch_data'] = array();

                // default sort: available batches earliest first
                $sortAlgorithm = 'sortBatchData';
                if ($this->get('batch_sort') == 'alphabetical') {
                    $sortAlgorithm = 'sortBatchDataByCode';
                }
                usort($available['batch_data'], array('Production_Custom_Factory', $sortAlgorithm));

                // remaining quantity
                $quantity_remaining = $quantity;

                $unique = $json = array();
                //create unique key for each quantity found
                foreach ($available['batch_data'] as $bidx => $bd) {
                    $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                        $this->registry['db'],
                        [
                            'parent_id'       => $wid,
                            'nomenclature_id' => $pid,
                            'batch_id'        => $bd['batch'],
                            'expire_date'     => $bd['expire'] ? : '0000-00-00',
                            'serial_number'   => $bd['serial'],
                            'cstm_number'     => $bd['custom'],
                            'delivery_price'  => round($bd['delivery_price'], $prec['gt2_rows']),
                            'currency'        => $bd['currency'],
                        ]
                    );
                    if (!isset($unique[$k])) {
                        $unique[$k] = 0;
                    }
                    $unique[$k] += $bd['quantity'];
                    //set existing batches for the article
                    if (!isset($existing_batches[$pid][$bd['batch']])) {
                        $existing_batches[$pid][$bd['batch']] = array(
                            'label' => $bd['batch_code'],
                            'option_value' => $bd['batch']
                        );
                    }
                    if ($rp->get('has_serial')) {
                        //set existing serials for the article
                        if (!isset($existing_serials[$pid][$bd['batch']][$bd['serial']])) {
                            $existing_serials[$pid][$bd['batch']][$bd['serial']] = array(
                                'label' => $bd['serial'],
                                'option_value' => $bd['serial']
                            );
                        }
                    }
                    //set existing custom number for the article
                    if (!isset($existing_custom[$pid][$bd['batch']][$bd['custom']])) {
                        $existing_custom[$pid][$bd['batch']][$bd['custom']] = array(
                            'label' => $bd['custom'],
                            'option_value' => $bd['custom']
                        );
                        if (!$bd['custom']) {
                            $existing_custom[$pid][$bd['batch']][$bd['custom']]['label'] =
                            $this->i18n('finance_warehouses_documents_no_custom_batch_number');
                        }
                    }

                    //prepare data and dependencies
                    if (empty($json[$bd['batch']])) {
                        $json[$bd['batch']] = $bd;
                        if (!$bd['serial']) {
                            $bd['serial'] = '';
                        } else {
                            $json[$bd['batch']]['serial'] = array($json[$bd['batch']]['serial'] => array('label' => $json[$bd['batch']]['serial'], 'option_value' => $json[$bd['batch']]['serial']));
                        }
                        if ($bd['custom']) {
                            $json[$bd['batch']]['custom'] = array($json[$bd['batch']]['custom'] => array('label' => $json[$bd['batch']]['custom'], 'option_value' => $json[$bd['batch']]['custom']));
                        } else {
                            $json[$bd['batch']]['custom'] = array('' => array('label' => $this->i18n('finance_warehouses_documents_no_custom_batch_number'), 'option_value' => ''));
                            $bd['custom'] = '';
                        }
                        $json[$bd['batch']]['expire_formatted'] = General::strftime('%d.%m.%Y', $json[$bd['batch']]['expire']);
                        $json[$bd['batch']]['available_quantity'] = array($bd['serial'] . '_' . $bd['custom'] => $json[$bd['batch']]['quantity']);
                        $json[$bd['batch']]['custom_serials'][$bd['custom']] = array($bd['serial']);
                        $json[$bd['batch']]['serial_customs'][$bd['serial']] = array($bd['custom']);
                        unset($json[$bd['batch']]['quantity']);
                    } else {
                        if (!$bd['serial']) {
                            $bd['serial'] = '';
                        }
                        if ($bd['serial'] && empty($json[$bd['batch']]['serial'][$bd['serial']])) {
                            $json[$bd['batch']]['serial'][$bd['serial']] = array('label' => $bd['serial'], 'option_value' => $bd['serial']);
                        }
                        if ($bd['custom'] && empty($json[$bd['batch']]['custom'][$bd['custom']])) {
                            $json[$bd['batch']]['custom'][$bd['custom']] = array('label' => $bd['custom'], 'option_value' => $bd['custom']);
                        } elseif (!$bd['custom'] && empty($json[$bd['batch']]['custom'][''])) {
                            $json[$bd['batch']]['custom'] = array('' => array('label' => $this->i18n('finance_warehouses_documents_no_custom_batch_number'), 'option_value' => ''));
                            $bd['custom'] = '';
                        }
                        $json[$bd['batch']]['available_quantity'][$bd['serial'] . '_' . $bd['custom']] = $bd['quantity'];
                        if (empty($json[$bd['batch']]['custom_serials'][$bd['custom']])) {
                            $json[$bd['batch']]['custom_serials'][$bd['custom']] = array();
                        }
                        if (!in_array($bd['serial'], $json[$bd['batch']]['custom_serials'][$bd['custom']])) {
                            $json[$bd['batch']]['custom_serials'][$bd['custom']][] = $bd['serial'];
                        }
                        if (empty($json[$bd['batch']]['serial_customs'][$bd['serial']])) {
                            $json[$bd['batch']]['serial_customs'][$bd['serial']] = array();
                        }
                        if (!in_array($bd['custom'], $json[$bd['batch']]['serial_customs'][$bd['serial']])) {
                            $json[$bd['batch']]['serial_customs'][$bd['serial']][] = $bd['custom'];
                        }
                    }

                    // now we fill in as many batch rows as necessary instead of the user
                    if ($quantity_remaining > 0 || empty($gt2['values'][$key]['batch_data'])) {
                        if ($quantity_remaining < $bd['quantity'] || count($available['batch_data']) == $bidx + 1) {
                            $bd['quantity'] = $quantity_remaining;
                        }
                        if ($article_info[$pid]['m'] == self::MEASURE_NUM) {
                            // each suggested batch should have an integer filled in even if available quantity has a fraction part
                            $bd['quantity'] = sprintf($quantity_num_format, floor($bd['quantity']));
                        } else {
                            $bd['quantity'] = sprintf($quantity_prec_format, $bd['quantity']);
                        }
                        $gt2['values'][$key]['batch_data'][] = $bd;
                        $quantity_remaining -= $bd['quantity'];
                    }
                }

                //prepare batch options for the current article
                //$batch_isCustom = $this->request['article_' . $key . '_batch_isCustom'];
                if (!empty($gt2['values'][$key]['batch_data'])) {
                    foreach ($gt2['values'][$key]['batch_data'] as $idx => $bd) {
                        $k = Finance_Warehouses_Documents::buildBatchUniqueKey(
                            $this->registry['db'],
                            [
                                'parent_id'       => $wid,
                                'nomenclature_id' => $pid,
                                'batch_id'        => $bd['batch'],
                                'expire_date'     => $bd['expire'] ? : '0000-00-00',
                                'serial_number'   => $bd['serial'],
                                'cstm_number'     => $bd['custom'],
                                'delivery_price'  => round($bd['delivery_price'], $prec['gt2_rows']),
                                'currency'        => $bd['currency'],
                            ]
                        );
                        $gt2['values'][$key]['batch_data'][$idx]['available_quantity'] =
                        sprintf($quantity_prec_format, (isset($unique[$k])? $unique[$k] : 0));
                        if (!empty($json[$bd['batch']])) {
                            $gt2['values'][$key]['batch_data'][$idx]['batch_data_json_object'] = $json[$bd['batch']];
                        }
                        /* if (!empty($batch_isCustom[$idx])) {
                         //set custom code for the new batch
                         $gt2['values'][$key]['batch_data'][$idx]['batch'] = $gt2['values'][$key]['batch_data'][$idx]['batch_code'];
                         } else */if (!isset($existing_batches[$pid][$bd['batch']])) {
                         $gt2['values'][$key]['batch_data'][$idx]['batch'] = '';
                        }
                    }
                }
            }
            // end batch row data preparation
        }

        $gt2['plain_values']['currency'] = $transfer->get('currency');
        $transfer->set('grouping_table_2', $gt2, true);
        $transfer->calculateGT2();

        // change formatting of quantity in rows with Num measure after GT2 calculations are performed
        $gt2 = $transfer->get('grouping_table_2');
        foreach ($gt2['values'] as $ridx => $row) {
            if ($row['article_measure_name'] == self::MEASURE_NUM) {
                $gt2['values'][$ridx]['quantity'] = sprintf($quantity_num_format, $row['quantity']);
            }
        }
        $transfer->set('grouping_table_2', $gt2, true);
        $transfer->set('num_rows', count($gt2['values']), true);

        // set prepared batch data into model
        $transfer->set('existing_batches', $existing_batches, true);
        $transfer->set('existing_serials', $existing_serials, true);
        $transfer->set('existing_custom', $existing_custom, true);

        return $transfer;
    }

    /**
     * Creates transfer of selected materials and quantities from specified
     * source warehouse to production warehouse designated for current production
     *
     * @param Production_Schedule $schedule - production schedule
     * @return bool - result of operation  true on success, false on error
     */
    private function createMaterialsTransfer(Production_Schedule $schedule) {

        // get submitted data from request
        $quantity = $this->request['quantity'] ?: array();
        $qty_min = $this->request['article_height'] ?: array();
        $article_me = $this->request['article_measure_name'] ?: array();
        $deleted = array();

        $prec = $this->registry['config']->getSectionParams('precision');

        foreach ($quantity as $idx => $qty) {
            $qty = floatval($qty);
            if (isset($article_me[$idx]) && $article_me[$idx] == self::MEASURE_NUM) {
                $quantity[$idx] = round($qty);
                // if it is a batch article, make sure that an integer quantity is set for each batch row
                if ($this->request->isRequested('article_' . $idx . '_quantity')) {
                    $batch_qty = $this->request['article_' . $idx . '_quantity'];
                    if (is_array($batch_qty)) {
                        foreach ($batch_qty as $b_idx => $b_qty) {
                            $batch_qty[$b_idx] = round($b_qty);
                        }
                    }
                    $this->request->set('article_' . $idx . '_quantity', $batch_qty, 'post', true);
                }
            }
            // mark zero-quantity rows as deleted in order to be omitted from transfer
            if (bccomp($quantity[$idx], 0, $prec['gt2_quantity']) < 1) {
                $deleted[$idx] = 1;
            }
        }

        if (!$quantity || count($quantity) == count($deleted)) {
            $this->messages->setError($this->i18n('plugin_error_commodities_transfer_no_quantity'));
            return false;
        }

        // and clear values of the latter field from request
        $this->request->set('article_height', array_fill_keys(array_keys($qty_min), ''), 'post', true);
        // make sure quantities are correct
        $this->request->set('quantity', $quantity, 'post', true);
        // set flags for deleted rows in request
        $this->request->set('deleted', $deleted, 'post', true);

        /** @var User $user */
        $user = $this->registry['currentUser'];
        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];

        // load i18n files for finance
        $lang = $this->registry['lang'];
        $lang_files = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance_warehouses_documents.ini'),
        );
        $this->loadI18NFiles($lang_files);

        $db->StartTrans();

        // prepare data for commodities transfer as if it were added from interface
        require_once PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.factory.php';
        $this->registry->set('get_old_vars', false, true);
        $employee = $user->get('employee') ?: $user->get('firstname') . ' ' . $user->get('lastname');
        $today = General::strftime($this->i18n('date_iso_short'));

        $custom_action = $this->registry['action'];
        $this->registry->set('action', 'add', true);

        // if production has previous material transfers, use their 'to_warehouse' as destination for next material transfer,
        // otherwise destination warehouse is defined by current value of personal/default dashlet setting
        $to_warehouse_data = '';
        $to_wid = $schedule->getProductionWarehouse();
        if ($to_wid) {
            $to_warehouse_data = Production_Custom_Factory::getWarehouseData($this->registry, $to_wid);
        } else {
            $to_warehouse_data = Production_Custom_Factory::defineWarehouse($this->registry, 'production_warehouse', true);
        }

        $transfer_params = array(
            'name' => '',
            'type' => PH_FINANCE_TYPE_COMMODITIES_TRANSFER,
            //'warehouse_data' => $this->request->get('warehouse_data'),
            'from' => $employee,
            'to' => $employee,
            'date' => $today,
            'to_warehouse_data' => $to_warehouse_data,
            'to_from' => $employee,
            'to_to' => $employee,
            'to_date' => $today,
            'department' => PH_DEPARTMENT_FIRST,
            'group' => PH_ROOT_GROUP,
            'status' => 'opened',
            'finance_after_action' => 'finish',
            // add custom relation in fin_reasons_relatives between
            // production record and commodities transfer
            'link_to_model_name' => 'Document',
            'link_to' => $schedule->get('id'),
        );
        foreach ($transfer_params as $k => $v) {
            $this->request->set($k, $v, 'all', true);
        }
        $transfer = Finance_Warehouses_Documents::buildModel($this->registry);

        // set a meaningful name in order to know what record is for
        $transfer->set('name', $this->i18n('plugin_name_transfer_material_incoming'), true);

        // save commodities transfer
        if ($transfer->save()) {
            // write history
            // set type and company in order to prepare GT2 table in old model
            $old_model = new Finance_Warehouses_Document(
                $this->registry,
                array(
                    'type' => $transfer->get('type'),
                    'company' => $transfer->get('company'),
                )
            );
            $this->registry->set('get_old_vars', true, true);
            $old_model->getGT2Vars();
            $old_model->sanitize();

            $filters = array('where' => array('fwd.id = ' . $transfer->get('id')));
            $transfer = Finance_Warehouses_Documents::searchOne($this->registry, $filters);
            $transfer->getGT2Vars();

            require_once(PH_MODULES_DIR . 'finance/models/finance.warehouses_documents.history.php');
            Finance_Warehouses_Documents_History::saveData(
                $this->registry,
                array(
                    'action_type' => 'add',
                    'new_model' => $transfer,
                    'old_model' => $old_model,
                ));

            // save create history
            $destination_params = array(
                'origin_method' => 'Dashlet',
                'origin_method_id' => $this->registry['dashlet'] ? $this->registry['dashlet']->get('id') : '0',
                'destination_model' => $transfer->modelName,
                'destination_id' => $transfer->get('id'),
                'destination_full_num' => $transfer->get('num'),
                'destination_name' => $transfer->get('name'),
            );
            $schedule->set('destination_params', $destination_params, true);
            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $schedule,
                    'action_type' => 'create',
                ));
            $schedule->unsetProperty('destination_params', true);
        } else {
            $db->FailTrans();
            $this->messages->setError(
                $this->i18n('error_finance_warehouses_documents_add_failed',
                    array($transfer->getModelTypeName())
                ), '', -1);
        }

        $this->registry->set('action', $custom_action, true);

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        return $result;
    }

    /**
     * Loads screen for starting of production schedule (initial selection of
     * materials and quantities to be transfered to production warehouse)
     *
     * @return string - fetched content and/or operation result
     */
    public function loadStart() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id'], 'planning');
        // schedule not found
        if (!$schedule) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_invalid_schedule_planning')
            ));
        }

        // get route card by id set in production schedule
        $route_card = $schedule->getRouteCard();
        // route card missing or not found
        if (!$route_card) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_missing_route_card')
            ));
        }

        // define id of warehouse to get materials from (source warehouse of transfer)
        if ($this->request['warehouse_data']) {
            $wid = explode('_', strval($this->request['warehouse_data']));
            $wid = end($wid);
        } else {
            $wid = Production_Custom_Factory::defineWarehouse($this->registry, 'material_warehouse');
        }

        // warehouse model
        $warehouse = null;
        if ($wid) {
            /** @var Finance_Warehouse $warehouse */
            $warehouse = Finance_Warehouses::searchOne(
                $this->registry,
                array('where' => array(
                    'fwh.id = \'' . $wid . '\'',
                    'fwh.active = 1',
                ))
            );
        }

        if (!$warehouse) {
            return $this->prepareErrorResponse(array(
                $this->i18n(
                    'plugin_error_no_material_warehouse',
                    array(
                        $this->i18n(
                            'plugin_error_settings_link',
                            array(
                                '<span class="strong pointer spanlink" onclick="production.settings(\'' . self::CONFIG_PREFIX . 'material_warehouse\');">',
                                '</span>'
                            )
                        )
                    )
                )
            ));
        }

        $lang = $this->registry['lang'];
        $lang_files = array(
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_warehouses_documents.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_documents_types.ini',
        );
        $this->loadI18NFiles($lang_files);

        $prec = $this->registry['config']->getSectionParams('precision');
        $quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);

        // prepare materials transfer variables and data for display in interface
        $transfer = $this->prepareLoadMaterialsTransfer($schedule, $route_card, $warehouse);
        $gt2 = $transfer->get('grouping_table_2');

        // prepare data for consumables from tech cards for display
        // (data is not saved or used, it is just displayed to inform user)
        $tech_cards = $route_card->getGroupingVarValues(array('card_tech_id', 'card_tech_multiple'), false);
        $tech_card = new Technological_Card(
            $this->registry,
            array(
                'type' => $this->get('type_technological_card'),
                'plain_vars' => array(),
            )
        );
        // prepare total consumables quantities necessary for production
        $cons_qty = array();
        $tech_card_ids = array();
        $product_qty = $schedule->getPlainVarValue('total_quantity');
        // variables to display
        $cons_vars = array('consumable_category_type', 'consumable_article_id', 'consumable_article_name', 'consumable_quantity', 'consumable_measure', 'consumable_group');
        foreach ($tech_cards as $tc) {
            if (!in_array($tc['card_tech_id'], $tech_card_ids)) {
                $tech_card_ids[] = $tc['card_tech_id'];
                $tech_card->set('id', $tc['card_tech_id'], true);
                $tech_card->unsetProperty('vars', true);
                $cons = $tech_card->getGroupingVarValues($cons_vars, false, $cons_vars[0]);
                // prepare total consumables necessary for production
                if ($cons) {
                    $cons_mul = (!empty($tc['card_tech_multiple']) ? $tc['card_tech_multiple'] : 1) * $product_qty;
                    foreach ($cons as $row) {
                        if (!empty($row['consumable_article_id'])) {
                            if (!isset($cons_qty[$row['consumable_article_id']])) {
                                unset($row['num']);
                                $cons_qty[$row['consumable_article_id']] = $row;
                                $cons_qty[$row['consumable_article_id']]['consumable_quantity'] = 0;
                            }
                            $cons_qty[$row['consumable_article_id']]['consumable_quantity'] += $row['consumable_quantity'] * $cons_mul;
                        }
                    }
                }
            }
        }
        if ($cons_qty) {
            foreach ($cons_qty as $article_id => $row) {
                if ($row['consumable_measure'] == self::MEASURE_NUM) {
                    $cons_qty[$article_id]['consumable_quantity'] =
                        ceil($cons_qty[$article_id]['consumable_quantity']);
                } else {
                    $cons_qty[$article_id]['consumable_quantity'] =
                        sprintf($quantity_prec_format, round($cons_qty[$article_id]['consumable_quantity'], $prec['gt2_quantity']));
                }
            }

            $tech_card->unsetProperty('id', true);
            $tech_card->unsetProperty('assoc_vars', true);
            $tech_card->set('group_vars', $tech_card->get('vars'), true);
            $tech_card->getVarsForTemplate();
            $vars = $tech_card->get('vars');
            $cons_var = reset($vars);
            if ($cons_var && !empty($cons_var['grouping'])) {
                $cons_var['values'] = array_map('array_values', array_values($cons_qty));
                $last_idx = array_search('consumable_quantity', $cons_var['names']);
                $cons_var['labels'][$last_idx] = $this->i18n('plugin_quantity');
                $cons_var['help'][$last_idx] = '';
                $cons_var['width'] = array_combine(array_keys($cons_var['width']), array_fill(0, count($cons_var['width']), ''));
                $cons_var['t_width'] = '';
            } else {
                unset($cons_var);
            }
        }
        // consumables end

        return $this->fetch(
            '_start.html',
            array(
                'plugin_action' => 'loadStart',
                'document' => $schedule,
                'model' => $transfer->sanitize(),
                'existing_batches' => $transfer->get('existing_batches'),
                'existing_serials' => $transfer->get('existing_serials'),
                'existing_custom' => $transfer->get('existing_custom'),
                'currencies_available' => $gt2['plain_vars']['currency']['options'],
                'warehouses' => array($warehouse->sanitize()),
                'warehouse_data_options' => Production_Custom_Factory::getWarehouseDataOptions($this->registry),
                'price_prec' => sprintf('%%.%dF', $prec['gt2_rows']),
                'cons_var' => (!empty($cons_var) ? $cons_var : false),
            ),
            $lang_files
        );
    }

    /**
     * Starts production schedule
     *
     * @return string - fetched content and/or operation result
     */
    public function start() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id'], 'planning');
        if (!$schedule) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_invalid_schedule_planning')
            ));
        }

        // get route card by id set in production schedule
        $route_card = $schedule->getRouteCard();

        // route card missing or not found
        if (!$route_card) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_missing_route_card')
            ));
        }

        // check if there is at least one non-zero row for materials (commodities)
        $quantity = $this->request['quantity'] ?: array();
        $quantity_any = array_filter($quantity, function($el) { return $el > 0; });
        if ($quantity && empty($quantity_any)) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_commodities_transfer_no_quantity')
            ));
        }

        $prec = $this->registry['config']->getSectionParams('precision');
        /** @var User $user */
        $user = $this->registry['currentUser'];
        $date_iso = General::strftime($this->i18n('date_iso'));
        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];

        // load i18n files for documents
        $lang = $this->registry['lang'];
        $lang_files = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $lang, '/documents.ini'),
        );
        $this->loadI18NFiles($lang_files);

        $db->StartTrans();

        // 1. lock route card if not locked
        if ($route_card->get('status') == 'opened') {
            $route_card->unsanitize();
            $route_card->setBasicVar(
                array('status', 'substatus', 'status_modified', 'status_modified_by'),
                array('locked', '0', $date_iso, $user->get('id'))
            );
            $route_card->sanitize();
        }

        // 2. update status, started, started_by of schedule, set data in production GT
        $old_model = clone $schedule;
        $schedule->unsanitize();
        $status = explode('_', $this->get('type_production_schedule_status_progress'));
        if (empty($status[1])) {
            $status[1] = 0;
        }
        $schedule->setBasicVar(
            array('status', 'substatus', 'status_modified', 'status_modified_by'),
            array($status[0], $status[1], $date_iso, $user->get('id'))
        );

        // save GT based on that of route card
        $this->registry->set('edit_all', true, true);
        $avars = $schedule->getAssocVars();
        $gt = $route_card->getGroupingVarValues(array(''), false, 'card_group');
        // prepare deviation caused by expected waste
        $deviation_steps = $route_card->getStepDeviation($gt);
        // calculate multiplier: divide planned quantity to quantity in route card
        $mul = $schedule->getPlainVarValue('total_quantity') / ($route_card->getPlainVarValue('product_quantity') ?: 1);

        // get source vars of route card
        $avars_src = $route_card->getAssocVars();

        foreach ($avars as $varname => &$var) {
            switch ($varname) {
                case 'started_date':
                    $var['value'] = $date_iso;
                    break;
                case 'started_by':
                    $var['value'] = $user->get('id');
                    break;
                case 'started_by_name':
                    $var['value'] = $user->get('firstname') . ' ' . $user->get('lastname');
                    break;
                default:
                    if (strpos($varname, 'pp_') === 0) {
                        $varname_src = preg_replace('#^pp_#', 'card_', $varname);
                        if (isset($avars_src[$varname_src]) && isset($avars_src[$varname_src]['value'])) {
                            // processing of pp_quantity, pp_quantity_min, pp_quantity_max
                            if ($varname == 'pp_quantity') {
                                // recalculate quantity from route card and calculate min/max qtys
                                foreach ($avars_src['card_total_quantity']['value'] as $idx => &$value) {
                                    $qty = round($value * $mul, $prec['gt2_quantity']);
                                    $avars[$varname]['value'][$idx] = $qty;
                                    $row = $gt[$idx];
                                    // algorithm for min planned qty
                                    $avars[$varname . '_min']['value'][$idx] = round($qty * (1 - floatval($row['card_deviation_min']) / 100), $prec['gt2_quantity']);
                                    // algorithm for max planned qty
                                    $dev_step =
                                        isset($deviation_steps[$row['card_tech_id'] . '_' . $row['card_step']]) ?
                                        $deviation_steps[$row['card_tech_id'] . '_' . $row['card_step']] :
                                        1;
                                    $avars[$varname . '_max']['value'][$idx] = round($qty * (1 + floatval($row['card_deviation_max']) / 100) * $dev_step, $prec['gt2_quantity']);

                                    // if article measure is Num., use next integer for float quantities
                                    if (isset($avars_src['card_measure']['value'][$idx]) && $avars_src['card_measure']['value'][$idx] == self::MEASURE_NUM) {
                                        $avars[$varname]['value'][$idx] = ceil($avars[$varname]['value'][$idx]);
                                        $avars[$varname . '_min']['value'][$idx] = ceil($avars[$varname . '_min']['value'][$idx]);
                                        $avars[$varname . '_max']['value'][$idx] = ceil($avars[$varname . '_max']['value'][$idx]);
                                    }
                                }
                                unset($value);
                            } else {
                                // copy same values
                                // IMPORTANT: get them from $gt, not $avars_src because the former is pre-sorted
                                $avars[$varname]['value'] = array_map(function($a) use ($varname_src) { return $a[$varname_src]; }, $gt);
                            }
                        }
                    } else {
                        unset($avars[$varname]);
                    }
            }
        }
        unset($var);

        $schedule->set('vars', array_values($avars), true);
        $this->registry->set('get_old_vars', true, true);
        if ($schedule->saveVars()) {
            $schedule->unsetProperty('vars', true);
            $schedule->unsetProperty('plain_vars', true);
            $schedule->slashesStrip();

            // save history for status change of production schedule
            Documents_History::saveData($this->registry, array(
                'model' => $schedule,
                'action_type' => 'status',
                'new_model' => Documents::searchOne(
                    $this->registry,
                    array(
                        'where' => array("d.id = '{$schedule->get('id')}'"),
                        'sanitize' => true,
                    )
                ),
                'old_model' => $old_model,
            ));
        } else {
            // save of additional vars failed
            $db->FailTrans();
            $this->messages->setError(
                $this->i18n(
                    'error_documents_edit_failed',
                    array($schedule->get('type_name'))
                )
            );
        }
        $schedule->sanitize();

        // 3. create transfer (if recipe includes commodity articles, it usually does)
        if (!$db->HasFailedTrans() && $quantity) {
            $this->createMaterialsTransfer($schedule);
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if (!$result) {
            return $this->prepareErrorResponse($this->messages->getErrors());
        }

        // display success message
        $this->messages->setMessage(sprintf('%s %s (%s):',
            $this->i18n('plugin_start_production'),
            $schedule->get('full_num'),
            $schedule->getPlainVarValue('product_name')
        ));
        $this->messages->setMessage($this->i18n('plugin_message_production_started_success'));

        // return to home screen after success
        return $this->home();
    }

    /**
     * Loads screen for additional selection of materials and quantities to be
     * transfered to production warehouse - after production has started
     *
     * @return string - fetched content and/or operation result
     */
    public function loadTransferMaterials() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id']);
        if (!$schedule) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_invalid_schedule_progress')
            ));
        }

        // get route card by id set in production schedule
        $route_card = $schedule->getRouteCard();

        // route card missing or not found
        if (!$route_card) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_missing_route_card')
            ));
        }

        // define id of warehouse to get materials from (source warehouse of transfer)
        if ($this->request['warehouse_data']) {
            $wid = explode('_', strval($this->request['warehouse_data']));
            $wid = end($wid);
        } else {
            $wid = Production_Custom_Factory::defineWarehouse($this->registry, 'material_warehouse');
        }

        // warehouse model
        $warehouse = null;
        if ($wid) {
            /** @var Finance_Warehouse $warehouse */
            $warehouse = Finance_Warehouses::searchOne(
                $this->registry,
                array(
                    'where' => array(
                        'fwh.id = \'' . $wid . '\'',
                        'fwh.active = 1',
                    ),
                )
            );
        }

        if (!$warehouse) {
            return $this->prepareErrorResponse(array(
                $this->i18n(
                    'plugin_error_no_material_warehouse',
                    array(
                        $this->i18n(
                            'plugin_error_settings_link',
                            array(
                                '<span class="strong pointer spanlink" onclick="production.settings(\'' . self::CONFIG_PREFIX . 'material_warehouse\');">',
                                '</span>'
                            )
                        )
                    )
                )
            ));
        }

        $lang = $this->registry['lang'];
        $lang_files = array(
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_warehouses_documents.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_documents_types.ini',
        );
        $this->loadI18NFiles($lang_files);

        $prec = $this->registry['config']->getSectionParams('precision');

        // prepare variables and data of material transfer for display in interface
        $transfer = $this->prepareLoadMaterialsTransfer($schedule, $route_card, $warehouse);
        $gt2 = $transfer->get('grouping_table_2');

        return $this->fetch(
            '_start.html',
            array(
                'plugin_action' => 'loadTransferMaterials',
                'document' => $schedule,
                'model' => $transfer->sanitize(),
                'existing_batches' => $transfer->get('existing_batches'),
                'existing_serials' => $transfer->get('existing_serials'),
                'existing_custom' => $transfer->get('existing_custom'),
                'currencies_available' => $gt2['plain_vars']['currency']['options'],
                'warehouses' => array($warehouse->sanitize()),
                'warehouse_data_options' => Production_Custom_Factory::getWarehouseDataOptions($this->registry),
                'price_prec' => sprintf('%%.%dF', $prec['gt2_rows']),
            ),
            $lang_files
        );
    }

    /**
     * Creates additional transfer of selected materials and quantities from specified
     * source warehouse to production warehouse designated for current production
     *
     * @see Production_Custom_Model::start - where initial transfer is created
     * @return string - fetched content and/or operation result
     */
    public function transferMaterials() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id']);
        if (!$schedule) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_invalid_schedule_progress')
            ));
        }

        // get route card by id set in production schedule
        $route_card = $schedule->getRouteCard();

        // route card missing or not found
        if (!$route_card) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_missing_route_card')
            ));
        }

        // check if there is at least one non-zero row for materials (commodities)
        $quantity = $this->request['quantity'] ?: array();
        $quantity_any = array_filter($quantity, function($el) { return $el > 0; });
        if (empty($quantity_any)) {
            return $this->prepareErrorResponse(array(
                $this->i18n('plugin_error_commodities_transfer_no_quantity')
            ));
        }

        // create transfer
        $result = $this->createMaterialsTransfer($schedule);

        if (!$result) {
            return $this->prepareErrorResponse($this->messages->getErrors());
        }

        // display success message
        $this->messages->setMessage($this->i18n('plugin_message_manage_quantity_success'));

        // return to home screen after success
        return $this->home();
    }

    /**
     * Review progress of production schedule
     *
     * @return string - fetched content and/or operation result
     */
    public function review() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id']);
        if (!$schedule) {
            $this->messages->setError($this->i18n('plugin_error_invalid_data'));
            return $this->home();
        }
        $schedule->unsanitize();

        // prepare used and wasted quantities so far
        $saved_qty = $schedule->getMaterialProgress();

        // data to display is based on production GT of schedule
        $gt = $schedule->getGroupingVarValues(array(''), true, 'pp_group');

        // keep track which row is the first one per each tech card and each
        // step from tech card
        $card_idx = $step_idx = array();

        $prec = $this->registry['config']->getSectionParams('precision');
        $quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);

        foreach ($gt as $ridx => &$row) {
            // id of tech card
            $card_key = $row['pp_tech_id'];
            if (!isset($card_idx[$card_key])) {
                // check if > 0, there should be no blank values for var though
                if ($card_key) {
                    // get 'current_specification_file' from Technological Card
                    $tech_card = new Technological_Card(
                        $this->registry,
                        array(
                            'id' => $card_key,
                            'type' => $this->get('type_technological_card')
                        ));
                    $row['file'] = $tech_card->getSpecificationFile();
                }

                $row['card_rowspan'] = 1;
                $card_idx[$card_key] = $ridx;
            } else {
                $gt[$card_idx[$card_key]]['card_rowspan']++;
            }

            // step key
            $step_key = $row['pp_tech_id'] . '_' . $row['pp_step'];
            if (!isset($step_idx[$step_key])) {
                $row['step_rowspan'] = 1;
                $step_idx[$step_key] = $ridx;
            } else {
                $gt[$step_idx[$step_key]]['step_rowspan']++;
            }

            // set deviation class and quantities into row
            $ukey = $step_key . '_' . $row['pp_article_id'];
            $saved_qty[$ukey]['total'] = $row['pp_quantity'];
            $row['used_quantity'] = sprintf($quantity_prec_format, isset($saved_qty[$ukey]['used']) ? $saved_qty[$ukey]['used'] : false);
            $row['waste_quantity'] = sprintf($quantity_prec_format, isset($saved_qty[$ukey]['waste']) ? $saved_qty[$ukey]['waste'] : 0);
        }

        // calcuate deviation for each row from GT of route card
        $devs =
            $schedule->calculateProgressDeviation(
                array_map(function($a) { return isset($a['total']) ? $a['total'] : 0; }, $saved_qty),
                array_map(function($a) { return isset($a['used']) ? $a['used'] : null; }, $saved_qty)
            );

        foreach ($gt as $ridx => &$row) {
            $ukey = Production_Custom_Factory::buildUniqueKey($row, 'pp_');
            if (isset($devs[$ukey])) {
                $row['class'] = $schedule->defineClass($devs[$ukey]);
            }
        }

        return $this->fetch('_review.html', array(
            'document' => $schedule->sanitize(),
            'data' => $gt,
            'vars' => $schedule->get('assoc_vars'),
        ));
    }

    /**
     * Review saved data in a production timesheet so far
     */
    public function reviewTimesheet() {

        $data = array();
        if ($this->request['timesheet_id']) {
            /** @var Production_Timesheet $timesheet */
            $timesheet = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'filters' => array(
                        'where' => array(
                            "d.id = '{$this->request['timesheet_id']}'",
                            "d.type = '{$this->get('type_production_timesheet')}'",
                        ),
                    ),
                    'model_name' => 'Production_Timesheet',
                ));
            if ($timesheet) {
                $timesheet = reset($timesheet);
                $data = $timesheet->getGroupingVarValues(array('diary_article_name', 'diary_real_quantity', 'diary_measure'));
                array_walk($data, function(&$row) {
                    unset($row['num']);
                    $row['diary_real_quantity'] = floatval($row['diary_real_quantity']);
                });
                $data = array_filter($data, function($row) {
                    return $row['diary_real_quantity'] > 0;
                });
            }
        }

        return $this->fetch(
            '_timesheet_review.html',
            array('data' => $data)
        );
    }

    /**
     * Load screen to manage production
     *
     * @return string - fetched content and/or operation result
     */
    public function loadManage() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id']);

        // invalid data or record not found
        if (!$schedule) {
            $this->messages->setError($this->i18n('plugin_error_invalid_data'));
            return $this->home();
        }

        /** @var Production_Timesheet $timesheet */
        $timesheet = null;
        if ($this->request['timesheet_id']) {
            // get timesheet if existing, check if finished, if user can view AND edit it
            $timesheet = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'filters' => array(
                        'where' => array(
                            "d.id = '{$this->request['timesheet_id']}'",
                            "d.type = '{$this->get('type_production_timesheet')}'",
                        ),
                    ),
                    'model_name' => 'Production_Timesheet',
                ));
            if ($timesheet) {
                $timesheet = reset($timesheet);
                $timesheet->unsanitize();
            }

            // invalid data or record not found
            if (!$timesheet || $timesheet->getEditAllowed() != 'allowed') {
                $this->messages->setError($this->i18n('plugin_error_invalid_data'));
                return $this->home();
            }
        } else {
            /** @var Documents_Type $type */
            $type = Documents_Types::searchOne($this->registry,
                array(
                    'where' => array(
                        'dt.id = ' . $this->get('type_production_timesheet'),
                        'dt.active = 1',
                    ),
                    'sanitize' => true,
                ));

            if (!$type) {
                $this->messages->setError($this->i18n('error_documents_add_failed', array($this->i18n('plugin_reported_for_the_day'))));
                $this->messages->setError($this->i18n('error_invalid_type'));
                return $this->home();
            }

            $params = array(
                'date' => $this->request['date'],
                'type' => $type->get('id'),
                'name' => $type->get('default_name') ?: $type->get('name'),
                'customer' => $this->get('own_company'),
                'employee' => $this->registry['currentUser']->get('employee'),
                'workplace_id' => $this->request['workplace_id'],
            );
            foreach ($type->getAll() as $key => $value) {
                if (strpos($key, 'default_') === 0) {
                    $key = str_replace('default_', '', $key);
                    if (!empty($params[$key])) {
                        continue;
                    }
                    if (in_array($key, array('group', 'department')) && !is_numeric($value)) {
                        $method = 'getDefault' . ucfirst($key);
                        $params[$key] = $type->$method();
                        if (!$params[$key]) {
                            $params[$key] = PH_GROUP_FIRST;
                        }
                    } else {
                        $params[$key] = $value;
                    }
                }
            }
            $timesheet = new Production_Timesheet($this->registry, $params);
        }

        // define type of selected workplace
        $type_workplace = $timesheet->getTypeWorkplace();

        // get production GT from schedule
        $gt = $schedule->getGroupingVarValues(array(''), false, 'pp_group');

        // filter rows applicable for the selected workplace
        $gt = array_filter($gt, function($row) use ($type_workplace) {
            return !empty($row['pp_type_workplace']) && $row['pp_type_workplace'] == $type_workplace;
        });

        // invalid data or record not found
        if (!$type_workplace || !$gt) {
            $this->messages->setError($this->i18n('plugin_error_invalid_data'));
            return $this->home();
        }

        // load i18n files for documents
        $lang_file_doc = sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $this->registry['lang'], '/documents.ini');
        $this->loadI18NFiles($lang_file_doc);

        //$prec = $this->registry['config']->getParam('precision', 'gt2_quantity');

        $tgt = array();
        if (!$this->request['timesheet_id']) {
            //TODO Add tasklist support in Stage 2
            //TODO uncomment setting 'type_workplace_tasklist' in config.php and remove the line below
            //$this->set('type_workplace_tasklist', 11, true);
            // search for tasklist document to pre-load data from
            /** @var Advanced_Document $tasklist */
            /* $tasklist = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'filters' => array(
                        'where' => array(
                            "d.type = '{$this->get('type_workplace_tasklist')}'",
                            "d.active = '1'",
                            "d.date = '{$this->request['date']}'",
                            "a____workplace_id = '{$this->request['workplace_id']}'",
                            "a____workshift_id = '{$this->request['workshift_id']}'",
                            "a____detail_production_schedule_id = '{$this->request['schedule_id']}'",
                        ),
                    ),
                    'action' => 'custom',
                    'model_name' => 'Workplace_Tasklist',
                ));
            if ($tasklist) {
                $tasklist = reset($tasklist);
                // copy notes
                $timesheet->set('notes', $tasklist->get('notes'), true);

                $tgt = $tasklist->getGroupingVarValues(
                    array(
                        // field identifying schedule
                        'detail_production_schedule_id',
                        // fields forming unique key of row
                        'detail_tech_id',
                        'detail_step',
                        'detail_article_id',
                        // fields for data to fill in screen
                        'detail_quantity',
                        'detail_time',
                    ), false);
                // filter data only for the currently processed schedule
                $tgt = array_filter($tgt, function($a) use ($this) {
                    return $a['detail_production_schedule_id'] == $this->request['schedule_id'];
                });

                // rebuild array with unique keys identifying each row
                $tgt = array_combine(
                    array_map(
                        array('Production_Custom_Factory', 'buildUniqueKey'),
                        $tgt,
                        array_fill(0, count($tgt), 'detail_')
                    ),
                    array_map(function($a) {
                        return array(
                            'diary_real_quantity' => $a['detail_quantity'],
                            'diary_time' => $a['detail_time']
                        );
                    }, $tgt)
                );
            } */

        } else {
            // flag that last row of production is present and product quantity can be added
            if ($timesheet->get('status') == 'closed' &&
            Production_Custom_Factory::getCommodities($this->registry, array($timesheet->getPlainVarValue('product_id')))) {
                // check if last row of GT is for current type of workplace
                $last_row_visible = $schedule->getGroupingVarValues(array('pp_type_workplace'), false);
                $last_row_visible = $last_row_visible ? end($last_row_visible) : false;
                $last_row_visible = $last_row_visible ? $last_row_visible['pp_type_workplace'] == $type_workplace : false;
                if ($last_row_visible) {
                    $timesheet->set('load_store', true, true);
                }
            }
        }

        $this->registry->set('get_old_vars', true, true);
        // get all assoc vars (forced)
        $avars = $schedule->getAssocVars(true);
        // change action in order to prepare validation of fields
        $custom_action = $this->registry['action'];
        $this->registry->set('action', (!$timesheet->get('id') ? 'add' : 'edit'), true);
        $timesheet->getVarsForTemplate();
        $this->registry->set('get_old_vars', false, true);
        $this->registry->set('action', $custom_action, true);

        // prepare additional vars and data for timesheet
        $tvars = $timesheet->get('vars') ?: array();
        foreach ($tvars as &$var) {
            if ($var['grouping'] && $var['name'] == 'diary_group') {
                // start processing of GT variable
                unset($var['t_width']);
                $var['t_custom_class'] = $var['name'] . ' reports_table';
                $var['edit_fields'] = array('diary_real_quantity', 'diary_time', 'diary_real_waste', 'diary_notes');
                foreach ($var['names'] as $idx => $name) {
                    if (in_array($name, array('diary_tech_code', 'diary_step'))) {
                        $var['hidden'][$idx] = 1;
                        if ($name == 'diary_step') {
                            $var['labels'][array_search('diary_tech_name', $var['names'])] .= ', ' . $var['labels'][$idx];
                        }
                    } elseif (in_array($name, $var['edit_fields'])) {
                        if (empty($var['js_methods'][$idx])) {
                            $var['js_methods'][$idx] = array();
                        }
                        if (empty($var['js_methods'][$idx]['onfocus'])) {
                            $var['js_methods'][$idx]['onfocus'] = '';
                        }
                        $var['js_methods'][$idx]['onfocus'] .= 'this.removeClassName(\'erred\');';
                        $var['custom_class'][$idx] .= ' ' . $name;
                    }
                }

                // visible rows, data, qty validation!!!!
                // fill in initial data
                if (!$timesheet->get('id')) {
                    // initial state of each added row in GT
                    $blank_row = array_fill(0, count($var['names']), '');
                    foreach ($gt as $ridx => $row) {
                        // set values from tasklist, if there is one (not used in Stage 1)
                        if (!empty($tgt)) {
                            $ukey = Production_Custom_Factory::buildUniqueKey($row, 'pp_');
                            if (!empty($tgt[$ukey])) {
                                $row = $row + $tgt[$ukey];
                            }
                        }

                        $var['values'][$ridx] = $blank_row;
                        $var['values_id'][$ridx] = array();

                        foreach ($row as $key => $value) {
                            // mapping of fields from schedule into timesheet
                            switch ($key) {
                                case 'pp_number':
                                    $fidx = array_search('diary_card_number', $var['names']);
                                    break;
                                default:
                                    $fidx = array_search(str_replace('pp_', 'diary_', $key), $var['names']);
                                    if ($fidx === false) {
                                        $fidx = array_search(str_replace('pp_', 'diary_planned_', $key), $var['names']);
                                    }
                            }
                            if ($fidx !== false) {
                                $var['values'][$ridx][$fidx] = $value;
                                // if variable is an autocompleter, prepare the id value as well
                                if (!empty($var['autocomplete']) && !empty($var['autocomplete'][$var['names'][$fidx]]['id_var'])) {
                                    $id_var = str_replace('diary_', 'pp_', $var['autocomplete'][$var['names'][$fidx]]['id_var']);
                                    if (isset($row[$id_var])) {
                                        $var['values_id'][$ridx][$fidx] = $row[$id_var];
                                    }
                                }
                            }
                        }
                    }
                }

                // add artificial fields+data to GT (those are not really present in model):

                // dummy model - get specification scheme for each Tech Card for display in dashlet
                $tech_card = new Technological_Card(
                    $this->registry,
                    array(
                        'type' => $this->get('type_technological_card'),
                    ));

                // add the file field to GT at the specified position
                $splice_idx = array_search('diary_description', $var['names']) + 1;
                $tcard_idx = array_search('diary_tech_id', $var['names']);
                foreach ($var as $key => &$value) {
                    if ($key == 'values' && !empty($value)) {
                        foreach ($value as &$row_values) {
                            $file = '';
                            if (!empty($row_values[$tcard_idx]) && $row_values[$tcard_idx] != $tech_card->get('id')) {
                                $tech_card->set('id', $row_values[$tcard_idx], true);
                                $file = $tech_card->getSpecificationFile();
                            }
                            array_splice($row_values, $splice_idx, 0, array($file));
                        }
                    } elseif (is_array($value) && isset($value[0])) {
                        switch ($key) {
                            case 'names':
                                $val = 'file';
                                break;
                            case 'types':
                                $val = 'file_upload';
                                break;
                            case 'labels':
                                $val = $this->i18n('plugin_scheme');
                                break;
                            default:
                                $val = '';
                        }
                        array_splice($value, $splice_idx, 0, array($val));
                    }
                }
                unset($value);

                // dummy model - get specification scheme for each Tech Card for display in dashlet
                $tech_card = new Technological_Card(
                    $this->registry,
                    array(
                        'type' => $this->get('type_technological_card'),
                    ));
                $material_qty = array();

                // add column with material quantity to allow material calculation
                $splice_idx = array_search('diary_real_quantity', $var['names']);
                foreach ($var as $key => &$value) {
                    if ($key == 'values' && !empty($value)) {
                        foreach ($value as &$row_values) {
                            $ukey = implode('_', array(
                                $row_values[array_search('diary_step', $var['names'])],
                                $row_values[array_search('diary_article_id', $var['names'])],
                            ));

                            $tech_card_id = $row_values[$tcard_idx];
                            if (empty($material_qty[$tech_card_id])) {
                                $tech_card->set('id', $tech_card_id, true);
                                $mat_group = $tech_card->getGroupingVarValues(
                                    ['material_article_id', 'material_step', 'material_quantity']
                                );
                                foreach($mat_group as $mRow) {
                                    $mkey = "{$mRow['material_step']}_{$mRow['material_article_id']}";
                                    $material_qty[$tech_card_id][$mkey] = $mRow['material_quantity'];
                                }
                            }

                            array_splice($row_values, $splice_idx, 0,
                                (!empty($material_qty[$tech_card_id][$ukey]) ? array($material_qty[$tech_card_id][$ukey]) : array(0)));
                        }
                    } elseif (is_array($value) && isset($value[0])) {
                        switch ($key) {
                            case 'names':
                                $val = 'material_quantity';
                                break;
                            case 'types':
                                $val = 'text';
                                break;
                            case 'hidden':
                                $val = 1;
                                break;
                            case 'labels':
                                $val = $this->i18n('plugin_material_quantity');
                                break;
                            case 'custom_class':
                                $val = ' material_quantity';
                                break;
                            default:
                                $val = '';
                        }
                        array_splice($value, $splice_idx, 0, array($val));
                    }
                }
                unset($value);

                // prepare used and wasted quantities so far (excluding current timesheet)
                $saved_qty = $schedule->getMaterialProgress($timesheet->get('id'));

                $splice_idx = array_search('diary_planned_quantity_max', $var['names']) + 1;
                foreach ($var as $key => &$value) {
                    if ($key == 'values' && !empty($value)) {
                        foreach ($value as &$row_values) {
                            $ukey = implode('_', array(
                                $row_values[array_search('diary_tech_id', $var['names'])],
                                $row_values[array_search('diary_step', $var['names'])],
                                $row_values[array_search('diary_article_id', $var['names'])],
                            ));
                            array_splice($row_values, $splice_idx, 0, (!empty($saved_qty[$ukey]) ? array_values($saved_qty[$ukey]) : array(0, 0)));
                        }
                    } elseif (is_array($value) && isset($value[0])) {
                        foreach (array('used', 'waste') as $field_idx => $field_name) {
                            switch ($key) {
                                case 'names':
                                    $val = $field_name;
                                    break;
                                case 'labels':
                                    $val = $this->i18n("plugin_{$field_name}_quantity");
                                    break;
                                case 'types':
                                case 'text_align':
                                case 'width':
                                    $val = $value[$splice_idx];
                                    break;
                                case 'hidden':
                                    $val = ($field_name == 'waste');
                                    break;
                                default:
                                    $val = '';
                            }
                            array_splice($value, $splice_idx + $field_idx , 0, array($val));
                        }
                    }
                }
                unset($value);
                // end processing of GT variable

            } elseif (!$var['grouping'] && !$var['gt2']) {
                // only the plain vars
                if (!$timesheet->get('id')) {
                    // set values for new model
                    if (isset($avars[$var['name']])) {
                        $var['value'] = $avars[$var['name']]['value'];
                    } elseif ($this->request->get($var['name'])) {
                        $var['value'] = $this->request->get($var['name']);
                    } elseif ($this->request->get(str_replace('_name', '_id', $var['name']))) {
                        $var['value'] =
                            $this->registry['db']->GetOne(
                                'SELECT name FROM ' . DB_TABLE_NOMENCLATURES_I18N . ' WHERE parent_id = ? AND lang = ?',
                                array(
                                    $this->request->get(str_replace('_name', '_id', $var['name'])),
                                    $this->registry['lang']
                                )
                            );
                    } elseif ($var['name'] == 'production_schedule_name') {
                        $var['value'] = $schedule->get('full_num');
                    } elseif ($var['name'] == 'production_schedule_id') {
                        $var['value'] = $schedule->get('id');
                    }
                    // set as a basic property to be used for display
                    $timesheet->set($var['name'], $var['value'], true);
                }
                $var['hidden'] = 1;

                //only show the variable production_intended_quantity
                if ($var['name'] == 'production_intended_quantity') {
                    $var['hidden'] = 0;
                    $var['readonly'] = 0;
                }
            }
        }
        unset($var);
        // end processing of additional vars
        $timesheet->set('vars', $tvars, true);

        $timesheet->getLayoutVars();

        $vars_layouts = $timesheet->isDefined('vars') ? $timesheet->get('vars') : array();
        foreach ($vars_layouts as $layout_id => &$layout_vars) {
            // get custom layouts
            $timesheet->getLayoutsDetails($layout_id);
        }

        $layouts_details = $timesheet->get('layouts_details');
        $last_layouts = array();
        if (!empty($layouts_details['notes'])) {
            $last_layouts['notes'] = array('view' => 1, 'edit' => 1) + $layouts_details['notes'];
            unset($layouts_details['notes']);
        }
        $layouts_details = $layouts_details + $last_layouts;
        $timesheet->set('layouts_details', $layouts_details, true);

        return $this->fetch(
            '_manage.html',
            array(
                'document' => $timesheet->sanitize(),
            ));
    }

    /**
     * Manage production - save timesheet or save+finish it (also updating stock
     * availability)
     *
     * @return string - fetched content and/or operation result
     */
    public function manage() {
        /** @var Production_Timesheet $old_model */
        $old_model = null;
        if ($this->request['id']) {
            $old_model = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'filters' => array(
                        'where' => array(
                            "d.id = '{$this->request['id']}'",
                            "d.type = '{$this->get('type_production_timesheet')}'",
                        ),
                    ),
                    'model_name' => 'Production_Timesheet',
                ));
            if ($old_model) {
                $old_model = reset($old_model);
                $old_model->unsanitize();
            }

            // invalid data submitted or record not found
            if (!$old_model || $old_model->getEditAllowed() != 'allowed') {
                $this->messages->setError($this->i18n('plugin_error_invalid_data'));
                return $this->home();
            }
        } else {
            $old_model = new Production_Timesheet(
                $this->registry,
                array(
                    'type' => $this->get('type_production_timesheet'),
                    'status' => 'opened',
                    'status_name' => $this->i18n('documents_status_opened'),
                    'ownership' => 'forwarded',
                    'workplace_id' => $this->request['workplace_id'],
                ));
        }

        // load i18n files for documents
        $this->loadI18NFiles(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $this->registry['lang'], '/documents.ini')
        );

        // build model from submitted data
        /** @var Production_Timesheet $timesheet */
        $timesheet = Model_Factory::buildFromRequest($this->registry, 'Production_Timesheet');

        // validate submitted data for GT of timesheet
        if (!$timesheet->validateManage($old_model)) {
            $errors = array_values($this->messages->getErrors());
            // get erred row numbers in order to highlight them in interface
            $row_keys = array_filter(array_keys($this->messages->getErrors()), function($a) { return $a > 0; });
            if ($row_keys) {
                $errors[0] = "<script type=\"text/javascript\">production.validateManage([" . implode(',', $row_keys) . "]);</script>" . $errors[0];
            }
            return $this->prepareErrorResponse($errors);
        }

        // validation went well and data can be saved
        if (!$timesheet->get('id')) {
            $timesheet->set('validity_term', General::strftime($this->i18n('date_iso')), true);
            $timesheet->set('customer_name', $this->get('own_company_name'), true);
        }
        foreach (array('status', 'status_name', 'substatus', 'substatus_name', 'ownership') as $k) {
            $timesheet->set($k, $old_model->get($k), true);
        }
        if ($this->request['after_action'] == 'finish' && $timesheet->get('status') != 'closed') {
            $timesheet->set('status', 'closed', true);
            $timesheet->set('substatus', '0', true);
        }
        $this->registry->set('get_old_vars', true, true);
        $old_model->getVars();
        $this->registry->set('get_old_vars', false, true);
        $timesheet->getVars();

        // collect changed quantities (difference between before and after state)
        $changed_quantity = array();

        if ($this->request['after_action'] == 'finish') {
            $changed_quantity = $timesheet->prepareChangedQuantity($old_model);

            $qp = $this->registry['config']->getParam('precision', 'gt2_quantity');
            $changed_quantity_filtered = array_filter(
                $changed_quantity,
                function($a) use ($qp) {
                    return bccomp($a['changed_diary_real_quantity'], 0, $qp) || bccomp($a['changed_diary_real_waste'], 0, $qp);
                });

            // check if lightbox for selection of batches should be displayed
            if ($changed_quantity_filtered &&
            !array_filter(array_keys($this->request->getAll()), function($a) { return preg_match('{^batch_\d+$}', $a); })) {
                $av_qty = $timesheet->checkMultipleBatches($changed_quantity);
                if ($av_qty !== false) {
                    $lang = $this->registry['lang'];
                    $lang_files = array(
                        PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_warehouses_documents.ini',
                        PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_documents_types.ini',
                    );

                    $data = array('scripts' => array(
                        "production.loadLightbox(" . json_encode(array(
                            'title' => $this->i18n('plugin_manage_batch_selection'),
                            'content' => $this->fetch(
                                '_manage_batches.html',
                                array(
                                    'data' => $av_qty,
                                    'measure_num' => self::MEASURE_NUM,
                                    'measure_options' => Dropdown::getMeasures(array($this->registry)),
                                ),
                                $lang_files
                            ),
                        )) . ");",
                    ));
                    return json_encode($data);
                }
            }

            $changed_quantity = $changed_quantity_filtered;
        }

        $action = ($timesheet->get('id') ? 'edit' : 'add');
        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];

        $db->StartTrans();
        if ($timesheet->save() && $timesheet->setStatus()) {
            // strip slashes because we will be using data from model next
            $timesheet->slashesStrip();

            if ($action == 'add') {
                $schedule = $this->getProductionSchedule($timesheet->getPlainVarValue('production_schedule_id'));
                if ($schedule) {
                    $action = 'transform';
                    $transform_params = array(
                        'origin_method' => 'Dashlet',
                        'origin_method_id' => $this->registry['dashlet'] ? $this->registry['dashlet']->get('id') : '0',
                        'origin_model' => 'Document',
                        'origin_id' => $schedule->get('id'),
                        'origin_full_num' => $schedule->get('full_num'),
                        'origin_name' => $schedule->get('name') ?: $schedule->get('type_name'),
                    );
                    $timesheet->set('transform_params', serialize($transform_params), true);

                    $destination_params = array(
                        'origin_method' => !empty($transform_params['origin_method']) ? $transform_params['origin_method'] : '',
                        'origin_method_id' => !empty($transform_params['origin_method_id']) ? $transform_params['origin_method_id'] : '',
                        'destination_model' => 'Document',
                        'destination_id' => $timesheet->get('id'),
                        'destination_full_num' => $timesheet->get('full_num'),
                        'destination_name' => $timesheet->get('name') ?: $timesheet->get('type_name'),
                    );
                    $schedule->set('destination_params', $destination_params, true);

                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $schedule,
                            'action_type' => 'create',
                        ));
                }
            }

            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $timesheet,
                    'action_type' => $action,
                    'new_model' => $timesheet,
                    'old_model' => $old_model,
                ));
            // strip slashes again if escaped during history save
            if ($timesheet->slashesEscaped) {
                $timesheet->slashesStrip();
            }

            // set back to initial value
            if ($action == 'transform') {
                $action = 'add';
            }

            if ($action == 'add') {
                // assign users
                $timesheet->defaultAssign();

                // save transformation relation to schedule
                $timesheet->set('clone_transform', 'transformed', true);
                $timesheet->set('origin_id', $timesheet->getPlainVarValue('production_schedule_id'), true);
                $timesheet->set('origin_model', 'Document', true);
                $timesheet->insertRelatives();
            }

            // if there are any changes in quantity
            if ($this->request['after_action'] == 'finish' && $changed_quantity) {
                // update warehouse availability of used, wasted and side-production materials
                if ($timesheet->updateMaterialsAvailability($changed_quantity)) {
                    // success message
                    if ($timesheet->get('availability_updated')) {
                        $this->messages->setMessage($this->i18n('plugin_message_manage_quantity_success'));
                    }
                } else {
                    $db->FailTrans();
                }
            }
        } else {
            $db->FailTrans();
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if ($result) {
            $this->messages->setMessage(
                $this->i18n("message_documents_{$action}_success",
                    array($timesheet->get('name'))
                ), '', -5);

            if ($this->request['after_action'] == 'finish' &&
            Production_Custom_Factory::getCommodities($this->registry, array($this->request['product_id']))) {
                // check if last row of GT is for current type of workplace
                $last_row_visible = $timesheet->getSchedule()->getRouteCard()->getGroupingVarValues(array('card_type_workplace'), false);
                $last_row_visible = $last_row_visible ? end($last_row_visible) : false;
                $last_row_visible = $last_row_visible ? $last_row_visible['card_type_workplace'] == $timesheet->getTypeWorkplace() : false;

                // redirect to product store screen
                if ($last_row_visible) {
                    $result = array();
                    $result['content'] = $this->loadStore($timesheet);
                    // display errors
                    if (strpos($result['content'], '{"errors":') === 0) {
                        $result = array_merge($result, json_decode($result['content'], true));
                        $result['content'] = $this->home();
                    }
                    return json_encode($result);
                }
            }

            // by default return to home screen
            return $this->home();
        } else {
            // display error messages (stay on the same screen)
            $this->messages->setError(
                $this->i18n("error_documents_{$action}_failed",
                    array($timesheet->get('name'))
                ), '', -1);
            return $this->prepareErrorResponse($this->messages->getErrors());
        }
    }

    /**
     * Loads screen for updating stock availability of produced article
     *
     * @param Production_Timesheet $timesheet - timesheet model (if already available)
     * @return string - fetched content and/or operation result
     */
    public function loadStore(Production_Timesheet $timesheet = null) {

        if (!$timesheet && $this->request['id']) {
            $timesheet = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'filters' => array(
                        'where' => array(
                            "d.id = '{$this->request['id']}'",
                            "d.type = '{$this->get('type_production_timesheet')}'",
                        ),
                    ),
                    'model_name' => 'Production_Timesheet',
                ));
            if ($timesheet) {
                $timesheet = reset($timesheet);
            }
        }

        if (!$timesheet) {
            $this->messages->unset_vars('messages');
            $this->messages->setError($this->i18n('plugin_error_invalid_data'));
            return $this->home();
        }

        // load i18n files for documents
        $lang = $this->registry['lang'];
        $lang_files = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $lang, '/documents.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance_warehouses_documents.ini'),
        );
        $this->loadI18NFiles($lang_files);

        // get product warehouse model
        $wid = Production_Custom_Factory::defineWarehouse($this->registry, 'product_warehouse');
        $warehouse = null;
        if ($wid) {
            /** @var Finance_Warehouse $warehouse */
            $warehouse = Finance_Warehouses::searchOne(
                $this->registry,
                array('where' => array(
                    'fwh.id = \'' . $wid . '\'',
                    'fwh.active = 1',
                ))
            );
        }

        if (!$warehouse) {
            return $this->prepareErrorResponse(array(
                $this->i18n(
                    'plugin_error_no_product_warehouse',
                    array(
                        $this->i18n(
                            'plugin_error_settings_link',
                            array(
                                '<span class="strong pointer spanlink" onclick="production.settings(\'' . self::CONFIG_PREFIX . 'product_warehouse\');">',
                                '</span>'
                            )
                        )
                    )
                )
            ));
        } elseif ($warehouse->get('locker') && $warehouse->get('locker_status') == 'opened') {
            $link = sprintf('%s?%s=%s&amp;%s=%s&amp;%s=%s&amp;%s=%s',
                    $_SERVER['PHP_SELF'],
                    $this->registry['module_param'], 'finance',
                    $this->registry['controller_param'], 'warehouses_documents',
                    'warehouses_documents', 'view',
                    'view', $warehouse->get('locker'));
            return $this->prepareErrorResponse(
                $this->i18n('error_finance_warehouse_locked', array($warehouse->get('name'), $link)),
                true
            );
        }

        // get product nomenclature
        $product_id = $timesheet->getPlainVarValue('product_id');
        $filters = array(
            'where' => array(
                'n.id IN (\'' . $product_id . '\')',
                'n.subtype = "commodity"',
            ),
        );
        $nom = Nomenclatures::searchOne($this->registry, $filters);

        if (empty($nom)) {
            $this->messages->setError(
                $this->i18n('plugin_production_product_not_commodity',
                    array($timesheet->getPlainVarValue('product_name'))));
            return $this->home();
        }

        $reason = new Finance_Expenses_Reason(
            $this->registry,
            array(
                'type' => $this->get('type_expenses_reason'),
                'company_data' => $this->get('company_data'),
            )
        );
        $table = $reason->getGT2Vars();

        // modify GT2 vars
        $info_vars = array('article_alternative_deliverer', 'article_alternative_deliverer_name', 'article_description');
        foreach ($info_vars as $idx => $var_name) {
            $var = &$table['vars'][$var_name];
            $var['disabled'] = true;
            $var['hidden'] = $var_name == 'article_alternative_deliverer';
            $var['position'] = $idx + 100;
            $var['width'] = '120';
            if ($var_name == 'article_alternative_deliverer_name') {
                $var['autocomplete'] = array(
                    'type' => 'documents',
                    'view_mode' => 'link',
                    'view_mode_url' => sprintf('%s?%s=documents&amp;documents=view&amp;view=', $_SERVER['PHP_SELF'], $this->registry['module_param']),
                    'fill_options' => array(
                        '$article_alternative_deliverer => <id>',
                        '$article_alternative_deliverer_name => <full_num>',
                    ),
                    'id_var' => 'article_alternative_deliverer',
                );
                $var['label'] = $this->i18n('plugin_production_schedule');
            } elseif ($var_name == 'article_description') {
                $var['label'] = $this->i18n('plugin_notes');
            }
        }
        unset($var);

        $show_vars = array_merge(
            array('article_code', 'article_name', 'article_measure_name', 'subtotal'),
            $info_vars
        );
        foreach ($table['vars'] as $var_name => &$var) {
            if (!in_array($var_name, $show_vars)) {
                $var['hidden'] = 1;
            } else {
                if (empty($var['custom_class'])) {
                    $var['custom_class'] = '';
                }
                $var['custom_class'] .= ' viewmode';
                // hide the arrow of the dropdown
                if ($var['type'] == 'dropdown') {
                    $var['back_label'] = '&nbsp;&nbsp;&nbsp;&nbsp;';
                    $var['back_label_style'] = 'background-color: #f1f1f1; margin: 0 0 -6px -20px; font-size: 1.7em;';
                }
                if ($var_name == 'article_name') {
                    $var['width'] = '';
                } elseif ($var_name == 'subtotal') {
                    $var['label'] = $this->i18n('plugin_cost');
                    $var['position'] = 99;
                }
            }
            $var['readonly'] = 1;
        }
        unset($var);

        $name = 'warehouse' . $wid . '_quantity';
        $table['vars'][$name] = array (
            'id' => $name,
            'name' => $name,
            'type' => 'text',
            'custom_class' => 'warehouse_quantity',
            'hidden' => 0,
            'readonly' => 0,
            'width' => '80',
            'label' => $this->i18n('quantity'),
            'help' => NULL,
            'js_methods' => array ('onkeyup' => 'gt2calc(this)'),
            'text_align' => 'right',
            'js_filter' => 'insertOnlyFloats',
            'position' => 97,
        );
        $name = 'warehouse' . $wid . '_available';
        $table['vars'][$name] = array (
            'id' => $name,
            'name' => $name,
            'type' => 'text',
            'hidden' => 1,
            'readonly' => 1,
            'width' => '80',
            'label' => $this->i18n('available'),
            'help' => NULL,
            'js_methods' => array ('onkeyup' => 'gt2calc(this)'),
            'text_align' => 'right',
            'js_filter' => 'insertOnlyFloats',
            'position' => 98,
        );
        uasort($table['vars'], function($a, $b) { return $a['position'] > $b['position']; });

        // hide plain vars
        foreach ($table['plain_vars'] as $var_name => $var_options) {
            $table['plain_vars'][$var_name]['hidden'] = 1;
        }
        $table['handover_direction'] = 'incoming';
        //hide all the buttons
        $table['hide_delete'] = true;
        $table['hide_multiple_rows_buttons'] = true;
        $table['show_select_buttons'] = false;
        $table['show_refresh_buttons'] = false;
        $table['width'] = '100%';

        $gt2_quantity_precision = $this->registry['config']->getParam('precision', 'gt2_quantity');
        $gt2_quantity_prec_format = '%.' . $gt2_quantity_precision . 'F';

        $wd = new Finance_Warehouses_Document($this->registry, array('type' => PH_FINANCE_TYPE_HANDOVER));
        $vars_settings = $wd->getBatchVarsSettings();
        $visible_batch_vars = array('available_quantity', 'expire', 'serial');
        foreach ($vars_settings as $var_name => $var) {
            $vars_settings[$var_name]['hidden'] = !in_array($var_name, $visible_batch_vars);
            $vars_settings[$var_name]['width'] = $var_name == 'serial' ? 200 : 100;
            if ($var_name == 'available_quantity') {
                $vars_settings[$var_name]['label'] = $this->i18n('plugin_produced_quantity');
            }
        }

        // prepare values
        $table['values'] = array(
            -1 => array(
                'article_id' => $nom->get('id'),
                'article_code' => $nom->get('code'),
                'article_second_code' => 'product',
                'price' => $nom->get('sell_price'),
                'last_delivery_price' => $nom->get('last_delivery_price'),
                'average_weighted_delivery_price' => $nom->get('average_weighted_delivery_price'),
                'article_name' => $nom->get('name'),
                'article_measure_name' => $nom->getPlainVarValue('measure_name'),
                'quantity' => '',
                'article_alternative_deliverer' => $timesheet->getPlainVarValue('production_schedule_id'),
                'article_alternative_deliverer_name' => $timesheet->getPlainVarValue('production_schedule_name'),
                'article_description' => $timesheet->get('notes'),
                // calculated cost price for the whole stored quantity
                'subtotal' => $timesheet->getSchedule()->calculateCostPrice(),
            ),
        );

        //get available quantities in the warehouse for the articles
        $qParams = array(
            'nom_id' => array($product_id),
            'customer_id' => $this->get('own_company'),
        );
        $available_quantities = $warehouse->getAvailableQuantity($qParams);

        //calculate the quantities for each row
        $available = array();
        $quantities = array();

        $articles = array($product_id => $nom);
        foreach ($table['values'] as $row_id => $row) {
            if (empty($articles[$row['article_id']]) || !empty($row['deleted'])) {
                //non-commodity article or deleted row
                unset($table['values'][$row_id]);
                continue;
            }
            $table['values'][$row_id]['vars_settings'] = $vars_settings;

            $table['values'][$row_id]['quantity'] = sprintf($gt2_quantity_prec_format, $row['quantity']);
            //set batch options in the row properties
            if ($articles[$row['article_id']]->get('has_batch')) {
                $batch_options = array(
                    'has_batch' => $articles[$row['article_id']]->get('has_batch'),
                    'has_serial' => $articles[$row['article_id']]->get('has_serial'),
                    'has_expire' => $articles[$row['article_id']]->get('has_expire'),
                    'has_batch_code' => $articles[$row['article_id']]->get('has_batch_code'),
                );
                $table['values'][$row_id] = array_merge($table['values'][$row_id], $batch_options);
            }

            //check availabilities in the warehouse
            if (!isset($table['values'][$row_id]['available_in'])) {
                $table['values'][$row_id]['available_in'] = array();
            }

            //incoming direction - just for compatibility set this warehouse here
            $table['values'][$row_id]['available_in'][] = $wid;
            //total quantity we set as available
            if (!empty($available_quantities[$row['article_id']]['quantity'])) {
                $available[$row_id][$wid] = $available_quantities[$row['article_id']]['quantity'];
            } else {
                $available[$row_id][$wid] = 0;
            }
            if (!$articles[$row['article_id']]->get('has_batch')) {
                $quantities[$row_id][$wid] = $table['values'][$row_id]['quantity'];
            }

            if ($articles[$row['article_id']]->get('has_batch')) {
                //if batch articles....JS will do the job
                $table['values'][$row_id]['warehouse' . $wid . '_quantity']  = sprintf($gt2_quantity_prec_format, 0);
            } else {
                $table['values'][$row_id]['warehouse' . $wid . '_quantity']  = sprintf($gt2_quantity_prec_format, $quantities[$row_id][$wid]);
            }
            $table['values'][$row_id]['warehouse' . $wid . '_available'] = sprintf($gt2_quantity_prec_format, $available[$row_id][$wid]);
        }

        $reason->set('grouping_table_2', $table, true);

        // if both product and materials have serial numbers they should be
        // mapped to each other on stock entry
        $articles_serial = $nom->get('has_batch') ? $timesheet->getSchedule()->prepareUnusedSerials() : false;

        return $this->fetch(
            '_store.html',
            array(
                'document' => $timesheet->sanitize(),
                'reason' => $reason->sanitize(),
                'warehouses' => array($warehouse->sanitize()),
                'articles_serial' => $articles_serial ? json_encode($articles_serial) : false,
            ),
            $lang_files
        );
    }

    /**
     * Stores produced quantity of product into product warehouse
     *
     * @return string - fetched content and/or operation result
     */
    public function store() {

        // prepare timesheet model
        /** @var Production_Timesheet $timesheet */
        $timesheet = Production_Custom_Factory::search(
            $this->registry,
            array(
                'filters' => array(
                    'where' => array(
                        "d.id = '{$this->request['id']}'",
                        "d.type = '{$this->get('type_production_timesheet')}'",
                    ),
                ),
                'model_name' => 'Production_Timesheet',
            ));
        if ($timesheet) {
            $timesheet = reset($timesheet);
            $timesheet->unsanitize();
        } else {
            $this->messages->unset_vars('messages');
            $this->messages->setError($this->i18n('plugin_error_invalid_data'));
            return $this->home();
        }

        // get product nomenclature
        $product_id = $timesheet->getPlainVarValue('product_id');
        $filters = array(
            'where' => array(
                'n.id IN (\'' . $product_id . '\')',
                'n.subtype = "commodity"',
            ),
        );
        $nom = Nomenclatures::search($this->registry, $filters);

        if (empty($nom)) {
            $this->messages->setError(
                $this->i18n('plugin_production_product_not_commodity',
                    array($timesheet->getPlainVarValue('product_name'))));
            return $this->home();
        }
        $nom = reset($nom);

        // load i18n files for documents
        $lang = $this->registry['lang'];
        $lang_files = array_merge(
            array(sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $lang, '/documents.ini')),
            FilesLib::readDir(PH_MODULES_DIR . 'finance/i18n/' . $lang, false, 'files_only', 'ini', true)
        );
        $this->loadI18NFiles($lang_files);

        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];
        /** @var User $user */
        $user = &$this->registry['currentUser'];
        $prec = $this->registry['config']->getSectionParams('precision');
        $date_iso = General::strftime($this->i18n('date_iso'));

        // get product warehouse id
        $wid = Production_Custom_Factory::defineWarehouse($this->registry, 'product_warehouse');

        // validate the intended quantity
        $intended_quantity = $timesheet->getPlainVarValue('production_intended_quantity');
        $warehouse_qty = $this->request["warehouse{$wid}_quantity"];
        foreach($warehouse_qty as $w_qty) {
            if ($w_qty > $intended_quantity) {
                return $this->prepareErrorResponse(array(
                   $this->i18n('error_production_intended_quantity_exceeded', array($w_qty, $intended_quantity)),
               ));
            }
        }

        /** @var Documents_Type $type */
        $type = Documents_Types::searchOne($this->registry,
            array(
                'where' => array(
                    'dt.id = ' . $this->get('type_production_record'),
                    'dt.active = 1',
                ),
                'sanitize' => true,
            ));
        if (!$type) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_documents_add_failed', array($this->i18n('document'))),
                $this->i18n('error_invalid_type'),
            ));
        }

        // get specified produced quantity
        $quantity = $qty_prec = 0;
        if ($this->request->get("warehouse{$wid}_quantity")) {
            $quantity = $this->request->get("warehouse{$wid}_quantity");
            $quantity = reset($quantity);
        }
        if ($timesheet->getPlainVarValue('product_measure') == self::MEASURE_NUM) {
            $quantity = floor($quantity);
            $quantity_prec_format = '%.0F';
        } else {
            $qty_prec = $prec['gt2_quantity'];
            $quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);
        }
        $quantity = sprintf($quantity_prec_format, round($quantity, $qty_prec));
        $this->request->set('quantity', array(-1 => $quantity), 'post', true);

        $db->StartTrans();

        $this->registry->set('edit_all', true, true);

        // 1. store product into warehouse
        $kind = 'product';
        // prepare all fields, in each model only applicable ones will be saved
        $default_data = array(
            'name' => '',
            'description' => '',
            'notes' => '',
            'location' => $this->i18n('plugin_product_warehouse'),
            'department' => $timesheet->get('department'),
            'group' => $timesheet->get('group'),
            'active' => 1,
            'is_portal' => $user->get('is_portal'),
            'employee' => $user->get('employee'),
            'employee1' => $user->get('employee'),
            'employee_name' => $user->get('employee_name') ?: ($user->get('firstname') . ' ' . $user->get('lastname')),
            'currency' => $this->get('currency'),
            'status' => 'finished',
            'company_data' => $this->get('company_data'),
            'customer' => $this->get('own_company'),
            'customer_name' => $this->get('own_company_name'),
            'issue_date' => $timesheet->get('date'),
            'date_of_payment' => $timesheet->get('date'),
        );

        // a temporary model to prepare input data for creation of real model with
        $wd = new Finance_Warehouses_Document(
            $this->registry,
            array(
                'type' => PH_FINANCE_TYPE_HANDOVER,
                'warehouse_data' => Production_Custom_Factory::defineWarehouse($this->registry, 'product_warehouse', true),
                'direction' => 'incoming',
            ) + $default_data
        );
        $wd->getGT2Vars();
        $gt2_values = $wd->get('grouping_table_2');
        $calc_price = !empty($gt2_values['calculated_price']) ? $gt2_values['calculated_price'] : 'price';

        $gt2_values = array($product_id => array('quantity' => 0) + reset($gt2_values['values']));
        // calculate value of 'calculated_price' field (price/last_delivery_price)
        // as cost price / quantity
        // (later it goes into last_delivery_price of nomenclature)
        $gt2_values[$product_id][$calc_price] = $quantity != 0 ? round($gt2_values[$product_id]['subtotal'] / $quantity, $prec['gt2_rows']) : 0;

        // keep the submitted batch data as-is, it will be re-submitted when
        // handover is created
        /** @var Nomenclature $nom */
        if ($nom->get('has_batch')) {
            $batch_keys = array_filter(
                array_keys($this->request->getAll('post')),
                function($a) { return strpos($a, 'article_-1_') === 0; }
            );
            $gt2_values[$product_id]['batch_data'] = array();
            foreach ($batch_keys as $bkey) {
                foreach ($this->request[$bkey] as $bidx => $bval) {
                    $gt2_values[$product_id]['batch_data'][$bidx][str_replace('article_-1_', '', $bkey)] = $bval;
                }
            }
        }

        $available_quantity = array();
        $available_quantity[$product_id] = array(
            'quantity' => $gt2_values[$product_id]['quantity'],
            'has_batch' => $nom->get('has_batch'),
            'batch_data' => isset($gt2_values[$product_id]['batch_data']) ? $gt2_values[$product_id]['batch_data'] : array(),
        );

        $qtys = array();
        $qtys[$product_id] = array('quantity' => $quantity);
        if ($nom->get('has_batch')) {
            $qtys[$product_id]['batch_data'] = $available_quantity[$product_id]['batch_data'];
            //check batch row quantity (produced quantity of a batch)
            $bdata = $available_quantity[$product_id]['batch_data'];
            if (count($bdata) != count(array_filter(array_column($bdata, 'quantity')))) {
                return $this->prepareErrorResponse(array(
                   $this->i18n('error_production_zero_batch_quantity'),
               ));
            }
        }

        $params = array(
            'wid' => $wid,
            'default_data' => $default_data,
            'qtys' => $qtys,
            'gt2_values' => $gt2_values,
            'available_quantity' => &$available_quantity,
            'direction' => 'incoming',
            'kind' => $kind,
        );
        if (!$timesheet->createHandover($params)) {
            $db->FailTrans();
        }

        if ($db->HasFailedTrans()) {
            $db->CompleteTrans();
            return $this->prepareErrorResponse(array_map(
                function($a) { return preg_replace('#article_\d+_#', 'article_-1_', $a); },
                $this->messages->getErrors())
            );
        }

        // 2. create production record - save batch codes and so on
        $params = array(
            'name' => $type->get('default_name') ?: $type->get('name'),
            'type' => $type->get('id'),
            'type_name' => $type->get('name'),
            'customer' => $this->get('own_company'),
            'customer_name' => $this->get('own_company_name'),
            'date' => $timesheet->get('date'),
            'employee' => $user->get('employee'),
            'employee_name' => $user->get('employee_name'),
            'office' => $user->get('office'),
            'office_name' => $user->get('office_name'),
            'department' => $type->getDefaultDepartment(),
            'group' => $type->getDefaultGroup(),
            'active' => 1,
            'status' => 'closed',
            'ownership' => 'forwarded',
            'origin_id' => $timesheet->get('id'),
            'origin_model' => 'Document',
            'clone_transform' => 'transformed',
            'parent_doc_num' => $timesheet->get('full_num'),
        );
        $record = new Production_Record($this->registry, $params);

        // get additional vars and set values
        $this->registry->set('get_old_vars', true, true);
        $record->getVars();
        $this->registry->set('get_old_vars', false, true);

        $vars = $record->get('vars');
        // no data will be saved in the GT for now
        $vars = array_filter($vars, function($a) { return !empty($a['name']) && empty($a['grouping']); });
        foreach ($vars as &$var) {
            switch ($var['name']) {
                case 'started_date':
                    $var['value'] = $date_iso;
                    break;
                case 'record_timesheet_name':
                    $var['value'] = $timesheet->get('full_num');
                    break;
                case 'record_timesheet_id':
                    $var['value'] = $timesheet->get('id');
                    break;
                case 'production_schedule_name':
                case 'production_schedule_id':
                case 'product_name':
                case 'product_id':
                case 'product_measure':
                    $var['value'] = $timesheet->getPlainVarValue($var['name']);
                    break;
                case 'product_quantity':
                    $var['value'] = $quantity;
                    break;
                case 'product_cost_price':
                    $cost_price = $this->request->get('subtotal');
                    $var['value'] = $cost_price && is_array($cost_price) ? reset($cost_price) : '';
                    break;
                case 'product_currency':
                    $var['value'] = $this->get('currency');
                    break;
                case 'product_expire':
                    if ($nom->get('has_expire') && $this->request->get('article_-1_expire')) {
                        $var['value'] = min($this->request->get('article_-1_expire'));
                    }
                    break;
                case 'product_serial':
                    $value = '';
                    if ($nom->get('has_serial') && $this->request->get('article_-1_serial')) {
                        $serials = $value = $this->request->get('article_-1_serial');

                        // processing when there are detailed serials
                        $material_keys = array_filter(
                            array_keys($this->request->getAll('post')),
                            function($a) { return strpos($a, 'material_-1_') === 0; }
                        );
                        if ($material_keys) {
                            $material_used = array();
                            foreach ($material_keys as $mkey) {
                                // get selected serials for material
                                $material_serials = $this->request->get($mkey);
                                // get material id
                                $mkey = str_replace('material_-1_', '', $mkey);

                                foreach ($value as $idx => $v) {
                                    if (!empty($material_serials[$idx])) {
                                        $material_used[$idx][$mkey] = $material_serials[$idx];
                                    }
                                }
                            }

                            $material_used_serials = array();
                            // set only non-empty material serials in visible field
                            foreach ($material_used as $idx => $ms) {
                                if ($ms) {
                                    $value[$idx] .= sprintf(' (%s)', implode(', ', $ms));

                                    foreach ($ms as $aid => $aserial) {
                                        if (!isset($material_used_serials[$serials[$idx]][$aid])) {
                                            $material_used_serials[$serials[$idx]][$aid] = array();
                                        }
                                        $material_used_serials[$serials[$idx]][$aid][] = $aserial;
                                    }
                                }
                            }
                            // store mapping which material serials were used
                            $record->set('description', serialize($material_used_serials), true);
                        }

                        if ($exp = $this->request->get('article_-1_expire')) {
                            foreach ($value as $idx => $v) {
                                if (isset($exp[$idx])) {
                                    $value[$idx] .= ', ' . General::strftime($this->i18n('date_short'), $exp[$idx]);
                                }
                            }
                        }

                        $value = implode("\n", $value);
                    } elseif ($nom->get('has_expire') && ($exp = $this->request->get('article_-1_expire')) && count($exp) > 1) {
                        // multiple expire dates
                        $value = $this->i18n('finance_warehouses_documents_expire') . ': ' .
                            implode(', ', array_map(array('General', 'strftime'), array_fill(0, count($exp), $this->i18n('date_short')), $exp));
                    } elseif ($nom->get('has_batch') && $this->request->get('article_-1_batch')) {
                        $batches = $value = $this->request->get('article_-1_batch');

                        // processing when there are detailed batches
                        $material_keys = array_filter(
                            array_keys($this->request->getAll('post')),
                            function($a) { return strpos($a, 'material_-1_') === 0; }
                        );
                        if ($material_keys) {
                            $material_used = array();
                            foreach ($material_keys as $mkey) {
                                // get selected batches for material
                                $material_batches = $this->request->get($mkey);
                                // get material id
                                $mkey = str_replace('material_-1_', '', $mkey);

                                foreach ($value as $idx => $v) {
                                    if (!empty($material_batches[$idx])) {
                                        $material_used[$idx][$mkey] = $material_batches[$idx];
                                    }
                                }
                            }

                            $material_used_batches = array();
                            // set only non-empty material batches in visible field
                            foreach ($material_used as $idx => $ms) {
                                if ($ms) {
                                    $value[$idx] .= sprintf(' (%s)', implode(', ', $ms));

                                    foreach ($ms as $aid => $abatch) {
                                        if (!isset($material_used_batches[$batches[$idx]][$aid])) {
                                            $material_used_batches[$batches[$idx]][$aid] = array();
                                        }
                                        $material_used_batches[$batches[$idx]][$aid][] = $abatch;
                                    }
                                }
                            }
                            // store mapping which material batches were used
                            $record->set('description', serialize($material_used_batches), true);
                        }

                        if ($exp = $this->request->get('article_-1_expire')) {
                            foreach ($value as $idx => $v) {
                                if (isset($exp[$idx])) {
                                    $value[$idx] .= ', ' . General::strftime($this->i18n('date_short'), $exp[$idx]);
                                }
                            }
                        }

                        $value = implode("\n", $value);
                    }
                    $var['value'] = $value;
                    break;
            }
        }
        unset($var);

        $record->set('vars', $vars, true);
        // transform record from timesheet
        if ($record->transform()) {
            $record->slashesStrip();
            $record->unsetProperty('assignments_observer', true);
            Documents_History::saveData(
                $this->registry,
                array(
                    'model' => $record,
                    'action_type' => 'add',
                    'new_model' => $record,
                    'old_model' => new Document($this->registry),
                )
            );
        } else {
            $db->FailTrans();
        }

        // 3. update produced quantity into requests; finish request if complete
        $schedule = $timesheet->getSchedule();

        $requests = $schedule->getGroupingVarValues(array('request_id'));
        $requests = array_map(function($a) { return $a['request_id']; }, $requests);
        if ($requests) {
            $requests = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'action' => 'custom',
                    'filters' => array(
                        'where' => array(
                            'd.id IN (\'' . implode('\', \'', $requests) . '\')',
                            'd.transform_num = \'' . $schedule->get('id') . '\'',
                        ),
                    ),
                    'sort' => array(
                        'd.id ASC',
                    ),
                    'model_name' => 'Production_Request',
                ));
        }

        // status for completed requests
        $status = explode('_', $this->get('type_production_request_status_finished'));
        if (empty($status[1])) {
            $status[1] = 0;
        }

        // prepare same data to be added as a new row in each request
        $rq_record_data = array(
            'production_record_name' => $record->get('full_num'),
            'production_record_id' => $record->get('id'),
            'production_schedule_name' => $schedule->get('full_num'),
            'production_schedule_id' => $schedule->get('id'),
            'production_date' => $record->get('date'),
        );

        $this->registry->set('get_old_vars', true, true);
        $total_quantity = $quantity;

        /** @var Production_Request[] $requests */
        foreach ($requests as $rq) {

            // check if there is still undistributed quantity
            if (bccomp($total_quantity, 0, $qty_prec) < 1) {
                break;
            }

            // get additional vars and set values
            $rq->unsanitize();
            $rq->getVars();
            $vars = $rq->getAssocVars();
            $rq->unsetProperty('assoc_vars', true);

            if (bccomp($vars['remaining_quantity']['value'], 0, $qty_prec) == 1) {
                // update quantities in production request
                $rq_quantity = $total_quantity;
                if (bccomp($rq_quantity, $vars['remaining_quantity']['value'], $qty_prec) == 1) {
                    $rq_quantity = round($vars['remaining_quantity']['value'], $qty_prec);
                }
                $total_quantity = bcsub($total_quantity, $rq_quantity, $qty_prec);
                $vars['produced_quantity']['value'] = sprintf($quantity_prec_format, bcadd($vars['produced_quantity']['value'], $rq_quantity, $qty_prec));
                $vars['remaining_quantity']['value'] = sprintf($quantity_prec_format, bcsub($vars['remaining_quantity']['value'], $rq_quantity, $qty_prec));

                // add new row into GT of production records
                foreach ($vars as $var_name => &$var) {
                    if (!empty($var['grouping']) && $var['type'] != 'group') {
                        if (empty($var['value'])) {
                            $var['value'] = array();
                        }
                        switch ($var['name']) {
                            case 'production_qty':
                                $var['value'][] = sprintf($quantity_prec_format, $rq_quantity);
                                break;
                            default:
                                $var['value'][] = isset($rq_record_data[$var['name']]) ? $rq_record_data[$var['name']] : '';
                                break;
                        }
                    }
                }
                unset($var);

                $old_model = clone $rq;
                $old_model->sanitize();
                $rq->set('vars', array_values($vars), true);

                // save updated data in production request
                if ($rq->save()) {
                    $rq->slashesStrip();
                    Documents_History::saveData(
                        $this->registry,
                        array(
                            'model' => $rq,
                            'action_type' => 'edit',
                            'new_model' => $rq,
                            'old_model' => $old_model,
                        ));
                } else {
                    $db->FailTrans();
                }
            }

            // if request is completed, clear transform_num and set status to completed
            if (bccomp($vars['remaining_quantity']['value'], 0, $qty_prec) < 1) {
                $rq->setBasicVar(
                    array('transform_num', 'status', 'substatus', 'status_modified', 'status_modified_by'),
                    array('', $status[0], $status[1], $date_iso, $user->get('id'))
                );
                Documents_History::saveData(
                    $this->registry,
                    array(
                        'model' => $rq,
                        'action_type' => 'status',
                        'new_model' => Documents::searchOne($this->registry,
                            array(
                                'where' => array('d.id = \'' . $rq->get('id') . '\''),
                                'sanitize' => true,
                            )),
                        'old_model' => $rq,
                    ));
            }

            $rq->sanitize();
        }
        $this->registry->set('get_old_vars', false, true);

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if (!$result) {
            return $this->prepareErrorResponse($this->messages->getErrors());
        } else {
            // display success message
            $this->messages->unset_vars('messages');
            $this->messages->setMessage($this->i18n('message_documents_add_success', array($record->get('name'))));
            $this->messages->setMessage($this->i18n('plugin_message_manage_quantity_success'));
            return $this->home();
        }
    }

    /**
     * Load screen to finish (complete) or terminate (cancel) production
     *
     * @return string - fetched content and/or operation result
     */
    public function loadFinish() {

        $schedule = $this->getProductionSchedule($this->request['schedule_id']);
        if (!$schedule) {
            return $this->prepareErrorResponse(array(
                $this->i18n('error_invalid_schedule_progress')
            ));
        }

        // if schedule has no initial commodity transfers, production can be directly finished
        $transfers = $schedule->getWhDocs($schedule->getTransferIds());
        $schedule->set('transfers', $transfers, true);
        if (!$transfers) {
            return $this->finish($schedule);
        }

        $prec = $this->registry['config']->getSectionParams('precision');
        $quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);

        // prepare aggregated values of initial quantities
        $gt2_values = $schedule->prepareMergedGT2Values($transfers);

        // get calculated available (remaining) quantity for production
        $available_quantity = $schedule->getProductionAvailableQuantity(array_keys($gt2_values));

        foreach ($gt2_values as $article_id => &$row) {
            if (!isset($available_quantity[$article_id]) || bccomp($available_quantity[$article_id]['quantity'], 0, $prec['gt2_quantity']) < 1) {
                unset($gt2_values[$article_id]);
            } else {
                $row['available_quantity'] = sprintf($quantity_prec_format, $available_quantity[$article_id]['quantity']);
                $row['used_quantity'] = sprintf($quantity_prec_format, bcsub($row['quantity'], $row['available_quantity'], $prec['gt2_quantity']));
                if (!empty($row['has_batch'])) {
                    foreach ($row['batch_data'] as $ukey => &$batch_data) {
                        if (empty($available_quantity[$article_id]['batch_data'][$ukey]) || bccomp($available_quantity[$article_id]['batch_data'][$ukey]['quantity'], 0, $prec['gt2_quantity']) < 1) {
                            unset($row['batch_data'][$ukey]);
                        } else {
                            $batch_data['available_quantity'] = sprintf($quantity_prec_format, $available_quantity[$article_id]['batch_data'][$ukey]['quantity']);
                            $batch_data['used_quantity'] = sprintf($quantity_prec_format, bcsub($batch_data['quantity'], $available_quantity[$article_id]['batch_data'][$ukey]['quantity'], $prec['gt2_quantity']));
                        }
                    }
                }
            }
        }
        unset($batch_data);
        unset($row);

        // no quantities left, production can be directly finished
        if (!$gt2_values) {
            return $this->finish($schedule);
        }

        $lang = $this->registry['lang'];
        $lang_files = array(
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_warehouses_documents.ini',
            PH_MODULES_DIR . 'finance/i18n/' . $lang . '/finance_documents_types.ini',
        );

        return $this->fetch(
            '_finish.html',
            array(
                'document' => $schedule,
                'values' => $gt2_values,
                'measure_num' => self::MEASURE_NUM,
            ),
            $lang_files
        );
    }

    /**
     * Finish (complete) or terminate (cancel) production
     *
     * @param Production_Schedule $schedule - schedule model (when method is called internally)
     * @return string - fetched content and/or operation result
     */
    public function finish(Production_Schedule $schedule = null) {

        // load i18n files for documents
        $lang = $this->registry['lang'];
        $lang_files = array(
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'documents/i18n/', $lang, '/documents.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance.ini'),
            sprintf('%s%s%s%s', PH_MODULES_DIR, 'finance/i18n/', $lang, '/finance_warehouses_documents.ini'),
        );
        $this->loadI18NFiles($lang_files);

        if (!$schedule) {
            $schedule = $this->getProductionSchedule($this->request['schedule_id']);
            if (!$schedule) {
                return $this->prepareErrorResponse(array(
                    $this->i18n('error_invalid_schedule_progress')
                ));
            }
            $schedule->set('transfers', $schedule->getWhDocs($schedule->getTransferIds()), true);
        }
        $schedule->unsanitize();

        $user = $this->registry['currentUser'];
        $date_iso = General::strftime($this->i18n('date_iso'));
        $prec = $this->registry['config']->getSectionParams('precision');
        //$quantity_prec_format = sprintf('%%.%dF', $prec['gt2_quantity']);

        /** @var ADODB_mysqli $db */
        $db = &$this->registry['db'];

        $db->StartTrans();

        // 0. save reverse transfer and waste
        if ($schedule->get('transfers')) {
            // transfers (an initial one and optional additional ones) that
            // move materials into production warehouse are related directly to schedule
            $transfers = $schedule->get('transfers');

            // prepare aggregated values of initial quantities
            $gt2_values = $schedule->prepareMergedGT2Values($transfers);

            // get calculated available (remaining) quantity for production
            $available_quantity = $schedule->getProductionAvailableQuantity(array_keys($gt2_values));

            // prepare submitted quantities - first get waste, then return everything else
            $qtys = array('waste' => array(), 'returned' => array());

            $waste = $this->request->isRequested('waste') ?
                array_filter($this->request['waste'], function($a) use ($prec) { return bccomp($a, 0, $prec['gt2_quantity']) == 1; }) : array();
            foreach ($waste as $article_id => $tqty) {
                if (isset($available_quantity[$article_id])) {
                    if (!empty($available_quantity[$article_id]['has_batch'])) {
                        $b = $this->request->get("batch_$article_id") ?: array();
                        $w = $this->request->get("waste_$article_id") ?: array();
                        if ($b && $w) {
                            $qtys['waste'][$article_id] = array('quantity' => 0, 'batch_data' => array());
                            foreach ($b as $idx => $ukey) {
                                if (isset($w[$idx]) && bccomp($w[$idx], 0, $prec['gt2_quantity']) == 1) {
                                    $qtys['waste'][$article_id]['batch_data'][$ukey] = array('quantity' => $w[$idx]);
                                    $qtys['waste'][$article_id]['quantity'] += $w[$idx];
                                }
                            }
                        }

                    } else {
                        $qtys['waste'][$article_id] = array('quantity' => $tqty);
                    }
                }
            }

            $default_data = array(
                'name' => '',
                'description' => '',
                'notes' => '',
                'location' => $this->i18n('plugin_production_warehouse'),
                'department' => $schedule->get('department'),
                'group' => $schedule->get('group'),
                'active' => 1,
                'is_portal' => $user->get('is_portal'),
                'employee' => $user->get('employee'),
                'employee_name' => $user->get('employee_name') ?: ($user->get('firstname') . ' ' . $user->get('lastname')),
                'currency' => $transfers[0]->get('currency'),
                'status' => 'finished',
            );

            if ($qtys['waste']) {
                $params = array(
                    'wid' => $transfers[0]->get('to_warehouse'),
                    'type' => PH_FINANCE_TYPE_WASTE,
                    'default_data' => $default_data,
                    'qtys' => $qtys['waste'],
                    'gt2_values' => $gt2_values,
                    'available_quantity' => &$available_quantity,
                );
                if (!$schedule->createWarehouseDocument($params)) {
                    $db->FailTrans();
                }
            }

            // get the total remaining quantity, no need to prepare batches as everything has to be returned
            $qtys['returned'] = array_filter(
                array_map(function($a) use ($prec) {
                    return bccomp($a['quantity'], 0, $prec['gt2_quantity']) == 1 ? array('quantity' => $a['quantity']) : false;
                }, $available_quantity));

            if ($qtys['returned']) {
                $params = array(
                    'wid' => $transfers[0]->get('to_warehouse'),
                    'to_wid' => $transfers[0]->get('warehouse'),
                    'type' => PH_FINANCE_TYPE_COMMODITIES_TRANSFER,
                    'default_data' => $default_data,
                    'qtys' => $qtys['returned'],
                    'gt2_values' => $gt2_values,
                    'available_quantity' => &$available_quantity,
                );
                if (!$schedule->createWarehouseDocument($params)) {
                    $db->FailTrans();
                }
            }

            // saving of warehouse documents failed, do not continue
            if ($db->HasFailedTrans()) {
                $db->CompleteTrans();
                return $this->prepareErrorResponse($this->messages->getErrors());
            }
        }

        // 1. unlock route card if only unfinished production
        $route_card = $schedule->getRouteCard();

        if ($route_card && $route_card->get('status') == 'locked') {
            $productions = $route_card->getProductions($schedule->get('type'));
            if (count($productions) == 1 && $productions[0] == $schedule->get('id')) {
                $route_card->setBasicVar(
                    array('status', 'substatus', 'status_modified', 'status_modified_by'),
                    array('opened', '0', $date_iso, $user->get('id'))
                );
            }
        }

        // 2. disconnect requests from schedule and update status if complete
        $requests = $schedule->getGroupingVarValues(array('request_id'));
        $requests = array_map(function($a) { return $a['request_id']; }, $requests);
        if ($requests) {
            $requests = Production_Custom_Factory::search(
                $this->registry,
                array(
                    'action' => 'custom',
                    'filters' => array(
                        'where' => array(
                            'd.id IN (\'' . implode('\', \'', $requests) . '\')',
                            'd.transform_num = \'' . $schedule->get('id') . '\'',
                        ),
                    ),
                    'sort' => array(
                        'd.id ASC',
                    ),
                    'model_name' => 'Production_Request',
                ));
        }
        $status = explode('_', $this->get('type_production_request_status_finished'));
        if (empty($status[1])) {
            $status[1] = 0;
        }
        foreach ($requests as $rq) {
            $rq->unsanitize();
            if (bccomp($rq->getPlainVarValue('remaining_quantity'), 0, $prec['gt2_quantity']) < 1) {
                // if request is completed, clear transform_num and set status to completed
                $rq->setBasicVar(
                    array('transform_num', 'status', 'substatus', 'status_modified', 'status_modified_by'),
                    array('', $status[0], $status[1], $date_iso, $user->get('id'))
                );
                Documents_History::saveData(
                    $this->registry,
                    array(
                        'model' => $rq,
                        'action_type' => 'status',
                        'new_model' => Documents::searchOne($this->registry,
                            array(
                                'where' => array('d.id = \'' . $rq->get('id') . '\''),
                                'sanitize' => true,
                            )),
                        'old_model' => $rq,
                    ));
            } else {
                // clear transform_num
                $rq->setBasicVar(array('transform_num'), array(''));
            }
            $rq->sanitize();
        }

        // 3. update status and additional vars of schedule
        // compare total requested to total produced quantity
        $rq_qty = $schedule->getPlainVarValue('total_quantity');
        $prod_qty = $schedule->getProducedQuantity();

        // determine state at which production is finished (if it is complete or not)
        $state = (bccomp($prod_qty, $rq_qty, $prec['gt2_quantity']) > -1) ? 'finished' : 'terminated';
        $status = explode('_', $this->get('type_production_schedule_status_' . $state));
        if (empty($status[1])) {
            $status[1] = 0;
        }
        $schedule->setBasicVar(
            array('status', 'substatus', 'status_modified', 'status_modified_by'),
            array($status[0], $status[1], $date_iso, $user->get('id'))
        );
        // get only the plain vars that have to be updated
        $plain_vars = $schedule->getFields();
        foreach ($plain_vars as $idx => $var) {
            switch ($var['name']) {
                case $state . '_date':
                    $plain_vars[$idx]['value'] = $date_iso;
                    break;
                case $state . '_by':
                    $plain_vars[$idx]['value'] = $user->get('id');
                    break;
                case $state . '_by_name':
                    $plain_vars[$idx]['value'] = General::slashesEscape($user->get('firstname') . ' ' . $user->get('lastname'));
                    break;
                default:
                    unset($plain_vars[$idx]);
            }
        }
        $schedule->processFields($plain_vars);
        $schedule->set('vars', $plain_vars, true);
        $schedule->replaceVars();
        $schedule->unsetProperty('vars');
        $schedule->sanitize();

        // save history for status change of production schedule
        Documents_History::saveData($this->registry, array(
            'model' => $schedule,
            'action_type' => 'status',
            'new_model' => Documents::searchOne(
                $this->registry,
                array(
                    'where' => array('d.id = \'' . $schedule->get('id') . '\''),
                    'sanitize' => true,
                )),
            'old_model' => $schedule,
        ));

        // 4. delete any unfinished timesheets
        $ids = $schedule->hasUnfinishedTimesheets();
        if ($ids && Documents::delete($this->registry, $ids)) {
            $document = new Document($this->registry);
            foreach ($ids as $id) {
                $document->set('id', $id, true);
                Documents_History::saveData($this->registry, array('model' => $document, 'action_type' => 'delete'));
            }
        }

        $result = !$db->HasFailedTrans();

        $db->CompleteTrans();

        if (!$result) {
            return $this->prepareErrorResponse($this->messages->getErrors());
        } else {
            // display success message
            $this->messages->setMessage(sprintf('%s %s (%s):',
                $this->i18n('plugin_finish_production'),
                $schedule->get('full_num'),
                $schedule->getPlainVarValue('product_name')
            ));
            $this->messages->setMessage($this->i18n("plugin_message_production_{$state}_success"));

            return $this->home();
        }
    }

    /**
     * Displays screen for selection of technological cards for route card of
     * product (triggered from a button in add/edit action of route card)
     *
     * @return string - fetched content and/or operation result
     */
    public function manageRouteCard() {

        $product_types = array($this->get('type_product'), $this->get('type_semiproduct'));

        // collect ids to search tech. cards for
        $product_ids = array();

        // first get the selected product and other products whose tech cards should be used
        /** @var Nomenclature $product */
        $product = Nomenclatures::searchOne($this->registry, array(
            'where' => array(
                'n.id = \'' . $this->request['product_id'] . '\'',
                'n.type IN (\'' . implode('\', \'', $product_types) . '\')',
                'n.active = 1',
            )));

        if ($product) {
            $product_ids[] = $product->get('id');

            // if product is set up to use tech cards of other similar products
            $similar = $product->getGroupingFields('similar_article_id');
            if ($similar) {
                $product->processFields($similar);
                if (!empty($similar[0]['value'])) {
                    $product_ids = array_merge($product_ids, array_unique(array_filter($similar[0]['value'])));
                }
            }
        }

        // previously saved data in Route card
        $selected = array_unique(array_filter($this->request['cards'] ?: array()));
        $multiple = array_intersect_key($this->request['multiple'] ?: array(), $selected);

        // then get the tech cards for all found products
        /** @var Technological_Card[] $tech_cards */
        $tech_cards = array();
        if ($product_ids) {
            $tech_cards = Production_Custom_Factory::search($this->registry, array(
                'module' => 'nomenclatures',
                'controller' => 'nomenclatures',
                'action' => 'search',
                'filters' => array(
                    'where' => array(
                        'n.type = \'' . $this->get('type_technological_card') . '\'',
                        'a____product_id IN (\'' . implode('\', \'', $product_ids) . '\')',
                        'n.active = 1',
                    ),
                    'sort' => array(
                        'n.id IN (\'' . implode('\',\'', $selected) . '\') DESC',
                        'FIND_IN_SET(n.id, \'' . implode(',', $selected) . '\') ASC',
                        'n.id ASC',
                    ),
                ),
                'model_name' => 'Technological_Card',
            ));
            // if there are saved values for multiplicity, set them to models
            if ($multiple) {
                foreach ($tech_cards as $card) {
                    $idx = array_search($card->get('id'), $selected);
                    if ($idx === false) {
                        break;
                    } elseif (!empty($multiple[$idx])) {
                        $card->set('multiple', $multiple[$idx], true);
                    }
                }
            }
        }

        $labels = $warnings = $values = array();
        if ($tech_cards) {
            $card = reset($tech_cards);
            $card->unsanitize();
            $labels = array(
                'num' => $this->i18n('num'),
                'multiple' => $this->i18n('gt2_tagregates_count'),
                'code' => $card->getLayoutName('code', false),
                'name' => $card->getLayoutName('name', false),
                'type_workplace' => '',
                'time_execute' => '',
                'scheme' => $this->i18n('plugin_scheme'),
            );
            foreach ($card->getFields() as $var) {
                if (isset($labels[$var['name']]) && !$labels[$var['name']]) {
                    $labels[$var['name']] = $var['label'];
                }
            }
            $card->sanitize();

            $product_quantity = floatval($this->request['product_quantity']) ?: 1;
            $prec = $this->registry['config']->getParam('precision', 'gt2_quantity');
            $product_measure = $product->getVarValue('measure_name', true);

            // prepare $values
            foreach ($tech_cards as $idx => $card) {
                // same fields per tech. card
                $common_values = array(
                    //'card_number' => '', // will be updated on submit
                    //'card_tech_multiple' => '1', // will be updated on submit
                    'card_tech_code' => $card->get('code'),
                    'card_tech_name' => $card->get('name'),
                    'card_tech_id' => $card->get('id'),
                    'card_type_workplace' => $card->getPlainVarValue('type_workplace'),
                );

                $card_values = $card->getGroupingVarValues(array(''), false, 'material_step');

                // GT has no values or invalid data is returned
                if (empty($card_values) || !empty($card_values['multiple'])) {
                    unset($tech_cards[$idx]);
                } else {
                    // IMPORTANT: sort rows by step, then by num so they are in
                    // the correct order in further production documents
                    usort($card_values, function($a, $b) {
                        if ($a['material_step'] != $b['material_step']) {
                            return $a['material_step'] < $b['material_step'] ? -1 : 1;
                        } else {
                            return $a['num'] < $b['num'] ? -1 : 1;
                        }
                    });

                    // filter the commodities from tech card
                    $commodities = Production_Custom_Factory::getCommodities(
                        $this->registry,
                        array_map(function($a) {
                            return isset($a['material_article_id']) ? $a['material_article_id'] : 0;
                        }, $card_values)
                    );

                    foreach ($card_values as $ridx => $row) {
                        $route_row = $common_values;
                        foreach ($row as $field => $value) {
                            $matches = array();
                            if (preg_match('#^material(.+)$#', $field, $matches)) {
                                $route_row['card'. $matches[1]] = $value;
                            }
                        }
                        if (!is_numeric($route_row['card_quantity'])) {
                            $route_row['card_quantity'] = 0;
                        }
                        // recalculate quantities for product quantity
                        // tech. cards contain quantities necessary for
                        // production of 1 unit (kg, meter, pc.) of the final product
                        $route_row['card_quantity'] =
                            sprintf('%.' . $prec . 'F', round($route_row['card_quantity'] * $product_quantity, $prec));
                        //$route_row['card_total_quantity'] = $route_row['card_quantity']; // will be updated on submit

                        // define waste measure - material's own measure for
                        // commodities, product measure for non-commodities
                        $route_row['card_waste_measure'] =
                            in_array($route_row['card_article_id'], $commodities) ? $route_row['card_measure'] : $product_measure;

                        $card_values[$ridx] = $route_row;
                    }
                    // make the keys start from 1 (then they will be json-encoded as object properties)
                    $card_values = array_combine(range(1, count($card_values)), $card_values);

                    $values[$card->get('id')] = $card_values;
                }
            }
        }

        $route_card = new Route_Card($this->registry, array('type' => $this->get('type_route_card')));
        $multiple_field = $route_card->getGroupingFields('card_tech_multiple');
        $route_card->processFields($multiple_field);
        $route_card->processFieldsForJSFM($multiple_field);
        $multiple_field = $multiple_field ? reset($multiple_field) : array();

        // display warning when there are no appropriate technological cards
        if (!$tech_cards) {
            if ($product) {
                $warnings[] = General::mb_ucfirst($this->i18n('error_no_items_found'));
                $warnings[] = $this->i18n('help_add_card', array(
                    '<a target="_blank" class="stronger" href="' .
                    sprintf('%s?%s=nomenclatures&nomenclatures=add&type=%d&product_name[]=%s&product_id[]=%d',
                        $_SERVER['PHP_SELF'],
                        $this->registry['module_param'],
                        $this->get('type_technological_card'),
                        htmlentities($product->get('name'), ENT_COMPAT, 'UTF-8'),
                        $product->get('id')
                    ) . '">',
                    '</a>',
                ));
            } else {
                // product was not found
                $product_name = $route_card->getFields('product_name');
                $product_name = ($product_name && !empty($product_name[0]['label'])) ? $product_name[0]['label'] : $this->i18n('plugin_product');
                $warnings[] = $this->i18n('error_regexpCompare', array('var_label' => $product_name, 'var_help' => '!'));
            }
        }

        return $this->fetch('_route_card.html', array(
            'dashlet' => Production_Custom_Factory::getDashlet($this->registry),
            'scripts' => array(
                PH_MODULES_URL . 'dashlets/plugins/' . $this->plugin_name . '/javascript/custom.js',
            ),
            'cards' => $tech_cards,
            'values' => $values,
            'selected' => $selected,
            'labels' => $labels,
            'multiple_field' => $multiple_field,
            'warnings' => $warnings,
            'dashlet_data' => array(
                'measure_num' => self::MEASURE_NUM,
            ),
        ));
    }

    /**
     * Variable holds ids of all sub-products that requests have been created for
     *
     * @var array - request ids as keys, array of all products from branch as value
     */
    private $_recipes = array();

    /**
     * Additional recursive function used by automation for creation of
     * subrequests
     *
     * @see Production_Automations_Controller::createProductionRequests
     * @param Documents_Controller $controller - controller that performs transformation
     * @param Document $request - production request model
     * @param int $level - recursion level
     * @return boolean - result of the operation
     */
    public function doCreateProductionRequests(Documents_Controller $controller, Document $request, $level = 1) {
        //stop endless recursions to keep the code safe, this should not happen though
        if ($level >= 50) {
            $this->messages->setError($this->i18n('error_production_request_recursion'));
            return false;
        }

        // cast to specific class in order to use extended functionality
        /** @var Production_Request $request */
        if (get_class($request) != 'Production_Request') {
            $request = Production_Custom_Factory::cast($request, 'Production_Request');
        }

        // get values of plain vars from current production request model
        $product_id = $request->getPlainVarValue('product_id');
        $product_name = $request->getPlainVarValue('product_name');
        $quantity = $request->getPlainVarValue('product_quantity');

        /** @var Route_Card $route_card */
        $route_card = $this->getRouteCards(array($product_id));
        if (count($route_card) != 1) {
            // error message
            $request->raiseError('plugin_error_single_route_card', '', '', array($product_name, count($route_card)));
            return false;
        }
        $route_card = reset($route_card);
        $route_card->unsanitize();

        // get data for sub-product ingredients from route card
        $materials = $route_card->getSubproductRows();
        unset($route_card);

        if (!$materials) {
            return true;
        }

        // initial request for initial product
        if (empty($this->_recipes)) {
            $this->_recipes = array($request->get('id') => array($product_id));
        }

        $prec = $this->registry['config']->getParam('precision', 'gt2_quantity');

        $transform = array(
            'id' => '0',
            'name' => '',
            'source_model' => 'Document',
            'source_type' => $request->get('type'),
            'destination_model' => 'Document',
            'destination_type' => $request->get('type'),
            'transform_type' => 'one2one',
            'method' => 'transformSimple',
            'settings' => '',
        );
        $transform_settings = implode("\n", array(
            'skip_intermediate_screen := 1',
            'transform_b_office := b_office',
            'transform_b_employee := b_employee',
            'transform_b_media := b_media',
            'transform_b_department := b_department',
            'transform_b_deadline := b_deadline',
            'transform_b_validity_term := b_validity_term',
            'transform_b_date := b_date',
            'transform_b_active := b_active',
            'equals_b_status := locked',
            'equals_b_substatus := ' .
                ($this->get('type_production_request_status_ready_with_subrequests') != 'locked' ?
                preg_replace('#^\w+_(\d+)$#', '$1',
                    $this->get('type_production_request_status_ready_with_subrequests')) : ''),
            'status_after_transform := ' . $request->get('status'),
            'substatus_after_transform := ' . $request->get('status') . '_' . intval($request->get('substatus')),
            'equals_b_name := [name]',
            'equals_b_group := [group]',
            'equals_b_group_index := [group_index]',
        ));

        $request_real = clone $request;

        //SUBRECIPES FOUND: RECURSIVELY ADD PRODUCTION REQUESTS
        foreach ($materials as $material_id => $material) {
            //CHECK IF CHILD REQUEST SHOULD BE ADDED (check the relations)
            //IMPORTANT: the recipe id is stored in documents_relatives.group_index field, origin is "transformed"
            $child_request_id = $request->getSubrequest($material_id);

            //THE CHILD PRODUCTION REQUEST HAS NOT BEEN ADDED YET, ADD CHILD REQUEST NOW
            if (!$child_request_id) {
                // transformation INCORRECTLY leaves model sanitized so we unsanitize it each time
                if ($request->isSanitized()) {
                    $request->unsanitize();
                }

                // IMPORTANT: material quantities are for production of 1 unit
                if (!empty($material['product_measure']) && $material['product_measure'] == self::MEASURE_NUM) {
                    // round fractions up to next integer
                    $calc_qty = ceil($quantity * $material['product_quantity']);
                } else {
                    // round according to GT2 quantity precision
                    $calc_qty = round($quantity * $material['product_quantity'], $prec);
                }
                $material['product_quantity'] = $material['remaining_quantity'] = $calc_qty;

                $transform['settings'] = str_replace(
                    array('[name]', '[group]', '[group_index]'),
                    array(
                        sprintf('%s %s %s %s',
                            $material['product_name'],
                            mb_strtolower($this->i18n('from'), mb_detect_encoding($this->i18n('from'))),
                            $request->get('type_name'),
                            $request->get('full_num')),
                        $request->getProductGroup($material_id),
                        $material_id
                    ),
                    $transform_settings) . "\n" .
                    implode("\n", array_map(function($a, $b) { return sprintf("equals_a_$a := $b"); }, array_keys($material), $material));

                if ($controller->{$transform['method']}($request, $transform)) {
                    $child_request_id = $request->get('transformed_id');
                    $request->unsetProperty('transformed_id', true);
                    // transformation assigns an up-to-date Document object to $request
                    // so we restore original object
                    $request = $request_real;

                    // remove transformation data from fields of child document
                    $this->registry['db']->Execute(
                        'UPDATE ' . DB_TABLE_DOCUMENTS . ' SET transform_num = 0, transform_full_num = NULL WHERE id = ?',
                        array($child_request_id)
                    );

                    // do not display messages from transformation
                    $this->messages->unset_vars('messages');
                } else {
                    // transformation failed
                    $this->messages->setError($this->i18n('error_production_request_creation_failed'), '', -5);

                    return false;
                }
            }

            $child = Documents::searchOne(
                $this->registry,
                array('where' => array('d.id = ' . $child_request_id))
            );

            // check for endless recursion:
            // each key holds products from parents of request along the branch;
            // different branches can create requests for the same sub-product;
            // but same sub-product more than once along a branch would lead to an endless recursion
            if (!empty($this->_recipes[$request->get('id')]) && in_array($material_id, $this->_recipes[$request->get('id')])) {
                $this->messages->setError($this->i18n('error_production_request_recursion'));
                $this->messages->setError($this->i18n('error_production_product_self',
                    array($material['product_name'])));
                return false;
            } else {
                $this->_recipes[$child_request_id] = array_merge($this->_recipes[$request->get('id')], array($material_id));
            }

            //RECURSE FOR FURTHER CREATING OF CHILD REQUESTS
            $result = $this->doCreateProductionRequests(
                $controller,
                $child,
                ++$level
            );
            // we don't use FailTrans in order to go through all possible
            // actions and display all error messages
            if (!$result) {
                $this->failed = true;
            }
        }

        return empty($this->failed);
    }

    /**
     * Saves personal settings of current user for dashlet
     *
     * @return string - fetched content and/or operation result
     */
    public function settings() {
        $dashlet = $this->registry['dashlet'];
        if (!$dashlet) {
            return;
        }
        // variables: all settings that user can overwrite values for
        $plugin_fields = $this->getPluginFields($dashlet, '#_warehouse$#');

        $plugin_fields['default_workplace_id'] = array(
            'name' => 'default_workplace_id',
            'type' => 'dropdown',
            'options' => Production_Custom_Factory::search(
                $this->registry,
                array(
                    'module' => 'nomenclatures',
                    'controller' => 'nomenclatures',
                    'action' => 'list',
                    'options' => 1,
                    'filters' => array(
                        'where' => array(
                            'n.type = \'' . $this->get('type_workplace') . '\'',
                            'n.active = 1',
                        ),
                        'sort' => array(
                            'ni18n.name ASC',
                        ),
                    ),
                )),
            'label' => $this->i18n('plugin_default_workplace'),
            'bb' => '0',
            'gt2' => '0',
            'grouping' => '0',
            'configurator' => '0',
            'required' => '0',
            'hidden' => '0',
        );

        // display form
        if (!$this->request->isPost()) {
            // values (if already saved)
            $ps = $this->registry['currentUser']->getPersonalSettings('dashlets_plugins', 'dashlet_' . $this->get('dashlet_id'));
            if ($ps) {
                $ps = unserialize($ps);
            }
            if (!is_array($ps)) {
                $ps = array();
            }
            $filtered_options = null;
            foreach ($plugin_fields as $k => &$var) {
                $sk = str_replace(self::CONFIG_PREFIX, '', $k);
                $var['value'] = isset($ps[$sk]) ? $ps[$sk] : '';
                $var['help'] = '';
                $var['required'] = false;
                unset($var['js_methods']);
                if (isset($var['options']) && $this->isDefined($sk)) {
                    if (isset($var['options']['user_default_warehouse'])) {
                        $dwh = $this->registry['currentUser']->get('default_warehouse');
                        $var['options']['user_default_warehouse']['label'] .=
                            sprintf(
                                ' (%s)',
                                ($dwh && isset($var['options'][$dwh]) ? $var['options'][$dwh]['label'] : '-')
                            );
                        // allow selection only of options for company and office from dashlet settings
                        if (!empty($var['custom_class']) && preg_match('#\bwarehouse\b#', $var['custom_class'])) {
                            if (!isset($filtered_options)) {
                                $filtered_options = $var['options'];
                                array_walk(
                                    $filtered_options,
                                    function (&$a, $k, $company_data) {
                                        if (!empty($a['active_option'])) {
                                            $a['active_option'] = empty($a['class_name']) || $a['class_name'] == $company_data;
                                        }
                                    },
                                    preg_replace('#^(\d+_\d+)_.*$#', '$1', $this->get('company_data')));
                            }
                            $var['options'] = $filtered_options;
                        }
                    }
                    if (isset($ps[$sk])) {
                        $var['first_option_label'] = $this->i18n('plugin_remove_personal_setting');
                    } elseif (isset($var['options'][$this->get($sk)])) {
                        $var['options'][$this->get($sk)]['label'] .=
                            sprintf(' (%s)', $this->i18n('plugin_default'));
                    }
                }
            }
            unset($var);

            return $this->fetch('_settings.html', array('plugin_fields' => $plugin_fields));

        // save
        } else {
            foreach ($plugin_fields as $key => &$var) {
                $var['value'] = $this->request[$key];
                // perform validation of warehouse roles
                if ($var['value'] == 'user_default_warehouse' && !$this->registry['currentUser']->get('default_warehouse')) {
                        $this->raiseError('plugin_error_no_default_warehouse', $key);
                } elseif (!empty($var['custom_class']) && preg_match('#\bwarehouse\b#', $var['custom_class'])) {
                    $wid = $var['value'];
                    if (!$wid) {
                        $wid = $this->get(str_replace(self::CONFIG_PREFIX, '', $var['name']));
                    }
                    if ($wid == 'user_default_warehouse') {
                        $wid = $this->registry['currentUser']->get('default_warehouse');
                    }
                    if (strpos(
                        Production_Custom_Factory::getWarehouseData($this->registry, $wid),
                        preg_replace('#^(\d+_\d+_).*$#', '$1', $this->get('company_data'))) !== 0) {
                        $this->raiseError('plugin_error_invalid_warehouse', $key, null, array($var['label'], ''));
                    }
                }
                if (!$var['value']) {
                    // no personal value will be saved
                    unset($plugin_fields[$key]);
                    continue;
                }
            }
            unset($var);
            $this->set('vars', $plugin_fields, true);
            if ($this->validateVars()) {
                foreach ($plugin_fields as $k => $var) {
                    unset($plugin_fields[$k]);
                    $sk = str_replace(self::CONFIG_PREFIX, '', $k);
                    $plugin_fields[$sk] = $var['value'];
                }
                if ($this->registry['currentUser']->savePersonalSettings(array(
                    array(
                        'dashlets_plugins',
                        'dashlet_' . $this->get('dashlet_id'),
                        serialize($plugin_fields)
                    )
                ))) {
                    $this->messages->setMessage($this->i18n('plugin_message_settings_save'));
                } else {
                    $this->messages->setError($this->i18n('plugin_error_settings_save'), '', -1);
                }
            } else {
                $this->messages->setError($this->i18n('plugin_error_settings_save'), '', -1);
            }

            $errors = $this->messages->getErrors();
            if ($errors) {
                return $this->prepareErrorResponse($errors, true);
            } else {
                return json_encode(array(
                    'messages' => $this->prepareMessages($this->messages->getMessages()),
                    'content' => ''
                ));
            }
        }
    }

    /***************************** system methods ****************************/

    /**
     * Gets status and substatus options corresponding to specified type field
     * and its selected value
     *
     * @return string - json-encoded options
     */
    public function getTypesStatuses() {
        $config = require 'config.php';
        $field = preg_replace('#^' . preg_quote(self::CONFIG_PREFIX, '#') . '#', '', $this->request['field']);

        return json_encode(Production_Custom_Factory::getStatuses(
            $this->registry,
            array(
                'model' => (isset($config[$field]['model']) ? $config[$field]['model'] : ''),
                'model_types' => explode(',', $this->request['model_types']),
            )));
    }

    /**
     * Gets requested documentation file located in 'docs' folder of plugin
     */
    public function getfile() {
        $filename = General::decrypt($this->request->get(__FUNCTION__), '_'. __FUNCTION__ . '_', 'xtea');
        $path = realpath(dirname(__FILE__) . '/../docs') . '/' . $filename;
        if ($filename && file_exists($path)) {
            $file = new File($this->registry, array('path' => $path, 'filename' => $filename));
            $file->sendFile();
        } else {
            $this->messages->setError($this->i18n('error_file_doesnot_exist'));
            $this->messages->insertInSession($this->registry);
            header('Location: ' . (!empty($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : PH_BASE_URL));
        }
        exit;
    }

    /****************************** internal methods *************************/

    /**
     * Prepares JSON-encoded formatted response containing error messages
     *
     * @param array $messages - error messages
     * @param bool $urls - if true, keeps relative links (anchors) of messages
     * @return string - a JSON-encoded string
     */
    private function prepareErrorResponse($messages = array(), $urls = false) {
        return json_encode(array(
            'errors' => $this->prepareMessages($messages, 'error', $urls)
        ));
    }

    /**
     * Fetches html content for success, warning or error messages to be
     * displayed
     *
     * @param array $messages - array of messages
     * @param string $mode - message (default), warning or error
     * @param bool $urls - if true, keeps relative links (anchors) of messages
     * @return string - fetched content
     */
    private function prepareMessages($messages = array(), $mode = 'message', $urls = false) {
        if (!empty($messages)) {
            $v = new Viewer($this->registry);
            $v->setFrameset('message.html');
            $v->data['display'] = $mode;
            // don't display relative URLs unless specified
            $v->data['items'] = $urls ? $messages : array_values($messages);
            $messages = $v->fetch();
            unset($v);
        } else {
            $messages = '';
        }
        return $messages;
    }

    /**
     * Fetches template for display
     *
     * @param string $template - name of a template file from current plugin
     * @param array $data - data to assign to viewer
     * @param array $lang_files - additional language files to be loaded
     * @return string - fetched content
     */
    private function fetch($template, $data = array(), $lang_files = array()) {
        $viewer = new Viewer($this->registry);
        $viewer->setFrameset('frameset_blank.html');
        $viewer->templatesDir = PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/templates/';
        $viewer->template = $template;
        foreach ($data as $k => $v) {
            $viewer->data[$k] = $v;
        }
        $lang_files = array_merge(
            $lang_files,
            array(PH_MODULES_DIR . 'dashlets/plugins/' . $this->plugin_name . '/i18n/' . $this->registry['lang'] . '/custom.ini')
        );
        $viewer->loadCustomI18NFiles($lang_files);
        return $viewer->fetch();
    }
}
