
if (env.current_lang == 'bg') {
    i18n['messages']['error_single_dashlet'] = 'Вече има зареден панел за производство.<br />Можете да работите само в един панел за производство!';
    i18n['messages']['error_single_page'] = 'Вече има зареден панел за производство в друга страница на браузъра.<br />Можете да работите само в един панел за производство!';
    i18n['messages']['error_browser_version'] = 'Версията на браузъра Ви не позволява коректна работа с инфо панела.<br />Моля, обновете браузъра си или отворете приложението с друг браузър!';
    i18n['messages']['error_plugin_biotrade_production_invalid_article_in'] = 'Вложеното количество и загубите за [article] надвишават общото количество за този материал, предвидено за това производство ([transfer_quantity])';
    i18n['messages']['confirm_quantity_produced'] = 'Системата изчисли, че очакваното произведено количество е [quantity_expected].\nСигурни ли сте, че искате да приключите производството с произведено количество [quantity_produced]?';
    i18n['messages']['confirm_expired_ingredients'] = 'Избрани са суровини с изтекъл/изтичащ срок на годност на следните редове:\n[data]Сигурни ли сте, че искате да стартирате производство?';
    i18n['labels']['plugin_biotrade_production_start'] = 'Старт';
    i18n['labels']['plugin_biotrade_production_finish'] = 'Приключи';
    i18n['labels']['plugin_biotrade_production_step_started'] = 'Стартирана';
    i18n['labels']['plugin_biotrade_production_step_ended'] = 'Приключена';
} else {
    // for any other language messages will be in English
    i18n['messages']['error_single_dashlet'] = 'There is already a production dashlet loaded in page.<br />You can work in a single production dashlet only!';
    i18n['messages']['error_single_page'] = 'There is already a production dashlet loaded in another browser page.<br />You can work in a single production dashlet only!';
    i18n['messages']['error_browser_version'] = 'Your browser version does not allow correct work with the dashlet. Please, update your browser or open the application with another browser!';
    i18n['messages']['error_plugin_biotrade_production_invalid_article_in'] = 'The used and wasted quantity of [article] is greater than it is planned for this production ([transfer_quantity])';
    i18n['messages']['confirm_quantity_produced'] = 'The system calculated that expected quantity produced is [quantity_expected].\nAre you sure you want to finish production with quantity produced [quantity_produced]?';
    i18n['messages']['confirm_expired_ingredients'] = 'Expired/expiring ingredients are selected in the following rows:\n[data]Are you sure you want to start production?';
    i18n['labels']['plugin_biotrade_production_start'] = 'Start';
    i18n['labels']['plugin_biotrade_production_finish'] = 'Finish';
    i18n['labels']['plugin_biotrade_production_step_started'] = 'Started';
    i18n['labels']['plugin_biotrade_production_step_ended'] = 'Finished';
}

/**
 * Make sure there is only one working dashlet on page
 */
checkSingleDashlet = function(d_id, custom_style_url) {
    var content_dashet = $('content_dashlet_' + d_id);
    if ($$('.production_table').length > 1 && content_dashet) {
        content_dashet.innerHTML = '<div class="error hcenter" style="margin: 10px;">' + i18n['messages']['error_single_dashlet'] + '<div>';
    } else if (typeof (gt2calc) == 'undefined' && content_dashet) {
        // IE8 or older
        content_dashet.innerHTML = '<div class="error hcenter" style="margin: 10px;">' + i18n['messages']['error_browser_version'] + '<div>';
    } else {
        // defer is not supported by Opera
        if (Cookie.get('production_loaded') && navigator.userAgent.toLowerCase().indexOf('opera') == -1) {
            content_dashet.innerHTML = '<div class="error hcenter" style="margin: 10px;">' + i18n['messages']['error_single_page'] + '<div>';
        } else {
            // set session-only cookie
            document.cookie = 'production_loaded=1';
            // cookie should be removed before page is unloaded
            window.onbeforeunload = function() {
                Cookie.erase('production_loaded');
            };
            // keep dashlet id in a global variable, so we don't have to pass it as an input parameter of every function
            dashlet_id = d_id;
            // inject CSS into head of document
            var link = document.createElement("link");
            link.type = 'text/css';
            link.rel = 'stylesheet';
            link.href = custom_style_url;
            $$('head')[0].appendChild(link);
        }
    }
    return true;
};

/**
 * Select 'start' or 'resume' production radio option
 */
selectProductionAction = function(element) {
    var action_rows = $$('.' + element.value + '_action');
    for (var i = 0; i < action_rows.length; i++) {
        // in these cases do not display button rows
        if (action_rows[i].id == 'start_btn_row' && !$('requests_container').innerHTML ||
        action_rows[i].id == 'resume_btn_row' && $('record_id') && !$('record_id').value) {
            continue;
        }
        removeClass(action_rows[i], 'hidden');
    }
    var opposite_action = element.value == 'start' ? 'resume' : 'start';
    action_rows = $$('.' + opposite_action + '_action');
    for (var i = 0; i < action_rows.length; i++) {
        addClass(action_rows[i], 'hidden');
    }
    if (element.value == 'start') {
        $('product').focus();
    } else {
        $('record_id').focus();
    }
    updateMessagesPanel('');
    return true;
};

/**
 * Update content and visiblity of messages panel of dashlet
 */
updateMessagesPanel = function(content) {
    var messages_panel = $('messages_panel_' + dashlet_id);
    if (messages_panel) {
        messages_panel.innerHTML = content;
        messages_panel.parentNode.style.display = (content ? '' : 'none');
        if (content) {
            new Effect.ScrollTo(messages_panel);
            Effect.Pulsate(messages_panel, {pulses: 1});
        }
    }
    return true;
};

/**
 * Enable/disable specified button
 */
toggleButtonActive = function(btn, enabled) {
    if (btn) {
        btn.disabled = !enabled;
        if (enabled) {
            removeClass(btn, 'inactive');
        } else {
            addClass(btn, 'inactive');
        }
    }
    return true;
};

/**
 * Select production to resume, toggle button visibility
 */
selectProduction = function(element) {
    var row = $('resume_btn_row');
    if (row) {
        if (element.value) {
            // selected option
            removeClass(row, 'hidden');
            toggleButtonActive($('resumeButton'), 1);
        } else {
            // please select
            addClass(row, 'hidden');
        }
    }
    updateMessagesPanel('');
    return true;
};

/**
 * Select product (recipe) to start production for, load requests table
 */
selectProduct = function(element) {
    var product_field_names = Array('quantity', 'measure', 'measure_id', 'lot', 'lot_id', 'subtype' , 'requests_container', 'start_btn_row');
    var product_fields = {};
    for (var i = 0; i < product_field_names.length; i++) {
        if (fld = $(product_field_names[i])) {
            product_fields[product_field_names[i]] = fld;
        } else {
            return false;
        }
    }

    if (element.value) {
        // recipe option selected
        Effect.Center('loading');
        Effect.Appear('loading');

        var quantity = parseFloat(product_fields['quantity'].value);
        if (isNaN(quantity)) {
            quantity = 0;
        }
        var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=biotrade_production&dashlet=' + dashlet_id + '&custom_plugin_action=selectProduct&product=' + element.value + '&quantity=' + quantity;
        var opt = {
            asynchronous: false,
            method:       'get',
            onSuccess:    function(t) {
                if (!checkAjaxResponse(t.responseText)) {
                    return;
                }
                eval(t.responseText);

                var messages = '';
                if (result.errors) {
                    messages += '<ul class="error">\n';
                    for (var err in result.errors) {
                        if (typeof result.errors[err] == 'string') {
                            messages += '<li>' + result.errors[err] + '</li>\n';
                        }
                    }
                    messages += '</ul>';
                }
                updateMessagesPanel(messages);

                for (f in product_fields) {
                    if (f == 'requests_container') {
                        product_fields[f].innerHTML = result.requests ? result.requests : '';
                    } else if (f == 'start_btn_row') {
                        if (result.requests) {
                            removeClass(product_fields[f], 'hidden');
                        } else {
                            addClass(product_fields[f], 'hidden');
                        }
                    } else {
                        product_fields[f].value = typeof result[f] != 'undefined' ? result[f] : '';
                        if (f == 'quantity' && result.quantity_restrict) {
                            product_fields[f].setAttribute('onKeyPress', 'return changeKey(this, event, ' + result.quantity_restrict + ')');
                        }
                    }
                    if (f == 'lot') {
                        var lot_fields = $$('.lot');
                        for (var l = 0; l < lot_fields.length; l++) {
                            if (result[f]) {
                                removeClass(lot_fields[l], 'hidden');
                            } else {
                                addClass(lot_fields[l], 'hidden');
                            }
                        }
                    }
                }

                // button should be deactivated initially
                toggleButtonActive($('startButton'), 0);

                // distribute quantity
                if (typeof product_fields['quantity'].onkeyup == 'function') {
                    product_fields['quantity'].onkeyup();
                }

                Effect.Fade('loading');
            },
            on404: function(t) {
                alert('Error 404: location "' + t.statusText + '" was not found.');
                return false;
            },
            onFailure: function(t) {
                alert('Error ' + t.status + ' -- ' + t.statusText);
                return false;
            }
        };

        new Ajax.Request(url, opt);
    } else {
        // please select option
        for (f in product_fields) {
            if (f == 'requests_container') {
                product_fields[f].innerHTML = '';
            } else if (f == 'start_btn_row') {
                addClass(product_fields[f], 'hidden');
            } else if (f == 'quantity') {
                product_fields[f].setAttribute('onKeyPress', 'return changeKey(this, event, insertOnlyFloats)');
            } else {
                product_fields[f].value = '';
            }
        }
        var lot_fields = $$('.lot');
        for (var l = 0; l < lot_fields.length; l++) {
            addClass(lot_fields[l], 'hidden');
        }
        updateMessagesPanel('');
    }
    return true;
};

/**
 * Distribute total quantity to requests and free quantity and
 * validate quantity before start of production
 *
 * @param element - the quantity input
 */
calculateStartQuantity = function (element) {
    // id of article measure for Num
    var measure_num = $('measure_num') ? $('measure_num').value : '1';
    // id of measure of product
    var measure_id = $('measure_id') ? $('measure_id').value : '';

    // round entered value to GT2 quantity precision
    var pow = Math.pow(10, env.precision.gt2_quantity);
    if (element.value.match(/^[^\.]*\./)) {
        if (measure_num == measure_id) {
            element.value = Math.round(element.value);
        } else {
            var cnt = element.value.replace(/^[^\.]*\./, '');
            if (cnt.length > env.precision.gt2_quantity) {
                element.value = Math.round(element.value * pow) / pow;
            }
        }
    }

    // the total production quantity
    var quantity = parseFloat(element.value) ? parseFloat(element.value) : 0;

    // distributed quantity per request
    var qty = $$('.quantity_entered');

    // activate/deactivate button
    toggleButtonActive($('startButton'), (qty.length && quantity > 0));

    for (var i = 0; i < qty.length; i++) {
        var quantity_request = parseFloat($(qty[i].id.replace(/entered/, 'request')).value);
        if (!quantity_request) {
            quantity_request = 0;
        }
        var quantity_produced = parseFloat($(qty[i].id.replace(/entered/, 'produced')).value);
        if (!quantity_produced) {
            quantity_produced = 0;
        }
        // quantity to distribute for production request
        var quantity_entered = (measure_num == measure_id) ?
                               Math.round(quantity_request - quantity_produced) :
                               Math.round((quantity_request - quantity_produced) * pow) / pow;
        if (quantity_entered > 0) {
            if (quantity < quantity_entered) {
                quantity_entered = quantity;
                removeClass(qty[i], 'completed');
            } else {
                addClass(qty[i], 'completed');
            }
            quantity = Math.round((quantity - quantity_entered) * pow) / pow;
        } else {
            quantity_entered = 0;
        }
        qty[i].value = (measure_num == measure_id) ?
                       quantity_entered.toFixed(0) :
                       quantity_entered.toFixed(env.precision.gt2_quantity);
    }

    // distribute remaining quantity to free quantity
    var quantity_without_request = $('quantity_without_request');
    if (quantity_without_request) {
        quantity_without_request.value = (measure_num == measure_id) ?
                                         quantity.toFixed(0) :
                                         quantity.toFixed(env.precision.gt2_quantity);
    }

    return true;
};

/**
 * Load screen for some action, call some custom method
 *
 *  @param btn - button that triggers function execution
 *  @param action - name of custom method:
 *  - selectMaterials - load materials screen for selection of materials to start production with
 *  - backToStart - load start screen of dashlet
 *  - startProduction - start production and load steps screen
 *  - resumeProduction - resume production and load steps or warehouse entry screen
 *  - cancelProduction - cancel production and load start screen
 *  - warehouseEntry - load screen for warehouse entry of production
 *  - finishProduction - save production in warehouse
 */
load = function(btn, action) {

    var form = $('biotrade_production');
    // container to load response into
    var container = load.arguments.length > 2 ? load.arguments[2] : 'production_container';
    container = $(container);
    if (!form || !container) {
        return false;
    }
    toggleButtonActive(btn, 0);
    Effect.Center('loading');
    Effect.Appear('loading');

    var params;
    if (action == 'backToStart') {
        // only the 'production_data' field is necessary so don't post all
        params = new Object();
        params.production_data = $('production_data') ? $('production_data').value : '';
        params = Object.toQueryString(params);
    } else if (['cancelProduction', 'resumeProduction', 'warehouseEntry'].indexOf(action) > -1) {
        // only id of production record
        params = new Object();
        params.record_id = $('record_id') ? $('record_id').value : '';
        params = Object.toQueryString(params);
    } else {
        params = Form.serialize(form);
    }
    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=biotrade_production&dashlet=' + dashlet_id + '&custom_plugin_action=' + action;
    var opt = {
        asynchronous: false,
        method:       'post',
        parameters:   params,
        onSuccess:    function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages = '';
            if (result.errors) {
                toggleButtonActive(btn, 1);
                messages += '<ul class="error">\n';
                for (var err in result.errors) {
                    if (typeof result.errors[err] == 'string') {
                        messages += '<li>' + result.errors[err] + '</li>\n';
                    }
                }
                messages += '</ul>';
            }
            if (result.messages) {
                messages += '<ul class="message">\n';
                for (var msg in result.messages) {
                    if (typeof result.messages[msg] == 'string') {
                        messages += '<li>' + result.messages[msg] + '</li>\n';
                    }
                }
                messages += '</ul>';
            }
            updateMessagesPanel(messages);

            if (result.content) {
                container.innerHTML = result.content;
                // load and execute scripts
                var scripts = container.getElementsByTagName('script');
                for (s in scripts) {
                    ajaxLoadJS(scripts[s]);
                }

                // update dashlet caption (when content is reloaded but not when only error messages are displayed)
                var production_step_caption = $('production_step_caption');
                if (production_step_caption) {
                    production_step_caption.innerHTML = result.step_caption ? ' &raquo; ' + result.step_caption : '';
                }
            }

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    new Ajax.Request(url, opt);
};

/**
 * Processing of GT2 table when screen for selection of materials is loaded:
 * Call 'onkeyup' of quantity fields in GT2 with materials in order to set colours of rows,
 * hide first column of GT2 table, hide measure dropdowns,
 * change validation of quantity fields for articles with Num measure
 */
processLoadMaterials = function() {
    // hide first column of table
    var hide_cells = $$('#production_inner_container div > .t_grouping_table > tbody > tr > th:first-child').concat($$('#production_inner_container div > .t_grouping_table > tbody > tr:not(.batch_data) > td:first-child'));
    for (var i = 0; i < hide_cells.length; i++) {
        hide_cells[i].style.display = 'none';
    }
    var gt2_qty = $$('.production_table .grouping_table2 .commodities_transfer_quantity');
    // id of article measure for Num
    var measure_num = $('measure_num') ? $('measure_num').value : '1';
    // change validation of all quantity fields for rows with articles with Num measure
    for (var i = 0; i < gt2_qty.length; i++) {
        var me = $$('.production_table .grouping_table2 #' + gt2_qty[i].id.replace(/quantity/, 'article_measure_name'));
        me = me ? me[0].value : '';
        if (me == measure_num) {
            gt2_qty[i].setAttribute('onKeyPress', 'return changeKey(this, event, insertOnlyDigits)');
            gt2_qty[i].setAttribute('onKeyUp', 'roundNum(this); ' + gt2_qty[i].getAttribute('onKeyUp'));
        }
        // call onkeyup for field
        gt2_qty[i].onkeyup();
    }
    var batch_qty = $$('.production_table .grouping_table2 .batch_data .quantity');
    // change validation of all batch quantity fields for rows with articles with Num measure
    for (var i = 0; i < batch_qty.length; i++) {
        if (batch_qty[i].parentNode.parentNode.id.match(/^articles_\d+_batch_\d+$/)) {
            // gt2 row index
            var idx = batch_qty[i].parentNode.parentNode.id.replace(/^articles_(\d+)_batch_\d+$/, '$1');
            if (typeof(gt2_qty[idx-1]) != 'undefined' && gt2_qty[idx-1].id == 'quantity_' + idx) {
                var onKeyPress = gt2_qty[idx-1].getAttribute('onKeyPress');
                if (onKeyPress.match(/insertOnlyDigits/)) {
                    batch_qty[i].setAttribute('onKeyPress', onKeyPress);
                    batch_qty[i].setAttribute('onKeyUp', 'roundNum(this); ' + batch_qty[i].getAttribute('onKeyUp'));
                }
            }
        }
    }
    // process measure dropdowns
    processHiddenDropdowns();
    return true;
};

/**
 * Processing of GT2 table when screen for warehouse entry (finish production) is loaded:
 * hide measure dropdowns,
 * change validation of quantity fields for articles with Num measure
 */
processLoadFinish = function() {
    // all editable fields that hold quantities in this screen
    var gt2_qty = $$('.production_table .grouping_table2 .article_width');
    // id of article measure for Num
    var measure_num = $('measure_num') ? $('measure_num').value : '1';
    // change validation of all quantity fields for rows with articles with Num measure
    for (var i = 0; i < gt2_qty.length; i++) {
        var me = $$('.production_table .grouping_table2 #' + gt2_qty[i].id.replace(/article_width/, 'article_measure_name'));
        me = me ? me[0].value : '';
        if (me == measure_num) {
            gt2_qty[i].setAttribute('onKeyPress', 'return changeKey(this, event, insertOnlyDigits)');
            gt2_qty[i].setAttribute('onKeyUp', 'roundNum(this); ' + gt2_qty[i].getAttribute('onKeyUp'));
        }
    }
    // process measure dropdowns
    processHiddenDropdowns();
};

/**
 * Fuction to round quantity fields in GT2 for articles with Num measure.
 * It should be called before gt2calc().
 * @param field
 */
roundNum = function(field) {
    var val = field && field.value ? parseFloat(field.value) : 0;
    if (isNaN(val)) {
        val = 0;
    }
    field.value = Math.round(val).toFixed(0);
    return true;
};

/**
 * Set colour of GT2 rows for materials according to warehouse availability and
 * minimal necessary quantity for production
 */
processMaterialsRow = function(field) {
    if (field.className.match(/commodities_transfer_quantity/)) {
        var av_qty = $(field.id.replace(/^quantity/, 'available_quantity'));
        var min_qty = $(field.id.replace(/^quantity/, 'article_height'));
        if (field.className.match(/erred/)) {
            // article is a consumable so quantity should not be marked as erred
            if (av_qty && av_qty.value == '-') {
                removeClass(field, 'erred');
                removeClass(av_qty, 'erred');
            }
        }
        var row = field.parentNode.parentNode;
        var upd_rows = Array(row);
        if (row.parentNode.rows.length > row.rowIndex+1) {
            var next_row = row.parentNode.rows[row.rowIndex+1];
            if (next_row && next_row.className.match(/batch_data/)) {
                upd_rows.push(next_row);
                // no batches for article
                if (!next_row.innerHTML.match(/<table/)) {
                    addClass(field, 'erred');
                    if (av_qty != null) {
                        addClass(av_qty, 'erred');
                    }
                }
            }
        }
        // validate against minimal quantity for production
        if (min_qty != null) {
            var qval = parseFloat(field.value);
            if (!qval) {
                qval = 0;
            }
            var mqval = parseFloat(min_qty.value);
            if (!mqval) {
                mqval = 0;
            }
            if (qval < mqval) {
                addClass(field, 'erred');
            }
        }
        // set and remove classes
        for (var i = 0; i < upd_rows.length; i++) {
            if (field.className.match(/erred/)) {
                addClass(upd_rows[i], 'row_red');
                removeClass(upd_rows[i], 'row_green');
            } else {
                addClass(upd_rows[i], 'row_green');
                removeClass(upd_rows[i], 'row_red');
            }
        }
        // (de)activate start button
        toggleButtonActive($('startButton'), !$$('.production_table .grouping_table2 .row_red').length);
    }
    return true;
};

/**
 * Set row spans and set colours of rows for steps when steps table is loaded,
 * hide first column of grouping table, modify table captions, hide measure dropdowns
 */
processLoadSteps = function() {
    // hide first column of table
    var hide_cells = $$('#production_inner_container div > .t_grouping_table > tbody > tr > th:first-child').concat($$('#production_inner_container div > .t_grouping_table > tbody > tr:not(.batch_data) > td:first-child'));
    for (var i = 0; i < hide_cells.length; i++) {
        hide_cells[i].style.display = 'none';
    }
    // remove trailing colons of captions in grouping table
    var captions = $$('#production_inner_container div > .t_grouping_table > tbody > tr > th > div > label');
    for (var i = 0; i < captions.length; i++) {
        captions[i].innerHTML = captions[i].innerHTML.replace(/:$/, '');
    }
    // process measure dropdowns
    processHiddenDropdowns();
    var steps_table = $$('#production_inner_container .t_grouping_table');
    if (steps_table.length) {
        steps_table = steps_table[0];
        // 0-index row is the header row
        var rowspan = 1;
        var step = prev_step = 0;
        for (var i = 1; i < steps_table.rows.length; i++) {
            var row_suffix = steps_table.rows[i].id.replace(/.*(_\d+)$/, '$1');
            // value of step field in current row
            step = $(gt_fields.step + row_suffix);
            if (step != null) {
                step = step.value;
                // set step value as row class for later use
                addClass(steps_table.rows[i], 'step_' + step);
            }
            if (!prev_step) {
                prev_step = step;
                rowspan = 1;
                //display/update step start/end datetime
                processStepHistory(step);
            } else if (step == prev_step && i < steps_table.rows.length - 1) {
                rowspan++;
            } else {
                // first and last row of the step
                var fridx = i - rowspan;
                var lridx = i - 1;
                // if this is the last row of the table
                if (i == steps_table.rows.length - 1 && step == prev_step) {
                    rowspan++;
                    lridx++;
                }
                $(gt_fields.step + '_' + fridx).parentNode.rowSpan = rowspan;
                $(gt_fields.history + '_' + fridx).parentNode.rowSpan = rowspan;
                for (var j = lridx; j > fridx; j--) {
                    $(gt_fields.step + '_' + j).parentNode.style.display = 'none';
                    $(gt_fields.history + '_' + j).parentNode.style.display = 'none';
                }
                prev_step = step;
                rowspan = 1;

                //display/update step start/end datetime
                processStepHistory(step);
            }
            processStepRows();
        }
    }
    return true;
};

/**
 * Display prompt for confirmation of selection of batches with expiry date in
 * the past or in the near future before starting of production.
 */
confirmExpired = function() {
    var display_confirm = false;
    var compare_date = new Date();
    compare_date.setDate(compare_date.getDate() + parseInt(env.batch_days_limit));
    compare_date = compare_date.format('Y-m-d');
    var dates = $$('.production_table .grouping_table2 tr.batch_data input[type="hidden"].datebox');
    var data = {};
    var pow = Math.pow(10, env.precision.gt2_quantity);

    for (var d = 0; d < dates.length; d++) {
        if (dates[d].id.match(/^article_-\d+_expire_\d+$/)) {
            var date_f = $(dates[d].id.replace('expire', 'expire_formatted'));
            var qty = $(dates[d].id.replace('expire', 'quantity'));
            qty = qty ? parseFloat(qty.value) : 0;
            if (isNaN(qty)) {
                qty = 0;
            }
            qty = Math.round(qty * pow) / pow;
            if (dates[d].value && dates[d].value < compare_date && !dates[d].disabled && qty > 0) {
                display_confirm = true;
                addClass(date_f, 'erred');
                var parts = dates[d].id.split('_');
                parts[1] = Math.abs(parseInt(parts[1]));
                if (!data[parts[1]]) {
                    data[parts[1]] = {
                        name: ($('article_name_' + parts[1]) != null ? $('article_name_' + parts[1]).value : ''),
                        rows: []
                    };
                }
                data[parts[1]].rows.push(parts[3]);
            } else {
                removeClass(date_f, 'erred');
            }
        }
    }

    if (display_confirm) {
        // display confirmation window before submit
        var txt = '';
        for (var d in data) {
            txt += ' - ' + data[d].name + ': ' + data[d].rows.join(', ') + "\n";
        }
        return confirm(i18n['messages']['confirm_expired_ingredients'].replace(/\[data\]/, txt));
    } else {
        return true;
    }
};

/**
 * Start/finish step and undo start/finish step
 */
processStep = function(forward) {
    // all available steps
    var steps = $('steps');
    // current step and state
    var step_current = $('step_current');
    var step_started = $('step_started');
    var stepBtn = $('stepButton');
    var stepBackBtn = $('stepBackButton');
    if (!steps || !step_current || !step_started || !stepBtn || !stepBackBtn) {
        return false;
    }
    // get steps as array
    steps = steps.value.split(',');
    var ss = parseInt(step_started.value);
    var sc = step_current.value;
    if (ss == 0 && forward == 1) {
        sc = steps[steps.indexOf(sc)+1];
    }
    var rows = $$('.step_' + sc);
    // id of article measure for Num
    var measure_num = $('measure_num') ? $('measure_num').value : '1';

    //array with vars that should be posted to the production record
    var record_vars;
    if (forward) {
        if (ss == 0) {
            //step start: pass all the fields to store the step in the production record
            record_vars = [gt_fields.article_name, gt_fields.article_id, gt_fields.article_lot, gt_fields.article_measure, gt_fields.article_quantity, gt_fields.article_in, gt_fields.article_loss, gt_fields.description];
        } else {
            //step finish: pass only the article_loss because only this field could be edited (article_id is sent to define the row in the GT)
            // pass article_measure too for rounding of article_loss value (if necessary)
            record_vars = [gt_fields.article_id, gt_fields.article_loss, gt_fields.article_measure];
        }
    } else {
        //undo step, only article_id and step are needed to define the row to be undone
        record_vars = [gt_fields.article_id];
    }

    var msgs = '';
    // GT2 quantity precision
    var pow = Math.pow(10, env.precision.gt2_quantity);
    // validate on start and finish step, also validate on undo of finished step
    // because invalid values could have been entered in the next unstarted step
    if (forward || !ss) {
        // validate total quantity (in+loss) entered for each article in all steps against tranferred quantity
        // do not allow step action if quantity exceeded for any article
        var quantities = new Object();
        var steps_table = $$('#production_inner_container .t_grouping_table');
        if (steps_table.length) {
            steps_table = steps_table[0];
            for (var i = 1; i < steps_table.rows.length; i++) {
                var row_suffix = steps_table.rows[i].id.replace(/.*(_\d+)$/, '$1');
                var flds = ['transfer_quantity', gt_fields.article_in, gt_fields.article_loss];
                // get label and value of article measure for row
                var article_me = $(gt_fields.article_measure + '_readonly' + row_suffix);
                var article_me_val = '';
                if (article_me != null) {
                    article_me_val = article_me.options[article_me.selectedIndex].value;
                    article_me = article_me.options[article_me.selectedIndex].text;
                }
                var fld_vals = new Object();
                for (var f = 0; f < flds.length; f++) {
                    var fld = $(flds[f] + row_suffix);
                    var val = 0;
                    if (fld) {
                        val = fld.value ? parseFloat(fld.value) : 0;
                        if (isNaN(val)) {
                            val = 0;
                        }
                        if (article_me_val == measure_num) {
                            val = Math.round(val);
                        } else {
                            val = Math.round(val * pow) / pow;
                        }
                        // round and format values of editable fields
                        if (!fld.readOnly) {
                            if (article_me_val == measure_num) {
                                fld.value = val.toFixed(0);
                            } else {
                                fld.value = val.toFixed(env.precision.gt2_quantity);
                            }
                        }
                    }
                    fld_vals[flds[f]] = val;
                }
                var article_id = $(gt_fields.article_id + row_suffix).value;
                // include both quantities from step in calculation
                var article_in = fld_vals[gt_fields.article_in] + fld_vals[gt_fields.article_loss];

                if (quantities[article_id]) {
                    quantities[article_id]['article_in'] += article_in;
                } else {
                    var article_name = $(gt_fields.article_name + row_suffix).value;
                    var t_qty = (article_me_val == measure_num) ? fld_vals.transfer_quantity.toFixed(0) : fld_vals.transfer_quantity.toFixed(env.precision.gt2_quantity);
                    quantities[article_id] = {'article_name': article_name, 'article_in': article_in, 'transfer_quantity': t_qty, 'article_me': article_me, 'article_me_val': article_me_val};
                }
            }
            var errors = Array();
            for (var a_id in quantities) {
                // round the accumulated value so that validation is correct
                quantities[a_id]['article_in'] = (quantities[a_id]['article_me_val'] == measure_num) ?
                                                 Math.round(quantities[a_id]['article_in']) :
                                                 Math.round(quantities[a_id]['article_in'] * pow) / pow;
                //ToDo consumables should have transfer_quantity -1 or something recognizable (for now check if the transfer_quantity is not 0)
                if (quantities[a_id]['article_in'] > quantities[a_id]['transfer_quantity'] && quantities[a_id]['transfer_quantity'] != 0) {
                    errors.push(i18n['messages']['error_plugin_biotrade_production_invalid_article_in'].replace(/\[article\]/, quantities[a_id]['article_name']).replace(/\[transfer_quantity\]/, quantities[a_id]['transfer_quantity'] + ' ' + quantities[a_id]['article_me']));
                }
            }
            if (errors.length > 0) {
                msgs += '<ul class="error">\n';
                for (var err in errors) {
                    if (typeof errors[err] == 'string') {
                        msgs += '<li>' + errors[err] + '</li>\n';
                    }
                }
                msgs += '</ul>';
                updateMessagesPanel(msgs);
                return;
            }
        }
    }
    updateMessagesPanel(msgs);

    Effect.Center('loading');
    Effect.Appear('loading');

    // get just the necessary data to submit for save
    var params = new Object();
    // document id
    params.record_id = $('record_id') ? $('record_id').value : '';
    // step action direction: 1 - forward, 0 - back
    params.forward = forward;
    //step number
    params.step = sc;
    //step started (1) or finished (0)
    params.finish = (forward) ? ss : ((ss) ? 0 : 1);
    for (var i = 0; i < rows.length; i++) {
        for (var j = 0; j < record_vars.length; j++) {
            params[record_vars[j]+'['+i+']'] = $$('#'+rows[i].id + ' .' + record_vars[j])[0].value;
        }
    }
    params = Object.toQueryString(params);

    var url = env.base_url + '?' + env.module_param + '=dashlets&dashlets=custom_action&plugin=biotrade_production&dashlet=' + dashlet_id + '&custom_plugin_action=processStep';
    var opt = {
        asynchronous: false,
        method:       'post',
        parameters:   params,
        onSuccess:    function(t) {
            if (!checkAjaxResponse(t.responseText)) {
                return;
            }
            eval(t.responseText);

            var messages = '';
            if (result.errors) {
                messages += '<ul class="error">\n';
                for (var err in result.errors) {
                    if (typeof result.errors[err] == 'string') {
                        messages += '<li>' + result.errors[err] + '</li>\n';
                    }
                }
                messages += '</ul>';
            } else {
                // table processing
                var step_idx = steps.indexOf(sc);
                var row_suffix = '';

                if (forward) {
                    // go forward
                    if (!ss) {
                        // START STEP
                        // enable back button
                        toggleButtonActive(stepBackBtn, 1);

                        for (var i = 0; i < rows.length; i++) {
                            row_suffix = rows[i].id.replace(/.*(_\d+)$/, '$1');

                            //update the step start datetime
                            if (result.step_start){
                                $(gt_fields.step_start + row_suffix).value = result.step_start;
                                $(gt_fields.step_start + '_formatted' + row_suffix).value = result.step_start_formatted;
                                $(gt_fields.step_user + row_suffix).value = result.step_user;
                                $(gt_fields.step_user + '_readonly' + row_suffix).value = result.step_user;
                            }
                        }

                        //update the current step
                        step_current.value = sc;
                    } else {
                        // FINISH STEP
                        for (var i = 0; i < rows.length; i++) {
                            row_suffix = rows[i].id.replace(/.*(_\d+)$/, '$1');

                            //update the step end datetime
                            if (result.step_end){
                                $(gt_fields.step_end + row_suffix).value = result.step_end;
                                $(gt_fields.step_end + '_formatted' + row_suffix).value = result.step_end_formatted;
                                $(gt_fields.step_user + row_suffix).value = result.step_user;
                                $(gt_fields.step_user + '_readonly' + row_suffix).value = result.step_user;
                            }
                        }
                    }
                } else {
                    // go backward
                    if (!ss) {
                        // UNDO FINISH STEP
                        for (var i = 0; i < rows.length; i++) {
                            row_suffix = rows[i].id.replace(/.*(_\d+)$/, '$1');

                            //update the step end datetime (remove the value)
                            $(gt_fields.step_end + row_suffix).value = '';
                            $(gt_fields.step_end + '_formatted' + row_suffix).value = '';
                        }

                        if (stepBtn.disabled) {
                            //activate the button
                            toggleButtonActive(stepBtn, 1);
                        }
                    } else {
                        // UNDO START STEP
                        for (var i = 0; i < rows.length; i++) {
                            row_suffix = rows[i].id.replace(/.*(_\d+)$/, '$1');

                            //update the step start datetime (remove the value)
                            $(gt_fields.step_start + row_suffix).value = '';
                            $(gt_fields.step_start + '_formatted' + row_suffix).value = '';
                            $(gt_fields.step_user + row_suffix).value = '';
                            $(gt_fields.step_user + '_readonly' + row_suffix).value = '';
                        }

                        if (step_idx != 0) {
                            //not first step
                            //update the current step (set it to previous)
                            step_current.value = steps[step_idx-1];
                        } else {
                            //first step
                            //update the current step (no step started yet)
                            step_current.value = 0;

                            // disable back button
                            toggleButtonActive(stepBackBtn, 0);
                        }
                    }
                }

                // toggle step state
                step_started.value = ss ? 0 : 1;

                // after last step is finished
                if (step_idx >= steps.length - 1 && ss && forward) {
                    //ToDo: perhaps hiding the button is better
                    toggleButtonActive(stepBtn, 0);
                    // display warehouse entry button
                    if (whBtn = $('whButton')) {
                        removeClass(whBtn.parentNode, 'hidden');
                    }
                } else {
                    // toggle button label
                    stepBtn.innerHTML = i18n['labels']['plugin_biotrade_production_' + (ss ? 'start' : 'finish')] + '&nbsp;&raquo;';
                    if (step_idx == steps.length - 1) {
                        // enable forward button
                        toggleButtonActive(stepBtn, 1);
                        // hide warehouse entry button
                        if (whBtn = $('whButton')) {
                            addClass(whBtn.parentNode, 'hidden');
                        }
                    }
                }

                //set row colors and article_in/article_loss readonly/viewmode flag where needed
                processStepRows();

                //display/update step start/end datetime
                processStepHistory(sc);
            }
            updateMessagesPanel(messages);

            Effect.Fade('loading');
        },
        on404: function(t) {
            alert('Error 404: location "' + t.statusText + '" was not found.');
            return false;
        },
        onFailure: function(t) {
            alert('Error ' + t.status + ' -- ' + t.statusText);
            return false;
        }
    };

    new Ajax.Request(url, opt);
};

/**
 * Inserts/updates step start/end date
 *
 * @param string step - step number
 */
processStepHistory = function(step) {

    //get the first row of the step
    var row = $$('.step_' + step)[0];
    var row_suffix = row.id.replace(/.*(_\d+)$/, '$1');

    if ($$('.step_' + step + ' .step_history_info')[0]) {
        //if the step is started then the div step_history_info and step_history_info_start are created,
        var user_name = '';
        if ($(gt_fields.step_start + row_suffix).value) {
            var user = $(gt_fields.step_user + '_readonly' + row_suffix);
            user_name = user.selectedIndex > 0 ? user.options[user.selectedIndex].text : '-';
            $$('.step_' + step + ' .step_started')[0].innerHTML =
                i18n['labels']['plugin_biotrade_production_step_started'] + ':&nbsp;&nbsp;' +
                $(gt_fields.step_start + '_formatted' + row_suffix).value +
                ' (' + user_name + ')';
        } else {
            $$('.step_' + step + ' .step_started')[0].innerHTML = '';
        }
        if ($(gt_fields.step_end + row_suffix).value) {
            if ($$('.step_' + step + ' .step_ended')[0]) {
                $$('.step_' + step + ' .step_ended')[0].innerHTML =
                    i18n['labels']['plugin_biotrade_production_step_ended'] + ':&nbsp;' +
                    $(gt_fields.step_end + '_formatted' + row_suffix).value +
                    ' (' + user_name + ')';
            } else {
                var step_history_info_end = document.createElement("div");
                step_history_info_end.className = 'step_ended';
                step_history_info_end.innerHTML =
                    i18n['labels']['plugin_biotrade_production_step_ended'] + ':&nbsp;' +
                    $(gt_fields.step_end + '_formatted' + row_suffix).value +
                    ' (' + user_name + ')';
                $$('.step_' + step + ' .step_history_info')[0].appendChild(step_history_info_end);
            }
        } else {
            if ($$('.step_' + step + ' .step_ended')[0]) {
                $$('.step_' + step + ' .step_ended')[0].innerHTML = '';
            }
        }
    } else if ($(gt_fields.step_start + row_suffix).value) {
        var user = $(gt_fields.step_user + '_readonly' + row_suffix);
        var user_name = user.selectedIndex > 0 ? user.options[user.selectedIndex].text : '-';

        var step_history_info = document.createElement("div");
        step_history_info.className = 'step_history_info';

        var step_history_info_start = document.createElement("div");
        step_history_info_start.className = 'step_started';
        step_history_info_start.innerHTML =
            i18n['labels']['plugin_biotrade_production_step_started'] + ':&nbsp;&nbsp;' +
            $(gt_fields.step_start + '_formatted' + row_suffix).value +
            ' (' + user_name + ')';
        step_history_info.appendChild(step_history_info_start);

        if ($(gt_fields.step_end + row_suffix).value) {
            var step_history_info_end = document.createElement("div");
            step_history_info_end.className = 'step_ended';
            step_history_info_end.innerHTML =
                i18n['labels']['plugin_biotrade_production_step_ended'] + ':&nbsp;' +
                $(gt_fields.step_end + '_formatted' + row_suffix).value +
                ' (' + user_name + ')';
            step_history_info.appendChild(step_history_info_end);
        }
        $(gt_fields.history + row_suffix).parentNode.appendChild(step_history_info);
        // hide the actual history field
        addClass($(gt_fields.history + row_suffix), 'hidden');
    }
};

/**
 * Updates row colors according to started/finished flags
 * Updates article_in/article_loss readonly/viewmode/validation
 */
processStepRows = function() {
    var steps_table = $$('#production_inner_container .t_grouping_table');
    if (steps_table.length) {
        steps_table = steps_table[0];
        var step = 0, unfinished_step = 0, first_unstarted_step = 0;
        // id of article measure for Num
        var measure_num = $('measure_num') ? $('measure_num').value : '1';
        for (var i = 1; i < steps_table.rows.length; i++) {
            var row_suffix = steps_table.rows[i].id.replace(/.*(_\d+)$/, '$1');
            // value of step field in current row
            step = $(gt_fields.step + row_suffix);
            if (step != null) {
                step = step.value;
            }
            // set row colours, make fields readonly according to state of step
            var started = $(gt_fields.step_start + row_suffix);
            var finished = $(gt_fields.step_end + row_suffix);
            var article_quantity = $(gt_fields.article_quantity + row_suffix);
            var article_loss = $(gt_fields.article_loss + row_suffix);
            var article_in = $(gt_fields.article_in + row_suffix);
            var article_me = $(gt_fields.article_measure + row_suffix);
            if (started && started.value) {
                var row_class = 'row_yellow';
                // make field 'article_in' readonly
                if (article_in) {
                    article_in.readOnly = true;
                    addClass(article_in, 'viewmode');
                    if (article_quantity && parseFloat(article_in.value) != parseFloat(article_quantity.value)) {
                        addClass(article_in, 'red');
                    }
                }
                if (finished && finished.value) {
                    removeClass(steps_table.rows[i], 'row_yellow');
                    row_class = 'row_green';
                    // make field 'article_loss' readonly
                    if (article_loss) {
                        article_loss.readOnly = true;
                        addClass(article_loss, 'viewmode');
                    }
                } else {
                    if (article_loss) {
                        article_loss.readOnly = false;
                        removeClass(article_loss, 'viewmode');
                    }
                    unfinished_step = step;
                }
                addClass(steps_table.rows[i], row_class);
            } else {
                removeClass(steps_table.rows[i], 'row_yellow');
                removeClass(steps_table.rows[i], 'row_green');

                // make field 'article_in' readonly
                if (article_in) {
                    if (unfinished_step || first_unstarted_step && first_unstarted_step != step) {
                        //the article_in for the first unstarted step should NOT be readonly
                        article_in.readOnly = true;
                        addClass(article_in, 'viewmode');
                    } else if (!unfinished_step || $('step_current').value == 0 && i == 1) {
                        article_in.readOnly = false;
                        removeClass(article_in, 'viewmode');
                        removeClass(article_in, 'red');
                        first_unstarted_step = step;
                    }
                }
                // make field 'article_loss' readonly
                if (article_loss) {
                    if (first_unstarted_step && first_unstarted_step == step) {
                        //the article_loss for the first unstarted step should NOT be readonly
                        article_loss.readOnly = false;
                        removeClass(article_loss, 'viewmode');
                    } else {
                        article_loss.readOnly = true;
                        addClass(article_loss, 'viewmode');
                    }
                }
            }
            // change validation of all quantity fields for rows with articles with Num measure
            if (article_me && article_me.value == measure_num) {
                if (article_in) {
                    article_in.setAttribute('onKeyPress', 'return changeKey(this, event, insertOnlyDigits)');
                }
                if (article_loss) {
                    article_loss.setAttribute('onKeyPress', 'return changeKey(this, event, insertOnlyDigits)');
                }
            }
        }
    }
};

/**
 * Hides dropdowns and displays label of selected option in table cell
 */
processHiddenDropdowns = function() {
    // process measure dropdowns in a grouping table or GT2 table
    var article_me = $$('#production_inner_container .t_grouping_table .selbox.viewmode');
    for (var i = 0; i < article_me.length; i++) {
        addClass(article_me[i], 'hidden');
        if (article_me[i].selectedIndex > -1 && article_me[i].value) {
            article_me[i].parentNode.innerHTML = article_me[i].options[article_me[i].selectedIndex].text + article_me[i].parentNode.innerHTML;
        }
    }
};

/**
 * Calculates quantity in warehouse from entered produced, waste and samples
 * quantities, validates produced quantity and expiry date.
 *
 * @param element - element that function is called from
 */
validateInWarehouse = function(element) {
    if (element.id.match(/^quantity_(produced|waste|samples)$/)) {
        var fields = ['produced', 'waste', 'samples', 'in_warehouse'];
        // GT2 quantity precision
        var pow = Math.pow(10, env.precision.gt2_quantity);
        // id of article measure for Num
        var measure_num = $('measure_num') ? $('measure_num').value : '1';
        // id of measure of product
        var measure_id = $('measure_id') ? $('measure_id').value : '';
        // maximal sample quantity
        var max_samples = $('max_samples') ? parseFloat($('max_samples').value) : 0;
        if (isNaN(max_samples)) {
            max_samples = 0;
        }

        var quantities = new Object();
        for (var f = 0; f < fields.length; f++) {
            quantities[fields[f] + '_field'] = $('quantity_' + fields[f]);
            // this should not happen...
            if (!quantities[fields[f] + '_field']) {
                toggleButtonActive($('finishButton'), 0);
                return false;
            }
            if (fields[f] == 'in_warehouse') {
                continue;
            }
            var val = quantities[fields[f] + '_field'].value;
            // round entered values to GT2 quantity precision
            if (val.match(/^[^\.]*\./)) {
                if (measure_num == measure_id) {
                    quantities[fields[f] + '_field'].value = val = Math.round(val);
                } else {
                    var cnt = val.replace(/^[^\.]*\./, '');
                    if (cnt.length > env.precision.gt2_quantity) {
                        val = Math.round(val * pow) / pow;
                        quantities[fields[f] + '_field'].value = val;
                    }
                }
            }
            val = parseFloat(val);
            if (isNaN(val)) {
                quantities[fields[f] + '_field'].value = val = 0;
            }
            quantities[fields[f]] = val;
        }
        // reduce waste
        if (quantities.waste > quantities.produced) {
            quantities['waste_field'].value = quantities.waste = quantities.produced;
        }
        // reduce samples
        if (quantities.samples > max_samples) {
            quantities['samples_field'].value = quantities.samples = max_samples;
        }
        if (quantities.waste + quantities.samples > quantities.produced) {
            quantities['samples_field'].value = quantities.samples = Math.round((quantities.produced - quantities.waste) * pow) / pow;
        }
        // set value to 'in_warehouse' readonly field
        quantities['in_warehouse_field'].value = Math.round((quantities.produced - quantities.waste - quantities.samples) * pow) / pow;
    }

    // get expiry date
    var expiry_date = $('expiry_date') != null ? $('expiry_date').value : '';
    // get produced quantity
    var quantity_produced = $('quantity_produced') != null ? $('quantity_produced').value : 0;

    // if there are fields with invalid values, disable finish button
    var valid = (parseFloat(quantity_produced) >= 0 &&
        expiry_date &&
        !($$('#production_inner_container .grouping_table2 .erred').length));
    toggleButtonActive($('finishButton'), valid);

    return valid;
};

/**
 * Calculates unused quantity to be returned when production is finished.
 * Validates entered loss value and warehouse selection.
 *
 * @param element - loss or warehouse field in row
 */
calculateReturnedQuantity = function(element) {
    // get data from specified GT2 fields in row
    var fields = {
        // transfered quantity for production
        quantity: 'quantity',
        // wasted quantity from steps
        steps_loss: 'article_height',
        // total wasted quantity (from steps + more)
        loss: 'article_width',
        // used quantity from steps
        steps_used: 'article_weight',
        // unused quantity to be returned
        returned: 'article_volume',
        // warehouse to be returned into
        warehouse: 'article_trademark'
    };
    var regex = new RegExp('^' + element.id.replace(/_\d+$/, ''));
    var article = new Object();
    for (var f in fields) {
        // field that function is called from
        if (fields[f].match(regex)) {
            article[f] = article[f + '_field'] = element;
        } else {
            article[f] = $(element.id.replace(regex, fields[f]));
            // this should not happen...
            if (!article[f]) {
                toggleButtonActive($('finishButton'), 0);
                return false;
            }
            if (f.match(/^(returned|loss|warehouse)$/)) {
                article[f + '_field'] = article[f];
            }
        }
        article[f] = parseFloat(article[f].value);
        if (isNaN(article[f])) {
            article[f] = 0;
        }
    }
    // GT2 quantity precision
    var pow = Math.pow(10, env.precision.gt2_quantity);
    article.returned_field.value = article.returned =
        Math.round((article.quantity - article.steps_used - article.loss) * pow) / pow;
    if (article.steps_loss > article.loss || article.returned < 0) {
        addClass(article.loss_field, 'erred');
    } else {
        removeClass(article.loss_field, 'erred');
    }
    if (article.returned > 0 && !article.warehouse) {
        addClass(article.warehouse_field, 'erred');
    } else {
        removeClass(article.warehouse_field, 'erred');
    }

    // get expiry date
    var expiry_date = $('expiry_date') != null ? $('expiry_date').value : '';
    // get produced quantity
    var quantity_produced = $('quantity_produced') != null ? $('quantity_produced').value : 0;

    // if there are fields with invalid values, disable finish button
    var valid = (parseFloat(quantity_produced) >= 0 && expiry_date &&
        !($$('#production_inner_container .grouping_table2 .erred').length));
    toggleButtonActive($('finishButton'), valid);
};

/**
 * Display prompt for confirmation of entered production quantity (if necessary)
 * and submit data for finishing of production.
 *
 * @param - finish button
 */
confirmFinish = function(btn) {
    var quantity_produced = $('quantity_produced');
    var quantity_expected = $('quantity_expected');
    var display_confirm = false;
    if (quantity_produced != null) {
        // call validation and calculation of loss quantity fields
        var gt2_qty = $$('.production_table .grouping_table2 .article_width');
        for (var i = 0; i < gt2_qty.length; i++) {
            gt2_qty[i].onkeyup();
        }
        // perform validation and calculation before submit
        if (!validateInWarehouse(quantity_produced)) {
            return false;
        }
    } else {
        toggleButtonActive(btn, 0);
        return false;
    }
    if (quantity_expected && quantity_expected.value != '') {
        quantity_produced = parseFloat(quantity_produced.value);
        quantity_expected = parseFloat(quantity_expected.value);
        if (isNaN(quantity_produced)) {
            quantity_produced = 0;
        }
        if (isNaN(quantity_expected)) {
            quantity_expected = 0;
        }
        if (quantity_produced != quantity_expected) {
            display_confirm = true;
        }
    }
    if (display_confirm) {
        var quantity_measure = $('quantity_measure') && $('quantity_measure').value ? ' ' + $('quantity_measure').value : '';
        // display confirmation window before submit
        if (confirm(i18n['messages']['confirm_quantity_produced']
            .replace(/\[quantity_expected\]/, quantity_expected + quantity_measure)
            .replace(/\[quantity_produced\]/, quantity_produced + quantity_measure))) {
            load(btn, 'finishProduction');
        } else {
            return false;
        }
    } else {
        // directly submit
        load(btn, 'finishProduction');
    }
};
