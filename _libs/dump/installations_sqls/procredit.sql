#############################################################################
### SQL nZoom Specific Updates - ПроКредит (http://procredit.n-zoom.com/) ###
#############################################################################


######################################################################################
# 2024-02-28 - Added new automation to copy files from document to another

# Added new automation to copy files from document to another
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Копиране на файлове', 0, NULL, 1, 'documents', NULL, 'before_action', '7', '#group table source\r\nsource_file_upload_var := category_file\r\nsource_category_name_var := category_name\r\nsource_category_id_var := category_id\r\nsource_category_allowed_ids := 115\r\nsource_description_var := category_file_desc\r\nsource_date_var := \r\n\r\n#fixed variable source\r\n#source_file_upload_var := scan_file\r\n#source_category_name_var := \r\n#source_category_id_var := \r\n#source_category_allowed_ids :=\r\n#source_description_var := \r\n#source_date_var := \r\n\r\ndestination_file_upload_var := category_file\r\ndestination_category_name_var := category_name\r\ndestination_category_id_var := category_id\r\ndestination_description_var := category_file_desc\r\ndestination_date_var := category_date\r\n\r\n', 'condition := \'[request_is_post]\' && in_array(\'[action]\', array(\'setstatus\'))\r\ncondition := $request->get(\'substatus\') == \'closed_15\'\r\ncondition := $new_model->getAdditionalVarValue(\'contr_kind\') != \'108\'\r\ncondition := $new_model->getAdditionalVarValue(\'contract_id\') != \'\'', 'plugin := procredit\r\nmethod := copyFiles', 'cancel_action_on_fail := 1', 1, 1, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%procredit%' AND `method` LIKE '%copyFiles%' AND automation_type='action');

######################################################################################
# 2024-07-08 - Added automations to synd the users from the Active Directory
#            - Added LDAP integration to the installation

# Added automations to synd the users from the Active Directory
# BASE DN: OU=Head Office,DC=procreditbank,DC=bg
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT'Импортиране на потребители и служители от Active Directory (OU=Head Office,DC=procreditbank,DC=bg)', 0, NULL, 1, 'users', NULL, 'crontab', '0', '# standard startup settings\r\nperiod := 1 day\r\nstart_time := 07:00\r\nstart_before := 08:00\r\nsend_to_email := <EMAIL>\r\nsend_to_notifications := errors, warnings\r\n\r\n# define which users (using LDAP authorization) never to be deactivated\r\nignore_user_ids := \r\nignore_user_roles := \r\n\r\n# ldap connection settings\r\nldap_host := ldap://**************\r\nldap_username := <EMAIL>\r\nldap_password := Y4m2G/Y0Wh2gjKRQIpJ6tdEgk9mCiooT\r\nldap_base_dn := OU=Head Office,DC=procreditbank,DC=bg\r\n\r\nldap_search_filter := (&(objectClass=user)(sn=*)(mail=*))\r\n\r\n# ldap attributes mapping\r\nldap_attribute_username := sAMAccountName\r\nldap_attribute_email := mail\r\nldap_attribute_firstname := givenName\r\nldap_attribute_lastname := sn\r\n# do not remove this attribute, it defines whether the user account is active or not in the Active Directory\r\nldap_attribute_account_flags := userAccountControl\r\nldap_attribute_gsm := \r\nldap_attribute_position := \r\nldap_attribute_department := department\r\nldap_attribute_description := \r\nldap_attribute_company := \r\nldap_attribute_memberOf := memberOf\r\nldap_attribute_displayname := displayName\r\n\r\n# user import settings\r\nuser_username := ldap_attribute_username\r\nuser_firstname := ldap_attribute_firstname\r\nuser_lastname := ldap_attribute_lastname \r\nuser_email := ldap_attribute_email \r\nuser_role := 3\r\nuser_permissions_origin := role\r\nuser_office := \r\nuser_default_working_hours := \r\nuser_customer := \r\nuser_trademark := \r\nuser_project := \r\nuser_default_group := ldap_attribute_department\r\nuser_default_department := ldap_attribute_department\r\nuser_default_company := \r\nuser_default_cashbox := \r\nuser_default_bank_account := \r\nuser_default_warehouse := \r\nuser_groups := ldap_attribute_department\r\nuser_departments := ldap_attribute_department\r\nuser_display_name := ldap_attribute_displayname\r\n\r\n# employee import settings\r\ncreate_employee := 0\r\nemployee_name := ldap_attribute_firstname\r\nemployee_lastname := ldap_attribute_lastname\r\nemployee_email := ldap_attribute_email\r\nemployee_gsm := \r\nemployee_phone := \r\nemployee_group := 1\r\nemployee_department := \r\n\r\n\r\n#try to guess the users name by the email\r\nguess_name_by_email := 1\r\n\r\n#activate already existing users\r\nreactivate_users := 0', 'condition := 1', 'plugin := ldap\r\nmethod := syncLDAPUsers', NULL, 1, 1, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%syncLDAPUsers%' AND `settings` LIKE '%OU=Head Office%');

# BASE DN: OU=Users,OU=Branches,DC=procreditbank,DC=bg
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Импортиране на потребители и служители от Active Directory (OU=Users,OU=Branches,DC=procreditbank,DC=bg)', 0, NULL, 1, 'users', NULL, 'crontab', '0', '# standard startup settings\r\nperiod := 1 day\r\nstart_time := 07:00\r\nstart_before := 08:00\r\nsend_to_email := <EMAIL>\r\nsend_to_notifications := errors, warnings\r\n\r\n# define which users (using LDAP authorization) never to be deactivated\r\nignore_user_ids := \r\nignore_user_roles := \r\n\r\n# ldap connection settings\r\nldap_host := ldap://**************\r\nldap_username := <EMAIL>\r\nldap_password := Y4m2G/Y0Wh2gjKRQIpJ6tdEgk9mCiooT\r\nldap_base_dn := OU=Users,OU=Branches,DC=procreditbank,DC=bg\r\n\r\nldap_search_filter := (&(objectClass=user)(sn=*)(mail=*))\r\n\r\n# ldap attributes mapping\r\nldap_attribute_username := sAMAccountName\r\nldap_attribute_email := mail\r\nldap_attribute_firstname := givenName\r\nldap_attribute_lastname := sn\r\n# do not remove this attribute, it defines whether the user account is active or not in the Active Directory\r\nldap_attribute_account_flags := userAccountControl\r\nldap_attribute_gsm := \r\nldap_attribute_position := \r\nldap_attribute_department := department\r\nldap_attribute_description := \r\nldap_attribute_company := \r\nldap_attribute_memberOf := memberOf\r\nldap_attribute_displayname := displayName\r\n\r\n# user import settings\r\nuser_username := ldap_attribute_username\r\nuser_firstname := ldap_attribute_firstname\r\nuser_lastname := ldap_attribute_lastname \r\nuser_email := ldap_attribute_email \r\nuser_role := 3\r\nuser_permissions_origin := role\r\nuser_office := \r\nuser_default_working_hours := \r\nuser_customer := \r\nuser_trademark := \r\nuser_project := \r\nuser_default_group := ldap_attribute_department\r\nuser_default_department := ldap_attribute_department\r\nuser_default_company := \r\nuser_default_cashbox := \r\nuser_default_bank_account := \r\nuser_default_warehouse := \r\nuser_groups := ldap_attribute_department\r\nuser_departments := ldap_attribute_department\r\nuser_display_name := ldap_attribute_displayname\r\n\r\n# employee import settings\r\ncreate_employee := 0\r\nemployee_name := ldap_attribute_firstname\r\nemployee_lastname := ldap_attribute_lastname\r\nemployee_email := ldap_attribute_email\r\nemployee_gsm := \r\nemployee_phone := \r\nemployee_group := 1\r\nemployee_department := \r\n\r\n\r\n#try to guess the users name by the email\r\nguess_name_by_email := 1\r\n\r\n#activate already existing users\r\nreactivate_users := 0', 'condition := 1', 'plugin := ldap\r\nmethod := syncLDAPUsers', NULL, 1, 1, 1
    WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%syncLDAPUsers%' AND `settings` LIKE '%OU=Branches%');

# Added LDAP integration to the installation
UPDATE settings SET value='ldap://**************' WHERE section='users' AND name='ldap_host';
UPDATE settings SET value='' WHERE section='users' AND name='ldap_username_prefix';
UPDATE settings SET value='' WHERE section='users' AND name='ldap_username_suffix';

######################################################################################
# 2024-10-01 - Added settings for model lang and translation from employees to the LDAP integration user sync
#            - Added automation to set user roles according to position (nom type 9) and department

# Added settings for model lang and translation from employees to the LDAP integration user sync
UPDATE `automations`
SET settings=CONCAT(settings, '\r\n\r\nmodel_lang := en\r\ntranslate_from_employee := 1')
WHERE `method` LIKE '%syncLDAPUsers%' AND settings NOT LIKE '%translate_from_employee%';

# Added automation to set user roles according to position (nom type 9) and department
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Задаване на роля на потребители', 0, NULL, 1, 'users', NULL, 'crontab', '0', 'period := 1 day\r\nstart_time := 07:00\r\nstart_before := 08:00\r\nsend_to_email := <EMAIL>\r\nsend_to_notifications := errors, warnings', 'where := u.active = 1\r\nwhere := u.id IN (SELECT u.id FROM users u JOIN customers c ON u.employee=c.id LEFT JOIN customers_cstm ccstm ON ccstm.var_id=102 AND c.id=ccstm.model_id LEFT JOIN nom n ON n.id=ccstm.value LEFT JOIN automations_history ah ON u.id=ah.model_id and ah.parent_id=158 WHERE ah.added IS NULL OR ah.added<c.modified AND c.modified>u.modified OR n.id IS NOT NULL AND ah.added IS NOT NULL AND n.modified>ah.added)\r\n', 'plugin := procredit\r\nmethod := updateUserRoles', NULL, 10, 0, 1
   WHERE NOT EXISTS  (SELECT id FROM automations WHERE `method` LIKE '%updateUserRoles%');

######################################################################################
# 2024-10-02 - Updated conditions of automation to change the role of users

#Updated conditions of automation to change the role of users
SELECT id INTO @automationId FROM `automations` WHERE `method` LIKE '%updateUserRoles%';
UPDATE `automations`
SET conditions=CONCAT('where := u.active = 1
where := u.role NOT IN (12,13,14,15,16,17,18,19,20,21) AND u.id IN (SELECT u.id FROM users u JOIN customers c ON u.employee=c.id LEFT JOIN customers_cstm ccstm ON ccstm.var_id=102 AND c.id=ccstm.model_id LEFT JOIN nom n ON n.id=ccstm.value LEFT JOIN automations_history ah ON u.id=ah.model_id and ah.parent_id=', @automationId, ' WHERE ah.added IS NULL OR ah.added<c.modified AND c.modified>u.modified OR n.id IS NOT NULL AND ah.added IS NOT NULL AND n.modified>ah.added)')
WHERE `method` LIKE '%updateUserRoles%';
