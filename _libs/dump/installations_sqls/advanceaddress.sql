################################################################################
### SQL nZoom Specific Updates Адванс Адрес (http://nzoom.ocenki.bg/)  ###
################################################################################

########################################################################
# 2012-05-15 - Add new report - 'qms_management' for the BGService installation (1707)

# Add new report - 'qms_management' for the BGService installation (1707)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  ('205', 'qms_management', 'nomenclature_type_id := 10\r\ndefault_bg_color := ECEDEE\r\ndoc_type_color := FFA500\r\nnomenclature_color := 3FCC59\r\ndoc_type_width := 40\r\ncode_name_width := 635\r\ncurrent_version_min_width := 10\r\ndate_current_width := 70', '0', '0', '1');
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  ('205', 'Управление на СУК', NULL, NULL, 'bg'),
  ('205', 'Management of QMS', NULL, NULL, 'en');
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '205', '0', '1');
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module` = 'reports'
      AND `action` = 'generate_report'
      AND `model_type` = '205';

######################################################################################
# 2012-05-18 - Added price report XLS plugin
#            - Added print resume PDF plugin

#Added price report XLS plugin
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(30, 'Document', 2, 'advanceaddress', 'generatePriceReport', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(30, 'Експортиране на Оценителски доклад', 'Експортиране на Оценителски доклад в XLS формат', 'bg', NOW()),
(30, 'Export of property price report', 'Generates price report of a property (or properties) in XLS format', 'en', NOW());

#Added print resume PDF plugin
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(31, 'Document', 2, 'advanceaddress', 'printResume', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(31, 'Опечатване на резюме', '', 'bg', NOW()),
(31, 'Принт resume', '', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'office_address', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Адрес на офис', NULL, 'bg'),
(LAST_INSERT_ID(), 'Office address', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'employee_phone', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Телефон на оценител', NULL, 'bg'),
(LAST_INSERT_ID(), 'Employee phone', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'employee_email', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Email на оценител', NULL, 'bg'),
(LAST_INSERT_ID(), 'Employee email', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'main_property_data', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Данни за обект за оценяване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Property data', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'stage_of_completion', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Степен на завършеност %', NULL, 'bg'),
(LAST_INSERT_ID(), 'Степен на завършеност %', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'liquidation_value', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Ликвидационна стойност %', NULL, 'bg'),
(LAST_INSERT_ID(), 'Ликвидационна стойност %', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'net_realizable_value', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'НРС %', NULL, 'bg'),
(LAST_INSERT_ID(), 'НРС %', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value1_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Пазарна стойност (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Пазарна стойност (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value1_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Пазарна стойност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Пазарна стойност (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value2_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Пазарна стойност с отчетена степен на завършеност (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Пазарна стойност с отчетена степен на завършеност (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value2_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Пазарна стойност с отчетена степен на завършеност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Пазарна стойност с отчетена степен на завършеност (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value3_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Ликвидационна стойност (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Ликвидационна стойност (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value3_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Ликвидационна стойност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Ликвидационна стойност (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value4_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Ликвидационна стойност с отчетена степен на завършеност (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Ликвидационна стойност с отчетена степен на завършеност (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value4_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Ликвидационна стойност с отчетена степен на завършеност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Ликвидационна стойност с отчетена степен на завършеност (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value5_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'НРС (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'НРС (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value5_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'НРС (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'НРС (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value6_BGN', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'НРС с отчетена степен на завършеност (BGN)', NULL, 'bg'),
(LAST_INSERT_ID(), 'НРС с отчетена степен на завършеност (BGN)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_value6_EUR', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'НРС с отчетена степен на завършеност (EUR)', NULL, 'bg'),
(LAST_INSERT_ID(), 'НРС с отчетена степен на завършеност (EUR)', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signature1', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подпис на заверил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Подпис на заверил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signed_employee_name', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Име на служител заверил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Име на служител заверил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signature2', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подпис на изготвил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Подпис на изготвил оценката', NULL, 'en');

######################################################################################
# 2012-05-31 - In the Excel export renamed the METHOD1 keyword with COST_APPROACH

# In the Excel export renamed the METHOD1 keyword with COST_APPROACH
UPDATE `nom_cstm` SET `value` = 'COST_APPROACH' WHERE `value` ='METHOD1';
UPDATE `files_i18n` SET `name` = 'COST_APPROACH' WHERE `name` ='METHOD1';

######################################################################################
# 2012-06-12 - Added new pattern plugin for presenting certain variables in Authorization letter document as numerated list
#            - Removed some of the print placeholders, added some more

# Added new pattern plugin for presenting certain variables in Authorization letter document as numerated list
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(32, 'Document', 1, 'advanceaddress', 'authorizationLetter', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(32, 'Възлагателно писмо - списъци', '', 'bg', NOW()),
(32, 'Authorization letter - lists', '', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'attached_document_list', 'Document', 'basic', 'pattern_plugins', ',32,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Списък на приложените документи', NULL, 'bg'),
(LAST_INSERT_ID(), 'List with attached documents', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'rated_properties_list', 'Document', 'basic', 'pattern_plugins', ',32,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Списък на обектите за оценяване', NULL, 'bg'),
(LAST_INSERT_ID(), 'List of the rated properties', NULL, 'en');

# Removed some of the print placeholders, added some more
DELETE pi18n.*, p.* FROM placeholders as p, placeholders_i18n as pi18n
WHERE p.id=pi18n.parent_id AND p.pattern_id=',31,' AND (p.varname LIKE 'market_value%' OR p.varname IN ('stage_of_completion', 'liquidation_value', 'net_realizable_value'));

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'evaluation_data', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Изчислени стойности', NULL, 'bg'),
(LAST_INSERT_ID(), 'Evaluation data', NULL, 'en');

######################################################################################
# 2012-06-14 - Added new import 'advance_document_invoices' for the Advance Address installation (1751)

# Added new import 'advance_document_invoices' for the Advance Address installation (1751)
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
  (11, 'advance_document_invoices', '', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (11, 'Фактури',  NULL, 'bg'),
  (11, 'Invoices', NULL, 'en');

######################################################################################
# 2012-06-21 - Added new report 'advance_orders_media' for the Advance Address installation (1751)
#            - Added permissions for generate and export the report 'advance_orders_media' for the Advance Address installation (1751)

# Added new report 'advance_orders_media' for the Advance Address installation (1751)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(216, 'advance_orders_media', 'document_report := 2\r\norder_type := rating_type\r\nrate_purpose := assessment_purpose\r\nobject_type_nomenclature := 9\r\nobject_type_prefix := type_object\r\ncity_prefix := city\r\nrole_type := recording_role_type\r\nrole_type_executor := 4\r\nrole_user_id := recording_role_id\r\nbank_id := bank_id\r\n\r\ndocument_month_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\ndocument_year_tags := 14', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(216, 'По източник на поръчката', NULL, NULL, 'bg'),
(216, 'By orders''s media', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_orders_media' for the Advance Address installation (1751)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 216, 0, 1),
('reports', 'export', 216, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=216;

######################################################################################
# 2012-06-26 - Added new automations for the Advance Address installation (1751) that will assign executors and observers to the document depending on the report's roles

# Added new automations for the Advance Address installation (1751) that will assign executors and observers to the document from type 2
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'step_id := 1\r\nrole_id_to_assign := 3\r\nrole_type := recording_role_type\r\nrole_employee_id := recording_role_id\r\nstatus_to_change := opened\r\nsubstatus_to_change := 1', 'condition := 1', 'plugin := advance_address\r\nmethod := reportRoles', NULL, 1, 0),
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'step_id := 2\r\nsubstatus_to_check := 2\r\nrole_id_to_assign := 4\r\nrole_type := recording_role_type\r\nrole_employee_id := recording_role_id', 'condition := (''[prev_b_substatus]'' != ''2'' && ''[b_substatus]'' == ''2'') || (''[prev_b_substatus]'' == ''2'' && ''[b_substatus]'' == ''2'')', 'plugin := advance_address\r\nmethod := reportRoles', NULL, 1, 0),
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'step_id := 3\r\nsubstatus_to_check := 4\r\nrole_id_to_assign := 2\r\nrole_type := recording_role_type\r\nrole_employee_id := recording_role_id', 'condition := (''[prev_b_substatus]'' != ''4'' && ''[b_substatus]'' == ''4'') || (''[prev_b_substatus]'' == ''4'' && ''[b_substatus]'' == ''4'')', 'plugin := advance_address\r\nmethod := reportRoles', NULL, 1, 0),
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'step_id := 3\r\nsubstatus_to_check :=5\r\nrole_id_to_assign := 5\r\nrole_type := recording_role_type\r\nrole_employee_id := recording_role_id', 'condition := (''[prev_b_substatus]'' != ''5'' && ''[b_substatus]'' == ''5'') || (''[prev_b_substatus]'' == ''5'' && ''[b_substatus]'' == ''5'')', 'plugin := advance_address\r\nmethod := reportRoles', NULL, 1, 0);

######################################################################################
# 2012-06-28 - Added before_action automation for Advance Address installation (1751) that allows print with "Resume" pattern only when documents of "Report" type are in specified status

# Added before_action automation for Advance Address installation (1751) that allows print with "Resume" pattern only when documents of "Report" type are in specified status
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Проверка на статус на Доклад преди Печат', 0, NULL, 1, 'documents', NULL, 'before_action', 2, 'check_status := locked\r\ncheck_substatus := 5\r\ncheck_pattern := 2', 'condition := 1', 'plugin := advance_address\r\nmethod := checkStatusBeforePrint', NULL, 1, 0);

######################################################################################
# 2012-07-12 - Added new placeholder for Authorization letter document pattern plugin

# Added new placeholder for Authorization letter document pattern plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'assessor_name', 'Document', 'basic', 'pattern_plugins', ',32,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Оценител', NULL, 'bg'),
(LAST_INSERT_ID(), 'Assessor', NULL, 'en');

######################################################################################
# 2012-07-18 - Added new report 'advance_report_by_customers' for the Advance Address installation (1751)
#            - Added permissions for generate and export the report 'advance_report_by_customers' for the Advance Address installation (1751)

# Added new report 'advance_report_by_customers' for the Advance Address installation (1751)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(223, 'advance_report_by_customers', 'document_report := 2\r\ndocument_invoice := 7\r\nbank_type_customers := 3\r\nclient_type_customer := 2\r\nagent_type_customer := 6\r\n\r\nrole_user_id := recording_role_id\r\nbank_id := bank_id\r\nagent_id := agent_id\r\nrole_type := recording_role_type\r\nobject_type_prefix := type_object\r\nrating_type := rating_type\r\nperiod_date := award_date\r\n\r\nrole_made_by := 4\r\nrole_observed := 3\r\nrole_rated := 2\r\nrole_signed := 5\r\n\r\ndocument_month_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\ndocument_year_tags := 14', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(223, 'Справка по контрагенти', NULL, NULL, 'bg'),
(223, 'Report by customers', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_report_by_customers' for the Advance Address installation (1751)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 223, 0, 1),
('reports', 'export', 223, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=223;

######################################################################################
# 2012-08-22 - Removed all of the print placeholders, and add them again

# Removed all of the print placeholders, and add them again
DELETE pi18n.*, p.* FROM placeholders as p, placeholders_i18n as pi18n
WHERE p.id=pi18n.parent_id AND p.pattern_id=',31,';

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'office_address', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Адрес на офис', NULL, 'bg'),
(LAST_INSERT_ID(), 'Office address', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signed_employee_name', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Име на служител заверил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Име на служител заверил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signed_employee_email', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Email на служител заверил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Email на служител заверил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'main_property_data', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Данни за обект за оценяване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Property data', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signature1', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подпис на заверил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Подпис на заверил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signature2', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подпис на изготвил оценката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Подпис на изготвил оценката', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'evaluation_data', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Изчислени стойности', NULL, 'bg'),
(LAST_INSERT_ID(), 'Evaluation data', NULL, 'en');

UPDATE patterns_i18n SET content=REPLACE(content, '[employee_name]', '[signed_employee_name]') WHERE parent_id IN (SELECT id FROM patterns WHERE plugin=31);

######################################################################################
# 2012-09-21 - Added new report 'advance_report_by_addresses' for the Advance Address installation (1751)
#            - Added permissions for generate and export the report 'advance_report_by_addresses' for the Advance Address installation (1751)

# Added new report 'advance_report_by_addresses' for the Advance Address installation (1751)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(231, 'advance_report_by_addresses', 'document_report := 2\r\nnom_rate_object_type := 9\r\nnom_construction_type := 11\r\nnom_city_type := 7\r\nnom_quarter_type := 12\r\nnom_address_type := 8\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nobject_type_prefix := type_object\r\nconstruction_prefix := construction\r\ncity_prefix := city_id\r\naddress_prefix := address_id\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\nmarket_value_prefix := market_value\r\nprice_square_meter_prefix := price_square_meter\r\n\r\nrole_made_by := 4', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(231, 'Справка по адрес', NULL, NULL, 'bg'),
(231, 'Report by addresses', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_report_by_addresses' for the Advance Address installation (1751)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 231, 0, 1),
('reports', 'export', 231, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=231;

######################################################################################
# 2012-09-25 - Added new report 'advance_timesheet_by_assessor' for the Advance Address installation (remote)
#            - Added permissions for generate and export the report 'advance_timesheet_by_assessor' for the Advance Address installation (remote)
#            - Added new report 'advance_report_by_offices' for the Advance Address installation (remote)
#            - Added permissions for generate and export the report 'advance_report_by_offices' for the Advance Address installation (remote)

# Added new report 'advance_timesheet_by_assessor' for the Advance Address installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(232, 'advance_timesheet_by_assessor', 'document_report := 2\r\ndocument_invoice := 7\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nagent_id := agent_id\r\nperiod_date := award_date\r\nobject_type_prefix := type_object\r\ncity_prefix := city_id\r\naddress_prefix := address_id\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\n\r\nrole_made_by := 4\r\nrole_observed := 3\r\nrole_rated := 2\r\nrole_signed := 5\r\n\r\ninvoice_payment_date := payment_date\r\ninvoice_way_of_payment := way_of_payment', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(232, 'Отчет на оценител', NULL, NULL, 'bg'),
(232, 'Timesheet by assessor', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_timesheet_by_assessor' for the Advance Address installation (remote)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 232, 0, 1),
('reports', 'export', 232, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=232;

# Added new report 'advance_report_by_offices' for the Advance Address installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(233, 'advance_report_by_offices', 'document_report := 2\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16\r\nbank_type_customers := 3\r\nclient_type_customer := 2\r\nnomenclature_type_object := 9\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nobject_type_prefix := type_object\r\n\r\nrole_made_by := 4\r\n\r\ncustomer_employee_type := type_empl\r\ncustomer_type_inside_assessor := 1\r\ncustomer_type_partner_assessor := 2', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(233, 'Справка по офиси', NULL, NULL, 'bg'),
(233, 'Report by offices', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_report_by_offices' for the Advance Address installation (remote)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 233, 0, 1),
('reports', 'export', 233, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=233;

######################################################################################
# 2012-10-16 - Added new report 'advance_report_by_banks' for the Advance Address installation (remote)
#            - Added permissions for generate and export the report 'advance_report_by_banks' for the Advance Address installation (remote)

# Added new report 'advance_report_by_banks' for the Advance Address installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(241, 'advance_report_by_banks', 'document_report := 2\r\ndocument_correct := 8\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16\r\nbank_type_customers := 3\r\nclient_type_customer := 2\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nobject_type_prefix := type_object\r\ncity_prefix := city_id\r\naddress_prefix := address_id\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\nperiod_date := award_date\r\nviewing_date := order_date\r\nmarket_value := fair_market_value\r\nliquidation_value := liquidation_value_percent\r\nrating_type := rating_type\r\nrate_objects_total := total\r\n\r\nrole_rated_by := 2', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(241, 'Справка по банки', NULL, NULL, 'bg'),
(241, 'Report by banks', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_report_by_banks' for the Advance Address installation (remote)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 241, 0, 1),
('reports', 'export', 241, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=241;

######################################################################################
# 2012-11-29 - Added new automation to auto tag the documents depending on the deadline date
#            - Added dashlet plugin for quick setting tags to finished report documents

# Added new automation to auto tag the documents depending on the deadline date
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'month_tag_1 := 2\r\nmonth_tag_2 := 3\r\nmonth_tag_3 := 4\r\nmonth_tag_4 := 5\r\nmonth_tag_5 := 6\r\nmonth_tag_6 := 7\r\nmonth_tag_7 := 8\r\nmonth_tag_8 := 9\r\nmonth_tag_9 := 10\r\nmonth_tag_10 := 11\r\nmonth_tag_11 := 12\r\nmonth_tag_12 := 13\r\n\r\nfirst_year_set := 2012\r\nlast_year_set := 2014\r\n\r\nyear_tag_2012 := 14\r\nyear_tag_2013 := 15\r\nyear_tag_2014 := 16', 'condition := ''[prev_b_deadline]'' != ''[b_deadline]''', 'plugin := advance_address\r\nmethod := autoTagReports', NULL, 1, 0);

# Added dashlet plugin for quick setting tags to finished report documents
INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
(NULL, 'update_reporting_period_tags', 'document_type_report := 2\r\ndocument_purpose := assessment_purpose\r\ndocument_total := total\r\ndocument_user_bank := bank_id\r\n\r\nmonth_tag_1 := 2\r\nmonth_tag_2 := 3\r\nmonth_tag_3 := 4\r\nmonth_tag_4 := 5\r\nmonth_tag_5 := 6\r\nmonth_tag_6 := 7\r\nmonth_tag_7 := 8\r\nmonth_tag_8 := 9\r\nmonth_tag_9 := 10\r\nmonth_tag_10 := 11\r\nmonth_tag_11 := 12\r\nmonth_tag_12 := 13\r\n\r\nfirst_year_set := 2012\r\nlast_year_set := 2014\r\n\r\nyear_tag_2012 := 14\r\nyear_tag_2013 := 15\r\nyear_tag_2014 := 16', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Смяна на тагове', 'Инфо панел за бърза смяна на таговете на избрани документи, сортирани по контрагент.', 'bg'),
(LAST_INSERT_ID(), 'Change tags', 'Dashlet for changing the tags of selected documents, sorted by customer.', 'en');

######################################################################################
# 2012-12-12 - Added placeholder for signature date (Resume print)

# Added placeholder for signature date (Resume print)
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signature_date', 'Document', 'basic', 'pattern_plugins', ',31,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на подписване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Signature date', NULL, 'en');

######################################################################################
# 2012-12-28 - Added setting for choosing which user can change the assessor filter in the 'advance_timesheet_by_assessor' report
#            - Change settings for dashlet plugin: 'update_reporting_period_tags'

# Added setting for choosing which user can change the assessor filter in the 'advance_timesheet_by_assessor' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\n\nusers_to_change_assessor := ')
WHERE `type`='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%users_to_change_assessor%';

# Change settings for dashlet plugin: 'update_reporting_period_tags'
UPDATE `dashlets_plugins`
  SET `settings` = 'document_type_report := 2\r\ndocument_type_correct := 8\r\ncustomer_type_bank := 3\r\ndocument_purpose := assessment_purpose\r\ndocument_total := total\r\ncorrect_doc_total := total\r\ndocument_currency := currency\r\ndocument_user_bank := bank_id\r\n\r\nmonth_tag_1 := 2\r\nmonth_tag_2 := 3\r\nmonth_tag_3 := 4\r\nmonth_tag_4 := 5\r\nmonth_tag_5 := 6\r\nmonth_tag_6 := 7\r\nmonth_tag_7 := 8\r\nmonth_tag_8 := 9\r\nmonth_tag_9 := 10\r\nmonth_tag_10 := 11\r\nmonth_tag_11 := 12\r\nmonth_tag_12 := 13\r\n\r\nfirst_year_set := 2012\r\nlast_year_set := 2014\r\n\r\nyear_tag_2012 := 14\r\nyear_tag_2013 := 15\r\nyear_tag_2014 := 16'
  WHERE `type` = 'update_reporting_period_tags';

######################################################################################
# 2013-01-10 - Added new pattern plugin for business trips

# Added new pattern plugin for business trips
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
(44, 'Document', 10, 'advanceaddress', 'businessTrips', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(44, 'Командировъчни', '', 'bg', NOW()),
(44, 'Business trips', '', 'en', NOW());

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'employees_list', 'Document', 'basic', 'pattern_plugins', ',44,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Списък на командированите служители', NULL, 'bg'),
(LAST_INSERT_ID(), 'List of employees on trip', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'trip_list', 'Document', 'basic', 'pattern_plugins', ',44,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Списък на фирмите', NULL, 'bg'),
(LAST_INSERT_ID(), 'List of trips', NULL, 'en');

INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'document_list', 'Document', 'basic', 'pattern_plugins', ',44,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Списък на докладите', NULL, 'bg'),
(LAST_INSERT_ID(), 'List of report documents', NULL, 'en');

######################################################################################
# 2013-01-31 - Changed conditions for automation using method "autoTagReports"

# Changed conditions for automation using method "autoTagReports"
UPDATE `automations`
  SET `conditions` = 'condition :=1'
  WHERE `method` LIKE '%autoTagReports%';

######################################################################################
# 2013-02-25 - Change settings for dashlet plugin: 'update_reporting_period_tags' to include statuses of the searched reports

# Change settings for dashlet plugin: 'update_reporting_period_tags' to include statuses of the searched reports
UPDATE `dashlets_plugins`
  SET `settings` = CONCAT(`settings`, '\r\n\r\nlocked_substatus_signed := 5\r\nlocked_substatus_ready := 6\r\nopen_substatus_check := 4')
  WHERE `type` = 'update_reporting_period_tags' AND `settings` NOT LIKE '%locked_substatus_signed%';

######################################################################################
# 2013-03-19 - Change settings for report 'advance_timesheet_by_assessor' so the visible role columns to be modifiable

# Change settings for report 'advance_timesheet_by_assessor' so the visible role columns to be modifiable
UPDATE `reports` SET `settings` = 'document_report := 2\r\ndocument_invoice := 7\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nagent_id := agent_id\r\nperiod_date := award_date\r\nobject_type_prefix := type_object\r\ncity_prefix := city_id\r\naddress_prefix := address_id\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\n\r\ninvoice_payment_date := payment_date\r\ninvoice_way_of_payment := way_of_payment\r\ninvoice_report := payment_report_id\r\n\r\nrole_made_by := 4\r\n\r\nusers_to_change_assessor := 1,7,10,6,3,83\r\nresults_visible_columns := made_by,rated,revised\r\nmade_by_complete_with := 3\r\nrated_complete_with := 5\r\nrevised_complete_with := 5\r\nsigned_complete_with :='
WHERE `type`='advance_timesheet_by_assessor';

######################################################################################
# 2013-04-11 - Change settings for report 'advance_timesheet_by_assessor' to include the tags ids for months and years

# Change settings for report 'advance_timesheet_by_assessor' to include the tags ids for months and years
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16') WHERE `type`='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%month_tags%';

######################################################################################
# 2013-04-16 - Added new automation to validate the data in the masks when a report document is about to be closed

# Added new automation to validate the data in the masks when a report document is about to be closed
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Валидация на маски', 0, NULL, 1, 'documents', NULL, 'before_action', 2, 'configurator_1_id := 247\r\nconfigurator_2_id := 273\r\nconfigurator_3_id := 307\r\nconfigurator_4_id := 334\r\nconfigurator_5_id := 360\r\nconfigurator_6_id := 396\r\nconfigurator_7_id := 424\r\nconfigurator_8_id := 447\r\nconfigurator_9_id := 471\r\nconfigurator_10_id := 498\r\nconfigurator_11_id := 519\r\nconfigurator_12_id := 543\r\nconfigurator_13_id := 565\r\n\r\nconfigurator_1_required_fields := type_object,city_id,address_number,construction,market_value\r\nconfigurator_2_required_fields := type_object_one,city_one_id,market_value_one\r\nconfigurator_3_required_fields := type_object_two,city_two_id,address_number_two,market_value_two\r\nconfigurator_4_required_fields := type_object_three\r\nconfigurator_5_required_fields := type_object_four\r\nconfigurator_6_required_fields := type_object_five\r\nconfigurator_7_required_fields := type_object_six\r\nconfigurator_8_required_fields := type_object_seven\r\nconfigurator_9_required_fields := type_object_eight\r\nconfigurator_10_required_fields := type_object_nine\r\nconfigurator_11_required_fields := type_object_ten\r\nconfigurator_12_required_fields := type_object_eleven\r\nconfigurator_13_required_fields := type_object_twelve\r\n\r\nmarket_value_var := fair_market_value', 'condition := 1', 'plugin := advance_address\r\nmethod := checkCompletedReportsMasks', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-05-27 - Change settings for report 'advance_report_by_banks' to include the new settings and to exclude the old, unused ones

# Change settings for report 'advance_report_by_banks' to include the new settings and to exclude the old, unused ones
UPDATE `reports` SET `settings` = 'document_report := 2\r\ndocument_correct := 8\r\ndocument_authorization_letter := 1\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16\r\nbank_type_customers := 3\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nobject_type_prefix := type_object\r\ncity_prefix := city_id\r\naddress_prefix := address_id\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\nperiod_date := award_date\r\nviewing_date := order_date\r\nmarket_value := fair_market_value\r\nliquidation_value := liquidation_value_percent\r\nrating_type := rating_type\r\nrate_objects_total := total\r\n\r\nrole_rated_by := 5' WHERE `type`='advance_report_by_banks' AND `settings` NOT LIKE '%document_authorization_letter%';

######################################################################################
# 2013-06-07 - Added automation to set current datetime as deadline of report when its status is changed to Locked -> Signed

# Added automation to set current datetime as deadline of report when its status is changed to Locked -> Signed
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Задаване на Дата на представяне на Доклад при Подпис', 0, NULL, 1, 'documents', NULL, 'action', 2, '', 'condition := ''[b_substatus]'' == ''5'' && ''[prev_b_substatus]'' != ''5''', 'method := setBasicVar\r\nvar_name := deadline\r\nvar_value := date(''Y-m-d H:i:00'')', NULL, 2, 0);

######################################################################################
# 2013-06-11 - Removed incorrectly added audit data (in invalid format) for assignments during import of invoice documents

# Removed incorrectly added audit data (in invalid format) for assignments during import of invoice documents
DELETE da.*
FROM `documents_history` dh, `documents_audit` da
WHERE dh.h_id=da.parent_id AND dh.action_type='edit' AND da.field_name LIKE 'assign%' AND da.field_value LIKE 'a:%';

######################################################################################
# 2013-07-04 - Change the settings of 'advance_timesheet_by_assessor' report so the name of the address to be taken from the visible field if not presented in the DB

# Change the settings of 'advance_timesheet_by_assessor' report so the name of the address to be taken from the visible field if not presented in the DB
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'address_prefix := address_id', 'address_prefix := address') WHERE type='advance_timesheet_by_assessor' AND `settings` LIKE '%address_prefix := address_id%';

######################################################################################
# 2013-07-16 - Added new table `diag_bg_maps_statistics` to save the statistics for performed searches in BG Maps
#            - Add autocomplete filter to contain the id of the document where the search is performed from

# Added new table `diag_bg_maps_statistics` to save the statistics for performed searches in BG Maps
DROP TABLE IF EXISTS `diag_bg_maps_statistics`;
CREATE TABLE IF NOT EXISTS `diag_bg_maps_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL DEFAULT '0',
  `search_string` text COLLATE utf8_unicode_ci,
  `found_results` int(11) NOT NULL DEFAULT '0',
  `search_date` datetime NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB  DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Add autocomplete filter to contain the id of the document where the search is performed from
UPDATE `_fields_meta` SET `source`=CONCAT(`source`, '\nautocomplete_filter := <id> => $id') WHERE `model`='Document' AND `model_type`=2 AND `type`='autocompleter' AND `source` LIKE '%bgMapsFindAddresses%' AND `source` NOT LIKE '%autocomplete_filter := <id> => $id%';

######################################################################################
# 2013-07-18 - Fixed the stats of the performed searchs in BG Maps

UPDATE _fields_meta SET source=REPLACE(source, 'autocomplete := autocompleters', 'autocomplete := autocompleters\nautocomplete_dont_kill_concurent_queries := 1')
WHERE source like '%autocomplete_plugin := advance_address%' AND source NOT LIKE '%autocomplete_dont_kill_concurent_queries := 1%';

######################################################################################
# 2013-08-28 - Change settings for report 'advance_report_by_offices' to include the new var for the office of the employees

# Change settings for report 'advance_report_by_offices' to include the new var for the office of the employees
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncustomer_office := office_empl') WHERE `type`='advance_report_by_offices' AND `settings` NOT LIKE '%customer_office%';

######################################################################################
# 2013-11-11 - Old start_model_type id for autoTagReports is changed in order to keep track of previous executions but not to be triggered further more
#            - autoTagReports automation is copied with different settings in order to be executed only once per model
#            - Automation for autoTagReports needs deadline to be set so the deadline condition is moved into the conditions column in the automation table

# Old start_model_type id for autoTagReports is changed in order to keep track of previous executions but not to be triggered further more
UPDATE `automations`
  SET `start_model_type` = `start_model_type`*10000
  WHERE `method` LIKE '%autoTagReports%';

# autoTagReports automation is copied with different settings in order to be executed only once per model
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
  (NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'month_tag_1 := 2\r\nmonth_tag_2 := 3\r\nmonth_tag_3 := 4\r\nmonth_tag_4 := 5\r\nmonth_tag_5 := 6\r\nmonth_tag_6 := 7\r\nmonth_tag_7 := 8\r\nmonth_tag_8 := 9\r\nmonth_tag_9 := 10\r\nmonth_tag_10 := 11\r\nmonth_tag_11 := 12\r\nmonth_tag_12 := 13\r\n\r\nfirst_year_set := 2012\r\nlast_year_set := 2014\r\n\r\nyear_tag_2012 := 14\r\nyear_tag_2013 := 15\r\nyear_tag_2014 := 16', 'condition := 1', 'plugin := advance_address\r\nmethod := autoTagReports', NULL, 1, 1);

# Automation for autoTagReports needs deadline to be set so the deadline condition is moved into the conditions column in the automations table
UPDATE `automations` SET `conditions` = 'condition := ''[b_deadline]'' != ''''' WHERE `method` LIKE '%autoTagReports%' AND `conditions` NOT LIKE '%[b_deadline]%';

######################################################################################
# 2013-11-12 - Updated settings for 'update_reporting_period_tags' dashlet to exclude the settings for statuses which will be no longer used

# Updated settings for 'update_reporting_period_tags' dashlet to exclude the settings for statuses which will be no longer used
UPDATE `dashlets_plugins` SET `settings`= 'document_type_report := 2\r\ndocument_type_correct := 8\r\ncustomer_type_bank := 3\r\ndocument_purpose := assessment_purpose\r\ndocument_total := total\r\ncorrect_doc_total := total\r\ndocument_currency := currency\r\ndocument_user_bank := bank_id\r\n\r\nmonth_tag_1 := 2\r\nmonth_tag_2 := 3\r\nmonth_tag_3 := 4\r\nmonth_tag_4 := 5\r\nmonth_tag_5 := 6\r\nmonth_tag_6 := 7\r\nmonth_tag_7 := 8\r\nmonth_tag_8 := 9\r\nmonth_tag_9 := 10\r\nmonth_tag_10 := 11\r\nmonth_tag_11 := 12\r\nmonth_tag_12 := 13\r\n\r\nfirst_year_set := 2012\r\nlast_year_set := 2014\r\n\r\nyear_tag_2012 := 14\r\nyear_tag_2013 := 15\r\nyear_tag_2014 := 16' WHERE `type`='update_reporting_period_tags' AND `settings` LIKE '%locked_substatus_signed%';

######################################################################################
# 2013-11-19 - Updated the mega automation with 3 conditions for reports (document type 2)
#            - Removed unused transition (Bug 3765)

# Updated the mega automation with 3 conditions for reports (document type 2)
UPDATE `automations` SET settings = 'condition1_statuses := opened_3, opened_4, locked, locked_5, locked_6, closed\r\ncondition2_statuses := closed\r\ncondition3_statuses := closed\r\n\r\nconfigurator_1_id := 247\r\nconfigurator_2_id := 273\r\nconfigurator_3_id := 307\r\nconfigurator_4_id := 334\r\nconfigurator_5_id := 360\r\nconfigurator_6_id := 396\r\nconfigurator_7_id := 424\r\nconfigurator_8_id := 447\r\nconfigurator_9_id := 471\r\nconfigurator_10_id := 498\r\nconfigurator_11_id := 519\r\nconfigurator_12_id := 543\r\nconfigurator_13_id := 565\r\n\r\nconfigurator_1_required_fields := type_object,city_id,address_number,construction,market_value\r\nconfigurator_2_required_fields := type_object_one,city_one_id,market_value_one\r\nconfigurator_3_required_fields := type_object_two,city_two_id,address_number_two,market_value_two\r\nconfigurator_4_required_fields := type_object_three\r\nconfigurator_5_required_fields := type_object_four\r\nconfigurator_6_required_fields := type_object_five\r\nconfigurator_7_required_fields := type_object_six\r\nconfigurator_8_required_fields := type_object_seven\r\nconfigurator_9_required_fields := type_object_eight\r\nconfigurator_10_required_fields := type_object_nine\r\nconfigurator_11_required_fields := type_object_ten\r\nconfigurator_12_required_fields := type_object_eleven\r\nconfigurator_13_required_fields := type_object_twelve\r\n\r\nmarket_value_var := fair_market_value' WHERE method LIKE '%checkCompletedReportsMasks%';

# Removed unused transition (Bug 3765)
DELETE FROM transitions;
DELETE FROM transitions_i18n;

######################################################################################
# 2014-02-24 - Updated the mega automation set new validation rules

# Updated the mega automation set new validation rules
UPDATE `automations` SET settings = 'condition1_statuses := opened_3, opened_4, locked, locked_5, locked_6, closed\r\ncondition2_statuses := closed\r\ncondition3_statuses := closed\r\n\r\nconfigurator_1_id := 247\r\nconfigurator_2_id := 273\r\nconfigurator_3_id := 307\r\nconfigurator_4_id := 334\r\nconfigurator_5_id := 360\r\nconfigurator_6_id := 396\r\nconfigurator_7_id := 424\r\nconfigurator_8_id := 447\r\nconfigurator_9_id := 471\r\nconfigurator_10_id := 498\r\nconfigurator_11_id := 519\r\nconfigurator_12_id := 543\r\nconfigurator_13_id := 565\r\n\r\nconfigurator_1_required_fields := type_object,city_id,address_number,construction,market_value,up_area,real_up_area,construction_year,metod_one\r\nconfigurator_2_required_fields := type_object_one,city_one_id,market_value_one,land_area_one,metod_one_one\r\nconfigurator_3_required_fields := type_object_two,city_two_id,address_number_two,market_value_two,up_area_two,beds_nums\r\nconfigurator_4_required_fields := type_object_three,city_three,column_num,market_value_three\r\nconfigurator_5_required_fields := type_object_four,annual_production,market_value_four\r\nconfigurator_6_required_fields := type_object_five,city_five,used_method_five\r\nconfigurator_7_required_fields := type_object_six,city_six,market_value_six\r\nconfigurator_8_required_fields := type_object_seven,used_area,market_value_seven\r\nconfigurator_9_required_fields := type_object_eight,city_eight,activity_registration,market_value_eight\r\nconfigurator_10_required_fields := type_object_nine,city_nine,land_area_nine,market_value_nine\r\nconfigurator_11_required_fields := type_object_ten,assets_type\r\nconfigurator_12_required_fields := type_object_eleven,city_eleven,land_area_eleven,price_square_meter_eleven,market_value_eleven\r\nconfigurator_13_required_fields := type_object_twelve,financial_asset_type,market_value_twelve\r\n\r\nmarket_value_var := fair_market_value' WHERE method LIKE '%checkCompletedReportsMasks%';

######################################################################################
# 2014-04-16 - Added new report 'advance_activity_analysis' for the Advance Address installation (remote)
#            - Added permissions for generate and export the report 'advance_activity_analysis' for the Advance Address installation (remote)

# Added new report 'advance_activity_analysis' for the Advance Address installation (remote)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (288, 'advance_activity_analysis', 'document_report := 2\r\ncustomer_banks := 3\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16\r\n\r\nadvance_adress_offices := 1,2,3,4,5,6\r\n\r\nnomenclature_type_categories := 4,6,7,8,9,10,11,13,14,15,16,17,18\r\n\r\nemployee_assessor := 1\r\nemployee_partner := 2\r\nemployee_assessor_var := type_empl\r\nemployee_office_var := office_empl\r\n\r\nrole_made_by := 4\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nagent_id := agent_id\r\nperiod_date := award_date\r\nobject_type_prefix := type_object\r\ncity_prefix := city_id\r\naddress_prefix := address\r\nquarter_prefix := quarter_id\r\nstreet_num_prefix := address_number\r\n\r\ninvoice_payment_date := payment_date\r\ninvoice_way_of_payment := way_of_payment\r\ninvoice_report := payment_report_id', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (288, 'Анализ на дейността', NULL, NULL, 'bg'),
  (288, 'Activity analysis', NULL, NULL, 'en');

# Added permissions for generate and export the report 'advance_activity_analysis' for the Advance Address installation (remote)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 288, 0, 1),
  ('reports', 'export', 288, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=288;

######################################################################################
# 2014-05-07 - Updated settings for 'advance_activity_analysis' report for the Advance Address installation (remote) to remove the unused settings

# Updated settings for 'advance_activity_analysis' report for the Advance Address installation (remote) to remove the unused settings
UPDATE `reports` SET `settings`='document_report := 2\r\ncustomer_banks := 3\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16\r\n\r\nadvance_adress_offices := 1,2,3,4,5,6\r\n\r\nnomenclature_type_categories := 4,6,7,8,9,10,11,13,14,15,16,17,18\r\n\r\nemployee_assessor := 1\r\nemployee_partner := 2\r\nemployee_assessor_var := type_empl\r\nemployee_office_var := office_empl\r\n\r\nrole_made_by := 4\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nagent_id := agent_id\r\nperiod_date := award_date\r\n\r\ninvoice_payment_date := payment_date\r\ninvoice_way_of_payment := way_of_payment\r\ninvoice_report := payment_report_id' WHERE `type`='advance_activity_analysis';

######################################################################################
# 2014-07-10 - Added automation that set the employee office to authorization letter

# Added automation that set the employee office to authorization letter
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Задаване на офис на оцените във възлагателно писмо', 0, NULL, 1, 'documents', NULL, 'before_action', '1', 'var_employee_id := recording_role_id\r\nvar_employee_role_type := recording_role_type\r\nvar_employee_office := office_empl\r\nemployee_role_assessor := 4\r\n', 'condition := 1', 'plugin := advance_address\r\nmethod := setOffice', '', 0, 0);

######################################################################################
# 2014-07-22 - Added setting for visible categories in the 'advance_activity_analysis' report
#            - Added setting for additional vars to be displayed in the result table in 'advance_report_by_banks' report
#            - Added automation for report participation
#            - Added new setting for report participation automation

# Added setting for visible categories in the 'advance_activity_analysis' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nvisible_categories := 3,13,14,15,16,17,18')
WHERE `type`='advance_activity_analysis' AND `settings` NOT LIKE '%visible_categories%';

# Added setting for additional vars to be displayed in the result table in 'advance_report_by_banks' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nland_area_prefix := land_area_meters\r\nbuild_area_prefix := up_area\r\nreal_build_area_prefix := real_up_area\r\nprice_per_sq_m_prefix := price_square_meter\r\ndegree_completion_prefix := degree_completion')
WHERE `type`='advance_report_by_banks' AND `settings` NOT LIKE '%land_area_prefix%';

# Added automation for report participation
INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, after_action, nums) VALUES
  ('Участие в доклад', 'documents', 'before_action', '1', 'field_recording_role := recording_role\r\nfield_recording_role_type := recording_role_type\r\nfield_recording_role_percent := recording_role_percent\r\noption_recording_role_type_viewer := 4', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := advance_address\r\nmethod := reportParticipation', 'cancel_action_on_fail := 1', 0),
  ('Участие в доклад', 'documents', 'before_action', '2', 'field_recording_role := recording_role\r\nfield_recording_role_type := recording_role_type\r\nfield_recording_role_percent := recording_role_percent\r\noption_recording_role_type_viewer := 4', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := advance_address\r\nmethod := reportParticipation', 'cancel_action_on_fail := 1', 0);

# Added new setting for report participation automation
UPDATE automations
  SET settings = REPLACE(settings, 'field_recording_role := recording_role', 'field_recording_role := recording_role\r\nfield_recording_role_id := recording_role_id')
  WHERE method LIKE 'plugin := advance_address%method := reportParticipation'
    AND settings NOT LIKE '%field_recording_role_id := recording_role_id%';

######################################################################################
# 2014-08-27 - Added setting panel for targets in the 'advance_activity_analysis' report

# PRE-DEPLOYED # Added setting panel for targets in the 'advance_activity_analysis' report
#UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ntarget_document := 12\r\ntarget_year := year\r\ntarget_employee := employee_id\r\ntarget_month_01 := january\r\ntarget_month_02 := february\r\ntarget_month_03 := march\r\ntarget_month_04 := april\r\ntarget_month_05 := may\r\ntarget_month_06 := june\r\ntarget_month_07 := july\r\ntarget_month_08 := august\r\ntarget_month_09 := september\r\ntarget_month_10 := october\r\ntarget_month_11 := november\r\ntarget_month_12 := december\r\ntarget_partner_month_01 := january_p\r\ntarget_partner_month_02 := february_p\r\ntarget_partner_month_03 := march_p\r\ntarget_partner_month_04 := april_p\r\ntarget_partner_month_05 := may_p\r\ntarget_partner_month_06 := june_p\r\ntarget_partner_month_07 := july_p\r\ntarget_partner_month_08 := august_p\r\ntarget_partner_month_09 := september_p\r\ntarget_partner_month_10 := october_p\r\ntarget_partner_month_11 := november_p\r\ntarget_partner_month_12 := december_p')
#WHERE `type`='advance_activity_analysis' AND `settings` NOT LIKE '%target_document%';

######################################################################################
# 2014-10-15 - Update settings for 'advance_orders_media' report so the tags to be searched by section id and not by id

# Update setings for 'advance_orders_media' report so the tags to be searched by section id and not by id
UPDATE `reports` SET `settings`='document_report := 2\r\norder_type := rating_type\r\nrate_purpose := assessment_purpose\r\nobject_type_nomenclature := 9\r\nobject_type_prefix := type_object\r\ncity_prefix := city\r\nrole_type := recording_role_type\r\nrole_type_executor := 4\r\nrole_user_id := recording_role_id\r\nbank_id := bank_id\r\n\r\ndocument_year_section := 1\r\ndocument_month_section := 2' WHERE `type`='advance_orders_media' AND `settings` NOT LIKE '%document_year_section%';

######################################################################################
# 2015-01-07 - Update settings for 'advance_timesheet_by_assessor' report with data for percent rate by assessor

# Update settings for 'advance_timesheet_by_assessor' report with data for percent rate by assessor
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nemployee_type := type_empl\r\nemployee_type_other := 3\r\nemployee_percent := recording_role_percent') WHERE `type`='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%employee_type%';

######################################################################################
# 2015-03-06 - Added additional settings for 'advance_timesheet_by_assessor' report containing the rating var

# PRE-DEPLOYED # Added additional settings for 'advance_timesheet_by_assessor' report containing the rating var
#UPDATE `reports` SET `settings`=REPLACE(`settings`, 'street_num_prefix := address_number', 'street_num_prefix := address_number\r\ndocument_report_rating := rating') WHERE type='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%document_report_rating%';

######################################################################################
# 2015-03-17 - Update settings of 'advance_report_by_banks' report

# Update settings of 'advance_report_by_banks' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, ':= city_id', ':= city_') WHERE type='advance_report_by_banks' AND `settings` LIKE '%:= city_id%';

######################################################################################
# 2015-03-24 - Update settings of 'advance_report_by_banks' report with correct rules for getting the needed vars

# Update settings of 'advance_report_by_banks' report
UPDATE `reports` SET `settings`='document_report := 2\r\ndocument_correct := 8\r\ndocument_authorization_letter := 1\r\n\r\nmonth_tags := 2,3,4,5,6,7,8,9,10,11,12,13\r\nyear_tags := 14,15,16,22\r\nbank_type_customers := 3\r\n\r\nrole_user_id := recording_role_id\r\nrole_type := recording_role_type\r\nbank_id := bank_id\r\nobject_type_prefix := type_object\r\ncity_prefix := city_\r\ncity_regexp := city_(.*_)?id\r\naddress_prefix := address_\r\naddress_regexp := address_(.*_)?id\r\nquarter_prefix := quarter_\r\nquarter_regexp := quarter_(.*_)?id\r\nstreet_num_prefix := address_number\r\nstreet_num_regexp := address_number(_.*)?\r\nperiod_date := award_date\r\nviewing_date := order_date\r\nmarket_value := fair_market_value\r\nliquidation_value := liquidation_value_percent\r\nrating_type := rating_type\r\nrate_objects_total := total\r\n\r\nrole_rated_by := 5\r\n\r\nland_area_prefix := land_area_meters\r\nbuild_area_prefix := up_area\r\nreal_build_area_prefix := real_up_area\r\nprice_per_sq_m_prefix := price_square_meter\r\ndegree_completion_prefix := degree_completion' WHERE type='advance_report_by_banks' AND `settings` NOT LIKE '%city_regexp%';

######################################################################################
# 2015-05-26 - Add new setting to report 'advance_report_by_addresses'

# Add new setting to report 'advance_report_by_addresses'
UPDATE reports
  SET settings = REPLACE(settings, 'price_square_meter_prefix := price_square_meter', 'price_square_meter_prefix := price_square_meter\r\nlast_report_file := last_report')
  WHERE `type` = 'advance_report_by_addresses'
    AND settings NOT LIKE '%last_report_file%';

######################################################################################
# 2015-08-17 - Added settings for 'advance_timesheet_by_assessor' to hold assessor office var

# Added settings for 'advance_timesheet_by_assessor' to hold assessor office var
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\nemployee_office := office_empl') WHERE `type`='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%employee_office%';

######################################################################################
# 2015-11-02 - Added setting for 'advance_timesheet_by_assessor' for hiding or showing the second table in the report

# Added setting for 'advance_timesheet_by_assessor' for hiding or showing the second table in the report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nhide_second_table :=') WHERE `type`='advance_timesheet_by_assessor' AND `settings` NOT LIKE '%hide_second_table%';

######################################################################################
# 2016-05-19 - Added new automation to check if the quarter is selected correctly
#            - Extend the `diag_bg_maps_statistics` table with additional columns so the history will be stored last performed search for every BB

# Added new automation to check if the quarter is selected correctly
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  ('Проверка на квартал на избран адрес', 0, NULL, 1, 'documents', NULL, 'action', '2', 'test_mode := 0\r\nbg_maps_public_key := 23D1183B0C19A448422DF47D5BEBDA42\r\n\r\naddress_nomenclature_id := 8\r\nquarter_nomenclature_id := 12\r\naddress_fields := address, address_one, address_two, address_three, address_four, address_five, address_six, address_seven, address_eight, address_nine, address_ten, address_eleven, address_twelve\r\naddress_fields_id := address_id, address_one_id, address_two_id, address_three_id, address_four_id, address_five_id, address_six_id, address_seven_id, address_eight_id, address_nine_id, address_ten_id, address_eleven_id, address_twelve_id\r\ncity_fields := city, city_one, city_two, city_three, city_four, city_five, city_six, city_seven, city_eight, city_nine, city_ten, city_eleven, city_twelve\r\ncity_fields_id := city_id, city_one_id, city_two_id, city_three_id, city_four_id, city_five_id, city_six_id, city_seven_id, city_eight_id, city_nine_id, city_ten_id, city_eleven_id, city_twelve_id\r\nquarter_fields := quarter, quarter_one, quarter_two, quarter_three, quarter_four, quarter_five, quarter_six, quarter_seven, quarter_eight\r\nquarter_fields_id := quarter_id, quarter_one_id, quarter_two_id, quarter_three_id, quarter_four_id, quarter_five_id, quarter_six_id, quarter_seven_id, quarter_eight_id\r\naddress_number_fields := address_number,address_number_one,address_number_two,address_number_three,address_number_four,address_number_five,address_number_six,address_number_seven,address_number_eight,address_number_nine,address_number_ten,address_number_eleven,address_number_twelve\r\n\r\nquarter_city_var := city_id\r\nquarter_city_name_var := city\r\naddress_city_var := city_id\r\naddress_city_name_var := city\r\naddress_quarter_var := quarter', 'condition := ‘[action]’ == ‘edit’', 'plugin := advance_address\r\nmethod := updateAddressDistrict', NULL, 0, 0, 1);

# Extend the `diag_bg_maps_statistics` table with additional columns so the history will be stored last performed search for every BB
ALTER TABLE `diag_bg_maps_statistics`
  ADD `used_function` VARCHAR( 255 ) NOT NULL DEFAULT 'FindAddresses' AFTER `search_string`,
  ADD `bb_id` INT NOT NULL DEFAULT '0' AFTER `used_function`;

######################################################################################
# 2016-05-31 - Added settings for updateAddressDistrict automation for AON containing postcode, X and Y positions

# Added settings for updateAddressDistrict automation for AON containing postcode, X and Y positions
UPDATE automations
  SET settings = REPLACE(settings, '\r\n\r\nquarter_city_var := city_id', '\r\nxpos_fields := xpos, xpos_one, xpos_two, xpos_three, xpos_four, xpos_five, xpos_six, xpos_seven, xpos_eight, xpos_nine, xpos_ten, xpos_eleven, xpos_twelve\r\nypos_fields := ypos, ypos_one, ypos_two, ypos_three, ypos_four, ypos_five, ypos_six, ypos_seven, ypos_eight, ypos_nine, ypos_ten, ypos_eleven, ypos_twelve\r\npostcode_fields := postcode, postcode_one, postcode_two, postcode_three, postcode_four, postcode_five, postcode_six, postcode_seven, postcode_eight, postcode_nine, postcode_ten, postcode_eleven, postcode_twelve\r\n\r\nquarter_city_var := city_id')
  WHERE `method` LIKE '%method := updateAddressDistrict%' AND settings NOT LIKE '%postcode_fields%';

######################################################################################
# 2016-06-08 - Added new crontab automation to fill the coordinates for the existing reports

# Added new crontab automation to fill the coordinates for the existing reports
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Попълване на координати на на адрес в доклади', 0, NULL, 1, 'documents', NULL, 'crontab', '2', 'start_time := 22:00\r\nstart_before := 23:00\r\n\r\nmax_records_to_process := 450\r\nbg_maps_public_key := 23D1183B0C19A448422DF47D5BEBDA42\r\n\r\naddress_fields := address, address_one, address_two, address_three, address_four, address_five, address_six, address_seven, address_eight, address_nine, address_ten, address_eleven, address_twelve\r\naddress_fields_id := address_id, address_one_id, address_two_id, address_three_id, address_four_id, address_five_id, address_six_id, address_seven_id, address_eight_id, address_nine_id, address_ten_id, address_eleven_id, address_twelve_id\r\ncity_fields := city, city_one, city_two, city_three, city_four, city_five, city_six, city_seven, city_eight, city_nine, city_ten, city_eleven, city_twelve\r\ncity_fields_id := city_id, city_one_id, city_two_id, city_three_id, city_four_id, city_five_id, city_six_id, city_seven_id, city_eight_id, city_nine_id, city_ten_id, city_eleven_id, city_twelve_id\r\nquarter_fields := quarter, quarter_one, quarter_two, quarter_three, quarter_four, quarter_five, quarter_six, quarter_seven, quarter_eight\r\nquarter_fields_id := quarter_id, quarter_one_id, quarter_two_id, quarter_three_id, quarter_four_id, quarter_five_id, quarter_six_id, quarter_seven_id, quarter_eight_id\r\naddress_number_fields := address_number,address_number_one,address_number_two,address_number_three,address_number_four,address_number_five,address_number_six,address_number_seven,address_number_eight,address_number_nine,address_number_ten,address_number_eleven,address_number_twelve\r\nxpos_fields := xpos, xpos_one, xpos_two, xpos_three, xpos_four, xpos_five, xpos_six, xpos_seven, xpos_eight, xpos_nine, xpos_ten, xpos_eleven, xpos_twelve\r\nypos_fields := ypos, ypos_one, ypos_two, ypos_three, ypos_four, ypos_five, ypos_six, ypos_seven, ypos_eight, ypos_nine, ypos_ten, ypos_eleven, ypos_twelve\r\npostcode_fields := postcode, postcode_one, postcode_two, postcode_three, postcode_four, postcode_five, postcode_six, postcode_seven, postcode_eight, postcode_nine, postcode_ten, postcode_eleven, postcode_twelve\r\n\r\nnom_address_type_id := 8\r\nnom_quarter_type_id := 12\r\nnom_quarter_city_var := city_id\r\nnom_quarter_city_name_var := city\r\nnom_address_city_var := city_id\r\nnom_address_city_name_var := city\r\nnom_address_quarter_var := quarter', 'condition := 1', 'plugin := advance_address\r\nmethod := autoCompleteAddressCoordinates', NULL, 0, 0, 1);

######################################################################################
# 2016-07-05 - Added a button that generates a reference to mask the current document

# Added a button that generates a reference to mask the current document
INSERT INTO `_fields_meta` (`model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
  ('Document', 2, 'report_button', 'button', NULL, 0, 'href := reports&reports=generate_report&report_type=advance_report_by_addresses&button=1&model_id=b_id', NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, NULL, NULL, NULL);

######################################################################################
# 2017-01-09 - Change settings city_id, quarter_id and address_id

# Change settings city_id, quarter_id and address_id
UPDATE reports
SET settings = REPLACE(settings, 'city_prefix := city_id', 'city_prefix := city')
where `type`='advance_report_by_addresses';

UPDATE reports
SET settings = REPLACE(settings, 'address_prefix := address_id', 'address_prefix := address_two')
where `type`='advance_report_by_addresses';

UPDATE reports
SET settings = REPLACE(settings, 'quarter_prefix := quarter_id', 'quarter_prefix := quarter')
where `type`='advance_report_by_addresses';

######################################################################################
# 2017-01-12 - Change address_two setting to address

# Change address_two setting to address
UPDATE reports
SET settings = REPLACE(settings, 'address_prefix := address_two', 'address_prefix := address')
where `type`='advance_report_by_addresses';

######################################################################################
# 2017-01-26 - Corrected quote characters in conditions of automations with id 22 and 24

# Corrected quote characters in conditions of automations with id 22 and 24
UPDATE automations SET conditions = REPLACE(conditions, '‘', "'");
UPDATE automations SET conditions = REPLACE(conditions, '’', "'");

######################################################################################
# 2017-02-22 - Added new placeholder for Authorization letter document pattern plugin

# Added new placeholder for Authorization letter document pattern plugin
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'signatory_name', 'Document', 'basic', 'pattern_plugins', ',32,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Подписващ', NULL, 'bg'),
(LAST_INSERT_ID(), 'Signatory', NULL, 'en');

######################################################################################
# 2017-02-24 - Added placeholders [signatory_name], [assessor_name], [attached_document_list], [rated_properties_list] to Handover document (33) as well

# Added placeholders [signatory_name], [assessor_name], [attached_document_list], [rated_properties_list] to Handover document (33) as well
UPDATE placeholders SET pattern_id=',32,33,' WHERE pattern_id=',32,' AND `usage`='pattern_plugins';

######################################################################################
# 2017-09-26 - Add new automation: buildTree, for building a tree of documents

# Add new automation: buildTree, for building a tree of documents
DELETE FROM automations WHERE method LIKE '%buildTree%';
INSERT INTO automations
  SET name = 'Дърво от документи',
    module = 'documents',
    start_model_type = '13',
    automation_type = 'before_action',
    settings = '',
    conditions = 'condition := $request->get(\'build_tree\') && \'[request_is_post]\' && \'[action]\' == \'view\'',
    method = 'plugin := advance_address\r\nmethod := buildTree',
    after_action = 'cancel_action_on_fail := 1',
    nums = 0;

######################################################################################
# 2017-09-27 - Add settings to allow rest for advance assesment

# Add settings to allow rest for advance assesment
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'allowed_rest_user_agents', 'form_assesment'),
  ('rest', 'filter_vars_customers_2', 'id, name, is_company, lastname'),
  ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id'),
  ('rest', 'filter_vars_documents_15', 'id, type, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info , gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description'),
  ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, desc_level_group'),
  ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2'),
  ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_tablearea_appr_comment,picture_desc,specific_description,remain_econ_life,room_type')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-09-28 - Update REST filters for documents of type 13

# Update REST filters for documents of type 13
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-10-11 - Update REST filters for documents of type 25

INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-10-17 - Added settings that replace roles for certain users

# Added settings that replace roles for certain users
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'filter_vars_users', 'firstname, lastname'),
('rest', 'current_user_role_2', '14'),
('rest', 'current_user_role_3', '14'),
('rest', 'current_user_role_4', '14');

######################################################################################
# 2017-10-18 - Update REST filters for documents types

# Update REST filters for documents
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc'),
  ('rest', 'filter_vars_documents_15', 'id, type, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info , gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, pop_place_id'),
  ('rest', 'filter_vars_documents_18', 'type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table'),
  ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-10-18 - Update REST filters for documents of type 18

# Update REST filters for documents
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-10-27 - Update REST filters for documents of type 13 and 2

# Update REST filters for documents of type 13 and 2
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc, eco_check, soc_check'),
 ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-01 - Update REST filters for customers of type 3

# Update REST filters for customers of type 3
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_customers_3', 'standard_value_check_grp')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-07 - Update REST filters for documents of type 32

# Update REST filters for documents of type 32
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_32', 'images_grp')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-07 - Update REST filters for documents of type 13

# Update REST filters for documents of type 13
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc, eco_check, soc_check, document_relat_grp')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-08 - Update REST filters for nomenclatures of type 23

# Update REST filters for nomenclatures of type 23
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_nomenclatures_23', 'doc_type_object')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-14 - Update REST filters for users

# Update REST filters for users
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_users', 'firstname, lastname, employee')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-16 - Update REST filters for documents from type 13 to type 31

# Update REST filters for documents from type 13 to type 31
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc, eco_check, soc_check, document_relat_grp, property_num, object_name, deadline'),
 ('rest', 'filter_vars_documents_14', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_15', 'id, type, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info , gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, pop_place_id'),
 ('rest', 'filter_vars_documents_16', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_17', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table, standard_object_name, area_comment'),
 ('rest', 'filter_vars_documents_19', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_20', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_21', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_22', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_23', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_24', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type, area_comment'),
 ('rest', 'filter_vars_documents_26', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_27', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_28', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_29', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_30', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_31', 'property_num, object_name')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-11-28 - Update REST filters for documents of type 18 and type 25

# Update REST filters for documents of type 18 and type 25
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table, standard_object_name, area_comment, total_aggregated_income_approach_value, total_aggregated_market_approach_value, total_aggregated_spending_approach_value'),
 ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type, area_comment, total_area_market, total_area_income, total_area_expense')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-12-11 - Update REST filters for documents  of type 13

# Update REST filters for documents of type 13
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc, eco_check, soc_check, document_relat_grp, property_num, object_name, deadline, receive_date, delivery_term, analog_quarter_name, analog_quarter_id')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-12-13 - Update REST filters for documents of type 35

# Update REST filters for documents of type 35
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_35', 'id, rated_object_group, object_improvement_grp, customer, customer_id')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2017-12-14 - Update settings for automation: buildTree
#            - Add new automation: reportRelatedDocuments

# Update settings for automation: buildTree
UPDATE automations
  SET conditions = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'view\'\r\ncondition := $request->get(\'build_tree\')'
  WHERE method LIKE '%buildTree%';

# Add new automation: reportRelatedDocuments
DELETE FROM automations WHERE method LIKE '%reportRelatedDocuments%';
INSERT INTO automations
  SET name = 'Свързани документи',
    module = 'documents',
    start_model_type = '13',
    automation_type = 'before_action',
    settings = '',
    conditions = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'view\'',
    method = 'plugin := advance_address\r\nmethod := reportRelatedDocuments',
    after_action = 'cancel_action_on_fail := 1',
    position = 1,
    nums = 0;
INSERT INTO automations
  SET name = 'Свързани документи',
    module = 'documents',
    start_model_type = '13',
    automation_type = 'action',
    settings = '',
    conditions = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'',
    method = 'plugin := advance_address\r\nmethod := reportRelatedDocuments',
    after_action = 'cancel_action_on_fail := 1',
    position = 0,
    nums = 0;

######################################################################################
# 2018-01-05 - Update REST filters for documents of type from 14 to 37 excluding some types

# Update REST filters for documents of type from 14 to 37 excluding some types
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_34', 'id, type, first_setting, second_setting, third_setting, fourth_setting, income_approach_calc_group_table, income_approach_calc_table, comment_object, customer'),
 ('rest', 'filter_vars_documents_37', 'id, type, first_setting, second_setting, third_setting, fourth_setting, income_approach_calc_group_table, income_approach_calc_table, comment_object, customer'),
 ('rest', 'filter_vars_documents_14', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_15', 'id, type, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info , gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, pop_place_id, land_distribution, land_distribution_principle, improvement_principle'),
 ('rest', 'filter_vars_documents_16', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_17', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table, standard_object_name, area_comment'),
 ('rest', 'filter_vars_documents_19', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_20', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_21', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_22', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_23', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_24', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type, area_comment'),
 ('rest', 'filter_vars_documents_26', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_27', 'property_num, object_name'),
 ('rest', 'filter_vars_documents_28', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_29', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_30', 'id, type, property_num, object_name'),
 ('rest', 'filter_vars_documents_31', 'id, type, property_num, object_name')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-01-08 - Update REST filters for documents of type from 18 and 25
# 			 - Update source for category_sek_name by adding a filter by standard_object_id

# Update REST filters for documents of type from 18 to 25 excluding some types
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_18', 'id, type, documents_description, choose_mask_sec, quantity_objects, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, year_last_repair, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, electric_installation,piping_installation,sewage_installation,heating_installation,ventilation_air_conditioning,gasification,security_installation,elevator,fire_installation,bms_installation,structured_cabling,photovoltaic_installation,area_group_table,aggregated_areas_group_table,aggregated_area_comment,picture_desc,spec_desc,standard_SEK,assessment_category, additional costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability,desc_level_group,standard_object_name,standard_object_name_id,dograma_table,doors_table,earth_table,electricity_table,equalizing_screed_table,expenses_profit_table,external_table,finish_table,floor_table,heating_table,iling_table,installations_table,iron_stuff_table,layer_walls_table,lift_table,not_desc_table,other_table,paint_table,piping_table,profit_table,reinforced_concrete_table,roof_water_drainage_table,rough_not_table,rough_table,sanitary_table,thermal_not_table,thermal_table,total_smr_table,ventilation_table,walls_roof_table,water_table,windows_table, standard_object_name, area_comment, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value'),
 ('rest', 'filter_vars_documents_25', 'id,type,choose_mask_third,econ_group_table,documents_description,object_name,use_way,object_id,object_floor,object_levels,object_prospect,last_repair_year,object_functionality,finish_works,support_condition,object_impression,object_isolation,basic_room_number,service_room_number,other_rooms,balcony_number,outer_door,bevels_presence,electricity_info,plumbing_info,heating_info,ventilation_conditioning,gasification_info,security_info,fire_instalation,cable_structure,area_group_table,area_appr_group_table,area_appr_comment,picture_desc,specific_description,remain_econ_life,room_type, area_comment, total_area_expense, total_area_market, total_area_income')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Update source for category_sek_name by adding a filter by standard_object_id
UPDATE `_fields_meta` SET `source`=CONCAT(`source`, '\nautocomplete_filter := <a__standard_object_id> => $standard_object_id')
WHERE model= 'Document' AND name = 'category_sek_name' AND model_type = 35 AND `source` NOT LIKE '%autocomplete_filter := <a__standard_object_id> => $standard_object_id%';

######################################################################################
# 2018-01-16 - Update REST filters for documents of type for type 38

# Update REST filters for documents of type for type 38
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_38', 'id, type, customer, gen_assumptions_group, spec_assumptions_group, swot_table, swot_config, established_differences_table');

######################################################################################
# 2018-01-17 - Update REST filters for documents of type for type 39

# Update REST filters for documents of type for type 39
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_39', 'id, type, customer, market_approach_group, suggest_group, total_final_object_value_bgn, total_final_object_value_eur, total_forced_sale_object_value_bgn, total_forced_sale_object_value_eur, suggest_table, forced_sale_group, mortgage_value_group, suggest_value_group, performance_group, performance_table, perf_total_object_value_bgn, perf_total_object_value_eur, perf_total_forced_sale_value_bgn, perf_total_forced_sale_value_eur, suggest_land_table, sugg_final_object_value_bgn, sugg_final_object_value_eur, total_nrz_object_value_bgn, total_nrz_object_value_eur, sugg_forced_sale_object_value_bgn, sugg_forced_sale_object_value_eur');

######################################################################################
# 2018-01-24 - Update REST filters for documents of type for type 40

# Update REST filters for documents of type for type 40
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_40', 'id, type, customer, report_name, assessment_object, to_current_hierarhy_name, average_return, location_table, size_table, quality_table, functional_spec_table, assessment_object_id, to_current_hierarchy_id, to_current_hierarchy_name, capital_rate, total_weighted_value, long_term_growth, discount_rate, capital_rate_id, town_table')
 ON DUPLICATE KEY UPDATE
  `value` = VALUES(`value`);

######################################################################################
# 2018-01-29 - Added bullet to rename autocomplete nomenclature names

# Added bullet to rename autocomplete nomenclature names
INSERT IGNORE INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
('fixBBNomenclatureNames', 'Rename autocomplete nomenclature names', 14230, 1, 1, NOW(), '0000-00-00 00:00:00');

######################################################################################
# 2018-01-31 - Update REST filters for documents of type for type 34, 37, 40 customers of type 3 and nomenclatures type 89

# Update REST filters for documents of type for type 34, 37, 40 customers of type 3 and nomenclatures type 89
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_nomenclatures_89', 'id, capital_rate_id, name'),
 ('rest', 'filter_vars_customers_3', 'standard_value_check_grp, capitalization_rate_min, capitalization_rate_max'),
 ('rest', 'filter_vars_documents_37', 'id, type, first_setting, second_setting, third_setting, fourth_setting, income_approach_calc_group_table, income_approach_calc_table, comment_object, customer, capital_rate_bank, rationale, other_capital_rate, other_rationale'),
 ('rest', 'filter_vars_documents_34', 'id, type, first_setting, second_setting, third_setting, fourth_setting, income_approach_calc_group_table, income_approach_calc_table, comment_object, customer, capital_rate_bank, rationale, other_capital_rate, other_rationale'),
 ('rest', 'filter_vars_documents_40', 'id, type, customer, report_name, assessment_object, to_current_hierarhy_name, average_return, location_table, size_table, quality_table, functional_spec_table, assessment_object_id, to_current_hierarchy_id, to_current_hierarchy_name, capital_rate, total_weighted_value, long_term_growth, discount_rate, capital_rate_id, town_table, total_weight, total_weighted_value')
ON DUPLICATE KEY UPDATE
 `value` = VALUES(`value`);

######################################################################################
# 2018-02-01 - Add vars filters for nomenclatures and documents used through REST

# Add vars filters for nomenclatures and documents used through REST
INSERT IGNORE INTO settings (section, name, value) VALUES ('rest', 'filter_vars_documents_33', 'all');
INSERT IGNORE INTO settings (section, name, value) VALUES ('rest', 'filter_vars_nomenclatures_84', 'all');

######################################################################################
# 2018-02-07 - Update REST filters for nomeclatures 85 and 23

# Update REST filters for nomeclatures 85 and 23
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_nomenclatures_85', 'id, name, type, ind_analog_type, ind_analog_objects_grp'),
 ('rest', 'filter_vars_nomenclatures_23', 'doc_type_object')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-02-14 - Added new column `xml_returned` in diag_bg_maps_statistics to keep information about the xml that we received from BG Maps

# Added new column `xml_returned` in diag_bg_maps_statistics to keep information about the xml that we received from BG Maps
ALTER TABLE `diag_bg_maps_statistics` ADD `xml_returned` TEXT NOT NULL AFTER `found_results`;

######################################################################################
# 2018-02-15 - Add new automation: reportRelatedDocumentsOnEditFirstLevel

# Add new automation: reportRelatedDocumentsOnEditFirstLevel
INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, nums)
  SELECT 'Свързани документи при редакция на обект от първо ниво' AS name,
      'documents' AS module,
      id AS start_model_type,
      'action' AS automation_type,
      '' AS settings,
      'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'' AS conditions,
      'plugin := advance_address\r\nmethod := reportRelatedDocumentsOnEditFirstLevel' AS method,
      0 AS nums
    FROM documents_types
    WHERE id IN (14, 15, 16)
      AND id NOT IN (SELECT start_model_type FROM automations WHERE module = 'documents' AND method LIKE '%reportRelatedDocumentsOnEditFirstLevel%');

######################################################################################
# 2018-02-23 - Added patterns plugin for preparing the needed extra data for report (document type 13)

# Added patterns plugin for preparing the needed extra data for report (document type 13)
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
(83, 'Document', 13, 'advanceaddress', 'prepareReport', '', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(83, 'Подготовка за печат на доклад', '', 'bg', NOW()),
(83, 'Print report', '', 'en', NOW());

# Added placeholders for special data which will be completed in the annex file
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'bank_logo', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Лого на банката', NULL, 'bg'),
(LAST_INSERT_ID(), 'Bank logo', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'object_address', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Адрес на обекта', NULL, 'bg'),
(LAST_INSERT_ID(), 'Site object', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'm_x', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Отметка за пазарен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Square check for market approach', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'r_x', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Отметка за приходен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Square check for revenue approach', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'c_x', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Отметка за разходен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Square check for costly approach', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'market_methods', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Методи за пазарен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Мarket approach methods', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'revenue_methods', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Методи за приходен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Revenue approach methods', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'costly_methods', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Методи за разходен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Costly approach methods', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'own_comp_certificate', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Сертификат', NULL, 'bg'),
(LAST_INSERT_ID(), 'Certificate', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'sertificate_num', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер на сертификат', NULL, 'bg'),
(LAST_INSERT_ID(), 'Certificate number', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'international_standards_deviations', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Отклонения', NULL, 'bg'),
(LAST_INSERT_ID(), 'Deviations', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_map_one', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Карта на адреса 1', NULL, 'bg'),
(LAST_INSERT_ID(), 'Map image 1', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_map_two', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Карта на адреса 2', NULL, 'bg'),
(LAST_INSERT_ID(), 'Map image 2', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'established_differences', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Установени разлики', NULL, 'bg'),
(LAST_INSERT_ID(), 'Established differences', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'special_assumptions_swot', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Позитивни страни', NULL, 'bg'),
(LAST_INSERT_ID(), 'Pros', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'spec_asumptions_name_swot', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Негативни страни', NULL, 'bg'),
(LAST_INSERT_ID(), 'Cons', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'legal_compliance', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Правно съотношение', NULL, 'bg'),
(LAST_INSERT_ID(), 'Legal compliance', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'technical_compliance', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Техническо съответствие', NULL, 'bg'),
(LAST_INSERT_ID(), 'Technical compliance', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'financial_justification', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Финансова обоснованост', NULL, 'bg'),
(LAST_INSERT_ID(), 'Financial justification', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'conclusion_def', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Заключение', NULL, 'bg'),
(LAST_INSERT_ID(), 'Conclusion', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'C1_2_documents', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица C1.1 Документи', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table C1.1 Documents', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'D1_1_assumptions', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица C1.1 Допускания', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table C1.1 Аssumptions', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'R1_1_results', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица R1.1 Резултати', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table R1.1 Results', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'R1_2_results', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица R1.2 Пазарен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table R1.2 Market approach', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'R1_3_forced_sale', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица R1.3 Принудителна продажба', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table R1.3 Forced sale', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'R1_6_results', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица R1.6 Пазарен подход', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table R1.6 Market approach', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'hierarchy', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица йерархия', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table hierarchy', NULL, 'en');

######################################################################################
# 2018-02-26 - Update settings of automations related to automation 'reportRelatedDocuments'
#            - Add automation 'reportRelatedDocumentsOnEditYesNo' which triggers automation 'reportRelatedDocuments'

# Update settings of automations related to automation 'reportRelatedDocuments'
UPDATE automations
  SET settings =
'# Документи от първо ниво
doc_types_first_level := 14,15,16
# Маски, които са обект на оценка в пазарните норми (type 40)
doc_revenue_approach_market_norms_assessment_objects_types := 25,26
# Маски, които са обекти на отчета в аналозите за наем (type 36)
doc_revenue_approach_analog_rent_object_report_types := 25,26,27,28,29,30,31'
  WHERE method LIKE '%reportRelatedDocuments%';
# Add automation 'reportRelatedDocumentsOnEditYesNo' which triggers automation 'reportRelatedDocuments'
INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, nums)
  SELECT CONCAT('Свързани документи при редакция на ', IF(id = 37, 'Да', 'Не')) AS name,
      'documents' AS module,
      id AS start_model_type,
      'action' AS automation_type,
'# Документи от първо ниво
doc_types_first_level := 14,15,16
# Маски, които са обект на оценка в пазарните норми (type 40)
doc_revenue_approach_market_norms_assessment_objects_types := 25,26
# Маски, които са обекти на отчета в аналозите за наем (type 36)
doc_revenue_approach_analog_rent_object_report_types := 25,26,27,28,29,30,31' AS settings,
      'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'' AS conditions,
      'plugin := advance_address\r\nmethod := reportRelatedDocumentsOnEditYesNo' AS method,
      0 AS nums
    FROM documents_types
    WHERE id IN (34, 37)
      AND id NOT IN (SELECT start_model_type FROM automations WHERE module = 'documents' AND method LIKE '%reportRelatedDocumentsOnEditYesNo%');

######################################################################################
# 2018-02-26 - Update REST filters for documents of type 2

# Update REST filters for documents of type 2
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-02-27 - Added a_graphics placeholder to the prepareReport plugin
#            - Update REST filters for documents of type 13
#            - Update REST filters for customers of type 1

# Added a_graphics placeholder to the prepareReport plugin
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_graphics', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Графика', NULL, 'bg'),
(LAST_INSERT_ID(), 'Scheme image', NULL, 'en');

# Update REST filters for documents of type 13
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_13', 'id, type, full_num, custom_num, added, name, previous_report_name, order_date, award_date, date, work_term, bank_name, bank_branch_name, customer, owner_name, loan_applicant_name, rating_type, assessment_purpose, currency_methods, ref_currencies, office, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, property_right_description, differ_from_standart, property_right_name, object_group_table, populated_place, municipality_name, region_name, postcode, street_name, street_number, gps_x, gps_y, map_one, map_two,region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, additional_desc_name, additional_desc_full, market_desc_name, market_desc_info, graphics, documents_description, choose_mask_first, property_num, identity_type, buildings_number, buildings_status, pop_place, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, area_table, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, hierarchy_document_relat_grp, created_from_document_id, created_from_document_name, all, bank_id, bank_branch_id, loan_applicant_id, differ_from_standart_id, property_right_id, populated_place_id, region_description_id,municipality_id, region_id, street_id, additional_desc_id, market_desc_id, previous_report_id, doc_group, doc_type, doc_number, doc_issued_by, valid_until, for_property, doc_description, doc_attached_file, date_uploaded, doc_added_by, images_grp, image_upload, image_position, image_main, image_desc, eco_check, soc_check, document_relat_grp, property_num, object_name, deadline, receive_date, delivery_term, analog_quarter_name, analog_quarter_id, assessment_purpose_desc, object_desc_long, to_date_use_desc, employee')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Update REST filters for customers of type 1
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_customers_1', 'id, name, lastname')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-02-28 - Added setting for docx2pdf conversion

# Added setting for docx2pdf conversion
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('docx2pdf', 'cloudconvert_apikey', 'a1NFz8-nhUUmI5IjxHf7hlOTsYYG5_7mwrwisIy6lz5cWtFbj3AXl1-pqPfLfOYXhuXzps-VffgdOKepyXqcFg');

######################################################################################
# 2018-03-01 - Update REST filters for documents of type 14 15 16 17 and 41

# Update REST filters for documents of type 14 15 16 17 and 41
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_14', 'all'),
  ('rest', 'filter_vars_documents_15', 'all'),
  ('rest', 'filter_vars_documents_16', 'all'),
  ('rest', 'filter_vars_documents_17', 'all'),
  ('rest', 'filter_vars_documents_41', 'all')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-03-08 - Update REST filters for users

# Update REST filters for users
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_users', 'firstname, lastname, employee, id')
  ON DUPLICATE KEY UPDATE
   `value` = VALUES(`value`);

######################################################################################
# 2018-03-13 - Update REST filters for documents of type 13

# Update REST filters for documents of type 13
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_13', 'all')
  ON DUPLICATE KEY UPDATE
   `value` = VALUES(`value`);

######################################################################################
# 2018-03-14 - Update REST filters for documents of type 39

# Update REST filters for documents of type 39
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_39', 'all')
  ON DUPLICATE KEY UPDATE
   `value` = VALUES(`value`);

######################################################################################
# 2018-03-15 - Update REST filters for documents of type 36
#            - Add vars filters for nomenclatures used through REST

# Update REST filters for documents of type 36
 INSERT INTO `settings` (`section`, `name`, `value`) VALUES #
   ('rest', 'filter_vars_documents_36', 'all') #
   ON DUPLICATE KEY UPDATE #
     `value` = VALUES(`value`); #

# Add vars filters for nomenclatures used through REST
INSERT IGNORE INTO settings (section, name, value) VALUES ('rest', 'filter_vars_nomenclatures_91', 'all');

######################################################################################
# 2018-03-22 - Added placeholders to the prepareReport plugin

# Added placeholders to the prepareReport plugin
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'custom_num', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Номер включващ "Реф №:"', NULL, 'bg'),
(LAST_INSERT_ID(), 'Custom document number', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_main_image', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Основно изображение от Оглед (мобилно приложение)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Main property image', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_owner_name_row', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Собственик на обекта (ред заедно с етикета)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Property owner', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_loan_applicant_name_row', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Кредитоискател (ред заедно с етикета)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Loan applicant', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_office_address', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Адрес на офиса', NULL, 'bg'),
(LAST_INSERT_ID(), 'Office address', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_employee_mobile', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Мобилни телефони на служител', NULL, 'bg'),
(LAST_INSERT_ID(), 'Employee mobile phones', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_owner_loan_applicant', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица с Възложител, Собственик, Кредитоискател', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table with Customer, Owner, Loan Applicant', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'a_assessment_purpose', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Цел на оценката (взема се от номенклатура Цели на оценката)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Assessment Purpose', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'bank_fin_institution_text', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Допълнителен текст за банки и финансови институции', NULL, 'bg'),
(LAST_INSERT_ID(), 'Text for banks and financial institutions', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_market_assessment', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица "ПАЗАРНА ИНФОРМАЦИЯ" (колоната за графика се скрива ако липсва изображение)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table Market Assessment', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'table_established_differences', 'Document', 'basic', 'patterns', ',83,', '', 0);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Таблица "УСТАНОВЕНИ РАЗЛИКИ " (не се показва за нови оценки)', NULL, 'bg'),
(LAST_INSERT_ID(), 'Table Established Differences', NULL, 'en');

# Delete hierarchy placeholder
DELETE pli.*, pl.* FROM placeholders as pl JOIN placeholders_i18n pli ON pl.id=pli.parent_id AND pl.varname='hierarchy' AND pattern_id=',83,';

######################################################################################
# 2018-03-29 - Update REST filters for statuses for documents

# Update REST filters for statuses for documents
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_documents_statuss', 'all')
  ON DUPLICATE KEY UPDATE
   `value` = VALUES(`value`);

######################################################################################
# 2018-04-04 - Updated settings of the bgMapsFindAddresses automation plugin so it can use districts

# Updated settings of the bgMapsFindAddresses automation plugin so it can use districts
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\n', '\r\n') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `source` NOT LIKE '%\r\n%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\n\r\nautocomplete_filter := <type> =>', '\r\nautocomplete_fill_options := $district => <district_id>\r\nautocomplete_fill_options := $district_name => <district_name>\r\n\r\nautocomplete_filter := <type> =>') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` NOT LIKE '%autocomplete_fill_options := $district_name => <district_name>%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\nautocomplete_plugin_param_address_fields :=', '\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_district_nomenclature_id := 95\r\nautocomplete_plugin_param_address_fields :=') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` NOT LIKE '%autocomplete_plugin_param_district_nomenclature_id := 95%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\nautocomplete_plugin_param_quarter_city_var :=', '\r\nautocomplete_plugin_param_district_city_var := city_id\r\nautocomplete_plugin_param_district_city_name_var := city\r\nautocomplete_plugin_param_quarter_city_var :=') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` NOT LIKE '%autocomplete_plugin_param_district_city_name_var := city%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\nautocomplete_plugin_param_address_city_var :=', '\r\nautocomplete_plugin_param_quarter_district_var := district_id\r\nautocomplete_plugin_param_quarter_district_name_var := district_name\r\nautocomplete_plugin_param_address_city_var :=') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` NOT LIKE '%autocomplete_plugin_param_quarter_district_var := district_id%';

######################################################################################
# 2018-04-10 - Fix duplicating settings for bgMapsFindAddresses autocompleter plugin

# Fix duplicating settings for bgMapsFindAddresses autocompleter plugin
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\n', '\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\n') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\n%';

######################################################################################
# 2018-04-30 - Add REST filters for customers of type 3

# Add REST filters for customers of type 3
UPDATE settings
  SET value = 'id, name, lastname, standard_value_check_grp, capitalization_rate_min, capitalization_rate_max'
  WHERE section = 'rest'
    AND name = 'filter_vars_customers_3';

######################################################################################
# 2018-05-14 - Add REST settings for ALVIS
#            - Edit the settings of the custom autocompleter for address for some of the masks to fix error of duplicating addresses
#            - Add setting for automation: reportRelatedDocuments

# Add REST settings for ALVIS
INSERT INTO settings (section, name, value) VALUES
  ('rest', 'filter_vars_layouts', 'id, name, description, visible')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);
INSERT INTO layouts_i18n (parent_id, description, lang) VALUES
  ('1999', 'alvis_columns_count := 5', 'bg'),
  ('2000', 'alvis_columns_count := 4', 'bg'),
  ('2002', 'alvis_columns_count := 4', 'bg'),
  ('2003', 'alvis_columns_count := 2', 'bg'),
  ('4497', 'alvis_columns_count := 3', 'bg'),
  ('2379', 'alvis_columns_count := 4', 'bg'),
  ('2380', 'alvis_columns_count := 4', 'bg'),
  ('2383', 'alvis_columns_count := 2', 'bg'),
  ('2384', 'alvis_columns_count := 3', 'bg'),
  ('2673', 'alvis_columns_count := 4', 'bg'),
  ('2674', 'alvis_columns_count := 4', 'bg'),
  ('2677', 'alvis_columns_count := 2', 'bg'),
  ('2678', 'alvis_columns_count := 2', 'bg')
  ON DUPLICATE KEY UPDATE
    `description` = VALUES(`description`);
UPDATE `_fields_meta`
  SET source = CONCAT(source, IF(source = '', '', '\r\n'), 'alvis_layout_full_row := 1')
  WHERE model = 'Document'
    AND model_type = 15
    AND name IN ('documents_description', 'other_info')
    AND source NOT LIKE '%alvis_layout_full_row%';
UPDATE `_fields_meta`
  SET source = CONCAT(source, IF(source = '', '', '\r\n'), 'alvis_layout_full_row := 1')
  WHERE model = 'Document'
    AND model_type = 18
    AND name IN ('documents_description')
    AND source NOT LIKE '%alvis_layout_full_row%';
UPDATE `_fields_meta`
  SET source = CONCAT(source, IF(source = '', '', '\r\n'), 'alvis_layout_full_row := 1')
  WHERE model = 'Document'
    AND model_type = 25
    AND name IN ('documents_description')
    AND source NOT LIKE '%alvis_layout_full_row%';
INSERT INTO settings (section, name, value) VALUES
  ('rest', 'filter_vars_documents_14', 'all'),
  ('rest', 'filter_vars_documents_15', 'all'),
  ('rest', 'filter_vars_documents_16', 'all'),
  ('rest', 'filter_vars_documents_17', 'all'),
  ('rest', 'filter_vars_documents_18', 'all'),
  ('rest', 'filter_vars_documents_19', 'all'),
  ('rest', 'filter_vars_documents_20', 'all'),
  ('rest', 'filter_vars_documents_21', 'all'),
  ('rest', 'filter_vars_documents_22', 'all'),
  ('rest', 'filter_vars_documents_23', 'all'),
  ('rest', 'filter_vars_documents_24', 'all'),
  ('rest', 'filter_vars_documents_25', 'all'),
  ('rest', 'filter_vars_documents_26', 'all'),
  ('rest', 'filter_vars_documents_27', 'all'),
  ('rest', 'filter_vars_documents_28', 'all'),
  ('rest', 'filter_vars_documents_29', 'all'),
  ('rest', 'filter_vars_documents_30', 'all'),
  ('rest', 'filter_vars_documents_31', 'all')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Edit the settings of the custom autocompleter for address for some of the masks to fix error of duplicating addresses
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, 'autocomplete_filter := <region_id> => $city_id_ten', 'autocomplete_filter := <region_id> => $city_ten_id') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%city_id_ten%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '$quarter_nine', '$quarter') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%$quarter_nine%' AND `name` LIKE '%address%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '$quarter_ten', '$quarter') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%$quarter_ten%' AND `name` LIKE '%address%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '$quarter_eleven', '$quarter') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%$quarter_eleven%' AND `name` LIKE '%address%';
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '$quarter_twelve', '$quarter') WHERE `source` LIKE '%bgMapsFindAddresses%' AND `model`='Document' AND `model_type`=2 AND `source` LIKE '%$quarter_twelve%' AND `name` LIKE '%address%';

# Add setting for automation: reportRelatedDocuments
UPDATE automations
  SET settings = CONCAT(settings, '\r\n# Маски, за които да се създават пазарни подходи\r\ndoc_type_market_approach_related_to_doc_types := 25,26,27,28,29,30,31')
  WHERE method LIKE '%reportRelatedDocuments'
    AND settings NOT LIKE '%doc_type_market_approach_related_to_doc_types%';

######################################################################################
# 2018-05-17 - Update settings for ALL reportRelatedDocuments automations

# Update settings for ALL reportRelatedDocuments automations
UPDATE automations
  SET settings = '# Документи от първо ниво\r\ndoc_types_first_level := 14,15,16\r\n# Маски, които са обект на оценка в пазарните норми (type 40)\r\ndoc_revenue_approach_market_norms_assessment_objects_types := 25,26,27,28,29,30,31\r\n# Маски, които са обекти на отчета в аналозите за наем (type 36)\r\ndoc_revenue_approach_analog_rent_object_report_types := 25,26,27,28,29,30,31\r\n# Маски, за които да се създават пазарни норми\r\ndoc_type_market_approach_related_to_doc_types := 25,26,27,28,29,30,31'
  WHERE method LIKE '%reportRelatedDocuments%';

######################################################################################
# 2018-06-15 - Add REST filters for documents of type 42

# Add REST filters for documents of type 42
INSERT INTO settings (section, name, value) VALUES
  ('rest', 'filter_vars_documents_42', 'all')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-06-18 - Changed settings for additional var street_name

# Changed settings for additional var street_name
UPDATE `_fields_meta`
  SET source = REPLACE(source, 'autocomplete_filter := <id> => $id', '#autocomplete_filter := <id> => $id')
  WHERE model = 'Document'
    AND model_type = 13
    AND name = 'street_name'
    AND source NOT LIKE '%#autocomplete_filter := <id> => $id%';

######################################################################################
# 2018-06-27 - Update settings for ALL reportRelatedDocuments automations
#            - Add automation reportRelatedDocumentsOnEditFirstLevel for documents of type 17 ("OPS")

# Update settings for ALL reportRelatedDocuments automations
UPDATE automations
  SET settings = REPLACE(settings, 'doc_types_first_level := 14,15,16', 'doc_types_first_level := 14,15,16,17\r\n# Документи от първо ниво, за които винаги да се създава документ „НЕ“\r\ndoc_types_first_level_always_revenue_approach_no := 17')
  WHERE method LIKE '%reportRelatedDocuments%'
    AND settings NOT LIKE '%doc_types_first_level_always_revenue_approach_no%';

# Add automation reportRelatedDocumentsOnEditFirstLevel for documents of type 17 ("OPS")
DELETE FROM automations WHERE method LIKE '%reportRelatedDocumentsOnEditFirstLevel' AND start_model_type = '17';
INSERT INTO automations
  SET name = 'Свързани документи при редакция на обект от първо ниво',
    module = 'documents',
    automation_type = 'action',
    start_model_type = '17',
    settings = '# Документи от първо ниво\r\ndoc_types_first_level := 14,15,16,17\r\n# Документи от първо ниво, за които винаги да се създава документ „НЕ“\r\ndoc_types_first_level_always_revenue_approach_no := 17\r\n# Маски, които са обект на оценка в пазарните норми (type 40)\r\ndoc_revenue_approach_market_norms_assessment_objects_types := 25,26,27,28,29,30,31,42\r\n# Маски, които са обекти на отчета в аналозите за наем (type 36)\r\ndoc_revenue_approach_analog_rent_object_report_types := 25,26,27,28,29,30,31,42\r\n# Маски, за които да се създават пазарни норми\r\ndoc_type_market_approach_related_to_doc_types := 25,26,27,28,29,30,31,42',
    conditions = 'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'',
    method = 'plugin := advance_address\r\nmethod := reportRelatedDocumentsOnEditFirstLevel',
    nums = 0;

######################################################################################
# 2018-06-29 - Added setting display_comments to the advance_timesheet_by_assessor report

# Added setting display_comments to the advance_timesheet_by_assessor report
UPDATE reports
  SET settings = REPLACE(settings, 'hide_second_table := 1', 'hide_second_table := 1\r\ndisplay_comments := 1')
  WHERE `type` = 'advance_timesheet_by_assessor'
    AND settings NOT LIKE '%display_comments%';

######################################################################################
# 2018-07-03 - Add REST filters for documents of type 35

# Add REST filters for documents of type 35
UPDATE settings
  SET value = CONCAT(value, ', one_calc_construction_right_grp, two_calc_construction_right_grp')
  WHERE section = 'rest'
    AND name = 'filter_vars_documents_35'
    AND value NOT LIKE '%one_calc_construction_right_grp%';

######################################################################################
# 2018-07-05 - Update settings for ALL reportRelatedDocuments automations

# Update settings for ALL reportRelatedDocuments automations
UPDATE automations
  SET settings = '# Документи от първо ниво\r\ndoc_types_first_level := 14,15,16,17\r\n# Документи от първо ниво, за които винаги да се създава документ „НЕ“\r\ndoc_types_first_level_always_revenue_approach_no := 17\r\n# Маски, които са обекти на отчета в аналозите за наем (type 36)\r\ndoc_revenue_approach_analog_rent_object_report_types := 25,26,27,28,29,30,31,42\r\n# Маски, за които да се създават пазарни норми\r\ndoc_type_market_approach_related_to_doc_types := 25,26,27,28,29,30,31,42'
  WHERE method LIKE '%reportRelatedDocuments%';

######################################################################################
# 2018-08-28 - Change settings for automation buildTree used for manually building a tree of documents
#            - Add new automation buildTree used for automatically building a tree of documents
#            - Move reportRelatedDocuments... automations after the automation for generating the hierarchy tree

# PRE-DEPLOYED # Change settings for automation buildTree used for manually building a tree of documents
#UPDATE automations
#  SET conditions = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'view\'\r\ncondition := $request->get(\'build_tree\')\r\ncondition := $request->get(\'elements\')',
#    name = 'Дърво от документи (ръчно)'
#  WHERE method LIKE '%buildTree%'
#    AND automation_type = 'before_action';

# PRE-DEPLOYED # Add new automation buildTree used for automatically building a tree of documents
#DELETE FROM automations WHERE method LIKE '%buildTree%' AND automation_type = 'action';
#INSERT INTO automations
#  SET name = 'Дърво от документи (автоматично)',
#    module = 'documents',
#    start_model_type = '13',
#    automation_type = 'action',
#    settings = '',
#    conditions = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edit\'))',
#    method = 'plugin := advance_address\r\nmethod := buildTree',
#    position = 1,
#    nums = 0;

# PRE-DEPLOYED # Move reportRelatedDocuments... automations after the automation for generating the hierarchy tree
#UPDATE automations
#  SET position = 2
#  WHERE method LIKE '%reportRelatedDocuments%'
#    AND automation_type = 'action';

######################################################################################
# 2018-09-12 - Update settings for ALL reportRelatedDocuments automations

# PRE-DEPLOYED # Update settings for ALL reportRelatedDocuments automations
#UPDATE automations
#  SET settings = REPLACE(settings, 'doc_types_first_level_always_revenue_approach_no', 'doc_types_first_level_always_revenue_approach_yes')
#  WHERE method LIKE '%reportRelatedDocuments%'
#    AND settings LIKE '%doc_types_first_level_always_revenue_approach_no%';

######################################################################################
# 2018-09-17 - Update REST filters for nomenclatures of type 47
#            - Change positions and visibility for fields used for columns in a combined table for SEK in ALVIS

# PRE-DEPLOYED # Update REST filters for nomenclatures of type 47
#INSERT INTO `settings` (`section`, `name`, `value`) VALUES
#  ('rest', 'filter_vars_nomenclatures_47', 'all')
#  ON DUPLICATE KEY UPDATE
#    `value` = VALUES(`value`);

# PRE-DEPLOYED # Change positions and visibility for fields used for columns in a combined table for SEK in ALVIS
#INSERT INTO `_fields_meta` (model, model_type, name, position, hidden)
#  (
#    SELECT model,
#        model_type,
#        name,
#        IF(position IN (10, 15), position + 15, IF(position IN (20, 25, 30), position - 10, position)) AS position,
#        IF(position = 20, 1, 0) AS hidden
#      FROM `_fields_meta`
#      WHERE model = 'Document'
#        AND model_type BETWEEN 18 AND 24
#        AND type != 'table'
#        AND position != 5
#        AND `table` IN (
#          SELECT `table`
#            FROM `_fields_meta`
#            WHERE model = 'Document'
#              AND model_type BETWEEN 18 AND 24
#              AND name IN (
#                'rough_table', 'earth_table', 'reinforced_concrete_table', 'layer_walls_table', 'roof_water_drainage_table',
#                'equalizing_screed_table', 'iron_stuff_table', 'rough_not_table', 'dograma_table', 'windows_table', 'doors_table',
#                'finish_table', 'walls_roof_table', 'thermal_table', 'paint_table', 'floor_table', 'iling_table', 'thermal_not_table',
#                'installations_table', 'water_table', 'piping_table', 'sanitary_table', 'electricity_table', 'heating_table',
#                'ventilation_table', 'other_table', 'lift_table', 'not_desc_table', 'external_table', 'total_smr_table',
#                'profit_table', 'expenses_profit_table'))
#  )
#  ON DUPLICATE KEY UPDATE
#    position = VALUES(position),
#    hidden = VALUES(hidden);

######################################################################################
# 2018-09-25 - Add the 'malvis' user-agent and fix filter vars for document 32

# Add settings to allow rest for malvis (mobile alvis)
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'allowed_rest_user_agents', 'form_assesment, malvis'),
  ('rest', 'filter_vars_documents_32', 'id,name,customer,images_grp,image_upload,image_main,image_upload_main, image_position,image_hierarchy_object, image_desc,group_vars,group,assoc_vars,vars')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2018-09-26 - Changed settings for additional vars to apply range validation for two fields
#            - Set help-text for the additional var object_name

# PRE-DEPLOYED # Changed settings for additional vars to apply  range validation for two fields
#UPDATE _fields_meta
#  SET validate = 'js_filter := insertOnlyFloats\r\nalvis_min_value := 5\r\nalvis_max_value := 25'
#WHERE name = 'value_construction_right';
#UPDATE _fields_meta
#  SET validate = 'js_filter := insertOnlyFloats\r\nalvis_min_value := 0.5\r\nalvis_max_value := 2.5'
#WHERE name = 'corr_construction_right';

# PRE-DEPLOYED # Set help-text for the additional var object_name
#INSERT INTO _fields_i18n
#  SELECT id, 'help', 'Напишете име, което да излиза в печата на доклада.', 'bg'
#    FROM `_fields_meta`
#    WHERE name = 'object_name'
#  ON DUPLICATE KEY UPDATE
#    content = VALUES(content);

######################################################################################
# 2018-10-09 - Add REST filters for nomenclatures of type 92

# Add REST filters for nomenclatures of type 92
INSERT INTO settings (section, name, value) VALUES
  ('rest', 'filter_vars_nomenclatures_92', 'all')
  ON DUPLICATE KEY UPDATE
  value = VALUES(value);

######################################################################################
# 2018-10-18 - Change rest filters for customers of type 3 (Bank)

# Change rest filters for customers of type 3 (Bank)
UPDATE settings
  SET value = CONCAT(value, ', residential_capital_rate, office_capital_rate, store_capital_rate, industry_capital_rate')
  WHERE section = 'rest'
    AND name = 'filter_vars_customers_3'
    AND value NOT LIKE '%residential_capital_rate%';

######################################################################################
# 2018-10-26 - Update REST filters for nomenclatures of type 11, 97, 98, 99
#            - Change visibility for fields used in ALVIS

# PRE-DEPLOYED # Update REST filters for nomenclatures of type 11, 97, 98, 99
#INSERT INTO `settings` (`section`, `name`, `value`) VALUES
#  ('rest', 'filter_vars_nomenclatures_11', 'all'),
#  ('rest', 'filter_vars_nomenclatures_97', 'all'),
#  ('rest', 'filter_vars_nomenclatures_98', 'all'),
#  ('rest', 'filter_vars_nomenclatures_99', 'all')
#  ON DUPLICATE KEY UPDATE
#    `value` = VALUES(`value`);
#
#UPDATE `settings` SET `value`='id, rated_object_group, object_improvement_grp, customer, customer_id, one_calc_construction_right_grp, two_calc_construction_right_grp, three_calc_construction_right_grp'
#  WHERE `section`='rest' AND `name`='filter_vars_documents_35';

######################################################################################
# 2018-10-29 - Add new automation syncReports for automatic syncing of Report and Report (1)

# PRE-DEPLOYED # Add new automation syncReports for automatic syncing of Report and Report (1)
#DELETE FROM automations WHERE method LIKE '%syncReports%';
#INSERT INTO automations
#  SET name = 'Синхронизация от Доклад (1) към Доклад',
#    module = 'documents',
#    start_model_type = 13,
#    automation_type = 'action',
#    settings = '',
#    conditions = 'condition := \'[request_is_post]\' && (in_array(\'[action]\', array(\'add\', \'edit\', \'view\')) && (\'[prev_b_date]\' != \'[b_date]\' || \'[prev_b_office]\' != \'[b_office]\' || \'[prev_a_delivery_term]\' != \'[a_delivery_term]\' || \'[prev_a_order_date]\' != \'[a_order_date]\' || \'[prev_a_receive_date]\' != \'[a_receive_date]\' || \'[prev_a_work_term]\' != \'[a_work_term]\' || \'[prev_a_owner_name]\' != \'[a_owner_name]\' || \'[prev_a_bank_name]\' != \'[a_bank_name]\' || \'[prev_a_bank_id]\' != \'[a_bank_id]\'))',
#    method = 'plugin := advance_address\r\nmethod := syncReports',
#    position = 3,
#    nums = 0;
#INSERT INTO automations
#  SET name = 'Синхронизация от Доклад към Доклад (1)',
#    module = 'documents',
#    start_model_type = 2,
#    automation_type = 'action',
#    settings = '',
#    conditions = 'condition := \'[request_is_post]\' && (in_array(\'[action]\', array(\'add\', \'edit\', \'view\')) && (\'[prev_b_date]\' != \'[b_date]\' || \'[prev_b_office]\' != \'[b_office]\' || \'[prev_b_deadline]\' != \'[b_deadline]\' || \'[prev_a_order_date]\' != \'[a_order_date]\' || \'[prev_a_send_doc_date]\' != \'[a_send_doc_date]\' || \'[prev_a_work_term]\' != \'[a_work_term]\' || \'[prev_a_owner_name]\' != \'[a_owner_name]\' || \'[prev_a_bank_name]\' != \'[a_bank_name]\' || \'[prev_a_bank_id]\' != \'[a_bank_id]\'))',
#    method = 'plugin := advance_address\r\nmethod := syncReports',
#    position = 21,
#    nums = 0;

######################################################################################
# 2018-10-29 - Update REST filters for nomenclatures of type 7
#            - Change visibility for fields used in ALVIS

# PRE-DEPLOYED # Update REST filters for nomenclatures of type 7
#INSERT INTO `settings` (`section`, `name`, `value`) VALUES
#  ('rest', 'filter_vars_nomenclatures_7', 'all')
#  ON DUPLICATE KEY UPDATE
#    `value` = VALUES(`value`);

######################################################################################
# 2018-11-05 - Add new automation updateReportBB for updating the BB of Report

# PRE-DEPLOYED # Add new automation updateReportBB for updating the BB of Report
#DELETE FROM automations WHERE method LIKE '%updateReportBB%';
#INSERT INTO automations
#  SET name = 'Обновяване на BB в Доклад',
#    module = 'documents',
#    start_model_type = 2,
#    automation_type = 'action',
#    settings = '',
#    conditions = 'condition := in_array(\'[action]\', array(\'setstatus\', \'multistatus\')) && in_array(\'[b_substatus]\', array(4, 5, 15, 16))',
#    method = 'plugin := advance_address\r\nmethod := updateReportBB',
#    position = 22,
#    nums = 0;

# Change conditions for automation checkCompletedReportsMasks to execute only when not REST
#UPDATE automations
#  SET conditions = 'condition := !Auth::$is_rest'
#  WHERE method LIKE '%checkCompletedReportsMasks%';

######################################################################################
# 2018-11-08 - Update settings for ALL reportRelatedDocuments automations

# Update settings for ALL reportRelatedDocuments automations
UPDATE automations
  SET settings = '# Документи от първо ниво\r\ndoc_types_first_level := 14,15,16,17\r\n# Документи от първо ниво, за които винаги да се създава документ „ДА“\r\ndoc_types_first_level_always_revenue_approach_yes := 17\r\n# Маски, които са обекти на отчета в аналозите за наем (type 36)\r\ndoc_revenue_approach_analog_rent_object_report_types := 18,19,20,21,22,23,24,25,26,27,28,29,30,31,42\r\n# Маски, за които да се създават пазарни норми\r\ndoc_type_market_approach_related_to_doc_types := 14,15,16,18,19,20,21,22,23,24,25,26,27,28,29,30,31,42'
  WHERE method LIKE '%reportRelatedDocuments%';

######################################################################################
# 2018-11-23 - Set analog_type tobe non-required

# Set analog_type tobe non-required
UPDATE `_fields_meta`
  SET required = 0
  WHERE model = 'Nomenclature'
    AND model_type IN (84, 91)
    AND name = 'analog_type';

######################################################################################
# 2018-11-29 - Add automations for writing supervision data

# PRE-DEPLOYED # Add automations for writing supervision data
#DELETE FROM automations WHERE method LIKE '%writeSupervisionData%';
#INSERT INTO automations
#  SET name = 'Записване на данни за супервизия',
#    module = 'documents',
#    start_model_type = 13,
#    automation_type = 'action',
#    settings = '',
#    conditions = 'condition := \'[request_is_post]\' && ($request->isRequested(\'supervision_comment_a1_supervision_comments_grp\') && $request->get(\'supervision_comment_a1_supervision_comments_grp\') != \'\' || $request->isRequested(\'supervision_comment_a2_supervision_comments_grp\') && $request->get(\'supervision_comment_a2_supervision_comments_grp\') != \'\' || $request->isRequested(\'supervision_comment_a3_supervision_comments_grp\') && $request->get(\'supervision_comment_a3_supervision_comments_grp\') != \'\')',
#    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
#    position = 4,
#    nums = 0;
#INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, position, nums)
#  SELECT 'Записване на данни за супервизия',
#      'documents',
#      dt.id,
#      'action',
#      '',
#      'condition := \'[request_is_post]\' && $request->isRequested(\'supervision_comment_supervision_comments_grp\') && $request->get(\'supervision_comment_supervision_comments_grp\') != \'\'',
#      'plugin := advance_address\r\nmethod := writeSupervisionData',
#      4,
#      0
#    FROM automations AS a
#    JOIN documents_types AS dt
#      ON (a.method LIKE '%writeSupervisionData%'
#        AND a.module = 'documents'
#        AND a.start_model_type = 13
#        AND (dt.id BETWEEN 14 AND 31
#          OR dt.id IN (42, 32, 33, 34, 37, 36, 35, 41, 38, 39)));

######################################################################################
# 2018-12-03 - Update rest filters

# Update rest filters
UPDATE settings
  SET value = CONCAT(value, ', supervision_comments_grp')
  WHERE section = 'rest'
    AND name REGEXP 'filter_vars_documents_(37|34|35|38)'
    AND value NOT LIKE '%supervision_comments_grp%';

######################################################################################
# 2018-12-14 - Update settings of automations for writing supervision data
#            - Update settings of automations for writing supervision data: set audit vars for each document type

# Update settings of automations for writing supervision data
DELETE FROM automations WHERE method LIKE '%writeSupervisionData%';
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 13,
    automation_type = 'action',
    settings = 'a1_supervision_history_edit_grp := \r\na2_supervision_history_edit_grp := \r\na3_supervision_history_edit_grp := ',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations (name, module, start_model_type, automation_type, settings, conditions, method, position, nums)
  SELECT name,
      module,
      dt.id,
      automation_type,
      'supervision_history_edit_grp := ',
      conditions,
      method,
      position,
      nums
    FROM automations AS a
    JOIN documents_types AS dt
      ON (a.method LIKE '%writeSupervisionData%'
        AND a.module = 'documents'
        AND a.start_model_type = 13
        AND (dt.id BETWEEN 14 AND 39
          OR dt.id IN (41, 42)));

# Update settings of automations for writing supervision data: set audit vars for each document type
DELETE FROM automations WHERE method LIKE '%writeSupervisionData%';
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 13,
    automation_type = 'action',
    settings = 'a1_supervision_history_edit_grp := date, office, order_date, work_term, valid_term, receive_date, delivery_term, owner_name, loan_applicant_name, bank_branch_name, currency_methods, ref_currencies, differ_from_standart, differ_description, standard_value_check_one, standard_value_check_two, standard_value_check_three, currency_methods_fixing, assessment_purpose_desc, property_right_name, property_right_description, object_name_kind, object_name_type, object_name_subtype, object_name_items, market_approach, revenue_approach, costly_approach, building_sos, to_date_use_desc, object_desc_long, standart_nom_name\r\na2_supervision_history_edit_grp := populated_place, municipality_name, region_name, postcode, street_name, street_number, place_lat, place_lon, map_one, map_two, analog_quarter_name, place_location, place_note, place_image, address_more_desc, region_description, full_description, transport_check, construction_check, public_service_check, infrastructure_check, eco_check, soc_check, additional_desc_name, additional_desc_full, market_desc_info, graphics\r\na3_supervision_history_edit_grp := doc_type, doc_number, doc_attached_file, doc_comments',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 14,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := property_num, property_subtype, identity_type, buildings_status, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, viza_date, permition_building, future_building_type, accepted_area_group, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 15,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := property_num, identity_type, buildings_status, area_reg_plan, use_restrict, zut_purpose, permanent_use_zut, access_type, side_to_road, used_as, side_to_road_meters, build_form, relievo_type, displacement_type, percent_density, kint_type, kota_roof, percent_planting, development_area, data_according_to, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, accepted_area_group, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, land_distribution, land_distribution_principle, improvement_principle, land_distribution_type, land_distribution_add_no',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 16,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := property_num, identity_type, area_reg_plan, doc_area_ownership, sketch_area, cadastral_area, percent_perfect_part, sqm_perfect_part, accepted_area, area_info, accepted_area_group, electricity_info, plumbing_info, sewerage_info, gasification_info, communication_distance, vertical_plan, fence_info, planting_info, other_info, picture_desc, specific_description, land_distribution, land_distribution_principle, improvement_principle, land_distribution_type, land_distribution_add_no',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 17,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := property_num, identity_type, other_info, other_info_description, value_construction_right, calc_construction_right, corr_construction_right',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 18,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_sec, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, remain_econ_life, remain_econ_life_left, electric_installation, piping_installation, sewage_installation, heating_installation, ventilation_air_conditioning, gasification, security_installation, elevator, fire_installation, bms_installation, structured_cabling, photovoltaic_installation, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, picture_desc, spec_desc, standard_SEK, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 19,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_sec, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, office_rating, remain_econ_life, remain_econ_life_left, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, standard_SEK, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 20,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_sec, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, surrounding_area_state, places_number, remain_econ_life, remain_econ_life_left, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, standard_SEK, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 21,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := remain_econ_life, remain_econ_life_left, choose_mask_sec, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, cultural_monument, object_position, object_outbuildings, surrounding_area_state, electric_installation, piping_installation, sewage_installation, heating_installation, ventilation_air_conditioning, gasification, security_installation, elevator, fire_installation, bms_installation, structured_cabling, photovoltaic_installation, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, picture_desc, spec_desc, standard_SEK, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 22,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_sec, object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, finishing_works, object_facade, inner_walls_barriers, overall_impression, object_maintenance, number_doorway, access_control, object_position, object_outbuildings, specialized_access, object_height, premises_type, surrounding_area_state, remain_econ_life, remain_econ_life_left, electric_installation, piping_installation, sewage_installation, heating_installation, ventilation_air_conditioning, gasification, security_installation, elevator, fire_installation, bms_installation, structured_cabling, lifting_facilities, photovoltaic_installation, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, picture_desc, spec_desc, standard_SEK, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 23,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := remain_econ_life, remain_econ_life_left, object_purpose, object_name, object_identifier, construction_type, above_ground_level, underground_level, construction_year, object_functionality, outer_walls, roof_construction, inner_walls_barriers, overall_impression, separate_entrance, parking_spots, warehouse_availability, outer_door, surrounding_area_state, last_repair_year, electric_installation, piping_installation, sewage_installation, security_installation, fire_installation, rzp_accepted_area, total_land_percent, total_land_meters, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, picture_desc, spec_desc, standard_SEK, assessment_category, additional_costs_percent, level_of_completion, object_tunnel, amortization_type, total_suitability, residual_suitability, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 24,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := object_purpose, object_name, object_identifier, construction_type, floor_count, above_ground_level, underground_level, construction_year, construction_phase, date_uve_rp, last_repair_year, construction_quality, object_functionality, outer_walls, roof_construction, overall_impression, remain_econ_life, remain_econ_life_left, electric_installation, piping_installation, sewage_installation, level_group, level_name_group, zp_na_group, zp_scheme_group, zp_arch_group, zp_other_group, accepted_area_group, zp_accepted_area, land_percent, land_meters, area_comment, total_zp_na_group, total_zp_scheme_group, total_zp_arch_group, total_zp_other_group, total_zp_accepted_area, total_rzp_na_group, total_rzp_scheme_group, total_rzp_arch_group, total_rzp_other_group, total_rzp_accepted_area, rzp_accepted_area, aggregated_level_group, aggregated_level_name_group, aggregated_zp_accepted_area, aggregated_market_approach, aggregated_market_approach_value, aggregated_income_approach, aggregated_income_approach_value, aggregated_spending_approach, aggregated_spending_approach_value, aggregated_area_comment, total_aggregated_zp_accepted_area, total_aggregated_rzp_accepted_area, total_aggregated_market_approach_value, total_aggregated_income_approach_value, total_aggregated_spending_approach_value, desc_level_name, desc_functional_distribution, desc_finishing_works, desc_level_condition, picture_desc, spec_desc, standard_SEK, assessment_category, additional_costs_percent, level_of_completion, amortization_type, total_suitability, residual_suitability, sek_expen_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 25,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_third, remain_econ_life_left, object_name, object_id, object_floor, object_levels, object_prospect, last_repair_year, object_functionality, finish_works, support_condition, object_impression, object_isolation, basic_room_number, service_room_number, other_rooms, balcony_number, outer_door, bevels_presence, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, gasification_info, security_info, fire_instalation, cable_structure, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, total_area_market, total_area_income, total_area_expense, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 26,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, object_isolation, basic_room_number, service_room_number, other_rooms, balcony_number, outer_door, bevels_presence, separate_entrance, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, gasification_info, security_info, fire_instalation, cable_structure, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 27,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_third, remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, basic_room_number, service_room_number, other_rooms, outer_door, human_flow, transport_access, parking_access, shop_window, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, gasification_info, security_info, fire_instalation, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 28,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_third, remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, basic_room_number, service_room_number, other_rooms, human_flow, transport_access, parking_access, office_env, office_class, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, security_info, fire_instalation, cable_structure, access_control, double_floor, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 29,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_third, other_rooms, remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, basic_room_number, service_room_number, consume_room_number, separate_kitchen, garden_terrace, sitting_places, categorization_type, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, security_info, fire_instalation, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 30,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, basic_room_number, service_room_number, other_rooms, outer_door, separate_entrance, transport_access, parking_access, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, security_info, fire_instalation, access_control, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 31,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := choose_mask_third, remain_econ_life_left, object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, basic_room_number, service_room_number, other_rooms, outer_door, separate_entrance, transport_access, parking_access, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, security_info, fire_instalation, access_control, room_type, floor_type, wall_type, roof_type, joinery_type, door_type, rzp_accepted_area, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, total_area_market, total_area_income, total_area_expense',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 32,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := image_upload, image_position, image_main, image_desc, image_upload_main, image_hierarchy_object',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 33,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := date_analogue, price_analog_eur, price_analog_eur_sqm, pi_area, land_value_eur, analogue_price_construction, correction_analogue_price_eur_sqm, correction_absolute_price, correction_percentage, correction_absolute_value, correction_analogue_weight, value_meth_choice, average_weighted_value, average_weighted_value_bgn, market_method_build_value_eur, market_method_build_value_bgn, value_improvements_eur, value_improvements_bgn, market_method_value_eur, market_method_value_bgn, object_comment',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 34,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := first_setting, second_setting, fourth_setting, third_setting, rent_EURsqm, rental_correct_coef, rent_EUR_month, rent_EUR_year, risk_out_of_rent, expenses_exploitation, expenses_to_owner, net_annual_rev_eur, capital_rate, value_bgn, value_eur, net_annual_rev, land_cost_eur, net_annual_income, capital_rate_average, residual_economic_life, object_multiplier, repairs_need, market_ratio, object_land_cost_eur, property_improve_eur, proposal_market_value_eur, proposal_market_value_bgn, comment_object, interest_rate_land',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 35,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := correction_adapted_standard, value_as_new_eur, assessment_date_age, residual_life, depreciation_physical_percentage, depreciation_functional_percentage, depreciation_economic_percentage, depreciation_total_percentage, depreciation_total_value, object_value_eur, repair_finishing_smr_eur, market_adaptation_ratio, build_land_value_eur, object_improvement_eur, market_value_cost_approach_eur, object_improvement_value_as_new_eur, object_improvement_general_operational_availability, object_improvement_assessment_date_age, object_improvement_residual_life, object_improvement_depreciation_physical_percentage, object_improvement_depreciation_functional_percentage, object_improvement_depreciation_economic_percentage, object_improvement_depreciation_total_percentage, object_improvement_depreciation_total_value, object_improvement_object_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 36,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := date_analogue, price_analog_eur, price_analog_eur_sqm, one_correction_percentage, correction_analogue_price_eur_sqm, correction_absolute_price, correction_percentage, rated_object_area, average_arithmetic_value, average_arithmetic_value_bgn, object_comment',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 37,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := first_setting, second_setting, fourth_setting, third_setting, rent_EURsqm, rental_correct_coef, rent_EUR_month, other_earnings, rent_EUR_year, risk_out_of_rent, expenses_exploitation, expenses_to_owner, net_annual_rev_eur, capital_rate, land_value_eur, land_interest_rate, residual_economic_life, object_multiplier, repairs_need, market_ratio, improvements_eur, value_bgn, value_eur, report_name, comment_object',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 38,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := general_assumptions_limits, gen_assumptions_name, special_assumptions, spec_asumptions_name, special_assumptions_swot, spec_asumptions_name_swot, legal_compliance, technical_compliance, financial_justification, conclusion_def, established_differences',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 39,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := market_approach_value, rev_approach_value, costly_approach_value, realization_term, discontinuous_factor, specific_terms, total_percent_discount, ps_percent_discount, forced_sale_notes, long_term_trend, mortgage_value_bgn, mortgage_value_eur, mortgage_value_sqm_bgn, mortgage_value_sqm_eur, net_real_value, net_real_value_bgn, net_real_value_eur, net_forced_sale_value_bgn, net_forced_sale_value_eur, net_forced_sale_sqm_value_bgn, net_forced_sale_sqm_value_eur',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 41,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := date_analogue, price_analog_eur, price_analog_eur_sqm, correction_analogue_price_eur_sqm, correction_absolute_price, correction_percentage, land_value, rated_object_area, average_arithmetic_value, average_arithmetic_value_bgn, object_comment',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;
INSERT INTO automations
  SET name = 'Записване на данни за супервизия',
    module = 'documents',
    start_model_type = 42,
    automation_type = 'action',
    settings = 'supervision_history_edit_grp := object_name, object_id, object_floor, object_levels, last_repair_year, object_functionality, finish_works, support_condition, object_impression, outer_door, remain_econ_life_left, remain_econ_life, electricity_info, plumbing_info, heating_info, ventilation_conditioning, gasification_info, level_group, level_name_group, zp_na_group, ochs_na_group, zp_scheme_group, ochs_scheme_group, zp_arch_group, ochs_arch_group, zp_other_group, ochs_other_group, accepted_area_group, zp_accepted_area, ochs_accepted_area, land_percent, land_meters, area_comment, rzp_accepted_area, total_area_market, total_area_income, total_area_expense, level_appr, level_name_appr, zp_acc_area, ochs_acc_area, object_market, perfect_market, area_market, object_income, perfect_income, area_income, object_expense, perfect_expense, area_expense, area_appr_comment, picture_desc, specific_description',
    conditions = 'condition := \'[request_is_post]\'',
    method = 'plugin := advance_address\r\nmethod := writeSupervisionData',
    position = 4,
    nums = 0;

######################################################################################
# 2019-02-21 - Set ALVIS settings for dome additional vars

# Set ALVIS settings for dome additional vars
UPDATE `_fields_meta`
  SET source = CONCAT(source, IF(source = '', '', '\r\n'), 'alvis_rows := 5')
  WHERE model = 'Document'
    AND model_type = 13
    AND name IN ('object_desc_long', 'to_date_use_desc')
    AND source NOT LIKE '%alvis_rows%';

######################################################################################
# 2019-07-10 - Added new setting for updateAddressDistrict automation so it can update a simple table

# Added new setting for updateAddressDistrict automation so it can update a simple table
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\naddress_nomenclature_id :=', '\r\nbb := 1\r\n\r\naddress_nomenclature_id :=') WHERE `method` LIKE '%updateAddressDistrict%' AND `settings` NOT LIKE '%\r\nbb :=%';

######################################################################################
# 2019-07-17 - Add keyname to REST accessible vars for module layouts

# Add keyname to REST accessible vars for module layouts
UPDATE settings
  SET value = 'id, name, keyname, description, visible'
  WHERE section = 'rest'
    AND name = 'filter_vars_layouts'
    AND value NOT LIKE '%keyname%';

######################################################################################
# 2019-08-09 - Update rest filters for documents of type 35

# PRE-DEPLOYED # Update rest filters for documents of type 35
#UPDATE settings
#  SET value = CONCAT(value, ', object_comment')
#  WHERE section = 'rest'
#    AND name = 'filter_vars_documents_35'
#    AND value NOT LIKE '%, object_comment';

######################################################################################
# 2019-09-26 - Add system_made_by to REST accessible vars for documents of type 2

# Add system_made_by to REST accessible vars for documents of type 2
UPDATE settings
  SET value = CONCAT(value, ', system_made_by')
  WHERE section = 'rest'
    AND name = 'filter_vars_documents_2'
    AND value NOT LIKE '%system_made_by%';

######################################################################################
# 2019-12-10 - Remove type 16 from the automations

# Remove type 16 from the automations
UPDATE automations
  SET settings = REPLACE(settings, 'doc_types_first_level := 14,15,16,17', 'doc_types_first_level := 14,15,17');
UPDATE automations
  SET settings = REPLACE(settings, 'doc_type_market_approach_related_to_doc_types := 14,15,16,18', 'doc_type_market_approach_related_to_doc_types := 14,15,18');

########################################################################
# 2020-01-29 - Changed the filename of the generated file

# Changed the filename of the generated file:
# a_bank_code - the bank abbreviation, prepared by the plugin
# a_created_from_document_name - a variable from document containing the original report document number
# revision - latest version of printed file, prepared by the plugin without leading zeros
 UPDATE patterns
 SET prefix='[a_bank_code]_[a_created_from_document_name]_[customer_name]_report_[revision]'
 WHERE model='Document' AND
         model_type=13 AND
         plugin=83;

######################################################################################
# 2020-02-10 - Update REST filters for nomenclatures of type "SEK version"

# Update REST filters for nomenclatures of type "Sek version"
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
 ('rest', 'filter_vars_nomenclatures_101', 'id, name, sek_version_date')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-02-13 - Add new report: rest_history_alvis

# Add new report: rest_history_alvis
INSERT INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  (408, 'rest_history_alvis', '', 0)
  ON DUPLICATE KEY UPDATE
    settings = VALUES(settings);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  (408, 'REST история ALVIS', 'bg'),
  (408, 'REST history ALVIS', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', 408, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = 408;

######################################################################################
# 2020-02-14 - Add new report: rest_alvis_reports_list

# Add new report: rest_alvis_reports_list
INSERT INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  (409, 'rest_alvis_reports_list', '', 0)
  ON DUPLICATE KEY UPDATE
    settings = VALUES(settings);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  (409, 'ALVIS: списък доклади', 'bg'),
  (409, 'ALVIS: reports list', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', 409, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     = 'generate_report'
      AND `model_type` = 409;

######################################################################################
# 2020-02-27 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_customers_2', 'id, name, is_company, lastname, phone, gsm')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-03-05 - Add settings and automations for ALVIS

# Add settings and automations for ALVIS
UPDATE settings
  SET value = CONCAT(value, ', correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp')
  WHERE section = 'rest'
    AND name = 'filter_vars_documents_2'
    AND value NOT LIKE '%, correction_time,%';
UPDATE settings
  SET value = CONCAT(value, ', real_role')
  WHERE section = 'rest'
    AND name = 'filter_vars_users'
    AND value NOT LIKE '%, real_role';
INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, position, nums)
  SELECT 'Записване на данни за супервизия', 'documents', 'action', '2', '', 'condition := \'[request_is_post]\'', 'plugin := advance_address\r\nmethod := writeSupervisionData', 4, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%writeSupervisionData%');
INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, position, nums)
  SELECT 'Приключване на „Доклад“', 'documents', 'action', '2', '', 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'\r\ncondition := $request->get(\'finishReport\') == \'1\'', 'method := status\r\nnew_status := locked\r\nnew_substatus := 5', 5, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%method := status%' AND name = 'Приключване на „Доклад“');

######################################################################################
# 2020-03-10 - Add new report: rest_alvis_supervision

# Add new report: rest_alvis_supervision
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  (410, 'rest_alvis_supervision', '', 0);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  (410, 'ALVIS: Супервизия', 'bg'),
  (410, 'ALVIS: Supervision', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', 410, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT `r`.`id`, `rd`.`id`, 'all'
  FROM `roles_definitions` AS `rd`
  JOIN `roles` AS `r`
    ON (`rd`.`module`     = 'reports'
      AND `rd`.`action`     = 'generate_report'
      AND `rd`.`model_type` = 410
      AND `r`.`id` IN (1, 14));

######################################################################################
# 2020-03-13 - Add new automations for ALVIS for updating assessment object substatus

# Add new automations for ALVIS for updating assessment object substatus
INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, position, nums)
  SELECT 'Обновяване на подтипа на обект', 'documents', 'action', '0', 'assessment_objects_types := 14, 15, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 42', 'condition := Auth::$is_rest\r\ncondition := ''[request_is_post]''\r\ncondition := ''[action]'' == ''edit''\r\ncondition := ''[prev_a_property_subtype]'' !== ''[a_property_subtype]'' || ''[prev_a_choose_mask_sec]'' !== ''[a_choose_mask_sec]'' || ''[prev_a_choose_mask_third]'' !== ''[a_choose_mask_third]''', 'plugin := advance_address\r\nmethod := reverseUpdateTree', 30, 0
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 0 AND automation_type = 'action' AND method LIKE '%reverseUpdateTree%');

######################################################################################
# 2020-03-18 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_nomenclatures_107', 'long_term_growth')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-03-26 - Update REST filters for nomenclature of type 102

INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_nomenclatures_102', 'all')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-03-27 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_nomenclatures_108', 'id, name, type, town_table, location_table, market_activity_table, exploitation_years_table')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-03-31 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_nomenclatures_109', 'id, name, type, completion_phase_grp, object_scale_grp, populated_place_grp, price_debt_grp, price_smr_grp, remaining_time_grp')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-04-01 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_nomenclatures_23', 'id, name, doc_type_object, object_name_kind_id'),
	('rest', 'filter_vars_nomenclatures_33', 'all')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-04-10 - Add automation for copying GT2 from "Report" into GT of "Report (1)" and another automation for validation (is there any assessment object created in "Report (1)"), which is deactivated

# Add automation for copying GT2 from "Report" into GT of "Report (1)" and another automation for validation (is there any assessment object created in "Report (1)"), which is deactivated
DELETE FROM automations WHERE method LIKE '%copyDataFromReportToReport1%' OR method LIKE '%validateReportEdit%';
INSERT INTO automations (name, module, automation_type, start_model_type, settings, conditions, method, after_action, position, nums, active) VALUES
  ('Копиране на данни от „Доклад“ в „Доклад (1)“', 'documents', 'action', '2', '', 'condition := \'[request_is_post]\'\r\ncondition := in_array(\'[action]\', array(\'add\', \'edit\'))', 'plugin := advance_address\r\nmethod := copyDataFromReportToReport1', null, 30, 0, 1),
  ('Валидация при редакция на „Доклад“', 'documents', 'before_action', '2', '', 'condition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'edit\'', 'plugin := advance_address\r\nmethod := validateReportEdit', 'cancel_action_on_fail := 1', 2, 0, 0);

######################################################################################
# 2020-04-14 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place, correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp, attachments, genfiles, draft_report, last_report, email_address')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-04-16 - Add attached_group to REST accessible vars for documents of type 2

# Add attached_group to REST accessible vars for documents of type 2
UPDATE settings
  SET value = CONCAT(value, ', attached_group')
  WHERE section = 'rest'
    AND name = 'filter_vars_documents_2'
    AND value NOT LIKE '%attached_group%';

######################################################################################
# 2020-05-04 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_documents_35', 'id, rated_object_group, object_improvement_grp, customer, customer_id, one_calc_construction_right_grp, two_calc_construction_right_grp, three_calc_construction_right_grp, supervision_comments_grp, object_comment, total_build_land_value_eur, total_market_value_cost_approach_eur, total_market_value_cost_approach_bgn')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-05-22 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
	('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place, correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp, attachments, genfiles, draft_report, last_report, email_address, attached_group, recording_role_group')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-06-19 - Remove old automations and add the new automattions that will handle the worked time

# Remove old automations and add the new automattions that will handle the worked time
DELETE FROM automations WHERE method LIKE '%brainstorm%';
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Вписване на дата в \'Дата на доклад\'', 0, NULL, 1, 'documents', NULL, 'action', '2', 'working_day_start := 09:00\r\nworking_day_end := 17:00\r\nupdate_var := deadline\r\nworking_time := work_time\r\nwork_start_date := last_resume_date\r\nforseen_working_time := work_term\r\nuse_nomenclature_forseen := 20\r\nnomenclature_work_time_var := work_term', 'condition := \'[prev_b_date]\' == \'\'\r\ncondition := \'[b_date]\' != \'\'', 'plugin := brainstorm\r\nmethod := recalculateTicketDeadline', NULL, 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%method := recalculateTicketDeadline%' AND `method` LIKE '%brainstorm%' AND `name` LIKE '%Вписване на дата%');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Докладът влиза в статус \'Пауза\'', 0, NULL, 1, 'documents', NULL, 'action', '2', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ncalc_based_on_var := a_last_resume_date\r\nupdate_var := work_time\r\nclear_var :=\r\nchange_date :=\r\n\r\nuse_updated_model := 1', 'condition := \'[prev_b_substatus]\' != \'14\'\r\ncondition := \'[b_substatus]\' == \'14\'', 'plugin := brainstorm\r\nmethod := calculateTicketAcceptTime', NULL, 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%method := calculateTicketAcceptTime%' AND `method` LIKE '%brainstorm%' AND `name` LIKE '%Докладът влиза в статус%');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Докладът излиза от статус \'Пауза\'', 0, NULL, 1, 'documents', NULL, 'action', '2', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\nupdate_var :=\r\nworking_time :=\r\nwork_start_date := last_resume_date\r\nforseen_working_time :=', 'condition := \'[prev_b_substatus]\' == \'14\'\r\ncondition := \'[b_substatus]\' != \'14\'', 'plugin := brainstorm\r\nmethod := recalculateTicketDeadline', NULL, 1, 0, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%method := recalculateTicketDeadline%' AND `method` LIKE '%brainstorm%' AND `name` LIKE '%Докладът излиза от статус%');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Докладът се приключва (влиза в статус \'Затворен\' или статус \'Заверен\')', 0, NULL, 1, 'documents', NULL, 'action', '2', 'working_day_start := 09:00\r\nworking_day_end := 18:00\r\ncalc_based_on_var := a_last_resume_date\r\nupdate_var := work_time\r\nclear_var :=\r\nchange_date :=\r\n', 'condition := (\'[prev_b_substatus]\' != \'6\' && \'[b_substatus]\' == \'6\') || (\'[prev_b_status]\' != \'closed\' && \'[b_substatus]\' == \'closed\')', 'plugin := brainstorm\r\nmethod := calculateTicketAcceptTime', NULL, 2, 1, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND automation_type = 'action' AND method LIKE '%method := calculateTicketAcceptTime%' AND `method` LIKE '%brainstorm%' AND `name` LIKE '%Докладът се приключва%');

######################################################################################
# 2020-07-06 - Change settings and conditions for automation: syncReports

# Change settings and conditions for automation: syncReports
UPDATE automations
  SET settings = '# Имена на променливи, които да се синхронизират, изброени със запетая, разделени по двойки чрез права черта, като вляво са променливите на обекта в който АД-то ще попълни стойности, а вдясно са променливите, от които ще вземе стойности (т.е. променливите на текущия запис, за който се изпълнява АД-то, в случая: даокументи от тип 2). Трябва всички синхронизиращи променливи (от които се взима стойност) да се описват и в conditions, за да се следят за промяна.\r\nsync_vars := custom_num|custom_num,date|date,order_date|order_date,receive_date|send_doc_date,award_date|award_date,work_term|work_term,delivery_term|deadline,bank_name|bank_name,bank_id|bank_id,bank_branch_name|bank_branch_name,customer|customer,owner_name|owner_name,loan_applicant_name|loan_applicant_name,loan_applicant_id|loan_applicant_id,office|office,rating_type|rating_type,assessment_purpose|assessment_purpose',
    conditions = "condition := '[request_is_post]'\r\ncondition := in_array('[action]', array('add', 'edit', 'view'))\r\ncondition := ('[prev_b_custom_num]' != '[b_custom_num]' || '[prev_b_date]' != '[b_date]' || '[prev_a_order_date]' != '[a_order_date]' || '[prev_a_send_doc_date]' != '[a_send_doc_date]' || '[prev_a_award_date]' != '[a_award_date]' || '[prev_a_work_term]' != '[a_work_term]' || '[prev_b_deadline]' != '[b_deadline]' || '[prev_a_bank_name]' != '[a_bank_name]' || '[prev_a_bank_id]' != '[a_bank_id]' || '[prev_a_bank_branch_name]' != '[a_bank_branch_name]' || '[prev_b_customer]' != '[b_customer]' || '[prev_a_owner_name]' != '[a_owner_name]' || '[prev_a_loan_applicant_name]' != '[a_loan_applicant_name]' || '[prev_a_loan_applicant_id]' != '[a_loan_applicant_id]' || '[prev_b_office]' != '[b_office]' || '[prev_a_rating_type]' != '[a_rating_type]' || '[prev_a_assessment_purpose]' != '[a_assessment_purpose]')"
  WHERE method LIKE '%syncReports%'
    AND start_model_type = 2
    AND settings NOT LIKE '%sync_vars%';
UPDATE automations
  SET settings = '# Имена на променливи, които да се синхронизират, изброени със запетая, разделени по двойки чрез права черта, като вляво са променливите на обекта в който АД-то ще попълни стойности, а вдясно са променливите, от които ще вземе стойности (т.е. променливите на текущия запис, за който се изпълнява АД-то, в случая: даокументи от тип 13). Трябва всички синхронизиращи променливи (от които се взима стойност) да се описват и в conditions, за да се следят за промяна.\r\nsync_vars := custom_num|custom_num,date|date,order_date|order_date,send_doc_date|receive_date,award_date|award_date,work_term|work_term,deadline|delivery_term,bank_name|bank_name,bank_id|bank_id,bank_branch_name|bank_branch_name,customer|customer,owner_name|owner_name,loan_applicant_name|loan_applicant_name,loan_applicant_id|loan_applicant_id,office|office,rating_type|rating_type,assessment_purpose|assessment_purpose',
    conditions = "condition := '[request_is_post]'\r\ncondition := in_array('[action]', array('add', 'edit', 'view'))\r\ncondition := ('[prev_b_custom_num]' != '[b_custom_num]' || '[prev_b_date]' != '[b_date]' || '[prev_a_order_date]' != '[a_order_date]' || '[prev_a_receive_date]' != '[a_receive_date]' || '[prev_a_award_date]' != '[a_award_date]' || '[prev_a_work_term]' != '[a_work_term]' || '[prev_a_delivery_term]' != '[a_delivery_term]' || '[prev_a_bank_name]' != '[a_bank_name]' || '[prev_a_bank_id]' != '[a_bank_id]' || '[prev_a_bank_branch_name]' != '[a_bank_branch_name]' || '[prev_b_customer]' != '[b_customer]' || '[prev_a_owner_name]' != '[a_owner_name]' || '[prev_a_loan_applicant_name]' != '[a_loan_applicant_name]' || '[prev_a_loan_applicant_id]' != '[a_loan_applicant_id]' || '[prev_b_office]' != '[b_office]' || '[prev_a_rating_type]' != '[a_rating_type]' || '[prev_a_assessment_purpose]' != '[a_assessment_purpose]')"
  WHERE method LIKE '%syncReports%'
    AND start_model_type = 13
    AND settings NOT LIKE '%sync_vars%';

######################################################################################
# 2020-07-13 - Add fields to the REST settings

# Add fields to the REST settings
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_documents_35', 'id, rated_object_group, object_improvement_grp, customer, customer_id, one_calc_construction_right_grp, two_calc_construction_right_grp, three_calc_construction_right_grp, supervision_comments_grp, object_comment, total_build_land_value_eur, total_market_value_cost_approach_eur, total_market_value_cost_approach_bgn,improvement_total_eur')
  ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-07-16 - Move pattern plugin "prepareReport" settings from code to database

# Move pattern plugin "prepareReport" settings from code to database
UPDATE patterns_plugins
  SET settings = 'doc_type_upi_empty := 14\r\ndoc_type_pi_building := 15\r\ndoc_type_ops := 17\r\ndoc_types_level_two := 18,19,20,21,22,23,24\r\ndoc_types_level_three := 25,26,27,28,29,30,31,42\r\nnom_d_approach_market_approach := 21432\r\nnom_d_approach_revenue_approach := 21433\r\nnom_d_approach_costly_approach := 21434\r\nnom_type_d_purpose_of_the_assessment := 32\r\nnom_coefficients_hard_values_real_estate := 22333\r\nnom_coefficients_hard_values_offices := 22334\r\nnom_coefficients_hard_values_stores := 22335\r\nnom_coefficients_hard_values_industrial := 22336'
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings = '';

######################################################################################
# 2020-07-21 - Update settings for report advance_orders_media
#            - Update settings for pattern plugin prepareReport

# Update settings for report advance_orders_media
UPDATE reports
  SET settings = CONCAT(settings, '\r\nnom_type_type_of_service := 113\r\nnom_type_d_assessment_purpose := 32')
  WHERE type = 'advance_orders_media'
    AND settings NOT LIKE '%nom_type_type_of_service%';
# Update settings for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '\r\nnom_type_of_service_update := 25986\r\nnom_type_of_service_new_assessment := 25985')
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings NOT LIKE '%nom_type_of_service_update%';

######################################################################################
# 2020-07-22 - Add bullet for updating reports objects subtype names

# Add bullet for updating reports objects subtype names
INSERT IGNORE INTO bullets
  SET bullet = 'advanceaddressSetReportsObjectsSubtypeNames',
      description = 'В ББ-то на документи от тип „Доклад“ (ID: 2) попълва името на подтипа, защото преди това ДП е била dropdown, а сега става АК.',
      revision = 16325;

######################################################################################
# 2020-08-27 - Change settings of pattern plugin prepareReport

# Change settings of pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = REPLACE(settings, '\r\nnom_type_d_purpose_of_the_assessment := 32', '')
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings LIKE '%nom_type_d_purpose_of_the_assessment%';

######################################################################################
# 2020-09-17 - Add rest_alvis_reports_generation report, to sign document

# Add new report: rest_alvis_supervision
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`) VALUES
  (411, 'rest_alvis_reports_signing', '', 0);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  (411, 'ALVIS: Подписване на доклад', 'bg'),
  (411, 'ALVIS: Report signing', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', 411, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT `r`.`id`, `rd`.`id`, 'all'
  FROM `roles_definitions` AS `rd`
  JOIN `roles` AS `r`
    ON (`rd`.`module`     = 'reports'
      AND `rd`.`action`     = 'generate_report'
      AND `rd`.`model_type` = 411
      AND `r`.`id` IN (1, 14));

######################################################################################
# 2020-09-25 - Update REST filters for documents of type 2

# Update REST filters for documents of type 2
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place, correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp, attachments, genfiles, draft_report, last_report, source_report, email_address, attached_group, recording_role_group')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-10-07 - Update REST filters for documents of type 2

# Update REST filters for documents of type 2 (certification_date)
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place, correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp, attachments, genfiles, draft_report, last_report, source_report, email_address, attached_group, recording_role_group, certification_date')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-10-12 - Add new settings for pattern plugin prepareReport

# Add new settings for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '\r\nnom_doc_types_ownership_document := 21890\r\nnom_doc_types_sketch_diagram := 21891')
  WHERE method = 'prepareReport'
    AND settings NOT LIKE '%nom_doc_types_ownership_document%';

######################################################################################
# 2020-10-15 - Update REST filters for documents of type 2

# Update REST filters for documents of type 2 (available_tags)
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('rest', 'filter_vars_documents_2', 'id, status, substatus, full_num, rating_type, assessment_purpose, bank_name, customer, award_date, total, office, created_document_id, group_table_2, deadline, assignments_observer, assignments_owner, tags, system_made_by, inspection_place, correction_time, ontime, layout_spell, technical_errors, object_description, methodolgy, photo_quality, analogues, rating, supervision_comments_grp, attachments, genfiles, draft_report, last_report, source_report, email_address, attached_group, recording_role_group, certification_date, available_tags')
   ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2020-11-03 - Change city var prefix for report advance_timesheet_by_assessor

# Change city var prefix for report advance_timesheet_by_assessor
UPDATE reports SET settings = REPLACE(settings, 'city_id', 'city_') WHERE type = 'advance_timesheet_by_assessor';

######################################################################################
# 2020-11-10 - Change quarter var prefix for report advance_timesheet_by_assessor

# Change quarter var prefix for report advance_timesheet_by_assessor
UPDATE reports SET settings = REPLACE(settings, 'quarter_id', 'quarter_') WHERE type = 'advance_timesheet_by_assessor';

######################################################################################
# 2020-11-12 - Add new settings for pattern plugin prepareReport

# Add new settings for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '\r\nnom_type_of_service_followup := 23072\r\nnom_type_of_service_diffbank := 23075')
  WHERE method = 'prepareReport'
    AND settings NOT LIKE '%nom_type_of_service_followup%';

######################################################################################
# 2020-12-21 - Add new settings for automation writeSupervisionData

# Add new settings for automation writeSupervisionData
UPDATE `automations`
SET `settings` = CONCAT(
        `settings`,
        '\r\n\r\nreport_supervision_substatuses := ',
        (SELECT IF(`value` LIKE 'TEST.ADVANCEADDRESS', '4, 33', '4, 35') FROM `settings` where `section` = 'sys' AND `name` = 'code' LIMIT 1)
    )
WHERE `method` LIKE '%writeSupervisionData%'
  AND `settings` NOT LIKE '%report_supervision_substatuses%';

######################################################################################
# 2021-05-28 - Change settings for report 'advance_timesheet_by_assessor'

# Change settings for report 'advance_timesheet_by_assessor'
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, '\r\ninvoice_way_of_payment := way_of_payment', '')
  WHERE `type` = 'advance_timesheet_by_assessor';
UPDATE `reports`
  SET `settings` = REPLACE(`settings`, '\r\ninvoice_payment_date', 'system_made_by := system_made_by\r\n\r\ninvoice_payment_date')
  WHERE `type` = 'advance_timesheet_by_assessor'
    AND `settings` NOT LIKE '%system_made_by%';

######################################################################################
# 2021-12-03 - Added new export plugin for Advance Address installation to export the address list as CSV

# Added new export plugin for Advance Address installation to export the address list as CSV
INSERT INTO `exports` (`type`, `model`, `model_type`, `settings`, `visible`)
 SELECT 'advanceaddress_export_adresseses', 'Document', 2, 'export_file_name := advanceaddress_address_list\r\nexport_hide_filters := format, separator\r\nexport_delimiter := ;\r\nexport_encoding := utf-8\r\n\r\nassignor := assignor_name\r\nsystem_made_by := system_made_by\r\nrating := rating_type\r\nassessment_purpose := assessment_purpose\r\naward_date := award_date\r\norder_date := order_date\r\nsend_doc_date := send_doc_date\r\nwork_term := work_term\r\nloan_applicant := loan_applicant_name\r\nbank := bank_name\r\n\r\ncity := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_thirteen,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentysix,city_twentythree,city_twentytwo,city_two\r\ncity_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nobject_kind := object_kind,object_kind_eighteen,object_kind_eleven,object_kind_fifteen,object_kind_five,object_kind_four,object_kind_fourteen,object_kind_nine,object_kind_nineteen,object_kind_seventeen,object_kind_sixteen,object_kind_three,object_kind_twenty,object_kind_twentyfour,object_kind_twentyone,object_kind_twentyseven,object_kind_twentythree,object_kind_twentytwo,object_kind_two\r\nobject_kind_id := object_kind_eighteen_id,object_kind_eleven_id,object_kind_fifteen_id,object_kind_five_id,object_kind_four_id,object_kind_fourteen_id,object_kind_id,object_kind_nine_id,object_kind_nineteen_id,object_kind_seventeen_id,object_kind_sixteen_id,object_kind_three_id,object_kind_twenty_id,object_kind_twentyfour_id,object_kind_twentyone_id,object_kind_twentyseven_id,object_kind_twentythree_id,object_kind_twentytwo_id,object_kind_two_id\r\nobject_type := object_type,object_type_eighteen,object_type_eleven,object_type_fifteen,object_type_five,object_type_four,object_type_fourteen,object_type_nine,object_type_nineteen,object_type_seven,object_type_seventeen,object_type_sixteen,object_type_three,object_type_twenty,object_type_twentyfour,object_type_twentyone,object_type_twentyseven,object_type_twentythree,object_type_twentytwo,object_type_two,object_type_two_name\r\nobject_type_id := object_type_eighteen_id,object_type_eleven_id,object_type_fifteen_id,object_type_five_id,object_type_four_id,object_type_fourteen_id,object_type_id,object_type_nine_id,object_type_nineteen_id,object_type_seven_id,object_type_seventeen_id,object_type_sixteen_id,object_type_three_id,object_type_twenty_id,object_type_twentyfour_id,object_type_twentyone_id,object_type_twentyseven_id,object_type_twentythree_id,object_type_twentytwo_id,object_type_two_id\r\ntype_object := type_object,type_object_eight,type_object_eighteen,type_object_eleven,type_object_fifteen,type_object_five,type_object_four,type_object_fourteen,type_object_nine,type_object_nineteen,type_object_one,type_object_seven,type_object_seventeen,type_object_six,type_object_sixteen,type_object_ten,type_object_three,type_object_twelve,type_object_twenty,type_object_twentyfive,type_object_twentyfour,type_object_twentyone,type_object_twentyseven,type_object_twentythree,type_object_twentytwo,type_object_two\r\ntype_name_object := type_name_eight_object,type_name_eighteen_object,type_name_eleven_object,type_name_fifteen_object,type_name_five_object,type_name_four_object,type_name_fourteen_object,type_name_nine_object,type_name_nineteen_object,type_name_object,type_name_one_object,type_name_seven_object,type_name_seventeen_object,type_name_six_object,type_name_sixteen_object,type_name_ten_object,type_name_three_object,type_name_twelve_object,type_name_twenty_object,type_name_twentyfive_object,type_name_twentyfour_object,type_name_twentyone_object,type_name_twentyseven_object,type_name_twentythree_object,type_name_twentytwo_object,type_name_two_object\r\nobject_identifier := object_identifier,object_identifier_eighteen,object_identifier_eleven,object_identifier_fifteen,object_identifier_five,object_identifier_four,object_identifier_fourteen,object_identifier_nine,object_identifier_nineteen,object_identifier_seventeen,object_identifier_sixteen,object_identifier_tree,object_identifier_twenty,object_identifier_twentyone,object_identifier_twentyseven,object_identifier_twentythree,object_identifier_twentytwo,object_identifier_two\r\naddress := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_thirteen,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentysix,address_twentythree,address_twentytwo,address_two\r\naddress_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\naddress_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_thirteen,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentysix,address_number_twentythree,address_number_twentytwo,address_number_two\r\nquarter := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_thirteen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentysix,quarter_twentythree,quarter_twentytwo,quarter_two\r\nquarter_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\npostcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nmunicipality_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id\r\nmunicipality := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two\r\narea := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\narea_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nxpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\nmarket_value := market_value,market_value_eight,market_value_eighteen,market_value_eleven,market_value_fifteen,market_value_five,market_value_four,market_value_fourteen,market_value_nine,market_value_nineteen,market_value_one,market_value_seven,market_value_seventeen,market_value_six,market_value_sixteen,market_value_ten,market_value_three,market_value_twelve,market_value_twenty,market_value_twentyfive,market_value_twentyfour,market_value_twentyone,market_value_twentyseven,market_value_twentythree,market_value_twentytwo,market_value_two\r\nprice_square_meter := price_square_meter,price_square_meter_eighteen,price_square_meter_eleven,price_square_meter_fifteen,price_square_meter_five,price_square_meter_four,price_square_meter_fourteen,price_square_meter_nine,price_square_meter_nineteen,price_square_meter_one,price_square_meter_seventeen,price_square_meter_sixteen,price_square_meter_three,price_square_meter_twenty,price_square_meter_twentyfour,price_square_meter_twentyone,price_square_meter_twentyseven,price_square_meter_twentythree,price_square_meter_twentytwo,price_square_meter_two', 1
  WHERE NOT EXISTS(SELECT id FROM exports WHERE type = 'advanceaddress_export_adresseses' AND model = 'Document' AND model_type = '2');

SET @export_id := (SELECT id FROM exports WHERE type = 'advanceaddress_export_adresseses' AND model = 'Document' AND model_type = '2');
INSERT IGNORE INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(@export_id, 'Експорт на адресите от доклади', '', 'bg'),
(@export_id, 'Export addresses from reports', '', 'en');

######################################################################################
# 2022-01-19 - Update REST filters for documents of type 38

# Update REST filters for documents of type 38
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
    ('rest', 'filter_vars_documents_38', 'all')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2022-03-08 - Add assumptions settings for pattern plugin prepareReport

# Add assumptions settings for pattern plugin prepareReport
UPDATE `patterns_plugins`
  SET `settings` = CONCAT(`settings`, '

# Специални допускания.
# Изброяват се ID-та на номенклатури (разделени със запетая), които са опции в колона „Специални допускания“ на втората таблица в секция „D1.1 ОБЩИ ДОПУСКАНИЯ И ОГРАНИЧЕНИЯ ЗА ОЦЕНКАТА, СПЕЦИАЛНИ ДОПУСКАНИЯ“ на раздел „D. ДОПУСКАНИЯ АНАЛИЗИ“.
# За всяка от следните настройки се създава таблица с антетка името на опцията от таблицата за специални допускания и стойност под антетката - текста от колона „Описание“.
# За незаконно строителство:
illegal_construction_assumption := 39776,39777
# За разлика между действиетлното състояние:
differences_assumption := 39774,39775')
  WHERE `method` = 'prepareReport'
    AND `settings` NOT LIKE '%illegal_construction_assumption%';

######################################################################################
# 2022-03-16 - Add setting for pattern plugin prepareReport

# Add setting for pattern plugin prepareReport
UPDATE `patterns_plugins`
  SET `settings` = CONCAT(`settings`, '

# ID на типа номенклатура: М Година на последен ремонт Сграда/Обект
nom_type_last_repair_year := 53')
  WHERE `method` = 'prepareReport'
    AND `settings` NOT LIKE '%nom_type_last_repair_year%';

######################################################################################
# 2022-03-31 - Add setting for pattern plugin prepareReport

# Add setting for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '\r\n\r\n# ID на типа номенклатури „Е Наличие на документ за енергийна ефективност“\r\nnom_type_energy_efficiency_document_existance := 124')
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings NOT LIKE '%nom_type_energy_efficiency_document_existance%';

######################################################################################
# 2022-04-19 - Add setting for pattern plugin prepareReport

# Add setting for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '\r\n\r\n# След колко символа да се пренасят линковете на аналозите\r\nanalogues_url_wrap_length := 90')
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings NOT LIKE '%analogues_url_wrap_length%';

######################################################################################
# 2022-05-26 - Add setting for pattern plugin prepareReport

# Add setting for pattern plugin prepareReport
UPDATE patterns_plugins
  SET settings = CONCAT(settings, '

# Широчини на колоните на таблица „РЕЗУЛТАТИ ОТ ОЦЕНКАТА“ в резюме за Уникредит
# пример: 0.375;0.125;0.5 (с дробни числа това би изглеждало така: 3/8;1/8;4/8)
ucbb_resume_evaluation_results_cols_widths := 0.375;0.125;0.5
#ucbb_resume_evaluation_results_cols_widths := 0.5;0.125;0.5')
  WHERE folder = 'advanceaddress'
    AND method = 'prepareReport'
    AND settings NOT LIKE '%ucbb_resume_evaluation_results_cols_widths%';

######################################################################################
# 2022-08-11 - Added table to save MAPEX requests statistics
#            - Automation to mark document with certain tag if any bb row in it has an address that came from Google
#            - Added automation which to recheck addresses and notify about missing addresses in MAPEX
#            - Update the field for full address in Document type Report (type 2)
#            - Deactivate the action automation for fixing the coordinates in BB

# Added table to save MAPEX requests statistics
DROP TABLE IF EXISTS `diag_mapex_statistics`;
CREATE TABLE `diag_mapex_statistics` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL,
  `document_id` int(11) NOT NULL DEFAULT '0',
  `search_string` text COLLATE utf8_unicode_ci,
  `used_function` varchar(255) COLLATE utf8_unicode_ci NOT NULL DEFAULT 'mapexFindAddresses',
  `bb_id` int(11) NOT NULL DEFAULT '0',
  `found_results` int(11) NOT NULL DEFAULT '0',
  `response_returned` text COLLATE utf8_unicode_ci NOT NULL,
  `search_date` datetime NOT NULL,
   PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Automation to mark document with certain tag if any bb row in it has an address that came from Google
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Проверка за адреси, дошли от Google', 0, NULL, 1, 'documents', NULL, 'action', '2', 'bb := 1\r\nbb_check_field := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\ncheck_string := GOOGLE\r\ngoogle_tag_id := 48', 'condition := \'[action]\' == \'edit\'', 'plugin := advance_address\r\nmethod := updateGoogleTags', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateGoogleTags%' AND `method` LIKE '%advance_address%');

# Added automation which to recheck addresses and notify about missing addresses in MAPEX
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Търсене и известяване за липсващи адреси', 0, NULL, 1, 'documents', NULL, 'crontab', '2', 'start_time := 01:00\r\nstart_before := 03:00\r\n\r\ngoogle_tag_id := 48\r\n\r\nmapex_notify_email :=\r\nadvance_address_notify_email :=\r\n\r\nsend_to_email :=', 'condition := 1', 'plugin := advance_address\r\nmethod := searchAndNotifyForMissingGoogleAddresses', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%searchAndNotifyForMissingGoogleAddresses%' AND `method` LIKE '%advance_address%');

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\nautocomplete_plugin_param_test_mode := 0\r\nautocomplete_plugin_param_limit_results := 10\r\nautocomplete_plugin_param_username := advance\r\nautocomplete_plugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nautocomplete_plugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nautocomplete_plugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# FILTER SETTINGS - these depend on the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $area => <district_name>\r\nautocomplete_fill_options := $area_id => <district_id>\r\nautocomplete_fill_options := $municipality => <region_name>\r\nautocomplete_fill_options := $municipality_id => <region_id>\r\nautocomplete_fill_options := $city => <city>\r\nautocomplete_fill_options := $city_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $quarter => <quarter_name>\r\nautocomplete_fill_options := $quarter_id => <quarter_id>\r\nautocomplete_fill_options := $address => <street>\r\nautocomplete_fill_options := $address_id => <street_id>\r\nautocomplete_fill_options := $address_number => <street_number>\r\nautocomplete_fill_options := $apartment_building => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $xpos => <x>\r\nautocomplete_fill_options := $ypos => <y>\r\n\r\n# NOMENCLATURES TYPES USED\r\nautocomplete_plugin_param_district_nomenclature_id := 5\r\nautocomplete_plugin_param_municipality_nomenclature_id := 6\r\nautocomplete_plugin_param_city_id := 7\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nautocomplete_plugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nautocomplete_plugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nautocomplete_plugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nautocomplete_plugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nautocomplete_plugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nautocomplete_plugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nautocomplete_plugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nautocomplete_plugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nautocomplete_plugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nautocomplete_plugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nautocomplete_plugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nautocomplete_plugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nautocomplete_plugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nautocomplete_plugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nautocomplete_plugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nautocomplete_plugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nautocomplete_plugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nautocomplete_plugin_param_district_mapex_var := code_area_mapex\r\nautocomplete_plugin_param_municipality_mapex_var := code_municipality_mapex\r\nautocomplete_plugin_param_municipality_district_name_var := area\r\nautocomplete_plugin_param_municipality_district_id_var := area_id\r\nautocomplete_plugin_param_city_mapex_var := ekatte_code\r\nautocomplete_plugin_param_city_district_name_var := area\r\nautocomplete_plugin_param_city_district_id_var := code_area_mapex\r\nautocomplete_plugin_param_city_municipality_name_var := municipality\r\nautocomplete_plugin_param_city_municipality_id_var := municipality_id\r\nautocomplete_plugin_param_quarter_mapex_var := quarter_id_mapex\r\nautocomplete_plugin_param_quarter_city_name_var := city\r\nautocomplete_plugin_param_quarter_city_id_var := city_id\r\nautocomplete_plugin_param_street_mapex_var := street_id_mapex\r\nautocomplete_plugin_param_street_city_name_var := city\r\nautocomplete_plugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress' AND `source` NOT LIKE '%autocomplete_plugin%';

# Deactivate the action automation for fixing the coordinates in BB
UPDATE `automations` SET `active`=0 WHERE `method` LIKE '%advance_address%' AND `method` LIKE '%updateAddressDistrict%' AND `active`=1;

######################################################################################
# 2022-08-18 - Update the field for full address in Document type Report (type 13)
#            - Added automation which to recheck addresses and notify about missing addresses in MAPEX
#            - Update settings of the automation searchAndNotifyForMissingGoogleAddresses for model 2

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\nautocomplete_plugin_param_test_mode := 0\r\nautocomplete_plugin_param_limit_results := 10\r\n\r\n# FILTER SETTINGS - these depend on the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $region_name => <district_name>\r\nautocomplete_fill_options := $region_id => <district_id>\r\nautocomplete_fill_options := $municipality_name => <region_name>\r\nautocomplete_fill_options := $municipality_id => <region_id>\r\nautocomplete_fill_options := $populated_place => <city>\r\nautocomplete_fill_options := $populated_place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $analog_quarter_name => <quarter_name>\r\nautocomplete_fill_options := $analog_quarter_id => <quarter_id>\r\nautocomplete_fill_options := $street_name => <street>\r\nautocomplete_fill_options := $street_id => <street_id>\r\nautocomplete_fill_options := $street_number => <street_number>\r\nautocomplete_fill_options := $apartment_building => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $place_lat => <x>\r\nautocomplete_fill_options := $place_lon => <y>\r\n\r\nautocomplete_plugin_param_username := advance\r\nautocomplete_plugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nautocomplete_plugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nautocomplete_plugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nautocomplete_plugin_param_district_nomenclature_id := 5\r\nautocomplete_plugin_param_municipality_nomenclature_id := 6\r\nautocomplete_plugin_param_city_id := 7\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nautocomplete_plugin_param_full_address_fields := full_adress\r\nautocomplete_plugin_param_full_address_fields_id := full_adress_id\r\nautocomplete_plugin_param_region_fields := region_name\r\nautocomplete_plugin_param_region_fields_id := region_id\r\nautocomplete_plugin_param_municipality_fields := municipality_name\r\nautocomplete_plugin_param_municipality_fields_id := municipality_id\r\nautocomplete_plugin_param_city_fields := populated_place\r\nautocomplete_plugin_param_city_fields_id := populated_place_id\r\nautocomplete_plugin_param_quarter_fields := analog_quarter_name\r\nautocomplete_plugin_param_quarter_fields_id := analog_quarter_id\r\nautocomplete_plugin_param_address_fields := street_name\r\nautocomplete_plugin_param_address_fields_id := street_id\r\nautocomplete_plugin_param_address_number := street_number\r\nautocomplete_plugin_param_block := apartment_building\r\nautocomplete_plugin_param_entrance := entrance\r\nautocomplete_plugin_param_xpos := place_lat\r\nautocomplete_plugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nautocomplete_plugin_param_district_mapex_var := code_area_mapex\r\nautocomplete_plugin_param_municipality_mapex_var := code_municipality_mapex\r\nautocomplete_plugin_param_municipality_district_name_var := area\r\nautocomplete_plugin_param_municipality_district_id_var := area_id\r\nautocomplete_plugin_param_city_mapex_var := ekatte_code\r\nautocomplete_plugin_param_city_district_name_var := area\r\nautocomplete_plugin_param_city_district_id_var := code_area_mapex\r\nautocomplete_plugin_param_city_municipality_name_var := municipality\r\nautocomplete_plugin_param_city_municipality_id_var := municipality_id\r\nautocomplete_plugin_param_quarter_mapex_var := quarter_id_mapex\r\nautocomplete_plugin_param_quarter_city_name_var := city\r\nautocomplete_plugin_param_quarter_city_id_var := city_id\r\nautocomplete_plugin_param_street_mapex_var := street_id_mapex\r\nautocomplete_plugin_param_street_city_name_var := city\r\nautocomplete_plugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress' AND `source` NOT LIKE '%autocomplete_plugin%';

# Added automation which to recheck addresses and notify about missing addresses in MAPEX
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Търсене и известяване за липсващи адреси', 0, NULL, 1, 'documents', NULL, 'crontab', '13', 'start_time := 01:00\r\nstart_before := 23:00\r\n\r\ntable_index := 24\r\ngoogle_tag_id := 41\r\n\r\nmapex_notify_email := <EMAIL>\r\nadvance_address_notify_email := <EMAIL>\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := advance_address\r\nmethod := searchAndNotifyForMissingGoogleAddresses', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%searchAndNotifyForMissingGoogleAddresses%' AND `method` LIKE '%advance_address%' AND `module`='documents' AND `start_model_type`=13);

# Update settings of the automation searchAndNotifyForMissingGoogleAddresses for model 2
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\ngoogle_tag_id :=', '\r\ntable_index := bb\r\ngoogle_tag_id :=') WHERE `method` LIKE '%searchAndNotifyForMissingGoogleAddresses%' AND `method` LIKE '%advance_address%' AND `module`='documents' AND `start_model_type`=2;

######################################################################################
# 2022-08-26 - Update the field for full address in Document type Report (type 13)
#            - Updated name of a setting for updateGoogleTags automation
#            - Added updateGoogleTags automation for document type 13

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\nautocomplete_plugin_param_test_mode := 0\r\nautocomplete_plugin_param_limit_results := 10\r\n\r\n# FILTER SETTINGS - these depend on the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $region_name => <district_name>\r\nautocomplete_fill_options := $region_id => <district_id>\r\nautocomplete_fill_options := $municipality_name => <region_name>\r\nautocomplete_fill_options := $municipality_id => <region_id>\r\nautocomplete_fill_options := $populated_place => <city>\r\nautocomplete_fill_options := $populated_place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $analog_quarter_name => <quarter_name>\r\nautocomplete_fill_options := $analog_quarter_id => <quarter_id>\r\nautocomplete_fill_options := $street_name => <street>\r\nautocomplete_fill_options := $street_id => <street_id>\r\nautocomplete_fill_options := $street_number => <street_number>\r\nautocomplete_fill_options := $apartment_building => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $place_lat => <x>\r\nautocomplete_fill_options := $place_lon => <y>\r\n\r\nautocomplete_plugin_param_username := advance\r\nautocomplete_plugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nautocomplete_plugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nautocomplete_plugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nautocomplete_plugin_param_district_nomenclature_id := 5\r\nautocomplete_plugin_param_municipality_nomenclature_id := 6\r\nautocomplete_plugin_param_city_id := 7\r\nautocomplete_plugin_param_quarter_nomenclature_id := 12\r\nautocomplete_plugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nautocomplete_plugin_param_full_address_fields := full_adress\r\nautocomplete_plugin_param_full_address_fields_id := full_adress_id\r\nautocomplete_plugin_param_region_fields := region_name\r\nautocomplete_plugin_param_region_fields_id := region_id\r\nautocomplete_plugin_param_municipality_fields := municipality_name\r\nautocomplete_plugin_param_municipality_fields_id := municipality_id\r\nautocomplete_plugin_param_city_fields := populated_place\r\nautocomplete_plugin_param_city_fields_id := populated_place_id\r\nautocomplete_plugin_param_quarter_fields := analog_quarter_name\r\nautocomplete_plugin_param_quarter_fields_id := analog_quarter_id\r\nautocomplete_plugin_param_address_fields := street_name\r\nautocomplete_plugin_param_address_fields_id := street_id\r\nautocomplete_plugin_param_address_number := street_number\r\nautocomplete_plugin_param_block := apartment_building\r\nautocomplete_plugin_param_entrance := entrance\r\nautocomplete_plugin_param_xpos := place_lat\r\nautocomplete_plugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nautocomplete_plugin_param_district_mapex_var := code_area_mapex\r\nautocomplete_plugin_param_municipality_mapex_var := code_municipality_mapex\r\nautocomplete_plugin_param_municipality_district_name_var := area\r\nautocomplete_plugin_param_municipality_district_id_var := area_id\r\nautocomplete_plugin_param_city_mapex_var := ekatte_code\r\nautocomplete_plugin_param_city_district_name_var := area\r\nautocomplete_plugin_param_city_district_id_var := code_area_mapex\r\nautocomplete_plugin_param_city_municipality_name_var := municipality\r\nautocomplete_plugin_param_city_municipality_id_var := municipality_id\r\nautocomplete_plugin_param_quarter_mapex_var := quarter_id_mapex\r\nautocomplete_plugin_param_quarter_city_name_var := city\r\nautocomplete_plugin_param_quarter_city_id_var := city_id\r\nautocomplete_plugin_param_street_mapex_var := street_id_mapex\r\nautocomplete_plugin_param_street_city_name_var := city\r\nautocomplete_plugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

# Updated name of a setting for updateGoogleTags automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'bb_check_field', 'check_field') WHERE `method` LIKE '%updateGoogleTags%' AND `method` LIKE '%advance_address%' AND `module`='documents' AND `settings` LIKE '%bb_check_field%';

# Added updateGoogleTags automation for document type 13
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Проверка за адреси, дошли от Google', 0, NULL, 1, 'documents', NULL, 'action', '13', 'bb :=\r\ncheck_field := full_adress_id\r\ncheck_string := GOOGLE\r\ngoogle_tag_id :=', 'condition := \'[action]\' == \'edit\'', 'plugin := advance_address\r\nmethod := updateGoogleTags', NULL, 0, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%updateGoogleTags%' AND `method` LIKE '%advance_address%' AND `start_model_type`=13);

######################################################################################
# 2022-08-30 - Update the field for full address in Document type Report (type 2)
#            - Update the field for full address in Document type Report (type 13)

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS - these depend on the document model type and the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 2\r\nautocomplete_plugin_param_group_source := configurator\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $address_name => <address_name>\r\nautocomplete_fill_options := $address_id => <address_id>\r\nautocomplete_fill_options := $district_name => <district_name>\r\nautocomplete_fill_options := $district_id => <district_id>\r\nautocomplete_fill_options := $region_name => <region_name>\r\nautocomplete_fill_options := $region_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $city => <city>\r\nautocomplete_fill_options := $city_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $quarter_name => <quarter_name>\r\nautocomplete_fill_options := $quarter_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nplugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nplugin_param_postcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nplugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nplugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nplugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nplugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nplugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nplugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nplugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nplugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nplugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nplugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nplugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nplugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nplugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nplugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nplugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress';

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $address_name => <address_name>\r\nautocomplete_fill_options := $address_id => <address_id>\r\nautocomplete_fill_options := $district_name => <district_name>\r\nautocomplete_fill_options := $district_id => <district_id>\r\nautocomplete_fill_options := $region_name => <region_name>\r\nautocomplete_fill_options := $region_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $city => <city>\r\nautocomplete_fill_options := $city_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $quarter_name => <quarter_name>\r\nautocomplete_fill_options := $quarter_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

######################################################################################
# 2022-09-01 - Update the field for full address in Document type Report (type 2)
#            - Update the field for full address in Document type Report (type 13)

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS - these depend on the document model type and the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 2\r\nautocomplete_plugin_param_group_source := configurator\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $address_name => <address_name>\r\nautocomplete_fill_options := $address_id => <address_id>\r\nautocomplete_fill_options := $district_name => <district_name>\r\nautocomplete_fill_options := $district_id => <district_id>\r\nautocomplete_fill_options := $region_name => <region_name>\r\nautocomplete_fill_options := $region_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nplugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nplugin_param_postcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nplugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nplugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nplugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nplugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nplugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nplugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nplugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nplugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nplugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nplugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nplugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nplugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nplugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nplugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nplugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress';

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $address_name => <address_name>\r\nautocomplete_fill_options := $address_id => <address_id>\r\nautocomplete_fill_options := $district_name => <district_name>\r\nautocomplete_fill_options := $district_id => <district_id>\r\nautocomplete_fill_options := $region_name => <region_name>\r\nautocomplete_fill_options := $region_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

######################################################################################
# 2022-09-13 - Update the field for full address in Document type Report (type 2)
#            - Update the field for full address in Document type Report (type 13)
#            - Fix the settings for full address field in Document type Report (type 13)

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS - these depend on the document model type and the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 2\r\nautocomplete_plugin_param_group_source := configurator\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nplugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nplugin_param_postcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nplugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nplugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nplugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nplugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nplugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nplugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nplugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nplugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nplugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nplugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nplugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nplugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nplugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nplugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nplugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress';

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress_id => <id>\r\nautocomplete_fill_options := $full_adress => <name>\r\nautocomplete_fill_options := $addr_name => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

# Fix the settings for full address field in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $full_adress => <name>\r\nautocomplete_fill_options := $addr_name => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

######################################################################################
# 2022-09-27 - Add import of "Market information" nomenclatures

# Add import of "Market information" nomenclatures
INSERT IGNORE INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
    (25, 'advanceaddress_market_information', '
# ID на типа номенклатури „Д Пазарна информация“
nom_type_market_information := 33
# Стойност по подразбиране за ДП „Държава“ (country)
nom_market_information_default_country := BG

# ID на типа номенклатури „Област“
nom_type_region := 5

# ID на типа номенклатури „Населено място“
nom_type_populated_place := 7

# ID на типа номенклатури „Квартал“
nom_type_neighborhood := 12

# ID на типа номенклатури „Д Вид обект“
nom_type_object_kind := 22
# ID на таг „Тип обект“ за номенклатури от типа „Д Вид обект“
nom_object_kind_tag_object_type := 40

# Колко RAM памет най-много може да използва импорта
memory_limit := 2048M
', 1);
INSERT IGNORE INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
    (25, 'Пазарна информация', '', 'bg'),
    (25, 'Market information', '', 'en');

######################################################################################
# 2023-02-01 - Update the field for full address in Document type Report (type 2)
#            - Update the field for full address in Document type Report (type 13)

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS - these depend on the document model type and the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 2\r\nautocomplete_plugin_param_group_source := configurator\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nplugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nplugin_param_postcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nplugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nplugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nplugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nplugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nplugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nplugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nplugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nplugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nplugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nplugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nplugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nplugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nplugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nplugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nplugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_city_parent_city := parent_city_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_quarter_ekkate := ekatte_code\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress';

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $full_adress => <name>\r\nautocomplete_fill_options := $addr_name => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_city_parent_city := parent_city_id\r\nplugin_param_quarter_mapex_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_quarter_ekkate := ekatte_code\r\nplugin_param_street_mapex_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

######################################################################################
# 2023-02-03 - Added bullet for updating duplicating quarters

# Added bullet for updating duplicating quarters
INSERT INTO `bullets` (`bullet`, `settings`, `description`, `revision`, `position`, `active`, `modified`, `fired`)
  SELECT 'updateDuplicatedQuarters', 'nom_quarter_type := 12\r\nduplicated_quarters_relations := 22239 => 24437,17101 => 26907,25857 => 18286,24274 => 16826,24102 => 22512,24077 => 16739,23663 => 22512,23516 => 16826,22530 => 16826,22527 => 25974,22526 => 17441,22513 => 22458,19751 => 20768,18497 => 20768,14439 => 22512,14254 => 20768,19874 => 22458,22831 => 24367,15848 => 26865,40954 => 15091,24300 => 14213,22479 => 27919,22478 => 25650,16774 => 14213,14470 => 18631,29808 => 19436,25909 => 37371,22123 => 25941,28851 => 14302,26050 => 15133,25936 => 13027,25135 => 22570,24461 => 16423,24381 => 25774,24270 => 15241,22878 => 25036,20265 => 13001,19853 => 12996,19611 => 13088,19606 => 13026,19570 => 13080,19533 => 14190,14424 => 13005,13023 => 19559,12985 => 19563,24096 => 13028,22826 => 14245,19785 => 13085,16907 => 13026,14574 => 12988,14379 => 19813,14224 => 19563,14181 => 13022,14179 => 13024,14176 => 13086,21195 => 24287,34038 => 14344,16189 => 25891,47143 => 20003,42718 => 20475,44122 => 14643,45409 => 22741,42805 => 14402\r\nbb_hard_update_quarters :=\r\n\r\ndocument_type_report := 2\r\nbb_var_quarter_name := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nbb_var_quarter_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\n\r\ndocument_type_report_one := 13\r\nreport_one_var_quarter_name := analog_quarter_name\r\nreport_one_var_quarter_id := analog_quarter_id\r\n\r\nnom_type_pp_analog := 84\r\npp_analog_quarter_name := analog_quarter_name\r\npp_analog_quarter_id := analog_quarter_id\r\n\r\nnom_type_prp_analog := 91\r\nprp_analog_quarter_name := analog_quarter_name\r\nprp_analog_quarter_id := analog_quarter_id\r\n\r\nnom_market_information := 33\r\nmarket_information_quarter_name := neighborhood\r\nmarket_information_quarter_id := neighborhood_id', 'Automation to update the duplicated records for quarters in the Advance Address installation', 18612, 0, 1, NOW(), '0000-00-00 00:00:00'
    WHERE NOT EXISTS(SELECT `bullet` FROM bullets WHERE `bullet`='updateDuplicatedQuarters');

######################################################################################
# 2023-02-07 - Updated settings for 'updateDuplicatedQuarters' bullet

# Updated settings for 'updateDuplicatedQuarters' bullet
UPDATE `bullets` SET `settings`='quarters_duplicates_search :=\r\n\r\nnom_quarter_type := 12\r\nnom_quarter_var_duplicate := replaced_by_quarter_id\r\n\r\ndocument_type_report := 2\r\nbb_var_quarter_name := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nbb_var_quarter_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\n\r\ndocument_type_report_one := 13\r\nreport_one_var_quarter_name := analog_quarter_name\r\nreport_one_var_quarter_id := analog_quarter_id\r\n\r\nnom_type_pp_analog := 84\r\npp_analog_quarter_name := analog_quarter_name\r\npp_analog_quarter_id := analog_quarter_id\r\n\r\nnom_type_prp_analog := 91\r\nprp_analog_quarter_name := analog_quarter_name\r\nprp_analog_quarter_id := analog_quarter_id\r\n\r\nnom_market_information := 33\r\nmarket_information_quarter_name := neighborhood\r\nmarket_information_quarter_id := neighborhood_id' WHERE `bullet`="updateDuplicatedQuarters" AND `settings` NOT LIKE '%quarters_duplicates_search%';

######################################################################################
# 2023-02-08 - Added bullet for updating duplicating streets

# Added bullet for updating duplicating streets
INSERT IGNORE INTO `bullets` (`bullet`, `settings`, `description`, `revision`, `position`, `active`, `modified`, `fired`)
  SELECT 'updateDuplicatedStreets', 'streets_duplicates_search :=\r\n\r\nnom_street_type := 8\r\nnom_street_var_duplicate := replaced_by_street_id\r\n\r\ndocument_type_report := 2\r\nbb_var_street_name := address,address_one,address_two,address_three,address_four,address_five,address_six,address_seven,address_eight,address_nine,address_ten,address_eleven,address_twelve,address_fourteen,address_fifteen,address_sixteen,address_twentyseven,address_eighteen,address_twentythree,address_twentyfour,address_seventeen,address_twenty,address_twentyone,address_twentytwo,address_nineteen,address_twentyfive\r\nbb_var_street_id := address_id,address_one_id,address_two_id,address_three_id,address_four_id,address_five_id,address_six_id,address_seven_id,address_eight_id,address_nine_id,address_ten_id,address_eleven_id,address_twelve_id,address_fourteen_id,address_fifteen_id,address_sixteen_id,address_twentyseven_id,address_eighteen_id,address_twentythree_id,address_twentyfour_id,address_seventeen_id,address_twentyfive_id\r\n\r\ndocument_type_report_one := 13\r\nreport_one_var_street_name := street_name\r\nreport_one_var_street_id := street_id\r\n\r\nnom_type_pp_analog := 84\r\npp_analog_street_name := analog_street_name\r\npp_analog_street_id := analog_street_id\r\n\r\nnom_type_prp_analog := 91\r\nprp_analog_street_name := analog_street_name\r\nprp_analog_street_id := analog_street_id', 'Automation to update the duplicated records for streets in the Advance Address installation', 18620, 0, 1, NOW(), '0000-00-00 00:00:00'
    WHERE NOT EXISTS(SELECT `bullet` FROM bullets WHERE `bullet`='updateDuplicatedStreets');

######################################################################################
# 2023-02-14 - Added bullet for updating the names of cities, districts and municipalities

# Added bullet for updating the names of cities, districts and municipalities
INSERT IGNORE INTO `bullets` (`bullet`, `settings`, `description`, `revision`, `position`, `active`, `modified`, `fired`)
  SELECT 'updateCitMunDis', 'cities_update_search :=\r\nmunicipalities_update_search := \r\ndistricts_update_search :\r\n\r\nnom_cities_type := 7\r\nnom_municipalities_type := 6\r\nnom_districts_type := 5\r\n\r\ndoc_type_report := 2\r\nbb_var_cities_name := city,city_one,city_two,city_three,city_four,city_five,city_six,city_seven,city_eight,city_nine,city_ten,city_eleven,city_twelve,city_fourteen,city_fifteen,city_sixteen,city_twentyseven,city_eighteen,city_twentythree,city_twentyfour,city_seventeen,city_twenty,city_twentyone,city_twentytwo,city_nineteen,city_twentyfive\r\nbb_var_cities_id := city_id,city_one_id,city_two_id,city_three_id,city_four_id,city_five_id,city_six_id,city_seven_id,city_eight_id,city_nine_id,city_ten_id,city_eleven_id,city_twelve_id,city_fourteen_id,city_fifteen_id,city_sixteen_id,city_twentyseven_id,city_eighteen_id,city_twentythree_id,city_twentyfour_id,city_seventeen_id,city_twenty_id,city_twentyone_id,city_twentytwo_id,city_nineteen_id,city_twentyfive_id\r\nbb_var_municipalities_name := municipality,municipality_one,municipality_two,municipality_three,municipality_four,municipality_five,municipality_six,municipality_seven,municipality_eight,municipality_nine,municipality_ten,municipality_eleven,municipality_twelve,municipality_fifteen,municipality_sixteen,municipality_twentyseven,municipality_eighteen,municipality_twentythree,municipality_twentyfour,municipality_seventeen,municipality_twenty,municipality_twentyone,municipality_twentytwo,municipality_fourteen,municipality_nineteen,municipality_twentyfive\r\nbb_var_municipalities_id := municipality_id,municipality_one_id,municipality_two_id,municipality_three_id,municipality_four_id,municipality_five_id,municipality_six_id,municipality_seven_id,municipality_eight_id,municipality_nine_id,municipality_ten_id,municipality_eleven_id,municipality_twelve_id,municipality_fourteen_id,municipality_fifteen_id,municipality_sixteen_id,municipality_twentyseven_id,municipality_eighteen_id,municipality_twentythree_id,municipality_twentyfour_id,municipality_seventeen_id,municipality_twenty_id,municipality_twentyone_id,municipality_twentytwo_id,municipality_nineteen_id,municipality_twentyfive_id\r\nbb_var_districts_name := area,area_one,area_two,area_three,area_four,area_five,area_six,area_seven,area_eight,area_nine,area_ten,area_eleven,area_twelve,area_fourteen,area_fifteen,area_sixteen,area_twentyseven,area_eighteen,area_twentythree,area_twentyfour,area_seventeen,area_twenty,area_twentyone,area_twentytwo,area_nineteen,area_twentyfive\r\nbb_var_districts_id := area_id,area_one_id,area_two_id,area_three_id,area_four_id,are_five_id,area_six_id,area_seven_id,area_eight_id,area_nine_id,area_ten_id,area_eleven_id,area_twelve_id,area_fourteen_id,area_fifteen_id,area_sixteen_id,area_twentyseven_id,area_eighteen_id,area_twentythree_id,area_twentyfour_id,area_seventeen_id,area_twenty_id,area_twentyone_id,area_twentytwo_id,area_nineteen_id,area_twentyfive_id\r\n\r\ndoc_type_report_one := 13\r\nreport_one_var_cities_name := populated_place\r\nreport_one_var_cities_id := populated_place_id\r\nreport_one_var_municipalities_name := municipality_name\r\nreport_one_var_municipalities_id := municipality_id\r\nreport_one_var_districts_name := region_name\r\nreport_one_var_districts_id := region_id\r\n\r\nnom_type_pp_analog := 84\r\npp_analog_var_cities_name := analog_populated_place\r\npp_analog_var_cities_id := analog_populated_place_id\r\npp_analog_var_municipalities_name := analog_municipality_name\r\npp_analog_var_municipalities_id := analog_municipality_id\r\npp_analog_var_districts_name := analog_region_name\r\npp_analog_var_districts_id := analog_region_id\r\n\r\nnom_type_prp_analog := 91\r\nprp_analog_var_cities_name := analog_populated_place\r\nprp_analog_var_cities_id := analog_populated_place_id\r\nprp_analog_var_municipalities_name := analog_municipality_name\r\nprp_analog_var_municipalities_id := analog_municipality_id\r\nprp_analog_var_districts_name := analog_region_name\r\nprp_analog_var_districts_id := analog_region_id\r\n\r\nnom_type_market_information := 33\r\nmarket_information_var_cities_name := populated_place\r\nmarket_information_var_cities_id := populated_place_id\r\nmarket_information_var_districts_name := region_name\r\nmarket_information_var_districts_id := region_id\r\n\r\nnom_type_street := 8\r\nstreet_var_cities_name := city\r\nstreet_var_cities_id := city_id\r\n\r\nnom_type_quarter := 12\r\nquarter_var_cities_name := city\r\nquarter_var_cities_id := city_id', 'Automation to update the duplicated records for cities, municipalities and districts in the Advance Address installation', 18641, 0, 1, NOW(), '0000-00-00 00:00:00'
    WHERE NOT EXISTS(SELECT `bullet` FROM bullets WHERE `bullet`='updateCitMunDis');

#########################################################################################
# 2023-02-23 - Changed the character set and collation

# Changed the character set and collation
ALTER TABLE `diag_bg_maps_statistics` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `diag_mapex_statistics` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

######################################################################################
# 2023-02-24 - Rename bullet 'updateCitMunDis' to 'updateNamesCitMunDisQtrStr' and update its settings

# Rename bullet 'updateCitMunDis' to 'updateNamesCitMunDisQtrStr' and update its settings
UPDATE `bullets` SET `bullet`='updateNamesCitMunDisQtrStr', `description`='Automation to update the names of quarters, streets, cities, municipalities and districts in the Advance Address installation' , `settings`='cities_update_search := \r\nmunicipalities_update_search :=\r\ndistricts_update_search :=\r\nquarters_update_search :=\r\nstreets_update_search :=\r\n\r\nnom_cities_type := 7\r\nnom_municipalities_type := 6\r\nnom_districts_type := 5\r\nnom_quarters_type := 12\r\nnom_streets_type := 8\r\n\r\ndoc_type_report := 2\r\nbb_var_cities_name := city,city_one,city_two,city_three,city_four,city_five,city_six,city_seven,city_eight,city_nine,city_ten,city_eleven,city_twelve,city_fourteen,city_fifteen,city_sixteen,city_twentyseven,city_eighteen,city_twentythree,city_twentyfour,city_seventeen,city_twenty,city_twentyone,city_twentytwo,city_nineteen,city_twentyfive\r\nbb_var_cities_id := city_id,city_one_id,city_two_id,city_three_id,city_four_id,city_five_id,city_six_id,city_seven_id,city_eight_id,city_nine_id,city_ten_id,city_eleven_id,city_twelve_id,city_fourteen_id,city_fifteen_id,city_sixteen_id,city_twentyseven_id,city_eighteen_id,city_twentythree_id,city_twentyfour_id,city_seventeen_id,city_twenty_id,city_twentyone_id,city_twentytwo_id,city_nineteen_id,city_twentyfive_id\r\nbb_var_municipalities_name := municipality,municipality_one,municipality_two,municipality_three,municipality_four,municipality_five,municipality_six,municipality_seven,municipality_eight,municipality_nine,municipality_ten,municipality_eleven,municipality_twelve,municipality_fifteen,municipality_sixteen,municipality_twentyseven,municipality_eighteen,municipality_twentythree,municipality_twentyfour,municipality_seventeen,municipality_twenty,municipality_twentyone,municipality_twentytwo,municipality_fourteen,municipality_nineteen,municipality_twentyfive\r\nbb_var_municipalities_id := municipality_id,municipality_one_id,municipality_two_id,municipality_three_id,municipality_four_id,municipality_five_id,municipality_six_id,municipality_seven_id,municipality_eight_id,municipality_nine_id,municipality_ten_id,municipality_eleven_id,municipality_twelve_id,municipality_fourteen_id,municipality_fifteen_id,municipality_sixteen_id,municipality_twentyseven_id,municipality_eighteen_id,municipality_twentythree_id,municipality_twentyfour_id,municipality_seventeen_id,municipality_twenty_id,municipality_twentyone_id,municipality_twentytwo_id,municipality_nineteen_id,municipality_twentyfive_id\r\nbb_var_districts_name := area,area_one,area_two,area_three,area_four,area_five,area_six,area_seven,area_eight,area_nine,area_ten,area_eleven,area_twelve,area_fourteen,area_fifteen,area_sixteen,area_twentyseven,area_eighteen,area_twentythree,area_twentyfour,area_seventeen,area_twenty,area_twentyone,area_twentytwo,area_nineteen,area_twentyfive\r\nbb_var_districts_id := area_id,area_one_id,area_two_id,area_three_id,area_four_id,are_five_id,area_six_id,area_seven_id,area_eight_id,area_nine_id,area_ten_id,area_eleven_id,area_twelve_id,area_fourteen_id,area_fifteen_id,area_sixteen_id,area_twentyseven_id,area_eighteen_id,area_twentythree_id,area_twentyfour_id,area_seventeen_id,area_twenty_id,area_twentyone_id,area_twentytwo_id,area_nineteen_id,area_twentyfive_id\r\nbb_var_quarters_name := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nbb_var_quarters_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nbb_var_streets_name := address,address_one,address_two,address_three,address_four,address_five,address_six,address_seven,address_eight,address_nine,address_ten,address_eleven,address_twelve,address_fourteen,address_fifteen,address_sixteen,address_twentyseven,address_eighteen,address_twentythree,address_twentyfour,address_seventeen,address_twenty,address_twentyone,address_twentytwo,address_nineteen,address_twentyfive\r\nbb_var_streets_id := address_id,address_one_id,address_two_id,address_three_id,address_four_id,address_five_id,address_six_id,address_seven_id,address_eight_id,address_nine_id,address_ten_id,address_eleven_id,address_twelve_id,address_fourteen_id,address_fifteen_id,address_sixteen_id,address_twentyseven_id,address_eighteen_id,address_twentythree_id,address_twentyfour_id,address_seventeen_id,address_twentyfive_id\r\n\r\ndoc_type_report_one := 13\r\nreport_one_var_cities_name := populated_place\r\nreport_one_var_cities_id := populated_place_id\r\nreport_one_var_municipalities_name := municipality_name\r\nreport_one_var_municipalities_id := municipality_id\r\nreport_one_var_districts_name := region_name\r\nreport_one_var_districts_id := region_id\r\nreport_one_var_quarters_name := analog_quarter_name\r\nreport_one_var_quarters_id := analog_quarter_id\r\nreport_one_var_streets_name := street_name\r\nreport_one_var_streets_id := street_id\r\n\r\nnom_type_pp_analog := 84\r\npp_analog_var_cities_name := analog_populated_place\r\npp_analog_var_cities_id := analog_populated_place_id\r\npp_analog_var_municipalities_name := analog_municipality_name\r\npp_analog_var_municipalities_id := analog_municipality_id\r\npp_analog_var_districts_name := analog_region_name\r\npp_analog_var_districts_id := analog_region_id\r\npp_analog_var_quarters_name := analog_quarter_name\r\npp_analog_var_quarters_id := analog_quarter_id\r\npp_analog_var_streets_name := analog_street_name\r\npp_analog_var_streets_id := analog_street_id\r\n\r\nnom_type_prp_analog := 91\r\nprp_analog_var_cities_name := analog_populated_place\r\nprp_analog_var_cities_id := analog_populated_place_id\r\nprp_analog_var_municipalities_name := analog_municipality_name\r\nprp_analog_var_municipalities_id := analog_municipality_id\r\nprp_analog_var_districts_name := analog_region_name\r\nprp_analog_var_districts_id := analog_region_id\r\nprp_analog_var_quarters_name := analog_quarter_name\r\nprp_analog_var_quarters_id := analog_quarter_id\r\nprp_analog_var_streets_name := analog_street_name\r\nprp_analog_var_streets_id := analog_street_id\r\n\r\nnom_type_market_information := 33\r\nmarket_information_var_cities_name := populated_place\r\nmarket_information_var_cities_id := populated_place_id\r\nmarket_information_var_districts_name := region_name\r\nmarket_information_var_districts_id := region_id\r\nmarket_information_var_quarters_name := neighborhood\r\nmarket_information_var_quarters_id := neighborhood_id\r\n\r\nnom_type_street := 8\r\nstreet_var_cities_name := city\r\nstreet_var_cities_id := city_id\r\n\r\nnom_type_quarter := 12\r\nquarter_var_cities_name := city\r\nquarter_var_cities_id := city_id' WHERE `bullet`="updateCitMunDis";

######################################################################################
# 2023-04-12 - Update the field for full address in Document type Report (type 2)
#            - Update the field for full address in Document type Report (type 13)

# Update the field for full address in Document type Report (type 2)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS - these depend on the document model type and the variant of the BB\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $city\r\nautocomplete_filter := <city_id> => $city_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 2\r\nautocomplete_plugin_param_group_source := configurator\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly and does not depend on the variant\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $neighborhood_code => <quarter_code>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_code => <street_code>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress,full_adress_eight,full_adress_eighteen,full_adress_eleven,full_adress_fifteen,full_adress_five,full_adress_four,full_adress_fourteen,full_adress_nine,full_adress_nineteen,full_adress_one,full_adress_seven,full_adress_seventeen,full_adress_six,full_adress_sixteen,full_adress_ten,full_adress_three,full_adress_twelve,full_adress_twenty,full_adress_twentyfive,full_adress_twentyfour,full_adress_twentyone,full_adress_twentyseven,full_adress_twentythree,full_adress_twentytwo,full_adress_two\r\nplugin_param_full_address_fields_id := full_adress_id,full_adress_id_eight,full_adress_id_eighteen,full_adress_id_eleven,full_adress_id_fifteen,full_adress_id_five,full_adress_id_four,full_adress_id_fourteen,full_adress_id_nine,full_adress_id_nineteen,full_adress_id_one,full_adress_id_seven,full_adress_id_seventeen,full_adress_id_six,full_adress_id_sixteen,full_adress_id_ten,full_adress_id_three,full_adress_id_twelve,full_adress_id_twenty,full_adress_id_twentyfive,full_adress_id_twentyfour,full_adress_id_twentyone,full_adress_id_twentyseven,full_adress_id_twentythree,full_adress_id_twentytwo,full_adress_id_two\r\nplugin_param_postcode := postcode,postcode_eight,postcode_eighteen,postcode_eleven,postcode_fifteen,postcode_five,postcode_four,postcode_fourteen,postcode_nine,postcode_nineteen,postcode_one,postcode_seven,postcode_seventeen,postcode_six,postcode_sixteen,postcode_ten,postcode_three,postcode_twelve,postcode_twenty,postcode_twentyfive,postcode_twentyfour,postcode_twentyone,postcode_twentyseven,postcode_twentythree,postcode_twentytwo,postcode_two\r\nplugin_param_region_fields := area,area_eight,area_eighteen,area_eleven,area_fifteen,area_five,area_four,area_fourteen,area_nine,area_nineteen,area_one,area_seven,area_seventeen,area_six,area_sixteen,area_ten,area_three,area_twelve,area_twenty,area_twentyfive,area_twentyfour,area_twentyone,area_twentyseven,area_twentythree,area_twentytwo,area_two\r\nplugin_param_region_fields_id := area_eight_id,area_eighteen_id,area_eleven_id,area_fifteen_id,area_five_id,area_four_id,area_fourteen_id,area_id,area_nine_id,area_nineteen_id,area_one_id,area_seven_id,area_seventeen_id,area_six_id,area_sixteen_id,area_ten_id,area_three_id,area_twelve_id,area_twenty_id,area_twentyfive_id,area_twentyfour_id,area_twentyone_id,area_twentyseven_id,area_twentythree_id,area_twentytwo_id,area_two_id\r\nplugin_param_municipality_fields := municipality,municipality_eight,municipality_eighteen,municipality_eleven,municipality_fifteen,municipality_five,municipality_four,municipality_fourteen,municipality_nine,municipality_nineteen,municipality_one,municipality_seven,municipality_seventeen,municipality_six,municipality_sixteen,municipality_ten,municipality_three,municipality_twelve,municipality_twenty,municipality_twentyfive,municipality_twentyfour,municipality_twentyone,municipality_twentyseven,municipality_twentythree,municipality_twentytwo,municipality_two,municipality_name\r\nplugin_param_municipality_fields_id := municipality_eight_id,municipality_eighteen_id,municipality_eleven_id,municipality_fifteen_id,municipality_five_id,municipality_four_id,municipality_fourteen_id,municipality_id,municipality_nine_id,municipality_nineteen_id,municipality_one_id,municipality_seven_id,municipality_seventeen_id,municipality_six_id,municipality_sixteen_id,municipality_ten_id,municipality_three_id,municipality_twelve_id,municipality_twenty_id,municipality_twentyfive_id,municipality_twentyfour_id,municipality_twentyone_id,municipality_twentyseven_id,municipality_twentythree_id,municipality_twentytwo_id,municipality_two_id,municipality_id\r\nplugin_param_city_fields := city,city_eight,city_eighteen,city_eleven,city_fifteen,city_five,city_four,city_fourteen,city_nine,city_nineteen,city_one,city_seven,city_seventeen,city_six,city_sixteen,city_ten,city_three,city_twelve,city_twenty,city_twentyfive,city_twentyfour,city_twentyone,city_twentyseven,city_twentythree,city_twentytwo,city_two\r\nplugin_param_city_fields_id := city_eight_id,city_eighteen_id,city_eleven_id,city_fifteen_id,city_five_id,city_four_id,city_fourteen_id,city_id,city_nine_id,city_nineteen_id,city_one_id,city_seven_id,city_seventeen_id,city_six_id,city_sixteen_id,city_ten_id,city_three_id,city_twelve_id,city_twenty_id,city_twentyfive_id,city_twentyfour_id,city_twentyone_id,city_twentyseven_id,city_twentythree_id,city_twentytwo_id,city_two_id\r\nplugin_param_quarter_fields := quarter,quarter_eight,quarter_eighteen,quarter_fifteen,quarter_five,quarter_four,quarter_fourteen,quarter_nineteen,quarter_one,quarter_seven,quarter_six,quarter_sixteen,quarter_sseventeen,quarter_three,quarter_twenty,quarter_twentyfive,quarter_twentyfour,quarter_twentyone,quarter_twentyseven,quarter_twentythree,quarter_twentytwo,quarter_two\r\nplugin_param_quarter_fields_id := quarter_eight_id,quarter_eighteen_id,quarter_fifteen_id,quarter_five_id,quarter_four_id,quarter_fourteen_id,quarter_id,quarter_nineteen_id,quarter_one_id,quarter_seven_id,quarter_seventeen_id,quarter_six_id,quarter_sixteen_id,quarter_three_id,quarter_twenty_id,quarter_twentyfive_id,quarter_twentyfour_id,quarter_twentyone_id,quarter_twentyseven_id,quarter_twentythree_id,quarter_twentytwo_id,quarter_two_id\r\nplugin_param_address_fields := address,address_eight,address_eighteen,address_eleven,address_fifteen,address_five,address_four,address_fourteen,address_nine,address_nineteen,address_one,address_seven,address_seventeen,address_six,address_sixteen,address_ten,address_three,address_twelve,address_twenty,address_twentyfive,address_twentyfour,address_twentyone,address_twentyseven,address_twentythree,address_twentytwo,address_two\r\nplugin_param_address_fields_id := address_eight_id,address_eighteen_id,address_eleven_id,address_fifteen_id,address_five_id,address_four_id,address_fourteen_id,address_id,address_nine_id,address_nineteen_id,address_one_id,address_seven_id,address_seventeen_id,address_six_id,address_sixteen_id,address_ten_id,address_three_id,address_twelve_id,address_twenty_id,address_twentyfive_id,address_twentyfour_id,address_twentyone_id,address_twentyseven_id,address_twentythree_id,address_twentytwo_id,address_two_id\r\nplugin_param_address_number := address_number,address_number_eight,address_number_eighteen,address_number_eleven,address_number_fifteen,address_number_five,address_number_four,address_number_fourteen,address_number_nine,address_number_nineteen,address_number_one,address_number_seven,address_number_seventeen,address_number_six,address_number_sixteen,address_number_ten,address_number_three,address_number_twelve,address_number_twenty,address_number_twentyfive,address_number_twentyfour,address_number_twentyone,address_number_twentyseven,address_number_twentythree,address_number_twentytwo,address_number_two\r\nplugin_param_block := apartment_building,apartment_building_eight,apartment_building_eighteen,apartment_building_eleven,apartment_building_fifteen,apartment_building_five,apartment_building_four,apartment_building_fourteen,apartment_building_nine,apartment_building_nineteen,apartment_building_one,apartment_building_seven,apartment_building_seventeen,apartment_building_six,apartment_building_sixteen,apartment_building_ten,apartment_building_three,apartment_building_twelve,apartment_building_twenty,apartment_building_twentyfive,apartment_building_twentyfour,apartment_building_twentyone,apartment_building_twentyseven,apartment_building_twentythree,apartment_building_twentytwo,apartment_building_two\r\nplugin_param_entrance := entrance,entrance_eight,entrance_eighteen,entrance_eleven,entrance_fifteen,entrance_five,entrance_four,entrance_fourteen,entrance_nine,entrance_nineteen,entrance_one,entrance_seven,entrance_seventeen,entrance_six,entrance_sixteen,entrance_ten,entrance_three,entrance_twelve,entrance_twenty,entrance_twentyfive,entrance_twentyfour,entrance_twentyone,entrance_twentyseven,entrance_twentythree,entrance_twentytwo,entrance_two\r\nplugin_param_xpos := xpos,xpos_eight,xpos_eighteen,xpos_eleven,xpos_fifteen,xpos_five,xpos_four,xpos_fourteen,xpos_nine,xpos_nineteen,xpos_one,xpos_seven,xpos_seventeen,xpos_six,xpos_sixteen,xpos_ten,xpos_three,xpos_twelve,xpos_twenty,xpos_twentyfive,xpos_twentyfour,xpos_twentyone,xpos_twentyseven,xpos_twentythree,xpos_twentytwo,xpos_two\r\nplugin_param_ypos := ypos,ypos_eight,ypos_eighteen,ypos_eleven,ypos_fifteen,ypos_five,ypos_four,ypos_fourteen,ypos_nine,ypos_nineteen,ypos_one,ypos_seven,ypos_seventeen,ypos_six,ypos_sixteen,ypos_ten,ypos_three,ypos_twelve,ypos_twenty,ypos_twentyfive,ypos_twentyfour,ypos_twentyone,ypos_twentyseven,ypos_twentythree,ypos_twentytwo,ypos_two\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_city_parent_city := parent_city_id\r\nplugin_param_quarter_mapex_var := quarter_hash_mapex\r\nplugin_param_quarter_mapex_id_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_quarter_ekkate := ekatte_code\r\nplugin_param_street_mapex_var := street_hash_mapex\r\nplugin_param_street_mapex_id_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=2 AND `name`='full_adress';

# Update the field for full address in Document type Report (type 13)
UPDATE `_fields_meta` SET `source`='# MAIN SETTINGS\r\nautocomplete := autocompleters\r\nautocomplete_alvis_skip_chaining := 1\r\nautocomplete_dont_kill_concurent_queries := 1\r\nautocomplete_plugin := advance_address\r\nautocomplete_plugin_search := mapexFindAddresses\r\nautocomplete_on_select := searchMapexAddressElementsNomenclatures\r\nautocomplete_min_chars := 6\r\n\r\n# FILTER SETTINGS\r\nautocomplete_filter := <id> => $id\r\nautocomplete_filter := <city> => $populated_place\r\nautocomplete_filter := <city_id> => $populated_place_id\r\nautocomplete_id_var := full_adress_id\r\nautocomplete_plugin_param_model_type := 13\r\nautocomplete_plugin_param_group_source := table\r\n\r\n# SUGGESTIONS AND FILL OPTIONS - these are changed on the fly\r\n# Change only $address_name to the real var in case you want\r\n# the suggestions to be marked with the search string\r\nautocomplete_suggestions := <address_name>\r\nautocomplete_fill_options := $full_adress_id => <address_id>\r\nautocomplete_fill_options := $full_adress => <name>\r\nautocomplete_fill_options := $addr_name => <address_name>\r\nautocomplete_fill_options := $addr_id => <address_id>\r\nautocomplete_fill_options := $dstrct_name => <district_name>\r\nautocomplete_fill_options := $dstrct_id => <district_id>\r\nautocomplete_fill_options := $reg_name => <region_name>\r\nautocomplete_fill_options := $reg_id => <region_id>\r\nautocomplete_fill_options := $post_code => <post_code>\r\nautocomplete_fill_options := $place => <city>\r\nautocomplete_fill_options := $place_id => <city_id>\r\nautocomplete_fill_options := $city_ekatte => <city_ekatte>\r\nautocomplete_fill_options := $neighborhood_name => <quarter_name>\r\nautocomplete_fill_options := $neighborhood_id => <quarter_id>\r\nautocomplete_fill_options := $neighborhood_code => <quarter_code>\r\nautocomplete_fill_options := $str => <street>\r\nautocomplete_fill_options := $str_id => <street_id>\r\nautocomplete_fill_options := $str_code => <street_code>\r\nautocomplete_fill_options := $str_number => <street_number>\r\nautocomplete_fill_options := $block_number => <block_number>\r\nautocomplete_fill_options := $entrance => <entrance>\r\nautocomplete_fill_options := $x => <x>\r\nautocomplete_fill_options := $y => <y>\r\n\r\n# below - SPECIAL PARAMETERS NOT PROCESSED BY processAutocompleteSource\r\nplugin_param_test_mode := 0\r\nplugin_param_limit_results := 10\r\n\r\nplugin_param_username := advance\r\nplugin_param_password := a72a0b6c-7be0-4f1d-9da8-328aebe3e251\r\nplugin_param_url := https://addressservice.mapex.bg//MapexAddressWebService.svc?wsdl\r\nplugin_param_service_url := https://addressservice.mapex.bg/MapexAddressWebService.svc\r\n\r\n# NOMENCLATURES TYPES USED\r\nplugin_param_district_nomenclature_id := 5\r\nplugin_param_municipality_nomenclature_id := 6\r\nplugin_param_city_id := 7\r\nplugin_param_quarter_nomenclature_id := 12\r\nplugin_param_address_nomenclature_id := 8\r\n\r\n# LIST OF VARIANT VARIABLE PACKED BY SOURCE FIELD\r\nplugin_param_full_address_fields := full_adress\r\nplugin_param_full_address_fields_id := full_adress_id\r\nplugin_param_postcode := postcode\r\nplugin_param_region_fields := region_name\r\nplugin_param_region_fields_id := region_id\r\nplugin_param_municipality_fields := municipality_name\r\nplugin_param_municipality_fields_id := municipality_id\r\nplugin_param_city_fields := populated_place\r\nplugin_param_city_fields_id := populated_place_id\r\nplugin_param_quarter_fields := analog_quarter_name\r\nplugin_param_quarter_fields_id := analog_quarter_id\r\nplugin_param_address_fields := street_name\r\nplugin_param_address_fields_id := street_id\r\nplugin_param_address_number := street_number\r\nplugin_param_block := apartment_building\r\nplugin_param_entrance := entrance\r\nplugin_param_xpos := place_lat\r\nplugin_param_ypos := place_lon\r\n\r\n# VARS OF THE NOMENCLATURES FROM RELATED TYPES\r\nplugin_param_district_mapex_var := code_area_mapex\r\nplugin_param_municipality_mapex_var := code_municipality_mapex\r\nplugin_param_municipality_district_name_var := area\r\nplugin_param_municipality_district_id_var := area_id\r\nplugin_param_city_mapex_var := ekatte_code\r\nplugin_param_city_district_name_var := area\r\nplugin_param_city_district_id_var := code_area_mapex\r\nplugin_param_city_municipality_name_var := municipality\r\nplugin_param_city_municipality_id_var := municipality_id\r\nplugin_param_city_parent_city := parent_city_id\r\nplugin_param_quarter_mapex_var := quarter_hash_mapex\r\nplugin_param_quarter_mapex_id_var := quarter_id_mapex\r\nplugin_param_quarter_city_name_var := city\r\nplugin_param_quarter_city_id_var := city_id\r\nplugin_param_quarter_ekkate := ekatte_code\r\nplugin_param_street_mapex_var := street_hash_mapex\r\nplugin_param_street_mapex_id_var := street_id_mapex\r\nplugin_param_street_city_name_var := city\r\nplugin_param_street_city_id_var := city_id' WHERE `model`="Document" AND `model_type`=13 AND `name`='full_adress';

######################################################################################
# 2023-04-28 - Add advance_report_copy report

# Add new report: advance_report_copy
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`)
  VALUES (451, 'advance_report_copy',
        'document_report := 2\r\n',
        0, 0, 0);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`)
  VALUES (451, 'Копиране на доклади', NULL, NULL, 'bg'),
         (451, 'Report copy', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
         ('reports', '', 'generate_report', 451, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports'
    AND `action` = 'generate_report'
    AND `model_type` = 451;


######################################################################################
# 2023-05-12 - Add new settings to advance_report_copy report

# Add new settings to report: advance_report_copy
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`)
  VALUES (451, 'advance_report_copy',
        'document_report := 2\r\nforce_include_archived := 0\r\n',
        0, 0, 0)
  ON DUPLICATE KEY UPDATE
    `settings` = VALUES(`settings`);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`)
  VALUES (451, 'ALVIS: Копиране на доклади', NULL, NULL, 'bg'),
         (451, 'ALVIS: Report copy', NULL, NULL, 'en')
  ON DUPLICATE KEY UPDATE
    `name` = VALUES(`name`);

######################################################################################
# 2023-06-19 - Add new settings to advance_report_copy report

# Add new settings to report: advance_report_copy
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`)
VALUES (451, 'advance_report_copy',
        'document_report := 2\r\nforce_include_archived := 0\r\n# the users with this nZoom roles will see all reports when searching\r\nroles_with_no_limitations := 1,2,3,4,5,6,10,11,15,16,17,18\r\n# The users with role 20 will see only reports in which they are selected to have option 2 or option 3 for recording_role_type in document of type 2\r\n# roles_limited_20 := 2,3\r\n',
        0, 0, 0)
ON DUPLICATE KEY UPDATE
    `settings` = VALUES(`settings`);

######################################################################################
# 2023-07-19 - Change settings for report 'advance_report_by_addresses'

# Change settings for report 'advance_report_by_addresses'
UPDATE `reports` SET `settings` = REPLACE(`settings`, '\nlast_report_file :=', '\nblock_prefix := apartment_building\nlast_report_file :=')
  WHERE `type` = 'advance_report_by_addresses' AND `settings` NOT LIKE '%block_prefix%' ;

######################################################################################
# 2023-12-01 - Restore id in autocompleter

 # Restore id in autocompleter
 UPDATE nom_cstm AS nc
     JOIN nom AS n
     ON nc.model_id = n.id
            -- analog_object_name_type_id
         AND nc.var_id = 8342
         AND nc.num = 1
         AND n.type = 84
         AND nc.lang = ''
     JOIN nom_cstm AS nc1
     ON nc1.model_id = n.id
            -- analog_object_name_type
         AND nc1.var_id = 8341
         AND nc1.num = 1
         AND nc1.lang = ''
         AND nc.value != '21263'
  SET nc.value = '21263'
 WHERE nc1.value LIKE '%поземлен имот%';

######################################################################################
# 2024-01-09 - Add pattern plugin for documents of type Offer(6)

# Add pattern plugin for documents of type Offer(6)
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `image`, `added`, `modified`) VALUES
  (107, 'Document', 6, 'advanceaddress', 'subjectToOffer', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (107, 'Предмет на оферта', '', 'bg', NOW()),
  (107, 'Subject to offer', '', 'en', NOW());
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
  (NULL, 'subject_to_offer_list', 'Document', 'basic', 'pattern_plugins', ',107,', '', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (LAST_INSERT_ID(), 'Предмет на оферта', NULL, 'bg'),
  (LAST_INSERT_ID(), 'Subject to offer', NULL, 'en');

######################################################################################
# 2024-03-07 - Allows the user to get all fields from the nomenclature 116

# Allows the user to get all fields from the nomenclature 116
INSERT INTO settings (`section`, `name`, `value`)
VALUES ('rest', 'filter_vars_nomenclatures_116', 'all');

######################################################################################
# 2024-03-01 - Add new automation for residential building registry

# Add new automation for residential building registry
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Проверка и създаване на нова номенклатура жилищни сгради', 0, NULL, 1, 'documents', null, 'action', '2', '', 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'setstatus\'\r\ncondition := \'[b_substatus]\' === \'6\'', 'plugin := advance_address\r\nmethod := addInResidentialBuildingRegistry', NULL, '0', '0', '1'
WHERE NOT EXISTS (SELECT id FROM `automations` WHERE `method` LIKE '%addInResidentialBuildingRegistry%' AND `start_model_type` = '2');

######################################################################################
# 2024-04-22 - Add bullet for initially completing the nomenclature of register of residential buildings

# Add bullet for initially completing the nomenclature of register of residential buildings
INSERT IGNORE INTO bullets (`bullet`, `description`, `settings`, `revision`, `position`, `active`)
VALUES ('fillResidentialBuildingNomenclature', 'Първоначално попълване на номенклатура жилищни сгради - РЖС', '', '19804', '1', '1');

######################################################################################
# 2024-05-21 - Update copied records with proper ids from source reports in hierarchical relationship description table

# Update copied records with proper ids from source reports in hierarchical relationship description table
UPDATE documents_cstm AS dc
    JOIN documents_cstm AS dc1
      ON dc.var_id = '1491'
      AND dc.value = dc1.model_id
      AND dc1.var_id IN (SELECT id FROM `_fields_meta` AS fm WHERE `name` = 'report_name_id')
    JOIN documents AS d
      ON dc.model_id = d.id
      AND d.`type` = 13 AND d.deleted = 0 AND d.active = 1
SET dc1.value = dc.model_id
WHERE dc.model_id != dc1.value;


######################################################################################
# 2024-05-30 - Update copied records with proper ids from source reports in table hierarchical relationship description

# Update copied records with proper ids from source reports in hierarchical relationship description table - hierarchy_document_relat_id
UPDATE documents_cstm AS dc
  JOIN documents_cstm AS dc1
  ON dc.var_id = '1485'
    AND dc.value = dc1.model_id
    AND dc1.var_id IN (SELECT id FROM `_fields_meta` AS fm WHERE `name` = 'report_name_id' AND model = 'Document')
  JOIN documents AS d
  ON dc.model_id = d.id
    AND d.`type` = 13 AND d.deleted = 0 AND d.active = 1
SET dc1.value = dc.model_id
WHERE dc.model_id != dc1.value;

######################################################################################
# 2024-06-13 - Changed condition for automation for residential building registry

# Added new condition for automation for residential building registry - save and finish report, create new nomenclature
UPDATE automations
SET `conditions` = 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'setstatus\' && \'[b_substatus]\' === \'6\' || \'[action]\' == \'edit\' && $request->get(\'finishReport\') == \'1\''
WHERE `method` LIKE '%addInResidentialBuildingRegistry%' AND `start_model_type` = '2'
LIMIT 1;

######################################################################################
# 2024-07-05 - Allows the user to get all fields from the nomenclature 114

# Allows the user to get all fields from the nomenclature 114
INSERT INTO settings (`section`, `name`, `value`)
VALUES ('rest', 'filter_vars_nomenclatures_114', 'all');

######################################################################################
# 2024-07-05 - Remove/Disable bullet for initial fill of residential buildings nomenclature

# Remove/Disable bullet for initial fill of residential buildings nomenclature
UPDATE bullets
SET description = CONCAT('REMOVED: ', description), active = 0
WHERE bullet = 'fillResidentialBuildingNomenclature'
LIMIT 1;

######################################################################################
# 2024-07-05 - Add new bullet for offices and administrative buildings registry

# Add new bullet for offices and administrative buildings registry
INSERT IGNORE INTO bullets (`bullet`, `description`, `settings`, `revision`, `position`, `active`)
VALUES ('fillBuildingRegistry', 'Първоначално попълване на номенклатури', 'building_types := 18,19\r\nreports_per_page := 100\r\n', '19808', '1', '1');

######################################################################################
# 2024-07-10 - Rename automation name for residential building registry

# Add new automation for residential building registry
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT 'Проверка и създаване на нова номенклатура', 0, NULL, 1, 'documents', null, 'action', '2', '', 'condition := Auth::$is_rest\r\ncondition := \'[request_is_post]\'\r\ncondition := \'[action]\' == \'setstatus\' && \'[b_substatus]\' == \'6\' || \'[action]\' == \'edit\' && $request->get(\'finishReport\') == \'1\'', 'plugin := advance_address\r\nmethod := addInResidentialBuildingRegistry', NULL, '0', '0', '1'
WHERE NOT EXISTS (SELECT id FROM `automations` WHERE `method` LIKE '%addInResidentialBuildingRegistry%' AND `start_model_type` = '2');

######################################################################################
# 2024-07-29 - Rename automation name for building registry

# Rename automation name for building registry
UPDATE `automations`
SET `name` = 'Проверка и създаване на нова номенклатура'
WHERE `method` LIKE '%addInResidentialBuildingRegistry%'
AND `name` = 'Проверка и създаване на нова номенклатура жилищни сгради';

UPDATE `automations`
SET `method` = REPLACE(`method`, 'addInResidentialBuildingRegistry', 'addInBuildingRegistry')
WHERE `method` LIKE '%addInResidentialBuildingRegistry%';

######################################################################################
# 2024-07-31 - Allows the user to get all fields from the nomenclature 117 natural risks

# Allows the user to get all fields from the nomenclature 117 natural risks
INSERT INTO settings (`section`, `name`, `value`)
VALUES ('rest', 'filter_vars_nomenclatures_117', 'all');

######################################################################################
# 2024-10-18 - Add new variable sek_version_name, change type dropdown to autocompleter for variable sek_version

# Add new variable sek_version_name, change type dropdown to autocompleter for variable sek_version
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`)
VALUES (4714, 'Nomenclature', 47, 'sek_version_name', 'autocompleter', '', 0, 1, 'autocomplete := nomenclatures\r\nautocomplete_suggestions := <name>\r\nautocomplete_fill_options := $sek_version => <id>\r\nautocomplete_fill_options := $sek_version_name => <name>\r\nautocomplete_filter := <type> => 101\r\n#autocomplete_combobox := 1', NULL, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2212, 5, '', '', '');
INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`)
VALUES (4714, 'label', 'Версия СЕК', 'bg');
# Convert dropdown to hidden text field
UPDATE `_fields_meta` SET `type`='text', `source`='', `hidden`=1 WHERE  `id`=4713;
# Add values for the new variable
INSERT IGNORE INTO nom_cstm
SELECT nc.model_id, 4714, 1, ni.NAME, NOW(), -1, NOW(), -1, ni.lang
FROM nom n
JOIN nom_i18n ni
  ON n.TYPE=101 AND ni.parent_id=n.id AND ni.lang='bg'
JOIN nom_cstm nc
  ON nc.var_id=4713 AND nc.VALUE=n.id;

######################################################################################
# 2024-11-29 - Added new report 'advance_basis_for_income'

# Added new report 'advance_basis_for_income'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (475, 'advance_basis_for_income', 'assignor_types := 2,3,5,6\r\nreport_documents_types := 2\r\ncustomer_types := 2,3,5,6\r\ndocument_var_assignor_id := assignor_id\r\nfinance_document_type := 101', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (475, 'Обединяване на доклади в основание за приход', NULL, NULL, 'bg'),
  (475, 'Consolidating reports into a revenue basis', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`) VALUES
  ('reports', '', 'generate_report', '475', '1'),
  ('reports', '', 'export', '475', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
    FROM `roles_definitions`
    WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '475';

######################################################################################
# 2024-12-16 - Added new setting in report 'advance_basis_for_income'

# Added new setting in report 'advance_basis_for_income'
UPDATE reports
    SET settings = 'assignor_types := 2,3,5,6\r\nreport_documents_types := 2\r\ncustomer_types := 2,3,5,6\r\ndocument_var_assignor_id := assignor_id\r\nfinance_document_type := 101\r\nreport_article_id := 135696'
WHERE `type` = 'advance_basis_for_income'
LIMIT 1;

######################################################################################
# 2024-12-18 - Added new setting in report 'advance_basis_for_income'

# Added new setting in report 'advance_basis_for_income'
UPDATE reports
    SET settings = 'assignor_types := 2,3,5,6\r\nreport_documents_types := 2\r\ncustomer_types := 2,3,5,6\r\ndocument_var_assignor_id := assignor_id\r\nfinance_document_type := 101\r\nreport_article_id := 135696\r\nreport_search_active := 1'
WHERE `type` = 'advance_basis_for_income'
LIMIT 1;

######################################################################################
# 2025-02-20 - Added new report 'advance_send_signed_reports'

# Added new report 'advance_send_signed_reports'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`)
VALUES (478, 'advance_send_signed_reports', '# Тип документи за търсене\r\nreport_documents_types := 2\r\n# Възложител тип\r\nassignor_types := 2\r\n# Клиент тип\r\ncustomer_types := 2,3\r\n# Променлива на възложител\r\ndocument_var_assignor_id := assignor_id\r\n# Променлива на ползвател\r\ndocument_var_bank_id := bank_id\r\n# Ползвател тип\r\nbank_types := 3\r\n# Търсене на активни доклади\r\nreport_search_active := 1\r\n# Статус заключен\r\ndocument_status := locked\r\n# Подстатус подпис\r\ndocument_sub_status := 5\r\n# Система за изготвяне - nZoom\r\ndocument_var_system_made_by := 2\r\n# Търсе на променливи в типове модели\r\nsearch_vars_in_models := 2\r\n# Променлива система за изготвяне\r\ndocument_var_system_made_by_id := system_made_by\r\n# ID на опция ''Подписан доклад''\r\nsigned_attached_file_option_id := 135698\r\n# ID на таг за изпратен доклад\r\nreport_tag_id := 52\r\n# Променлива на категория на прикачения файл\r\ndocument_var_doc_type_category := doc_type\r\n# Променлива на Файл\r\ndocument_var_attached_doc_file := attached_doc_file\r\n# Променлива на приложен документ\r\ndocument_var_attached_doc := attached_doc\r\n# Email на клиент\r\ndocument_var_email_address := email_address\r\n# Email на банков служител\r\ndocument_var_bank_employee_email := bank_employee_email\r\n# Имейл централен офис, ако контрагента е тип Банка(3)\r\ndocument_var_email_bank_office := email_bank_office\r\n# Потребители, които имат достъп до всички резултати от справката\r\npower_users := 1,3\r\n', 0, 0, 1);

INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`)
VALUES (478, 'Изпращане на подписани доклади', NULL, NULL, 'bg'),
       (478, 'Sending signed reports', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `controller`, `action`, `model_type`, `position`)
VALUES ('reports', '', 'generate_report', '478', '1'),
       ('reports', '', 'export', '478', '1');

INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
SELECT
    '1',
    `id`,
    'all'
FROM `roles_definitions`
WHERE `module` = 'reports'
  AND `action` IN ('generate_report', 'export')
  AND `model_type` = '478';

######################################################################################
# 2025-02-20 - Added new settings in report 'advance_send_signed_reports'

# Added new settings in report 'advance_send_signed_reports'
UPDATE reports
SET settings = '# Тип документи за търсене\r\nreport_documents_types := 2\r\n# Възложител тип\r\nassignor_types := 2\r\n# Клиент тип\r\ncustomer_types := 2,3\r\n# Променлива на възложител\r\ndocument_var_assignor_id := assignor_id\r\n# Променлива на ползвател\r\ndocument_var_bank_id := bank_id\r\n# Ползвател тип\r\nbank_types := 3\r\n# Търсене на активни доклади\r\nreport_search_active := 1\r\n# Статус заключен\r\ndocument_status := locked\r\n# Подстатус подпис\r\ndocument_sub_status := 5\r\n# Система за изготвяне - nZoom\r\ndocument_var_system_made_by := 2\r\n# Търсе на променливи в типове модели\r\nsearch_vars_in_models := 2\r\n# Променлива система за изготвяне\r\ndocument_var_system_made_by_id := system_made_by\r\n# ID на опция ''Подписан доклад''\r\nsigned_attached_file_option_id := 135698\r\n# ID на таг за изпратен доклад\r\nreport_tag_id := 52\r\n# Променлива на категория на прикачения файл\r\ndocument_var_doc_type_category := doc_type\r\n# Променлива на Файл\r\ndocument_var_attached_doc_file := attached_doc_file\r\n# Променлива на приложен документ\r\ndocument_var_attached_doc := attached_doc\r\n# Email на клиент\r\ndocument_var_email_address := email_address\r\n# Email на банков служител\r\ndocument_var_bank_employee_email := bank_employee_email\r\n# Имейл централен офис, ако контрагента е тип Банка(3)\r\ndocument_var_email_bank_office := email_bank_office\r\n# Потребители, които имат достъп до всички резултати от справката\r\npower_users := 1,3\r\n# Служител\r\ndocument_var_recording_role_id := recording_role_id\r\n# Роля по доклада\r\ndocument_var_recording_role_type := recording_role_type\r\n'
WHERE `type` = 'advance_send_signed_reports'
LIMIT 1;

######################################################################################
# 2025-02-25 - Edit and added new settings in report 'advance_send_signed_reports'

# Edit and added new settings in report 'advance_send_signed_reports'
UPDATE reports
SET settings = '# Тип документи за търсене\r\nreport_documents_types := 2\r\n# Възложител тип\r\nassignor_types := 2\r\n# Клиент тип\r\ncustomer_types := 2,3\r\n# Променлива на възложител\r\ndocument_var_assignor_id := assignor_id\r\n# Променлива на ползвател\r\ndocument_var_bank_id := bank_id\r\n# Ползвател тип\r\nbank_types := 3\r\n# Търсене на активни доклади\r\nreport_search_active := 1\r\n# Статус заключен\r\ndocument_status := locked\r\n# Подстатус подпис\r\ndocument_sub_status := 5\r\n# Система за изготвяне - nZoom\r\ndocument_var_system_made_by := 2,1\r\n# Търсе на променливи в типове модели\r\nsearch_vars_in_models := 2\r\n# Променлива система за изготвяне\r\ndocument_var_system_made_by_id := system_made_by\r\n# ID на опция ''Подписан доклад''\r\nsigned_attached_file_option_id := 140137\r\n# ID на таг за изпратен доклад\r\nreport_tag_id := 52\r\n# Променлива на категория на прикачения файл\r\ndocument_var_doc_type_category := doc_type\r\n# Променлива на Файл\r\ndocument_var_attached_doc_file := attached_doc_file\r\n# Променлива на приложен документ\r\ndocument_var_attached_doc := attached_doc\r\n# Email на клиент\r\ndocument_var_email_address := email_address\r\n# Email на банков служител\r\ndocument_var_bank_employee_email := bank_employee_email\r\n# Имейл централен офис, ако контрагента е тип Банка(3)\r\ndocument_var_email_bank_office := email_bank_office\r\n# Роли, които нямат достъп до всички резултати от справката\r\nrestricted_roles := 4,15\r\n# Служител\r\ndocument_var_recording_role_id := recording_role_id\r\n# Роля по доклада\r\ndocument_var_recording_role_type := recording_role_type\r\n'
WHERE `type` = 'advance_send_signed_reports'
LIMIT 1;

######################################################################################
# 2025-03-07 - Edit and added new settings in report 'advance_basis_for_income'

# Edit and added new settings in report 'advance_basis_for_income'
UPDATE reports
SET settings = '# Възложител тип\r\nassignor_types := 2,3,5,6\r\n# Тип документи за търсене\r\nreport_documents_types := 2\r\n# Клиент тип\r\ncustomer_types := 2,3,5,6\r\n# Променлива на възложител\r\ndocument_var_assignor_id := assignor_id\r\n# Основание за приход\r\nfinance_document_type := 101\r\n# Идентификатор на услуга\r\nreport_article_id := 135696\r\n# Търсене на активни доклади\r\nreport_search_active := 1\r\n# Променлива на ползвател\r\ndocument_var_bank_id := bank_id\r\n# Ползвател тип\r\nbank_types := 3\r\n# Начин на плащане\r\ndocument_var_payment_way := payment_way\r\n# Тип документ - връзка към възлагателно писмо\r\ndocument_var_document_type := 1\r\n'
WHERE `type` = 'advance_basis_for_income'
LIMIT 1;

######################################################################################
# 2025-03-19 - Added automation to send file to Eurotrust
#            - Added automation for after callback to save the downloaded file(s) in the corresponding
#            - Added automation to change the status of the document write after

INSERT INTO automations (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Изпращане на доклада за подпис', 0, NULL, 1, 'documents', NULL, 'before_action', '2', 'sign_from_var_last_report := last_report\r\nusers_by_email := \r\ndocument_description := Доклад на Адванс Експертни Оценки\r\ndocument_expire_interval := +2 hours\r\ndocument_coverage := 20000\r\n#document_annotation_text := \r\n#document_annotation_font_name := \r\n#document_annotation_font_size := \r\n#document_annotation_font_color := \r\n#document_annotation_axis_x := \r\n#document_annotation_axis_y := \r\n#document_annotation_axis_page := ', 'condition := \'[request_is_post]\' == \'1\' && in_array(\'[action]\', array(\'setstatus\'))\r\ncondition := $request->get(\'substatus\') == \'closed_37\'\r\ncondition :=  \'[prev_b_substatus]\' != 37\r\n', 'plugin := sign\r\nmethod := signEurotrust\r\n', 'cancel_action_on_fail := 1', 0, 1, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'documents' AND start_model_type = 2 AND method LIKE '%signEurotrust%');

INSERT INTO automations (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Записване на подписан файл', 0, NULL, 1, 'callbacks', NULL, 'action', '0', 'launch_automations_upon_success := 1\r\n', 'condition := \'[action]\' == \'call\' && $new_model->modelName == \'Callbacks_Request\'', 'plugin := sign\r\nmethod := saveEurotrustSignedFiles\r\n', NULL, 0, 1, 1
    WHERE NOT EXISTS(SELECT id FROM automations WHERE module = 'callbacks' AND method LIKE '%saveEurotrustSignedFiles%');

INSERT INTO automations (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Смяна на статус на "Подписан" след подписване', 0, NULL, 1, 'documents', NULL, 'action', '2', '', 'condition :=  \'[action]\' == \'sign\' && $new_model->get(\'signed_file_vars\')\r\ncondition := \'[prev_b_substatus]\' == 37', 'method := status\r\nnew_status := closed\r\nnew_substatus := 38', '', 0, 1, 1
     WHERE NOT EXISTS (SELECT id FROM `automations` WHERE `method` LIKE '%status%' AND `start_model_type` = '2' AND conditions LIKE '%sign%');

UPDATE `settings` SET `value`= '93ac463b-37d4-42ac-ae5g-b7f843773af4' WHERE section='eurotrust' AND name='vendorKey';
UPDATE `settings` SET `value`= 'gmHrzgFu7TznVY5Q' WHERE section='eurotrust' AND name='vendorNumber';
UPDATE `settings` SET `value`= '1' WHERE section='eurotrust' AND name='testMode';
UPDATE `settings` SET `value`= '1' WHERE section='1' AND name='logRequest';

######################################################################################
# 2025-03-27 - Fixed eurotrust setting param

# Fixed eurotrust setting param
UPDATE automations SET settings=REPLACE(settings, 'document_annotation_axis_page', 'document_annotation_page') WHERE method like '%signEurotrust%' AND settings like '%document_annotation_axis_page%';

######################################################################################
# 2025-04-01 - Added automation to save the signed file into a table
#            - Removed automation the standard automation saveEurotrustSignedFiles

INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Записване на подписан файл в таблица', 0, NULL, 1, 'callbacks', NULL, 'action', '0', 'launch_automations_upon_success := 1\r\n\r\ndestination_group_var := attached_group\r\nattached_doc_file := [signed_file_id]\r\ndoc_type := 135718\r\ndocument_type_name := \r\nattached_doc := Електронно подписан доклад\r\nattached_doc_notes := [modified]\r\ndoc_issued_by := \r\ndoc_to_object := ', 'condition := \'[action]\' == \'call\' && $new_model->modelName == \'Callbacks_Request\'', 'plugin := advance_address\r\nmethod := saveEurotrustSignedFilesIntoTable\r\n', NULL, 0, 1, 1
    WHERE NOT EXISTS(SELECT id FROM `automations` WHERE module = 'callbacks' AND method LIKE '%saveEurotrustSignedFilesIntoTable%');

DELETE FROM `automations` WHERE method LIKE '%saveEurotrustSignedFiles%' AND method like '%plugin := sign%';

######################################################################################
# 2025-04-11 - Added new settings in report 'advance_basis_for_income'

UPDATE reports
    SET `settings` = '# Възложител тип\nassignor_types := 2,3,5,6\n# Тип документи за търсене\nreport_documents_types := 2\n# Клиент тип\ncustomer_types := 2,3,5,6\n# Променлива на възложител\ndocument_var_assignor_id := assignor_id\n# Основание за приход\nfinance_document_type := 101\n# Идентификатор на услуга\nreport_article_id := 136662\n# Търсене на активни доклади\nreport_search_active := 1\n# Променлива на ползвател\ndocument_var_bank_id := bank_id\n# Ползвател тип\nbank_types := 3\n# Начин на плащане\ndocument_var_payment_way := payment_way\n# Тип документ - връзка към възлагателно писмо\ndocument_var_document_type := 1\n# Тип финансов документ, използва се в списъка с типове документи\nfinance_type_basis_for_income := 101\n'
    WHERE `type` = 'advance_basis_for_income';

######################################################################################
# 2025-04-14 - Replace modified date with formatted one

UPDATE automations SET settings=REPLACE(settings, 'attached_doc_notes := [modified]', 'attached_doc_notes := php(date(''d.m.Y H:i:s'', strtotime(''[modified]'')))')
WHERE module = 'callbacks' AND method LIKE '%saveEurotrustSignedFilesIntoTable%';

######################################################################################
# 2025-05-16 - Added new settings in report 'advance_send_signed_reports'
# document_var_last_report
# add_last_report_version_file

UPDATE reports
SET `settings` = '# Тип документи за търсене\nreport_documents_types := 2\n# Възложител тип\nassignor_types := 2,3\n# Клиент тип\ncustomer_types := 2,3\n# Променлива на възложител\ndocument_var_assignor_id := assignor_id\n# Променлива на ползвател\ndocument_var_bank_id := bank_id\n# Ползвател тип\nbank_types := 3\n# Търсене на активни доклади\nreport_search_active := 1\n# Статус заключен\ndocument_status := locked\n# Подстатус подпис\ndocument_sub_status := 6,38\n# Система за изготвяне - nZoom\ndocument_var_system_made_by := 2,1\n# Търсе на променливи в типове модели\nsearch_vars_in_models := 2\n# Променлива система за изготвяне\ndocument_var_system_made_by_id := system_made_by\n# ID на опция ''Подписан доклад''\nsigned_attached_file_option_id := 140137\n# ID на таг за изпратен доклад\nreport_tag_id := 52\n# Променлива на категория на прикачения файл\ndocument_var_doc_type_category := doc_type\n# Променлива на Файл\ndocument_var_attached_doc_file := attached_doc_file\n# Променлива на приложен документ\ndocument_var_attached_doc := attached_doc\n# Email на клиент\ndocument_var_email_address := email_address\n# Email на банков служител\ndocument_var_bank_employee_email := bank_employee_email\n# Имейл централен офис, ако контрагента е тип Банка(3)\ndocument_var_email_bank_office := email_bank_office\n# Роли, които нямат достъп до всички резултати от справката\nrestricted_roles := 3,4,15\n# Служител\ndocument_var_recording_role_id := recording_role_id\n# Роля по доклада\ndocument_var_recording_role_type := recording_role_type\n# Последна версия доклад\ndocument_var_last_report := last_report\n# Да се добавя ли и файла от последна версия на доклада\nadd_last_report_version_file := 0\n'
    WHERE `type` = 'advance_send_signed_reports';

######################################################################################
# 2025-05-19 - Edited comment for setting add_last_report_version_file


UPDATE reports
SET `settings` = '# Тип документи за търсене\nreport_documents_types := 2\n# Възложител тип\nassignor_types := 2,3\n# Клиент тип\ncustomer_types := 2,3\n# Променлива на възложител\ndocument_var_assignor_id := assignor_id\n# Променлива на ползвател\ndocument_var_bank_id := bank_id\n# Ползвател тип\nbank_types := 3\n# Търсене на активни доклади\nreport_search_active := 1\n# Статус заключен\ndocument_status := locked\n# Подстатус подпис\ndocument_sub_status := 6,38\n# Система за изготвяне - nZoom\ndocument_var_system_made_by := 2,1\n# Търсе на променливи в типове модели\nsearch_vars_in_models := 2\n# Променлива система за изготвяне\ndocument_var_system_made_by_id := system_made_by\n# ID на опция ''Подписан доклад''\nsigned_attached_file_option_id := 140137\n# ID на таг за изпратен доклад\nreport_tag_id := 52\n# Променлива на категория на прикачения файл\ndocument_var_doc_type_category := doc_type\n# Променлива на Файл\ndocument_var_attached_doc_file := attached_doc_file\n# Променлива на приложен документ\ndocument_var_attached_doc := attached_doc\n# Email на клиент\ndocument_var_email_address := email_address\n# Email на банков служител\ndocument_var_bank_employee_email := bank_employee_email\n# Имейл централен офис, ако контрагента е тип Банка(3)\ndocument_var_email_bank_office := email_bank_office\n# Роли, които нямат достъп до всички резултати от справката\nrestricted_roles := 3,4,15\n# Служител\ndocument_var_recording_role_id := recording_role_id\n# Роля по доклада\ndocument_var_recording_role_type := recording_role_type\n# Последна версия доклад\ndocument_var_last_report := last_report\n# Да се добавя ли и файла от последна версия на доклада. Важи само за доклади за които системата на изготвяне е ALVIS\nadd_last_report_version_file := 0\n'
WHERE `type` = 'advance_send_signed_reports';
