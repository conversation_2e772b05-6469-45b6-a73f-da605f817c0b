###################################################################################
### SQL nZoom Specific Updates - CREDITINS (http://creditins.n-zoom.com/) ###
###################################################################################

########################################################################
# 2016-10-04 - Added report 'bgservice_calls' to the CREDITINS installation (Bug 3075)
#            - Added automation which gets the list calls from the previous day and makes events and minitasks from their rows (Bug 3149)

# Added report 'bgservice_calls' to the ITG installation (Bug 3075)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (172, 'bgservice_calls', 'document_calls_list := 19\r\nproject_comercial_campaign := 3\r\nproject_request := \r\ncustomer_id := customer_id\r\ncall_status := list_call_status\r\ncall_date := list_to_date\r\ncall_date_next_call := list_datenext_call\r\ncall_minutes := list_call_minute\r\ncall_description := call_description\r\ncall_comment := list_coment\r\n', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (172, 'Обаждания', NULL, NULL, 'bg'),
  (172, 'Calls', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 172, 0, 1),
  ('reports', 'export', 172, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=172;

# Added automation which gets the list calls from the previous day and makes events and minitasks from their rows (Bug 3149)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  (NULL, 'Лист обаждания - добавяне на събитие и назаначаване на участници', 0, NULL, 1, 'documents', NULL, 'crontab', '19', 'start_time := 01:00\r\nstart_before := 03:00\r\ncall_type := list_call_status\r\ncall_meeting := call_status_second\r\ncall_minitasks := call_status_fift,call_status_first\r\ncall_minitasks_special := call_status_fourth\r\ncustomer_name := customer_name\r\ncustomer_id := customer_id\r\ncall_list_next_step := list_datenext_call\r\ncall_description := call_description\r\ncall_next_step_comment := list_coment\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := bgservice\r\nmethod := callListMeetings', NULL, 0, 0, 1);

########################################################################
# 2016-11-02 - Added campaigns calls dashlet plugin to the CREDITINS installation (Bug 4024)

# PRE-DEPLOYED # Added campaigns calls dashlet plugin to the CREDITINS installation (Bug 4024)
# INSERT INTO `dashlets_plugins` (`id`, `type`, `settings`, `is_portal`, `visible`) VALUES
#     (NULL, 'campaign_calls', NULL, '0', '1');
# INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Обаждания по кампании', NULL, 'bg'),
#   (LAST_INSERT_ID(), 'Campaign calls', NULL, 'en');

########################################################################
# 2016-12-01 - Added new automation to calculate penalty interest for repayment schedules in CreditIns installation (CREDITINS)

# Added new automation to calculate penalty interest for repayment schedules in CreditIns installation (CREDITINS)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Изчисляване на наказателна лихва', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 01:00\r\nstart_before := 03:00\r\n\r\nnom_interest_type := 12\r\nnom_interest_percent_bnb := interest_percent_bnb\r\nnom_interest_percent_other := interest_percent_other', 'condition := 1', 'plugin := creditins\r\nmethod := calculatePenaltyInterest', NULL, 1, 0, 1);

########################################################################
# 2016-12-08 - Added new crontab automation to distribute payments from the previous day to respective repayment schedules in CreditIns installation (CREDITINS)

# Added new crontab automation to distribute payments from the previous day to respective repayment schedules in CreditIns installation (CREDITINS)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Разпределение на плащания към погасителни планове', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 00:30\r\nstart_before := 01:00\r\n\r\nincomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\n\r\nsend_to_email :=', 'condition := 1', 'plugin := creditins\r\nmethod := distributePaymentsByRepaymentSchedule', NULL, 1, 0, 1);

########################################################################
# 2016-12-13 - Added additional settings for calculatePenaltyInterest automation for the incomes reason related to repaymnet schedule in CreditIns installation (CREDITINS)

# Added additional settings for calculatePenaltyInterest automation for rleated incomes reason in CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nincomes_reason_type_id := 104\r\nnom_penalty_interest_id := 54') WHERE `method` LIKE '%calculatePenaltyInterest%' AND `settings` NOT LIKE '%nom_penalty_interest_id%';

########################################################################
# 2017-01-31 - Added additional settings for calculatePenaltyInterest automation for CKR interest in CreditIns installation (CREDITINS)

# Added additional settings for calculatePenaltyInterest automation for CKR interest in CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ndocument_ckr_interest_total := penalty_int_ckr') WHERE `method` LIKE '%calculatePenaltyInterest%' AND `settings` NOT LIKE '%document_ckr_interest_total%';

########################################################################
# 2017-02-14 - Added new automation for importing customers and contracts from CreditIns website to CreditIns nZoom installation (CREDITINS)

# Added new automation for importing customers and contracts from CreditIns website to CreditIns nZoom installation (CREDITINS)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Импортиране на договори', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'atm_start_time := 08:00\r\natm_start_before := 19:00\r\natm_interval := T1H\r\n\r\nclient_type_id := 3\r\n\r\nclient_relative_type := bond_type\r\nclient_relative_name := c_person_name\r\nclient_relative_lastname := c_person_name_f\r\nclient_relative_address := c_person_address\r\nclient_relative_phone := c_person_phone\r\nclient_relative_email := c_person_email\r\nclient_workplace := workplace\r\nclient_credit_ins_id := credit_ins_id\r\nclient_report_cpr := code_record\r\nclient_last_export := last_export\r\nclient_borrower_type := borrower_type\r\nclient_tag_declined := 2\r\nclient_tag_for_export := 3\r\nclient_tag_invalid_ucn := 7\r\n\r\ndocument_contract_type_id := 6\r\ndocument_contract_status_active := 22\r\ndocument_contract_central_office := 1\r\ndocument_contract_tag_export := 5\r\ndocument_old_contract := 8\r\n\r\ndocument_contract_franchise_partner := franchise_partner\r\ndocument_contract_credit_product := credit_product\r\ndocument_contract_credit_product_id := credit_product_id\r\ndocument_contract_application_method := application_method\r\ndocument_contract_grant_credit := grant_credit\r\ndocument_contract_interest := interest_rate_annual\r\ndocument_contract_gpr := gpr\r\ndocument_contract_type_reporting_period := type_reporting_period\r\ndocument_contract_repayment_period := repayment_period\r\n\r\ndocument_declined_request_type_id := 14\r\ndocument_declined_request_status_declined := 19\r\n\r\ndocument_declined_request_rejection_date := date_rejection\r\ndocument_declined_request_num := number_from_site\r\ndocument_declined_request_amount := amount_requested\r\n\r\ndocument_contract_application_method_credit_ins_option := 1\r\ndocument_contract_period_month_option := 1\r\n\r\ncustomer_employee_credit_ins_username := name_site\r\n\r\nnom_type_credit_product := 5\r\nnom_type_credit_product_gpr := gpr\r\n\r\nfir_type_incomes_reason_loan_contract_type := 104\r\nfir_type_incomes_reason_loan_contract_company := 1\r\nfir_type_incomes_reason_loan_contract_office := 1\r\nfir_type_incomes_reason_loan_contract_payment_type := bank\r\nfir_type_incomes_reason_loan_contract_payment_container := 1\r\nfir_type_incomes_reason_loan_contract_article_measure := 1\r\nfir_type_incomes_reason_loan_contract_article_id := 57\r\n\r\ncustomers_connection_link := https://admin.creditins.bg/api/clients.aspx?access_token=1b0dbba7-5bce-4eeb-8ebb-05371df634a5&created_at_from=%s&created_at_to=%s\r\ncontracts_connection_link := https://admin.creditins.bg/api/contracts.aspx?access_token=1b0dbba7-5bce-4eeb-8ebb-05371df634a5&requested_at_from=%s&requested_at_to=%s\r\n\r\ncredit_status_rejected := 3\r\ncredit_status_approved := 5\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := creditins\r\nmethod := importContracts', NULL, 1, 0, 1);

########################################################################
# 2017-02-15 - Deactivate automation importContracts by default and change the period for execution with correct time format for CreditIns nZoom installation (CREDITINS)

# Deactivate automation importContracts by default and change the period for execution with correct time format for CreditIns nZoom installation (CREDITINS)
UPDATE `automations` SET `active`=0, `settings`=REPLACE(`settings`, 'atm_interval := T1H', 'atm_interval := PT1H') WHERE `method` LIKE '%importContracts%' AND `settings` LIKE '%atm_interval := T1H%';

########################################################################
# 2017-02-16 - Added new setting for padi contract status for importContracts automation for CreditIns nZoom installation (CREDITINS)

# Added new setting for padi contract status for importContracts automation for CreditIns nZoom installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\ndocument_contract_central_office := 1', '\r\ndocument_contract_status_paid := 20\r\ndocument_contract_central_office := 1') WHERE `method` LIKE '%importContracts%' AND `settings` NOT LIKE '%document_contract_status_paid%';

########################################################################
# 2017-03-10 - Added new report 'creditins_ckr_export' which will be used for exporting files to Central Credit Register in CreditIns installation (CREDITINS)
#            - Added new automation for CreditIns installation (CREDITINS) which will generate a summary file for Central Credit Register once each month

# Added new report 'creditins_ckr_export' which will be used for exporting files to Central Credit Register in CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (358, 'creditins_ckr_export', 'skip_session_filters := 1\r\n\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\n\r\ntag_contract_export := 5\r\ntag_contract_reexport := 6\r\n\r\nclient_type_id := 3\r\nclient_borrower_type := borrower_type\r\n\r\nowned_company_type_id := 7\r\nowned_company_code_declaring_unit := code_bae\r\nowned_company_credit_ins_id := 352\r\n\r\ndocument_contract_type_id := 6\r\ndocument_contract_credit_product := credit_product_id\r\ndocument_contract_grant_credit := grant_credit\r\ndocument_yearly_interest := interest_rate_annual\r\ndocument_ckr_penalty := penalty_int_ckr\r\n\r\nnom_product_type_id := 5\r\nnom_product_code := code_record', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (358, 'Експорт към ЦКР', '', NULL, 'bg'),
  (358, 'CKR export', '', NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '358', '0', '1'),
  ('reports', 'export', '358', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '358';

# PRE-DEPLOYED # Added new automation for CreditIns installation (CREDITINS) which will generate a summary file for Central Credit Register once each month
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#('Създаване на CUCR файл за ЦКР', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_month_date := last_date\r\nstart_time := 23:30\r\nstart_before := 23:59\r\n\r\nowned_company_type_id := 7\r\nowned_company_code_declaring_unit := code_bae\r\nowned_company_credit_ins_id := 352\r\n\r\ndocument_contract_type_id := 6\r\ndocument_contract_grant_credit := grant_credit\r\ndocument_yearly_interest := interest_rate_annual\r\ndocument_ckr_penalty := penalty_int_ckr\r\ndocument_date_payment := date_payment_doc\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := creditins\r\nmethod := createMonthCucrFile', NULL, 1, 0, 1);

########################################################################
# 2017-03-20 - Added new automation 'changeContractToProceeding' will change the status of a loan agreement when it is marked as proceedinf and will issue a correction for the incomes reason in CreditIns installation (CREDITINS)

# PRE-DEPLOYED # Added new automation 'changeContractToProceeding' will change the status of a loan agreement when it is marked as proceedinf and will issue a correction for the incomes reason in CreditIns installation (CREDITINS)
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#('Преминаване на договор в Съдебно производство', 0, NULL, 1, 'projects', NULL, 'action', '1', 'contract_num := contract_num_id\r\nnew_document_status := closed\r\nnew_document_substatus := 23\r\nincomes_reason_for_contract_type_id := 104', 'condition := ''[a_contract_num_id]'' != '''' && ''[prev_a_contract_num_id]'' != ''[a_contract_num_id]''', 'plugin := creditins\r\nmethod := changeContractToProceeding', NULL, 1, 1, 1);

########################################################################
# 2017-03-21 - Added new setting dor proceeding substatus which marks the substatus which the documents will not be distribute payments in distributePaymentsByRepaymentSchedule automation for CreditIns nZoom installation (CREDITINS)
#            - Added two new settings for ignoring documents when calculating penalty interest depending on their tags or theri status in calculatePenaltyInterest automation in CreditIns nZoom installation (CREDITINS)

# Added new setting dor proceeding substatus which marks the substatus which the documents will not be distribute payments in distributePaymentsByRepaymentSchedule automation for CreditIns nZoom installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nschedule_var_gt2_id :=', '\r\ndocument_substatus_proceeding := 23\r\n\r\nschedule_var_gt2_id :=') WHERE `method` LIKE '%distributePaymentsByRepaymentSchedule%' AND `settings` NOT LIKE '%document_substatus_proceeding%';

# Added two new settings for ignoring documents when calculating penalty interest depending on their tags or theri status in calculatePenaltyInterest automation in CreditIns nZoom installation (CREDITINS)
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nignore_tags := 8\r\nignore_statuses := closed_20, closed_23') WHERE `method` LIKE '%calculatePenaltyInterest%' AND `settings` NOT LIKE '%ignore_tags%';

########################################################################
# 2017-03-22 - Added new setting for changeContractToProceeding automation to contain the id of a special nomenclature 'proceeding equalization' in CreditIns installation (CREDITINS)

# Added new setting for changeContractToProceeding automation to contain the id of a special nomenclature 'proceeding equalization' in CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nproceeding_equalize_article_id := 103') WHERE `method` LIKE '%changeContractToProceeding%' AND `settings` NOT LIKE '%proceeding_equalize_article_id%';

########################################################################
# 2017-03-28 - Added new automation which will create incomes and expenses reasons when certain fields in the proceeding project has benn filled (and correct them when they are changed) in CreditIns installation (CREDITINS)
#            - Added new automation which will not allow changing a field in the proceeding project which will lead to correction that will make the realted reason overpaid in in CreditIns installation (CREDITINS)

# PRE-DEPLOYED # Added new automation which will create incomes and expenses reasons when certain fields in the proceeding project has benn filled (and correct them when they are changed) in CreditIns installation (CREDITINS)
# Added new automation which will not allow changing a field in the proceeding project which will lead to correction that will make the realted reason overpaid in in CreditIns installation (CREDITINS)
#INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#('Създаване на ОП и ПР за разходи дело', 0, NULL, 1, 'projects', NULL, 'action', '1', 'process_settings := left_principal, left_interest, left_warranty, left_official_interest_p444, left_official_interest, lawyer_tax, state_tax, lawyer_enforce, state_enforce, lawyer_claim, state_claim, expert_tax\r\n\r\nleft_principal_value_var := principal\r\nleft_interest_value_var := interest\r\nleft_warranty_value_var := warranty\r\nleft_official_interest_p444_value_var := legal_interest_according\r\nleft_official_interest_value_var := legal_interest\r\nlawyer_tax_value_var := lawyer_imperatives__amount\r\nstate_tax_value_var := statetax_imper__amount\r\nlawyer_enforce_value_var := enforcement_advocate__amount\r\nstate_enforce_value_var := state_enforce__amount\r\nlawyer_claim_value_var := claim_jurist__amount\r\nstate_claim_value_var := tax_claim__amount\r\nexpert_tax_value_var := expert_tax__amount\r\n\r\nleft_principal_price_var := principal\r\nleft_interest_price_var := interest\r\nleft_warranty_price_var := warranty\r\nleft_official_interest_p444_price_var := legal_interest_according\r\nleft_official_interest_price_var := legal_interest\r\nlawyer_tax_price_var := lawyer_imperatives__amount\r\nstate_tax_price_var := statetax_imper__amount\r\nlawyer_enforce_price_var := enforcement_advocate__amount\r\nstate_enforce_price_var := state_enforce__amount\r\nlawyer_claim_price_var := claim_jurist__amount\r\nstate_claim_price_var := tax_claim__amount\r\nexpert_tax_price_var := expert_tax__amount\r\n\r\nout_of_config_date_var := date_start\r\nlawyer_tax_date_var := lawyer_imperatives__date\r\nstate_tax_date_var := statetax_imper__date\r\nlawyer_enforce_date_var := enforcement_advocate__date\r\nstate_enforce_date_var := state_enforce__date\r\nlawyer_claim_date_var := claim_jurist__date\r\nstate_claim_date_var := tax_claim__date\r\nexpert_tax_date_var := expert_tax__date\r\n\r\nleft_principal_article_id := 104\r\nleft_interest_article_id := 105\r\nleft_warranty_article_id := 106\r\nleft_official_interest_p444_article_id := 107\r\nleft_official_interest_article_id := 108\r\nlawyer_tax_article_id := 96\r\nstate_tax_article_id := 28\r\nlawyer_enforce_article_id := 97\r\nstate_enforce_article_id := 50\r\nlawyer_claim_article_id := 98\r\nstate_claim_article_id := 51\r\nexpert_tax_article_id := 102\r\n\r\nleft_principal_create_reason := Finance_Incomes_Reason_103\r\nleft_interest_create_reason := Finance_Incomes_Reason_103\r\nleft_warranty_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_p444_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_create_reason := Finance_Incomes_Reason_103\r\nlawyer_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nexpert_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\n\r\ndefault_measure_option := 1\r\n\r\nlawyer_tax_expense_reason_customer := 12\r\nlawyer_enforce_expense_reason_customer := 12\r\nlawyer_claim_expense_reason_customer := private_judge\r\nexpert_tax_expense_reason_customer := expert\r\ndefault_expense_reason_customer := 1\r\n\r\nreason_income := 103\r\nreason_expense := 101\r\nreason_expense_state_tax := 105\r\n\r\nreason_income_103_company_id := 1\r\nreason_income_103_office_id := 1\r\nreason_income_103_payment_type := bank\r\nreason_income_103_container_id := 1\r\n\r\nreason_expense_101_company_id := 1\r\nreason_expense_101_office_id := 1\r\nreason_expense_101_payment_type := bank\r\nreason_expense_101_container_id := 1\r\n\r\nreason_expense_105_company_id := 1\r\nreason_expense_105_office_id := 1\r\nreason_expense_105_payment_type := bank\r\nreason_expense_105_container_id := 1', 'condition := (''[a_left_principal]'' != ''[prev_a_left_principal]'' && ''[a_left_principal]'' != '''' && ''[a_left_principal]'' != ''0'') || (''[a_left_interest]'' != ''[prev_a_left_interest]'' && ''[a_left_interest]'' != '''' && ''[a_left_interest]'' != ''0'') || (''[a_left_warranty]'' != ''[prev_a_left_warranty]'' && ''[a_left_warranty]'' != '''' && ''[a_left_warranty]'' != ''0'') || (''[a_left_official_interest_p444]'' != ''[prev_a_left_official_interest_p444]'' && ''[a_left_official_interest_p444]'' != '''' && ''[a_left_official_interest_p444]'' != ''0'') || (''[a_left_official_interest]'' != ''[prev_a_left_official_interest]'' && ''[a_left_official_interest]'' != '''' && ''[a_left_official_interest]'' != ''0'') || (''[a_lawyer_imperatives__amount]'' != ''[prev_a_lawyer_imperatives__amount]'' && ''[a_lawyer_imperatives__amount]'' != '''' && ''[a_lawyer_imperatives__amount]'' != ''0'') || (''[a_statetax_imper__amount]'' != ''[prev_a_statetax_imper__amount]'' && ''[a_statetax_imper__amount]'' != '''' && ''[a_statetax_imper__amount]'' != ''0'') || (''[a_enforcement_advocate__amount]'' != ''[prev_a_enforcement_advocate__amount]'' && ''[a_enforcement_advocate__amount]'' != '''' && ''[a_enforcement_advocate__amount]'' != ''0'') || (''[a_state_enforce__amount]'' != ''[prev_a_state_enforce__amount]'' && ''[a_state_enforce__amount]'' != '''' && ''[a_state_enforce__amount]'' != ''0'') || (''[a_claim_jurist__amount]'' != ''[prev_a_claim_jurist__amount]'' && ''[a_claim_jurist__amount]'' != '''' && ''[a_claim_jurist__amount]'' != ''0'') || (''[a_tax_claim__amount]'' != ''[prev_a_tax_claim__amount]'' && ''[a_tax_claim__amount]'' != '''' && ''[a_tax_claim__amount]'' != ''0'') || (''[a_expert_tax__amount]'' != ''[prev_a_expert_tax__amount]'' && ''[a_expert_tax__amount]'' != '''' && ''[a_expert_tax__amount]'' != ''0'')', 'plugin := creditins\r\nmethod := createIncomesAndExpensesForProceeding', NULL, 1, 0, 1),
#('Валидация на сумите в свързаните основания', 0, NULL, 1, 'projects', NULL, 'before_action', '1', 'process_settings := left_principal, left_interest, left_warranty, left_official_interest_p444, left_official_interest, lawyer_tax, state_tax, lawyer_enforce, state_enforce, lawyer_claim, state_claim, expert_tax\r\n\r\nleft_principal_value_var := principal\r\nleft_interest_value_var := interest\r\nleft_warranty_value_var := warranty\r\nleft_official_interest_p444_value_var := legal_interest_according\r\nleft_official_interest_value_var := legal_interest\r\nlawyer_tax_value_var := lawyer_imperatives__amount\r\nstate_tax_value_var := statetax_imper__amount\r\nlawyer_enforce_value_var := enforcement_advocate__amount\r\nstate_enforce_value_var := state_enforce__amount\r\nlawyer_claim_value_var := claim_jurist__amount\r\nstate_claim_value_var := tax_claim__amount\r\nexpert_tax_value_var := expert_tax__amount\r\n\r\nleft_principal_article_id := 104\r\nleft_interest_article_id := 105\r\nleft_warranty_article_id := 106\r\nleft_official_interest_p444_article_id := 107\r\nleft_official_interest_article_id := 108\r\nlawyer_tax_article_id := 96\r\nstate_tax_article_id := 28\r\nlawyer_enforce_article_id := 97\r\nstate_enforce_article_id := 50\r\nlawyer_claim_article_id := 98\r\nstate_claim_article_id := 51\r\nexpert_tax_article_id := 102\r\n\r\nleft_principal_create_reason := Finance_Incomes_Reason_103\r\nleft_interest_create_reason := Finance_Incomes_Reason_103\r\nleft_warranty_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_p444_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_create_reason := Finance_Incomes_Reason_103\r\nlawyer_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nexpert_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101', 'condition := (''[action]'' == ''add'' || ''[action]'' == ''edit'')  && ''[request_is_post]'' == ''1''', 'plugin := creditins\r\nmethod := validateReasonsChange', 'cancel_action_on_fail := 1', 0, 0, 1);

########################################################################
# 2017-04-07 - Added new report 'creditins_customer_file' for CreditIns installation (CREDITINS)

# Added new report 'creditins_customer_file' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (361, 'creditins_customer_file', 'customer_type_client := 3\r\n\r\ncustomer_client_workplace := workplace\r\ncustomer_client_bond := bond_type\r\ncustomer_client_name := c_person_name\r\ncustomer_client_family_name := c_person_name_f\r\ncustomer_client_address := c_person_address\r\ncustomer_client_phone := c_person_phone\r\ncustomer_client_email := c_person_email\r\n\r\nproject_proceeding_type := 1\r\n\r\ndocument_type_id := 6\r\ndocument_contract_deadline := repayment_period\r\ndocument_contract_credit := grant_credit\r\ndocument_status_active := opened\r\ndocument_substatus_active := 22', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (361, 'Досие на клиент', '', NULL, 'bg'),
  (361, 'Client file', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '361', '0', '1'),
  ('reports', 'export', '361', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '361';

########################################################################
# 2017-04-11 - Fixed settings for CreditIns customer and added setting for last export var name in 'creditins_ckr_export' for CreditIns installation (CREDITINS)
#            - Fixed settings and conditions of 'createIncomesAndExpensesForProceeding' automation for CreditIns installation (CREDITINS)

# Fixed settings for CreditIns customer and added setting for last export var name in 'creditins_ckr_export' for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'owned_company_credit_ins_id := 352', 'owned_company_credit_ins_id := 1') WHERE `type`='creditins_ckr_export' AND `settings` LIKE '%owned_company_credit_ins_id := 352%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'client_borrower_type := borrower_type', 'client_borrower_type := borrower_type\r\nclient_last_export := last_export') WHERE `type`='creditins_ckr_export' AND `settings` NOT LIKE '%client_last_export%';

# Fixed settings and conditions of 'createIncomesAndExpensesForProceeding' automation for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`='process_settings := left_principal, left_interest, left_warranty, left_official_interest_p444, left_official_interest, lawyer_tax, state_tax, lawyer_enforce, state_enforce, lawyer_claim, state_claim, expert_tax\r\n\r\nleft_principal_value_var := principal\r\nleft_interest_value_var := interest\r\nleft_warranty_value_var := warranty\r\nleft_official_interest_p444_value_var := legal_interest_according\r\nleft_official_interest_value_var := legal_interest\r\nlawyer_tax_value_var := lawyer_imperatives__amount\r\nstate_tax_value_var := statetax_imper__amount\r\nlawyer_enforce_value_var := enforcement_advocate__amount\r\nstate_enforce_value_var := state_enforce__amount\r\nlawyer_claim_value_var := claim_jurist__amount\r\nstate_claim_value_var := tax_claim__amount\r\nexpert_tax_value_var := expert_tax__amount\r\n\r\nleft_principal_price_var := principal\r\nleft_interest_price_var := interest\r\nleft_warranty_price_var := warranty\r\nleft_official_interest_p444_price_var := legal_interest_according\r\nleft_official_interest_price_var := legal_interest\r\nlawyer_tax_price_var := lawyer_imperatives__amount\r\nstate_tax_price_var := statetax_imper__amount\r\nlawyer_enforce_price_var := enforcement_advocate__amount\r\nstate_enforce_price_var := state_enforce__amount\r\nlawyer_claim_price_var := claim_jurist__amount\r\nstate_claim_price_var := tax_claim__amount\r\nexpert_tax_price_var := expert_tax__amount\r\n\r\nout_of_config_date_var := date_start\r\nlawyer_tax_date_var := lawyer_imperatives__date\r\nstate_tax_date_var := statetax_imper__date\r\nlawyer_enforce_date_var := enforcement_advocate__date\r\nstate_enforce_date_var := state_enforce__date\r\nlawyer_claim_date_var := claim_jurist__date\r\nstate_claim_date_var := tax_claim__date\r\nexpert_tax_date_var := expert_tax__date\r\n\r\nleft_principal_article_id := 104\r\nleft_interest_article_id := 105\r\nleft_warranty_article_id := 106\r\nleft_official_interest_p444_article_id := 107\r\nleft_official_interest_article_id := 108\r\nlawyer_tax_article_id := 96\r\nstate_tax_article_id := 28\r\nlawyer_enforce_article_id := 97\r\nstate_enforce_article_id := 50\r\nlawyer_claim_article_id := 98\r\nstate_claim_article_id := 51\r\nexpert_tax_article_id := 102\r\n\r\nleft_principal_create_reason := Finance_Incomes_Reason_103\r\nleft_interest_create_reason := Finance_Incomes_Reason_103\r\nleft_warranty_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_p444_create_reason := Finance_Incomes_Reason_103\r\nleft_official_interest_create_reason := Finance_Incomes_Reason_103\r\nlawyer_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_enforce_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nlawyer_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\nstate_claim_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_105\r\nexpert_tax_create_reason := Finance_Incomes_Reason_103, Finance_Expenses_Reason_101\r\n\r\ndefault_measure_option := 1\r\n\r\nlawyer_tax_expense_reason_customer := 12\r\nlawyer_enforce_expense_reason_customer := 12\r\nlawyer_claim_expense_reason_customer := private_judge_id\r\nexpert_tax_expense_reason_customer := expert\r\nstate_tax_expense_reason_customer := court_id\r\nstate_enforce_expense_reason_customer := court_id\r\nstate_claim_expense_reason_customer := court_id\r\ndefault_expense_reason_customer := 1\r\n\r\nreason_income := 103\r\nreason_expense := 101\r\nreason_expense_state_tax := 105\r\n\r\nreason_income_103_company_id := 1\r\nreason_income_103_office_id := 1\r\nreason_income_103_payment_type := bank\r\nreason_income_103_container_id := 1\r\n\r\nreason_expense_101_company_id := 1\r\nreason_expense_101_office_id := 1\r\nreason_expense_101_payment_type := bank\r\nreason_expense_101_container_id := 1\r\n\r\nreason_expense_105_company_id := 1\r\nreason_expense_105_office_id := 1\r\nreason_expense_105_payment_type := bank\r\nreason_expense_105_container_id := 1', `conditions`='condition := (''[a_principal]'' != ''[prev_a_principal]'' && ''[a_principal]'' != '''' && ''[a_principal]'' != ''0'') || (''[a_interest]'' != ''[prev_a_interest]'' && ''[a_interest]'' != '''' && ''[a_interest]'' != ''0'') || (''[a_warranty]'' != ''[prev_a_warranty]'' && ''[a_warranty]'' != '''' && ''[a_warranty]'' != ''0'') || (''[a_legal_interest_according]'' != ''[prev_a_legal_interest_according]'' && ''[a_legal_interest_according]'' != '''' && ''[a_legal_interest_according]'' != ''0'') || (''[a_legal_interest]'' != ''[prev_a_legal_interest]'' && ''[a_legal_interest]'' != '''' && ''[a_legal_interest]'' != ''0'') || (''[a_lawyer_imperatives__amount]'' != ''[prev_a_lawyer_imperatives__amount]'' && ''[a_lawyer_imperatives__amount]'' != '''' && ''[a_lawyer_imperatives__amount]'' != ''0'') || (''[a_statetax_imper__amount]'' != ''[prev_a_statetax_imper__amount]'' && ''[a_statetax_imper__amount]'' != '''' && ''[a_statetax_imper__amount]'' != ''0'') || (''[a_enforcement_advocate__amount]'' != ''[prev_a_enforcement_advocate__amount]'' && ''[a_enforcement_advocate__amount]'' != '''' && ''[a_enforcement_advocate__amount]'' != ''0'') || (''[a_state_enforce__amount]'' != ''[prev_a_state_enforce__amount]'' && ''[a_state_enforce__amount]'' != '''' && ''[a_state_enforce__amount]'' != ''0'') || (''[a_claim_jurist__amount]'' != ''[prev_a_claim_jurist__amount]'' && ''[a_claim_jurist__amount]'' != '''' && ''[a_claim_jurist__amount]'' != ''0'') || (''[a_tax_claim__amount]'' != ''[prev_a_tax_claim__amount]'' && ''[a_tax_claim__amount]'' != '''' && ''[a_tax_claim__amount]'' != ''0'') || (''[a_expert_tax__amount]'' != ''[prev_a_expert_tax__amount]'' && ''[a_expert_tax__amount]'' != '''' && ''[a_expert_tax__amount]'' != ''0'')' WHERE `method` LIKE '%createIncomesAndExpensesForProceeding%' AND `conditions` LIKE '%left_principal%';

########################################################################
# 2017-04-12 - Added test mode for CUCR files in 'creditins_ckr_export' report for CreditIns installation (CREDITINS)

# Added test mode for CUCR files in 'creditins_ckr_export' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncucr_test_mode := 0') WHERE `type`='creditins_ckr_export' AND `settings` NOT LIKE '%cucr_test_mode%';

########################################################################
# 2017-04-13 - Added additional settings 'createIncomesAndExpensesForProceeding' automation for status and tag which will be marked on the contract after full payment for CreditIns installation (CREDITINS)

# Added additional settings 'createIncomesAndExpensesForProceeding' automation for status and tag which will be marked on the contract after full payment for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'schedule_var_penalty := penalty_interest', 'schedule_var_penalty := penalty_interest\r\n\r\ntag_reexport := 6\r\nstatus_fully_paid := closed_20') WHERE `method` LIKE '%distributePaymentsByRepaymentSchedule%' AND `settings` NOT LIKE '%status_fully_paid%';

########################################################################
# 2017-04-20 - Added settings for tag, paid status and related incomes reason type in 'creditins_customer_file' report for CreditIns installation (CREDITINS)

# Added settings for tag, paid status and related incomes reason type in 'creditins_customer_file' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ndocument_full_paid_status := closed_20\r\ndocument_full_paid_tag := 6\r\n\r\nincomes_reason_type_id := 104') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_full_paid_status%';

########################################################################
# 2017-04-26 - The setting for penalty is replaced with setting for the nomenclature type for owed sum by contract in calculatePenaltyInterest automation for CreditIns installation (CREDITINS)

# The setting for penalty is replaced with setting for the nomenclature type for owed sum by contract in calculatePenaltyInterest automation for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'nom_penalty_interest_id := 54', 'contract_owed_sum_id := 57') WHERE `method` LIKE '%calculatePenaltyInterest%' AND `settings` LIKE '%nom_penalty_interest_id%';

########################################################################
# 2017-05-04 - Added two new panels 'Credits' and 'CKR/NOI report' and few corresponding settings in creditins_customer_file for CreditIns installation (CREDITINS)

# Added two new panels 'Credits' and 'CKR/NOI report' and few corresponding settings in creditins_customer_file for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'document_substatus_active := 22\r\n', 'document_substatus_paid := 20\r\ndocument_substatus_annuled := 21\r\ndocument_substatus_active := 22\r\n') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_substatus_paid%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'document_contract_credit := grant_credit\r\n', 'document_contract_credit := grant_credit\r\ndocument_contract_credit_product := credit_product_id\r\ndocument_contract_application_method := application_method\r\ndocument_contract_annual_interest_rate := interest_rate_annual\r\ndocument_contract_period := type_reporting_period\r\n') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_contract_annual_interest_rate%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ncustomer_client_email := c_person_email', '\r\ncustomer_client_email := c_person_email\r\ncustomer_client_ckr_noi_file := files_info\r\ncustomer_client_ckr_report_type := report_kind\r\ncustomer_client_ckr_report_date := report_date\r\ncustomer_client_ckr_report_notes := additional_info\r\ncustomer_client_ckr_option := 1\r\ncustomer_client_noi_option := 2') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%customer_client_ckr_noi_file%';

########################################################################
# 2017-05-12 - Added two new automation to distribute the payment to the related repayment plan when the payment is marked as finished for CreditIns installation (CREDITINS)

# Added two new automation to distribute the payment to the related repayment plan when the payment is marked as finished for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
(NULL, 'Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'PKO', 'incomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\ndocument_substatus_proceeding := 23\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\n\r\ntag_reexport := 6\r\nstatus_fully_paid := closed_20', 'condition := \'[b_status]\' == \'finished\' && \'[b_status]\' != \'[prev_b_status]\'', 'plugin := creditins\r\nmethod := distributePaymentByRepaymentSchedule', NULL, 1, 1, 0),
(NULL, 'Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'BP', 'incomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\ndocument_substatus_proceeding := 23\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\n\r\ntag_reexport := 6\r\nstatus_fully_paid := closed_20', 'condition := \'[b_status]\' == \'finished\' && \'[b_status]\' != \'[prev_b_status]\'', 'plugin := creditins\r\nmethod := distributePaymentByRepaymentSchedule', NULL, 1, 1, 0);

########################################################################
# 2017-05-17 - Added a new panel 'Communications with the client' and few corresponding settings in 'creditins_customer_file' for CreditIns installation (CREDITINS)

# Added a new panel 'Communications with the client' and few corresponding settings in 'creditins_customer_file' for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_status_active', '\r\ndocument_contract_early_repayment_date := early_repayment_date\r\ndocument_status_active') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_contract_early_repayment_date%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nincomes_reason_type_id', '\r\n\r\ndocument_calls_type_id := 12\r\ndocument_calls_date := list_to_date\r\ndocument_calls_customer := customer_id\r\n\r\nincomes_reason_type_id') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_calls_type_id%';

########################################################################
# 2017-05-22 - Added two new automation to distribute the payment to the related repayment plan when the payment is added from an incomes reason CreditIns installation (CREDITINS)

# Added two new automation to distribute the payment to the related repayment plan when the payment is added from an incomes reason CreditIns installation (CREDITINS)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'incomes_reasons', 'action', '104', 'incomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\ndocument_substatus_proceeding := 23\r\npayment_types = BP, PKO\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\n\r\ntag_reexport := 6\r\nstatus_fully_paid := closed_20', 'condition := \'[action]\' == \'addpayment\' && \'[request_is_post]\' == \'1\'', 'plugin := creditins\r\nmethod := distributePaymentByRepaymentSchedule', NULL, 1, 0, 0);

########################################################################
# 2017-05-29 - Edit the js methods for the visible fields of the GT2 table in the Repayment plan document in CreditIns installation (CREDITINS)
#            - Update the setttings of the 'creditins_customer_file' report to include the fields of the grouping table for distributed payments and the reexport tag

# Edit the js methods for the visible fields of the GT2 table in the Repayment plan document in CreditIns installation (CREDITINS)
UPDATE `_fields_meta` SET `source`='agregate := sum\ntext_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => crow = this.id.replace(/.*_(\\d+)/, \'$1\'); princ = $(\'price_\' + crow).value ? parseFloat($(\'price_\' + crow).value) : 0; inter = $(\'quantity_\' + crow).value ? parseFloat($(\'quantity_\' + crow).value) : 0; warr = $(\'article_trademark_\' + crow).value ? parseFloat($(\'article_trademark_\' + crow).value) : 0; inter_plus_warr = $(\'last_delivery_price_\' + crow).value ? parseFloat($(\'last_delivery_price_\' + crow).value) : 0; penalty = $(\'article_delivery_code_\' + crow).value ? parseFloat($(\'article_delivery_code_\' + crow).value) : 0; total_payment = $(\'article_second_code_\' + crow).value ? parseFloat($(\'article_second_code_\' + crow).value) : 0; princ_paid = $(\'discount_value_\' + crow).value ? parseFloat($(\'discount_value_\' + crow).value) : 0; inter_paid = $(\'article_barcode_\' + crow).value ? parseFloat($(\'article_barcode_\' + crow).value) : 0; warr_paid = $(\'article_height_\' + crow).value ? parseFloat($(\'article_height_\' + crow).value) : 0; inter_plus_warr_paid = $(\'article_width_\' + crow).value ? parseFloat($(\'article_width_\' + crow).value) : 0; penalty_paid = $(\'article_weight_\' + crow).value ? parseFloat($(\'article_weight_\' + crow).value) : 0; total_payment_paid = $(\'article_volume_\' + crow).value ? parseFloat($(\'article_volume_\' + crow).value) : 0; ipw = inter + warr; tp = princ + ipw + penalty; ipwp = inter_paid + warr_paid; tpp = princ_paid + ipwp + penalty_paid; pl = princ-princ_paid; il = inter-inter_paid; wl = warr-warr_paid; ipwl = ipw-ipwp; penl = penalty-penalty_paid; tpl = tp - tpp; $(\'last_delivery_price_\' + crow).value = ipw.toFixed(2); $(\'article_second_code_\' + crow).value = tp.toFixed(2); $(\'article_width_\' + crow).value = ipwp.toFixed(2); $(\'article_volume_\' + crow).value = tpp.toFixed(2); $(\'average_weighted_delivery_price_\' + crow).value = pl.toFixed(2); $(\'free_field1_\' + crow).value = il.toFixed(2); $(\'free_field2_\' + crow).value = wl.toFixed(2); $(\'free_field3_\' + crow).value = ipwl.toFixed(2); $(\'free_field4_\' + crow).value = penl.toFixed(2); $(\'free_field5_\' + crow).value = tpl.toFixed(2); gt2calc($(\'price\'));' WHERE `model`='Document' AND `model_type`='6' AND `name` IN ('price','quantity','article_trademark','article_delivery_code','discount_value','article_barcode','article_height','article_weight') AND `source` NOT LIKE '%parseFloat%';

# Update the setttings of the 'creditins_customer_file' report to include the fields of the grouping table for distributed payments and the reexport tag
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%schedule_var_gt2_id%';

########################################################################
# 2017-06-02 - Added new report 'creditins_overdue_payments' for CreditIns installation (CREDITINS)

# Added new report 'creditins_overdue_payments' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (366, 'creditins_overdue_payments', 'contract_document_id := 6\r\ncontract_status_active := 22\r\ncontract_tag_old_contract := 8\r\ncontract_assignment_date := assignment_date\r\ncontract_payment_doc_id := payment_document_id\r\ncontract_payment_date := date_payment_doc\r\ncontract_payment_principal := principal\r\ncontract_payment_warranty := amount_warranty\r\ncontract_payment_interest := contractual_interest\r\ncontract_payment_penalty := penalty_interest\r\n\r\ncustomer_client_type_id := 3\r\ncustomer_contact_person_first_name := c_person_name\r\ncustomer_contact_person_family_name := c_person_name_f\r\ncustomer_contact_person_phone := c_person_phone', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (366, 'Просрочени вземания', '', NULL, 'bg'),
  (366, 'Overdue payments', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '366', '0', '1'),
  ('reports', 'export', '366', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '366';

########################################################################
# 2017-06-12 - Added new functionality for creating proceedings from loan contracts in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# Added new functionality for creating proceedings from loan contracts in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_client_type_id', '\r\ncontract_main_principal := grant_credit\r\ncontract_status_proceeding := 23\r\n\r\ncustomer_client_type_id') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%contract_main_principal%';
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nproject_type_id := 1\r\nproject_customer_ucn := personal_num\r\nproject_proceeding_date := date_proceedings\r\nproject_lawsuit_id := lawsuit_type_id\r\nproject_lawsuit_type := lawsuit_type\r\nproject_contract_status_id := lawsuit_status_id\r\nproject_contract_status := lawsuit_status\r\nproject_contract_num_id := contract_num_id\r\nproject_contract_num := contract_num\r\nproject_contract_date := contract_date\r\nproject_contract_principal := contract_principal\r\nproject_contract_owed_total := total_repayment\r\nproject_contract_date_payable := date_payable\r\nproject_contract_owed_principal := principal\r\nproject_contract_owed_interest := interest\r\nproject_contract_owed_warranty := warranty\r\nproject_contract_owed_penalty := legal_interest_according\r\n\r\nlawsuit_nom_id := 94\r\nlawsuit_status_nom_id := 36') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%project_type_id%';

########################################################################
# 2017-06-14 - Added freezing table headers in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# Added freezing table headers in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nfreeze_table_headers := 1') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%freeze_table_headers%';

########################################################################
# 2017-06-16 - Added automations to validate the annullment of a loan contract and to annull the incomes reason related to the contract in  'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# Added automations to validate the annullment of a loan contract and to annull the incomes reason related to the contract in  'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Анулиране на договор (валидация)', 0, NULL, 1, 'documents', NULL, 'before_action', '6', 'related_incomes_reason_type := 104', 'condition := \'[action]\' == \'setstatus\' && $this->registry->get(\'request\')->get(\'substatus\') == \'closed_21\' && \'[b_substatus]\' != \'21\'', 'plugin := creditins\r\nmethod := validateContractAnnulment', 'cancel_action_on_fail := 1', 1, 0, 1),
('Нулиране на основание за приход при анулиране на договор за заем', 0, NULL, 1, 'documents', NULL, 'action', '6', 'related_incomes_reason_type := 104', 'condition := \'[b_substatus]\' == \'21\' && \'[b_status]\' != \'[prev_b_status]\'', 'plugin := creditins\r\nmethod := annulIncomesReasonWhenAnnulContract', NULL, 1, 1, 1);

########################################################################
# 2017-06-23 - Added new fields to be completed in the project when creating one from 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# PRE-DEPLOYED # Added new fields to be completed in the project when creating one from 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
#UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_client_type_id', '\r\ncontract_repayment_period := repayment_period\r\n\r\ncustomer_client_type_id') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%contract_repayment_period%';
#UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nproject_type_id', '\r\ncustomer_client_workplace := workplace\r\n\r\nproject_type_id') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%customer_client_workplace %';
#UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nlawsuit_nom_id', '\r\nproject_repayment_period := repayment_period\r\nproject_all_deadlines := maturity_contributions\r\nproject_overdue_deadlines := overdue_maturoty_contributions\r\n\r\nlawsuit_nom_id') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%project_repayment_period%';

########################################################################
# 2017-06-28 - Added new settings for 'creditins_customer_file' report for CreditIns installation (CREDITINS) to contain the tag for declined customers

# Added new settings for 'creditins_customer_file' report for CreditIns installation (CREDITINS) to contain the tag for declined customers
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nproject_proceeding_type', '\r\ncustomer_client_tag_declined := 2\r\n\r\nproject_proceeding_type') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%customer_client_tag_declined%';

########################################################################
# 2017-06-29 - Added additional settings for 'creditins_overdue_payments' report for CreditIns installation (CREDITINS) to the related incomes reason type and the nomenclature for equalizing proceeding sum

# Added additional settings for 'creditins_overdue_payments' report for CreditIns installation (CREDITINS) to the related incomes reason type and the nomenclature for equalizing proceeding sum
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nincomes_reason_type_id := 104\r\nproceeding_equalize_article_id := 103') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%incomes_reason_type_id%';

########################################################################
# 2017-07-07 - Added new report 'creditins_payments_per_period' for CreditIns installation (CREDITINS)

# Added new report 'creditins_payments_per_period' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (367, 'creditins_payments_per_period', 'customer_type_client := 3\r\n\r\ndocument_type_id := 6\r\ndocument_substatus_annuled := 21\r\n\r\nincomes_reason_type_id := 104\r\n\r\npayments_tag_checked := 9', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (367, 'Плащания за период', '', NULL, 'bg'),
  (367, 'Payments per period', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '367', '0', '1'),
  ('reports', 'export', '367', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '367';

########################################################################
# 2017-07-14 - Added new report 'creditins_proceedings_by_client' for CreditIns installation (CREDITINS)

# Added new report 'creditins_proceedings_by_client' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (368, 'creditins_proceedings_by_client', 'customer_type_client := 3\r\ncustomer_type_court := 4\r\ncustomer_type_judge := 5\r\n\r\nprojects_type_id := 1\r\nproject_proceeding_date := date_proceedings\r\nproject_lawsuit_number := lawsuit_number\r\nproject_lawsuit_status := lawsuit_status_id\r\nproject_court := court_id\r\nproject_private_judge := private_judge_id\r\nproject_proceeding_type := lawsuit_type_id\r\nproject_total_costs := total_costs\r\nproject_lawyer_tax_zd := lawyer_imperatives__amount\r\nproject_state_tax_zd := statetax_imper__amount\r\nproject_other_tax_zd := imperatives_fee__amount\r\nproject_lawyer_tax_id := enforcement_advocate__amount\r\nproject_state_tax_id := enforce_tax__amount\r\nproject_other_tax_id := state_enforce__amount\r\nproject_lawyer_tax_ip := claim_jurist__amount\r\nproject_state_tax_ip := tax_claim__amount\r\nproject_other_tax_ip := other_fee__amount\r\nproject_expert_tax_ip := expert_tax__amount\r\n\r\nproject_principal_payment := registered_principal__payment\r\nproject_interest_payment := registered_interest__payment\r\nproject_warranty_payment := registered_warranty__payment\r\nproject_law_interest_left_payment := interest_according__payment\r\nproject_law_interest_payment := penalty_int__payment\r\nproject_payment_lawyer_tax_zd := lawyer_imperatives__payment\r\nproject_payment_state_tax_zd := statetax_imper__payment\r\nproject_payment_other_tax_zd := imperatives_fee__payment\r\nproject_payment_lawyer_tax_id := enforcement_advocate__payment\r\nproject_payment_state_tax_id := enforce_tax__payment\r\nproject_payment_other_tax_id := state_enforce__payment\r\nproject_payment_lawyer_tax_ip := claim_jurist__payment\r\nproject_payment_state_tax_ip := tax_claim__payment\r\nproject_payment_other_tax_ip := other_fee__payment\r\nproject_payment_expert_tax_ip := expert_tax__payment\r\n\r\nproject_remain_payment := remain_payment\r\nproject_principal_left := registered_principal__rest\r\nproject_interest_left := registered_interest__rest\r\nproject_warranty_left := registered_warranty__rest\r\nproject_law_interest_left_left := interest_according__rest\r\nproject_law_interest_left := penalty_int__rest\r\nproject_left_lawyer_tax_zd := lawyer_imperatives__rest\r\nproject_left_state_tax_zd := statetax_imper__rest\r\nproject_left_other_tax_zd := imperatives_fee__rest\r\nproject_left_lawyer_tax_id := enforcement_advocate__rest\r\nproject_left_state_tax_id := enforce_tax__rest\r\nproject_left_other_tax_id := state_enforce__rest\r\nproject_left_lawyer_tax_ip := claim_jurist__rest\r\nproject_left_state_tax_ip := tax_claim__rest\r\nproject_left_other_tax_ip := other_fee__rest\r\nproject_left_expert_tax_ip := expert_tax__rest\r\n\r\nnomenclatures_type_proceeding_to_include := 95,94,24', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (368, 'Дела по клиенти', '', NULL, 'bg'),
  (368, 'Proceedings by client', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '368', '0', '1'),
  ('reports', 'export', '368', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '368';

########################################################################
# 2017-07-18 - Added new settings for 'creditins_customer_file' report for CreditIns installation (CREDITINS) to contain the statuses for proceedings

# Added new settings for 'creditins_customer_file' report for CreditIns installation (CREDITINS) to contain the statuses for proceedings
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_full_paid_status', '\r\ndocument_substatus_proceeding := 23\r\ndocument_substatus_proceeding_paid := 24\r\ndocument_full_paid_status') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_substatus_proceeding%';

########################################################################
# 2017-07-26 - Added additional settings for creating new records in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# Added additional settings for creating new records in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nproject_calls_campaign := 879\r\n\r\n# settings for creation of records\r\ncreate_model := document\r\ncreate_type := 16\r\ntransform_b_customer := customer_id\r\ntransform_b_date := curr_date\r\ntransform_b_employee := employee_id\r\ntransform_a_contract_num := contract_num\r\ntransform_a_contract_date := contract_date\r\ntransform_a_overdue01 := deadline01\r\ntransform_a_overdue02 := deadline02\r\ntransform_a_overdue03 := deadline03\r\ntransform_a_overdue04 := deadline04\r\ntransform_a_overdue05 := deadline05\r\ntransform_a_overdue06 := deadline06\r\ntransform_a_overdue07 := deadline07\r\ntransform_a_overdue08 := deadline08\r\ntransform_a_overdue09 := deadline09\r\ntransform_a_overdue10 := deadline10\r\ntransform_a_overdue11 := deadline11\r\ntransform_a_overdue12 := deadline12\r\ntransform_a_overdue13 := deadline13\r\ntransform_a_overdue14 := deadline14\r\ntransform_a_overdue15 := deadline15\r\ntransform_a_overdue16 := deadline16\r\ntransform_a_overdue17 := deadline17\r\ntransform_a_overdue18 := deadline18\r\ntransform_a_overdue19 := deadline19\r\ntransform_a_overdue20 := deadline20') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%project_calls_campaign%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_client_type_id', '\r\ndocument_emails_for_overdue_type_id := 16\r\n\r\ncustomer_client_type_id') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%document_emails_for_overdue_type_id%';

########################################################################
# 2017-07-28 - Added new setting for documents annulled status in CUCR month export automation for CreditIns installation (CREDITINS)

# Added new setting for documents annulled status in CUCR month export automation for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nsend_to_email :=', '\r\ndocument_status_annulled := 21\r\n\r\nsend_to_email :=') WHERE `method` LIKE '%createMonthCucrFile%' AND `settings` NOT LIKE '%document_status_annulled%';

########################################################################
# 2017-07-31 - Remove the automation for multiple distribution ot payments that will not be used anumore in CreditIns installation (CREDITINS)

# Remove the automation for multiple distribution ot payments that will not be used anumore in CreditIns installation (CREDITINS)
DELETE FROM `automations` WHERE `method` LIKE '%distributePaymentsByRepaymentSchedule%';

########################################################################
# 2017-08-01 - Added new setting for Nephin id in 'creditins_ckr_export' report for CreditIns installation (CREDITINS)

# Added new setting for Nephin id in 'creditins_ckr_export' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_product_type_id :=', '\r\ndocument_nefin_id := nephin_id\r\n\r\nnom_product_type_id :=') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` NOT LIKE '%document_nefin_id%';

########################################################################
# 2017-08-09 - Added new settings for Nephin id and project data for CUCR export in 'createMonthCucrFile' automation for CreditIns installation (CREDITINS)

# Added new settings for Nephin id and project data for CUCR export in 'createMonthCucrFile' automation for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ndocument_status_annulled :=', '\r\ndocument_nefin_id := nephin_id\r\n\r\ndocument_tag_court_paid := 11\r\ndocument_status_include_in_export := closed_23\r\ndocument_status_court_paid := closed_24\r\ndocument_status_annulled :=') WHERE `method` LIKE '%createMonthCucrFile%' AND `settings` NOT LIKE '%document_nefin_id%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nsend_to_email :=', '\r\n\r\nproject_type_proceeding := 1\r\nproject_contract_num := contract_num_id\r\nproject_principal := principal\r\nproject_legal_interest := legal_interest\r\nproject_legal_interest_according := legal_interest_according\r\nproject_registered_principal_left := registered_principal__rest\r\nproject_registered_interest_left := registered_interest__rest\r\nproject_interest_according_left := interest_according__rest\r\nproject_penalty_int_left := penalty_int__rest \r\nproject_registered_principal_court := registered_principal__court\r\nproject_registered_interest_court := registered_interest__court\r\nproject_interest_according_court := interest_according__court\r\nproject_penalty_int_court := penalty_int__court\r\nproject_lawyer_imperatives_court := lawyer_imperatives__court\r\nproject_statetax_imper_court := statetax_imper__court\r\nproject_imperatives_fee_court := imperatives_fee__court\r\nproject_enforcement_advocate_court := enforcement_advocate__court\r\nproject_state_enforce_court := state_enforce__court\r\nproject_enforce_tax_court := enforce_tax__court\r\nproject_claim_jurist_court := claim_jurist__court\r\nproject_tax_claim_court := tax_claim__court\r\nproject_other_fee_court := other_fee__court\r\nproject_expert_tax_court := expert_tax__court\r\n\r\nsend_to_email :=') WHERE `method` LIKE '%createMonthCucrFile%' AND `settings` NOT LIKE '%project_type_proceeding%';

########################################################################
# 2017-08-10 - Removed setting for reexport tag for 'distributePaymentByRepaymentSchedule' automations for CreditIns installation (CREDITINS)
#            - Added new setting for proceeding general obligations in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)

# Removed setting for reexport tag for 'distributePaymentByRepaymentSchedule' automations for CreditIns installation (CREDITINS)
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'tag_reexport := 6\r\n', '') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` LIKE '%tag_reexport := 6%';

# Added new setting for proceeding general obligations in 'creditins_overdue_payments' report for CreditIns installation (CREDITINS)
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nlawsuit_nom_id :=', '\r\nproject_general_obligation := general_obligation\r\n\r\nlawsuit_nom_id :=') WHERE `type` LIKE 'creditins_overdue_payments' AND `settings` NOT LIKE '%project_general_obligation%';

########################################################################
# 2017-08-18 - Added new settings for 'creditins_ckr_export' report for CreditIns installation (CREDITINS) shich will be used when generating CUCR files

# Added new settings for 'creditins_ckr_export' report for CreditIns installation (CREDITINS) shich will be used when generating CUCR files
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_nefin_id :=', '\r\ndocument_date_payment := date_payment_doc\r\ndocument_nefin_id :=') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` NOT LIKE '%document_date_payment%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_product_type_id :=', '\r\n\r\ndocument_tag_court_paid := 11\r\ndocument_status_include_in_export := closed_23\r\ndocument_status_court_paid := closed_24\r\ndocument_status_annulled := 21\r\n\r\nproject_type_proceeding := 1\r\nproject_contract_num := contract_num_id\r\nproject_principal := principal\r\nproject_legal_interest := legal_interest\r\nproject_legal_interest_according := legal_interest_according\r\nproject_registered_principal_left := registered_principal__rest\r\nproject_registered_interest_left := registered_interest__rest\r\nproject_interest_according_left := interest_according__rest\r\nproject_penalty_int_left := penalty_int__rest \r\nproject_registered_principal_court := registered_principal__court\r\nproject_registered_interest_court := registered_interest__court\r\nproject_interest_according_court := interest_according__court\r\nproject_penalty_int_court := penalty_int__court\r\nproject_lawyer_imperatives_court := lawyer_imperatives__court\r\nproject_statetax_imper_court := statetax_imper__court\r\nproject_imperatives_fee_court := imperatives_fee__court\r\nproject_enforcement_advocate_court := enforcement_advocate__court\r\nproject_state_enforce_court := state_enforce__court\r\nproject_enforce_tax_court := enforce_tax__court\r\nproject_claim_jurist_court := claim_jurist__court\r\nproject_tax_claim_court := tax_claim__court\r\nproject_other_fee_court := other_fee__court\r\nproject_expert_tax_court := expert_tax__court\r\n\r\nnom_product_type_id :=') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` NOT LIKE '%document_tag_court_paid%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncucr_test_mode := 0', '') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` LIKE '%cucr_test_mode := 0%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncucr_test_mode := 1', '') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` LIKE '%cucr_test_mode := 1%';

########################################################################
# 2017-08-21 - Added month data for previous CUCR file exports
#            - Added additional setting for deadline month date for generating the CUCR file

# Added month data for previous CUCR file exports
UPDATE exports_log SET `log`=CONCAT(`log`, '\r\nexport_month := ', DATE_FORMAT(exported, "%Y-%m")) WHERE `file_name` LIKE '%CUCR%' AND `log` NOT LIKE '%export_month%';

# Added additional setting for deadline month date for generating the CUCR file
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncucr_deadline_month_date := 15') WHERE `type` LIKE 'creditins_ckr_export' AND `settings` NOT LIKE '%cucr_deadline_month_date%';

######################################################################################
# 2017-09-19 - Add new report: 'creditins_credits_for_period'

# Add new report: 'creditins_credits_for_period'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`) VALUES
  ('372', 'creditins_credits_for_period', '# id на типа контрагенти „Клиент“\r\ncustomer_type_client := 3\r\n# id на типа документи „Договор за заем“\r\ndocument_type_loan_agreement := 6');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('372', 'Отпуснати кредити за период', 'bg'),
  ('372', 'Credits granted for period', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '372', '1'),
  ('reports', 'export', '372', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '372';

######################################################################################
# 2017-09-29 - Added SMS sending automations

# Added SMS sending automations
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Изпраща SMS при Преминаване на Договор за заем в статус "Погасен" или "Съдебно изплатен"', 0, NULL, 1, 'documents', NULL, 'before_action', '6', '# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\r\n#test_recipient := 0886403964 \r\nsms_gateway_url := http://rcv.smspay.bg/users/send_bulk_prepaid.php\r\nsms_gateway_user_id := \r\nsms_gateway_service_id := \r\nsms_template := Vashiqt kredit e uspeshno pogasen! Moje da kandidatstvate otnovo na www.creditins.bg! Za info tel. 070020121;\r\n', 'condition := \'[prev_b_substatus]\' != 20 && \'[prev_b_substatus]\' != 24\r\ncondition := $this->registry->get(\'request\')->get(\'substatus\') == \'closed_20\' || $this->registry->get(\'request\')->get(\'substatus\') == \'closed_24\'', 'plugin := creditins\r\nmethod := sendSMS', 'cancel_action_on_fail := 1', 1, 1, 1),
('Изпраща SMS за напомняне за настъпващ падеж на неплатена вноска 2 дена преди настъпването му', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 09:00\r\nstart_before := 12:00\r\nsend_to_email := \r\n\r\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\r\n#test_recipient := 0886403964 \r\nsms_gateway_url := http://rcv.smspay.bg/users/send_bulk_prepaid.php\r\nsms_gateway_user_id := \r\nsms_gateway_service_id := \r\nsms_template := CreditIns Vi uvedomiava, che kam padej [date_of_payment] po dog [name] daljite [total_unpaid_sum] lv, ot koito [principal] glavnica, [insurance] garanciya i [interest] lihva\r\n\r\nperiod_days := 2', 'where := d.active = 1\r\nwhere := d.status = \'opened\'\r\nwhere := d.substatus = 22\r\nwhere := gt2t1.article_code = CURDATE() AND gt2t1.free_field5 > 0 \r\n', 'plugin := creditins\r\nmethod := sendSMS', 'cancel_action_on_fail := 1', 1, 0, 1),
('Изпраща SMS за напомняне за неплатена вноска с настъпващ падеж в текущият ден', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 09:00\r\nstart_before := 12:00\r\nsend_to_email := \r\n\r\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\r\n#test_recipient := 0886403964 \r\nsms_gateway_url := http://rcv.smspay.bg/users/send_bulk_prepaid.php\r\nsms_gateway_user_id := \r\nsms_gateway_service_id := \r\nsms_template := CreditIns Vi uvedomiava, che kam padej [date_of_payment] po dog [name] daljite [total_unpaid_sum] lv, ot koito [principal] glavnica, [insurance] garanciya i [interest] lihva\r\n', 'where := d.active = 1\r\nwhere := d.status = \'opened\'\r\nwhere := d.substatus = 22\r\nwhere := gt2t1.article_code = DATE_SUB(CURDATE(), INTERVAL 2 DAY) AND gt2t1.free_field5 > 0 \r\n', 'plugin := creditins\r\nmethod := sendSMS', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2017-10-04 - Added setting for project campaign calls in 'creditins_customer_file' report
#            - Fixed the settings of crontab automations that send SMS

# Added setting for project campaign calls in 'creditins_customer_file' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\nproject_calls_campaign := 879') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%project_calls_campaign%';

# Fixed the settings of crontab automations that send SMS
UPDATE automations SET conditions=REPLACE(conditions, 'DATE_SUB', 'DATE_ADD') WHERE method LIKE '%sendSMS%' AND conditions LIKE '%DATE_SUB%';
UPDATE automations SET settings=CONCAT(settings, '\r\n\r\nperiod_days := 2') WHERE method LIKE '%sendSMS%' AND settings NOT LIKE '%period_days := 2%' AND conditions LIKE '%DATE_ADD%';
UPDATE automations SET settings=REPLACE(settings, '\r\n\r\nperiod_days := 2', '') WHERE method LIKE '%sendSMS%' AND settings LIKE '%period_days := 2%' AND conditions NOT LIKE '%DATE_ADD%';
UPDATE automations SET name='Изпраща SMS за напомняне за настъпващ падеж на неплатена вноска 2 дена преди настъпването му' WHERE method LIKE '%sendSMS%' AND conditions LIKE '%DATE_ADD%' AND automation_type='crontab';
UPDATE automations SET name='Изпраща SMS за напомняне за неплатена вноска с настъпващ падеж в текущият ден' WHERE method LIKE '%sendSMS%' AND conditions NOT LIKE '%DATE_ADD%' AND automation_type='crontab';

######################################################################################
# 2017-10-05 - Added settings for calls info in 'creditins_overdue_payments' report
#            - Added setting for new status fraud in 'creditins_customer_file' report
#            - Added settings for completing two extra vars in project processding in 'creditins_overdue_payments' report

# Added settings for calls info in 'creditins_overdue_payments' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\n# settings for creation of records', '\r\n\r\ndocument_call_type_id := 12\r\ndocument_call_customer := customer_id\r\ndocument_call_date := list_to_date\r\ndocument_call_status := list_call_status\r\ndocument_call_description := call_description\r\ndocument_call_payment_date := list_datenext_call\r\n\r\n# settings for creation of records') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%document_call_type_id%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nproject_proceeding_date := ', '\r\nproject_customer_address_card := address_card\r\nproject_proceeding_date := ') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%project_customer_address_card%';

# Added setting for new status fraud in 'creditins_customer_file' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_full_paid_status := ', '\r\ndocument_substatus_fraud := 25\r\ndocument_full_paid_status := ') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%document_substatus_fraud%';

# Added settings for completing two extra vars in project processding in 'creditins_overdue_payments' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ntransform_a_maturity_overdue_payment := first_unpaid_date\r\ntransform_a_overdue_debt := total_overdue') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%transform_a_overdue_debt%';

######################################################################################
# 2017-10-09 - Add new report: 'creditins_expected_earnings_per_period'
#            - Changed the SMS text to the crontab sending messages
#            - Added report for birthday congratulations

# Add new report: 'creditins_expected_earnings_per_period'
DELETE FROM `reports` WHERE `type` = 'creditins_expected_earnings_per_period';
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`) VALUES
  ('373', 'creditins_expected_earnings_per_period', '# id на типа документи „Договор за заем“\r\ndocument_type_loan_agreement := 6\r\n# id на състояние „Активен“ за документи от тип „Договор за заем“\r\ndocument_loan_agreement_substatus_active := 22');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('373', 'Очаквани постъпления за период', 'bg'),
  ('373', 'Expected earnings per period', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '373', '1'),
  ('reports', 'export', '373', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '373';

# Changed the SMS text to the crontab sending messages
UPDATE automations SET settings=REPLACE(settings, 'sms_template := CreditIns Vi uvedomiava, che kam padej [date_of_payment] po dog [name] daljite [total_unpaid_sum] lv, ot koito [principal] glavnica, [insurance] garanciya i [interest] lihva', 'sms_template := CreditIns Vi uvedomiava, che kam padej [date_of_payment] po dog [name] daljite [total_unpaid_sum] lv, ot koito [principal] glavnica, [insurance] garanciya, [interest] lihva i [interest2] zakonna lihva za zabava') WHERE method LIKE '%sendSMS%' AND automation_type='crontab';
UPDATE automations SET settings=REPLACE(settings, 'start_time', 'start_after') WHERE method LIKE '%sendSMS%' AND automation_type='crontab';

# Added report for birthday congratulations
INSERT IGNORE INTO `reports` VALUES
(157, 'birthday_congratulations', 'allows_email_sending := 1\r\nemail_origin := self\r\nsend_sms := 0\r\nexclude_customer_types := \r\nsender_email := \r\nsender_name_bg := \r\nsender_name_en := \r\nsender_name := \r\nsms_server_path := \r\nsms_username := \r\nsms_password := \r\nsms_dlr := 1\r\nsms_method := \r\nmobile_number_regexp := ', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` VALUES
(157, 'Поздравления за рожден ден', NULL, NULL, 'bg'),
(157, 'Birthday congratulations', NULL, NULL, 'en');

########################################################################
# 2017-10-10 - Added permissions to previously added report

# Added permissions to previously added report
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 157, 0, 1),
('reports', 'export', 157, 0, 2);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action IN ('generate_report', 'export') AND model_type IN (157);

########################################################################
# 2017-10-11 - Added setting for new partial reports in the 'creditins_proceedings_by_client' report

# Added setting for new partial reports in the 'creditins_proceedings_by_client' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nproject_principal_payment :=', '\r\n\r\nproject_court_confirmed_sum := total_court_recognized\r\nproject_total_paid_sum := total_paid_duty\r\nproject_court_before_pay := total_before_court\r\n\r\nproject_principal_payment :=') WHERE `type`='creditins_proceedings_by_client' AND `settings` NOT LIKE '%project_court_confirmed_sum%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnomenclatures_type_proceeding_to_include :=', '\r\n\r\nproject_expenses_expense_type := cost_type_id\r\nproject_expenses_expense_date := expense_date\r\nproject_expenses_amount := amount_expense\r\nproject_incomes_expense_type := profit_type_id\r\nproject_incomes_expense_date := income_date\r\nproject_incomes_amount := amount_income\r\nproject_contract_num := contract_num_id\r\n\r\nnom_expenses_list_ids := 96, 28, 99, 97, 50, 100, 98, 51, 101, 102\r\nnom_incomes_list_ids := 96, 28, 99, 97, 50, 100, 98, 51, 101, 102, 104, 105, 106, 107, 108\r\n\r\nnomenclatures_type_proceeding_to_include :=') WHERE `type`='creditins_proceedings_by_client' AND `settings` NOT LIKE '%project_expenses_expense_type%';

########################################################################
# 2017-10-13 - Added setting for dew extra vars which will be completed when importing contracts with 'importContracts' automation

# Added setting for dew extra vars which will be completed when importing contracts with 'importContracts' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\ndocument_declined_request_type_id :=', '\r\ndocument_contract_total_interest := contract_rate\r\ndocument_contract_total_warranty := contract_warranty\r\ndocument_contract_total_installment_fee := contract_total_amount_fee\r\ndocument_declined_request_type_id :=') WHERE `method` LIKE '%importContracts%' AND `settings` NOT LIKE '%document_contract_total_interest%';

########################################################################
# 2017-10-16 - Added setting for date var which will be completed when a contract is fully paid

# Added setting for date var which will be completed when a contract is fully paid
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nstatus_fully_paid :=', '\r\nschedule_var_repayment_date := repayment_day\r\n\r\nstatus_fully_paid :=') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` NOT LIKE '%schedule_var_repayment_date%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_status_active :=', '\r\ndocument_contract_repayment_date := repayment_day\r\ndocument_status_active :=') WHERE `type` = 'creditins_customer_file' AND `settings` NOT LIKE '%document_contract_repayment_date%';

########################################################################
# 2017-10-19 - Added additional settings to make writing history in the additional data for extensions possible; for writing the repayment status and for generating and sending annexes (on extension)
#            - Added patterns plugin for preparing the needed extra data for annexes which will be sent to the creditor
#            - Added placeholders for special data which will be completed in the annex file
#            - Update the existing patterns with the correct placeholders

# Added additional settings to make writing history in the additional data for extensions possible; for writing the repayment status and for generating and sending annexes (on extension)
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\nstatus_fully_paid :=', '\r\nschedule_var_type_repayment := type_repayment\r\nschedule_type_repayment_in_advance := 1\r\nschedule_type_repayment_in_time := 2\r\n\r\nstatus_fully_paid :=') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` NOT LIKE '%schedule_var_type_repayment%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_status_active :=', '\r\ndocument_contract_type_repayment := type_repayment\r\ndocument_contract_type_repayment_in_advance := 1\r\ndocument_contract_type_repayment_in_time := 2\r\ndocument_contract_extension_date := date_time_extensions\r\ndocument_contract_extension_user := user_extended_credit_id\r\ndocument_contract_extension_user_name := user_extended_credit\r\ndocument_contract_extended_installment := extended_installment\r\ndocument_contract_extended_installment_date := maturity_extended_install\r\ndocument_contract_extension_difference_interest := amount_extend_int\r\ndocument_contract_extension_difference_warranty := amount_extend_fee\r\n\r\ndocument_email_template := 1005\r\ndocument_file_template := 40\r\n\r\ndocument_status_active :=') WHERE `type` = 'creditins_customer_file' AND `settings` NOT LIKE '%document_contract_type_repayment%';

# Added patterns plugin for preparing the needed extra data for annexes which will be sent to the creditor
INSERT IGNORE INTO `patterns_plugins` (`model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
('Document', 6, 'creditins', 'Creditins_Custom_Factory::prepareExtendedGT2', '', '', NOW(), NOW());
SET @pattern_plugin_id:= LAST_INSERT_ID();
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(@pattern_plugin_id, 'Подготовка на удължената GT2 таблица', 'GT2 таблицата се обработва по специфичен начин, за да се включи в анекса, който се генерира и изпраща на клиента', 'bg', NOW()),
(@pattern_plugin_id, 'Prepare the extended GT2 table', 'GT2 table is processed in a specific way to be included in the annex which is generated and sent to the client', 'en', NOW());

# Added placeholders for special data which will be completed in the annex file
INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_extended_gt2', 'Document', 'basic', 'patterns', ',40,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Удължен погасителен план', NULL, 'bg'),
(LAST_INSERT_ID(), 'Extended repayment plan', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_extended_payment_value', 'Document', 'basic', 'patterns', ',40,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Добавена сума след удължаване', NULL, 'bg'),
(LAST_INSERT_ID(), 'Added sum after extension', NULL, 'en');

INSERT IGNORE INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'contract_extended_payment_date', 'Document', 'basic', 'patterns', ',40,', '', 1);
INSERT IGNORE INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Дата на удължената вноска', NULL, 'bg'),
(LAST_INSERT_ID(), 'Extended payment date', NULL, 'en');

# Update the existing patterns with the correct placeholders
UPDATE `patterns` SET `plugin`=@pattern_plugin_id WHERE `id`=40 AND `plugin`=0;
UPDATE `patterns_i18n` SET `content`=REPLACE(`content`, '[bg_a_group_table_2]', '[contract_extended_gt2]') WHERE `parent_id`=40 AND `content` LIKE '%[bg_a_group_table_2]%';
UPDATE `patterns_i18n` SET `content`=REPLACE(`content`, '[last_delivery_price]', '[contract_extended_payment_value]') WHERE `parent_id`=40 AND `content` LIKE '%[last_delivery_price]%';
UPDATE `patterns_i18n` SET `content`=REPLACE(`content`, '[article_code]', '[contract_extended_payment_date|date_format:%d.%m.%Y]') WHERE `parent_id`=40 AND `content` LIKE '%[article_code]%';

########################################################################
# 2017-10-20 - Added settings for SMS gateway
#            - Added SMS db table

# Added settings for SMS gateway
UPDATE automations
  SET settings=REPLACE(settings, 'sms_gateway_url := http://rcv.smspay.bg/users/send_bulk_prepaid.php\r\nsms_gateway_user_id := \r\nsms_gateway_service_id := ', 'sms_gateway_url := http://api.smspro.bg/bsms/send\r\nsms_gateway_apikey := 6be04cd46a632ccb25de5527b1409779')
  WHERE method like '%sendSMS%';
# Remove a trailing semicolon
UPDATE automations
  SET settings=REPLACE(settings, '070020121;', '070020121')
  WHERE method like '%sendSMS%';

# Added SMS db table
DROP TABLE IF EXISTS `sms`;
CREATE TABLE `sms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `document_id` int(11) NOT NULL DEFAULT '0',
  `recipient_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `recipient_number` varchar(15) COLLATE utf8_unicode_ci DEFAULT NULL,
  `content` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `notes` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `send_status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `send_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `dlr_status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL,
  `dlr_date` datetime DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='contains SMS messages sent for CREDITINS only';

########################################################################
# 2017-10-27 - Added additional settings for 'creditins_ckr_export' report for statuses and tags for fraud contracts and for paid contracts and remove unused settings

# Added additional settings for 'creditins_ckr_export' report for statuses and tags for fraud contracts and for paid contracts and remove unused settings
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_status_annulled :=', '\r\ndocument_status_paid := closed_20\r\ndocument_status_fraud := closed_25\r\ndocument_status_annulled :=') WHERE `type` = 'creditins_ckr_export' AND `settings` NOT LIKE '%document_status_fraud%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_status_include_in_export :=', '\r\ndocument_tag_exclude_from_ckr := 12\r\ndocument_status_include_in_export :=') WHERE `type` = 'creditins_ckr_export' AND `settings` NOT LIKE '%document_tag_exclude_from_ckr%';

UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nproject_legal_interest := legal_interest', '') WHERE `type` = 'creditins_ckr_export' AND `settings` LIKE '%project_legal_interest := legal_interest%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nproject_legal_interest_according := legal_interest_according', '') WHERE `type` = 'creditins_ckr_export' AND `settings` LIKE '%project_legal_interest_according := legal_interest_according%';

######################################################################################
# 2017-10-31 - Add new report: 'creditins_history_of_credit_changes'
#            - Added bullet to complete the starting data for contracts
#            - Fixed the starting date of crontab SMS automations
#            - Added Halloween promotion automation sending SMS
#            - Expanded the SMS db table with some new fields
#            - Automation to equalize the incomes reason to the paid part of the contract when the contract is marked as fraud

# Add new report: 'creditins_history_of_credit_changes'
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`) VALUES
  ('375', 'creditins_history_of_credit_changes', '# id-та на състояния, в които да не са документите от тип „Договор за заем“\r\ncontracts_substatus_not_in := 21,25\r\n\r\n# цвят за тип промня: Предсрочно погасени\r\nchange_type_color_1 := #006600\r\n# цвят за тип промня: Погасени по план\r\nchange_type_color_2 := #33CC33\r\n# цвят за тип промня: Удължени с период\r\nchange_type_color_3 := #9966FF\r\n# цвят за тип промня: Преминали към дело\r\nchange_type_color_4 := #FF0000\r\n# цвят за тип промня: Погасени от дело\r\nchange_type_color_5 := #FF9900');
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('375', 'История на промени по кредити', 'bg'),
  ('375', 'History of credit changes', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '375', '1'),
  ('reports', 'export', '375', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '375';

# Added bullet to complete the starting data for contracts
INSERT IGNORE INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
('creditInsUpdateContractStartingSums', 'Update the starting amount of the contracts in CreditIns according to the information from the Cherry Inc site.', 14044, 1, 1, NOW(), '0000-00-00 00:00:00');

# PRE-DEPLOYED # Fixed the starting date of crontab SMS automations
# UPDATE automations SET settings=REPLACE(settings, 'start_after', 'start_time') WHERE method like '%sms%';
# UPDATE automations SET after_action='' WHERE method like '%sms%' AND automation_type='crontab';

# PRE-DEPLOYED # Added Halloween promotion automation sending SMS
# INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Изпраща SMS за промоция Helloween', 0, NULL, 1, 'customers', NULL, 'crontab', '3', 'start_time := 09:00\r\nstart_before := 18:00\r\nsend_to_email := <EMAIL>\r\n\r\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\r\n#test_recipient := 0886403964 \r\nsms_gateway_url := http://api.smspro.bg/bsms/send\r\nsms_gateway_apikey := 6be04cd46a632ccb25de5527b1409779\r\nsms_template := Posreshtni Halloween i vzemi kredit s otstupka 50% ot lihvata do kraq na denq ot www.creditins.bg! Za info 070020121', 'where := c.active = 1\r\nwhere := c.subtype = \'normal\'\r\nwhere := c.id IN (SELECT customer FROM documents WHERE type=6 AND substatus=20)\r\nwhere := c.id NOT IN (SELECT customer FROM documents WHERE type=6 AND substatus IN (22, 23, 24, 25))', 'plugin := creditins\r\nmethod := sendSMS', '', 1, 1, 1);

# PRE-DEPLOYED # Expanded the SMS db table with some new fields
# ALTER TABLE `sms`
#   CHANGE COLUMN `document_id` `model_id` INT(11) NOT NULL DEFAULT '0' AFTER `id`,
#   ADD COLUMN `model_name` VARCHAR(255) NOT NULL DEFAULT '0' AFTER `model_id`;
# UPDATE sms SET model_name='Document';

# PRE-DEPLOYED # Automation to equalize the incomes reason to the paid part of the contract when the contract is marked as fraud
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Приравняване на основание след маркиране на договор за заем като "Измама"', 0, NULL, 1, 'documents', NULL, 'action', '6', 'related_incomes_reason_type := 104', 'condition := \'[b_substatus]\' == \'25\' && \'[prev_b_substatus]\' == \'22\'', 'plugin := creditins\r\nmethod := annulIncomesReasonWhenAnnulContract', NULL, 1, 1, 1);

######################################################################################
# 2017-11-09 - Added November 2017 promotion automation sending SMS

# PRE-DEPLOYED # Added November 2017 promotion automation sending SMS
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Изпраща SMS за промоция ноември 2017', 0, NULL, 1, 'customers', NULL, 'crontab', '3', 'start_time := 09:00\r\nstart_before := 18:00\r\nsend_to_email := <EMAIL>\r\n\r\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\r\ntest_mode := 1\r\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\r\ntest_recipient := 0886403964 \r\nsms_gateway_url := http://api.smspro.bg/bsms/send\r\nsms_gateway_apikey := 6be04cd46a632ccb25de5527b1409779\r\nsms_template := Vzemi kredit do 30.11.2017 i se vuzpolzvai ot 50% namalenie na lihvata ot www.creditins.bg! Za info. 070020121', 'where := c.active = 1\r\nwhere := c.subtype = \'normal\'\r\nwhere := (c.id IN (SELECT customer FROM documents WHERE type=6 AND substatus=20) AND c.id NOT IN (SELECT customer FROM documents WHERE type=6 AND substatus IN (22, 23, 24, 25))) OR c.id IN (SELECT last_unpaid_only.customer FROM (SELECT d.customer, COUNT(gt2.id) as count_unpaid, (SELECT COUNT(g.id) FROM gt2_details g WHERE g.model=\'Document\' and g.model_id=d.id AND g.free_field5=0) as count_paid FROM gt2_details gt2 JOIN documents d ON gt2.model=\'Document\' AND gt2.model_id=d.id AND d.type=6 AND d.substatus=22 AND free_field5!=0 GROUP BY d.id HAVING count_unpaid=1 AND count_paid>=1) as last_unpaid_only)', 'plugin := creditins\r\nmethod := sendSMS', '', 1, 1, 1);

######################################################################################
# 2017-11-21 - Added crontab automation for sending e-mails for upcoming deadlines
#            - Add new placeholders for installment deadline and for the installment amount to pay
#            - Update the existing template with the new placeholders

# PRE-DEPLOYED # Added crontab automation for sending e-mails for upcoming deadlines
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#   ('Напомняне за наближаващ падеж', 0, NULL, 1, 'customers', NULL, 'crontab', '3', 'start_time := 11:00\r\nstart_before := 12:00\r\nstart_week_day := 1\r\nsend_to_email := <EMAIL>\r\n\r\ndocument_contract_type := 6\r\ndocument_status_active := opened_22\r\ndocument_email_template := 1006', 'condition := 1', 'plugin := creditins\r\nmethod := notifyUpcomingDeadline', '', 1, 0, 1);

# PRE-DEPLOYED # Add new placeholders for installment deadline and for the installment amount to pay
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('installment_amount_to_pay', 'Document', 'send', 'emails', ',1006,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Installment amount left to pay', NULL, 'en'),
#   (LAST_INSERT_ID(), 'Остатък за плащане по вноска', NULL, 'bg');

# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('installment_deadline', 'Document', 'send', 'emails', ',1006,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Installment deadline', NULL, 'en'),
#   (LAST_INSERT_ID(), 'Падеж на вноска', NULL, 'bg');

# PRE-DEPLOYED # Update the existing template with the new placeholders
# UPDATE `emails_i18n` SET `body`=REPLACE(`body`, '[a_article_code]', '[installment_deadline]') WHERE `parent_id`=1006;
# UPDATE `emails_i18n` SET `body`=REPLACE(`body`, '[а_free_field5]', '[installment_amount_to_pay]') WHERE `parent_id`=1006;

######################################################################################
# 2017-11-22 - Added sms delivery log table
#            - Fixed the sms delivery status
#            - Reconfigure the automation that sends SMS for the paid documents (from before_action to crontab)
#            - Change the sequence and starting interval of the other crontab automations

# PRE-DEPLOYED # Added sms delivery log table
# CREATE TABLE IF NOT EXISTS `sms_delivery_log` (
#   `id` int(11) NOT NULL AUTO_INCREMENT,
#   `sms_id` int(11) DEFAULT NULL COMMENT 'id (smspay)',
#   `status` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'status (smspay)',
#   `attempt` int(11) DEFAULT NULL COMMENT 'attempt (smspay)',
#   `comment` int(11) DEFAULT NULL COMMENT 'comment id (nZoom)',
#   `model_id` int(11) DEFAULT NULL COMMENT 'model id (nZoom)',
#   `model_name` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'model name (nZoom)',
#   `remote_addr` varchar(255) COLLATE utf8_unicode_ci DEFAULT NULL COMMENT 'remote IP address',
#   `date` datetime DEFAULT NULL COMMENT 'ts (smspay)',
#   PRIMARY KEY (`id`)
# ) ENGINE=MyISAM DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci COMMENT='stores the smspay.bg delivery log';

# PRE-DEPLOYED # Fixed the sms delivery status
# UPDATE sms_delivery_log a
# JOIN (
#   SELECT sms_id, MAX(DATE) DATE
#   FROM sms_delivery_log
#   GROUP BY sms_id) b
#   ON a.sms_id=b.sms_id AND a.date=b.date
# JOIN sms s
#   ON a.sms_id=s.id AND (a.status != s.dlr_status OR a.date != s.dlr_date)
# SET s.dlr_status = a.status, s.dlr_date = a.date;

# PRE-DEPLOYED # Reconfigure the automation that sends SMS for the paid documents (from before_action to crontab)
# UPDATE automations SET
#  position=1,
#  nums=0,
#  automation_type='crontab',
#  after_action='',
#  name='Изпраща SMS на Договор за заем в статус "Погасен" или "Съдебно изплатен"',
#  conditions='where := d.active = 1\nwhere := d.status = ''closed''\nwhere := d.substatus IN (20, 24)\n#exclude the too old documents\nwhere := d.status_modified>''2017-11-22 00:00:00''\n#make sure that no SMS informing of paid document has been sent\nwhere := d.id NOT IN (SELECT model_id FROM sms WHERE model_name=''Document'' AND content LIKE ''Vashiqt kredit e uspeshno pogasen!%'')',
#  settings='start_time := 10:30\nstart_before := 17:30\nsend_to_email := <EMAIL>\n\n# В тестов режим не се изпраща SMS-и, а само създава коментар към поръчката\ntest_mode := 1\n# При настроен тестов получател (във формат 08XXXXXXXX или 09XXXXXXXX) се изпраща SMS до този получател\ntest_recipient := 0899980401\nsms_gateway_url := http://api.smspro.bg/bsms/send\nsms_gateway_apikey := 6be04cd46a632ccb25de5527b1409779\nsms_template := Vashiqt kredit e uspeshno pogasen! Moje da kandidatstvate otnovo na www.creditins.bg! Za info tel. 070020121'
# WHERE id=31;

# PRE-DEPLOYED # Change the sequence and starting interval of the other crontab automations
# UPDATE automations SET position=2, settings=REPLACE(settings, 'start_time := 09:00', 'start_time := 10:30') WHERE id=32;
# UPDATE automations SET settings=REPLACE(settings, 'start_before := 18:00', 'start_before := 17:30') WHERE id=32;
# UPDATE automations SET position=3, settings=REPLACE(settings, 'start_time := 09:00', 'start_time := 10:30') WHERE id=33;
# UPDATE automations SET settings=REPLACE(settings, 'start_before := 18:00', 'start_before := 17:30') WHERE id=33;

######################################################################################
# 2017-11-23 - Added crontab automation for sending e-mails for overdue payments, 10, 15, 30 and 62 days after the installment deadline
#            - Add placeholder for list of overdue months

# PRE-DEPLOYED # Added crontab automation for sending e-mails for overdue payments, 62 days after the deadline
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#   ('E-mail за просрочие на вноска на 30 ден', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 03:30\r\nstart_before := 04:00\r\nperiod_overdue_days := 30\r\nsend_to_email := <EMAIL>\r\n\r\ndocument_contract_type := 6\r\ndocument_status_active := opened_22\r\ndocument_email_template := 1002\r\n\r\nkeyword := overdue_30', 'condition := 1', 'plugin := creditins\r\nmethod := notifyPayOverdue', '', 1, 0, 1),
#   ('E-mail за просрочие на вноска на 15 ден', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 03:00\r\nstart_before := 03:30\r\nperiod_overdue_days := 15\r\nsend_to_email := <EMAIL>\r\n\r\ndocument_contract_type := 6\r\ndocument_status_active := opened_22\r\ndocument_email_template := 1002\r\n\r\nkeyword := overdue_15', 'condition := 1', 'plugin := creditins\r\nmethod := notifyPayOverdue', '', 1, 0, 1),
#   ('Съдебен e-mail на 45 ден', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 04:00\r\nstart_before := 04:30\r\nperiod_overdue_days := 45\r\nsend_to_email := <EMAIL>\r\n\r\ndocument_contract_type := 6\r\ndocument_status_active := opened_22\r\ndocument_email_template := 1003\r\n\r\nkeyword := overdue_45', 'condition := 1', 'plugin := creditins\r\nmethod := notifyPayOverdue', '', 1, 0, 1),
#   ('E-mail за предсрочна изискуемост на 62 ден', 0, NULL, 1, 'documents', NULL, 'crontab', '6', 'start_time := 05:00\r\nstart_before := 05:30\r\nperiod_overdue_days := 62\r\nsend_to_email := <EMAIL>\r\n\r\ndocument_contract_type := 6\r\ndocument_status_active := opened_22\r\ndocument_email_template := 1007\r\ndocument_credit_product_var := credit_product_id\r\n\r\ncredit_extra_id := 23\r\nkeyword := overdue_62', 'condition := 1', 'plugin := creditins\r\nmethod := notifyPayOverdue', '', 1, 0, 1);

# PRE-DEPLOYED # Add placeholder for list of overdue months
# INSERT INTO `placeholders` (`varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
#   ('overdue_months', 'Document', 'send', 'emails', ',1007,', '', 0);
# INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
#   (LAST_INSERT_ID(), 'Месеци с просрочени вноски', NULL, 'bg'),
#   (LAST_INSERT_ID(), 'Months with overdue payments', NULL, 'en');

######################################################################################
# 2017-11-30 - Added EasyPay plugin log tables

CREATE TABLE IF NOT EXISTS `plugin_pay_requests` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `TYPE` varchar(25) COLLATE utf8_unicode_ci NOT NULL COMMENT 'CHECK, BILLING or DEPOSIT',
  `MERCHANTID` varchar(10) COLLATE utf8_unicode_ci NOT NULL,
  `IDN` varchar(64) COLLATE utf8_unicode_ci NOT NULL,
  `TID` varchar(26) COLLATE utf8_unicode_ci NOT NULL COMMENT 'empty when TYPE is CHECK',
  `TOTAL` int(12) unsigned NOT NULL COMMENT 'used only for deposits',
  `CHECKSUM` varchar(26) COLLATE utf8_unicode_ci NOT NULL,
  `REQUEST` text COLLATE utf8_unicode_ci NOT NULL,
  `STATUS` int(3) NOT NULL DEFAULT '-1',
  `AMOUNT` int(12) unsigned NOT NULL COMMENT 'returned sum of unpaid invoices - integer because in (cents)',
  `VALIDTO` date NOT NULL,
  `SHORTDESC` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `LONGDESC` text COLLATE utf8_unicode_ci NOT NULL,
  `RESPONSE` text COLLATE utf8_unicode_ci NOT NULL,
  `REMOTE_IP` varchar(25) COLLATE utf8_unicode_ci NOT NULL,
  `DIAG` text COLLATE utf8_unicode_ci NOT NULL,
  `ADDED` datetime NOT NULL,
  `RESPONDED` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1 ;

CREATE TABLE IF NOT EXISTS `plugin_pay_invoices` (
  `parent_id` int(11) NOT NULL,
  `IDN` varchar(255) COLLATE utf8_unicode_ci NOT NULL COMMENT 'concat of IDN and INVOICE num (IDN.INVOICE)',
  `AMOUNT` int(12) unsigned NOT NULL,
  `VALIDTO` date NOT NULL,
  `SHORTDESC` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `LONGDESC` text COLLATE utf8_unicode_ci NOT NULL,
  PRIMARY KEY (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

CREATE TABLE IF NOT EXISTS `plugin_pay_confirms` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `TYPE` varchar(25) COLLATE utf8_unicode_ci NOT NULL COMMENT 'BILLING, PARTIAL or DEPOSIT',
  `IDN` varchar(64) COLLATE utf8_unicode_ci NOT NULL,
  `MERCHANTID` varchar(10) COLLATE utf8_unicode_ci NOT NULL,
  `TID` varchar(26) COLLATE utf8_unicode_ci NOT NULL,
  `DATE` datetime NOT NULL,
  `TOTAL` int(12) unsigned NOT NULL,
  `INVOICES` text COLLATE utf8_unicode_ci NOT NULL COMMENT 'used only in BILLING payments, comma-separated values of IDN.INVOICE',
  `CHECKSUM` varchar(26) COLLATE utf8_unicode_ci NOT NULL,
  `REQUEST` text COLLATE utf8_unicode_ci NOT NULL,
  `STATUS` int(3) NOT NULL DEFAULT '-1',
  `RESPONSE` text COLLATE utf8_unicode_ci NOT NULL,
  `REMOTE_IP` varchar(25) COLLATE utf8_unicode_ci NOT NULL,
  `DIAG` text COLLATE utf8_unicode_ci NOT NULL,
  `ADDED` datetime NOT NULL,
  `RESPONDED` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1 ;

CREATE TABLE IF NOT EXISTS `plugin_pay_full_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `REMOTE_IP` varchar(25) COLLATE utf8_unicode_ci NOT NULL,
  `QUERY_STRING` text COLLATE utf8_unicode_ci NOT NULL,
  `BLACKLISTED` int(4) NOT NULL,
  `ERRORS` varchar(255) COLLATE utf8_unicode_ci NOT NULL,
  `ADDED` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci AUTO_INCREMENT=1 ;

######################################################################################
# 2017-12-01 - Fixed var values, wrong entered as multilang vars

# Delete the vars which have both entries with lang and without lang. The var with lang must be deleted
DELETE d1
FROM `documents_cstm` as d1, `documents_cstm` d2
WHERE d1.var_id IN
(SELECT `id` FROM `_fields_meta` WHERE `name` IN ("franchise_partner", "credit_product_id", "credit_product", "application_method", "grant_credit", "interest_rate_annual", "gpr", "type_reporting_period", "repayment_period", "number_from_site", "amount_requested", "date_rejection", "contract_rate", "contract_warranty", "contract_total_amount_fee")
AND `model`="Document" AND `model_type`=6) AND d1.lang!="" AND d1.var_id=d2.var_id AND d1.model_id=d2.model_id AND d1.num=d2.num AND d2.lang="";

# Remove the lang from the remaining vars
UPDATE `documents_cstm` SET `lang`=""
WHERE `var_id` IN
(SELECT `id` FROM `_fields_meta` WHERE `name` IN ("franchise_partner", "credit_product_id", "credit_product", "application_method", "grant_credit", "interest_rate_annual", "gpr", "type_reporting_period", "repayment_period", "number_from_site", "amount_requested", "date_rejection", "contract_rate", "contract_warranty", "contract_total_amount_fee")
AND `model`="Document" AND `model_type`=6) AND `lang`!="";

######################################################################################
# 2017-12-07 - Added general report for notification sending
#            - Added specific settings of SMS provider to report, enabled SMS test mode
#            - Added saved filters to specify search of groups of customers from report

# PRE-DEPLOYED # Added general report for notification sending
# SET @id := 378;
#
# INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
# (@id, 'customer_notification_sender', '#########################\r\n# notification settings #\r\n#########################\r\n\r\n# enable email/sms sending and email template creation\r\nallows_email_sending := 1\r\n# specify origin of email templates\r\nemail_origin := self\r\n# enable sms template creation\r\nallows_send_sms := 1\r\n\r\n#########################\r\n# filters settings      #\r\n#########################\r\n\r\n# enabled notification types\r\nnotification_types := sms, email\r\n\r\n# max number of customer group radio options to be displayed horizontally, otherwise vertically\r\nquery_options_align_horizontal_max_num := 4\r\n\r\n# optional labels for customer group filter\r\nquery_label_bg := \r\nquery_label_en := \r\n\r\n#########################\r\n# display settings      #\r\n#########################\r\n\r\n# display notification preview button (1/0)\r\ndisplay_preview_button := 0\r\n\r\n# freeze caption row of results (1/0)\r\nfreeze_table_headers := 1\r\n\r\n#########################\r\n# sending settings      #\r\n#########################\r\n\r\n# send results email to user who started notification sending (1/0)\r\nsend_results_email := 0\r\n\r\n# email settings (custom sender data)\r\nsender_email := \r\nsender_name := \r\nsender_name_bg := \r\nsender_name_en := \r\n\r\n# sms settings\r\ntest_mode := 0\r\ntest_recipient := \r\nsms_provider := \r\n', 0, 0, 1);
#
# INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
# (@id, 'Изпращане на SMS и e-mail', NULL, NULL, 'bg'),
# (@id, 'Notification sender (SMS and e-mail)', NULL, NULL, 'en');
#
# INSERT IGNORE INTO `roles_definitions` (`id`, `module`, `controller`, `action`, `model_type`, `requires_model`, `position`) VALUES
# (NULL, 'reports', '', 'generate_report', @id, 0, 1);
# INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT '1', `id`, 'all'
#   FROM `roles_definitions`
#   WHERE `module` = 'reports' AND `action` IN ('generate_report') AND `model_type` = @id;
#
# CREATE TABLE IF NOT EXISTS `reports_customer_notification_sender` (
#     `customer_id` int(11) DEFAULT NULL COMMENT 'id of normal customer',
#     `recipient_id` int(11) DEFAULT NULL COMMENT 'id of person customer or contact',
#     `user_id` int(11) DEFAULT NULL,
#     `type` enum('email','sms') COLLATE utf8_unicode_ci NOT NULL DEFAULT 'email',
#     `selected` tinyint(1) NOT NULL DEFAULT 0,
# UNIQUE KEY `recipient_id` (`recipient_id`,`user_id`,`type`)
# ) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;
#
# # Added specific settings of SMS provider to report, enabled SMS test mode
# UPDATE `reports`
# SET `settings` = CONCAT(REPLACE(`settings`, 'test_mode := 0', 'test_mode := 1'), '\r\nsms_gateway_url := http://api.smspro.bg/bsms/send\r\nsms_gateway_apikey := 6be04cd46a632ccb25de5527b1409779')
# WHERE `type` = 'customer_notification_sender' AND `settings` NOT LIKE '%sms_gateway_url%';
#
# # Added saved filters to specify search of groups of customers from report
# INSERT IGNORE INTO `filters` (`id`, `name`, `module`, `controller`, `module_from`, `controller_from`, `params`, `user_defined`, `active`, `added`, `added_by`, `modified`, `modified_by`) VALUES
# (NULL, 'Без активни договори', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjY0NDoiIyDQutC+0L3RgtGA0LDQs9C10L3RgtC4INGC0LjQvyDQutC70LjQtdC90YIgKNC40LQgMyksINC60L7QuNGC0L4g0LjQvNCw0YIg0LrRitC8INGB0LXQsdC1INGB0Lgg0LTQvtC60YPQvNC10L3RgtC4INGC0LjQvyDQlNC+0LPQvtCy0L7RgCDQt9CwINC30LDQtdC8ICjQuNC0IDYpINCyINGB0YLQsNGC0YPRgSDQl9Cw0YLQstC+0YDQtdC9INC/0L7Qs9Cw0YHQtdC9ICjQuNC0IDIwKSDQuCDQvdGP0LzQsNGCINC00L7Qs9C+0LLQvtGA0Lgg0LIg0YHRgtCw0YLRg9GB0Lgg0JfQsNGC0LLQvtGA0LXQvSDQn9GA0LXQvNC40L3QsNC7INC60YrQvCDRgdGK0LTQtdCx0L3QviDQtNC10LvQviAo0LjQtCAyMykg0Lgg0YHRgtCw0YLRg9GBINCe0YLQstC+0YDQtdC9INCQ0LrRgtC40LLQtdC9ICjQuNC0IDIyKQp3aGVyZSA6PSBjLnR5cGUgPSBcJzNcJwp3aGVyZSA6PSBjLmlkIElOIChTRUxFQ1QgY3VzdG9tZXIgRlJPTSBkb2N1bWVudHMgV0hFUkUgdHlwZSA9IFwnNlwnIEFORCBzdGF0dXMgPSBcJ2Nsb3NlZFwnIEFORCBzdWJzdGF0dXMgPSBcJzIwXCcpCndoZXJlIDo9IGMuaWQgTk9UIElOIChTRUxFQ1QgY3VzdG9tZXIgRlJPTSBkb2N1bWVudHMgV0hFUkUgdHlwZSA9IFwnNlwnIEFORCBzdWJzdGF0dXMgSU4gKFwnMjJcJywgXCcyM1wnKSkiO30=', 1, 1, NOW(), -1, NOW(), -1),
# (NULL, 'С оставаща последна вноска по кредит', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjExMjI6IiMg0LrQvtC90YLRgNCw0LPQtdC90YLQuCDRgtC40L8g0LrQu9C40LXQvdGCICjQuNC0IDMpLCDQutC+0LjRgtC+INC40LzQsNGCINC60YrQvCDRgdC10LHQtSDRgdC4INC00L7QutGD0LzQtdC90YLQuCDRgtC40L8g0JTQvtCz0L7QstC+0YAg0LfQsCDQt9Cw0LXQvCAo0LjQtCA2KSDQsiDRgdGC0LDRgtGD0YEg0J7RgtCy0L7RgNC10L0g0LDQutGC0LjQstC10L0gKNC40LQgMjIpINC4INC40LzQsNGCINC/0L7QstC10YfQtSDQvtGCINC10LTQvdCwINCy0L3QvtGB0LrQsCAo0L/QvtCy0LXRh9C1INC+0YIg0LXQtNC40L0g0YDQtdC0INCyIGd0MiDRgtCw0LHQu9C40YbQsNGC0LAg0LIg0LTQvtC60YPQvNC10L3RgiDRgtC40L8g0JTQvtCz0L7QstC+0YAg0LfQsCDQt9Cw0LXQvCAo0LjQtCA2KSDQuCDRgdCw0LzQviDQv9C+0YHQu9C10LTQvdCw0YLQsCDQstC90L7RgdC60LAg0LjQvNCwINGB0YLQvtC50L3QvtGB0YIg0L/Qvi3Qs9C+0LvRj9C80LAg0L7RgiAwLjAwINCyINC60L7Qu9C+0L3QsCBmcmVlX2ZpZWxkNSAo0J7QsdGJ0L4g0L7RgdGCLiDQvtGCINCy0L3QvtGB0LrQsCkg4oCTINC+0YHRgtCw0L3QsNC70LjRgtC1INGC0YDRj9Cx0LLQsCDQtNCwINGB0LAg0LjQt9C/0LvQsNGC0LXQvdC4ICjQsiDQutC+0LvQvtC90LAgZnJlZV9maWVsZDUgKNCe0LHRidC+INC+0YHRgi4g0L7RgiDQstC90L7RgdC60LApINGB0YPQvNCw0YLQsCDQtSDRgNCw0LLQvdCwINC90LAgMC4wMC4Kd2hlcmUgOj0gYy50eXBlID0gXCczXCcKd2hlcmUgOj0gYy5pZCBJTiAoU0VMRUNUIGQuY3VzdG9tZXIgRlJPTSBkb2N1bWVudHMgZCBKT0lOIGd0Ml9kZXRhaWxzIGcgT04gZC5pZCA9IGcubW9kZWxfaWQgQU5EIGcubW9kZWwgPSBcJ0RvY3VtZW50XCcgV0hFUkUgZC50eXBlID0gXCc2XCcgQU5EIGQuc3RhdHVzID0gXCdvcGVuZWRcJyBBTkQgZC5zdWJzdGF0dXMgPSBcJzIyXCcgR1JPVVAgYnkgZC5pZCBIQVZJTkcgQ09VTlQoZy5pZCkgPiAxIEFORCBTVU0oQ09OVkVSVChnLmZyZWVfZmllbGQ1LCBERUNJTUFMKDExLDIpKSkgPSAoU0VMRUNUIENPTlZFUlQoZzIuZnJlZV9maWVsZDUsIERFQ0lNQUwoMTEsMikpIEZST00gZ3QyX2RldGFpbHMgZzIgV0hFUkUgZzIuaWQgPSBNQVgoZy5pZCkpKSI7fQ==', 1, 1, NOW(), -1, NOW(), -1),
# (NULL, 'Частично погасени', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjkyMzoiIyDQstGB0LjRh9C60Lgg0LrQvtC90YLRgNCw0LPQtdC90YLQuCDRgtC40L8g0LrQu9C40LXQvdGCICjQuNC0IDMpLCDQutC+0LjRgtC+INC40LzQsNGCINC60YrQvCDRgdC10LHQtSDRgdC4INC00L7QutGD0LzQtdC90YIg0YLQuNC/INCU0L7Qs9C+0LLQvtGAINC30LAg0LfQsNC10LwgKNC40LQgNikg0LIg0YHRgtCw0YLRg9GBINCe0YLQstC+0YDQtdC9INCw0LrRgtC40LLQtdC9ICjQuNC0IDIyKSwg0LrQvtC40YLQviDQvdGP0LzQsNGCINC/0YDQvtGB0YDQvtGH0LXQvdC4INCy0L3QvtGB0LrQuCAo0L/RgNC+0YHRgNC+0YfQtdC90Lgg0YHQsCDRgtC10LfQuCDRgNC10LTQvtCy0LUg0LIgZ3QyINGC0LDQsdC70LjRhtCw0YLQsCDQutGK0Lwg0L/QvtCz0LDRgdC40YLQtdC70L3QuNGP0YIg0L/Qu9Cw0L0sINC30LAg0LrQvtC40YLQviDQtNCw0YLQsNGC0LAg0LIg0L/QvtC70LUgYXJ0aWNsZV9jb2RlICjQn9Cw0LTQtdC2INCy0L3QvtGB0LrQsCkg0LUg0LzQuNC90LDQu9CwINC4INGB0YPQvNCw0YLQsCDQsiDQv9C+0LvQtSAJYXJ0aWNsZV92b2x1bWUgKNCe0LHRidC+INC/0LvQsNGC0LXQvdC+INC/0L4g0LLQvdC+0YHQutCwKSDQtSDRgNCw0LLQvdCwINC90LAgMC4wMCDQu9CyLgp3aGVyZSA6PSBjLnR5cGUgPSBcJzNcJwp3aGVyZSA6PSBjLmlkIElOIChTRUxFQ1QgZC5jdXN0b21lciBGUk9NIGRvY3VtZW50cyBkIEpPSU4gZ3QyX2RldGFpbHMgZyBPTiBkLmlkID0gZy5tb2RlbF9pZCBBTkQgZy5tb2RlbCA9IFwnRG9jdW1lbnRcJyBXSEVSRSBkLnR5cGUgPSBcJzZcJyBBTkQgZC5zdGF0dXMgPSBcJ29wZW5lZFwnIEFORCBkLnN1YnN0YXR1cyA9IFwnMjJcJyBHUk9VUCBieSBkLmlkIEhBVklORyBDT1VOVChEQVRFKGcuYXJ0aWNsZV9jb2RlKSA8IENVUkRBVEUoKSBBTkQgZy5hcnRpY2xlX3ZvbHVtZSA9IDApID0gMCkiO30=', 1, 1, NOW(), -1, NOW(), -1),
# (NULL, 'Отхвърлени клиенти', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjQ0MDoiIyDQstGB0LjRh9C60Lgg0LrQvtC90YLRgNCw0LPQtdC90YLQuCDRgtC40L8g0LrQu9C40LXQvdGCICjQuNC0IDMpLCDQutC+0LjRgtC+INC40LzQsNGCINC60YrQvCDRgdC10LHQtSDRgdC4INC00L7QutGD0LzQtdC90YIg0YLQuNC/INCX0LDRj9Cy0LrQsCDQt9CwINC60YDQtdC00LjRgiAo0L7RgtGF0LLRitGA0LvQtdC90LApICjQuNC0IDE0KSDQuCDQvdGP0LzQsNGCINC00L7QutGD0LzQtdC90YIg0YLQuNC/INCU0L7Qs9C+0LLQvtGAINC30LAg0LfQsNC10LwgKNC40LQgNikuCndoZXJlIDo9IGMudHlwZSA9IFwnM1wnCndoZXJlIDo9IGMuaWQgSU4gKFNFTEVDVCBjdXN0b21lciBGUk9NIGRvY3VtZW50cyBXSEVSRSB0eXBlID0gXCcxNFwnKQp3aGVyZSA6PSBjLmlkIE5PVCBJTiAoU0VMRUNUIGN1c3RvbWVyIEZST00gZG9jdW1lbnRzIFdIRVJFIHR5cGUgPSBcJzZcJykiO30=', 1, 1, NOW(), -1, NOW(), -1);

######################################################################################
# 2017-12-15 - Updated saved filters to specify search of groups of customers from report for notification sending

# PRE-DEPLOYED # Updated saved filters to specify search of groups of customers from report for notification sending
# INSERT INTO `filters` (`id`, `name`, `module`, `controller`, `module_from`, `controller_from`, `params`, `user_defined`, `active`, `added`, `added_by`, `modified`, `modified_by`) VALUES
# (NULL, 'Частично погасени', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjkyMToiIyDQstGB0LjRh9C60Lgg0LrQvtC90YLRgNCw0LPQtdC90YLQuCDRgtC40L8g0LrQu9C40LXQvdGCICjQuNC0IDMpLCDQutC+0LjRgtC+INC40LzQsNGCINC60YrQvCDRgdC10LHQtSDRgdC4INC00L7QutGD0LzQtdC90YIg0YLQuNC/INCU0L7Qs9C+0LLQvtGAINC30LAg0LfQsNC10LwgKNC40LQgNikg0LIg0YHRgtCw0YLRg9GBINCe0YLQstC+0YDQtdC9INCw0LrRgtC40LLQtdC9ICjQuNC0IDIyKSwg0LrQvtC40YLQviDQvdGP0LzQsNGCINC/0YDQvtGB0YDQvtGH0LXQvdC4INCy0L3QvtGB0LrQuCAo0L/RgNC+0YHRgNC+0YfQtdC90Lgg0YHQsCDRgtC10LfQuCDRgNC10LTQvtCy0LUg0LIgZ3QyINGC0LDQsdC70LjRhtCw0YLQsCDQutGK0Lwg0L/QvtCz0LDRgdC40YLQtdC70L3QuNGP0YIg0L/Qu9Cw0L0sINC30LAg0LrQvtC40YLQviDQtNCw0YLQsNGC0LAg0LIg0L/QvtC70LUgYXJ0aWNsZV9jb2RlICjQn9Cw0LTQtdC2INCy0L3QvtGB0LrQsCkg0LUg0LzQuNC90LDQu9CwINC4INGB0YPQvNCw0YLQsCDQsiDQv9C+0LvQtSAJYXJ0aWNsZV92b2x1bWUgKNCe0LHRidC+INC/0LvQsNGC0LXQvdC+INC/0L4g0LLQvdC+0YHQutCwKSDQtSDRgNCw0LLQvdCwINC90LAgMC4wMCDQu9CyLgp3aGVyZSA6PSBjLnR5cGUgPSBcJzNcJwp3aGVyZSA6PSBjLmlkIElOIChTRUxFQ1QgZC5jdXN0b21lciBGUk9NIGRvY3VtZW50cyBkIEpPSU4gZ3QyX2RldGFpbHMgZyBPTiBkLmlkID0gZy5tb2RlbF9pZCBBTkQgZy5tb2RlbCA9IFwnRG9jdW1lbnRcJyBXSEVSRSBkLnR5cGUgPSBcJzZcJyBBTkQgZC5zdGF0dXMgPSBcJ29wZW5lZFwnIEFORCBkLnN1YnN0YXR1cyA9IFwnMjJcJyBHUk9VUCBieSBkLmlkIEhBVklORyBTVU0oREFURShnLmFydGljbGVfY29kZSkgPCBDVVJEQVRFKCkgQU5EIGcuYXJ0aWNsZV92b2x1bWUgPSAwKSA9IDApIjt9', 1, 1, NOW(), -1, NOW(), -1)
# ON DUPLICATE KEY UPDATE `params` = VALUES(`params`);

######################################################################################
# 2017-12-18 - Updated saved filters to specify search of groups of customers from report for notification sending

# PRE-DEPLOYED # Updated saved filters to specify search of groups of customers from report for notification sending
# INSERT INTO `filters` (`id`, `name`, `module`, `controller`, `module_from`, `controller_from`, `params`, `user_defined`, `active`, `added`, `added_by`, `modified`, `modified_by`) VALUES
# (NULL, 'Частично погасени', 'customers', 'customers', 'reports', 'customer_notification_sender', 'YToxOntzOjEzOiJzZWFyY2hfZmllbGRzIjtzOjk2MzoiIyDQstGB0LjRh9C60Lgg0LrQvtC90YLRgNCw0LPQtdC90YLQuCDRgtC40L8g0LrQu9C40LXQvdGCICjQuNC0IDMpLCDQutC+0LjRgtC+INC40LzQsNGCINC60YrQvCDRgdC10LHQtSDRgdC4INC00L7QutGD0LzQtdC90YIg0YLQuNC/INCU0L7Qs9C+0LLQvtGAINC30LAg0LfQsNC10LwgKNC40LQgNikg0LIg0YHRgtCw0YLRg9GBINCe0YLQstC+0YDQtdC9INCw0LrRgtC40LLQtdC9ICjQuNC0IDIyKSwg0LrQvtC40YLQviDQvdGP0LzQsNGCINC/0YDQvtGB0YDQvtGH0LXQvdC4INCy0L3QvtGB0LrQuCAo0L/RgNC+0YHRgNC+0YfQtdC90Lgg0YHQsCDRgtC10LfQuCDRgNC10LTQvtCy0LUg0LIgZ3QyINGC0LDQsdC70LjRhtCw0YLQsCDQutGK0Lwg0L/QvtCz0LDRgdC40YLQtdC70L3QuNGP0YIg0L/Qu9Cw0L0sINC30LAg0LrQvtC40YLQviDQtNCw0YLQsNGC0LAg0LIg0L/QvtC70LUgYXJ0aWNsZV9jb2RlICjQn9Cw0LTQtdC2INCy0L3QvtGB0LrQsCkg0LUg0LzQuNC90LDQu9CwINC4INGB0YPQvNCw0YLQsCDQsiDQv9C+0LvQtSAJYXJ0aWNsZV92b2x1bWUgKNCe0LHRidC+INC/0LvQsNGC0LXQvdC+INC/0L4g0LLQvdC+0YHQutCwKSDQtSDRgNCw0LLQvdCwINC90LAgMC4wMCDQu9CyLgp3aGVyZSA6PSBjLnR5cGUgPSBcJzNcJwp3aGVyZSA6PSBjLmlkIElOIChTRUxFQ1QgZC5jdXN0b21lciBGUk9NIGRvY3VtZW50cyBkIEpPSU4gZ3QyX2RldGFpbHMgZyBPTiBkLmlkID0gZy5tb2RlbF9pZCBBTkQgZy5tb2RlbCA9IFwnRG9jdW1lbnRcJyBXSEVSRSBkLnR5cGUgPSBcJzZcJyBBTkQgZC5zdGF0dXMgPSBcJ29wZW5lZFwnIEFORCBkLnN1YnN0YXR1cyA9IFwnMjJcJyBHUk9VUCBieSBkLmlkIEhBVklORyBTVU0oREFURShnLmFydGljbGVfY29kZSkgPCBDVVJEQVRFKCkgQU5EIGcuYXJ0aWNsZV92b2x1bWUgPSAwKSA9IDAgQU5EIE1JTihEQVRFKGcuYXJ0aWNsZV9jb2RlKSkgPCBDVVJEQVRFKCkpIjt9', 1, 1, NOW(), -1, NOW(), -1)
# ON DUPLICATE KEY UPDATE `params` = VALUES(`params`);

########################################################################
# 2017-12-20 - Added new report 'creditins_distribution_by_components' for CreditIns installation (CREDITINS)
#            - Added freezed headers for 'creditins_distribution_by_components' report

# Added new report 'creditins_distribution_by_components' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (379, 'creditins_distribution_by_components', '', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (379, 'Събрани суми по пера, продукт, служител', '', NULL, 'bg'),
  (379, 'Collected sums by components, product and employee', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '379', '0', '1'),
  ('reports', 'export', '379', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '379';

# Added freezed headers for 'creditins_distribution_by_components' report
UPDATE `reports` SET `settings`='freeze_table_headers := 1' WHERE `type`='creditins_distribution_by_components' AND `settings` NOT LIKE '%freeze_table_headers%';

########################################################################
# 2018-03-29 - Increased the field size for MERCHANTID in plugin_pay_requests and plugin_pay_confirms

# Increased the field size for MERCHANTID in plugin_pay_requests and plugin_pay_confirms
ALTER TABLE `plugin_pay_requests`
  CHANGE COLUMN `MERCHANTID` `MERCHANTID` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci' AFTER `TYPE`;
ALTER TABLE `plugin_pay_confirms`
  CHANGE COLUMN `MERCHANTID` `MERCHANTID` VARCHAR(255) NOT NULL COLLATE 'utf8_unicode_ci' AFTER `IDN`;

########################################################################
# 2018-06-26 - Added additional settings for the 'creditins_proceedings_by_client' report

# Added additional settings for the 'creditins_proceedings_by_client' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_expenses_list_ids :=', '\r\nproject_date_transfer_responsible := date_transfer_resposible\r\n\r\nnom_expenses_list_ids :=') WHERE `type` LIKE '%creditins_proceedings_by_client%' AND `settings` NOT LIKE '%project_date_transfer_responsible%';

########################################################################
# 2018-06-29 - Added new report 'creditins_phone_search' for CreditIns installation (CREDITINS)

# Added new report 'creditins_phone_search' for CreditIns installation (CREDITINS)
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (386, 'creditins_phone_search', 'customer_client_type := 3\r\ncustomer_potential_client_type := 2\r\n\r\ncustomer_contact_email := c_person_email\r\ncustomer_contact_phone := c_person_phone\r\ncustomer_workplace := workplace', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (386, 'Търсене по телефонен номер', '', NULL, 'bg'),
  (386, 'Search by phone number', '', NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', '386', '0', '1'),
  ('reports', 'export', '386', '0', '2');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all' FROM `roles_definitions`
  WHERE `module` = 'reports' AND `action` IN ('generate_report', 'export') AND `model_type` = '386';

########################################################################
# 2018-07-05 - Added new automation to create the payments table and the related incomes reason when a loan contract is signed

# Added new automation to create the payments table and the related incomes reason when a loan contract is signed
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Създаване на падежна таблица при подписване на договор', 0, NULL, 1, 'documents', NULL, 'action', '6', 'credit_amount := grant_credit\r\ncredit_interest := contract_rate\r\ncredit_warranty := contract_warranty\r\ncredit_periods := repayment_period\r\ncredit_product := credit_product_id\r\ncredit_product_name := credit_product\r\ncredit_status_active := opened_22\r\ncredit_tag_export := 5\r\n\r\nfir_type_incomes_reason_loan_contract_type := 104\r\nfir_type_incomes_reason_loan_contract_company := 1\r\nfir_type_incomes_reason_loan_contract_office := 1\r\nfir_type_incomes_reason_loan_contract_payment_type := bank\r\nfir_type_incomes_reason_loan_contract_payment_container := 1\r\nfir_type_incomes_reason_loan_contract_article_id := 57', 'condition := \'[prev_date]\' == \'\' && \'[date]\' != \'\'', 'plugin := creditins\r\nmethod := createContractDeadlineTable', NULL, 0, 1, 1);

########################################################################
# 2018-07-09 - Fix the settings of the report 'creditins_proceedings_by_client' so the settings to match the correct vars

# Fix the settings of the report 'creditins_proceedings_by_client' so the settings to match the correct vars
UPDATE `reports` SET `settings`='customer_type_client := 3\r\ncustomer_type_court := 4\r\ncustomer_type_judge := 5\r\n\r\nprojects_type_id := 1\r\nproject_proceeding_date := date_proceedings\r\nproject_lawsuit_number := lawsuit_number\r\nproject_lawsuit_status := lawsuit_status_id\r\nproject_court := court_id\r\nproject_private_judge := private_judge_id\r\nproject_proceeding_type := lawsuit_type_id\r\nproject_total_costs := total_costs\r\nproject_lawyer_tax_zd := lawyer_imperatives__amount\r\nproject_state_tax_zd := statetax_imper__amount\r\nproject_other_tax_zd := imperatives_fee__amount\r\nproject_lawyer_tax_id := enforcement_advocate__amount\r\nproject_state_tax_id := state_enforce__amount\r\nproject_other_tax_id := enforce_tax__amount\r\nproject_lawyer_tax_ip := claim_jurist__amount\r\nproject_state_tax_ip := tax_claim__amount\r\nproject_other_tax_ip := other_fee__amount\r\nproject_expert_tax_ip := expert_tax__amount\r\n\r\nproject_court_confirmed_sum := total_court_recognized\r\nproject_total_paid_sum := total_paid_duty\r\nproject_court_before_pay := total_before_court\r\n\r\nproject_principal_payment := registered_principal__payment\r\nproject_interest_payment := registered_interest__payment\r\nproject_warranty_payment := registered_warranty__payment\r\nproject_law_interest_left_payment := interest_according__payment\r\nproject_law_interest_payment := penalty_int__payment\r\nproject_payment_lawyer_tax_zd := lawyer_imperatives__payment\r\nproject_payment_state_tax_zd := statetax_imper__payment\r\nproject_payment_other_tax_zd := imperatives_fee__payment\r\nproject_payment_lawyer_tax_id := enforcement_advocate__payment\r\nproject_payment_state_tax_id := state_enforce__payment\r\nproject_payment_other_tax_id := enforce_tax__payment\r\nproject_payment_lawyer_tax_ip := claim_jurist__payment\r\nproject_payment_state_tax_ip := tax_claim__payment\r\nproject_payment_other_tax_ip := other_fee__payment\r\nproject_payment_expert_tax_ip := expert_tax__payment\r\n\r\nproject_remain_payment := remain_payment\r\nproject_principal_left := registered_principal__rest\r\nproject_interest_left := registered_interest__rest\r\nproject_warranty_left := registered_warranty__rest\r\nproject_law_interest_left_left := interest_according__rest\r\nproject_law_interest_left := penalty_int__rest\r\nproject_left_lawyer_tax_zd := lawyer_imperatives__rest\r\nproject_left_state_tax_zd := statetax_imper__rest\r\nproject_left_other_tax_zd := imperatives_fee__rest\r\nproject_left_lawyer_tax_id := enforcement_advocate__rest\r\nproject_left_state_tax_id := state_enforce__rest\r\nproject_left_other_tax_id := enforce_tax__rest\r\nproject_left_lawyer_tax_ip := claim_jurist__rest\r\nproject_left_state_tax_ip := tax_claim__rest\r\nproject_left_other_tax_ip := other_fee__rest\r\nproject_left_expert_tax_ip := expert_tax__rest\r\n\r\nproject_expenses_expense_type := cost_type_id\r\nproject_expenses_expense_date := expense_date\r\nproject_expenses_amount := amount_expense\r\nproject_incomes_expense_type := profit_type_id\r\nproject_incomes_expense_date := income_date\r\nproject_incomes_amount := amount_income\r\nproject_contract_num := contract_num_id\r\nproject_date_transfer_responsible := date_transfer_resposible\r\n\r\nnom_expenses_list_ids := 96, 28, 99, 97, 50, 100, 98, 51, 101, 102\r\nnom_incomes_list_ids := 96, 28, 99, 97, 50, 100, 98, 51, 101, 102, 104, 105, 106, 107, 108\r\n\r\nnomenclatures_type_proceeding_to_include := 95,94,24\r\nfreeze_table_headers := 1' WHERE `type`='creditins_proceedings_by_client' AND `settings` LIKE '%project_payment_other_tax_id := state_enforce__payment%';

########################################################################
# 2018-07-10 - Update the conditions which activates 'createContractDeadlineTable' to include '0000-00-00' as possible previous value

# Update the conditions which activates 'createContractDeadlineTable' to include '0000-00-00' as possible previous value
UPDATE `automations` SET `conditions`='condition := (\'[prev_date]\' == \'\' || \'[prev_date]\' == \'0000-00-00\') && (\'[date]\' != \'\' && \'[date]\' != \'0000-00-00\')' WHERE `method` LIKE '%createContractDeadlineTable%' AND `conditions` NOT LIKE '%0000-00-00%';

########################################################################
# 2018-07-18 - Added new automations for automatic distribution of the payment

# Added new automations for automatic distribution of the payment
SET @new_position := (SELECT MAX(`position`)+1 FROM `automations` WHERE `module`='finance' AND `controller`='payments' AND `start_model_type`='BP');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Автоматично разпределяне на Съдебно плащане след отбелязване с таг', 0, NULL, 1, 'finance', 'payments', 'action', 'BP', 'complete_order := interest_according__rest,penalty_int__rest,registered_warranty__rest,registered_interest__rest,registered_principal__rest,lawyer_imperatives__rest,statetax_imper__rest,imperatives_fee__rest,enforcement_advocate__rest,state_enforce__rest,enforce_tax__rest,tax_claim__rest,other_fee__rest, expert_tax__rest\r\nproceeding_id := 1\r\nproceeding_remain_payment := remain_payment\r\nproceeding_paid := total_paid_duty\r\nproceeding_profit_income_date := income_date\r\nproceeding_profit_type := profit_type_id\r\nproceeding_profit_type_name := profit_type\r\nproceeding_profit_description := description_income\r\nproceeding_profit_proceeding := suit_kind\r\nproceeding_profit_amount := amount_income\r\n\r\nnomenclature_type_income := 8\r\nnomenclature_type_income_lawsuit :=lawsuit_type\r\narticle_id_lawyer_imperatives := 96\r\narticle_id_statetax_imper := 28\r\narticle_id_imperatives_fee := 99\r\narticle_id_enforcement_advocate := 97\r\narticle_id_state_enforce := 50\r\narticle_id_enforce_tax := 100\r\narticle_id_tax_claim := 51\r\narticle_id_other_fee := 101\r\narticle_id_expert_tax := 102\r\narticle_id_registered_principal := 104\r\narticle_id_registered_interest := 105\r\narticle_id_registered_warranty := 106\r\narticle_id_interest_according := 107\r\narticle_id_penalty_int := 108', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\', \'add\'))\r\ncondition := (!$old_model->get(\'tags\') || !in_array(10, $old_model->get(\'tags\')))\r\ncondition := $new_model->getTags() && in_array(10, $new_model->get(\'tags\'))', 'plugin := creditins\r\nmethod := tagProceedingsPayments', NULL, @new_position, 1, 1);
SET @new_position := (SELECT MAX(`position`)+1 FROM `automations` WHERE `module`='finance' AND `controller`='payments' AND `start_model_type`='PKO');
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Автоматично разпределяне на Съдебно плащане след отбелязване с таг', 0, NULL, 1, 'finance', 'payments', 'action', 'PKO', 'complete_order := interest_according__rest,penalty_int__rest,registered_warranty__rest,registered_interest__rest,registered_principal__rest,lawyer_imperatives__rest,statetax_imper__rest,imperatives_fee__rest,enforcement_advocate__rest,state_enforce__rest,enforce_tax__rest,tax_claim__rest,other_fee__rest, expert_tax__rest\r\nproceeding_id := 1\r\nproceeding_remain_payment := remain_payment\r\nproceeding_paid := total_paid_duty\r\nproceeding_profit_income_date := income_date\r\nproceeding_profit_type := profit_type_id\r\nproceeding_profit_type_name := profit_type\r\nproceeding_profit_description := description_income\r\nproceeding_profit_proceeding := suit_kind\r\nproceeding_profit_amount := amount_income\r\n\r\nnomenclature_type_income := 8\r\nnomenclature_type_income_lawsuit :=lawsuit_type\r\narticle_id_lawyer_imperatives := 96\r\narticle_id_statetax_imper := 28\r\narticle_id_imperatives_fee := 99\r\narticle_id_enforcement_advocate := 97\r\narticle_id_state_enforce := 50\r\narticle_id_enforce_tax := 100\r\narticle_id_tax_claim := 51\r\narticle_id_other_fee := 101\r\narticle_id_expert_tax := 102\r\narticle_id_registered_principal := 104\r\narticle_id_registered_interest := 105\r\narticle_id_registered_warranty := 106\r\narticle_id_interest_according := 107\r\narticle_id_penalty_int := 108', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\', \'add\'))\r\ncondition := (!$old_model->get(\'tags\') || !in_array(10, $old_model->get(\'tags\')))\r\ncondition := $new_model->getTags() && in_array(10, $new_model->get(\'tags\'))', 'plugin := creditins\r\nmethod := tagProceedingsPayments', NULL, @new_position, 1, 1);

########################################################################
# 2018-07-19 - Change the name ot the method which distribute the proceedings payments

# Change the name ot the method which distribute the proceedings payments
UPDATE `automations` SET `method`=REPLACE(`method`, 'tagProceedingsPayments', 'distributePaymentsByProceedings') WHERE `method` LIKE '%tagProceedingsPayments%';

########################################################################
# 2018-07-25 - Added rest settings for the CreditinsApi
#            - Added email template for password reset procedure used by CreditinsApi
#            - Added automations used by CreditinsApi

# Added rest settings for the CreditinsApi
UPDATE settings SET value='CreditInsAPI' WHERE section='rest' AND name='allowed_rest_user_agents';
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_customers_3', 'id, type, name, lastname, web_user, web_pass, ekatte_id, ekatte_name, address, gsm, phone, email, ucn, identity_num, identity_date, identity_valid, address_by_personal_id, workplace, position, contracttype, netsalary, web_access_token, web_expiration_timestamp, web_pass_recovery_expiration_timestamp, added, modified, deleted');
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_documents_19', 'id, type, name, customer, substatus, cred_product_id, requested_sum, number_timeunits, tax_garant, notes, promocode_id, vnoskatotal, vnoskaglavnica, vnoskalihva, vnoskagarant, sum_for_return, garant, lihva, added, modified, deleted');
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_documents_6', 'id, type, name, customer, substatus, date, grant_credit, credit_product_id, repayment_period, type_reporting_period, contract_warranty, grant_add_info, contract_total_amount_fee, vnoska_glavnica, vnoska_rate, vnoska_warranty, sum_for_return, group_table_2, added, modified, deleted');
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_nomenclatures_18', 'id, type, name, disc_percent, added, modified, deleted');
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_nomenclatures_19', 'id, type, name, added, modified, deleted');
INSERT IGNORE INTO `settings` (`section`, `name`, `value`) VALUES ('rest', 'filter_vars_nomenclatures_5', 'id, type, name, minsum, maxsum, timeunits, mintimeunits, maxtimeunits, year_lihven_percent, gpr, group_tax_garant, group_minmaxperiodiposumi, added, modified, deleted');

# Added email template for password reset procedure used by CreditinsApi
INSERT IGNORE INTO `emails` (`id`, `model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`)
VALUES (1025, 'Customer', 3, '', 1, 1, now(), 1, now(), 1, '0000-00-00 00:00:00', 0);
INSERT IGNORE INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
(1025, 'ForgottenPass', 'За да възстановите паролата си <a href="http://example.com/?token=[a_web_pass_recovery_token]">натиснете тук</a>.', '', 'bg', now());

# Added automations used by CreditinsApi
INSERT IGNORE INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`,
`automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
VALUES (NULL, 'Изпращане при промяна на парола на Customer', 0, NULL, 1, 'customers', NULL, 'action', '3',
'email_template := 1025\r\ncustomers := b_id\r\nnot_system_email := 1\r\nsend_to_email := <EMAIL>, <EMAIL>',
'condition := (\'[prev_a_web_pass_recovery_token]\' != \'[a_web_pass_recovery_token]\') && \'[a_web_pass_recovery_token]\' != \'\'', 'method := sendMail', NULL, 0, 0, 1),
(NULL, 'Валидиране за уникалност на email', 0, NULL, 1, 'customers', NULL, 'before_action', '3', '', '# само при въведен поне един email\r\ncondition := \'[request_is_post]\'\r\ncondition := !empty((array_filter($request->get(\'link\'), function($v, $k) use ($request) {$found_emails = array_filter($request->get(\'link_types\'), function($a){return $a == \'email\';}); return !empty($found_emails) && in_array($k, array_keys($found_emails)) && !empty($v);}, ARRAY_FILTER_USE_BOTH)))', 'method := validateUnique\r\nunique_keys := b_email', 'cancel_action_on_fail := 1', 0, 0, 1),
(NULL, 'Валидиране за уникалност на username', 0, NULL, 1, 'customers', NULL, 'before_action', '3', '', '# само при избрана стойност за web_use_site\r\ncondition := \'[request_is_post]\' && $request->get(\'web_use_site\')', 'method := validateUnique\r\nunique_keys := a_web_user', 'cancel_action_on_fail := 1', 1, 0, 1),
(NULL, 'Криптиране на парола', 0, NULL, 1, 'customers', NULL, 'before_action', '3', '', '# само при добавяне и редакция\r\ncondition := \'[request_is_post]\'\r\ncondition := (\'[action]\' == \'add\' || \'[action]\' == \'edit\')\r\n# само при избрана стойност за web_use_site \r\ncondition := \'[a_web_use_site]\' == 1\r\n# и когато паролата не е била криптирана - не започва с $  (plain text)\r\ncondition :=  \'[a_web_pass]\' != \'\'\r\ncondition := !preg_match(\'#^\\$#\', \'[a_web_pass]\')', 'method := setAdditionalVar\r\nvar_name := web_pass\r\n\r\nvar_value := php(password_hash(\'[a_web_pass]\', PASSWORD_BCRYPT))', '', 2, 0, 1);

########################################################################
# 2018-07-26 - Added new setting for declined client tag in 'creditins_proceedings_by_client' report

# Added new setting for declined client tag in 'creditins_proceedings_by_client' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ncustomer_tag_declined := 2') WHERE `type`='creditins_phone_search' AND `settings` NOT LIKE '%customer_tag_declined%';

########################################################################
# 2018-08-01 - Updated the `settings` of the distributePaymentsByProceedings automation and the conditions which start it

# Updated the `settings` of the distributePaymentsByProceedings automation and the conditions which start it
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\narticle_id_claim_jurist := 98') WHERE `method` LIKE '%distributePaymentsByProceedings%' AND `method` LIKE '%creditins%' AND `settings` NOT LIKE '%article_id_claim_jurist%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'tax_claim__rest,', 'tax_claim__rest,claim_jurist__rest,') WHERE `method` LIKE '%distributePaymentsByProceedings%' AND `method` LIKE '%creditins%' AND `settings` NOT LIKE '%claim_jurist__rest%';
UPDATE `automations` SET `conditions`=REPLACE(`conditions`, 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\', \'add\'))\r\n', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\', \'add\', \'multiadd\'))\r\n') WHERE `method` LIKE '%distributePaymentsByProceedings%' AND `method` LIKE '%creditins%' AND `conditions` NOT LIKE '%multiadd%';

########################################################################
# 2018-08-27 - Update the `settings` of the creditins_proceedings_by_client reports to contain the additional vars for clients

# Update the `settings` of the creditins_proceedings_by_client reports to contain the additional vars for clients
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nprojects_type_id := 1', '\r\n\r\ncustomer_client_workplace := workplace\r\ncustomer_client_contact_name := c_person_name\r\ncustomer_client_contact_lastname := c_person_name_f\r\ncustomer_client_contact_phone := c_person_phone\r\n\r\nprojects_type_id := 1') WHERE `type` LIKE '%creditins_proceedings_by_client%' AND `settings` NOT LIKE '%customer_client_workplace%';

########################################################################
# 2018-09-20 - Update the finance setting for automatic distribution of payments (set to 'suggest')
#            - Added automation which will distribute the payments when the 'not distributed' payments tag is removed
#            - Added automation which will validate the removing of the 'not distributed' payments tag
#            - Added settings for tag 'not distributed' payments tag and send_to_email to distributePaymentByRepaymentSchedule automation

# Update the finance setting for automatic distribution of payments (set to 'suggest')
UPDATE `settings` SET `value`='suggest' WHERE `section`='finance' AND `name`='payments_auto_balance' AND `value`='yes';

# Added automation which will distribute the payments when the 'not distributed' payments tag is removed
# Added automation which will validate the removing of the 'not distributed' payments tag
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'BP', 'incomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\ndocument_substatus_proceeding := 23\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\nschedule_var_repayment_date := repayment_day\r\nschedule_var_type_repayment := type_repayment\r\nschedule_type_repayment_in_advance := 1\r\nschedule_type_repayment_in_time := 2\r\n\r\nstatus_fully_paid := closed_20', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\'))\r\ncondition := ($old_model->get(\'tags\') && in_array(15, $old_model->get(\'tags\')))\r\ncondition := ($new_model->getTags() && (!$new_model->get(\'tags\') || !in_array(15, $new_model->get(\'tags\'))))', 'plugin := creditins\r\nmethod := distributePaymentByRepaymentSchedule', NULL, 1, 0, 1),
('Разпределяне на плащане към погасителни планове', 0, NULL, 1, 'finance', 'payments', 'action', 'PKO', 'incomes_reason_type_id := 104\r\ndocument_repayment_schedule_type_id := 6\r\ndocument_substatus_proceeding := 23\r\n\r\nschedule_var_gt2_id := column_connection_id\r\nschedule_var_gt2_row := column_connection\r\nschedule_var_payment_id := payment_document_id\r\nschedule_var_payment_num := payment_document\r\nschedule_var_payment_date := date_payment_doc\r\nschedule_var_principal := principal\r\nschedule_var_warranty := amount_warranty\r\nschedule_var_interest := contractual_interest\r\nschedule_var_penalty := penalty_interest\r\nschedule_var_repayment_date := repayment_day\r\nschedule_var_type_repayment := type_repayment\r\nschedule_type_repayment_in_advance := 1\r\nschedule_type_repayment_in_time := 2\r\n\r\nstatus_fully_paid := closed_20', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\'))\r\ncondition := ($old_model->get(\'tags\') && in_array(15, $old_model->get(\'tags\')))\r\ncondition := ($new_model->getTags() && (!$new_model->get(\'tags\') || !in_array(15, $new_model->get(\'tags\'))))', 'plugin := creditins\r\nmethod := distributePaymentByRepaymentSchedule', NULL, 1, 0, 1),
('Валидация на премахнване на таг неразпределено плащане', 0, NULL, 1, 'finance', 'payments', 'before_action', 'BP', 'document_repayment_schedule_type_id := 6\r\nstatus_fully_paid := closed_20\r\npayment_tag_not_distributed_payment := 15', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\'))', 'plugin := creditins\r\nmethod := validateUndistributedTagRemoval', 'cancel_action_on_fail := 1', 0, 0, 1),
('Валидация на премахнване на таг неразпределено плащане', 0, NULL, 1, 'finance', 'payments', 'before_action', 'PKO', 'document_repayment_schedule_type_id := 6\r\nstatus_fully_paid := closed_20\r\npayment_tag_not_distributed_payment := 15', 'condition := in_array(\'[action]\', array(\'tag\', \'multitag\'))', 'plugin := creditins\r\nmethod := validateUndistributedTagRemoval', 'cancel_action_on_fail := 1', 0, 0, 1);

# Added settings for tag 'not distributed' payments tag and send_to_email to distributePaymentByRepaymentSchedule automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\npayment_tag_not_distributed_payment := 15\r\n\r\nsend_to_email :=') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` NOT LIKE '%payment_tag_not_distributed_payment%';

########################################################################
# 2018-09-25 - Added automation which will change the tags of a customer when a loan request is approved ot declined

# Added automation which will change the tags of a customer when a loan request is approved ot declined
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Промяна на статуса на заявка за кредит (отхвърляне)', 0, NULL, 1, 'documents', NULL, 'action', '19', 'status_request_approved := 26\r\nstatus_request_declined := 27\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\n\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\n\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\n\r\ndocument_loan_contract := 6', 'condition := \'[b_substatus]\' == \'27\' && \'[prev_b_substatus]\' != \'27\'', 'plugin := creditins\r\nmethod := processCreditRequestStatusChange', NULL, 1, 1, 1),
('Промяна на статуса на заявка за кредит (одобряване)', 0, NULL, 1, 'documents', NULL, 'action', '19', 'status_request_approved := 26\r\nstatus_request_canceled := 27\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\n\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\n\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\n\r\ndocument_loan_contract := 6', 'condition := \'[b_substatus]\' == \'26\' && \'[prev_b_substatus]\' != \'26\'', 'plugin := creditins\r\nmethod := processCreditRequestStatusChange', NULL, 1, 1, 1);

########################################################################
# 2018-10-02 - Added setting for tag undistributed payment in 'creditins_customer_file' report
#            - Updated the settings of the distributePaymentByRepaymentSchedule automation so it will contain the email id and the users which will be notified

# Added setting for tag undistributed payment in 'creditins_customer_file' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\npayment_tag_not_distributed_payment := 15') WHERE `type`='creditins_customer_file' AND `settings` NOT LIKE '%payment_tag_not_distributed_payment%';

# Updated the settings of the distributePaymentByRepaymentSchedule automation so it will contain the email id and the users which will be notified
UPDATE `automations` SET `settings` = CONCAT(`settings`, '\r\n\r\nemail_id := 1027\r\nusers :=') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` NOT LIKE '%email_id%';

########################################################################
# 2018-10-09 - Update the automations for distribution with the correct status which to be observed
#            - Added placeholder for customer name and its link to his/hers client file report
#            - Update the content of the notification e-mail body to contain the new placeholder

# Update the automations for distribution with the correct status which to be observed
UPDATE `automations` SET `settings` = REPLACE(`settings`, 'status_fully_paid := closed_20', 'status_active := opened_22') WHERE `method` LIKE '%validateUndistributedTagRemoval%' AND `settings` NOT LIKE '%status_active%';
UPDATE `automations` SET `settings` = REPLACE(`settings`, 'status_fully_paid :=', 'status_active := opened_22\r\nstatus_fully_paid :=') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` NOT LIKE '%status_active%';

# Added placeholder for customer name and its link to his/hers client file report
INSERT INTO `placeholders` (`id`, `varname`, `model`, `type`, `usage`, `pattern_id`, `source`, `multilang`) VALUES
(NULL, 'customer_name_report_link', 'Document', 'send', 'emails', ',1027,', 'customer_name_report_link', 0);
INSERT INTO `placeholders_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Име на клиент/линк към Досие на клиент', NULL, 'bg'),
(LAST_INSERT_ID(), 'Client name/link to Customer file', NULL, 'en');

# Update the content of the notification e-mail body to contain the new placeholder
UPDATE `emails_i18n` SET `body`=REPLACE(`body`, '[customer_name]&nbsp;[customer_lastname]', '[customer_name_report_link]') WHERE `parent_id`=1027 AND `body` NOT LIKE '%[customer_name_report_link]%';

########################################################################
# 2018-10-11 - Fixed the conditions of the validation of email of clients (customers 3)

# Fixed the conditions of the validation of email of clients (customers 3)
UPDATE automations SET conditions='# само при въведен поне един email\r\ncondition := \'[request_is_post]\'\r\ncondition := !empty($request->get(\'link\'))\r\ncondition := !empty($request->get(\'link_types\'))\r\ncondition := !empty((array_filter($request->get(\'link\'), function($v, $k) use ($request) {$found_emails = array_filter($request->get(\'link_types\'), function($a){return $a == \'email\';}); return !empty($found_emails) && in_array($k, array_keys($found_emails)) && !empty($v);}, ARRAY_FILTER_USE_BOTH)))'
WHERE method like '%validateUnique%' AND conditions LIKE '%found_emails%';

########################################################################
# 2018-10-21 - Added new dashlet 'creditins_customers_with_multiple_active_contracts' for CreditIns installation

# Added new dashlet 'creditins_customers_with_multiple_active_contracts' for CreditIns installation
INSERT INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`) VALUES
('creditins_customers_with_multiple_active_contracts', 'customer_type_client := 3\r\ndocument_type_contract := 6\r\ndocument_active_status := opened_22\r\n\r\nrecords_per_page := 5', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Клиенти с повече от един активен договор', 'Списък на клиенти с повече от един активен договор', 'bg'),
(LAST_INSERT_ID(), 'Clients with more than one active contracts', 'List of clients with more than one active contracts', 'en');

########################################################################
# 2018-10-23 - Added editing to customers data and tags after the loan contract is signed

# Added editing to customers data and tags after the loan contract is signed
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnot_change_ckr_code := 2') WHERE `method` LIKE '%processCreditRequestStatusChange%' AND `settings` NOT LIKE '%not_change_ckr_code%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\nnot_change_ckr_code := 2') WHERE `method` LIKE '%createContractDeadlineTable%' AND `settings` NOT LIKE '%not_change_ckr_code%';

########################################################################
# 2019-02-20 - Added approval and confirmation of loan request in the CreditinsApi

# Added hidden variable for email in the Contracts (document type 6)
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(1747, 'Document', 6, 'use_email', 'text', 'text', 0, 1, '', '', 0, 1, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 3767, 60, '', '', '');
INSERT IGNORE INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(1747, 'label', 'Email', 'bg');

# Added REST fields for related persons data (bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone)
UPDATE `settings` SET `value`='id, type, name, customer, use_email, substatus, bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone, cred_product_id, requested_sum, number_timeunits, tax_garant, notes, promocode_id, vnoskatotal, vnoskaglavnica, vnoskalihva, vnoskagarant, sum_for_return, garant, lihva, added, modified, deleted' WHERE  name='filter_vars_documents_19';
UPDATE `settings` SET `value`='id, type, name, lastname, web_user, web_pass, bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone, ekatte_id, ekatte_name, address, gsm, phone, email, ucn, identity_num, identity_date, identity_valid, address_by_personal_id, workplace, position, contracttype, netsalary, web_access_token, web_expiration_timestamp, web_pass_recovery_expiration_timestamp, added, modified, deleted' WHERE  name='filter_vars_customers_3';

# Added REST fields for email templates
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'filter_vars_emails', 'id, name, subject, body');

# Added automations to create request token and send email upon loan request approval in the loan request document (19)
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Установяване на token при добавяне на заявка за кредит', 0, NULL, 1, 'documents', NULL, 'action', '19', '', 'condition := \'[action]\' == \'add\'', 'method := setAdditionalVar\r\nvar_name := request_token\r\nvar_value := php(md5(time()))', NULL, 2, 1, 1),
('Изпращане на писмо към кредитоискател след потвърдена заявка за кредит', 0, NULL, 1, 'documents', NULL, 'action', '19', 'email_template := 1033\r\nemails := a_use_email\r\nnot_system_email := 1', 'condition := \'[prev_b_substatus]\' != 26 && \'[b_substatus]\' == 26', 'method := sendMail', NULL, 0, 1, 1);

# Added automations to send emails upon loan request confirmation in contract document (6)
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Изпращане на писмо към кредитоискател след създаване на договор за кредит', 0, NULL, 1, 'documents', NULL, 'action', '6', 'email_template := 1034\r\nemails := a_use_email\r\nattached_files_templates := 44\r\nnot_system_email := 1', '#IMPORTANT: the action should be edit, not add (the document has been transformed)\r\ncondition := \'[action]\' == \'edit\'', 'method := sendMail', NULL, 10, 1, 1);

# Fixed the automation that sets the name of the contract document (6) to be action instead of before_action
UPDATE `automations` SET `conditions`='condition := \'[action]\' == \'edit\'', `automation_type`='action', `nums`='1', `active`='1' WHERE  `id`=44;

# Added approval and confirmation emails
INSERT IGNORE INTO `emails` (`id`, `model`, `model_type`, `name`, `active`, `group`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(1033, 'Document', 19, '', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0),
(1034, 'Document',  6, '', 1, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
UPDATE emails SET model='Document', model_type=19 WHERE id=1033;
UPDATE emails SET model='Document',  model_type=6 WHERE id=1034;
INSERT IGNORE INTO `emails_i18n` (`parent_id`, `subject`, `body`, `description`, `lang`, `translated`) VALUES
(1033, 'Изпратената от Вас заявка за получаване на кредит е одобрена!', '<p>Здравейте, <strong>[customer_name] [customer_lastname]</strong></p>\r\n\r\n<p>Благодарим Ви, че ни избрахте!</p>\r\n\r\n<p>Честито!</p>\r\n\r\n<p>Изпратената от Вас заявка за получаване на кредит е одобрена!</p>\r\n\r\n<p>Изпращаме Ви информация за начина на получаване на исканата от Вас сума, като моля да следвате посочените няколко лесни стъпки:</p>\r\n\r\n<p>При изпращане на заявката Ви за кредит сте посочили, че желаете &quot;КРЕДИТ ИНС&quot; ООД, да ангажира за Вас ДРУЖЕСТВО ГАРАНТ, което да гарантира изплащане на вземането ви към нас. Поради тази причина към месечната Ви вноска по кредита е калкулирана допълнителна такса &quot;гаранция&quot;, с чиито размер сте се запознали на сайта на дружеството ни при подаване на заявката за кредит и която може да видите в погасителния план в договора за кредит.</p>\r\n\r\n<p><strong>ПЪРВА СТЪПКА &ndash; ПОДПИСВАНЕ НА ДОГОВОРА ЗА КРЕДИТ</strong></p>\r\n\r\n<p>Запознайте се и подпишете договора си за кредит, като кликнете върху следния линк:<br />\r\n<a href="http://credit.n1s.co/confirmrequest/?token=[a_request_token]"><strong>http://credit.n1s.co/confirmrequest/?token=[a_request_token]</strong></a>.<br />\r\nВеднага след, като получим изпратеното от Вас потвърждение ще изпратим желаната сума по избрания от Вас в попълнената заявка начин.</p>\r\n\r\n<p><strong>ВТОРА СТЪПКА &ndash; ПОЛУЧАВАНЕ НА ЖЕЛАНАТА ОТ ВАС СУМА</strong></p>\r\n\r\n<p>Можете да получите Вашия кредит в брой, срещу лична карта, на каса в офис на EasyPay, в един от офисите на фирмата. За адреса им може да се информирате от тук: <strong><a href="https://easypay.bg/?p=offices">https://easypay.bg/?p=offices</a></strong>.</p>\r\n\r\n<p><strong>ВЪЗМОЖНОСТ ЗА РЕФИНАНСИРАНЕ НА КРЕДИТА ВИ</strong></p>\r\n\r\n<p>Можете да продължите периода на кредита си, ако преди изтичане на падежа му ни уведомите на посочения мейл: <EMAIL> или на телефон 0700 20 121. В този случай ще трябва да заплатите само натрупаната до момента лихва и дължимата такса &quot;гарант&quot;, като кредитът Ви ще бъде продължен за нов срок дълъг, колкото първоначалния такъв.</p>\r\n\r\n<p><strong>КЪДЕ И КАК ДА ВЪРНЕТЕ КРЕДИТА СИ?</strong></p>\r\n\r\n<p>Кредита може да бъде върнат <strong><a href="https://easypay.bg/?p=offices">във всеки един клон на Изипей АД</a></strong> чрез системата за разплащания на <strong><a href="https://www.epay.bg">ePay.bg</a></strong> в полза на &ldquo;КРЕДИТ ИНС&rdquo; ООД по Клиентски номер: <strong>8634280546</strong></p>\r\n\r\n<p>Може да върнете сумата и по банков път по следната банкова сметка на &bdquo;КРЕДИТ ИНС&rdquo; ООД открита в УниКредит Булбанк АД:<br />\r\nIBAN: **********************<br />\r\nBIC: UNCRBGSF<br />\r\nКато посочите Вашето ЕГН, като основание.</p>\r\n\r\n<p>Разгледайте предлаганите от нас продукти на следния адрес: <a href="https://www.creditins.bg/">www.creditins.bg</a></p>\r\n\r\n<p>С уважение,<br />\r\nЕкипът на &quot;КРЕДИТ ИНС&quot; ООД<br />\r\nЗа контакти: 0700 20 121<br />\r\nE-mail:<EMAIL></p>\r\n', '', 'bg', NOW()),
(1034, 'Вашият договор в Кредит Инс', '<p>Прикачен ще намерите вашият договор [document_num] в Кредит Инс.</p>\r\n', '', 'bg', NOW());
UPDATE emails_i18n SET
subject='Изпратената от Вас заявка за получаване на кредит е одобрена!',
body='<p>Здравейте, <strong>[customer_name] [customer_lastname]</strong></p>\r\n\r\n<p>Благодарим Ви, че ни избрахте!</p>\r\n\r\n<p>Честито!</p>\r\n\r\n<p>Изпратената от Вас заявка за получаване на кредит е одобрена!</p>\r\n\r\n<p>Изпращаме Ви информация за начина на получаване на исканата от Вас сума, като моля да следвате посочените няколко лесни стъпки:</p>\r\n\r\n<p>При изпращане на заявката Ви за кредит сте посочили, че желаете &quot;КРЕДИТ ИНС&quot; ООД, да ангажира за Вас ДРУЖЕСТВО ГАРАНТ, което да гарантира изплащане на вземането ви към нас. Поради тази причина към месечната Ви вноска по кредита е калкулирана допълнителна такса &quot;гаранция&quot;, с чиито размер сте се запознали на сайта на дружеството ни при подаване на заявката за кредит и която може да видите в погасителния план в договора за кредит.</p>\r\n\r\n<p><strong>ПЪРВА СТЪПКА &ndash; ПОДПИСВАНЕ НА ДОГОВОРА ЗА КРЕДИТ</strong></p>\r\n\r\n<p>Запознайте се и подпишете договора си за кредит, като кликнете върху следния линк:<br />\r\n<a href="http://credit.n1s.co/confirmrequest/?token=[a_request_token]"><strong>http://credit.n1s.co/confirmrequest/?token=[a_request_token]</strong></a>.<br />\r\nВеднага след, като получим изпратеното от Вас потвърждение ще изпратим желаната сума по избрания от Вас в попълнената заявка начин.</p>\r\n\r\n<p><strong>ВТОРА СТЪПКА &ndash; ПОЛУЧАВАНЕ НА ЖЕЛАНАТА ОТ ВАС СУМА</strong></p>\r\n\r\n<p>Можете да получите Вашия кредит в брой, срещу лична карта, на каса в офис на EasyPay, в един от офисите на фирмата. За адреса им може да се информирате от тук: <strong><a href="https://easypay.bg/?p=offices">https://easypay.bg/?p=offices</a></strong>.</p>\r\n\r\n<p><strong>ВЪЗМОЖНОСТ ЗА РЕФИНАНСИРАНЕ НА КРЕДИТА ВИ</strong></p>\r\n\r\n<p>Можете да продължите периода на кредита си, ако преди изтичане на падежа му ни уведомите на посочения мейл: <EMAIL> или на телефон 0700 20 121. В този случай ще трябва да заплатите само натрупаната до момента лихва и дължимата такса &quot;гарант&quot;, като кредитът Ви ще бъде продължен за нов срок дълъг, колкото първоначалния такъв.</p>\r\n\r\n<p><strong>КЪДЕ И КАК ДА ВЪРНЕТЕ КРЕДИТА СИ?</strong></p>\r\n\r\n<p>Кредита може да бъде върнат <strong><a href="https://easypay.bg/?p=offices">във всеки един клон на Изипей АД</a></strong> чрез системата за разплащания на <strong><a href="https://www.epay.bg">ePay.bg</a></strong> в полза на &ldquo;КРЕДИТ ИНС&rdquo; ООД по Клиентски номер: <strong>8634280546</strong></p>\r\n\r\n<p>Може да върнете сумата и по банков път по следната банкова сметка на &bdquo;КРЕДИТ ИНС&rdquo; ООД открита в УниКредит Булбанк АД:<br />\r\nIBAN: **********************<br />\r\nBIC: UNCRBGSF<br />\r\nКато посочите Вашето ЕГН, като основание.</p>\r\n\r\n<p>Разгледайте предлаганите от нас продукти на следния адрес: <a href="https://www.creditins.bg/">www.creditins.bg</a></p>\r\n\r\n<p>С уважение,<br />\r\nЕкипът на &quot;КРЕДИТ ИНС&quot; ООД<br />\r\nЗа контакти: 0700 20 121<br />\r\nE-mail:<EMAIL></p>\r\n'
WHERE parent_id=1033 AND lang='bg';
UPDATE emails_i18n SET
subject='Вашият договор в Кредит Инс',
body='<p>Прикачен ще намерите вашият договор [document_num] в Кредит Инс.</p>\r\n'
WHERE parent_id=1034 AND lang='bg';

########################################################################
# 2019-02-27 - Added REST fields for related persons data (bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone) for customers

# Added REST fields for related persons data (bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone) for customers
UPDATE `settings` SET `value`='id, type, name, lastname, web_user, web_pass, c_person_table, ekatte_id, ekatte_name, address, gsm, phone, email, ucn, identity_num, identity_date, identity_valid, address_by_personal_id, workplace, position, contracttype, netsalary, web_access_token, web_expiration_timestamp, web_pass_recovery_expiration_timestamp, added, modified, deleted' WHERE  name='filter_vars_customers_3';

########################################################################
# 2019-03-06 - Updated condition and settings of the existing 'createContractDeadlineTable' automation
#            - Added two additional automations to validate contract date change and to update the contract deadline table

# Updated condition and settings of the existing 'createContractDeadlineTable' automation
UPDATE `automations` SET `conditions`='condition := (\'[prev_b_date]\' == \'\' || \'[prev_b_date]\' == \'0000-00-00\') && (\'[b_date]\' != \'\' && \'[b_date]\' != \'0000-00-00\')', `settings`=CONCAT(`settings`, '\r\n\r\nskip_full_procedure := 0') WHERE `method` LIKE '%createContractDeadlineTable%' AND `settings` NOT LIKE '%skip_full_procedure%';

# Added two additional automations to validate contract date change and to update the contract deadline table
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на промяна на началната дата на договор', 0, NULL, 1, 'documents', NULL, 'before_action', '6', 'contract_active_status := 22\r\nextended_installment := extended_installment', 'condition := \'[action]\' == \'edit\'\r\ncondition := \'[request_is_post]\'', 'plugin := creditins\r\nmethod := validateContractDateChange', 'cancel_action_on_fail := 1', 1, 0, 1),
('Прегенериране на падежна таблица при промяна на дата на договор', 0, NULL, 1, 'documents', NULL, 'action', '6', 'credit_amount := grant_credit\r\ncredit_interest := contract_rate\r\ncredit_warranty := contract_warranty\r\ncredit_periods := repayment_period\r\ncredit_product := credit_product_id\r\ncredit_product_name := credit_product\r\ncredit_status_active := opened_22\r\ncredit_tag_export := 5\r\n\r\nfir_type_incomes_reason_loan_contract_type := 104\r\nfir_type_incomes_reason_loan_contract_company := 1\r\nfir_type_incomes_reason_loan_contract_office := 1\r\nfir_type_incomes_reason_loan_contract_payment_type := bank\r\nfir_type_incomes_reason_loan_contract_payment_container := 1\r\nfir_type_incomes_reason_loan_contract_article_id := 57\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\nnot_change_ckr_code := 2\r\n\r\nskip_full_procedure := 1', 'condition := \'[prev_b_date]\' != \'\' && \'[prev_b_date]\' != \'0000-00-00\' && \'[b_date]\' != \'\' && \'[b_date]\' != \'0000-00-00\' && \'[prev_b_date]\' != \'[b_date]\'', 'plugin := creditins\r\nmethod := createContractDeadlineTable', NULL, 1, 0, 1);

########################################################################
# 2019-03-12 - Fix the conditions of the 'createContractDeadlineTable' automation which creates the table in first place

# Fix the conditions of the 'createContractDeadlineTable' automation which creates the table in first place
UPDATE `automations` SET `conditions`='condition := (\'[b_date]\' != \'\' && \'[b_date]\' != \'0000-00-00\')' WHERE `method` LIKE '%createContractDeadlineTable%' AND `settings` LIKE '%skip_full_procedure := 0%';

########################################################################
# 2019-06-13 - The automations, which change the tags of a customer when a loan request is approved ot declined, are added again

# The automations, which change the tags of a customer when a loan request is approved ot declined, are added again
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Промяна на статуса на заявка за кредит (отхвърляне)', 0, NULL, 1, 'documents', NULL, 'action', '19', 'status_request_approved := 26\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\n\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\n\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\n\r\ndocument_loan_contract := 6\r\nnot_change_ckr_code := 2', 'condition := \'[b_substatus]\' == \'27\' && \'[prev_b_substatus]\' != \'27\'', 'plugin := creditins\r\nmethod := processCreditRequestStatusChange', NULL, 1, 1, 1),
('Промяна на статуса на заявка за кредит (одобряване)', 0, NULL, 1, 'documents', NULL, 'action', '19', 'status_request_approved := 26\r\n\r\ntag_customer_declined := 2\r\ntag_customer_export := 3\r\ntag_customer_reexport := 4\r\n\r\nnew_code_ckr := 1\r\nborrower_type := 1\r\n\r\ncustomer_code_record := code_record\r\ncustomer_borrower_type := borrower_type\r\n\r\ndocument_loan_contract := 6\r\nnot_change_ckr_code := 2', 'condition := \'[b_substatus]\' == \'26\' && \'[prev_b_substatus]\' != \'26\'', 'plugin := creditins\r\nmethod := processCreditRequestStatusChange', NULL, 1, 1, 1);

########################################################################
# 2019-06-28 - Added a new 'copyCreditRequestDataToClient' automation which updates client data from the document when theres a difference between the them.

# Added a new 'copyCreditRequestDataToClient' automation which updates client data from the document when theres a difference between the them.
INSERT INTO `automations` (`name`, `module`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`,`position`, `nums`) VALUES
('Копиране на данни от "Заявка за кредит" към "Клиент"', 'documents', 'action', '19', 'customer_type_client := 3', 'condition := \'[action]\' == \'setstatus\' && \'[request_is_post]\' == \'1\' && \'[b_substatus]\' == \'26\'', 'plugin := creditins\r\nmethod := copyCreditRequestDataToClient', 4, 0);

########################################################################
# 2020-02-19 - Added new settings for 'creditins_overdue_payments' for the first and the last overdue payment date

# Added new settings for 'creditins_overdue_payments' for the first and the last overdue payment date
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nlawsuit_nom_id :=', '\r\nproject_first_overdue_date := first_overdue_contributions\r\nproject_last_overdue_date := last_overdue_contributions\r\n\r\nlawsuit_nom_id :=') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%project_first_overdue_date%';

######################################################################################
# 2020-04-08 - Add new report: 'creditins_owed_dues'

# Add new report: 'creditins_owed_dues'
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (412, 'creditins_owed_dues', '#Настройки на типа документи „Договор за заем“\r\ndocument_type_loan_agreement := 6\r\ndocument_loan_contract_status_paid := 20\r\ndocument_loan_contract_status_active := 22\r\ndocument_loan_contract_status_proceeding := 23\r\ndocument_loan_contract_status_proceeding_paid := 24\r\n\r\ndocument_loan_contract_paid_date := date_payment_doc\r\ndocument_loan_contract_paid_principal := principal\r\ndocument_loan_contract_paid_warranty := amount_warranty\r\ndocument_loan_contract_paid_interest := contractual_interest\r\ndocument_loan_contract_repayment_period := repayment_period\r\ndocument_loan_contract_extension_date := date_time_extensions\r\ndocument_loan_contract_extension_interest := amount_extend_int\r\ndocument_loan_contract_extension_warranty := amount_extend_fee\r\n\r\n# Настройки на проект „Съдебно производство“\r\nproceeding_type := 1\r\nproceeding_loan_contract_id := contract_num_id\r\nproceeding_date := date_proceedings\r\nproceeding_income_type := profit_type_id\r\nproceeding_income_date := income_date\r\nproceeding_income_amount := amount_income\r\nproceeding_income_type_principal := 104\r\nproceeding_income_type_interest := 105\r\nproceeding_income_type_warranty := 106\r\nproceeding_expense_types_included := 96,28,99,97,50,100,98,51,101,102\r\nproceeding_expense_type := cost_type_id\r\nproceeding_expense_date := expense_date\r\nproceeding_expense_amount := amount_expense\r\n', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `lang`) VALUES
  ('412', 'Дължими суми към период', 'bg'),
  ('412', 'Owed dues for period', 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `position`) VALUES
  ('reports', 'generate_report', '412', '1'),
  ('reports', 'export', '412', '1');
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT '1', `id`, 'all'
  FROM `roles_definitions`
    WHERE `module`     = 'reports'
      AND `action`     IN ('generate_report', 'export')
      AND `model_type` = '412';

########################################################################
# 2020-04-09 - Added new settings for 'creditins_owed_dues' for the date of proceeding fully paid

# Added new settings for 'creditins_owed_dues' for the date of proceeding fully paid
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndocument_loan_contract_extension_date :=', '\r\ndocument_loan_contract_court_completed := date_court_completion\r\ndocument_loan_contract_extension_date :=') WHERE `type`='creditins_owed_dues' AND `settings` NOT LIKE '%document_loan_contract_court_completed%';

########################################################################
# 2020-04-16 - Update settings of the 'creditins_owed_dues' to use the correct date
#            - Update the payments table of the loan contracts to show the distribution dat
#            - Update the payments table of the loan contracts to contain the payment date
#            - Updated settings for distribute date and payment date in distributePaymentByRepaymentSchedule automation
#            - Updated settings for distrubite and payment date in 'creditins_customer_file' report

# Update settings of the 'creditins_owed_dues' to use the correct date
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'document_loan_contract_paid_date := date_payment_doc', 'document_loan_contract_paid_date := true_payment_date') WHERE `type` LIKE '%creditins_owed_dues%' AND `settings` LIKE '%document_loan_contract_paid_date := date_payment_doc%';

# Update the payments table of the loan contracts to show the distribution date
UPDATE documents AS d, documents_cstm AS dc1, fin_payments AS fp, documents_cstm AS dc2
SET dc2.value=DATE_FORMAT(fp.status_modified, '%Y-%m-%d %H:%i:%s')
WHERE d.type = 6 AND !d.deleted AND d.active AND dc1.model_id = d.id AND dc1.var_id = 1702 AND (dc1.lang = '' OR dc1.lang = 'bg') AND dc1.value != '' AND dc1.value = fp.id AND dc2.model_id = dc1.model_id AND dc2.var_id = 1709 AND dc2.num = dc1.num AND (dc2.lang = '' OR dc2.lang = 'bg');

# Update the payments table of the loan contracts to contain the payment date
INSERT INTO `documents_cstm`
    SELECT d.id, 1742, d_cstm.num, fp.issue_date, NOW(), 1, NOW(), 1, ''
    FROM documents AS d
    JOIN documents_cstm AS d_cstm
      ON (d.type = 6 AND !d.deleted AND d.active AND d_cstm.model_id = d.id AND d_cstm.var_id = 1702 AND (d_cstm.lang = '' OR d_cstm.lang = 'bg') AND d_cstm.value != '')
    JOIN fin_payments AS fp
      ON (d_cstm.value = fp.id)
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`), `added` = NOW(), `added_by`=1, `modified`=NOW(), `modified_by`=1;

# Updated settings for distribute date and payment date in distributePaymentByRepaymentSchedule automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'schedule_var_payment_date := date_payment_doc', 'schedule_var_payment_date := true_payment_date\r\nschedule_var_distribute_date := date_payment_doc') WHERE `method` LIKE '%distributePaymentByRepaymentSchedule%' AND `settings` LIKE '%schedule_var_payment_date := date_payment_doc%';

# Updated settings for distrubite and payment date in 'creditins_customer_file' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'schedule_var_payment_date := date_payment_doc', 'schedule_var_payment_date := true_payment_date\r\nschedule_var_distribute_date := date_payment_doc') WHERE `type` LIKE '%creditins_customer_file%' AND `settings` LIKE '%schedule_var_payment_date := date_payment_doc%';

######################################################################################
# 2020-06-08 - Added lk_face, lk_back to the list of variables
#            - Added lk_face, lk_back to the customer type 3

# Added lk_face, lk_back to the list of variables
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'filter_vars_customers_3', 'id, type, name, lastname, web_user, web_pass, c_person_table, ekatte_id, ekatte_name, address, gsm, phone, email, ucn, identity_num, identity_date, identity_valid, address_by_personal_id, lk_face, lk_back, workplace, position, contracttype, netsalary, web_access_token, web_expiration_timestamp, web_pass_recovery_expiration_timestamp, added, modified, deleted')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

# Added lk_face, lk_back to the customer type 3
INSERT IGNORE INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(331, 'Customer', 3, 'lk_face', 'file_upload', NULL, 0, 1, 'allowed_extensions := jpg,jpeg,jpe,gif,tif,png\r\nupload_max_filesize := 5М\r\nmax_width := 3000\r\nmax_height := 3000\r\nview_mode := thumbnail\r\nthumb_width := 100\r\nthumb_height := 100', '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4829, 2, '', '', ''),
(332, 'Customer', 3, 'lk_back', 'file_upload', NULL, 0, 1, 'allowed_extensions := jpg,jpeg,jpe,gif,tif,png\r\nupload_max_filesize := 5М\r\nmax_width := 3000\r\nmax_height := 3000\r\nview_mode := thumbnail\r\nthumb_width := 100\r\nthumb_height := 100', '', 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 4829, 3, '', '', '');
INSERT IGNORE INTO `_fields_i18n` VALUES
(331, 'label', 'Лична карта (лице)', 'bg'),
(332, 'label', 'Лична карта (гръб)', 'bg');
INSERT IGNORE INTO `layouts` (`layout_id`, `model`, `model_type`, `keyname`, `system`, `visible`, `place`, `info_header_visibility`, `active`, `added`, `added_by`, `modified`, `modified_by`, `deleted`, `deleted_by`) VALUES
(4829, 'Customer', '3', 'keyname_4829', 0, 0, 46, 0, 1, NOW(), 1, NOW(), 1, '0000-00-00 00:00:00', 0);
INSERT IGNORE INTO `layouts_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
(4829, 'Лична карта изображения', '', 'bg', NOW());
INSERT IGNORE INTO `layouts_permissions` (`parent_id`, `action_type`, `group_id`) VALUES
(4829, 'view', 1),
(4829, 'edit', 1);

######################################################################################
# 2021-01-27 - Added automation that fixes calculatePenaltyInterest

# Added automation that fixes calculatePenaltyInterest
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
SELECT NULL, 'FIX: Изчисляване на наказателна лихва', 0, NULL, 1, 'documents', NULL, 'crontab', '6', '#start_time := 01:00\r\n#start_before := 03:00\r\n\r\nnom_interest_type := 12\r\nnom_interest_percent_bnb := interest_percent_bnb\r\nnom_interest_percent_other := interest_percent_other\r\n\r\nincomes_reason_type_id := 104\r\ncontract_owed_sum_id := 57\r\n\r\ndocument_ckr_interest_total := penalty_int_ckr\r\n\r\nignore_tags := 8\r\nignore_statuses := closed_20, closed_23, closed_21, closed_24, closed_25', 'condition := 1', 'plugin := creditins\r\nmethod := decreasePenaltyInterest', NULL, 1, 0, 0
WHERE NOT EXISTS (SELECT id FROM automations WHERE  method LIKE '%decreasePenaltyInterest%');

######################################################################################
# 2021-02-23 - Added personal_data_agreement, declaration_info, political_figure_declaration, general_terms_declaration, guarantee_agreement_declaration, personal_data_guarantee_agreement to the list of variables

# Added personal_data_agreement, declaration_info, political_figure_declaration, general_terms_declaration, guarantee_agreement_declaration, personal_data_guarantee_agreement to the list of variables
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'filter_vars_documents_19', 'id, type, name, customer, use_email, substatus, bond_type, c_person_address, c_person_email, c_person_name, c_person_name_f, c_person_phone, cred_product_id, requested_sum, number_timeunits, tax_garant, notes, promocode_id, vnoskatotal, vnoskaglavnica, vnoskalihva, vnoskagarant, sum_for_return, garant, lihva, personal_data_agreement, declaration_info, political_figure_declaration, general_terms_declaration, guarantee_agreement_declaration, personal_data_guarantee_agreement, added, modified, deleted')
ON DUPLICATE KEY UPDATE
    `value` = VALUES(`value`);

######################################################################################
# 2021-10-08 - Added lk_selfie field

# Added lk_selfie field
UPDATE `settings`
SET `value`='id, type, name, lastname, web_user, web_pass, c_person_table, ekatte_id, ekatte_name, address, gsm, phone, email, ucn, identity_num, identity_date, identity_valid, address_by_personal_id, lk_face, lk_back, lk_selfie, workplace, position, contracttype, netsalary, web_access_token, web_expiration_timestamp, web_pass_recovery_expiration_timestamp, added, modified, deleted'
WHERE  name='filter_vars_customers_3';

#########################################################################################
# 2023-02-14 - Changed the character set and collation

# Changed the character set and collation
ALTER TABLE `sms` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `sms_delivery_log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `plugin_pay_confirms` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `plugin_pay_full_log` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `plugin_pay_invoices` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
ALTER TABLE `plugin_pay_requests` CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

#########################################################################################
# 2024-01-10 - Update settings of the 'creditins_overdue_payments' with documents 'mail for overdue payments' vars
#            - Drop the specific table for 'creditins_overdue_payments' report. It will be recreated on first generation of the report

# Update settings of the 'creditins_overdue_payments' with documents 'mail for overdue payments' vars
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndocument_call_type_id :=', '\r\n\r\ndocument_mail_overdue_type_id := 16\r\ndocument_mail_overdue_contract := contract_num_id\r\n\r\ndocument_call_type_id :=') WHERE `type`='creditins_overdue_payments' AND `settings` NOT LIKE '%document_mail_overdue_type_id%';

# Drop the specific table for 'creditins_overdue_payments' report. It will be recreated on first generation of the report
DROP TABLE IF EXISTS `reports_creditins_overdue_payments`;
