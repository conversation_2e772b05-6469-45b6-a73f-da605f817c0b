###############################################################################
### SQL nZoom Specific Updates АОН България (http://aon.n-zoom.com/)  ###
###############################################################################

######################################################################################
# 2011-07-04 - Added report who_is_resting

INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(86, 'who_is_resting', 'document_type_id := 1\nfree_days_start_date := leave_start_date\nfree_days_finish_date := leave_finish_date\ndeputy := name_deputy', 0, 0, 0);

INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(86, 'Кой е в отпуск?', NULL, NULL, 'bg'),
(86, 'Who is resting?', NULL, NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id=86;
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'export', id, 0, 2 FROM `reports` WHERE id=86;
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=86;

######################################################################################
# 2011-11-14 - Added automation (calculateDateOfPayment) for all contracts to complete the table with date of payments

# Added automation (calculateDateOfPayment) for all contracts to complete the table with date of payments
INSERT IGNORE INTO automations (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
  SELECT NULL, 0, NULL, 1, 'contracts', NULL, 'action', con_type.id, 'premium_value := premium__value\r\npremium_currency := premium__currency\r\ncommission_percent := commission\r\nnumber_rows := number_installments', 'condition := 1', 'plugin := aon\r\nmethod := calculateDateOfPayment', NULL, 0, 1
  FROM contracts_types con_type WHERE con_type.deleted_by=0;

######################################################################################
# 2011-11-15 - Added automation (setGT2ReadonlyRows) for all contracts types to make the "paid" rows of GT2 (of annex) readonly

# Added automation (setGT2ReadonlyRows) for all contracts types to make the "paid" rows of GT2 (of annex) readonly
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
  SELECT NULL, '0', NULL, '1', 'contracts', '', 'before_action', `id`, '', 'condition := 1', 'plugin := aon\r\nmethod := setGT2ReadonlyRows', '', '0', '0'
    FROM `contracts_types`;

######################################################################################
# 2011-11-30 - Added automation (setGT2RowsKeys) for all contracts types to add unique keys for each GT2 row of a contract/annex

# Added automation (setGT2RowsKeys) for all contracts types to add unique keys for each GT2 row of a contract/annex
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
  SELECT NULL, '0', NULL, '1', 'contracts', '', 'action', `id`, '', 'condition := 1', 'plugin := aon\r\nmethod := setGT2RowsKeys', '', '0', '0'
    FROM `contracts_types`;

######################################################################################
# 2011-12-01 - Added automation (validateGT2RowsCount) for all contracts types to validate that the GT2 rows count does not exceeds the number of the installments

# Added automation (validateGT2RowsCount) for all contracts types to validate that the GT2 rows count does not exceeds the number of the installments
INSERT IGNORE INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
  SELECT NULL, '0', NULL, '1', 'contracts', '', 'before_action', `id`, '', 'condition := 1', 'plugin := aon\r\nmethod := validateGT2RowsCount', 'cancel_action_on_fail := 1', '0', '0'
    FROM `contracts_types`;

######################################################################################
# 2011-12-22 - Set positions for automations setGT2RowsKeys and calculateDateOfPayment automations

# Set positions for automations setGT2RowsKeys and calculateDateOfPayment automations
UPDATE `automations` SET `position`=1 WHERE `method` LIKE '%calculateDateOfPayment%';
UPDATE `automations` SET `position`=2 WHERE `method` LIKE '%setGT2RowsKeys%';

######################################################################################
# 2012-01-04 - Added new automation which will add new nomenclatures when a BSO document is set with substatus active

# Added new automation which will add new nomenclatures when a BSO document is set with substatus active
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 0, NULL, 1, 'documents', NULL, 'action', 2, 'document_type_blank := type_blank\r\ndocument_starting_number := number_of\r\ndocument_ending_number := to_number\r\nnomenclature_type := 33\r\nnomenclature_type_blank := type_blank\r\nnomenclature_insurer := insurer_id\r\nnomenclature_insurer_name := insurer_name\r\nnomenclature_protocol := protocol_id\r\nnomenclature_protocol_num := protocol_num', 'condition := ''[prev_b_substatus]'' != ''13''\r\ncondition := ''[b_substatus]'' == ''13''', 'plugin := aon\r\nmethod := addBSONomenclatures', NULL, 1, 1);

######################################################################################
# 2012-01-11 - Added new report 'aon_checking_giving_bso_numbers' for the AON installation (1745)

# Added new report 'aon_checking_giving_bso_numbers' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(192, 'aon_checking_giving_bso_numbers', 'customer_insurer_type := 4\r\nnomenclature_bso_type_id := 33\r\nnomenclature_type_blank := type_blank\r\nnomenclature_status := fsa_status\r\nnomenclature_status_free := 1\r\nnomenclature_status_used := 2\r\nnomenclature_status_annuled := 3\r\nnomenclature_insurer := insurer_id\r\nnomenclature_policy_id := policy_id\r\nnomenclature_policy_num := policy_num\r\nnomenclature_protocol_id := protocol_id\r\nnomenclature_car_id := car_id\r\nnomenclature_car_name := car_name\r\nnomenclature_status_used := 2\r\nnomenclature_car_type_id := 5\r\nnomenclature_car_rama_num := car_rama_num\r\ndocument_acceptance_protocol_id := 2\r\nskip_session_filters := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(192, 'Проверка и даване на БСО номера', NULL, NULL, 'bg'),
(192, 'Checking and giving bso numbers', NULL, NULL, 'en');

INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`)
  SELECT 'reports', 'generate_report', id, 0, 1 FROM `reports` WHERE id=192;
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND model_type=192;

######################################################################################
# 2012-01-13 - Added additional option for free status in the settings of addBSONomenclatures automation

# Added additional option for free status in the settings of addBSONomenclatures automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_status := fsa_status\r\nnomenclature_status_free := 1') WHERE `method` LIKE('%addBSONomenclatures%');

######################################################################################
# 2012-01-26 - Added additional option to set the office for the added nomenclature in addBSONomenclatures automation
#            - Added additional setting to the button settings for taking office from the contract before redirecting to the 'aon_checking_giving_bso_numbers' report
#            - Added additional setting to the 'aon_checking_giving_bso_numbers' report, used for taking information about the office name for the BSO

# Added additional option for free status in the settings of addBSONomenclatures automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_office := brought_to') WHERE `method` LIKE('%addBSONomenclatures%');

# Added additional setting to the button settings for taking office from the contract before redirecting to the 'aon_checking_giving_bso_numbers' report
UPDATE `_fields_meta` SET `source` = CONCAT(`source`, '\r\noffice := b_office') WHERE `type`='button' AND `source` LIKE ('%show_type := give_bso%');

# Added additional setting to the 'aon_checking_giving_bso_numbers' report, used for taking information about the office name for the BSO
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\nnomenclature_office := brought_to') WHERE `type`='aon_checking_giving_bso_numbers';

######################################################################################
# 2012-02-09 - Added new report 'aon_create_invoices' for the AON installation (1745)

# Added new report 'aon_create_invoices' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(193, 'aon_create_invoices', 'document_invoice_id := 8\r\ndocument_invoice_type := invoice_type\r\ndocument_invoice_value := invoice_value\r\ndocument_invoice_differences := invoice_differences\r\ndocument_invoice_differences_comment := invoice_differences_notes\r\ncustomer_insurer_type := 4\r\ncustomer_client_type := 3\r\ncontracts_kasko_policy_1 := 4\r\ncontracts_kasko_policy_2 := 6\r\ncontracts_insurer := insurer_id\r\n\r\nskip_session_filters := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(193, 'Създаване на фактури', NULL, NULL, 'bg'),
(193, 'Create invoices', NULL, NULL, 'en');

######################################################################################
# 2012-02-15 - Updated settings for calculateDateOfPayment automation
#            - Added permissions for generate and export the report 'aon_create_invoices' for the AON installation (1745)

# Updated settings for calculateDateOfPayment automation
UPDATE `automations` SET `settings`=CONCAT(`settings`,'\r\ninsurer_id := insurer_id\r\ninsurer_name := insurer_name\r\nalternative_insurer :=\r\nhas_shared_commission := 0\r\ncommission_type_standart := 1\r\ncommission_type_shared := 2') WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type`=1 AND `settings` NOT LIKE '%alternative_insurer%';
UPDATE `automations` SET `settings`=CONCAT(`settings`,'\r\ninsurer_id := insurer_id\r\ninsurer_name := insurer_name\r\nalternative_insurer :=\r\nhas_shared_commission := 1\r\ncommission_type_standart := 1\r\ncommission_type_shared := 2\r\nshared_commission := shared_commission_id\r\nshared_commission_name := shared_commission_name\r\nshared_commission_percent := shared_commission_percent\r\nshared_commission_value := shared_commission_value\r\nshared_commission_discount := shared_commission_valuelocal') WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type` IN (2,4,5,6) AND `settings` NOT LIKE '%alternative_insurer%';
UPDATE `automations` SET `settings`=CONCAT(`settings`,'\r\ninsurer_id := insurer_id\r\ninsurer_name := insurer_name\r\nalternative_insurer := commission_with\r\nuse_alternative_insurer := 1\r\nalternative_insurer_id := id_insurer\r\nalternative_insurer_name := name_insurer\r\nhas_shared_commission := 1\r\ncommission_type_standart := 1\r\ncommission_type_shared := 2\r\nshared_commission := shared_commission_id\r\nshared_commission_name := shared_commission_name\r\nshared_commission_percent := shared_commission_percent\r\nshared_commission_value := shared_commission_value\r\nshared_commission_discount := shared_commission_valuelocal') WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type`=3 AND `settings` NOT LIKE '%alternative_insurer%';

# Added permissions for generate and export the report 'aon_create_invoices' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 193, 0, 1),
('reports', 'export', 193, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=193;

######################################################################################
# 2012-02-20 - Added new report 'aon_upcoming_deadlines' for the AON installation (1745)

# Added new report 'aon_upcoming_deadlines' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(195, 'aon_upcoming_deadlines', 'included_contract_types := 1,2,3,4,5,6\r\ntype_insurance_types := 9,11,12,13,14,20,18,10,6,17,21,22,4,23,31,16,24,25,15,26,27,28,29,30,32,19\r\ninsurance_group := insurance_group\r\ninsurance_type_base := insurance_type_con\r\ncontract_individual_insurance_type := 4\r\nindividual_insurance_car_id := car_id\r\nnumber_payments := number_installments\r\nnomenclature_car_type_id := 5\r\nnomenclature_car_rama_num := car_rama_num\r\nsearched_commission_type := 1', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(195, 'Предстоящи падежи', NULL, NULL, 'bg'),
(195, 'Upcoming deadlines', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_upcoming_deadlines' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 195, 0, 1),
('reports', 'export', 195, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=195;

######################################################################################
# 2012-02-22 - Removed not used variable described in the settings of calculateDateOfPayment automation for some contracts types

# Removed not used variable described in the settings of calculateDateOfPayment automation for some contracts types
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nshared_commission_discount := shared_commission_valuelocal', '') WHERE `method` LIKE '%calculateDateOfPayment%' AND `settings` LIKE '%shared_commission_discount := shared_commission_valuelocal%';

######################################################################################
# 2012-02-29 - Updated reports settings for 'aon_create_invoices' report
#            - Updated button settings that triggers 'aon_create_invoices' report from Documents module
#            - Updated settings for calculateDateOfPayment automation by adding insurer branch var

# Updated reports settings for 'aon_create_invoices' report
UPDATE `reports` SET `settings`='included_contract_types := 1,2,3,4,5,6\r\ncontract_type_fee := 7\r\ndocument_invoice_id := 8\r\ndocument_invoice_type := invoice_type\r\ndocument_invoice_value := invoice_value\r\ndocument_invoice_differences := invoice_differences\r\ndocument_invoice_differences_comment := invoice_differences_notes\r\ncustomer_insurer_type := 4\r\ncustomer_client_type := 3\r\ncontracts_kasko_policy_1 := 4\r\ncontracts_kasko_policy_2 := 6\r\ncontracts_insurance_type_prefix := insurance_type_con\r\ncontracts_tax_value := tax__valuelocal\r\ncontracts_number_payments := number_installments\r\nincluded_contract_types_with_branch := 3,4,6\r\ncontract_type_invoice_fee := 3\r\ncontract_fee_value := fee_value\r\n\r\nskip_session_filters := 1' WHERE `type`='aon_create_invoices';

# Updated button settings that triggers 'aon_create_invoices' report from Documents module
UPDATE `_fields_meta` SET `source`='href := index.php?launch=reports\r\nreport_type := aon_create_invoices\r\ntarget := _self\r\ntype_invoice := a_invoice_type\r\ninvoice_value := a_invoice_value\r\ndocument_id := b_id\r\ninsurer[] := b_customer\r\ncustomer_branch[] := b_branch' WHERE `model`='Document' AND `model_type`=8 AND `type`='button' AND `source` LIKE '%aon_create_invoices%';

# Updated settings for calculateDateOfPayment automation by adding insurer branch var
UPDATE `automations` SET `settings`=CONCAT(`settings`,'\r\ninsurer_branch := insurer_branch') WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type` IN (3,4,6) AND `settings` NOT LIKE '%insurer_branch %';

######################################################################################
# 2012-03-15 - Added new report 'aon_create_fee' for the AON installation (1745)

# Added new report 'aon_create_fee' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(199, 'aon_create_fee', 'customer_client_type := 3\r\ncustomer_office_type := 6\r\n\r\nincluded_contract_types := 1,2,3,4,5,6\r\ncontract_fee_id := 7\r\ncontracts_insurance_type_prefix := insurance_type_con\r\ncontracts_fee_var := fee\r\ncontracts_fee_value := 1\r\ncontract_type_with_office_id := 1\r\ncontract_main_office := producing_office\r\n\r\nskip_session_filters := 1', 0, 0, 0);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(199, 'Създаване на такса', NULL, NULL, 'bg'),
(199, 'Create fee', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_create_fee' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 199, 0, 1),
('reports', 'export', 199, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=199;

######################################################################################
# 2012-04-04 - Added new report 'aon_supervision_log' for the AON installation (1745)

# Added new report 'aon_create_fee' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(201, 'aon_supervision_log', 'included_contract_types := 1,2,3,4,5,6\r\ninsurer_id := insurer_id\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurance_bonus := premium__value\r\ninsurance_currency := number_installments__value\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission\r\ninsurance_commission_bonus := commission__value\r\ninsurance_commission_bonus_bgn := commission__valuelocal', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(201, 'Дневник на надзора', NULL, NULL, 'bg'),
(201, 'Supervision log', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_supervision_log' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 201, 0, 1),
('reports', 'export', 201, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=201;

######################################################################################
# 2012-04-17 - Added new import 'aon_trade_credits' for the AON installation (1745)

# Added new import 'aon_trade_credits' for the AON installation (1745)
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
  (10, 'aon_trade_credits', '', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (10, 'Търговски кредити', NULL, 'bg'),
  (10, 'Trade credits',     NULL, 'en');

######################################################################################
# 2012-06-13 - Added new report 'aon_bonuses_to_invoice' for the AON installation (1745)

# Added new report 'aon_bonuses_to_invoice' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(213, 'aon_bonuses_to_invoice', 'included_contract_types := 1,2,3,4,5,6', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(213, 'Премии подлежащи на фактуриране', NULL, NULL, 'bg'),
(213, 'Invoiceable bonuses', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_bonuses_to_invoice' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 213, 0, 1),
('reports', 'export', 213, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=213;

######################################################################################
# 2012-06-15 - Added new report 'aon_upcoming_renewals' for the AON installation (1745)

# Added new report 'aon_upcoming_renewals' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(214, 'aon_upcoming_renewals', 'included_contract_types := 1,2,3,4,5,6\r\nincluded_categories := 9,10,11,12,13\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurer_id := insurer_id\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission\r\ncar_name := car_name', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(214, 'Предстоящи подновявания', NULL, NULL, 'bg'),
(214, 'Upcoming renewals', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_upcoming_renewals' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 214, 0, 1),
('reports', 'export', 214, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=214;

######################################################################################
# 2012-06-20 - Added new report 'aon_create_report_for_netherlands' for the AON installation (1745)

# Added new report 'aon_create_report_for_netherlands' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(215, 'aon_create_report_for_netherlands', 'document_type_invoice := 8\r\ninvoice_value := invoice_value\r\ninvoice_differences := invoice_differences\r\ninvoice_notes := invoice_differences_notes\r\n\r\nincluded_contract_types := 1,2,3,4,5,6,7\r\nacgn_policy := 1\r\npolicy_type_local := local\r\npolicy_type_program := program\r\n\r\nnumber_rows := number_installments\r\ninsurance_group := insurance_group\r\nclient_type := client_type\r\nbusiness_type := business_type\r\n\r\ndocument_to_add := 9\r\ninvoice_id_bb := bb_variant\r\ninvoice_num_bb := bb_name\r\ninvoice_date_bb := bb_note\r\ninvoice_amount_bb := bb_value\r\ninvoice_differences_bb := bb_quantity', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(215, 'Създай отчет за Холандия', NULL, NULL, 'bg'),
(215, 'Create report for Netherlands', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_create_report_for_netherlands' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 215, 0, 1),
('reports', 'export', 215, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=215;

######################################################################################
# 2012-06-26 - Added setting for 'aon_create_report_for_netherlands' report to point which field to be used to record the note of the invoice in the report document

# Added setting for 'aon_create_report_for_netherlands' report to point which field to be used to record the note of the invoice in the report document
UPDATE `reports`
SET `settings`=CONCAT(`settings`, '\r\ninvoice_notes_bb := invoice_note')
WHERE `type`='aon_create_report_for_netherlands' AND `settings` NOT LIKE '%invoice_notes_bb%';

######################################################################################
# 2012-09-17 - Added new report 'aon_advanced_incomes_by_clients' for the AON installation (1745)

# Added new report 'aon_advanced_incomes_by_clients' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(229, 'aon_advanced_incomes_by_clients', 'included_contract_types := 1,2,3,4,5,6\r\nincluded_categories := 9,10,11,12,13\r\ncustomer_tags := 7\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurer_id := insurer_id\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission\r\ninsurance_shared_commission := shared_commission_valuelocal', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(229, 'Премиен приход по клиенти', NULL, NULL, 'bg'),
(229, 'Advanced incomes from clients', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_advanced_incomes_by_clients' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 229, 0, 1),
('reports', 'export', 229, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=229;

######################################################################################
# 2012-09-26 - Added new report 'aon_supervision_information' for the AON installation (1745)

# Added new report 'aon_supervision_information' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(234, 'aon_supervision_information', 'included_contract_types := 1,2,3,4,5,6\r\ninsurance_group := insurance_group\r\ninsurer_id := insurer_id\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission__valuelocal', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(234, 'Справка за надзора', NULL, NULL, 'bg'),
(234, 'Supervision information', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_supervision_information' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 234, 0, 1),
('reports', 'export', 234, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND (action='generate_report' OR action='export') AND model_type=234;

######################################################################################
# 2013-01-21 - Added automations for setting employee of current user as administracive contact when adding contract of types 1-6 for the AON installation (1745)

# Added automations for setting employee of current user as administracive contact when adding contract of types 1-6 for the AON installation (1745)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
SELECT NULL, NULL, 0, NULL, 1, 'contracts', '', 'action', `id`, '', 'condition := ''[b_id]'' != '''' && ''[prev_b_id]'' == ''''', 'plugin := aon\r\nmethod := setContractContacts', '', 3, 1
FROM `contracts_types` WHERE `id` IN (1,2,3,4,5,6);

######################################################################################
# 2013-03-14 - Added new dashlet plugin to correct the policies and to issue correct document for AON installation (1745)

INSERT INTO `dashlets_plugins` (`type`, `settings`, `is_portal`, `visible`) VALUES
('aon_correct_policy', 'available_contract_types := 1,2,3,4,5,6\r\n\r\nshow_gt2_fields :=  article_alternative_deliverer_name,article_height,free_text1,quantity,price,free_field1,subtotal,discount_percentage,discount_value,subtotal_discount_value\r\ncheck_active_row := free_field2\r\ncheck_deactivate_row_value := fsa_yes\r\n\r\ngrouping_table_name := affix_group\r\ngrouping_table_note := affix_note\r\ngrouping_table_iv := affix_insurance_value\r\ngrouping_table_pv := affix_premium_value\r\ngrouping_table_start := affix_start\r\ngrouping_table_end := affix_end\r\n\r\nconfig_table_iv := insurance__value\r\nconfig_table_pv := premium__value\r\nconfig_table_currency := number_installments__value\r\nconfig_table_iv_curr := insurance__valuelocal\r\nconfig_table_pv_curr := premium__valuelocal\r\nconfig_table_commission := commission\r\nconfig_table_commission_value := commission__value\r\nconfig_table_commission_value_curr := commission__valuelocal', 0, 1);
INSERT INTO `dashlets_plugins_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(LAST_INSERT_ID(), 'Корекция на полици', 'Бързо коригиране на полици', 'bg'),
(LAST_INSERT_ID(), 'Correct policies', 'Fast correction of policies', 'en');

######################################################################################
# 2013-03-28 - Updated settings for aon_correct_policy dashlet plugin for AON installation (1745) to include number payments and fix the name of the finish field in grouping table

# Updated settings for aon_correct_policy dashlet plugin for AON installation (1745) to include number payments and fix the name of the finish field in grouping table
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\nconfig_table_count_payments := number_installments') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%config_table_count_payments%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'affix_end', 'affix_finish') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%affix_finish%';

######################################################################################
# 2013-04-09 - Updated settings for the 'aon_create_invoices' report so it can mark gt2 rows used in invoices with certain option for AON installation (1745)

# Updated settings for the 'aon_create_invoices' report so it can mark gt2 rows used in invoices with certain option for AON installation (1745)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncontract_yes_option := fsa_yes') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contract_yes_option%';

######################################################################################
# 2013-04-23 - Updated relations between policy rows and invoice (document with id 8) rows to contain the invoice date inside policy gt2

# Updated relations between policy rows and invoice (document with id 8) rows to contain the invoice date inside policy gt2
UPDATE `gt2_details_i18n` AS gt2_i18n, `documents` AS d SET gt2_i18n.`free_text3`=d.`date`
WHERE gt2_i18n.`free_text2`=d.custom_num AND d.custom_num!='';

######################################################################################
# 2013-05-10 - Create temporary table to successfully transfer the needed data
#            - Collect information for relations between gt2 rows in contracts and gt2 rows in documents
#            - Create the relation between gt2 rows in contracts and gt2 rows in documents
#            - Empty temp table
#            - Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts except contract type 7
#            - Update relations in gt2 rows of contracts
#            - Complete the other data concerning the documents
#            - Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts with type 7
#            - Complete the other data concerning the documents
#            - Remove the temp table
#            - Added new crontab automation which will start the needed additional agreements and will relate the new rows from contracts to documents rows (if needed)

# Create temporary table to successfully transfer the needed data
DROP TABLE IF EXISTS `_temp_table`;
CREATE TABLE IF NOT EXISTS `_temp_table` (
  `gt2_contract` int(11) DEFAULT NULL,
  `gt2_document` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Collect information for relations between gt2 rows in contracts and gt2 rows in documents
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
  SELECT gt2.id, gt2_2.id as doc_gt2
  FROM contracts as con
  JOIN gt2_details AS gt2
   ON (gt2.model='Contract' AND gt2.model_id=con.id)
  JOIN gt2_details_i18n AS gt2_i18n
   ON (gt2_i18n.parent_id=gt2.id)
  JOIN gt2_details AS gt2_2
   ON (gt2_2.model='Document' AND gt2_2.free_field2=gt2.id)
  WHERE con.annulled_by=0 AND con.subtype='contract' AND con.deleted_by=0 AND gt2_i18n.free_text2!='' AND gt2_i18n.free_text2 IS NOT NULL;

# Create the relation between gt2 rows in contracts and gt2 rows in documents
UPDATE `gt2_details` AS gt2, `_temp_table` as tt SET gt2.article_second_code=tt.gt2_document WHERE gt2.id=tt.gt2_contract;

# Empty temp table
TRUNCATE TABLE `_temp_table`;

# Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts except contract type 7
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
    SELECT gt2_3.id, gt2.id
    FROM `gt2_details` AS gt2
    LEFT JOIN `gt2_details` AS gt2_2
      ON ( gt2_2.id = gt2.free_field2 AND gt2_2.model = 'Contract' )
    LEFT JOIN `contracts` AS con
      ON ( gt2.article_id = con.id )
    LEFT JOIN documents AS doc
      ON ( doc.id = gt2.`model_id` )
    LEFT JOIN `gt2_details` AS gt2_3
      ON ( gt2_3.model = 'Contract' AND gt2_3.model_id = con.id AND gt2_3.free_field5 = gt2.article_barcode )
    WHERE gt2.model = "Document" AND gt2_2.id IS NULL AND gt2.free_field2 != "" AND con.type!=7;

# Update relations in gt2 rows of contracts
UPDATE `gt2_details` AS gt2, `_temp_table` as tt SET gt2.free_field2=tt.gt2_contract WHERE gt2.id=tt.gt2_document;

# Complete the other data concerning the documents
UPDATE `gt2_details` AS gt2
  JOIN `gt2_details_i18n` AS gt2_i18n
    ON ( gt2.id = gt2_i18n.parent_id AND gt2_i18n.lang = "bg" )
  JOIN `_temp_table` AS tt
    ON ( gt2.id = tt.gt2_contract AND gt2.model = 'Contract' )
  JOIN `gt2_details` AS gt2_2
    ON ( gt2_2.id = tt.gt2_document )
  JOIN `documents` AS d
    ON ( gt2_2.model_id = d.id )
  SET gt2.article_second_code=tt.gt2_document, gt2.free_field2='fsa_yes',
      gt2_i18n.free_text2=d.custom_num, gt2_i18n.free_text3=d.date;

# Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts with type 7
TRUNCATE TABLE `_temp_table`;
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
    SELECT gt2_3.id, gt2.id
    FROM `gt2_details` AS gt2
    LEFT JOIN `gt2_details` AS gt2_2
      ON ( gt2_2.id = gt2.free_field2 AND gt2_2.model = 'Contract' )
    LEFT JOIN `contracts` AS con
      ON ( gt2.article_id = con.id )
    LEFT JOIN documents AS doc
      ON ( doc.id = gt2.`model_id` )
    LEFT JOIN `gt2_details` AS gt2_3
      ON ( gt2_3.model = 'Contract' AND gt2_3.model_id = con.id AND gt2_3.free_field5 = gt2.article_barcode )
    WHERE gt2.model = "Document" AND gt2_2.id IS NULL AND gt2.free_field2 != "" AND con.type=7;

# Complete the other data concerning the documents
UPDATE `gt2_details` AS gt2, `_temp_table` as tt SET gt2.free_field2=tt.gt2_contract WHERE gt2.id=tt.gt2_document;
UPDATE `gt2_details` AS gt2
  JOIN `gt2_details_i18n` AS gt2_i18n
    ON ( gt2.id = gt2_i18n.parent_id AND gt2_i18n.lang = "bg" )
  JOIN `_temp_table` AS tt
    ON ( gt2.id = tt.gt2_contract AND gt2.model = 'Contract' )
  JOIN `gt2_details` AS gt2_2
    ON ( gt2_2.id = tt.gt2_document )
  JOIN `documents` AS d
    ON ( gt2_2.model_id = d.id )
  SET gt2.article_second_code=tt.gt2_document, gt2.free_field2='fsa_yes',
      gt2_i18n.free_text2=d.custom_num, gt2_i18n.free_text3=d.date;

# Remove the temp table
DROP TABLE IF EXISTS `_temp_table`;

# Added new crontab automation which will start the needed additional agreements and will relate the new rows from contracts to documents rows (if needed)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
('Влизане в сила на допълнителни споразумения', 0, NULL, 1, 'contracts', NULL, 'crontab', 1, 'start_time := 03:00\r\nstart_before := 04:00\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := aon\r\nmethod := startAdditionalAgreements', NULL, 0, 0);

######################################################################################
# 2013-05-27 - Updated settings for 'aon_advanced_incomes_by_clients' report to exclude already unused variables

# Updated settings for 'aon_advanced_incomes_by_clients' report to exclude already unused variables
UPDATE `reports` SET `settings`='included_contract_types := 1,2,3,4,5,6\r\nincluded_categories := 9,10,11,12,13\r\ncustomer_tags := 7\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission\r\ninsurance_shared_commission := shared_commission_valuelocal' WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` LIKE '%insurer_id%';

######################################################################################
# 2013-06-11 - Fix relations between gt2 contract rows and gt2 document rows
#            - Create temporary table to successfully transfer the needed data
#            - Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts except contract type 7
#            - Update relations in gt2 rows of contracts
#            - Complete the other data concerning the documents
#            - Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts with type 7
#            - Complete the other data concerning the documents
#            - Collect the annexes which have to be marked as failed
#            - Mark as failed the annexes which are not supposed to be executed
#            - Remove the temp table
#            - Added additional setting to the 'aon_advanced_incomes_by_clients' report, used for taking information about client and business type

# Fix relations between gt2 contract rows and gt2 document rows
# Create temporary table to successfully transfer the needed data
DROP TABLE IF EXISTS `_temp_table`;
CREATE TABLE IF NOT EXISTS `_temp_table` (
  `gt2_contract` int(11) DEFAULT NULL,
  `gt2_document` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COLLATE=utf8_unicode_ci;

# Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts except contract type 7
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
    SELECT gt2_3.id, gt2.id
    FROM `gt2_details` AS gt2
    LEFT JOIN `gt2_details` AS gt2_2
      ON ( gt2_2.id = gt2.free_field2 AND gt2_2.model = 'Contract' )
    LEFT JOIN `contracts` AS con
      ON ( gt2.article_id = con.id )
    LEFT JOIN documents AS doc
      ON ( doc.id = gt2.`model_id` )
    LEFT JOIN `gt2_details` AS gt2_3
      ON ( gt2_3.model = 'Contract' AND gt2_3.model_id = con.id AND gt2_3.free_field5 = gt2.article_barcode )
    WHERE gt2.model = "Document" AND gt2_2.id IS NULL AND gt2.free_field2 != "" AND con.type!=7;

# Update relations in gt2 rows of contracts
UPDATE `gt2_details` AS gt2, `_temp_table` as tt SET gt2.free_field2=tt.gt2_contract WHERE gt2.id=tt.gt2_document;

# Complete the other data concerning the documents
UPDATE `gt2_details` AS gt2
  JOIN `gt2_details_i18n` AS gt2_i18n
    ON ( gt2.id = gt2_i18n.parent_id AND gt2_i18n.lang = "bg" )
  JOIN `_temp_table` AS tt
    ON ( gt2.id = tt.gt2_contract AND gt2.model = 'Contract' )
  JOIN `gt2_details` AS gt2_2
    ON ( gt2_2.id = tt.gt2_document )
  JOIN `documents` AS d
    ON ( gt2_2.model_id = d.id )
  SET gt2.article_second_code=tt.gt2_document, gt2.free_field2='fsa_yes',
      gt2_i18n.free_text2=d.custom_num, gt2_i18n.free_text3=d.date;

# Collect information for relations between gt2 rows in contracts and gt2 rows in documents for all contracts with type 7
TRUNCATE TABLE `_temp_table`;
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
    SELECT gt2_3.id, gt2.id
    FROM `gt2_details` AS gt2
    LEFT JOIN `gt2_details` AS gt2_2
      ON ( gt2_2.id = gt2.free_field2 AND gt2_2.model = 'Contract' )
    LEFT JOIN `contracts` AS con
      ON ( gt2.article_id = con.id )
    LEFT JOIN documents AS doc
      ON ( doc.id = gt2.`model_id` )
    LEFT JOIN `gt2_details` AS gt2_3
      ON ( gt2_3.model = 'Contract' AND gt2_3.model_id = con.id AND gt2_3.free_field5 = gt2.article_barcode )
    WHERE gt2.model = "Document" AND gt2_2.id IS NULL AND gt2.free_field2 != "" AND con.type=7;

# Complete the other data concerning the documents
UPDATE `gt2_details` AS gt2, `_temp_table` as tt SET gt2.free_field2=tt.gt2_contract WHERE gt2.id=tt.gt2_document;
UPDATE `gt2_details` AS gt2
  JOIN `gt2_details_i18n` AS gt2_i18n
    ON ( gt2.id = gt2_i18n.parent_id AND gt2_i18n.lang = "bg" )
  JOIN `_temp_table` AS tt
    ON ( gt2.id = tt.gt2_contract AND gt2.model = 'Contract' )
  JOIN `gt2_details` AS gt2_2
    ON ( gt2_2.id = tt.gt2_document )
  JOIN `documents` AS d
    ON ( gt2_2.model_id = d.id )
  SET gt2.article_second_code=tt.gt2_document, gt2.free_field2='fsa_yes',
      gt2_i18n.free_text2=d.custom_num, gt2_i18n.free_text3=d.date;

# Collect the annexes which have to be marked as failed
TRUNCATE TABLE `_temp_table`;
INSERT INTO _temp_table (`gt2_contract`, `gt2_document`)
  SELECT c.id, NULL FROM `contracts` as c WHERE c.subtype = 'annex' AND c.subtype_status = 'waiting' AND (c.id != (SELECT MAX(c2.id) FROM `contracts` AS c2 WHERE c2.subtype = 'annex' AND c2.subtype_status = 'waiting' AND c2.parent_record = c.parent_record AND c.deleted_by=0) OR c.deleted_by!="0");

# Mark as failed the annexes which are not supposed to be executed
UPDATE `contracts` AS c, `_temp_table` as tt SET c.status='closed', c.subtype_status='failed' WHERE c.id=tt.gt2_contract;

# Remove the temp table
DROP TABLE IF EXISTS `_temp_table`;

# Added additional setting to the 'aon_advanced_incomes_by_clients' report, used for taking information about client and business type
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ninsurance_client_type := client_type\r\ninsurance_business_type := business_type') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%insurance_business_type%';

######################################################################################
# 2013-06-12 - Fixed previously wrong written document numbers in the contracts gt2 rows

# Fixed previously wrong written document numbers in the contracts gt2 rows
UPDATE `gt2_details` AS gt2, `gt2_details_i18n` AS gt2_i18n SET gt2.free_field2='', gt2_i18n.free_text2='', gt2_i18n.free_text3='' WHERE gt2.`model` = 'Contract' AND gt2.id = gt2_i18n.parent_id AND (gt2.article_second_code = "" OR gt2.article_second_code = "0" OR gt2.article_second_code IS NULL) AND gt2_i18n.free_text2 IS NOT NULL AND gt2_i18n.free_text2 != "" AND gt2_i18n.free_text2 != "0";

######################################################################################
# 2013-06-14 - Fixed not completed field `free_field4` for gt2 rows added by dashlet for empty deadline table in the contract

# Fixed not completed field `free_field4` for gt2 rows added by dashlet for empty deadline table in the contract
UPDATE `gt2_details` SET `free_field4`="1" WHERE (`free_field4` IS NULL OR `free_field4`="" OR `free_field4`="0") AND `model`="Contract" AND `model_id` IN (SELECT `id` FROM `contracts` WHERE `type`!=7) AND DATE_FORMAT(`added`, "%Y-%m-%d")>='2013-03-14';

######################################################################################
# 2013-06-20 - Added automations to translate records

# Added automation to translate records
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на документи', 0, NULL, 1, 'documents', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на контрагенти', 0, NULL, 1, 'customers', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на номенклатури', 0, NULL, 1, 'nomenclatures', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Автоматичен превод на договори', 0, NULL, 1, 'contracts', NULL, 'action', 0, '', 'condition := 1', 'plugin := aon\nmethod := autoTranslate', NULL, 1, 1);

######################################################################################
# 2013-07-02 - Added automations to validate additional variables(insurers) in contracts types 1 and 2

# Added automations to validate additional variables(insurers) in contracts types 1 and 2
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Валидация на застрхователи в AGCN полица',               0, NULL, 1, 'contracts', '', 'before_action', 1, '', 'condition := 1', 'plugin := aon\r\nmethod := validateInsurers', 'cancel_action_on_fail := 1', 0, 0),
(NULL, 'Валидация на застрхователи в Презастрахователна полица', 0, NULL, 1, 'contracts', '', 'before_action', 2, '', 'condition := 1', 'plugin := aon\r\nmethod := validateInsurers', 'cancel_action_on_fail := 1', 0, 0);

######################################################################################
# 2013-09-24 - Added periodical export of data

# Added periodical export of data
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Периодичен експорт на данни', 0, NULL, 1, 'customers', 'customers', 'crontab', 3, 'period := 1 day\nstart_time := 03:00\nstart_before := 04:00\n#send_to_email := <EMAIL>\nexport_db := aon_export\nexport_host := localhost\nexport_port := \nexport_user := aon\nexport_pass := ZFmN694BvtaSQpuiAhaS\nexport_table_clients := L1_CLIENT\nexport_table_insurers := L1_INSURER\nexport_table_products := L1_PRODUCT\nexport_table_revenue := L1_REVENUE\nexport_table_departments := L1_DEPARTMENT\nexport_table_users := L1_USER\nexport_table_industry := L1_INDUSTRY\nclient_type := 3\ninsurer_type := 4\nemployee_type := 1\nrevenue_type := 8\ncontract_type_fee := 7', 'condition := 1', 'plugin := aon\r\nmethod := periodicalExport', NULL, 0, 0);

######################################################################################
# 2013-11-18 - Added additional setting to the 'aon_create_invoices' report, used for taking information about the insured client for fee policies

# Added additional setting to the 'aon_create_invoices' report, used for taking information about the insured client for fee policies
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncontract_fee_insured := insured_id') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contract_fee_insured%';

######################################################################################
# 2013-11-19 - Added additional setting to the 'aon_create_report_for_netherlands' report, used for taking information from fee contracts

# Added additional setting to the 'aon_create_report_for_netherlands' report, used for taking information from fee contracts
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncontract_fee := 7\r\npolicy_type_fee := Fee\r\ncontract_fee_value := fee_value\r\ncontract_fee_insured := insured_id') WHERE `type`='aon_create_report_for_netherlands' AND `settings` NOT LIKE '%policy_type_fee%';

######################################################################################
# 2013-12-05 - Added new automation plugin to make assignments in function of the status for vacations(documents)

INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, NULL, '0', NULL, '1', 'documents', NULL, 'before_action', '1', 'substatus_for_manager := 19\nsubstatus_for_accountant := 15\nsubstatus_for_ceo := 16', 'condition := 1', 'plugin := aon\nmethod := manageVacationsAssignments', 'cancel_action_on_fail := 1', '0', '0');

######################################################################################
# 2013-12-12 - Added extra additional var for client for 'aon_checking_giving_bso_numbers'

# Added extra additional var for client for 'aon_checking_giving_bso_numbers'
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\nnomenclature_client_id := client_id\r\nnomenclature_client_name := client_name') WHERE `type` LIKE '%aon_checking_giving_bso_numbers%' AND `settings` NOT LIKE '%nomenclature_client_id%';

######################################################################################
# 2013-12-16 - Added automation to create list of claims

# Added automation to create list of claims
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Съставя списък с щети (конкатенация)', 0, NULL, 1, 'documents', NULL, 'before_action', '4', 'update_var := claim_description\r\nconcat_var := claim_name\r\nconcat_glue := +\r\n#make_unique := 1\r\n#sort := 1\r\n', 'condition := 1', 'plugin := aon\r\nmethod := createClaimList', NULL, 0, 0);

######################################################################################
# 2013-12-17 - Added automation to calculate deadline in health insurance (document type 4)

# Added automation to calculate deadline in health insurance (document type 4)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`) VALUES
(NULL, 'Изчисляване на срок за възстановяване (deadline)', 0, NULL, 1, 'documents', NULL, 'action', '4', 'date_insurer_var := date\r\nperiod_var := reimbursement_period\r\ntype_days_var := reimbursement_type_days\r\nupdate_var := deadline', 'condition := \'[b_date]\' != \'\' && \'[b_date]\' != \'0000-00-00\' && \'[prev_b_date]\' != \'[b_date]\'', 'plugin := aon\r\nmethod := calculateHealthInsuranceDeadline', NULL, 1, 0);
UPDATE `settings` SET `value`='date, insured_person_id, insured_person_name, custom_num, payment_date, paid_sum, paid_sum_currency, waiting_docs' WHERE section='documents' AND name='multiedit_4';
UPDATE `settings` SET `value`='active, name, customer, status, substatus, tag, add_attachments, assignments, assignments_manage, assignments_decision, assignments_observer, trademark, date, deadline' WHERE section='documents' AND name='audit';

########################################################################
# 2013-12-27 - Added AON health insurance numbers import plugin

# Added AON health insurance numbers import plugin
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
(12, 'aon_health_unsurance_numbers', '', 1);

INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(12, 'Импортиране на ЗОН', NULL, 'bg'),
(12, 'Import of health insurance numbers', NULL, 'en');

######################################################################################
# 2014-01-28 - Update the settings of 'aon_create_report_for_netherlands' report to contain settings for recoginizing fee documents

# Update the settings of 'aon_create_report_for_netherlands' report to contain settings for recoginizing fee documents
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ninvoice_notes := invoice_differences_notes', '\r\ninvoice_notes := invoice_differences_notes\r\ninvoice_type := invoice_type\r\ninvoice_type_fee := 3') WHERE `type`='aon_create_report_for_netherlands' AND `settings` NOT LIKE '%invoice_type_fee%';

######################################################################################
# 2013-03-20 - Added new report 'aon_paid_leave_reqests' for the AON installation (1745)

# Added new report 'aon_paid_leave_reqests' for the AON installation (1745)
INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
(287, 'aon_paid_leave_reqests', 'document_leave_type :=\r\nfree_days_type :=\r\nfree_days_count :=\r\nfree_days_year :=\r\nfree_days_start_date :=\r\nfree_days_end_date :=\r\n\r\nleave_request_paid :=\r\nleave_request_unpaid :=\r\n\r\ndays_off_substatus_approved :=\r\ndays_off_substatus_disapproved :=\r\n\r\ndays_off_type_paid :=\r\ndays_off_type_unpaid :=\r\n\r\nemployee_date_start_work :=\r\nemployee_available_days_off :=\r\nemployee_special_days_off :=\r\nemployee_special_year_days_off :=\r\nemployee_special_days_off_leave_reason :=\r\nemployee_special_days_off_option_left :=', 0, 0, 1);
INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
(287, 'Отпуски', NULL, NULL, 'bg'),
(287, 'Leave requests', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_paid_leave_reqests' for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
('reports', 'generate_report', 287, 0, 1);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action='generate_report' AND model_type=287;

######################################################################################
# 2013-04-01 - Added export for 'aon_paid_leave_reqests' report for the AON installation (1745)

# Added export for 'aon_paid_leave_reqests' report for the AON installation (1745)
INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'export', 287, 0, 2);
INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action='export' AND model_type=287;

######################################################################################
# 2013-06-06 - Added new settings for report 'aon_supervision_information'

# Added new settings for report 'aon_supervision_information'
UPDATE `reports` SET `settings` = 'included_contract_types := 1,2,3,4,5,6\ninsurance_group := insurance_group\ninsurer_id := insurer_id\nid_insurer := id_insurer\ninsurance_bonus_bgn := premium__valuelocal\ninsurance_commission := commission__valuelocal' WHERE `id` = 234;

######################################################################################
# 2014-08-01 - Replaced Cyrillic character ("а") with Latin one in autocompleter settings

# PRE-DEPLOYED # Replaced Cyrillic character ("а") with Latin one in autocompleter settings
#UPDATE `_fields_meta` SET `source`=REPLACE(`source`, 'аutocomplete_filter', 'autocomplete_filter') WHERE `source` LIKE '%аutocomplete_filter%';

########################################################################
# 2014-08-19 - Updated settings for 'aon_advanced_incomes_by_clients' report for AON installation (AON)

# Updated settings for 'aon_advanced_incomes_by_clients' report for AON installation (AON)
UPDATE `reports` SET `settings` = 'included_contract_types := 1,2,3,4,5,6,7\r\nincluded_categories := 9,10,11,12,13\r\ncustomer_tags := 7\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission := commission\r\ninsurance_shared_commission := shared_commission_valuelocal\r\ninsurance_client_type := client_type\r\ninsurance_business_type := business_type\r\n\r\ndocument_type_invoice := 8' WHERE `type`='aon_advanced_incomes_by_clients';

########################################################################
# 2014-10-03 - Added new automations for validating insurance sum and count for the policies in AON installation (AON)

# Added new automations for validating insurance sum and count for the policies in AON installation (AON)
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '1', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '2', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '3', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '4', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '5', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация на застрахователна сума и вноски', 0, NULL, 1, 'contracts', NULL, 'before_action', '6', 'premium_value := premium__value\r\nnumber_rows := number_installments\r\nrows_currency := number_installments__value\r\n\r\ntable_commission_value := price\r\ntable_currency := free_field1', 'condition := 1', 'plugin := aon\r\nmethod := validateInsuranceCommissionSum', 'cancel_action_on_fail := 1', 1, 0, 1);

########################################################################
# 2014-11-24 - Chaged conditions for the manageVacationsAssignments automation

# Chaged conditions for the manageVacationsAssignments automation
UPDATE `automations` SET `conditions` = 'condition := ''[action]'' == ''setstatus'' && [request_is_post]' WHERE `id` = 45;

######################################################################################
# 2015-03-31 - Added new report 'aon_create_debit_note' for the AON installation (AON)
#            - Added permissions for generate and export the report 'aon_create_debit_note' for the AON installation (AON)

# PRE-DEPLOYED # Added new report 'aon_create_debit_note' for the AON installation (AON)
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
# (311, 'aon_create_debit_note', 'document_debit_note := 11\r\ndocument_debit_note_type := debit_type\r\ndocument_debit_note_value := debit_value\r\ndocument_debit_note_differences := debit_differences\r\ndocument_debit_note_differences_comment := debit_differences_notes\r\ndocument_debit_note_car_id := car_id\r\ndocument_debit_note_car_name := car_name\r\n\r\ncustomer_type_insurer := 4\r\ncustomer_type_client := 3\r\n\r\nexclude_contract_type := 7\r\n\r\ncasco_policies_individual := 4\r\ncasco_policies_group := 6\r\ncontracts_insurer_id := insurer_id\r\ncontracts_alternatve_insurer_id := id_insurer\r\ncontracts_insurance_type_prefix := insurance_type_con\r\ncontracts_number_payments := number_installments\r\ncontract_casco_car := car_id\r\ncontract_casco_car_name := car_name\r\ncontract_reinsurer_policy := 2\r\n\r\nskip_session_filters := 1', 0, 0, 1);
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
# (311, 'Създаване на дебит нота', NULL, NULL, 'bg'),
# (311, 'Create debit note', NULL, NULL, 'en');

# # Added permissions for generate and export the report 'aon_create_debit_note' for the AON installation (AON)
# INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
# ('reports', 'generate_report', 311, 0, 1);
# INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT 1, id, 'all' FROM `roles_definitions` WHERE module='reports' AND action='generate_report' AND model_type=311;

######################################################################################
# 2015-04-01 - Added new report 'aon_future_debits' for the AON installation (AON)
#            - Added permissions for generate and export the report 'aon_future_debits' for the AON installation (AON)
#            - Added new automation to update the paid amount of debit noties payments for AON installation (AON)

# PRE-DEPLOYED # Added new report 'aon_future_debits' for the AON installation (AON)
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
# (312, 'aon_future_debits', 'aonfd_reports_contract_types := "1","2","3","4","5","6"\n\raonfd_reports_report_model := Contract\n\raonfd_reports_currency := BGN\n\raonfd_reports_customer_type := 4', 0, 0, 1);
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
# (312, 'Предстоящи дебит ноти за издаване', NULL, NULL, 'bg'),
# (312, 'Future debit notes', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_future_debits' for the AON installation (AON)
# INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
# ('reports', 'generate_report', 312, 0, 1),
# ('reports', 'export', 312, 0, 1);
# INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=312;

# PRE-DEPLOYED # Added new automation to update the paid amount of debit noties payments for AON installation (AON)
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#   ('Преизчисляване на платената сума по дебит нота', 0, NULL, 1, 'documents', NULL, 'action', '12', 'debit_note_payment_id := article_id\r\ndebit_note_payment_currently_paid := article_height\r\ndebit_note_payment_value := article_width\r\n\r\ndebit_note_paid_value := debit_value_paid', 'condition := ''[prev_b_status]'' != ''closed''\r\ncondition := ''[b_status]'' == ''closed''', 'plugin := aon\r\nmethod := updateDebitNotePaidAmount', NULL, 1, 1, 1);

######################################################################################
# 2015-04-02 - Added new report 'aon_fees' for the AON installation (AON)
#            - Added permissions for generate and export the report 'aon_fees' for the AON installation (AON)
#            - Added new automation to check if the paid amount does not overpay the debit note AON installation (AON)

# PRE-DEPLOYED # Added new report 'aon_fees' for the AON installation (AON)
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
# (313,'aon_fees', 'aonf_reports_filters_customers_type := 3\r\naonf_reports_contract_types := "4","6"\r\naonf_reports_car_confg := car_id\r\naonf_reports_insurance_type1 := insurance_type_con4\r\naonf_reports_insurance_type2 := insurance_type_con6\r\naonf_reports_chassis_field := car_rama_num\r\naonf_reports_level_service := level_service_id\r\naonf_reports_price := price_service', 0, 0, 1);
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
# (313, 'Дължими такси (НО)', NULL, NULL, 'bg'),
# (313, 'Due fees (LS)', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_fees' for the AON installation (AON)
# INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
# ('reports', 'generate_report', 313, 0, 1),
# ('reports', 'export', 313, 0, 1);
# INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=313;

# PRE-DEPLOYED # Added new automation to check if the paid amount does not overpay the debit note AON installation (AON)
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#   ('Валидация на плащанията по дебит нота', 0, NULL, 1, 'documents', NULL, 'before_action', '12', 'debit_note_payment_id := article_id\r\ndebit_note_total := price\r\ndebit_note_payment_currently_paid := article_height\r\ndebit_note_payment_value := article_width', 'condition := ''[request_is_post]'' == ''1'' && in_array(''[action]'', array(''add'', ''edit'', ''setstatus''))', 'plugin := aon\r\nmethod := validateDebitNotePaymentPaidAmount', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2015-04-14 - Added new report 'aon_general_report_by_car' for the AON installation (AON)
#            - Added permissions for generate and export the report 'aon_general_report_by_car' for the AON installation (AON)

# Added new report 'aon_general_report_by_car' for the AON installation (AON)
 INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
    (314, 'aon_general_report_by_car', 'aongrc_reports_filters_customers_type := 3\r\naongrc_reports_contract_types := "4","6"\r\naongrc_reports_car_confg := car_id\r\naongrc_reports_insurance_type1 := insurance_type_con4\r\naongrc_reports_insurance_type2 := insurance_type_con6\r\naongrc_reports_chassis_field := car_rama_num\r\naongrc_reports_level_service := level_service_id\r\naongrc_reports_price := price_service\r\naongrc_reports_report_model := Contract\r\naongrc_reports_currency := BGN\r\naongrc_reports_customer_type := 4\r\naongrc_reports_bonus_price_field := premium__valuelocal\r\naongrc_reports_annulled_tag := 12', 0, 0, 1);
 INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
    (314, 'Обща справка по автомобили', NULL, NULL, 'bg'),
    (314, 'General report by car', NULL, NULL, 'en');

# Added permissions for generate and export the report 'aon_general_report_by_car' for the AON installation (AON)
 INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
    ('reports', 'generate_report', 314, 0, 1),
    ('reports', 'export', 314, 0, 1);
 INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
   SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=314;

#####################################################################################
# 2015-04-14 - Updated the settings of the report 'aon_future_debits' for AON installation and added the setting [aonfd_reports_annulled_tag] which defines the annulled tag id

# Updated the settings of the report 'aon_future_debits' for AON installation and added the setting [aonfd_reports_annulled_tag] which defines the annulled tag id
UPDATE `reports`
SET `settings`='aonfd_reports_contract_types := 1,2,3,4,5,6\r\naonfd_reports_report_model := Contract\r\naonfd_reports_currency := BGN\r\naonfd_reports_customer_type := 4\r\naonfd_reports_annulled_tag := 12'
WHERE `id` = 312;

#####################################################################################
# 2015-04-15 - Updated the settings of `aon_fees` to ignore rows with annulled tag
#            - Switched the names of `aon_fees` and `aon_general_report_by_car` in `reports_i18n`

# Updated the settings of `aon_fees` to ignore rows with annulled tag
UPDATE `reports`
SET `settings` = 'aonf_reports_filters_customers_type := 3\r\naonf_reports_contract_types := "4","6"\r\naonf_reports_car_confg := car_id\r\naonf_reports_insurance_type1 := insurance_type_con4\r\naonf_reports_insurance_type2 := insurance_type_con6\r\naonf_reports_chassis_field := car_rama_num\r\naonf_reports_level_service := level_service_id\r\naonf_reports_price := price_service\r\naonf_reports_annulled_tag := 12'
WHERE `id` = 313;

# Switched the names of `aon_fees` and `aon_general_report_by_car` in `reports_i18n`
UPDATE `reports_i18n` SET `name` = 'Обща справка по автомобили' WHERE `parent_id` = 313 AND `lang` = 'bg';
UPDATE `reports_i18n` SET `name` = 'General report by car' WHERE `parent_id` = 313 AND `lang` = 'en';
UPDATE `reports_i18n` SET `name` = 'Дължими такси (НО)' WHERE `parent_id` = 314 AND `lang` = 'bg';
UPDATE `reports_i18n` SET `name` = 'Due Fees (LS)' WHERE `parent_id` = 314 AND `lang` = 'en';

#####################################################################################
# 2015-04-16 - Updated the settings of `aon_create_debit_note` to ignore policies with annulled tag

# Updated the settings of `aon_create_debit_note` to ignore policies with annulled tag
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontract_annulled_tag := 12') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%contract_annulled_tag%';

#####################################################################################
# 2015-04-23 - Added automations to manage with changing records related to annulled debit notes
#            - Updated the settings of `aon_create_debit_note` to not allow attaching of policies to debit notes

# Added automations to manage with changing records related to annulled debit notes
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Анулиране на дебит нота (изчистване на свързани данни)', 0, NULL, 1, 'documents', NULL, 'action', '11', 'payment_type := 12', 'condition := ''[prev_b_substatus]'' != ''26''\r\ncondition := ''[b_substatus]'' == ''26''', 'plugin := aon\r\nmethod := clearAnnulledDebitNoteRelations', NULL, 0, 0, 1),
('Анулиране на дебит нота (проверка на плащане)', 0, NULL, 1, 'documents', NULL, 'before_action', '11', 'check_status := closed_26\r\npayment_type := 12', 'condition := ''[request_is_post]'' && ''[action]''==''setstatus''', 'plugin := aon\r\nmethod := checkDebitNoteAnullment', 'cancel_action_on_fail := 1', 0, 0, 1);

# Updated the settings of `aon_create_debit_note` to not allow attaching of policies to debit notes
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ndebit_note_annulled_substatus := 26') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%debit_note_annulled_substatus%';

#####################################################################################
# 2015-04-28 - Updated the settings of `aon_supervision_log` with data for annulled tag
#            - Updated the settings of `aon_create_report_for_netherlands` with data for annulled tag

# Updated the settings of `aon_supervision_log` with data for annulled tag
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontracts_tag_annulled := 12') WHERE `type`='aon_supervision_log' AND `settings` NOT LIKE '%contracts_tag_annulled%';

# Updated the settings of `aon_create_report_for_netherlands` with data for annulled tag
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontracts_tag_annulled := 12') WHERE `type`='aon_create_report_for_netherlands' AND `settings` NOT LIKE '%contracts_tag_annulled%';

#####################################################################################
# 2015-04-30 - Updated the settings of `aon_upcoming_renewals` to include the commission total and the installment counts

# Updated the settings of `aon_upcoming_renewals` to include the commission total and the installment counts
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ninsurance_commission_sum := commission__valuelocal\r\ninsurance_payment_total := number_installments') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%insurance_payment_total%';

#####################################################################################
# 2015-04-30 - Added report 'aon_report_to_insurer' for the AON installation (AON)
#            - Added permissions for generate the report 'aon_report_to_insurer' for the AON installation (AON)

# PRE-DEPLOYED # Added report [Report to insurer]
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#   (316, 'aon_report_to_insurer', 'report_filters_required := customer,period_from,period_to\r\nreport_model_type := Document\r\nreport_document_type := 11\r\naonrti_payment_column := debit_value_paid\r\naonrti_filter_customer := insurer_id\r\naonrti_payment_doc_type := 12\r\naonrti_payment_status := closed\r\naonrti_target_document_type := 13\r\naonrti_target_document_ignore_statuses := locked,closed', 0, 0, 1);
#
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#   ('316', 'Report to insurer', NULL, NULL, 'en'),
#   ('316', 'Отчет към застраховател', NULL, NULL, 'bg');
#
# # Added permissions for generate the report 'aon_report_to_insurer' for the AON installation (AON)
#  INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#     ('reports', 'generate_report', 316, 0, 1);
#  INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#    SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report') AND `model_type`=316;

######################################################################################
# 2015-05-23 - Updated settings for the automation that cheacked the validity of the sums (commission / premium value) to include shared commission
#            - Updated the dashlet for correcting policies to work correctly with shared commission

# Updated settings for the automation that cheacked the validity of the sums (commission / premium value) to include shared commission
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nshared_commission_field := free_field4\r\nshared_commission_shared := 2') WHERE `method` LIKE('%validateInsuranceCommissionSum%') AND `settings` NOT LIKE '%shared_commission_field%';

# Updated the dashlet for correcting policies to work correctly with shared commission
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ntype_commission_field := type_commission\r\nshared_commission_types := 2,4,6\r\ncommission_type_field := free_field4\r\nstandard_commission_value := 1\r\nshared_commission_value := 2\r\n\r\ngrouping_shared_table_name := shared_commission_group\r\ngrouping_shared_customer_id := shared_commission_id\r\ngrouping_shared_customer_name := shared_commission_name\r\ngrouping_shared_percent := shared_commission_percent\r\ngrouping_shared_value := shared_commission_value\r\ngrouping_shared_value_currency := shared_commission_valuelocal') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%type_commission_field%';

#####################################################################################
# 2015-06-09 - Updated the settings of `aon_create_debit_note` to registration number and insurer name in BG

# Updated the settings of `aon_create_debit_note` to registration number and insurer name in BG
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ndocument_debit_note_car_reg_num := car_name_print\r\ncustomer_insurer_name_in_bgn := name_in_bg') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%document_debit_note_car_reg_num%';

#####################################################################################
# 2015-06-17 - Updated the settings of `aon_create_invoices` report to contain the tag for annulled policy and to contain relation to fee
#            - Update the existing fees

# Updated the settings of `aon_create_invoices` report to contain the tag for annulled policy and to contain relation to fee
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontract_annulled_tag := 12\r\ncontract_fee_invoiced := invoiced_fee') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contract_annulled_tag%';

# Update the existing fees
SET @var_type_invoice := (SELECT `id` FROM _fields_meta WHERE `model`='Document' AND `name`='invoice_type' AND `model_type`=8);
SET @var_nvoiced_fee := (SELECT `id` FROM _fields_meta WHERE `model`='Contract' AND `name`='invoiced_fee' AND `model_type`=7);
INSERT IGNORE INTO `contracts_cstm`
SELECT `free_field2`, @var_nvoiced_fee, 1, '', NULL, NULL, NULL, NULL, NOW(), 1, NOW(), 1, '' FROM `gt2_details` WHERE `model`='Document' AND `model_id` IN (SELECT `model_id` FROM documents_cstm WHERE `var_id`=@var_type_invoice AND `value`=3) AND `free_field2`!="";
UPDATE `contracts_cstm` c_cstm, `gt2_details` gt2 SET c_cstm.value=gt2.id WHERE gt2.free_field2=c_cstm.model_id AND c_cstm.var_id=@var_nvoiced_fee AND gt2.model='Document' AND gt2.model_id IN (SELECT `model_id` FROM documents_cstm WHERE `var_id`=@var_type_invoice AND `value`=3) AND gt2.free_field2!="";

#####################################################################################
# 2015-06-18 - Remove settings that are not used in `aon_create_debit_note` report

# Remove settings that are not used in `aon_create_debit_note` report
UPDATE `reports` SET `settings` = 'document_debit_note := 11\r\ndocument_debit_note_type := debit_type\r\ndocument_debit_note_value := debit_value\r\ndocument_debit_note_car_id := car_id\r\ndocument_debit_note_car_name := car_name\r\n\r\ncustomer_type_insurer := 4\r\ncustomer_type_client := 3\r\n\r\nexclude_contract_type := 7\r\n\r\ncasco_policies_individual := 4\r\ncasco_policies_group := 6\r\ncontracts_insurer_id := insurer_id\r\ncontracts_alternatve_insurer_id := id_insurer\r\ncontracts_insurance_type_prefix := insurance_type_con\r\ncontracts_number_payments := number_installments\r\ncontract_casco_car := car_id\r\ncontract_casco_car_name := car_name\r\ncontract_reinsurer_policy := 2\r\n\r\nskip_session_filters := 1\r\ncontract_annulled_tag := 12\r\ndebit_note_annulled_substatus := 26\r\n\r\ndocument_debit_note_car_reg_num := car_name_print\r\ncustomer_insurer_name_in_bgn := name_in_bg' WHERE `type`='aon_create_debit_note' AND `settings` LIKE '%document_debit_note_differences%';

#####################################################################################
# 2015-06-22 - Updated the settings of `aon_bonuses_to_invoice` report to contain the tag for annulled policy

# Updated the settings of `aon_bonuses_to_invoice` report to contain the tag for annulled policy
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontract_annulled_tag := 12') WHERE `type`='aon_bonuses_to_invoice' AND `settings` NOT LIKE '%contract_annulled_tag%';

#####################################################################################
# 2015-06-26 - Updated the settings of `aon_advanced_incomes_by_clients` report to contain the tag for annulled policy

# Updated the settings of `aon_advanced_incomes_by_clients` report to contain the tag for annulled policy
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ncontract_annulled_tag := 12') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%contract_annulled_tag%';

#####################################################################################
# 2015-07-03 - Updated the settings of `aon_fees` report to add insurer type

# Updated the settings of `aon_fees` report to add insurer type
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\naonf_reports_name_insurer := id_insurer\r\naonf_reports_filters_customer_insurer_type := 4\r\naongrc_reports_annulled_tag := 12') WHERE `type` = 'aon_fees' AND `settings` NOT LIKE '%aonf_reports_filters_customer_insurer_type%';

#####################################################################################
# 2015-07-15 - Added validation for unique data in planned visits schedule

# Added validation for unique data in planned visits schedule
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация на уникалността на график за посещение', 0, NULL, 1, 'documents', '', 'before_action', '14', 'policy := policy_id\r\npolicy_from := policy_from\r\npolicy_to := policy_to\r\ninsurer := policy_insurer_id\r\nhospital := hospital_id\r\n\r\nplan_from := plan_date_from\r\nplan_to := plan_date_to\r\nstart_hour := plan_start_hour\r\nend_hour := plan_end_hour', 'condition := [request_is_post] && (''[action]'' == ''add'' || ''[action]'' == ''edit'')', 'plugin := aon\r\nmethod := checkDuplicateScheduleVisits', 'cancel_action_on_fail := 1', 0, 0, 1);

#####################################################################################
# 2015-07-27 - Added additional settings to 'aon_advanced_incomes_by_clients' so the insurer of the policy to be shown

# PRE-DEPLOYED # Added additional settings to 'aon_advanced_incomes_by_clients' so the insurer of the policy to be shown
# UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\ninsurer_id := insurer_id\r\nid_insurer := id_insurer') WHERE `type` = 'aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%insurer_id%';

######################################################################################
# 2015-07-29 - Added additional settings for automation which check the validity of the schedule so locked rows to remain locked

# Added additional settings for automation which check the validity of the schedule so locked rows to remain locked
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nhour_interval := plan_interval\r\nhours_capacity := plan_capacity\r\nplan_locked := plan_locked') WHERE `method` LIKE('%checkDuplicateScheduleVisits%') AND `settings` NOT LIKE '%hour_interval%';

######################################################################################
# 2015-10-14 - Updated settings of 'aon_advanced_incomes_by_clients' report (for AON installation) so the report will include fees correctly

# Updated settings of 'aon_advanced_incomes_by_clients' report (for AON installation) so the report will include fees correctly
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncontract_fee_type := 7\r\ndocument_invoice_type := invoice_type\r\ndocument_invoice_type_fee := 3') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%document_invoice_type_fee%';

######################################################################################
# 2015-10-27 - Added placeholders in the template of the medical exam confirmation email

# Added placeholders in the template of the medical exam confirmation email
UPDATE `emails_i18n` SET body='<table border="0" cellpadding="0" cellspacing="0">\r\n	<tbody>\r\n		<tr>\r\n			<td>Здравейте [hin_name],</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С настоящото писмо Ви уведомяваме, че имате резервация за провеждане на профилактичен преглед на дата [reservation_date] от [reservation_start_hour] часа в здравно заведение [a_hospital_name] с адрес [a_hospital_address]. Вие имате възможност за [reservation_corrections] брой корекции на резервиран час.</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Моля имайте предвид, че корекция на заявения вече час можете да направите до [reservation_window_days] дни преди заявената дата!</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>При възникнали въпроси можете да се свържете с Вашия отговорник по профилактика [a_aon_employee_name] на телефон [a_aon_employee_gsm] или да пишете имейл на [a_aon_employee_email].</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С уважение,</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Екипът на Аон България</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n		</tr>\r\n		<tr>\r\n			<td><img alt="" src="http://aon.n-zoom.com/resources/uploads/image/220px-Aon_Corporation_logo.jpg" style="float:right; height:37px; margin-bottom:10px; margin-top:10px; width:88px" /></td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n' WHERE parent_id=1001 AND lang='bg';

######################################################################################
# 2015-10-28 - Added automation to remind the user of incoming medical exams
#            - Added placeholders to the template of the medical exam reminder email
#            - Updated the contact placeholders in the template of the medical exam confirmation email

# PRE-DEPLOYED # Added automation to remind the user of incoming medical exams
# INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
  (NULL, 'Напомняне за предстоящи профилактични прегледи', 0, NULL, 1, 'documents', NULL, 'crontab', '15', 'email_template := 1003\r\nstart_time := 08:00\r\nstart_before := 09:00\r\nperiod := 1 day\r\n', 'condition := 5\r\nfilter_sql_condition := 5 OR 5 AND deleted = 0 AND active=1 AND type = \'15\' AND id IN (SELECT dc1.model_id FROM documents_cstm dc1 JOIN nom_cstm nc1  ON dc1.var_id=3109 AND nc1.var_id=902 AND dc1.value=nc1.model_id AND nc1.value!=\'\' JOIN documents_cstm dc2 ON dc2.var_id=3113 AND dc1.model_id=dc2.model_id AND dc1.num=dc2.num AND DATE_SUB(dc2.value, INTERVAL 1 day) = CURDATE())\r\n  ', 'plugin := aon\r\nmethod := reminderMedicalExams', NULL, 0, 0, 1);
# PRE-DEPLOYED # Added placeholders in the template of the medical exam reminder email
# UPDATE `emails_i18n` SET body='<table border="0" cellpadding="0" cellspacing="0">\r\n	<tbody>\r\n		<tr>\r\n			<td>Здравейте [hin_name],</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С настоящото писмо Ви напомняме, че имате резервация за провеждане на профилактичен преглед на дата [reservation_date] от [reservation_start_hour] часа в здравно заведение [a_hospital_name] с адрес [a_hospital_address].</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>При възникнали въпроси можете да се свържете с Вашия отговорник по профилактика: [contact_data].</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С уважение,</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Екипът на Аон България</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n		</tr>\r\n		<tr>\r\n			<td><img alt="" src="http://aon.n-zoom.com/resources/uploads/image/220px-Aon_Corporation_logo.jpg" style="float:right; height:37px; margin-bottom:10px; margin-top:10px; width:88px" /></td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n' WHERE parent_id=1003 AND lang='bg';
# PRE-DEPLOYED # Updated the contact placeholders in the template of the medical exam confirmation email
# UPDATE `emails_i18n` SET body='<table border="0" cellpadding="0" cellspacing="0">\r\n	<tbody>\r\n		<tr>\r\n			<td>Здравейте [hin_name],</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С настоящото писмо Ви уведомяваме, че имате резервация за провеждане на профилактичен преглед на дата [reservation_date] от [reservation_start_hour] часа в здравно заведение [a_hospital_name] с адрес [a_hospital_address]. Вие имате възможност за [reservation_corrections] брой корекции на резервиран час.</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Моля имайте предвид, че корекция на заявения вече час можете да направите до [reservation_window_days] дни преди заявената дата!</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>При възникнали въпроси можете да се свържете с Вашия отговорник по профилактика: [contact_data].</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>С уважение,</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>Екипът на Аон България</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td>&nbsp;</td>\r\n		</tr>\r\n		<tr>\r\n			<td><strong>МОЛЯ, НЕ ОТГОВАРЯЙТЕ НА ТОЗИ E-MAIL!</strong> Той е генериран и изпратен от автоматичната система за известяване на <strong>nZoom</strong>.</td>\r\n		</tr>\r\n		<tr>\r\n			<td><img alt="" src="http://aon.n-zoom.com/resources/uploads/image/220px-Aon_Corporation_logo.jpg" style="float:right; height:37px; margin-bottom:10px; margin-top:10px; width:88px" /></td>\r\n		</tr>\r\n	</tbody>\r\n</table>\r\n' WHERE parent_id=1001 AND lang='bg';

######################################################################################
# 2015-11-12 - Added new report - 'aon_leasing_companies' - for AON (AON) installation

# PRE-DEPLOYED # Added new report - 'aon_leasing_companies' - for AON (AON) installation
# INSERT INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#   (330, 'aon_leasing_companies', 'leasing_customers_type := 3\r\nincluded_contract_types := 4,6\r\nincluded_insurance_types := 61,18\r\n\r\ninsurance_type_contract_var_4 := insurance_type_con4\r\ninsurance_type_contract_var_6 := insurance_type_con6\r\ncontract_insurer_id := insurer_id\r\ncontract_car_id := car_id\r\ncontract_tax_var := tax__valuelocal\r\ncontract_premium_var := premium__valuelocal\r\ncontract_leasing_customer_var := id_insurer\r\ncontract_installment_count_var := number_installments\r\n\r\nnom_car_type := 5\r\nnom_car_rama := car_rama_num', 0, 0, 1);
# INSERT INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#   (330, 'Справка по Лизингови компании', NULL, NULL, 'bg'),
#   (330, 'Report by leasing companies', NULL, NULL, 'en');
#
# INSERT INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#   ('reports', 'generate_report', 330, 0, 1),
#   ('reports', 'export', 330, 0, 1);
# INSERT INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#   SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=330;

######################################################################################
# 2015-11-17 - Fee contracts are completed with their related invoice num
#            - Added setting for aon_create_invoices report to contain the var name for invopice num for fees

# Fee contracts are completed with their related invoice num
INSERT INTO `contracts_cstm` (`var_id`, `model_id`,`num`, `value`, `added`, `added_by`, `modified`, `modified_by`, `lang`)
  SELECT '709', c.id, '1', d.custom_num, c_cstm.added, c_cstm.added_by, c_cstm.modified, c_cstm.modified_by, ''
  FROM contracts as c
  INNER JOIN contracts_cstm c_cstm
    ON (c_cstm.model_id=c.id AND c_cstm.var_id="708" AND c_cstm.value IS NOT NULL AND c_cstm.value!="" AND c_cstm.value!="0")
  INNER JOIN gt2_details as gt2
    ON (gt2.id=c_cstm.value AND gt2.model="Document")
  INNER JOIN documents as d
    ON (gt2.model_id=d.id)
  WHERE c.`type`=7 AND c.active=1 AND c.subtype="contract" AND c.annulled_by=0 AND c.deleted_by=0
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`), `modified` = VALUES(`modified`), `modified_by` = VALUES(`modified_by`);

# Added setting for aon_create_invoices report to contain the var name for invopice num for fees
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ncontract_fee_invoiced_visible := invoiced_visible') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contract_fee_invoiced_visible%';

######################################################################################
# 2015-12-11 - Update 'aon_create_debit_note' report with data for the alternative document - insurer devit note

# Update 'aon_create_debit_note' report with data for the alternative document - insurer devit note
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, 'debit_note_type=a_debit_type', 'debit_note_type=b_type') WHERE `model`="Document" AND `type`="button" AND `model_type` IN (11, 16) AND `source` LIKE '%debit_note_type=a_debit_type%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_type_insurer :=', '\r\n\r\ndocument_debit_note_insurer := 16\r\ndocument_debit_note_annulled_substatus :=\r\n\r\ncustomer_type_insurer :=') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%document_debit_note_insurer%';

######################################################################################
# 2015-12-28 - Update 'aon_advanced_incomes_by_clients' report settings with data for leasing company var names

# Update 'aon_advanced_incomes_by_clients' report settings with data for leasing company var names
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncontract_leasing_company_1 := insurer_company_id\r\ncontract_leasing_company_2 := insurer_company_id\r\ncontract_leasing_company_3 := insurer_company_id\r\ncontract_leasing_company_4 := id_insurer\r\ncontract_leasing_company_5 := insurer_company_id\r\ncontract_leasing_company_6 := id_insurer') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%contract_leasing_company_1%';

######################################################################################
# 2016-01-13 - Update 'aon_checking_giving_bso_numbers' report settings with data for health insurance

# Update 'aon_checking_giving_bso_numbers' report settings with data for health insurance
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ncontract_health_insurance := 5') WHERE `type`='aon_checking_giving_bso_numbers' AND `settings` NOT LIKE '%contract_health_insurance%';

######################################################################################
# 2016-01-26 - Update 'aon_supervision_information' report settings for included categories
#            - Change the var for commision in 'aon_advanced_incomes_by_clients' report

# Update 'aon_supervision_information' report settings for included categories
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nincluded_categories := 4,6,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32') WHERE `type`='aon_supervision_information' AND `settings` NOT LIKE '%included_categories%';

# Change the var for commision in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ninsurance_commission := commission\r\n', '\r\ninsurance_commission := commission__valuelocal\r\n') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%insurance_commission := commission__valuelocal%';

######################################################################################
# 2016-01-28 - Change the var for car id in 'aon_upcoming_renewals' report
#            - Added settings for 'aon_upcoming_renewals' report that contain data for car nomenclature

# Change the var for car id in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'car_name := car_name', 'car_name := car_id') WHERE `type`='aon_upcoming_renewals' AND `settings` LIKE '%car_name := car_name%';

# Added settings for 'aon_upcoming_renewals' report that contain data for car nomenclature
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncar_nom_type_id := 5\r\ncar_rama := car_rama_num') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%car_nom_type_id%';

######################################################################################
# 2016-02-26 - Update 'aon_leasing_companies' report settings

# Update 'aon_leasing_companies' report settings
UPDATE `reports` SET `settings`='leasing_customers_type := 3,4\r\nincluded_contract_types := 1,2,3,4,5,6\r\nincluded_categories := 9,10,11,12,13\r\n\r\ncontract_insurer_id := insurer_id\r\ncontract_car_id := car_id\r\ncontract_tax_var := tax__valuelocal\r\ncontract_premium_var := premium__valuelocal\r\ncontract_insurance_type_prefix := insurance_type_con\r\ncontract_insurance_group := insurance_group\r\ncontract_leasing_customer_var_1 := insurer_company_id\r\ncontract_leasing_customer_var_2 := insurer_company_id\r\ncontract_leasing_customer_var_3 := insurer_company_id\r\ncontract_leasing_customer_var_4 := id_insurer\r\ncontract_leasing_customer_var_5 := insurer_company_id\r\ncontract_leasing_customer_var_6 := id_insurer\r\ncontract_installment_count_var := number_installments\r\n\r\nnom_car_type := 5\r\nnom_car_rama := car_rama_num\r\n\r\nstandard_commission_value := 1\r\nshared_commission_value := 2' WHERE `type`='aon_leasing_companies';

######################################################################################
# 2016-04-06 - Added new import 'aon_import_casco_policies' for the AON installation (AON)

# Added new import 'aon_import_casco_policies' for the AON installation (AON)
INSERT INTO `imports` (`id`, `type`, `settings`, `visible`) VALUES
  (17, 'aon_import_casco_policies', 'dzilink := https://wsnewtest.dzi.bg/axis2/services/DZIMTPLServices?wsdl\r\ndziusername := TS2_AON\r\ndzipassword := TS2_AON\r\n\r\nbulstradlink :=\r\nbulstradusername :=\r\nbulstradpassword :=\r\n\r\nimport_test_mode := 0', 1);
INSERT INTO `imports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
  (17, 'Импорт на полици каско/гражданска отговорност', NULL, 'bg'),
  (17, 'Import CASCO/liability policies', NULL, 'en');

######################################################################################
# 2016-05-13 - Updated conditions of crontab automations

# Updated conditions of crontab automations
UPDATE `automations` SET `conditions` = 'where := d.active = 1\r\nwhere := a__insured_person_id != ''''\r\nwhere := (SELECT value FROM nom_cstm WHERE var_id = 902 AND model_id = dcstm0.value) != ''''\r\nwhere := DATE_FORMAT(a__reserved_date, ''%Y-%m-%d'') = DATE_ADD(CURDATE(), INTERVAL 1 DAY)\r\nwhere := dcstm0.num = dcstm1.num' WHERE `id` = 64;

######################################################################################
# 2016-06-03 - Added option for recalculating a GT2 table (payment plan) with a button for AON installation
#            - The automation for caculating the GT2 table is updated so it can be run more than once

# Added option for recalculating a GT2 table (payment plan) with a button for AON installation
UPDATE `_fields_meta` SET `source`='onclick := if (confirm(''Падежната таблица ще бъде създадена наново с актуалните данни! Сигурни ли сте, че желаете да продължите?'')) {this.form.action += ''&amp;recalculate_payments_table=1''; $$(''button.button[type=\\''submit\\''][name=\\''saveButton1\\'']'')[0].click();}' WHERE `model`='Contract' AND `name`='create_twice' AND `type`='button' AND `source` NOT LIKE '%onclick%';

# The automation for caculating the GT2 table is updated so it can be run more than once
UPDATE `automations` SET `nums`=0, `conditions`='condition := ''[action]'' == ''add'' || (''[action]'' == ''edittopic'' && ''[request_is_post]'' == ''1'' && $request->get(''recalculate_payments_table''))' WHERE `method` LIKE '%calculateDateOfPayment%' AND `conditions` NOT LIKE '%recalculate_payments_table%';

######################################################################################
# 2016-08-05 - Updated automation 'setContractContacts' to not be executed if the adiministrative contact is already set
#            - Updated contracts added after 02.08.2016 to complete the respective administrative contact
#            - Update contract for Casco policies to return their validity date with 1 day back because the BULSTRAD file contains dates with one day forward

# PRE-DEPLOYED # Updated automation 'setContractContacts' to not be executed if the adiministrative contact is already set
#UPDATE automations SET `conditions`= 'condition := \'[b_id]\' != \'\' && \'[prev_b_id]\' == \'\' && \'[b_self_administrative]\' == \'\'' WHERE `conditions` NOT LIKE '%b_self_administrative%' AND `module`='contracts' AND `start_model_type`=4 AND `method` LIKE '%setContractContacts%';
#
## Updated contracts added after 02.08.2016 to complete the respective administrative contact
#UPDATE contracts as c, users as u
#SET c.self_administrative=u.employee, c.self_adm_email=u.email
#WHERE u.employee=c.employee AND DATE_FORMAT(c.added, '%Y-%m-%d')>='2016-08-03' AND (c.self_administrative IS NULL OR c.self_administrative=0) AND c.`type`=4 AND c.subtype='contract';
#
## Update contract for Casco policies to return their validity date with 1 day back because the BULSTRAD file contains dates with one day forward
#UPDATE contracts as c, contracts_cstm as c_cstm
#SET c.date_validity=DATE_SUB(c.date_start,INTERVAL 1 DAY)
#WHERE c_cstm.model_id=c.id AND c_cstm.var_id=405 AND c_cstm.value=18 AND
#      DATE_FORMAT(c.added, '%Y-%m-%d')>='2016-08-03' AND c.`type`=4 AND
#      c.subtype='contract' AND DATE_ADD(c.date_start,INTERVAL 1 YEAR)=c.date_validity;

######################################################################################
# 2016-08-09 - Added missing setting for 'aon_general_report_by_car' report for AON installation (AON)

# Added missing setting for 'aon_general_report_by_car' report for AON installation (AON)
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\naongrc_reports_insurance_insurer := id_insurer') WHERE `type`='aon_general_report_by_car' AND `settings` NOT LIKE '%aongrc_reports_insurance_insurer%';

######################################################################################
# 2016-12-20 - Added extra settings for 'addBSONomenclatures' automation for AON installation (AON) which contain the names of the prefix and suffix for the BSO nomenclatures
#            - Added missing setting in the 'calculateDateOfPayment' automation for AON installation (AON)

# Added extra settings for 'addBSONomenclatures' automation for AON installation (AON) which contain the names of the prefix and suffix for the BSO nomenclatures
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nnomenclature_type :=', '\r\ndocument_prefix := prefix_name\r\ndocument_suffix := suffix_name\r\nnomenclature_type :=') WHERE `method` LIKE '%addBSONomenclatures%' AND `settings` NOT LIKE '%document_prefix%';

# Added missing setting in the 'calculateDateOfPayment' automation for AON installation (AON)
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nalternative_insurer :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND `settings` NOT LIKE '%alternative_insurer :=%';

######################################################################################
# 2017-02-28 - Added extra settings for 'aon_bonuses_to_invoice' report for AON installation (AON) to include types customers insurers and clients
#            - Added extra settings for 'aon_advanced_incomes_by_clients' report for AON installation (AON) to include customers type insurer

# Added extra settings for 'aon_bonuses_to_invoice' report for AON installation (AON) to include types customers insurers and clients
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncustomer_insurer_type := 4\r\ncustomer_client_type := 3') WHERE `type`='aon_bonuses_to_invoice' AND `settings` NOT LIKE '%customer_insurer_type%';

# Added extra settings for 'aon_advanced_incomes_by_clients' report for AON installation (AON) to include customers type insurer
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncustomer_insurer_type := 4') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%customer_insurer_type%';

######################################################################################
# 2017-04-25 - Added new automations for preventing adding fee is fee is already added or if shared commission is set
#            - Added new automations for preventing adding fee is fee is already added or if shared commission is set

# Added new automations for preventing adding fee is fee is already added or if shared commission is set for contracts types 1 to 6
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '1', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1),
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '2', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1),
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '3', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1),
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '4', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1),
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '5', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1),
('Проверка за съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'before_action', '6', 'shared_commission_percent := shared_commission_value\r\nfee_value := fee__value\r\nfee_count_payments := feenumber_installments\r\n\r\nfee_policy_id := fee_policy_id\r\nfee_policy_num := fee_policy_name\r\nfee_policy_value := fee_policy_value\r\nfee_policy_from := fee_from\r\nfee_policy_to := fee_to', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[request_is_post]'' == ''1''', 'plugin := aon\r\nmethod := checkAddingFee', 'cancel_action_on_fail := 1', 1, 0, 1);

# Added new automations for creating fees for contracts types 1 to 6
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '1', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1),
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '2', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1),
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '3', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1),
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '4', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1),
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '5', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1),
('Съставяне на таблицата с такси', 0, NULL, 1, 'contracts', NULL, 'action', '6', 'fee_type_id := 7\r\n\r\nfee_insured_id := insured_id\r\nfee_insured_name := insured_name\r\nfee_business_type := business_type\r\nfee_client_type := client_type\r\nfee_value := fee_value\r\n\r\ninsurance_type_var_prefix := insurance_type_con\r\nshared_commission_percent := shared_commission_value\r\n\r\npolicy_business_type := business_type\r\npolicy_client_type := client_type\r\n\r\n#policy_fee_value := fee__value\r\npolicy_fee_value_bgn := fee__valuelocal\r\npolicy_fee_count_payments := feenumber_installments\r\npolicy_fee_policy_id := fee_policy_id\r\npolicy_fee_policy_num := fee_policy_name\r\npolicy_fee_policy_value := fee_policy_value\r\npolicy_fee_policy_from := fee_from\r\npolicy_fee_policy_to := fee_to\r\npolicy_fee_num := fee_num', 'condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]''\r\n\r\n\r\n', 'plugin := aon\r\nmethod := addFee', NULL, 1, 1, 1);

######################################################################################
# 2017-05-10 - Added new report - 'aon_invoiced_uninvoiced_fees' - for AON (AON) installation

# Added new report - 'aon_invoiced_uninvoiced_fees' - for AON (AON) installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (364, 'aon_invoiced_uninvoiced_fees', 'contract_type_fee := 7\r\n\r\ncustomer_insurer_type := 4\r\ncustomer_client_type := 3\r\ncontract_fee_value := fee_value\r\ncontract_fee_invoiced := invoiced_fee\r\ncontract_fee_invoiced_num := invoiced_visible\r\ncontract_fee_insured := insured_id\r\n\r\npolicy_contract_types := 1,2,3,4,5,6\r\npolicy_insurance_type_prefix := insurance_type_con\r\npolicy_insurer := insurer_id\r\npolicy_fee_id := fee_policy_id\r\npolicy_fee_num := fee_num', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (364, 'Фактурирани/нефактурирани Такси (FEE)', NULL, NULL, 'bg'),
  (364, 'Invoiced/uninviced fees', NULL, NULL, 'en');

INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 364, 0, 1),
  ('reports', 'export', 364, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=364;

######################################################################################
# 2017-05-11 - Added additional settings for model vars in the feee and the policy in 'checkAddingFee' and 'addFee' automations for AON (AON) installation

# Added additional settings for model vars in the feee and the policy in 'checkAddingFee' and 'addFee' automations for AON (AON) installation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\nfee_type := 7\r\nfee_invoiced := invoiced_fee'), `nums`=0 WHERE `method` LIKE '%checkAddingFee%' AND `settings` NOT LIKE '%invoiced_fee%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nfee_value := fee_value', '\r\nfee_value := fee_value\r\nfee_invoiced := invoiced_fee'), `nums`=0 WHERE `method` LIKE '%addFee%' AND `settings` NOT LIKE '%invoiced_fee%';

######################################################################################
# 2017-05-15 - Updated conditions for 'addFee' automations for AON (AON) installation to match the cases with changed fee value

# Updated conditions for 'addFee' automations for AON (AON) installation to match the cases with changed fee value
UPDATE `automations` SET `conditions`='condition := in_array(''[action]'', array(''add'', ''edittopic'')) && ((''[prev_a_fee__value]'' == '''' && ''[prev_a_fee__value]'' != ''[a_fee__value]'' && ''[prev_a_feenumber_installments]'' == '''' && ''[prev_a_feenumber_installments]'' != ''[a_feenumber_installments]'') || (''[prev_a_fee__value]'' != '''' && ''[prev_a_fee__value]'' != ''[a_fee__value]''))' WHERE `method` LIKE '%addFee%' AND `conditions` NOT LIKE '%''[prev_a_fee__value]'' == ''''%';

######################################################################################
# 2017-08-16 - Updated incorrectly calculated commission value and commission value in local currency for all the contracts FOR DZI Insurance group

# Updated incorrectly calculated commission value and commission value in local currency for all the contracts FOR DZI Insurance group
UPDATE contracts as c, contracts_cstm as cc_insur_val, contracts_cstm as cc_insurer, contracts_cstm as com, contracts_cstm as prem_val
SET cc_insur_val.value=CAST(ROUND(((prem_val.value * com.value)/100), 2) AS DECIMAL(6,2))
WHERE c.subtype='contract' AND c.annulled_by=0 AND c.id=cc_insurer.model_id AND
      cc_insurer.var_id IN (106, 205, 305, 408, 505, 605) AND cc_insurer.value LIKE '166' AND
      c.id=cc_insur_val.model_id AND cc_insur_val.var_id IN (123, 223, 326, 422, 520, 629) AND
      c.id=com.model_id AND com.var_id IN (122,222,325,421,519,628) AND c.id=prem_val.model_id AND
      prem_val.var_id IN (116, 215, 319, 418, 513, 625) AND ROUND(((prem_val.value * com.value)/100), 2)!=cc_insur_val.value;

UPDATE contracts as c, contracts_cstm as cc_insur_val_loc, contracts_cstm as cc_insurer, contracts_cstm as com, contracts_cstm as prem_val_loc
SET cc_insur_val_loc.value=CAST(ROUND(((prem_val_loc.value * com.value)/100), 2) AS DECIMAL(6,2))
WHERE c.subtype='contract' AND c.annulled_by=0 AND c.id=cc_insurer.model_id AND
      cc_insurer.var_id IN (106, 205, 305, 408, 505, 605) AND cc_insurer.value LIKE '166' AND
      c.id=cc_insur_val_loc.model_id AND cc_insur_val_loc.var_id IN (124, 224, 327, 423, 521, 630) AND
      c.id=com.model_id AND com.var_id IN (122,222,325,421,519,628) AND c.id=prem_val_loc.model_id AND
      prem_val_loc.var_id IN (118, 216, 321, 420, 515, 627) AND ROUND(((prem_val_loc.value * com.value)/100), 2)!=cc_insur_val_loc.value;

# PRE-DEPLOYED ######################################################################################
## 2017-08-28 - Added new report - 'aon_prepare_payments_table' - for AON (AON) installation
##            - Added additional setting for calculateDateOfPayment automation to contain a custom system additional var name which will be used in preliminary payments table calculation
##            - Added source for the button which will adtivate the preliminary calculation of the payments table
#
## Added new report - 'aon_prepare_payments_table' - for AON (AON) installation
#INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
#  (369, 'aon_prepare_payments_table', 'extra_session_field := service_field_correct_add_policy', 0, 0, 0);
#INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
#  (369, 'Подготовка на падежна таблица', NULL, NULL, 'bg'),
#  (369, 'Prepare payments table', NULL, NULL, 'en');
#INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
#  ('reports', 'generate_report', 369, 0, 1);
#INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
#  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report') AND `model_type`=369;
#
## Added additional setting for calculateDateOfPayment automation to contain a custom system additional var name which will be used in preliminary payments table calculation
#UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ncontract_precalculated_gt2_field := service_field_correct_add_policy') WHERE `method` LIKE '%calculateDateOfPayment%' AND `settings` NOT LIKE '%contract_precalculated_gt2_field%';
#
## Added source for the button which will adtivate the preliminary calculation of the payments table
#UPDATE `_fields_meta` SET `source`='permissions := add\r\nhref := reports&reports=generate_report&report_type=aon_prepare_payments_table\r\ntarget := lightbox\r\nlightbox_width := 800\r\nlightbox_height := 500\r\nlightbox_background_color := #F1F1F1' WHERE `type`='button' AND `name`='correction_before_add' AND `model`='Contract' AND `source` NOT LIKE '%href%';

######################################################################################
# 2017-09-15 - Added new report - 'aon_checking_giving_stickers' - for AON installation (AON)

# Added new report - 'aon_checking_giving_stickers' - for AON (AON) installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (371, 'aon_checking_giving_stickers', 'customer_insurer_type := 4\r\nnomenclature_bso_type_id := 33\r\nnomenclature_type_stickers := 11\r\nnomenclature_type_green_cards := 10\r\n\r\nnomenclature_type_blank := type_blank\r\nnomenclature_status := fsa_status\r\nnomenclature_status_free := 1\r\nnomenclature_status_used := 2\r\nnomenclature_status_annuled := 3\r\nnomenclature_insurer := insurer_id\r\nnomenclature_policy_id := policy_id\r\nnomenclature_policy_num := policy_num\r\nnomenclature_protocol_id := protocol_id\r\nnomenclature_status_used := 2\r\ndocument_acceptance_protocol_id := 2\r\nnomenclature_office := brought_to\r\nnomenclature_client_id := client_id\r\nnomenclature_client_name := client_name\r\ncontract_health_insurance := 5\r\n\r\nactive_insurance_groups := 17\r\nstandard_commission_type := 1\r\nshared_commission_type := 2\r\nadditional_commission_type := 3\r\n\r\nskip_session_filters := 1', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (371, 'Проверка и даване на Стикери/ЗК/ГФ', NULL, NULL, 'bg'),
  (371, 'Checking and giving stickers', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 371, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report') AND `model_type`=371;

######################################################################################
# 2017-09-25 - Added new import - 'aon_import_debit_note_payments'

# Added new import - 'aon_import_debit_note_payments'
INSERT IGNORE INTO `imports` VALUES (18, 'aon_import_debit_note_payments', NULL, 1);
INSERT IGNORE INTO `imports_i18n` VALUES (18, 'Импорт на плащания към дебит ноти', NULL, 'bg');
INSERT IGNORE INTO `imports_i18n` VALUES (18, 'Import debit note payments', NULL, 'en');

######################################################################################
# 2017-09-26 - Updated settings in calculateDateOfPayment to include calculatinos for tax and fee for casco policies
#            - Added new autiomation to validate if the tax and/or fee has the correct value that matches the values from the GT2 table
#            - Update 'aon_prepare_payments_table' report settings with extra setting for casco policies
#            - Added settings for field names containing the insurer default values for stickers, green card and fond in 'aon_checking_giving_stickers' report

# Updated settings in calculateDateOfPayment to include calculatinos for tax and fee for casco policies
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ninsurance_group := insurance_group\r\ninsurance_group_go := 17\r\n\r\ntax_value := tax__value\r\ntax_value_bgn := tax__valuelocal\r\nfee_value := fee__value\r\nfee_value_bgn := fee__valuelocal\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ncasco_policy := 1'), `nums`=0 WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type` IN (4, 6) AND `settings` NOT LIKE '%casco_policy%';
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ninsurance_group := insurance_group\r\ninsurance_group_go := 17\r\n\r\ntax_value := tax__value\r\ntax_value_bgn := tax__valuelocal\r\nfee_value := fee__value\r\nfee_value_bgn := fee__valuelocal\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ncasco_policy :='), `nums`=0 WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type` IN (1, 2, 3, 5) AND `settings` NOT LIKE '%casco_policy%';

# Added new autiomation to validate if the tax and/or fee has the correct value that matches the values from the GT2 table
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Проверка на данък и такса ГФО/ОФ', 0, NULL, 1, 'contracts', NULL, 'before_action', '4', 'number_rows := number_installments\r\ncommission_type_standart := 1\r\n\r\ntax_value := tax__value\r\ntax_value_bgn := tax__valuelocal\r\nfee_value := fee__value\r\nfee_value_bgn := fee__valuelocal\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ninsurer_id := insurer_id\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ncontract_precalculated_gt2_field := service_field_correct_add_policy', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\' && $request->get(\'insurance_group\') == \'17\'', 'plugin := aon\r\nmethod := addCheckTaxGFO', NULL, 1, 0, 1),
('Проверка на данък и такса ГФО/ОФ', 0, NULL, 1, 'contracts', NULL, 'before_action', '6', 'number_rows := number_installments\r\ncommission_type_standart := 1\r\n\r\ntax_value := tax__value\r\ntax_value_bgn := tax__valuelocal\r\nfee_value := fee__value\r\nfee_value_bgn := fee__valuelocal\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ninsurer_id := insurer_id\r\ninsurer_customer_type := 4\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf\r\n\r\ncontract_precalculated_gt2_field := service_field_correct_add_policy', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\' && $request->get(\'insurance_group\') == \'17\'', 'plugin := aon\r\nmethod := addCheckTaxGFO', NULL, 1, 0, 1);

# Update 'aon_prepare_payments_table' report settings with extra setting for casco policies
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncontracts_casco_policies_types := 4,6') WHERE `type`='aon_prepare_payments_table' AND `settings` NOT LIKE '%contracts_casco_policies_types%';

# Added settings for field names containing the insurer default values for stickers, green card and fond in 'aon_checking_giving_stickers' report
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ninsurer_sticker := fee_sticker\r\ninsurer_green_card := fee_zk\r\ninsurer_fond := fee_gf') WHERE `type`='aon_checking_giving_stickers' AND `settings` NOT LIKE '%insurer_sticker%';

######################################################################################
# 2017-09-27 - Updated automation for validation of the GFO and Tax to cancel the action attempt if the validation fails

# Updated automation for validation of the GFO and Tax to cancel the action attempt if the validation fails
UPDATE `automations` SET `after_action`='cancel_action_on_fail := 1' WHERE `method` LIKE '%addCheckTaxGFO%' AND (`after_action` NOT LIKE '%cancel_action_on_fail%' OR `after_action` IS NULL);

######################################################################################
# 2017-10-23 - Updated the visible name of the 'aon_checking_giving_stickers' report
#            - Added settings containing additional fields to complete the starting tax in when creating the new contract
#            - Changed settings of the 'validateInsuranceCommissionSum' automation to contain the flag value of the standard commission

# Updated the visible name of the 'aon_checking_giving_stickers' report
UPDATE `reports_i18n` SET `name`='Проверка и даване на Стикери/ЗК' WHERE `parent_id`=371 AND `lang`='bg' AND `name` LIKE '%/ГФ%';

# Added settings containing additional fields to complete the starting tax in when creating the new contract
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ntax_value :=', '\r\n\r\nstarting_commission := start_commission\r\nstarting_commission_value := start_commission__value\r\nstarting_commission_value_bgn := start_commission__valuelocal\r\ntax_value :=') WHERE  `method` LIKE '%calculateDateOfPayment%' AND (`start_model_type`='4' OR `start_model_type`='6') AND `settings` NOT LIKE '%starting_commission :=%';

# Changed settings of the 'validateInsuranceCommissionSum' automation to contain the flag value of the standard commission
UPDATE `automations` SET `settings`=REPLACE(`settings`, 'shared_commission_shared := 2', 'standard_commission_type := 1') WHERE `method` LIKE '%validateInsuranceCommissionSum%' AND `settings` LIKE '%shared_commission_shared%';

######################################################################################
# 2017-11-07 - Add new settings for report 'aon_create_report_for_netherlands'

# Add new settings for report 'aon_create_report_for_netherlands'
UPDATE `reports`
  SET `settings` = CONCAT(`settings`, '\r\n\r\n# id на тип договори „Каско/ГО (индивидуална)“\r\ncontracts_type_casco_cl_individual := 4\r\n# id на тип договори „Каско/ГО (групова)“\r\ncontracts_type_casco_cl_group := 6')
  WHERE `type` = 'aon_create_report_for_netherlands'
    AND `settings` NOT LIKE '%contracts_type_casco_cl_individual%';

######################################################################################
# 2017-11-16 - Added new report - 'aon_payments_distribution' - for AON installation (AON)

# Added new report - 'aon_payments_distribution' - for AON (AON) installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (376, 'aon_payments_distribution', 'debit_note_payment_document := 12\r\n\r\ndebit_note_payment_date := date_bank_payment\r\ndebit_note_payment_value := import_value\r\ndebit_note_payment_currency := import_currency\r\ndebit_note_payment_not_distributed_value := import_value_notdistr\r\ndebit_note_payment_not_distributed_currency := import_currency\r\ndebit_note_payment_other_side := import_name\r\ndebit_note_payment_transition_reason := import_reason_info\r\ndebit_note_payment_additional_info := import_more_info\r\ndebit_notes_payments_customers_types := 1,3,4\r\ndebit_notes_payments_status_partially_paid := locked_30\r\ndebit_notes_payments_status_fully_paid := closed_31\r\n\r\ncustomer_unknown_id := 3612\r\n\r\ndebit_note_document := 11\r\ndebit_note_document_insurer_id := insurer_id\r\ndebit_note_document_value := debit_value\r\ndebit_note_document_paid := debit_value_paid\r\ndebit_note_status_partially_paid := opened_29\r\ndebit_note_status_fully_paid := opened_25\r\ndebit_note_status_overpaid := opened_33', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (376, 'Разпределяне на плащания', NULL, NULL, 'bg'),
  (376, 'Payments distribution', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 376, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report') AND `model_type`=376;

######################################################################################
# 2017-12-07 - Added settings to remove +/-/X from the GT2 in debit notes (11, 16)
#            - Added parameters to the buttons redirecting to aon_create_debit_note. They filter by dates

# Added settings to remove +/-/X from the GT2 in debit notes (11, 16)
UPDATE _fields_meta SET source=CONCAT(source, '\nhide_multiple_rows_buttons := 1\nhide_delete := 1') WHERE model_type IN (11,16) AND model='Document' AND source NOT LIKE '%hide_multiple_rows_buttons%' AND name='group_table_2';

# Added parameters to the buttons redirecting to aon_create_debit_note. They filter by dates
UPDATE _fields_meta SET source=REPLACE(source, 'href := reports&reports=generate_report&report_type=aon_create_debit_note&document_id=b_id&client=b_customer&debit_note_type=b_type&debit_note_value=a_debit_value&insurer[]=a_insurer_id', 'href := reports&reports=generate_report&report_type=aon_create_debit_note&document_id=b_id&client=b_customer&debit_note_type=b_type&debit_note_value=a_debit_value&insurer[]=a_insurer_id&from_date=a_debit_from_date&to_date=a_debit_to_date') WHERE model_type IN (11,16) AND model='Document' AND source NOT LIKE '%a_debit_to_date%' AND name='insert_policy';

######################################################################################
# 2017-12-12 - Added additional aon_payments_distribution report settings to point to vars which will contain the relation between the debit note payment and the policies

# Added additional aon_payments_distribution report settings to point to vars which will contain the relation between the debit note payment and the policies
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndebit_notes_payments_customers_types :=', '\r\ndebit_note_payment_policy_row_id := dn_rowid\r\ndebit_note_payment_policy_row_value := dn_value\r\ndebit_notes_payments_customers_types :=') WHERE `type` LIKE '%aon_payments_distribution%' AND `settings` NOT LIKE '%debit_note_payment_policy_row_id%';

######################################################################################
# 2017-12-22 - Update the gross premium and balance for all contracts added after 2016-01-01

# Update the gross premium and balance for all contracts added after 2016-01-01
UPDATE contracts c, gt2_details gt2
SET gt2.average_weighted_delivery_price=ROUND((gt2.subtotal + gt2.last_delivery_price + (IF (gt2.article_code IS NULL, 0, gt2.article_code)) + gt2.article_width + gt2.article_weight), 6)
WHERE c.type IN (4, 6) AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2';

UPDATE contracts c, gt2_details gt2
SET gt2.average_weighted_delivery_price=ROUND((gt2.subtotal + gt2.last_delivery_price), 6)
WHERE c.type IN (1,2,3,5) AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2';

UPDATE contracts c, gt2_details gt2, gt2_details_i18n as gt2_i18n
SET gt2_i18n.article_deliverer_name=ROUND(gt2.average_weighted_delivery_price*(-1), 6)
WHERE c.type<=6 AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2' AND gt2.id=gt2_i18n.parent_id;

######################################################################################
# 2018-01-03 - Fixed negative zero values for average_weighted_delivery_price for balance in contracts

# Fixed negative zero values for average_weighted_delivery_price for balance in contracts
UPDATE contracts c, gt2_details gt2, gt2_details_i18n as gt2_i18n
SET gt2_i18n.article_deliverer_name=(IF (ROUND(gt2.average_weighted_delivery_price*(-1), 6)='-0', 0, ROUND(gt2.average_weighted_delivery_price*(-1), 6)))
WHERE c.type<=6 AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2' AND gt2.id=gt2_i18n.parent_id;

######################################################################################
# 2018-01-09 - Added new automation to handle the debit notes and the policies when debit note payment is annulled

# Added new automation to handle the debit notes and the policies when debit note payment is annulled
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Анулиране на плащания към дебит ноти', 0, NULL, 1, 'documents', NULL, 'action', '12', 'debit_note_payment_document := 12\r\ndebit_note_payment_policy_row_id := dn_rowid\r\ndebit_note_payment_policy_row_value := dn_value\r\n\r\ndebit_note_document := 11\r\ndebit_note_document_paid := debit_value_paid\r\ndebit_note_document_value := debit_value\r\ndebit_note_status_unpaid := opened\r\ndebit_note_status_partially_paid := locked_29\r\ndebit_note_status_fully_paid := locked_25\r\ndebit_note_status_overpaid := locked_33', 'condition := \'[action]\' == \'setstatus\'\r\ncondition := \'[prev_b_substatus]\' != \'32\'\r\ncondition := \'[b_substatus]\' == \'32\'', 'plugin := aon\r\nmethod := annulDebitNotePayment', NULL, 1, 1, 1);

######################################################################################
# 2018-01-12 - Fixed automation to create tasks from contracts and change reminder dates
#            - Update the first reminer date for standart and health insurance policy
#            - Update the second reminer date for standart and health insurance policy

-- PRE-DEPLOYED # Fixed automation to create tasks from contracts and change reminder dates
-- UPDATE `automations` SET `conditions`='condition := in_array(\'[action]\', array(\'add\', \'edittopic\', \'edit\')) && ((\'[prev_b_date_validity]\' >= date(\'Y-m-d\') || \'[b_date_validity]\' >= date(\'Y-m-d\')) && \'[prev_b_date_start]\' <= date(\'Y-m-d\') || \'[b_date_start]\' <= date(\'Y-m-d\')) && (\'[prev_a_reminder__days]\'!=\'[a_reminder__days]\' || \'[prev_b_date_start]\'!=\'[b_date_start]\' || \'[prev_b_date_validity]\'!=\'[b_date_validity]\')', `method`='method := setAdditionalVar\r\nvar_name := reminderdate;remindersecdate\r\nvar_value := php(Calculator::calc_sql($registry, \'SELECT DATE_ADD("[b_date_start]", INTERVAL ("[a_reminder__days]"*(CEIL((DATEDIFF(CURDATE(),"[b_date_start]"))/"[a_reminder__days]"))) DAY)\'));php(Calculator::calc_sql($registry, \'SELECT DATE_ADD("[b_date_start]", INTERVAL (("[a_reminder__days]"*(CEIL((DATEDIFF(CURDATE(),"[b_date_start]"))/"[a_reminder__days]")))) DAY)\'))' WHERE `id` IN (69,66) AND `conditions` NOT LIKE '%\'edit\'%';
-- UPDATE `automations` SET `conditions`='where := a__reminderdate >= CURDATE()\r\nwhere := a__reminderdate <= DATE_ADD(CURDATE(), INTERVAL 10 DAY)\r\nwhere := co.subtype=\'contract\'\r\nwhere := DATE_FORMAT(co.date_validity, \'%Y-%m-%d\') >= CURDATE()\r\nwhere := co.id NOT IN (SELECT c.id FROM contracts c JOIN tasks t ON c.custom_num=t.equipment AND t.type = 2 AND t.active = 1 AND t.deleted_by = 0 WHERE c.active=1 AND c.deleted_by=0 AND c.subtype=\'contract\')' WHERE `id` IN (70,67) AND `conditions` NOT LIKE '%a__reminderdate <= DATE_ADD(CURDATE()%';
-- UPDATE `automations` SET `conditions`='where := a__remindersecdate >= CURDATE()\r\nwhere := a__remindersecdate <= DATE_ADD(CURDATE(), INTERVAL 10 DAY)\r\nwhere := co.subtype=\'contract\'\r\nwhere := DATE_FORMAT(co.date_validity, \'%Y-%m-%d\') >= CURDATE()\r\nwhere := co.id NOT IN (SELECT c.id FROM contracts c JOIN contracts_cstm as cctm ON cctm.model_id=c.id AND cctm.var_id=362 JOIN tasks t ON c.custom_num=t.equipment AND t.type = 2 AND t.active = 1 AND t.deleted_by = 0 AND DATE_FORMAT(t.planned_finish_date, \'%Y-%m-%d\')=DATE_FORMAT(cctm.value, \'%Y-%m-%d\') WHERE c.active=1 AND c.deleted_by=0 AND c.subtype=\'contract\')' WHERE `id`=73 AND `conditions` NOT LIKE '%a__remindersecdate <= DATE_ADD(CURDATE()%';
-- UPDATE `automations` SET `conditions`='where := a__remindersecdate >= CURDATE()\r\nwhere := a__remindersecdate <= DATE_ADD(CURDATE(), INTERVAL 10 DAY)\r\nwhere := co.subtype=\'contract\'\r\nwhere := DATE_FORMAT(co.date_validity, \'%Y-%m-%d\') >= CURDATE()\r\nwhere := co.id NOT IN (SELECT c.id FROM contracts c JOIN contracts_cstm as cctm ON cctm.model_id=c.id AND cctm.var_id=576 JOIN tasks t ON c.custom_num=t.equipment AND t.type = 2 AND t.active = 1 AND t.deleted_by = 0 AND DATE_FORMAT(t.planned_finish_date, \'%Y-%m-%d\')=DATE_FORMAT(cctm.value, \'%Y-%m-%d\') WHERE c.active=1 AND c.deleted_by=0 AND c.subtype=\'contract\')' WHERE `id`=75 AND `conditions` NOT LIKE '%a__remindersecdate <= DATE_ADD(CURDATE()%';

-- # Update the first reminer date for standart and health insurance policy
-- UPDATE contracts c, contracts_cstm cc1, contracts_cstm cc2
-- SET cc1.value=DATE_ADD(c.date_start, INTERVAL cc1.value DAY)
-- WHERE c.id=cc1.model_id AND cc1.var_id=358 AND cc1.value!='' AND c.id=cc2.model_id AND cc2.var_id=360 AND cc2.value!='' AND c.subtype='contract' AND c.type IN (3) AND c.date_validity>=CURDATE() AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND DATE_ADD(c.date_start, INTERVAL cc1.value DAY)!=cc2.value;

-- UPDATE contracts c, contracts_cstm cc1, contracts_cstm cc2
-- SET cc1.value=DATE_ADD(c.date_start, INTERVAL cc1.value DAY)
-- WHERE c.id=cc1.model_id AND cc1.var_id=572 AND cc1.value!='' AND c.id=cc2.model_id AND cc2.var_id=574 AND cc2.value!='' AND c.subtype='contract' AND c.type IN (5) AND c.date_validity>=CURDATE() AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND DATE_ADD(c.date_start, INTERVAL cc1.value DAY)!=cc2.value;

-- # Update the second reminer date for standart and health insurance policy
-- UPDATE contracts c, contracts_cstm cc1, contracts_cstm cc3
-- SET cc3.value=(IF (DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)>c.date_validity, c.date_validity, DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)))
-- WHERE c.type IN (3) AND c.date_validity>=CURDATE() AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND c.subtype='contract' AND c.id=cc1.model_id AND cc1.var_id=358 AND cc1.value!='' AND c.id=cc3.model_id AND cc3.var_id=362 AND cc3.value!='' AND cc3.value!=(IF (DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)>c.date_validity, c.date_validity, DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)));

-- UPDATE contracts c, contracts_cstm cc1, contracts_cstm cc3
-- SET cc3.value=(IF (DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)>c.date_validity, c.date_validity, DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)))
-- WHERE c.type IN (5) AND c.date_validity>=CURDATE() AND c.active=1 AND c.deleted_by=0 AND c.annulled_by=0 AND c.subtype='contract' AND c.id=cc1.model_id AND cc1.var_id=572 AND cc1.value!='' AND c.id=cc3.model_id AND cc3.var_id=576 AND cc3.value!='' AND cc3.value!=(IF (DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)>c.date_validity, c.date_validity, DATE_ADD(c.date_start, INTERVAL (cc1.value*(CEIL((DATEDIFF(CURDATE(),c.date_start))/cc1.value))) DAY)));

######################################################################################
# 2018-01-16 - Added tags to the import of payments to debit notes

UPDATE `imports` SET `settings`='tag_incoming_payment := 14\r\ntag_outgoing_payment := 15\r\n' WHERE  `type`='aon_import_debit_note_payments' AND (settings NOT LIKE '%tag_incoming_payment%' OR settings IS NULL);

######################################################################################
# 2018-01-22 - Update the javascript functions which will update the bruto premium fields in the AON contracts
#            - Update Balance of the debit notes with the blaance from the related policies

# Update the javascript functions which will update the bruto premium fields in the AON contracts
UPDATE `_fields_meta` SET `source`='text_align := right\r\npermissions_edit := 1\r\npermissions_view := 1\r\njs_method := onkeyup => gt2calc(this);crow = this.id.replace(/.*_(\\d+)/,\'$1\');gf = $(\'article_code_\' + crow).value ? parseFloat($(\'article_code_\'+crow).value) : 0;stick = $(\'article_width_\' + crow).value?parseFloat($(\'article_width_\' +crow).value) : 0;gc = $(\'article_weight_\'+crow).value ? parseFloat($(\'article_weight_\' + crow).value) : 0;zp=$(\'subtotal_\' + crow).value ? parseFloat($(\'subtotal_\' + crow).value) : 0;fee= $(\'last_delivery_price_\' + crow).value ?parseFloat($(\'last_delivery_price_\' + crow).value) : 0;total_premium = gf +stick + gc + zp + fee;balance = total_premium*(-1);$(\'average_weighted_delivery_price_\' + crow).value = (Math.round(total_premium * 100) / 100).toFixed(2);$(\'article_deliverer_name_\' + crow).value = (Math.round(balance * 100) / 100).toFixed(2); gt2calc(this)' WHERE `name` IN ('price', 'quantity', 'last_delivery_price', 'article_code', 'article_width', 'article_weight') AND `model`='Contract' AND `model_type` IN (4, 6);
UPDATE `_fields_meta` SET `source`='text_align := right\r\npermissions_edit := 1\r\npermissions_view := 1\r\njs_method := onkeyup => gt2calc(this); crow = this.id.replace(/.*_(\\d+)/,\'$1\'); zp = $(\'subtotal_\' + crow).value ? parseFloat($(\'subtotal_\' +crow).value) : 0; fee = $(\'last_delivery_price_\' + crow).value ?parseFloat($(\'last_delivery_price_\' + crow).value) : 0; total_premium = zp +fee; total_balance = (zp + fee) * (-1); $(\'average_weighted_delivery_price_\' +crow).value = (Math.round(total_premium * 100) / 100).toFixed(2); $(\'article_deliverer_name_\' +crow).value = (Math.round(total_balance * 100) / 100).toFixed(2); gt2calc(this)' WHERE `name` IN ('price', 'quantity', 'last_delivery_price') AND `model`='Contract' AND `model_type` IN (1, 2, 3, 5);

# Update Balance of the debit notes with the blaance from the related policies
#UPDATE contracts c, gt2_details gt2, gt2_details_i18n as gt2_i18n, gt2_details gt2_dn
#SET gt2_dn.free_field4=gt2_i18n.article_deliverer_name
#WHERE c.type<=6 AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.free_field2!="" AND gt2.free_field2!="0" AND gt2.model="Contract" AND gt2.free_field4 != '2' AND gt2.id=gt2_i18n.parent_id AND gt2_i18n.lang="bg" AND gt2_dn.free_field2=gt2.id AND (gt2_dn.free_field4 IS NULL OR gt2_dn.free_field4="" OR gt2_dn.free_field4="0");

######################################################################################
# 2018-01-24 - Update the missing bruto premium values for some of the related rows

# Update the missing bruto premium values for ome of the related rows
#UPDATE contracts c, gt2_details gt2, gt2_details_i18n as gt2_i18n, gt2_details gt2_dn, documents d
#SET gt2_dn.free_field4=gt2_i18n.article_deliverer_name
#WHERE c.type<=6 AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2' AND gt2.id=gt2_i18n.parent_id AND gt2_i18n.lang="bg" AND gt2_i18n.free_text5!="" AND gt2_i18n.free_text5!="0" AND gt2_dn.free_field2=gt2.id AND (gt2_dn.free_field4 IS NULL OR gt2_dn.free_field4="" OR gt2_dn.free_field4="0") AND gt2_dn.model_id=d.id AND d.type=11;

######################################################################################
# 2018-01-25 - Added addition settings for 'aon_upcoming_deadlines' report

# Added addition settings for 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nindividual_insurance_car_id :=', '\r\ncontract_group_insurance_type := 6\r\nindividual_insurance_car_id :=') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%contract_group_insurance_type%';
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ncontract_tag_cancelled := 12') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%contract_tag_cancelled%';

######################################################################################
# 2018-01-26 - Added addition settings for 'aon_bonuses_to_invoice' report
#            - Added addition settings for casco policies in 'aon_advanced_incomes_by_clients' report

# Added addition settings for 'aon_bonuses_to_invoice' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_insurer_type := ', '\r\nincluded_casco_policies_types := 4,6\r\n\r\ncontract_number_installments := number_installments\r\ncustomer_insurer_type := ') WHERE `type`='aon_bonuses_to_invoice' AND `settings` NOT LIKE '%contract_number_installments%';

# Added addition settings for casco policies in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nincluded_categories :=', '\r\nincluded_casco_policies_types := 4,6\r\nincluded_categories :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%included_casco_policies_types%';

######################################################################################
# 2018-01-29 - Rename 'aon_leasing_companies' report and additional settings to it

# Rename 'aon_leasing_companies' report and additional settings to it
UPDATE `reports_i18n` SET `name`='Справка по Лизингова компания / Клиент' WHERE `parent_id`=330 AND `lang`='bg';
UPDATE `reports_i18n` SET `name`='Report by leasing companies / clients' WHERE `parent_id`=330 AND `lang`='en';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncontract_insurer_id :=', '\r\nincluded_casco_policies_types := 4,6\r\ncustomer_clients_types := 1,3\r\n\r\ncontract_insurer_id :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%customer_clients_types%';

######################################################################################
# 2018-02-01 - Added additional settings to annulDebitNotePayment automation

# Added additional settings to annulDebitNotePayment automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\n\r\ndebit_note_payment_status_annuled := 32\r\ndebit_note_payment_date := date_bank_payment') WHERE `method` LIKE('%annulDebitNotePayment%') AND `settings` NOT LIKE '%debit_note_payment_status_annuled%';

######################################################################################
# 2018-02-22 - Fully updated settings for the 'aon_report_to_insurer' report
#            - Change the visible name of the 'aon_leasing_companies' report

# Fully updated settings for the 'aon_report_to_insurer' report
UPDATE `reports` SET `settings`='document_debit_note_type := 11\r\ndocument_debit_note_var_value := debit_value\r\ndocument_debit_note_var_paid_value := debit_value_paid\r\ndocument_debit_note_var_order_date := insurer_order_date\r\ndocument_debit_note_var_order_num := insurer_order_name\r\ndocument_debit_note_var_order_id := insurer_order_id\r\n\r\ndocument_debit_note_payment_type := 12\r\ndocument_debit_note_payment_date := date_bank_payment\r\ndocument_debit_note_payment_var_related_gt2 := dn_rowid\r\ndocument_debit_note_payment_var_related_sum := dn_value\r\ndocument_debit_note_payment_value := import_value\r\ndocument_debit_note_payment_not_distributed_value := import_value_notdistr\r\ndocument_debit_note_payment_tag_issued_report := 16\r\n\r\ndocument_report_type := 13\r\ndocument_ignore_statuses := locked_33' WHERE `type`='aon_report_to_insurer';

# Change the visible name of the 'aon_leasing_companies' report
UPDATE `reports_i18n` SET `name` = 'Справка по клиент' WHERE `parent_id` = 330 AND `lang` = 'bg';
UPDATE `reports_i18n` SET `name` = 'Report by client' WHERE `parent_id` = 330 AND `lang` = 'en';

######################################################################################
# 2018-03-02 - Added additional settings to validateDebitNotePaymentPaidAmount automation for tag 'reported payment'
#            - Added setting for tag 'reported payment' for debit notes payments documents

# Added additional settings to annulDebitNotePayment automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\ntag_reported_payment := 16') WHERE `method` LIKE('%validateDebitNotePaymentPaidAmount%') AND `settings` NOT LIKE '%tag_reported_payment%';

# Added setting for tag 'reported payment' for debit notes payments documents
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_unknown_id :=', '\r\ndebit_notes_payments_tag_reported_payment := 16\r\n\r\ncustomer_unknown_id :=') WHERE `type` LIKE '%aon_payments_distribution%' AND `settings` NOT LIKE '%debit_notes_payments_tag_reported_payment%';

######################################################################################
# 2018-03-06 - The automation 'validateDebitNotePaymentPaidAmount' is changed to 'validateDebitNotePaymentAnnulment' and its settings are changed

# The automation 'validateDebitNotePaymentPaidAmount' is changed to 'validateDebitNotePaymentAnnulment' and its settings are changed
UPDATE `automations` SET `method`=REPLACE(`method`, 'validateDebitNotePaymentPaidAmount', 'validateDebitNotePaymentAnnulment'), `conditions`='condition := \'[request_is_post]\' && \'[action]\'==\'setstatus\'', `settings`='check_status := closed_32\r\ntag_reported_payment := 16', `name`='Валидация за анулиране на плащания по дебит нота', `active`=1 WHERE `method` LIKE('%validateDebitNotePaymentPaidAmount%');

######################################################################################
# 2018-03-07 - Added plugin to print Document 13 in XLSX format
#            - Removed all unnecessary patterns and renamed the existing ones

# Added plugin to print Document 13 in XLSX format
INSERT IGNORE INTO `patterns_plugins` (`id`, `model`, `model_type`, `folder`, `method`, `settings`, `image`, `added`, `modified`) VALUES
  (84, 'Document', 13, 'aon', 'prepareReport', '', '', NOW(), NOW());
INSERT IGNORE INTO `patterns_plugins_i18n` (`parent_id`, `name`, `description`, `lang`, `translated`) VALUES
  (84, 'Подготовка за печат на отчет', '', 'bg', NOW()),
  (84, 'Print report', '', 'en', NOW());

# Removed all unnecessary patterns and renamed the existing ones
UPDATE patterns SET active=0, deleted= NOW(), deleted_by=1 WHERE id IN (26, 27, 28, 29, 30);
UPDATE patterns_i18n SET name='Не се използва' WHERE parent_id IN (26, 27, 28, 29, 30);
UPDATE patterns_i18n SET name='Каско' WHERE parent_id=23;
UPDATE patterns_i18n SET name='ГО' WHERE parent_id=24;
UPDATE patterns_i18n SET name='Каско и ГО' WHERE parent_id=25;
UPDATE patterns SET plugin='84' WHERE id IN (23, 24, 25);

######################################################################################
# 2018-03-28 - Replaced full_num with id of debit note in the policies GT2 (contracts GT2), so that free_text5 could be displayed as dropdown

# Replaced full_num with id of debit note in the policies GT2 (contracts GT2), so that free_text5 could be displayed as dropdown
UPDATE gt2_details gt2_policy
JOIN gt2_details_i18n gt2_policy_i18n
  ON gt2_policy.id=gt2_policy_i18n.parent_id AND gt2_policy.model='contract'
JOIN gt2_details gt2_debit_note
  ON gt2_debit_note.free_field2=gt2_policy.id AND gt2_debit_note.model='Document' AND gt2_debit_note.article_id=gt2_policy.model_id
JOIN documents debit_note
  ON gt2_debit_note.model_id=debit_note.id AND debit_note.type IN (11,16) AND gt2_policy_i18n.free_text5=debit_note.full_num
SET gt2_policy_i18n.free_text5=debit_note.id;

######################################################################################
# 2018-04-02 - Fix the numbers of invoices and reports in contracts to point to ids
#            - Removed settings which will no longer be used from checkDuplicateScheduleVisits automation

# Fix the numbers of invoices and reports to be ids
UPDATE contracts c, gt2_details as gt2_con, gt2_details_i18n as gt2_con_i18n, documents d, gt2_details as gt2_doc
SET gt2_con_i18n.free_text2=d.id
WHERE c.`type`<7 AND c.subtype='contract' AND c.deleted_by=0 AND c.id=gt2_con.model_id AND gt2_con.model='Contract' AND gt2_con_i18n.parent_id=gt2_con.id AND gt2_con_i18n.free_text2!="" AND d.`type`=8 AND gt2_con_i18n.free_text2=d.custom_num AND d.id=gt2_doc.model_id AND gt2_doc.model='Document' AND gt2_doc.article_id=c.id;

UPDATE contracts c, gt2_details as gt2_con, documents d
SET gt2_con.article_trademark=d.id
WHERE c.`type` IN (4,6) AND c.subtype='contract' AND c.deleted_by=0 AND c.id=gt2_con.model_id AND gt2_con.model='Contract' AND gt2_con.article_trademark!="" AND d.type=13 AND gt2_con.article_trademark=d.custom_num AND gt2_con.article_alternative_deliverer=d.customer;

# Removed settings which will no longer be used from checkDuplicateScheduleVisits automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\npolicy_from := policy_from\r\npolicy_to := policy_to\r\n', '\r\n') WHERE `method` LIKE '%checkDuplicateScheduleVisits%' AND `settings` LIKE '%policy_from := policy_from%';

######################################################################################
# 2018-04-12 - Added automation executed upon add/edit of report document (type 13)

# PRE-DEPLOYED # Added automation executed upon add/edit of report document (type 13)
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
# ('Ръчно добавяне/редакция на отчет към застраховател', 0, NULL, 1, 'documents', NULL, 'action', '13', 'debit_note_document_type := 11', 'condition := 1', 'plugin := aon\r\nmethod := updateAONReport', NULL, 0, 1, 1);

######################################################################################
# 2018-04-16 - Added automation executed upon lock of report document (type 13)
#            - Remove count restrictions of automation that executes upon add/edit of report document (type 13)
#            - Updated settings of the 'aon_correct_policy' dashlet plugin so it can work with stickers and with taxes and fees

# PRE-DEPLOYED # Added automation executed upon add/edit of report document (type 13)
# INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
#  ('Обновяване на полици с номер и дата на отчет след потвърждаването му', 0, NULL, 1, 'documents', NULL, 'before_action', '13', '', 'condition := \'[request_is_post]\' == \'1\' && $this->registry->get(\'request\')->get(\'substatus\') == \'locked_33\' && \'[action]\' == \'setstatus\'', 'plugin := aon\r\nmethod := updateAONPoliciesFromReport', 'cancel_action_on_fail := 1', 0, 0, 1);

# PRE-DEPLOYED # Remove count restrictions of automation that executes upon add/edit of report document (type 13)
# UPDATE automations SET nums=0 WHERE method like '%updateAONReport%';

# Updated settings of the 'aon_correct_policy' dashlet plugin so it can work with stickers and with taxes and fees
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\nshow_gt2_fields :=', '\r\ncasco_policies := 4,6\r\n\r\nshow_gt2_fields :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%casco_policies :=%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\ntype_commission_field :=', '\r\nconfig_table_tax := tax\r\nconfig_table_tax_value := tax__value\r\nconfig_table_tax_value_curr := tax__valuelocal\r\nconfig_table_fee_value := fee__value\r\nconfig_table_fee_value_curr := fee__valuelocal\r\n\r\ntype_commission_field :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%config_table_tax :=%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'show_gt2_fields := article_alternative_deliverer_name,article_height,free_text1,quantity,price,free_field1,subtotal,discount_percentage,discount_value,subtotal_discount_value', 'show_gt2_fields := article_alternative_deliverer_name,article_height,free_text1,quantity,price,free_field1,subtotal,last_delivery_price,discount_percentage,discount_value,subtotal_discount_value,article_code,article_width,article_weight,average_weighted_delivery_price') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%article_weight%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\ngrouping_shared_table_name :=', '\r\nsticker_value := 3\r\n\r\ngrouping_shared_table_name :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%sticker_value :=%';

######################################################################################
# 2018-04-17 - Updated setttings for the visile fields in 'aon_correct_policy' dashlet plugin

# Updated setttings for the visile fields in 'aon_correct_policy' dashlet plugin
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, 'show_gt2_fields :=  article_alternative_deliverer_name,article_height,free_text1,quantity,price,free_field1,subtotal,discount_percentage,discount_value,subtotal_discount_value', 'show_gt2_fields := article_alternative_deliverer_name,article_height,free_text1,quantity,price,free_field1,subtotal,last_delivery_price,discount_percentage,discount_value,subtotal_discount_value,article_code,article_width,article_weight,average_weighted_delivery_price') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%last_delivery_price%';

######################################################################################
# 2018-04-24 - Added value for variable tax ROUND((tax__valuelocal/premium__valuelocal)*100, 2) for contract types 1, 2, 3 and 5

# Added value for variable tax ROUND((tax__valuelocal/premium__valuelocal)*100, 2) for contract types 1, 2, 3 and 5
INSERT INTO contracts_cstm
  SELECT co.id as model_id,
         CASE
         WHEN p.var_id=118 THEN 119
         WHEN p.var_id=216 THEN 220
         WHEN p.var_id=321 THEN 322
         WHEN p.var_id=515 THEN 516
         END as var_id,
         1 as num,
         IF (p.value=0, '0.00', ROUND((tv.value/p.value)*100, 2)) as value,
         NULL as formula,
         NULL as `index`,
         NULL as `index_date`,
         NULL as `index_formula`,
         NOW() as added, 1 as added_by,
         NOW() as modified, 1 as modified_by,
         '' as lang
  FROM contracts co
    JOIN contracts_cstm p
      ON co.type IN (1,2,3,5) AND co.deleted=0 AND co.active=1 AND co.id=p.model_id AND p.value!='' AND p.var_id IN (118, 216, 321, 515) # premium__valuelocal
    JOIN contracts_cstm tv
      ON co.id=tv.model_id AND tv.value!='' AND tv.var_id IN (113, 212, 313, 510) # tax__valuelocal
    LEFT JOIN contracts_cstm t
      ON co.id=t.model_id AND t.var_id IN (119, 220, 322, 516) # tax__valuelocal
  WHERE t.value IS NULL OR t.value!=IF (p.value=0, '0.00', ROUND((tv.value/p.value)*100, 2))
ON DUPLICATE KEY UPDATE
  `value` = VALUES(`value`);

######################################################################################
# 2018-04-25 - Define the casco fields visible and editable

# Define the casco fields visible and editable
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncheck_active_row', '\r\nspecial_casco_fields := average_weighted_delivery_price, last_delivery_price, article_code, article_width, article_weight\r\nspecial_casco_fields_editable := last_delivery_price, article_code, article_width, article_weight\r\ncheck_active_row') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%special_casco_fields_editable%';

######################################################################################
# 2018-05-15 - Added bullet to fix the tax for the old contract, based on the related debit notes

# Added bullet to fix the tax for the old contract, based on the related debit notes
INSERT INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
('aonFixContractTaxes', 'Update the tax (of the old contracts), based on the debit notes.', 14454, 0, 1, NOW(), NOW());

######################################################################################
# 2018-06-08 - Added new settings for tag for system approved debit notes and acceptable balance difference in 'aon_report_to_insurer' report for the AON installation

# Added new settings for tag for system approved debit notes and acceptable balance difference in 'aon_report_to_insurer' report for the AON installation
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndocument_debit_note_payment_type :=', '\r\ndocument_debit_note_tag_system_approved := 17\r\ndocument_debit_note_acceptable_balance_difference := 50\r\n\r\ndocument_debit_note_payment_type :=') WHERE `type`='aon_report_to_insurer' AND `settings` NOT LIKE '%document_debit_note_acceptable_balance_difference%';

######################################################################################
# 2018-06-15 - Added new report - 'aon_create_timesheet' - for the AON installation

# Added new report - 'aon_create_timesheet' - for the AON installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (384, 'aon_create_timesheet', 'customer_type_insurer := 4\r\ncustomer_type_client := 3\r\nexclude_contract_type := 7\r\n\r\ncasco_policies_individual := 4\r\ncasco_policies_group := 6\r\n\r\ndebit_note_substatus_paid := 25\r\ndebit_note_substatus_partial := 29\r\n\r\npayment_debit_note_type_id := 12\r\npayment_debit_note_row := dn_rowid\r\npayment_debit_note_value := dn_value\r\n\r\ndocument_timesheet_type_id := 13\r\n\r\ncontracts_insurer_id := insurer_id\r\ncontracts_alternatve_insurer_id := id_insurer\r\ncontracts_insurance_type_prefix := insurance_type_con\r\ncontracts_number_payments := number_installments\r\ncontract_reinsurer_policy := 2\r\n\r\nskip_session_filters := 1\r\n\r\ncustomer_insurer_name_in_bgn := name_in_bg', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (384, 'Създаване на отчет', NULL, NULL, 'bg'),
  (384, 'Create timesheet', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 384, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report') AND `model_type`=384;

######################################################################################
# 2018-06-18 - Added new settings for accpetable difference between paid value and value of debit note in 'aon_create_timesheet' for the AON installation

# Added new settings for accpetable difference between paid value and value of debit note in 'aon_create_timesheet' for the AON installation
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndebit_note_substatus_paid :=', '\r\n\r\ndebit_note_type_id := 11\r\ndebit_note_value := debit_value\r\ndebit_note_paid_value := debit_value_paid\r\ndebit_note_tag_system_approved := 17\r\ndebit_note_acceptable_balance_difference := 50\r\ndebit_note_substatus_paid :=') WHERE `type`='aon_create_timesheet' AND `settings` NOT LIKE '%debit_note_acceptable_balance_difference%';

######################################################################################
# 2018-07-13 - Added new settings paid to in debit notes in 'aon_create_timesheet' for the AON installation
#            - Updated the settings of the button which leads to 'aon_create_timesheet' report

# Added new settings paid to in debit notes in 'aon_create_timesheet' for the AON installation
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\npayment_debit_note_type_id :=', '\r\ndebit_note_paid_to := debit_payment_to\r\ndebit_note_paid_to_insurer := 2\r\n\r\npayment_debit_note_type_id :=') WHERE `type`='aon_create_timesheet' AND `settings` NOT LIKE '%debit_note_paid_to_insurer%';

# Updated the settings of the button which leads to 'aon_create_timesheet' report
UPDATE `_fields_meta` SET `source`=REPLACE(`source`, '\r\ntarget := _self', '\r\nfrom_date := php(strftime(\'%Y-%m-%d\', strtotime(\'-3 months\')))\r\nto_date := php(date(\'Y-m-d\'))\r\ntarget := _self') WHERE `model`='Document' AND `model_type`='13' AND `type`='button' AND `source` LIKE '%aon_create_timesheet%' AND `name`='insert_policy' AND `source` NOT LIKE '%from_date%';

######################################################################################
# 2018-10-11 - Update Balance of the debit notes with the balance from the related policies

# Update Balance of the debit notes with the balance from the related policies
#UPDATE contracts c, gt2_details gt2, gt2_details_i18n as gt2_i18n, gt2_details gt2_dn, documents d
#SET gt2_dn.free_field4=gt2_i18n.article_deliverer_name
#WHERE c.type<=6 AND c.active=1 AND c.deleted_by=0 AND DATE_FORMAT(c.added, "%Y-%m-%d")>='2016-01-01' AND c.id=gt2.model_id AND gt2.model="Contract" AND gt2.free_field4 != '2' AND gt2.id=gt2_i18n.parent_id AND gt2_i18n.lang="bg" AND gt2_i18n.free_text5!="" AND gt2_i18n.free_text5!="0" AND gt2_dn.free_field2=gt2.id AND gt2_dn.model_id=d.id AND d.type="11" AND d.substatus!="26" AND gt2_dn.free_field4!=gt2_i18n.article_deliverer_name;

######################################################################################
# 2018-11-29 - Added addition settings for 'aon_leasing_companies' report

# Added addition settings for 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ncontract_insurance_type_prefix :=', '\r\ncontract_insurance_value_var := insurance__valuelocal\r\ncontract_insurance_type_prefix :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%contract_insurance_value_var%';

######################################################################################
# 2019-01-04 - Added additional settings for sorting results in 'aon_create_debit_note' report

# Added additional settings for sorting results in 'aon_create_debit_note' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nskip_session_filters :=', '\r\n\r\nnomenclatures_sort := 18,61\r\n\r\nskip_session_filters :=') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%nomenclatures_sort%';

######################################################################################
# 2019-01-07 - Added additional setting to mark if the inactive emplyees will be shown in the account manager filter in 'aon_upcoming_deadlines' report
#            - Added additional setting to mark if the inactive emplyees will be shown in the account manager filter in 'aon_upcoming_renewals' report

# Added additional setting to mark if the inactive emplyees will be shown in the account manager filter in 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=CONCAT('include_inactive_employees := 1\r\n', `settings`) WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%include_inactive_employees%';

# Added additional setting to mark if the inactive emplyees will be shown in the account manager filter in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=CONCAT('include_inactive_employees := 1\r\n', `settings`) WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%include_inactive_employees%';

######################################################################################
# 2019-01-08 - Added additional setting to mark if the inactive employees will be shown in the account manager filter in 'aon_invoiced_uninvoiced_fees' report

# Added additional setting to mark if the inactive emplyees will be shown in the account manager filter in 'aon_invoiced_uninvoiced_fees' report
UPDATE `reports` SET `settings`=CONCAT('include_inactive_employees := 1\r\n', `settings`) WHERE `type`='aon_invoiced_uninvoiced_fees' AND `settings` NOT LIKE '%include_inactive_employees%';

######################################################################################
# 2019-02-05 - Added bullet to set the tax for all the existing Casco contracts to 2%

# Added bullet to set the tax for all the existing Casco contracts to 2%
INSERT INTO `bullets` (`bullet`, `description`, `revision`, `position`, `active`, `modified`, `fired`) VALUES
('aonAdd2PercentTax', 'Set the tax of all Casco policies to 2%', 15069, 0, 1, NOW(), '0000-00-00 00:00:00');

######################################################################################
# 2019-02-06 - Mark the old bullet for updating taxes (aonFixContractTaxes) as fired and inactive

# Mark the old bullet for updating taxes (aonFixContractTaxes) as fired and inactive
UPDATE `bullets` SET `fired`=NOW(), `active`=0 WHERE `bullet`='aonFixContractTaxes';

######################################################################################
# 2019-02-13 - Mark the old bullet for updating taxes (aonFixContractTaxes) as fired and inactive

# Add settings for var name of the insurer default fee for green card and sticker
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\ninsurer_sticker_reissue_fee := reissue_sticker_fee\r\ninsurer_green_card_reissue_fee := reissue_zk_fee') WHERE `type`='aon_checking_giving_stickers' AND `settings` NOT LIKE '%insurer_sticker_reissue_fee%';

######################################################################################
# 2019-03-14 - Updated formulas for calculation in GT2 tables in contracts (types 1-6)

# Updated formulas for calculations in GT2 tables in contracts (types 1-6)
UPDATE _fields_meta SET `source`=CONCAT(`source`, '\njavascript := function calcContractSums(el) { crow = el.id.replace(/.*_(\\d+)/,\'$1\');gt2calc($(\'last_delivery_price_\' + crow));gf = $(\'article_code_\' + crow).value ? parseFloat($(\'article_code_\'+crow).value) : 0;stick = $(\'article_width_\' + crow).value?parseFloat($(\'article_width_\' +crow).value) : 0;gc = $(\'article_weight_\'+crow).value ? parseFloat($(\'article_weight_\' + crow).value) : 0;zp=$(\'subtotal_\' + crow).value ? parseFloat($(\'subtotal_\' + crow).value) : 0;fee= $(\'last_delivery_price_\' + crow).value ?parseFloat($(\'last_delivery_price_\' + crow).value) : 0;total_premium = gf +stick + gc + zp + fee;balance = total_premium*(-1);$(\'average_weighted_delivery_price_\' + crow).value = (Math.round(total_premium * 100) / 100).toFixed(2);$(\'article_deliverer_name_\' + crow).value = (Math.round(balance * 100) / 100).toFixed(2); gt2calc($(\'last_delivery_price_\' + crow)); }')  WHERE `name`='group_table_2' AND `model`='Contract' AND `model_type` IN (4,6) AND `source` NOT LIKE '%calcContractSums%';
UPDATE _fields_meta SET `source`='js_method := onkeyup => calcContractSums(this);' WHERE `name` IN ('quantity', 'price', 'last_delivery_price', 'article_code', 'article_width', 'article_weight') AND `model`='Contract' AND `model_type` IN (4,6) AND `source` NOT LIKE '%calcContractSums%';
UPDATE _fields_meta SET `source`=CONCAT(`source`, '\njavascript := function calcContractSums(el) { crow = el.id.replace(/.*_(\\d+)/,\'$1\'); gt2calc($(\'last_delivery_price_\' + crow)); zp = $(\'subtotal_\' + crow).value ? parseFloat($(\'subtotal_\' +crow).value) : 0; fee = $(\'last_delivery_price_\' + crow).value ?parseFloat($(\'last_delivery_price_\' + crow).value) : 0; total_premium = zp +fee; total_balance = (zp + fee) * (-1); $(\'average_weighted_delivery_price_\' +crow).value = (Math.round(total_premium * 100) / 100).toFixed(2); $(\'article_deliverer_name_\' +crow).value = (Math.round(total_balance * 100) / 100).toFixed(2); gt2calc($(\'last_delivery_price_\' + crow)); }')  WHERE `name`='group_table_2' AND `model`='Contract' AND `model_type` IN (1,2,3,5) AND `source` NOT LIKE '%calcContractSums%';
UPDATE _fields_meta SET `source`='js_method := onkeyup => calcContractSums(this);' WHERE `name` IN ('quantity', 'price', 'last_delivery_price') AND `model`='Contract' AND `model_type` IN (1,2,3,5) AND `source` NOT LIKE '%calcContractSums%';

######################################################################################
# 2019-03-15 - Fixed formulas for calculation in GT2 tables in contracts (types 1-6)
#            - Additional formula fix to return the previous settings of the fields that trigger the main calculation function

# Fixed formulas for calculation in GT2 tables in contracts (types 1-6)
UPDATE _fields_meta SET `source`=REPLACE(`source`, 'gt2calc($(\'last_delivery_price_\' + crow));', 'gt2calc($(\'price_\' + crow));') WHERE `name`='group_table_2' AND `model`='Contract' AND `model_type` IN (1,2,3,4,5,6) AND `source` LIKE '%gt2calc($(\'last_delivery_price_\' + crow));%';

# Additional formula fix to return the previous settings of the fields that trigger the main calculation function
UPDATE _fields_meta SET `source`='text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => calcContractSums(this);' WHERE `name` IN ('quantity', 'price', 'last_delivery_price') AND `model`='Contract' AND `model_type` IN (1,2,3,5);
UPDATE _fields_meta SET `source`='agregate := sum\ntext_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => calcContractSums(this);' WHERE `name` IN ('article_width', 'article_weight') AND `model`='Contract' AND `model_type` IN (4,6);
UPDATE _fields_meta SET `source`='text_align := right\npermissions_edit := 1\npermissions_view := 1\njs_method := onkeyup => calcContractSums(this);' WHERE `name` IN ('quantity', 'price', 'last_delivery_price', 'article_code') AND `model`='Contract' AND `model_type` IN (4,6);

######################################################################################
# 2019-03-20 - Update 'aon_prepare_payments_table' report settings with extra setting for casco policies
#            - Complete the automations 'aon_prepare_payments_table' with extra options for nomenclatures

# Update 'aon_prepare_payments_table' report settings with extra setting for casco policies
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ninsurance_type_sticker := 62\r\ninsurance_type_zk := 61') WHERE `type`='aon_prepare_payments_table' AND `settings` NOT LIKE '%insurance_type_sticker%';

# Complete the automations 'aon_prepare_payments_table' with extra options for nomenclatures
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ncasco_policy :=', '\r\ninsurance_type_var_prefix := insurance_type_con\r\n\r\nnomenclature_policy_id := policy_id\r\nnomenclature_policy_num := policy_num\r\nnomenclature_status := fsa_status\r\nnomenclature_status_used := 2\r\nnomenclature_client_id := client_id\r\nnomenclature_client_name := client_name\r\n\r\ncasco_policy :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND `settings` NOT LIKE '%insurance_type_var_prefix%';

######################################################################################
# 2019-03-25 - Added two new automations 'validateZKStickerField' to validate if sticker/zk is selected for a newly added Casco policy

# Added two new automations 'validateZKStickerField' to validate if sticker/zk is selected for a newly added Casco policy
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`) VALUES
('Валидация за въведен Стикер/Зелена Карта', 0, NULL, 1, 'contracts', NULL, 'before_action', '4', 'insurer_id := insurer_id\r\npremium_value := premium__value\r\ncommission_percent := commission\r\nnumber_rows := number_installments\r\npremium_currency := number_installments__value\r\ninsurance_type := insurance_type_con4\r\n\r\ninsurance_type_sticker := 61\r\ninsurance_type_zk := 62\r\n\r\ncontract_precalculated_gt2_field := service_field_correct_add_policy', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\' && $request->get(\'insurance_group\') == \'17\' && ($request->get(\'insurance_type_con4\') == \'61\' || $request->get(\'insurance_type_con4\') == \'62\')', 'plugin := aon\r\nmethod := validateZKStickerField', 'cancel_action_on_fail := 1', 1, 0, 1),
('Валидация за въведен Стикер/Зелена Карта', 0, NULL, 1, 'contracts', NULL, 'before_action', '6', 'insurer_id := insurer_id\r\npremium_value := premium__value\r\ncommission_percent := commission\r\nnumber_rows := number_installments\r\npremium_currency := number_installments__value\r\ninsurance_type := insurance_type_con6\r\n\r\ninsurance_type_sticker := 61\r\ninsurance_type_zk := 62\r\n\r\ncontract_precalculated_gt2_field := service_field_correct_add_policy', 'condition := \'[action]\' == \'add\' && \'[request_is_post]\' == \'1\' && $request->get(\'insurance_group\') == \'17\' && ($request->get(\'insurance_type_con6\') == \'61\' || $request->get(\'insurance_type_con6\') == \'62\')', 'plugin := aon\r\nmethod := validateZKStickerField', 'cancel_action_on_fail := 1', 1, 0, 1);

######################################################################################
# 2019-04-25 - Update 'aon_report_to_insurer' report settings with extra setting for customers for which we will substruct the commission

# Update 'aon_report_to_insurer' report settings with extra setting for customers for which we will substruct the commission
UPDATE `reports` SET `settings` = CONCAT(`settings`, '\r\n\r\ncustomers_substract_commission := 206') WHERE `type`='aon_report_to_insurer' AND `settings` NOT LIKE '%customers_substract_commission%';

######################################################################################
# 2019-05-03 - Updated settings for calculateDateOfPayment automation with settings for system nomenclature

# Updated settings for calculateDateOfPayment automation with settings for system nomenclature
UPDATE `automations` SET `settings`=CONCAT(`settings`,'\r\nsystem_nomenclatures := 180435') WHERE `method` LIKE '%calculateDateOfPayment%' AND `start_model_type` IN (4,6) AND `settings` NOT LIKE '%system_nomenclatures%';

######################################################################################
# 2019-05-07 - Updated settings for aon_prepare_payments_table report to include the correct ids for type insurances zk and sticker

# Updated settings for aon_prepare_payments_table report to include the correct ids for type insurances zk and sticker
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'insurance_type_sticker := 62', 'insurance_type_sticker := 61') WHERE `type`='aon_prepare_payments_table' AND `settings` LIKE '%insurance_type_sticker := 62%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, 'insurance_type_zk := 61', 'insurance_type_zk := 62') WHERE `type`='aon_prepare_payments_table' AND `settings` LIKE '%insurance_type_zk := 61%';

######################################################################################
# 2019-05-29 - Updated settings for aon_checking_giving_stickers report to include the system nomenclatures

# Updated settings for aon_checking_giving_stickers report to include the system nomenclatures
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nsystem_nomenclatures := 180435') WHERE `type`='aon_checking_giving_stickers' AND `settings` NOT LIKE '%system_nomenclatures%';

######################################################################################
# 2020-01-23 - Updated settings for fields that containt the info for the related invoice

#  Updated settings for fields that containt the info for the related invoice
UPDATE _fields_meta SET `source`='text_align := left\r\npermissions_edit := 1\r\npermissions_view := 1\r\nmethod := getCustomDropdown\r\ntable := DB_TABLE_DOCUMENTS\r\ntable_i18n := DB_TABLE_DOCUMENTS_I18N\r\nlabel := custom_num\r\nvalue := id\r\nwhere := type=8 AND id IN (select gt2_i.free_text2 from gt2_details as gt2 join gt2_details_i18n as gt2_i on gt2.model=\'Contract\' and gt2.model_id=\"$model_id\" and gt2.id=gt2_i.parent_id  AND gt2_i.free_text2!=\"\")\r\nview_mode := link\r\nview_mode_url := index.php?launch=documents&documents=view&view=' WHERE `model`='Contract' AND `model_type`<7 AND `name`='free_text2' AND `source` NOT LIKE '%gt2_details_i18n%';

######################################################################################
# 2020-03-05 - Updated settings for aon_upcoming_deadlines report to include the flag to point if the cancel policies will be shown

# Updated settings for aon_upcoming_deadlines report to include the flag to point if the cancel policies will be shown
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nshow_canceled_policies := 1') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%show_canceled_policies%';

######################################################################################
# 2020-06-16 - Added export plugin for AON damages

# Added export plugin for AON damages
INSERT IGNORE INTO `exports` (`id`, `type`, `model`, `model_type`, `settings`, `visible`) VALUES
(55, 'aon_damages', 'Document', 4, 'export_file_name := health insurance (damages).xlsx\r\nexport_hide_filters := format, separator\r\nexport_tags := \r\nmax_count := 500', 1);

INSERT IGNORE INTO `exports_i18n` (`parent_id`, `name`, `description`, `lang`) VALUES
(55, 'Експорт на регистрирани щети', '', 'bg'),
(55, 'Export of damages', '', 'en');

######################################################################################
# 2020-09-18 - Updated settings for aon_leasing_companies report to include cancelled tag

# Updated settings for aon_leasing_companies report to include cancelled tag
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nnom_car_type :=', '\r\ncontract_tag_cancelled := 12\r\n\r\nnom_car_type :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%contract_tag_cancelled%';

######################################################################################
# 2020-09-30 - Added additional settings for 'aon_payments_distribution'

# Added additional settings for 'aon_payments_distribution'
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndebit_note_status_partially_paid :=', '\r\ndebit_note_document_debit_differences := debit_differences\r\ndebit_note_document_unallocated := unallocated_payments\r\ndebit_note_document_unpaid_dn_values := debit_values_all_document\r\ndebit_note_status_partially_paid :=') WHERE `type`='aon_payments_distribution' AND `settings` NOT LIKE '%debit_note_document_debit_differences%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_unknown_id :=', '\r\ndebit_notes_payments_statuses_unallocated := 30,34\r\n\r\ncustomer_unknown_id :=') WHERE `type`='aon_payments_distribution' AND `settings` NOT LIKE '%debit_notes_payments_statuses_unallocated%';

######################################################################################
# 2020-10-05 - Fixed the incorrect value of import_value_date (2805)

# Fixed the incorrect value of import_value_date (2805)
UPDATE documents_cstm SET
value=CONCAT_WS("-", SUBSTRING(VALUE, 7,4), SUBSTRING(VALUE, 4,2), SUBSTRING(VALUE, 1,2))
WHERE var_id=2805 and value LIKE '%.%';

######################################################################################
# 2021-02-15 - Updated settings for aon_upcoming_renewals report to include cancelled tag
#            - Added optional setting for aon_advanced_incomes_by_clients report which allows direct export if the total results are more than certain quantity

# Updated settings for aon_leasing_companies report to include cancelled tag
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ncontract_tag_cancelled := 12') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%contract_tag_cancelled%';

# Added optional setting for aon_advanced_incomes_by_clients report which allows direct export if the total results are more than certain quantity
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nexport_over :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%export_over%';

######################################################################################
# 2021-06-30 - Added automationwhich will generate link and password for the GDPR linker
#            - Added rest settings to work with the GDPR linker

# Added automationwhich will generate link and password for the GDPR linker
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Генериране на достъп', 0, NULL, 1, 'documents', NULL, 'action', '17', 'customer_unknown_id := 1\r\ncustomer_password_field := gdpr_password', 'condition := (\'[action]\' == \'edit\' || \'[action]\' == \'add\')\r\ncondition := $request->get(\'generate_access\')', 'plugin := human_resources\r\nmethod := createGdprAccess', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%createGdprAccess%' AND `method` LIKE '%human_resource%');

# Added rest settings to work with the GDPR linker
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'allowed_rest_user_agents', 'GDPRFileDownloader'),
('rest', 'filter_vars_documents_17', 'id, name, group_table_2, gdpr_files_grp, full_num, customer, customer_name, date, notes, description, group, department, layouts_details, type'),
('rest', 'filter_vars_patterns', 'id, name, content'),
('rest', 'filter_vars_users', 'id, display_name, firstname, lastname, username, rights, employee, default_department, default_group'),
('rest', 'filter_vars_customers', 'id, name, lastname, email'),
('rest', 'filter_vars_documents_types', 'id, related_customers_types, default_customer, default_group, default_name, default_department'),
('rest', 'filter_vars_emails', 'id, subject, body')
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);

######################################################################################
# 2021-07-05 - Added setting for location var in the checkDuplicateScheduleVisits automation

# Added setting for location var in the checkDuplicateScheduleVisits automation
UPDATE `automations` SET `settings`=CONCAT(`settings`, '\r\nlocation := customer_location') WHERE `method` LIKE('%checkDuplicateScheduleVisits%') AND `settings` NOT LIKE '%customer_location%';

######################################################################################
# 2021-07-09 - Added rest settings to work with the medical-exams-pwa

# Added rest settings to work with the medical-exams-pwa
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
('rest', 'allowed_rest_user_agents', 'GDPRFileDownloader, portal-medicalexams'),
('rest', 'filter_vars_documents_14', 'all'),
('rest', 'filter_vars_documents_15', 'all'),
('rest', 'filter_vars_nomenclatures_8', 'all'),
('rest', 'filter_vars_users', 'id, name, lastname, email')
ON DUPLICATE KEY UPDATE `value` = VALUES(`value`);

######################################################################################
# 2021-08-26 - Added automation to send notifications for added or changed reservations for medical exams (document type 15)

# Added automation to send notifications for added or changed reservations for medical exams (document type 15)
INSERT INTO `automations` (`id`, `name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`)
    SELECT NULL, 'Известяване за добавена или променена резервация за медицински преглед', 0, NULL, 1, 'documents', NULL, 'action', '15', '# Брой дни преди датата на резервация, в случай че не е е било зададено в документа - plat_corr_days. [не е задължителен. По подразбиране е 5]\r\n#corr_days := 5\r\n\r\n# ИД на шаблона за известяване, който да се използва. [не е задължителен. По подразбиране е 1001]\r\nmail_pattern_id := 1001\r\n', 'condition := (''[action]'' == ''edit'')', 'plugin := aon\r\nmethod := medicalExamsNotificationSend', NULL, 1, 0
WHERE NOT EXISTS(SELECT id FROM automations WHERE method LIKE '%plugin := aon\r\nmethod := medicalExamsNotificationSend%');

######################################################################################
# 2021-12-29 - Added "reply to" variables to the GDPR document
#            - Enhance the automation to set sender and reply to variables

# Added "reply to" variables to the GDPR document
DELETE FROM _fields_meta WHERE `id` IN (102489, 102490, 102491, 102492);
INSERT INTO `_fields_meta` (`id`, `model`, `model_type`, `name`, `type`, `searchable`, `sortable`, `outlooks`, `source`, `validate`, `required`, `hidden`, `readonly`, `calculate`, `bb`, `grouping`, `gt2`, `configurator`, `table`, `multilang`, `multiadd`, `multiedit`, `auditable`, `layout_id`, `position`, `width`, `width_print`, `height`) VALUES
(102489, 'Document', 17, 'custom_from_name', 'text', '', 0, 1, NULL, NULL, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2106, 27, '260', '', ''),
(102490, 'Document', 17, 'custom_sender', 'text', '', 0, 1, NULL, NULL, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2106, 28, '260', '', ''),
(102491, 'Document', 17, 'custom_replyto_name', 'text', '', 0, 1, NULL, NULL, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2106, 29, '260', '', ''),
(102492, 'Document', 17, 'custom_replyto', 'text', '', 0, 1, NULL, NULL, 0, 1, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 1, 2106, 30, '260', '', '');

DELETE FROM `_fields_i18n` WHERE `parent_id` IN (102489, 102490, 102491, 102492);
INSERT INTO `_fields_i18n` (`parent_id`, `content_type`, `content`, `lang`) VALUES
(102489, 'label', 'Sender', 'bg'),
(102490, 'label', 'Sender Email', 'bg'),
(102491, 'label', 'Reply To', 'bg'),
(102492, 'label', 'Reply To Email', 'bg');

# Enhance the automation to set sender and reply to variables
UPDATE `automations` SET `method`='method := setAdditionalVar\r\n#var_name := custom_sender; custom_from_name;custom_replyto; custom_replyto_name\r\n#var_value := <EMAIL>; php($this->registry->get(\'originalUser\')->get(\'firstname\').\' \'.$this->registry->get(\'originalUser\')->get(\'lastname\').\' (Ей Ар Ес България)\'); php($this->registry->get(\'originalUser\')->get(\'email\')); php($this->registry->get(\'originalUser\')->get(\'firstname\').\' \'.$this->registry->get(\'originalUser\')->get(\'lastname\').\' (Ей Ар Ес България)\')\r\nvar_name := custom_sender; custom_from_name\r\nvar_value := <EMAIL>; php($this->registry->get(\'originalUser\')->get(\'firstname\').\' \'.$this->registry->get(\'originalUser\')->get(\'lastname\').\' (Ей Ар Ес България)\')\r\n' WHERE  `id`=102;


######################################################################################
# 2022-01-19 - Added setting which will define which actions will trigger the setGT2RowsKeys automation

# Added setting which will define which actions will trigger the setGT2RowsKeys automation
UPDATE `automations` SET `settings`='actions_execute := add, addannex, edittopic' WHERE `method` LIKE '%setGT2RowsKeys%' AND `method` LIKE '%aon%' AND `settings` NOT LIKE '%actions_execute%';

######################################################################################
# 2022-03-10 - Added posibility to show the policy discount in 'aon_upcoming_deadlines' report
#            - Added discounts if required in the 'aon_report_to_insurer' report
#            - Added discounts settings in the 'aon_payments_distribution' report
#            - Added discounts settings in the 'aon_create_debit_note' report
#            - Added settings to manage different calculation of the shared commmission based on the main commission and not the ZP
#            - Added settings for recalculating discount if such is set in the policy

# Added posibility to show the policy discount in 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%include_discount%';

# Added discounts if required in the 'aon_report_to_insurer' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_report_to_insurer' AND `settings` NOT LIKE '%include_discount%';

# Added discounts settings in the 'aon_payments_distribution' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_payments_distribution' AND `settings` NOT LIKE '%include_discount%';

# Added discounts settings in the 'aon_create_debit_note' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%include_discount%';

# Added settings to manage different calculation of the shared commmission based on the main commission and not the ZP
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\ngrouping_shared_table_name :=', '\r\n\r\nshared_commission_parent_field_base_calculate := price\r\n\r\ngrouping_shared_table_name :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%shared_commission_parent_field_base_calculate%';
UPDATE `dashlets_plugins` SET `settings`=CONCAT(`settings`, '\r\n\r\ntable_discount_percent := disc_percentage\r\ntable_discount_value := disc_value\r\ntable_discount_value_currency := disc_valuelocal\r\ntable_discount_customer := 1') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%table_discount_percent%';

# Added settings for recalculating discount if such is set in the policy
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\nshared_commission_parent_field_base_calculate :=', '\r\ndiscount_value := 4\r\nshared_commission_parent_field_base_calculate :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%\r\ndiscount_value :=%';

######################################################################################
# 2022-03-11 - Added settings for recalculating discount and an optional way to calculate shared commission when building the payments table

# Added settings for recalculating discount and an optional way to calculate shared commission when building the payments table
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nshared_commission_name :=', '\r\nshared_commission_parent_field_base_calculate := price\r\nshared_commission_discount_field_priority :=\r\nshared_commission_name :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND `settings` NOT LIKE '%shared_commission_parent_field_base_calculate%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nshared_commission :=', '\r\ncommission_type_discount := 4\r\nshared_commission :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND settings NOT LIKE '%commission_type_discount%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nshared_commission :=', '\r\ndiscount_percentage_field :=\r\nshared_commission :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND settings NOT LIKE '%discount_percentage_field%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\ndiscount_percentage_field :=', '\r\ndiscount_customer :=\r\ndiscount_percentage_field :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND settings NOT LIKE '%discount_customer%';
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\nshared_commission :=', '\r\ndiscount_total_field :=\r\nshared_commission :=') WHERE `method` LIKE '%calculateDateOfPayment%' AND settings NOT LIKE '%discount_total_field%';

######################################################################################
# 2022-03-16 - Added settings for discount payments type in 'aon_checking_giving_stickers' report

# Added settings for discount payments type in 'aon_checking_giving_stickers' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nadditional_commission_type :=', '\r\ndiscount_commission_type := 4\r\nadditional_commission_type :=') WHERE `type`='aon_checking_giving_stickers' AND `settings` NOT LIKE '%discount_commission_type%';

######################################################################################
# 2022-03-18 - Added posibility to show the policy discount in 'aon_bonuses_to_invoice' report
#            - Added posibility to show the policy discount in 'aon_upcoming_renewals' report
#            - Added posibility to show the policy discount in 'aon_advanced_incomes_by_clients' report

# Added posibility to show the policy discount in 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_bonuses_to_invoice' AND `settings` NOT LIKE '%include_discount%';

# Added posibility to show the policy discount in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_var_name := disc_valuelocal') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%include_discount%';

# Added posibility to show the policy discount in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\nstandard_policy_type := 1\r\ndiscount_policy_type := 4') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%include_discount%';

######################################################################################
# 2022-03-21 - Added posibility to show the policy discount in 'aon_leasing_companies' report
#            - Added settings to allow showing balance for all policies in the 'aon_upcoming_deadlines' report
#            - Added settings to allow showing balance for all policies in the 'aon_bonuses_to_invoice' report

# Added posibility to show the policy discount in 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_discount :=\r\ndiscount_policy_type := 4') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%include_discount%';

# Added settings to allow showing balance for all policies in the 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nshow_balance_for_all_policies :=') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%show_balance_for_all_policies%';

# Added settings to allow showing balance for all policies in the 'aon_bonuses_to_invoice' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nshow_balance_for_all_policies :=') WHERE `type`='aon_bonuses_to_invoice' AND `settings` NOT LIKE '%show_balance_for_all_policies%';

######################################################################################
# 2022-03-22 - Change the settings to contain the discount policy type in 'aon_upcoming_renewals' report
#            - Added settings to allow showing balance for all policies in the 'aon_advanced_incomes_by_clients' report
#            - Added settings to allow showing balance for all policies in the 'aon_leasing_companies' report

# Change the settings to contain the discount policy type in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndiscount_var_name := disc_valuelocal', '\r\ndiscount_policy_type := 4') WHERE `type`='aon_upcoming_renewals' AND `settings` LIKE '%discount_var_name%';

# Added settings to allow showing balance for all policies in the 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nshow_balance_for_all_policies :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%show_balance_for_all_policies%';

# Added settings to allow showing balance for all policies in the 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\nshow_balance_for_all_policies :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%show_balance_for_all_policies%';

######################################################################################
# 2022-04-04 - Added settings to allow including discounts in all policies in 'aon_create_debit_note' report

# Added settings to allow including discounts in all policies in 'aon_create_debit_note' report
UPDATE `reports` SET `settings`=CONCAT( 'calculate_tax_all_policies :=\r\n', `settings`) WHERE `type`='aon_create_debit_note' AND `settings` NOT LIKE '%calculate_tax_all_policies%';

######################################################################################
# 2022-06-08 - Added settings for showing notes in 'aon_upcoming_deadlines' report
#            - Added settings for showing notes in 'aon_upcoming_renewals' report
#            - Added settings for showing notes in 'aon_advanced_incomes_by_clients' report

# Added settings for showing notes in 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ninclude_discount :=', '\r\nshow_notes :=\r\n\r\ninclude_discount :=') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%show\_notes%';

# Added settings for showing notes in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\nshow_notes :=') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%show\_notes%';

# Added settings for showing notes in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\nshow_notes :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%show\_notes%';

######################################################################################
# 2022-06-23 - Added new settings for hiding the sums for shared commission in 'aon_advanced_incomes_by_clients' report

# Added new settings for hiding the sums for shared commission in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ndiscount_policy_type :=', '\r\nshared_policy_type := 2\r\ndiscount_policy_type :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%shared\_policy\_type%';
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nshow_balance_for_all_policies :=', '\r\ninclude_zp_sums_for_shared_commission := 1\r\n\r\nshow_balance_for_all_policies :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%include\_zp\_sums\_for\_shared\_commission%';

######################################################################################
# 2022-07-04 - Added posibility to show data for agents in 'aon_leasing_companies' report

# Added posibility to show data for agents in 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_agents :=\r\ninsurance_agent_id :=\r\nagent_commission_percent :=\r\nagent_commission_value :=\r\nagent_property_address :=\r\nagent_property_description :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%include_agents%';

######################################################################################
# 2022-07-05 - Added new setting for customer type agent in 'aon_leasing_companies' report

# Added new setting for customer type agent in 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\ninsurance_agent_id :=', '\r\ncustomers_agents_type :=\r\ninsurance_agent_id :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%customers\_agents\_type%';

######################################################################################
# 2022-07-05 - Added new setting gt2 relation in agents table in 'aon_leasing_companies' report

# Added new setting gt2 relation in agents table in 'aon_leasing_companies' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nagent_property_address :=', '\r\nagent_commission_gt2_relation :=\r\nagent_property_address :=') WHERE `type`='aon_leasing_companies' AND `settings` NOT LIKE '%agent\_commission\_gt2\_relation%';

######################################################################################
# 2022-07-12 - Added new settings for agents commission to 'aon_advanced_incomes_by_clients' report

# Added new settings for agents commission to 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\n\r\ninclude_agents_commission :=\r\nagent_id :=\r\nagent_commission_percent :=\r\nagent_commission_value :=\r\nagent_commission_gt2_relation :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%include\_agents\_commission%';

######################################################################################
# 2022-09-30 - Added settings for fixing the insurance sum and premium value for cars in casco group policies

# Added settings for fixing the insurance sum and premium value for cars in casco group policies
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\nshow_gt2_fields :=', '\r\ncasco_policy_group := 6\r\n\r\nshow_gt2_fields :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%casco_policy_group%';
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\n\r\nconfig_table_iv :=', '\r\ngrouping_table_casco_group_zp := premium_value\r\ngrouping_table_casco_group_zs := insurance_value\r\n\r\nconfig_table_iv :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%grouping_table_casco_group_zp%';

######################################################################################
# 2022-10-12 - Added settings for types which will trigger the tax validation

# Added settings for types which will trigger the tax validation
UPDATE `dashlets_plugins` SET `settings`=REPLACE(`settings`, '\r\ncommission_type_field :=', '\r\ntax_validation_types := 4,6\r\ncommission_type_field :=') WHERE `type`='aon_correct_policy' AND `settings` NOT LIKE '%tax_validation_types%';

######################################################################################
# 2022-10-27 - Added settings for pattern plugin prepareReport

# Added settings for pattern plugin prepareReport
UPDATE `patterns_plugins` SET `settings`='pattern_casco := 23\r\npattern_go := 24\r\npattern_casco_and_go := 25\r\n\r\ninsurer_levins := 206\r\ninsurer_bulstrad := 152\r\ninsurer_dzi := 166\r\ninsurer_armeec := 15\r\ninsurer_uniqa := \r\ninsurer_bulgaria_insurance := \r\ninsurer_allianz := \r\ninsurer_allianzlife := \r\n\r\nnom_casco_id := 18\r\nnom_go_id := 61\r\n' WHERE method='prepareReport';

######################################################################################
# 2022-12-29 - Added new settings for SMTP oauth2

# Added new settings for SMTP oauth2
INSERT INTO `settings` (`section`, `name`, `value`) VALUES
  ('smtp', 'oauth2_provider', 'Azure'),
  ('smtp', 'oauth2_client_id', '3b426fb8-2aea-42cb-a42f-90f0b5f3d7e6'),
  ('smtp', 'oauth2_client_secret', '****************************************'),
  ('smtp', 'oauth2_tenant_id', '463adbdf-9f29-40e1-8fc4-43c13528458d'),
  ('smtp', 'oauth2_refresh_token', '**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************')
ON DUPLICATE KEY UPDATE
  `value` = VALUES(`value`);

######################################################################################
# 2023-01-17 - Added settings for showing car model in 'aon_upcoming_deadlines' report
#            - Added settings for showing car name in 'aon_upcoming_renewals' report

# Added settings for showing car model in 'aon_upcoming_deadlines' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ninclude_discount :=', '\r\nshow_car_name :=\r\n\r\ninclude_discount :=') WHERE `type`='aon_upcoming_deadlines' AND `settings` NOT LIKE '%show\_car\_name%';

# Added settings for showing car name in 'aon_upcoming_renewals' report
UPDATE `reports` SET `settings`=CONCAT(`settings`, '\r\nshow_car_name :=') WHERE `type`='aon_upcoming_renewals' AND `settings` NOT LIKE '%show\_car\_name%';

######################################################################################
# 2023-01-18 - Added settings for showing filter for leasing company in 'aon_advanced_incomes_by_clients' report
#            - Added settings for showing car model in 'aon_advanced_incomes_by_clients' report

# Added settings for showing filter for leasing company in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ndocument_type_invoice :=', '\r\n\r\nshow_leasing_company_filter :=\r\n\r\ndocument_type_invoice :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%show\_leasing\_company\_filter%';

# Added settings for showing car model in 'aon_advanced_incomes_by_clients' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\ninclude_agents_commission :=', '\r\nshow_car_name :=\r\n\r\ninclude_agents_commission :=') WHERE `type`='aon_advanced_incomes_by_clients' AND `settings` NOT LIKE '%show\_car\_name%';

######################################################################################
# 2023-11-03 - Added settings for damage commission in 'aon_create_invoices' report

# Added settings for damage commission in 'aon_create_invoices' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nincluded_contract_types_with_branch :=', '\r\ncontracts_commission_damage := commission_damage\r\ncontracts_commission_damage_tag := 21\r\nincluded_contract_types_with_branch :=') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contracts\_commission\_damage%';

######################################################################################
# 2024-02-05 - Added new report - 'aon_clients_insurances' - for the AON installation

# Added new report - 'aon_clients_insurances' - for the AON installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (467, 'aon_clients_insurances', 'years_max_period := 3\r\n\r\nincluded_categories := 9,10,11,12,13\r\nincluded_contract_types := 1,2,3,4,5,6\r\ninsurance_group := insurance_group\r\ninsurance_type_prefix := insurance_type_con\r\ninsurance_bonus_bgn := premium__valuelocal\r\ninsurance_commission_sum := commission__valuelocal', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (467, 'Застраховки към клиент', NULL, NULL, 'bg'),
  (467, 'Clients insurances', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 467, 0, 1),
  ('reports', 'export', 467, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=467;

######################################################################################
# 2024-06-24 - Added settings for the full_num column in plugin for AON damages

# Added settings for the full_num column in plugin for AON damages
UPDATE `exports`
SET settings=CONCAT(settings, "\r\nfull_num_column_user_ids := 94, 101, 117, 123, 131, 142")
WHERE type='aon_damages' AND settings NOT LIKE '%full_num_column_user_ids%';

######################################################################################
# 2024-09-13 - Added settings for distribution commission in 'aon_create_invoices' report

# Added settings for distribution commission in 'aon_create_invoices' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\nincluded_contract_types_with_branch :=', '\r\ncontracts_commission_distribution := commission_distribution\r\nincluded_contract_types_with_branch :=') WHERE `type`='aon_create_invoices' AND `settings` NOT LIKE '%contracts\_commission\_distribution%';

######################################################################################
# 2024-10-15 - Added settings for commission type in 'insurers_create_report' report

# Added settings for commission type in 'insurers_create_report' report
UPDATE `reports` SET `settings`=REPLACE(`settings`, '\r\n\r\nincluded_contract_types :=', '\r\ncommission_type := distribution\r\n\r\nincluded_contract_types :=') WHERE `type`='insurers_create_report' AND `settings` NOT LIKE '%commission\_type%';

######################################################################################
# 2024-10-22 - Added new report - 'ars_product_report' - for the ARS installation

# Added new report - 'ars_product_report' - for the ARS installation
INSERT IGNORE INTO `reports` (`id`, `type`, `settings`, `position`, `is_portal`, `visible`) VALUES
  (473, 'ars_product_report', 'document_type_invoice := 8\r\ninvoice_value := invoice_value\r\ninvoice_differences := invoice_differences\r\ninvoice_notes := invoice_differences_notes\r\ninvoice_type := invoice_type\r\ninvoice_type_fee := 3\r\n\r\nincluded_contract_types := 1,2,3,4,5,6,7\r\nacgn_policy := 1\r\npolicy_type_local := program\r\npolicy_type_program := local\r\n\r\nnumber_rows := number_installments\r\ninsurance_group := insurance_group\r\nclient_type := client_type\r\nbusiness_type := business_type\r\n\r\ndocument_to_add := 9\r\ninvoice_id_bb := bb_variant\r\ninvoice_num_bb := bb_name\r\ninvoice_date_bb := bb_note\r\ninvoice_amount_bb := bb_value\r\ninvoice_differences_bb := bb_quantity\r\ninvoice_notes_bb := invoice_note\r\n\r\ncontract_fee := 7\r\npolicy_type_fee := Fee\r\ncontract_fee_value := fee_value\r\ncontract_fee_insured := insured_id\r\ncontracts_tag_annulled := 12\r\n\r\n# id на тип договори „Каско/ГО (индивидуална)“\r\ncontracts_type_casco_cl_individual := 4\r\n# id на тип договори „Каско/ГО (групова)“\r\ncontracts_type_casco_cl_group := 6', 0, 0, 1);
INSERT IGNORE INTO `reports_i18n` (`parent_id`, `name`, `section`, `description`, `lang`) VALUES
  (473, 'Продуктов рипорт', NULL, NULL, 'bg'),
  (473, 'Product report', NULL, NULL, 'en');
INSERT IGNORE INTO `roles_definitions` (`module`, `action`, `model_type`, `requires_model`, `position`) VALUES
  ('reports', 'generate_report', 473, 0, 1),
  ('reports', 'export', 473, 0, 1);
INSERT IGNORE INTO `roles_permissions` (`parent_id`, `definition_id`, `permission`)
  SELECT 1, `id`, 'all' FROM `roles_definitions` WHERE `module`='reports' AND `action` IN ('generate_report', 'export') AND `model_type`=473;

INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'ars_product_report', 'bg', 'reports_date_filter', 'Дата на издаване на фактурата'),
  ('reports', 'ars_product_report', 'bg', 'reports_customer', 'Контрагент'),
  ('reports', 'ars_product_report', 'bg', 'reports_show_annulled', 'Покажи прекратени / анулирани полици'),
  ('reports', 'ars_product_report', 'bg', 'reports_hide_fee', 'Не показвай Такси (fee)'),
  ('reports', 'ars_product_report', 'bg', 'reports_from_date', 'от'),
  ('reports', 'ars_product_report', 'bg', 'reports_to_date', 'до'),
  ('reports', 'ars_product_report', 'bg', 'reports_invoice_num', 'Invoice No'),
  ('reports', 'ars_product_report', 'bg', 'reports_invoice_date', 'Invoice Date'),
  ('reports', 'ars_product_report', 'bg', 'reports_account_manager', 'Account Manager'),
  ('reports', 'ars_product_report', 'bg', 'reports_account_manager_department', 'Department'),
  ('reports', 'ars_product_report', 'bg', 'reports_client', 'Client'),
  ('reports', 'ars_product_report', 'bg', 'reports_contract_date', 'Contract Date'),
  ('reports', 'ars_product_report', 'bg', 'reports_policy_number', 'Policy Number'),
  ('reports', 'ars_product_report', 'bg', 'reports_payment_num', 'Pay Num'),
  ('reports', 'ars_product_report', 'bg', 'reports_payment_deadline', 'Due date'),
  ('reports', 'ars_product_report', 'bg', 'reports_policy_type', 'Policy Type'),
  ('reports', 'ars_product_report', 'bg', 'reports_type', 'Type'),
  ('reports', 'ars_product_report', 'bg', 'reports_policy_period', 'Policy Period'),
  ('reports', 'ars_product_report', 'bg', 'reports_client_type', 'Client Type'),
  ('reports', 'ars_product_report', 'bg', 'reports_business_type', 'Business Type'),
  ('reports', 'ars_product_report', 'bg', 'reports_bonus_bgn', 'Premium (BGN)'),
  ('reports', 'ars_product_report', 'bg', 'reports_premium_and_taxes_bgn', 'Premium and Taxes (BGN)'),
  ('reports', 'ars_product_report', 'bg', 'reports_balance', 'Balance'),
  ('reports', 'ars_product_report', 'bg', 'reports_difference', 'Difference'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_total', 'Comm. (BGN)'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_distribution_percent', 'Com. distribution %'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_distribution_value', 'Com. distribution (BGN)'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_damage_percent', 'Com. damages %'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_damage_value', 'Com. damages (BGN)'),
  ('reports', 'ars_product_report', 'bg', 'reports_no_number', '[no num]'),
  ('reports', 'ars_product_report', 'bg', 'reports_policy_annulled', 'Terminated / cancelled'),
  ('reports', 'ars_product_report', 'bg', 'reports_policy_annulled_yes', 'yes'),
  ('reports', 'ars_product_report', 'bg', 'reports_add_report_to_netherlands', 'Add report for Netherlands'),
  ('reports', 'ars_product_report', 'bg', 'error_actions_not_completed', 'An error occurred while trying to complete the required operations! Please contact the nZoom support!'),
  ('reports', 'ars_product_report', 'bg', 'error_reports_complete_required_filters', 'Please complete required filters!');
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'ars_product_report', 'en', 'reports_date_filter', 'Date issue invoice'),
  ('reports', 'ars_product_report', 'en', 'reports_customer', 'Customer'),
  ('reports', 'ars_product_report', 'en', 'reports_show_annulled', 'Show cancelled/annulled policies'),
  ('reports', 'ars_product_report', 'en', 'reports_hide_fee', 'Hide fee'),
  ('reports', 'ars_product_report', 'en', 'reports_from_date', 'from'),
  ('reports', 'ars_product_report', 'en', 'reports_to_date', 'to'),
  ('reports', 'ars_product_report', 'en', 'reports_invoice_num', 'Invoice No'),
  ('reports', 'ars_product_report', 'en', 'reports_invoice_date', 'Invoice Date'),
  ('reports', 'ars_product_report', 'en', 'reports_account_manager', 'Account Manager'),
  ('reports', 'ars_product_report', 'en', 'reports_account_manager_department', 'Department'),
  ('reports', 'ars_product_report', 'en', 'reports_client', 'Client'),
  ('reports', 'ars_product_report', 'en', 'reports_contract_date', 'Contract Date'),
  ('reports', 'ars_product_report', 'en', 'reports_policy_number', 'Policy Number'),
  ('reports', 'ars_product_report', 'en', 'reports_payment_num', 'Pay Num'),
  ('reports', 'ars_product_report', 'en', 'reports_payment_deadline', 'Due date'),
  ('reports', 'ars_product_report', 'en', 'reports_policy_type', 'Policy Type'),
  ('reports', 'ars_product_report', 'en', 'reports_type', 'Type'),
  ('reports', 'ars_product_report', 'en', 'reports_policy_period', 'Policy Period'),
  ('reports', 'ars_product_report', 'en', 'reports_client_type', 'Client Type'),
  ('reports', 'ars_product_report', 'en', 'reports_business_type', 'Business Type'),
  ('reports', 'ars_product_report', 'en', 'reports_bonus_bgn', 'Premium (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_premium_and_taxes_bgn', 'Premium and Taxes (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_balance', 'Balance'),
  ('reports', 'ars_product_report', 'en', 'reports_difference', 'Difference'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_total', 'Comm. (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_distribution_percent', 'Com. distribution %'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_distribution_value', 'Com. distribution (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_damage_percent', 'Com. damages %'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_damage_value', 'Com. damages (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_no_number', '[no number]'),
  ('reports', 'ars_product_report', 'en', 'reports_policy_annulled', 'Terminated / cancelled'),
  ('reports', 'ars_product_report', 'en', 'reports_policy_annulled_yes', 'yes'),
  ('reports', 'ars_product_report', 'en', 'reports_add_report_to_netherlands', 'Add report for Netherlands'),
  ('reports', 'ars_product_report', 'en', 'error_actions_not_completed', 'An error occurred while trying to complete the required operations! Please contact the nZoom support!'),
  ('reports', 'ars_product_report', 'en', 'error_reports_complete_required_filters', 'Please complete required filters!');

######################################################################################
# 2025-01-21 - Added new labels for 'ars_product_report' report

# Added new labels for 'ars_product_report' report
INSERT IGNORE INTO `i18n` (`module`, `group`, `lang`, `name`, `value`) VALUES
  ('reports', 'ars_product_report', 'bg', 'reports_commission_percent', 'Commission %'),
  ('reports', 'ars_product_report', 'bg', 'reports_commission_value', 'Commission (BGN)'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_percent', 'Commission %'),
  ('reports', 'ars_product_report', 'en', 'reports_commission_value', 'Commission (BGN)');

######################################################################################
# 2025-01-29 - Added new automation for creating debit note on certain date after a deadline date

# Added new automation for creating debit note on certain date after a deadline date
INSERT INTO `automations` (`name`, `depend`, `exclude`, `should_continue`, `module`, `controller`, `automation_type`, `start_model_type`, `settings`, `conditions`, `method`, `after_action`, `position`, `nums`, `active`)
  SELECT 'Автоматично добавяне на дебит нота', 0, NULL, 1, 'contracts', NULL, 'crontab', '0', 'start_time := 03:00\r\nstart_before := 05:00\r\n\r\ncontract_types := 5\r\nfirst_possible_deadline := 2024-04-01\r\ndeadline_due_days := 45\r\npayment_date_added_days := 15\r\n\r\ncontract_var_insurer := insurer_id\r\ncontract_var_number_installments := number_installments\r\ncontract_var_insurance_type_prefix := insurance_type_con\r\n\r\ncustomer_type_insurer := 4\r\ncustomer_var_insurer_name_in_bgn := name_in_bg\r\n\r\ndoc_type_debit_note := 16\r\ndoc_var_customer_eik_egn := customer_egn_eik\r\ndoc_var_customer_name_in_bg := name_in_bg\r\ndoc_var_insurer_name := insurer_name\r\ndoc_var_insurer_id := insurer_id\r\ndoc_var_insurer_name_in_bg := insurer_in_bg\r\ndoc_var_debit_note_date := debit_from_date\r\ndoc_var_debit_note_to_date := debit_to_date\r\ndoc_var_debit_note_value := debit_value\r\ndoc_var_debit_note_debit_type := debit_type\r\n\r\nsend_to_email := <EMAIL>', 'condition := 1', 'plugin := aon\r\nmethod := createDebitNote', NULL, 1, 0, 1
    WHERE NOT EXISTS (SELECT id FROM automations WHERE `method` LIKE '%createDebitNote%' AND `method` LIKE '%aon%');

######################################################################################
# 2025-02-27 - Added setting for tax type in 'createDebitNote' automation

# Added setting for tax type in 'createDebitNote' automation
UPDATE `automations` SET `settings`=REPLACE(`settings`, '\r\n\r\ncustomer_type_insurer :=', '\r\ncontract_var_tax := tax\r\n\r\ncustomer_type_insurer :=') WHERE  `method` LIKE '%aon%' AND `method` LIKE '%createDebitNote%' AND `settings` NOT LIKE '%contract\_var\_tax%';
