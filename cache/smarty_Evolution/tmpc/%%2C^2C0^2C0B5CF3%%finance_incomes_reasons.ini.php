<?php $_config_vars = array (
  'finance_incomes_reason_audit' => 'приходен документ',
  'finance_incomes_reasons' => 'Приходни документи',
  'finance_incomes_reason' => 'Приходен документ',
  'finance_incomes_reasons_invoice' => 'Фактура',
  'finance_incomes_reasons_proforma_invoice' => 'Проформа фактура',
  'finance_incomes_reasons_debit_notice' => 'Дебитно известие',
  'finance_incomes_reasons_credit_notice' => 'Кредитно известие',
  'finance_incomes_reasons_correct_reason' => 'Корекция',
  'finance_incomes_reasons_name' => 'Относно',
  'finance_incomes_reasons_num' => 'Номер',
  'finance_incomes_reasons_currency' => 'Валута',
  'finance_incomes_reasons_customer' => 'Контрагент',
  'finance_incomes_reasons_project' => 'Проект',
  'finance_incomes_reasons_phase' => 'Фаза',
  'finance_incomes_reasons_company_data' => 'Каса/Банкова сметка',
  'finance_incomes_reasons_office' => 'Офис',
  'finance_incomes_reasons_company' => 'Фирма',
  'finance_incomes_reasons_type' => 'Тип',
  'finance_incomes_reasons_department' => 'Отдел',
  'finance_incomes_reasons_group' => 'Група',
  'finance_incomes_reasons_description' => 'Описание',
  'finance_incomes_reasons_notes' => 'Забележки',
  'finance_incomes_reasons_cashbox' => 'Каса',
  'finance_incomes_reasons_bank_account' => 'Банкова сметка',
  'finance_incomes_reasons_container_id' => 'Каса/Банкова сметка',
  'finance_incomes_reasons_employee' => 'Служител',
  'finance_incomes_reasons_total' => 'Общо',
  'finance_incomes_reasons_amount' => 'Сума',
  'finance_incomes_reasons_paid_amount' => 'Платена сума до момента',
  'finance_incomes_reasons_remaining_amount' => 'Оставаща сума за плащане',
  'finance_incomes_reasons_date_of_payment' => 'Падеж',
  'finance_incomes_reasons_date_of_handover' => 'Дата на предаване',
  'finance_incomes_reasons_release_date' => 'Дата на освобождаване',
  'finance_incomes_reasons_relatives_children' => 'Създател на',
  'finance_incomes_reasons_relatives_parent' => 'Създаден от',
  'finance_incomes_reasons_relative_name' => 'Име на свързания запис',
  'finance_incomes_reasons_issue_date' => 'Дата на документа',
  'finance_incomes_reasons_issue_by' => 'Съставил основанието',
  'finance_incomes_reasons_fiscal_event_date' => 'Дата на данъчно събитие',
  'finance_incomes_reasons_date_of_receive' => 'Дата на получаване',
  'finance_incomes_reasons_eik' => 'ЕИК',
  'finance_incomes_reasons_vat_num' => 'Номер по ДДС',
  'finance_incomes_reasons_customer_address' => 'Адрес на контрагент',
  'finance_incomes_reasons_received_by' => 'МОЛ',
  'finance_incomes_reasons_date_of_issue' => 'Дата на издаване',
  'finance_incomes_reasons_distributed' => 'Разпределен',
  'finance_incomes_reasons_tags' => 'Тагове',
  'finance_incomes_reasons_fiscal_total' => 'Данъчна основа (фискална)',
  'finance_incomes_reasons_fiscal_total_vat' => 'ДДС (фискален)',
  'finance_incomes_reasons_fiscal_total_with_vat' => 'Обща сума (фискална)',
  'finance_incomes_reasons_action_email' => 'Писма',
  'finance_incomes_reasons_added' => 'Дата на добавяне',
  'finance_incomes_reasons_added_by' => 'Добавено от',
  'finance_incomes_reasons_modified' => 'Дата на промяна',
  'finance_incomes_reasons_modified_by' => 'Променено от',
  'finance_incomes_reasons_counter' => 'Брояч',
  'finance_incomes_reasons_reason' => 'Основание за издаване на Кредитно/Дебитно известие',
  'finance_incomes_reasons_article' => 'Артикул',
  'finance_incomes_reasons_name_num' => '[Номер] Относно',
  'finance_incomes_reasons_customer_name_code' => '[Код] Контрагент',
  'finance_incomes_reasons_project_name_code' => '[Код] Проект',
  'finance_incomes_reasons_invoice_code' => 'Код за електронни фактури',
  'finance_incomes_reasons_payment_type' => 'Начин на плащане',
  'finance_incomes_reasons_total_vat_rate' => 'ДДС ставка',
  'finance_incomes_reasons_payment_status' => 'Статус на плащане',
  'finance_incomes_reasons_handovered_status' => 'Статус на приемо-предаване',
  'finance_incomes_reasons_total_no_vat_reason_text' => 'Основание за неначисляване на ДДС',
  'finance_incomes_reasons_total_with_vat' => 'Общо с ДДС',
  'finance_incomes_reasons_total_vat' => 'Размер ДДС',
  'finance_incomes_reasons_total_amount_with_vat' => 'Обща стойност (с ДДС)',
  'finance_incomes_reasons_invoice_amount' => 'Фактурирано',
  'finance_incomes_reasons_total_paid_amount' => 'Платено (общо)',
  'finance_incomes_reasons_total_remaining_amount' => 'За плащане (общо)',
  'finance_incomes_reasons_direct_paid_amount' => 'Платено (по основанието)',
  'finance_incomes_reasons_amount_to_be_paid' => 'За плащане (по основанието)',
  'finance_incomes_reasons_fin_field_1' => 'Свободно поле 1',
  'finance_incomes_reasons_fin_field_2' => 'Свободно поле 2',
  'finance_incomes_reasons_add' => 'Добавяне на %s',
  'finance_incomes_reasons_addinvoice' => 'Издаване на фактура',
  'finance_incomes_reasons_addhandover_incoming' => 'Приемане на стока',
  'finance_incomes_reasons_addhandover_outgoing' => 'Издаване на стока',
  'finance_incomes_reasons_addhandover_autofill' => 'Попълване',
  'finance_incomes_reasons_addproformainvoice' => 'Издаване на проформа фактура',
  'finance_incomes_reasons_addcorrect' => 'Добавяне на корекция',
  'finance_incomes_reasons_addcreditdebit' => 'Добавяне на кредитни/дебитни известия',
  'finance_incomes_reasons_addcredit' => 'Добавяне на кредитно известие',
  'finance_incomes_reasons_commodities_reservation' => 'Запазване на стоки',
  'finance_incomes_reasons_addpayment' => 'Добавяне на плащане',
  'finance_incomes_reasons_edit' => 'Редакция на %s',
  'finance_incomes_reasons_view' => 'Разглеждане на %s',
  'finance_incomes_reasons_translate' => 'Превод на %s',
  'finance_incomes_reasons_attachments' => 'Файлове към %s',
  'finance_incomes_reasons_assign' => 'Назначаване на %s',
  'finance_incomes_reasons_generate' => 'Генериране на файл по шаблон от %s',
  'finance_incomes_reasons_history' => 'История на %s',
  'finance_incomes_reasons_history_activity' => 'История',
  'finance_incomes_reasons_relatives' => 'Връзки към %s',
  'finance_incomes_reasons_payments' => 'Баланс на %s',
  'finance_incomes_reasons_advances_relatives' => 'Връзки на %s с авансови фактури',
  'finance_incomes_reasons_add_advance' => 'Добавяне на авансова фактура',
  'finance_incomes_reasons_advances' => 'Аванси',
  'finance_incomes_reasons_advance' => 'Авансова фактура',
  'finance_incomes_reasons_advance_default_text' => 'Авансово плащане по фактура %s/%s',
  'finance_incomes_reasons_proforma_advances_relatives' => 'Връзки на %s с авансови проформи',
  'finance_incomes_reasons_add_proforma_advance' => 'Добавяне на авансова проформа',
  'finance_incomes_reasons_proforma_advances' => 'Проформа аванси',
  'finance_incomes_reasons_proforma_advance' => 'Авансова проформа',
  'finance_incomes_reasons_distribution' => 'Разпределяне на %s',
  'finance_incomes_reasons_printform' => 'Печатна форма на %s',
  'finance_incomes_reasons_communications' => 'Комуникации',
  'finance_incomes_reasons_emails' => 'Писма',
  'finance_incomes_reasons_comments' => 'Коментари',
  'finance_incomes_reasons_remaining_sum' => 'Оставаща сума',
  'finance_incomes_reasons_remaining_sum_no_vat' => 'без ДДС',
  'finance_incomes_reasons_invoice_total' => 'Стойност на фактурата',
  'finance_invoices_templates' => 'Фактури за издаване',
  'finance_invoices_templates_invoices' => 'Фактури',
  'finance_invoices_templates_invoice' => 'Фактура',
  'finance_invoices_templates_proforma_invoice' => 'Проформа фактура',
  'finance_invoices_templates_name' => 'Относно',
  'finance_invoices_templates_customer' => 'Контрагент',
  'finance_invoices_templates_type' => 'Тип',
  'finance_invoices_templates_total' => 'Общо',
  'finance_invoices_templates_total_with_vat' => 'Общо с ДДС',
  'finance_invoices_templates_issue_date' => 'Дата на документа',
  'finance_invoices_templates_send_to' => 'Изпратено до',
  'finance_invoices_templates_invoice_not_send' => 'не е изпратена',
  'finance_invoices_templates_error_issuing_invoice' => 'Грешка при издаване',
  'finance_invoices_templates_error_sending_invoice' => 'Грешка при изпращане',
  'finance_invoices_templates_distributed' => 'Разпределена',
  'finance_invoices_templates_warning_distributed' => 'Не е разпределена',
  'finance_invoices_templates_file' => 'Файл',
  'finance_invoices_templates_download_file' => 'изтегли файла',
  'finance_incomes_reasons_generated_files' => 'Генерирани файлове',
  'finance_incomes_reasons_generated_revisions' => 'Добави нов или презапиши',
  'finance_incomes_reasons_generated_add_new' => 'Добави нов',
  'finance_incomes_reasons_pattern_variables' => 'Моля, попълнете данните, използвани в шаблона!',
  'finance_incomes_reasons_pattern_modify' => 'Последни редакции на шаблона',
  'finance_incomes_reasons_generated_get_revision' => 'Вземи данни от Версия',
  'finance_incomes_reasons_generated_save_revision' => 'Замести версия',
  'finance_incomes_reasons_generate_revision_title' => 'Име на Версията',
  'finance_incomes_reasons_generate_revision_description' => 'Описание на Версията',
  'finance_incomes_reasons_generated_filename' => 'Файл',
  'finance_incomes_reasons_generated_revision' => 'Версия',
  'finance_incomes_reasons_generated_pattern' => 'Шаблон',
  'finance_incomes_reasons_generated_description' => 'Описание',
  'finance_incomes_reasons_generated_added' => 'Добавен на',
  'finance_incomes_reasons_generated_added_by' => 'Добавен от',
  'finance_incomes_reasons_file_not_exist' => 'Файлът е изтрит или повреден',
  'finance_incomes_reasons_payment' => 'Плащане',
  'finance_incomes_reasons_payment_amount' => 'Сума на плащането',
  'finance_incomes_reasons_payment_paid' => 'Платена сума по този приходен документ',
  'finance_incomes_reasons_remaining_invoices_amount' => 'Оставаща сума за фактуриране',
  'finance_incomes_reasons_invoice_status' => 'Статус на фактуриране',
  'finance_incomes_reasons_invoice_status_not_invoicable' => 'не подлежи на фактуриране',
  'finance_incomes_reasons_invoice_status_not_invoiced' => 'нефактуриран',
  'finance_incomes_reasons_invoice_status_partial' => 'частично фактуриран',
  'finance_incomes_reasons_invoice_status_invoiced' => 'фактуриран',
  'finance_incomes_reasons_paid_default' => 'платено основание',
  'finance_incomes_reasons_not_paid_default' => 'неплатено основание',
  'finance_incomes_reasons_paid_advance' => 'платена авансова фактура',
  'finance_incomes_reasons_not_paid_advance' => 'неплатена авансова фактура',
  'finance_incomes_reasons_payment_day_soon' => 'фактура с наближаваща дата на плащане',
  'message_finance_incomes_reasons_edit_success' => 'Успешна редакция на %s',
  'message_finance_incomes_reasons_add_success' => 'Успешно добавяне на %s',
  'message_finance_incomes_reasons_translate_success' => 'Успешен превод на %s',
  'message_finance_incomes_reasons_invoice_success' => 'Фактурата беше създадена успешно!',
  'message_finance_incomes_reasons_proforma_invoice_success' => 'Проформа фактурата беше създадена успешно!',
  'message_finance_incomes_reasons_addcreditdebit_success' => 'Действието беше успешно!',
  'message_finance_incomes_reasons_addcorrect_success' => 'Корекцията беше създадена успешно!',
  'message_finance_incomes_reasons_quantity_change_success' => 'Количествата в [reason_type_name] бяха променени!',
  'message_finance_incomes_reasons_addcredit_success' => 'Успешно създадено кредитно известие към [invoice_type_name]!',
  'message_finance_incomes_reasons_adddebit_success' => 'Успешно създадено дебитно известие към [invoice_type_name]!',
  'message_finance_incomes_reasons_distribute_success' => 'Успешно разпределяне на %s',
  'message_finance_incomes_reasons_no_available_quantities' => 'Избраните стоки нямат наличност!',
  'message_finance_incomes_reasons_printform_success' => 'Печатната форма на фактурата бе запазена успешно!',
  'message_finance_incomes_reasons_add_advance_success' => 'Авансовата фактура беше добавена успешно!',
  'message_finance_incomes_reasons_add_advance_proforma_success' => 'Авансовата проформа фактура беше добавена успешно!',
  'error_no_such_finance_incomes_reason' => 'Няма приходен документ с този идентификационен номер!',
  'error_no_such_finance_invoice' => 'Тази фактура е временно недостъпна или не съществува!',
  'error_finance_incomes_reasons_add_failed' => 'Грешка при добавяне на %s',
  'error_finance_incomes_reasons_edit_failed' => 'Грешка при редакция на %s',
  'error_finance_incomes_reasons_translate_failed' => 'Грешка при превод на %s',
  'error_finance_incomes_reasons_invoice_failed' => 'Грешка при създаването на фактура!',
  'error_finance_incomes_reasons_proforma_invoice_failed' => 'Грешка при създаването на проформа фактура!',
  'error_finance_incomes_reasons_invoice_no_quantity' => 'Няма достатъчно нефактурирани количества по [reason_type_name]!',
  'error_finance_incomes_reasons_advanced_negative' => 'Отчислената стойност авансова фактура е невалидна (отрицателна)!',
  'error_finance_incomes_reasons_advance_invalid_sum' => 'Сумата за авансовата фактура е невалидна!',
  'error_finance_incomes_reasons_paid_relatives' => 'Избраното действие ще предизвика надплащане на приходния документ!<br />Моля, преразпределете плащанията по следните записи: %s!',
  'error_finance_incomes_reasons_paid_relatives2' => 'Избраното действие ще предизвика надплащане на приходния документ!',
  'error_finance_incomes_reasons_handover_no_quantity' => 'Няма достатъчно неиздадени количества по [reason_type_name]!',
  'error_finance_incomes_reasons_addcreditdebit_failed' => 'Грешка при добавяне на кредитни/дебитни известия!',
  'error_finance_incomes_reasons_addcreditdebit_no_data' => 'Няма промяна в данните на [invoice_type_name]. Кредитни/дебитни известия се издават само след промяна в данните на [invoice_type_name]!',
  'error_finance_incomes_reasons_addcorrect_failed' => 'Грешка при създаване на корекция към %s!',
  'error_finance_incomes_reasons_debit_denied' => 'Нефактурираните количества в [reason_type_name] не са достатъчни за издаване на дебитно известие към [invoice_type_name], а настройките за тип "[reason_type_name]" не позволяват издаването на корекция!',
  'error_finance_incomes_reasons_advance_over' => 'Опитахте се да разпределите %s към авансова фактура %s, за която има остатък за разпределяне %s!',
  'error_finance_incomes_reasons_advance_debit_denied' => 'Към авансова фактура не могат да бъдат издавани дебитни известия!',
  'error_finance_incomes_reasons_advance_debit_denied_add_invoice' => 'Можете да издадете нова авансова фактура <a href="[add_invoice_url]">оттук</a>.',
  'error_finance_incomes_reasons_credit_denied' => 'Вече са издадени кредитни известия за всички количества във фактурата!',
  'error_finance_incomes_reasons_distribute_failed' => 'Грешка при разпределяне на %s',
  'error_finance_incomes_reasons_printform_failed' => 'Грешка при запазване на печатната форма на фактурата',
  'error_finance_incomes_reasons_invoice_printform_doesnot_match_invoice' => 'Стойността на фактурата в печатната форма не съвпада със стойността на оригиналната фактура',
  'error_finance_incomes_reasons_add_advance_failed' => 'Грешка при добавяне на авансова фактура!',
  'error_finance_incomes_reasons_add_advance_proforma_failed' => 'Грешка при добавяне на авансова проформа фактура!',
  'error_no_finance_incomes_reasons_or_annulled' => 'Не са избрани приходни документи или някой от избраните е анулиран',
  'error_finance_incomes_reasons_different_types' => 'Избраните приходни документи са от различни типове',
  'error_finance_incomes_reasons_multiprint_failed' => 'Грешка при множествен печат на %s',
  'error_finance_incomes_reasons_no_counter' => 'Няма брояч за тип "%s", фирма "%s" и офис "%s"!',
  'error_finance_incomes_reasons_invalid_type' => 'Избран е невалиден или неактивен тип!',
  'error_finance_incomes_reasons_no_customer_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_company_specified' => 'Не е избранa фирма!',
  'error_finance_incomes_reasons_no_container_id_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_issue_date_specified' => 'Моля, задайте %s!',
  'error_finance_incomes_reasons_no_date_of_payment_specified' => 'Моля, задайте %s!',
  'error_finance_incomes_reasons_no_fiscal_event_date_specified' => 'Моля, задайте %s!',
  'error_finance_incomes_reasons_no_cd_reason_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_employee_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_project_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_trademark_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_description_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_fin_field_1_specified' => 'Моля, изберете %s!',
  'error_finance_incomes_reasons_no_fin_field_2_specified' => 'Моля, изберете %s!',
  'warning_finance_incomes_reasons_addcorrect_denied' => 'Настройките за типа на документа не позволяват издаването на корекция към приходния документ!',
  'warning_finance_incomes_reasons_distribution_deleted' => 'Поради извършената корекция е необходимо отново да разпределите [reason_type_name].',
  'finance_incomes_reasons_distribution_outdated_legend' => 'Била е извършена промяна на разпределянето на пера по елементи. Необходимо е да разпределите записа отново.',
  'finance_incomes_reasons_distribution_not_saved_legend' => 'Разпределянето все още не е записано. Попълнените стойности са според данните за разпределяне по подразбиране.',
  'finance_incomes_reasons_log_add' => '%s добавя %s (стойност: %s)',
  'finance_incomes_reasons_log_system_add' => '%s добавя %s (стойност: %s)',
  'finance_incomes_reasons_log_edit' => '%s редактира %s (стойност: %s)',
  'finance_incomes_reasons_log_annul' => '%s анулира %s (стойност: %s)',
  'finance_incomes_reasons_log_assign' => '%s назначава %s (стойност: %s)',
  'finance_incomes_reasons_log_translate' => '%s превежда %s (стойност: %s)',
  'finance_incomes_reasons_log_status' => '%s променя статуса на %s (стойност: %s)',
  'finance_incomes_reasons_log_multistatus' => '%s променя статуса на %s (стойност: %s)',
  'finance_incomes_reasons_log_generate' => '%s генерира файл за %s, използвайки шаблон "%s"%s',
  'finance_incomes_reasons_log_system_generate' => '%s генерира файл за %s, използвайки шаблон "%s"%s',
  'finance_incomes_reasons_log_generate_delete' => '%s изтрива генериран файл за %s (файл: %s)',
  'finance_incomes_reasons_log_modified_attachments' => '%s променя прикачен файл за %s (файл: %s)',
  'finance_incomes_reasons_log_modified_gen' => '%s променя генериран файл за %s (файл: %s)',
  'finance_incomes_reasons_log_add_attachments' => '%s добавя прикачен файл за %s (файл: %s)',
  'finance_incomes_reasons_log_export' => '%s експортира файл за %s (файл: %s)',
  'finance_incomes_reasons_log_addinvoice' => '%s добавя фактура към %s (стойност: %s)',
  'finance_incomes_reasons_log_addproformainvoice' => '%s добавя проформа фактура към %s (стойност: %s)',
  'finance_incomes_reasons_log_addinvoice_advance' => '%s добавя авансова фактура към %s (стойност: %s)',
  'finance_incomes_reasons_log_addproformainvoice_advance' => '%s добавя авансова проформа фактура към %s (стойност: %s)',
  'finance_incomes_reasons_log_advances' => '%s редактира аванси към %s (стойност: %s)',
  'finance_incomes_reasons_log_addcorrect' => '%s добавя корекция към %s (стойност: %s)',
  'finance_incomes_reasons_log_addcredit' => '%s добавя кредитно известие към %s (стойност: %s)',
  'finance_incomes_reasons_log_adddebit' => '%s добавя дебитно известие към %s (стойност: %s)',
  'finance_incomes_reasons_log_addpayment' => '%s добавя плащане към %s (стойност: %s)',
  'finance_incomes_reasons_log_email' => '%s изпраща e-mail за приходен документ',
  'finance_incomes_reasons_log_receive_email' => '%s получава e-mail към приходен документ',
  'finance_incomes_reasons_log_receive_email_detailed' => 'От %s е получен e-mail към приходен документ, изпратен',
  'finance_incomes_reasons_log_add_comment' => '%s добавя коментар за приходен документ',
  'finance_incomes_reasons_log_edit_comment' => '%s редактира коментар за приходен документ',
  'finance_incomes_reasons_log_distribute' => '%s разпределя %s (стойност: %s)',
  'finance_incomes_reasons_log_system_distribute' => '%s разпределя %s (стойност: %s)',
  'finance_incomes_reasons_log_print' => '%s отпечатва %s, използвайки шаблон "%s"%s',
  'finance_incomes_reasons_log_multiprint' => '%s отпечатва %s, използвайки шаблон "%s"%s',
  'finance_incomes_reasons_log_printform' => '%s записва данни в печатната форма на %s (стойност: %s)',
  'finance_incomes_reasons_log_addhandover' => '%s издава приемо-предавателен протокол %s (стойност: %s)',
  'finance_incomes_reasons_log_system_addhandover' => '%s издава приемо-предавателен протокол %s (стойност: %s)',
  'finance_incomes_reasons_log_commodities_reservation' => '%s издава протокол за %s (стойност: %s)',
  'finance_incomes_reasons_log_tag' => '%s променя тагове на %s (стойност: %s)',
  'finance_incomes_reasons_log_multitag' => '%s променя тагове на %s (стойност: %s)',
  'finance_incomes_reasons_log_empty' => '%s премахва плащане по %s (стойност: %s)',
  'finance_incomes_reasons_log_payments' => '%s редактира баланс на %s (стойност: %s)',
  'finance_incomes_reasons_log_system_payments' => '%s редактира баланс на %s (стойност: %s)',
  'finance_incomes_reasons_log_activate' => '%s активира %s (стойност: %s)',
  'finance_incomes_reasons_log_deactivate' => '%s деактивира %s (стойност: %s)',
  'finance_incomes_reasons_log_create' => '%s създава %s чрез %s',
  'finance_incomes_reasons_log_transform' => '%s трансформира %s (стойност: %s) от %s',
  'finance_incomes_reasons_logtype_add' => 'Добавяне',
  'finance_incomes_reasons_logtype_edit' => 'Редакция',
  'finance_incomes_reasons_logtype_annul' => 'Анулиране',
  'finance_incomes_reasons_logtype_assign' => 'Назначаване',
  'finance_incomes_reasons_logtype_translate' => 'Превод',
  'finance_incomes_reasons_logtype_status' => 'Промяна на статус',
  'finance_incomes_reasons_logtype_multistatus' => 'Множествена промяна на статус',
  'finance_incomes_reasons_logtype_generate' => 'Генериране на файл',
  'finance_incomes_reasons_logtype_generate_delete' => 'Изтриване на файл',
  'finance_incomes_reasons_logtype_modified_attachments' => 'Променяне на файл',
  'finance_incomes_reasons_logtype_modified_gen' => 'Променяне на файл',
  'finance_incomes_reasons_logtype_add_attachments' => 'Добавяне на файл',
  'finance_incomes_reasons_logtype_export' => 'Експортиране',
  'finance_incomes_reasons_logtype_addinvoice' => 'Издаване на фактура',
  'finance_incomes_reasons_logtype_addproformainvoice' => 'Издаване на проформа фактура',
  'finance_incomes_reasons_logtype_advances' => 'Редакция на аванси',
  'finance_incomes_reasons_logtype_addcorrect' => 'Корекция към основание',
  'finance_incomes_reasons_logtype_adddebit' => 'Дебитно известие към фактура',
  'finance_incomes_reasons_logtype_addcredit' => 'Кредитно известие към фактура',
  'finance_incomes_reasons_logtype_addpayment' => 'Добавяне на плащане',
  'finance_incomes_reasons_logtype_email' => 'Изпращане на e-mail',
  'finance_incomes_reasons_logtype_receive_email' => 'Получаване на e-mail',
  'finance_incomes_reasons_logtype_add_comment' => 'Добавяне на коментар',
  'finance_incomes_reasons_logtype_edit_comment' => 'Редакция на коментар',
  'finance_incomes_reasons_logtype_distribute' => 'Разпределяне',
  'finance_incomes_reasons_logtype_print' => 'Печат',
  'finance_incomes_reasons_logtype_multiprint' => 'Множествен печат',
  'finance_incomes_reasons_logtype_printform' => 'Печатна форма',
  'finance_incomes_reasons_logtype_addhandover' => 'Издаване на ППП',
  'finance_incomes_reasons_logtype_commodities_reservation' => 'Запазване на стоки',
  'finance_incomes_reasons_logtype_tag' => 'Промяна на тагове',
  'finance_incomes_reasons_logtype_multitag' => 'Множествена промяна на тагове',
  'finance_incomes_reasons_logtype_empty' => 'Премахване на плащане',
  'finance_incomes_reasons_logtype_payments' => 'Редактиране на баланс',
  'finance_incomes_reasons_logtype_activate' => 'Активиране',
  'finance_incomes_reasons_logtype_deactivate' => 'Деактивиране',
  'finance_incomes_reasons_logtype_create' => 'Създаване',
  'finance_incomes_reasons_logtype_transform' => 'Трансформиране',
  'finance_incomes_reasons_status' => 'Статус',
  'finance_incomes_reasons_substatus' => 'Подстатус',
  'finance_incomes_reasons_after_action_add_unfinished' => 'Добави',
  'finance_incomes_reasons_after_action_edit_unfinished' => 'Редактирай',
  'finance_incomes_reasons_after_action_finish' => 'Приключи',
  'finance_incomes_reasons_after_action_payment' => 'Плати',
  'finance_incomes_reasons_after_action_invoice' => 'Фактурирай',
  'finance_incomes_reasons_after_action_issue' => 'Издай',
  'finance_incomes_reasons_after_action_issue_finish' => 'Издай и приключи',
  'finance_incomes_reasons_after_action_issue_payment' => 'Издай и плати',
  'finance_incomes_reasons_action_add' => 'добавите',
  'finance_incomes_reasons_action_edit' => 'редактирате',
  'help_finance_incomes_reasons_after_action_unfinished' => 'С този бутон ще [action] записа, като той ще остане отворен за редакция.',
  'help_finance_incomes_reasons_after_action_finish' => 'С този бутон ще [action] записа и ще го приключите – няма да можете да го редактирате.',
  'help_finance_incomes_reasons_after_action_payment' => 'С този бутон ще [action] записа и ще създадете плащане, което ще покрие цялата стойност на този запис.',
  'help_finance_incomes_reasons_after_action_invoice' => 'С този бутон ще [action] записа и ще имате възможност да издадете фактура.',
  'help_finance_incomes_reasons_phase' => 'Фаза към избрания проект',
  'help_finance_incomes_reasons_total_amount_with_vat' => 'Обща стойност на приходния документ.',
  'help_finance_incomes_reasons_invoice_amount' => 'Сумата от основанието, която е била фактурирана.<br /><strong>ВАЖНО!</strong> Това НЕ означава, че посочената сума е платена! Фактурираната сума трябва да бъде платена към съответната фактура.',
  'help_finance_incomes_reasons_total_paid_amount' => 'Цялата платена сума към това основание. Включени са както директните плащания към основанието, така и плащанията към свързаните финансови документи.',
  'help_finance_incomes_reasons_direct_paid_amount' => 'Директно платената сума към текущия приходен документ.',
  'help_finance_incomes_reasons_amount_to_be_paid' => 'Сумата, която може да бъде платена директно към текущия приходен документ.',
  'finance_incomes_reasons_incremental_correct' => 'увеличаване',
  'finance_incomes_reasons_decremental_correct' => 'намаляване',
  'finance_incomes_reasons_correct_for_credit' => 'За кредитното известие',
  'finance_incomes_reasons_correct_for_debit' => 'За дебитното известие',
  'finance_incomes_reasons_correct_for_both' => 'За двете известия',
  'finance_incomes_reasons_create_correct' => 'Желаете ли да създадете корекция към приходния документ?',
  'finance_incomes_reasons_correct_for_credit_description' => 'Ако създадете корекция за кредитното известие, това ще намали количеството на артикулите в приходния документ. В противен случай количеството ще се върне в документа като нефактурирано.',
  'finance_incomes_reasons_correct_for_debit_description' => 'Ако създадете корекция за дебитното известие, това ще увеличи количеството на артикулите в приходния документ. В противен случай, ако има нефактурирано количество в документа, то ще се прехвърли към дебитното известие. Ако количеството в документа е недостатъчно, ще се генерира корекция, без значение какво сте избрали.',
  'finance_incomes_reasons_issue_correct' => 'Издай корекция към приходния документ',
  'finance_incomes_reasons_correct_description' => 'Ако не искате да създавате корекция към изходния документ, махнете отметката.<br /><br />Ако намалите количествата в някой ред, то тези количества ще се върнат като нефактурирани в изходния документ.<br /><br />Ако увеличите количествата в някой ред, то трябва да имате достатъчно нефактурирано количество в изходния документ, от където да се попълни това увеличаване.<br /><br />В случай че няма достатъчно нeфактурирано количество или промените цени, то корекция ще се издаде, без значение какво сте избрали тук.',
  'finance_incomes_reasons_issue_invoice_from_proforma' => 'Издадената фактура ще бъде идентична на настоящата проформа фактура и няма да подлежи на редакция.<br />Желаете ли да издадете фактура от проформа фактура?',
  'finance_incomes_reasons_update_customer_info' => 'Обновяване на данни от контрагент',
  'help_finance_incomes_reasons_update_customer_info' => 'Натиснете тук, за да обновите данните за контрагента в настоящия финансов документ.',
  'finance_incomes_reasons_handover_direction' => 'Вид',
  'finance_incomes_reasons_handover_direction_incoming' => 'Приемателен',
  'finance_incomes_reasons_handover_direction_outgoing' => 'Предавателен',
  'finance_incomes_reasons_cd_reason' => 'Основание за издаване на Кредитно/Дебитно известие',
  'error_finance_incomes_reasons_handover_failed_incoming' => 'Допусната е грешка при приемане на стоката.',
  'error_finance_incomes_reasons_handover_failed_outgoing' => 'Допусната е грешка при издаване на стоката.',
  'error_finance_incomes_reasons_commodities_reservation_failed' => 'Допусната е грешка при запазването на стоката',
  'message_finance_incomes_reasons_handover_success_incoming' => 'Стоката е успешно приета. Брой създадени ППП:',
  'message_finance_incomes_reasons_handover_success_outgoing' => 'Стоката е успешно издадена. Брой създадени ППП:',
  'message_finance_incomes_reasons_commodities_reservation_success' => 'Стоката е запазена успешно. Брой създадени ПЗС:',
  'message_finance_incomes_reasons_confirm_annulment_invoice' => 'Сигурни ли сте, че искате да анулирате фактура № %s?',
  'message_finance_incomes_reasons_confirm_annulment_credit_notice' => 'Сигурни ли сте, че искате да анулирате кредитно известие № %s?',
  'message_finance_incomes_reasons_confirm_annulment_debit_notice' => 'Сигурни ли сте, че искате да анулирате дебитно известие № %s?',
  'message_finance_annul_success' => 'Успешно анулиране на %s',
  'error_finance_annul_failed' => 'Грешка при анулиране на %s',
  'error_finance_incomes_reasons_credit_notice_annul_not_allowed' => 'Анулирането на %s не е позволено поради свързаност с коригиращ документ <a href="%s" target="_blank">%s</a>!',
  'help_finance_incomes_reasons_fiscal_event_date' => 'Ако дата не бъде въведена, се взема датата на документа!',
  'help_finance_incomes_reasons_cd_reason' => 'Според закона задължително трябва да се попълни причина за издаване на Кредитно/Дебитно известие',
  'error_finance_incomes_reasons_no_valid_mails' => 'Няма въведени валидни e-mail адреси!',
  'error_finance_incomes_reasons_invalid_mails' => 'Има невалидни e-mail адреси!',
  'error_finance_incomes_reasons_generate_document' => 'Грешка при генериране на файлове!',
  'message_finance_incomes_reasons_email_success' => 'Съобщението беше изпратено успешно!',
  'error_finance_incomes_reason_email_failed' => 'Съобщението не беше изпратено!',
  'message_finance_incomes_reasons_edit_advances' => 'Успешна редакция на връзки с авансови фактури',
  'error_finance_incomes_reasons_edit_advances' => 'Грешка при редактиране на връзки с авансови фактури',
  'error_finance_incomes_reasons_invoice_advance_rows' => 'Не са описани всички авансови фактури във фактурата',
  'message_finance_incomes_reasons_edit_payment_success' => 'Успешно редактиране на баланс на %s',
  'error_finance_incomes_reasons_edit_payment_failed' => 'Грешка при редактиране на баланс на %s',
  'error_finance_invoices_advanced_no_payment_type' => 'Моля, изберете каса/банкова сметка!',
  'error_finance_invoices_advanced_no_periods_start' => 'Моля, изберете начало на периода!',
  'error_finance_invoices_advanced_no_periods_end' => 'Моля, изберете край на периода!',
  'error_finance_invoices_advanced_no_pay_after' => 'Моля, въведете падеж!',
  'error_finance_invoices_advanced_recurrence_wrong_period' => 'Невалидна начална или крайна дата на периода!',
  'error_finance_invoices_advanced_invoiced_period' => 'Периодът е частично или напълно фактуриран! Възможна дата за начало на период - %s!',
  'error_finance_invoices_advanced_recurrence_template_start' => 'Невалидна начална дата на периода - преди началната дата на шаблона!',
  'error_finance_invoices_advanced_recurrence_template_end' => 'Невалидна крайна дата на периода - след крайната дата на шаблона!',
  'error_finance_invoices_advanced_exist_today' => 'Не може да издавате повече от веднъж дневно фактура за същия шаблон!',
  'error_finance_incomes_reasons_receive_date' => 'Дата на получаване не може да е преди дата на издаване!',
  'error_finance_invoice_preview' => 'Няма данни за фактурата',
  'error_finance_incomes_reason_wrong_issue_date' => 'Датата на документа е невалидна. Първата възможна дата за издаване е [issue_date]!',
  'error_finance_incomes_reason_wrong_date_of_payment' => 'Датата на падеж е невалидна. Първата възможна дата за падеж е [date_of_payment]!',
  'error_finance_incomes_reason_issue_invoice_from_proforma_issue_date' => 'Датата на документа на фактурата не може да бъде преди тази на проформа фактурата.',
  'error_finance_correct_both_changed' => 'Не е допустима едновременна промяна на повече от едно поле!',
  'error_finance_correct_empty' => 'Не е извършена никаква промяна по документа!',
  'error_finance_correct_more_in_handover' => 'Задали сте количество, по-малко от вече предаденото с ППП на клиента, за следните артикули:',
  'error_finance_incomes_reasons_expenses_reason_payment' => 'Записът не може да бъде приключен, защото към %sродителския документ%s има разпределени основания за разход, които покриват по-голяма сума от избраната за фактуриране!',
  'error_finance_incomes_reasons_expenses_reason_redistribute' => 'Моля, преразпределете основанията за разход или изберете по-малка сума за фактуриране!',
  'error_finance_incomes_reason_overpaid' => 'Разпределената сума по приходния документ (%.2f) не може да надвишава общата му стойност (%s). За да можете да приложите промените, намалете <a href="%s" target="_blank">разпределената сума по документа</a> или <a href="%s" target="_blank">производните му документи</a>.',
  'message_finance_incomes_reasons_addpayment_success' => 'Успешно добавяне на плащане',
  'error_finance_incomes_reasons_addpayment_failed' => 'Грешка при добавяне на плащане',
  'error_finance_incomes_reasons_advanced_total' => 'Сумата с всички аванси (%.02f) е по-голяма от сумата на приходния документ (%.02f)!',
  'error_finance_incomes_reasons_advanced_over_maxshareable' => 'Сумата с всички аванси (%.02f) е по-голяма от максимално допустимата за отчисления (%.02f)!',
  'error_finance_incomes_reasons_advanced_proforma_total' => 'Сумата на авансовата проформа и всички авансови фактури (%.02f) е по-голяма от сумата на приходния документ (%.02f)!',
  'error_finance_incomes_reasons_can_not_add_advance' => 'Не могат да се добавят повече авансови фактури за приходния документ!',
  'error_finance_incomes_reasons_advanced_paid_amount' => 'Общата платена сума по аванси и по приходния документ надвишава стойността му!',
  'error_finance_incomes_reasons_advanced_value_greater_than_remainder' => 'За авансова фактура %s отчислената сума (%.02f) е по-голяма от остатъка (%.02f)!',
  'error_finance_incomes_reasons_advanced_value_lower_than_invoiced' => 'За авансова фактура %s отчислената сума (%.02f) е по-малка от вече фактурираната (%.02f)!',
  'error_finance_incomes_reasons_advanced_value_greater_than_uninvoiced' => 'За авансова фактура %s отчислената сума (%.02f) е по-голяма от нефактурирания остатък (%.02f)!',
  'error_finance_incomes_reasons_advanced_value_is_invalid' => 'За авансова фактура %s e въведена невалидна стойност (%.02f)!',
  'error_finance_incomes_reasons_allocated_amount_total' => 'Сборът от въведените суми не бива да надвишава неразпределената сума от приходния документ!',
  'error_finance_incomes_reasons_allocated_amount_row' => 'За всеки ред въведената сума не бива да надвишава неразпределената сума!',
  'warning_finance_incomes_reasons_invoice_fiscal_dates' => 'Датата на данъчно събитие може да е между %s и %s!',
  'error_finance_incomes_reasons_different_advances_vat' => 'Избраните авансови фактури са с различни ДДС ставки спрямо приходния документ или помежду си!',
  'error_finance_incomes_reasons_advances_vat_wrong' => 'Избраната ДДС ставка е различна от тази на приходния документ!',
  'error_finance_incomes_reasons_advance_overdue' => 'Въведената стойност за авансовата фактура е по-голяма от сумата, която ще се фактурира за същия период!',
  'error_finance_incomes_reasons_invoice_from_proforma_vat_wrong' => 'За приходния документ вече има издадени фактури с различна ДДС ставка от тази на проформата!',
  'error_finance_incomes_reasons_has_unfinished_invoice' => 'Грешка! Има <a href="%s" target="_blank">неприключена фактура</a>!',
  'error_finance_incomes_reasons_invoice_over_price' => 'Не може цената на артикула да е по-голяма отколкото в родителския документ!',
  'finance_advance' => 'Авансова фактура',
  'finance_advances_no_vat' => 'Авансова фактура (Всички суми са без начислен ДДС)',
  'finance_advances_to_share' => 'За отчисляване',
  'finance_advances_shared' => 'Отчислено',
  'finance_advances_invoiced' => 'Фактурирано',
  'finance_advances_remainder' => 'Остатък',
  'finance_advances_total' => 'Общо',
  'finance_advances_currency' => 'Валута',
  'finance_advances_invoiced_total' => 'Фактурирано',
  'finance_advances_remainder_total' => '*Остатък',
  'finance_advances_total_to_share' => '*За фактуриране',
  'help_finance_help_advance' => '',
  'help_finance_advances_no_vat' => '',
  'help_finance_advances_invoiced' => 'Фактурирана част (фактура или проформа) от този аванс за тази сделка',
  'help_finance_advances_to_share' => 'Сума за аванс по този документ',
  'help_finance_advances_shared' => 'Сума за аванс по този документ',
  'help_finance_advances_remainder' => 'Максимална сума за разпределяне от авансовата фактура',
  'help_finance_advances_total' => 'Обща сума на авансовата фактура без ДДС',
  'help_finance_advances_currency' => 'Валута',
  'help_finance_advances_invoiced_total' => 'Общо фактурирано (фактура или проформа) по всички авансови фактури',
  'help_finance_advances_remainder_total' => 'Остатък за разпределяне към този документ. <br /><strong>*ВНИМАНИЕ:</strong> от сумата са приспаднатни плащанията към документа, проформа фактурите с плащене по тях и кредитни известия.',
  'help_finance_advances_total_to_share' => 'Обща сума за разпределяне към този документ. <br /><strong>*ВНИМАНИЕ:</strong> възможно е да има платени проформа фактури или кредитни известия, които са приспаднати в тази сума.',
); ?>